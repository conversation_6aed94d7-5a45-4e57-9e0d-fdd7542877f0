---
title: 13.T<PERSON><PERSON><PERSON> 美化：评论框体验双重增强（输入提示 + 表情放大）
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 20246
date: 2025-07-11 05:13:45
---


## **13. Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）**

### **🚀 前言：功能介绍**

> 本指南将，为您的Twikoo评论区添加两个非常实用的功能：
> 1.  **输入框提示气泡**：当访客点击“昵称”、“邮箱”、“网址”输入框时，上方会弹出一个友好的提示气泡。
> 2.  **表情悬停放大**：当访客在表情选择面板中，将鼠标悬停在任意一个表情上时，会显示一个放大的预览图，方便看清表情细节。

### **🗺️ 核心流程概览**
1.  **添加整合后的CSS样式**：将两个功能所需的所有CSS代码合并到一个文件中。
2.  **添加表情放大的JavaScript脚本**：将实现放大功能的JS代码添加到另一个文件中。
3.  **确保文件被正确注入**：检查并确认您的主题配置加载了这两个文件。

***

### **第一步：添加整合后的CSS代码**

我们将把“输入提示”和“表情放大”两个功能所需的全部CSS代码，合并到您的**自定义CSS文件**中（例如 `source/custom/css/tip_style.css`）。

请将下面代码框中的**所有内容**，完整地复制并添加到您的自定义CSS文件中。

```css
/* --- 评论框输入提示 CSS --- */
/* 设置文字内容 :nth-child(1)的作用是选择第几个输入框 */
.el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(1):before {
    content: '输入QQ号会自动获取昵称和头像🐧';
}

.el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(2):before {
    content: '收到回复将会发送到您的邮箱📧';
}

.el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(3):before {
    content: '可以通过昵称访问您的网站🔗';
}

/* 当用户点击输入框时显示提示 */
.el-input.el-input--small.el-input-group.el-input-group--prepend:focus-within::before,
.el-input.el-input--small.el-input-group.el-input-group--prepend:focus-within::after {
    display: block;
}

/* 提示气泡的主体样式 */
.el-input.el-input--small.el-input-group.el-input-group--prepend::before {
    display: none;
    position: absolute;
    top: -60px;
    white-space: nowrap;
    border-radius: 10px;
    left: 50%;
    transform: translate(-50%);
    padding: 14px 18px;
    background: #444;
    color: #fff;
    z-index: 10; /* 确保在顶层 */
}

/* 提示气泡的小三角 */
.el-input.el-input--small.el-input-group.el-input-group--prepend::after {
    display: none;
    content: '';
    position: absolute;
    border: 12px solid transparent;
    border-top-color: #444;
    left: 50%;
    transform: translate(-50%, -48px);
    z-index: 10; /* 确保在顶层 */
}


/* --- 表情悬停放大 CSS --- */
#owo-big {
    position: fixed;
    display: none;
    align-items: center;
    background-color: rgb(255, 255, 255);
    border: 1px #aaa solid;
    border-radius: 10px;
    z-index: 9999;
    transform: translate(0, -105%);
    overflow: hidden;
    animation: owoIn 0.3s cubic-bezier(0.42, 0, 0.3, 1.11);
}

[data-theme=dark] #owo-big {
    background-color: #4a4a4a
}

#owo-big img {
    width: 100%;
}

/* 动画效果代码由 Heo 提供 */
@keyframes owoIn {
    0% {
        transform: translate(0, -95%);
        opacity: 0;
    }
    100% {
        transform: translate(0, -105%);
        opacity: 1;
    }
}
```

***

### **第二步：添加表情放大的JavaScript脚本**

接下来，将实现“表情放大”功能的JavaScript代码，添加到您的**自定义JS文件**中（例如 `source/custom/js/tip_main.js`）。

```javascript
// 如果当前页有评论就执行函数
if (document.getElementById('post-comment')) owoBig();

// 表情放大
function owoBig() {
    let flag = 1, // 设置节流阀
        owo_time = '', // 设置计时器
        m = 3; // 设置放大倍数
    // 创建盒子
    let div = document.createElement('div'),
        body = document.querySelector('body');
    // 设置ID
    div.id = 'owo-big';
    // 插入盒子
    body.appendChild(div)

    // 构造observer
    let observer = new MutationObserver(mutations => {

        for (let i = 0; i < mutations.length; i++) {
            let dom = mutations[i].addedNodes,
                owo_body = '';
            if (dom.length == 2 && dom[1].className == 'OwO-body') owo_body = dom[1];
            // 如果需要在评论内容中启用此功能请解除下面的注释
            // else if (dom.length == 1 && dom[0].className == 'tk-comment') owo_body = dom[0];
            else continue;
            
            // 禁用右键（手机端长按会出现右键菜单，为了体验给禁用掉）
            if (document.body.clientWidth <= 768) owo_body.addEventListener('contextmenu', e => e.preventDefault());
            // 鼠标移入
            owo_body.onmouseover = (e) => {
                    if (flag && e.target.tagName == 'IMG') {
                        flag = 0;
                        // 移入300毫秒后显示盒子
                        owo_time = setTimeout(() => {
                            let height = e.target.clientHeight * m, // 盒子高
                                width = e.target.clientWidth * m, // 盒子宽
                                left = (e.x - e.offsetX) - (width - e.target.clientWidth) / 2, // 盒子与屏幕左边距离
                                top = e.y - e.offsetY; // 盒子与屏幕顶部距离

                            if ((left + width) > body.clientWidth) left -= ((left + width) - body.clientWidth + 10); // 右边缘检测，防止超出屏幕
                            if (left < 0) left = 10; // 左边缘检测，防止超出屏幕
                            // 设置盒子样式
                            div.style.cssText = `display:flex; height:${height}px; width:${width}px; left:${left}px; top:${top}px;`;
                            // 在盒子中插入图片
                            div.innerHTML = `<img src="${e.target.src}">`
                        }, 300);
                    }
                };
            // 鼠标移出隐藏盒子
            owo_body.onmouseout = () => { div.style.display = 'none', flag = 1, clearTimeout(owo_time); }
        }

    })
    observer.observe(document.getElementById('post-comment'), { subtree: true, childList: true }) // 监听的 元素 和 配置项
}
```

***

### **第三步：确认文件注入**

最后，请确保您的**主题配置文件** (`themes/anzhiyu/_config.yml`) 中，已经正确注入了您存放上述代码的自定义CSS和JS文件,并使用Inject诸如对应的CSS与JS



