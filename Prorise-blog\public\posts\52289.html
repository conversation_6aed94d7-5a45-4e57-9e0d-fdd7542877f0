<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>SpringAI（三）：3. 会话核心 API 深度解析 | Prorise的小站</title><meta name="keywords" content="Java微服务篇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="SpringAI（三）：3. 会话核心 API 深度解析"><meta name="application-name" content="SpringAI（三）：3. 会话核心 API 深度解析"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="SpringAI（三）：3. 会话核心 API 深度解析"><meta property="og:url" content="https://prorise666.site/posts/52289.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="3. 会话核心 API 深度解析在上一章，我们成功打通了前后端的任督二脉，实现了基础的聊天功能。但一个真正的智能应用，不能只有“一问一答”的瞬时反应，它还需要具备“反思”过去（可观测性）和“铭记”历史（对话记忆）的能力。本章，我们将深入 Spring AI 的两大核心机制，为我们的 AI-Copil"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta name="description" content="3. 会话核心 API 深度解析在上一章，我们成功打通了前后端的任督二脉，实现了基础的聊天功能。但一个真正的智能应用，不能只有“一问一答”的瞬时反应，它还需要具备“反思”过去（可观测性）和“铭记”历史（对话记忆）的能力。本章，我们将深入 Spring AI 的两大核心机制，为我们的 AI-Copil"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/52289.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"SpringAI（三）：3. 会话核心 API 深度解析",postAI:"true",pageFillDescription:"3. 会话核心 API 深度解析, 3.1 揭开黑盒：ChatClient 的可观测性, 3.1.1 核心利器 Advisor 与 SimpleLoggerAdvisor, 3.1.2 实战：为 AI-Copilot 开启调试日志, 3.1.3 解读日志输出, **3.1.4 ChatClient 详解(重点内容) **, 3.2 赋予AI记忆：ChatMemory 深度实践, 3.2.1 核心设计：策略与存储分离, 3.2.2 记忆策略与存储详解, 3.2.3 实战：为 AI-Copilot 实现多轮对话, 3.3 管理长期记忆：实现会话历史服务, 3.3.1 后端服务搭建：基于 MyBatis-Plus, 3.3.2 API 接口文档会话核心深度解析在上一章我们成功打通了前后端的任督二脉实现了基础的聊天功能但一个真正的智能应用不能只有一问一答的瞬时反应它还需要具备反思过去可观测性和铭记历史对话记忆的能力本章我们将深入的两大核心机制为我们的赋予更深层次的智能揭开黑盒的可观测性在进行任何复杂的系统开发时可观测性都是成功的基石对于应用开发而言这一点尤为重要我们通过的流畅与大语言模型交互但这层优雅的抽象也可能成为一个黑盒我们编写的你好在框架内部可能会被动态地与系统级指令历史对话甚至是函数调用的定义组合在一起形成一个远比我们想象中复杂的最终请求体当模型的响应与我们的预期出现偏差例如它没有遵循我们的系统指令或者忘记了之前的对话内容我们面临的第一个也是最核心的难题便是我的应用最终到底向发送了什么内容如果无法看清这个黑盒的内部后续的提示词工程上下文管理优化乃至错误排查都将无从谈起本节将深入探讨提供的核心调试工具让我们能够点亮一盏灯彻底照亮的内部通信链路核心利器与为了解决上述的可观测性问题引入了名为顾问的优雅设计模式在软件工程领域这与面向切面编程中的通知或网络编程中的拦截器思想一脉相承允许我们在不侵入核心逻辑的前提下在其请求发送前和响应返回后织入我们自定义的横切关注点如日志记录指标监控等在众多的内置实现中是我们进行开发和调试时最不可或缺的利器它的核心作用可以简洁地概括如下特性描述核心职责打印完整的和工作模式日志级别为时激活适用场景开发调试调优上下文问题排查性质只读无侵入不修改请求或响应提供了一种灵活而强大的方法来拦截修改和增强应用程序中的驱动的交互使用用户文本调用模型时一种常见模式是使用上下文数据附加或增强提示此上下文数据可以是不同的类型常见的包括您自己的私有数据和对话历史记录会记录的和数据这对于调试和监控交互非常有用实战为开启调试日志在的生态中启用的过程极其简单在我们之前的做法中通过使用这个注解进行精细化的日志控制但这并不意味着它俩冲突不过仅在我们这一层使用的效果远比手动日志要来得好得多配置日志级别在文件中添加配置告诉日志系统我们关心包下的信息关键将包的日志级别设置为只有这样内部的判断才会为推荐将我们自己应用的包也设为方便观察完整调用链路在中应用顾问我们将的应用集中在包中进行配置创建定义并为其配置默认的根据自动配置好的实例一个配置了日志顾问的实例修改的构造函数现在不再需要而是直接注入我们配置好的其他方法解读日志输出完成了以上配置并重启应用后当我们调用任何一个聊天接口时控制台中将自动出现详尽的调试信息你好你好我是很高兴能为您服务这段日志是极其宝贵的调试信息它清晰地展示了发送给大语言模型的最终请求体和模型返回的原始响应让我们能彻底洞察黑盒内部为后续的调优和问题排查提供了坚实的基础详解重点内容提供用于与模型通信的支持同步和流式编程模型创建是使用对象创建的您可以获取自动配置的实例或者以编程方式创建一个在最简单的用例中提供自动配置创建一个原型以便注入到你的类中注意我们项目中采用的是将本身注册为的方式如节所示这在管理默认配置时更为方便是中进行大模型交互的核心它提供了一个优雅的流式在我们的项目中我们没有在业务代码如或中临时创建实例而是采用了一种更强大更可维护的模式在中将其配置为单例这种方式的好处是显而易见的集中配置所有默认行为如模型选项等系统提示以及最重要的顾问都在一个地方统一配置简化业务业务代码只需要注入配置好的无需关心其复杂的构建过程可以直接调用其功能一致性确保整个应用的所有交互都遵循相同的基本配置和增强逻辑如日志和对话记忆下面我们来详细解析在项目中是如何运用的流式的启动调用链所有交互都始于方法这是一个无参数的方法它会返回一个调用链的起点允许我们后续构建请求构建内容这是最常用的方法用于设置用户的提问内容在我们的中我们正是用它来传递用户的输入用于设置系统级指令引导的行为和角色虽然我们在最终代码中是通过来管理上下文但在需要临时改变角色的场景下可以直接在调用链中使用此方法增强请求这是最强大的功能之一在我们的项目中是实现对话记忆的关键来自于我们最终的这段代码的含义是方法接收一个表达式允许我们对本次请求的进行配置用于向的执行上下文传递参数在这里我们将以为键传入我们之前在中配置的会自动捕获这个参数并根据它来查找对应的历史消息然后将这些历史消息注入到最终发送给的中执行与响应处理支持两种主要的执行模式同步调用会阻塞并等待返回完整响应它适用于不需要实时反馈的场景流式调用会立即返回一个来自响应式编程库的响应会以数据块的形式持续不断地推送过来为了提供最佳的用户体验我们的项目完全采用了流式调用来自于我们最终的声明我们想要一个流式响应我们只关心响应流中的文本内容组合起来就构成了一个完整的具备对话记忆能力的流式聊天请求返回结构化数据虽然我们的聊天功能主要处理文本流但还能将的响应直接转换为对象这在需要生成特定结构的场景下极为有用假设我们想让生成一个演员的电影列表我们可以这样定义一个然后我们可以这样调用请生成演员汤姆汉克斯的电影作品列表以格式返回包含和两个字段注意通常结构化数据需要等待完整响应因此使用会自动提示按指定格式输出并将返回的字符串反序列化为对象对于泛型用法也同样直观请为汤姆汉克斯和比尔默里生成电影作品列表配置模型参数我们可以在请求级别覆盖默认的模型参数如的最新语法使用直接的方法进行链式构建这是一个示例展示如何在单次请求中设置参数写一首关于宇宙的诗设置更高的温度以获得更有创意的回答当然最佳实践依然是在中通过设置全局默认值只在需要时进行局部覆盖通过这样重构本节内容现在完全与项目代码保持一致清晰地解释了我们所使用的每一个背后的原理和目的赋予记忆深度实践大语言模型的接口遵循协议其核心特性之一就是无状态每一次调用都是一次全新的独立的对话这种金鱼记忆显然无法满足构建一个能持续对话的智能应用的需求为了解决这一核心痛点提供了强大而灵活的功能核心设计策略与存储分离在对话记忆功能上的核心设计思想是软件工程中关注点分离原则的经典体现将记忆的策略如何记住与记忆的存储记在哪里相分离这一思想通过两个核心接口得以实现策略接口它定义了记忆的行为和策略例如它决定当对话历史过长时应该保留哪些消息遗忘哪些消息存储接口它定义了记忆的物理存储和检索它的职责非常纯粹就是在后端如内存数据库存取数据我们必须严格辨析两个极易混淆的概念概念定义与范围目的与用途对话记忆用于构建下一次的一个相关的有限的对话历史子集为服务让理解上下文进行连贯对话对话记录一次会话中全部完整的消息交换历史为应用和用户服务用于审计回溯查看历史抽象旨在管理对话记忆而不是完整的对话记录如果您需要维护所有交换消息的完整记录应考虑使用不同的方法比如我们稍后将介绍的基于的方案记忆策略与存储详解记忆策略这是中最常用也是默认的记忆策略它实现了一种高效的滑动窗口机制只保留最近的条消息作为上下文在成本性能和相关性之间取得了最佳平衡当消息数量超过此限制时将驱逐较旧的消息但会保留系统消息设置窗口大小为条消息记忆存储它负责将对话消息进行物理存储提供了多种内置实现实现类存储介质优点缺点适用场景内存零配置极速数据易失开发测试原型关系型数据库可靠持久化需配置生产环境高可用高扩展性配置复杂大规模分布式系统图数据库利用图关系需图数据库知识需要分析对话关系在我们的项目中我们将采用实现生产级的持久化记忆实战为实现多轮对话我们将使用为我们的应用添加持久化的多轮对话能力添加数据库依赖配置数据库与执行请在您的数据库中执行以下来创建所需的表修改在中配置记忆功能我们将的创建和的应用全部集中在配置类中定义并为其配置默认的根据自动配置好的实例聊天内存实例用于维护对话上下文一个配置了日志顾问和内存顾问的实例会自动配置当检测到依赖时会自动使用存储库这里不需要手动配置依赖自动配置机制自动配置说明会自动检测到依赖和中的配置并自动创建一个的我们的方法会自动使用这个作为的后端存储顾问链顺序将添加到链中的顺序至关重要中会先执行改造后端业务代码以支持会话改造聊天请求用户消息内容会话可选参数为空时自动生成获取有效的会话非空的会话改造聊天服务提供流式聊天功能获取流式聊天响应用户消息会话流式响应验证消息重点在这里通过注入会话记忆服务当前不可用请稍后重试改造将内容包装成格式实际内容如果序列化失败发送错误信息发送结束信号改造前端以管理和传递安装库改造获取流式聊天回复的对象用户消息会话返回对象供处理使用原生发送请求流式聊天请求失败改造使用钩子使用会话管理和用户头像消息类型定义消息列表数据符合格式您好我是您的已准备就绪用户输入的消息内容计算属性合并流式数据纯函数跳过空数据或结束标识解析格式实际内容如果解析失败作为纯文本处理向后兼容监听流数据变化实现真正的流式体验流数据一到达就立即切换到流式显示模式立即关闭启用打字效果实时更新内容发送消息处理函数用户输入的消息内容验证消息有效性和是否正在加载中添加用户消息到对话列表使用填充样式在黑色背景下更清晰立即添加思考状态气泡正在思考中设置加载状态清空输入框获取对象使用处理流数据默认支持协议流结束后停止打字效果停止打字效果已在中处理已实时更新流式请求失败更新最后一个消息为错误消息抱歉服务暂时不可用请稍后重试表单提交处理函数当用户点击发送按钮或按下回车键时触发清理输入框取消请求处理函数用于中断当前请求预留功能实现请求取消功能录音状态变化处理函数录音状态事件对象触发器事件处理函数处理特殊命令触发如等触发器事件对象应用头部聊天主体区域使用组件输入区域消息发送组件请输入您的问题重启应用并刷新前端页面现在您的已经拥有了基于数据库的持久化记忆管理长期记忆实现会话历史服务解决了的上下文问题但作为应用开发者我们还需要为用户提供查看管理他们完整对话记录的功能这需要我们直接操作这张表后端服务搭建基于我们将按照标准三层架构构建一套完整的面向业务的会话历史管理服务依赖与配置添加依赖主启动类添加注解指向接口包添加配置数据访问层创建用于操作的实体类创建继承的接口业务逻辑层定义服务接口实现服务接口新的会话接口层创建新的暴露三个端点删除成功删除失败或会话不存在接口文档以下是为会话历史管理功能提供的后端接口文档基础获取所有会话列表获取所有已存在的会话的概要信息列表按最后更新时间倒序排列请求请求参数无成功响应操作成功你好介绍一下你自己请用写一个快排响应数据字段说明字段类型描述会话的唯一标识符根据会话第一条用户消息生成的默认标题最长个字符会话的最后更新时间格式获取指定会话详情根据提供的获取该会话的完整聊天记录按消息时间升序排列请求请求参数路径参数参数类型状态描述必需要查询的会话成功响应操作成功你好介绍一下你自己你好我是一个由驱动的大语言模型响应数据字段说明字段类型描述消息的数据库主键所属会话的消息的文本内容消息角色值为或消息的创建时间戳格式删除指定会话根据提供的删除该会话的所有相关聊天记录请求请求参数路径参数参数类型状态描述必需要删除的会话成功响应操作成功删除成功响应数据字段说明字段类型描述操作是否成功表示成功表示失败操作结果的文本描述信息",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-08 13:53:47",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#3-%E4%BC%9A%E8%AF%9D%E6%A0%B8%E5%BF%83-API-%E6%B7%B1%E5%BA%A6%E8%A7%A3%E6%9E%90"><span class="toc-text">3. 会话核心 API 深度解析</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-%E6%8F%AD%E5%BC%80%E9%BB%91%E7%9B%92%EF%BC%9AChatClient-%E7%9A%84%E5%8F%AF%E8%A7%82%E6%B5%8B%E6%80%A7"><span class="toc-text">3.1 揭开黑盒：ChatClient 的可观测性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-1-%E6%A0%B8%E5%BF%83%E5%88%A9%E5%99%A8-Advisor-%E4%B8%8E-SimpleLoggerAdvisor"><span class="toc-text">3.1.1 核心利器: Advisor 与 SimpleLoggerAdvisor</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-2-%E5%AE%9E%E6%88%98%EF%BC%9A%E4%B8%BA-AI-Copilot-%E5%BC%80%E5%90%AF%E8%B0%83%E8%AF%95%E6%97%A5%E5%BF%97"><span class="toc-text">3.1.2 实战：为 AI-Copilot 开启调试日志</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-3-%E8%A7%A3%E8%AF%BB%E6%97%A5%E5%BF%97%E8%BE%93%E5%87%BA"><span class="toc-text">3.1.3 解读日志输出</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-4-ChatClient-%E8%AF%A6%E8%A7%A3-%E9%87%8D%E7%82%B9%E5%86%85%E5%AE%B9"><span class="toc-text">**3.1.4 ChatClient 详解(重点内容) **</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-%E8%B5%8B%E4%BA%88AI%E8%AE%B0%E5%BF%86%EF%BC%9AChatMemory-%E6%B7%B1%E5%BA%A6%E5%AE%9E%E8%B7%B5"><span class="toc-text">3.2 赋予AI记忆：ChatMemory 深度实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#3-2-1-%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1%EF%BC%9A%E7%AD%96%E7%95%A5%E4%B8%8E%E5%AD%98%E5%82%A8%E5%88%86%E7%A6%BB"><span class="toc-text">3.2.1 核心设计：策略与存储分离</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-2-2-%E8%AE%B0%E5%BF%86%E7%AD%96%E7%95%A5%E4%B8%8E%E5%AD%98%E5%82%A8%E8%AF%A6%E8%A7%A3"><span class="toc-text">3.2.2 记忆策略与存储详解</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-2-3-%E5%AE%9E%E6%88%98%EF%BC%9A%E4%B8%BA-AI-Copilot-%E5%AE%9E%E7%8E%B0%E5%A4%9A%E8%BD%AE%E5%AF%B9%E8%AF%9D"><span class="toc-text">3.2.3 实战：为 AI-Copilot 实现多轮对话</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-%E7%AE%A1%E7%90%86%E9%95%BF%E6%9C%9F%E8%AE%B0%E5%BF%86%EF%BC%9A%E5%AE%9E%E7%8E%B0%E4%BC%9A%E8%AF%9D%E5%8E%86%E5%8F%B2%E6%9C%8D%E5%8A%A1"><span class="toc-text">3.3 管理长期记忆：实现会话历史服务</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#3-3-1-%E5%90%8E%E7%AB%AF%E6%9C%8D%E5%8A%A1%E6%90%AD%E5%BB%BA%EF%BC%9A%E5%9F%BA%E4%BA%8E-MyBatis-Plus"><span class="toc-text">3.3.1 后端服务搭建：基于 MyBatis-Plus</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-3-2-API-%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3"><span class="toc-text">3.3.2 API 接口文档</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java微服务篇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">SpringAI（三）：3. 会话核心 API 深度解析</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-03-20T14:13:45.000Z" title="发表于 2025-03-20 22:13:45">2025-03-20</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:47.166Z" title="更新于 2025-07-08 13:53:47">2025-07-08</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">7.8k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>33分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="SpringAI（三）：3. 会话核心 API 深度解析"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/52289.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/52289.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url">Java微服务篇</a><h1 id="CrawlerTitle" itemprop="name headline">SpringAI（三）：3. 会话核心 API 深度解析</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-03-20T14:13:45.000Z" title="发表于 2025-03-20 22:13:45">2025-03-20</time><time itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:47.166Z" title="更新于 2025-07-08 13:53:47">2025-07-08</time></header><div id="postchat_postcontent"><h2 id="3-会话核心-API-深度解析"><a href="#3-会话核心-API-深度解析" class="headerlink" title="3. 会话核心 API 深度解析"></a><strong>3. 会话核心 API 深度解析</strong></h2><p>在上一章，我们成功打通了前后端的任督二脉，实现了基础的聊天功能。但一个真正的智能应用，不能只有“一问一答”的瞬时反应，它还需要具备“反思”过去（可观测性）和“铭记”历史（对话记忆）的能力。本章，我们将深入 Spring AI 的两大核心机制，为我们的 AI-Copilot 赋予更深层次的智能。</p><h3 id="3-1-揭开黑盒：ChatClient-的可观测性"><a href="#3-1-揭开黑盒：ChatClient-的可观测性" class="headerlink" title="3.1 揭开黑盒：ChatClient 的可观测性"></a><strong>3.1 揭开黑盒：<code>ChatClient</code> 的可观测性</strong></h3><p>在进行任何复杂的系统开发时，可观测性（Observability）都是成功的基石。对于 AI 应用开发而言，这一点尤为重要。我们通过 <code>ChatClient</code> 的流畅API与大语言模型（LLM）交互，但这层优雅的抽象也可能成为一个“黑盒”。我们编写的 <code>.user("你好")</code>，在 Spring AI 框架内部，可能会被动态地与系统级指令、历史对话、甚至是函数调用（Function Calling）的定义组合在一起，形成一个远比我们想象中复杂的最终请求体。</p><p>当模型的响应与我们的预期出现偏差——例如，它没有遵循我们的系统指令，或者忘记了之前的对话内容——我们面临的第一个、也是最核心的难题便是：<strong>我的应用最终到底向 AI 发送了什么内容？</strong> 如果无法看清这个“黑盒”的内部，后续的 Prompt Engineering（提示词工程）、上下文管理优化、乃至错误排查都将无从谈起。本节将深入探讨 Spring AI 提供的核心调试工具，让我们能够点亮一盏灯，彻底照亮 <code>ChatClient</code> 的内部通信链路。</p><h4 id="3-1-1-核心利器-Advisor-与-SimpleLoggerAdvisor"><a href="#3-1-1-核心利器-Advisor-与-SimpleLoggerAdvisor" class="headerlink" title="3.1.1 核心利器: Advisor 与 SimpleLoggerAdvisor"></a><strong>3.1.1 核心利器: <code>Advisor</code> 与 <code>SimpleLoggerAdvisor</code></strong></h4><p>为了解决上述的可观测性问题，Spring AI 引入了名为 <strong><code>Advisor</code> (顾问)</strong> 的优雅设计模式。在软件工程领域，这与面向切面编程（AOP）中的“通知”（Advice）或网络编程中的“拦截器”（Interceptor）思想一脉相承。<code>Advisor</code> 允许我们在不侵入 <code>ChatClient</code> 核心逻辑的前提下，在其请求发送前和响应返回后“织入”我们自定义的横切关注点，如日志记录、指标监控等。</p><p>在众多 <code>Advisor</code> 的内置实现中，<code>SimpleLoggerAdvisor</code> 是我们进行开发和调试时最不可或缺的利器。它的核心作用可以简洁地概括如下：</p><table><thead><tr><th align="left">特性</th><th align="left">描述</th></tr></thead><tbody><tr><td align="left"><strong>核心职责</strong></td><td align="left">打印完整的<code>ChatRequest</code>和<code>ChatResponse</code></td></tr><tr><td align="left"><strong>工作模式</strong></td><td align="left">日志级别为<code>DEBUG</code>/<code>TRACE</code>时激活</td></tr><tr><td align="left"><strong>适用场景</strong></td><td align="left">开发调试、Prompt调优、上下文问题排查</td></tr><tr><td align="left"><strong>性质</strong></td><td align="left">只读、无侵入，不修改请求或响应</td></tr></tbody></table><p><code>Advisors</code> API 提供了一种灵活而强大的方法来拦截、修改和增强 Spring 应用程序中的 AI 驱动的交互。使用用户文本调用 AI 模型时，一种常见模式是使用上下文数据附加或增强提示。此上下文数据可以是不同的类型，常见的包括您自己的私有数据和对话历史记录。</p><p><code>SimpleLoggerAdvisor</code> 会记录 <code>ChatClient</code> 的 request 和 response 数据，这对于调试和监控 AI 交互非常有用。</p><h4 id="3-1-2-实战：为-AI-Copilot-开启调试日志"><a href="#3-1-2-实战：为-AI-Copilot-开启调试日志" class="headerlink" title="3.1.2 实战：为 AI-Copilot 开启调试日志"></a><strong>3.1.2 实战：为 AI-Copilot 开启调试日志</strong></h4><p>在 Spring Boot 的生态中，启用 <code>SimpleLoggerAdvisor</code> 的过程极其简单。在我们之前的做法中，通过使用 <code>@Slf4j</code> 这个注解进行精细化的日志控制，但这并不意味着它俩冲突。不过，仅在我们这一层，使用 <code>SimpleLoggerAdvisor</code> 的效果远比手动日志要来得好得多。</p><p><strong>1. 配置日志级别</strong></p><p>在 <code>src/main/resources/application.yml</code> 文件中添加 <code>logging.level</code> 配置，告诉日志系统我们关心 <code>advisor</code> 包下的 <code>DEBUG</code> 信息。</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># src/main/resources/application.yml</span></span><br><span class="line"><span class="attr">logging:</span></span><br><span class="line">  <span class="attr">level:</span></span><br><span class="line">    <span class="comment"># 关键：将 advisor 包的日志级别设置为 DEBUG。</span></span><br><span class="line">    <span class="comment"># 只有这样，SimpleLoggerAdvisor 内部的 isDebugEnabled() 判断才会为 true。</span></span><br><span class="line">    <span class="attr">org.springframework.ai.chat.client.advisor:</span> <span class="string">DEBUG</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 推荐：将我们自己应用的包也设为 DEBUG，方便观察完整调用链路。</span></span><br><span class="line">    <span class="attr">com.copilot.aicopilotbackend:</span> <span class="string">DEBUG</span></span><br></pre></td></tr></tbody></table></figure><p><strong>2. 在 <code>ChatClient</code> Bean 中应用顾问</strong></p><p>我们将 <code>SimpleLoggerAdvisor</code> 的应用，集中在 <code>config</code> 包中进行配置。</p><p><strong>创建 <code>AiConfig.java</code>:</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/config/AiConfig.java</span></span><br><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.config;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.ChatClient;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.model.ChatModel;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Bean;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Configuration;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Configuration</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">AiConfig</span> {</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 定义 ChatClient Bean，并为其配置默认的 Advisor。</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> chatModel Spring Boot 根据 application.yml 自动配置好的 ChatModel 实例</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 一个配置了日志顾问的 ChatClient 实例</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> ChatClient <span class="title function_">chatClient</span><span class="params">(ChatModel chatModel)</span> {</span><br><span class="line">        <span class="keyword">return</span> ChatClient.builder(chatModel)</span><br><span class="line">                .defaultAdvisors(<span class="keyword">new</span> <span class="title class_">SimpleLoggerAdvisor</span>())</span><br><span class="line">                .build();</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>修改 <code>ChatService.java</code> 的构造函数:</strong><br>现在 <code>ChatService</code> 不再需要 <code>ChatClient.Builder</code>，而是直接注入我们配置好的 <code>ChatClient</code> Bean。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/service/ChatService.java</span></span><br><span class="line"><span class="meta">@Slf4j</span></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ChatService</span> {</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> ChatClient chatClient;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">ChatService</span><span class="params">(ChatClient chatClient)</span> {</span><br><span class="line">        <span class="built_in">this</span>.chatClient = chatClient;</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">    <span class="comment">// ... 其他方法</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="3-1-3-解读日志输出"><a href="#3-1-3-解读日志输出" class="headerlink" title="3.1.3 解读日志输出"></a><strong>3.1.3 解读日志输出</strong></h4><p>完成了以上配置并重启应用后，当我们调用任何一个聊天API接口时，控制台中将自动出现详尽的调试信息：</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line">2025-06-26 18:30:00.123 DEBUG 12345 --- [nio-8080-exec-1] o.s.a.c.c.a.SimpleLoggerAdvisor       :</span><br><span class="line">--- Request:</span><br><span class="line">[</span><br><span class="line">  {</span><br><span class="line">    "messageType": "USER",</span><br><span class="line">    "content": "你好"</span><br><span class="line">  }</span><br><span class="line">]</span><br><span class="line">--- Response:</span><br><span class="line">{</span><br><span class="line">  "messageType": "ASSISTANT",</span><br><span class="line">  "content": "你好！我是AI-Copilot，很高兴能为您服务。"</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p>这段日志是极其宝贵的调试信息，它清晰地展示了发送给大语言模型API的<strong>最终请求体</strong>和模型返回的<strong>原始响应</strong>，让我们能彻底洞察“黑盒”内部，为后续的 Prompt 调优和问题排查提供了坚实的基础。</p><h4 id="3-1-4-ChatClient-详解-重点内容"><a href="#3-1-4-ChatClient-详解-重点内容" class="headerlink" title="**3.1.4 ChatClient 详解(重点内容) **"></a>**3.1.4 <code>ChatClient</code> 详解(重点内容) **</h4><p><code>ChatClient</code> 提供用于与 AI 模型通信的 Fluent API，支持同步和流式编程模型。</p><p><strong>1. 创建 <code>ChatClient</code></strong></p><p><code>ChatClient</code> 是使用 <code>ChatClient.Builder</code> 对象创建的。您可以获取自动配置的 <code>ChatClient.Builder</code> 实例，或者以编程方式创建一个。</p><p>在最简单的用例中， Spring AI 提供 Spring Boot 自动配置，创建一个原型 <code>ChatClient.Builder</code> bean，以便注入到你的类中。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">MyController</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> ChatClient chatClient;</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">MyController</span><span class="params">(ChatClient.Builder chatClientBuilder)</span> {</span><br><span class="line">        <span class="built_in">this</span>.chatClient = chatClientBuilder.build();</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">    <span class="meta">@GetMapping("/ai")</span></span><br><span class="line">    String <span class="title function_">generation</span><span class="params">(String userInput)</span> {</span><br><span class="line">        <span class="keyword">return</span> <span class="built_in">this</span>.chatClient.prompt()</span><br><span class="line">            .user(userInput)</span><br><span class="line">            .call()</span><br><span class="line">            .content();</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><blockquote><p>注意：我们项目中采用的是将 <code>ChatClient</code> 本身注册为 Bean 的方式，如 <code>3.1.2</code> 节所示，这在管理默认配置时更为方便。</p></blockquote><p><code>ChatClient</code> 是 Spring AI 中进行大模型交互的核心，它提供了一个优雅的流式（Fluent）API。在我们的 AI-Copilot 项目中，我们没有在业务代码（如Controller或Service）中临时创建 <code>ChatClient</code> 实例，而是采用了一种更强大、更可维护的模式：<strong>在 <code>AiConfig</code> 中将其配置为单例 Bean</strong>。</p><p>这种方式的好处是显而易见的：</p><ul><li><strong>集中配置</strong>：所有默认行为，如模型选项（temperature、top_p等）、系统提示、以及最重要的 <code>Advisors</code>（顾问），都在一个地方统一配置。</li><li><strong>简化业务</strong>：业务代码只需要注入配置好的 <code>ChatClient</code> Bean，无需关心其复杂的构建过程，可以直接调用其功能。</li><li><strong>一致性</strong>：确保整个应用的所有AI交互都遵循相同的基本配置和增强逻辑（如日志和对话记忆）。</li></ul><p>下面，我们来详细解析在项目中是如何运用 <code>ChatClient</code> 的流式API的。</p><p><strong>1. 启动调用链 (<code>.prompt()</code>)</strong></p><p>所有交互都始于 <code>.prompt()</code> 方法。这是一个无参数的方法，它会返回一个调用链的起点，允许我们后续构建请求。</p><p><strong>2. 构建Prompt内容 (<code>.user()</code>, <code>.system()</code>)</strong></p><ul><li><code>.user(String message)</code>: 这是最常用的方法，用于设置用户的提问内容。在我们的 <code>ChatService</code> 中，我们正是用它来传递用户的输入。</li><li><code>.system(String message)</code>: 用于设置系统级指令，引导AI的行为和角色。虽然我们在最终代码中是通过 <code>defaultAdvisors</code> 来管理上下文，但在需要临时改变AI角色的场景下，可以直接在调用链中使用此方法。</li></ul><p><strong>3. 增强请求 (<code>.advisors()</code>)</strong></p><p>这是 <code>ChatClient</code> 最强大的功能之一。在我们的项目中，<code>Advisor</code> 是实现对话记忆的关键。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 来自于我们最终的 ChatService.java</span></span><br><span class="line">.advisors(a -&gt; a.param(ChatMemory.CONVERSATION_ID, conversationId))</span><br></pre></td></tr></tbody></table></figure><p>这段代码的含义是：</p><ul><li><code>.advisors(...)</code> 方法接收一个 <code>Consumer&lt;AdvisorSpec&gt;</code> Lambda表达式，允许我们对本次请求的 <code>Advisors</code> 进行配置。</li><li><code>a.param(key, value)</code> 用于向 <code>Advisor</code> 的执行上下文传递参数。</li><li>在这里，我们将 <code>conversationId</code> 以 <code>ChatMemory.CONVERSATION_ID</code> 为键传入。我们之前在 <code>AiConfig</code> 中配置的 <code>MessageChatMemoryAdvisor</code> 会自动捕获这个参数，并根据它来查找对应的历史消息，然后将这些历史消息注入到最终发送给AI的 <code>Prompt</code> 中。</li></ul><p><strong>4. 执行与响应处理 (<code>.stream().content()</code>)</strong></p><p><code>ChatClient</code> 支持两种主要的执行模式：</p><ul><li><strong>同步调用</strong>: <code>.call()</code> 会阻塞并等待AI返回完整响应。它适用于不需要实时反馈的场景。</li><li><strong>流式调用</strong>: <code>.stream()</code> 会立即返回一个 <code>Flux</code> (来自响应式编程库 Project Reactor)，AI的响应会以数据块的形式持续不断地推送过来。</li></ul><p>为了提供最佳的用户体验，我们的项目<strong>完全采用了流式调用</strong>。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 来自于我们最终的 ChatService.java</span></span><br><span class="line">.stream()  <span class="comment">// 1. 声明我们想要一个流式响应</span></span><br><span class="line">.content() <span class="comment">// 2. 我们只关心响应流中的文本内容 (Flux&lt;String&gt;)</span></span><br></pre></td></tr></tbody></table></figure><p>组合起来，<code>chatClient.prompt()...stream().content()</code> 就构成了一个完整的、具备对话记忆能力的流式聊天请求。</p><p><strong>5. 返回结构化数据 (<code>.entity()</code>)</strong></p><p>虽然我们的聊天功能主要处理文本流，但 <code>ChatClient</code> 还能将AI的响应直接转换为Java对象（POJO），这在需要AI生成特定JSON结构的场景下极为有用。</p><p>假设我们想让AI生成一个演员的电影列表，我们可以这样定义一个<code>record</code>：</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">ActorFilms</span><span class="params">(String actor, List&lt;String&gt; movies)</span> {}</span><br></pre></td></tr></tbody></table></figure><p>然后，我们可以这样调用：</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="type">ActorFilms</span> <span class="variable">actorFilms</span> <span class="operator">=</span> chatClient.prompt()</span><br><span class="line">    .user(<span class="string">"请生成演员汤姆·汉克斯的电影作品列表，以JSON格式返回，包含 actor 和 movies 两个字段。"</span>)</span><br><span class="line">    .call() <span class="comment">// 注意：通常结构化数据需要等待完整响应，因此使用 .call()</span></span><br><span class="line">    .entity(ActorFilms.class);</span><br></pre></td></tr></tbody></table></figure><p><code>ChatClient</code> 会自动提示AI按指定格式输出，并将返回的JSON字符串反序列化为 <code>ActorFilms</code> 对象。对于泛型，用法也同样直观：</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line">List&lt;ActorFilms&gt; actorFilms = chatClient.prompt()</span><br><span class="line">    .user(<span class="string">"请为汤姆·汉克斯和比尔·默里生成电影作品列表..."</span>)</span><br><span class="line">    .call()</span><br><span class="line">    .entity(<span class="keyword">new</span> <span class="title class_">ParameterizedTypeReference</span>&lt;List&lt;ActorFilms&gt;&gt;() {});</span><br></pre></td></tr></tbody></table></figure><p><strong>6. 配置模型参数 (<code>.options()</code>)</strong></p><p>我们可以在请求级别覆盖默认的模型参数（如 <code>temperature</code>）。<code>OpenAiChatOptions</code> 的最新语法使用直接的 <code>.</code> 方法进行链式构建。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 这是一个示例，展示如何在单次请求中设置参数</span></span><br><span class="line"><span class="type">String</span> <span class="variable">creativeResponse</span> <span class="operator">=</span> chatClient.prompt()</span><br><span class="line">    .user(<span class="string">"写一首关于宇宙的诗"</span>)</span><br><span class="line">    .options(OpenAiChatOptions.builder()</span><br><span class="line">        .model(<span class="string">"gpt-4o"</span>)</span><br><span class="line">        .temperature(<span class="number">0.9f</span>) <span class="comment">// 设置更高的温度以获得更有创意的回答</span></span><br><span class="line">        .build()</span><br><span class="line">    )</span><br><span class="line">    .call()</span><br><span class="line">    .content();</span><br></pre></td></tr></tbody></table></figure><p>当然，最佳实践依然是在 <code>AiConfig</code> 中通过 <code>.defaultOptions()</code> 设置全局默认值，只在需要时进行局部覆盖。</p><p>通过这样重构，本节内容现在完全与项目代码保持一致，清晰地解释了我们所使用的每一个API背后的原理和目的。</p><h3 id="3-2-赋予AI记忆：ChatMemory-深度实践"><a href="#3-2-赋予AI记忆：ChatMemory-深度实践" class="headerlink" title="3.2 赋予AI记忆：ChatMemory 深度实践"></a><strong>3.2 赋予AI记忆：<code>ChatMemory</code> 深度实践</strong></h3><p>大语言模型（LLM）的API接口遵循HTTP协议，其核心特性之一就是<strong>无状态（Stateless）</strong>。每一次API调用都是一次全新的、独立的对话。这种“金鱼记忆”显然无法满足构建一个能持续对话的智能应用的需求。</p><p>为了解决这一核心痛点，Spring AI 提供了强大而灵活的<code>ChatMemory</code>功能。</p><h4 id="3-2-1-核心设计：策略与存储分离"><a href="#3-2-1-核心设计：策略与存储分离" class="headerlink" title="3.2.1 核心设计：策略与存储分离"></a><strong>3.2.1 核心设计：策略与存储分离</strong></h4><p>Spring AI在对话记忆功能上的核心设计思想，是软件工程中“关注点分离”原则的经典体现：<strong>将记忆的策略（如何记住）与记忆的存储（记在哪里）相分离</strong>。</p><p>这一思想通过两个核心接口得以实现：</p><ol><li><strong><code>ChatMemory</code> (策略接口)</strong>: 它定义了记忆的<strong>行为和策略</strong>。例如，它决定当对话历史过长时，应该保留哪些消息、遗忘哪些消息。</li><li><strong><code>ChatMemoryRepository</code> (存储接口)</strong>: 它定义了记忆的<strong>物理存储和检索</strong>。它的职责非常纯粹，就是在后端（如内存、数据库、Redis）存取<code>Message</code>数据。</li></ol><p>我们必须严格辨析两个极易混淆的概念：</p><table><thead><tr><th align="left">概念</th><th align="left">定义与范围</th><th align="left">目的与用途</th></tr></thead><tbody><tr><td align="left"><strong>对话记忆 (Chat Memory)</strong></td><td align="left">用于构建下一次Prompt的、一个相关的、有限的对话历史<strong>子集</strong>。</td><td align="left"><strong>为AI服务</strong>，让AI理解上下文，进行连贯对话。</td></tr><tr><td align="left"><strong>对话记录 (Chat History)</strong></td><td align="left">一次会话中<strong>全部、完整的</strong>消息交换历史。</td><td align="left"><strong>为应用和用户服务</strong>，用于审计、回溯、查看历史。</td></tr></tbody></table><p><code>ChatMemory</code> 抽象旨在管理<em>对话记忆</em>，而不是完整的<em>对话记录</em>。如果您需要维护所有交换消息的完整记录，应考虑使用不同的方法，比如我们稍后将介绍的基于 <code>MyBatis-Plus</code> 的方案。</p><h4 id="3-2-2-记忆策略与存储详解"><a href="#3-2-2-记忆策略与存储详解" class="headerlink" title="3.2.2 记忆策略与存储详解"></a><strong>3.2.2 记忆策略与存储详解</strong></h4><p><strong>1. 记忆策略 - <code>MessageWindowChatMemory</code></strong></p><p>这是 Spring AI中最常用、也是默认的记忆策略。它实现了一种高效的“滑动窗口”机制，只保留最近的 N 条消息作为上下文，在成本、性能和相关性之间取得了最佳平衡。当消息数量超过此限制时，将驱逐较旧的消息，但会保留系统消息。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="type">MessageWindowChatMemory</span> <span class="variable">memory</span> <span class="operator">=</span> MessageWindowChatMemory.builder()</span><br><span class="line">	.maxMessages(<span class="number">20</span>) <span class="comment">// 设置窗口大小为 20 条消息</span></span><br><span class="line">    .build();</span><br></pre></td></tr></tbody></table></figure><p><strong>2. 记忆存储 - <code>ChatMemoryRepository</code></strong></p><p>它负责将对话消息进行物理存储。Spring AI 提供了多种内置实现。</p><table><thead><tr><th align="left">实现类</th><th align="left">存储介质</th><th align="left">优点</th><th align="left">缺点</th><th align="left">适用场景</th></tr></thead><tbody><tr><td align="left"><code>InMemory...Repository</code></td><td align="left">JVM 内存</td><td align="left">零配置, 极速</td><td align="left">数据易失</td><td align="left">开发, 测试, 原型</td></tr><tr><td align="left"><code>Jdbc...Repository</code></td><td align="left">关系型数据库</td><td align="left">可靠, 持久化</td><td align="left">需配置</td><td align="left"><strong>生产环境</strong></td></tr><tr><td align="left"><code>Cassandra...Repository</code></td><td align="left">Cassandra</td><td align="left">高可用, 高扩展性</td><td align="left">配置复杂</td><td align="left">大规模分布式系统</td></tr><tr><td align="left"><code>Neo4j...Repository</code></td><td align="left">Neo4j图数据库</td><td align="left">利用图关系</td><td align="left">需图数据库知识</td><td align="left">需要分析对话关系</td></tr></tbody></table><p>在我们的项目中，我们将采用 <code>JdbcChatMemoryRepository</code> 实现生产级的持久化记忆。</p><h4 id="3-2-3-实战：为-AI-Copilot-实现多轮对话"><a href="#3-2-3-实战：为-AI-Copilot-实现多轮对话" class="headerlink" title="3.2.3 实战：为 AI-Copilot 实现多轮对话"></a><strong>3.2.3 实战：为 AI-Copilot 实现多轮对话</strong></h4><p>我们将使用 <code>JdbcChatMemoryRepository</code> 为我们的应用添加持久化的多轮对话能力。</p><p><strong>1. 添加数据库依赖 (<code>pom.xml</code>)</strong></p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-starter-model-chat-memory-repository-jdbc<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure><p><strong>2. 配置数据库与 <code>application.yml</code></strong></p><ul><li><p><strong>执行 DDL</strong>: 请在您的 MySQL 数据库中执行以下 SQL 来创建所需的表。</p><figure class="highlight sql"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">CREATE TABLE</span> IF <span class="keyword">NOT</span> <span class="keyword">EXISTS</span> `spring_ai_chat_memory` (</span><br><span class="line">  `id` <span class="type">BIGINT</span> <span class="keyword">NOT NULL</span> AUTO_INCREMENT,</span><br><span class="line">  `conversation_id` <span class="type">VARCHAR</span>(<span class="number">255</span>) <span class="keyword">NOT NULL</span>,</span><br><span class="line">  `content` TEXT <span class="keyword">NOT NULL</span>,</span><br><span class="line">  `type` <span class="type">VARCHAR</span>(<span class="number">50</span>) <span class="keyword">NOT NULL</span>,</span><br><span class="line">  `<span class="type">timestamp</span>` <span class="type">TIMESTAMP</span> <span class="keyword">NULL</span> <span class="keyword">DEFAULT</span> <span class="built_in">CURRENT_TIMESTAMP</span>,</span><br><span class="line">  `media` JSON <span class="keyword">DEFAULT</span> <span class="keyword">NULL</span>,</span><br><span class="line">  `metadata` JSON <span class="keyword">DEFAULT</span> <span class="keyword">NULL</span>,</span><br><span class="line">  <span class="keyword">PRIMARY KEY</span> (`id`),</span><br><span class="line">  KEY `idx_conversation_id` (`conversation_id`)</span><br><span class="line">);</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>修改 <code>application.yml</code></strong>:</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">spring:</span></span><br><span class="line">  <span class="attr">ai:</span></span><br><span class="line">    <span class="attr">chat:</span></span><br><span class="line">      <span class="attr">memory:</span></span><br><span class="line">        <span class="attr">repository:</span></span><br><span class="line">          <span class="attr">jdbc:</span></span><br><span class="line">            <span class="attr">initialize-schema:</span> <span class="string">never</span></span><br><span class="line">            <span class="attr">platform:</span> <span class="string">mysql</span></span><br><span class="line">  <span class="attr">datasource:</span></span><br><span class="line">    <span class="attr">url:</span> <span class="string">**********************************************************************************************************</span></span><br><span class="line">    <span class="attr">username:</span> <span class="string">root</span></span><br><span class="line">    <span class="attr">password:</span> <span class="string">root</span></span><br><span class="line">    <span class="attr">driver-class-name:</span> <span class="string">com.mysql.cj.jdbc.Driver</span></span><br></pre></td></tr></tbody></table></figure></li></ul><p><strong>3. 在 <code>AiConfig</code> 中配置记忆功能</strong></p><p>我们将 <code>ChatMemory</code> 的创建和 <code>MessageChatMemoryAdvisor</code> 的应用，全部集中在 <code>AiConfig</code> 配置类中。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.config;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.ChatClient;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.memory.ChatMemory;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.memory.MessageWindowChatMemory;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.model.ChatModel;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Bean;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Configuration;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Configuration</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">AiConfig</span> {</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 定义 ChatClient Bean，并为其配置默认的 Advisor。</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> chatModel Spring Boot 根据 application.yml 自动配置好的 ChatModel 实例</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> chatMemory 聊天内存实例，用于维护对话上下文</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 一个配置了日志顾问和内存顾问的 ChatClient 实例</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> ChatClient <span class="title function_">chatClient</span><span class="params">(ChatModel chatModel, ChatMemory chatMemory)</span> {</span><br><span class="line">        <span class="keyword">return</span> ChatClient.builder(chatModel)</span><br><span class="line">                .defaultAdvisors(</span><br><span class="line">                        <span class="keyword">new</span> <span class="title class_">SimpleLoggerAdvisor</span>(),</span><br><span class="line">                        MessageChatMemoryAdvisor.builder(chatMemory).build()</span><br><span class="line">                )</span><br><span class="line">                .build();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * Spring AI 会自动配置 ChatMemory Bean</span></span><br><span class="line"><span class="comment">     * 当检测到 JDBC 依赖时会自动使用 JDBC 存储库</span></span><br><span class="line"><span class="comment">     * 这里不需要手动配置，依赖自动配置机制</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">}</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>自动配置说明</strong>: Spring AI 会自动检测到 <code>spring-ai-starter-model-chat-memory-repository-jdbc</code> 依赖和 <code>application.yml</code> 中的配置，并自动创建一个 <code>JdbcChatMemoryRepository</code> 的 Bean。我们的 <code>chatMemory()</code> 方法会自动使用这个 Bean 作为 <code>MessageWindowChatMemory</code> 的后端存储。</p></blockquote><blockquote><p><strong>顾问链顺序</strong>: 将 advisor 添加到链中的顺序至关重要。<code>ChatClient.builder().advisors(advisor1, advisor2)</code> 中，<code>advisor1</code> 会先执行。</p></blockquote><p><strong>4. 改造后端业务代码以支持会话</strong></p><ul><li><p><strong>改造<code>ChatRequest</code></strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.dto.request;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.fasterxml.jackson.annotation.JsonProperty;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.UUID;</span><br><span class="line"></span><br><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 聊天请求DTO</span></span><br><span class="line"><span class="comment"> * <span class="doctag">@param</span> message 用户消息内容</span></span><br><span class="line"><span class="comment"> * <span class="doctag">@param</span> conversationId 会话ID，可选参数，为空时自动生成</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">ChatRequest</span><span class="params">(</span></span><br><span class="line"><span class="params">        @JsonProperty("message")</span> String message,</span><br><span class="line">        <span class="meta">@JsonProperty("conversationId")</span> String conversationId,</span><br><span class="line">        <span class="meta">@JsonProperty("isDeepThink")</span> Boolean isDeepThink</span><br><span class="line">) {</span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 获取有效的会话ID</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 非空的会话ID</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">getEffectiveConversationId</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> conversationId != <span class="literal">null</span> ? conversationId : UUID.randomUUID().toString();</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><ul><li><p><strong>改造 <code>ChatService.java</code></strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/service/ChatService.java</span></span><br><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.exception.SystemException;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.exception.ErrorCode;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.validation.ChatMessageValidator;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.ChatClient;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.memory.ChatMemory;</span><br><span class="line"><span class="keyword">import</span> org.springframework.stereotype.Service;</span><br><span class="line"><span class="keyword">import</span> reactor.core.publisher.Flux;</span><br><span class="line"></span><br><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 聊天服务</span></span><br><span class="line"><span class="comment"> * 提供AI流式聊天功能</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ChatService</span> {</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> ChatClient chatClient;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">ChatService</span><span class="params">(ChatClient chatClient)</span> {</span><br><span class="line">        <span class="built_in">this</span>.chatClient = chatClient;</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 获取流式聊天响应</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> message 用户消息</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> conversationId 会话ID</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 流式响应</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="keyword">public</span> Flux&lt;String&gt; <span class="title function_">getStreamingChat</span><span class="params">(String message, String conversationId)</span> {</span><br><span class="line">        <span class="comment">// 验证消息</span></span><br><span class="line">        ChatMessageValidator.validateMessage(message);</span><br><span class="line">        </span><br><span class="line">        <span class="keyword">return</span> chatClient.prompt()</span><br><span class="line">                .user(message)</span><br><span class="line">            	<span class="comment">// 重点在这里，通过advisors注入会话记忆</span></span><br><span class="line">                .advisors(a -&gt; a.param(ChatMemory.CONVERSATION_ID, conversationId))</span><br><span class="line">                .stream()</span><br><span class="line">                .content()</span><br><span class="line">                .onErrorMap(e -&gt; <span class="keyword">new</span> <span class="title class_">SystemException</span>(ErrorCode.AI_SERVICE_UNAVAILABLE, </span><br><span class="line">                                                   <span class="string">"AI服务当前不可用，请稍后重试"</span>, e));</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>改造 <code>ChatController.java</code></strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.controller;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.request.ChatRequest;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.service.ChatService;</span><br><span class="line"><span class="keyword">import</span> com.fasterxml.jackson.databind.ObjectMapper;</span><br><span class="line"><span class="keyword">import</span> lombok.RequiredArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.http.MediaType;</span><br><span class="line"><span class="keyword">import</span> org.springframework.http.codec.ServerSentEvent;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.*;</span><br><span class="line"><span class="keyword">import</span> reactor.core.publisher.Flux;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.time.Duration;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="meta">@RequestMapping("/api/v1/chat")</span></span><br><span class="line"><span class="meta">@RequiredArgsConstructor</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ChatController</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> ChatService chatService;</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> <span class="type">ObjectMapper</span> <span class="variable">objectMapper</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">ObjectMapper</span>();</span><br><span class="line"></span><br><span class="line">    <span class="meta">@PostMapping(</span></span><br><span class="line"><span class="meta">            value = "/stream",</span></span><br><span class="line"><span class="meta">            consumes = MediaType.APPLICATION_JSON_VALUE,</span></span><br><span class="line"><span class="meta">            produces = MediaType.TEXT_EVENT_STREAM_VALUE</span></span><br><span class="line"><span class="meta">    )</span></span><br><span class="line">    <span class="keyword">public</span> Flux&lt;ServerSentEvent&lt;String&gt;&gt; <span class="title function_">stream</span><span class="params">(<span class="meta">@RequestBody</span> ChatRequest req)</span> {</span><br><span class="line">        <span class="keyword">return</span> chatService.getStreamingChat(req.message(), req.getEffectiveConversationId())</span><br><span class="line">                .map(content -&gt; {</span><br><span class="line">                    <span class="keyword">try</span> {</span><br><span class="line">                        <span class="comment">// 将AI内容包装成JSON格式：{"content": "实际内容"}</span></span><br><span class="line">                        <span class="type">String</span> <span class="variable">jsonContent</span> <span class="operator">=</span> objectMapper.writeValueAsString(</span><br><span class="line">                                Map.of(<span class="string">"content"</span>, content)</span><br><span class="line">                        );</span><br><span class="line">                        <span class="keyword">return</span> ServerSentEvent.&lt;String&gt;builder()</span><br><span class="line">                                .data(jsonContent)</span><br><span class="line">                                .build();</span><br><span class="line">                    } <span class="keyword">catch</span> (Exception e) {</span><br><span class="line">                        <span class="comment">// 如果JSON序列化失败，发送错误信息</span></span><br><span class="line">                        <span class="keyword">return</span> ServerSentEvent.&lt;String&gt;builder()</span><br><span class="line">                                .data(<span class="string">"{\"content\": \"\"}"</span>)</span><br><span class="line">                                .build();</span><br><span class="line">                    }</span><br><span class="line">                })</span><br><span class="line">                .concatWith(Flux.just(</span><br><span class="line">                        <span class="comment">// 发送结束信号</span></span><br><span class="line">                        ServerSentEvent.&lt;String&gt;builder()</span><br><span class="line">                                .data(<span class="string">"[DONE]"</span>)</span><br><span class="line">                                .build()</span><br><span class="line">                ));</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure></li></ul></li></ul><p><strong>5. 改造前端以管理和传递 <code>conversationId</code></strong></p><ul><li><p><strong>安装 <code>uuid</code> 库</strong>:</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">pnpm add uuid</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>改造 <code>src/apis/chatService.js</code></strong>:</p><figure class="highlight javascript"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/apis/chatService.js</span></span><br><span class="line"></span><br><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 获取流式聊天回复的Response对象</span></span><br><span class="line"><span class="comment"> * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">message</span> - 用户消息</span></span><br><span class="line"><span class="comment"> * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">conversationId</span> - 会话ID</span></span><br><span class="line"><span class="comment"> * <span class="doctag">@returns</span> {<span class="type">Promise&lt;Response&gt;</span>} 返回Response对象供useXStream处理</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"><span class="keyword">export</span> <span class="keyword">const</span> <span class="title function_">getStreamingChatResponse</span> = <span class="keyword">async</span> (<span class="params">message, conversationId</span>) =&gt; {</span><br><span class="line">    <span class="keyword">try</span> {</span><br><span class="line">        <span class="comment">// 使用原生fetch发送请求</span></span><br><span class="line">        <span class="keyword">const</span> response = <span class="keyword">await</span> <span class="title function_">fetch</span>(<span class="string">'http://localhost:8080/api/v1/chat/stream'</span>, {</span><br><span class="line">            <span class="attr">method</span>: <span class="string">'POST'</span>,</span><br><span class="line">            <span class="attr">headers</span>: {</span><br><span class="line">                <span class="string">'Content-Type'</span>: <span class="string">'application/json'</span>,</span><br><span class="line">            },</span><br><span class="line">            <span class="attr">body</span>: <span class="title class_">JSON</span>.<span class="title function_">stringify</span>({</span><br><span class="line">                message,</span><br><span class="line">                conversationId</span><br><span class="line">            })</span><br><span class="line">        });</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (!response.<span class="property">ok</span>) {</span><br><span class="line">            <span class="keyword">throw</span> <span class="keyword">new</span> <span class="title class_">Error</span>(<span class="string">`HTTP error! status: <span class="subst">${response.status}</span>`</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="keyword">return</span> response;</span><br><span class="line">    } <span class="keyword">catch</span> (error) {</span><br><span class="line">        <span class="variable language_">console</span>.<span class="title function_">error</span>(<span class="string">'流式聊天请求失败:'</span>, error);</span><br><span class="line">        <span class="keyword">throw</span> error;</span><br><span class="line">    }</span><br><span class="line">};</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>改造 <code>src/views/ChatView.vue</code></strong>:</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br><span class="line">197</span><br><span class="line">198</span><br><span class="line">199</span><br><span class="line">200</span><br><span class="line">201</span><br><span class="line">202</span><br><span class="line">203</span><br><span class="line">204</span><br><span class="line">205</span><br><span class="line">206</span><br><span class="line">207</span><br><span class="line">208</span><br><span class="line">209</span><br><span class="line">210</span><br><span class="line">211</span><br><span class="line">212</span><br><span class="line">213</span><br><span class="line">214</span><br><span class="line">215</span><br><span class="line">216</span><br><span class="line">217</span><br><span class="line">218</span><br><span class="line">219</span><br><span class="line">220</span><br><span class="line">221</span><br><span class="line">222</span><br><span class="line">223</span><br><span class="line">224</span><br><span class="line">225</span><br><span class="line">226</span><br><span class="line">227</span><br><span class="line">228</span><br><span class="line">229</span><br><span class="line">230</span><br><span class="line">231</span><br><span class="line">232</span><br><span class="line">233</span><br><span class="line">234</span><br></pre></td><td class="code"><pre><span class="line">    &lt;script setup lang="ts"&gt;</span><br><span class="line">    import { ref, nextTick, computed, watch } from 'vue';</span><br><span class="line">    import { v4 as uuidv4 } from 'uuid';</span><br><span class="line">    import { useXStream } from 'vue-element-plus-x';</span><br><span class="line">    import { getStreamingChatResponse } from '@/apis/chatService';</span><br><span class="line">import type { BubbleListItemProps } from 'vue-element-plus-x/types/BubbleList';</span><br><span class="line">    import ConversationManager from '../components/ConversationManager.vue';</span><br><span class="line">    import { useConversations } from '../composables/useConversations';</span><br><span class="line">    </span><br><span class="line">    // 使用 useXStream 钩子</span><br><span class="line">    const { startStream, cancel, data, error, isLoading } = useXStream();</span><br><span class="line">    </span><br><span class="line">    // 使用会话管理</span><br><span class="line">    const {</span><br><span class="line">      currentConversationId,</span><br><span class="line">      currentConversation,</span><br><span class="line">      currentMessages,</span><br><span class="line">      addMessage,</span><br><span class="line">      clearCurrentMessages</span><br><span class="line">    } = useConversations();</span><br><span class="line">    </span><br><span class="line">    // AI和用户头像</span><br><span class="line">    const avatarUser = 'https://bu.dusays.com/2025/06/16/684f747174bc3.webp';</span><br><span class="line">    const avatarAI = 'https://bu.dusays.com/2025/06/26/685cf1884034c.png';</span><br><span class="line">    </span><br><span class="line">    // 消息类型定义</span><br><span class="line">    type MessageType = BubbleListItemProps &amp; {</span><br><span class="line">      key: number;</span><br><span class="line">      role: 'user' | 'ai';</span><br><span class="line">      messageId: string;</span><br><span class="line">    };</span><br><span class="line">    </span><br><span class="line">    // 消息列表数据 - 符合BubbleList格式</span><br><span class="line">    const messages = ref&lt;MessageType[]&gt;([</span><br><span class="line">      {</span><br><span class="line">        key: 1,</span><br><span class="line">        role: 'ai',</span><br><span class="line">        messageId: uuidv4(),</span><br><span class="line">        placement: 'start',</span><br><span class="line">        content: '您好！我是您的 AI-Copilot，已准备就绪。',</span><br><span class="line">        avatar: avatarAI,</span><br><span class="line">        avatarSize: '48px',</span><br><span class="line">        isMarkdown: true,</span><br><span class="line">        shape: 'corner',</span><br><span class="line">        variant: 'filled'</span><br><span class="line">      }</span><br><span class="line">    ]);</span><br><span class="line">    </span><br><span class="line">    // 用户输入的消息内容</span><br><span class="line">    const inputMessage = ref('');</span><br><span class="line">    </span><br><span class="line">    // 计算属性 - 合并流式数据（纯函数）</span><br><span class="line">    const streamContent = computed(() =&gt; {</span><br><span class="line">      if (!data.value.length) return '';</span><br><span class="line">    </span><br><span class="line">      let text = '';</span><br><span class="line">      for (let index = 0; index &lt; data.value.length; index++) {</span><br><span class="line">        const chunk = data.value[index].data;</span><br><span class="line">    </span><br><span class="line">        // 跳过空数据或结束标识</span><br><span class="line">        if (!chunk || chunk === '[DONE]' || chunk.trim() === '') {</span><br><span class="line">          continue;</span><br><span class="line">        }</span><br><span class="line">    </span><br><span class="line">        try {</span><br><span class="line">          // 解析JSON格式：{"content": "实际内容"}</span><br><span class="line">          const parsed = JSON.parse(chunk);</span><br><span class="line">          const content = parsed.content;</span><br><span class="line">          if (content !== undefined &amp;&amp; content !== null &amp;&amp; content !== '') {</span><br><span class="line">            text += content;</span><br><span class="line">          }</span><br><span class="line">        } catch (error) {</span><br><span class="line">          // 如果解析失败，作为纯文本处理（向后兼容）</span><br><span class="line">          if (typeof chunk === 'string' &amp;&amp; chunk.trim() !== '') {</span><br><span class="line">            text += chunk;</span><br><span class="line">          }</span><br><span class="line">        }</span><br><span class="line">      }</span><br><span class="line">    </span><br><span class="line">      return text;</span><br><span class="line">    });</span><br><span class="line">    </span><br><span class="line">    // 监听流数据变化，实现真正的流式体验</span><br><span class="line">    watch(streamContent, (newContent) =&gt; {</span><br><span class="line">      if (newContent &amp;&amp; messages.value.length &gt; 0) {</span><br><span class="line">        const lastMessage = messages.value[messages.value.length - 1];</span><br><span class="line">        if (lastMessage.role === 'ai') {</span><br><span class="line">          // 流数据一到达就立即切换到流式显示模式</span><br><span class="line">          if (lastMessage.loading) {</span><br><span class="line">            lastMessage.loading = false; // 立即关闭loading</span><br><span class="line">            lastMessage.typing = true;   // 启用打字效果</span><br><span class="line">          }</span><br><span class="line">          lastMessage.content = newContent; // 实时更新内容</span><br><span class="line">        }</span><br><span class="line">      }</span><br><span class="line">    }, { immediate: true });</span><br><span class="line">    </span><br><span class="line">    /**</span><br><span class="line">     * 发送消息处理函数</span><br><span class="line">     * @param {string} message - 用户输入的消息内容</span><br><span class="line">     */</span><br><span class="line">    const handleSend = async (message) =&gt; {</span><br><span class="line">      // 验证消息有效性和是否正在加载中</span><br><span class="line">      if (!message.trim() || isLoading.value) return;</span><br><span class="line">    </span><br><span class="line">      // 添加用户消息到对话列表</span><br><span class="line">      const userMessage: MessageType = {</span><br><span class="line">        key: messages.value.length + 1,</span><br><span class="line">        role: 'user',</span><br><span class="line">        messageId: uuidv4(),</span><br><span class="line">        placement: 'end',</span><br><span class="line">        content: message.trim(),</span><br><span class="line">        avatar: avatarUser,</span><br><span class="line">        avatarSize: '32px',</span><br><span class="line">        shape: 'corner',</span><br><span class="line">        variant: 'filled' // 使用填充样式，在黑色背景下更清晰</span><br><span class="line">      };</span><br><span class="line">      messages.value.push(userMessage);</span><br><span class="line">    </span><br><span class="line">      // 立即添加AI思考状态气泡</span><br><span class="line">      const aiMessage: MessageType = {</span><br><span class="line">        key: messages.value.length + 1,</span><br><span class="line">        role: 'ai',</span><br><span class="line">        messageId: uuidv4(),</span><br><span class="line">        placement: 'start',</span><br><span class="line">        content: '正在思考中...',</span><br><span class="line">        avatar: avatarAI,</span><br><span class="line">        avatarSize: '48px',</span><br><span class="line">        isMarkdown: true,</span><br><span class="line">        shape: 'corner',</span><br><span class="line">        variant: 'filled',</span><br><span class="line">        loading: true, // 设置加载状态</span><br><span class="line">        typing: false</span><br><span class="line">      };</span><br><span class="line">      messages.value.push(aiMessage);</span><br><span class="line">    </span><br><span class="line">      // 清空输入框</span><br><span class="line">      inputMessage.value = '';</span><br><span class="line">    </span><br><span class="line">      try {</span><br><span class="line">        // 获取Response对象</span><br><span class="line">        const response = await getStreamingChatResponse(message, conversationId.value);</span><br><span class="line">        const readableStream = response.body!;</span><br><span class="line">    </span><br><span class="line">        // 使用useXStream处理SSE流数据（默认支持SSE协议）</span><br><span class="line">        await startStream({ readableStream });</span><br><span class="line">    </span><br><span class="line">        // 流结束后，停止打字效果</span><br><span class="line">        const lastAiMessage = messages.value[messages.value.length - 1];</span><br><span class="line">        if (lastAiMessage.role === 'ai') {</span><br><span class="line">          lastAiMessage.typing = false; // 停止打字效果</span><br><span class="line">          // loading 已在 watch 中处理，content 已实时更新</span><br><span class="line">        }</span><br><span class="line">    </span><br><span class="line">      } catch (err) {</span><br><span class="line">        console.error('流式请求失败:', err);</span><br><span class="line">    </span><br><span class="line">        // 更新最后一个AI消息为错误消息</span><br><span class="line">        const lastAiMessage = messages.value[messages.value.length - 1];</span><br><span class="line">        if (lastAiMessage.role === 'ai') {</span><br><span class="line">          lastAiMessage.content = '抱歉，服务暂时不可用，请稍后重试。';</span><br><span class="line">          lastAiMessage.loading = false;</span><br><span class="line">          lastAiMessage.typing = false;</span><br><span class="line">        }</span><br><span class="line">      }</span><br><span class="line">    };</span><br><span class="line">    </span><br><span class="line">    </span><br><span class="line">    /**</span><br><span class="line">     * 表单提交处理函数</span><br><span class="line">     * 当用户点击发送按钮或按下回车键时触发</span><br><span class="line">     */</span><br><span class="line">    const handleSubmit = () =&gt; {</span><br><span class="line">      if (inputMessage.value.trim()) {</span><br><span class="line">        handleSend(inputMessage.value);</span><br><span class="line">        // 清理输入框</span><br><span class="line">        inputMessage.value = '';</span><br><span class="line">      }</span><br><span class="line">    };</span><br><span class="line">    </span><br><span class="line">    /**</span><br><span class="line">     * 取消请求处理函数</span><br><span class="line">     * 用于中断当前AI请求（预留功能）</span><br><span class="line">     */</span><br><span class="line">    const handleCancel = () =&gt; {</span><br><span class="line">      // TODO: 实现请求取消功能</span><br><span class="line">      console.log('Cancel request');</span><br><span class="line">    };</span><br><span class="line">    </span><br><span class="line">    /**</span><br><span class="line">     * 录音状态变化处理函数</span><br><span class="line">     * @param {Object} event - 录音状态事件对象</span><br><span class="line">     */</span><br><span class="line">    const handleRecordingChange = (event) =&gt; {</span><br><span class="line">      console.log('Recording state changed:', event);</span><br><span class="line">    };</span><br><span class="line">    </span><br><span class="line">    /**</span><br><span class="line">     * 触发器事件处理函数</span><br><span class="line">     * 处理特殊命令触发（如 /help、/clear 等）</span><br><span class="line">     * @param {Object} event - 触发器事件对象</span><br><span class="line">     */</span><br><span class="line">    const handleTrigger = (event) =&gt; {</span><br><span class="line">      console.log('Trigger event:', event);</span><br><span class="line">    };</span><br><span class="line">    &lt;/script&gt;</span><br><span class="line">    </span><br><span class="line">    &lt;template&gt;</span><br><span class="line">      &lt;div class="flex flex-col h-full"&gt;</span><br><span class="line">        &lt;!-- 应用头部 --&gt;</span><br><span class="line">        &lt;header class="bg-white shadow-md p-4 text-center"&gt;</span><br><span class="line">          &lt;h1 class="text-xl font-bold text-primary"&gt;AI-Copilot&lt;/h1&gt;</span><br><span class="line">        &lt;/header&gt;</span><br><span class="line">    </span><br><span class="line">        &lt;!-- 聊天主体区域 --&gt;</span><br><span class="line">        &lt;main class="flex-1 overflow-hidden"&gt;</span><br><span class="line">          &lt;!-- 使用BubbleList组件 --&gt;</span><br><span class="line">          &lt;BubbleList :list="messages" max-height="100%" :always-show-scrollbar="false" :show-back-button="true"</span><br><span class="line">            :btn-loading="isLoading" btn-color="#409EFF" /&gt;</span><br><span class="line">        &lt;/main&gt;</span><br><span class="line">    </span><br><span class="line">        &lt;!-- 输入区域 --&gt;</span><br><span class="line">        &lt;footer class="p-4 bg-white"&gt;</span><br><span class="line">          &lt;!-- 消息发送组件 --&gt;</span><br><span class="line">          &lt;Sender v-model="inputMessage" placeholder="请输入您的问题..." :auto-size="{ minRows: 1, maxRows: 6 }" :read-only="false"</span><br><span class="line">            :disabled="false" :submitBtnDisabled="isLoading" :loading="isLoading" :clearable="true" :allowSpeech="true"</span><br><span class="line">            submitType="enter" :headerAnimationTimer="300" inputWidth="100%" variant="default" :showUpdown="true"</span><br><span class="line">            :inputStyle="{}" :triggerStrings="['/help', '/clear']" :triggerPopoverVisible="false"</span><br><span class="line">            triggerPopoverWidth="fit-content" triggerPopoverLeft="0px" :triggerPopoverOffset="8"</span><br><span class="line">            triggerPopoverPlacement="top-start" @submit="handleSubmit" @cancel="handleCancel"</span><br><span class="line">            @recordingChange="handleRecordingChange" @trigger="handleTrigger" /&gt;</span><br><span class="line">        &lt;/footer&gt;</span><br><span class="line">      &lt;/div&gt;</span><br><span class="line">    &lt;/template&gt;</span><br></pre></td></tr></tbody></table></figure></li></ul><p>重启应用并刷新前端页面，现在您的 AI-Copilot 已经拥有了基于数据库的持久化“记忆”！</p><h3 id="3-3-管理长期记忆：实现会话历史服务"><a href="#3-3-管理长期记忆：实现会话历史服务" class="headerlink" title="3.3 管理长期记忆：实现会话历史服务"></a><strong>3.3 管理长期记忆：实现会话历史服务</strong></h3><p><code>ChatMemory</code> 解决了 AI 的上下文问题，但作为应用开发者，我们还需要为用户提供查看、管理他们完整对话记录（Chat History）的功能。这需要我们直接操作 <code>spring_ai_chat_memory</code> 这张表。</p><h4 id="3-3-1-后端服务搭建：基于-MyBatis-Plus"><a href="#3-3-1-后端服务搭建：基于-MyBatis-Plus" class="headerlink" title="3.3.1 后端服务搭建：基于 MyBatis-Plus"></a><strong>3.3.1 后端服务搭建：基于 MyBatis-Plus</strong></h4><p>我们将按照标准三层架构，构建一套完整的、面向业务的会话历史管理服务。</p><p><strong>1. 依赖与配置</strong></p><ul><li><p><strong><code>pom.xml</code></strong>: 添加 MyBatis-Plus 依赖。</p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>com.baomidou<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>mybatis-plus-spring-boot3-starter<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">version</span>&gt;</span>3.5.7<span class="tag">&lt;/<span class="name">version</span>&gt;</span> </span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>主启动类</strong>: 添加 <code>@MapperScan</code> 注解。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">@SpringBootApplication</span></span><br><span class="line"><span class="meta">@MapperScan("com.copilot.aicopilotbackend.repository")</span> <span class="comment">// 指向 Mapper 接口包</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">AiCopilotBackendApplication</span> { <span class="comment">/* ... */</span> }</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong><code>application.yml</code></strong>: 添加 MyBatis-Plus 配置。</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">mybatis-plus:</span></span><br><span class="line">  <span class="attr">configuration:</span></span><br><span class="line">    <span class="attr">map-underscore-to-camel-case:</span> <span class="literal">true</span></span><br><span class="line">    <span class="attr">log-impl:</span> <span class="string">org.apache.ibatis.logging.stdout.StdOutImpl</span></span><br></pre></td></tr></tbody></table></figure></li></ul><p><strong>2. 数据访问层 (DAO)</strong></p><ul><li><p><strong><code>entity/ChatMemoryEntity.java</code></strong>: 创建用于 MyBatis-Plus 操作的实体类。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.entity;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.annotation.*;</span><br><span class="line"><span class="keyword">import</span> lombok.Data;</span><br><span class="line"><span class="keyword">import</span> java.time.LocalDateTime;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Data</span></span><br><span class="line"><span class="meta">@TableName("spring_ai_chat_memory")</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ChatMemoryEntity</span> {</span><br><span class="line">    <span class="meta">@TableId(value = "id", type = IdType.AUTO)</span></span><br><span class="line">    <span class="keyword">private</span> Long id;</span><br><span class="line">    <span class="meta">@TableField("conversation_id")</span></span><br><span class="line">    <span class="keyword">private</span> String conversationId;</span><br><span class="line">    <span class="meta">@TableField("content")</span></span><br><span class="line">    <span class="keyword">private</span> String content;</span><br><span class="line">    <span class="meta">@TableField("type")</span></span><br><span class="line">    <span class="keyword">private</span> String type;</span><br><span class="line">    <span class="meta">@TableField("timestamp")</span></span><br><span class="line">    <span class="keyword">private</span> LocalDateTime timestamp;</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong><code>repository/ChatMemoryMapper.java</code></strong>: 创建继承 <code>BaseMapper</code> 的 Mapper 接口。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.repository;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.core.mapper.BaseMapper;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.entity.ChatMemoryEntity;</span><br><span class="line"><span class="keyword">import</span> org.apache.ibatis.annotations.Mapper;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Mapper</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">interface</span> <span class="title class_">ChatMemoryMapper</span> <span class="keyword">extends</span> <span class="title class_">BaseMapper</span>&lt;ChatMemoryEntity&gt; {</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><p><strong>3. 业务逻辑层 (Service)</strong></p><ul><li><p><strong><code>service/IChatHistoryService.java</code></strong>: 定义服务接口。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.extension.service.IService;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.entity.ChatMemoryEntity;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">interface</span> <span class="title class_">IChatHistoryService</span> <span class="keyword">extends</span> <span class="title class_">IService</span>&lt;ChatMemoryEntity&gt; {</span><br><span class="line">    List&lt;Map&lt;String, Object&gt;&gt; <span class="title function_">getAllConversations</span><span class="params">()</span>;</span><br><span class="line">    List&lt;ChatMemoryEntity&gt; <span class="title function_">getChatHistory</span><span class="params">(String conversationId)</span>;</span><br><span class="line">    <span class="type">boolean</span> <span class="title function_">deleteChatHistory</span><span class="params">(String conversationId)</span>;</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong><code>service/impl/ChatHistoryServiceImpl.java</code></strong>: 实现服务接口。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service.impl;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;</span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.entity.ChatMemoryEntity;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.repository.ChatMemoryMapper;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.service.IChatHistoryService;</span><br><span class="line"><span class="keyword">import</span> org.springframework.stereotype.Service;</span><br><span class="line"><span class="keyword">import</span> java.time.LocalDateTime;</span><br><span class="line"><span class="keyword">import</span> java.util.HashMap;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Collectors;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ChatHistoryServiceImpl</span> <span class="keyword">extends</span> <span class="title class_">ServiceImpl</span>&lt;ChatMemoryMapper, ChatMemoryEntity&gt; <span class="keyword">implements</span> <span class="title class_">IChatHistoryService</span> {</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> List&lt;Map&lt;String, Object&gt;&gt; <span class="title function_">getAllConversations</span><span class="params">()</span> {</span><br><span class="line">        QueryWrapper&lt;ChatMemoryEntity&gt; idQueryWrapper = <span class="keyword">new</span> <span class="title class_">QueryWrapper</span>&lt;&gt;();</span><br><span class="line">        idQueryWrapper.select(<span class="string">"conversation_id"</span>, <span class="string">"timestamp"</span>).orderByDesc(<span class="string">"timestamp"</span>);</span><br><span class="line">        List&lt;ChatMemoryEntity&gt; allRecords = <span class="built_in">this</span>.list(idQueryWrapper);</span><br><span class="line"></span><br><span class="line">        List&lt;String&gt; distinctConvIds = allRecords.stream()</span><br><span class="line">                .map(ChatMemoryEntity::getConversationId)</span><br><span class="line">                .distinct()</span><br><span class="line">                .collect(Collectors.toList());</span><br><span class="line"></span><br><span class="line">        <span class="keyword">return</span> distinctConvIds.stream().map(id -&gt; {</span><br><span class="line">            Map&lt;String, Object&gt; conversationInfo = <span class="keyword">new</span> <span class="title class_">HashMap</span>&lt;&gt;();</span><br><span class="line">            conversationInfo.put(<span class="string">"id"</span>, id);</span><br><span class="line"></span><br><span class="line">            <span class="type">ChatMemoryEntity</span> <span class="variable">firstUserMessage</span> <span class="operator">=</span> <span class="built_in">this</span>.query()</span><br><span class="line">                    .eq(<span class="string">"conversation_id"</span>, id).eq(<span class="string">"type"</span>, <span class="string">"USER"</span>)</span><br><span class="line">                    .orderByAsc(<span class="string">"timestamp"</span>).last(<span class="string">"LIMIT 1"</span>).one();</span><br><span class="line">            <span class="type">String</span> <span class="variable">title</span> <span class="operator">=</span> (firstUserMessage != <span class="literal">null</span> &amp;&amp; firstUserMessage.getContent() != <span class="literal">null</span>)</span><br><span class="line">                    ? firstUserMessage.getContent().trim() : <span class="string">"新的会话"</span>;</span><br><span class="line">            conversationInfo.put(<span class="string">"label"</span>, title.length() &gt; <span class="number">20</span> ? title.substring(<span class="number">0</span>, <span class="number">20</span>) + <span class="string">"..."</span> : title);</span><br><span class="line"></span><br><span class="line">            <span class="type">ChatMemoryEntity</span> <span class="variable">lastMessage</span> <span class="operator">=</span> <span class="built_in">this</span>.query()</span><br><span class="line">                    .eq(<span class="string">"conversation_id"</span>, id).orderByDesc(<span class="string">"timestamp"</span>)</span><br><span class="line">                    .last(<span class="string">"LIMIT 1"</span>).one();</span><br><span class="line">            conversationInfo.put(<span class="string">"updatedAt"</span>, lastMessage != <span class="literal">null</span> ? lastMessage.getTimestamp() : LocalDateTime.now());</span><br><span class="line"></span><br><span class="line">            <span class="keyword">return</span> conversationInfo;</span><br><span class="line">        }).collect(Collectors.toList());</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> List&lt;ChatMemoryEntity&gt; <span class="title function_">getChatHistory</span><span class="params">(String conversationId)</span> {</span><br><span class="line">        QueryWrapper&lt;ChatMemoryEntity&gt; queryWrapper = <span class="keyword">new</span> <span class="title class_">QueryWrapper</span>&lt;&gt;();</span><br><span class="line">        queryWrapper.eq(<span class="string">"conversation_id"</span>, conversationId).orderByAsc(<span class="string">"timestamp"</span>);</span><br><span class="line">        <span class="keyword">return</span> <span class="built_in">this</span>.list(queryWrapper);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> <span class="type">boolean</span> <span class="title function_">deleteChatHistory</span><span class="params">(String conversationId)</span> {</span><br><span class="line">        QueryWrapper&lt;ChatMemoryEntity&gt; queryWrapper = <span class="keyword">new</span> <span class="title class_">QueryWrapper</span>&lt;&gt;();</span><br><span class="line">        queryWrapper.eq(<span class="string">"conversation_id"</span>, conversationId);</span><br><span class="line">        <span class="keyword">return</span> <span class="built_in">this</span>.remove(queryWrapper);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><p><strong>4. API 接口层 (Controller)</strong></p><ul><li><p><strong><code>controller/ChatHistoryController.java</code></strong>: 创建新的 Controller，暴露三个 API 端点。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.controller;</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">import</span> com.copilot.aicopilotbackend.dto.response.ApiResponse;</span><br><span class="line">    <span class="keyword">import</span> com.copilot.aicopilotbackend.entity.ChatMemoryEntity;</span><br><span class="line">    <span class="keyword">import</span> com.copilot.aicopilotbackend.service.IChatHistoryService;</span><br><span class="line">    <span class="keyword">import</span> lombok.RequiredArgsConstructor;</span><br><span class="line">    <span class="keyword">import</span> org.springframework.web.bind.annotation.*;</span><br><span class="line">    <span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line">    </span><br><span class="line">    <span class="meta">@RestController</span></span><br><span class="line">    <span class="meta">@RequestMapping("/api/v1/chat-history")</span></span><br><span class="line">    <span class="meta">@RequiredArgsConstructor</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ChatHistoryController</span> {</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> IChatHistoryService chatHistoryService;</span><br><span class="line">    </span><br><span class="line">        <span class="meta">@GetMapping("/conversations")</span></span><br><span class="line">        <span class="keyword">public</span> ApiResponse&lt;List&lt;Map&lt;String, Object&gt;&gt;&gt; <span class="title function_">getAllConversations</span><span class="params">()</span> {</span><br><span class="line">            <span class="keyword">return</span> ApiResponse.success(chatHistoryService.getAllConversations());</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">        <span class="meta">@GetMapping("/conversation/{conversationId}")</span></span><br><span class="line">        <span class="keyword">public</span> ApiResponse&lt;List&lt;ChatMemoryEntity&gt;&gt; <span class="title function_">getChatHistory</span><span class="params">(<span class="meta">@PathVariable</span> String conversationId)</span> {</span><br><span class="line">            <span class="keyword">return</span> ApiResponse.success(chatHistoryService.getChatHistory(conversationId));</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">        <span class="meta">@DeleteMapping("/conversation/{conversationId}")</span></span><br><span class="line">        <span class="keyword">public</span> ApiResponse&lt;Map&lt;String, Object&gt;&gt; <span class="title function_">deleteChatHistory</span><span class="params">(<span class="meta">@PathVariable</span> String conversationId)</span> {</span><br><span class="line">            <span class="type">boolean</span> <span class="variable">success</span> <span class="operator">=</span> chatHistoryService.deleteChatHistory(conversationId);</span><br><span class="line">            Map&lt;String, Object&gt; result = Map.of(</span><br><span class="line">                <span class="string">"success"</span>, success,</span><br><span class="line">                <span class="string">"message"</span>, success ? <span class="string">"删除成功"</span> : <span class="string">"删除失败或会话不存在"</span></span><br><span class="line">            );</span><br><span class="line">            <span class="keyword">return</span> ApiResponse.success(result);</span><br><span class="line">        }</span><br><span class="line">    }</span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="3-3-2-API-接口文档"><a href="#3-3-2-API-接口文档" class="headerlink" title="3.3.2 API 接口文档"></a><strong>3.3.2 API 接口文档</strong></h4><p>以下是为“会话历史管理”功能提供的后端RESTful API接口文档。</p><p><strong>基础URL</strong>: <code>http://localhost:8080/api/v1</code></p><hr><p><strong>1. 获取所有会话列表</strong></p><p>获取所有已存在的会话的概要信息列表，按最后更新时间倒序排列。</p><ul><li><strong>请求</strong>: <code>GET /chat-history/conversations</code></li><li><strong>请求参数</strong>: 无</li><li><strong>成功响应 (<code>200 OK</code>)</strong>:<figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">  <span class="attr">"code"</span><span class="punctuation">:</span> <span class="string">"00000"</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"message"</span><span class="punctuation">:</span> <span class="string">"操作成功"</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"data"</span><span class="punctuation">:</span> <span class="punctuation">[</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">      <span class="attr">"id"</span><span class="punctuation">:</span> <span class="string">"conv_1718985665_a1b2c3d4"</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"label"</span><span class="punctuation">:</span> <span class="string">"你好，介绍一下你自己"</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"updatedAt"</span><span class="punctuation">:</span> <span class="string">"2025-06-22T01:21:05.123456"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">      <span class="attr">"id"</span><span class="punctuation">:</span> <span class="string">"conv_1718985601_e5f6g7h8"</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"label"</span><span class="punctuation">:</span> <span class="string">"请用Java写一个快排..."</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"updatedAt"</span><span class="punctuation">:</span> <span class="string">"2025-06-22T01:20:01.789012"</span></span><br><span class="line">    <span class="punctuation">}</span></span><br><span class="line">  <span class="punctuation">]</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"timestamp"</span><span class="punctuation">:</span> <span class="string">"2025-06-27T10:00:00.000000"</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li><li><strong>响应数据字段说明</strong>:</li></ul><table><thead><tr><th align="left">字段</th><th align="left">类型</th><th align="left">描述</th></tr></thead><tbody><tr><td align="left"><code>id</code></td><td align="left"><code>String</code></td><td align="left">会话的唯一标识符。</td></tr><tr><td align="left"><code>label</code></td><td align="left"><code>String</code></td><td align="left">根据会话第一条用户消息生成的默认标题（最长20个字符）。</td></tr><tr><td align="left"><code>updatedAt</code></td><td align="left"><code>String</code></td><td align="left">会话的最后更新时间（ISO 8601格式）。</td></tr></tbody></table><hr><p><strong>2. 获取指定会话详情</strong></p><p>根据提供的<code>conversationId</code>，获取该会话的完整聊天记录，按消息时间升序排列。</p><ul><li><strong>请求</strong>: <code>GET /chat-history/conversation/{conversationId}</code></li><li><strong>请求参数</strong>:<ul><li><strong>路径参数</strong>:<table><thead><tr><th align="left">参数</th><th align="left">类型</th><th align="left">状态</th><th align="left">描述</th></tr></thead><tbody><tr><td align="left"><code>conversationId</code></td><td align="left"><code>String</code></td><td align="left"><strong>必需</strong></td><td align="left">要查询的会话ID。</td></tr></tbody></table></li></ul></li><li><strong>成功响应 (<code>200 OK</code>)</strong>:<figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">  <span class="attr">"code"</span><span class="punctuation">:</span> <span class="string">"00000"</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"message"</span><span class="punctuation">:</span> <span class="string">"操作成功"</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"data"</span><span class="punctuation">:</span> <span class="punctuation">[</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">      <span class="attr">"id"</span><span class="punctuation">:</span> <span class="number">101</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"conversationId"</span><span class="punctuation">:</span> <span class="string">"conv_1718985665_a1b2c3d4"</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"content"</span><span class="punctuation">:</span> <span class="string">"你好，介绍一下你自己"</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"type"</span><span class="punctuation">:</span> <span class="string">"USER"</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"timestamp"</span><span class="punctuation">:</span> <span class="string">"2025-06-22T01:21:00.123456"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">      <span class="attr">"id"</span><span class="punctuation">:</span> <span class="number">102</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"conversationId"</span><span class="punctuation">:</span> <span class="string">"conv_1718985665_a1b2c3d4"</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"content"</span><span class="punctuation">:</span> <span class="string">"你好！我是一个由Spring AI驱动的大语言模型..."</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"type"</span><span class="punctuation">:</span> <span class="string">"ASSISTANT"</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"timestamp"</span><span class="punctuation">:</span> <span class="string">"2025-06-22T01:21:05.123456"</span></span><br><span class="line">    <span class="punctuation">}</span></span><br><span class="line">  <span class="punctuation">]</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"timestamp"</span><span class="punctuation">:</span> <span class="string">"2025-06-27T10:01:00.000000"</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li><li><strong>响应数据字段说明</strong>:</li></ul><table><thead><tr><th align="left">字段</th><th align="left">类型</th><th align="left">描述</th></tr></thead><tbody><tr><td align="left"><code>id</code></td><td align="left"><code>Long</code></td><td align="left">消息的数据库主键ID。</td></tr><tr><td align="left"><code>conversationId</code></td><td align="left"><code>String</code></td><td align="left">所属会话的ID。</td></tr><tr><td align="left"><code>content</code></td><td align="left"><code>String</code></td><td align="left">消息的文本内容。</td></tr><tr><td align="left"><code>type</code></td><td align="left"><code>String</code></td><td align="left">消息角色，值为 <code>USER</code> 或 <code>ASSISTANT</code>。</td></tr><tr><td align="left"><code>timestamp</code></td><td align="left"><code>String</code></td><td align="left">消息的创建时间戳（ISO 8601格式）。</td></tr></tbody></table><hr><p><strong>3. 删除指定会话</strong></p><p>根据提供的<code>conversationId</code>，删除该会话的所有相关聊天记录。</p><ul><li><p><strong>请求</strong>: <code>DELETE /chat-history/conversation/{conversationId}</code></p></li><li><p><strong>请求参数</strong>:</p><ul><li><strong>路径参数</strong>:<table><thead><tr><th align="left">参数</th><th align="left">类型</th><th align="left">状态</th><th align="left">描述</th></tr></thead><tbody><tr><td align="left"><code>conversationId</code></td><td align="left"><code>String</code></td><td align="left"><strong>必需</strong></td><td align="left">要删除的会话ID。</td></tr></tbody></table></li></ul></li><li><p><strong>成功响应 (<code>200 OK</code>)</strong>:</p><figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">  <span class="attr">"code"</span><span class="punctuation">:</span> <span class="string">"00000"</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"message"</span><span class="punctuation">:</span> <span class="string">"操作成功"</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"data"</span><span class="punctuation">:</span> <span class="punctuation">{</span></span><br><span class="line">      <span class="attr">"success"</span><span class="punctuation">:</span> <span class="literal"><span class="keyword">true</span></span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"message"</span><span class="punctuation">:</span> <span class="string">"删除成功"</span></span><br><span class="line">  <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"timestamp"</span><span class="punctuation">:</span> <span class="string">"2025-06-27T10:02:00.000000"</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>响应数据字段说明</strong>:</p></li></ul><table><thead><tr><th align="left">字段</th><th align="left">类型</th><th align="left">描述</th></tr></thead><tbody><tr><td align="left"><code>success</code></td><td align="left"><code>Boolean</code></td><td align="left">操作是否成功。<code>true</code>表示成功，<code>false</code>表示失败。</td></tr><tr><td align="left"><code>message</code></td><td align="left"><code>String</code></td><td align="left">操作结果的文本描述信息。</td></tr></tbody></table><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/52289.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/52289.html&quot;)">SpringAI（三）：3. 会话核心 API 深度解析</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/52289.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=SpringAI（三）：3. 会话核心 API 深度解析&amp;url=https://prorise666.site/posts/52289.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java微服务篇<span class="tagsPageCount">11</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/18714.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用</div></div></a></div><div class="next-post pull-right"><a href="/posts/51850.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">SpringAI（四）：4. 深入 Prompt 工程与结构化输出</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/59358.html" title="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</div></div></a></div><div><a href="/posts/5770.html" title="SpringAI（七）：7. Embedding Models：万物皆可向量化"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（七）：7. Embedding Models：万物皆可向量化</div></div></a></div><div><a href="/posts/47912.html" title="SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</div></div></a></div><div><a href="/posts/60609.html" title="SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南</div></div></a></div><div><a href="/posts/22322.html" title="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</div></div></a></div><div><a href="/posts/29776.html" title="SpringAI（六）：6. AI 的创造力：集成文生图能力"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（六）：6. AI 的创造力：集成文生图能力</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData = {
  title: "SpringAI（三）：3. 会话核心 API 深度解析",
  date: "2025-03-20 22:13:45",
  updated: "2025-07-08 13:53:47",
  tags: ["Java微服务篇"],
  categories: ["后端技术","Java"],
  content: "\n## **3. 会话核心 API 深度解析**\n\n在上一章，我们成功打通了前后端的任督二脉，实现了基础的聊天功能。但一个真正的智能应用，不能只有“一问一答”的瞬时反应，它还需要具备“反思”过去（可观测性）和“铭记”历史（对话记忆）的能力。本章，我们将深入 Spring AI 的两大核心机制，为我们的 AI-Copilot 赋予更深层次的智能。\n\n### **3.1 揭开黑盒：`ChatClient` 的可观测性**\n\n在进行任何复杂的系统开发时，可观测性（Observability）都是成功的基石。对于 AI 应用开发而言，这一点尤为重要。我们通过 `ChatClient` 的流畅API与大语言模型（LLM）交互，但这层优雅的抽象也可能成为一个“黑盒”。我们编写的 `.user(\"你好\")`，在 Spring AI 框架内部，可能会被动态地与系统级指令、历史对话、甚至是函数调用（Function Calling）的定义组合在一起，形成一个远比我们想象中复杂的最终请求体。\n\n当模型的响应与我们的预期出现偏差——例如，它没有遵循我们的系统指令，或者忘记了之前的对话内容——我们面临的第一个、也是最核心的难题便是：**我的应用最终到底向 AI 发送了什么内容？** 如果无法看清这个“黑盒”的内部，后续的 Prompt Engineering（提示词工程）、上下文管理优化、乃至错误排查都将无从谈起。本节将深入探讨 Spring AI 提供的核心调试工具，让我们能够点亮一盏灯，彻底照亮 `ChatClient` 的内部通信链路。\n\n#### **3.1.1 核心利器: `Advisor` 与 `SimpleLoggerAdvisor`**\n\n为了解决上述的可观测性问题，Spring AI 引入了名为 **`Advisor` (顾问)** 的优雅设计模式。在软件工程领域，这与面向切面编程（AOP）中的“通知”（Advice）或网络编程中的“拦截器”（Interceptor）思想一脉相承。`Advisor` 允许我们在不侵入 `ChatClient` 核心逻辑的前提下，在其请求发送前和响应返回后“织入”我们自定义的横切关注点，如日志记录、指标监控等。\n\n在众多 `Advisor` 的内置实现中，`SimpleLoggerAdvisor` 是我们进行开发和调试时最不可或缺的利器。它的核心作用可以简洁地概括如下：\n\n| 特性 | 描述 |\n| :--- | :--- |\n| **核心职责** | 打印完整的`ChatRequest`和`ChatResponse` |\n| **工作模式** | 日志级别为`DEBUG`/`TRACE`时激活 |\n| **适用场景** | 开发调试、Prompt调优、上下文问题排查 |\n| **性质** | 只读、无侵入，不修改请求或响应 |\n\n`Advisors` API 提供了一种灵活而强大的方法来拦截、修改和增强 Spring 应用程序中的 AI 驱动的交互。使用用户文本调用 AI 模型时，一种常见模式是使用上下文数据附加或增强提示。此上下文数据可以是不同的类型，常见的包括您自己的私有数据和对话历史记录。\n\n`SimpleLoggerAdvisor` 会记录 `ChatClient` 的 request 和 response 数据，这对于调试和监控 AI 交互非常有用。\n\n#### **3.1.2 实战：为 AI-Copilot 开启调试日志**\n\n在 Spring Boot 的生态中，启用 `SimpleLoggerAdvisor` 的过程极其简单。在我们之前的做法中，通过使用 `@Slf4j` 这个注解进行精细化的日志控制，但这并不意味着它俩冲突。不过，仅在我们这一层，使用 `SimpleLoggerAdvisor` 的效果远比手动日志要来得好得多。\n\n**1. 配置日志级别**\n\n在 `src/main/resources/application.yml` 文件中添加 `logging.level` 配置，告诉日志系统我们关心 `advisor` 包下的 `DEBUG` 信息。\n\n```yaml\n# src/main/resources/application.yml\nlogging:\n  level:\n    # 关键：将 advisor 包的日志级别设置为 DEBUG。\n    # 只有这样，SimpleLoggerAdvisor 内部的 isDebugEnabled() 判断才会为 true。\n    org.springframework.ai.chat.client.advisor: DEBUG\n    \n    # 推荐：将我们自己应用的包也设为 DEBUG，方便观察完整调用链路。\n    com.copilot.aicopilotbackend: DEBUG\n```\n\n**2. 在 `ChatClient` Bean 中应用顾问**\n\n我们将 `SimpleLoggerAdvisor` 的应用，集中在 `config` 包中进行配置。\n\n**创建 `AiConfig.java`:**\n\n```java\n// src/main/java/com/copilot/aicopilotbackend/config/AiConfig.java\npackage com.copilot.aicopilotbackend.config;\n\nimport org.springframework.ai.chat.client.ChatClient;\nimport org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;\nimport org.springframework.ai.chat.model.ChatModel;\nimport org.springframework.context.annotation.Bean;\nimport org.springframework.context.annotation.Configuration;\n\n@Configuration\npublic class AiConfig {\n\n    /**\n     * 定义 ChatClient Bean，并为其配置默认的 Advisor。\n     * @param chatModel Spring Boot 根据 application.yml 自动配置好的 ChatModel 实例\n     * @return 一个配置了日志顾问的 ChatClient 实例\n     */\n    @Bean\n    public ChatClient chatClient(ChatModel chatModel) {\n        return ChatClient.builder(chatModel)\n                .defaultAdvisors(new SimpleLoggerAdvisor())\n                .build();\n    }\n}\n```\n\n**修改 `ChatService.java` 的构造函数:**\n现在 `ChatService` 不再需要 `ChatClient.Builder`，而是直接注入我们配置好的 `ChatClient` Bean。\n\n```java\n// src/main/java/com/copilot/aicopilotbackend/service/ChatService.java\n@Slf4j\n@Service\npublic class ChatService {\n    \n    private final ChatClient chatClient;\n\n    public ChatService(ChatClient chatClient) {\n        this.chatClient = chatClient;\n    }\n    \n    // ... 其他方法\n}\n```\n\n#### **3.1.3 解读日志输出**\n\n完成了以上配置并重启应用后，当我们调用任何一个聊天API接口时，控制台中将自动出现详尽的调试信息：\n\n```log\n2025-06-26 18:30:00.123 DEBUG 12345 --- [nio-8080-exec-1] o.s.a.c.c.a.SimpleLoggerAdvisor       :\n--- Request:\n[\n  {\n    \"messageType\": \"USER\",\n    \"content\": \"你好\"\n  }\n]\n--- Response:\n{\n  \"messageType\": \"ASSISTANT\",\n  \"content\": \"你好！我是AI-Copilot，很高兴能为您服务。\"\n}\n```\n\n这段日志是极其宝贵的调试信息，它清晰地展示了发送给大语言模型API的**最终请求体**和模型返回的**原始响应**，让我们能彻底洞察“黑盒”内部，为后续的 Prompt 调优和问题排查提供了坚实的基础。\n\n#### **3.1.4 `ChatClient` 详解(重点内容) **\n\n`ChatClient` 提供用于与 AI 模型通信的 Fluent API，支持同步和流式编程模型。\n\n**1. 创建 `ChatClient`**\n\n`ChatClient` 是使用 `ChatClient.Builder` 对象创建的。您可以获取自动配置的 `ChatClient.Builder` 实例，或者以编程方式创建一个。\n\n在最简单的用例中， Spring AI 提供 Spring Boot 自动配置，创建一个原型 `ChatClient.Builder` bean，以便注入到你的类中。\n\n```java\n@RestController\nclass MyController {\n\n    private final ChatClient chatClient;\n    \n    public MyController(ChatClient.Builder chatClientBuilder) {\n        this.chatClient = chatClientBuilder.build();\n    }\n    \n    @GetMapping(\"/ai\")\n    String generation(String userInput) {\n        return this.chatClient.prompt()\n            .user(userInput)\n            .call()\n            .content();\n    }\n}\n```\n\n> 注意：我们项目中采用的是将 `ChatClient` 本身注册为 Bean 的方式，如 `3.1.2` 节所示，这在管理默认配置时更为方便。\n\n`ChatClient` 是 Spring AI 中进行大模型交互的核心，它提供了一个优雅的流式（Fluent）API。在我们的 AI-Copilot 项目中，我们没有在业务代码（如Controller或Service）中临时创建 `ChatClient` 实例，而是采用了一种更强大、更可维护的模式：**在 `AiConfig` 中将其配置为单例 Bean**。\n\n这种方式的好处是显而易见的：\n\n  * **集中配置**：所有默认行为，如模型选项（temperature、top\\_p等）、系统提示、以及最重要的 `Advisors`（顾问），都在一个地方统一配置。\n  * **简化业务**：业务代码只需要注入配置好的 `ChatClient` Bean，无需关心其复杂的构建过程，可以直接调用其功能。\n  * **一致性**：确保整个应用的所有AI交互都遵循相同的基本配置和增强逻辑（如日志和对话记忆）。\n\n下面，我们来详细解析在项目中是如何运用 `ChatClient` 的流式API的。\n\n**1. 启动调用链 (`.prompt()`)**\n\n所有交互都始于 `.prompt()` 方法。这是一个无参数的方法，它会返回一个调用链的起点，允许我们后续构建请求。\n\n**2. 构建Prompt内容 (`.user()`, `.system()`)**\n\n  * `.user(String message)`: 这是最常用的方法，用于设置用户的提问内容。在我们的 `ChatService` 中，我们正是用它来传递用户的输入。\n  * `.system(String message)`: 用于设置系统级指令，引导AI的行为和角色。虽然我们在最终代码中是通过 `defaultAdvisors` 来管理上下文，但在需要临时改变AI角色的场景下，可以直接在调用链中使用此方法。\n\n**3. 增强请求 (`.advisors()`)**\n\n这是 `ChatClient` 最强大的功能之一。在我们的项目中，`Advisor` 是实现对话记忆的关键。\n\n```java\n// 来自于我们最终的 ChatService.java\n.advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))\n```\n\n这段代码的含义是：\n\n  * `.advisors(...)` 方法接收一个 `Consumer<AdvisorSpec>` Lambda表达式，允许我们对本次请求的 `Advisors` 进行配置。\n  * `a.param(key, value)` 用于向 `Advisor` 的执行上下文传递参数。\n  * 在这里，我们将 `conversationId` 以 `ChatMemory.CONVERSATION_ID` 为键传入。我们之前在 `AiConfig` 中配置的 `MessageChatMemoryAdvisor` 会自动捕获这个参数，并根据它来查找对应的历史消息，然后将这些历史消息注入到最终发送给AI的 `Prompt` 中。\n\n**4. 执行与响应处理 (`.stream().content()`)**\n\n`ChatClient` 支持两种主要的执行模式：\n\n  * **同步调用**: `.call()` 会阻塞并等待AI返回完整响应。它适用于不需要实时反馈的场景。\n  * **流式调用**: `.stream()` 会立即返回一个 `Flux` (来自响应式编程库 Project Reactor)，AI的响应会以数据块的形式持续不断地推送过来。\n\n为了提供最佳的用户体验，我们的项目**完全采用了流式调用**。\n\n```java\n// 来自于我们最终的 ChatService.java\n.stream()  // 1. 声明我们想要一个流式响应\n.content() // 2. 我们只关心响应流中的文本内容 (Flux<String>)\n```\n\n组合起来，`chatClient.prompt()...stream().content()` 就构成了一个完整的、具备对话记忆能力的流式聊天请求。\n\n**5. 返回结构化数据 (`.entity()`)**\n\n虽然我们的聊天功能主要处理文本流，但 `ChatClient` 还能将AI的响应直接转换为Java对象（POJO），这在需要AI生成特定JSON结构的场景下极为有用。\n\n假设我们想让AI生成一个演员的电影列表，我们可以这样定义一个`record`：\n\n```java\npublic record ActorFilms(String actor, List<String> movies) {}\n```\n\n然后，我们可以这样调用：\n\n```java\nActorFilms actorFilms = chatClient.prompt()\n    .user(\"请生成演员汤姆·汉克斯的电影作品列表，以JSON格式返回，包含 actor 和 movies 两个字段。\")\n    .call() // 注意：通常结构化数据需要等待完整响应，因此使用 .call()\n    .entity(ActorFilms.class);\n```\n\n`ChatClient` 会自动提示AI按指定格式输出，并将返回的JSON字符串反序列化为 `ActorFilms` 对象。对于泛型，用法也同样直观：\n\n```java\nList<ActorFilms> actorFilms = chatClient.prompt()\n    .user(\"请为汤姆·汉克斯和比尔·默里生成电影作品列表...\")\n    .call()\n    .entity(new ParameterizedTypeReference<List<ActorFilms>>() {});\n```\n\n**6. 配置模型参数 (`.options()`)**\n\n我们可以在请求级别覆盖默认的模型参数（如 `temperature`）。`OpenAiChatOptions` 的最新语法使用直接的 `.` 方法进行链式构建。\n\n```java\n// 这是一个示例，展示如何在单次请求中设置参数\nString creativeResponse = chatClient.prompt()\n    .user(\"写一首关于宇宙的诗\")\n    .options(OpenAiChatOptions.builder()\n        .model(\"gpt-4o\")\n        .temperature(0.9f) // 设置更高的温度以获得更有创意的回答\n        .build()\n    )\n    .call()\n    .content();\n```\n\n当然，最佳实践依然是在 `AiConfig` 中通过 `.defaultOptions()` 设置全局默认值，只在需要时进行局部覆盖。\n\n通过这样重构，本节内容现在完全与项目代码保持一致，清晰地解释了我们所使用的每一个API背后的原理和目的。\n### **3.2 赋予AI记忆：`ChatMemory` 深度实践**\n\n大语言模型（LLM）的API接口遵循HTTP协议，其核心特性之一就是**无状态（Stateless）**。每一次API调用都是一次全新的、独立的对话。这种“金鱼记忆”显然无法满足构建一个能持续对话的智能应用的需求。\n\n为了解决这一核心痛点，Spring AI 提供了强大而灵活的`ChatMemory`功能。\n\n#### **3.2.1 核心设计：策略与存储分离**\n\nSpring AI在对话记忆功能上的核心设计思想，是软件工程中“关注点分离”原则的经典体现：**将记忆的策略（如何记住）与记忆的存储（记在哪里）相分离**。\n\n这一思想通过两个核心接口得以实现：\n\n1.  **`ChatMemory` (策略接口)**: 它定义了记忆的**行为和策略**。例如，它决定当对话历史过长时，应该保留哪些消息、遗忘哪些消息。\n2.  **`ChatMemoryRepository` (存储接口)**: 它定义了记忆的**物理存储和检索**。它的职责非常纯粹，就是在后端（如内存、数据库、Redis）存取`Message`数据。\n\n我们必须严格辨析两个极易混淆的概念：\n\n| 概念 | 定义与范围 | 目的与用途 |\n| :--- | :--- | :--- |\n| **对话记忆 (Chat Memory)** | 用于构建下一次Prompt的、一个相关的、有限的对话历史**子集**。 | **为AI服务**，让AI理解上下文，进行连贯对话。 |\n| **对话记录 (Chat History)** | 一次会话中**全部、完整的**消息交换历史。 | **为应用和用户服务**，用于审计、回溯、查看历史。 |\n\n`ChatMemory` 抽象旨在管理*对话记忆*，而不是完整的*对话记录*。如果您需要维护所有交换消息的完整记录，应考虑使用不同的方法，比如我们稍后将介绍的基于 `MyBatis-Plus` 的方案。\n\n#### **3.2.2 记忆策略与存储详解**\n\n**1. 记忆策略 - `MessageWindowChatMemory`**\n\n这是 Spring AI中最常用、也是默认的记忆策略。它实现了一种高效的“滑动窗口”机制，只保留最近的 N 条消息作为上下文，在成本、性能和相关性之间取得了最佳平衡。当消息数量超过此限制时，将驱逐较旧的消息，但会保留系统消息。\n\n```java\nMessageWindowChatMemory memory = MessageWindowChatMemory.builder()\n\t.maxMessages(20) // 设置窗口大小为 20 条消息\n    .build();\n```\n\n**2. 记忆存储 - `ChatMemoryRepository`**\n\n它负责将对话消息进行物理存储。Spring AI 提供了多种内置实现。\n\n| 实现类 | 存储介质 | 优点 | 缺点 | 适用场景 |\n| :--- | :--- | :--- | :--- | :--- |\n| `InMemory...Repository` | JVM 内存 | 零配置, 极速 | 数据易失 | 开发, 测试, 原型 |\n| `Jdbc...Repository` | 关系型数据库 | 可靠, 持久化 | 需配置 | **生产环境** |\n| `Cassandra...Repository`| Cassandra | 高可用, 高扩展性 | 配置复杂 | 大规模分布式系统 |\n| `Neo4j...Repository` | Neo4j图数据库 | 利用图关系 | 需图数据库知识 | 需要分析对话关系 |\n\n在我们的项目中，我们将采用 `JdbcChatMemoryRepository` 实现生产级的持久化记忆。\n\n#### **3.2.3 实战：为 AI-Copilot 实现多轮对话**\n\n我们将使用 `JdbcChatMemoryRepository` 为我们的应用添加持久化的多轮对话能力。\n\n**1. 添加数据库依赖 (`pom.xml`)**\n\n```xml\n<dependency>\n    <groupId>org.springframework.ai</groupId>\n    <artifactId>spring-ai-starter-model-chat-memory-repository-jdbc</artifactId>\n</dependency>\n```\n\n**2. 配置数据库与 `application.yml`**\n\n  * **执行 DDL**: 请在您的 MySQL 数据库中执行以下 SQL 来创建所需的表。\n\n    ```sql\n    CREATE TABLE IF NOT EXISTS `spring_ai_chat_memory` (\n      `id` BIGINT NOT NULL AUTO_INCREMENT,\n      `conversation_id` VARCHAR(255) NOT NULL,\n      `content` TEXT NOT NULL,\n      `type` VARCHAR(50) NOT NULL,\n      `timestamp` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,\n      `media` JSON DEFAULT NULL,\n      `metadata` JSON DEFAULT NULL,\n      PRIMARY KEY (`id`),\n      KEY `idx_conversation_id` (`conversation_id`)\n    );\n    ```\n\n  * **修改 `application.yml`**:\n\n    ```yaml\n    spring:\n      ai:\n        chat:\n          memory:\n            repository:\n              jdbc:\n                initialize-schema: never\n                platform: mysql\n      datasource:\n        url: ****************************************************************************************************        username: root\n        password: root\n        driver-class-name: com.mysql.cj.jdbc.Driver\n    ```\n\n**3. 在 `AiConfig` 中配置记忆功能**\n\n我们将 `ChatMemory` 的创建和 `MessageChatMemoryAdvisor` 的应用，全部集中在 `AiConfig` 配置类中。\n\n```java\npackage com.copilot.aicopilotbackend.config;\n\nimport org.springframework.ai.chat.client.ChatClient;\nimport org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;\nimport org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;\nimport org.springframework.ai.chat.memory.ChatMemory;\nimport org.springframework.ai.chat.memory.MessageWindowChatMemory;\nimport org.springframework.ai.chat.model.ChatModel;\nimport org.springframework.context.annotation.Bean;\nimport org.springframework.context.annotation.Configuration;\n\n@Configuration\npublic class AiConfig {\n\n    /**\n     * 定义 ChatClient Bean，并为其配置默认的 Advisor。\n     * @param chatModel Spring Boot 根据 application.yml 自动配置好的 ChatModel 实例\n     * @param chatMemory 聊天内存实例，用于维护对话上下文\n     * @return 一个配置了日志顾问和内存顾问的 ChatClient 实例\n     */\n    @Bean\n    public ChatClient chatClient(ChatModel chatModel, ChatMemory chatMemory) {\n        return ChatClient.builder(chatModel)\n                .defaultAdvisors(\n                        new SimpleLoggerAdvisor(),\n                        MessageChatMemoryAdvisor.builder(chatMemory).build()\n                )\n                .build();\n    }\n\n    /**\n     * Spring AI 会自动配置 ChatMemory Bean\n     * 当检测到 JDBC 依赖时会自动使用 JDBC 存储库\n     * 这里不需要手动配置，依赖自动配置机制\n     */\n}\n\n```\n\n> **自动配置说明**: Spring AI 会自动检测到 `spring-ai-starter-model-chat-memory-repository-jdbc` 依赖和 `application.yml` 中的配置，并自动创建一个 `JdbcChatMemoryRepository` 的 Bean。我们的 `chatMemory()` 方法会自动使用这个 Bean 作为 `MessageWindowChatMemory` 的后端存储。\n\n> **顾问链顺序**: 将 advisor 添加到链中的顺序至关重要。`ChatClient.builder().advisors(advisor1, advisor2)` 中，`advisor1` 会先执行。\n\n**4. 改造后端业务代码以支持会话**\n\n- **改造`ChatRequest`**\n\n  ```java\n  package com.copilot.aicopilotbackend.dto.request;\n  \n  import com.fasterxml.jackson.annotation.JsonProperty;\n  \n  import java.util.UUID;\n  \n  /**\n   * 聊天请求DTO\n   * @param message 用户消息内容\n   * @param conversationId 会话ID，可选参数，为空时自动生成\n   */\n  public record ChatRequest(\n          @JsonProperty(\"message\") String message,\n          @JsonProperty(\"conversationId\") String conversationId,\n          @JsonProperty(\"isDeepThink\") Boolean isDeepThink\n  ) {\n      /**\n       * 获取有效的会话ID\n       * @return 非空的会话ID\n       */\n      public String getEffectiveConversationId() {\n          return conversationId != null ? conversationId : UUID.randomUUID().toString();\n      }\n  }\n  \n  ```\n\n  \n\n  * **改造 `ChatService.java`**:\n\n    ```java\n    // src/main/java/com/copilot/aicopilotbackend/service/ChatService.java\n    package com.copilot.aicopilotbackend.service;\n    \n    import com.copilot.aicopilotbackend.exception.SystemException;\n    import com.copilot.aicopilotbackend.exception.ErrorCode;\n    import com.copilot.aicopilotbackend.validation.ChatMessageValidator;\n    import org.springframework.ai.chat.client.ChatClient;\n    import org.springframework.ai.chat.memory.ChatMemory;\n    import org.springframework.stereotype.Service;\n    import reactor.core.publisher.Flux;\n    \n    /**\n     * 聊天服务\n     * 提供AI流式聊天功能\n     */\n    @Service\n    public class ChatService {\n        \n        private final ChatClient chatClient;\n    \n        public ChatService(ChatClient chatClient) {\n            this.chatClient = chatClient;\n        }\n        \n        /**\n         * 获取流式聊天响应\n         * @param message 用户消息\n         * @param conversationId 会话ID\n         * @return 流式响应\n         */\n        public Flux<String> getStreamingChat(String message, String conversationId) {\n            // 验证消息\n            ChatMessageValidator.validateMessage(message);\n            \n            return chatClient.prompt()\n                    .user(message)\n                \t// 重点在这里，通过advisors注入会话记忆\n                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))\n                    .stream()\n                    .content()\n                    .onErrorMap(e -> new SystemException(ErrorCode.AI_SERVICE_UNAVAILABLE, \n                                                       \"AI服务当前不可用，请稍后重试\", e));\n        }\n    }\n    ```\n\n  * **改造 `ChatController.java`**:\n\n    ```java\n    package com.copilot.aicopilotbackend.controller;\n    \n    import com.copilot.aicopilotbackend.dto.request.ChatRequest;\n    import com.copilot.aicopilotbackend.service.ChatService;\n    import com.fasterxml.jackson.databind.ObjectMapper;\n    import lombok.RequiredArgsConstructor;\n    import org.springframework.http.MediaType;\n    import org.springframework.http.codec.ServerSentEvent;\n    import org.springframework.web.bind.annotation.*;\n    import reactor.core.publisher.Flux;\n    \n    import java.time.Duration;\n    import java.util.Map;\n    \n    @RestController\n    @RequestMapping(\"/api/v1/chat\")\n    @RequiredArgsConstructor\n    public class ChatController {\n    \n        private final ChatService chatService;\n        private final ObjectMapper objectMapper = new ObjectMapper();\n    \n        @PostMapping(\n                value = \"/stream\",\n                consumes = MediaType.APPLICATION_JSON_VALUE,\n                produces = MediaType.TEXT_EVENT_STREAM_VALUE\n        )\n        public Flux<ServerSentEvent<String>> stream(@RequestBody ChatRequest req) {\n            return chatService.getStreamingChat(req.message(), req.getEffectiveConversationId())\n                    .map(content -> {\n                        try {\n                            // 将AI内容包装成JSON格式：{\"content\": \"实际内容\"}\n                            String jsonContent = objectMapper.writeValueAsString(\n                                    Map.of(\"content\", content)\n                            );\n                            return ServerSentEvent.<String>builder()\n                                    .data(jsonContent)\n                                    .build();\n                        } catch (Exception e) {\n                            // 如果JSON序列化失败，发送错误信息\n                            return ServerSentEvent.<String>builder()\n                                    .data(\"{\\\"content\\\": \\\"\\\"}\")\n                                    .build();\n                        }\n                    })\n                    .concatWith(Flux.just(\n                            // 发送结束信号\n                            ServerSentEvent.<String>builder()\n                                    .data(\"[DONE]\")\n                                    .build()\n                    ));\n        }\n    }\n    \n    ```\n\n\n\n\n**5. 改造前端以管理和传递 `conversationId`**\n\n  * **安装 `uuid` 库**:\n\n    ```bash\n    pnpm add uuid\n    ```\n\n  * **改造 `src/apis/chatService.js`**:\n\n    ```javascript\n    // src/apis/chatService.js\n    \n    /**\n     * 获取流式聊天回复的Response对象\n     * @param {string} message - 用户消息\n     * @param {string} conversationId - 会话ID\n     * @returns {Promise<Response>} 返回Response对象供useXStream处理\n     */\n    export const getStreamingChatResponse = async (message, conversationId) => {\n        try {\n            // 使用原生fetch发送请求\n            const response = await fetch('http://localhost:8080/api/v1/chat/stream', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    message,\n                    conversationId\n                })\n            });\n    \n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n    \n            return response;\n        } catch (error) {\n            console.error('流式聊天请求失败:', error);\n            throw error;\n        }\n    };\n    \n    ```\n\n  * **改造 `src/views/ChatView.vue`**:\n\n    ```vue\n    <script setup lang=\"ts\">\n    import { ref, nextTick, computed, watch } from 'vue';\n    import { v4 as uuidv4 } from 'uuid';\n    import { useXStream } from 'vue-element-plus-x';\n    import { getStreamingChatResponse } from '@/apis/chatService';\nimport type { BubbleListItemProps } from 'vue-element-plus-x/types/BubbleList';\n    import ConversationManager from '../components/ConversationManager.vue';\n    import { useConversations } from '../composables/useConversations';\n    \n    // 使用 useXStream 钩子\n    const { startStream, cancel, data, error, isLoading } = useXStream();\n    \n    // 使用会话管理\n    const {\n      currentConversationId,\n      currentConversation,\n      currentMessages,\n      addMessage,\n      clearCurrentMessages\n    } = useConversations();\n    \n    // AI和用户头像\n    const avatarUser = 'https://bu.dusays.com/2025/06/16/684f747174bc3.webp';\n    const avatarAI = 'https://bu.dusays.com/2025/06/26/685cf1884034c.png';\n    \n    // 消息类型定义\n    type MessageType = BubbleListItemProps & {\n      key: number;\n      role: 'user' | 'ai';\n      messageId: string;\n    };\n    \n    // 消息列表数据 - 符合BubbleList格式\n    const messages = ref<MessageType[]>([\n      {\n        key: 1,\n        role: 'ai',\n        messageId: uuidv4(),\n        placement: 'start',\n        content: '您好！我是您的 AI-Copilot，已准备就绪。',\n        avatar: avatarAI,\n        avatarSize: '48px',\n        isMarkdown: true,\n        shape: 'corner',\n        variant: 'filled'\n      }\n    ]);\n    \n    // 用户输入的消息内容\n    const inputMessage = ref('');\n    \n    // 计算属性 - 合并流式数据（纯函数）\n    const streamContent = computed(() => {\n      if (!data.value.length) return '';\n    \n      let text = '';\n      for (let index = 0; index < data.value.length; index++) {\n        const chunk = data.value[index].data;\n    \n        // 跳过空数据或结束标识\n        if (!chunk || chunk === '[DONE]' || chunk.trim() === '') {\n          continue;\n        }\n    \n        try {\n          // 解析JSON格式：{\"content\": \"实际内容\"}\n          const parsed = JSON.parse(chunk);\n          const content = parsed.content;\n          if (content !== undefined && content !== null && content !== '') {\n            text += content;\n          }\n        } catch (error) {\n          // 如果解析失败，作为纯文本处理（向后兼容）\n          if (typeof chunk === 'string' && chunk.trim() !== '') {\n            text += chunk;\n          }\n        }\n      }\n    \n      return text;\n    });\n    \n    // 监听流数据变化，实现真正的流式体验\n    watch(streamContent, (newContent) => {\n      if (newContent && messages.value.length > 0) {\n        const lastMessage = messages.value[messages.value.length - 1];\n        if (lastMessage.role === 'ai') {\n          // 流数据一到达就立即切换到流式显示模式\n          if (lastMessage.loading) {\n            lastMessage.loading = false; // 立即关闭loading\n            lastMessage.typing = true;   // 启用打字效果\n          }\n          lastMessage.content = newContent; // 实时更新内容\n        }\n      }\n    }, { immediate: true });\n    \n    /**\n     * 发送消息处理函数\n     * @param {string} message - 用户输入的消息内容\n     */\n    const handleSend = async (message) => {\n      // 验证消息有效性和是否正在加载中\n      if (!message.trim() || isLoading.value) return;\n    \n      // 添加用户消息到对话列表\n      const userMessage: MessageType = {\n        key: messages.value.length + 1,\n        role: 'user',\n        messageId: uuidv4(),\n        placement: 'end',\n        content: message.trim(),\n        avatar: avatarUser,\n        avatarSize: '32px',\n        shape: 'corner',\n        variant: 'filled' // 使用填充样式，在黑色背景下更清晰\n      };\n      messages.value.push(userMessage);\n    \n      // 立即添加AI思考状态气泡\n      const aiMessage: MessageType = {\n        key: messages.value.length + 1,\n        role: 'ai',\n        messageId: uuidv4(),\n        placement: 'start',\n        content: '正在思考中...',\n        avatar: avatarAI,\n        avatarSize: '48px',\n        isMarkdown: true,\n        shape: 'corner',\n        variant: 'filled',\n        loading: true, // 设置加载状态\n        typing: false\n      };\n      messages.value.push(aiMessage);\n    \n      // 清空输入框\n      inputMessage.value = '';\n    \n      try {\n        // 获取Response对象\n        const response = await getStreamingChatResponse(message, conversationId.value);\n        const readableStream = response.body!;\n    \n        // 使用useXStream处理SSE流数据（默认支持SSE协议）\n        await startStream({ readableStream });\n    \n        // 流结束后，停止打字效果\n        const lastAiMessage = messages.value[messages.value.length - 1];\n        if (lastAiMessage.role === 'ai') {\n          lastAiMessage.typing = false; // 停止打字效果\n          // loading 已在 watch 中处理，content 已实时更新\n        }\n    \n      } catch (err) {\n        console.error('流式请求失败:', err);\n    \n        // 更新最后一个AI消息为错误消息\n        const lastAiMessage = messages.value[messages.value.length - 1];\n        if (lastAiMessage.role === 'ai') {\n          lastAiMessage.content = '抱歉，服务暂时不可用，请稍后重试。';\n          lastAiMessage.loading = false;\n          lastAiMessage.typing = false;\n        }\n      }\n    };\n    \n    \n    /**\n     * 表单提交处理函数\n     * 当用户点击发送按钮或按下回车键时触发\n     */\n    const handleSubmit = () => {\n      if (inputMessage.value.trim()) {\n        handleSend(inputMessage.value);\n        // 清理输入框\n        inputMessage.value = '';\n      }\n    };\n    \n    /**\n     * 取消请求处理函数\n     * 用于中断当前AI请求（预留功能）\n     */\n    const handleCancel = () => {\n      // TODO: 实现请求取消功能\n      console.log('Cancel request');\n    };\n    \n    /**\n     * 录音状态变化处理函数\n     * @param {Object} event - 录音状态事件对象\n     */\n    const handleRecordingChange = (event) => {\n      console.log('Recording state changed:', event);\n    };\n    \n    /**\n     * 触发器事件处理函数\n     * 处理特殊命令触发（如 /help、/clear 等）\n     * @param {Object} event - 触发器事件对象\n     */\n    const handleTrigger = (event) => {\n      console.log('Trigger event:', event);\n    };\n</script>\n \n<template>\n <div class="\&quot;flex" flex-col="" h-full\"="">\n \n<header class="\&quot;bg-white" shadow-md="" p-4="" text-center\"="">\n  <h1 class="\&quot;text-xl" font-bold="" text-primary\"="">AI-Copilot\n\n \n \n <main class="\&quot;flex-1" overflow-hidden\"="">\n \n <bubblelist :list="\&quot;messages\&quot;" max-height="\&quot;100%\&quot;" :always-show-scrollbar="\&quot;false\&quot;" :show-back-button="\&quot;true\&quot;\n" :btn-loading="\&quot;isLoading\&quot;" btn-color="\&quot;#409EFF\&quot;">\n</bubblelist></main></h1></header></div></template></div></div></main>\n \n \n <footer class="\&quot;p-4" bg-white\"="">\n \n <sender 6="" v-model="\&quot;inputMessage\&quot;" placeholder="\&quot;请输入您的问题...\&quot;" :auto-size="\&quot;{" minrows:="" 1,="" maxrows:="" }\"="" :read-only="\&quot;false\&quot;\n" :disabled="\&quot;false\&quot;" :submitbtndisabled="\&quot;isLoading\&quot;" :loading="\&quot;isLoading\&quot;" :clearable="\&quot;true\&quot;" :allowspeech="\&quot;true\&quot;\n" submittype="\&quot;enter\&quot;" :headeranimationtimer="\&quot;300\&quot;" inputwidth="\&quot;100%\&quot;" variant="\&quot;default\&quot;" :showupdown="\&quot;true\&quot;\n" :inputstyle="\&quot;{}\&quot;" :triggerstrings="\&quot;['/help'," '="" clear']\"="" :triggerpopovervisible="\&quot;false\&quot;\n" triggerpopoverwidth="\&quot;fit-content\&quot;" triggerpopoverleft="\&quot;0px\&quot;" :triggerpopoveroffset="\&quot;8\&quot;\n" triggerpopoverplacement="\&quot;top-start\&quot;" @submit="\&quot;handleSubmit\&quot;" @cancel="\&quot;handleCancel\&quot;\n" @recordingchange="\&quot;handleRecordingChange\&quot;" @trigger="\&quot;handleTrigger\&quot;">\n\n</sender></footer></div>\n\n ```\n\n重启应用并刷新前端页面，现在您的 AI-Copilot 已经拥有了基于数据库的持久化“记忆”！\n\n### **3.3 管理长期记忆：实现会话历史服务**\n\n`ChatMemory` 解决了 AI 的上下文问题，但作为应用开发者，我们还需要为用户提供查看、管理他们完整对话记录（Chat History）的功能。这需要我们直接操作 `spring_ai_chat_memory` 这张表。\n\n#### **3.3.1 后端服务搭建：基于 MyBatis-Plus**\n\n我们将按照标准三层架构，构建一套完整的、面向业务的会话历史管理服务。\n\n**1. 依赖与配置**\n\n * **`pom.xml`**: 添加 MyBatis-Plus 依赖。\n \n ```xml\n<dependency>\n<groupid>com.baomidou</groupid>\n<artifactid>mybatis-plus-spring-boot3-starter</artifactid>\n<version>3.5.7</version>\n</dependency>\n ```\n * **主启动类**: 添加 `@MapperScan` 注解。\n \n ```java\n @SpringBootApplication\n @MapperScan(\"com.copilot.aicopilotbackend.repository\") // 指向 Mapper 接口包\n public class AiCopilotBackendApplication { /* ... */ }\n ```\n * **`application.yml`**: 添加 MyBatis-Plus 配置。\n ```yaml\n mybatis-plus:\n configuration:\n map-underscore-to-camel-case: true\n log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n ```\n\n**2. 数据访问层 (DAO)**\n\n * **`entity/ChatMemoryEntity.java`**: 创建用于 MyBatis-Plus 操作的实体类。\n\n ```java\n package com.copilot.aicopilotbackend.entity;\n\n import com.baomidou.mybatisplus.annotation.*;\n import lombok.Data;\n import java.time.LocalDateTime;\n\n @Data\n @TableName(\"spring_ai_chat_memory\")\n public class ChatMemoryEntity {\n @TableId(value = \"id\", type = IdType.AUTO)\n private Long id;\n @TableField(\"conversation_id\")\n private String conversationId;\n @TableField(\"content\")\n private String content;\n @TableField(\"type\")\n private String type;\n @TableField(\"timestamp\")\n private LocalDateTime timestamp;\n }\n ```\n\n * **`repository/ChatMemoryMapper.java`**: 创建继承 `BaseMapper` 的 Mapper 接口。\n\n ```java\n package com.copilot.aicopilotbackend.repository;\n\n import com.baomidou.mybatisplus.core.mapper.BaseMapper;\n import com.copilot.aicopilotbackend.entity.ChatMemoryEntity;\n import org.apache.ibatis.annotations.Mapper;\n\n @Mapper\n public interface ChatMemoryMapper extends BaseMapper<chatmemoryentity>{\n }\n ```\n\n**3. 业务逻辑层 (Service)**\n\n * **`service/IChatHistoryService.java`**: 定义服务接口。\n\n ```java\n package com.copilot.aicopilotbackend.service;\n\n import com.baomidou.mybatisplus.extension.service.IService;\n import com.copilot.aicopilotbackend.entity.ChatMemoryEntity;\n import java.util.List;\n import java.util.Map;\n\n public interface IChatHistoryService extends IService<chatmemoryentity>{\n List<map<string ,="" object="">&gt; getAllConversations();\n List<chatmemoryentity>getChatHistory(String conversationId);\n boolean deleteChatHistory(String conversationId);\n }\n ```\n\n * **`service/impl/ChatHistoryServiceImpl.java`**: 实现服务接口。\n\n ```java\n package com.copilot.aicopilotbackend.service.impl;\n\n import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;\n import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;\n import com.copilot.aicopilotbackend.entity.ChatMemoryEntity;\n import com.copilot.aicopilotbackend.repository.ChatMemoryMapper;\n import com.copilot.aicopilotbackend.service.IChatHistoryService;\n import org.springframework.stereotype.Service;\n import java.time.LocalDateTime;\n import java.util.HashMap;\n import java.util.List;\n import java.util.Map;\n import java.util.stream.Collectors;\n\n @Service\n public class ChatHistoryServiceImpl extends ServiceImpl<chatmemorymapper ,="" chatmemoryentity="">implements IChatHistoryService {\n\n @Override\n public List<map<string ,="" object="">&gt; getAllConversations() {\n QueryWrapper<chatmemoryentity>idQueryWrapper = new QueryWrapper&lt;&gt;();\n idQueryWrapper.select(\"conversation_id\", \"timestamp\").orderByDesc(\"timestamp\");\n List<chatmemoryentity>allRecords = this.list(idQueryWrapper);\n\n List<string>distinctConvIds = allRecords.stream()\n .map(ChatMemoryEntity::getConversationId)\n .distinct()\n .collect(Collectors.toList());\n\n return distinctConvIds.stream().map(id -&gt; {\n Map<string ,="" object="">conversationInfo = new HashMap&lt;&gt;();\n conversationInfo.put(\"id\", id);\n\n ChatMemoryEntity firstUserMessage = this.query()\n .eq(\"conversation_id\", id).eq(\"type\", \"USER\")\n .orderByAsc(\"timestamp\").last(\"LIMIT 1\").one();\n String title = (firstUserMessage != null &amp;&amp; firstUserMessage.getContent() != null)\n ? firstUserMessage.getContent().trim() : \"新的会话\";\n conversationInfo.put(\"label\", title.length() &gt; 20 ? title.substring(0, 20) + \"...\" : title);\n\n ChatMemoryEntity lastMessage = this.query()\n .eq(\"conversation_id\", id).orderByDesc(\"timestamp\")\n .last(\"LIMIT 1\").one();\n conversationInfo.put(\"updatedAt\", lastMessage != null ? lastMessage.getTimestamp() : LocalDateTime.now());\n\n return conversationInfo;\n }).collect(Collectors.toList());\n }\n\n @Override\n public List<chatmemoryentity>getChatHistory(String conversationId) {\n QueryWrapper<chatmemoryentity>queryWrapper = new QueryWrapper&lt;&gt;();\n queryWrapper.eq(\"conversation_id\", conversationId).orderByAsc(\"timestamp\");\n return this.list(queryWrapper);\n }\n\n @Override\n public boolean deleteChatHistory(String conversationId) {\n QueryWrapper<chatmemoryentity>queryWrapper = new QueryWrapper&lt;&gt;();\n queryWrapper.eq(\"conversation_id\", conversationId);\n return this.remove(queryWrapper);\n }\n }\n ```\n\n**4. API 接口层 (Controller)**\n\n * **`controller/ChatHistoryController.java`**: 创建新的 Controller，暴露三个 API 端点。\n \n ```java\npackage com.copilot.aicopilotbackend.controller;\n \n import com.copilot.aicopilotbackend.dto.response.ApiResponse;\n import com.copilot.aicopilotbackend.entity.ChatMemoryEntity;\n import com.copilot.aicopilotbackend.service.IChatHistoryService;\n import lombok.RequiredArgsConstructor;\n import org.springframework.web.bind.annotation.*;\n import java.util.List;\nimport java.util.Map;\n \n @RestController\n @RequestMapping(\"/api/v1/chat-history\")\n @RequiredArgsConstructor\npublic class ChatHistoryController {\n \n private final IChatHistoryService chatHistoryService;\n \n @GetMapping(\"/conversations\")\n public ApiResponse<list<map<string ,="" object="">&gt;&gt; getAllConversations() {\n return ApiResponse.success(chatHistoryService.getAllConversations());\n }\n \n @GetMapping(\"/conversation/{conversationId}\")\n public ApiResponse<list<chatmemoryentity>&gt; getChatHistory(@PathVariable String conversationId) {\n return ApiResponse.success(chatHistoryService.getChatHistory(conversationId));\n }\n \n @DeleteMapping(\"/conversation/{conversationId}\")\n public ApiResponse<map<string ,="" object="">&gt; deleteChatHistory(@PathVariable String conversationId) {\n boolean success = chatHistoryService.deleteChatHistory(conversationId);\n Map<string ,="" object="">result = Map.of(\n \"success\", success,\n \"message\", success ? \"删除成功\" : \"删除失败或会话不存在\"\n );\n return ApiResponse.success(result);\n }\n }\n ```\n\n#### **3.3.2 API 接口文档**\n\n以下是为“会话历史管理”功能提供的后端RESTful API接口文档。\n\n**基础URL**: `http://localhost:8080/api/v1`\n\n-----\n\n**1. 获取所有会话列表**\n\n获取所有已存在的会话的概要信息列表，按最后更新时间倒序排列。\n\n * **请求**: `GET /chat-history/conversations`\n * **请求参数**: 无\n * **成功响应 (`200 OK`)**:\n ```json\n {\n \"code\": \"00000\",\n \"message\": \"操作成功\",\n \"data\": [\n {\n \"id\": \"conv_1718985665_a1b2c3d4\",\n \"label\": \"你好，介绍一下你自己\",\n \"updatedAt\": \"2025-06-22T01:21:05.123456\"\n },\n {\n \"id\": \"conv_1718985601_e5f6g7h8\",\n \"label\": \"请用Java写一个快排...\",\n \"updatedAt\": \"2025-06-22T01:20:01.789012\"\n }\n ],\n \"timestamp\": \"2025-06-27T10:00:00.000000\"\n }\n ```\n * **响应数据字段说明**:\n\n| 字段 | 类型 | 描述 |\n| :--- | :--- | :--- |\n| `id` | `String` | 会话的唯一标识符。 |\n| `label`| `String` | 根据会话第一条用户消息生成的默认标题（最长20个字符）。 |\n| `updatedAt`|`String` | 会话的最后更新时间（ISO 8601格式）。 |\n\n-----\n\n**2. 获取指定会话详情**\n\n根据提供的`conversationId`，获取该会话的完整聊天记录，按消息时间升序排列。\n\n * **请求**: `GET /chat-history/conversation/{conversationId}`\n * **请求参数**:\n * **路径参数**:\n | 参数 | 类型 | 状态 | 描述 |\n | :--- | :--- | :--- | :--- |\n | `conversationId` | `String` | **必需** | 要查询的会话ID。 |\n * **成功响应 (`200 OK`)**:\n ```json\n {\n \"code\": \"00000\",\n \"message\": \"操作成功\",\n \"data\": [\n {\n \"id\": 101,\n \"conversationId\": \"conv_1718985665_a1b2c3d4\",\n \"content\": \"你好，介绍一下你自己\",\n \"type\": \"USER\",\n \"timestamp\": \"2025-06-22T01:21:00.123456\"\n },\n {\n \"id\": 102,\n \"conversationId\": \"conv_1718985665_a1b2c3d4\",\n \"content\": \"你好！我是一个由Spring AI驱动的大语言模型...\",\n \"type\": \"ASSISTANT\",\n \"timestamp\": \"2025-06-22T01:21:05.123456\"\n }\n ],\n \"timestamp\": \"2025-06-27T10:01:00.000000\"\n }\n ```\n * **响应数据字段说明**:\n\n| 字段 | 类型 | 描述 |\n| :--- | :--- | :--- |\n| `id` | `Long` | 消息的数据库主键ID。 |\n| `conversationId`| `String` | 所属会话的ID。 |\n| `content` | `String` | 消息的文本内容。 |\n| `type` | `String` | 消息角色，值为 `USER` 或 `ASSISTANT`。 |\n| `timestamp` | `String` | 消息的创建时间戳（ISO 8601格式）。 |\n\n-----\n\n**3. 删除指定会话**\n\n根据提供的`conversationId`，删除该会话的所有相关聊天记录。\n\n * **请求**: `DELETE /chat-history/conversation/{conversationId}`\n * **请求参数**:\n \n * **路径参数**:\n | 参数 | 类型 | 状态 | 描述 |\n | :--- | :--- | :--- | :--- |\n | `conversationId` | `String` | **必需** | 要删除的会话ID。 |\n * **成功响应 (`200 OK`)**:\n ```json\n {\n \"code\": \"00000\",\n \"message\": \"操作成功\",\n \"data\": {\n \"success\": true,\n \"message\": \"删除成功\"\n },\n \"timestamp\": \"2025-06-27T10:02:00.000000\"\n }\n ```\n * **响应数据字段说明**:\n\n| 字段 | 类型 | 描述 |\n| :--- | :--- | :--- |\n| `success`| `Boolean` | 操作是否成功。`true`表示成功，`false`表示失败。 |\n| `message`| `String` | 操作结果的文本描述信息。 |\n\n\n-----\n\n" };<div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#3-%E4%BC%9A%E8%AF%9D%E6%A0%B8%E5%BF%83-API-%E6%B7%B1%E5%BA%A6%E8%A7%A3%E6%9E%90"><span class="toc-number">1.</span> <span class="toc-text">3. 会话核心 API 深度解析</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-%E6%8F%AD%E5%BC%80%E9%BB%91%E7%9B%92%EF%BC%9AChatClient-%E7%9A%84%E5%8F%AF%E8%A7%82%E6%B5%8B%E6%80%A7"><span class="toc-number">1.1.</span> <span class="toc-text">3.1 揭开黑盒：ChatClient 的可观测性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-1-%E6%A0%B8%E5%BF%83%E5%88%A9%E5%99%A8-Advisor-%E4%B8%8E-SimpleLoggerAdvisor"><span class="toc-number">1.1.1.</span> <span class="toc-text">3.1.1 核心利器: Advisor 与 SimpleLoggerAdvisor</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-2-%E5%AE%9E%E6%88%98%EF%BC%9A%E4%B8%BA-AI-Copilot-%E5%BC%80%E5%90%AF%E8%B0%83%E8%AF%95%E6%97%A5%E5%BF%97"><span class="toc-number">1.1.2.</span> <span class="toc-text">3.1.2 实战：为 AI-Copilot 开启调试日志</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-3-%E8%A7%A3%E8%AF%BB%E6%97%A5%E5%BF%97%E8%BE%93%E5%87%BA"><span class="toc-number">1.1.3.</span> <span class="toc-text">3.1.3 解读日志输出</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-4-ChatClient-%E8%AF%A6%E8%A7%A3-%E9%87%8D%E7%82%B9%E5%86%85%E5%AE%B9"><span class="toc-number">1.1.4.</span> <span class="toc-text">**3.1.4 ChatClient 详解(重点内容) **</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-%E8%B5%8B%E4%BA%88AI%E8%AE%B0%E5%BF%86%EF%BC%9AChatMemory-%E6%B7%B1%E5%BA%A6%E5%AE%9E%E8%B7%B5"><span class="toc-number">1.2.</span> <span class="toc-text">3.2 赋予AI记忆：ChatMemory 深度实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#3-2-1-%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1%EF%BC%9A%E7%AD%96%E7%95%A5%E4%B8%8E%E5%AD%98%E5%82%A8%E5%88%86%E7%A6%BB"><span class="toc-number">1.2.1.</span> <span class="toc-text">3.2.1 核心设计：策略与存储分离</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-2-2-%E8%AE%B0%E5%BF%86%E7%AD%96%E7%95%A5%E4%B8%8E%E5%AD%98%E5%82%A8%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.2.2.</span> <span class="toc-text">3.2.2 记忆策略与存储详解</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-2-3-%E5%AE%9E%E6%88%98%EF%BC%9A%E4%B8%BA-AI-Copilot-%E5%AE%9E%E7%8E%B0%E5%A4%9A%E8%BD%AE%E5%AF%B9%E8%AF%9D"><span class="toc-number">1.2.3.</span> <span class="toc-text">3.2.3 实战：为 AI-Copilot 实现多轮对话</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-%E7%AE%A1%E7%90%86%E9%95%BF%E6%9C%9F%E8%AE%B0%E5%BF%86%EF%BC%9A%E5%AE%9E%E7%8E%B0%E4%BC%9A%E8%AF%9D%E5%8E%86%E5%8F%B2%E6%9C%8D%E5%8A%A1"><span class="toc-number">1.3.</span> <span class="toc-text">3.3 管理长期记忆：实现会话历史服务</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#3-3-1-%E5%90%8E%E7%AB%AF%E6%9C%8D%E5%8A%A1%E6%90%AD%E5%BB%BA%EF%BC%9A%E5%9F%BA%E4%BA%8E-MyBatis-Plus"><span class="toc-number">1.3.1.</span> <span class="toc-text">3.3.1 后端服务搭建：基于 MyBatis-Plus</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-3-2-API-%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3"><span class="toc-number">1.3.2.</span> <span class="toc-text">3.3.2 API 接口文档</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div></string></map<string></list<chatmemoryentity></list<map<string></chatmemoryentity></chatmemoryentity></chatmemoryentity></string></string></chatmemoryentity></chatmemoryentity></map<string></chatmemorymapper></chatmemoryentity></map<string></chatmemoryentity></chatmemoryentity>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>