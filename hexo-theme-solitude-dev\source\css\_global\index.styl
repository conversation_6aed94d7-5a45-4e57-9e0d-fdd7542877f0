:root
  --efu-white #fff
  --efu-white-op rgba(255, 255, 255, 0.2)
  --efu-black #000
  --efu-black-op rgba(0, 0, 0, 0.2)
  --efu-none #00000000
  --efu-gray #999999
  --efu-gray-op #9999992b
  --efu-cyan #00bcd4
  --efu-cyan-op #00bcd42b
  --efu-vip #e5a80d
  --efu-speed #57bd6a
  --efu-main var(--efu-theme)
  --efu-main-op var(--efu-theme-op)
  --efu-main-op-deep var(--efu-theme-op-deep)
  --efu-main-op-light var(--efu-theme-op-light)
  --efu-main-none var(--efu-theme-none)
  --efu-shadow-theme 0 8px 12px -3px var(--efu-theme-op)
  --efu-shadow-blackdeep 0 2px 16px -3px rgba(0, 0, 0, .15)
  --efu-shadow-main 0 8px 12px -3px var(--efu-main-op)
  --efu-shadow-blue 0 8px 12px -3px rgba(40, 109, 234, .20)
  --efu-shadow-white 0 8px 12px -3px rgba(255, 255, 255, .20)
  --efu-shadow-black 0 0 12px 4px rgba(0, 0, 0, .05)
  --efu-shadow-yellow 0px 38px 77px -26px rgba(255, 201, 62, .12)
  --efu-shadow-red 0 8px 12px -3px #ee7d7936
  --efu-shadow-green 0 8px 12px -3px #87ee7936
  --efu-logo-color linear-gradient(215deg, #4584ff 0%, #cf0db9 100%)
  --efu-snackbar-time 5s
  --global-font-size 14px
  --global-bg #fff
  --hr-border #97bcfb
  --hr-before-color #6ea2f9
  --search-bg #f6f8fa
  --search-input-color #4c4948
  --search-result-title #4c4948
  --preloader-bg #37474f
  --preloader-color #fff
  --tab-border-color #f0f0f0
  --tab-botton-bg #f0f0f0
  --tab-botton-color #1f2d3d
  --tab-button-hover-bg #dcdcdc
  --tab-button-active-bg #fff
  --sidebar-bg #f6f8fa
  --btn-hover-color #ff7242
  --btn-color #fff
  --btn-bg var(--efu-main)
  --text-bg-hover #f6f8fa
  --light-grey #eee
  --text-highlight-color #1f2d3d
  --blockquote-color #6a737d
  --blockquote-bg rgba(73, 177, 245, 0.1)
  --reward-pop #f5f5f5
  --toc-link-color #666261
  --card-box-shadow 0 3px 8px 6px rgba(7, 17, 27, 0.06)
  --card-hover-box-shadow 0 3px 8px 6px rgba(7, 17, 27, 0.15)
  --offset 0px
  --hlscrollbar-bg #121212
  --gap .5rem
  --radius 12px
  --efu-music var(--efu-main)

body
  position relative
  min-height 100%
  background var(--efu-background)
  color var(--efu-fontcolor)
  font-size $font-size
  font-family $font-family
  line-height 2
  -webkit-tap-highlight-color transparent
  margin 0
  if !hexo-config('copy.enable')
    user-select none
    -webkit-user-select none

*
  box-sizing border-box

html
  line-height 1.15
  -webkit-text-size-adjust 100%
  text-size-adjust 100%
  height: 100%
  font-size 20px
  overflow-y overlay

main
  display block

// scrollbar - chrome/safari
*::-webkit-scrollbar
  width 12px
  height 6px

*::-webkit-scrollbar-thumb
  background var(--scrollbar-color)
  opacity .5
  border-radius 8px
  cursor pointer
  border 2px solid var(--efu-background)

  &:hover
    background var(--efu-lighttext)
    display block

*::-webkit-scrollbar-track
  background-color transparent

::-webkit-scrollbar-thumb
  background var(--efu-card-border)
  opacity .5
  border-radius 8px
  cursor pointer
  border 2px solid var(--efu-background)

::-webkit-scrollbar-thumb:hover
  background var(--efu-main)
  opacity 1
  display block !important

::-webkit-scrollbar-track
  background-color var(--efu-none)

if hexo-config('lazyload.enable') && !hexo-config('lazyload.placeholder')
  img
    &[data-lazy-src]:not(.loaded)
      filter blur(8px) brightness(1)

    &[data-lazy-src].error
      filter none

::selection
  background var(--efu-main)
  color var(--efu-white)

h1
  font-size 2em
  margin .67em 0

h1, h2, h3, h4, h5, h6
  padding-top 0
  padding-left 0
  font-weight 700
  position relative
  margin .5rem 0
  color var(--efu-fontcolor)

hr
  margin .5rem 0
  border 1px dashed var(--efu-theme-op)

a
  color var(--efu-fontcolor)
  text-decoration none
  transition all .3s ease 0s
  overflow-wrap break-word
  -webkit-user-drag none

abbr[title]
  border-bottom none
  text-decoration underline dotted

b, strong
  font-weight bolder

code, kbd, samp, pre
  font-family $code-font-family
  font-size $code-font-size

small
  font-size 80%

sub, sup
  font-size 75%
  line-height 0
  position relative
  vertical-align baseline

sub
  bottom -.25em

sup
  top -.5em

button, input, optgroup, select, textarea
  font-family inherit
  font-size 100%
  line-height 1.15
  margin 0

button, input
  overflow visible

input::placeholder
  color var(--efu-gray)

button, select
  text-transform none

[type=button], [type=reset], [type=submit], button
  appearance button

fieldset
  padding .35em .75em .625em

legend
  box-sizing border-box
  color inherit
  display table
  max-width 100%
  padding 0
  white-space normal

progress
  vertical-align baseline

textarea
  overflow auto

[type=checkbox], [type=radio]
  box-sizing border-box
  padding 0

[type=number]::-webkit-inner-spin-button, [type=number]::-webkit-outer-spin-button
  height auto

[type=search]
  appearance textfield
  outline-offset -2px

[type=search]::-webkit-search-decoration
  appearance none

input[type=checkbox] + label::before, input[type=radio] + label::before
  content ''
  display inline-block
  width 16px
  height 16px
  border 1px solid #999
  background-color #fff
  margin-right 5px
  vertical-align middle

::-webkit-file-upload-button
  appearance button
  font inherit

summary
  display list-item

template
  display none

[hidden]
  display none

input::placeholder
  color var(--efu-fontcolor)

.table-wrap
  overflow-x scroll
  margin 0 0 1rem

table
  width 100%
  border-spacing 0
  border-collapse collapse
  empty-cells show

  +maxWidth768()
    display block
    overflow-x auto

    &::-webkit-scrollbar
      display none

table thead
  background var(--efu-secondbg)

table td, table th
  padding .3rem .6rem
  border var(--style-border-always)
  vertical-align middle
  min-width 100px

button
  padding 0
  outline 0
  border none
  color var(--efu-fontcolor)
  background 0 0
  cursor pointer

img
  border-style none
  max-width 100%
  transition all .2s ease 0s
  -webkit-user-drag none

img:not([src]), img[src=""]
  opacity 0

.img-alt
  font-size 12px
  margin 0
  margin-top 8px
  color var(--efu-secondtext)

.is-center
  text-align center
  display flex
  flex-wrap wrap
  justify-content center
  flex-direction row
  align-items center

iframe
  border-radius 12px

.copy-true
  user-select all

.pull-left
  float left

.pull-right
  float right

blockquote
  border var(--style-border-always)
  background-color var(--efu-secondbg)
  color var(--efu-secondtext)
  border-radius 8px
  margin .5rem 0
  padding .5rem .8rem

li, ul
  list-style none
  display block
  margin 0
  padding 0

[data-theme=dark] img
  filter brightness(1)

a.extend
  .next
    right 0

  .prev
    left 0

b, strong
  color var(--efu-lighttext)

.button--animated
  border-radius 8px
  transition .3s
  position relative

i.solitude
  font-size 20px
  line-height 1
  font-synthesis style

#body-wrap
  display flex
  flex-direction column
  min-height 100vh
  justify-content space-between

.layout
  display flex
  margin 0 auto
  padding 0 1.5rem
  width 100%
  max-width 1200px
  +maxWidth768()
    padding 0

  &#content-inner
    position relative
    max-width 1400px
    flex-grow 1

    /.post &
      margin-top 16px

  &.hide-aside
    > div:first-child
      width 100%

  > div:first-child
    width calc(100% - 300px)

    .hide-aside &
      width 100%

    +maxWidth1200()
      width 100%

  > #post, #category, #tag, #archive
    align-self flex-start
    animation slide-in .6s .1s backwards
    position relative
    if hexo-config('aside.position') == 0
      order 2

    +maxWidth768()
      box-shadow none
      border none
      padding 0 1rem
  > #post
    padding 0

.show
  opacity 1 !important
  pointer-events all !important

span.tags-punctuation
  i
    font-weight bold
    font-size 12px
    transition none
    margin-right 2px
    opacity .4

    #post-info &
      font-size 14px
      line-height 32px

if hexo-config('memorial.enable')
  .memorial
    -webkit-filter grayscale(100%)
    -moz-filter grayscale(100%)
    -ms-filter grayscale(100%)
    -o-filter grayscale(100%)
    filter grayscale(100%)

if hexo-config('background.enable')
  #global_bg
    position fixed
    opacity hexo-config('background.opacity')
    width 100%
    height 100%
    background-image url(hexo-config('background.light'))
    background-size cover
    background-position center
    pointer-events none
    background-repeat no-repeat

    [data-theme=dark] &
      background-image url(hexo-config('background.dark'))
