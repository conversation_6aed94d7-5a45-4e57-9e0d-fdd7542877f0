Español 丨[English](README_en-US.md)丨[简体中文](README.md)丨[繁体中文](README_zh-Hant.md)

<div align="center">

<img width="70%" src=".github/screenshot.avif" />

Un tema elegante para Hexo que soporta lazy loading, PWA, Latex y múltiples sistemas de comentarios.

Diseño del tema totalmente autorizado por [@<PERSON> Hong Heo](https://github.com/zhheo)

![Paquete npm](https://img.shields.io/npm/v/hexo-theme-solitude)
![Licencia](https://img.shields.io/github/license/everfu/hexo-theme-solitude?color=FF5531)
[![Estrellas](https://img.shields.io/github/stars/everfu/hexo-theme-solitude)](https://github.com/everfu/hexo-theme-solitude/stargazers)
[![Descargas NPM](https://img.shields.io/npm/d18m/hexo-theme-solitude)](https://www.npmjs.com/package/hexo-theme-solitude)

![Versión hexo](https://img.shields.io/badge/hexo-7.0.0+-blue?logo=hexo&logoColor=white)
![Versión node](https://img.shields.io/badge/node-14.0.0+-white?logo=node.js&logoColor=white)
![JetBrains](https://img.shields.io/badge/jetbrains-support-white?logo=jetbrains)

![Visitas página](https://komarev.com/ghpvc/?username=hexo-theme-solitude&color=blue)
![jsdelivr](https://img.shields.io/jsdelivr/npm/hd/hexo-theme-solitude)
![Publicación npm](https://img.shields.io/github/actions/workflow/status/everfu/hexo-theme-solitude/npm-publish.yml)

</div>

## Características

- Lazy loading de páginas (Pjax), imágenes (LazyLoad) y aplicación offline (PWA)
- Comentarios (Twikoo, Waline, Valine, Artalk, Giscus), soporta comentarios duales
- Cambio de modo claro y oscuro (ColorMode)
- Lightbox (medium-zoom, fancybox)
- Fórmulas matemáticas (Latex)
- Páginas destacadas: Artículos instantáneos, Mi equipo, Herramientas en línea, Sala de música, Enlaces de amigos, Página de álbum, Página de Douban, Página de mensajes tipo "bala"
- Funciones de artículos: Resumen AI, resaltado de código

> Si tienes alguna pregunta, por favor abre un [issue](https://github.com/everfu/hexo-theme-solitude/issues)

## Aplicación

1. Instala usando el paquete NPM

    ```bash
    npm i hexo-theme-solitude
    ```

2. Aplica el tema

    ```yaml
    theme: solitude
    ```

Visita la [Documentación](https://solitude.js.org/) para más información.

## Comunidad

[![Discord](https://img.shields.io/discord/1266610921942548553?logo=discord&label=discord&logoColor=white)](https://discord.gg/HZXAnK4Sut)
[![Grupo QQ](https://img.shields.io/badge/QQ%20Group-948375336-FFD700?logo=Tencent-QQ&logoColor=white)](https://qm.qq.com/q/mxfomMvJPG)

## Patrocinadores

<a href="https://edgeone.ai/zh?from=github">
    <img src="./.github/edgeone.avif" width="250">
</a>

[La aceleración CDN y la protección de seguridad de este proyecto son patrocinadas por Tencent EdgeOne](https://edgeone.ai/zh?from=github)

---

<a href="https://yxvm.com/">
    <img src="./.github/support.avif" style="border-radius:8px" width="250">
</a>

[NodeSupport](https://github.com/NodeSeekDev/NodeSupport) ha patrocinado este proyecto

## Licencia

> Por favor, mantén la información de copyright del tema

[![Estado FOSSA](https://app.fossa.com/api/projects/git%2Bgithub.com%2Fvalor-x%2Fhexo-theme-solitude.svg?type=small)](https://app.fossa.com/projects/git%2Bgithub.com%2Fvalor-x%2Fhexo-theme-solitude?ref=badge_large)

Licencia [Apache-2.0](./LICENSE) &copy; 2025-presente [everfu](https://github.com/everfu)