/* 弹窗样式 */
.winbox {
    border-radius: 12px;
    overflow: hidden;
}
/* 修复全屏按钮可能导致的bug */
.wb-full {
    display: none;
}
.wb-min {
    background-position: center;
}
[data-theme='dark'] .wb-body,
[data-theme='dark'] #changeBgBox {
    background: #333 !important;
}
/* 弹窗内背景选项的容器 */
.bgbox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
/* 背景选项的通用样式 */
.pimgbox,
.imgbox,
.box {
    width: 166px;
    margin: 10px;
    background-size: cover;
    cursor: pointer;
}
.pimgbox,
.imgbox {
    border-radius: 10px;
    overflow: hidden;
}
.pimgbox { height: 240px; } /* 手机壁纸尺寸 */
.imgbox { height: 95px; }  /* 电脑壁纸尺寸 */
.box { height: 100px; } /* 纯色/渐变色块尺寸 */

/* 移动端适配 */
@media screen and (max-width: 768px) {
    .pimgbox,
    .imgbox,
    .box {
        height: 73px;
        width: 135px;
    }
    .pimgbox {
        height: 205px;
    }
    .wb-min {
        display: none;
    }
    #changeBgBox .wb-body::-webkit-scrollbar {
        display: none;
    }
}