.post-reward
    .reward-button(title=_p('award.tipping') onclick="AddRewardMask()")
        i.solitude.fas.fa-heart
        =_p('award.tipping')
    .reward-main
        ul.reward-all
            span.reward-title=theme.post.award.title
            ul.reward-group
                - let list = theme.post.award.list || []
                for item in list
                    li.reward-item
                        a(href=url_for(item.url))
                            img.post-qr-code-img.nolazyload(src=item.qcode, alt=item.name, style="border-color: " + item.color)
                        .post-qr-code-desc=item.name
            a.reward-main-btn(href=url_for(theme.post.award.appreciators))
                .reward-text= _p('award.title')
                .reward-dec=theme.post.award.desc

script.
    function RemoveRewardMask() {
        let rewardMainElements = document.querySelectorAll(".reward-main");
        let quitBoxElement = document.querySelector("#quit-box");

        rewardMainElements.forEach(element => {
            element.style.display = "none";
        });

        if (quitBoxElement) {
            quitBoxElement.style.display = "none";
        }
    }

    function AddRewardMask() {
        let rewardMainElements = document.querySelectorAll(".reward-main");
        let quitBoxElement = document.querySelector("#quit-box");

        rewardMainElements.forEach(element => {
            element.style.display = "flex";
        });

        if (quitBoxElement) {
            quitBoxElement.style.display = "flex";
        }
    }