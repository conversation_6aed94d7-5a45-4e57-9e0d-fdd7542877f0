<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>第四章：内容管理 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="第四章：内容管理"><meta name="application-name" content="第四章：内容管理"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="第四章：内容管理"><meta property="og:url" content="https://prorise666.site/posts/19658.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第四章：内容管理在前面的章节中，我们聚焦于“交易”本身，学习了如何设计商品、订单、促销等核心功能。但一个成功的电商平台，绝不仅仅是一个冷冰冰的交易场所。它还需要有温度、有吸引力、能够留住用户的内容。这一章，我们就来学习如何管理这些“内容”。 4.1 内容管理系统4.1.1 内容管理系统的定义在我早期"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp"><meta name="description" content="第四章：内容管理在前面的章节中，我们聚焦于“交易”本身，学习了如何设计商品、订单、促销等核心功能。但一个成功的电商平台，绝不仅仅是一个冷冰冰的交易场所。它还需要有温度、有吸引力、能够留住用户的内容。这一章，我们就来学习如何管理这些“内容”。 4.1 内容管理系统4.1.1 内容管理系统的定义在我早期"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/19658.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"第四章：内容管理",postAI:"true",pageFillDescription:"第四章：内容管理, 4.1 内容管理系统, 4.1.1 内容管理系统的定义, 4.1.2 内容管理系统的原理, 4.2 店铺装修, 4.2.1 店铺装修的设计思路, 4.2.2 店铺装修的常见组件, 1. 商品类组件, 2. 图文类组件, 3. 营销类组件, 4. 其他类组件, 4.2.3 店铺装修的产品设计, 1. 整体产品架构, 2. 商家端（B端）设计：所见即所得的装修后台, 4.3 专题页产品设计, 4.3.1 什么是专题页, 4.3.2 专题页的需求分析, 4.3.3 专题页的产品设计, 1. 工厂：创建与管理模板, 2. 流水线：使用模板创建专题页, 3. 仓库：管理已创建的专题页, 4.4 频道页产品设计, 4.4.1 什么是频道页, 4.4.2 频道页的需求分析及产品设计, 4.5 本章总结第四章内容管理在前面的章节中我们聚焦于交易本身学习了如何设计商品订单促销等核心功能但一个成功的电商平台绝不仅仅是一个冷冰冰的交易场所它还需要有温度有吸引力能够留住用户的内容这一章我们就来学习如何管理这些内容内容管理系统内容管理系统的定义在我早期的产品生涯中如果运营同事想修改首页上的一句宣传语或者替换一张活动图片她需要给我提一个需求单我再排期给研发工程师工程师修改代码测试再发布上线整个流程非常繁琐效率极低为了解决这个问题我需要为运营团队提供一个可以自主高效地管理网站内容的后台工具这就是内容管理系统诞生的初衷从广义上讲我们之前课程中为自媒体平台设计的文章管理后台其实就是一个基础的它实现了最核心的一点让非技术人员如运营编辑可以通过一个可视化的后台去创建修改发布和管理网站上的内容而无需再依赖研发人员而在更严格的定义下一个专业的如上图案例所示其能力远不止于管理文章它是一个能够让运营人员在不写一行代码的情况下自由定义网站的页面布局栏目以及填充具体内容的强大软件系统它的核心思想在于将内容与展现彻底分离内容文字图片被存储在数据库中而展现页面的样式布局则由模板来控制运营人员只需要关心内容的生产而无需关心它最终会如何展示内容管理系统的原理要理解的原理我最喜欢用搭乐高来做比喻最小单位乐高积木这就好比是我们系统里的基础内容组件比如一个轮播图一个商品推荐模块一篇文字介绍不同的搭建拼装过程这就好比是我们的后台运营人员就像一个乐高大师他可以自由地选择用哪些积木以及把它们拼装在页面的哪个位置迥然各异的结果乐高成品这就好比是用户最终看到的前端页面同样是那些积木既可以搭成一辆跑车也可以搭成一座城堡这个搭乐高的过程在我的产品设计中被抽象为三个核心技术步骤基础组件首先我需要和研发团队一起把所有可能用到的内容元素都预先开发成一个个独立的可复用的组件例如轮播图组件商品推荐组件文章列表组件视频播放组件这些组件就是我们的乐高积木是构成页面的原子单位位置内容然后在后台我需要提供一个可视化的页面编辑器运营人员可以在这里像拖拽积木一样决定哪个组件内容应该出现在页面的哪个位置比如他可以决定把轮播图组件放在页面顶部然后为这个组件上传张要轮播的图片动态页面最后当用户访问这个页面时系统会根据运营人员在后台的配置实时地动态地将这些组件和它们绑定的内容从数据库中读取出来然后像搭积木一样瞬间组装成一个完整的网页呈现给用户总结一下内容管理系统本质上是一个将内容生产与技术开发解耦的软件系统它通过组件化的设计思路让运营人员可以像搭乐高一样灵活高效地创建和管理动态页面店铺装修在上一节我们学习了的底层原理现在我们就将这个强大的理论应用到电商平台最核心的场景之一店铺装修店铺装修的设计思路我为什么要设计店铺装修功能因为我必须满足商家个性化经营的核心诉求一个商家除了上架和售卖商品外更希望能方便地打造自己独特的店铺形象以区别于其他店铺吸引和留住属于自己的客户因此我给店铺装修的定义是一套允许商家或平台运营在无需编写代码的情况下对店铺页面进行动态配置的系统它的本质就是在电商产品中的典型运用它的底层设计思路完全源于我们之前学习的三步曲提供基础组件我为商家预先准备好各种与电商相关的装修材料设置位置内容商家可以通过一个可视化的后台自由地组合这些材料并填充自己的内容生成动态页面用户的店铺首页会根据商家的配置动态地生成实现千店千面店铺装修的常见组件那么作为产品经理我应该为商家提供一个怎样的装修材料市场呢我会提供哪些基础组件给他们使用为了让商家能清晰地找到自己想要的材料我将所有组件归纳为四大类别商品类图文类营销类及其他类下面我将为你逐一拆解每一类组件的设计商品类组件这是店铺装修中最核心最高频的组件类型定义常见的展示形式为了满足不同的陈列需求我至少会提供以上三种主流的样式大图模式适合突出展示单个爆款或主打新品双列模式在有限的屏幕空间内可以展示更多商品提升浏览效率分类滑动模式允许用户在同一个组件内通过横向滑动来切换和浏览不同分类的商品配置逻辑商家在使用这个组件时他的操作流程是清晰的三步首先选择一个心仪的展示样式然后从自己的商品库中添加商品最后再对这个组件的展示信息如标题要不要显示价格等进行微调组件信息拆解这张思维导图完整地展示了我为商品类组件设计的所有可配置项它允许商家对组件的每一个细节进行自定义最终以上所有的设计都会落地为像上图这样的后台操作界面商家在右侧进行配置左侧可以实时预览到最终的效果真正做到所见即所得图文类组件这是用于展示非商品内容的最具灵活性的组件常用于品牌宣传活动介绍榜单推荐等场景定义它的核心价值在于让商家可以用图文并茂的形式向用户传递信息讲述故事从而提升店铺的内容感和品牌调性常见的展示形式我为图文组件设计了多种主流的样式以满足不同的内容承载需求单图展示最简洁的形式通常用于店铺头图或者某个活动的巨幅海报视觉冲击力强轮播图形式可以在有限的区域内承载多张图片信息是首页黄金位置最常用的组件用于轮播展示多个重要活动或商品多图形式可以将多张小图以矩阵的方式进行陈列常用于买家秀热门榜单等场景配置逻辑商家配置图文组件的流程非常直观先选择一个自己喜欢的展示形式然后上传图片并为每张图片配置跳转链接可以链接到商品分类或活动页最后再对组件的样式如背景色间距进行微调组件信息拆解这张思维导图完整地展示了我为图文类组件设计的所有可配置项它主要分为图片和文字两个部分商家可以自由地组合使用例如他可以只上传图片构成一个图片广告也可以只使用文字发布一个店铺公告最终商家在后台的操作界面就像这样在右侧的配置面板他可以输入文字设置样式调整间距而在左侧就能实时地看到页面的最终效果真正做到所见即所得营销类组件这是将我们之前在第三章设计的各种营销活动以组件的形式直接移植到店铺首页的强大工具页面的动态配置主要是为了满足各种活动而活动中往往具备营销推广信息因此除了常规图片和商品配置外还有使用非常频繁的营销类组件定义它的核心目的是让店铺内正在进行的营销活动能够在首页最显眼的位置得到曝光和引流从而提升活动的参与率和转化率常见的营销类型通过这个组件商家可以在首页搭建出各式各样的活动楼层例如秒杀类型展示正在进行或即将开始的秒杀商品并带有倒计时营造紧张的抢购氛围拼团类型以列表或卡片的形式展示店铺内的拼团商品优惠券类型将店铺内可领取的优惠券直接展示出来方便用户一键领取配置逻辑商家使用此组件的逻辑是先从下拉菜单中选择一种营销类型如秒杀然后系统会让他去关联一个已经创建好的具体活动最后再对这个活动楼层的展示样式进行一些简单的编辑组件信息拆解这张思维导图归纳了营销类组件的可配置项核心就是组件类型和添加优惠活动这两步这张图完美地诠释了营销类组件设计的灵活性和扩展性当商家在后台选择秒杀类型时配置面板就会让他去关联一个已经创建好的秒杀活动选择拼团类型时配置面板则会让他去关联一个拼团活动选择优惠券类型时他则可以勾选多张希望展示给用户的优惠券通过这种根据不同类型动态变换配置项的设计我用一个营销类组件就兼容了未来所有可能新增的营销玩法是典型的对扩展开放对修改关闭的设计原则的体现其他类组件最后我们来看其他类组件我将它定义为除商品图文营销之外那些用于完善页面布局优化用户导航体验的基础性辅助组件这类组件中虽然包含了搜索框辅助空白分割线等但从产品设计的角度看最具设计价值和复杂性的是店铺自定义分类导航组件我将重点为你拆解这个组件的设计思路核心矛盾后台类目前台导航要理解这个组件我们必须先理解一个平台电商的底层逻辑后台类目平台的标准语言为了方便平台管理和统一搜索所有商品在上传时都必须归属到平台预设的标准化的后台类目中这个类目体系是庞大且固定的就像一个国家图书馆的图书分类法如服装鞋包女鞋高跟鞋商家通常无法修改前台导航商家的营销语言但是一个商家从他自己经营的角度可能完全不想按照平台的类目来组织商品他可能希望在店铺首页设置一个像上图这样包含店长推荐夏日清凉好物新品速递等更具营销感和个性化的店铺前台导航这个导航是直接呈现给顾客的必须灵活可由商家自定义解决方案建立映射关系那么我作为产品经理如何调和后台类目的固定性与前台导航的灵活性之间的矛盾呢答案就是在产品设计中建立一套映射关系这张后台截图就揭示了这种映射关系当一个商家上传商品时他需要做两件事选择平台类目他必须为商品选择一个后台类目例如图中的食品饮料糖果巧克力巧克力这是为了让平台能认识索引这个商品选择店铺分组同时他还可以为商品选择一个或多个自己创建的店铺分组即自定义分类例如图中的新品上架这是为了让这个商品能够出现在他自己店铺首页的新品这个导航栏下面店铺导航栏组件的设计基于以上逻辑我设计的店铺导航栏组件就水到渠成了它包含了两个部分分类管理后台首先我需要在商家中心里为商家提供一个独立的店铺分类管理功能在这里他们可以像管理文章目录一样自由地创建编辑排序自己的导航项如首页新品活动关于我们装修页的组件配置在店铺装修页面商家可以将这个导航栏组件拖拽到页面上系统会自动读取商家在步骤中创建好的分类并生成导航栏当端用户点击某个导航项如新品时系统就会自动筛选并展示出所有被商家打上新品这个标签的商品通过这种后台管分类前台配商品的映射设计我就完美地解决了平台与商家在商品组织方式上的核心矛盾除了最复杂的导航栏组件其他类还包括一些简单的布局工具如搜索框组件提供店内搜索功能辅助空白和分割线组件用于调整页面布局和呼吸感这些组件的配置相对简单主要是样式和尺寸的调整这里就不再赘述店铺装修的产品设计整体产品架构通过上面的讲解接下来需要考虑下我们应该在哪些端口提供怎样的产品功能在着手设计具体界面前我首先要从宏观上规划整个系统的产品架构我的设计将整个店铺装修系统拆分为两个相辅相成的部分商家端端和用户端端商家端端装修工厂这是我提供给商家的核心工具它的定位是一个功能强大的装修工厂商家可以在这里像室内设计师一样对自己的店铺进行随心所欲的设计和改造它必须包含组件的选择页面的编辑内容的填充等一系列后台功能用户端端品牌橱窗这是最终呈现给消费者的店铺页面它的定位是一个精致的品牌橱窗它的唯一任务就是忠实地美观地将商家在端配置好的装修效果给渲染和展示出来商家端端设计所见即所得的装修后台对于商家来说装修后台的体验必须直观易用不能有太高的学习门槛为此我设计了一个所见即所得的三栏式布局这个后台的核心由三个区域构成左侧组件库这里是装修材料市场陈列着我们在上一节设计的所有基础组件商品图文营销等中间实时预览区这里是装修画布以手机模型的样式实时地地展示店铺最终的模样右侧配置面板这里是工具箱当商家在中间的画布上选中某一个组件时这个区域就会浮现提供针对该组件的所有详细配置项上图就是一个真实的商家端装修后台我们可以清晰地看到整个交互流程商家从左侧的组件库中将一个大图广告组件拖拽到中间的手机预览区中当他点击这个广告后右侧就会立刻浮现出针对这个广告的设置选项例如上传图片修改样式设置跳转链接等整个过程非常流畅直观专题页产品设计在掌握了店铺装修这一赋予商家个性的工具后我们还需要一个能让平台运营人员针对特定活动或主题快速搭建聚合页面的强大武器这就是专题页什么是专题页我给专题页的定义是一个围绕着特定主题聚合了多种内容元素如商品图片优惠券文章等的独立页面它的核心价值在于解决了大促活动中信息碎片化的问题如果没有专题页一个大型活动例如双十一手机会场的各种信息会散落在的各个角落用户无法形成整体认知而专题页就是将所有相关信息都汇集到一个统一的入口为用户打造一个一站式的沉浸式购物场景我们来看京东的这个案例为了联合利华超级品牌日这个活动他们打造了一个专题页这个页面里有活动头图有代言人有优惠券有不同楼层的商品推荐所有与活动相关的信息都被聚合在了这一个页面里为用户提供了沉浸式一站式的购物体验极大地提升了活动的转化效率专题页的需求分析在大型电商平台中运营活动是常态每周甚至每天都可能有新的促销主题上线如果每次活动都需要研发人员去从零开发一个新页面效率会极其低下运营的需求会被严重阻塞因此我作为产品经理在设计这个功能时核心的需求就是打造一个能让运营人员高效率低成本可复用地批量生产活动专题页的后台系统专题页的产品设计要实现高效率和可复用我的核心设计思路是将专题页的生产拆分为两个阶段第一阶段由更专业的设计师或高级运营人员负责创建模板第二阶段由一线的普通运营人员负责使用模板来快速生成页面上面这张流程图完整地展示了我这套两阶段的设计思想工厂创建与管理模板创建模板的过程就和我们之前学习的店铺装修非常相似高级用户设计师或高级运营可以使用我们提供的基础组件商品图文营销等通过拖拽的方式自由地搭建出一个页面的通用框架或版式例如他可以创建一个双十一主会场模板包含顶部轮播图中腰部秒杀楼层底部商品列表等固定结构所有搭建好的模板都会统一存放在模板列表中形成一个可供随时调用的模板库运营主管可以在这里对模板进行启用停用等管理确保一线运营人员只能使用审核过的规范的模板流水线使用模板创建专题页顺着上面梳理出的流程先来考虑下端如何让商家使用模板创建专题页包含哪些页面及功能当模板库搭建好之后一线运营人员创建专题页的过程就变成了一个极其简单的流水线作业我为他们设计了一个三步走的创建向导第一步选择模板当运营人员接到一个新活动需求时例如做一个夏季清仓专题他创建专题页的第一步就是从我们预设好的模板库中选择一个最符合本次活动调性的模板第二步配置信息选好模板后运营就进入了填空模式他完全不需要关心页面的布局和样式只需要根据模板预留好的坑位上传对应的素材如专题主图并从商品库中选择要填充的商品即可第三步创建完成完成信息配置后点击创建一个全新的精美的专题页就瞬间生成了运营人员可以点击预览也可以直接返回列表仓库管理已创建的专题页所有通过模板创建好的专题页都会进入这张专题列表进行统一的仓储和管理运营可以在这里精准地控制每一个专题页的上线下线状态通过设置活动起止时间并对已上线的活动进行后续的编辑或查看数据等操作通过这套模板化流程化的产品设计我成功地将原本可能需要数天开发周期的专题页搭建工作变成了运营人员可以在几分钟内就高效完成的常规工作极大地提升了整个公司的运营效率和活动的迭代速度频道页产品设计我们最后来学习一种在大型平台中承担着二级门户角色的重要页面频道页什么是频道页我给频道页的定义是一个大型业务分类的常态化的首页它和我们之前学习的专题页有本质的区别专题页通常是临时的活动导向的生命周期短例如双十一手机会场频道页则是常态化的分类导向的是内一个固定的长期的流量入口例如手机频道女装频道只有当一个平台的业务体量足够大某一个垂直品类如美妆护肤的运营足够复杂和精细时才需要为它建立一个专属的频道页我们来看京东的这个案例这两个频道页都不是一个简单的商品列表它们都聚合了二级分类导航品牌精选促销活动热门榜单等多种内容形态每一个频道页都像是一个美妆护肤或运动户外领域里的小首页承担着对这个垂直品类进行深度运营和流量分发的职责频道页的需求分析及产品设计需求分析我为什么需要设计一个独立的频道页系统而不是直接用店铺装修功能来搭建呢核心需求在于业务的垂直深度和运营的独立性对于一个大型平台来说电脑手机女装等一级类目其体量和运营复杂度堪比一个小型的垂直电商他们需要一个专属的高度自定义的首页来承载该品类下所有的内容和活动从而更好地引导用户分发流量产品设计高效率的产品复用在明确了需求后我进行产品设计时发现频道页的搭建过程与我们上一节学习的专题页在底层逻辑上是完全一致的因此我完全可以复用模板化流程化这套成熟的设计思路用一套后台系统同时支撑起专题页和频道页这两种核心的内容承载形式这不仅极大地节约了研发资源也保证了运营后台体验的一致性第一步工厂创建频道页模板同样由高级运营或设计师使用我们标准化的组件库来搭建不同频道如手机频道女装频道的通用模板第二步流水线使用模板创建更新频道页一线的品类运营人员只需要像流水线作业一样选择一个对应自己品类的模板然后填空就能快速生成或更新自己的频道页内容第三步仓库管理频道页所有创建好的频道页也会进入一个专属的列表进行统一管理运营可以在这里控制每一个频道页的发布状态和内容通过这种高度的产品复用我就能用最低的成本最高效地满足不同业务线的内容运营需求本章总结在本章我们系统性地学习了电商内容侧的产品设计核心思想我们明确了所有内容型产品设计的底层灵魂就是内容管理系统它的本质是将内容与展现分离从而赋予非技术人员自主管理内容的能力三大应用我们深入探讨了在电商平台中的三大核心应用店铺装修赋予商家千店千面的个性化能力专题页赋予平台运营高效率低成本地搭建活动页的能力频道页赋予核心品类垂直化精细化地运营其二级门户的能力底层哲学而支撑起这所有应用场景的底层设计哲学都是将内容与展现分离通过组件化提供装修材料再通过模板化提升生产效率掌握了这套思想你就能应对任何复杂的内容型产品的设计挑战至此我们第四章的学习全部结束",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-26 21:11:09",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%9B%9B%E7%AB%A0%EF%BC%9A%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86"><span class="toc-text">第四章：内容管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-1-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F"><span class="toc-text">4.1 内容管理系统</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-1-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-text">4.1.1 内容管理系统的定义</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-2-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F%E7%9A%84%E5%8E%9F%E7%90%86"><span class="toc-text">4.1.2 内容管理系统的原理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-2-%E5%BA%97%E9%93%BA%E8%A3%85%E4%BF%AE"><span class="toc-text">4.2 店铺装修</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-1-%E5%BA%97%E9%93%BA%E8%A3%85%E4%BF%AE%E7%9A%84%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-text">4.2.1 店铺装修的设计思路</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-2-%E5%BA%97%E9%93%BA%E8%A3%85%E4%BF%AE%E7%9A%84%E5%B8%B8%E8%A7%81%E7%BB%84%E4%BB%B6"><span class="toc-text">4.2.2 店铺装修的常见组件</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%95%86%E5%93%81%E7%B1%BB%E7%BB%84%E4%BB%B6"><span class="toc-text">1. 商品类组件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9B%BE%E6%96%87%E7%B1%BB%E7%BB%84%E4%BB%B6"><span class="toc-text">2. 图文类组件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E8%90%A5%E9%94%80%E7%B1%BB%E7%BB%84%E4%BB%B6"><span class="toc-text">3. 营销类组件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E5%85%B6%E4%BB%96%E7%B1%BB%E7%BB%84%E4%BB%B6"><span class="toc-text">4. 其他类组件</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-3-%E5%BA%97%E9%93%BA%E8%A3%85%E4%BF%AE%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">4.2.3 店铺装修的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%95%B4%E4%BD%93%E4%BA%A7%E5%93%81%E6%9E%B6%E6%9E%84"><span class="toc-text">1. 整体产品架构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%95%86%E5%AE%B6%E7%AB%AF%EF%BC%88B%E7%AB%AF%EF%BC%89%E8%AE%BE%E8%AE%A1%EF%BC%9A%E6%89%80%E8%A7%81%E5%8D%B3%E6%89%80%E5%BE%97%E7%9A%84%E8%A3%85%E4%BF%AE%E5%90%8E%E5%8F%B0"><span class="toc-text">2. 商家端（B端）设计：所见即所得的装修后台</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-3-%E4%B8%93%E9%A2%98%E9%A1%B5%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">4.3 专题页产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-1-%E4%BB%80%E4%B9%88%E6%98%AF%E4%B8%93%E9%A2%98%E9%A1%B5"><span class="toc-text">4.3.1 什么是专题页</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-2-%E4%B8%93%E9%A2%98%E9%A1%B5%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">4.3.2 专题页的需求分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-3-%E4%B8%93%E9%A2%98%E9%A1%B5%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">4.3.3 专题页的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E2%80%9C%E5%B7%A5%E5%8E%82%E2%80%9D%EF%BC%9A%E5%88%9B%E5%BB%BA%E4%B8%8E%E7%AE%A1%E7%90%86%E6%A8%A1%E6%9D%BF"><span class="toc-text">1. “工厂”：创建与管理模板</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E2%80%9C%E6%B5%81%E6%B0%B4%E7%BA%BF%E2%80%9D%EF%BC%9A%E4%BD%BF%E7%94%A8%E6%A8%A1%E6%9D%BF%E5%88%9B%E5%BB%BA%E4%B8%93%E9%A2%98%E9%A1%B5"><span class="toc-text">2. “流水线”：使用模板创建专题页</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E2%80%9C%E4%BB%93%E5%BA%93%E2%80%9D%EF%BC%9A%E7%AE%A1%E7%90%86%E5%B7%B2%E5%88%9B%E5%BB%BA%E7%9A%84%E4%B8%93%E9%A2%98%E9%A1%B5"><span class="toc-text">3. “仓库”：管理已创建的专题页</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-4-%E9%A2%91%E9%81%93%E9%A1%B5%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">4.4 频道页产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-1-%E4%BB%80%E4%B9%88%E6%98%AF%E9%A2%91%E9%81%93%E9%A1%B5"><span class="toc-text">4.4.1 什么是频道页</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-2-%E9%A2%91%E9%81%93%E9%A1%B5%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E5%8F%8A%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">4.4.2 频道页的需求分析及产品设计</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-5-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">4.5 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">第四章：内容管理</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-26T13:11:09.139Z" title="更新于 2025-07-26 21:11:09">2025-07-26</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">7k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>20分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="第四章：内容管理"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/19658.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/19658.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">第四章：内容管理</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time><time itemprop="dateCreated datePublished" datetime="2025-07-26T13:11:09.139Z" title="更新于 2025-07-26 21:11:09">2025-07-26</time></header><div id="postchat_postcontent"><h1 id="第四章：内容管理"><a href="#第四章：内容管理" class="headerlink" title="第四章：内容管理"></a>第四章：内容管理</h1><p>在前面的章节中，我们聚焦于“交易”本身，学习了如何设计商品、订单、促销等核心功能。但一个成功的电商平台，绝不仅仅是一个冷冰冰的交易场所。它还需要有温度、有吸引力、能够留住用户的内容。这一章，我们就来学习如何管理这些“内容”。</p><h2 id="4-1-内容管理系统"><a href="#4-1-内容管理系统" class="headerlink" title="4.1 内容管理系统"></a>4.1 内容管理系统</h2><h3 id="4-1-1-内容管理系统的定义"><a href="#4-1-1-内容管理系统的定义" class="headerlink" title="4.1.1 内容管理系统的定义"></a>4.1.1 内容管理系统的定义</h3><p>在我早期的产品生涯中，如果运营同事想修改首页上的一句宣传语，或者替换一张活动图片，她需要给我提一个需求单，我再排期给研发工程师，工程师修改代码、测试、再发布上线……整个流程非常繁琐，效率极低。</p><p>为了解决这个问题，我需要为运营团队，提供一个可以“<strong>自主、高效地管理网站内容</strong>”的后台工具。这，就是内容管理系统（Content Management System, CMS）诞生的初衷。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150544242.png" alt="image-20250726150544242"></p><p>从广义上讲，我们之前课程中为自媒体平台设计的“文章管理”后台，其实就是一个基础的CMS。它实现了最核心的一点：<strong>让非技术人员（如运营、编辑），可以通过一个可视化的后台，去创建、修改、发布和管理网站上的内容</strong>，而无需再依赖研发人员。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150610613.png" alt="image-20250726150610613"></p><p>而在更严格的定义下，一个专业的CMS，如上图案例所示，其能力远不止于管理文章。它是一个能够让运营人员，在不写一行代码的情况下，自由定义网站的页面布局、栏目、以及填充具体内容的强大软件系统。</p><p>它的核心思想在于——<strong>将“内容”与“展现”彻底分离</strong>。内容（文字、图片）被存储在数据库中，而展现（页面的样式、布局）则由模板来控制。运营人员只需要关心“内容”的生产，而无需关心它最终会“如何展示”。</p><h3 id="4-1-2-内容管理系统的原理"><a href="#4-1-2-内容管理系统的原理" class="headerlink" title="4.1.2 内容管理系统的原理"></a>4.1.2 内容管理系统的原理</h3><p>要理解CMS的原理，我最喜欢用“<strong>搭乐高</strong>”来做比喻。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150706862.png" alt="image-20250726150706862"></p><ul><li><strong>最小单位（乐高积木）</strong>：这就好比是我们系统里的基础“<strong>内容组件</strong>”，比如一个轮播图、一个商品推荐模块、一篇文字介绍。</li><li><strong>不同的搭建（拼装过程）</strong>：这就好比是我们的<strong>CMS后台</strong>。运营人员就像一个乐高大师，他可以自由地选择用哪些“积木”，以及把它们拼装在页面的哪个位置。</li><li><strong>迥然各异的结果（乐高成品）</strong>：这就好比是用户最终看到的<strong>前端页面</strong>。同样是那些“积木”，既可以搭成一辆跑车，也可以搭成一座城堡。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150744252.png" alt="image-20250726150744252"></p><p>这个“搭乐高”的过程，在我的产品设计中，被抽象为三个核心技术步骤：</p><ol><li><strong>基础组件</strong>：首先，我需要和研发团队一起，把所有可能用到的内容元素，都预先开发成一个个独立的、可复用的“<strong>组件</strong>”。例如，‘轮播图组件’、‘商品推荐组件’、‘文章列表组件’、‘视频播放组件’。这些组件就是我们的“乐高积木”，是构成页面的原子单位。</li><li><strong>位置+内容</strong>：然后，在CMS后台，我需要提供一个可视化的页面编辑器。运营人员可以在这里，像“拖拽”积木一样，决定<strong>哪个组件（内容），应该出现在页面的哪个位置</strong>。比如，他可以决定“把轮播图组件放在页面顶部”，然后为这个组件上传5张要轮播的图片。</li><li><strong>动态页面</strong>：最后，当用户访问这个页面时，系统会根据运营人员在后台的配置，<strong>实时地、动态地</strong>将这些组件和它们绑定的内容，从数据库中读取出来，然后像“搭积木”一样，瞬间“组装”成一个完整的网页，呈现给用户。</li></ol><p>总结一下，内容管理系统（CMS），本质上是一个<strong>将内容生产与技术开发解耦</strong>的软件系统。它通过“<strong>组件化</strong>”的设计思路，让运营人员可以像搭乐高一样，灵活、高效地创建和管理动态页面。</p><hr><h2 id="4-2-店铺装修"><a href="#4-2-店铺装修" class="headerlink" title="4.2 店铺装修"></a>4.2 店铺装修</h2><p>在上一节，我们学习了CMS的底层原理。现在，我们就将这个强大的理论，应用到电商平台最核心的场景之一——<strong>店铺装修</strong>。</p><h3 id="4-2-1-店铺装修的设计思路"><a href="#4-2-1-店铺装修的设计思路" class="headerlink" title="4.2.1 店铺装修的设计思路"></a>4.2.1 店铺装修的设计思路</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151509850.png" alt="image-20250726151509850"></p><p>我为什么要设计“店铺装修”功能？因为我必须满足商家“<strong>个性化经营</strong>”的核心诉求。一个商家，除了上架和售卖商品外，更希望能方便地打造自己独特的店铺形象，以区别于其他店铺，吸引和留住属于自己的客户。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151519631.png" alt="image-20250726151519631"></p><p>因此，我给“店铺装修”的定义是：<strong>一套允许商家（或平台运营），在无需编写代码的情况下，对店铺页面进行动态配置的系统。</strong> 它的本质，就是CMS在电商产品中的典型运用。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151534604.png" alt="image-20250726151534604"></p><p>它的底层设计思路，完全源于我们之前学习的CMS三步曲：</p><ol><li><strong>提供基础组件</strong>：我为商家预先准备好各种与电商相关的“装修材料”。</li><li><strong>设置位置+内容</strong>：商家可以通过一个可视化的后台，自由地组合这些“材料”，并填充自己的内容。</li><li><strong>生成动态页面</strong>：用户的店铺首页，会根据商家的配置，动态地生成，实现“千店千面”。</li></ol><h3 id="4-2-2-店铺装修的常见组件"><a href="#4-2-2-店铺装修的常见组件" class="headerlink" title="4.2.2 店铺装修的常见组件"></a>4.2.2 店铺装修的常见组件</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151549452.png" alt="image-20250726151549452"></p><p>那么，作为产品经理，我应该为商家提供一个怎样的“装修材料市场”呢？我会提供哪些基础组件给他们使用？</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151603118.png" alt="image-20250726151603118"></p><p>为了让商家能清晰地找到自己想要的“材料”，我将所有组件，归纳为四大类别：<strong>商品类、图文类、营销类、及其他类</strong>。下面我将为你逐一拆解每一类组件的设计。</p><h4 id="1-商品类组件"><a href="#1-商品类组件" class="headerlink" title="1. 商品类组件"></a>1. 商品类组件</h4><p>这是店铺装修中最核心、最高频的组件类型。</p><ul><li><p><strong>定义</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151616933.png" alt="image-20250726151616933"></p></li><li><p><strong>常见的展示形式</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151629264.png" alt="image-20250726151629264">为了满足不同的陈列需求，我至少会提供以上三种主流的样式：</p><ul><li><strong>大图模式</strong>：适合突出展示单个爆款或主打新品。</li><li><strong>双列模式</strong>：在有限的屏幕空间内，可以展示更多商品，提升浏览效率。</li><li><strong>分类滑动模式</strong>：允许用户在同一个组件内，通过横向滑动，来切换和浏览不同分类的商品。</li></ul></li><li><p><strong>配置逻辑</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151700053.png" alt="image-20250726151700053">商家在使用这个组件时，他的操作流程是清晰的三步：首先选择一个心仪的“<strong>展示样式</strong>”，然后从自己的商品库中“<strong>添加商品</strong>”，最后再对这个组件的“<strong>展示信息</strong>”（如标题、要不要显示价格等）进行微调。</p></li><li><p><strong>组件信息拆解</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151726638.png" alt="image-20250726151726638">这张思维导图，完整地展示了我为“商品类组件”设计的所有可配置项。它允许商家对组件的每一个细节进行自定义。</p></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151835815.png" alt="image-20250726151835815">最终，以上所有的设计，都会落地为像上图这样的后台操作界面。商家在右侧进行配置，左侧可以实时预览到最终的效果，真正做到“所见即所得”。</p><hr><h4 id="2-图文类组件"><a href="#2-图文类组件" class="headerlink" title="2. 图文类组件"></a>2. 图文类组件</h4><p>这是用于展示非商品内容的、最具灵活性的组件，常用于品牌宣传、活动介绍、榜单推荐等场景。</p><ul><li><p><strong>定义</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152332242.png" alt="image-20250726152332242"><br>它的核心价值，在于让商家可以用图文并茂的形式，向用户传递信息、讲述故事，从而提升店铺的“内容感”和“品牌调性”。</p></li><li><p><strong>常见的展示形式</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152342998.png" alt="image-20250726152342998"><br>我为图文组件设计了多种主流的样式，以满足不同的内容承载需求：</p><ul><li><strong>单图展示</strong>：最简洁的形式，通常用于店铺头图、或者某个活动的巨幅海报，视觉冲击力强。</li><li><strong>轮播图形式</strong>：可以在有限的区域内，承载多张图片信息，是首页黄金位置最常用的组件，用于轮播展示多个重要活动或商品。</li><li><strong>多图形式</strong>：可以将多张小图以矩阵的方式进行陈列，常用于“买家秀”、“热门榜单”等场景。</li></ul></li><li><p><strong>配置逻辑</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152356474.png" alt="image-20250726152356474"><br>商家配置图文组件的流程非常直观：先选择一个自己喜欢的“<strong>展示形式</strong>”，然后上传图片并为每张图片“<strong>配置跳转链接</strong>”（可以链接到商品、分类或活动页），最后再对组件的“<strong>样式</strong>”（如背景色、间距）进行微调。</p></li><li><p><strong>组件信息拆解</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152415434.png" alt="image-20250726152415434"><br>这张思维导图，完整地展示了我为“图文类组件”设计的所有可配置项。它主要分为“图片”和“文字”两个部分，商家可以自由地组合使用。例如，他可以只上传图片，构成一个图片广告；也可以只使用文字，发布一个店铺公告。</p></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152454089.png" alt="image-20250726152454089"><br>最终，商家在后台的操作界面就像这样。在右侧的配置面板，他可以输入文字、设置样式、调整间距，而在左侧就能实时地看到页面的最终效果，真正做到“所见即所得”。</p><h4 id="3-营销类组件"><a href="#3-营销类组件" class="headerlink" title="3. 营销类组件"></a>3. 营销类组件</h4><p>这是将我们之前在第三章设计的各种营销活动，以“组件”的形式，直接“移植”到店铺首页的强大工具、页面的动态配置主要是为了满足各种活动，而活动中往往具备营销推广信息，因此除了常规图片和商品配置外，还有使用非常频繁的营销类组件</p><ul><li><p><strong>定义</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152548141.png" alt="image-20250726152548141"><br>它的核心目的，是让店铺内正在进行的营销活动，能够在首页最显眼的位置得到<strong>曝光和引流</strong>，从而提升活动的参与率和转化率。</p></li><li><p><strong>常见的营销类型</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152557494.png" alt="image-20250726152557494"><br>通过这个组件，商家可以在首页，搭建出各式各样的“活动楼层”，例如：</p><ul><li><strong>秒杀类型</strong>：展示正在进行或即将开始的秒杀商品，并带有倒计时，营造紧张的抢购氛围。</li><li><strong>拼团类型</strong>：以列表或卡片的形式，展示店铺内的拼团商品。</li><li><strong>优惠券类型</strong>：将店铺内可领取的优惠券，直接展示出来，方便用户一键领取。</li></ul></li><li><p><strong>配置逻辑</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152639022.png" alt="image-20250726152639022"><br>商家使用此组件的逻辑是：先从下拉菜单中“<strong>选择一种营销类型</strong>”（如秒杀），然后系统会让他去“<strong>关联一个已经创建好的具体活动</strong>”，最后再对这个活动楼层的“<strong>展示样式</strong>”进行一些简单的编辑。</p></li><li><p><strong>组件信息拆解</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152647885.png" alt="image-20250726152647885"><br>这张思维导图，归纳了营销类组件的可配置项，核心就是“<strong>组件类型</strong>”和“<strong>添加优惠活动</strong>”这两步。</p></li></ul><p>这张图完美地诠释了“营销类组件”设计的灵活性和扩展性。当商家在后台…</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152902959.png" alt="image-20250726152902959"></p><ul><li>…选择“<strong>秒杀</strong>”类型时，配置面板就会让他去关联一个已经创建好的“秒杀活动”。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152943608.png" alt="image-20250726152943608"></p><ul><li>…选择“<strong>拼团</strong>”类型时，配置面板则会让他去关联一个“拼团活动”。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152953465.png" alt="image-20250726152953465"></p><ul><li>…选择“<strong>优惠券</strong>”类型时，他则可以勾选多张希望展示给用户的优惠券。</li></ul><p>通过这种<strong>根据不同类型，动态变换配置项</strong>的设计，我用一个“营销类组件”，就兼容了未来所有可能新增的营销玩法，是典型的“<strong>对扩展开放，对修改关闭</strong>”的设计原则的体现。</p><hr><h4 id="4-其他类组件"><a href="#4-其他类组件" class="headerlink" title="4. 其他类组件"></a>4. 其他类组件</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154021085.png" alt="image-20250726154021085"></p><p>最后，我们来看“其他类组件”。我将它定义为：<strong>除商品、图文、营销之外，那些用于完善页面布局、优化用户导航体验的基础性辅助组件。</strong></p><p>这类组件中，虽然包含了搜索框、辅助空白、分割线等，但从产品设计的角度看，最具设计价值和复杂性的，是“<strong>店铺自定义分类导航</strong>”组件。我将重点为你拆解这个组件的设计思路。</p><p><strong>1. 核心矛盾：后台类目 vs. 前台导航</strong></p><p>要理解这个组件，我们必须先理解一个平台电商的底层逻辑。</p><ul><li><strong>后台类目：平台的“标准语言”</strong></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154039950.png" alt="image-20250726154039950"></p><p>为了方便平台管理和统一搜索，所有商品在上传时，都必须归属到平台预设的、标准化的“<strong>后台类目</strong>”中。这个类目体系是庞大且固定的，就像一个国家图书馆的图书分类法（如：服装鞋包 &gt; 女鞋 &gt; 高跟鞋），商家通常无法修改。</p><ul><li><strong>前台导航：商家的“营销语言”</strong></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154115185.png" alt="image-20250726154115185"></p><p>但是，一个商家从他自己经营的角度，可能完全不想按照平台的类目来组织商品。他可能希望在店铺首页，设置一个像上图这样，包含“<strong>店长推荐</strong>”、“<strong>夏日清凉好物</strong>”、“<strong>新品速递</strong>”等，更具营销感和个性化的“<strong>店铺前台导航</strong>”。这个导航是直接呈现给顾客的，必须灵活、可由商家自定义。</p><p><strong>2. 解决方案：建立映射关系</strong></p><p>那么，我作为产品经理，如何调和“后台类目的固定性”与“前台导航的灵活性”之间的矛盾呢？答案就是——<strong>在产品设计中，建立一套映射关系。</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154215194.png" alt="image-20250726154215194"></p><p>这张后台截图，就揭示了这种映射关系。当一个商家上传商品时，他需要做两件事：</p><ol><li><strong>选择平台类目</strong>：他必须为商品选择一个“后台类目”，例如图中的“食品饮料 &gt; 糖果/巧克力 &gt; 巧克力”。这是为了让<strong>平台</strong>能认识、索引这个商品。</li><li><strong>选择店铺分组</strong>：同时，他还可以为商品选择一个或多个自己创建的“<strong>店铺分组</strong>”（即自定义分类），例如图中的“新品上架”。这，是为了让这个商品能够出现在他<strong>自己店铺</strong>首页的“新品”这个导航栏下面。</li></ol><p><strong>3. “店铺导航栏组件”的设计</strong></p><p>基于以上逻辑，我设计的“店铺导航栏组件”就水到渠成了。它包含了两个部分：</p><ul><li><strong>A. 分类管理后台</strong>：首先，我需要在“商家中心”里，为商家提供一个独立的“<strong>店铺分类管理</strong>”功能。在这里，他们可以像管理文章目录一样，自由地创建、编辑、排序自己的导航项（如：首页、新品、活动、关于我们）。</li><li><strong>B. 装修页的组件配置</strong>：在店铺装修页面，商家可以将这个“导航栏组件”拖拽到页面上。系统会自动读取商家在A步骤中创建好的分类，并生成导航栏。当C端用户点击某个导航项（如“新品”）时，系统就会自动筛选并展示出所有被商家打上“新品”这个标签的商品。</li></ul><p>通过这种“<strong>后台管分类，前台配商品</strong>”的映射设计，我就完美地解决了平台与商家在商品组织方式上的核心矛盾。</p><p>除了最复杂的导航栏组件，“其他类”还包括一些简单的布局工具，如<strong>搜索框组件</strong>（提供店内搜索功能）、<strong>辅助空白和分割线组件</strong>（用于调整页面布局和呼吸感），这些组件的配置相对简单，主要是样式和尺寸的调整，这里就不再赘述。</p><hr><h3 id="4-2-3-店铺装修的产品设计"><a href="#4-2-3-店铺装修的产品设计" class="headerlink" title="4.2.3 店铺装修的产品设计"></a>4.2.3 店铺装修的产品设计</h3><h4 id="1-整体产品架构"><a href="#1-整体产品架构" class="headerlink" title="1. 整体产品架构"></a>1. 整体产品架构</h4><p>通过上面的讲解，接下来需要考虑下我们应该在哪些端口提供怎样的产品功能？</p><p>在着手设计具体界面前，我首先要从宏观上，规划整个系统的产品架构。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726160112838.png" alt="image-20250726160112838"></p><p>我的设计，将整个店铺装修系统，拆分为两个相辅相成的部分：<strong>商家端（B端）<strong>和</strong>用户端（C端）</strong>。</p><ul><li><strong>商家端 (B端) - “装修工厂”</strong>：这是我提供给商家的核心工具。它的定位是一个功能强大的“装修工厂”，商家可以在这里，像室内设计师一样，对自己的店铺进行随心所欲的设计和改造。它必须包含<strong>组件的选择、页面的编辑、内容的填充</strong>等一系列后台功能。</li><li><strong>用户端 (C端) - “品牌橱窗”</strong>：这是最终呈现给消费者的店铺页面。它的定位是一个精致的“品牌橱窗”，它的唯一任务，就是<strong>忠实地、美观地</strong>，将商家在B端配置好的装修效果，给渲染和展示出来。</li></ul><h4 id="2-商家端（B端）设计：所见即所得的装修后台"><a href="#2-商家端（B端）设计：所见即所得的装修后台" class="headerlink" title="2. 商家端（B端）设计：所见即所得的装修后台"></a>2. 商家端（B端）设计：所见即所得的装修后台</h4><p>对于商家来说，装修后台的体验必须直观、易用，不能有太高的学习门槛。为此，我设计了一个“<strong>所见即所得</strong>”（WYSIWYG）的三栏式布局。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726160119503.png" alt="image-20250726160119503"></p><p>这个后台的核心，由三个区域构成：</p><ol><li><strong>左侧：组件库</strong>：这里是“装修材料市场”，陈列着我们在上一节设计的所有基础组件（商品、图文、营销等）。</li><li><strong>中间：实时预览区</strong>：这里是“装修画布”，以手机模型的样式，实时地、1:1地展示店铺最终的模样。</li><li><strong>右侧：配置面板</strong>：这里是“工具箱”，当商家在中间的画布上选中某一个组件时，这个区域就会浮现，提供针对该组件的所有详细配置项。</li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/df4eeb09bf2108cd642388d650247522.png" alt="img"></p><p>上图就是一个真实的商家端装修后台。我们可以清晰地看到整个交互流程：商家从<strong>左侧</strong>的组件库中，将一个“大图广告”组件，拖拽到<strong>中间</strong>的手机预览区中。当他点击这个广告后，<strong>右侧</strong>就会立刻浮现出针对这个广告的设置选项，例如上传图片、修改样式、设置跳转链接等。整个过程非常流畅、直观。</p><hr><h2 id="4-3-专题页产品设计"><a href="#4-3-专题页产品设计" class="headerlink" title="4.3 专题页产品设计"></a>4.3 专题页产品设计</h2><p>在掌握了“店铺装修”这一赋予商家个性的工具后，我们还需要一个能让平台运营人员，针对特定活动或主题，快速搭建聚合页面的强大武器。这，就是“<strong>专题页</strong>”。</p><h3 id="4-3-1-什么是专题页"><a href="#4-3-1-什么是专题页" class="headerlink" title="4.3.1 什么是专题页"></a>4.3.1 什么是专题页</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161653423.png" alt="image-20250726161653423"></p><p>我给专题页的定义是：<strong>一个围绕着特定主题，聚合了多种内容元素（如商品、图片、优惠券、文章等）的独立页面。</strong></p><p>它的核心价值，在于解决了大促活动中，信息“<strong>碎片化</strong>”的问题。如果没有专题页，一个大型活动（例如，“双十一手机会场”）的各种信息，会散落在App的各个角落，用户无法形成整体认知。而专题页，就是将所有相关信息，都汇集到一个统一的入口，为用户打造一个“一站式”的沉浸式购物场景。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161714252.png" alt="image-20250726161714252"></p><p>我们来看京东的这个案例。为了“联合利华超级品牌日”这个活动，他们打造了一个专题页。这个页面里，有活动头图、有代言人、有优惠券、有不同楼层的商品推荐…所有与活动相关的信息，都被“聚合”在了这一个页面里，为用户提供了沉浸式、一站式的购物体验，极大地提升了活动的转化效率。</p><h3 id="4-3-2-专题页的需求分析"><a href="#4-3-2-专题页的需求分析" class="headerlink" title="4.3.2 专题页的需求分析"></a>4.3.2 专题页的需求分析</h3><p>在大型电商平台中，运营活动是常态。每周、甚至每天，都可能有新的促销主题上线。如果每次活动都需要研发人员去从零开发一个新页面，效率会极其低下，运营的需求会被严重阻塞。</p><p>因此，我作为产品经理，在设计这个功能时，核心的需求就是：<strong>打造一个能让运营人员“高效率、低成本、可复用”地批量生产活动专题页的后台系统。</strong></p><h3 id="4-3-3-专题页的产品设计"><a href="#4-3-3-专题页的产品设计" class="headerlink" title="4.3.3 专题页的产品设计"></a>4.3.3 专题页的产品设计</h3><p>要实现“高效率”和“可复用”，我的核心设计思路，是将专题页的生产，拆分为两个阶段：</p><ul><li><strong>第一阶段</strong>：由更专业的设计师或高级运营人员，负责“<strong>创建模板</strong>”。</li><li><strong>第二阶段</strong>：由一线的普通运营人员，负责“<strong>使用模板</strong>”来快速生成页面。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161759444.png" alt="image-20250726161759444"></p><p>上面这张流程图，完整地展示了我这套“两阶段”的设计思想。</p><h4 id="1-“工厂”：创建与管理模板"><a href="#1-“工厂”：创建与管理模板" class="headerlink" title="1. “工厂”：创建与管理模板"></a>1. “工厂”：创建与管理模板</h4><p>“创建模板”的过程，就和我们之前学习的“店铺装修”非常相似。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161819056.png" alt="image-20250726161819056"></p><p>高级用户（设计师或高级运营）可以使用我们提供的基础组件（商品、图文、营销等），通过拖拽的方式，自由地“搭建”出一个页面的通用框架或版式。例如，他可以创建一个“双十一主会场模板”，包含顶部轮播图、中腰部秒杀楼层、底部商品列表等固定结构。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161927025.png" alt="image-20250726161927025"></p><p>所有搭建好的模板，都会统一存放在“<strong>模板列表</strong>”中，形成一个可供随时调用的模板库。运营主管可以在这里，对模板进行启用、停用等管理，确保一线运营人员只能使用审核过的、规范的模板。</p><h4 id="2-“流水线”：使用模板创建专题页"><a href="#2-“流水线”：使用模板创建专题页" class="headerlink" title="2. “流水线”：使用模板创建专题页"></a>2. “流水线”：使用模板创建专题页</h4><p>“顺着上面梳理出的流程，先来考虑下B端如何让商家使用模板创建专题页，包含哪些页面及功能？</p><p>当模板库搭建好之后，一线运营人员创建专题页的过程，就变成了一个极其简单的“流水线”作业。我为他们设计了一个三步走的创建向导。</p><ul><li><strong>第一步：选择模板</strong></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162030043.png" alt="image-20250726162030043"></p><p>当运营人员接到一个新活动需求时（例如，做一个“夏季清仓”专题），他创建专题页的第一步，就是从我们预设好的模板库中，选择一个最符合本次活动调性的模板。</p><ul><li><strong>第二步：配置信息</strong></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162103204.png" alt="image-20250726162103204"></p><p>选好模板后，运营就进入了“<strong>填空模式</strong>”。他完全不需要关心页面的布局和样式，只需要根据模板预留好的“坑位”，上传对应的素材（如专题主图），并从商品库中选择要填充的商品即可。</p><ul><li><strong>第三步：创建完成</strong></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162122774.png" alt="image-20250726162122774"></p><p>完成信息配置后，点击创建，一个全新的、精美的专题页就瞬间生成了。运营人员可以点击“预览”，也可以直接返回列表。</p><h4 id="3-“仓库”：管理已创建的专题页"><a href="#3-“仓库”：管理已创建的专题页" class="headerlink" title="3. “仓库”：管理已创建的专题页"></a>3. “仓库”：管理已创建的专题页</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162134047.png" alt="image-20250726162134047"></p><p>所有通过模板创建好的专题页，都会进入这张“<strong>专题列表</strong>”进行统一的仓储和管理。运营可以在这里，精准地控制每一个专题页的<strong>上线/下线状态</strong>（通过设置活动起止时间），并对已上线的活动，进行后续的编辑或查看数据等操作。</p><p>通过这套“<strong>模板化、流程化</strong>”的产品设计，我成功地将原本可能需要数天开发周期的专题页搭建工作，变成了运营人员可以在几分钟内就高效完成的常规工作，极大地提升了整个公司的运营效率和活动的迭代速度。</p><hr><h2 id="4-4-频道页产品设计"><a href="#4-4-频道页产品设计" class="headerlink" title="4.4 频道页产品设计"></a>4.4 频道页产品设计</h2><p>我们最后来学习一种在大型平台中，承担着“二级门户”角色的重要页面——<strong>频道页</strong>。</p><h3 id="4-4-1-什么是频道页"><a href="#4-4-1-什么是频道页" class="headerlink" title="4.4.1 什么是频道页"></a>4.4.1 什么是频道页</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163039627.png" alt="image-20250726163039627"></p><p>我给<strong>频道页</strong>的定义是：<strong>一个大型业务分类的、常态化的首页。</strong></p><p>它和我们之前学习的“专题页”有本质的区别：</p><ul><li><strong>专题页</strong>：通常是<strong>临时的、活动导向的</strong>，生命周期短，例如“双十一手机会场”。</li><li><strong>频道页</strong>：则是<strong>常态化的、分类导向的</strong>，是App内一个固定的、长期的流量入口，例如“手机频道”、“女装频道”。</li></ul><p>只有当一个平台的业务体量足够大，某一个垂直品类（如“美妆护肤”）的运营足够复杂和精细时，才需要为它建立一个专属的“频道页”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163058003.png" alt="image-20250726163058003"></p><p>我们来看京东的这个案例。这两个频道页，都不是一个简单的商品列表。它们都聚合了二级分类导航、品牌精选、促销活动、热门榜单等多种内容形态。每一个频道页，都像是一个“美妆护肤”或“运动户外”领域里的“小首页”，承担着对这个垂直品类进行深度运营和流量分发的职责。</p><h3 id="4-4-2-频道页的需求分析及产品设计"><a href="#4-4-2-频道页的需求分析及产品设计" class="headerlink" title="4.4.2 频道页的需求分析及产品设计"></a>4.4.2 频道页的需求分析及产品设计</h3><p><strong>需求分析</strong></p><p>我为什么需要设计一个独立的频道页系统，而不是直接用“店铺装修”功能来搭建呢？核心需求在于<strong>业务的垂直深度和运营的独立性</strong>。</p><p>对于一个大型平台来说，“电脑”、“手机”、“女装”等一级类目，其体量和运营复杂度，堪比一个小型的垂直电商。他们需要一个专属的、高度自定义的“首页”，来承载该品类下所有的内容和活动，从而更好地引导用户、分发流量。</p><p><strong>产品设计：高效率的产品复用</strong></p><p>在明确了需求后，我进行产品设计时，发现频道页的搭建过程，与我们上一节学习的“<strong>专题页</strong>”，在底层逻辑上是<strong>完全一致的</strong>。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163146623.png" alt="image-20250726163146623"></p><p>因此，我完全可以复用“<strong>模板化、流程化</strong>”这套成熟的设计思路，用一套后台系统，同时支撑起“专题页”和“频道页”这两种核心的内容承载形式。这不仅极大地节约了研发资源，也保证了运营后台体验的一致性。</p><ul><li><p><strong>第一步：“工厂” - 创建频道页模板</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163202855.png" alt="image-20250726163202855"><br>同样，由高级运营或设计师，使用我们标准化的组件库，来搭建不同频道（如“手机频道”、“女装频道”）的通用“模板”。</p></li><li><p><strong>第二步：“流水线” - 使用模板创建/更新频道页</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163220667.png" alt="image-20250726163220667"><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163229983.png" alt="image-20250726163229983"><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163239268.png" alt="image-20250726163239268"><br>一线的品类运营人员，只需要像流水线作业一样，选择一个对应自己品类的模板，然后“填空”，就能快速生成或更新自己的频道页内容。</p></li><li><p><strong>第三步：“仓库” - 管理频道页</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163248180.png" alt="image-20250726163248180"><br>所有创建好的频道页，也会进入一个专属的列表进行统一管理。运营可以在这里控制每一个频道页的发布状态和内容。</p></li></ul><p>通过这种高度的“<strong>产品复用</strong>”，我就能用最低的成本，最高效地满足不同业务线的内容运营需求。</p><hr><h2 id="4-5-本章总结"><a href="#4-5-本章总结" class="headerlink" title="4.5 本章总结"></a>4.5 本章总结</h2><p>在本章，我们系统性地学习了电商“内容侧”的产品设计。</p><ul><li><p><strong>核心思想</strong>：我们明确了所有内容型产品设计的底层灵魂，就是<strong>内容管理系统（CMS）</strong>。它的本质，是<strong>将“内容”与“展现”分离</strong>，从而赋予非技术人员自主管理内容的能力。</p></li><li><p><strong>三大应用</strong>：我们深入探讨了CMS在电商平台中的三大核心应用：</p><ul><li><strong>店铺装修</strong>：赋予<strong>商家</strong>“千店千面”的个性化能力。</li><li><strong>专题页</strong>：赋予<strong>平台运营</strong>“高效率、低成本”地搭建活动页的能力。</li><li><strong>频道页</strong>：赋予<strong>核心品类</strong>“垂直化、精细化”地运营其“二级门户”的能力。</li></ul></li><li><p><strong>底层哲学</strong>：而支撑起这所有应用场景的底层设计哲学，都是<strong>将“内容”与“展现”分离，通过“组件化”提供装修材料，再通过“模板化”提升生产效率</strong>。</p></li></ul><p>掌握了这套思想，你就能应对任何复杂的内容型产品的设计挑战。至此，我们第四章的学习全部结束。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/19658.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/19658.html&quot;)">第四章：内容管理</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/19658.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=第四章：内容管理&amp;url=https://prorise666.site/posts/19658.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/27803.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">第三章：活动管理-总价活动</div></div></a></div><div class="next-post pull-right"><a href="/posts/51707.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">第五章：用户运营</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"第四章：内容管理",date:"2025-07-26 12:13:45",updated:"2025-07-26 21:11:09",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第四章：内容管理\n\n在前面的章节中，我们聚焦于“交易”本身，学习了如何设计商品、订单、促销等核心功能。但一个成功的电商平台，绝不仅仅是一个冷冰冰的交易场所。它还需要有温度、有吸引力、能够留住用户的内容。这一章，我们就来学习如何管理这些“内容”。\n\n\n## 4.1 内容管理系统\n\n### 4.1.1 内容管理系统的定义\n\n在我早期的产品生涯中，如果运营同事想修改首页上的一句宣传语，或者替换一张活动图片，她需要给我提一个需求单，我再排期给研发工程师，工程师修改代码、测试、再发布上线……整个流程非常繁琐，效率极低。\n\n为了解决这个问题，我需要为运营团队，提供一个可以“**自主、高效地管理网站内容**”的后台工具。这，就是内容管理系统（Content Management System, CMS）诞生的初衷。\n\n![image-20250726150544242](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150544242.png)\n\n从广义上讲，我们之前课程中为自媒体平台设计的“文章管理”后台，其实就是一个基础的CMS。它实现了最核心的一点：**让非技术人员（如运营、编辑），可以通过一个可视化的后台，去创建、修改、发布和管理网站上的内容**，而无需再依赖研发人员。\n\n![image-20250726150610613](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150610613.png)\n\n而在更严格的定义下，一个专业的CMS，如上图案例所示，其能力远不止于管理文章。它是一个能够让运营人员，在不写一行代码的情况下，自由定义网站的页面布局、栏目、以及填充具体内容的强大软件系统。\n\n它的核心思想在于——**将“内容”与“展现”彻底分离**。内容（文字、图片）被存储在数据库中，而展现（页面的样式、布局）则由模板来控制。运营人员只需要关心“内容”的生产，而无需关心它最终会“如何展示”。\n\n### 4.1.2 内容管理系统的原理\n\n要理解CMS的原理，我最喜欢用“**搭乐高**”来做比喻。\n\n![image-20250726150706862](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150706862.png)\n\n* **最小单位（乐高积木）**：这就好比是我们系统里的基础“**内容组件**”，比如一个轮播图、一个商品推荐模块、一篇文字介绍。\n* **不同的搭建（拼装过程）**：这就好比是我们的**CMS后台**。运营人员就像一个乐高大师，他可以自由地选择用哪些“积木”，以及把它们拼装在页面的哪个位置。\n* **迥然各异的结果（乐高成品）**：这就好比是用户最终看到的**前端页面**。同样是那些“积木”，既可以搭成一辆跑车，也可以搭成一座城堡。\n\n![image-20250726150744252](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150744252.png)\n\n这个“搭乐高”的过程，在我的产品设计中，被抽象为三个核心技术步骤：\n\n1.  **基础组件**：首先，我需要和研发团队一起，把所有可能用到的内容元素，都预先开发成一个个独立的、可复用的“**组件**”。例如，‘轮播图组件’、‘商品推荐组件’、‘文章列表组件’、‘视频播放组件’。这些组件就是我们的“乐高积木”，是构成页面的原子单位。\n2.  **位置+内容**：然后，在CMS后台，我需要提供一个可视化的页面编辑器。运营人员可以在这里，像“拖拽”积木一样，决定**哪个组件（内容），应该出现在页面的哪个位置**。比如，他可以决定“把轮播图组件放在页面顶部”，然后为这个组件上传5张要轮播的图片。\n3.  **动态页面**：最后，当用户访问这个页面时，系统会根据运营人员在后台的配置，**实时地、动态地**将这些组件和它们绑定的内容，从数据库中读取出来，然后像“搭积木”一样，瞬间“组装”成一个完整的网页，呈现给用户。\n\n总结一下，内容管理系统（CMS），本质上是一个**将内容生产与技术开发解耦**的软件系统。它通过“**组件化**”的设计思路，让运营人员可以像搭乐高一样，灵活、高效地创建和管理动态页面。\n\n---\n## 4.2 店铺装修\n\n在上一节，我们学习了CMS的底层原理。现在，我们就将这个强大的理论，应用到电商平台最核心的场景之一——**店铺装修**。\n\n### 4.2.1 店铺装修的设计思路\n\n![image-20250726151509850](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151509850.png)\n\n我为什么要设计“店铺装修”功能？因为我必须满足商家“**个性化经营**”的核心诉求。一个商家，除了上架和售卖商品外，更希望能方便地打造自己独特的店铺形象，以区别于其他店铺，吸引和留住属于自己的客户。\n\n![image-20250726151519631](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151519631.png)\n\n因此，我给“店铺装修”的定义是：**一套允许商家（或平台运营），在无需编写代码的情况下，对店铺页面进行动态配置的系统。** 它的本质，就是CMS在电商产品中的典型运用。\n\n![image-20250726151534604](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151534604.png)\n\n它的底层设计思路，完全源于我们之前学习的CMS三步曲：\n1.  **提供基础组件**：我为商家预先准备好各种与电商相关的“装修材料”。\n2.  **设置位置+内容**：商家可以通过一个可视化的后台，自由地组合这些“材料”，并填充自己的内容。\n3.  **生成动态页面**：用户的店铺首页，会根据商家的配置，动态地生成，实现“千店千面”。\n\n### 4.2.2 店铺装修的常见组件\n\n![image-20250726151549452](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151549452.png)\n\n那么，作为产品经理，我应该为商家提供一个怎样的“装修材料市场”呢？我会提供哪些基础组件给他们使用？\n\n![image-20250726151603118](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151603118.png)\n\n为了让商家能清晰地找到自己想要的“材料”，我将所有组件，归纳为四大类别：**商品类、图文类、营销类、及其他类**。下面我将为你逐一拆解每一类组件的设计。\n\n#### 1. 商品类组件\n\n这是店铺装修中最核心、最高频的组件类型。\n\n* **定义**\n    ![image-20250726151616933](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151616933.png)\n    \n* **常见的展示形式**\n    ![image-20250726151629264](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151629264.png)为了满足不同的陈列需求，我至少会提供以上三种主流的样式：\n    * **大图模式**：适合突出展示单个爆款或主打新品。\n    * **双列模式**：在有限的屏幕空间内，可以展示更多商品，提升浏览效率。\n    * **分类滑动模式**：允许用户在同一个组件内，通过横向滑动，来切换和浏览不同分类的商品。\n    \n* **配置逻辑**\n    ![image-20250726151700053](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151700053.png)商家在使用这个组件时，他的操作流程是清晰的三步：首先选择一个心仪的“**展示样式**”，然后从自己的商品库中“**添加商品**”，最后再对这个组件的“**展示信息**”（如标题、要不要显示价格等）进行微调。\n    \n* **组件信息拆解**\n    ![image-20250726151726638](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151726638.png)这张思维导图，完整地展示了我为“商品类组件”设计的所有可配置项。它允许商家对组件的每一个细节进行自定义。\n\n![image-20250726151835815](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151835815.png)最终，以上所有的设计，都会落地为像上图这样的后台操作界面。商家在右侧进行配置，左侧可以实时预览到最终的效果，真正做到“所见即所得”。\n\n---\n#### 2. 图文类组件\n\n这是用于展示非商品内容的、最具灵活性的组件，常用于品牌宣传、活动介绍、榜单推荐等场景。\n\n* **定义**\n    ![image-20250726152332242](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152332242.png)\n    它的核心价值，在于让商家可以用图文并茂的形式，向用户传递信息、讲述故事，从而提升店铺的“内容感”和“品牌调性”。\n\n* **常见的展示形式**\n    ![image-20250726152342998](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152342998.png)\n    我为图文组件设计了多种主流的样式，以满足不同的内容承载需求：\n    * **单图展示**：最简洁的形式，通常用于店铺头图、或者某个活动的巨幅海报，视觉冲击力强。\n    * **轮播图形式**：可以在有限的区域内，承载多张图片信息，是首页黄金位置最常用的组件，用于轮播展示多个重要活动或商品。\n    * **多图形式**：可以将多张小图以矩阵的方式进行陈列，常用于“买家秀”、“热门榜单”等场景。\n\n* **配置逻辑**\n    ![image-20250726152356474](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152356474.png)\n    商家配置图文组件的流程非常直观：先选择一个自己喜欢的“**展示形式**”，然后上传图片并为每张图片“**配置跳转链接**”（可以链接到商品、分类或活动页），最后再对组件的“**样式**”（如背景色、间距）进行微调。\n\n* **组件信息拆解**\n    ![image-20250726152415434](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152415434.png)\n    这张思维导图，完整地展示了我为“图文类组件”设计的所有可配置项。它主要分为“图片”和“文字”两个部分，商家可以自由地组合使用。例如，他可以只上传图片，构成一个图片广告；也可以只使用文字，发布一个店铺公告。\n\n![image-20250726152454089](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152454089.png)\n最终，商家在后台的操作界面就像这样。在右侧的配置面板，他可以输入文字、设置样式、调整间距，而在左侧就能实时地看到页面的最终效果，真正做到“所见即所得”。\n\n#### 3. 营销类组件\n\n这是将我们之前在第三章设计的各种营销活动，以“组件”的形式，直接“移植”到店铺首页的强大工具、页面的动态配置主要是为了满足各种活动，而活动中往往具备营销推广信息，因此除了常规图片和商品配置外，还有使用非常频繁的营销类组件\n\n* **定义**\n    ![image-20250726152548141](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152548141.png)\n    它的核心目的，是让店铺内正在进行的营销活动，能够在首页最显眼的位置得到**曝光和引流**，从而提升活动的参与率和转化率。\n\n* **常见的营销类型**\n    ![image-20250726152557494](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152557494.png)\n    通过这个组件，商家可以在首页，搭建出各式各样的“活动楼层”，例如：\n    * **秒杀类型**：展示正在进行或即将开始的秒杀商品，并带有倒计时，营造紧张的抢购氛围。\n    * **拼团类型**：以列表或卡片的形式，展示店铺内的拼团商品。\n    * **优惠券类型**：将店铺内可领取的优惠券，直接展示出来，方便用户一键领取。\n\n* **配置逻辑**\n    ![image-20250726152639022](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152639022.png)\n    商家使用此组件的逻辑是：先从下拉菜单中“**选择一种营销类型**”（如秒杀），然后系统会让他去“**关联一个已经创建好的具体活动**”，最后再对这个活动楼层的“**展示样式**”进行一些简单的编辑。\n\n* **组件信息拆解**\n    ![image-20250726152647885](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152647885.png)\n    这张思维导图，归纳了营销类组件的可配置项，核心就是“**组件类型**”和“**添加优惠活动**”这两步。\n\n这张图完美地诠释了“营销类组件”设计的灵活性和扩展性。当商家在后台…\n\n![image-20250726152902959](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152902959.png)\n\n* …选择“**秒杀**”类型时，配置面板就会让他去关联一个已经创建好的“秒杀活动”。\n\n![image-20250726152943608](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152943608.png)\n\n* …选择“**拼团**”类型时，配置面板则会让他去关联一个“拼团活动”。\n\n![image-20250726152953465](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152953465.png)\n\n* …选择“**优惠券**”类型时，他则可以勾选多张希望展示给用户的优惠券。\n\n通过这种**根据不同类型，动态变换配置项**的设计，我用一个“营销类组件”，就兼容了未来所有可能新增的营销玩法，是典型的“**对扩展开放，对修改关闭**”的设计原则的体现。\n\n\n---\n#### 4. 其他类组件\n\n![image-20250726154021085](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154021085.png)\n\n最后，我们来看“其他类组件”。我将它定义为：**除商品、图文、营销之外，那些用于完善页面布局、优化用户导航体验的基础性辅助组件。**\n\n这类组件中，虽然包含了搜索框、辅助空白、分割线等，但从产品设计的角度看，最具设计价值和复杂性的，是“**店铺自定义分类导航**”组件。我将重点为你拆解这个组件的设计思路。\n\n**1. 核心矛盾：后台类目 vs. 前台导航**\n\n要理解这个组件，我们必须先理解一个平台电商的底层逻辑。\n\n* **后台类目：平台的“标准语言”**\n\n![image-20250726154039950](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154039950.png)\n\n为了方便平台管理和统一搜索，所有商品在上传时，都必须归属到平台预设的、标准化的“**后台类目**”中。这个类目体系是庞大且固定的，就像一个国家图书馆的图书分类法（如：服装鞋包 > 女鞋 > 高跟鞋），商家通常无法修改。\n\n* **前台导航：商家的“营销语言”**\n\n![image-20250726154115185](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154115185.png)\n\n但是，一个商家从他自己经营的角度，可能完全不想按照平台的类目来组织商品。他可能希望在店铺首页，设置一个像上图这样，包含“**店长推荐**”、“**夏日清凉好物**”、“**新品速递**”等，更具营销感和个性化的“**店铺前台导航**”。这个导航是直接呈现给顾客的，必须灵活、可由商家自定义。\n\n**2. 解决方案：建立映射关系**\n\n那么，我作为产品经理，如何调和“后台类目的固定性”与“前台导航的灵活性”之间的矛盾呢？答案就是——**在产品设计中，建立一套映射关系。**\n\n![image-20250726154215194](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154215194.png)\n\n这张后台截图，就揭示了这种映射关系。当一个商家上传商品时，他需要做两件事：\n\n1.  **选择平台类目**：他必须为商品选择一个“后台类目”，例如图中的“食品饮料 > 糖果/巧克力 > 巧克力”。这是为了让**平台**能认识、索引这个商品。\n2.  **选择店铺分组**：同时，他还可以为商品选择一个或多个自己创建的“**店铺分组**”（即自定义分类），例如图中的“新品上架”。这，是为了让这个商品能够出现在他**自己店铺**首页的“新品”这个导航栏下面。\n\n**3. “店铺导航栏组件”的设计**\n\n基于以上逻辑，我设计的“店铺导航栏组件”就水到渠成了。它包含了两个部分：\n\n* **A. 分类管理后台**：首先，我需要在“商家中心”里，为商家提供一个独立的“**店铺分类管理**”功能。在这里，他们可以像管理文章目录一样，自由地创建、编辑、排序自己的导航项（如：首页、新品、活动、关于我们）。\n* **B. 装修页的组件配置**：在店铺装修页面，商家可以将这个“导航栏组件”拖拽到页面上。系统会自动读取商家在A步骤中创建好的分类，并生成导航栏。当C端用户点击某个导航项（如“新品”）时，系统就会自动筛选并展示出所有被商家打上“新品”这个标签的商品。\n\n通过这种“**后台管分类，前台配商品**”的映射设计，我就完美地解决了平台与商家在商品组织方式上的核心矛盾。\n\n除了最复杂的导航栏组件，“其他类”还包括一些简单的布局工具，如**搜索框组件**（提供店内搜索功能）、**辅助空白和分割线组件**（用于调整页面布局和呼吸感），这些组件的配置相对简单，主要是样式和尺寸的调整，这里就不再赘述。\n\n---\n\n### 4.2.3 店铺装修的产品设计\n\n#### 1. 整体产品架构\n\n通过上面的讲解，接下来需要考虑下我们应该在哪些端口提供怎样的产品功能？\n\n在着手设计具体界面前，我首先要从宏观上，规划整个系统的产品架构。\n\n![image-20250726160112838](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726160112838.png)\n\n我的设计，将整个店铺装修系统，拆分为两个相辅相成的部分：**商家端（B端）**和**用户端（C端）**。\n\n* **商家端 (B端) - “装修工厂”**：这是我提供给商家的核心工具。它的定位是一个功能强大的“装修工厂”，商家可以在这里，像室内设计师一样，对自己的店铺进行随心所欲的设计和改造。它必须包含**组件的选择、页面的编辑、内容的填充**等一系列后台功能。\n* **用户端 (C端) - “品牌橱窗”**：这是最终呈现给消费者的店铺页面。它的定位是一个精致的“品牌橱窗”，它的唯一任务，就是**忠实地、美观地**，将商家在B端配置好的装修效果，给渲染和展示出来。\n\n#### 2. 商家端（B端）设计：所见即所得的装修后台\n\n对于商家来说，装修后台的体验必须直观、易用，不能有太高的学习门槛。为此，我设计了一个“**所见即所得**”（WYSIWYG）的三栏式布局。\n\n![image-20250726160119503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726160119503.png)\n\n这个后台的核心，由三个区域构成：\n1.  **左侧：组件库**：这里是“装修材料市场”，陈列着我们在上一节设计的所有基础组件（商品、图文、营销等）。\n2.  **中间：实时预览区**：这里是“装修画布”，以手机模型的样式，实时地、1:1地展示店铺最终的模样。\n3.  **右侧：配置面板**：这里是“工具箱”，当商家在中间的画布上选中某一个组件时，这个区域就会浮现，提供针对该组件的所有详细配置项。\n\n![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/df4eeb09bf2108cd642388d650247522.png)\n\n上图就是一个真实的商家端装修后台。我们可以清晰地看到整个交互流程：商家从**左侧**的组件库中，将一个“大图广告”组件，拖拽到**中间**的手机预览区中。当他点击这个广告后，**右侧**就会立刻浮现出针对这个广告的设置选项，例如上传图片、修改样式、设置跳转链接等。整个过程非常流畅、直观。\n\n\n\n---\n## 4.3 专题页产品设计\n\n在掌握了“店铺装修”这一赋予商家个性的工具后，我们还需要一个能让平台运营人员，针对特定活动或主题，快速搭建聚合页面的强大武器。这，就是“**专题页**”。\n\n### 4.3.1 什么是专题页\n\n![image-20250726161653423](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161653423.png)\n\n我给专题页的定义是：**一个围绕着特定主题，聚合了多种内容元素（如商品、图片、优惠券、文章等）的独立页面。**\n\n它的核心价值，在于解决了大促活动中，信息“**碎片化**”的问题。如果没有专题页，一个大型活动（例如，“双十一手机会场”）的各种信息，会散落在App的各个角落，用户无法形成整体认知。而专题页，就是将所有相关信息，都汇集到一个统一的入口，为用户打造一个“一站式”的沉浸式购物场景。\n\n![image-20250726161714252](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161714252.png)\n\n我们来看京东的这个案例。为了“联合利华超级品牌日”这个活动，他们打造了一个专题页。这个页面里，有活动头图、有代言人、有优惠券、有不同楼层的商品推荐...所有与活动相关的信息，都被“聚合”在了这一个页面里，为用户提供了沉浸式、一站式的购物体验，极大地提升了活动的转化效率。\n\n### 4.3.2 专题页的需求分析\n\n在大型电商平台中，运营活动是常态。每周、甚至每天，都可能有新的促销主题上线。如果每次活动都需要研发人员去从零开发一个新页面，效率会极其低下，运营的需求会被严重阻塞。\n\n因此，我作为产品经理，在设计这个功能时，核心的需求就是：**打造一个能让运营人员“高效率、低成本、可复用”地批量生产活动专题页的后台系统。**\n\n### 4.3.3 专题页的产品设计\n\n要实现“高效率”和“可复用”，我的核心设计思路，是将专题页的生产，拆分为两个阶段：\n* **第一阶段**：由更专业的设计师或高级运营人员，负责“**创建模板**”。\n* **第二阶段**：由一线的普通运营人员，负责“**使用模板**”来快速生成页面。\n\n![image-20250726161759444](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161759444.png)\n\n上面这张流程图，完整地展示了我这套“两阶段”的设计思想。\n\n#### 1. “工厂”：创建与管理模板\n\n“创建模板”的过程，就和我们之前学习的“店铺装修”非常相似。\n\n![image-20250726161819056](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161819056.png)\n\n高级用户（设计师或高级运营）可以使用我们提供的基础组件（商品、图文、营销等），通过拖拽的方式，自由地“搭建”出一个页面的通用框架或版式。例如，他可以创建一个“双十一主会场模板”，包含顶部轮播图、中腰部秒杀楼层、底部商品列表等固定结构。\n\n![image-20250726161927025](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161927025.png)\n\n所有搭建好的模板，都会统一存放在“**模板列表**”中，形成一个可供随时调用的模板库。运营主管可以在这里，对模板进行启用、停用等管理，确保一线运营人员只能使用审核过的、规范的模板。\n\n#### 2. “流水线”：使用模板创建专题页\n\n“顺着上面梳理出的流程，先来考虑下B端如何让商家使用模板创建专题页，包含哪些页面及功能？\n\n当模板库搭建好之后，一线运营人员创建专题页的过程，就变成了一个极其简单的“流水线”作业。我为他们设计了一个三步走的创建向导。\n\n* **第一步：选择模板**\n\n![image-20250726162030043](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162030043.png)\n\n当运营人员接到一个新活动需求时（例如，做一个“夏季清仓”专题），他创建专题页的第一步，就是从我们预设好的模板库中，选择一个最符合本次活动调性的模板。\n\n* **第二步：配置信息**\n\n![image-20250726162103204](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162103204.png)\n\n选好模板后，运营就进入了“**填空模式**”。他完全不需要关心页面的布局和样式，只需要根据模板预留好的“坑位”，上传对应的素材（如专题主图），并从商品库中选择要填充的商品即可。\n\n* **第三步：创建完成**\n\n![image-20250726162122774](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162122774.png)\n\n完成信息配置后，点击创建，一个全新的、精美的专题页就瞬间生成了。运营人员可以点击“预览”，也可以直接返回列表。\n\n#### 3. “仓库”：管理已创建的专题页\n\n![image-20250726162134047](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162134047.png)\n\n所有通过模板创建好的专题页，都会进入这张“**专题列表**”进行统一的仓储和管理。运营可以在这里，精准地控制每一个专题页的**上线/下线状态**（通过设置活动起止时间），并对已上线的活动，进行后续的编辑或查看数据等操作。\n\n通过这套“**模板化、流程化**”的产品设计，我成功地将原本可能需要数天开发周期的专题页搭建工作，变成了运营人员可以在几分钟内就高效完成的常规工作，极大地提升了整个公司的运营效率和活动的迭代速度。\n\n---\n## 4.4 频道页产品设计\n\n我们最后来学习一种在大型平台中，承担着“二级门户”角色的重要页面——**频道页**。\n\n### 4.4.1 什么是频道页\n\n![image-20250726163039627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163039627.png)\n\n我给**频道页**的定义是：**一个大型业务分类的、常态化的首页。**\n\n它和我们之前学习的“专题页”有本质的区别：\n* **专题页**：通常是**临时的、活动导向的**，生命周期短，例如“双十一手机会场”。\n* **频道页**：则是**常态化的、分类导向的**，是App内一个固定的、长期的流量入口，例如“手机频道”、“女装频道”。\n\n只有当一个平台的业务体量足够大，某一个垂直品类（如“美妆护肤”）的运营足够复杂和精细时，才需要为它建立一个专属的“频道页”。\n\n![image-20250726163058003](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163058003.png)\n\n我们来看京东的这个案例。这两个频道页，都不是一个简单的商品列表。它们都聚合了二级分类导航、品牌精选、促销活动、热门榜单等多种内容形态。每一个频道页，都像是一个“美妆护肤”或“运动户外”领域里的“小首页”，承担着对这个垂直品类进行深度运营和流量分发的职责。\n\n### 4.4.2 频道页的需求分析及产品设计\n\n**需求分析**\n\n我为什么需要设计一个独立的频道页系统，而不是直接用“店铺装修”功能来搭建呢？核心需求在于**业务的垂直深度和运营的独立性**。\n\n对于一个大型平台来说，“电脑”、“手机”、“女装”等一级类目，其体量和运营复杂度，堪比一个小型的垂直电商。他们需要一个专属的、高度自定义的“首页”，来承载该品类下所有的内容和活动，从而更好地引导用户、分发流量。\n\n**产品设计：高效率的产品复用**\n\n在明确了需求后，我进行产品设计时，发现频道页的搭建过程，与我们上一节学习的“**专题页**”，在底层逻辑上是**完全一致的**。\n\n![image-20250726163146623](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163146623.png)\n\n因此，我完全可以复用“**模板化、流程化**”这套成熟的设计思路，用一套后台系统，同时支撑起“专题页”和“频道页”这两种核心的内容承载形式。这不仅极大地节约了研发资源，也保证了运营后台体验的一致性。\n\n* **第一步：“工厂” - 创建频道页模板**\n    ![image-20250726163202855](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163202855.png)\n    同样，由高级运营或设计师，使用我们标准化的组件库，来搭建不同频道（如“手机频道”、“女装频道”）的通用“模板”。\n\n* **第二步：“流水线” - 使用模板创建/更新频道页**\n    ![image-20250726163220667](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163220667.png)\n    ![image-20250726163229983](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163229983.png)\n    ![image-20250726163239268](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163239268.png)\n    一线的品类运营人员，只需要像流水线作业一样，选择一个对应自己品类的模板，然后“填空”，就能快速生成或更新自己的频道页内容。\n\n* **第三步：“仓库” - 管理频道页**\n    ![image-20250726163248180](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163248180.png)\n    所有创建好的频道页，也会进入一个专属的列表进行统一管理。运营可以在这里控制每一个频道页的发布状态和内容。\n\n通过这种高度的“**产品复用**”，我就能用最低的成本，最高效地满足不同业务线的内容运营需求。\n\n---\n\n## 4.5 本章总结\n\n在本章，我们系统性地学习了电商“内容侧”的产品设计。\n\n* **核心思想**：我们明确了所有内容型产品设计的底层灵魂，就是**内容管理系统（CMS）**。它的本质，是**将“内容”与“展现”分离**，从而赋予非技术人员自主管理内容的能力。\n\n* **三大应用**：我们深入探讨了CMS在电商平台中的三大核心应用：\n    * **店铺装修**：赋予**商家**“千店千面”的个性化能力。\n    * **专题页**：赋予**平台运营**“高效率、低成本”地搭建活动页的能力。\n    * **频道页**：赋予**核心品类**“垂直化、精细化”地运营其“二级门户”的能力。\n\n* **底层哲学**：而支撑起这所有应用场景的底层设计哲学，都是**将“内容”与“展现”分离，通过“组件化”提供装修材料，再通过“模板化”提升生产效率**。\n\n掌握了这套思想，你就能应对任何复杂的内容型产品的设计挑战。至此，我们第四章的学习全部结束。\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%9B%9B%E7%AB%A0%EF%BC%9A%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86"><span class="toc-number">1.</span> <span class="toc-text">第四章：内容管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-1-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F"><span class="toc-number">1.1.</span> <span class="toc-text">4.1 内容管理系统</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-1-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-number">1.1.1.</span> <span class="toc-text">4.1.1 内容管理系统的定义</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-2-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F%E7%9A%84%E5%8E%9F%E7%90%86"><span class="toc-number">1.1.2.</span> <span class="toc-text">4.1.2 内容管理系统的原理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-2-%E5%BA%97%E9%93%BA%E8%A3%85%E4%BF%AE"><span class="toc-number">1.2.</span> <span class="toc-text">4.2 店铺装修</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-1-%E5%BA%97%E9%93%BA%E8%A3%85%E4%BF%AE%E7%9A%84%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-number">1.2.1.</span> <span class="toc-text">4.2.1 店铺装修的设计思路</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-2-%E5%BA%97%E9%93%BA%E8%A3%85%E4%BF%AE%E7%9A%84%E5%B8%B8%E8%A7%81%E7%BB%84%E4%BB%B6"><span class="toc-number">1.2.2.</span> <span class="toc-text">4.2.2 店铺装修的常见组件</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%95%86%E5%93%81%E7%B1%BB%E7%BB%84%E4%BB%B6"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">1. 商品类组件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9B%BE%E6%96%87%E7%B1%BB%E7%BB%84%E4%BB%B6"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">2. 图文类组件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E8%90%A5%E9%94%80%E7%B1%BB%E7%BB%84%E4%BB%B6"><span class="toc-number">1.2.2.3.</span> <span class="toc-text">3. 营销类组件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E5%85%B6%E4%BB%96%E7%B1%BB%E7%BB%84%E4%BB%B6"><span class="toc-number">1.2.2.4.</span> <span class="toc-text">4. 其他类组件</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-3-%E5%BA%97%E9%93%BA%E8%A3%85%E4%BF%AE%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.2.3.</span> <span class="toc-text">4.2.3 店铺装修的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%95%B4%E4%BD%93%E4%BA%A7%E5%93%81%E6%9E%B6%E6%9E%84"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">1. 整体产品架构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%95%86%E5%AE%B6%E7%AB%AF%EF%BC%88B%E7%AB%AF%EF%BC%89%E8%AE%BE%E8%AE%A1%EF%BC%9A%E6%89%80%E8%A7%81%E5%8D%B3%E6%89%80%E5%BE%97%E7%9A%84%E8%A3%85%E4%BF%AE%E5%90%8E%E5%8F%B0"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">2. 商家端（B端）设计：所见即所得的装修后台</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-3-%E4%B8%93%E9%A2%98%E9%A1%B5%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.</span> <span class="toc-text">4.3 专题页产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-1-%E4%BB%80%E4%B9%88%E6%98%AF%E4%B8%93%E9%A2%98%E9%A1%B5"><span class="toc-number">1.3.1.</span> <span class="toc-text">4.3.1 什么是专题页</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-2-%E4%B8%93%E9%A2%98%E9%A1%B5%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.3.2.</span> <span class="toc-text">4.3.2 专题页的需求分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-3-%E4%B8%93%E9%A2%98%E9%A1%B5%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.3.</span> <span class="toc-text">4.3.3 专题页的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E2%80%9C%E5%B7%A5%E5%8E%82%E2%80%9D%EF%BC%9A%E5%88%9B%E5%BB%BA%E4%B8%8E%E7%AE%A1%E7%90%86%E6%A8%A1%E6%9D%BF"><span class="toc-number">1.3.3.1.</span> <span class="toc-text">1. “工厂”：创建与管理模板</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E2%80%9C%E6%B5%81%E6%B0%B4%E7%BA%BF%E2%80%9D%EF%BC%9A%E4%BD%BF%E7%94%A8%E6%A8%A1%E6%9D%BF%E5%88%9B%E5%BB%BA%E4%B8%93%E9%A2%98%E9%A1%B5"><span class="toc-number">1.3.3.2.</span> <span class="toc-text">2. “流水线”：使用模板创建专题页</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E2%80%9C%E4%BB%93%E5%BA%93%E2%80%9D%EF%BC%9A%E7%AE%A1%E7%90%86%E5%B7%B2%E5%88%9B%E5%BB%BA%E7%9A%84%E4%B8%93%E9%A2%98%E9%A1%B5"><span class="toc-number">1.3.3.3.</span> <span class="toc-text">3. “仓库”：管理已创建的专题页</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-4-%E9%A2%91%E9%81%93%E9%A1%B5%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.4.</span> <span class="toc-text">4.4 频道页产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-1-%E4%BB%80%E4%B9%88%E6%98%AF%E9%A2%91%E9%81%93%E9%A1%B5"><span class="toc-number">1.4.1.</span> <span class="toc-text">4.4.1 什么是频道页</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-2-%E9%A2%91%E9%81%93%E9%A1%B5%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E5%8F%8A%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.4.2.</span> <span class="toc-text">4.4.2 频道页的需求分析及产品设计</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-5-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.5.</span> <span class="toc-text">4.5 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>