.card-widget {
    padding: 0px!important;     /* 原文padding: 10px!important;这样会导致微信卡css发生错误 */
    max-height: calc(100vh - 100px);
}

.card-times a, .card-times div {
    color: var(--efu-fontcolor);
}

#card-widget-calendar .item-content {
    display: flex;
}

#calendar-area-left {
    width: 45%;
}

#calendar-area-right {
    width: 55%;
}

#calendar-area-left, #calendar-area-right {
    height: 100%;
    padding: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

#calendar-main {
    width: 100%;
}

#calendar-week {
    height: 1.2rem;
    font-size: 14px;
    letter-spacing: 1px;
    font-weight: 700;
    align-items: center;
    display: flex;
}

#calendar-date {
    height: 3rem;
    line-height: 1.3;
    font-size: 64px;
    letter-spacing: 3px;
    color: var(--anzhiyu-main);
    font-weight: 700;
    align-items: center;
    display: flex;
    position: absolute;
    top: calc(50% - 2.1rem);
}

#calendar-lunar, #calendar-solar {
    height: 1rem;
    font-size: 12px;
    align-items: center;
    display: flex;
    position: absolute;
}

#calendar-solar {
    bottom: 1.5rem;  /* 原文bottom: 2.1rem;这样会导致微信卡css发生错误 */
}

#calendar-lunar {
    bottom: 0.5rem;   /* 原文bottom: 1rem;这样会导致微信卡css发生错误 */
    color: var(--efu-secondtext);
}

#calendar-main a {
    height: 1rem;
    width: 1rem;
    border-radius: 50%;
    font-size: 12px;
    line-height: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
}

#calendar-main a.now {
    background: var(--anzhiyu-main);
    color: var(--efu-card-bg);
}

#calendar-main .calendar-rh a {
    color: var(--efu-secondtext);
}

.calendar-r0, .calendar-r1, .calendar-r2, .calendar-r3, .calendar-r4, .calendar-r5, .calendar-rh {
    height: 1.2rem;
    display: flex;
}

.calendar-d0, .calendar-d1, .calendar-d2, .calendar-d3, .calendar-d4, .calendar-d5, .calendar-d6 {
    width: calc(100% / 7);
    display: flex;
    justify-content: center;
    align-items: center;
}

#card-widget-schedule .item-content {
    display: flex;
}

#schedule-area-left, #schedule-area-right {
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

#schedule-area-left {
    width: 30%;
}

#schedule-area-right {
    width: 70%;
    padding: 0 5px;
}

.schedule-r0, .schedule-r1, .schedule-r2 {
    height: 2rem;
    width: 100%;
    align-items: center;
    display: flex;
}

.schedule-d0 {
    width: 30px;
    margin-right: 5px;
    text-align: center;
    font-size: 12px;
}

.schedule-d1 {
    width: calc(100% - 35px);
    height: 1.5rem;
    align-items: center;
    display: flex;
}

progress::-webkit-progress-bar {
    background: linear-gradient(to right, var(--anzhiyu-main-op-deep), var(--anzhiyu-main-op), var(--anzhiyu-main-op-light));
    border-radius: 5px;
    overflow: hidden;
}

progress::-webkit-progress-value {
    background: var(--anzhiyu-main);
    border-radius: 5px;
    color:white;
}

.aside-span1, .aside-span2 {
    height: 1rem;
    font-size: 12px;
    z-index: 1;
    display: flex;
    align-items: center;
    position: absolute;
    color:white;
}

.aside-span1 {
    margin-left: 5px;
    color:white;
}

.aside-span2 {
    right: 20px;
    color: white;
}

.aside-span2 a {
    margin: 0 3px;
}

#pBar_month, #pBar_week, #pBar_year {
    width: 100%;
    border-radius: 5px;
    height: 100%;
}

#schedule-date, #schedule-days, #schedule-title {
    display: flex;
    align-items: center;
}

#schedule-title {
    height: 25px;
    line-height: 1;
    font-size: 14px;
    font-weight: 700;
}

#schedule-days {
    height: 40px;
    line-height: 1;
    font-size: 30px;
    font-weight: 900;
    color: var(--anzhiyu-main);
}

#schedule-date {
    height: 20px;
    line-height: 1;
    font-size: 12px;
    color: var(--efu-secondtext);
}