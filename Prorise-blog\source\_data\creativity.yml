- category_name: "前端开发"
  creativity_list:
  - name: "HTML5/CSS3"
    color: "#e44d26"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg"
  - name: "JavaScript"
    color: "#f7df1e"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg"
  - name: "TypeScript"
    color: "#3178c6"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg"
  - name: "Vue.js"
    color: "#4fc08d"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/vuejs/vuejs-original.svg"
  - name: "React"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/react/react-original-wordmark.svg"
  - name: "小程序开发"
    color: "#09b83e"
    icon: "https://res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png"
  - name: "Sass"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/sass/sass-original.svg"
  - name: "uniapp"
    color: "#FEFEF7"
    icon: "https://ts2.tc.mm.bing.net/th/id/ODLS.e63c536b-f08d-4550-b4cd-f6ca9d84b67e?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "Webpack"
    color: "#8dd6f9"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/webpack/webpack-original.svg"
  - name: "Vite"
    color: "#646cff"
    icon: "https://vitejs.dev/logo.svg"
  - name: "Canvas动画"
    color: "#FFFFFF"
    icon: "https://ts4.tc.mm.bing.net/th/id/ODLS.97013f0b-ae3b-41b4-8c13-ae3ac47fd06d?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "GSAP动画库"
    color: "#FFFFFF"
    icon: "https://ts4.tc.mm.bing.net/th/id/ODLS.827513d4-9077-440d-92b0-1f3d73888d03?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2"
  - name: "Electron"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/electron/electron-original.svg"
  - name: "TailwindCSS"
    color: "#FFFFFF"
    icon: "https://ts3.tc.mm.bing.net/th/id/ODLS.095f9b22-a70b-47ed-bdb1-070466f08dc4?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "Bootstrap"
    color: "#7952b3"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/bootstrap/bootstrap-original.svg"
  - name: "Element Plus"
    color: "#409eff"
    icon: "https://ts3.tc.mm.bing.net/th/id/ODLS.57304398-66ea-459d-8d9c-4647aea8751b?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2" 
  - name: "DaisyUI"
    color: "#FFFFFF"
    icon: "https://ts2.tc.mm.bing.net/th/id/ODLS.abfe53b0-a009-4cb9-9eb6-0e088c68c907?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2"

- category_name: "后端编程语言"
  creativity_list:
  - name: "Java"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg"
  - name: "Python"
    color: "#3776ab"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg"
  - name: "PHP"
    color: "#777bb4"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/php/php-original.svg"
  - name: "Node.js"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg"
  - name: "C/C++"
    color: "#00599c"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/cplusplus/cplusplus-original.svg"

- category_name: "Spring全家桶"
  creativity_list:
  - name: "Spring Boot"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg"
  - name: "Spring Framework"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg"
  - name: "Spring MVC"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg"
  - name: "Spring Security"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg"

- category_name: "后端框架与库"
  creativity_list:
  - name: "MyBatis"
    color: "#FFFFFF"
    icon: "https://ts4.tc.mm.bing.net/th/id/ODLS.513057f8-5234-45b9-92f8-da60ba13023d?w=32&h=32&qlt=93&pcl=fffffa&o=6&pid=1.2"
  - name: "MyBatis-Plus"
    color: "#FFFFFF"
    icon: "https://ts1.tc.mm.bing.net/th/id/ODLS.b5a2813e-eac8-4f6d-8c4a-3955777e3300?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2"
  - name: "Django"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/django/django-plain.svg"
  - name: "Flask"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/flask/flask-original.svg"
  - name: "FastAPI"
    color: "#FFFFFF"
    icon: "https://ts2.tc.mm.bing.net/th/id/ODLS.f4c3d716-9b6f-42a3-9099-8af0c6828d50?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "Scrapy"
    color: "#60a839"
    icon: "https://ts1.tc.mm.bing.net/th/id/ODLS.7c59a4c2-1c1a-47a1-b5c0-a9a7a345bbfb?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "ThinkPHP"
    color: "#FFFFFF"
    icon: "https://ts3.tc.mm.bing.net/th/id/ODLS.06dc16eb-e885-4f1a-890e-25dc17a3483b?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"

- category_name: "数据库与运维"
  creativity_list:
  - name: "MySQL"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg"
  - name: "MongoDB"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg"
  - name: "Redis"
    color: "#dc382d"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/redis/redis-original.svg"
  - name: "Linux"
    color: "#fcc624"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/linux/linux-original.svg"
  - name: "Docker"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg"
  - name: "Nginx"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nginx/nginx-original.svg"
  - name: "Git"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg"

- category_name: "AI与设计"
  creativity_list:
  - name: "提示词工程"
    color: "#FFFFFF"
    icon: "https://upload.wikimedia.org/wikipedia/commons/0/04/ChatGPT_logo.svg"
  - name: "UI设计"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/figma/figma-original.svg"
  - name: "UE设计"
    color: "#9c27b0"
    icon: "https://upload.wikimedia.org/wikipedia/commons/c/c2/Adobe_XD_CC_icon.svg"



- category_name: "开发框架"
  creativity_list:
  - name: "RuoYi框架"
    color: "#FFFFFF"
    icon: "https://ts2.tc.mm.bing.net/th/id/ODLS.5715a21c-7c5f-4781-ae97-2867ef553257?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2g"
  - name: "GoEazy网络框架"
    color: "#FFFFFF"
    icon: "https://ts3.tc.mm.bing.net/th/id/ODLS.d7f53ebd-3c0c-415f-943b-197fa248b289?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2"