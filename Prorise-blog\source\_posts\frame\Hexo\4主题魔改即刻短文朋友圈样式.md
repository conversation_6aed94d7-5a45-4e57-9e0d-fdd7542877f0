---
title: 4.主题魔改：即刻短文朋友圈样式
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 61533
date: 2025-07-10 19:13:45
---

### **4.主题魔改：即刻短文朋友圈样式**

###### **前言：功能介绍与重要提示**

本指南将引导您修改“即刻短文 (`/essay/`)”页面，使其在**移动端（手机等小屏幕设备）**的显示效果，模仿成大家非常熟悉的**微信朋友圈**时间线样式。在PC端，它将保持原有的样式不变。

> **警告：** 这同样是一项“魔改”操作，会覆盖主题的核心文件。在开始前，**强烈建议您备份 `themes/anzhiyu` 文件夹**，以便在出现问题时可以随时恢复。

---
###### **第一步：替换“即刻短文”页面布局文件**

此修改需要替换掉负责渲染 `/essay/` 页面的核心模板。

1.  **找到目标文件**：
    `themes/anzhiyu/layout/includes/page/essay.pug`

2.  **替换内容**：
    将这个文件的**全部内容**，用下面提供的**完整代码**进行覆盖。

    ```pug
    //- essay.pug
    if site.data.essay
      each i in site.data.essay
        #essay_page
          .author-content.author-content-item.essayPage.single(style = i.top_background ? `background: url(${i.top_background}) left 28% / cover no-repeat;` : "")
          .author-info
            .msg
              .avatar
                  a.essay-avatar(href=i.avatarLink)
                    img(src=i.avatar)
              .eassy-name=i.name
            .desc=i.desc
          #bber
            section.timeline.page-1.content
              ul#waterfall.list
                each item, index in i.essay_list
                  if index < i.limit
                    li.bber-item
                      .user-avatar(style = i.top_background ? `background: url(${i.avatar}) left 28% / cover no-repeat #ffffffad;` : "")
                      .bber-content
                        .right
                          .bber-name=i.name
                          p.datacont= item.content
                            if item.image
                              .bber-container-img
                                each iten, indey in item.image
                                  a.bber-content-img(href=url_for(item.image[indey]), target="_blank",  data-fancybox="gallery", data-caption="")
                                    img(src=url_for(item.image[indey]))
                                .bber-content-noimg
                                .bber-content-noimg
                                .bber-content-noimg
                            if item.video
                              .bber-container-img
                                each iten, indey in item.video
                                  if (item.video[indey].includes('player.bilibili.com'))
                                    div(style="position: relative; padding: 30% 45%;margin-top: 10px;margin-bottom: 10px;")
                                      iframe(style="position: absolute; width: 100%; height: 100%; left: 0; top: 0;margin: 0;border-radius: 12px;border: var(--style-border);" src=url_for(item.video[indey]) scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true")
                                  else
                                    a.bber-content-video(href=url_for(item.video[indey]) data-fancybox="gallery", data-caption="")
                                      video(src=url_for(item.video[indey]))
                                .bber-content-noimg
                                .bber-content-noimg
                                .bber-content-noimg
                            if item.aplayer
                              .bber-music
                                meting-js(id=item.aplayer.id, server=item.aplayer.server, type="song", mutex="true",preload="none", theme="var(--anzhiyu-main)", data-lrctype="0", order="list")
                          .bber-bottom
                            .bber-info-address
                              if item.address
                                  span=item.address
                            .bber-info-time
                              - var datedata = new Date(item.date).toISOString()
                              time.datatime(datetime= item.date)= datedata
                              .bber-info-link
                                if item.link
                                  a.bber-content-link(title="跳转到短文指引的链接", href=url_for(item.link),  rel="external nofollow")
                                    i.anzhiyufont.anzhiyu-icon-link
                                    | 链接
                              .bber-reply(onclick="rm.rightMenuCommentText(" + `'${item.content}'` + ")")
                                i.anzhiyufont.anzhiyu-icon-message
                      hr.essay-hr
          #bber-tips(style='color: var(--anzhiyu-secondtext);')
            ="- 只展示最近" + i.limit + "条短文 -"
    ```

---
###### **第二步：添加自定义朋友圈样式**

1.  **创建CSS文件**
  
* 在 `themes/anzhiyu/source/css/` 目录下，新建一个文件，命名为 `essay-style.css`。
  
2.  **粘贴CSS代码**
    * 将下面的CSS代码完整复制到您刚创建的 `essay-style.css` 文件中。

    ```css
    .author-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        position: relative;
        top: -35px;
        z-index: 3;
        margin-right: 25px;
    }

    .author-info .msg {
        display: flex;
        flex-direction: row-reverse;
        align-items: flex-start;
    }

    a.essay-avatar {
        height: 60px;
        width: 60px;
        display: block;
        border-radius: 10px;
        background: #fff;
    }

    [data-theme=dark] a.essay-avatar {
        background: #ffffffad;
    }

    .eassy-name {
        margin-right: 10px;
        padding-top: 2px;
        color: #eee;
        font-weight: 600;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;
        display: -webkit-box;
        overflow: hidden;
    }

    .content.narrow-row {
        padding: 0 50px;
    }

    #bber {
        margin-top: -2rem;
    }

    #bber div.bber-content {
        position: relative;
        flex-direction: column;
    }

    #bber .bber-container-img {
        justify-content: flex-start;
    }

    #bber .bber-content-img,
    #bber .bber-content-video {
        width: 32%;
        margin-right: 1%;
    }

    #bber .bber-content .datacont {
        font-size: 14px;
        font-weight: 400;
        color: var(--font-color);
    }

    #bber .bber-content-img img {
        width: 100%;
    }

    #bber .bber-bottom {
        display: inline !important;
    }

    #bber>section>ul>li>div .bber-info-from,
    #bber>section>ul>li>div .bber-info-time {
        background: 0 0;
        padding: 0;
        height: 26px;
    }

    hr.essay-hr {
        margin: 25px auto;
        border: 1px solid var(--hr-border);
    }

    .bber-name,
    .user-avatar {
        display: none;
    }

    .bber-reply {
        position: absolute;
        right: 15px;
        bottom: 0;
        height: 26px;
        line-height: 26px;
    }

    time.datatime {
        font-size: 13px;
    }

    .bber-info-link {
        position: absolute;
        right: 40px;
        bottom: 0;
        line-height: 26px;
    }

    .bber-info-address {
        color: #4e9df8;
        font-size: .7rem;
    }

    div#bber-tips {
        padding-bottom: 20px;
    }

    @media screen and (max-width:900px),
    (max-height:580px) {
        body[data-type=essay] #body-wrap .layout #page {
            padding: 0 5px !important;
        }

        body[data-type=essay] div#post-comment {
            padding: 20px 15px !important;
        }

        body[data-type=essay] .layout {
            padding: 0 !important;
        }

        body[data-type=essay] .right {
            margin-left: 55px;
        }

        div#essay_page {
            border: var(--style-border);
            box-shadow: var(--anzhiyu-shadow-border);
            border-radius: 10px;
        }

        a.essay-avatar {
            height: 60px;
            width: 60px;
        }

        .author-content.author-content-item.essayPage.single {
            height: 16rem;
            border-radius: 10px;
        }

        .desc {
            font-size: 14px;
        }

        #bber {
            margin-top: -3rem !important;
            width: 95%;
            margin: auto;
        }

        #bber .timeline ul li.bber-item {
            width: 95%;
            border: none;
            border-radius: 0;
            padding: 0;
            background: 0 0;
            box-shadow: none;
        }

        #bber .timeline ul li.bber-item:hover {
            border: none;
        }

        .bber-name {
            font-size: 16px;
            font-weight: 800;
            display: block;
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 10px;
            overflow: hidden;
            position: absolute;
            display: block;
        }
    }
    ```

---
###### **第三步：在主题配置中注入新样式**

1.  **打开主题配置文件** (`themes/anzhiyu/_config.yml`)。
2.  **找到 `inject:` 配置项**，在 `head:` 列表中添加我们新建的CSS文件。
    ```yaml
    inject:
      head:
        # - 其他 head 内容...
        - '<link rel="stylesheet" href="/css/essay-style.css">'
    ```

---
###### **第四步：调整数据文件 (`essay.yml`)**

为了匹配新的朋友圈样式（例如顶部的背景图、您的头像和昵称），我们需要在 `source/_data/essay.yml` 文件的配置部分，添加或修改一些字段。

* 打开 `source/_data/essay.yml` 文件。
* 在文件的最顶部的配置区（`-` 后面），确保包含以下字段：

    ```yaml
    - 
      # --- 页面顶部信息，模拟朋友圈个人主页 ---
      name: "Prorise"                         # 【新增】您的昵称，会显示在背景图上方
      desc: "代码构建世界，思想驱动未来"     # 【新增】您的个性签名
      avatar: "/img/user/avatar.webp"      # 【新增】您的头像，会显示在昵称旁边
      avatarLink: /about/                  # 【新增】点击头像跳转的链接
      top_background: "https://example.com/your-bg.jpg" # 朋友圈顶部的背景图
      
      # --- 页面原有配置 ---
      title: "即刻短文"
      limit: 30
      # ...等等
      
      essay_list:
        # ...您的短文列表保持不变...
    ```

---