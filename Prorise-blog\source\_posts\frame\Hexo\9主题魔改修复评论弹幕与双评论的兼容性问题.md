---
title: 9.主题魔改：修复“评论弹幕”与双评论的兼容性问题
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 50205
date: 2025-07-11 01:13:45
---

### **9.主题魔改：修复“评论弹幕”与双评论的兼容性问题**

###### **前言：问题诊断与解决方案**

* **问题现象**：当您在主题中配置了**双评论系统**（例如 `use: [Twikoo, Waline]`）时，依赖于Twikoo的**评论弹幕**功能会失效。
* **问题根源**：主题的部分代码在判断是否启用弹幕时，使用了 `theme.comments.use == 'Twikoo'` 这样的判断条件。这段代码要求 `use` 的值必须是**字符串** `'Twikoo'`。当您将其配置为**数组** `['Twikoo', 'Waline']` 时，`['Twikoo', 'Waline'] == 'Twikoo'` 的结果永远为 `false`，导致弹幕功能无法被正确加载。
* **解决方案**：我们将直接修改主题的模板文件，让这些判断条件变得更“聪明”，能够正确识别出您的 `use` 配置中是否**包含** Twikoo。

> **警告：** 这同样是一项涉及修改主题核心文件的“魔改”操作。在开始前，**强烈建议您备份整个 `themes/anzhiyu` 文件夹**。

---
###### **第一步：修改 `additional-js.pug` (核心触发文件)**

这个文件负责在页面底部加载弹幕的核心脚本，我们需要修正它的加载条件。

1.  **找到并打开文件**：
    `themes/anzhiyu/layout/includes/additional-js.pug`

2.  **修改判断条件**：
    * 在文件中找到（大约在第78行）下面这行代码：
        ```pug
        // 原来的错误条件
        if theme.comment_barrage_config.enable && theme.comments.use == 'Twikoo' && page.comments
        ```
    * 将其**替换**为以下这行修正后的代码：
        ```pug
        // 修正后的正确条件
        if theme.comment_barrage_config.enable && theme.comments.use && theme.comments.use.includes('Twikoo') && page.comments
        ```
    * **修改解释**：我们将简单的 `== 'Twikoo'` 判断，改为了 `theme.comments.use.includes('Twikoo')`。这行新代码会先检查 `use` 是否存在，然后检查它是否一个**包含** `'Twikoo'` 字符串的数组（或本身就是'Twikoo'字符串），这样就能完美兼容您的双评论配置了。

---
###### **第二步：修改 `flink.pug` (友链页面兼容性)**

友链页面可能也包含与评论相关的逻辑，我们也需要一并修正。

1.  **找到并打开文件**：
    `themes/anzhiyu/layout/includes/page/flink.pug`

2.  **搜索并修改**：
    * 在这个文件中，使用搜索功能 (`Ctrl+F`) 查找 `theme.comments.use == 'Twikoo'`。
    * 如果找到了完全相同的判断条件，请同样将其修改为 `theme.comments.use && theme.comments.use.includes('Twikoo')`。

**第三步：修改css文件以适配个人配色（可选）**

位于/themes/anzhiyu/source/css\_extra/commentBarrage

将如下的CSS复制进去或自行选择修改

```css
.comment-barrage {
  position: fixed;
  bottom: 0;
  right: 60px;
  padding: 0 0 20px 10px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: flex-end;
  z-index: 999;
  transition: 0.3s;
}
@media screen and (max-width: 768px) {
  .comment-barrage {
    display: none !important;
  }
}
.comment-barrage-item {
  min-width: 300px;
  max-width: 300px;
  width: fit-content;
  min-height: 80px;
  max-height: 150px;
  margin: 4px;
  padding: 8px 14px;
  background: rgba(240, 245, 249, 0.95);
  border-radius: 12px;
  color: rgb(30, 32, 34);
  animation: barrageIn 0.6s cubic-bezier(0.42, 0, 0.3, 1.11);
  transition: 0.3s;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(201, 214, 223, 0.6);
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transform: translateZ(0);
  position: fixed;
  box-shadow: 0 4px 12px rgba(82, 97, 107, 0.15);
}
.comment-barrage-item:hover {
  border: 1px solid rgb(82, 97, 107);
  box-shadow: 0 6px 20px rgba(82, 97, 107, 0.25);
  transform: translateY(-2px);
}
.comment-barrage-item.out {
  opacity: 0;
  animation: barrageOut 0.6s cubic-bezier(0.42, 0, 0.3, 1.11);
}
.comment-barrage-item a.barrageContent:hover {
  color: rgb(82, 97, 107);
}
.comment-barrage-item.hovered {
  opacity: 0;
}
.comment-barrage-item .comment-barrage-close {
  color: rgb(82, 97, 107);
  cursor: pointer;
  line-height: 1;
  padding: 4px;
  transition: color 0.2s ease;
}
.comment-barrage-item .comment-barrage-close:hover {
  color: rgb(30, 32, 34);
}
.comment-barrage-item pre {
  display: none;
}
.comment-barrage-item p img:not(.tk-owo-emotion) {
  display: none;
}

.comment-barrage-item .barrageHead {
  height: 30px;
  padding: 0;
  line-height: 30px;
  font-size: 12px;
  border-bottom: 1px solid rgba(201, 214, 223, 0.4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 700;
  padding-bottom: 6px;
}
.comment-barrage-item .barrageHead .barrageTitle {
  color: rgb(240, 245, 249);
  margin-right: 8px;
  background: rgb(82, 97, 107);
  line-height: 1;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  transition: all 0.2s ease;
}
.comment-barrage-item .barrageHead .barrageTitle.barrageBloggerTitle {
  background: rgb(30, 32, 34);
  box-shadow: 0 2px 6px rgba(30, 32, 34, 0.2);
}
.comment-barrage-item .barrageAvatar {
  width: 18px;
  height: 18px;
  margin: 0;
  margin-left: auto;
  margin-right: 8px;
  border-radius: 50%;
  background: rgb(201, 214, 223);
  border: 1px solid rgba(82, 97, 107, 0.2);
}
.comment-barrage-item .barrageContent {
  font-size: 14px !important;
  font-weight: 400 !important;
  height: calc(100% - 30px);
  overflow: scroll;
  cursor: pointer;
  line-height: 1.4;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar {
  height: 0;
  width: 4px;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar-track {
  background: rgba(201, 214, 223, 0.3);
  border-radius: 2px;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar-thumb {
  background: rgba(82, 97, 107, 0.5);
  border-radius: 2px;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar-thumb:hover {
  background: rgb(82, 97, 107);
}
.comment-barrage-item .barrageContent::-webkit-scrollbar-button {
  display: none;
}
.comment-barrage-item .barrageContent p {
  margin: 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.comment-barrage-item .barrageContent blockquote p {
  margin: 0;
}
.comment-barrage-item .barrageContent h1,
.comment-barrage-item .barrageContent h2,
.comment-barrage-item .barrageContent h3,
.comment-barrage-item .barrageContent h4 {
  font-size: 14px !important;
  font-weight: 600 !important;
  margin: 8px 0 !important;
  color: rgb(30, 32, 34);
}

@media (prefers-color-scheme: dark) {
  .comment-barrage-item {
    background: rgba(30, 32, 34, 0.95);
    color: rgb(240, 245, 249);
    border: 1px solid rgba(82, 97, 107, 0.6);
  }
  
  .comment-barrage-item:hover {
    border: 1px solid rgb(201, 214, 223);
    box-shadow: 0 6px 20px rgba(201, 214, 223, 0.15);
  }
  
  .comment-barrage-item .barrageHead {
    border-bottom: 1px solid rgba(82, 97, 107, 0.4);
  }
  
  .comment-barrage-item .barrageHead .barrageTitle {
    background: rgb(201, 214, 223);
    color: rgb(30, 32, 34);
  }
  
  .comment-barrage-item .barrageHead .barrageTitle.barrageBloggerTitle {
    background: rgb(240, 245, 249);
    color: rgb(30, 32, 34);
  }
  
  .comment-barrage-item .barrageAvatar {
    background: rgb(82, 97, 107);
    border: 1px solid rgba(201, 214, 223, 0.2);
  }
  
  .comment-barrage-item .barrageContent::-webkit-scrollbar-track {
    background: rgba(82, 97, 107, 0.3);
  }
  
  .comment-barrage-item .barrageContent::-webkit-scrollbar-thumb {
    background: rgba(201, 214, 223, 0.5);
  }
}

```



---