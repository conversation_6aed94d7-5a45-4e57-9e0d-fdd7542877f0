- let home_top_config = theme.home_top
- let { title, subTitle, siteText,default_descr } = home_top_config
if home_top_config.enable
  #home_top
    .swiper_container_card(style='height: auto;width: 100%')
      #bannerGroup
        #random-banner
          if theme.peoplecanvas.enable
            canvas#peoplecanvas
          else 
            .banners-title
              .banners-title-big=title
              .banners-title-big=subTitle
              .banners-title-small=siteText
            include ../anzhiyu/tags-group-all.pug
        .categoryGroup
          each item in home_top_config.category
            .categoryItem(style=`box-shadow:${item.shadow}`)
              a.categoryButton(href=url_for(item.path) class=`${item.class}`)
                span.categoryButtonText=item.name
                if (item.icon.startsWith("fa"))
                  i(class=`${item.icon}`)
                else if (item.icon.startsWith("icon"))
                  svg.icon(aria-hidden="true")
                    use(xlink:href=`#`+ item.icon)
                else if (item.icon.startsWith("anzhiyu"))
                  i.anzhiyufont(class=`${item.icon}`)
      if theme.home_top.swiper.enable
        #swiper_container_blog
          #swiper_container.blog-slider.swiper-container-fade.swiper-container-horizontal
            .blog-slider__wrp.swiper-wrapper(style='transition-duration: 0ms;')
              //- 文章卡片
              - let swiper_list = sort_attr_post("swiper_list")
              each item in swiper_list
                .blog-slider__item.swiper-slide(style='width: 750px; opacity: 1; transform: translate3d(0px, 0px, 0px); transition-duration: 0ms;')
                  a.blog-slider__img(href=item.path, title=item.title)
                    img(width='48' height='48'  src=item.cover alt='图片' onerror=`this.src=` + error_img + `; this.onerror = null;`)
                  .blog-slider__content
                    if (timemode === 'updated' )
                      span.blog-slider__code= item.updated.format('YYYY-MM-DD')
                    else
                      span.blog-slider__code= item.date.format('YYYY-MM-DD')
                    - const content = strip_html(item.content)
                    - let expert = content.substring(0, theme.index_post_content.length) 
                    - content.length > theme.index_post_content.length ? expert += ' ...' : ''
                    a.blog-slider__title(href=item.path, alt=item.title)= item.title
                    .blog-slider__text= item.description ? item.description : expert
                    a.blog-slider__button(href=item.path, alt=item.title) 详情   
            //- 轮播图组件
            .blog-slider__pagination.swiper-pagination-clickable.swiper-pagination-bullets

          #topPostGroup
            - let top_group_list = sort_attr_post("top_group_list").splice(0, 4)
            each item in top_group_list
              .top-group-list-item
                .post_cover.left_radius
                  a(href=item.path title=item.title)
                    span.top-group-text 荐
                    img.post_bg(alt=item.title, src=item.cover, onerror=`this.src=` + error_img + `; this.onerror = null;`)
                .top-group-info
                  a.article-title(href=item.path title=item.title)=item.title
            .top-group-list-none
            .top-group-list-none
            .top-group-list-none
      else 
        .topGroup
          - let top_group_list = sort_attr_post("top_group_list").splice(0, 6)
          - let top_group_banner_tips = theme.home_top.banner.tips
          - let top_group_banner_title = theme.home_top.banner.title
          - let top_group_banner_image = theme.home_top.banner.image
          - let top_group_banner_link = url_for(theme.home_top.banner.link)

          each item, index in top_group_list.slice(0, 6)
            .recent-post-item(onclick=`pjax.loadUrl("${url_for(item.path)}")`)
              .post_cover.left_radius
                a(href=item.path, title=item.title)
                  span.recent-post-top-text 荐
                  img.post_bg(onerror=`this.onerror=null;this.src='${url_for(theme.error_img.post_page)}'`, alt='cover',  src=item.cover)
              .recent-post-info
                a.article-title(href=item.path, title=item.title)=item.title
          if theme.home_top.banner.enable
            a#todayCard.todayCard(href=top_group_banner_link, style='z-index: 1;')
              .todayCard-info
                .todayCard-tips=top_group_banner_tips
                .todayCard-title=top_group_banner_title
              img.todayCard-cover(src=url_for(top_group_banner_image) onerror=`this.onerror=null;this.src='${url_for(theme.error_img.post_page)}'` alt='封面')
              .banner-button-group
                .banner-button(onclick='event.stopPropagation();event.preventDefault();anzhiyu.hideTodayCard();')
                  i.anzhiyufont.anzhiyu-icon-arrow-circle-right
                  span.banner-button-text 更多推荐
        
        link(rel="stylesheet", href=theme.home_top.banner.top_group_banner_css)
      
  if theme.home_top.swiper.enable
    if !site.data.essay
      script(src=url_for(theme.home_top.swiper.swiper_js))
    script.
      function initBlogSlider() {
        var swiper = new Swiper(".blog-slider", {
          passiveListeners: true,
          spaceBetween: 30,
          effect: "fade",
          loop: true,
          autoplay: {
            disableOnInteraction: true,
            delay: 3000,
          },
          mousewheel: {
            passive: true, // 将 mousewheel 事件处理程序标记为被动的
          },
          // autoHeight: true,
          pagination: {
            el: ".blog-slider__pagination",
            clickable: true,
          },
        });

        var comtainer = document.getElementById("swiper_container");
        if (comtainer !== null) {
          comtainer.onmouseenter = function () {
            swiper.autoplay.stop();
          };
          comtainer.onmouseleave = function () {
            swiper.autoplay.start();
          };
        }
      }
      setTimeout(()=>{
        initBlogSlider()
      }, 100)

