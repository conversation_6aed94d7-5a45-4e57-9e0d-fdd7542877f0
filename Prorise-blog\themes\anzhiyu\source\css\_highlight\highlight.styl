$highlight_theme = hexo-config('highlight_theme')
wordWrap = $highlight_enable && !$highlight_line_number && hexo-config('code_word_wrap')
wordWrapPrismjs = $prismjs_enable && !$prismjs_line_number && hexo-config('code_word_wrap')

@require 'theme'

:root
  --hl-color: $highlight-foreground
  --hl-bg: $highlight-background
  --hltools-bg: $highlight-tools.bg-color
  --hltools-color: $highlight-tools.color
  --hlnumber-bg: $highlight-gutter.bg-color
  --hlnumber-color: $highlight-gutter.color
  --hlscrollbar-bg: $highlight-scrollbar
  --hlexpand-bg: linear-gradient(180deg, rgba($highlight-background, .6), rgba($highlight-background, .9))
[data-theme='dark']
  --hl-color: alpha(#FFFFFF, .7)
  --hl-bg: lighten(#121212, 2)
  --hltools-bg: lighten(#121212, 3)
  --hltools-color: #90a4ae
  --hlnumber-bg: lighten(#121212, 2)
  --hlnumber-color: alpha(#FFFFFF, .4)
  --hlscrollbar-bg: lighten(#121212, 5)
  --hlexpand-bg: linear-gradient(180deg, rgba(lighten(#121212, 2), .6), rgba(lighten(#121212, 2), .9))

if $highlight_enable
  @require 'highlight/index'

if $prismjs_enable
  @require 'prismjs/index'

$code-block
  overflow: auto
  margin: 0 0 20px
  padding: 0
  if $highlight_theme == 'light' || ($highlight_theme == 'mac light')
    background: var(--anzhiyu-card-bg)
    border: var(--style-border-always);
  else
    background: var(--hl-bg)
  color: var(--hl-color)
  line-height: $line-height-code-block

  if (wordWrap || wordWrapPrismjs)
    counter-reset: line
    white-space: pre-wrap

#article-container
  pre,
  code
    font-size: $code-font-size
    font-family: $code-font-family !important

  code
    padding: 2px 4px
    background: $code-background
    color: $code-foreground

  pre
    @extend $code-block
    padding: 10px 20px

    code
      padding: 0
      background: none
      color: var(--hl-color)
      text-shadow: none

  figure.highlight
    @extend $code-block
    position: relative

    pre
      margin: 0
      padding: 8px 0
      border: none

    figcaption,
    .caption
      padding: 6px 0 2px 14px
      font-size: $code-font-size
      line-height: 1em

      a
        float: right
        padding-right: 10px
        color: var(--hl-color)

        &:hover
          border-bottom-color: var(--hl-color)

  .highlight-tools
    position: relative
    display: flex
    align-items: center
    overflow: hidden
    min-height: 24px
    height: 2.15em
    background: var(--hltools-bg)
    color: var(--hltools-color)
    font-size: $code-font-size
    if $highlight_theme == 'light' || ($highlight_theme == 'mac light')
      border-bottom: var(--style-border-always);

    &.closed
      if $highlight_theme == 'light' || ($highlight_theme == 'mac light')
        border-bottom: 1px solid transparent;
        height: calc(2.15em - 1px);
      & ~ *
        display: none

      .expand
        transition: all .3s
        transform: rotate(-90deg) !important

    .expand
      position: absolute
      padding: .57em .7em
      cursor: pointer
      transition: transform .3s

      & + .code-lang
        left: 1.7em

    .code-lang
      position: absolute
      left: 14px
      text-transform: uppercase
      font-weight: bold
      font-size: 1.15em
      user-select: none

    .copy-notice
      position: absolute
      right: 2.4em
      opacity: 0
      transition: opacity .4s

    .copy-button
      position: absolute
      right: 14px
      cursor: pointer
      transition: color .2s

      &:hover
        color: var(--anzhiyu-main)

  .gutter
    user-select: none

  .gist table
    width: auto

    td
      border: none

  if $highlight_theme == 'mac' || ($highlight_theme == 'mac light')
    figure.highlight
      margin: 0 0 24px
      border-radius: 7px
      box-shadow: 0 5px 10px 0 $highlight-mac-border
      -webkit-transform: translateZ(0)

      .highlight-tools
        &:after
          position: absolute
          left: 14px
          width: 12px
          height: 12px
          border-radius: 50%
          background: #fc625d
          box-shadow: 20px 0 #fdbc40, 40px 0 #35cd4b
          content: ' '

        .expand
          right: 0

          &.closed
            transition: all .3s
            transform: rotate(90deg) !important

          & ~ .copy-notice
            right: 3.45em

          & ~ .copy-button
            right: 2.1em

        .code-lang
          left: 75px

  if hexo-config('highlight_height_limit')
    .code-expand-btn
      position: absolute
      bottom: 0px
      z-index: 10
      width: 100%
      transition: all 0.3s
      font-size: 20px;
      if $highlight_theme == 'light' || ($highlight_theme == 'mac light')
        background: var(--anzhiyu-secondbg)
      else
        background: var(--hlexpand-bg)
      text-align: center
      font-size: $code-font-size
      cursor: pointer
      transform: translateZ(0);
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      font-size: 16px
      if $highlight_theme == 'light' || ($highlight_theme == 'mac light')
        &:hover
          background: var(--anzhiyu-main);
          i
            color: var(--anzhiyu-white);

      i
        if $highlight_theme == 'light' || ($highlight_theme == 'mac light')
          color: var(--anzhiyu-fontcolor)
        else 
          color: var(--hlnumber-color)
        animation: 1.2s ease 0s infinite normal none running code-expand-key

      &.expand-done
        transform: rotate(180deg)
        transition: all 0s, background 0.3s;

        & + table,
        & + pre
          margin-bottom: 1.8em

      &:not(.expand-done)
        & ~ table,
        & ~ pre
          overflow: hidden
          height: unit(hexo-config('highlight_height_limit'), px)

@keyframes code-expand-key
  0%
    opacity: .6

  50%
    opacity: .1

  100%
    opacity: .6