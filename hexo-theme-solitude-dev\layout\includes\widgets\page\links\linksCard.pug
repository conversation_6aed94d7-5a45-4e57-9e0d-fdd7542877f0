.site-card-group
    each item in data.link_list
        .site-card
            if item.tag
                if item.color == 'vip' || item.color == 'speed'
                    span.site-card-tag(class=item.color)= item.tag
                        i.light
                else
                    span.site-card-tag(style=`background-color: ${item.color};`)= item.tag
            a.img(title=item.name, href=url_for(item.link))
                img.flink-avatar(src=item.topimg + (data.topimg_suffix || ''), alt=item.name)
            a.info.cf-friends-link(title=item.name, href=url_for(item.link))
                .site-card-avatar
                    img.flink-avatar.cf-friends-avatar(src=item.avatar + (data.suffix || ''), alt=item.name)
                    .img-alt.is-center= item.name
                .site-card-text
                    span.title.cf-friends-name= item.name
                    span.desc(title=item.descr)= item.descr