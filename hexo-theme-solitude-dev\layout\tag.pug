extends includes/layout.pug

block content
    main.layout#content-inner
        #tag
            #tag-page-tags
                each tag in site.tags.find({ parent: { $exists: false } }).data
                    a(id=tag.name class=(tag.name === page.tag ? 'select' : '') href=url_for(tag.path))
                        span.tags-punctuation
                            i.solitude.fas.fa-hashtag
                            =tag.name
                            span.tagsPageCount=tag.length
            .recent-posts#recent-posts
                each post,index in page.posts.find({ parent: { $exists: false } }).data
                    include includes/widgets/home/<USER>
                include includes/mixins/pagination
            if theme.comment.hot_tip.enable
                include ./includes/widgets/third-party/hot/index.pug
        include includes/widgets/aside/aside