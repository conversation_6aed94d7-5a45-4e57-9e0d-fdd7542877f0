- const { server, site } = theme.artalk

script.
    function updatePostsBasedOnComments() {
        const location = window.location;
        const posts = Array.from(document.querySelectorAll('.recent-post-item[onclick] .post_cover a'))
            .map(item => item.href.replace(location, ''));

        fetchCommentsData(posts)
            .then(data => updatePostElements(posts, data))
            .catch(error => console.error("Error fetching comments:", error));
    }

    function fetchCommentsData(posts) {
        const url = `!{server}/api/v2/stats/page_comment?page_keys=${posts.join(',')}&site_name=!{site}`;
        return fetch(url)
            .then(res => res.json())
            .then(data => data.data);
    }

    function updatePostElements(posts, data) {
        posts.forEach(post => {
            const commentCount = data[post];
            if (commentCount > !{count}) {
                const postElement = document.querySelector(`.recent-post-item[onclick*="${post}"]`);
                if (postElement) {
                    addHotTipIfNeeded(postElement);
                }
            }
        });
    }

    function addHotTipIfNeeded(postElement) {
        const infoTopTips = postElement.querySelector(".recent-post-info-top-tips");
        const originalSpan = infoTopTips?.querySelector(".original");
        const existingHotTip = infoTopTips?.querySelector(".hot-tip");

        if (!existingHotTip && originalSpan) {
            const hotTip = createHotTipElement();
            infoTopTips.insertBefore(hotTip, originalSpan);
        }
    }

    function createHotTipElement() {
        const hotTip = document.createElement("span");
        hotTip.classList.add("hot-tip");

        const icon = document.createElement("i");
        icon.classList.add("solitude", "fas", "fa-fire-flame-curved");
        hotTip.appendChild(icon);

        const commentCount = document.createTextNode("!{_p('hot-tip')}");
        hotTip.appendChild(commentCount);

        return hotTip;
    }