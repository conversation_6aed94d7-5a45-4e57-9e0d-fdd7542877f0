- var custom = theme.nav.right.custom || []
each item in custom
    .nav-button(id=item.id)
        a.site-page(href=url_for(item.url) || 'javascript:void(0);', onclick=item.onclick, title=item.title || '')
            i.solitude(class=item.icon || '')
if theme.nav.right.random
    .nav-button#randomPost_button
        a.site-page(onclick="toRandomPost()", title=_p('nav.randompost'), href="javascript:void(0);")
            i.solitude.fas.fa-dice-d6
if theme.search.enable
    .nav-button#search-button
        a.site-page.social-icon.search(href="javascript:void(0);", title=_p('nav.search'))
            i.solitude.fas.fa-magnifying-glass
if theme.console.enable
    .nav-button#nav-console
        a.console_switchbutton(onclick="sco.showConsole()", title=_p('nav.console'), href="javascript:void(0);")
            label
                i.left
                i.center
                i.right
if !theme.rightside.percent
    .nav-button#nav-totop(onclick="sco.toTop()")
        a.totopbtn
            i.solitude.fas.fa-arrow-up
            span#percent= "0"
#toggle-menu
    a.site-page
        i.solitude.fas.fa-bars