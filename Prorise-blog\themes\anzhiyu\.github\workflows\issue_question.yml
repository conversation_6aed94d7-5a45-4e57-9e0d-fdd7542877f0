name: Issue Question

on:
  issues:
    types: [labeled]

jobs:
  create-comment:
    runs-on: ubuntu-latest
    if: github.event.label.name == 'question'
    steps:
      - name: Create comment
        uses: actions-cool/issues-helper@v2.0.0
        with:
          actions: "create-comment"
          token: ${{ secrets.ISSUSE_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            Hello @${{ github.event.issue.user.login }},  please input issue by template and add detail. Issues labeled by Need More Info will be closed if no activities in 7 days.
            你好 @${{ github.event.issue.user.login }}，请按照issue模板填写, 并详细说明问题和复现步骤等, 60天内未回复issue自动关闭。
