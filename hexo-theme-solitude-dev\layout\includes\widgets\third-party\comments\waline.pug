- const { envId, option ,pageview } = theme.waline
- const { lazyload, count, commentBarrage,use } = theme.comment

script.
    (() => {
        let walineInitFunction = window.walineFn || null

        function initWaline(initFn) {
            const walineOptions = {
                el: '#waline-wrap',
                serverURL: '!{envId}',
                pageview: !{pageview},
                dark: 'html[data-theme="dark"]',
                path: window.location.pathname,
                comment: !{count},
                ...!{JSON.stringify(option)}
            }
            const walineInstance = initFn(walineOptions)
            utils.addGlobalFn('pjax', () => walineInstance.destroy(), 'destroyWaline')
            GLOBAL_CONFIG.lightbox && utils.lightbox(document.querySelectorAll('#comment .wl-content img:not(.wl-emoji)'))
            sco.owoBig({
                body: '.wl-emoji-popup',
                item: '.wl-tab-wrapper button'
            })
        }

        async function loadWaline() {
            if (walineInitFunction) initWaline(walineInitFunction)
            else {
                await utils.getCSS('!{url_for(theme.cdn.waline_css)}')
                const {init} = await import('!{url_for(theme.cdn.waline_js)}')
                walineInitFunction = init || Waline.init
                initWaline(walineInitFunction)
                window.walineFn = walineInitFunction
            }
            !{commentBarrage} && barrageWaline()
        }

        if (!{use[0] === 'Waline'} || !{lazyload}) {
            if (!{lazyload}) utils.loadComment(document.getElementById('waline-wrap'), loadWaline)
            else loadWaline()
        } else window.loadTwoComment = loadWaline
    })()

if commentBarrage
    script.
        async function barrageWaline() {
            const url = new URL('!{envId}/api/comment')
            const params = {path: window.location.pathname, sortBy: 'insertedAt_asc'}
            Object.entries(params).forEach(([key, value]) => url.searchParams.append(key, value))
            await fetch(url).then(async res => {
                if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`)
                const data = await res.json();
                const regex = /<img [^>]*class="wl-emoji"[^>]*>/;
                const init = () => {
                    initializeCommentBarrage(data.data.data
                        .map(item => ({
                            nick: item.nick,
                            mailId: item.avatar,
                            content: item.comment.replace(regex, ''),
                            id: item.objectId
                        })))
                }
                if (typeof initializeCommentBarrage === "undefined") await utils.getScript('!{url_for(theme.cdn.commentBarrage)}').then(init)
                else init()
            }).catch(error => console.error("An error occurred while fetching comments: ", error))
        }