<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>2️⃣ 电商实战（下） | Prorise的小站</title><meta name="keywords" content="产品经理实战"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="2️⃣ 电商实战（下）"><meta name="application-name" content="2️⃣ 电商实战（下）"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="2️⃣ 电商实战（下）"><meta property="og:url" content="https://prorise666.site/posts/30992.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第七章：售后管理在我看来，用户下单付款，绝不意味着我们服务的结束，恰恰相反，它开启了我们与用户之间，一段更需要“信任”来维系的长期关系。 如何处理好用户在购后遇到的种种问题，如何设计一套公平、清晰、高效的售后流程，直接决定了我们平台的口碑和用户的复购率。 7.1 售后场景构建在设计任何具体的售后功"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp"><meta name="description" content="第七章：售后管理在我看来，用户下单付款，绝不意味着我们服务的结束，恰恰相反，它开启了我们与用户之间，一段更需要“信任”来维系的长期关系。 如何处理好用户在购后遇到的种种问题，如何设计一套公平、清晰、高效的售后流程，直接决定了我们平台的口碑和用户的复购率。 7.1 售后场景构建在设计任何具体的售后功"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/30992.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"2️⃣ 电商实战（下）",postAI:"true",pageFillDescription:"第七章：售后管理, 7.1 售后场景构建, 1. 不同角色的关注点, 2. 核心售后场景提炼, 3. 售后职责与需求提取, 7.2 售后流程分析, 1. 场景一：待付款取消订单, 2. 场景二：待发货取消订单, 3. 场景三：待收货x2F已完成 - 退款退货, 4. 场景四：待收货x2F已完成 - 申请换货, 5.商家端与平台端的售后管理页面, 7.3 交易纠纷处理, 1. 平台介入的原则与时机, 2. 交易纠纷处理流程与功能设计, 3. 证据链条设计, 第八章：电商后台 - 种草管理, 8.1 学习目标, 8.2 话题分类与话题管理, 8.2.1 话题分类管理, 8.2.2 话题管理, 8.3 种草内容与评论管理, 8.3.1 内容审核策略, 第九章：电商后台 - 财务管理, 9.1 学习目标, 9.2 清算与结算, 9.2.1 核心概念定义（清算 vs 结算）, 9.2.2 资金与信息流程分析, 9.2.3 核心合规问题：二清, 9.2.4 平台对账管理, 9.3 财务管理, 9.3.1 商家账单与提现管理, 1. 商家端设计, 2. 平台端设计, 9.3.2 平台抽佣与计费规则, 9.3.3 发票管理, 第十章：分销电商, 10.1 学习目标, 10.2 分销电商项目背景, 1. 为什么要做分销电商？, 2. 分销电商的核心需求, 10.2.1 什么是分销电商, 10.2.2 核心角色定义（供货商、分销商、消费者）, 10.3 分销电商的优势, 10.3.1 低成本快速裂变, 10.3.2 强信任关系转化, 10.3.3 轻资产运营, 10.4 分销电商搭建思路, 10.4.1 分销员体系设计, 10.4.2 商品与供应链管理, 10.4.3 佣金与结算体系, 10.5 分销电商产品设计, 10.5.1 平台端核心功能, 10.5.2 商家端核心功能, 10.5.3 分销商端核心功能, 10.6 本章总结, 第十一章：直播电商, 11.1 直播电商项目背景, 11.1.1 为什么需要直播电商？, 11.1.2 到底什么是直播电商？, 11.1.3 直播电商的三种主流模式, 11.2 直播电商的设计思路, 11.2.1 核心角色与需求分析, 11.2.2 核心业务流程梳理, 11.2.3 整体功能架构规划, 11.3 直播电商的产品设计, 11.3.1 平台端：规则的制定者与秩序的守护者, 11.3.2 商家端：商户的运营指挥中心, 11.3.3 用户端：主播与观众的互动舞台, 11.4 直播电商的关键技术, 11.4.1 核心概念：推流与拉流, 11.4.2 直播的技术全景图, 11.4.3 产品经理的技术选型：自研 vs. 第三方SDK第七章售后管理在我看来用户下单付款绝不意味着我们服务的结束恰恰相反它开启了我们与用户之间一段更需要信任来维系的长期关系如何处理好用户在购后遇到的种种问题如何设计一套公平清晰高效的售后流程直接决定了我们平台的口碑和用户的复购率售后场景构建在设计任何具体的售后功能之前我的第一步是构建出所有可能发生的售后场景我需要绘制一张完整的售后地图确保我的设计能够覆盖所有可能的用户求助路径不同角色的关注点售后这件事从来不是一个单方的行为它至少牵动着用户商家平台这三方的心我构建场景会先从理解这三方的不同痛点和关注点开始角色核心关注点用户我买的东西不想要了有问题怎么才能快速方便地退换商家用户退回来的货有没有问题退换货的运费成本谁来承担差评会不会影响我的店铺平台我们的售后规则是否公平如何才能在保障用户体验和控制商家风险之间找到平衡处理这些纠纷需要多少客服人力核心售后场景提炼基于用户的核心关注点我就可以提炼出用户在订单生命周期的不同阶段会发起的几大类核心售后场景这些场景就是我们后续设计流程的需求来源订单状态用户可能发起的售后场景订单未付款取消订单订单已付款待发货取消订单申请仅退款待收货已完成申请退款退货申请换货申请仅退款如商品漏发任意环节交易纠纷申请平台介入售后职责与需求提取场景明确后我需要为这些场景定义清晰的责任边界和业务规则这部分内容将是我后续撰写和设计流程的基础我的设计将遵循以下的基本职责划分用户职责发起者一切售后行为由买家发起我的用户端产品设计必须为用户在订单详情页等位置提供清晰便捷的售后申请入口商家职责处理者商家收到售后申请进行响应商家是处理售后问题的第一责任人我的商家后台设计必须为商家提供处理这些申请的技术支持但具体的处理结果同意拒绝由商家自己决定平台职责保障者与仲裁者当用户与商家发生纠纷时平台介入保障双方权益我的平台运营后台设计必须包含一套纠纷仲裁机制在保障用户交易安全的前提下对纠纷进行公平的判决售后流程分析在我们构建了售后场景明确了各方角色的职责之后下一步就是为每一个具体的场景设计出清晰严谨可执行的业务流程我设计的售后流程就像是一部法律它需要清晰地定义出在某种售后场景下用户和商家各自拥有什么权利需要履行什么义务以及系统应该如何根据他们的操作来自动地流转订单的售后状态我们将逐一分析几种最高频的售后场景场景一待付款取消订单核心逻辑这是最简单的售后场景角色完全由买家单方面发起和完成流程用户在我的订单中找到待付款的订单直接点击取消订单订单状态即变为已取消我的设计思考为什么这个流程商家完全不参与因为在用户付款之前这笔订单尚未进入商家的待办事项即待发货列表中没有对商家产生任何实质性的履约成本因此我设计的流程允许用户在这个阶段自由地无条件地取消订单场景二待发货取消订单核心逻辑这个场景开始涉及到买卖双方的交互角色由买家发起申请但需要商家进行审核流程买家在待发货订单中发起取消申请商家在后台收到申请进行审核若商家同意则订单取消系统自动退款若商家拒绝则订单继续保持待发货状态我的设计思考为什么商家需要审核因为用户付款后订单就已经进入了商家的履约流程商家可能已经在拣货打包甚至已经交给了快递员但还未揽件这个审核的环节就是我留给商家的一个拦截窗口让他去确认这笔订单是否还能从他的发货流程中被成功地拦截下来场景三待收货已完成退款退货这是最复杂的售后场景因为它涉及到逆向物流即用户把商品寄回给商家我必须为这个场景设计一套严谨的多步骤的售后状态机我的流程与状态设计售后状态触发动作下一步状态初始买家在用户端对待收货或已完成的订单发起退款退货申请并填写理由待商家审核待商家审核商家在后台审核通过买家的申请待买家发货待买家发货买家在用户端填写退货的快递单号或上门与自行寄回商家待收货商家待收货商家在后台确认已收到买家寄回的商品且商品完好无损退款中退款成功场景四待收货已完成申请换货换货流程因为涉及到双向物流买家寄回商家寄出所以它的状态机比退货更复杂我的流程与状态设计换货流程的前步与退款退货完全一致从待商家审核到商家待收货在商家确认收到退货后流程继续售后状态触发动作下一步状态商家待收货商家在后台确认收到退货后将新的商品重新发货给买家并填写新的快递单号待买家收货待买家收货买家在用户端确认收到商家换发的商品换货流程结束通过为每一个售后场景都设计这样一套清晰的流程和状态机我就可以确保我们平台用户商家三方在处理复杂的售后问题时都有据可依有路可循商家端与平台端的售后管理页面交易纠纷处理在我们设计的售后流程中大部分的退款退货申请都可以由用户和商家通过协商来顺利解决但是我们必须考虑到一种情况当用户和商家无法达成一致时应该怎么办比如用户申请退货的理由是商品有质量问题并上传了图片而商家则反驳认为是用户人为损坏并拒绝了退款申请此时双方就陷入了交易纠纷如果平台不介入这个矛盾将永远无法解决并会极大地损害用户对平台的信任因此我必须设计一套公平公正透明的平台介入仲裁机制平台介入的原则与时机我设计这套仲裁机制会遵循以下核心原则保障交易安全我设计的平台规则会优先保障用户的合法权益明确介入时机平台介入的触发器非常明确在售后流程中任何一方的合理请求被另一方拒绝时系统就应该为被拒绝的一方提供申请平台介入的入口依赖双方举证平台作为法官绝不偏听偏信我的判决必须建立在双方提供的证据之上交易纠纷处理流程与功能设计整个交易纠纷的处理我将它设计为一个严谨的多方参与的线上流程用户端功能当用户的售后申请被商家拒绝后我会在他的订单详情页提供一个申请平台介入的按钮点击后会进入举证页面用户可以在这里上传他认为能支持自己诉求的文字图片视频等证据商家端功能当用户申请平台介入后这笔售后订单在商家后台的状态就会变为待商家举证商家同样需要在这个订单的详情页进入举证页面上传对他有利的证据如发货前的商品完好视频与用户的聊天记录等平台端功能这是我们内部客服和仲裁团队的法庭维权列表所有用户申请介入的纠纷单都会进入到这个独立的维权列表工作队列中等待我们的客服法官来处理维权详情页这是法官的判案工作台我设计的这个页面会聚合这笔纠纷的所有信息原始的订单信息完整的售后申请记录和双方的沟通日志买家提供的证据卖家提供的证据在页面的最下方我会为法官提供最终的判决功能按钮比如支持买家或支持卖家证据链条设计证据是我们整个仲裁流程的核心因此我设计的举证页面无论是用户端还是商家端都必须支持上传多种形式的证据证据类型我的设计说明文字描述供双方清晰地有条理地陈述事情的经过和自己的诉求图片视频证据这是最直观的证据如商品损坏部位的照片开箱视频证明商品货不对板的截图等凭证类文件包括但不限于与对方的聊天记录发货退货的快递底单甚至是物流公司出具的红章证明等通过这套严谨的申请介入双方举证平台判决的流程我为我们的电商平台建立起了一道能化解交易矛盾保障用户和商家双方合法权益的安全网第八章电商后台种草管理在第三章我们为用户端设计了商品种草这个核心的内容驱动的社区化模块用户可以在这里发布自己的购物心得并与其他用户进行互动现在我们必须回到平台运营后台来为这个模块设计一套相应的管理系统我们作为平台至少需要解决三个核心问题用户发布种草笔记时可选的话题分类从哪里来笔记可以关联的话题标签又从哪里来用户发布的这些海量的用户生产内容我们平台如何进行管理和审核这一章我们就来逐一设计解决这些问题的后台功能学习目标在本节中我的核心目标是带大家掌握电商后台中社区化模块的管理后台设计我们将学习如何设计一套话题分类与话题的二级管理体系并为运营同事设计高效的种草内容与评论的审核后台话题分类与话题管理为了让用户发布的种草笔记能够被有组织有结构地呈现我必须在后台预先定义好一套分类与话题的体系话题分类管理话题分类是我为种草社区设定的最高层级的类似频道的内容划分比如服饰穿搭数码评测美妆心得等我设计的分类管理后台核心功能如下基础管理运营人员可以对分类进行新增编辑删除查询状态管理每个分类都有显示隐藏两种状态运营可以将某个分类暂时隐藏那么这个分类就不会在用户端展示用户发布时也无法选择排序运营可以通过调整一个排序值来控制这些分类在用户端的显示顺序话题管理话题是隶属于某个话题分类之下更具体更聚焦的标签比如在服饰穿搭这个分类下就可以有小个子穿搭夏日多巴胺等多个热门话题我设计的话题管理后台除了基础的增删改查和状态管理外最核心的一个设计点是关联分类在新增或编辑一个话题时我必须让运营可以从我们上一步创建好的话题分类列表中选择一个来与这个话题进行关联这个分类话题的二级结构就构成了我们整个种草社区内容组织的骨架种草内容与评论管理内容审核策略对于社区如果每一条内容都采用先审后发的模式那审核的压力会极大并且会严重影响用户的发布体验因此我通常会采用先发后审的策略系统先通过我们设计的敏感词系统进行一次自动过滤只要内容不包含敏感词就允许它被正常发布立刻对其他用户可见然后这条内容会进入我们后台的人工审核队列由运营同事在稍后进行二次审核来处理那些漏网之鱼第九章电商后台财务管理欢迎来到第九章在这一章我们将探讨电商平台的心脏资金的流动我作为产品经理虽然不需要成为财务专家但我必须深刻理解电商交易中资金清算与结算的基本逻辑和合规要求因为任何与钱相关的设计都必须将安全准确合规这三个词刻在我的脑海里学习目标在本章中我的核心目标是带大家掌握电商后台财务管理的基础我们将重点学习清算与结算的核心概念及业务流程并深入理解其中至关重要的合规问题清算与结算当一个用户在我们的平台支付了元这元是如何从用户的口袋安全准确地在扣除我们的平台佣金后最终到达商家的口袋里的要回答这个问题我们就必须先理解两个核心的金融概念清算和结算核心概念定义清算结算我用一个通俗的方式来帮大家理解这两个概念概念核心产出清算它的工作是对某一个周期内比如一天发生的所有交易数据进行汇总分类计算最终准确地算出今天我平台总共应该给商家打多少钱给商家打多少钱结算它的工作是依据清算得出的结果进行实际的资金划拨操作把钱从一个账户转移到另一个账户简单来说清算是脑力劳动结算是体力劳动先算清楚再打款资金与信息流程分析在一个合规的电商平台中清结算的过程包含了资金流和信息流这两条并行的线信息流我们平台的订单数据会流入到我们后台的管理页面我们的财务同事会基于这些信息来进行查询和对账资金流用户支付的钱会先进入到一个第三方的清结算机构比如支付宝微信支付银行的资金账户中这个机构会根据我们平台提供的清算信息进行结算最终将钱打到商家的结算账户中商家再进行提现核心合规问题二清在理解了上述流程后我们就必须来探讨电商支付领域最重要也是最危险的一条红线二清什么是二清二清指的是二次清算它是指没有获得国家支付业务许可证的机构比如我们这个电商平台自身直接经手交易资金并进行清分结算的行为为什么违规如果我们平台的业务流程是买家平台公司账户卖家这意味着我们平台在中间形成了一个汇集了所有交易资金的资金池这种行为是在行使银行或支付机构的职能但我们并没有获得对应的金融牌照这是严重违规的会面临巨大的法律和政策风险合规的流程是怎样的合规的流程必须是买家持牌机构卖家持牌机构就是指像支付宝微信支付这样拥有国家颁发的支付业务许可证的机构在整个交易过程中我们平台只能处理信息流即订单信息绝对不能触碰资金流我们只能向持牌机构下达支付和结算的指令而实际的资金划拨必须由这些持牌机构来完成平台对账管理基于上述的合规流程我设计的平台财务后台一个最核心的功能就是对账管理它的作用是我们的系统需要每天自动地从我们合作的持牌支付机构如支付宝微信支付那里下载前一天的交易账单然后系统需要将这份外部账单与我们自己数据库里的内部订单记录进行逐条比对最终目的是确保每一笔交易的金额状态内外部都是完全一致的并自动地将差异和问题比如掉单标记出来供我们的财务同事进行处理财务管理在我们节设计的清结算流程中我们确保了交易的资金都安全合规地进入到了由持牌支付机构监管的账户中现在我们就需要设计一套功能来处理这笔钱后续的分配和管理财务管理后台就是我们用来处理平台与商家之间分钱和打钱的系统商家账单与提现管理这个功能的设计我需要同时考虑商家端和平台端两个方面因为它是买卖双方之间的一个完整互动流程商家端设计我需要为商家提供一套清晰透明便捷的账房工具商家查看结算记录在我设计的商家后台中会有一个结算中心商家可以在这里清晰地看到平台在每个结算周期如每月为他结算的总订单数总结算金额并能查询到构成这笔总额的每一笔订单明细确保账目的清晰透明商家申请提现当结算完成后这笔钱就会进入商家的可提现余额我会为商家设计一个账户概况页面清晰地展示他的账户余额并提供一个醒目的申请提现按钮点击后商家可以输入他希望提现的金额并确认收款的银行账户信息商家查看提现记录提交申请后商家可以在提现记录页面实时地追踪这笔提现的状态如待审核提现中已到账已驳回等平台端设计商家的提现申请会触发我们平台运营后台的一系列审核和操作流程平台审核提现申请我需要为我们的财务同事设计一个提现审核列表所有商家的提现申请都会进入这个工作队列财务同事的核心操作就是对申请进行审核审核通过后该笔申请的状态就会流转为待转账财务执行转账进入待转账队列后财务同事会通过企业网银等方式进行线下的实际打款打款完成后他会在后台点击确认转账按钮并填写相关的支付凭证信息此时这笔提现流程才算最终完成状态变为已转账平台抽佣与计费规则在清算的过程中一个核心的业务逻辑就是计算我们平台的收入即平台抽佣我的设计我会在平台运营后台设计一个计费规则管理模块在这里我的业务部门可以为不同的商品类目配置不同的交易佣金比例比如服装类目抽佣数码类目抽佣系统应用在我们节的清算环节系统就会自动地根据这些预设好的规则去计算每一笔订单我们平台应该抽取的佣金然后再把剩下的金额计入商家的可结算金额中发票管理一个完善的财务后台还需要处理发票这个重要的业务我的设计我需要设计两套发票流程商家向平台申请服务费发票商家可以就支付给我们的平台服务费向我们申请开具发票用户向商家申请商品发票用户可以就购买的商品向商家申请开具发票这个申请会流转到商家后台由商家进行处理第十章分销电商欢迎来到第十章在前面的章节中我们已经完整地学习了如何设计一个人货场模型下的平台型电商现在我们将探讨一种能为平台带来强大裂变增长能力的建立在社交关系链之上的高级模式分销电商学习目标在本章中我的核心目标是带大家系统性地掌握分销电商的业务模式与产品设计我们将从项目背景出发理解分销电商的定义和核心角色并最终学会如何为这个模式设计其独特的产品功能分销电商项目背景为什么要做分销电商我之所以要考虑在我们的电商产品中融入分销模式其核心的驱动力是为了解决传统电商模式获客成本越来越高的瓶颈分销电商本质上是一种的模式它通过一种利益共享的机制将我们平台上的海量端用户转化为成千上万的小分销商让他们利用自己的私域流量和社交信任去为我们获取更多的新用户分销电商的核心需求基于这个背景我提炼出的搭建分销系统的核心产品需求如下用户可以申请成为平台的分销商商家有权利自定义自己店铺的商品是否允许分销分销商可以发展自己的下线但为了确保业务合规层级不能超过两级什么是分销电商我给分销电商的定义是一个通过设置销售提成作为激励驱动平台用户即分销商利用其自有的社交关系进行商品分享和销售裂变并最终达成自购省钱分享赚钱目的的商业模式正如案例所示分销最常见的形态就是用户将一个带有自己专属二维码或链接的商品海报分享到微信群或朋友圈当他的好友通过这个链接完成购买后他就能获得平台支付的相应比例的佣金在这个模式中我们平台需要为分销商解决好除了销售以外的一切后顾之忧即统一提供货源仓储配送和售后服务核心角色定义供货商分销商消费者我设计分销系统需要清晰地定义出这个新生态中的三个核心角色核心角色我的定义与解读供货商这是货的来源他们可以是我们平台自营的商品也可以是我们平台上参与了分销活动的第三方商家他们的核心诉求是提升商品销量分销商这是我们这个模式中新增的核心角色他们是平台的普通用户在申请成为分销商后就拥有了带货的资格他们不拥有商品不处理订单不负责发货他们唯一的工作就是分享和推广他们的核心诉求是赚取佣金消费者这是最终完成购买的终端用户他们通常是某个分销商的好友或粉丝他们的购买决策很大程度上是建立在对分销商的信任之上分销电商的优势我们已经清楚了分销电商的定义和核心角色现在我需要回答一个关键的商业问题作为一个产品或业务的决策者我为什么要选择分销这种模式答案在于一个设计良好的分销体系能为我们带来传统电商模式难以企及的三大核心优势低成本快速裂变在我看来分销模式最强大最核心的优势就是它解决了现代电商最头痛的问题高昂的获客成本传统模式的困境传统的电商平台需要花费巨额的市场预算去购买流量投放广告来吸引用户分销模式的破局分销模式本质上是将我们的营销预算从购买流量变为了奖励用户我不再花钱给广告平台而是把这部分钱以销售佣金的形式直接分给了帮我们带来客户的分销商这相当于我们将每一个分销商都发展成了我们行走的广告牌和销售渠道他们利用自己的社交关系链进行一带十十带百的裂变式传播正如云集的案例数据显示其单个用户维系成本显著低于阿里京东等传统流量驱动的电商平台这就是裂变带来的低成本优势强信任关系转化分销模式的第二个巨大优势是它能带来极高的销售转化率和用户忠诚度传统模式的挑战用户面对一个冰冷的平台推送的广告内心天然是带有防备和不信任的分销模式的破解分销模式的传播是建立在社交信任的基础之上的朋友的推荐远比平台的广告更具说服力当一个消费者看到他朋友圈里一位他所信任的好友或在真实地分享一款产品的使用心得时他的购买决策链路会变得极短这种基于信任的转化效果是惊人的云集案例中提到的复购率达到就是这种强信任关系带来高用户粘性的最好证明轻资产运营分销模式的第三个优势是它为分销商这个角色提供了一种极具吸引力的轻资产运营模式我把它总结为你只管卖其他都交给我电商环节由谁负责对分销商意味着什么供货选品平台供货商分销商无需自己找货源仓储库存平台供货商分销商无需自己租仓库压库存发货物流平台供货商分销商无需自己打包发快递售后服务平台供货商分销商无需自己处理复杂的退换货问题推广销售分销商分销商只需要专注于他最擅长最核心的一件事分享和推广正是这种轻资产的模式极大地降低了个人成为小老板的门槛使得我们的分销商队伍可以像滚雪球一样快速地发展和壮大分销电商搭建思路我们已经理解了分销电商的是什么和为什么现在我们就进入最核心的怎么做的环节要搭建一套完整的分销电商体系我作为产品经理需要从顶层设计好三大核心支柱分销员体系商品与供应链体系以及佣金与结算体系这三大支柱共同构成了我们分销业务的骨架分销员体系设计分销业务核心是人的生意因此我首先要设计好我们分销员这个核心角色的完整生命周期和组织结构角色与层级为了激励分销员为平台带来更多的下线分销员我设计的体系通常会包含分销层级核心逻辑一个高级别的一级分销商可以邀请新人成为他的二级分销商当二级分销商卖出商品时一级分销商也能获得一部分的团队奖励我的合规设计要点我必须强调为了确保业务的合法合规在国内设计分销体系时计佣计算佣金的层级绝对不能超过三级这是一个不可逾越的红线核心流程我设计的整个分销员体系必须能够支撑起以下几个核心的业务流程成为分销商普通用户可以通过一个申请入口提交申请由平台审核通过后获得分销商身份分享商品分销商可以在内选择商品生成带有自己专属推广码的海报或链接并分享出去发展下线分销商可以生成自己专属的邀请码邀请好友来注册成为自己的下线分销商购买转化当一个普通消费者通过分销商分享的链接完成购买后系统需要准确地记录下这笔订单的归属商品与供应链管理分销员只负责推广而不负责货因此我必须在后台设计好货的管理逻辑平台侧在平台运营后台我需要设计一个总开关可以一键启用或关闭整个平台的分销功能商家侧在商家后台我需要为商家提供两级控制权店铺级开关商家可以决定自己整个店铺是否参与平台的分销活动商品级开关在参与活动的前提下商家还可以进一步地去勾选指定的某些商品来参与分销佣金与结算体系这是驱动整个分销体系运转的发动机我设计的佣金结算体系必须公平透明准确佣金规则配置我需要在平台运营后台设计一个强大的佣金规则引擎它需要支持运营同事可以灵活地按不同维度来设置佣金比例按商品设置不同的商品可以有不同的佣金比例按分销商等级设置高级别的分销商可以享受更高的佣金比例团队奖励设置可以设置当下线分销商出单时其上级可以获得的奖励比例结算与提现当一笔通过分销链接产生的订单完成交易即已过售后维权期后系统需要自动地将计算好的佣金打入对应分销商的佣金账户中同时我需要在分销商的专属后台为他设计清晰的收益报表和便捷的佣金提现功能综上所述我搭建分销电商的整体思路就是围绕人分销员体系货商品管理钱佣金体系这三大核心分别为用户端商家端平台端设计出支撑其运转所必需的功能分销电商产品设计在我们明确了分销电商的搭建思路之后现在我们就进入具体的产品功能设计环节我将严格按照平台端商家端分销商端这三个不同的使用者视角来分别进行功能设计的拆解平台端核心功能这是整个分销系统的总控制器由我们平台的运营人员使用用来设定整个分销业务的游戏规则分销规则配置我设计的后台必须有一个全局的分销设置页面在这里运营可以设置是否开启分销是否开启自购分佣分销层级最多支持几级以及每一级的抽佣比例分销员等级管理为了激励分销商我还会设计一个分销等级管理后台运营可以在这里创建不同的分销商等级如初级中级高级并为每个等级配置不同的邀请奖励和销售抽成比例以及对应的升级规则分销员审核管理当有普通用户申请成为分销商时他们的申请会进入到这个后台的待审核列表中运营人员可以在这里查看申请人的信息并进行通过或驳回的操作订单与结算管理我需要设计一个分销订单列表让运营和财务可以清晰地看到每一笔通过分销产生的订单以及这笔订单需要为哪几级的分销商分别计算多少佣金同时还需要结算设置和提现管理功能来处理佣金的发放商家端核心功能这是我们设计给供货商即参与分销的商家使用的后台核心是让他们能够对自己店铺的分销业务进行自主管理分销商品管理在商家后台的商品管理模块我需要为商家提供一个分销商品设置的功能在这里商家可以勾选自己店铺中愿意拿出利润来进行分销的商品并且可以为这些商品设定一个基础的佣金比例分销业绩查看我还需要为商家提供一个查看分销业绩的报表在这里他可以看到是哪些分销商为他带来了哪些订单让他可以直观地感受到分销为店铺带来的价值分销商端核心功能这是我们设计给分销商本人使用的个人工作台它通常会内嵌在我们用户端的个人中心里申请成为分销商首先我需要在用户端的个人中心等位置为普通用户提供一个清晰的申请成为分销商的入口选品中心与推广当用户成为分销商后他的个人中心就会出现分销中心的模块在分销中心里他可以浏览所有可供分销的商品在商品详情页上会有专属于他的自购省钱和分享赚钱按钮点击分享赚钱系统会自动为他生成带有专属推广二维码的精美海报收益与提现这是分销商最关心的模块我设计的这个页面必须清晰地展示他的今日收益累计收益等核心数据并提供一个醒目的提现入口团队管理为了鼓励裂变我还需要为分销商设计一个简单的我的团队管理功能在这里他可以获取专属的邀请链接海报用于发展自己的下线团队并查看团队的业绩概况本章总结在本章我们系统性地学习了分销电商这种独特的商业模式背景与优势我们理解了它通过社交裂变来降低获客成本提升转化率的核心价值搭建思路我们明确了搭建分销体系需要从分销员商品佣金这三大支柱入手产品设计我们分别为平台商家分销商这三方设计了支撑其业务运转所必需的核心功能第十一章直播电商欢迎来到第十一章在过去的学习中我们已经掌握了平台电商的稳固根基和分销电商的裂变增长现在我将带您进入一个能将购物体验和销售转化推向极致的全新领域直播电商这是一种将实时互动与商品销售无缝融合的极具沉浸感的商业模式直播电商项目背景在我负责的产品中每当要引入一个像直播这样重大的新功能时我都会先回归到最根本的商业问题上我们现有的模式遇到了什么瓶颈而这个新功能是否能成为破局的关键为什么需要直播电商传统的货架式电商本质是人找货用户带着目的来搜索比价这种模式在今天面临着越来越大的挑战流量越来越贵用户的注意力越来越分散单纯的打折促销也越来越难以打动他们我发现直播电商恰好能从三个方面完美地破解这些困局从花钱买流量到内容吸流量传统电商需要不断地投入巨额广告费去购买流量而直播电商特别是与关键意见领袖的合作是利用主播自带的影响力和内容创作能力将他的粉丝高效地吸引到我们的平台上来这是一种更聪明更具性价比的获客方式从理性对比到感性促单在传统电商的图文页用户的决策链路相对较长消费也更趋于理性但在直播间里主播通过现场试用实时互动和限时限量的话术能够营造出一种不买就亏了的紧迫感和热烈氛围这极大地激发了用户的感性消费和冲动购买转化率自然远超平时从静态浏览到沉浸互动图文详情页是静态的单向的而直播是一种所见即所得的沉浸式体验我可以实时看到衣服的上身效果可以要求主播展示产品的某个细节可以通过弹幕与成千上万的人交流这种丰富立体的购物体验是传统电商无法比拟的到底什么是直播电商所以到底什么是直播电商在我看来直播电商的核心是商业模式从以货为中心向以人为中心的彻底转变它不再是冰冷的货架而是基于一个活生生的你所信任或喜爱的主播来建立交易消费者购买的不仅仅是商品本身更是对这个主播的品味专业度或个人魅力的信任票这种以信任为前提的商业模式其根基依然是电商但能量却被放大了无数倍我们想象一个真实的场景当主播在镜头前一边讲解着手机的各项参数一边实时回答着待机时间多久拍照效果怎么样这些弹幕提问并在几万人的共同见证下喊出上链接时那一刻它已经超越了单纯的卖货变成了一场极具参与感的线上狂欢这就是直播电商的魅力直播电商的三种主流模式理解了直播电商的价值和内核后作为产品经理我的下一步就是从顶层设计上思考我们平台到底要做哪一种在我的实践中通常会遇到三种主流的业务模式带货模式这是最典型爆发力最强的一种如果我的业务目标是在短期内快速提升品牌知名度引爆一款单品的销量那么与外部的头部合作无疑是最佳选择他们带来海量粉丝我们提供优质商品这是一场强强联合店铺直播模式店播这是一种更着眼于长期健康的模式我把它看作是平台必须为商家提供的基础设施我们赋能平台上的商家让他们可以在自己的一亩三分地里由老板或者店员自己出镜进行常态化的直播这不追求一夜爆火而是为了帮助商家更好地维护自己的老客沉淀私域流量是一种细水长流的生意直播分销模式这是一种最大化利用平台生态的极具想象力的模式它将直播和分销结合允许我们的普通用户申请成为分销主播平台提供统一的货盘他们只需要开播去推广就能赚取佣金这相当于将我们平台上成千上万的用户都变成了我们行走的会说话的销售渠道直播电商的设计思路在上一节我们明确了为什么要做直播电商现在我的角色就要从一个业务分析师切换到一个产品架构师在真正开始画原型写文档之前我必须先搭建起整个产品的骨架这个过程我称之为设计思路的梳理核心角色与需求分析要设计一个好的系统我首先要清晰地定义出这个系统里都有谁他们分别想做什么这就是角色与需求分析在直播电商这个场景里我识别出了四个核心角色普通用户他们是观众是消费者他们的核心诉求是逛得开心买得方便店铺主播他们是表演者是销售员他们是直播间的灵魂核心诉求是互动热烈卖得更多店铺运营他们是幕后管理者他们负责申请开通直播管理直播计划处理订单等核心诉求是管理高效掌控全局平台这就是我们自己我们的核心诉求是秩序井然生态繁荣需要有最高的管理权限为了确保不遗漏任何关键功能我会将这些角色的核心需求整理成一张清晰的列表作为我们后续产品设计的需求清单角色我的解读核心需求点普通用户能流畅地观看直播并与主播进行实时互动如发弹幕点赞能在直播间里方便地查看正在讲解的商品并快速下单购买店铺运营需要有一个后台可以向平台方提交开通店铺直播功能的申请对于已经创建或正在直播的场次需要有管理和控制的能力店铺主播能够在内轻松地发起一场直播并能便捷地将自己店铺的商品上架到直播间进行讲解在直播过程中能看到观众的互动并进行回应以提升直播间热度平台作为系统的所有者我们需要有能力对所有店铺的直播间进行统一的管理和监控确保合规核心业务流程梳理当我把这些零散的需求点都定义清楚后下一步就是用一条流程线将它们串联起来形成一个完整的业务闭环我需要确保不同角色之间的协作是顺畅的我通常会用一张泳道图来可视化这个核心流程让团队里的每一个人都能清晰地看到自己负责的部分在整个业务链条中所处的位置这个流程是这样运转的一切的起点是店铺运营向平台提交了开通直播的申请平台审核通过后该店铺就获得了直播的能力店铺主播现在可以正式发起直播并将准备好的上架商品海量的普通用户被吸引进入直播间观看直播并在主播的带动下完成下单最后订单流转到店铺运营那里由他们进行确认订单和后续的履约发货你看通过这样一张流程图一个完整的多角色协作的业务故事就被清晰地呈现了出来整体功能架构规划有了角色和流程我就可以在脑海中勾勒出整个产品的功能架构蓝图了我会把需要开发的功能按照使用者的不同划分到不同的端里去我将整个直播电商系统规划为三大功能模块用户端这是我们产品的主阵地承载了最多的功能它既包含了普通用户的观看互动购买功能也包含了主播开播管理商品等核心功能在这里我暂时将主播端和用户端合并在一起考虑因为它们都发生在同一个内很多界面是共通的商家端这就是我为店铺运营人员所设计的后台管理系统他们在这里申请权限管理直播间平台端这是我们自己使用的上帝后台在这里我们可以管理所有商家和直播间设定平台的规则至此直播电商的设计思路就已经非常清晰了我们明确了为谁设计核心角色设计什么需求列表以及它们如何协同工作业务流程和功能架构这个清晰的骨架将是我们下一节进行具体产品功能设计的坚实基础直播电商的产品设计在我们梳理清楚了设计思路明确了要做什么之后现在就到了将蓝图转化为具体页面的阶段作为产品经理我会兵分三路同时推进平台端商家端用户端这三个关键阵地的产品设计平台端规则的制定者与秩序的守护者我设计平台后台的唯一原则就是权责对等平台作为整个直播生态的所有者必须拥有至高无上的管理权限来确保整个业务健康有序地运转这主要体现在两个方面管店铺和管直播直播店铺管理我们必须有一个准入机制并非所有商家都有资格开通直播否则劣质的直播内容会摧毁用户体验因此我需要为平台的运营同事设计一个强大的店铺审核后台这个后台的核心就是对资格状态的精细化管理运营人员在这里可以清晰地看到所有申请店铺的列表并进行审核查看取消资格或恢复资格等操作每一个按钮都代表了平台的一种管理权力是确保直播商家质量的第一道防线直播间管理除了管人店铺我们更要管事直播平台需要能够监控到所有正在发生和已经发生的直播在这个界面我最看重的是操作栏里的结束按钮这代表了平台的干预权当一场直播出现违规内容或其他紧急情况时平台必须有能力在第一时间从最高权限上强制将其关停这是我们作为平台方必须承担的责任也是保障平台安全的生命线商家端商户的运营指挥中心对于商家而言直播是他们最重要的营销工具和销售渠道之一因此我为他们设计的商家后台必须像一个作战指挥室专业高效功能完备申请与配置商家的直播之旅始于申请我需要为他们提供一个清晰的申请入口并明确告知他们需要满足的条件这既是功能也是一种规则的宣导当商家获得资格后他们就需要一个专业的直播间管理后台在这里他们可以创建编辑管理自己所有的直播场次我设计的核心思路是状态驱动你会发现一场直播在未开始直播中已结束等不同状态下商家可以进行的操作是完全不同的比如未开始的可以编辑而已结束的只能查看数据这种精细化的权限控制能有效防止商家的误操作数据复盘直播的魅力在于可以通过数据不断优化一场直播结束后商家最关心的问题就是这场直播效果怎么样如果我不能回答这个问题那么我设计的这个功能就是失败的因此我必须为商家提供一个详尽的数据战报这个战报至少要包含三类核心数据流量数据有多少人看最高同时有多少人在线涨了多少粉互动数据谁给我刷了礼物价值多少带货数据卖了什么商品卖了多少件只有提供了这些数据商家才能进行有效的复盘我们的直播功能才算真正为商家创造了价值用户端主播与观众的互动舞台用户端是整个直播产品的门面是所有用户能直接感知到的地方我把它分为两条主线来设计主播的开播之旅和观众的看播之旅主播的开播之旅我设计主播端的核心理念是简单高效所见即所得主播在手机方寸之间就要完成一场直播的全部准备工作第一步设置直播信息一场直播的门面就是封面和标题我必须让主播可以轻松地上传一张吸引人的封面图并起一个有噱头的标题此外立即开始和预定时间这两个选项也至关重要预定时间能让主播提前预告进行蓄水这是专业运营的必备功能第二步关联带货商品这是直播电商的灵魂我需要为主播提供一个极为便捷的选品流程让他们能从自己的店铺商品库中快速勾选出本场要带货的商品并添加到直播间的小黄车里第三步直播中的掌控当直播开始后主播的手机屏幕就变成了他的驾驶舱美颜滤镜镜头翻转这些是基础功能能让主播呈现出最好的状态更重要的是他需要有管理商品与观众互动等一系列工具观众的看播之旅我设计观众端的核心理念是沉浸体验无缝下单我要让用户看得开心买得顺滑核心互动界面用户进入直播间首先看到的是一个集视频画面和实时互动区于一体的界面下方的聊天弹幕区是营造社区感和热闹氛围的关键让用户感觉自己不是一个人在看而是在和成千上万的人一起云逛街商品浏览与购买当主播开始介绍商品时我必须为用户提供一个清晰无干扰的商品展示区这个区域通常在屏幕下方以列表形式呈现用户点击后无需跳出直播间就能查看商品详情并完成购买这里的设计要点在于商品状态的实时同步当主播讲解某个商品时它的状态可能是待上架当主播喊出上链接时它会立刻变为马上抢而当商品售罄时它又会变为已抢完这种实时的状态变化是制造稀缺感激发用户下单欲望的关键所在直播电商的关键技术在完成了产品的长相用户界面和骨架功能逻辑设计之后我必须和技术团队坐下来探讨它的内脏和血脉也就是实现这一切所需要的技术作为产品经理我不需要会写代码但我必须理解其核心原理这能让我评估技术方案的可行性预估开发成本并在关键的技术选型上与团队进行有质量的对话核心概念推流与拉流整个复杂的直播技术可以被简化为两个最核心的动作推流和拉流推流我把它理解为上传直播的过程它指的是主播的手机端直播端采集自己的声音和画面并将其像水流一样推送到云端服务器的行为拉流我把它理解为下载直播的过程它指的是成千上万的观众从云端服务器那里将直播内容拉取到自己手机上进行观看的行为一次流畅的直播体验本质上就是一次高质量的推和成千上万次高质量的拉所共同构成的直播的技术全景图在推与拉之间是一个庞大而精密的后台服务系统为了让团队清晰地理解这个系统我通常会展示这样一张技术架构图我可以带你走一遍这个流程主播端推送方一切的源头是主播我们会集成一个推流它就像一个专业的打包和邮寄工具负责将主播的音视频内容采集压缩然后通过推流节点发送到最近的云服务器服务端处理中心这是直播的中央厨房直播服务器接收到主播的推流后会立刻进行一系列的加工处理例如转码服务为了适配不同观众的网络状况服务器会将原始视频流实时转码成高清标清流畅等多个版本录制服务服务器会将整场直播录制成一个视频文件方便用户随时回顾截图服务自动截取直播的精彩瞬间作为封面安全服务对直播内容进行实时监控防止违规观众端拉取方经过处理的直播流会被分发到全球的分发节点这就像是遍布全球的前置仓库当观众打开时他们的播放会自动连接到离他们最近的节点去拉取直播内容这样无论用户身在何处都能获得低延迟高流畅的观看体验产品经理的技术选型自研第三方了解到这套系统的复杂性后一个关键的决策就摆在了我的面前这套系统我们是自己从零开始搭建还是直接采购成熟的方案我的答案以及我给几乎所有公司的建议都是果断选择第三方原因很简单作为一家电商公司我们的核心竞争力在于交易而非底层视频技术自研一套稳定高并发低延迟的全球直播系统其投入是天文数字聪明的产品决策是站在巨人的肩膀上市面上有非常多专业成熟的云服务商提供完整的视频直播解决方案我们只需要将他们的集成到我们的产品中就能在短时间内以可控的成本上线高质量的直播功能在做技术选型时我会和技术负责人一起重点考察几家头部厂商例如阿里云它的视频直播阿里云直播服务服务在国内市场份额巨大技术稳定文档齐全网易云信网易云信网易云信直播服务在社交娱乐领域的解决方案经验丰富尤其在即时通讯和音视频的结合上很有优势腾讯云腾讯云的互动直播解决方案腾讯云直播服务尤其强调互动连麦等场景非常适合需要强社交属性的直播玩法最终我们会根据他们的产品性能功能丰富度服务支持以及价格等多个维度综合评估选择最适合我们当前业务需求的合作伙伴",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-26 21:20:37",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E7%AB%A0%EF%BC%9A%E5%94%AE%E5%90%8E%E7%AE%A1%E7%90%86"><span class="toc-text">第七章：售后管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#7-1-%E5%94%AE%E5%90%8E%E5%9C%BA%E6%99%AF%E6%9E%84%E5%BB%BA"><span class="toc-text">7.1 售后场景构建</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%B8%8D%E5%90%8C%E8%A7%92%E8%89%B2%E7%9A%84%E5%85%B3%E6%B3%A8%E7%82%B9"><span class="toc-text">1. 不同角色的关注点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E6%A0%B8%E5%BF%83%E5%94%AE%E5%90%8E%E5%9C%BA%E6%99%AF%E6%8F%90%E7%82%BC"><span class="toc-text">2. 核心售后场景提炼</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%94%AE%E5%90%8E%E8%81%8C%E8%B4%A3%E4%B8%8E%E9%9C%80%E6%B1%82%E6%8F%90%E5%8F%96"><span class="toc-text">3. 售后职责与需求提取</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-2-%E5%94%AE%E5%90%8E%E6%B5%81%E7%A8%8B%E5%88%86%E6%9E%90"><span class="toc-text">7.2 售后流程分析</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E5%BE%85%E4%BB%98%E6%AC%BE%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95"><span class="toc-text">1. 场景一：待付款取消订单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E5%BE%85%E5%8F%91%E8%B4%A7%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95"><span class="toc-text">2. 场景二：待发货取消订单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9A%E5%BE%85%E6%94%B6%E8%B4%A7-%E5%B7%B2%E5%AE%8C%E6%88%90-%E9%80%80%E6%AC%BE%E9%80%80%E8%B4%A7"><span class="toc-text">3. 场景三：待收货/已完成 - 退款退货</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E5%9C%BA%E6%99%AF%E5%9B%9B%EF%BC%9A%E5%BE%85%E6%94%B6%E8%B4%A7-%E5%B7%B2%E5%AE%8C%E6%88%90-%E7%94%B3%E8%AF%B7%E6%8D%A2%E8%B4%A7"><span class="toc-text">4. 场景四：待收货/已完成 - 申请换货</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-%E5%95%86%E5%AE%B6%E7%AB%AF%E4%B8%8E%E5%B9%B3%E5%8F%B0%E7%AB%AF%E7%9A%84%E5%94%AE%E5%90%8E%E7%AE%A1%E7%90%86%E9%A1%B5%E9%9D%A2"><span class="toc-text">5.商家端与平台端的售后管理页面</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-3-%E4%BA%A4%E6%98%93%E7%BA%A0%E7%BA%B7%E5%A4%84%E7%90%86"><span class="toc-text">7.3 交易纠纷处理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%B9%B3%E5%8F%B0%E4%BB%8B%E5%85%A5%E7%9A%84%E5%8E%9F%E5%88%99%E4%B8%8E%E6%97%B6%E6%9C%BA"><span class="toc-text">1. 平台介入的原则与时机</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E4%BA%A4%E6%98%93%E7%BA%A0%E7%BA%B7%E5%A4%84%E7%90%86%E6%B5%81%E7%A8%8B%E4%B8%8E%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-text">2. 交易纠纷处理流程与功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E8%AF%81%E6%8D%AE%E9%93%BE%E6%9D%A1%E8%AE%BE%E8%AE%A1"><span class="toc-text">3. 证据链条设计</span></a></li></ol></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%85%AB%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0-%E7%A7%8D%E8%8D%89%E7%AE%A1%E7%90%86"><span class="toc-text">第八章：电商后台 - 种草管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#8-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">8.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-2-%E8%AF%9D%E9%A2%98%E5%88%86%E7%B1%BB%E4%B8%8E%E8%AF%9D%E9%A2%98%E7%AE%A1%E7%90%86"><span class="toc-text">8.2 话题分类与话题管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-1-%E8%AF%9D%E9%A2%98%E5%88%86%E7%B1%BB%E7%AE%A1%E7%90%86"><span class="toc-text">8.2.1 话题分类管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-2-%E8%AF%9D%E9%A2%98%E7%AE%A1%E7%90%86"><span class="toc-text">8.2.2 话题管理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-3-%E7%A7%8D%E8%8D%89%E5%86%85%E5%AE%B9%E4%B8%8E%E8%AF%84%E8%AE%BA%E7%AE%A1%E7%90%86"><span class="toc-text">8.3 种草内容与评论管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-1-%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8%E7%AD%96%E7%95%A5"><span class="toc-text">8.3.1 内容审核策略</span></a></li></ol></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B9%9D%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0-%E8%B4%A2%E5%8A%A1%E7%AE%A1%E7%90%86"><span class="toc-text">第九章：电商后台 - 财务管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#9-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">9.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#9-2-%E6%B8%85%E7%AE%97%E4%B8%8E%E7%BB%93%E7%AE%97"><span class="toc-text">9.2 清算与结算</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%E5%AE%9A%E4%B9%89%EF%BC%88%E6%B8%85%E7%AE%97-vs-%E7%BB%93%E7%AE%97%EF%BC%89"><span class="toc-text">9.2.1 核心概念定义（清算 vs 结算）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-2-%E8%B5%84%E9%87%91%E4%B8%8E%E4%BF%A1%E6%81%AF%E6%B5%81%E7%A8%8B%E5%88%86%E6%9E%90"><span class="toc-text">9.2.2 资金与信息流程分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-3-%E6%A0%B8%E5%BF%83%E5%90%88%E8%A7%84%E9%97%AE%E9%A2%98%EF%BC%9A%E2%80%9C%E4%BA%8C%E6%B8%85%E2%80%9D"><span class="toc-text">9.2.3 核心合规问题：“二清”</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-4-%E5%B9%B3%E5%8F%B0%E5%AF%B9%E8%B4%A6%E7%AE%A1%E7%90%86"><span class="toc-text">9.2.4 平台对账管理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#9-3-%E8%B4%A2%E5%8A%A1%E7%AE%A1%E7%90%86"><span class="toc-text">9.3 财务管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-1-%E5%95%86%E5%AE%B6%E8%B4%A6%E5%8D%95%E4%B8%8E%E6%8F%90%E7%8E%B0%E7%AE%A1%E7%90%86"><span class="toc-text">9.3.1 商家账单与提现管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%95%86%E5%AE%B6%E7%AB%AF%E8%AE%BE%E8%AE%A1"><span class="toc-text">1. 商家端设计</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%BE%E8%AE%A1"><span class="toc-text">2. 平台端设计</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-2-%E5%B9%B3%E5%8F%B0%E6%8A%BD%E4%BD%A3%E4%B8%8E%E8%AE%A1%E8%B4%B9%E8%A7%84%E5%88%99"><span class="toc-text">9.3.2 平台抽佣与计费规则</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-3-%E5%8F%91%E7%A5%A8%E7%AE%A1%E7%90%86"><span class="toc-text">9.3.3 发票管理</span></a></li></ol></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E7%AB%A0%EF%BC%9A%E5%88%86%E9%94%80%E7%94%B5%E5%95%86"><span class="toc-text">第十章：分销电商</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#10-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">10.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-2-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-text">10.2 分销电商项目背景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E5%81%9A%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-text">1. 为什么要做分销电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E7%9A%84%E6%A0%B8%E5%BF%83%E9%9C%80%E6%B1%82"><span class="toc-text">2. 分销电商的核心需求</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-1-%E4%BB%80%E4%B9%88%E6%98%AF%E5%88%86%E9%94%80%E7%94%B5%E5%95%86"><span class="toc-text">10.2.1 什么是分销电商</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-2-%E6%A0%B8%E5%BF%83%E8%A7%92%E8%89%B2%E5%AE%9A%E4%B9%89%EF%BC%88%E4%BE%9B%E8%B4%A7%E5%95%86%E3%80%81%E5%88%86%E9%94%80%E5%95%86%E3%80%81%E6%B6%88%E8%B4%B9%E8%80%85%EF%BC%89"><span class="toc-text">10.2.2 核心角色定义（供货商、分销商、消费者）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-3-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E7%9A%84%E4%BC%98%E5%8A%BF"><span class="toc-text">10.3 分销电商的优势</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-1-%E4%BD%8E%E6%88%90%E6%9C%AC%E5%BF%AB%E9%80%9F%E8%A3%82%E5%8F%98"><span class="toc-text">10.3.1 低成本快速裂变</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-2-%E5%BC%BA%E4%BF%A1%E4%BB%BB%E5%85%B3%E7%B3%BB%E8%BD%AC%E5%8C%96"><span class="toc-text">10.3.2 强信任关系转化</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-3-%E8%BD%BB%E8%B5%84%E4%BA%A7%E8%BF%90%E8%90%A5"><span class="toc-text">10.3.3 轻资产运营</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-4-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E6%90%AD%E5%BB%BA%E6%80%9D%E8%B7%AF"><span class="toc-text">10.4 分销电商搭建思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-1-%E5%88%86%E9%94%80%E5%91%98%E4%BD%93%E7%B3%BB%E8%AE%BE%E8%AE%A1"><span class="toc-text">10.4.1 分销员体系设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-2-%E5%95%86%E5%93%81%E4%B8%8E%E4%BE%9B%E5%BA%94%E9%93%BE%E7%AE%A1%E7%90%86"><span class="toc-text">10.4.2 商品与供应链管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-3-%E4%BD%A3%E9%87%91%E4%B8%8E%E7%BB%93%E7%AE%97%E4%BD%93%E7%B3%BB"><span class="toc-text">10.4.3 佣金与结算体系</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-5-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">10.5 分销电商产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-1-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-text">10.5.1 平台端核心功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-2-%E5%95%86%E5%AE%B6%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-text">10.5.2 商家端核心功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-3-%E5%88%86%E9%94%80%E5%95%86%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-text">10.5.3 分销商端核心功能</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-6-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">10.6 本章总结</span></a></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E4%B8%80%E7%AB%A0%EF%BC%9A%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86"><span class="toc-text">第十一章：直播电商</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#11-1-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-text">11.1 直播电商项目背景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-1-%E4%B8%BA%E4%BB%80%E4%B9%88%E9%9C%80%E8%A6%81%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-text">11.1.1 为什么需要直播电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-2-%E5%88%B0%E5%BA%95%E4%BB%80%E4%B9%88%E6%98%AF%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-text">11.1.2 到底什么是直播电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-3-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E4%B8%89%E7%A7%8D%E4%B8%BB%E6%B5%81%E6%A8%A1%E5%BC%8F"><span class="toc-text">11.1.3 直播电商的三种主流模式</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-2-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-text">11.2 直播电商的设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-1-%E6%A0%B8%E5%BF%83%E8%A7%92%E8%89%B2%E4%B8%8E%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">11.2.1 核心角色与需求分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-2-%E6%A0%B8%E5%BF%83%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B%E6%A2%B3%E7%90%86"><span class="toc-text">11.2.2 核心业务流程梳理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-3-%E6%95%B4%E4%BD%93%E5%8A%9F%E8%83%BD%E6%9E%B6%E6%9E%84%E8%A7%84%E5%88%92"><span class="toc-text">11.2.3 整体功能架构规划</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-3-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">11.3 直播电商的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-1-%E5%B9%B3%E5%8F%B0%E7%AB%AF%EF%BC%9A%E8%A7%84%E5%88%99%E7%9A%84%E5%88%B6%E5%AE%9A%E8%80%85%E4%B8%8E%E7%A7%A9%E5%BA%8F%E7%9A%84%E5%AE%88%E6%8A%A4%E8%80%85"><span class="toc-text">11.3.1 平台端：规则的制定者与秩序的守护者</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-2-%E5%95%86%E5%AE%B6%E7%AB%AF%EF%BC%9A%E5%95%86%E6%88%B7%E7%9A%84%E8%BF%90%E8%90%A5%E6%8C%87%E6%8C%A5%E4%B8%AD%E5%BF%83"><span class="toc-text">11.3.2 商家端：商户的运营指挥中心</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-3-%E7%94%A8%E6%88%B7%E7%AB%AF%EF%BC%9A%E4%B8%BB%E6%92%AD%E4%B8%8E%E8%A7%82%E4%BC%97%E7%9A%84%E4%BA%92%E5%8A%A8%E8%88%9E%E5%8F%B0"><span class="toc-text">11.3.3 用户端：主播与观众的互动舞台</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-4-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E5%85%B3%E9%94%AE%E6%8A%80%E6%9C%AF"><span class="toc-text">11.4 直播电商的关键技术</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%EF%BC%9A%E6%8E%A8%E6%B5%81%E4%B8%8E%E6%8B%89%E6%B5%81"><span class="toc-text">11.4.1 核心概念：推流与拉流</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-2-%E7%9B%B4%E6%92%AD%E7%9A%84%E6%8A%80%E6%9C%AF%E5%85%A8%E6%99%AF%E5%9B%BE"><span class="toc-text">11.4.2 直播的技术全景图</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-3-%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E7%9A%84%E6%8A%80%E6%9C%AF%E9%80%89%E5%9E%8B%EF%BC%9A%E8%87%AA%E7%A0%94-vs-%E7%AC%AC%E4%B8%89%E6%96%B9SDK"><span class="toc-text">11.4.3 产品经理的技术选型：自研 vs. 第三方SDK</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" itemprop="url">产品经理实战</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理实战</span></a></span></div></div><h1 class="post-title" itemprop="name headline">2️⃣ 电商实战（下）</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-21T07:09:48.000Z" title="发表于 2025-07-21 15:09:48">2025-07-21</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-26T13:20:37.069Z" title="更新于 2025-07-26 21:20:37">2025-07-26</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">15.2k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>44分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="2️⃣ 电商实战（下）"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/30992.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/30992.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" itemprop="url">产品经理实战</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" tabindex="-1" itemprop="url">产品经理实战</a><h1 id="CrawlerTitle" itemprop="name headline">2️⃣ 电商实战（下）</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-21T07:09:48.000Z" title="发表于 2025-07-21 15:09:48">2025-07-21</time><time itemprop="dateCreated datePublished" datetime="2025-07-26T13:20:37.069Z" title="更新于 2025-07-26 21:20:37">2025-07-26</time></header><div id="postchat_postcontent"><hr><h1 id="第七章：售后管理"><a href="#第七章：售后管理" class="headerlink" title="第七章：售后管理"></a>第七章：售后管理</h1><p>在我看来，用户下单付款，绝不意味着我们服务的结束，恰恰相反，它开启了我们与用户之间，一段更需要“信任”来维系的长期关系。</p><p>如何处理好用户在购后遇到的种种问题，如何设计一套公平、清晰、高效的售后流程，直接决定了我们平台的口碑和用户的复购率。</p><h2 id="7-1-售后场景构建"><a href="#7-1-售后场景构建" class="headerlink" title="7.1 售后场景构建"></a>7.1 售后场景构建</h2><p>在设计任何具体的售后功能之前，我的第一步，是<strong>构建出所有可能发生的售后场景</strong>。我需要绘制一张完整的“售后地图”，确保我的设计，能够覆盖所有可能的用户求助路径。</p><h3 id="1-不同角色的关注点"><a href="#1-不同角色的关注点" class="headerlink" title="1. 不同角色的关注点"></a>1. 不同角色的关注点</h3><p>“售后”这件事，从来不是一个单方的行为，它至少牵动着<strong>用户、商家、平台</strong>这三方的心。我构建场景，会先从理解这三方的不同“痛点”和“关注点”开始。</p><table><thead><tr><th align="left"><strong>角色</strong></th><th align="left"><strong>核心关注点</strong></th></tr></thead><tbody><tr><td align="left"><strong>用户</strong></td><td align="left">“我买的东西不想要了/有问题，怎么才能快速、方便地退/换？”</td></tr><tr><td align="left"><strong>商家</strong></td><td align="left">“用户退回来的货有没有问题？退换货的运费成本谁来承担？差评会不会影响我的店铺？”</td></tr><tr><td align="left"><strong>平台</strong></td><td align="left">“我们的售后规则是否公平？如何才能在保障用户体验和控制商家风险之间，找到平衡？处理这些纠纷需要多少客服人力？”</td></tr></tbody></table><h3 id="2-核心售后场景提炼"><a href="#2-核心售后场景提炼" class="headerlink" title="2. 核心售后场景提炼"></a>2. 核心售后场景提炼</h3><p>基于用户的核心关注点，我就可以提炼出，用户在订单生命周期的不同阶段，会发起的几大类核心售后场景。</p><p>这些场景，就是我们后续设计流程的“<strong>需求来源</strong>”。</p><table><thead><tr><th align="left"><strong>订单状态</strong></th><th align="left"><strong>用户可能发起的售后场景</strong></th></tr></thead><tbody><tr><td align="left"><strong>订单未付款</strong></td><td align="left"><code>取消订单</code></td></tr><tr><td align="left"><strong>订单已付款、待发货</strong></td><td align="left"><code>取消订单（申请仅退款）</code></td></tr><tr><td align="left"><strong>待收货 / 已完成</strong></td><td align="left"><code>申请退款退货</code>、 <code>申请换货</code>、<code>申请仅退款</code>（如：商品漏发）</td></tr><tr><td align="left"><strong>任意环节</strong></td><td align="left"><code>交易纠纷，申请平台介入</code></td></tr></tbody></table><h3 id="3-售后职责与需求提取"><a href="#3-售后职责与需求提取" class="headerlink" title="3. 售后职责与需求提取"></a>3. 售后职责与需求提取</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723135351237.png" alt="image-20250723135351237"></p><p>场景明确后，我需要为这些场景，定义清晰的“<strong>责任边界</strong>”和“<strong>业务规则</strong>”。这部分内容，将是我后续撰写PRD和设计流程的基础。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723135410717.png" alt="image-20250723135410717"></p><p>我的设计，将遵循以下的基本职责划分：</p><ul><li><p><strong>用户职责：发起者</strong><br><code>一切售后行为，由买家发起</code>。我的用户端产品设计，必须为用户，在订单详情页等位置，提供清晰、便捷的售后申请入口。</p></li><li><p><strong>商家职责：处理者</strong><br><code>商家收到售后申请进行响应</code>。商家是处理售后问题的第一责任人。我的商家后台设计，必须为商家提供处理这些申请的技术支持，但<strong>具体的处理结果（同意/拒绝），由商家自己决定</strong>。</p></li><li><p><strong>平台职责：保障者与仲裁者</strong><br><code>当用户与商家发生纠纷时，平台介入，保障双方权益</code>。我的平台运营后台设计，必须包含一套“<strong>纠纷仲裁</strong>”机制。在保障用户交易安全的前提下，对纠纷进行公平的判决。</p></li></ul><hr><h2 id="7-2-售后流程分析"><a href="#7-2-售后流程分析" class="headerlink" title="7.2 售后流程分析"></a>7.2 售后流程分析</h2><p>在我们构建了售后场景，明确了各方角色的职责之后，下一步，就是为每一个具体的场景，设计出<strong>清晰、严谨、可执行</strong>的业务流程。</p><p>我设计的售后流程，就像是一部“<strong>法律</strong>”。它需要清晰地定义出，在某种售后场景下，用户和商家，各自拥有什么<strong>权利</strong>，需要履行什么<strong>义务</strong>，以及系统应该如何根据他们的操作，来<strong>自动地流转订单的售后状态</strong>。</p><p>我们将逐一分析几种最高频的售后场景。</p><h3 id="1-场景一：待付款取消订单"><a href="#1-场景一：待付款取消订单" class="headerlink" title="1. 场景一：待付款取消订单"></a>1. 场景一：待付款取消订单</h3><ul><li><strong>核心逻辑</strong>：这是最简单的售后场景。<ul><li><strong>角色</strong>：完全由<strong>买家</strong>单方面发起和完成。</li><li><strong>流程</strong>：用户在“我的订单”中，找到“待付款”的订单，直接点击“取消订单”，订单状态即变为“已取消”。</li></ul></li><li><strong>我的设计思考</strong>：为什么这个流程，商家完全不参与？因为在用户付款之前，这笔订单，尚未进入商家的“待办事项（即待发货列表）”中，没有对商家产生任何实质性的履约成本。因此，我设计的流程，允许用户在这个阶段，自由地、无条件地取消订单。</li></ul><h3 id="2-场景二：待发货取消订单"><a href="#2-场景二：待发货取消订单" class="headerlink" title="2. 场景二：待发货取消订单"></a>2. 场景二：待发货取消订单</h3><ul><li><strong>核心逻辑</strong>：这个场景，开始涉及到买卖双方的交互。<ul><li><strong>角色</strong>：由<strong>买家</strong>发起申请，但需要<strong>商家</strong>进行审核。</li><li><strong>流程</strong>：买家在“待发货”订单中，发起“取消申请” -&gt; 商家在后台收到申请，进行审核 -&gt; 若商家同意，则订单取消，系统自动退款；若商家拒绝，则订单继续保持“待发货”状态。</li></ul></li><li><strong>我的设计思考</strong>：为什么商家需要审核？因为用户付款后，订单就已经进入了商家的履约流程。商家可能已经在“拣货”、“打包”，甚至已经交给了快递员但还未揽件。这个“审核”的环节，就是我留给商家的一个“<strong>拦截窗口</strong>”，让他去确认，这笔订单，是否还能从他的发货流程中，被成功地拦截下来。</li></ul><h3 id="3-场景三：待收货-已完成-退款退货"><a href="#3-场景三：待收货-已完成-退款退货" class="headerlink" title="3. 场景三：待收货/已完成 - 退款退货"></a>3. 场景三：待收货/已完成 - 退款退货</h3><p>这是最复杂的售后场景，因为它涉及到“<strong>逆向物流</strong>”（即用户把商品寄回给商家）。我必须为这个场景，设计一套严谨的、多步骤的“<strong>售后状态机</strong>”。</p><p><strong>我的流程与状态设计</strong>：</p><table><thead><tr><th align="left">售后状态</th><th align="left">触发动作</th><th align="left">下一步状态</th></tr></thead><tbody><tr><td align="left"><strong>（初始）</strong></td><td align="left"><strong>买家</strong>在用户端，对“待收货”或“已完成”的订单，发起退款退货申请，并填写理由。</td><td align="left"><code>待商家审核</code></td></tr><tr><td align="left"><code>待商家审核</code></td><td align="left"><strong>商家</strong>在后台，审核通过买家的申请。</td><td align="left"><code>待买家发货</code></td></tr><tr><td align="left"><code>待买家发货</code></td><td align="left"><strong>买家</strong>在用户端，填写退货的快递单号，或上门与自行寄回</td><td align="left"><code>商家待收货</code></td></tr><tr><td align="left"><code>商家待收货</code></td><td align="left"><strong>商家</strong>在后台，确认已收到买家寄回的商品，且商品完好无损。</td><td align="left"><code>退款中 / 退款成功</code></td></tr></tbody></table><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141252995.png" alt="image-20250723141252995"></p><h3 id="4-场景四：待收货-已完成-申请换货"><a href="#4-场景四：待收货-已完成-申请换货" class="headerlink" title="4. 场景四：待收货/已完成 - 申请换货"></a>4. 场景四：待收货/已完成 - 申请换货</h3><p>换货流程，因为涉及到“<strong>双向物流</strong>”（买家寄回 -&gt; 商家寄出），所以它的状态机，比退货更复杂。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141408765.png" alt="image-20250723141408765"></p><p><strong>我的流程与状态设计</strong>：<br>换货流程的前4步，与退款退货完全一致（从<code>待商家审核</code>到<code>商家待收货</code>）。在商家确认收到退货后，流程继续：</p><table><thead><tr><th align="left">售后状态</th><th align="left">触发动作</th><th align="left">下一步状态</th></tr></thead><tbody><tr><td align="left"><code>商家待收货</code></td><td align="left"><strong>商家</strong>在后台，确认收到退货后，将新的商品，重新发货给买家，并填写新的快递单号。</td><td align="left"><code>待买家收货</code></td></tr><tr><td align="left"><code>待买家收货</code></td><td align="left"><strong>买家</strong>在用户端，确认收到商家换发的商品。</td><td align="left"><strong>（换货流程结束）</strong></td></tr></tbody></table><p>通过为每一个售后场景，都设计这样一套清晰的流程和状态机，我就可以确保，我们平台、用户、商家三方，在处理复杂的售后问题时，都有据可依，有路可循。</p><h3 id="5-商家端与平台端的售后管理页面"><a href="#5-商家端与平台端的售后管理页面" class="headerlink" title="5.商家端与平台端的售后管理页面"></a>5.商家端与平台端的售后管理页面</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141535312.png" alt="image-20250723141535312"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141546753.png" alt="image-20250723141546753"></p><hr><h2 id="7-3-交易纠纷处理"><a href="#7-3-交易纠纷处理" class="headerlink" title="7.3 交易纠纷处理"></a>7.3 交易纠纷处理</h2><p>在我们设计的售后流程中，大部分的退款、退货申请，都可以由用户和商家，通过协商来顺利解决。</p><p>但是，我们必须考虑到一种情况：<strong>当用户和商家，无法达成一致时，应该怎么办？</strong><br>比如，用户申请退货的理由是“商品有质量问题”，并上传了图片；而商家则反驳，认为是用户“人为损坏”，并拒绝了退款申请。</p><p>此时，双方就陷入了“<strong>交易纠纷</strong>”。如果平台不介入，这个矛盾将永远无法解决，并会极大地损害用户对平台的信任。因此，我必须设计一套<strong>公平、公正、透明</strong>的<strong>平台介入仲裁机制</strong>。</p><h3 id="1-平台介入的原则与时机"><a href="#1-平台介入的原则与时机" class="headerlink" title="1. 平台介入的原则与时机"></a>1. 平台介入的原则与时机</h3><p>我设计这套仲裁机制，会遵循以下核心原则：</p><ul><li><strong>保障交易安全</strong>：我设计的平台规则，会<strong>优先保障用户的合法权益</strong>。</li><li><strong>明确介入时机</strong>：平台介入的“<strong>触发器</strong>”非常明确——<strong>在售后流程中，任何一方的合理请求，被另一方“拒绝”时</strong>，系统就应该为被拒绝的一方，提供“<strong>申请平台介入</strong>”的入口。</li><li><strong>依赖双方举证</strong>：平台作为“法官”，<strong>绝不偏听偏信</strong>。我的判决，必须建立在双方提供的“<strong>证据</strong>”之上。</li></ul><h3 id="2-交易纠纷处理流程与功能设计"><a href="#2-交易纠纷处理流程与功能设计" class="headerlink" title="2. 交易纠纷处理流程与功能设计"></a>2. 交易纠纷处理流程与功能设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142127598.png" alt="image-20250723142127598"></p><p>整个交易纠纷的处理，我将它设计为一个严谨的、多方参与的线上流程。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142252925.png" alt="image-20250723142252925"></p><ul><li><p><strong>用户端功能</strong><br>当用户的售后申请被商家拒绝后，我会在他的订单详情页，提供一个“<strong>申请平台介入</strong>”的按钮。点击后，会进入“<strong>举证页面</strong>”，用户可以在这里，上传他认为能支持自己诉求的文字、图片、视频等证据。</p></li><li><p><strong>商家端功能</strong><br>当用户申请平台介入后，这笔售后订单，在商家后台的状态，就会变为“<strong>待商家举证</strong>”。商家同样需要在这个订单的详情页，进入“<strong>举证页面</strong>”，上传对他有利的证据（如：发货前的商品完好视频、与用户的聊天记录等）。</p></li><li><p><strong>平台端功能</strong><br>这是我们内部客服和仲裁团队的“<strong>法庭</strong>”。</p><ol><li><p><strong>维权列表</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142331985.png" alt="image-20250723142331985"></p><p>所有用户申请介入的纠纷单，都会进入到这个独立的“<strong>维权列表</strong>”工作队列中，等待我们的客服“法官”来处理。</p></li><li><p><strong>维权详情页</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142348594.png" alt="image-20250723142348594"></p><p>这是“法官”的判案工作台。我设计的这个页面，会<strong>聚合</strong>这笔纠纷的所有信息：</p><ul><li>原始的订单信息。</li><li>完整的售后申请记录和双方的沟通日志。</li><li><strong>买家提供的证据</strong>。</li><li><strong>卖家提供的证据</strong>。</li></ul><p>在页面的最下方，我会为“法官”，提供最终的“<strong>判决</strong>”功能按钮，比如“<strong>支持买家</strong>”或“<strong>支持卖家</strong>”。</p></li></ol></li></ul><h3 id="3-证据链条设计"><a href="#3-证据链条设计" class="headerlink" title="3. 证据链条设计"></a>3. 证据链条设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142456946.png" alt="image-20250723142456946"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142519553.png" alt="image-20250723142519553"></p><p>“<strong>证据</strong>”，是我们整个仲裁流程的核心。因此，我设计的“举证页面”（无论是用户端还是商家端），都必须支持上传多种形式的证据。</p><table><thead><tr><th align="left"><strong>证据类型</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><strong>文字描述</strong></td><td align="left">供双方清晰地、有条理地，陈述事情的经过和自己的诉求。</td></tr><tr><td align="left"><strong>图片/视频证据</strong></td><td align="left">这是最直观的证据。如：商品损坏部位的照片、开箱视频、证明商品货不对板的截图等。</td></tr><tr><td align="left"><strong>凭证类文件</strong></td><td align="left">包括但不限于：与对方的<strong>聊天记录</strong>、<strong>发货/退货的快递底单</strong>、甚至是物流公司出具的“<strong>红章证明</strong>”等。</td></tr></tbody></table><p>通过这套严谨的“<strong>申请介入 -&gt; 双方举证 -&gt; 平台判决</strong>”的流程，我为我们的电商平台，建立起了一道能化解交易矛盾、保障用户和商家双方合法权益的“安全网”。</p><hr><h1 id="第八章：电商后台-种草管理"><a href="#第八章：电商后台-种草管理" class="headerlink" title="第八章：电商后台 - 种草管理"></a>第八章：电商后台 - 种草管理</h1><p>在第三章，我们为用户端，设计了“<strong>商品种草</strong>”这个核心的、内容驱动的社区化模块。用户可以在这里，发布自己的购物心得，并与其他用户进行互动。</p><p>现在，我们必须回到<strong>平台运营后台</strong>，来为这个模块，设计一套相应的<strong>管理系统</strong>。我们作为平台，至少需要解决三个核心问题：</p><ol><li>用户发布“种草”笔记时，可选的“<strong>话题分类</strong>”从哪里来？</li><li>笔记可以关联的“<strong>话题标签</strong>”又从哪里来？</li><li>用户发布的这些海量的UGC（用户生产内容），我们平台<strong>如何进行管理和审核</strong>？</li></ol><p>这一章，我们就来逐一设计解决这些问题的后台功能。</p><h2 id="8-1-学习目标"><a href="#8-1-学习目标" class="headerlink" title="8.1 学习目标"></a>8.1 学习目标</h2><p>在本节中，我的核心目标是，带大家掌握电商后台中，社区化模块的管理后台设计。我们将学习如何设计一套<strong>话题分类</strong>与<strong>话题</strong>的二级管理体系，并为运营同事设计高效的<strong>种草内容</strong>与<strong>评论</strong>的审核后台。</p><h2 id="8-2-话题分类与话题管理"><a href="#8-2-话题分类与话题管理" class="headerlink" title="8.2 话题分类与话题管理"></a>8.2 话题分类与话题管理</h2><p>为了让用户发布的“种草”笔记，能够被有组织、有结构地呈现，我必须在后台，预先定义好一套“<strong>分类</strong>”与“<strong>话题</strong>”的体系。</p><h3 id="8-2-1-话题分类管理"><a href="#8-2-1-话题分类管理" class="headerlink" title="8.2.1 话题分类管理"></a>8.2.1 话题分类管理</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723143340432.png" alt="image-20250723143340432"></p><p>“<strong>话题分类</strong>”，是我为“种草”社区，设定的最高层级的、类似“频道”的内容划分。比如：“服饰穿搭”、“数码评测”、“美妆心得”等。</p><p>我设计的“<strong>分类管理</strong>”后台，核心功能如下：</p><ul><li><strong>基础管理</strong>：运营人员可以对分类，进行<strong>新增、编辑、删除、查询</strong>。</li><li><strong>状态管理</strong>：每个分类都有“<strong>显示/隐藏</strong>”两种状态。运营可以将某个分类暂时“隐藏”，那么这个分类，就不会在用户端展示，用户发布时也无法选择。</li><li><strong>排序</strong>：运营可以通过调整一个“<strong>排序值</strong>”，来控制这些分类，在用户端的显示顺序。</li></ul><h3 id="8-2-2-话题管理"><a href="#8-2-2-话题管理" class="headerlink" title="8.2.2 话题管理"></a>8.2.2 话题管理</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723143405323.png" alt="image-20250723143405323"></p><p>“<strong>话题</strong>”，是隶属于某个“话题分类”之下，更具体、更聚焦的“标签”。比如，在“服饰穿搭”这个分类下，就可以有“#OOTD”、“#小个子穿搭”、“#夏日多巴胺”等多个热门话题。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723143428063.png" alt="image-20250723143428063"></p><p>我设计的“<strong>话题管理</strong>”后台，除了基础的增删改查和状态管理外，最核心的一个设计点是：</p><ul><li><strong>关联分类</strong>：在新增或编辑一个“话题”时，我必须让运营，可以从我们上一步创建好的“<strong>话题分类</strong>”列表中，选择一个，来<strong>与这个话题进行关联</strong>。</li></ul><p>这个“<strong>分类-话题</strong>”的二级结构，就构成了我们整个“种草”社区，内容组织的骨架。</p><h2 id="8-3-种草内容与评论管理"><a href="#8-3-种草内容与评论管理" class="headerlink" title="8.3 种草内容与评论管理"></a>8.3 种草内容与评论管理</h2><h3 id="8-3-1-内容审核策略"><a href="#8-3-1-内容审核策略" class="headerlink" title="8.3.1 内容审核策略"></a>8.3.1 内容审核策略</h3><p>对于UGC社区，如果每一条内容，都采用“<strong>先审后发</strong>”的模式，那审核的压力会极大，并且会严重影响用户的发布体验。</p><p>因此，我通常会采用“<strong>先发后审</strong>”的策略：</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723143508932.png" alt="image-20250723143508932"></p><ol><li>系统先通过我们设计的“<strong>敏感词</strong>”系统，进行一次自动过滤。</li><li>只要内容不包含敏感词，就<strong>允许它被正常发布</strong>，立刻对其他用户可见。</li><li>然后，这条内容，会进入我们后台的“<strong>人工审核</strong>”队列，由运营同事，在稍后进行二次审核，来处理那些“漏网之鱼”。</li></ol><hr><h1 id="第九章：电商后台-财务管理"><a href="#第九章：电商后台-财务管理" class="headerlink" title="第九章：电商后台 - 财务管理"></a>第九章：电商后台 - 财务管理</h1><p>欢迎来到第九章。在这一章，我们将探讨电商平台的心脏——资金的流动。我作为产品经理，虽然不需要成为财务专家，但我必须深刻理解电商交易中，资金清算与结算的基本逻辑和合规要求。</p><p>因为任何与“钱”相关的设计，都必须将<strong>安全、准确、合规</strong>这三个词，刻在我的脑海里。</p><h2 id="9-1-学习目标"><a href="#9-1-学习目标" class="headerlink" title="9.1 学习目标"></a>9.1 学习目标</h2><p>在本章中，我的核心目标是，带大家掌握电商后台财务管理的基础。我们将重点学习<strong>清算与结算</strong>的核心概念及业务流程，并深入理解其中至关重要的<strong>合规问题</strong>。</p><h2 id="9-2-清算与结算"><a href="#9-2-清算与结算" class="headerlink" title="9.2 清算与结算"></a>9.2 清算与结算</h2><p>当一个用户在我们的平台支付了100元，这100元是如何，从用户的口袋，安全、准确地，在扣除我们的平台佣金后，最终到达商家的口袋里的？</p><p>要回答这个问题，我们就必须先理解两个核心的金融概念：<strong>清算</strong>和<strong>结算</strong>。</p><h3 id="9-2-1-核心概念定义（清算-vs-结算）"><a href="#9-2-1-核心概念定义（清算-vs-结算）" class="headerlink" title="9.2.1 核心概念定义（清算 vs 结算）"></a>9.2.1 核心概念定义（清算 vs 结算）</h3><p>我用一个通俗的方式，来帮大家理解这两个概念：</p><table><thead><tr><th align="left"><strong>概念</strong></th><th align="left"><strong>核心产出</strong></th></tr></thead><tbody><tr><td align="left"><strong>清算</strong></td><td align="left">它的工作，是对某一个周期内（比如一天）发生的所有交易数据，进行汇总、分类、计算。最终准确地算出：“<strong>今天，我平台总共应该给A商家打多少钱，给B商家打多少钱。</strong>”</td></tr><tr><td align="left">**结算 **</td><td align="left">它的工作，是依据“清算”得出的结果，进行<strong>实际的资金划拨</strong>操作，把钱从一个账户，转移到另一个账户。</td></tr></tbody></table><p>简单来说，<strong>清算是“脑力劳动”，结算是“体力劳动”</strong>。先算清楚，再打款。</p><h3 id="9-2-2-资金与信息流程分析"><a href="#9-2-2-资金与信息流程分析" class="headerlink" title="9.2.2 资金与信息流程分析"></a>9.2.2 资金与信息流程分析</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723144613976.png" alt="image-20250723144613976"></p><p>在一个合规的电商平台中，清结算的过程，包含了“<strong>资金流</strong>”和“<strong>信息流</strong>”这两条并行的线。</p><ul><li><strong>信息流</strong>：我们平台的<strong>订单数据</strong>，会流入到我们后台的<strong>管理页面</strong>。我们的财务同事，会基于这些信息，来进行<strong>查询和对账</strong>。</li><li><strong>资金流</strong>：用户支付的钱，会先进入到一个<strong>第三方的“清结算机构”</strong>（比如支付宝、微信支付、银行）的<strong>资金账户</strong>中。这个机构，会根据我们平台提供的“清算”信息，进行“结算”，最终将钱，打到商家的<strong>结算账户</strong>中，商家再进行<strong>提现</strong>。</li></ul><h3 id="9-2-3-核心合规问题：“二清”"><a href="#9-2-3-核心合规问题：“二清”" class="headerlink" title="9.2.3 核心合规问题：“二清”"></a>9.2.3 核心合规问题：“二清”</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723144729774.png" alt="image-20250723144729774"></p><p>在理解了上述流程后，我们就必须来探讨电商支付领域，最重要、也是最危险的一条“<strong>红线</strong>”——“<strong>二清</strong>”。</p><ul><li><p><strong>什么是“二清”？</strong><br>“二清”，指的是“<strong>二次清算</strong>”。它是指，<strong>没有获得国家支付业务许可证的机构</strong>（比如我们这个电商平台自身），直接经手交易资金，并进行清分结算的行为。</p></li><li><p><strong>为什么违规？</strong><br>如果我们平台的业务流程是：<code>买家 -&gt; 平台公司账户 -&gt; 卖家</code>。这意味着，我们平台，在中间形成了一个汇集了所有交易资金的“<strong>资金池</strong>”。这种行为，是在行使“银行”或“支付机构”的职能，但我们并没有获得对应的金融牌照，这是<strong>严重违规</strong>的，会面临巨大的法律和政策风险。</p></li><li><p><strong>合规的流程是怎样的？</strong><br>合规的流程，必须是：<code>买家 -&gt; 持牌机构 -&gt; 卖家</code>。</p><p>“<strong>持牌机构</strong>”，就是指像<strong>支付宝、微信支付</strong>这样，拥有国家颁发的《支付业务许可证》的机构。在整个交易过程中，我们平台，<strong>只能处理“信息流”</strong>（即订单信息），<strong>绝对不能触碰“资金流”</strong>。我们只能向“持牌机构”，下达支付和结算的“指令”，而实际的资金划拨，必须由这些持牌机构来完成。</p></li></ul><h3 id="9-2-4-平台对账管理"><a href="#9-2-4-平台对账管理" class="headerlink" title="9.2.4 平台对账管理"></a>9.2.4 平台对账管理</h3><p>基于上述的合规流程，我设计的平台财务后台，一个最核心的功能，就是<strong>对账管理</strong>。</p><ul><li><strong>它的作用是</strong>：我们的系统，需要每天自动地，从我们合作的持牌支付机构（如支付宝、微信支付）那里，下载前一天的“<strong>交易账单</strong>”。</li><li>然后，系统需要将这份“<strong>外部账单</strong>”，与我们自己数据库里的“<strong>内部订单记录</strong>”，进行<strong>逐条比对</strong>。</li><li><strong>最终目的</strong>：是确保每一笔交易的金额、状态，内外部都是完全一致的，并自动地将差异和问题（比如“掉单”）标记出来，供我们的财务同事进行处理。</li></ul><hr><h2 id="9-3-财务管理"><a href="#9-3-财务管理" class="headerlink" title="9.3 财务管理"></a>9.3 财务管理</h2><p>在我们<code>9.2</code>节设计的“清结算”流程中，我们确保了交易的资金，都安全、合规地，进入到了由持牌支付机构监管的账户中。</p><p>现在，我们就需要设计一套功能，来处理这笔钱后续的分配和管理。<strong>财务管理</strong>后台，就是我们用来处理平台与商家之间“<strong>分钱</strong>”和“<strong>打钱</strong>”的系统。</p><h3 id="9-3-1-商家账单与提现管理"><a href="#9-3-1-商家账单与提现管理" class="headerlink" title="9.3.1 商家账单与提现管理"></a>9.3.1 商家账单与提现管理</h3><p>这个功能的设计，我需要同时考虑<strong>商家端</strong>和<strong>平台端</strong>两个方面，因为它是买卖双方之间的一个完整互动流程。</p><h4 id="1-商家端设计"><a href="#1-商家端设计" class="headerlink" title="1. 商家端设计"></a>1. 商家端设计</h4><p>我需要为商家，提供一套清晰、透明、便捷的“<strong>账房</strong>”工具。</p><ul><li><strong>商家查看结算记录</strong><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103418115.png" alt="image-20250724103418115"></li></ul><p>在我设计的商家后台中，会有一个“<strong>结算中心</strong>”。商家可以在这里，清晰地看到平台在每个结算周期（如每月），为他结算的<strong>总订单数</strong>、<strong>总结算金额</strong>，并能查询到构成这笔总额的<strong>每一笔订单明细</strong>，确保账目的清晰透明。</p><ul><li><p><strong>商家申请提现</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103501653.png" alt="image-20250724103501653"></p><p>当结算完成后，这笔钱就会进入商家的“<strong>可提现余额</strong>”。我会为商家设计一个“<strong>账户概况</strong>”页面，清晰地展示他的账户余额。并提供一个醒目的“<strong>申请提现</strong>”按钮。点击后，商家可以输入他希望提现的金额，并确认收款的银行账户信息。</p></li><li><p><strong>商家查看提现记录</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103538754.png" alt="image-20250724103538754"></p><p>提交申请后，商家可以在“<strong>提现记录</strong>”页面，实时地追踪这笔提现的状态，如<code>待审核</code>、<code>提现中</code>、<code>已到账</code>、<code>已驳回</code>等。</p></li></ul><h4 id="2-平台端设计"><a href="#2-平台端设计" class="headerlink" title="2. 平台端设计"></a>2. 平台端设计</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103610743.png" alt="image-20250724103610743"></p><p>商家的“提现申请”，会触发我们平台运营后台的一系列审核和操作流程。</p><ul><li><p><strong>平台审核提现申请</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103651498.png" alt="image-20250724103651498"></p><p>我需要为我们的财务同事，设计一个“<strong>提现审核</strong>”列表。所有商家的提现申请，都会进入这个工作队列。财务同事的核心操作，就是对申请进行“<strong>审核</strong>”。审核通过后，该笔申请的状态，就会流转为“<strong>待转账</strong>”。</p></li><li><p><strong>财务执行转账</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103718370.png" alt="image-20250724103718370"></p><p>进入“待转账”队列后，财务同事，会通过企业网银等方式，进行线下的实际打款。打款完成后，他会在后台，点击“<strong>确认转账</strong>”按钮，并填写相关的支付凭证信息。此时，这笔提现流程，才算最终完成，状态变为“<strong>已转账</strong>”。</p></li></ul><h3 id="9-3-2-平台抽佣与计费规则"><a href="#9-3-2-平台抽佣与计费规则" class="headerlink" title="9.3.2 平台抽佣与计费规则"></a>9.3.2 平台抽佣与计费规则</h3><p>在“清算”的过程中，一个核心的业务逻辑，就是计算我们平台的收入，即“<strong>平台抽佣</strong>”。</p><ul><li><strong>我的设计</strong>：我会在平台运营后台，设计一个“<strong>计费规则管理</strong>”模块。在这里，我的业务部门，可以为<strong>不同的商品类目，配置不同的交易佣金比例</strong>（比如：服装类目抽佣5%，数码类目抽佣3%）。</li><li><strong>系统应用</strong>：在我们<code>9.2</code>节的“清算”环节，系统就会自动地，根据这些预设好的规则，去计算每一笔订单我们平台应该抽取的佣金，然后再把剩下的金额，计入商家的“可结算金额”中。</li></ul><h3 id="9-3-3-发票管理"><a href="#9-3-3-发票管理" class="headerlink" title="9.3.3 发票管理"></a>9.3.3 发票管理</h3><p>一个完善的财务后台，还需要处理“<strong>发票</strong>”这个重要的业务。</p><ul><li><strong>我的设计</strong>：我需要设计两套发票流程。<ol><li><strong>商家向平台申请服务费发票</strong>：商家可以就支付给我们的“<strong>平台服务费</strong>”，向我们申请开具发票。</li><li><strong>用户向商家申请商品发票</strong>：用户可以就购买的“<strong>商品</strong>”，向商家申请开具发票。这个申请，会流转到<strong>商家后台</strong>，由商家进行处理。</li></ol></li></ul><hr><h1 id="第十章：分销电商"><a href="#第十章：分销电商" class="headerlink" title="第十章：分销电商"></a>第十章：分销电商</h1><p>欢迎来到第十章。在前面的章节中，我们已经完整地学习了，如何设计一个“人、货、场”模型下的平台型电商。现在，我们将探讨一种能为平台带来强大“<strong>裂变增长</strong>”能力的、建立在<strong>社交关系链</strong>之上的高级模式——<strong>分销电商</strong>。</p><h2 id="10-1-学习目标"><a href="#10-1-学习目标" class="headerlink" title="10.1 学习目标"></a>10.1 学习目标</h2><p>在本章中，我的核心目标是，带大家系统性地掌握分销电商的业务模式与产品设计。我们将从项目背景出发，理解分销电商的定义和核心角色，并最终学会如何为这个模式，设计其独特的产品功能。</p><h2 id="10-2-分销电商项目背景"><a href="#10-2-分销电商项目背景" class="headerlink" title="10.2 分销电商项目背景"></a>10.2 分销电商项目背景</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724104514357.png" alt="image-20250724104514357"></p><h3 id="1-为什么要做分销电商？"><a href="#1-为什么要做分销电商？" class="headerlink" title="1. 为什么要做分销电商？"></a>1. 为什么要做分销电商？</h3><p>我之所以要考虑在我们的电商产品中，融入分销模式，其核心的驱动力，是为了解决传统电商模式“<strong>获客成本越来越高</strong>”的瓶颈。</p><p>分销电商，本质上是一种**S2B2C (Supply chain to Business to Customer)**的模式。它通过一种“<strong>利益共享</strong>”的机制，将我们平台上的海量“<strong>C端用户</strong>”，转化为成千上万的“<strong>小B（分销商）</strong>”，让他们利用自己的私域流量和社交信任，去为我们获取更多的新用户。</p><h3 id="2-分销电商的核心需求"><a href="#2-分销电商的核心需求" class="headerlink" title="2. 分销电商的核心需求"></a>2. 分销电商的核心需求</h3><p>基于这个背景，我提炼出的、搭建分销系统的核心产品需求如下：</p><ol><li><strong>用户可以申请成为平台的分销商</strong>。</li><li><strong>商家有权利自定义自己店铺的商品，是否允许分销</strong>。</li><li><strong>分销商可以发展自己的下线</strong>，但为了确保业务合规，<strong>层级不能超过两级</strong>。</li></ol><h3 id="10-2-1-什么是分销电商"><a href="#10-2-1-什么是分销电商" class="headerlink" title="10.2.1 什么是分销电商"></a>10.2.1 什么是分销电商</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724113646666.png" alt="image-20250724113646666"></p><p>我给<strong>分销电商</strong>的定义是：<strong>一个通过设置“销售提成”作为激励，驱动平台用户（即分销商），利用其自有的“社交关系”进行商品分享和销售裂变，并最终达成“自购省钱，分享赚钱”目的的商业模式。</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724113704425.png" alt="image-20250724113704425"></p><p>正如案例所示，分销最常见的形态，就是用户将一个带有自己专属二维码或链接的商品海报，分享到微信群或朋友圈。当他的好友通过这个链接完成购买后，他就能获得平台支付的相应比例的佣金。</p><p>在这个模式中，我们平台，需要为分销商解决好除了“销售”以外的一切后顾之忧，即<strong>统一提供货源、仓储、配送和售后服务</strong>。</p><h3 id="10-2-2-核心角色定义（供货商、分销商、消费者）"><a href="#10-2-2-核心角色定义（供货商、分销商、消费者）" class="headerlink" title="10.2.2 核心角色定义（供货商、分销商、消费者）"></a>10.2.2 核心角色定义（供货商、分销商、消费者）</h3><p>我设计分销系统，需要清晰地定义出这个新生态中的三个核心角色：</p><table><thead><tr><th align="left"><strong>核心角色</strong></th><th align="left"><strong>我的定义与解读</strong></th></tr></thead><tbody><tr><td align="left"><strong>供货商 (Supplier)</strong></td><td align="left">这是“<strong>货</strong>”的来源。他们可以是<strong>我们平台自营</strong>的商品，也可以是我们平台上<strong>参与了分销活动的第三方商家</strong>。他们的核心诉求，是<strong>提升商品销量</strong>。</td></tr><tr><td align="left"><strong>分销商 (Distributor)</strong></td><td align="left">这是我们这个模式中，<strong>新增的核心角色</strong>。他们是平台的普通用户，在申请成为分销商后，就拥有了“<strong>带货</strong>”的资格。他们<strong>不拥有商品、不处理订单、不负责发货</strong>，他们唯一的工作，就是<strong>分享和推广</strong>。他们的核心诉-求，是<strong>赚取佣金</strong>。</td></tr><tr><td align="left"><strong>消费者 (Consumer)</strong></td><td align="left">这是最终完成购买的<strong>终端用户</strong>。他们通常是某个分销商的<strong>好友或粉丝</strong>。他们的购买决策，很大程度上是建立在对分销商的<strong>信任</strong>之上。</td></tr></tbody></table><hr><h2 id="10-3-分销电商的优势"><a href="#10-3-分销电商的优势" class="headerlink" title="10.3 分销电商的优势"></a>10.3 分销电商的优势</h2><p>我们已经清楚了分销电商的定义和核心角色。现在，我需要回答一个关键的商业问题：<strong>作为一个产品或业务的决策者，我为什么要选择分销这种模式？</strong></p><p>答案在于，一个设计良好的分销体系，能为我们带来传统电商模式，难以企及的三大核心优势。</p><h3 id="10-3-1-低成本快速裂变"><a href="#10-3-1-低成本快速裂变" class="headerlink" title="10.3.1 低成本快速裂变"></a>10.3.1 低成本快速裂变</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724132513350.png" alt="image-20250724132513350"></p><p>在我看来，分销模式最强大、最核心的优势，就是它解决了现代电商最头痛的问题——<strong>高昂的获客成本</strong>。</p><ul><li><strong>传统模式的困境</strong>：传统的电商平台，需要花费巨额的市场预算，去购买流量、投放广告，来吸引用户。</li><li><strong>分销模式的破局</strong>：分销模式，本质上是将我们的营销预算，<strong>从“购买流量”，变为了“奖励用户”</strong>。我不再花钱给广告平台，而是把这部分钱，以“<strong>销售佣金</strong>”的形式，直接分给了帮我们带来客户的分销商。</li></ul><p>这相当于，我们<strong>将每一个分销商，都发展成了我们“行走的广告牌”和“销售渠道”</strong>。他们利用自己的社交关系链，进行“一带十、十带百”的<strong>裂变式传播</strong>。正如云集的案例数据显示，其“<strong>单个用户维系成本</strong>”，显著低于阿里、京东等传统流量驱动的电商平台。这就是裂变带来的低成本优势。</p><h3 id="10-3-2-强信任关系转化"><a href="#10-3-2-强信任关系转化" class="headerlink" title="10.3.2 强信任关系转化"></a>10.3.2 强信任关系转化</h3><p>分销模式的第二个巨大优势，是它能带来<strong>极高的销售转化率</strong>和<strong>用户忠诚度</strong>。</p><ul><li><strong>传统模式的挑战</strong>：用户面对一个冰冷的平台推送的广告，内心天然是带有“防备”和“不信任”的。</li><li><strong>分销模式的破解</strong>：分销模式的传播，是建立在“<strong>社交信任</strong>”的基础之上的。<strong>朋友的推荐，远比平台的广告，更具说服力。</strong></li></ul><p>当一个消费者，看到他朋友圈里，一位他所信任的好友或KOL，在真实地分享一款产品的使用心得时，他的购买决策链路会变得极短。这种基于信任的转化，效果是惊人的。云集案例中提到的“<strong>复购率达到93.6%</strong>”，就是这种强信任关系，带来高用户粘性的最好证明。</p><h3 id="10-3-3-轻资产运营"><a href="#10-3-3-轻资产运营" class="headerlink" title="10.3.3 轻资产运营"></a>10.3.3 轻资产运营</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724132621058.png" alt="image-20250724132621058"></p><p>分销模式的第三个优势，是它为“<strong>分销商</strong>”这个角色，提供了一种<strong>极具吸引力的“轻资产”运营</strong>模式。</p><p>我把它总结为“<strong>你只管卖，其他都交给我</strong>”。</p><table><thead><tr><th align="left"><strong>电商环节</strong></th><th align="left"><strong>由谁负责？</strong></th><th align="left"><strong>对分销商意味着什么？</strong></th></tr></thead><tbody><tr><td align="left"><strong>供货/选品</strong></td><td align="left"><strong>平台/供货商</strong></td><td align="left">分销商<strong>无需</strong>自己找货源</td></tr><tr><td align="left"><strong>仓储/库存</strong></td><td align="left"><strong>平台/供货商</strong></td><td align="left">分销商<strong>无需</strong>自己租仓库、压库存</td></tr><tr><td align="left"><strong>发货/物流</strong></td><td align="left"><strong>平台/供货商</strong></td><td align="left">分销商<strong>无需</strong>自己打包、发快递</td></tr><tr><td align="left"><strong>售后服务</strong></td><td align="left"><strong>平台/供货商</strong></td><td align="left">分销商<strong>无需</strong>自己处理复杂的退换货问题</td></tr><tr><td align="left"><strong>推广/销售</strong></td><td align="left"><strong>分销商</strong></td><td align="left"><strong>分销商只需要专注于他最擅长、最核心的一件事：分享和推广。</strong></td></tr></tbody></table><p>正是这种“轻资产”的模式，极大地降低了个人成为“小老板”的门槛，使得我们的分销商队伍，可以像滚雪球一样，快速地发展和壮大。</p><hr><h2 id="10-4-分销电商搭建思路"><a href="#10-4-分销电商搭建思路" class="headerlink" title="10.4 分销电商搭建思路"></a>10.4 分销电商搭建思路</h2><p>我们已经理解了分销电商的“是什么”和“为什么”。现在，我们就进入最核心的“<strong>怎么做</strong>”的环节。</p><p>要搭建一套完整的分销电商体系，我作为产品经理，需要从顶层，设计好<strong>三大核心支柱</strong>：</p><p><strong>分销员体系</strong>、<strong>商品与供应链体系</strong>、以及<strong>佣金与结算体系</strong>。这三大支柱，共同构成了我们分销业务的“骨架”。</p><h3 id="10-4-1-分销员体系设计"><a href="#10-4-1-分销员体系设计" class="headerlink" title="10.4.1 分销员体系设计"></a>10.4.1 分销员体系设计</h3><p>分销业务，核心是“<strong>人</strong>”的生意。因此，我首先要设计好，我们“<strong>分销员</strong>”这个核心角色的完整生命周期和组织结构。</p><p><strong>1. 角色与层级</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133059672.png" alt="image-20250724133059672"></p><p>为了激励分销员，为平台带来更多的“下线”分销员，我设计的体系，通常会包含“<strong>分销层级</strong>”。</p><ul><li><strong>核心逻辑</strong>：一个高级别的“一级分销商”，可以邀请新人，成为他的“二级分销商”。当“二级分销商”卖出商品时，“一级分销商”也能获得一部分的“团队奖励”。</li><li><strong>我的合规设计要点</strong>：我必须强调，为了确保业务的合法合规，在国内设计分销体系时，<strong>计佣（计算佣金）的层级，绝对不能超过三级</strong>。这是一个不可逾越的红线。</li></ul><p><strong>2. 核心流程</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133213227.png" alt="image-20250724133213227"></p><p>我设计的整个分销员体系，必须能够支撑起以下几个核心的业务流程：</p><ul><li><strong>成为分销商</strong>：普通用户，可以通过一个申请入口，提交申请，由平台审核通过后，获得“分销商”身份。</li><li><strong>分享商品</strong>：分销商可以在App内，选择商品，生成带有自己专属“推广码”的海报或链接，并分享出去。</li><li><strong>发展下线</strong>：分销商可以生成自己专属的“邀请码”，邀请好友来注册，成为自己的“下线”分销商。</li><li><strong>购买转化</strong>：当一个普通消费者，通过分销商分享的链接完成购买后，系统需要准确地记录下这笔订单的归属。</li></ul><h3 id="10-4-2-商品与供应链管理"><a href="#10-4-2-商品与供应链管理" class="headerlink" title="10.4.2 商品与供应链管理"></a>10.4.2 商品与供应链管理</h3><p>分销员只负责“推广”，而不负责“货”。因此，我必须在后台，设计好“<strong>货</strong>”的管理逻辑。</p><ul><li><strong>平台侧</strong>：在平台运营后台，我需要设计一个“<strong>总开关</strong>”，可以一键启用或关闭整个平台的分销功能。</li><li><strong>商家侧</strong>：在商家后台，我需要为商家，提供<strong>两级控制权</strong>：<ol><li><strong>店铺级开关</strong>：商家可以决定，自己整个店铺，是否参与平台的分销活动。</li><li><strong>商品级开关</strong>：在参与活动的前提下，商家还可以进一步地，去勾选“<strong>指定</strong>”的某些商品，来参与分销。</li></ol></li></ul><h3 id="10-4-3-佣金与结算体系"><a href="#10-4-3-佣金与结算体系" class="headerlink" title="10.4.3 佣金与结算体系"></a>10.4.3 佣金与结算体系</h3><p>这是驱动整个分销体系运转的“<strong>发动机</strong>”。我设计的佣金结算体系，必须<strong>公平、透明、准确</strong>。</p><ul><li><strong>佣金规则配置</strong>：我需要在平台运营后台，设计一个强大的“<strong>佣金规则引擎</strong>”。它需要支持运营同事，可以灵活地，按不同维度，来设置佣金比例。<ul><li><strong>按商品设置</strong>：不同的商品，可以有不同的佣-金比例。</li><li><strong>按分销商等级设置</strong>：高级别的分销商，可以享受更高的佣金比例。</li><li><strong>团队奖励设置</strong>：可以设置当下线分销商出单时，其上级可以获得的奖励比例。</li></ul></li><li><strong>结算与提现</strong>：当一笔通过分销链接产生的订单，<strong>完成交易</strong>（即，已过售后维权期）后，系统需要<strong>自动地</strong>，将计算好的佣金，打入对应分销商的“<strong>佣金账户</strong>”中。同时，我需要在分销商的专属后台，为他设计清晰的“<strong>收益报表</strong>”和便捷的“<strong>佣金提现</strong>”功能。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133510027.png" alt="image-20250724133510027"></p><p>综上所述，我搭建分销电商的整体思路，就是围绕“<strong>人（分销员体系）</strong>”、“<strong>货（商品管理）</strong>”、“<strong>钱（佣金体系）</strong>”这三大核心，分别为<strong>用户端、商家端、平台端</strong>，设计出支撑其运转所必需的功能。</p><hr><h2 id="10-5-分销电商产品设计"><a href="#10-5-分销电商产品设计" class="headerlink" title="10.5 分销电商产品设计"></a>10.5 分销电商产品设计</h2><p>在我们明确了分销电商的搭建思路之后，现在，我们就进入具体的<strong>产品功能设计</strong>环节。我将严格按照<strong>平台端、商家端、分销商端</strong>这三个不同的使用者视角，来分别进行功能设计的拆解。</p><h3 id="10-5-1-平台端核心功能"><a href="#10-5-1-平台端核心功能" class="headerlink" title="10.5.1 平台端核心功能"></a>10.5.1 平台端核心功能</h3><p>这是整个分销系统的“<strong>总控制器</strong>”，由我们平台的运营人员使用，用来设定整个分销业务的“游戏规则”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140309332.png" alt="image-20250724140309332"></p><ul><li><strong>分销规则配置</strong>：我设计的后台，必须有一个全局的“<strong>分销设置</strong>”页面。在这里，运营可以设置<code>是否开启分销</code>、<code>是否开启自购分佣</code>、<code>分销层级</code>（最多支持几级）、以及每一级的<code>抽佣比例</code>。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140324144.png" alt="image-20250724140324144"></p><ul><li><strong>分销员等级管理</strong>：为了激励分销商，我还会设计一个“<strong>分销等级</strong>”管理后台。运营可以在这里，创建不同的分销商等级（如：初级、中级、高级），并为每个等级，配置不同的<strong>邀请奖励</strong>和<strong>销售抽成</strong>比例，以及对应的<strong>升级规则</strong>。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140344850.png" alt="image-20250724140344850"></p><ul><li><strong>分销员审核管理</strong>：当有普通用户，申请成为分销商时，他们的申请会进入到这个后台的“<strong>待审核</strong>”列表中。运营人员可以在这里，查看申请人的信息，并进行“<strong>通过</strong>”或“<strong>驳回</strong>”的操作。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140428712.png" alt="image-20250724140428712"></p><ul><li><strong>订单与结算管理</strong>：我需要设计一个“<strong>分销订单</strong>”列表，让运营和财务，可以清晰地看到每一笔通过分销产生的订单，以及这笔订单需要为哪几级的分销商，分别计算多少佣金。同时，还需要“<strong>结算设置</strong>”和“<strong>提现管理</strong>”功能，来处理佣金的发放。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140527700.png" alt="image-20250724140527700"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724141040118.png" alt="image-20250724141040118"></p><hr><h3 id="10-5-2-商家端核心功能"><a href="#10-5-2-商家端核心功能" class="headerlink" title="10.5.2 商家端核心功能"></a>10.5.2 商家端核心功能</h3><p>这是我们设计给“<strong>供货商</strong>”（即参与分销的商家）使用的后台，核心是让他们能够<strong>对自己店铺的分销业务，进行自主管理</strong>。</p><ul><li><strong>分销商品管理</strong>：在商家后台的“<strong>商品管理</strong>”模块，我需要为商家提供一个“<strong>分销商品设置</strong>”的功能。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140703094.png" alt="image-20250724140703094"><br>在这里，商家可以<strong>勾选</strong>自己店铺中，愿意拿出利润来进行分销的商品。并且，可以为这些商品，<strong>设定一个基础的佣金比例</strong>。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140729521.png" alt="image-20250724140729521"></p><ul><li><strong>分销业绩查看</strong>：我还需要为商家，提供一个查看<strong>分销业绩</strong>的报表。在这里，他可以看到是<strong>哪些分销商</strong>，为他带来了<strong>哪些订单</strong>，让他可以直观地感受到分销为店铺带来的价值。</li></ul><h3 id="10-5-3-分销商端核心功能"><a href="#10-5-3-分销商端核心功能" class="headerlink" title="10.5.3 分销商端核心功能"></a>10.5.3 分销商端核心功能</h3><p>这是我们设计给“<strong>分销商</strong>”本人使用的“<strong>个人工作台</strong>”，它通常会内嵌在我们用户端App的“个人中心”里。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140758440.png" alt="image-20250724140758440"></p><ul><li><strong>申请成为分销商</strong>：首先，我需要在用户端的“个人中心”等位置，为普通用户，提供一个清晰的“<strong>申请成为分销商</strong>”的入口。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140808421.png" alt="image-20250724140808421"></p><ul><li><strong>选品中心与推广</strong>：当用户成为分销商后，他的个人中心，就会出现“<strong>分销中心</strong>”的模块。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140842614.png" alt="image-20250724140842614"><br>在分销中心里，他可以浏览所有可供分销的商品。在商品详情页上，会有专属于他的“<strong>自购省钱</strong>”和“<strong>分享赚钱</strong>”按钮。点击“分享赚钱”，系统会自动为他生成带有<strong>专属推广二维码</strong>的精美海报。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140918643.png" alt="image-20250724140918643"></p><ul><li><strong>收益与提现</strong>：这是分销商最关心的模块。我设计的这个页面，必须清晰地展示他的<code>今日收益</code>、<code>累计收益</code>等核心数据，并提供一个醒目的“<strong>提现</strong>”入口。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140943661.png" alt="image-20250724140943661"></p><ul><li><strong>团队管理</strong>：为了鼓励“裂变”，我还需要为分销商，设计一个简单的“<strong>我的团队</strong>”管理功能。在这里，他可以获取专属的<strong>邀请链接/海报</strong>，用于发展自己的下线团队，并查看团队的业绩概况。</li></ul><h2 id="10-6-本章总结"><a href="#10-6-本章总结" class="headerlink" title="10.6 本章总结"></a>10.6 本章总结</h2><p>在本章，我们系统性地学习了“<strong>分销电商</strong>”这种独特的商业模式。</p><ul><li><strong>背景与优势</strong>：我们理解了它通过<strong>社交裂变</strong>，来<strong>降低获客成本</strong>、提升<strong>转化率</strong>的核心价值。</li><li><strong>搭建思路</strong>：我们明确了搭建分销体系，需要从<strong>分销员、商品、佣金</strong>这三大支柱入手。</li><li><strong>产品设计</strong>：我们分别为<strong>平台、商家、分销商</strong>这三方，设计了支撑其业务运转所必需的核心功能。</li></ul><hr><h1 id="第十一章：直播电商"><a href="#第十一章：直播电商" class="headerlink" title="第十一章：直播电商"></a>第十一章：直播电商</h1><p>欢迎来到第十一章。在过去的学习中，我们已经掌握了平台电商的稳固根基和分销电商的裂变增长。现在，我将带您进入一个能将“<strong>购物体验</strong>”和“<strong>销售转化</strong>”推向极致的全新领域——<strong>直播电商</strong>。这是一种将“<strong>实时互动</strong>”与“<strong>商品销售</strong>”无缝融合的、极具沉浸感的商业模式。</p><hr><h2 id="11-1-直播电商项目背景"><a href="#11-1-直播电商项目背景" class="headerlink" title="11.1 直播电商项目背景"></a>11.1 直播电商项目背景</h2><p>在我负责的产品中，每当要引入一个像“直播”这样重大的新功能时，我都会先回归到最根本的商业问题上：我们现有的模式遇到了什么瓶颈？而这个新功能，是否能成为破局的关键？</p><h3 id="11-1-1-为什么需要直播电商？"><a href="#11-1-1-为什么需要直播电商？" class="headerlink" title="11.1.1 为什么需要直播电商？"></a>11.1.1 为什么需要直播电商？</h3><p>传统的货架式电商，本质是“人找货”，用户带着目的来搜索、比价。这种模式在今天面临着越来越大的挑战：流量越来越贵，用户的注意力越来越分散，单纯的打折促销也越来越难以打动他们。</p><p>我发现，直播电商恰好能从三个方面，完美地破解这些困局。</p><ol><li><strong>从“花钱买流量”到“内容吸流量”</strong>：传统电商需要不断地投入巨额广告费，去购买流量。而直播电商，特别是与KOL（关键意见领袖）的合作，是利用主播自带的影响力和内容创作能力，将他的粉丝高效地吸引到我们的平台上来。这是一种更聪明、更具性价比的获客方式。</li><li><strong>从“理性对比”到“感性促单”</strong>：在传统电商的图文页，用户的决策链路相对较长，消费也更趋于理性。但在直播间里，主播通过现场试用、实时互动和限时限量的话术，能够营造出一种“不买就亏了”的紧迫感和热烈氛围，这极大地激发了用户的感性消费和冲动购买，转化率自然远超平时。</li><li><strong>从“静态浏览”到“沉浸互动”</strong>：图文详情页是静态的、单向的。而直播，是一种“所见即所得”的沉浸式体验。我可以实时看到衣服的上身效果，可以要求主播展示产品的某个细节，可以通过弹幕与成千上万的人交流。这种丰富、立体的购物体验，是传统电商无法比拟的。</li></ol><h3 id="11-1-2-到底什么是直播电商？"><a href="#11-1-2-到底什么是直播电商？" class="headerlink" title="11.1.2 到底什么是直播电商？"></a>11.1.2 到底什么是直播电商？</h3><p>所以，到底什么是直播电商？</p><p>在我看来，直播电商的核心，是<strong>商业模式从“以货为中心”向“以人为中心”的彻底转变</strong>。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724194744839.png" alt="image-20250724194744839"></p><p>它不再是冰冷的货架，而是基于一个活生生的、你所信任或喜爱的主播，来建立交易。消费者购买的，不仅仅是商品本身，更是对这个主播的品味、专业度或个人魅力的“信任票”。这种以信任为前提的商业模式，其根基依然是电商，但能量却被放大了无数倍。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724194842043.png" alt="image-20250724194842043"></p><p>我们想象一个真实的场景：当主播在镜头前，一边讲解着手机的各项参数，一边实时回答着“待机时间多久？”、“拍照效果怎么样？”这些弹幕提问，并在几万人的共同见证下，喊出“3、2、1，上链接！”时，那一刻，它已经超越了单纯的“卖货”，变成了一场极具参与感的线上狂欢。这就是直播电商的魅力。</p><h3 id="11-1-3-直播电商的三种主流模式"><a href="#11-1-3-直播电商的三种主流模式" class="headerlink" title="11.1.3 直播电商的三种主流模式"></a>11.1.3 直播电商的三种主流模式</h3><p>理解了直播电商的价值和内核后，作为产品经理，我的下一步就是从顶层设计上，思考我们平台到底要做哪一种。在我的实践中，通常会遇到三种主流的业务模式。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195004429.png" alt="image-20250724195004429"></p><ol><li><p><strong>KOL带货模式</strong><br>这是最典型、爆发力最强的一种。如果我的业务目标是在短期内快速提升品牌知名度、引爆一款单品的销量，那么与外部的头部KOL合作，无疑是最佳选择。他们带来海量粉丝，我们提供优质商品，这是一场强强联合。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195028134.png" alt="image-20250724195028134"></p></li><li><p><strong>店铺直播模式（店播）</strong><br>这是一种更着眼于长期、健康的模式。我把它看作是平台必须为商家提供的“基础设施”。我们赋能平台上的商家，让他们可以在自己的“一亩三分地”里，由老板或者店员自己出镜，进行常态化的直播。这不追求一夜爆火，而是为了帮助商家更好地维护自己的老客、沉淀私域流量，是一种细水长流的生意。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195103702.png" alt="image-20250724195103702"></p></li><li><p><strong>直播分销模式</strong><br>这是一种最大化利用平台生态的、极具想象力的模式。它将直播和分销结合，允许我们的普通用户申请成为“分销主播”。平台提供统一的货盘，他们只需要开播去推广，就能赚取佣金。这相当于将我们平台上成千上万的用户，都变成了我们“行走的、会说话的”销售渠道。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195113471.png" alt="image-20250724195113471"></p></li></ol><hr><h2 id="11-2-直播电商的设计思路"><a href="#11-2-直播电商的设计思路" class="headerlink" title="11.2 直播电商的设计思路"></a>11.2 直播电商的设计思路</h2><p>在上一节，我们明确了“为什么”要做直播电商。现在，我的角色就要从一个业务分析师，切换到一个产品架构师。在真正开始画原型、写文档之前，我必须先搭建起整个产品的“骨架”。这个过程，我称之为“设计思路”的梳理。</p><h3 id="11-2-1-核心角色与需求分析"><a href="#11-2-1-核心角色与需求分析" class="headerlink" title="11.2.1 核心角色与需求分析"></a>11.2.1 核心角色与需求分析</h3><p>要设计一个好的系统，我首先要清晰地定义出：<strong>这个系统里，都有谁？他们分别想做什么？</strong> 这就是角色与需求分析。在直播电商这个场景里，我识别出了四个核心角色。</p><ol><li><strong>普通用户</strong>：他们是观众，是消费者。他们的核心诉求是“逛得开心，买得方便”。</li><li><strong>店铺主播</strong>：他们是表演者，是销售员。他们是直播间的灵魂，核心诉求是“互动热烈，卖得更多”。</li><li><strong>店铺运营</strong>：他们是幕后管理者。他们负责申请开通直播、管理直播计划、处理订单等。核心诉求是“管理高效，掌控全局”。</li><li><strong>平台</strong>：这就是我们自己。我们的核心诉求是“秩序井然，生态繁荣”，需要有最高的管理权限。</li></ol><p>为了确保不遗漏任何关键功能，我会将这些角色的核心需求，整理成一张清晰的列表，作为我们后续产品设计的“需求清单”。</p><table><thead><tr><th align="left"><strong>角色</strong></th><th align="left"><strong>我的解读（核心需求点）</strong></th></tr></thead><tbody><tr><td align="left"><strong>普通用户</strong></td><td align="left">1. 能流畅地观看直播，并与主播进行实时互动（如发弹幕、点赞）。<br>2. 能在直播间里，方便地查看正在讲解的商品，并快速下单购买。</td></tr><tr><td align="left"><strong>店铺运营</strong></td><td align="left">1. 需要有一个后台，可以向平台方，提交开通“店铺直播”功能的申请。<br>2. 对于已经创建或正在直播的场次，需要有管理和控制的能力。</td></tr><tr><td align="left"><strong>店铺主播</strong></td><td align="left">1. 能够在App内，轻松地发起一场直播，并能便捷地将自己店铺的商品，上架到直播间进行讲解。<br>2. 在直播过程中，能看到观众的互动，并进行回应，以提升直播间热度。</td></tr><tr><td align="left"><strong>平台</strong></td><td align="left">作为系统的所有者，我们需要有能力对所有店铺的直播间，进行统一的管理和监控，确保合规。</td></tr></tbody></table><h3 id="11-2-2-核心业务流程梳理"><a href="#11-2-2-核心业务流程梳理" class="headerlink" title="11.2.2 核心业务流程梳理"></a>11.2.2 核心业务流程梳理</h3><p>当我把这些零散的需求点都定义清楚后，下一步，就是用一条“流程线”，将它们串联起来，形成一个完整的业务闭环。我需要确保不同角色之间的协作是顺畅的。</p><p>我通常会用一张“泳道图”来可视化这个核心流程，让团队里的每一个人都能清晰地看到，自己负责的部分，在整个业务链条中所处的位置。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195621927.png" alt="image-20250724195621927"></p><p>这个流程是这样运转的：</p><ol><li>一切的起点，是“<strong>店铺运营</strong>”向“<strong>平台</strong>”提交了开通直播的申请。</li><li>“<strong>平台</strong>”审核通过后，该店铺就获得了直播的能力。</li><li>“<strong>店铺主播</strong>”现在可以正式“<strong>发起直播</strong>”，并将准备好的“<strong>上架商品</strong>”。</li><li>海量的“<strong>普通用户</strong>”被吸引进入直播间“<strong>观看直播</strong>”，并在主播的带动下完成“<strong>下单</strong>”。</li><li>最后，订单流转到“<strong>店铺运营</strong>”那里，由他们进行“<strong>确认订单</strong>”和后续的履约发货。</li></ol><p>你看，通过这样一张流程图，一个完整的、多角色协作的业务故事，就被清晰地呈现了出来。</p><h3 id="11-2-3-整体功能架构规划"><a href="#11-2-3-整体功能架构规划" class="headerlink" title="11.2.3 整体功能架构规划"></a>11.2.3 整体功能架构规划</h3><p>有了角色和流程，我就可以在脑海中，勾勒出整个产品的“功能架构蓝图”了。</p><p>我会把需要开发的功能，按照使用者的不同，划分到不同的“端”里去。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195732918.png" alt="image-20250724195732918"></p><p>我将整个直播电商系统，规划为三大功能模块：</p><ul><li><strong>用户端</strong>：这是我们产品的主阵地，承载了最多的功能。它既包含了“<strong>普通用户</strong>”的观看、互动、购买功能，也包含了“<strong>主播</strong>”开播、管理商品等核心功能。<em>（在这里，我暂时将主播端和用户端合并在一起考虑，因为它们都发生在同一个App内，很多界面是共通的）</em>。</li><li><strong>商家端</strong>：这就是我为“<strong>店铺运营</strong>”人员，所设计的后台管理系统。他们在这里申请权限、管理直播间。</li><li><strong>平台端</strong>：这是我们自己使用的“<strong>上帝后台</strong>”。在这里，我们可以管理所有商家和直播间，设定平台的规则。</li></ul><p>至此，直播电商的设计思路就已经非常清晰了。我们明确了“<strong>为谁设计</strong>”（核心角色）、“<strong>设计什么</strong>”（需求列表）、以及“<strong>它们如何协同工作</strong>”（业务流程和功能架构）。这个清晰的骨架，将是我们下一节进行具体产品功能设计的坚实基础。</p><hr><h2 id="11-3-直播电商的产品设计"><a href="#11-3-直播电商的产品设计" class="headerlink" title="11.3 直播电商的产品设计"></a>11.3 直播电商的产品设计</h2><p>在我们梳理清楚了设计思路、明确了“要做什么”之后，现在，就到了将蓝图转化为具体页面的阶段。作为产品经理，我会兵分三路，同时推进<strong>平台端、商家端、用户端</strong>这三个关键阵地的产品设计。</p><h3 id="11-3-1-平台端：规则的制定者与秩序的守护者"><a href="#11-3-1-平台端：规则的制定者与秩序的守护者" class="headerlink" title="11.3.1 平台端：规则的制定者与秩序的守护者"></a>11.3.1 平台端：规则的制定者与秩序的守护者</h3><p>我设计平台后台的唯一原则，就是“<strong>权责对等</strong>”。平台作为整个直播生态的“所有者”，必须拥有至高无上的管理权限，来确保整个业务健康、有序地运转。这主要体现在两个方面：<strong>管店铺</strong>和<strong>管直播</strong>。</p><p><strong>1. 直播店铺管理</strong></p><p>我们必须有一个“准入机制”。并非所有商家都有资格开通直播，否则劣质的直播内容会摧毁用户体验。因此，我需要为平台的运营同事，设计一个强大的店铺审核后台。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095520168.png" alt="image-20250725095520168"></p><p>这个后台的核心，就是对“<strong>资格状态</strong>”的精细化管理。运营人员在这里，可以清晰地看到所有申请店铺的列表，并进行“<strong>审核</strong>”、“<strong>查看</strong>”、“<strong>取消资格</strong>”或“<strong>恢复资格</strong>”等操作。每一个按钮，都代表了平台的一种管理权力，是确保直播商家质量的第一道防线。</p><p><strong>2. 直播间管理</strong></p><p>除了管“人”（店铺），我们更要管“事”（直播）。平台需要能够监控到所有正在发生和已经发生的直播。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095618366.png" alt="image-20250725095618366"></p><p>在这个界面，我最看重的，是“<strong>操作</strong>”栏里的“<strong>结束</strong>”按钮。这代表了平台的“<strong>干预权</strong>”。当一场直播出现违规内容或其他紧急情况时，平台必须有能力在第一时间，从最高权限上，强制将其关停。这是我们作为平台方，必须承担的责任，也是保障平台安全的生命线。</p><h3 id="11-3-2-商家端：商户的运营指挥中心"><a href="#11-3-2-商家端：商户的运营指挥中心" class="headerlink" title="11.3.2 商家端：商户的运营指挥中心"></a>11.3.2 商家端：商户的运营指挥中心</h3><p>对于商家而言，直播是他们最重要的营销工具和销售渠道之一。因此，我为他们设计的商家后台，必须像一个“<strong>作战指挥室</strong>”，专业、高效、功能完备。</p><p><strong>1. 申请与配置</strong></p><p>商家的直播之旅，始于“<strong>申请</strong>”。我需要为他们提供一个清晰的申请入口，并明确告知他们需要满足的条件，这既是功能，也是一种规则的宣导。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095657234.png" alt="image-20250725095657234"></p><p>当商家获得资格后，他们就需要一个专业的“<strong>直播间管理</strong>”后台。在这里，他们可以创建、编辑、管理自己所有的直播场次。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095735548.png" alt="image-20250725095735548"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100501358.png" alt="image-20250725100501358"></p><p>我设计的核心思路是“<strong>状态驱动</strong>”。你会发现，一场直播在“未开始”、“直播中”、“已结束”等不同状态下，商家可以进行的操作是完全不同的。比如，“未开始”的可以“编辑”，而“已结束”的只能“查看数据”。这种精细化的权限控制，能有效防止商家的误操作。</p><p><strong>2. 数据复盘</strong></p><p>直播的魅力，在于可以通过数据不断优化。一场直播结束后，商家最关心的问题就是：“<strong>这场直播效果怎么样？</strong>”。如果我不能回答这个问题，那么我设计的这个功能就是失败的。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095756930.png" alt="image-20250725095756930"></p><p>因此，我必须为商家提供一个详尽的“<strong>数据战报</strong>”。这个战报至少要包含三类核心数据：</p><ul><li><strong>流量数据</strong>：有多少人看？最高同时有多少人在线？涨了多少粉？</li><li><strong>互动数据</strong>：谁给我刷了礼物？价值多少？</li><li><strong>带货数据</strong>：卖了什么商品？卖了多少件？</li></ul><p>只有提供了这些数据，商家才能进行有效的复盘，我们的直播功能才算真正为商家创造了价值。</p><h3 id="11-3-3-用户端：主播与观众的互动舞台"><a href="#11-3-3-用户端：主播与观众的互动舞台" class="headerlink" title="11.3.3 用户端：主播与观众的互动舞台"></a>11.3.3 用户端：主播与观众的互动舞台</h3><p>用户端，是整个直播产品的“门面”，是所有用户能直接感知到的地方。我把它分为两条主线来设计：<strong>主播的“开播”之旅</strong>，和<strong>观众的“看播”之旅</strong>。</p><p><strong>1. 主播的开播之旅</strong></p><p>我设计主播端的核心理念是“<strong>简单高效，所见即所得</strong>”。主播在手机方寸之间，就要完成一场直播的全部准备工作。</p><ul><li><p><strong>第一步：设置直播信息</strong><br>一场直播的“门面”，就是封面和标题。我必须让主播可以轻松地上传一张吸引人的封面图，并起一个有噱头的标题。此外，“<strong>立即开始</strong>”和“<strong>预定时间</strong>”这两个选项也至关重要。“预定时间”能让主播提前预告，进行蓄水，这是专业运营的必备功能。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100613314.png" alt="image-20250725100613314"></p></li></ul><p>​	<img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100636104.png" alt="image-20250725100636104"></p><ul><li><p><strong>第二步：关联带货商品</strong><br>这是直播电商的“灵魂”。我需要为主播提供一个极为便捷的“<strong>选品</strong>”流程，让他们能从自己的店铺商品库中，快速勾选出本场要带货的商品，并添加到直播间的“小黄车”里。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100708800.png" alt="image-20250725100708800"></p></li><li><p><strong>第三步：直播中的掌控</strong><br>当直播开始后，主播的手机屏幕就变成了他的“<strong>驾驶舱</strong>”。美颜、滤镜、镜头翻转这些是基础功能，能让主播呈现出最好的状态。更重要的是，他需要有管理商品、与观众互动等一系列工具。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100747775.png" alt="image-20250725100747775"></p></li></ul><p><strong>2. 观众的看播之旅</strong></p><p>我设计观众端的核心理念是“<strong>沉浸体验，无缝下单</strong>”。我要让用户看得开心，买得顺滑。</p><ul><li><p><strong>核心互动界面</strong><br>用户进入直播间，首先看到的是一个集“<strong>视频画面</strong>”和“<strong>实时互动区</strong>”于一体的界面。下方的聊天弹幕区是营造社区感和热闹氛围的关键，让用户感觉自己不是一个人在看，而是在和成千上万的人一起“云逛街”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100818156.png" alt="image-20250725100818156"></p></li><li><p><strong>商品浏览与购买</strong><br>当主播开始介绍商品时，我必须为用户提供一个清晰、无干扰的商品展示区。这个区域通常在屏幕下方，以列表形式呈现。用户点击后，无需跳出直播间，就能查看商品详情并完成购买。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100846494.png" alt="image-20250725100846494"></p><p>这里的设计要点在于商品“<strong>状态</strong>”的实时同步。当主播讲解某个商品时，它的状态可能是“<strong>待上架</strong>”；当主播喊出“上链接”时，它会立刻变为“<strong>马上抢</strong>”；而当商品售罄时，它又会变为“<strong>已抢完</strong>”。这种实时的状态变化，是制造稀缺感、激发用户下单欲望的关键所在。</p></li></ul><hr><h2 id="11-4-直播电商的关键技术"><a href="#11-4-直播电商的关键技术" class="headerlink" title="11.4 直播电商的关键技术"></a>11.4 直播电商的关键技术</h2><p>在完成了产品的“长相”（用户界面）和“骨架”（功能逻辑）设计之后，我必须和技术团队坐下来，探讨它的“内脏和血脉”——也就是实现这一切所需要的技术。</p><p>作为产品经理，我不需要会写代码，但我必须理解其核心原理。这能让我评估技术方案的可行性、预估开发成本，并在关键的技术选型上，与团队进行有质量的对话。</p><h3 id="11-4-1-核心概念：推流与拉流"><a href="#11-4-1-核心概念：推流与拉流" class="headerlink" title="11.4.1 核心概念：推流与拉流"></a>11.4.1 核心概念：推流与拉流</h3><p>整个复杂的直播技术，可以被简化为两个最核心的动作：“<strong>推流</strong>”和“<strong>拉流</strong>”。</p><ul><li><strong>推流</strong>：我把它理解为“<strong>上传直播</strong>”的过程。它指的是主播的手机端（直播端）采集自己的声音和画面，并将其像水流一样，“推”送到云端服务器的行为。</li><li><strong>拉流</strong>：我把它理解为“<strong>下载直播</strong>”的过程。它指的是成千上万的观众，从云端服务器那里，将直播内容“拉”取到自己手机上进行观看的行为。</li></ul><p>一次流畅的直播体验，本质上就是一次高质量的“推”和成千上万次高质量的“拉”所共同构成的。</p><h3 id="11-4-2-直播的技术全景图"><a href="#11-4-2-直播的技术全景图" class="headerlink" title="11.4.2 直播的技术全景图"></a>11.4.2 直播的技术全景图</h3><p>在“推”与“拉”之间，是一个庞大而精密的后台服务系统。为了让团队清晰地理解这个系统，我通常会展示这样一张技术架构图。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725101657009.png" alt="image-20250725101657009"></p><p>我可以带你走一遍这个流程：</p><ol><li><strong>主播端（推送方）</strong>：一切的源头是主播。我们App会集成一个“<strong>推流SDK</strong>”，它就像一个专业的打包和邮寄工具，负责将主播的音视频内容采集、压缩，然后通过“<strong>推流节点</strong>”，发送到最近的云服务器。</li><li><strong>服务端（处理中心）</strong>：这是直播的“中央厨房”。“<strong>直播服务器</strong>”接收到主播的推流后，会立刻进行一系列的加工处理，例如：<ul><li><strong>转码服务</strong>：为了适配不同观众的网络状况，服务器会将原始视频流，实时转码成高清、标清、流畅等多个版本。</li><li><strong>录制服务</strong>：服务器会将整场直播，录制成一个视频文件（VOD），方便用户随时回顾。</li><li><strong>截图服务</strong>：自动截取直播的精彩瞬间作为封面。</li><li><strong>安全服务</strong>：对直播内容进行实时监控，防止违规。</li></ul></li><li><strong>观众端（拉取方）</strong>：经过处理的直播流，会被分发到全球的“<strong>CDN分发节点</strong>”。这就像是遍布全球的“前置仓库”。当观众打开App时，他们的“<strong>播放SDK</strong>”会自动连接到离他们最近的CDN节点，去“拉取”直播内容。这样，无论用户身在何处，都能获得低延迟、高流畅的观看体验。</li></ol><h3 id="11-4-3-产品经理的技术选型：自研-vs-第三方SDK"><a href="#11-4-3-产品经理的技术选型：自研-vs-第三方SDK" class="headerlink" title="11.4.3 产品经理的技术选型：自研 vs. 第三方SDK"></a>11.4.3 产品经理的技术选型：自研 vs. 第三方SDK</h3><p>了解到这套系统的复杂性后，一个关键的决策就摆在了我的面前：<strong>这套系统，我们是自己从零开始搭建，还是直接采购成熟的方案？</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725101806254.png" alt="image-20250725101806254"></p><p>我的答案，以及我给几乎所有公司的建议都是：<strong>果断选择第三方。</strong></p><p>原因很简单：作为一家电商公司，我们的核心竞争力在于“交易”而非“底层视频技术”。自研一套稳定、高并发、低延迟的全球直播系统，其投入是天文数字。聪明的产品决策，是“<strong>站在巨人的肩膀上</strong>”。</p><p>市面上有非常多专业、成熟的云服务商，提供完整的视频直播解决方案。我们只需要将他们的SDK集成到我们的产品中，就能在短时间内，以可控的成本，上线高质量的直播功能。</p><p>在做技术选型时，我会和技术负责人一起，重点考察几家头部厂商，例如：</p><ul><li><strong>阿里云</strong>：它的视频直播（<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly93d3cuYWxpeXVuLmNvbS9wcm9kdWN0L2xpdmU">阿里云直播服务</a>）服务，在国内市场份额巨大，技术稳定，文档齐全。</li><li><strong>网易云信</strong>：网易云信（<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly95dW54aW4uMTYzLmNvbS9saXZl">网易云信直播服务</a>）在社交、娱乐领域的解决方案经验丰富，尤其在IM（即时通讯）和音视频的结合上很有优势。</li><li><strong>腾讯云</strong>：腾讯云的互动直播解决方案（<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly9jbG91ZC50ZW5jZW50LmNvbS9zb2x1dGlvbi9pbHZi">腾讯云直播服务</a>），尤其强调“互动连麦”等场景，非常适合需要强社交属性的直播玩法。</li></ul><p>最终，我们会根据他们的产品性能、功能丰富度、服务支持以及价格等多个维度，综合评估，选择最适合我们当前业务需求的合作伙伴。</p></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/30992.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/30992.html&quot;)">2️⃣ 电商实战（下）</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/30992.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=2️⃣ 电商实战（下）&amp;url=https://prorise666.site/posts/30992.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理实战<span class="categoryesPageCount">5</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理实战<span class="tagsPageCount">5</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/30404.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">2️⃣ 电商实战（上）</div></div></a></div><div class="next-post pull-right"><a href="/posts/8823.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理进阶（一）：第一章：电商基础</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/30401.html" title="1️⃣ 内容产品模型实战"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-18</div><div class="title">1️⃣ 内容产品模型实战</div></div></a></div><div><a href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-26</div><div class="title">4️⃣ 商业B端产品经理实战</div></div></a></div><div><a href="/posts/30404.html" title="2️⃣ 电商实战（上）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">2️⃣ 电商实战（上）</div></div></a></div><div><a href="/posts/45404.html" title="3️⃣ 电商运营实战"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-25</div><div class="title">3️⃣ 电商运营实战</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"2️⃣ 电商实战（下）",date:"2025-07-21 15:09:48",updated:"2025-07-26 21:20:37",tags:["产品经理实战"],categories:["产品经理实战"],content:"\n---\n# 第七章：售后管理\n\n在我看来，用户下单付款，绝不意味着我们服务的结束，恰恰相反，它开启了我们与用户之间，一段更需要“信任”来维系的长期关系。\n\n如何处理好用户在购后遇到的种种问题，如何设计一套公平、清晰、高效的售后流程，直接决定了我们平台的口碑和用户的复购率。\n\n## 7.1 售后场景构建\n\n在设计任何具体的售后功能之前，我的第一步，是**构建出所有可能发生的售后场景**。我需要绘制一张完整的“售后地图”，确保我的设计，能够覆盖所有可能的用户求助路径。\n\n### 1. 不同角色的关注点\n\n“售后”这件事，从来不是一个单方的行为，它至少牵动着**用户、商家、平台**这三方的心。我构建场景，会先从理解这三方的不同“痛点”和“关注点”开始。\n\n| **角色** | **核心关注点** |\n| :--- | :--- |\n| **用户**| “我买的东西不想要了/有问题，怎么才能快速、方便地退/换？” |\n| **商家**| “用户退回来的货有没有问题？退换货的运费成本谁来承担？差评会不会影响我的店铺？” |\n| **平台**| “我们的售后规则是否公平？如何才能在保障用户体验和控制商家风险之间，找到平衡？处理这些纠纷需要多少客服人力？” |\n\n### 2. 核心售后场景提炼\n\n基于用户的核心关注点，我就可以提炼出，用户在订单生命周期的不同阶段，会发起的几大类核心售后场景。\n\n这些场景，就是我们后续设计流程的“**需求来源**”。\n\n| **订单状态** | **用户可能发起的售后场景** |\n| :--- | :--- |\n| **订单未付款** | `取消订单` |\n| **订单已付款、待发货** | `取消订单（申请仅退款）` |\n| **待收货 / 已完成** | `申请退款退货`、 `申请换货`、`申请仅退款`（如：商品漏发） |\n| **任意环节** | `交易纠纷，申请平台介入` |\n\n### 3. 售后职责与需求提取\n\n![image-20250723135351237](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723135351237.png)\n\n场景明确后，我需要为这些场景，定义清晰的“**责任边界**”和“**业务规则**”。这部分内容，将是我后续撰写PRD和设计流程的基础。\n\n![image-20250723135410717](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723135410717.png)\n\n我的设计，将遵循以下的基本职责划分：\n* **用户职责：发起者**\n    `一切售后行为，由买家发起`。我的用户端产品设计，必须为用户，在订单详情页等位置，提供清晰、便捷的售后申请入口。\n\n* **商家职责：处理者**\n    `商家收到售后申请进行响应`。商家是处理售后问题的第一责任人。我的商家后台设计，必须为商家提供处理这些申请的技术支持，但**具体的处理结果（同意/拒绝），由商家自己决定**。\n\n* **平台职责：保障者与仲裁者**\n    `当用户与商家发生纠纷时，平台介入，保障双方权益`。我的平台运营后台设计，必须包含一套“**纠纷仲裁**”机制。在保障用户交易安全的前提下，对纠纷进行公平的判决。\n\n\n\n---\n## 7.2 售后流程分析\n\n在我们构建了售后场景，明确了各方角色的职责之后，下一步，就是为每一个具体的场景，设计出**清晰、严谨、可执行**的业务流程。\n\n我设计的售后流程，就像是一部“**法律**”。它需要清晰地定义出，在某种售后场景下，用户和商家，各自拥有什么**权利**，需要履行什么**义务**，以及系统应该如何根据他们的操作，来**自动地流转订单的售后状态**。\n\n我们将逐一分析几种最高频的售后场景。\n\n### 1. 场景一：待付款取消订单\n\n* **核心逻辑**：这是最简单的售后场景。\n    * **角色**：完全由**买家**单方面发起和完成。\n    * **流程**：用户在“我的订单”中，找到“待付款”的订单，直接点击“取消订单”，订单状态即变为“已取消”。\n* **我的设计思考**：为什么这个流程，商家完全不参与？因为在用户付款之前，这笔订单，尚未进入商家的“待办事项（即待发货列表）”中，没有对商家产生任何实质性的履约成本。因此，我设计的流程，允许用户在这个阶段，自由地、无条件地取消订单。\n\n### 2. 场景二：待发货取消订单\n\n* **核心逻辑**：这个场景，开始涉及到买卖双方的交互。\n    * **角色**：由**买家**发起申请，但需要**商家**进行审核。\n    * **流程**：买家在“待发货”订单中，发起“取消申请” -> 商家在后台收到申请，进行审核 -> 若商家同意，则订单取消，系统自动退款；若商家拒绝，则订单继续保持“待发货”状态。\n* **我的设计思考**：为什么商家需要审核？因为用户付款后，订单就已经进入了商家的履约流程。商家可能已经在“拣货”、“打包”，甚至已经交给了快递员但还未揽件。这个“审核”的环节，就是我留给商家的一个“**拦截窗口**”，让他去确认，这笔订单，是否还能从他的发货流程中，被成功地拦截下来。\n\n\n\n### 3. 场景三：待收货/已完成 - 退款退货\n\n这是最复杂的售后场景，因为它涉及到“**逆向物流**”（即用户把商品寄回给商家）。我必须为这个场景，设计一套严谨的、多步骤的“**售后状态机**”。\n\n**我的流程与状态设计**：\n\n| 售后状态 | 触发动作 | 下一步状态 |\n| :--- | :--- | :--- |\n| **（初始）** | **买家**在用户端，对“待收货”或“已完成”的订单，发起退款退货申请，并填写理由。 | `待商家审核` |\n| `待商家审核` | **商家**在后台，审核通过买家的申请。 | `待买家发货` |\n| `待买家发货`| **买家**在用户端，填写退货的快递单号，或上门与自行寄回 | `商家待收货` |\n| `商家待收货`| **商家**在后台，确认已收到买家寄回的商品，且商品完好无损。 | `退款中 / 退款成功`|\n\n![image-20250723141252995](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141252995.png)\n\n### 4. 场景四：待收货/已完成 - 申请换货\n\n换货流程，因为涉及到“**双向物流**”（买家寄回 -> 商家寄出），所以它的状态机，比退货更复杂。\n\n![image-20250723141408765](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141408765.png)\n\n**我的流程与状态设计**：\n换货流程的前4步，与退款退货完全一致（从`待商家审核`到`商家待收货`）。在商家确认收到退货后，流程继续：\n\n| 售后状态 | 触发动作 | 下一步状态 |\n| :--- | :--- | :--- |\n| `商家待收货`| **商家**在后台，确认收到退货后，将新的商品，重新发货给买家，并填写新的快递单号。 | `待买家收货` |\n| `待买家收货`| **买家**在用户端，确认收到商家换发的商品。 | **（换货流程结束）** |\n\n通过为每一个售后场景，都设计这样一套清晰的流程和状态机，我就可以确保，我们平台、用户、商家三方，在处理复杂的售后问题时，都有据可依，有路可循。\n\n### 5.商家端与平台端的售后管理页面\n\n![image-20250723141535312](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141535312.png)![image-20250723141546753](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141546753.png)\n\n\n-----\n\n## 7.3 交易纠纷处理\n\n在我们设计的售后流程中，大部分的退款、退货申请，都可以由用户和商家，通过协商来顺利解决。\n\n但是，我们必须考虑到一种情况：**当用户和商家，无法达成一致时，应该怎么办？**\n比如，用户申请退货的理由是“商品有质量问题”，并上传了图片；而商家则反驳，认为是用户“人为损坏”，并拒绝了退款申请。\n\n此时，双方就陷入了“**交易纠纷**”。如果平台不介入，这个矛盾将永远无法解决，并会极大地损害用户对平台的信任。因此，我必须设计一套**公平、公正、透明**的**平台介入仲裁机制**。\n\n### 1\\. 平台介入的原则与时机\n\n我设计这套仲裁机制，会遵循以下核心原则：\n\n  * **保障交易安全**：我设计的平台规则，会**优先保障用户的合法权益**。\n  * **明确介入时机**：平台介入的“**触发器**”非常明确——**在售后流程中，任何一方的合理请求，被另一方“拒绝”时**，系统就应该为被拒绝的一方，提供“**申请平台介入**”的入口。\n  * **依赖双方举证**：平台作为“法官”，**绝不偏听偏信**。我的判决，必须建立在双方提供的“**证据**”之上。\n\n### 2\\. 交易纠纷处理流程与功能设计\n\n![image-20250723142127598](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142127598.png)\n\n整个交易纠纷的处理，我将它设计为一个严谨的、多方参与的线上流程。\n\n![image-20250723142252925](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142252925.png)\n\n  * **用户端功能**\n    当用户的售后申请被商家拒绝后，我会在他的订单详情页，提供一个“**申请平台介入**”的按钮。点击后，会进入“**举证页面**”，用户可以在这里，上传他认为能支持自己诉求的文字、图片、视频等证据。\n\n  * **商家端功能**\n    当用户申请平台介入后，这笔售后订单，在商家后台的状态，就会变为“**待商家举证**”。商家同样需要在这个订单的详情页，进入“**举证页面**”，上传对他有利的证据（如：发货前的商品完好视频、与用户的聊天记录等）。\n\n  * **平台端功能**\n    这是我们内部客服和仲裁团队的“**法庭**”。\n\n    1.  **维权列表**\n        ![image-20250723142331985](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142331985.png)\n\n        所有用户申请介入的纠纷单，都会进入到这个独立的“**维权列表**”工作队列中，等待我们的客服“法官”来处理。\n\n    2.  **维权详情页**\n        ![image-20250723142348594](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142348594.png)\n\n        这是“法官”的判案工作台。我设计的这个页面，会**聚合**这笔纠纷的所有信息：\n\n          * 原始的订单信息。\n          * 完整的售后申请记录和双方的沟通日志。\n          * **买家提供的证据**。\n          * **卖家提供的证据**。\n\n        在页面的最下方，我会为“法官”，提供最终的“**判决**”功能按钮，比如“**支持买家**”或“**支持卖家**”。\n\n### 3\\. 证据链条设计\n\n![image-20250723142456946](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142456946.png)\n\n![image-20250723142519553](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142519553.png)\n\n“**证据**”，是我们整个仲裁流程的核心。因此，我设计的“举证页面”（无论是用户端还是商家端），都必须支持上传多种形式的证据。\n\n| **证据类型** | **我的设计说明** |\n| :--- | :--- |\n| **文字描述** | 供双方清晰地、有条理地，陈述事情的经过和自己的诉求。 |\n| **图片/视频证据**| 这是最直观的证据。如：商品损坏部位的照片、开箱视频、证明商品货不对板的截图等。 |\n| **凭证类文件** | 包括但不限于：与对方的**聊天记录**、**发货/退货的快递底单**、甚至是物流公司出具的“**红章证明**”等。 |\n\n通过这套严谨的“**申请介入 -\\> 双方举证 -\\> 平台判决**”的流程，我为我们的电商平台，建立起了一道能化解交易矛盾、保障用户和商家双方合法权益的“安全网”。\n\n\n\n\n\n---\n# 第八章：电商后台 - 种草管理\n\n在第三章，我们为用户端，设计了“**商品种草**”这个核心的、内容驱动的社区化模块。用户可以在这里，发布自己的购物心得，并与其他用户进行互动。\n\n现在，我们必须回到**平台运营后台**，来为这个模块，设计一套相应的**管理系统**。我们作为平台，至少需要解决三个核心问题：\n1.  用户发布“种草”笔记时，可选的“**话题分类**”从哪里来？\n2.  笔记可以关联的“**话题标签**”又从哪里来？\n3.  用户发布的这些海量的UGC（用户生产内容），我们平台**如何进行管理和审核**？\n\n这一章，我们就来逐一设计解决这些问题的后台功能。\n\n## 8.1 学习目标\n\n在本节中，我的核心目标是，带大家掌握电商后台中，社区化模块的管理后台设计。我们将学习如何设计一套**话题分类**与**话题**的二级管理体系，并为运营同事设计高效的**种草内容**与**评论**的审核后台。\n\n## 8.2 话题分类与话题管理\n\n为了让用户发布的“种草”笔记，能够被有组织、有结构地呈现，我必须在后台，预先定义好一套“**分类**”与“**话题**”的体系。\n\n### 8.2.1 话题分类管理\n\n![image-20250723143340432](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723143340432.png)\n\n“**话题分类**”，是我为“种草”社区，设定的最高层级的、类似“频道”的内容划分。比如：“服饰穿搭”、“数码评测”、“美妆心得”等。\n\n我设计的“**分类管理**”后台，核心功能如下：\n* **基础管理**：运营人员可以对分类，进行**新增、编辑、删除、查询**。\n* **状态管理**：每个分类都有“**显示/隐藏**”两种状态。运营可以将某个分类暂时“隐藏”，那么这个分类，就不会在用户端展示，用户发布时也无法选择。\n* **排序**：运营可以通过调整一个“**排序值**”，来控制这些分类，在用户端的显示顺序。\n\n### 8.2.2 话题管理\n\n![image-20250723143405323](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723143405323.png)\n\n“**话题**”，是隶属于某个“话题分类”之下，更具体、更聚焦的“标签”。比如，在“服饰穿搭”这个分类下，就可以有“#OOTD”、“#小个子穿搭”、“#夏日多巴胺”等多个热门话题。\n\n![image-20250723143428063](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723143428063.png)\n\n我设计的“**话题管理**”后台，除了基础的增删改查和状态管理外，最核心的一个设计点是：\n* **关联分类**：在新增或编辑一个“话题”时，我必须让运营，可以从我们上一步创建好的“**话题分类**”列表中，选择一个，来**与这个话题进行关联**。\n\n这个“**分类-话题**”的二级结构，就构成了我们整个“种草”社区，内容组织的骨架。\n\n## 8.3 种草内容与评论管理\n\n### 8.3.1 内容审核策略\n\n对于UGC社区，如果每一条内容，都采用“**先审后发**”的模式，那审核的压力会极大，并且会严重影响用户的发布体验。\n\n因此，我通常会采用“**先发后审**”的策略：\n\n![image-20250723143508932](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723143508932.png)\n\n1.  系统先通过我们设计的“**敏感词**”系统，进行一次自动过滤。\n2.  只要内容不包含敏感词，就**允许它被正常发布**，立刻对其他用户可见。\n3.  然后，这条内容，会进入我们后台的“**人工审核**”队列，由运营同事，在稍后进行二次审核，来处理那些“漏网之鱼”。\n\n\n\n---\n# 第九章：电商后台 - 财务管理\n\n欢迎来到第九章。在这一章，我们将探讨电商平台的心脏——资金的流动。我作为产品经理，虽然不需要成为财务专家，但我必须深刻理解电商交易中，资金清算与结算的基本逻辑和合规要求。\n\n因为任何与“钱”相关的设计，都必须将**安全、准确、合规**这三个词，刻在我的脑海里。\n\n## 9.1 学习目标\n\n在本章中，我的核心目标是，带大家掌握电商后台财务管理的基础。我们将重点学习**清算与结算**的核心概念及业务流程，并深入理解其中至关重要的**合规问题**。\n\n## 9.2 清算与结算\n\n当一个用户在我们的平台支付了100元，这100元是如何，从用户的口袋，安全、准确地，在扣除我们的平台佣金后，最终到达商家的口袋里的？\n\n要回答这个问题，我们就必须先理解两个核心的金融概念：**清算**和**结算**。\n\n### 9.2.1 核心概念定义（清算 vs 结算）\n\n我用一个通俗的方式，来帮大家理解这两个概念：\n\n| **概念** | **核心产出** |\n| :--- | :--- |\n| **清算** | 它的工作，是对某一个周期内（比如一天）发生的所有交易数据，进行汇总、分类、计算。最终准确地算出：“**今天，我平台总共应该给A商家打多少钱，给B商家打多少钱。**” |\n| **结算 ** | 它的工作，是依据“清算”得出的结果，进行**实际的资金划拨**操作，把钱从一个账户，转移到另一个账户。 |\n\n简单来说，**清算是“脑力劳动”，结算是“体力劳动”**。先算清楚，再打款。\n\n### 9.2.2 资金与信息流程分析\n\n![image-20250723144613976](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723144613976.png)\n\n在一个合规的电商平台中，清结算的过程，包含了“**资金流**”和“**信息流**”这两条并行的线。\n* **信息流**：我们平台的**订单数据**，会流入到我们后台的**管理页面**。我们的财务同事，会基于这些信息，来进行**查询和对账**。\n* **资金流**：用户支付的钱，会先进入到一个**第三方的“清结算机构”**（比如支付宝、微信支付、银行）的**资金账户**中。这个机构，会根据我们平台提供的“清算”信息，进行“结算”，最终将钱，打到商家的**结算账户**中，商家再进行**提现**。\n\n### 9.2.3 核心合规问题：“二清”\n\n![image-20250723144729774](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723144729774.png)\n\n在理解了上述流程后，我们就必须来探讨电商支付领域，最重要、也是最危险的一条“**红线**”——“**二清**”。\n\n* **什么是“二清”？**\n    “二清”，指的是“**二次清算**”。它是指，**没有获得国家支付业务许可证的机构**（比如我们这个电商平台自身），直接经手交易资金，并进行清分结算的行为。\n* **为什么违规？**\n    如果我们平台的业务流程是：`买家 -> 平台公司账户 -> 卖家`。这意味着，我们平台，在中间形成了一个汇集了所有交易资金的“**资金池**”。这种行为，是在行使“银行”或“支付机构”的职能，但我们并没有获得对应的金融牌照，这是**严重违规**的，会面临巨大的法律和政策风险。\n* **合规的流程是怎样的？**\n    合规的流程，必须是：`买家 -> 持牌机构 -> 卖家`。\n    \n    “**持牌机构**”，就是指像**支付宝、微信支付**这样，拥有国家颁发的《支付业务许可证》的机构。在整个交易过程中，我们平台，**只能处理“信息流”**（即订单信息），**绝对不能触碰“资金流”**。我们只能向“持牌机构”，下达支付和结算的“指令”，而实际的资金划拨，必须由这些持牌机构来完成。\n\n### 9.2.4 平台对账管理\n\n基于上述的合规流程，我设计的平台财务后台，一个最核心的功能，就是**对账管理**。\n* **它的作用是**：我们的系统，需要每天自动地，从我们合作的持牌支付机构（如支付宝、微信支付）那里，下载前一天的“**交易账单**”。\n* 然后，系统需要将这份“**外部账单**”，与我们自己数据库里的“**内部订单记录**”，进行**逐条比对**。\n* **最终目的**：是确保每一笔交易的金额、状态，内外部都是完全一致的，并自动地将差异和问题（比如“掉单”）标记出来，供我们的财务同事进行处理。\n\n\n\n\n\n---\n## 9.3 财务管理\n\n在我们`9.2`节设计的“清结算”流程中，我们确保了交易的资金，都安全、合规地，进入到了由持牌支付机构监管的账户中。\n\n现在，我们就需要设计一套功能，来处理这笔钱后续的分配和管理。**财务管理**后台，就是我们用来处理平台与商家之间“**分钱**”和“**打钱**”的系统。\n\n### 9.3.1 商家账单与提现管理\n\n这个功能的设计，我需要同时考虑**商家端**和**平台端**两个方面，因为它是买卖双方之间的一个完整互动流程。\n\n#### 1. 商家端设计\n\n我需要为商家，提供一套清晰、透明、便捷的“**账房**”工具。\n\n* **商家查看结算记录**![image-20250724103418115](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103418115.png)\n\n在我设计的商家后台中，会有一个“**结算中心**”。商家可以在这里，清晰地看到平台在每个结算周期（如每月），为他结算的**总订单数**、**总结算金额**，并能查询到构成这笔总额的**每一笔订单明细**，确保账目的清晰透明。\n    \n* **商家申请提现**\n    ![image-20250724103501653](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103501653.png)\n\n    当结算完成后，这笔钱就会进入商家的“**可提现余额**”。我会为商家设计一个“**账户概况**”页面，清晰地展示他的账户余额。并提供一个醒目的“**申请提现**”按钮。点击后，商家可以输入他希望提现的金额，并确认收款的银行账户信息。\n\n* **商家查看提现记录**\n    ![image-20250724103538754](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103538754.png)\n\n    提交申请后，商家可以在“**提现记录**”页面，实时地追踪这笔提现的状态，如`待审核`、`提现中`、`已到账`、`已驳回`等。\n\n#### 2. 平台端设计\n\n![image-20250724103610743](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103610743.png)\n\n商家的“提现申请”，会触发我们平台运营后台的一系列审核和操作流程。\n\n* **平台审核提现申请**\n    ![image-20250724103651498](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103651498.png)\n\n    我需要为我们的财务同事，设计一个“**提现审核**”列表。所有商家的提现申请，都会进入这个工作队列。财务同事的核心操作，就是对申请进行“**审核**”。审核通过后，该笔申请的状态，就会流转为“**待转账**”。\n\n* **财务执行转账**\n    ![image-20250724103718370](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724103718370.png)\n\n    进入“待转账”队列后，财务同事，会通过企业网银等方式，进行线下的实际打款。打款完成后，他会在后台，点击“**确认转账**”按钮，并填写相关的支付凭证信息。此时，这笔提现流程，才算最终完成，状态变为“**已转账**”。\n\n### 9.3.2 平台抽佣与计费规则\n\n在“清算”的过程中，一个核心的业务逻辑，就是计算我们平台的收入，即“**平台抽佣**”。\n* **我的设计**：我会在平台运营后台，设计一个“**计费规则管理**”模块。在这里，我的业务部门，可以为**不同的商品类目，配置不同的交易佣金比例**（比如：服装类目抽佣5%，数码类目抽佣3%）。\n* **系统应用**：在我们`9.2`节的“清算”环节，系统就会自动地，根据这些预设好的规则，去计算每一笔订单我们平台应该抽取的佣金，然后再把剩下的金额，计入商家的“可结算金额”中。\n\n### 9.3.3 发票管理\n\n一个完善的财务后台，还需要处理“**发票**”这个重要的业务。\n* **我的设计**：我需要设计两套发票流程。\n    1.  **商家向平台申请服务费发票**：商家可以就支付给我们的“**平台服务费**”，向我们申请开具发票。\n    2.  **用户向商家申请商品发票**：用户可以就购买的“**商品**”，向商家申请开具发票。这个申请，会流转到**商家后台**，由商家进行处理。\n\n\n---\n# 第十章：分销电商\n\n欢迎来到第十章。在前面的章节中，我们已经完整地学习了，如何设计一个“人、货、场”模型下的平台型电商。现在，我们将探讨一种能为平台带来强大“**裂变增长**”能力的、建立在**社交关系链**之上的高级模式——**分销电商**。\n\n## 10.1 学习目标\n\n在本章中，我的核心目标是，带大家系统性地掌握分销电商的业务模式与产品设计。我们将从项目背景出发，理解分销电商的定义和核心角色，并最终学会如何为这个模式，设计其独特的产品功能。\n\n## 10.2 分销电商项目背景\n\n![image-20250724104514357](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724104514357.png)\n\n### 1. 为什么要做分销电商？\n\n我之所以要考虑在我们的电商产品中，融入分销模式，其核心的驱动力，是为了解决传统电商模式“**获客成本越来越高**”的瓶颈。\n\n分销电商，本质上是一种**S2B2C (Supply chain to Business to Customer)**的模式。它通过一种“**利益共享**”的机制，将我们平台上的海量“**C端用户**”，转化为成千上万的“**小B（分销商）**”，让他们利用自己的私域流量和社交信任，去为我们获取更多的新用户。\n\n### 2. 分销电商的核心需求\n\n基于这个背景，我提炼出的、搭建分销系统的核心产品需求如下：\n1.  **用户可以申请成为平台的分销商**。\n2.  **商家有权利自定义自己店铺的商品，是否允许分销**。\n3.  **分销商可以发展自己的下线**，但为了确保业务合规，**层级不能超过两级**。\n\n### 10.2.1 什么是分销电商\n\n![image-20250724113646666](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724113646666.png)\n\n我给**分销电商**的定义是：**一个通过设置“销售提成”作为激励，驱动平台用户（即分销商），利用其自有的“社交关系”进行商品分享和销售裂变，并最终达成“自购省钱，分享赚钱”目的的商业模式。**\n\n![image-20250724113704425](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724113704425.png)\n\n正如案例所示，分销最常见的形态，就是用户将一个带有自己专属二维码或链接的商品海报，分享到微信群或朋友圈。当他的好友通过这个链接完成购买后，他就能获得平台支付的相应比例的佣金。\n\n在这个模式中，我们平台，需要为分销商解决好除了“销售”以外的一切后顾之忧，即**统一提供货源、仓储、配送和售后服务**。\n\n### 10.2.2 核心角色定义（供货商、分销商、消费者）\n\n我设计分销系统，需要清晰地定义出这个新生态中的三个核心角色：\n\n| **核心角色** | **我的定义与解读** |\n| :--- | :--- |\n| **供货商 (Supplier)** | 这是“**货**”的来源。他们可以是**我们平台自营**的商品，也可以是我们平台上**参与了分销活动的第三方商家**。他们的核心诉求，是**提升商品销量**。 |\n| **分销商 (Distributor)**| 这是我们这个模式中，**新增的核心角色**。他们是平台的普通用户，在申请成为分销商后，就拥有了“**带货**”的资格。他们**不拥有商品、不处理订单、不负责发货**，他们唯一的工作，就是**分享和推广**。他们的核心诉-求，是**赚取佣金**。 |\n| **消费者 (Consumer)**| 这是最终完成购买的**终端用户**。他们通常是某个分销商的**好友或粉丝**。他们的购买决策，很大程度上是建立在对分销商的**信任**之上。 |\n\n\n\n---\n## 10.3 分销电商的优势\n\n我们已经清楚了分销电商的定义和核心角色。现在，我需要回答一个关键的商业问题：**作为一个产品或业务的决策者，我为什么要选择分销这种模式？**\n\n答案在于，一个设计良好的分销体系，能为我们带来传统电商模式，难以企及的三大核心优势。\n\n### 10.3.1 低成本快速裂变\n\n![image-20250724132513350](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724132513350.png)\n\n在我看来，分销模式最强大、最核心的优势，就是它解决了现代电商最头痛的问题——**高昂的获客成本**。\n\n* **传统模式的困境**：传统的电商平台，需要花费巨额的市场预算，去购买流量、投放广告，来吸引用户。\n* **分销模式的破局**：分销模式，本质上是将我们的营销预算，**从“购买流量”，变为了“奖励用户”**。我不再花钱给广告平台，而是把这部分钱，以“**销售佣金**”的形式，直接分给了帮我们带来客户的分销商。\n\n这相当于，我们**将每一个分销商，都发展成了我们“行走的广告牌”和“销售渠道”**。他们利用自己的社交关系链，进行“一带十、十带百”的**裂变式传播**。正如云集的案例数据显示，其“**单个用户维系成本**”，显著低于阿里、京东等传统流量驱动的电商平台。这就是裂变带来的低成本优势。\n\n### 10.3.2 强信任关系转化\n\n分销模式的第二个巨大优势，是它能带来**极高的销售转化率**和**用户忠诚度**。\n\n* **传统模式的挑战**：用户面对一个冰冷的平台推送的广告，内心天然是带有“防备”和“不信任”的。\n* **分销模式的破解**：分销模式的传播，是建立在“**社交信任**”的基础之上的。**朋友的推荐，远比平台的广告，更具说服力。**\n\n当一个消费者，看到他朋友圈里，一位他所信任的好友或KOL，在真实地分享一款产品的使用心得时，他的购买决策链路会变得极短。这种基于信任的转化，效果是惊人的。云集案例中提到的“**复购率达到93.6%**”，就是这种强信任关系，带来高用户粘性的最好证明。\n\n### 10.3.3 轻资产运营\n\n![image-20250724132621058](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724132621058.png)\n\n分销模式的第三个优势，是它为“**分销商**”这个角色，提供了一种**极具吸引力的“轻资产”运营**模式。\n\n我把它总结为“**你只管卖，其他都交给我**”。\n\n| **电商环节** | **由谁负责？** | **对分销商意味着什么？** |\n| :--- | :--- | :--- |\n| **供货/选品**| **平台/供货商** | 分销商**无需**自己找货源 |\n| **仓储/库存**| **平台/供货商** | 分销商**无需**自己租仓库、压库存 |\n| **发货/物流**| **平台/供货商** | 分销商**无需**自己打包、发快递 |\n| **售后服务**| **平台/供货商** | 分销商**无需**自己处理复杂的退换货问题 |\n| **推广/销售**| **分销商**| **分销商只需要专注于他最擅长、最核心的一件事：分享和推广。** |\n\n正是这种“轻资产”的模式，极大地降低了个人成为“小老板”的门槛，使得我们的分销商队伍，可以像滚雪球一样，快速地发展和壮大。\n\n\n\n\n---\n## 10.4 分销电商搭建思路\n\n我们已经理解了分销电商的“是什么”和“为什么”。现在，我们就进入最核心的“**怎么做**”的环节。\n\n要搭建一套完整的分销电商体系，我作为产品经理，需要从顶层，设计好**三大核心支柱**：\n\n**分销员体系**、**商品与供应链体系**、以及**佣金与结算体系**。这三大支柱，共同构成了我们分销业务的“骨架”。\n\n### 10.4.1 分销员体系设计\n\n分销业务，核心是“**人**”的生意。因此，我首先要设计好，我们“**分销员**”这个核心角色的完整生命周期和组织结构。\n\n**1. 角色与层级**\n\n![image-20250724133059672](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133059672.png)\n\n为了激励分销员，为平台带来更多的“下线”分销员，我设计的体系，通常会包含“**分销层级**”。\n* **核心逻辑**：一个高级别的“一级分销商”，可以邀请新人，成为他的“二级分销商”。当“二级分销商”卖出商品时，“一级分销商”也能获得一部分的“团队奖励”。\n* **我的合规设计要点**：我必须强调，为了确保业务的合法合规，在国内设计分销体系时，**计佣（计算佣金）的层级，绝对不能超过三级**。这是一个不可逾越的红线。\n\n**2. 核心流程**\n\n![image-20250724133213227](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133213227.png)\n\n我设计的整个分销员体系，必须能够支撑起以下几个核心的业务流程：\n* **成为分销商**：普通用户，可以通过一个申请入口，提交申请，由平台审核通过后，获得“分销商”身份。\n* **分享商品**：分销商可以在App内，选择商品，生成带有自己专属“推广码”的海报或链接，并分享出去。\n* **发展下线**：分销商可以生成自己专属的“邀请码”，邀请好友来注册，成为自己的“下线”分销商。\n* **购买转化**：当一个普通消费者，通过分销商分享的链接完成购买后，系统需要准确地记录下这笔订单的归属。\n\n### 10.4.2 商品与供应链管理\n\n分销员只负责“推广”，而不负责“货”。因此，我必须在后台，设计好“**货**”的管理逻辑。\n\n* **平台侧**：在平台运营后台，我需要设计一个“**总开关**”，可以一键启用或关闭整个平台的分销功能。\n* **商家侧**：在商家后台，我需要为商家，提供**两级控制权**：\n    1.  **店铺级开关**：商家可以决定，自己整个店铺，是否参与平台的分销活动。\n    2.  **商品级开关**：在参与活动的前提下，商家还可以进一步地，去勾选“**指定**”的某些商品，来参与分销。\n\n### 10.4.3 佣金与结算体系\n\n这是驱动整个分销体系运转的“**发动机**”。我设计的佣金结算体系，必须**公平、透明、准确**。\n\n* **佣金规则配置**：我需要在平台运营后台，设计一个强大的“**佣金规则引擎**”。它需要支持运营同事，可以灵活地，按不同维度，来设置佣金比例。\n    * **按商品设置**：不同的商品，可以有不同的佣-金比例。\n    * **按分销商等级设置**：高级别的分销商，可以享受更高的佣金比例。\n    * **团队奖励设置**：可以设置当下线分销商出单时，其上级可以获得的奖励比例。\n* **结算与提现**：当一笔通过分销链接产生的订单，**完成交易**（即，已过售后维权期）后，系统需要**自动地**，将计算好的佣金，打入对应分销商的“**佣金账户**”中。同时，我需要在分销商的专属后台，为他设计清晰的“**收益报表**”和便捷的“**佣金提现**”功能。\n\n![image-20250724133510027](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133510027.png)\n\n综上所述，我搭建分销电商的整体思路，就是围绕“**人（分销员体系）**”、“**货（商品管理）**”、“**钱（佣金体系）**”这三大核心，分别为**用户端、商家端、平台端**，设计出支撑其运转所必需的功能。\n\n\n---\n## 10.5 分销电商产品设计\n\n在我们明确了分销电商的搭建思路之后，现在，我们就进入具体的**产品功能设计**环节。我将严格按照**平台端、商家端、分销商端**这三个不同的使用者视角，来分别进行功能设计的拆解。\n\n### 10.5.1 平台端核心功能\n\n这是整个分销系统的“**总控制器**”，由我们平台的运营人员使用，用来设定整个分销业务的“游戏规则”。\n\n![image-20250724140309332](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140309332.png)\n\n* **分销规则配置**：我设计的后台，必须有一个全局的“**分销设置**”页面。在这里，运营可以设置`是否开启分销`、`是否开启自购分佣`、`分销层级`（最多支持几级）、以及每一级的`抽佣比例`。\n\n![image-20250724140324144](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140324144.png)\n\n* **分销员等级管理**：为了激励分销商，我还会设计一个“**分销等级**”管理后台。运营可以在这里，创建不同的分销商等级（如：初级、中级、高级），并为每个等级，配置不同的**邀请奖励**和**销售抽成**比例，以及对应的**升级规则**。\n\n![image-20250724140344850](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140344850.png)\n\n* **分销员审核管理**：当有普通用户，申请成为分销商时，他们的申请会进入到这个后台的“**待审核**”列表中。运营人员可以在这里，查看申请人的信息，并进行“**通过**”或“**驳回**”的操作。\n\n![image-20250724140428712](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140428712.png)\n\n* **订单与结算管理**：我需要设计一个“**分销订单**”列表，让运营和财务，可以清晰地看到每一笔通过分销产生的订单，以及这笔订单需要为哪几级的分销商，分别计算多少佣金。同时，还需要“**结算设置**”和“**提现管理**”功能，来处理佣金的发放。\n\n![image-20250724140527700](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140527700.png)\n\n![image-20250724141040118](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724141040118.png)\n\n\n\n---\n\n### 10.5.2 商家端核心功能\n\n这是我们设计给“**供货商**”（即参与分销的商家）使用的后台，核心是让他们能够**对自己店铺的分销业务，进行自主管理**。\n\n* **分销商品管理**：在商家后台的“**商品管理**”模块，我需要为商家提供一个“**分销商品设置**”的功能。\n\n![image-20250724140703094](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140703094.png)\n在这里，商家可以**勾选**自己店铺中，愿意拿出利润来进行分销的商品。并且，可以为这些商品，**设定一个基础的佣金比例**。\n\n![image-20250724140729521](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140729521.png)\n\n* **分销业绩查看**：我还需要为商家，提供一个查看**分销业绩**的报表。在这里，他可以看到是**哪些分销商**，为他带来了**哪些订单**，让他可以直观地感受到分销为店铺带来的价值。\n\n### 10.5.3 分销商端核心功能\n\n这是我们设计给“**分销商**”本人使用的“**个人工作台**”，它通常会内嵌在我们用户端App的“个人中心”里。\n\n![image-20250724140758440](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140758440.png)\n\n* **申请成为分销商**：首先，我需要在用户端的“个人中心”等位置，为普通用户，提供一个清晰的“**申请成为分销商**”的入口。\n\n![image-20250724140808421](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140808421.png)\n\n* **选品中心与推广**：当用户成为分销商后，他的个人中心，就会出现“**分销中心**”的模块。\n\n![image-20250724140842614](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140842614.png)\n在分销中心里，他可以浏览所有可供分销的商品。在商品详情页上，会有专属于他的“**自购省钱**”和“**分享赚钱**”按钮。点击“分享赚钱”，系统会自动为他生成带有**专属推广二维码**的精美海报。\n\n![image-20250724140918643](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140918643.png)\n\n* **收益与提现**：这是分销商最关心的模块。我设计的这个页面，必须清晰地展示他的`今日收益`、`累计收益`等核心数据，并提供一个醒目的“**提现**”入口。\n\n![image-20250724140943661](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140943661.png)\n\n* **团队管理**：为了鼓励“裂变”，我还需要为分销商，设计一个简单的“**我的团队**”管理功能。在这里，他可以获取专属的**邀请链接/海报**，用于发展自己的下线团队，并查看团队的业绩概况。\n\n## 10.6 本章总结\n\n在本章，我们系统性地学习了“**分销电商**”这种独特的商业模式。\n\n- **背景与优势**：我们理解了它通过**社交裂变**，来**降低获客成本**、提升**转化率**的核心价值。\n- **搭建思路**：我们明确了搭建分销体系，需要从**分销员、商品、佣金**这三大支柱入手。\n- **产品设计**：我们分别为**平台、商家、分销商**这三方，设计了支撑其业务运转所必需的核心功能。\n\n\n\n---\n\n# 第十一章：直播电商\n\n欢迎来到第十一章。在过去的学习中，我们已经掌握了平台电商的稳固根基和分销电商的裂变增长。现在，我将带您进入一个能将“**购物体验**”和“**销售转化**”推向极致的全新领域——**直播电商**。这是一种将“**实时互动**”与“**商品销售**”无缝融合的、极具沉浸感的商业模式。\n\n---\n## 11.1 直播电商项目背景\n\n在我负责的产品中，每当要引入一个像“直播”这样重大的新功能时，我都会先回归到最根本的商业问题上：我们现有的模式遇到了什么瓶颈？而这个新功能，是否能成为破局的关键？\n\n### 11.1.1 为什么需要直播电商？\n\n传统的货架式电商，本质是“人找货”，用户带着目的来搜索、比价。这种模式在今天面临着越来越大的挑战：流量越来越贵，用户的注意力越来越分散，单纯的打折促销也越来越难以打动他们。\n\n我发现，直播电商恰好能从三个方面，完美地破解这些困局。\n\n1.  **从“花钱买流量”到“内容吸流量”**：传统电商需要不断地投入巨额广告费，去购买流量。而直播电商，特别是与KOL（关键意见领袖）的合作，是利用主播自带的影响力和内容创作能力，将他的粉丝高效地吸引到我们的平台上来。这是一种更聪明、更具性价比的获客方式。\n2.  **从“理性对比”到“感性促单”**：在传统电商的图文页，用户的决策链路相对较长，消费也更趋于理性。但在直播间里，主播通过现场试用、实时互动和限时限量的话术，能够营造出一种“不买就亏了”的紧迫感和热烈氛围，这极大地激发了用户的感性消费和冲动购买，转化率自然远超平时。\n3.  **从“静态浏览”到“沉浸互动”**：图文详情页是静态的、单向的。而直播，是一种“所见即所得”的沉浸式体验。我可以实时看到衣服的上身效果，可以要求主播展示产品的某个细节，可以通过弹幕与成千上万的人交流。这种丰富、立体的购物体验，是传统电商无法比拟的。\n\n### 11.1.2 到底什么是直播电商？\n\n所以，到底什么是直播电商？\n\n在我看来，直播电商的核心，是**商业模式从“以货为中心”向“以人为中心”的彻底转变**。\n\n![image-20250724194744839](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724194744839.png)\n\n它不再是冰冷的货架，而是基于一个活生生的、你所信任或喜爱的主播，来建立交易。消费者购买的，不仅仅是商品本身，更是对这个主播的品味、专业度或个人魅力的“信任票”。这种以信任为前提的商业模式，其根基依然是电商，但能量却被放大了无数倍。\n\n![image-20250724194842043](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724194842043.png)\n\n我们想象一个真实的场景：当主播在镜头前，一边讲解着手机的各项参数，一边实时回答着“待机时间多久？”、“拍照效果怎么样？”这些弹幕提问，并在几万人的共同见证下，喊出“3、2、1，上链接！”时，那一刻，它已经超越了单纯的“卖货”，变成了一场极具参与感的线上狂欢。这就是直播电商的魅力。\n\n### 11.1.3 直播电商的三种主流模式\n\n理解了直播电商的价值和内核后，作为产品经理，我的下一步就是从顶层设计上，思考我们平台到底要做哪一种。在我的实践中，通常会遇到三种主流的业务模式。\n\n![image-20250724195004429](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195004429.png)\n\n1.  **KOL带货模式**\n    这是最典型、爆发力最强的一种。如果我的业务目标是在短期内快速提升品牌知名度、引爆一款单品的销量，那么与外部的头部KOL合作，无疑是最佳选择。他们带来海量粉丝，我们提供优质商品，这是一场强强联合。\n\n    ![image-20250724195028134](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195028134.png)\n\n2.  **店铺直播模式（店播）**\n    这是一种更着眼于长期、健康的模式。我把它看作是平台必须为商家提供的“基础设施”。我们赋能平台上的商家，让他们可以在自己的“一亩三分地”里，由老板或者店员自己出镜，进行常态化的直播。这不追求一夜爆火，而是为了帮助商家更好地维护自己的老客、沉淀私域流量，是一种细水长流的生意。\n\n    ![image-20250724195103702](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195103702.png)\n\n3.  **直播分销模式**\n    这是一种最大化利用平台生态的、极具想象力的模式。它将直播和分销结合，允许我们的普通用户申请成为“分销主播”。平台提供统一的货盘，他们只需要开播去推广，就能赚取佣金。这相当于将我们平台上成千上万的用户，都变成了我们“行走的、会说话的”销售渠道。\n\n    ![image-20250724195113471](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195113471.png)\n\n\n-----\n\n## 11.2 直播电商的设计思路\n\n在上一节，我们明确了“为什么”要做直播电商。现在，我的角色就要从一个业务分析师，切换到一个产品架构师。在真正开始画原型、写文档之前，我必须先搭建起整个产品的“骨架”。这个过程，我称之为“设计思路”的梳理。\n\n### 11.2.1 核心角色与需求分析\n\n要设计一个好的系统，我首先要清晰地定义出：**这个系统里，都有谁？他们分别想做什么？** 这就是角色与需求分析。在直播电商这个场景里，我识别出了四个核心角色。\n\n1.  **普通用户**：他们是观众，是消费者。他们的核心诉求是“逛得开心，买得方便”。\n2.  **店铺主播**：他们是表演者，是销售员。他们是直播间的灵魂，核心诉求是“互动热烈，卖得更多”。\n3.  **店铺运营**：他们是幕后管理者。他们负责申请开通直播、管理直播计划、处理订单等。核心诉求是“管理高效，掌控全局”。\n4.  **平台**：这就是我们自己。我们的核心诉求是“秩序井然，生态繁荣”，需要有最高的管理权限。\n\n为了确保不遗漏任何关键功能，我会将这些角色的核心需求，整理成一张清晰的列表，作为我们后续产品设计的“需求清单”。\n\n| **角色** | **我的解读（核心需求点）** |\n| :--- | :--- |\n| **普通用户** | 1. 能流畅地观看直播，并与主播进行实时互动（如发弹幕、点赞）。<br>2. 能在直播间里，方便地查看正在讲解的商品，并快速下单购买。 |\n| **店铺运营** | 1. 需要有一个后台，可以向平台方，提交开通“店铺直播”功能的申请。<br>2. 对于已经创建或正在直播的场次，需要有管理和控制的能力。 |\n| **店铺主播** | 1. 能够在App内，轻松地发起一场直播，并能便捷地将自己店铺的商品，上架到直播间进行讲解。<br>2. 在直播过程中，能看到观众的互动，并进行回应，以提升直播间热度。 |\n| **平台** | 作为系统的所有者，我们需要有能力对所有店铺的直播间，进行统一的管理和监控，确保合规。 |\n\n### 11.2.2 核心业务流程梳理\n\n当我把这些零散的需求点都定义清楚后，下一步，就是用一条“流程线”，将它们串联起来，形成一个完整的业务闭环。我需要确保不同角色之间的协作是顺畅的。\n\n我通常会用一张“泳道图”来可视化这个核心流程，让团队里的每一个人都能清晰地看到，自己负责的部分，在整个业务链条中所处的位置。\n\n![image-20250724195621927](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195621927.png)\n\n这个流程是这样运转的：\n\n1.  一切的起点，是“**店铺运营**”向“**平台**”提交了开通直播的申请。\n2.  “**平台**”审核通过后，该店铺就获得了直播的能力。\n3.  “**店铺主播**”现在可以正式“**发起直播**”，并将准备好的“**上架商品**”。\n4.  海量的“**普通用户**”被吸引进入直播间“**观看直播**”，并在主播的带动下完成“**下单**”。\n5.  最后，订单流转到“**店铺运营**”那里，由他们进行“**确认订单**”和后续的履约发货。\n\n你看，通过这样一张流程图，一个完整的、多角色协作的业务故事，就被清晰地呈现了出来。\n\n### 11.2.3 整体功能架构规划\n\n有了角色和流程，我就可以在脑海中，勾勒出整个产品的“功能架构蓝图”了。\n\n我会把需要开发的功能，按照使用者的不同，划分到不同的“端”里去。\n\n![image-20250724195732918](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195732918.png)\n\n我将整个直播电商系统，规划为三大功能模块：\n\n  * **用户端**：这是我们产品的主阵地，承载了最多的功能。它既包含了“**普通用户**”的观看、互动、购买功能，也包含了“**主播**”开播、管理商品等核心功能。*（在这里，我暂时将主播端和用户端合并在一起考虑，因为它们都发生在同一个App内，很多界面是共通的）*。\n  * **商家端**：这就是我为“**店铺运营**”人员，所设计的后台管理系统。他们在这里申请权限、管理直播间。\n  * **平台端**：这是我们自己使用的“**上帝后台**”。在这里，我们可以管理所有商家和直播间，设定平台的规则。\n\n至此，直播电商的设计思路就已经非常清晰了。我们明确了“**为谁设计**”（核心角色）、“**设计什么**”（需求列表）、以及“**它们如何协同工作**”（业务流程和功能架构）。这个清晰的骨架，将是我们下一节进行具体产品功能设计的坚实基础。\n\n\n\n\n---\n\n## 11.3 直播电商的产品设计\n\n在我们梳理清楚了设计思路、明确了“要做什么”之后，现在，就到了将蓝图转化为具体页面的阶段。作为产品经理，我会兵分三路，同时推进**平台端、商家端、用户端**这三个关键阵地的产品设计。\n\n### 11.3.1 平台端：规则的制定者与秩序的守护者\n\n我设计平台后台的唯一原则，就是“**权责对等**”。平台作为整个直播生态的“所有者”，必须拥有至高无上的管理权限，来确保整个业务健康、有序地运转。这主要体现在两个方面：**管店铺**和**管直播**。\n\n**1. 直播店铺管理**\n\n我们必须有一个“准入机制”。并非所有商家都有资格开通直播，否则劣质的直播内容会摧毁用户体验。因此，我需要为平台的运营同事，设计一个强大的店铺审核后台。\n\n![image-20250725095520168](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095520168.png)\n\n这个后台的核心，就是对“**资格状态**”的精细化管理。运营人员在这里，可以清晰地看到所有申请店铺的列表，并进行“**审核**”、“**查看**”、“**取消资格**”或“**恢复资格**”等操作。每一个按钮，都代表了平台的一种管理权力，是确保直播商家质量的第一道防线。\n\n**2. 直播间管理**\n\n除了管“人”（店铺），我们更要管“事”（直播）。平台需要能够监控到所有正在发生和已经发生的直播。\n\n![image-20250725095618366](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095618366.png)\n\n在这个界面，我最看重的，是“**操作**”栏里的“**结束**”按钮。这代表了平台的“**干预权**”。当一场直播出现违规内容或其他紧急情况时，平台必须有能力在第一时间，从最高权限上，强制将其关停。这是我们作为平台方，必须承担的责任，也是保障平台安全的生命线。\n\n### 11.3.2 商家端：商户的运营指挥中心\n\n对于商家而言，直播是他们最重要的营销工具和销售渠道之一。因此，我为他们设计的商家后台，必须像一个“**作战指挥室**”，专业、高效、功能完备。\n\n**1. 申请与配置**\n\n商家的直播之旅，始于“**申请**”。我需要为他们提供一个清晰的申请入口，并明确告知他们需要满足的条件，这既是功能，也是一种规则的宣导。\n\n![image-20250725095657234](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095657234.png)\n\n当商家获得资格后，他们就需要一个专业的“**直播间管理**”后台。在这里，他们可以创建、编辑、管理自己所有的直播场次。\n\n![image-20250725095735548](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095735548.png)\n\n\n\n![image-20250725100501358](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100501358.png)\n\n我设计的核心思路是“**状态驱动**”。你会发现，一场直播在“未开始”、“直播中”、“已结束”等不同状态下，商家可以进行的操作是完全不同的。比如，“未开始”的可以“编辑”，而“已结束”的只能“查看数据”。这种精细化的权限控制，能有效防止商家的误操作。\n\n**2. 数据复盘**\n\n直播的魅力，在于可以通过数据不断优化。一场直播结束后，商家最关心的问题就是：“**这场直播效果怎么样？**”。如果我不能回答这个问题，那么我设计的这个功能就是失败的。\n\n![image-20250725095756930](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095756930.png)\n\n因此，我必须为商家提供一个详尽的“**数据战报**”。这个战报至少要包含三类核心数据：\n* **流量数据**：有多少人看？最高同时有多少人在线？涨了多少粉？\n* **互动数据**：谁给我刷了礼物？价值多少？\n* **带货数据**：卖了什么商品？卖了多少件？\n\n只有提供了这些数据，商家才能进行有效的复盘，我们的直播功能才算真正为商家创造了价值。\n\n### 11.3.3 用户端：主播与观众的互动舞台\n\n用户端，是整个直播产品的“门面”，是所有用户能直接感知到的地方。我把它分为两条主线来设计：**主播的“开播”之旅**，和**观众的“看播”之旅**。\n\n**1. 主播的开播之旅**\n\n我设计主播端的核心理念是“**简单高效，所见即所得**”。主播在手机方寸之间，就要完成一场直播的全部准备工作。\n\n* **第一步：设置直播信息**\n    一场直播的“门面”，就是封面和标题。我必须让主播可以轻松地上传一张吸引人的封面图，并起一个有噱头的标题。此外，“**立即开始**”和“**预定时间**”这两个选项也至关重要。“预定时间”能让主播提前预告，进行蓄水，这是专业运营的必备功能。\n\n    ![image-20250725100613314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100613314.png)\n\n\n​\t![image-20250725100636104](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100636104.png)\n* **第二步：关联带货商品**\n    这是直播电商的“灵魂”。我需要为主播提供一个极为便捷的“**选品**”流程，让他们能从自己的店铺商品库中，快速勾选出本场要带货的商品，并添加到直播间的“小黄车”里。\n\n    ![image-20250725100708800](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100708800.png)\n\n* **第三步：直播中的掌控**\n    当直播开始后，主播的手机屏幕就变成了他的“**驾驶舱**”。美颜、滤镜、镜头翻转这些是基础功能，能让主播呈现出最好的状态。更重要的是，他需要有管理商品、与观众互动等一系列工具。\n\n    ![image-20250725100747775](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100747775.png)\n\n**2. 观众的看播之旅**\n\n我设计观众端的核心理念是“**沉浸体验，无缝下单**”。我要让用户看得开心，买得顺滑。\n\n* **核心互动界面**\n    用户进入直播间，首先看到的是一个集“**视频画面**”和“**实时互动区**”于一体的界面。下方的聊天弹幕区是营造社区感和热闹氛围的关键，让用户感觉自己不是一个人在看，而是在和成千上万的人一起“云逛街”。\n\n    ![image-20250725100818156](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100818156.png)\n\n* **商品浏览与购买**\n    当主播开始介绍商品时，我必须为用户提供一个清晰、无干扰的商品展示区。这个区域通常在屏幕下方，以列表形式呈现。用户点击后，无需跳出直播间，就能查看商品详情并完成购买。\n\n    ![image-20250725100846494](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100846494.png)\n\n    这里的设计要点在于商品“**状态**”的实时同步。当主播讲解某个商品时，它的状态可能是“**待上架**”；当主播喊出“上链接”时，它会立刻变为“**马上抢**”；而当商品售罄时，它又会变为“**已抢完**”。这种实时的状态变化，是制造稀缺感、激发用户下单欲望的关键所在。\n\n\n\n---\n\n\n## 11.4 直播电商的关键技术\n\n在完成了产品的“长相”（用户界面）和“骨架”（功能逻辑）设计之后，我必须和技术团队坐下来，探讨它的“内脏和血脉”——也就是实现这一切所需要的技术。\n\n作为产品经理，我不需要会写代码，但我必须理解其核心原理。这能让我评估技术方案的可行性、预估开发成本，并在关键的技术选型上，与团队进行有质量的对话。\n\n### 11.4.1 核心概念：推流与拉流\n\n整个复杂的直播技术，可以被简化为两个最核心的动作：“**推流**”和“**拉流**”。\n\n* **推流**：我把它理解为“**上传直播**”的过程。它指的是主播的手机端（直播端）采集自己的声音和画面，并将其像水流一样，“推”送到云端服务器的行为。\n* **拉流**：我把它理解为“**下载直播**”的过程。它指的是成千上万的观众，从云端服务器那里，将直播内容“拉”取到自己手机上进行观看的行为。\n\n一次流畅的直播体验，本质上就是一次高质量的“推”和成千上万次高质量的“拉”所共同构成的。\n\n### 11.4.2 直播的技术全景图\n\n在“推”与“拉”之间，是一个庞大而精密的后台服务系统。为了让团队清晰地理解这个系统，我通常会展示这样一张技术架构图。\n\n![image-20250725101657009](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725101657009.png)\n\n我可以带你走一遍这个流程：\n1.  **主播端（推送方）**：一切的源头是主播。我们App会集成一个“**推流SDK**”，它就像一个专业的打包和邮寄工具，负责将主播的音视频内容采集、压缩，然后通过“**推流节点**”，发送到最近的云服务器。\n2.  **服务端（处理中心）**：这是直播的“中央厨房”。“**直播服务器**”接收到主播的推流后，会立刻进行一系列的加工处理，例如：\n    * **转码服务**：为了适配不同观众的网络状况，服务器会将原始视频流，实时转码成高清、标清、流畅等多个版本。\n    * **录制服务**：服务器会将整场直播，录制成一个视频文件（VOD），方便用户随时回顾。\n    * **截图服务**：自动截取直播的精彩瞬间作为封面。\n    * **安全服务**：对直播内容进行实时监控，防止违规。\n3.  **观众端（拉取方）**：经过处理的直播流，会被分发到全球的“**CDN分发节点**”。这就像是遍布全球的“前置仓库”。当观众打开App时，他们的“**播放SDK**”会自动连接到离他们最近的CDN节点，去“拉取”直播内容。这样，无论用户身在何处，都能获得低延迟、高流畅的观看体验。\n\n### 11.4.3 产品经理的技术选型：自研 vs. 第三方SDK\n\n了解到这套系统的复杂性后，一个关键的决策就摆在了我的面前：**这套系统，我们是自己从零开始搭建，还是直接采购成熟的方案？**\n\n![image-20250725101806254](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725101806254.png)\n\n我的答案，以及我给几乎所有公司的建议都是：**果断选择第三方。**\n\n原因很简单：作为一家电商公司，我们的核心竞争力在于“交易”而非“底层视频技术”。自研一套稳定、高并发、低延迟的全球直播系统，其投入是天文数字。聪明的产品决策，是“**站在巨人的肩膀上**”。\n\n市面上有非常多专业、成熟的云服务商，提供完整的视频直播解决方案。我们只需要将他们的SDK集成到我们的产品中，就能在短时间内，以可控的成本，上线高质量的直播功能。\n\n在做技术选型时，我会和技术负责人一起，重点考察几家头部厂商，例如：\n* **阿里云**：它的视频直播（[阿里云直播服务](https://www.aliyun.com/product/live)）服务，在国内市场份额巨大，技术稳定，文档齐全。\n* **网易云信**：网易云信（[网易云信直播服务](https://yunxin.163.com/live)）在社交、娱乐领域的解决方案经验丰富，尤其在IM（即时通讯）和音视频的结合上很有优势。\n* **腾讯云**：腾讯云的互动直播解决方案（[腾讯云直播服务](https://cloud.tencent.com/solution/ilvb)），尤其强调“互动连麦”等场景，非常适合需要强社交属性的直播玩法。\n\n最终，我们会根据他们的产品性能、功能丰富度、服务支持以及价格等多个维度，综合评估，选择最适合我们当前业务需求的合作伙伴。"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E7%AB%A0%EF%BC%9A%E5%94%AE%E5%90%8E%E7%AE%A1%E7%90%86"><span class="toc-number">1.</span> <span class="toc-text">第七章：售后管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#7-1-%E5%94%AE%E5%90%8E%E5%9C%BA%E6%99%AF%E6%9E%84%E5%BB%BA"><span class="toc-number">1.1.</span> <span class="toc-text">7.1 售后场景构建</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%B8%8D%E5%90%8C%E8%A7%92%E8%89%B2%E7%9A%84%E5%85%B3%E6%B3%A8%E7%82%B9"><span class="toc-number">1.1.1.</span> <span class="toc-text">1. 不同角色的关注点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E6%A0%B8%E5%BF%83%E5%94%AE%E5%90%8E%E5%9C%BA%E6%99%AF%E6%8F%90%E7%82%BC"><span class="toc-number">1.1.2.</span> <span class="toc-text">2. 核心售后场景提炼</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%94%AE%E5%90%8E%E8%81%8C%E8%B4%A3%E4%B8%8E%E9%9C%80%E6%B1%82%E6%8F%90%E5%8F%96"><span class="toc-number">1.1.3.</span> <span class="toc-text">3. 售后职责与需求提取</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-2-%E5%94%AE%E5%90%8E%E6%B5%81%E7%A8%8B%E5%88%86%E6%9E%90"><span class="toc-number">1.2.</span> <span class="toc-text">7.2 售后流程分析</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E5%BE%85%E4%BB%98%E6%AC%BE%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95"><span class="toc-number">1.2.1.</span> <span class="toc-text">1. 场景一：待付款取消订单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E5%BE%85%E5%8F%91%E8%B4%A7%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95"><span class="toc-number">1.2.2.</span> <span class="toc-text">2. 场景二：待发货取消订单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9A%E5%BE%85%E6%94%B6%E8%B4%A7-%E5%B7%B2%E5%AE%8C%E6%88%90-%E9%80%80%E6%AC%BE%E9%80%80%E8%B4%A7"><span class="toc-number">1.2.3.</span> <span class="toc-text">3. 场景三：待收货/已完成 - 退款退货</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E5%9C%BA%E6%99%AF%E5%9B%9B%EF%BC%9A%E5%BE%85%E6%94%B6%E8%B4%A7-%E5%B7%B2%E5%AE%8C%E6%88%90-%E7%94%B3%E8%AF%B7%E6%8D%A2%E8%B4%A7"><span class="toc-number">1.2.4.</span> <span class="toc-text">4. 场景四：待收货/已完成 - 申请换货</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-%E5%95%86%E5%AE%B6%E7%AB%AF%E4%B8%8E%E5%B9%B3%E5%8F%B0%E7%AB%AF%E7%9A%84%E5%94%AE%E5%90%8E%E7%AE%A1%E7%90%86%E9%A1%B5%E9%9D%A2"><span class="toc-number">1.2.5.</span> <span class="toc-text">5.商家端与平台端的售后管理页面</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-3-%E4%BA%A4%E6%98%93%E7%BA%A0%E7%BA%B7%E5%A4%84%E7%90%86"><span class="toc-number">1.3.</span> <span class="toc-text">7.3 交易纠纷处理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%B9%B3%E5%8F%B0%E4%BB%8B%E5%85%A5%E7%9A%84%E5%8E%9F%E5%88%99%E4%B8%8E%E6%97%B6%E6%9C%BA"><span class="toc-number">1.3.1.</span> <span class="toc-text">1. 平台介入的原则与时机</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E4%BA%A4%E6%98%93%E7%BA%A0%E7%BA%B7%E5%A4%84%E7%90%86%E6%B5%81%E7%A8%8B%E4%B8%8E%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.2.</span> <span class="toc-text">2. 交易纠纷处理流程与功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E8%AF%81%E6%8D%AE%E9%93%BE%E6%9D%A1%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.3.</span> <span class="toc-text">3. 证据链条设计</span></a></li></ol></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%85%AB%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0-%E7%A7%8D%E8%8D%89%E7%AE%A1%E7%90%86"><span class="toc-number">2.</span> <span class="toc-text">第八章：电商后台 - 种草管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#8-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">2.1.</span> <span class="toc-text">8.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-2-%E8%AF%9D%E9%A2%98%E5%88%86%E7%B1%BB%E4%B8%8E%E8%AF%9D%E9%A2%98%E7%AE%A1%E7%90%86"><span class="toc-number">2.2.</span> <span class="toc-text">8.2 话题分类与话题管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-1-%E8%AF%9D%E9%A2%98%E5%88%86%E7%B1%BB%E7%AE%A1%E7%90%86"><span class="toc-number">2.2.1.</span> <span class="toc-text">8.2.1 话题分类管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-2-%E8%AF%9D%E9%A2%98%E7%AE%A1%E7%90%86"><span class="toc-number">2.2.2.</span> <span class="toc-text">8.2.2 话题管理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-3-%E7%A7%8D%E8%8D%89%E5%86%85%E5%AE%B9%E4%B8%8E%E8%AF%84%E8%AE%BA%E7%AE%A1%E7%90%86"><span class="toc-number">2.3.</span> <span class="toc-text">8.3 种草内容与评论管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-1-%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8%E7%AD%96%E7%95%A5"><span class="toc-number">2.3.1.</span> <span class="toc-text">8.3.1 内容审核策略</span></a></li></ol></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B9%9D%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0-%E8%B4%A2%E5%8A%A1%E7%AE%A1%E7%90%86"><span class="toc-number">3.</span> <span class="toc-text">第九章：电商后台 - 财务管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#9-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">3.1.</span> <span class="toc-text">9.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#9-2-%E6%B8%85%E7%AE%97%E4%B8%8E%E7%BB%93%E7%AE%97"><span class="toc-number">3.2.</span> <span class="toc-text">9.2 清算与结算</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%E5%AE%9A%E4%B9%89%EF%BC%88%E6%B8%85%E7%AE%97-vs-%E7%BB%93%E7%AE%97%EF%BC%89"><span class="toc-number">3.2.1.</span> <span class="toc-text">9.2.1 核心概念定义（清算 vs 结算）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-2-%E8%B5%84%E9%87%91%E4%B8%8E%E4%BF%A1%E6%81%AF%E6%B5%81%E7%A8%8B%E5%88%86%E6%9E%90"><span class="toc-number">3.2.2.</span> <span class="toc-text">9.2.2 资金与信息流程分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-3-%E6%A0%B8%E5%BF%83%E5%90%88%E8%A7%84%E9%97%AE%E9%A2%98%EF%BC%9A%E2%80%9C%E4%BA%8C%E6%B8%85%E2%80%9D"><span class="toc-number">3.2.3.</span> <span class="toc-text">9.2.3 核心合规问题：“二清”</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-4-%E5%B9%B3%E5%8F%B0%E5%AF%B9%E8%B4%A6%E7%AE%A1%E7%90%86"><span class="toc-number">3.2.4.</span> <span class="toc-text">9.2.4 平台对账管理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#9-3-%E8%B4%A2%E5%8A%A1%E7%AE%A1%E7%90%86"><span class="toc-number">3.3.</span> <span class="toc-text">9.3 财务管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-1-%E5%95%86%E5%AE%B6%E8%B4%A6%E5%8D%95%E4%B8%8E%E6%8F%90%E7%8E%B0%E7%AE%A1%E7%90%86"><span class="toc-number">3.3.1.</span> <span class="toc-text">9.3.1 商家账单与提现管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%95%86%E5%AE%B6%E7%AB%AF%E8%AE%BE%E8%AE%A1"><span class="toc-number">3.3.1.1.</span> <span class="toc-text">1. 商家端设计</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%BE%E8%AE%A1"><span class="toc-number">3.3.1.2.</span> <span class="toc-text">2. 平台端设计</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-2-%E5%B9%B3%E5%8F%B0%E6%8A%BD%E4%BD%A3%E4%B8%8E%E8%AE%A1%E8%B4%B9%E8%A7%84%E5%88%99"><span class="toc-number">3.3.2.</span> <span class="toc-text">9.3.2 平台抽佣与计费规则</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-3-%E5%8F%91%E7%A5%A8%E7%AE%A1%E7%90%86"><span class="toc-number">3.3.3.</span> <span class="toc-text">9.3.3 发票管理</span></a></li></ol></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E7%AB%A0%EF%BC%9A%E5%88%86%E9%94%80%E7%94%B5%E5%95%86"><span class="toc-number">4.</span> <span class="toc-text">第十章：分销电商</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#10-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">4.1.</span> <span class="toc-text">10.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-2-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-number">4.2.</span> <span class="toc-text">10.2 分销电商项目背景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E5%81%9A%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-number">4.2.1.</span> <span class="toc-text">1. 为什么要做分销电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E7%9A%84%E6%A0%B8%E5%BF%83%E9%9C%80%E6%B1%82"><span class="toc-number">4.2.2.</span> <span class="toc-text">2. 分销电商的核心需求</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-1-%E4%BB%80%E4%B9%88%E6%98%AF%E5%88%86%E9%94%80%E7%94%B5%E5%95%86"><span class="toc-number">4.2.3.</span> <span class="toc-text">10.2.1 什么是分销电商</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-2-%E6%A0%B8%E5%BF%83%E8%A7%92%E8%89%B2%E5%AE%9A%E4%B9%89%EF%BC%88%E4%BE%9B%E8%B4%A7%E5%95%86%E3%80%81%E5%88%86%E9%94%80%E5%95%86%E3%80%81%E6%B6%88%E8%B4%B9%E8%80%85%EF%BC%89"><span class="toc-number">4.2.4.</span> <span class="toc-text">10.2.2 核心角色定义（供货商、分销商、消费者）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-3-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E7%9A%84%E4%BC%98%E5%8A%BF"><span class="toc-number">4.3.</span> <span class="toc-text">10.3 分销电商的优势</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-1-%E4%BD%8E%E6%88%90%E6%9C%AC%E5%BF%AB%E9%80%9F%E8%A3%82%E5%8F%98"><span class="toc-number">4.3.1.</span> <span class="toc-text">10.3.1 低成本快速裂变</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-2-%E5%BC%BA%E4%BF%A1%E4%BB%BB%E5%85%B3%E7%B3%BB%E8%BD%AC%E5%8C%96"><span class="toc-number">4.3.2.</span> <span class="toc-text">10.3.2 强信任关系转化</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-3-%E8%BD%BB%E8%B5%84%E4%BA%A7%E8%BF%90%E8%90%A5"><span class="toc-number">4.3.3.</span> <span class="toc-text">10.3.3 轻资产运营</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-4-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E6%90%AD%E5%BB%BA%E6%80%9D%E8%B7%AF"><span class="toc-number">4.4.</span> <span class="toc-text">10.4 分销电商搭建思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-1-%E5%88%86%E9%94%80%E5%91%98%E4%BD%93%E7%B3%BB%E8%AE%BE%E8%AE%A1"><span class="toc-number">4.4.1.</span> <span class="toc-text">10.4.1 分销员体系设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-2-%E5%95%86%E5%93%81%E4%B8%8E%E4%BE%9B%E5%BA%94%E9%93%BE%E7%AE%A1%E7%90%86"><span class="toc-number">4.4.2.</span> <span class="toc-text">10.4.2 商品与供应链管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-3-%E4%BD%A3%E9%87%91%E4%B8%8E%E7%BB%93%E7%AE%97%E4%BD%93%E7%B3%BB"><span class="toc-number">4.4.3.</span> <span class="toc-text">10.4.3 佣金与结算体系</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-5-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">4.5.</span> <span class="toc-text">10.5 分销电商产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-1-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-number">4.5.1.</span> <span class="toc-text">10.5.1 平台端核心功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-2-%E5%95%86%E5%AE%B6%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-number">4.5.2.</span> <span class="toc-text">10.5.2 商家端核心功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-3-%E5%88%86%E9%94%80%E5%95%86%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-number">4.5.3.</span> <span class="toc-text">10.5.3 分销商端核心功能</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-6-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">4.6.</span> <span class="toc-text">10.6 本章总结</span></a></li></ol></li><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E4%B8%80%E7%AB%A0%EF%BC%9A%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86"><span class="toc-number">5.</span> <span class="toc-text">第十一章：直播电商</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#11-1-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-number">5.1.</span> <span class="toc-text">11.1 直播电商项目背景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-1-%E4%B8%BA%E4%BB%80%E4%B9%88%E9%9C%80%E8%A6%81%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-number">5.1.1.</span> <span class="toc-text">11.1.1 为什么需要直播电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-2-%E5%88%B0%E5%BA%95%E4%BB%80%E4%B9%88%E6%98%AF%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-number">5.1.2.</span> <span class="toc-text">11.1.2 到底什么是直播电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-3-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E4%B8%89%E7%A7%8D%E4%B8%BB%E6%B5%81%E6%A8%A1%E5%BC%8F"><span class="toc-number">5.1.3.</span> <span class="toc-text">11.1.3 直播电商的三种主流模式</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-2-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-number">5.2.</span> <span class="toc-text">11.2 直播电商的设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-1-%E6%A0%B8%E5%BF%83%E8%A7%92%E8%89%B2%E4%B8%8E%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">5.2.1.</span> <span class="toc-text">11.2.1 核心角色与需求分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-2-%E6%A0%B8%E5%BF%83%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B%E6%A2%B3%E7%90%86"><span class="toc-number">5.2.2.</span> <span class="toc-text">11.2.2 核心业务流程梳理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-3-%E6%95%B4%E4%BD%93%E5%8A%9F%E8%83%BD%E6%9E%B6%E6%9E%84%E8%A7%84%E5%88%92"><span class="toc-number">5.2.3.</span> <span class="toc-text">11.2.3 整体功能架构规划</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-3-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">5.3.</span> <span class="toc-text">11.3 直播电商的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-1-%E5%B9%B3%E5%8F%B0%E7%AB%AF%EF%BC%9A%E8%A7%84%E5%88%99%E7%9A%84%E5%88%B6%E5%AE%9A%E8%80%85%E4%B8%8E%E7%A7%A9%E5%BA%8F%E7%9A%84%E5%AE%88%E6%8A%A4%E8%80%85"><span class="toc-number">5.3.1.</span> <span class="toc-text">11.3.1 平台端：规则的制定者与秩序的守护者</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-2-%E5%95%86%E5%AE%B6%E7%AB%AF%EF%BC%9A%E5%95%86%E6%88%B7%E7%9A%84%E8%BF%90%E8%90%A5%E6%8C%87%E6%8C%A5%E4%B8%AD%E5%BF%83"><span class="toc-number">5.3.2.</span> <span class="toc-text">11.3.2 商家端：商户的运营指挥中心</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-3-%E7%94%A8%E6%88%B7%E7%AB%AF%EF%BC%9A%E4%B8%BB%E6%92%AD%E4%B8%8E%E8%A7%82%E4%BC%97%E7%9A%84%E4%BA%92%E5%8A%A8%E8%88%9E%E5%8F%B0"><span class="toc-number">5.3.3.</span> <span class="toc-text">11.3.3 用户端：主播与观众的互动舞台</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-4-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E5%85%B3%E9%94%AE%E6%8A%80%E6%9C%AF"><span class="toc-number">5.4.</span> <span class="toc-text">11.4 直播电商的关键技术</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%EF%BC%9A%E6%8E%A8%E6%B5%81%E4%B8%8E%E6%8B%89%E6%B5%81"><span class="toc-number">5.4.1.</span> <span class="toc-text">11.4.1 核心概念：推流与拉流</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-2-%E7%9B%B4%E6%92%AD%E7%9A%84%E6%8A%80%E6%9C%AF%E5%85%A8%E6%99%AF%E5%9B%BE"><span class="toc-number">5.4.2.</span> <span class="toc-text">11.4.2 直播的技术全景图</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-3-%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E7%9A%84%E6%8A%80%E6%9C%AF%E9%80%89%E5%9E%8B%EF%BC%9A%E8%87%AA%E7%A0%94-vs-%E7%AC%AC%E4%B8%89%E6%96%B9SDK"><span class="toc-number">5.4.3.</span> <span class="toc-text">11.4.3 产品经理的技术选型：自研 vs. 第三方SDK</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>