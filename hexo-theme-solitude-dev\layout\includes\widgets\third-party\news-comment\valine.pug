script.
    (() => {
        const emojiReg = /:[a-z0-9_\u4e00-\u9fa5]+:/g
        const changeContent = (content) => {
            if (content === '') return content;

            const replacements = [
                {regex: /<img.*?src="(.*?)"?[^\>]+>/ig, replacement: '[!{_p("console.newest_comment.image")}]'},
                {
                    regex: /<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi,
                    replacement: '[!{_p("console.newest_comment.link")}]'
                },
                {regex: /```[\s\S]*?```/g, replacement: '[!{_p("console.newest_comment.code")}]'},
                {regex: /<[^>]+>/g, replacement: ""}
            ];

            content = replacements.reduce((str, {regex, replacement}) => str.replace(regex, replacement), content);

            return content.length > 150 ? content.substring(0, 100) + '...' : content;
        }

        const $asideList = document.querySelector('.card-recent-comment .aside-list')
        const newestCommentInit = () => {
            if (!document.querySelector('.card-recent-comment')) return
            const data = utils.saveToLocal.get('valine-newest-comment')
            if (data) {
                generateHtml(data)
            } else {
                getComment()
            }
        }

        const getComment = async () => {
            const settings = {
                "method": "GET",
                "headers": {
                    "X-LC-Id": '!{theme.valine.appId}',
                    "X-LC-Key": '!{theme.valine.appKey}',
                    "Content-Type": "application/json"
                },
            }
            if (typeof md5 === "undefined") await utils.getScript('!{url_for(theme.cdn.blueimp_md5)}')
            await fetch('!{theme.valine.serverURLs}/1.1/classes/Comment?limit=8&order=-createdAt', settings).then(res => res.json())
                .then(async result => {
                    let ls = []
                    for (const i of result.results.slice(0, !{limit})) {
                        if (emojiReg.test(i.comment)) continue
                        ls.push({
                            content: changeContent(i.comment),
                            avatar: '!{theme.comment.avatar}' + '/avatar/' + md5(i.mail.toLowerCase()),
                            nick: i.nick,
                            url: i.url,
                            date: i.updatedAt || i.createdAt
                        })
                    }
                    utils.saveToLocal.set('valine-newest-comment', ls, !{theme.comment.newest_comment.storage})
                    generateHtml(ls)
                }).catch(error => {
                    console.error(error);
                    $asideList.textContent = "!{_p('newest_comment.error')}"
                })
        }

        const generateHtml = array => {
            const $dom = document.querySelector('.card-recent-comment .aside-list')
            $dom.innerHTML = array.length ? array.map(item => `
                        <div class='aside-list-item'>
                          <div onclick='pjax.loadUrl(\"${item.url}\")' class='thumbnail'>
                            <img src='${item.avatar}' alt='${item.nick}'>
                            <div class='name'><span>${item.nick}</span></div>
                          </div>
                          <div class='content'>
                            <div class='comment' onclick='pjax.loadUrl("${item.url}")'>${item.content}</div>
                            <time class="datetime" datetime="${item.date}"></time>
                          </div>
                        </div>
                      `).join('') : "!{_p('newest_comment.zero')}"
            window.lazyLoadInstance?.update()
            window.pjax?.refresh()
            sco?.changeTimeFormat(document.querySelectorAll('.aside-list-item time'))
        }

        window.addEventListener('DOMContentLoaded', newestCommentInit, false)
        window.addEventListener('pjax:complete', newestCommentInit, false);
    })()