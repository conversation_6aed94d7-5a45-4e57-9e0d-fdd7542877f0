.category-bar-items#category-bar-items(class=is_home() ? 'home' : '')
    .category-bar-item(class=is_home() ? 'select' : '', id="category-bar-home")
        a(href=url_for('/'))= __('home.home')
    .category-bar-item
        a(href=url_for('/archives/'))= __('home.archives')
    each item in site.categories.find({ parent: { $exists: false } }).data
        .category-bar-item(class=select ? (select === item.name ? 'select' : '') : '', id=item.name)
            a(href=url_for(item.path))= item.name
div.category-bar-right
    - let categories = site.categories.find({parent: {$exists: false}})
    - let word_count = categories.map(category => category.name).join('').length
    if word_count > 40 || categories.data.length > 10
        .category-bar-next#category-bar-next(onclick="sco.scrollCategoryBarToRight()")
            i.solitude.fas.fa-angles-right
    if theme.page.categories
        a.category-bar-more(href=url_for('/categories/'))= __('more')
