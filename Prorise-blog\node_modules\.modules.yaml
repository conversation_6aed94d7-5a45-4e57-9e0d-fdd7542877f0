hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.3.3':
    '@adobe/css-tools': private
  '@algolia/cache-browser-local-storage@4.20.0':
    '@algolia/cache-browser-local-storage': private
  '@algolia/cache-common@4.20.0':
    '@algolia/cache-common': private
  '@algolia/cache-in-memory@4.20.0':
    '@algolia/cache-in-memory': private
  '@algolia/client-account@4.20.0':
    '@algolia/client-account': private
  '@algolia/client-analytics@4.20.0':
    '@algolia/client-analytics': private
  '@algolia/client-common@4.20.0':
    '@algolia/client-common': private
  '@algolia/client-personalization@4.20.0':
    '@algolia/client-personalization': private
  '@algolia/client-search@4.20.0':
    '@algolia/client-search': private
  '@algolia/logger-common@4.20.0':
    '@algolia/logger-common': private
  '@algolia/logger-console@4.20.0':
    '@algolia/logger-console': private
  '@algolia/requester-browser-xhr@4.20.0':
    '@algolia/requester-browser-xhr': private
  '@algolia/requester-common@4.20.0':
    '@algolia/requester-common': private
  '@algolia/requester-node-http@4.20.0':
    '@algolia/requester-node-http': private
  '@algolia/transporter@4.20.0':
    '@algolia/transporter': private
  '@asamuzakjp/css-color@3.2.0':
    '@asamuzakjp/css-color': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/types@7.28.0':
    '@babel/types': private
  '@csstools/color-helpers@5.0.2':
    '@csstools/color-helpers': private
  '@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-calc': private
  '@csstools/css-color-parser@3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-color-parser': private
  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.4':
    '@csstools/css-tokenizer': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  a-sync-waterfall@1.0.1:
    a-sync-waterfall: private
  abbrev@2.0.0:
    abbrev: private
  acorn@7.4.1:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  algoliasearch@4.20.0:
    algoliasearch: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@2.2.1:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@1.3.2:
    anymatch: private
  aplayer@1.10.1:
    aplayer: private
  archy@1.0.0:
    archy: private
  argparse@2.0.1:
    argparse: private
  arr-diff@2.0.0:
    arr-diff: private
  arr-flatten@1.1.0:
    arr-flatten: private
  arr-union@3.1.0:
    arr-union: private
  array-unique@0.2.1:
    array-unique: private
  asap@2.0.6:
    asap: private
  assert-never@1.4.0:
    assert-never: private
  assign-symbols@1.0.0:
    assign-symbols: private
  async-each@1.0.6:
    async-each: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  atob@2.1.2:
    atob: private
  babel-polyfill@6.26.0:
    babel-polyfill: private
  babel-runtime@6.26.0:
    babel-runtime: private
  babel-walk@3.0.0-canary-5:
    babel-walk: private
  balanced-match@1.0.2:
    balanced-match: private
  balloon-css@0.5.2:
    balloon-css: private
  base@0.11.2:
    base: private
  basic-auth@2.0.1:
    basic-auth: private
  binary-extensions@1.13.1:
    binary-extensions: private
  bluebird@3.7.2:
    bluebird: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bytes@3.1.2:
    bytes: private
  cache-base@1.0.1:
    cache-base: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  camel-case@4.1.2:
    camel-case: private
  chalk@1.1.3:
    chalk: private
  character-parser@2.2.0:
    character-parser: private
  cheerio-select@2.1.0:
    cheerio-select: private
  chokidar@1.7.0:
    chokidar: private
  class-utils@0.3.6:
    class-utils: private
  clean-css@4.2.4:
    clean-css: private
  collection-visit@1.0.0:
    collection-visit: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  command-exists@1.2.9:
    command-exists: private
  commander@2.20.3:
    commander: private
  component-emitter@1.3.1:
    component-emitter: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.0:
    compression: private
  concat-map@0.0.1:
    concat-map: private
  connect@3.7.0:
    connect: private
  constantinople@4.0.1:
    constantinople: private
  copy-descriptor@0.1.1:
    copy-descriptor: private
  core-js@2.6.12:
    core-js: private
  core-util-is@1.0.3:
    core-util-is: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-select@5.2.2:
    css-select: private
  css-what@6.2.2:
    css-what: private
  cssstyle@4.6.0:
    cssstyle: private
  cuid@2.1.8:
    cuid: private
  data-urls@5.0.0:
    data-urls: private
  debug@2.6.9:
    debug: private
  decimal.js@10.6.0:
    decimal.js: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  deepmerge@4.3.1:
    deepmerge: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  define-property@2.0.2:
    define-property: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  doctypes@1.1.0:
    doctypes: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  dompurify@3.2.6:
    dompurify: private
  domutils@3.2.2:
    domutils: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  encodeurl@2.0.0:
    encodeurl: private
  encoding-sniffer@0.2.1:
    encoding-sniffer: private
  ent@2.2.2:
    ent: private
  entities@4.5.0:
    entities: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@1.0.5:
    escape-string-regexp: private
  esprima@4.0.1:
    esprima: private
  etag@1.8.1:
    etag: private
  expand-brackets@0.1.5:
    expand-brackets: private
  expand-range@1.8.2:
    expand-range: private
  extend-shallow@3.0.2:
    extend-shallow: private
  extglob@0.3.2:
    extglob: private
  fast-equals@3.0.3:
    fast-equals: private
  filelist@1.0.4:
    filelist: private
  filename-regex@2.0.1:
    filename-regex: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.1.2:
    finalhandler: private
  for-in@1.0.2:
    for-in: private
  for-own@0.1.5:
    for-own: private
  form-data@4.0.3:
    form-data: private
  fragment-cache@0.2.1:
    fragment-cache: private
  fresh@0.5.2:
    fresh: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@1.2.13:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-value@2.0.6:
    get-value: private
  glob-base@0.3.0:
    glob-base: private
  glob-parent@2.0.0:
    glob-parent: private
  glob@7.2.3:
    glob: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-ansi@2.0.0:
    has-ansi: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-value@1.0.0:
    has-value: private
  has-values@1.0.0:
    has-values: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hexo-bunyan@1.0.0:
    hexo-bunyan: private
  hexo-cli@4.3.2:
    hexo-cli: private
  hexo-front-matter@1.0.0:
    hexo-front-matter: private
  hexo-fs@3.1.0:
    hexo-fs: private
  hexo-i18n@2.0.0:
    hexo-i18n: private
  hexo-log@0.2.0:
    hexo-log: private
  hexo-pagination@3.0.0:
    hexo-pagination: private
  highlight.js@11.11.1:
    highlight.js: private
  html-encoding-sniffer@4.0.0:
    html-encoding-sniffer: private
  html-minifier@4.0.0:
    html-minifier: private
  htmlparser2@10.0.0:
    htmlparser2: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  iconv-lite@0.6.3:
    iconv-lite: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  is-accessor-descriptor@1.0.1:
    is-accessor-descriptor: private
  is-binary-path@1.0.1:
    is-binary-path: private
  is-buffer@1.1.6:
    is-buffer: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-descriptor@1.0.1:
    is-data-descriptor: private
  is-descriptor@1.0.3:
    is-descriptor: private
  is-docker@2.2.1:
    is-docker: private
  is-dotfile@1.0.3:
    is-dotfile: private
  is-equal-shallow@0.1.3:
    is-equal-shallow: private
  is-expression@4.0.0:
    is-expression: private
  is-extendable@1.0.1:
    is-extendable: private
  is-extglob@1.0.0:
    is-extglob: private
  is-glob@2.0.1:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-posix-bracket@0.1.1:
    is-posix-bracket: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-primitive@2.0.0:
    is-primitive: private
  is-promise@2.2.2:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  jake@10.9.2:
    jake: private
  js-stringify@1.0.2:
    js-stringify: private
  js-yaml-js-types@1.0.1(js-yaml@4.1.0):
    js-yaml-js-types: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdom@25.0.1:
    jsdom: private
  jsonparse@1.3.1:
    jsonparse: private
  jstransformer@1.0.0:
    jstransformer: private
  kind-of@3.2.2:
    kind-of: private
  lower-case@1.1.4:
    lower-case: private
  lru-cache@10.4.3:
    lru-cache: private
  luxon@3.6.1:
    luxon: private
  map-cache@0.2.2:
    map-cache: private
  map-visit@1.0.0:
    map-visit: private
  marked@15.0.12:
    marked: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  math-random@1.0.4:
    math-random: private
  meting@1.2.0:
    meting: private
  micro-memoize@4.1.3:
    micro-memoize: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@3.0.0:
    mime: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mixin-deep@1.3.2:
    mixin-deep: private
  mkdirp@0.5.6:
    mkdirp: private
  moize@6.1.6:
    moize: private
  moment-timezone@0.5.48:
    moment-timezone: private
  moment@2.30.1:
    moment: private
  morgan@1.10.0:
    morgan: private
  ms@2.0.0:
    ms: private
  mv@2.1.1:
    mv: private
  nanomatch@1.2.13:
    nanomatch: private
  ncp@2.0.0:
    ncp: private
  negotiator@0.6.4:
    negotiator: private
  nib@1.2.0(stylus@0.62.0):
    nib: private
  no-case@2.3.2:
    no-case: private
  normalize-path@3.0.0:
    normalize-path: private
  nth-check@2.1.1:
    nth-check: private
  nunjucks@3.2.4(chokidar@3.6.0):
    nunjucks: private
  nwsapi@2.2.20:
    nwsapi: private
  object-assign@4.1.1:
    object-assign: private
  object-copy@0.1.0:
    object-copy: private
  object-visit@1.0.1:
    object-visit: private
  object.omit@2.0.1:
    object.omit: private
  object.pick@1.3.0:
    object.pick: private
  on-finished@2.3.0:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  open@8.4.2:
    open: private
  param-case@2.1.1:
    param-case: private
  parse-glob@3.0.4:
    parse-glob: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5-parser-stream@7.1.2:
    parse5-parser-stream: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  pascal-case@3.1.2:
    pascal-case: private
  pascalcase@0.1.1:
    pascalcase: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  posix-character-classes@0.1.1:
    posix-character-classes: private
  preserve@0.2.0:
    preserve: private
  pretty-hrtime@1.0.3:
    pretty-hrtime: private
  prismjs@1.30.0:
    prismjs: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  promise-polyfill@7.1.0:
    promise-polyfill: private
  promise@7.3.1:
    promise: private
  pug-attrs@3.0.0:
    pug-attrs: private
  pug-code-gen@3.0.3:
    pug-code-gen: private
  pug-error@2.1.0:
    pug-error: private
  pug-filters@4.0.0:
    pug-filters: private
  pug-lexer@5.0.1:
    pug-lexer: private
  pug-linker@4.0.0:
    pug-linker: private
  pug-load@3.0.0:
    pug-load: private
  pug-parser@6.0.0:
    pug-parser: private
  pug-runtime@3.0.1:
    pug-runtime: private
  pug-strip-comments@2.0.0:
    pug-strip-comments: private
  pug-walk@2.0.0:
    pug-walk: private
  pug@3.0.3:
    pug: private
  punycode@1.4.1:
    punycode: private
  randomatic@3.1.1:
    randomatic: private
  range-parser@1.2.1:
    range-parser: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@2.2.1:
    readdirp: private
  regenerator-runtime@0.10.5:
    regenerator-runtime: private
  regex-cache@0.4.4:
    regex-cache: private
  regex-not@1.0.2:
    regex-not: private
  relateurl@0.2.7:
    relateurl: private
  remove-trailing-separator@1.1.0:
    remove-trailing-separator: private
  repeat-element@1.1.4:
    repeat-element: private
  repeat-string@1.6.1:
    repeat-string: private
  resolve-url@0.2.1:
    resolve-url: private
  resolve@1.22.10:
    resolve: private
  ret@0.1.15:
    ret: private
  rfdc@1.4.1:
    rfdc: private
  rimraf@2.4.5:
    rimraf: private
  rrweb-cssom@0.7.1:
    rrweb-cssom: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-json-stringify@1.2.0:
    safe-json-stringify: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-regex@1.1.0:
    safe-regex: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.3.0:
    sax: private
  saxes@6.0.0:
    saxes: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  set-value@2.0.1:
    set-value: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  smoothscroll@0.4.0:
    smoothscroll: private
  snapdragon-node@2.1.1:
    snapdragon-node: private
  snapdragon-util@3.0.1:
    snapdragon-util: private
  snapdragon@0.8.2:
    snapdragon: private
  source-map-resolve@0.5.3:
    source-map-resolve: private
  source-map-url@0.4.1:
    source-map-url: private
  source-map@0.6.1:
    source-map: private
  split-string@3.1.0:
    split-string: private
  sprintf-js@1.1.3:
    sprintf-js: private
  static-extend@0.1.2:
    static-extend: private
  statuses@1.5.0:
    statuses: private
  stream-to-array@2.3.0:
    stream-to-array: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-indent@3.0.0:
    strip-indent: private
  striptags@3.2.0:
    striptags: private
  stylus@0.62.0:
    stylus: private
  supports-color@2.0.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  symbol-tree@3.2.4:
    symbol-tree: private
  text-table@0.2.0:
    text-table: private
  through2@4.0.2:
    through2: private
  tildify@2.0.0:
    tildify: private
  titlecase@1.1.3:
    titlecase: private
  tldts-core@6.1.86:
    tldts-core: private
  tldts@6.1.86:
    tldts: private
  to-object-path@0.3.0:
    to-object-path: private
  to-regex-range@5.0.1:
    to-regex-range: private
  to-regex@3.0.2:
    to-regex: private
  toidentifier@1.0.1:
    toidentifier: private
  token-stream@1.0.0:
    token-stream: private
  tough-cookie@5.1.2:
    tough-cookie: private
  tr46@5.1.1:
    tr46: private
  tslib@2.8.1:
    tslib: private
  uglify-js@3.10.4:
    uglify-js: private
  undici@7.11.0:
    undici: private
  union-value@1.0.1:
    union-value: private
  unpipe@1.0.0:
    unpipe: private
  unset-value@1.0.0:
    unset-value: private
  upper-case@1.1.3:
    upper-case: private
  urix@0.1.0:
    urix: private
  use@3.1.1:
    use: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  vary@1.1.2:
    vary: private
  void-elements@3.1.0:
    void-elements: private
  w3c-xmlserializer@5.0.0:
    w3c-xmlserializer: private
  warehouse@5.0.1:
    warehouse: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@14.2.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  with@7.0.2:
    with: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.3:
    ws: private
  xml-name-validator@5.0.0:
    xml-name-validator: private
  xmlchars@2.2.0:
    xmlchars: private
ignoredBuilds:
  - hexo-util
  - core-js
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.8.0
pendingBuilds: []
prunedAt: Sat, 26 Jul 2025 15:19:23 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmmirror.com/
skipped:
  - bindings@1.5.0
  - file-uri-to-path@1.0.0
  - fsevents@1.2.13
  - fsevents@2.3.3
  - nan@2.22.2
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\个人博客搭建\Prorise-blog\node_modules\.pnpm
virtualStoreDirMaxLength: 60
