if hexo-config('highlight.enable')
  @import "color"
  @import "theme/" + hexo-config('highlight.theme')

if $highlight_enable
  @require "highlight/index.styl"

if $prismjs_enable
  @require "prismjs/index.styl"

figure
  &:hover
    box-shadow var(--efu-shadow-border-hover)

  .copy-btn
    position absolute
    top 1rem
    right 1rem
    z-index 10
    color var(--efu-fontcolor)
    cursor pointer
    transition all .2s ease 0s

    &:hover
      color var(--efu-lighttext)

  &.highlight
    background-color var(--efu-hl-bg)
    color var(--efu-fontcolor)
    border var(--style-border-always)
    overflow hidden
    border-radius 8px 8px 4px 4px
    position relative
    margin .5rem 0 .5rem
    padding 0
    line-height 1.6

    .highlight-tools
      position relative
      display flex
      -webkit-box-align center
      align-items center
      overflow hidden
      min-height 1.2rem
      border-radius 8px 8px 0 0
      height 2.15em
      font-size 16px
      background var(--efu-hltools-bg)
      border-bottom var(--style-border-always)
      color var(--efu-fontcolor)

      &.closed
        .expand
          transition all .3s ease 0s

        & ~ *
          display none

      .copy-button
        position absolute
        cursor pointer
        transition color .2s
        font-size 18px

      .copy-button:hover
        color var(--efu-main)

      .expand
        position absolute
        padding 0.4rem 0.3rem
        cursor pointer
        transition transform .3s ease 0s

      .code-lang
        text-transform capitalize
        position absolute
        font-weight 700
        font-size 1.15em
        user-select none

      .caption
        position absolute
        right 3.5rem
        font-size 14px
        color var(--efu-secondtext)

        +maxWidth768()
          display none

    .code-expand-btn
      background var(--efu-hltools-bg)
      transition .3s
      backdrop-filter saturate(180%) blur(20px)
      transform translateZ(0)
      border-top var(--style-border-always)
      position absolute
      bottom 0
      z-index 1
      width 100%
      text-align center
      font-size 16px
      cursor pointer
      display flex
      align-items center
      justify-content center
      height 32px

      i
        color var(--efu-fontcolor)
        line-height 1
        animation 1.2s ease 0s infinite normal none running code-expand-key

      &:hover
        background var(--efu-main)

        i
          color var(--efu-white)

      &.expand-done
        display none

      &:not(.expand-done)
        & ~ table,
        & ~ pre
          overflow: hidden
          height: unit(hexo-config('highlight.limit'), px)