include ../widgets/page/banner
if theme.brevity.enable
    #bber
        section.timeline.page-1
            ul.list.waterfall
                each item in site.data.brevity.slice(0, theme.brevity.strip)
                    li.item
                        if theme.brevity.style === 2
                            .meta
                                img.avatar(src=theme.aside.my_card.author.img)
                                .info
                                    span.bber_nick= config.author
                                    time.datetime.bber_date(datetime=moment(item.date).format())
                                if theme.comment.use && item.content
                                    a.bber-reply.goComment(onclick=`sco.toTalk('${item.content}')`)
                                        i.solitude.fas.fa-comment

                        #bber-content
                            p.datacont= item.content
                            if item.image
                                .bber-content-img
                                    each img in item.image
                                        if typeof img === 'string'
                                            img(src=img, alt=item.content || "图片暂无描述")
                                        else
                                            img(src=img.url, alt=(img.alt || item.content || "图片暂无描述"))

                        if item.aplayer
                            .bber-music
                                meting-js(server=item.aplayer.server type="song" id=item.aplayer.id mutex="true" preload="none" theme="var(--efu-main)" data-lrctype="0")

                        if item.video
                            .bber-video
                                if item.video.player
                                    video(src=item.video.player controls="controls" style="object-fit: cover;")
                                if item.video.bilibili
                                    iframe(src='//player.bilibili.com/player.html?autoplay=0&bvid=' + item.video.bilibili scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true")

                        if theme.brevity.style === 1
                            hr
                            .bber-bottom
                                .bber-info
                                    .bber-info-time
                                        i.solitude.fas.fa-calendar-day
                                        time.datetime(datetime=moment(item.date).format())
                                    if item.location
                                        .bber-info-location
                                            i.solitude.fas.fa-location-dot
                                            = item.location
                                    if item.link
                                        a.bber-content-link(href=url_for(item.link) target="_blank")
                                            i.solitude.fas.fa-link
                                            = _p('essay.link')
                                if theme.comment.use && item.content
                                    a.bber-reply(onclick=`sco.toTalk('${item.content}')`)
                                        i.solitude.fas.fa-comment
            #bber-tips
                if theme.brevity.strip === -1
                    = _p('essay.tip0')
                else
                    = _p('essay.tip1').replace('#{count}', theme.brevity.strip)
