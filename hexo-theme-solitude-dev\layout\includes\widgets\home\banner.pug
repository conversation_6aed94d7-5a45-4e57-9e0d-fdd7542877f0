- const { title, desc, icon} = theme.hometop.banner

div.banners-title
    div.banners-title-big!= title
    if typeof(desc) === 'object'
        div.banners-title-small
        script.
            var home_subtitle = !{JSON.stringify(desc || [])};
    else
        div.banners-title-small= desc

- var group = theme.hometop.group
if group
    div.banners-links
        each value,label in group
            - var array = value.split('||')
            a.banners-link-btn(href=url_for(trim(array[0])) style=`background-image: ${array[2]} `)
                if array[1]
                    i.solitude(class=array[1])
                .banners-link-title= label

if icon
    div.tags-group-all
        .tags-group-wrapper
            each i in [1,2]
                - var keys = Object.keys(icon)
                - var pairs = keys.map((val, i) => i % 2 ? [keys[i - 1], val] : []).filter(x => x.length)
                each pair in pairs
                    .tags-group-icon-pair
                        each key in pair
                            .tags-group-icon(style=`background: ${icon[key].color}`)
                                if icon[key].img
                                    img.nolazyload(src=icon[key].img, title=key)
                                else if icon[key].icon
                                    i(class=icon[key].icon, style=`color: ${icon[key].icon_color ? icon[key].icon_color : ''}`)