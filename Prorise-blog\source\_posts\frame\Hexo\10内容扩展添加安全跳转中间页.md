---
title: 10.内容扩展：添加“安全跳转”中间页
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 24286
date: 2025-07-11 02:13:45
---

### **10.内容扩展：添加“安全跳转”中间页**

###### **前言：功能介绍与必要性**

本指南将引导您使用 `hexo-safego` 插件，为您博客的所有外部链接（即指向其他网站的链接）添加一个“安全跳转”中间页。

当访客点击一个外部链接时，会先跳转到这个由您完全控制的中间页。这个页面的主要作用是：
1.  **风险提示**：提醒访客他们即将离开您的网站，前往一个您无法保证其安全性的外部站点。
2.  **免责声明**：作为站长，您尽到了告知义务，避免因友链等网站出现问题而对您的博客声誉造成影响。
3.  **提升专业度**：规范的跳转流程让博客显得更专业、更负责。

###### **核心流程概览**
1.  **安装插件**：安装 `hexo-safego` 及其前置依赖。
2.  **配置插件**：在您的站点配置文件 `_config.yml` 中添加并设置插件的各项参数。
3.  **（可选）进阶定制**：修改跳转页面的样式和布局。

---
###### **第一步：安装插件**

1.  **安装前置依赖 `cheerio`**
    * 该插件依赖 `cheerio` 来解析HTML。Hexo通常已内置此依赖，但为确保万无一失，可以先在您博客的根目录终端中执行安装：
    ```bash
    npm install cheerio --save
    ```

2.  **安装 `hexo-safego` 插件**
    * 接着，安装插件本体：
    ```bash
    npm install hexo-safego --save
    ```

---
###### **第二步：配置插件 (根目录 `_config.yml`)**

打开您博客**根目录**下的 `_config.yml` 文件，在文件末尾添加以下配置。这是一份包含所有常用选项的完整模板，您可以根据注释进行修改。

```yaml
# Hexo SafeGo 安全跳转插件配置
# --------------------------------------
hexo_safego:
  # --- 基本功能设置 ---
  general:
    enable: true                      # 【必填】插件总开关
    enable_base64_encode: true      # 【推荐】是否对链接进行Base64编码，让URL更干净，也能轻微防止爬虫
    enable_target_blank: true       # 【推荐】是否在新标签页中打开外链

  # --- 安全与路径设置 ---
  security:
    url_param_name: 'u'               # URL中的参数名, 例如 /go.html?u=...
    html_file_name: 'go.html'         # 跳转页面的文件名，可自定义
    ignore_attrs:                     # 忽略带有这些属性的链接，例如相册的链接
      - 'data-fancybox'

  # --- 生效范围设置 ---
  scope:
    apply_containers:                 # 指定插件在哪些HTML容器内生效
      - '#article-container'          # 默认为文章内容区，您可以添加其他选择器，如 '#aside-content'
    # apply_pages:                      # 【可选】只在特定路径下的页面生效，例如 ['/posts/']
    # exclude_pages:                    # 【可选】在哪些页面完全禁用此插件

  # --- 域名白名单 ---
  whitelist:
    domain_whitelist:                 # 【重要】白名单内的域名不会被跳转，请务必加入您自己的所有域名
      - "prorise.com"                 # <--- 请替换为您自己的主域名
      - "prorise666.site"             # <--- 您绑定的Waline/Twikoo域名
      - "waline.prorise666.site"      # <--- 所有您自己的、不希望跳转的域名

  # --- 跳转页面外观设置 ---
  appearance:
    avatar: '/img/user/avatar.webp'   # 【可选】跳转页面上显示的头像
    title: "Prorise 的博客"             # 【可选】跳转页面大标题
    subtitle: "安全中心"              # 【可选】跳转页面副标题
    darkmode: true                    # 【可选】是否为跳转页启用深色模式
    countdowntime: 5                  # 【可选】自动跳转的倒计时秒数，设置为 -1 则不自动跳转，需要用户手动点击
  
  # --- 调试设置 ---
  debug:
    enable: false                     # 调试模式，一般保持false。遇到问题时可开启，会输出详细处理日志
```

---
###### **（可选）第三步：进阶 - 自定义跳转页面**

如果您具备前端知识，并且希望深度定制跳转页面的外观，可以修改插件的源文件。

> **警告**：这属于高阶操作，会直接修改 `node_modules` 里的文件。当您未来通过 `npm update` 更新插件时，您的所有修改都**会被覆盖**。请在操作前知晓此风险。

1.  **找到文件**：
    `node_modules/hexo-safego/lib/go.html`

2.  **修改内容**：
    * 打开这个文件，您可以看到完整的HTML和CSS代码。
    * 作者已经将通用样式、夜间模式样式和白天模式样式分块写好，并添加了注释，方便您修改颜色、布局等。

对于大多数用户，我建议**只通过第二步的YAML配置**来修改标题和头像就足够了，除非您有强烈的定制需求。

---