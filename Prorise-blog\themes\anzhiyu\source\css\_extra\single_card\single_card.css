.author-content.author-content-item {
  height: 19rem;
  background-color: var(--anzhiyu-main);
  background-position: left 28%;
  background-repeat: no-repeat;
  background-size: cover;
  color: var(--anzhiyu-white);
  overflow: hidden;
  margin-top: 0;
  background-image: linear-gradient(-45deg, var(--anzhiyu-main), #0f4667, #2a6973 150%, #67044d);
  background-size: 400%;
  animation: gradient 15s ease infinite;
}
