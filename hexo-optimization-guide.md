
## 3. 推荐文章展示

### 目标说明
配置推荐文章功能，突出重要内容，提升内容曝光度。

### 操作步骤

**步骤1：在主题配置中启用推荐文章**
打开文件：`_config.anzhiyu.yml`

找到或添加推荐文章配置：
```yaml
# 推荐文章
recommend:
  enable: true
  # 推荐文章数量
  limit: 6
  # 推荐文章标题
  title: 推荐文章
  # 推荐文章排序方式: date, updated, random
  order_by: date
```

**步骤2：标记推荐文章**
在需要推荐的文章的 Front Matter 中添加 `sticky` 属性：

打开任意一篇文章，例如：`source/_posts/example-post.md`
```yaml
---
title: 示例文章标题
date: 2024-01-01 12:00:00
categories: 技术
tags: 
  - Hexo
  - 博客
sticky: 100  # 数值越大，优先级越高
---
```

**步骤3：配置首页推荐文章显示**
在 `_config.anzhiyu.yml` 中确保首页显示配置正确：
```yaml
# 首页设置
index:
  # 首页推荐文章
  top_post:
    enable: true
    # 推荐文章数量
    limit: 3
```

### 验证方法
```bash
hexo clean && hexo g && hexo s
```
访问首页，检查是否显示了推荐文章区域，并且设置了 `sticky` 属性的文章是否出现在推荐位置。

---

## 4. 分类层级优化

### 目标说明
重新规划文章分类结构，确保分类层次清晰，提升内容的可发现性。

### 操作步骤

**步骤1：规划分类结构**
建议的分类结构：
```
技术分享/
├── 前端开发
├── 后端开发
├── 工具使用
└── 学习笔记

生活随笔/
├── 日常生活
├── 读书笔记
└── 旅行记录

项目展示/
├── 个人项目
└── 开源贡献
```

**步骤2：配置分类页面**
打开文件：`_config.anzhiyu.yml`

确保分类页面配置正确：
```yaml
# 分类页面
category:
  enable: true
  # 分类页面布局
  layout: category
  # 是否显示文章数量
  show_count: true
  # 分类排序方式: name, length
  order_by: name
```

**步骤3：批量更新现有文章分类**
检查 `source/_posts/` 目录下的所有文章，统一分类格式：

```yaml
---
title: 文章标题
date: 2024-01-01
categories: 
  - 技术分享
  - 前端开发  # 子分类
tags:
  - JavaScript
  - Vue.js
---
```

**步骤4：创建分类页面**
如果还没有分类页面，创建：
```bash
hexo new page categories
```

编辑 `source/categories/index.md`：
```yaml
---
title: 分类
date: 2024-01-01
type: categories
layout: categories
---
```

### 验证方法
```bash
hexo clean && hexo g && hexo s
```
访问分类页面，检查分类结构是否清晰，文章是否正确归类。

---

## 5. 静态资源 CDN

### 目标说明
配置 CDN 加速静态资源加载，提升全球访问速度。

### 操作步骤

**步骤1：配置 CDN 设置**
打开文件：`_config.anzhiyu.yml`

找到或添加 CDN 配置：
```yaml
# CDN 设置
CDN:
  # 是否启用 CDN
  enable: true
  # CDN 提供商
  provider: jsdelivr  # 可选: jsdelivr, unpkg, cdnjs
  
  # 第三方库 CDN
  third_party:
    jquery: https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js
    vue: https://cdn.jsdelivr.net/npm/vue@next/dist/vue.global.prod.js
    
  # 主题资源 CDN
  theme:
    css: https://cdn.jsdelivr.net/npm/hexo-theme-anzhiyu@latest/source/css/
    js: https://cdn.jsdelivr.net/npm/hexo-theme-anzhiyu@latest/source/js/
```

**步骤2：配置图片 CDN（可选）**
如果有图床服务，可以配置：
```yaml
# 图片 CDN
image_cdn:
  enable: false
  # 图床地址
  url: https://your-image-cdn.com/
```

**步骤3：配置字体 CDN**
```yaml
# 字体 CDN
font_cdn:
  enable: true
  google_fonts: https://fonts.googleapis.com/
  # 或使用国内镜像
  # google_fonts: https://fonts.font.im/
```

### 验证方法
```bash
hexo clean && hexo g && hexo s
```
打开浏览器开发者工具，检查 Network 标签页，确认静态资源是否从 CDN 加载。

---

## 6. 插件配置优化

### 目标说明
检查并优化所有插件配置，移除不必要的插件，提升网站性能。

### 操作步骤

**步骤1：检查当前安装的插件**
```bash
npm list --depth=0
```

**步骤2：优化 hexo-neat 配置**
打开文件：`_config.yml`

找到或添加 hexo-neat 配置：
```yaml
# hexo-neat 压缩优化
neat_enable: true
neat_html:
  enable: true
  exclude:
neat_css:
  enable: true
  exclude:
    - '*.min.css'
neat_js:
  enable: true
  mangle: true
  output:
  compress:
  exclude:
    - '*.min.js'
```
