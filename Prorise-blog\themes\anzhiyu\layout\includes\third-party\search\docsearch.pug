- const { appId, apiKey, indexName, option } = theme.docsearch

.docsearch-wrap
  #docsearch(style="display:none")
  link(rel="stylesheet" href=url_for(theme.asset.docsearch_css))
  script(src=url_for(theme.asset.docsearch_js))
  script.
    (() => {
      docsearch(Object.assign({
        appId: '!{appId}',
        apiKey: '!{apiKey}',
        indexName: '!{indexName}',
        container: '#docsearch',
      }, !{JSON.stringify(option)}))

      const handleClick = () => {
        document.querySelector('.DocSearch-Button').click()
      }

      const searchClickFn = () => {
        anzhiyu.addEventListenerPjax(document.querySelector('#search-button > .search'), 'click', handleClick)
      }

      searchClickFn()
      window.addEventListener('pjax:complete', searchClickFn)
    })()
