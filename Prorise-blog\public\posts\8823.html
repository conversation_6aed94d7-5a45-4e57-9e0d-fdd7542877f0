<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理进阶（一）：第一章：电商基础 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理进阶（一）：第一章：电商基础"><meta name="application-name" content="产品经理进阶（一）：第一章：电商基础"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理进阶（一）：第一章：电商基础"><meta property="og:url" content="https://prorise666.site/posts/8823.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第一章：电商基础欢迎来到我们关于电商产品管理的新篇章。电子商务，无疑是过去二十年间，最深刻地改变了我们生活方式和商业形态的领域之一。作为这个领域的从业者，我们就是现代数字商场的“建筑师”。 在第一章，我将带大家一起，为我们即将开始的“建筑”生涯，打下最坚实的地基。我们将从零售最本质的源头开始，理解我"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp"><meta name="description" content="第一章：电商基础欢迎来到我们关于电商产品管理的新篇章。电子商务，无疑是过去二十年间，最深刻地改变了我们生活方式和商业形态的领域之一。作为这个领域的从业者，我们就是现代数字商场的“建筑师”。 在第一章，我将带大家一起，为我们即将开始的“建筑”生涯，打下最坚实的地基。我们将从零售最本质的源头开始，理解我"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/8823.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理进阶（一）：第一章：电商基础",postAI:"true",pageFillDescription:"第一章：电商基础, 1.1 学习目标, 1.2 零售行业的发展, 1.2.1 传统零售, 1.2.2 电子商务, 1.3 零售三要素, 1.3.1 核心概念：人、货、场, 1.3.2 零售三要素在电商的体现, 1. 人在电商的体现（如账号、用户画像）, 2. 货在电商的体现（商品形式与来源）, 3. 场在电商的体现（平台构建与整合）, 1.4 电商平台运营模式, 1.4.1 自营模式, 1.4.2 招商模式, 1.4.3 联营模式, 1.4.4 混合模式, 1.5 电商平台交易模式, 1.5.1 B2B（企业对企业）, 1.5.2 B2C（企业对个人）, 1.5.3 C2C（个人对个人）, 1.5.4 F2C（工厂对个人）等, 1.6 电商平台盈利模式, 1.6.1 销售收入, 1.6.2 广告收入, 1.6.3 平台服务费, 1. 店铺租金, 2. 技术服务费, 3. 交易提成, 1.7 本章总结第一章电商基础欢迎来到我们关于电商产品管理的新篇章电子商务无疑是过去二十年间最深刻地改变了我们生活方式和商业形态的领域之一作为这个领域的从业者我们就是现代数字商场的建筑师在第一章我将带大家一起为我们即将开始的建筑生涯打下最坚实的地基我们将从零售最本质的源头开始理解我们所处的这个波澜壮阔的行业学习目标在本章中我的核心目标是帮助我们建立起对电子商务领域的全局性结构化的认知我们将一起追溯零售行业的发展脉络掌握驱动所有商业运转的零售三要素并清晰地辨别当前主流电商平台的各种运营交易及盈利模式零售行业的发展要理解什么是电子商务我们必须先理解什么是商务也就是零售在我看来电子商务并非一个全新的物种而是传统零售在数字时代的一次伟大进化传统零售传统零售指的是所有以实体门店为主要交易场所的商业活动它的形态可以是我们楼下的小杂货店可以是市中心的百货商店也可以是大型的连锁超市和购物中心它的本质是一手交钱一手交货的面对面交易它的核心限制是它无法摆脱时间和空间的束缚一家商店必须在固定的营业时间时间限制在固定的地理位置空间限制才能服务于能够到达这里的顾客电子商务电子商务则是以互联网为媒介进行商品交易的全新零售形态它的形态是我们手机里无处不在的淘宝京东拼多多等购物它的本质是通过信息流资金流物流的线上整合来完成交易它的核心突破是它彻底打破了传统零售在时间和空间上的限制我可以在任何时间深夜或凌晨任何地点家里或路上购买到来自世界各地的商品正是这一根本性的突破创造出了一个无比巨大的全新的数字商业世界也为我们产品经理提供了施展才华的广阔舞台我将这两者的核心区别总结在下面的表格里这能帮助我们更清晰地理解这次进化零售形态交易场所核心限制典型代表传统零售实体门店受限于时间和空间连锁超市百货商场电子商务互联网平台打破了时间和空间的限制淘宝京东拼多多零售三要素我们已经清楚了传统零售和电子商务的区别那么一个更深层次的问题是抛开线上线下的外在形式所有交易这件事有没有一些共通的永恒不变的内在要素答案是肯定的在我看来所有零售的底层逻辑都可以被拆解为三个最基本的核心要素掌握了它们就等于掌握了理解一切商业模式的钥匙核心概念人货场这三个永恒不变的核心要素我称之为零售三要素人即我们的消费者用户货即我们所销售的商品或服务场即交易发生的场景或场所从传统零售到电子商务的进化并不是发明了新的要素而是用信息化的手段对这三个要素的形态进行了一次彻底的颠覆性的重构下面这张表格是我认为对这场重构最精辟的总结零售形态人货场核心基础传统零售面对面的顾客实体商品实体门店依赖实体电子商务账号商品信息线上平台信息化零售三要素在电商的体现现在我们来深入理解这信息化重构后的新人新货新场对我们产品经理来说具体意味着什么人在电商的体现如账号用户画像在电商的世界里人不再是商店里一个个模糊的面孔而是被数字化为了一个唯一的账号这个账号是我们识别理解服务用户的数字容器它不仅包含了用户的手机号昵称年龄性别等基础信息更重要的是它记录了用户在我们平台的一切行为数据他浏览过什么搜索过什么购买过什么收藏过什么我正是通过分析这些账号数据才能为用户建立起精准的用户画像从而为他提供个性化的商品推荐和购物体验货在电商的体现商品形式与来源在电商的世界里货不再是货架上可以触摸的实体而是被数字化为了详尽的商品信息我作为产品经理最重要的工作之一就是设计好承载这些信息的商品详情页我需要通过组合运用文字描述高清图片展示视频用户评价等多种信息形态在用户无法亲身接触到商品的情况下最大化地还原商品的真实样貌和价值激发用户的购买欲望场在电商的体现平台构建与整合在电商的世界里场不再是具体的物理门店而是我们所构建的线上平台本身从我们的首页分类页搜索结果页到商品详情页再到购物车和支付流程这整个用户走过的路径共同构成了这个全新的数字化的商场我作为产品经理就是这个商场的总设计师我的职责是确保这个商场的动线是流畅的导购是智能的收银是便捷的为每一个数字化的人提供一次完美的购买数字化货的旅程电商平台运营模式我们已经知道了电商的人货场是信息化的那么一个电商公司具体是如何组织和管理这信息化的人货场的呢这就由它的运营模式所决定在我看来要剖析任何一个电商平台的运营模式都离不开一个最经典最底层的商业框架进销存进商品是从哪里来的商品来源销商品是在哪里卖的销售渠道存商品由谁来仓储和配送仓储配送进销存这三个环节由谁来主导由谁来负责就共同决定了电商平台的四种主流运营模式自营模式自营模式我把它理解为线上品牌连锁超市平台自己既是超市老板也是唯一的售货员进货源平台自己负责采购商品或是通过买手团队向品牌方自采或是自己贴牌自产销销售平台搭建自己的电商网站或直接面向消费者进行销售存仓储平台自己建立仓库或租赁自己管理库存和订单履约自己负责或统一外包物流配送我的解读在这种模式下平台对商品价格服务体验有绝对的掌控权京东自营就是最典型的代表它的优点是能提供极致的用户体验和品质保障从而建立起强大的品牌信任但缺点也非常明显这是一个重资产模式平台需要承担巨大的采购成本和库存风险招商模式招商模式我把它理解为线上购物中心或集市平台是商场房东负责搭建和维护商场并吸引商家进来开店进货源平台的采购实际上是招商通过签约邀请成千上万的第三方商家入驻开店销销售所有商家共享平台这个统一的销售渠道存仓储商家各自负责自己的商品仓储打包和发货我的解读在这种模式下平台的核心是搭台子和定规则淘宝就是最典型的代表它的优点是轻资产运营可以快速地低成本地极大丰富平台上的商品数量但缺点是平台对商品质量和服务的掌控力较弱用户体验参差不齐联营模式联营模式是介于自营和招商之间的一种合作模式我称之为线上品牌专柜进货源与招商模式类似平台邀请品牌方或大型供应商入驻销销售同样在统一的平台上进行销售存仓储这是最关键的区别在联营模式下虽然货权依然属于品牌方但品牌方会将一部分或全部商品放入平台自建的仓库中由平台来统一负责仓储和配送我的解读这种模式最典型的就是天猫超市或京东上的大部分品牌旗舰店品牌方提供货但用户享受到的是平台如京东物流的配送服务它试图结合招商模式的货品丰富和自营模式的体验保障是一种强强联合的模式混合模式混合模式就是字面意思即平台同时运营着自营和招商联营业务我的解读如今几乎所有的电商巨头都是混合模式的运营者最典型的就是京东和亚马逊它们既有自营板块来保证核心品类的体验和口碑又有庞大的开放平台招商板块来丰富商品的多样性这能让它们最大化地满足不同用户的需求实现优势互补我将这四种模式的核心区别总结在下面的表格里运营模式平台角色核心优势核心劣势典型代表自营模式零售商体验品控物流极佳模式重成本高有限京东自营招商模式商场房东模式轻极其丰富体验品控难统一淘宝店联营模式代销商品控物流体验好较丰富运营模式复杂天猫京东旗舰店混合模式零售商房东优势互补覆盖面广内部资源协调复杂亚马逊京东电商平台交易模式在我们上一节学习的运营模式中我们关心的是平台如何组织货源和履约而在交易模式中我关心的是谁在和谁做生意一个电商平台的交易模式本质上是由商品卖家和商品买家这两方的角色属性来决定的我们常说的其实就是一种简写它的命名规则是卖方角色英文首字母买方角色英文首字母我们来详细拆解几种最主流的交易模式企业对企业即企业与企业之间的电子商务我把它理解为是将传统的企业间贸易批发采购等行为搬到了线上核心特征大批量低频次交易的订单金额通常很大但交易频次相对较低决策理性且复杂采购方通常需要经过内部的多部门如采购技术财务审批决策周期长非常理性价格非标流程复杂价格通常不是固定的需要经过报价询价订购等多个环节并涉及到复杂的合同发票和物流流程典型代表阿里巴巴它就是连接上游工厂供应商和下游零售商家的典型平台企业对个人即企业与个人消费者之间的电子商务这是我们日常生活中接触最多也最熟悉的一种模式核心特征小批量高频次用户通常是购买单件或少量商品但购买行为可能会非常频繁决策感性且迅速购买决策通常由个人做出很容易受到品牌营销促销评价等因素影响决策路径短价格标准体验为王商品价格是标准化的所有消费者看到的都一样平台竞争的核心在于品牌营销用户体验和客户服务典型代表天猫京东唯品会拼多多等都是典型的平台个人对个人即个人与个人之间的电子商务我把它理解为一个线上跳蚤市场或个人闲置物品交易集市核心特征平台是中介平台本身不卖货而是作为第三方为买卖双方提供一个信息发布在线沟通交易担保的场所依赖生态服务模式的成立强依赖于成熟的第三方支付工具如支付宝解决信任问题和物流公司解决物品交付问题非标品为主交易的商品大多是二手的非标准化的典型代表目前国内最成功的平台就是阿里的闲鱼工厂对个人等即工厂与个人消费者之间的电子商务这是一种旨在去中间化的模式核心特征短路经济它砍掉了传统零售中的经销商代理商等所有中间环节让消费者可以直接从工厂下单购买高性价比因为没有了中间商赚差价理论上能为消费者提供更低的价格模式它常常与用户直连制造模式结合即工厂根据用户的订单和需求来进行生产实现零库存典型代表必要就是模式的知名探索者我将这几种主流的交易模式总结在下面的表格中方便我们对比交易模式卖方买方核心特征典型代表企业企业订单量大决策复杂价格多为协商企业个人品牌化体验为王价格标准化天猫京东个人个人平台撮合依赖第三方支付与物流闲鱼工厂个人去中间化高性价比按需生产必要电商平台盈利模式我们前面讨论了电商的运营模式交易模式这些模式虽然千差万别但它们都服务于一个最终的共同的目的盈利作为产品经理我必须深刻地理解我所在平台的盈利模式因为这直接决定了我的工作重心和产品设计的方向我的每一个功能设计最终都应该直接或间接地为平台的盈利目标服务我们来看一下像天猫京东这些巨头它们是如何通过多种方式来构建自己强大的盈利能力的我将这些复杂的方式归纳为三大主流的盈利模式销售收入这是最直接也是最古老的盈利模式我把它简单地理解为赚差价核心逻辑这种模式主要应用于自营型电商平台首先以一个采购价从供应商那里把商品买断然后在自己的平台上以一个零售价卖给消费者这两者之间的差价扣除仓储物流人力等成本后就是平台的利润我的设计思考如果我所在平台的核心收入是销售收入那么我的产品设计就会高度聚焦于提升交易转化率和销售额比如我会重点优化商品推荐算法简化购物车到支付的流程设计各种优惠券和促销活动功能等一切为了让用户买得更多买得更快广告收入当一个平台拥有了巨大的用户流量那么它的流量本身就成了可以售卖的商品我把这种模式理解为卖流量或卖广告位核心逻辑这种模式是招商平台最核心的收入来源平台本身不靠卖货赚钱而是通过向希望在平台上获得曝光的商家出售广告位来盈利我的拓展设计广告产品的设计本身就是一个复杂的产品领域常见的广告形式包括关键词竞价商家对某个搜索关键词如运动鞋进行出价出价高者其商品就会排在搜索结果的前列这是淘宝天猫最核心的收入来源展示广告平台将首页的分类页的固定位置等作为广告位按时间或曝光量售卖给品牌商家信息流广告在猜你喜欢等个性化推荐信息流中穿插一些看起来像普通内容的广告商品平台服务费这也是招商平台平台模式的另一种重要收入我把它理解为收租金和佣金就像一个大型购物中心向入驻的品牌专柜收取各种费用一样我把它细分为以下几种店铺租金也叫平台年费商家需要每年向平台缴纳一笔固定的费用才能获得在平台上开设店铺的资格这就像是实体店的年租金技术服务费平台为商家提供了一整套复杂的开店交易管理数据分析的系统为此平台会收取一定的技术服务费作为软件系统和数据服务的费用交易提成这是最常见的一种方式平台会从商家的每一笔成功交易的流水中抽取一个固定比例的佣金通常在不等商家卖得越多平台赚得越多这种模式将平台和商家的利益进行了深度绑定本章总结到这里我们关于电商基础的学习就告一段落了我们来快速回顾一下本章的核心知识地图知识模块核心内容零售行业的发展我们理解了从传统零售到电子商务的进化是商业在时间和空间上的伟大突破零售三要素我们掌握了所有商业的底层逻辑人货场以及它们在电商时代如何被信息化为账号商品信息线上平台电商平台运营模式我们学习了通过进销存框架来区分自营招商联营混合这四种主流的运营模式电商平台交易模式我们学习了通过买卖双方的角色来区分等不同的交易模式电商平台盈利模式我们学习了电商最核心的三大盈利来源赚差价销售收入卖流量广告收入收佣金平台服务费掌握了这些最底层的概念和框架我们就拥有了一双能看透所有复杂电商产品的慧眼在下一章我们将开始进入更具体的实战设计中",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-26 21:16:27",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%9F%BA%E7%A1%80"><span class="toc-text">第一章：电商基础</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">1.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-2-%E9%9B%B6%E5%94%AE%E8%A1%8C%E4%B8%9A%E7%9A%84%E5%8F%91%E5%B1%95"><span class="toc-text">1.2 零售行业的发展</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-1-%E4%BC%A0%E7%BB%9F%E9%9B%B6%E5%94%AE"><span class="toc-text">1.2.1 传统零售</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-2-%E7%94%B5%E5%AD%90%E5%95%86%E5%8A%A1"><span class="toc-text">1.2.2 电子商务</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-3-%E9%9B%B6%E5%94%AE%E4%B8%89%E8%A6%81%E7%B4%A0"><span class="toc-text">1.3 零售三要素</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-3-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%EF%BC%9A%E4%BA%BA%E3%80%81%E8%B4%A7%E3%80%81%E5%9C%BA"><span class="toc-text">1.3.1 核心概念：人、货、场</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-3-2-%E9%9B%B6%E5%94%AE%E4%B8%89%E8%A6%81%E7%B4%A0%E5%9C%A8%E7%94%B5%E5%95%86%E7%9A%84%E4%BD%93%E7%8E%B0"><span class="toc-text">1.3.2 零售三要素在电商的体现</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E2%80%9C%E4%BA%BA%E2%80%9D%E5%9C%A8%E7%94%B5%E5%95%86%E7%9A%84%E4%BD%93%E7%8E%B0%EF%BC%88%E5%A6%82%E8%B4%A6%E5%8F%B7%E3%80%81%E7%94%A8%E6%88%B7%E7%94%BB%E5%83%8F%EF%BC%89"><span class="toc-text">1. “人”在电商的体现（如账号、用户画像）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E2%80%9C%E8%B4%A7%E2%80%9D%E5%9C%A8%E7%94%B5%E5%95%86%E7%9A%84%E4%BD%93%E7%8E%B0%EF%BC%88%E5%95%86%E5%93%81%E5%BD%A2%E5%BC%8F%E4%B8%8E%E6%9D%A5%E6%BA%90%EF%BC%89"><span class="toc-text">2. “货”在电商的体现（商品形式与来源）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E2%80%9C%E5%9C%BA%E2%80%9D%E5%9C%A8%E7%94%B5%E5%95%86%E7%9A%84%E4%BD%93%E7%8E%B0%EF%BC%88%E5%B9%B3%E5%8F%B0%E6%9E%84%E5%BB%BA%E4%B8%8E%E6%95%B4%E5%90%88%EF%BC%89"><span class="toc-text">3. “场”在电商的体现（平台构建与整合）</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-4-%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0%E8%BF%90%E8%90%A5%E6%A8%A1%E5%BC%8F"><span class="toc-text">1.4 电商平台运营模式</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-1-%E8%87%AA%E8%90%A5%E6%A8%A1%E5%BC%8F"><span class="toc-text">1.4.1 自营模式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-2-%E6%8B%9B%E5%95%86%E6%A8%A1%E5%BC%8F"><span class="toc-text">1.4.2 招商模式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-3-%E8%81%94%E8%90%A5%E6%A8%A1%E5%BC%8F"><span class="toc-text">1.4.3 联营模式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-4-%E6%B7%B7%E5%90%88%E6%A8%A1%E5%BC%8F"><span class="toc-text">1.4.4 混合模式</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-5-%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0%E4%BA%A4%E6%98%93%E6%A8%A1%E5%BC%8F"><span class="toc-text">1.5 电商平台交易模式</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-1-B2B%EF%BC%88%E4%BC%81%E4%B8%9A%E5%AF%B9%E4%BC%81%E4%B8%9A%EF%BC%89"><span class="toc-text">1.5.1 B2B（企业对企业）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-2-B2C%EF%BC%88%E4%BC%81%E4%B8%9A%E5%AF%B9%E4%B8%AA%E4%BA%BA%EF%BC%89"><span class="toc-text">1.5.2 B2C（企业对个人）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-3-C2C%EF%BC%88%E4%B8%AA%E4%BA%BA%E5%AF%B9%E4%B8%AA%E4%BA%BA%EF%BC%89"><span class="toc-text">1.5.3 C2C（个人对个人）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-4-F2C%EF%BC%88%E5%B7%A5%E5%8E%82%E5%AF%B9%E4%B8%AA%E4%BA%BA%EF%BC%89%E7%AD%89"><span class="toc-text">1.5.4 F2C（工厂对个人）等</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-6-%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0%E7%9B%88%E5%88%A9%E6%A8%A1%E5%BC%8F"><span class="toc-text">1.6 电商平台盈利模式</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-6-1-%E9%94%80%E5%94%AE%E6%94%B6%E5%85%A5"><span class="toc-text">1.6.1 销售收入</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-6-2-%E5%B9%BF%E5%91%8A%E6%94%B6%E5%85%A5"><span class="toc-text">1.6.2 广告收入</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-6-3-%E5%B9%B3%E5%8F%B0%E6%9C%8D%E5%8A%A1%E8%B4%B9"><span class="toc-text">1.6.3 平台服务费</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BA%97%E9%93%BA%E7%A7%9F%E9%87%91"><span class="toc-text">1. 店铺租金</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%8A%80%E6%9C%AF%E6%9C%8D%E5%8A%A1%E8%B4%B9"><span class="toc-text">2. 技术服务费</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%BA%A4%E6%98%93%E6%8F%90%E6%88%90"><span class="toc-text">3. 交易提成</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-7-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">1.7 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理进阶（一）：第一章：电商基础</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-24T08:13:45.000Z" title="发表于 2025-07-24 16:13:45">2025-07-24</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-26T13:16:27.107Z" title="更新于 2025-07-26 21:16:27">2025-07-26</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">5.2k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>15分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理进阶（一）：第一章：电商基础"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/8823.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/8823.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理进阶（一）：第一章：电商基础</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-24T08:13:45.000Z" title="发表于 2025-07-24 16:13:45">2025-07-24</time><time itemprop="dateCreated datePublished" datetime="2025-07-26T13:16:27.107Z" title="更新于 2025-07-26 21:16:27">2025-07-26</time></header><div id="postchat_postcontent"><h1 id="第一章：电商基础"><a href="#第一章：电商基础" class="headerlink" title="第一章：电商基础"></a>第一章：电商基础</h1><p>欢迎来到我们关于电商产品管理的新篇章。电子商务，无疑是过去二十年间，最深刻地改变了我们生活方式和商业形态的领域之一。作为这个领域的从业者，我们就是现代数字商场的“建筑师”。</p><p>在第一章，我将带大家一起，为我们即将开始的“建筑”生涯，打下最坚实的地基。我们将从零售最本质的源头开始，理解我们所处的这个波澜壮阔的行业。</p><h2 id="1-1-学习目标"><a href="#1-1-学习目标" class="headerlink" title="1.1 学习目标"></a>1.1 学习目标</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721152555400.png" alt="image-20250721152555400"></p><p>在本章中，我的核心目标是，帮助我们建立起对电子商务领域的全局性、结构化的认知。我们将一起追溯零售行业的发展脉络，掌握驱动所有商业运转的“零售三要素”，并清晰地辨别当前主流电商平台的各种运营、交易及盈利模式。</p><h2 id="1-2-零售行业的发展"><a href="#1-2-零售行业的发展" class="headerlink" title="1.2 零售行业的发展"></a>1.2 零售行业的发展</h2><p>要理解什么是“电子商务”，我们必须先理解什么是“商务”，也就是“零售”。在我看来，电子商务并非一个全新的物种，而是传统零售在数字时代的一次<strong>伟大进化</strong>。</p><h3 id="1-2-1-传统零售"><a href="#1-2-1-传统零售" class="headerlink" title="1.2.1 传统零售"></a>1.2.1 传统零售</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721152624549.png" alt="image-20250721152624549"></p><p><strong>传统零售</strong>，指的是所有<strong>以实体门店为主要交易场所</strong>的商业活动。</p><ul><li><strong>它的形态</strong>：可以是我们楼下的小<code>杂货店</code>，可以是市中心的<code>百货商店</code>，也可以是大型的<code>连锁超市</code>和<code>购物中心</code>。</li><li><strong>它的本质</strong>：是“一手交钱，一手交货”的面对面交易。</li><li><strong>它的核心限制</strong>：是它无法摆脱<strong>时间和空间</strong>的束缚。一家商店，必须在固定的营业时间（时间限制），在固定的地理位置（空间限制），才能服务于能够到达这里的顾客。</li></ul><h3 id="1-2-2-电子商务"><a href="#1-2-2-电子商务" class="headerlink" title="1.2.2 电子商务"></a>1.2.2 电子商务</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721152732714.png" alt="image-20250721152732714"></p><p><strong>电子商务（E-commerce）</strong>，则是<strong>以互联网为媒介进行商品交易</strong>的全新零售形态。</p><ul><li><strong>它的形态</strong>：是我们手机里无处不在的<code>淘宝</code>、<code>京东</code>、<code>拼多多</code>等购物App。</li><li><strong>它的本质</strong>：是通过信息流、资金流、物流的线上整合，来完成交易。</li><li><strong>它的核心突破</strong>：是它彻底<strong>打破了传统零售在时间和空间上的限制</strong>。我可以在任何时间（深夜或凌晨）、任何地点（家里或路上），购买到来自世界各地的商品。</li></ul><p>正是这一根本性的突破，创造出了一个无比巨大的、全新的数字商业世界，也为我们产品经理，提供了施展才华的广阔舞台。</p><hr><p>我将这两者的核心区别，总结在下面的表格里，这能帮助我们更清晰地理解这次“进化”。</p><table><thead><tr><th align="left"><strong>零售形态</strong></th><th align="left"><strong>交易场所</strong></th><th align="left"><strong>核心限制</strong></th><th align="left"><strong>典型代表</strong></th></tr></thead><tbody><tr><td align="left"><strong>传统零售</strong></td><td align="left">实体门店</td><td align="left"><strong>受限于时间和空间</strong></td><td align="left">连锁超市、百货商场</td></tr><tr><td align="left"><strong>电子商务</strong></td><td align="left">互联网平台</td><td align="left"><strong>打破了时间和空间的限制</strong></td><td align="left">淘宝、京东、拼多多</td></tr></tbody></table><hr><h2 id="1-3-零售三要素"><a href="#1-3-零售三要素" class="headerlink" title="1.3 零售三要素"></a>1.3 零售三要素</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721153351348.png" alt="image-20250721153351348"></p><p>我们已经清楚了传统零售和电子商务的区别。</p><p>那么，一个更深层次的问题是：抛开线上、线下的外在形式，所有“<strong>交易</strong>”这件事，有没有一些共通的、永恒不变的内在要素？</p><p>答案是肯定的。在我看来，所有零售的底层逻辑，都可以被拆解为三个最基本的核心要素。掌握了它们，就等于掌握了理解一切商业模式的钥匙。</p><h3 id="1-3-1-核心概念：人、货、场"><a href="#1-3-1-核心概念：人、货、场" class="headerlink" title="1.3.1 核心概念：人、货、场"></a>1.3.1 核心概念：人、货、场</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721153419012.png" alt="image-20250721153419012"></p><p>这三个永恒不变的核心要素，我称之为“<strong>零售三要素</strong>”：</p><ul><li><strong>人 (People)</strong>：即我们的消费者、用户。</li><li><strong>货 (Goods)</strong>：即我们所销售的商品或服务。</li><li><strong>场 (Place)</strong>：即交易发生的场景或场所。</li></ul><p>从传统零售到电子商务的进化，并不是发明了新的要素，而是用<strong>信息化</strong>的手段，对这三个要素的<strong>形态</strong>，进行了一次彻底的、颠覆性的重构。</p><p>下面这张表格，是我认为对这场“重构”最精辟的总结：</p><table><thead><tr><th align="left"><strong>零售形态</strong></th><th align="left"><strong>人 (People)</strong></th><th align="left"><strong>货 (Goods)</strong></th><th align="left"><strong>场 (Place)</strong></th><th align="left"><strong>核心基础</strong></th></tr></thead><tbody><tr><td align="left"><strong>传统零售</strong></td><td align="left">面对面的顾客</td><td align="left">实体商品</td><td align="left">实体门店</td><td align="left"><strong>依赖实体</strong></td></tr><tr><td align="left"><strong>电子商务</strong></td><td align="left"><strong>账号</strong></td><td align="left"><strong>商品信息</strong></td><td align="left"><strong>线上平台</strong></td><td align="left"><strong>信息化</strong></td></tr></tbody></table><h3 id="1-3-2-零售三要素在电商的体现"><a href="#1-3-2-零售三要素在电商的体现" class="headerlink" title="1.3.2 零售三要素在电商的体现"></a>1.3.2 零售三要素在电商的体现</h3><p>现在，我们来深入理解，这信息化重构后的“新人、新货、新场”，对我们产品经理来说，具体意味着什么。</p><h4 id="1-“人”在电商的体现（如账号、用户画像）"><a href="#1-“人”在电商的体现（如账号、用户画像）" class="headerlink" title="1. “人”在电商的体现（如账号、用户画像）"></a>1. “人”在电商的体现（如账号、用户画像）</h4><p>在电商的世界里，“人”不再是商店里一个个模糊的面孔，而是被数字化为了一个唯一的“<strong>账号 (Account)</strong>”。</p><p>这个账号，是我们识别、理解、服务用户的“数字容器”。它不仅包含了用户的<code>手机号</code>、<code>昵称</code>、<code>年龄</code>、<code>性别</code>等基础信息，更重要的是，它记录了用户在我们平台的一切<strong>行为数据</strong>——他浏览过什么、搜索过什么、购买过什么、收藏过什么。</p><p>我正是通过分析这些账号数据，才能为用户建立起精准的“<strong>用户画像</strong>”，从而为他提供个性化的商品推荐和购物体验。</p><h4 id="2-“货”在电商的体现（商品形式与来源）"><a href="#2-“货”在电商的体现（商品形式与来源）" class="headerlink" title="2. “货”在电商的体现（商品形式与来源）"></a>2. “货”在电商的体现（商品形式与来源）</h4><p>在电商的世界里，“货”不再是货架上可以触摸的实体，而是被数字化为了详尽的“<strong>商品信息 (Product Information)</strong>”。</p><p>我作为产品经理，最重要的工作之一，就是设计好承载这些信息的“<strong>商品详情页</strong>”。我需要通过组合运用<code>文字描述</code>、<code>高清图片</code>、<code>展示视频</code>、<code>用户评价</code>等多种信息形态，在用户无法亲身接触到商品的情况下，最大化地还原商品的真实样貌和价值，激发用户的购买欲望。</p><h4 id="3-“场”在电商的体现（平台构建与整合）"><a href="#3-“场”在电商的体现（平台构建与整合）" class="headerlink" title="3. “场”在电商的体现（平台构建与整合）"></a>3. “场”在电商的体现（平台构建与整合）</h4><p>在电商的世界里，“场”不再是具体的物理门店，而是我们所构建的“<strong>线上平台 (Online Platform)</strong>”本身。</p><p>从我们App的<code>首页</code>、<code>分类页</code>、<code>搜索结果页</code>，到<code>商品详情页</code>，再到<code>购物车</code>和<code>支付流程</code>，这整个用户走过的路径，共同构成了这个全新的、数字化的“商场”。</p><p>我作为产品经理，就是这个商场的“总设计师”。我的职责，是确保这个商场的“动线”是流畅的，“导购”是智能的，“收银”是便捷的，为每一个数字化的“人”，提供一次完美的、购买数字化“货”的旅程。</p><hr><h2 id="1-4-电商平台运营模式"><a href="#1-4-电商平台运营模式" class="headerlink" title="1.4 电商平台运营模式"></a>1.4 电商平台运营模式</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154619607.png" alt="image-20250721154619607"></p><p>我们已经知道了电商的“人货场”是信息化的。那么，一个电商公司具体是如何组织和管理这信息化的“人货场”的呢？这就由它的<strong>运营模式</strong>所决定。</p><p>在我看来，要剖析任何一个电商平台的运营模式，都离不开一个最经典、最底层的商业框架——“<strong>进销存</strong>”。</p><ul><li><strong>进 (Procurement)</strong>：商品是从哪里来的？（<strong>商品来源</strong>）</li><li><strong>销 (Sales)</strong>：商品是在哪里卖的？（<strong>销售渠道</strong>）</li><li><strong>存 (Inventory)</strong>：商品由谁来仓储和配送？（<strong>仓储配送</strong>）</li></ul><p><strong>进、销、存这三个环节，由谁来主导、由谁来负责，就共同决定了电商平台的四种主流运营模式。</strong></p><h3 id="1-4-1-自营模式"><a href="#1-4-1-自营模式" class="headerlink" title="1.4.1 自营模式"></a>1.4.1 自营模式</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154659803.png" alt="image-20250721154659803"></p><p><strong>自营模式</strong>，我把它理解为“<strong>线上品牌连锁超市</strong>”。平台自己既是“超市老板”，也是唯一的“售货员”。</p><ul><li><strong>进（货源）</strong>：平台自己负责采购商品。或是通过买手团队向品牌方<strong>自采</strong>，或是自己贴牌<strong>自产</strong>。</li><li><strong>销（销售）</strong>：平台搭建自己的电商网站或App，直接面向消费者进行销售。</li><li><strong>存（仓储）</strong>：平台自己建立仓库（或租赁），自己管理库存和订单履约，自己负责（或统一外包）物流配送。</li><li><strong>我的解读</strong>：在这种模式下，平台对<strong>商品、价格、服务、体验</strong>有绝对的掌控权。<strong>京东自营</strong>就是最典型的代表。它的<strong>优点</strong>是能提供极致的用户体验和品质保障，从而建立起强大的品牌信任。但<strong>缺点</strong>也非常明显，这是一个“重资产”模式，平台需要承担巨大的采购成本和库存风险。</li></ul><h3 id="1-4-2-招商模式"><a href="#1-4-2-招商模式" class="headerlink" title="1.4.2 招商模式"></a>1.4.2 招商模式</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154744575.png" alt="image-20250721154744575"></p><p><strong>招商模式</strong>，我把它理解为“<strong>线上购物中心</strong>”或“<strong>集市</strong>”。平台是“商场房东”，负责搭建和维护商场，并吸引商家进来开店。</p><ul><li><strong>进（货源）</strong>：平台的“采购”，实际上是“<strong>招商</strong>”。通过签约，邀请成千上万的第三方商家入驻开店。</li><li><strong>销（销售）</strong>：所有商家共享平台这个统一的销售渠道。</li><li><strong>存（仓储）</strong>：<strong>商家各自负责</strong>自己的商品仓储、打包和发货。</li><li><strong>我的解读</strong>：在这种模式下，平台的核心是“搭台子”和“定规则”。<strong>淘宝</strong>就是最典型的代表。它的<strong>优点</strong>是“轻资产”运营，可以快速地、低成本地极大丰富平台上的商品数量（SKU）。但<strong>缺点</strong>是平台对商品质量和服务的掌控力较弱，用户体验参差不齐。</li></ul><h3 id="1-4-3-联营模式"><a href="#1-4-3-联营模式" class="headerlink" title="1.4.3 联营模式"></a>1.4.3 联营模式</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154834614.png" alt="image-20250721154834614"></p><p><strong>联营模式</strong>，是介于自营和招商之间的一种合作模式，我称之为“<strong>线上品牌专柜</strong>”。</p><ul><li><strong>进（货源）</strong>：与招商模式类似，平台邀请品牌方或大型供应商入驻。</li><li><strong>销（销售）</strong>：同样在统一的平台上进行销售。</li><li><strong>存（仓储）</strong>：这是最关键的区别。在联营模式下，虽然货权依然属于品牌方，但品牌方会将一部分或全部商品，<strong>放入平台自建的仓库中，由平台来统一负责仓储和配送</strong>。</li><li><strong>我的解读</strong>：这种模式，最典型的就是<strong>天猫超市</strong>或<strong>京东上的大部分品牌旗舰店</strong>。品牌方提供货，但用户享受到的是平台（如京东物流）的配送服务。它试图结合招商模式的“货品丰富”和自营模式的“体验保障”，是一种强强联合的模式。</li></ul><h3 id="1-4-4-混合模式"><a href="#1-4-4-混合模式" class="headerlink" title="1.4.4 混合模式"></a>1.4.4 混合模式</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154910305.png" alt="image-20250721154910305"></p><p><strong>混合模式</strong>，就是字面意思，即平台<strong>同时运营着自营和招商/联营业务</strong>。</p><ul><li><strong>我的解读</strong>：如今，几乎所有的电商巨头，都是混合模式的运营者。最典型的就是<strong>京东</strong>和<strong>亚马逊</strong>。它们既有“自营”板块，来保证核心品类的体验和口碑；又有庞大的“开放平台（招商）”板块，来丰富商品的多样性。这能让它们最大化地满足不同用户的需求，实现优势互补。</li></ul><hr><p>我将这四种模式的核心区别，总结在下面的表格里：</p><table><thead><tr><th align="left"><strong>运营模式</strong></th><th align="left"><strong>平台角色</strong></th><th align="left"><strong>核心优势</strong></th><th align="left"><strong>核心劣势</strong></th><th align="left"><strong>典型代表</strong></th></tr></thead><tbody><tr><td align="left"><strong>自营模式</strong></td><td align="left"><strong>零售商</strong></td><td align="left">体验、品控、物流极佳</td><td align="left">模式重、成本高、SKU有限</td><td align="left">京东自营</td></tr><tr><td align="left"><strong>招商模式</strong></td><td align="left"><strong>商场房东</strong></td><td align="left">模式轻、SKU极其丰富</td><td align="left">体验、品控难统一</td><td align="left">淘宝C店</td></tr><tr><td align="left"><strong>联营模式</strong></td><td align="left"><strong>代销商</strong></td><td align="left">品控+物流体验好，SKU较丰富</td><td align="left">运营模式复杂</td><td align="left">天猫、京东旗舰店</td></tr><tr><td align="left"><strong>混合模式</strong></td><td align="left"><strong>零售商 + 房东</strong></td><td align="left">优势互补、覆盖面广</td><td align="left">内部资源协调复杂</td><td align="left">亚马逊、京东</td></tr></tbody></table><hr><h2 id="1-5-电商平台交易模式"><a href="#1-5-电商平台交易模式" class="headerlink" title="1.5 电商平台交易模式"></a>1.5 电商平台交易模式</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160205118.png" alt="image-20250721160205118"></p><p>在我们上一节学习的“运营模式”中，我们关心的是平台“<strong>如何组织货源和履约</strong>”。而在“<strong>交易模式</strong>”中，我关心的是“<strong>谁在和谁做生意</strong>”。</p><p>一个电商平台的交易模式，本质上是由<strong>商品卖家</strong>和<strong>商品买家</strong>这两方的<strong>角色属性</strong>来决定的。我们常说的B2B、B2C、C2C，其实就是一种简写，它的命名规则是：<br><strong>卖方角色英文首字母 + 2(to) + 买方角色英文首字母</strong></p><p>我们来详细拆解几种最主流的交易模式。</p><h3 id="1-5-1-B2B（企业对企业）"><a href="#1-5-1-B2B（企业对企业）" class="headerlink" title="1.5.1 B2B（企业对企业）"></a>1.5.1 B2B（企业对企业）</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160318526.png" alt="image-20250721160318526"></p><p><strong>B2B (Business to Business)</strong>，即<strong>企业与企业之间</strong>的电子商务。我把它理解为，是将传统的企业间贸易、批发、采购等行为，搬到了线上。</p><ul><li><strong>核心特征</strong>：<ul><li><strong>大批量、低频次</strong>：B2B交易的订单金额通常很大，但交易频次相对较低。</li><li><strong>决策理性且复杂</strong>：采购方通常需要经过内部的多部门（如采购、技术、财务）审批，决策周期长，非常理性。</li><li><strong>价格非标、流程复杂</strong>：价格通常不是固定的，需要经过<code>报价</code>、<code>询价</code>、<code>订购</code>等多个环节，并涉及到复杂的合同、发票和物流流程。</li></ul></li><li><strong>典型代表</strong>：<strong>阿里巴巴(1688.com)</strong>，它就是连接上游工厂/供应商，和下游零售商家的典型B2B平台。</li></ul><h3 id="1-5-2-B2C（企业对个人）"><a href="#1-5-2-B2C（企业对个人）" class="headerlink" title="1.5.2 B2C（企业对个人）"></a>1.5.2 B2C（企业对个人）</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160339627.png" alt="image-20250721160339627"></p><p><strong>B2C (Business to Customer)</strong>，即<strong>企业与个人消费者之间</strong>的电子商务。这是我们日常生活中，接触最多、也最熟悉的一种模式。</p><ul><li><strong>核心特征</strong>：<ul><li><strong>小批量、高频次</strong>：用户通常是购买单件或少量商品，但购买行为可能会非常频繁。</li><li><strong>决策感性且迅速</strong>：购买决策通常由个人做出，很容易受到品牌、营销、促销、评价等因素影响，决策路径短。</li><li><strong>价格标准、体验为王</strong>：商品价格是标准化的，所有消费者看到的都一样。平台竞争的核心，在于品牌、营销、用户体验和客户服务。</li></ul></li><li><strong>典型代表</strong>：<strong>天猫、京东、唯品会、拼多多</strong>等，都是典型的B2C平台。</li></ul><h3 id="1-5-3-C2C（个人对个人）"><a href="#1-5-3-C2C（个人对个人）" class="headerlink" title="1.5.3 C2C（个人对个人）"></a>1.5.3 C2C（个人对个人）</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160404064.png" alt="image-20250721160404064"></p><p><strong>C2C (Customer to Customer)</strong>，即<strong>个人与个人之间</strong>的电子商务。我把它理解为一个“<strong>线上跳蚤市场</strong>”或“<strong>个人闲置物品交易集市</strong>”。</p><ul><li><strong>核心特征</strong>：<ul><li><strong>平台是中介</strong>：平台本身不卖货，而是作为第三方，为买卖双方提供一个信息发布、在线沟通、交易担保的场所。</li><li><strong>依赖生态服务</strong>：C2C模式的成立，强依赖于成熟的<strong>第三方支付工具</strong>（如支付宝，解决信任问题）和<strong>物流公司</strong>（解决物品交付问题）。</li><li><strong>非标品为主</strong>：交易的商品大多是二手的、非标准化的。</li></ul></li><li><strong>典型代表</strong>：目前国内最成功的C2C平台，就是阿里的<strong>闲鱼</strong>。</li></ul><h3 id="1-5-4-F2C（工厂对个人）等"><a href="#1-5-4-F2C（工厂对个人）等" class="headerlink" title="1.5.4 F2C（工厂对个人）等"></a>1.5.4 F2C（工厂对个人）等</h3><p><strong>F2C (Factory to Customer)</strong>，即<strong>工厂与个人消费者之间</strong>的电子商务。这是一种旨在“<strong>去中间化</strong>”的模式。</p><ul><li><strong>核心特征</strong>：<ul><li><strong>短路经济</strong>：它砍掉了传统零售中的经销商、代理商等所有中间环节，让消费者可以直接从工厂下单购买。</li><li><strong>高性价比</strong>：因为没有了中间商赚差价，理论上能为消费者提供更低的价格。</li><li><strong>C2M模式</strong>：它常常与C2M（Customer-to-Manufacturer，用户直连制造）模式结合，即工厂根据用户的订单和需求来进行生产，实现零库存。</li></ul></li><li><strong>典型代表</strong>：<strong>必要App</strong>就是F2C/C2M模式的知名探索者。</li></ul><hr><p>我将这几种主流的交易模式，总结在下面的表格中，方便我们对比：</p><table><thead><tr><th align="left"><strong>交易模式</strong></th><th align="left"><strong>卖方 (Seller)</strong></th><th align="left"><strong>买方 (Buyer)</strong></th><th align="left"><strong>核心特征</strong></th><th align="left"><strong>典型代表</strong></th></tr></thead><tbody><tr><td align="left"><strong>B2B</strong></td><td align="left">企业 (Business)</td><td align="left">企业 (Business)</td><td align="left">订单量大、决策复杂、价格多为协商</td><td align="left">1688.com</td></tr><tr><td align="left"><strong>B2C</strong></td><td align="left">企业 (Business)</td><td align="left">个人 (Consumer)</td><td align="left">品牌化、体验为王、价格标准化</td><td align="left">天猫、京东</td></tr><tr><td align="left"><strong>C2C</strong></td><td align="left">个人 (Consumer)</td><td align="left">个人 (Consumer)</td><td align="left">平台撮合、依赖第三方支付与物流</td><td align="left">闲鱼</td></tr><tr><td align="left"><strong>F2C</strong></td><td align="left">工厂 (Factory)</td><td align="left">个人 (Consumer)</td><td align="left">去中间化、高性价比、按需生产</td><td align="left">必要</td></tr></tbody></table><hr><h2 id="1-6-电商平台盈利模式"><a href="#1-6-电商平台盈利模式" class="headerlink" title="1.6 电商平台盈利模式"></a>1.6 电商平台盈利模式</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160704848.png" alt="image-20250721160704848"></p><p>我们前面讨论了电商的运营模式、交易模式，这些模式虽然千差万别，但它们都服务于一个最终的、共同的目的——<strong>盈利</strong>。</p><p>作为产品经理，我必须深刻地理解我所在平台的盈利模式，因为这直接决定了我的<strong>工作重心</strong>和<strong>产品设计的方向</strong>。我的每一个功能设计，最终都应该直接或间接地，为平台的盈利目标服务。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160725452.png" alt="image-20250721160725452"></p><p>我们来看一下，像天猫、京东这些巨头，它们是如何通过多种方式，来构建自己强大的盈利能力的。我将这些复杂的方式，归纳为三大主流的盈利模式。</p><h3 id="1-6-1-销售收入"><a href="#1-6-1-销售收入" class="headerlink" title="1.6.1 销售收入"></a>1.6.1 销售收入</h3><p>这是最直接，也是最古老的盈利模式。我把它简单地理解为“<strong>赚差价</strong>”。</p><ul><li><strong>核心逻辑</strong>：这种模式主要应用于<strong>自营型</strong>电商。平台首先以一个“采购价”从供应商那里把商品买断，然后在自己的平台上以一个“零售价”卖给消费者。这两者之间的差价，扣除仓储、物流、人力等成本后，就是平台的利润。</li><li><strong>我的设计思考</strong>：如果我所在平台的核心收入是销售收入，那么我的产品设计，就会<strong>高度聚焦于提升交易转化率和销售额（GMV）</strong>。比如，我会重点优化商品推荐算法、简化购物车到支付的流程、设计各种优惠券和促销活动功能等，一切为了让用户“买得更多，买得更快”。</li></ul><h3 id="1-6-2-广告收入"><a href="#1-6-2-广告收入" class="headerlink" title="1.6.2 广告收入"></a>1.6.2 广告收入</h3><p>当一个平台拥有了巨大的用户流量，那么它的流量本身，就成了可以售卖的商品。我把这种模式，理解为“<strong>卖流量</strong>”或“<strong>卖广告位</strong>”。</p><ul><li><strong>核心逻辑</strong>：这种模式是<strong>招商平台</strong>最核心的收入来源。平台本身不靠卖货赚钱，而是通过向希望在平台上获得曝光的商家，出售广告位来盈利。</li><li><strong>我的拓展设计</strong>：广告产品的设计，本身就是一个复杂的产品领域。常见的广告形式包括：<ul><li><strong>关键词竞价</strong>：商家对某个搜索关键词（如“运动鞋”）进行出价，出价高者，其商品就会排在搜索结果的前列。这是淘宝/天猫最核心的收入来源。</li><li><strong>展示广告</strong>：平台将首页的Banner、分类页的固定位置等，作为“广告位”，按时间或曝光量，售卖给品牌商家。</li><li><strong>信息流广告</strong>：在“猜你喜欢”等个性化推荐信息流中，穿插一些看起来像普通内容的广告商品。</li></ul></li></ul><h3 id="1-6-3-平台服务费"><a href="#1-6-3-平台服务费" class="headerlink" title="1.6.3 平台服务费"></a>1.6.3 平台服务费</h3><p>这也是**招商平台（平台模式）**的另一种重要收入，我把它理解为“<strong>收租金和佣金</strong>”。就像一个大型购物中心，向入驻的品牌专柜收取各种费用一样。</p><p>我把它细分为以下几种：</p><h4 id="1-店铺租金"><a href="#1-店铺租金" class="headerlink" title="1. 店铺租金"></a>1. 店铺租金</h4><p>也叫“<strong>平台年费</strong>”。商家需要每年向平台缴纳一笔固定的费用，才能获得在平台上开设店铺的资格。这就像是实体店的“年租金”。</p><h4 id="2-技术服务费"><a href="#2-技术服务费" class="headerlink" title="2. 技术服务费"></a>2. 技术服务费</h4><p>平台为商家提供了一整套复杂的开店、交易、管理、数据分析的系统，为此，平台会收取一定的“<strong>技术服务费</strong>”，作为软件系统和数据服务的费用。</p><h4 id="3-交易提成"><a href="#3-交易提成" class="headerlink" title="3. 交易提成"></a>3. 交易提成</h4><p>这是最常见的一种方式。平台会从商家的每一笔成功交易的流水中，抽取一个固定比例的佣金，通常在1%-5%不等。<strong>商家卖得越多，平台赚得越多</strong>，这种模式将平台和商家的利益进行了深度绑定。</p><hr><h2 id="1-7-本章总结"><a href="#1-7-本章总结" class="headerlink" title="1.7 本章总结"></a>1.7 本章总结</h2><p>到这里，我们关于“电商基础”的学习就告一段落了。我们来快速回顾一下本章的核心知识地图：</p><table><thead><tr><th align="left"><strong>知识模块</strong></th><th align="left"><strong>核心内容</strong></th></tr></thead><tbody><tr><td align="left"><strong>零售行业的发展</strong></td><td align="left">我们理解了从<strong>传统零售</strong>到<strong>电子商务</strong>的进化，是商业在<strong>时间和空间</strong>上的伟大突破。</td></tr><tr><td align="left"><strong>零售三要素</strong></td><td align="left">我们掌握了所有商业的底层逻辑——<strong>人、货、场</strong>，以及它们在电商时代，如何被<strong>信息化</strong>为<strong>账号、商品信息、线上平台</strong>。</td></tr><tr><td align="left"><strong>电商平台运营模式</strong></td><td align="left">我们学习了通过“<strong>进销存</strong>”框架，来区分<strong>自营、招商、联营、混合</strong>这四种主流的运营模式。</td></tr><tr><td align="left"><strong>电商平台交易模式</strong></td><td align="left">我们学习了通过“<strong>买卖双方的角色</strong>”，来区分<strong>B2B, B2C, C2C, F2C</strong>等不同的交易模式。</td></tr><tr><td align="left"><strong>电商平台盈利模式</strong></td><td align="left">我们学习了电商最核心的三大盈利来源：<strong>赚差价（销售收入）、卖流量（广告收入）、收佣金（平台服务费）</strong>。</td></tr></tbody></table><p>掌握了这些最底层的概念和框架，我们就拥有了一双能看透所有复杂电商产品的“慧眼”。在下一章，我们将开始进入更具体的实战设计中。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/8823.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/8823.html&quot;)">产品经理进阶（一）：第一章：电商基础</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/8823.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理进阶（一）：第一章：电商基础&amp;url=https://prorise666.site/posts/8823.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/30992.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">2️⃣ 电商实战（下）</div></div></a></div><div class="next-post pull-right"><a href="/posts/8272.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理进阶（二）：第二章：电商项目立项</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理进阶（一）：第一章：电商基础",date:"2025-07-24 16:13:45",updated:"2025-07-26 21:16:27",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第一章：电商基础\n\n欢迎来到我们关于电商产品管理的新篇章。电子商务，无疑是过去二十年间，最深刻地改变了我们生活方式和商业形态的领域之一。作为这个领域的从业者，我们就是现代数字商场的“建筑师”。\n\n在第一章，我将带大家一起，为我们即将开始的“建筑”生涯，打下最坚实的地基。我们将从零售最本质的源头开始，理解我们所处的这个波澜壮阔的行业。\n\n## 1.1 学习目标\n\n![image-20250721152555400](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721152555400.png)\n\n在本章中，我的核心目标是，帮助我们建立起对电子商务领域的全局性、结构化的认知。我们将一起追溯零售行业的发展脉络，掌握驱动所有商业运转的“零售三要素”，并清晰地辨别当前主流电商平台的各种运营、交易及盈利模式。\n\n## 1.2 零售行业的发展\n\n要理解什么是“电子商务”，我们必须先理解什么是“商务”，也就是“零售”。在我看来，电子商务并非一个全新的物种，而是传统零售在数字时代的一次**伟大进化**。\n\n### 1.2.1 传统零售\n\n![image-20250721152624549](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721152624549.png)\n\n**传统零售**，指的是所有**以实体门店为主要交易场所**的商业活动。\n\n* **它的形态**：可以是我们楼下的小`杂货店`，可以是市中心的`百货商店`，也可以是大型的`连锁超市`和`购物中心`。\n* **它的本质**：是“一手交钱，一手交货”的面对面交易。\n* **它的核心限制**：是它无法摆脱**时间和空间**的束缚。一家商店，必须在固定的营业时间（时间限制），在固定的地理位置（空间限制），才能服务于能够到达这里的顾客。\n\n### 1.2.2 电子商务\n\n![image-20250721152732714](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721152732714.png)\n\n**电子商务（E-commerce）**，则是**以互联网为媒介进行商品交易**的全新零售形态。\n\n* **它的形态**：是我们手机里无处不在的`淘宝`、`京东`、`拼多多`等购物App。\n* **它的本质**：是通过信息流、资金流、物流的线上整合，来完成交易。\n* **它的核心突破**：是它彻底**打破了传统零售在时间和空间上的限制**。我可以在任何时间（深夜或凌晨）、任何地点（家里或路上），购买到来自世界各地的商品。\n\n正是这一根本性的突破，创造出了一个无比巨大的、全新的数字商业世界，也为我们产品经理，提供了施展才华的广阔舞台。\n\n---\n我将这两者的核心区别，总结在下面的表格里，这能帮助我们更清晰地理解这次“进化”。\n\n| **零售形态** | **交易场所** | **核心限制** | **典型代表** |\n| :--- | :--- | :--- | :--- |\n| **传统零售** | 实体门店 | **受限于时间和空间** | 连锁超市、百货商场 |\n| **电子商务** | 互联网平台 | **打破了时间和空间的限制** | 淘宝、京东、拼多多 |\n\n\n\n\n---\n## 1.3 零售三要素\n\n![image-20250721153351348](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721153351348.png)\n\n我们已经清楚了传统零售和电子商务的区别。\n\n那么，一个更深层次的问题是：抛开线上、线下的外在形式，所有“**交易**”这件事，有没有一些共通的、永恒不变的内在要素？\n\n答案是肯定的。在我看来，所有零售的底层逻辑，都可以被拆解为三个最基本的核心要素。掌握了它们，就等于掌握了理解一切商业模式的钥匙。\n\n### 1.3.1 核心概念：人、货、场\n\n![image-20250721153419012](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721153419012.png)\n\n这三个永恒不变的核心要素，我称之为“**零售三要素**”：\n* **人 (People)**：即我们的消费者、用户。\n* **货 (Goods)**：即我们所销售的商品或服务。\n* **场 (Place)**：即交易发生的场景或场所。\n\n从传统零售到电子商务的进化，并不是发明了新的要素，而是用**信息化**的手段，对这三个要素的**形态**，进行了一次彻底的、颠覆性的重构。\n\n下面这张表格，是我认为对这场“重构”最精辟的总结：\n\n| **零售形态** | **人 (People)** | **货 (Goods)** | **场 (Place)** | **核心基础** |\n| :--- | :--- | :--- | :--- | :--- |\n| **传统零售** | 面对面的顾客 | 实体商品 | 实体门店 | **依赖实体** |\n| **电子商务** | **账号** | **商品信息** | **线上平台** | **信息化** |\n\n### 1.3.2 零售三要素在电商的体现\n\n现在，我们来深入理解，这信息化重构后的“新人、新货、新场”，对我们产品经理来说，具体意味着什么。\n\n#### 1. “人”在电商的体现（如账号、用户画像）\n\n在电商的世界里，“人”不再是商店里一个个模糊的面孔，而是被数字化为了一个唯一的“**账号 (Account)**”。\n\n这个账号，是我们识别、理解、服务用户的“数字容器”。它不仅包含了用户的`手机号`、`昵称`、`年龄`、`性别`等基础信息，更重要的是，它记录了用户在我们平台的一切**行为数据**——他浏览过什么、搜索过什么、购买过什么、收藏过什么。\n\n我正是通过分析这些账号数据，才能为用户建立起精准的“**用户画像**”，从而为他提供个性化的商品推荐和购物体验。\n\n#### 2. “货”在电商的体现（商品形式与来源）\n\n在电商的世界里，“货”不再是货架上可以触摸的实体，而是被数字化为了详尽的“**商品信息 (Product Information)**”。\n\n我作为产品经理，最重要的工作之一，就是设计好承载这些信息的“**商品详情页**”。我需要通过组合运用`文字描述`、`高清图片`、`展示视频`、`用户评价`等多种信息形态，在用户无法亲身接触到商品的情况下，最大化地还原商品的真实样貌和价值，激发用户的购买欲望。\n\n#### 3. “场”在电商的体现（平台构建与整合）\n\n在电商的世界里，“场”不再是具体的物理门店，而是我们所构建的“**线上平台 (Online Platform)**”本身。\n\n从我们App的`首页`、`分类页`、`搜索结果页`，到`商品详情页`，再到`购物车`和`支付流程`，这整个用户走过的路径，共同构成了这个全新的、数字化的“商场”。\n\n我作为产品经理，就是这个商场的“总设计师”。我的职责，是确保这个商场的“动线”是流畅的，“导购”是智能的，“收银”是便捷的，为每一个数字化的“人”，提供一次完美的、购买数字化“货”的旅程。\n\n\n\n---\n## 1.4 电商平台运营模式\n\n![image-20250721154619607](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154619607.png)\n\n我们已经知道了电商的“人货场”是信息化的。那么，一个电商公司具体是如何组织和管理这信息化的“人货场”的呢？这就由它的**运营模式**所决定。\n\n在我看来，要剖析任何一个电商平台的运营模式，都离不开一个最经典、最底层的商业框架——“**进销存**”。\n\n* **进 (Procurement)**：商品是从哪里来的？（**商品来源**）\n* **销 (Sales)**：商品是在哪里卖的？（**销售渠道**）\n* **存 (Inventory)**：商品由谁来仓储和配送？（**仓储配送**）\n\n**进、销、存这三个环节，由谁来主导、由谁来负责，就共同决定了电商平台的四种主流运营模式。**\n\n### 1.4.1 自营模式\n\n![image-20250721154659803](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154659803.png)\n\n**自营模式**，我把它理解为“**线上品牌连锁超市**”。平台自己既是“超市老板”，也是唯一的“售货员”。\n\n* **进（货源）**：平台自己负责采购商品。或是通过买手团队向品牌方**自采**，或是自己贴牌**自产**。\n* **销（销售）**：平台搭建自己的电商网站或App，直接面向消费者进行销售。\n* **存（仓储）**：平台自己建立仓库（或租赁），自己管理库存和订单履约，自己负责（或统一外包）物流配送。\n* **我的解读**：在这种模式下，平台对**商品、价格、服务、体验**有绝对的掌控权。**京东自营**就是最典型的代表。它的**优点**是能提供极致的用户体验和品质保障，从而建立起强大的品牌信任。但**缺点**也非常明显，这是一个“重资产”模式，平台需要承担巨大的采购成本和库存风险。\n\n### 1.4.2 招商模式\n\n![image-20250721154744575](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154744575.png)\n\n**招商模式**，我把它理解为“**线上购物中心**”或“**集市**”。平台是“商场房东”，负责搭建和维护商场，并吸引商家进来开店。\n\n* **进（货源）**：平台的“采购”，实际上是“**招商**”。通过签约，邀请成千上万的第三方商家入驻开店。\n* **销（销售）**：所有商家共享平台这个统一的销售渠道。\n* **存（仓储）**：**商家各自负责**自己的商品仓储、打包和发货。\n* **我的解读**：在这种模式下，平台的核心是“搭台子”和“定规则”。**淘宝**就是最典型的代表。它的**优点**是“轻资产”运营，可以快速地、低成本地极大丰富平台上的商品数量（SKU）。但**缺点**是平台对商品质量和服务的掌控力较弱，用户体验参差不齐。\n\n### 1.4.3 联营模式\n\n![image-20250721154834614](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154834614.png)\n\n**联营模式**，是介于自营和招商之间的一种合作模式，我称之为“**线上品牌专柜**”。\n\n* **进（货源）**：与招商模式类似，平台邀请品牌方或大型供应商入驻。\n* **销（销售）**：同样在统一的平台上进行销售。\n* **存（仓储）**：这是最关键的区别。在联营模式下，虽然货权依然属于品牌方，但品牌方会将一部分或全部商品，**放入平台自建的仓库中，由平台来统一负责仓储和配送**。\n* **我的解读**：这种模式，最典型的就是**天猫超市**或**京东上的大部分品牌旗舰店**。品牌方提供货，但用户享受到的是平台（如京东物流）的配送服务。它试图结合招商模式的“货品丰富”和自营模式的“体验保障”，是一种强强联合的模式。\n\n### 1.4.4 混合模式\n\n![image-20250721154910305](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154910305.png)\n\n**混合模式**，就是字面意思，即平台**同时运营着自营和招商/联营业务**。\n\n* **我的解读**：如今，几乎所有的电商巨头，都是混合模式的运营者。最典型的就是**京东**和**亚马逊**。它们既有“自营”板块，来保证核心品类的体验和口碑；又有庞大的“开放平台（招商）”板块，来丰富商品的多样性。这能让它们最大化地满足不同用户的需求，实现优势互补。\n\n---\n我将这四种模式的核心区别，总结在下面的表格里：\n\n| **运营模式** | **平台角色** | **核心优势** | **核心劣势** | **典型代表** |\n| :--- | :--- | :--- | :--- | :--- |\n| **自营模式** | **零售商** | 体验、品控、物流极佳 | 模式重、成本高、SKU有限 | 京东自营 |\n| **招商模式** | **商场房东** | 模式轻、SKU极其丰富 | 体验、品控难统一 | 淘宝C店 |\n| **联营模式** | **代销商** | 品控+物流体验好，SKU较丰富 | 运营模式复杂 | 天猫、京东旗舰店 |\n| **混合模式** | **零售商 + 房东** | 优势互补、覆盖面广 | 内部资源协调复杂 | 亚马逊、京东 |\n\n\n\n\n---\n## 1.5 电商平台交易模式\n\n![image-20250721160205118](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160205118.png)\n\n在我们上一节学习的“运营模式”中，我们关心的是平台“**如何组织货源和履约**”。而在“**交易模式**”中，我关心的是“**谁在和谁做生意**”。\n\n一个电商平台的交易模式，本质上是由**商品卖家**和**商品买家**这两方的**角色属性**来决定的。我们常说的B2B、B2C、C2C，其实就是一种简写，它的命名规则是：\n**卖方角色英文首字母 + 2(to) + 买方角色英文首字母**\n\n我们来详细拆解几种最主流的交易模式。\n\n### 1.5.1 B2B（企业对企业）\n\n![image-20250721160318526](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160318526.png)\n\n**B2B (Business to Business)**，即**企业与企业之间**的电子商务。我把它理解为，是将传统的企业间贸易、批发、采购等行为，搬到了线上。\n\n* **核心特征**：\n    * **大批量、低频次**：B2B交易的订单金额通常很大，但交易频次相对较低。\n    * **决策理性且复杂**：采购方通常需要经过内部的多部门（如采购、技术、财务）审批，决策周期长，非常理性。\n    * **价格非标、流程复杂**：价格通常不是固定的，需要经过`报价`、`询价`、`订购`等多个环节，并涉及到复杂的合同、发票和物流流程。\n* **典型代表**：**阿里巴巴(1688.com)**，它就是连接上游工厂/供应商，和下游零售商家的典型B2B平台。\n\n### 1.5.2 B2C（企业对个人）\n\n![image-20250721160339627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160339627.png)\n\n**B2C (Business to Customer)**，即**企业与个人消费者之间**的电子商务。这是我们日常生活中，接触最多、也最熟悉的一种模式。\n\n* **核心特征**：\n    * **小批量、高频次**：用户通常是购买单件或少量商品，但购买行为可能会非常频繁。\n    * **决策感性且迅速**：购买决策通常由个人做出，很容易受到品牌、营销、促销、评价等因素影响，决策路径短。\n    * **价格标准、体验为王**：商品价格是标准化的，所有消费者看到的都一样。平台竞争的核心，在于品牌、营销、用户体验和客户服务。\n* **典型代表**：**天猫、京东、唯品会、拼多多**等，都是典型的B2C平台。\n\n### 1.5.3 C2C（个人对个人）\n\n![image-20250721160404064](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160404064.png)\n\n**C2C (Customer to Customer)**，即**个人与个人之间**的电子商务。我把它理解为一个“**线上跳蚤市场**”或“**个人闲置物品交易集市**”。\n\n* **核心特征**：\n    * **平台是中介**：平台本身不卖货，而是作为第三方，为买卖双方提供一个信息发布、在线沟通、交易担保的场所。\n    * **依赖生态服务**：C2C模式的成立，强依赖于成熟的**第三方支付工具**（如支付宝，解决信任问题）和**物流公司**（解决物品交付问题）。\n    * **非标品为主**：交易的商品大多是二手的、非标准化的。\n* **典型代表**：目前国内最成功的C2C平台，就是阿里的**闲鱼**。\n\n### 1.5.4 F2C（工厂对个人）等\n\n**F2C (Factory to Customer)**，即**工厂与个人消费者之间**的电子商务。这是一种旨在“**去中间化**”的模式。\n\n* **核心特征**：\n    * **短路经济**：它砍掉了传统零售中的经销商、代理商等所有中间环节，让消费者可以直接从工厂下单购买。\n    * **高性价比**：因为没有了中间商赚差价，理论上能为消费者提供更低的价格。\n    * **C2M模式**：它常常与C2M（Customer-to-Manufacturer，用户直连制造）模式结合，即工厂根据用户的订单和需求来进行生产，实现零库存。\n* **典型代表**：**必要App**就是F2C/C2M模式的知名探索者。\n\n---\n我将这几种主流的交易模式，总结在下面的表格中，方便我们对比：\n\n| **交易模式** | **卖方 (Seller)** | **买方 (Buyer)** | **核心特征** | **典型代表** |\n| :--- | :--- | :--- | :--- | :--- |\n| **B2B** | 企业 (Business) | 企业 (Business) | 订单量大、决策复杂、价格多为协商 | 1688.com |\n| **B2C** | 企业 (Business) | 个人 (Consumer) | 品牌化、体验为王、价格标准化 | 天猫、京东 |\n| **C2C** | 个人 (Consumer) | 个人 (Consumer) | 平台撮合、依赖第三方支付与物流 | 闲鱼 |\n| **F2C** | 工厂 (Factory) | 个人 (Consumer) | 去中间化、高性价比、按需生产 | 必要 |\n\n\n\n\n---\n\n## 1.6 电商平台盈利模式\n\n![image-20250721160704848](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160704848.png)\n\n我们前面讨论了电商的运营模式、交易模式，这些模式虽然千差万别，但它们都服务于一个最终的、共同的目的——**盈利**。\n\n作为产品经理，我必须深刻地理解我所在平台的盈利模式，因为这直接决定了我的**工作重心**和**产品设计的方向**。我的每一个功能设计，最终都应该直接或间接地，为平台的盈利目标服务。\n\n![image-20250721160725452](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160725452.png)\n\n我们来看一下，像天猫、京东这些巨头，它们是如何通过多种方式，来构建自己强大的盈利能力的。我将这些复杂的方式，归纳为三大主流的盈利模式。\n\n### 1.6.1 销售收入\n\n这是最直接，也是最古老的盈利模式。我把它简单地理解为“**赚差价**”。\n\n* **核心逻辑**：这种模式主要应用于**自营型**电商。平台首先以一个“采购价”从供应商那里把商品买断，然后在自己的平台上以一个“零售价”卖给消费者。这两者之间的差价，扣除仓储、物流、人力等成本后，就是平台的利润。\n* **我的设计思考**：如果我所在平台的核心收入是销售收入，那么我的产品设计，就会**高度聚焦于提升交易转化率和销售额（GMV）**。比如，我会重点优化商品推荐算法、简化购物车到支付的流程、设计各种优惠券和促销活动功能等，一切为了让用户“买得更多，买得更快”。\n\n### 1.6.2 广告收入\n\n当一个平台拥有了巨大的用户流量，那么它的流量本身，就成了可以售卖的商品。我把这种模式，理解为“**卖流量**”或“**卖广告位**”。\n\n* **核心逻辑**：这种模式是**招商平台**最核心的收入来源。平台本身不靠卖货赚钱，而是通过向希望在平台上获得曝光的商家，出售广告位来盈利。\n* **我的拓展设计**：广告产品的设计，本身就是一个复杂的产品领域。常见的广告形式包括：\n    * **关键词竞价**：商家对某个搜索关键词（如“运动鞋”）进行出价，出价高者，其商品就会排在搜索结果的前列。这是淘宝/天猫最核心的收入来源。\n    * **展示广告**：平台将首页的Banner、分类页的固定位置等，作为“广告位”，按时间或曝光量，售卖给品牌商家。\n    * **信息流广告**：在“猜你喜欢”等个性化推荐信息流中，穿插一些看起来像普通内容的广告商品。\n\n### 1.6.3 平台服务费\n\n这也是**招商平台（平台模式）**的另一种重要收入，我把它理解为“**收租金和佣金**”。就像一个大型购物中心，向入驻的品牌专柜收取各种费用一样。\n\n我把它细分为以下几种：\n\n#### 1. 店铺租金\n也叫“**平台年费**”。商家需要每年向平台缴纳一笔固定的费用，才能获得在平台上开设店铺的资格。这就像是实体店的“年租金”。\n\n#### 2. 技术服务费\n平台为商家提供了一整套复杂的开店、交易、管理、数据分析的系统，为此，平台会收取一定的“**技术服务费**”，作为软件系统和数据服务的费用。\n\n#### 3. 交易提成\n这是最常见的一种方式。平台会从商家的每一笔成功交易的流水中，抽取一个固定比例的佣金，通常在1%-5%不等。**商家卖得越多，平台赚得越多**，这种模式将平台和商家的利益进行了深度绑定。\n\n---\n\n## 1.7 本章总结\n\n到这里，我们关于“电商基础”的学习就告一段落了。我们来快速回顾一下本章的核心知识地图：\n\n| **知识模块** | **核心内容** |\n| :--- | :--- |\n| **零售行业的发展** | 我们理解了从**传统零售**到**电子商务**的进化，是商业在**时间和空间**上的伟大突破。 |\n| **零售三要素** | 我们掌握了所有商业的底层逻辑——**人、货、场**，以及它们在电商时代，如何被**信息化**为**账号、商品信息、线上平台**。 |\n| **电商平台运营模式** | 我们学习了通过“**进销存**”框架，来区分**自营、招商、联营、混合**这四种主流的运营模式。 |\n| **电商平台交易模式** | 我们学习了通过“**买卖双方的角色**”，来区分**B2B, B2C, C2C, F2C**等不同的交易模式。 |\n| **电商平台盈利模式** | 我们学习了电商最核心的三大盈利来源：**赚差价（销售收入）、卖流量（广告收入）、收佣金（平台服务费）**。 |\n\n掌握了这些最底层的概念和框架，我们就拥有了一双能看透所有复杂电商产品的“慧眼”。在下一章，我们将开始进入更具体的实战设计中。\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%9F%BA%E7%A1%80"><span class="toc-number">1.</span> <span class="toc-text">第一章：电商基础</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.</span> <span class="toc-text">1.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-2-%E9%9B%B6%E5%94%AE%E8%A1%8C%E4%B8%9A%E7%9A%84%E5%8F%91%E5%B1%95"><span class="toc-number">1.2.</span> <span class="toc-text">1.2 零售行业的发展</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-1-%E4%BC%A0%E7%BB%9F%E9%9B%B6%E5%94%AE"><span class="toc-number">1.2.1.</span> <span class="toc-text">1.2.1 传统零售</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-2-%E7%94%B5%E5%AD%90%E5%95%86%E5%8A%A1"><span class="toc-number">1.2.2.</span> <span class="toc-text">1.2.2 电子商务</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-3-%E9%9B%B6%E5%94%AE%E4%B8%89%E8%A6%81%E7%B4%A0"><span class="toc-number">1.3.</span> <span class="toc-text">1.3 零售三要素</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-3-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%EF%BC%9A%E4%BA%BA%E3%80%81%E8%B4%A7%E3%80%81%E5%9C%BA"><span class="toc-number">1.3.1.</span> <span class="toc-text">1.3.1 核心概念：人、货、场</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-3-2-%E9%9B%B6%E5%94%AE%E4%B8%89%E8%A6%81%E7%B4%A0%E5%9C%A8%E7%94%B5%E5%95%86%E7%9A%84%E4%BD%93%E7%8E%B0"><span class="toc-number">1.3.2.</span> <span class="toc-text">1.3.2 零售三要素在电商的体现</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E2%80%9C%E4%BA%BA%E2%80%9D%E5%9C%A8%E7%94%B5%E5%95%86%E7%9A%84%E4%BD%93%E7%8E%B0%EF%BC%88%E5%A6%82%E8%B4%A6%E5%8F%B7%E3%80%81%E7%94%A8%E6%88%B7%E7%94%BB%E5%83%8F%EF%BC%89"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">1. “人”在电商的体现（如账号、用户画像）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E2%80%9C%E8%B4%A7%E2%80%9D%E5%9C%A8%E7%94%B5%E5%95%86%E7%9A%84%E4%BD%93%E7%8E%B0%EF%BC%88%E5%95%86%E5%93%81%E5%BD%A2%E5%BC%8F%E4%B8%8E%E6%9D%A5%E6%BA%90%EF%BC%89"><span class="toc-number">1.3.2.2.</span> <span class="toc-text">2. “货”在电商的体现（商品形式与来源）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E2%80%9C%E5%9C%BA%E2%80%9D%E5%9C%A8%E7%94%B5%E5%95%86%E7%9A%84%E4%BD%93%E7%8E%B0%EF%BC%88%E5%B9%B3%E5%8F%B0%E6%9E%84%E5%BB%BA%E4%B8%8E%E6%95%B4%E5%90%88%EF%BC%89"><span class="toc-number">1.3.2.3.</span> <span class="toc-text">3. “场”在电商的体现（平台构建与整合）</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-4-%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0%E8%BF%90%E8%90%A5%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.4.</span> <span class="toc-text">1.4 电商平台运营模式</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-1-%E8%87%AA%E8%90%A5%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.4.1.</span> <span class="toc-text">1.4.1 自营模式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-2-%E6%8B%9B%E5%95%86%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.4.2.</span> <span class="toc-text">1.4.2 招商模式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-3-%E8%81%94%E8%90%A5%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.4.3.</span> <span class="toc-text">1.4.3 联营模式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-4-%E6%B7%B7%E5%90%88%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.4.4.</span> <span class="toc-text">1.4.4 混合模式</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-5-%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0%E4%BA%A4%E6%98%93%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.5.</span> <span class="toc-text">1.5 电商平台交易模式</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-1-B2B%EF%BC%88%E4%BC%81%E4%B8%9A%E5%AF%B9%E4%BC%81%E4%B8%9A%EF%BC%89"><span class="toc-number">1.5.1.</span> <span class="toc-text">1.5.1 B2B（企业对企业）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-2-B2C%EF%BC%88%E4%BC%81%E4%B8%9A%E5%AF%B9%E4%B8%AA%E4%BA%BA%EF%BC%89"><span class="toc-number">1.5.2.</span> <span class="toc-text">1.5.2 B2C（企业对个人）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-3-C2C%EF%BC%88%E4%B8%AA%E4%BA%BA%E5%AF%B9%E4%B8%AA%E4%BA%BA%EF%BC%89"><span class="toc-number">1.5.3.</span> <span class="toc-text">1.5.3 C2C（个人对个人）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-4-F2C%EF%BC%88%E5%B7%A5%E5%8E%82%E5%AF%B9%E4%B8%AA%E4%BA%BA%EF%BC%89%E7%AD%89"><span class="toc-number">1.5.4.</span> <span class="toc-text">1.5.4 F2C（工厂对个人）等</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-6-%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0%E7%9B%88%E5%88%A9%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.6.</span> <span class="toc-text">1.6 电商平台盈利模式</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-6-1-%E9%94%80%E5%94%AE%E6%94%B6%E5%85%A5"><span class="toc-number">1.6.1.</span> <span class="toc-text">1.6.1 销售收入</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-6-2-%E5%B9%BF%E5%91%8A%E6%94%B6%E5%85%A5"><span class="toc-number">1.6.2.</span> <span class="toc-text">1.6.2 广告收入</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-6-3-%E5%B9%B3%E5%8F%B0%E6%9C%8D%E5%8A%A1%E8%B4%B9"><span class="toc-number">1.6.3.</span> <span class="toc-text">1.6.3 平台服务费</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BA%97%E9%93%BA%E7%A7%9F%E9%87%91"><span class="toc-number">1.6.3.1.</span> <span class="toc-text">1. 店铺租金</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%8A%80%E6%9C%AF%E6%9C%8D%E5%8A%A1%E8%B4%B9"><span class="toc-number">1.6.3.2.</span> <span class="toc-text">2. 技术服务费</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%BA%A4%E6%98%93%E6%8F%90%E6%88%90"><span class="toc-number">1.6.3.3.</span> <span class="toc-text">3. 交易提成</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-7-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.7.</span> <span class="toc-text">1.7 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>