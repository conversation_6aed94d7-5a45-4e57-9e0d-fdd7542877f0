---
title: SpringAI（四）：4. 深入 Prompt 工程与结构化输出
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 51850
date: 2025-03-21 01:13:45
---

## **4. 深入 Prompt 工程与结构化输出**

### **引言：当程序员遇上简历筛选**

> “这个候选人到底是什么水平？”

这个问题，往往能让我们在堆积如山的简历中反复阅读、查找关键信息，最后得出一个模糊的印象。本章，我们将用技术解决这个难题：通过深入探索 Prompt 工程，让大语言模型成为您的私人招聘助理，并按我们指定的、精确的“剧本”格式，输出结构化的候选人报告。

### **4.1 Prompt 的重要性与基本原则**

如果说 `ChatClient` 是连接你和 AI 的电话线，那么 **Prompt 就是你在这条电话线上说的话**。你说得是否清晰、准确、有技巧，直接决定了电话那头的 AI 能否理解你的意图并给出满意的答复。Prompt 工程（Prompt Engineering）就是一门研究如何与 AI 高效沟通的艺术与科学。

> **Prompt 是与 AI 沟通的 API**。就像调用一个软件 API 需要遵循其定义的参数和格式一样，与 AI 沟通也需要遵循一定的范式，才能获得稳定、可控的输出。

以下是编写高效 Prompt 的几个基本原则：

1.  **清晰具体**：避免使用模糊的词语。
2.  **提供上下文**：如果问题需要背景知识，请在 Prompt 中提供。
3.  **设定角色**：这是最有效的技巧之一，可以极大地约束 AI 的行为和语言风格。
4.  **施加约束**：明确告诉模型你**不**想要什么，或者输出必须遵循的格式。
5.  **提供示例**：如果需要模型遵循特定的输出格式或风格，最好的方法就是给它一两个例子。

### **4.2 `Prompt` 与 `Message` 详解**

#### **4.2.1 `Prompt` 类：与 AI 对话的标准容器**

在 Spring AI 中，`org.springframework.ai.chat.prompt.Prompt` 类是所有与 `ChatClient` 交互的载体。它不仅仅是一个简单的字符串包装器，而是一个结构化的对象，用于封装发送给模型的完整指令集。

你可以把 `chatClient.prompt().user("...")` 这种便捷方式看作是寄一个**信封**，而使用 `new Prompt(...)` 则是打包一个可定制的**快递包裹**，功能更强大。

| 构造函数 | 说明 |
| :--- | :--- |
| **`Prompt(String contents)`** | 最简单的形式，内部会将字符串包装成一个 `UserMessage`。 |
| **`Prompt(List<Message> messages)`** | **最通用、最强大**的形式。允许传入一个由不同角色消息组成的列表。 |
| **`Prompt(..., PromptOptions options)`**| 在以上形式的基础上，附加一次性的、请求级别的模型参数。 |

#### **4.2.2 `Message` 接口：定义对话角色**

`Message` 是构成 `Prompt` 的基本单位。Spring AI 定义了四种核心的 `Message` 类型。

| 消息类型 (Message Type) | 核心作用与职责 |
| :--- | :--- |
| **`SystemMessage`** | **系统指令**：用于设定 AI 的角色、行为准则、个性、目标和任何高级指令。 |
| **`UserMessage`** | **用户输入**：代表最终用户的提问、指令或对话内容。 |
| **`AssistantMessage`** | **助手回复**：代表 AI 自己**之前**的回复，是构建多轮对话历史的关键。 |
| **`FunctionMessage`** | **函数结果**：用于函数调用场景，将外部工具的执行结果返回给 AI。 |

### **4.3 `PromptTemplate`：让 Prompt 动起来**

`PromptTemplate` 允许你定义一个包含占位符（如 `{variable}`）的模板字符串，然后用一个 `Map` 来填充这些变量，最终渲染出一个完整的 `Prompt` 对象，让你的 Prompt 变得动态和可复用。

#### **4.3.1 API 详解**

| 方法 | 说明 |
| :--- | :--- |
| **`PromptTemplate(String template)`** | 构造函数，传入包含占位符的模板字符串。 |
| **`create(Map<String, Object> model)`** | 接收一个 Map，用 Map 中的键值对替换模板中的占位符，并返回一个完整的 `Prompt` 对象。 |

#### **4.3.2 实战：动态翻译器**

我们将为 `ChatService` 添加一个新方法，该方法可以根据传入的目标语言，动态地构建一个翻译 Prompt。

1.  **在 `ChatService.java` 中添加新方法**

    ```java
    // src/main/java/com/copilot/aicopilotbackend/service/ChatService.java
    @Service
    public class ChatService {
        // ... 原有方法 ...
        public Flux<String> getDynamicTranslatedStream(String targetLanguage, String text) {
            String systemPromptTemplate = """
                你是一个专业的、精通多种语言的翻译家。
                请将用户接下来发送的所有内容都翻译成 {targetLanguage}。
                不要添加任何与翻译结果无关的解释或寒暄。
                """;
            return chatClient.prompt()
                    .system(spec -> spec.text(systemPromptTemplate).param("targetLanguage", targetLanguage))
                    .user(text)
                    .stream()
                    .content();
        }
    }
    ```

2.  **在 `ChatController.java` 中添加新端点**

    ```java
    // src/main/java/com/copilot/aicopilotbackend/controller/ChatController.java
    @RestController
    @RequestMapping("/api/v1/chat")
    public class ChatController {
        // ... 原有方法 ...
        @GetMapping(value = "/translate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
        public Flux<String> translate(@RequestParam String targetLanguage, @RequestParam String text) {
            return chatService.getDynamicTranslatedStream(targetLanguage, text);
        }
    }
    ```

### **4.4 结构化输出：将 AI 响应映射为 POJO**

现在，我们进入本章的核心。我们将学习如何让 AI 从一个自由的“创作者”，转变为一个能精确输出结构化数据的“数据工程师”。

#### **4.4.1 烦恼与优势：为何需要结构化？**

**1. 原始输出的烦恼**

如果我们直接要求 AI 总结一份简历，得到的可能是对程序极不友好的纯文本：

> "这位候选人叫张三，看起来有超过8年的Java开发经验，技能方面，他提到了Spring Boot、微服务和Docker..."

**问题显而易见**：

  * **格式不稳定**：每次请求的措辞和格式都可能变化。
  * **难以解析**：我们需要编写脆弱的正则表达式来提取“8年”、“Java”等关键词。
  * **无法直接使用**：无法直接将这段文本映射到我们的 `Candidate` 对象上。

**2. 结构化的优势**

如果我们能让 AI 直接输出 JSON，程序就可以无缝解析，无需任何复杂的后处理。

```json
{
  "name": "张三",
  "yearsOfExperience": 8,
  "skills": ["Java", "Spring Boot", "Microservices", "Docker"]
}
```

Spring AI 正是通过在**提示词中追加格式化指令**，并结合内置的**转换器（Converter）**来实现这一目标的。

#### **4.4.2 API 详解：`OutputConverter` 家族**

| 转换器 (Converter) | 输出类型 | 核心用途 |
| :--- | :--- | :--- |
| **`BeanOutputConverter<T>`** | `T` (您自定义的 POJO) | **最常用、最强大**。能将 AI 的响应直接映射到一个定义好的 Java 类或 Record。 |
| **`MapOutputConverter`** | `Map<String, Object>`| 当输出的键值对不固定时使用。 |
| **`ListOutputConverter`** | `List<String>` | 当您只需要一个简单的字符串列表时。 |

#### **4.4.3 BeanOutputConverter实战：从简历中提取结构化数据**

我们将构建一个全新的、独立的服务，用于演示如何从非结构化的简历文本中，提取出结构化的 `Candidate` 信息。

**1. 定义数据模型 (`dto/request/Candidate.java`)**

```java
// src/main/java/com/copilot/aicopilotbackend/dto/request/Candidate.java
package com.copilot.aicopilotbackend.dto.request;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;

public record Candidate(
        @JsonPropertyDescription("候选人姓名") String name,
        @JsonPropertyDescription("候选人工作年限") int yearsOfExperience,
        @JsonPropertyDescription("候选人掌握的技能列表") List<String> skills
) {}
```

**2. 创建解析服务 (`service/ExtractionService.java`)**

```java
// src/main/java/com/copilot/aicopilotbackend/service/ExtractionService.java
package com.copilot.aicopilotbackend.service;

import com.copilot.aicopilotbackend.dto.request.Candidate;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.stereotype.Service;
import java.util.Map;

@Service
public class ExtractionService {

    private final ChatClient chatClient;

    public ExtractionService(ChatClient chatClient) {
        this.chatClient = chatClient;
    }

    public Candidate extractCandidateFrom(String resumeText) {
        var outputConverter = new BeanOutputConverter<>(Candidate.class);
        String formatInstructions = outputConverter.getFormat();

        String promptTemplateString = """
                从下面的简历文本中提取信息。
                {format}
                简历文本:
                {resume}
                """;
        PromptTemplate promptTemplate = new PromptTemplate(promptTemplateString);
        Prompt prompt = promptTemplate.create(Map.of(
                "resume", resumeText,
                "format", formatInstructions
        ));

        return chatClient.prompt(prompt)
                .call()
                .entity(outputConverter);
    }
}
```

**3. 创建 API 端点 (`controller/ExtractionController.java`)**

```java
// src/main/java/com/copilot/aicopilotbackend/controller/ExtractionController.java
package com.copilot.aicopilotbackend.controller;

import com.copilot.aicopilotbackend.dto.request.Candidate;
import com.copilot.aicopilotbackend.service.ExtractionService;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/extraction")
public class ExtractionController {

    private final ExtractionService extractionService;

    public ExtractionController(ExtractionService extractionService) {
        this.extractionService = extractionService;
    }

    @PostMapping("/candidate")
    public Candidate extractData(@RequestBody Map<String, String> request) {
        return extractionService.extractCandidateFrom(request.get("resumeText"));
    }
}
```

**4. 运行与测试**

  * **请求体**:
    ```json
    {
      "resumeText": "张三是一名资深软件工程师，拥有超过8年的Java开发经验。他精通Spring Boot, Microservices, 和 Docker技术。"
    }
    ```
  * **预期 JSON 响应**:
    ```json
    {
      "name": "张三",
      "yearsOfExperience": 8,
      "skills": [ "Java", "Spring Boot", "Microservices", "Docker" ]
    }
    ```

#### **4.4.4 ListOutputConverter扩展：处理集合类型**

`BeanOutputConverter` 的强大之处在于它也能处理泛型集合。现在我们举一个电影推荐案例，来展示如何输出一个 `List<Film>`

**1. 定义 `Film` DTO (`dto/response/Film.java`)**

```java
// src/main/java/com/copilot/aicopilotbackend/dto/response/Film.java
package com.copilot.aicopilotbackend.dto.response;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;

public record Film(
    @JsonPropertyDescription("电影名称") String name,
    @JsonPropertyDescription("上映时间") String releaseDate,
    @JsonPropertyDescription("导演名称") String directorName,
    @JsonPropertyDescription("电影简介") String desc
) {}
```

**2. 在 `FilmService` 和 `FilmController` 中实现**

对于Enity来说：大模型输出转换为实体对象看起来还是比较复杂的，不过Spring AI还提供更简易的方式：直接在 `call() `后面调用 `entity `，把对应的class类型传递进去即可， **并且在提示词中** `**{format}** `**占位符不需要再手动添加** 。

```java
package com.copilot.aicopilotbackend.controller;

import com.copilot.aicopilotbackend.dto.response.Film;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
@RestController
@RequestMapping("api/v1/films")
public class FilmController {
    private final ChatClient chatClient;

    public FilmController(ChatClient chatClient) {
        this.chatClient = chatClient;
    }

//    @GetMapping("films")
//    /**
//     * 普通方法实现，并不优雅
//     */
//    public List<Film> getFilmsByActor(String actor) {
//        // 使用 ParameterizedTypeReference 来精确指定我们期望的复杂泛型类型
//        var converter = new BeanOutputConverter<>(new ParameterizedTypeReference<List<Film>>() {});
//        String format = converter.getFormat();
//        String userMessage = "帮我找五部 {actor} 主演的电影。 {format}";
//        Prompt prompt = new PromptTemplate(userMessage).create(Map.of("actor", actor, "format", format));
//        String content = chatClient.prompt(prompt).call().content();
//        return converter.convert(content);
//    }

    /**
     * 使用更优雅的 .entity() 写法
     * @param actor
     * @return
     */
    @GetMapping("as-list")
    public List<Film> getFilmsByActor(String actor) {
        return  chatClient.prompt()
                .user(u-> u.text("帮我找五部 {actor} 主演的电影").param("actor", actor))
                .call()
                .entity(new ParameterizedTypeReference<List<Film>>() {});
    }


}

```

##### 接口文档

**示例请求**

```
http://localhost:8080/api/v1/films/as-list?actor=汤姆·汉克斯
```

**响应示例**

成功返回的响应体将是一个电影对象列表，示例如下：

```json
[
    {
        "name": "荒岛余生",
        "releaseDate": "2000-12-22",
        "directorName": "罗伯特·泽米吉斯",
        "desc": "一名联邦快递员工在南太平洋荒岛求生的故事"
    },
    {
        "name": "阿甘正传",
        "releaseDate": "1994-07-06",
        "directorName": "罗伯特·泽米吉斯",
        "desc": "智商仅相当于孩童的善良男子见证美国近代历史的故事"
    },
    {
        "name": "阿波罗13号",
        "releaseDate": "1995-06-30",
        "directorName": "朗·霍华德",
        "desc": "宇航员在月球执行任务时遭遇意外的故事"
    },
    {
        "name": "玩具总动员",
        "releaseDate": "1995-11-22",
        "directorName": "约翰·拉塞特",
        "desc": "玩具牛仔胡迪和太空战警巴斯光年的冒险故事"
    },
    {
        "name": "达芬奇密码",
        "releaseDate": "2006-05-19",
        "directorName": "朗·霍华德",
        "desc": "哈佛大学教授破解中世纪符号学的惊悚故事"
    }
]
```

-----

#### **4.4.5 拓展：`MapOutputConverter` - 输出动态键值对**

我们已经学会了如何将AI的响应输出为单个对象（`Film`）或对象列表（`List<Film>`）。但有时，我们希望输出的JSON的**键（key）本身就是动态的**。例如，以电影名作为键，电影信息作为值，来构建一个 `Map`。

`MapOutputConverter` 正是为此类场景而生。它适用于当您需要一个灵活的、键值对不固定的 `Map<String, Object>` 作为输出，而不想为此创建专门的Java类时。

**1. 在 `FilmService.java` 中添加新方法**

我们将添加一个 `getFilmsAsMap` 方法，要求 AI 以电影名为键来组织返回的数据。

正常的写法是这样的，但是使用Entity为核心的写法我们就无需指定`mapOutputConverter`了

```java
/**
 * 使用 MapOutputConverter 获取以电影名为键的电影信息 Map
 */
public Map<String, Object> getFilmsAsMap(String style) {
    var mapOutputConverter = new MapOutputConverter();
    
    String userMessage = """
        帮我找五部{style}的电影，以电影名为分组键，值为电影信息。
        电影信息需要包含电影名称、上映时间、导演名、电影简介等内容。
        {format}
        """;
    
    String format = mapOutputConverter.getFormat();
    Prompt prompt = new PromptTemplate(userMessage).create(Map.of("style", style, "format", format));
    
    String content = chatClient.prompt(prompt).call().content();
    return mapOutputConverter.convert(content);
}
```



**在 `FilmController.java` 中添加新端点**

```java
@GetMapping("/as-map")
public Map<String, Object> getFilmsAsMap(@RequestParam(defaultValue = "华语流行") String style) {
    // 为了简洁，我们直接使用 .entity() 的优雅写法
    String userMessage = """
            帮我找五部{style}的电影，以电影名为分组键，值为电影信息。
            电影信息需要包含电影名称、上映时间、导演名、电影简介等内容。
            """;
    return chatClient.prompt()
            .user(u -> u.text(userMessage).param("style", style))
            .call()
            .entity(new ParameterizedTypeReference<Map<String, Object>>() {
            });
}
```

启动应用后，访问此新端点 `http://localhost:8080/api/v1/films/as-map?style=经典武侠`。

  * **预期 JSON 响应 (内容可能不同)**：

    ```json
    {
        "东邪西毒": {
            "电影名称": "东邪西毒",
            "上映时间": "1994-09-17",
            "导演名": "王家卫",
            "电影简介": "讲述欧阳锋、黄药师等江湖人物的爱恨情仇与宿命纠葛。"
        },
        "新龙门客栈": {
            "电影名称": "新龙门客栈",
            "上映时间": "1992-08-27",
            "导演名": "徐克",
            "电影简介": "明朝东厂与江湖侠客在龙门客栈展开生死对决的故事。"
        },
        "笑傲江湖": {
            "电影名称": "笑傲江湖",
            "上映时间": "1990-01-27",
            "导演名": "胡金铨",
            "电影简介": "令狐冲卷入江湖纷争，最终领悟武学真谛的传奇故事。"
        },
        "卧虎藏龙": {
            "电影名称": "卧虎藏龙",
            "上映时间": "2000-07-07",
            "导演名": "李安",
            "电影简介": "围绕青冥剑展开的江湖恩怨与儿女情长的武侠传奇。"
        },
        "英雄": {
            "电影名称": "英雄",
            "上映时间": "2002-12-19",
            "导演名": "张艺谋",
            "电影简介": "刺客无名向秦王讲述刺杀真相的多重视角武侠史诗。"
        }
    }
    ```

-----

#### **4.4.6 深度揭秘：`ParameterizedTypeReference` 的“魔法”**

您一定很好奇，为什么在处理 `List<Film>` 或 `Map<String, Object>` 这样的复杂类型时，我们不能像 `Film.class` 那样直接传递 `List<Film>.class`，而是需要用到 `new ParameterizedTypeReference<List<Film>>() {}` 这样看起来有些古怪的语法？

答案在于 Java 语言的一个核心特性：**类型擦除 **。

**1. 问题根源：类型擦除**

我们可以用一个简单的比喻来理解它：

> 想象一下您要寄一个快递。在打包时，您在箱子上贴了一个标签：“易碎品：玻璃花瓶”（这就像是**编译时**的泛型类型 `List<Film>`）。编译器（打包员）会检查您的标签是否正确。
>
> 但是，一旦您的包裹被放上快递车，快递员（**运行时**的 JVM）只关心这是“一个包裹”（一个原始的 `List`)，而不再关心里面具体是“玻璃花瓶”还是“铁块”。箱子上关于“玻璃花瓶”的具体信息，在运输过程中被“擦除”了。

这就是类型擦除。在运行时，JVM 并不知道一个 `List` 对象的泛型参数具体是 `Film` 还是 `String`。因此，像 `List<Film>.class` 这样的语法在 Java 中是**非法**的，因为在运行时根本不存在一个与 `List<String>.class` 相区别的 `List<Film>.class`，它们都是同一个 `List.class`。

这就给我们的程序带来了麻烦：当 `chatClient` 返回一段 JSON 数组时，它需要知道是应该把这个数组里的每个元素都转成 `Film` 对象，还是转成别的什么对象。如果只给它 `List.class`，信息就不足了。

**2. 解决方案：“超级类型令牌” 的巧计**

`ParameterizedTypeReference` 正是绕过类型擦除的“天才魔术”。这个魔术的核心在于书写的那个看似多余的大括号 `{}`。

当您写下 `new ParameterizedTypeReference<List<Film>>() {}` 时，做的**不仅仅是**创建一个对象，而是**创建了一个匿名的内部类**！

这个匿名内部类是这样的：

```java
// 代码:
new ParameterizedTypeReference<List<Film>>() {};

// 编译器在背后实际创建了一个类似这样的东西:
class SomeAnonymousClass extends ParameterizedTypeReference<List<Film>> {
    // 类体是空的
}
```

**这里的“魔法”就发生了**：虽然 Java 会擦除字段和方法参数的泛型信息，但它**会把一个类所继承的父类（或实现的接口）的泛型信息，作为元数据永久地记录在子类的 `.class` 文件中**。

所以，`SomeAnonymousClass` 这个匿名类，它永远地“记住”了自己是 `ParameterizedTypeReference<List<Film>>` 的一个子类。

**Spring 框架就可以利用这一点来“反向破解”：**

1.  `.entity()` 方法接收到您创建的这个匿名内部类的实例。
2.  它通过 Java 的**反射**机制，调用这个实例的 `getClass().getGenericSuperclass()` 方法。
3.  这个方法会返回一个 `ParameterizedType` 对象，Spring 可以从这个对象中，安全地、精确地提取出那个“被擦除”的泛型参数——`List<Film>`。
4.  “魔法”完成！Spring 现在知道了您想要的完整类型，从而可以正确地将 JSON 数组反序列化为您期望的 `List<Film>` 对象。





---