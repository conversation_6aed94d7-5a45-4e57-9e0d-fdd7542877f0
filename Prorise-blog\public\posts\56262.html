<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理入门（二）：第二章：需求收集与管理 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理入门（二）：第二章：需求收集与管理"><meta name="application-name" content="产品经理入门（二）：第二章：需求收集与管理"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理入门（二）：第二章：需求收集与管理"><meta property="og:url" content="https://prorise666.site/posts/56262.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第二章：需求收集与管理2.1 什么是需求在我的产品经理生涯中，我始终认为，一切工作的起点和终点都是“需求”。如果我们对需求的理解出现了偏差，那么后续无论多精美的设计、多优秀的技术，都只是在错误的地基上建造楼阁，最终难免会坍塌。那么，到底什么是需求？让我们一起深入地探索它的本质。 2.1.1 学习目标"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta name="description" content="第二章：需求收集与管理2.1 什么是需求在我的产品经理生涯中，我始终认为，一切工作的起点和终点都是“需求”。如果我们对需求的理解出现了偏差，那么后续无论多精美的设计、多优秀的技术，都只是在错误的地基上建造楼阁，最终难免会坍塌。那么，到底什么是需求？让我们一起深入地探索它的本质。 2.1.1 学习目标"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/56262.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理入门（二）：第二章：需求收集与管理",postAI:"true",pageFillDescription:"第二章：需求收集与管理, 2.1 什么是需求, 2.1.1 学习目标, 2.1.2 公司背景与项目背景, 2.1.3 什么是需求（案例）, 2.1.4 需求的常见形式, 2.1.5 需求的定义, 2.2 需求如何收集, 2.2.1 学习目标, 2.2.2 需求的来源, 2.2.3 需求收集方式分类, 2.2.4 常见的需求收集方法, 2.2.5 竞品分析, 1. 竞品的定义与分类, 2. 竞品分析方法, 3. 竞品分析的适用场景, 2.2.6 用户访谈, 1. 用户访谈的定义与流程, 2. 访谈问题设计要点, 3. 用户访谈示例, 4. 注意事项及适用场景, 2.2.7 实地调研, 1. 实地调研的定义, 2. 如何进行实地调研, 3. 实地调研适用场景, 2.3 需求管理, 2.3.1 学习目标, 2.3.2 需求池定义, 2.3.3 需求状态, 2.3.4 需求池的作用, 2.3.5 需求池管理原则, 2.4 本章总结第二章需求收集与管理什么是需求在我的产品经理生涯中我始终认为一切工作的起点和终点都是需求如果我们对需求的理解出现了偏差那么后续无论多精美的设计多优秀的技术都只是在错误的地基上建造楼阁最终难免会坍塌那么到底什么是需求让我们一起深入地探索它的本质学习目标在本节中我的核心目标是帮助我们建立起对需求的深度认知我希望在本节学习结束后我们都能具备一种穿透能力能够穿透用户表面的只言片语直达他们内心深处真正的未被言说的渴望与痛点公司背景与项目背景在讨论理论之前我习惯先设定一个场景因为脱离了场景谈需求就如同无源之水让我们虚构一个案例背景以便更好地代入思考公司背景我们是一家生鲜食材供应商通过自研的线上平台为全国数千家餐厅提供每日的食材采购与配送服务项目背景近期平台的客服部门收到了大量餐厅采购员的抱怨普遍反映我们的下单流程烦琐效率低下因此公司决定立项由我来负责优化平台的下单体验提升客户满意度和下单频次什么是需求案例好了背景就绪现在作为这个项目的我开始接触来自一线用户的声音在我的经验里这些声音也就是最初始的需求通常以三种面貌出现案例用户以提问题方式给出的需求一位用户在的反馈区留言每天都不知道我要吃啥我怎么才能知道今天应该点什么外卖呢我的解读用户提出的问题是他目前面临的问题或许我们可以推出一个简单的小插件入口随机抽取今天应该吃什么案例用户以提目的方式给出的需求一位用户反馈说我每天吃饭的预算有限希望平台能够让我自己快速点到块钱以内的外卖我的解读这位用户给了我一个非常明确的目的快速找到元以内的外卖这是一个清晰的待满足的诉求我的工作就是思考如何最好地帮他达成这个目的是增加一个价格区间筛选还是专门开辟一个平价专区案例用户以提方案方式给出的需求一位用户希望我们做一个智能推荐功能他描述道只要一点就进入这个推荐就提供价钱选择还有结合自己的口味和当下季节以及哪个地方的给推荐性价比口碑最好的外卖并且支持点击一下就自动付款下单的功能我的解读这是一位高阶用户他不仅提了目的甚至帮我把完整的产品方案都设计好了这恰恰是最需要我警惕和深度分析的情况他这个宏大的方案里其实包含了多个本质目的的集合我懒得选要智能推荐我要省钱要性价比我要好吃要口碑好我要方便要一键下单我的职责不是照抄这个方案而是将这些本质目的拆解开来评估实现难度和用户价值设计出更合理更可落地的产品方案需求的常见形式通过上面三个小案例我们可以总结出用户最原始的需求通常以三种形式传递给我们我把它们整理成了下面的表格方便我们记忆形式用户表达方式我的解读应对思路提问题为什么怎么用户在某个操作中遇到了困难感到困惑我需要追问定位他被卡住的场景和具体痛点提目的我想要我希望能用户明确表达了期望达成的效果我需要思考达成这个目的有哪些可能的路径和方案提方案你应该加个只要做个用户给出了自认为的解决方案我需要翻译这个方案是为了解决什么问题有没有更好的方案需求的定义那么综合以上所有我来给出我对需求的最终定义我认为需要区分两个概念原始需求即用户直接表达出来的未经加工的问题目的或方案它是我们工作的输入和起点产品需求这是我们产品经理经过分析挖掘转化后真正应该去做的东西我对它的定义是在特定场景下为满足用户的本质目的我们所设计出的一套完整的解决方案所以我的工作从来不是对用户的原始需求照单全收而是要经历一个原始需求本质目的产品需求的深度转化过程这趟旅程的质量决定了我们最终产品的成败需求如何收集我们已经深刻理解了需求的本质知道它藏在用户的只言片语背后那接下来的问题就是我们该去哪里以及如何才能高效地把这些藏着的需求挖掘出来这就是需求收集的工作在我看来这绝不是一个被动等待的过程而是一项需要我们主动出击运用多种侦查手段的系统工程学习目标在这一节我的目标是为我们装备一套实用的需求挖掘工具箱我将带大家梳理需求的来源并详细介绍几种我最常用且行之有效的收集方法包括但不限于竞品分析用户访谈等学完本节我希望我们都能根据不同的目的和场景自信地选择并运用最合适的工具需求的来源在动手挖掘之前我们先要画出藏宝图明确需求可能藏在哪些地方我习惯将所有的需求来源归为两大类外部需求和内部需求一名优秀的产品经理必须同时对这两个方向保持敏锐外部需求这类需求来自于我们公司围墙之外是市场和用户的直接声音它包括用户通过用户访谈反馈调研等直接获取客户对于端产品这是指付费客户提出的具体要求竞品通过分析竞争对手的动向和功能市场行业宏观的政策变化技术趋势社会热点等内部需求这类需求源自于公司内部的各个协作方通常服务于公司的战略和商业目标它包括老板管理层基于公司战略发展提出的方向性要求运营市场团队为支撑某项运营活动或营销策略而提出的产品需求销售客服团队来自一线炮火声为解决客户问题或促进销售而提出的需求技术设计团队出于提升系统性能优化架构统一设计规范等内部优化目的提出的需求需求收集方式分类了解了需求的来源我们就要选择具体的挖掘工具了在此之前我先把这些工具也就是收集方法从性质上分为两类定性方式和定量方式想清楚用哪类方式能让我们的目标更明确定性方式我用它来回答为什么当我需要深入探索用户的动机感受行为背后的原因时我会采用定性方式它的特点是样本小但洞察深比如我可以通过访谈真正理解一个用户为什么对我的产品感到不爽定量方式我用它来回答是什么和有多少当我想验证一个假设或者了解某个现象的普遍性时我会采用定量方式它的特点是样本大且结果可以被统计能反映普遍规律比如我可以通过问卷了解到底有百分之多少的用户认为我的产品不好用为了方便你理解我总结了下面的表格方式分类核心目的特点常用方法举例定性方式探究为什么深入有背景样本小无法统计用户访谈实地调研可用性测试定量方式度量是什么有多少广泛可统计样本大结论客观问卷调查数据分析测试常见的需求收集方法在我的工具箱里有许多种具体的需求收集方法经过多年的实践我筛选出了最高效最常用的一批它们几乎能覆盖我工作中的场景这些方法包括用户访谈通过对于用户的访谈掌握需求问卷调查通过发放调查问卷来调查竞品分析通过竞争对手身上所知头脑风暴与团队进行奇思妙想观察法观察身边的情况实地体验观察法对于实际地点去实地调研数据分析通过已有或网络的现成数据进行分析在接下来的小节中我将重点挑选其中几个最为核心的方法为大家进行详细的拆解和说明竞品分析我有一个观点我们永远不应该闭门造车竞品分析对我来说不是为了抄袭而是为了站在巨人的肩膀上洞察我们所处的战场格局从他人的成败中学习最终找到我们自己独特的取胜之道竞品的定义与分类首先我们要明确谁是我们的竞品我的定义很简单任何正在与我们争夺同一批目标用户的时间或金钱的产品都是我们的竞品在分析时我不会把所有竞品混为一谈而是习惯将他们分为三类采取不同的应对策略直接竞品这是最显而易见的对手我们的目标用户产品形态和核心功能都高度重叠比如如果我是做美团外卖的那饿了么就是我的直接竞品我们是在同一个赛道里进行着刺刀见红的肉搏间接竞品他们的目标用户和我们有重叠但是满足用户需求的产品形态或解决方案不同比如对于外卖平台方便蜂等便利店甚至叮咚买菜这样的生鲜电商都是我的间接竞品他们都在解决用户足不出户解决吃饭问题这个需求潜在竞品这类产品目前和我们没有直接竞争但未来有可能凭借其资源技术或用户规模跨界进入我们的领域比如一个拥有海量流量的社交巨头如果某天宣布要大力发展本地生活服务那它就会立刻成为我最警惕的潜在竞品为了方便我们快速识别我总结了下面的表格竞品分类核心特征举例假设我们是微信直接竞品目标用户产品形态核心功能都高度相似钉钉在办公场景下间接竞品满足用户的同一类核心需求但方案不同抖音争夺用户时长电话短信解决通信需求潜在竞品目前无竞争但未来可能进入市场的重量级玩家一个新兴的技术驱动的社交创业公司竞品分析方法明确了要分析谁下一步就是如何分析仅仅是截几张图看几个功能是远远不够的我需要一个框架来保证分析的系统性和深度我个人最推崇的是用户体验五要素模型它能帮我像剥洋葱一样从表到里地把一个产品彻底解构表现层这是最表层的用户能直接感知的视觉设计包括配色字体图标布局的美感等框架层这是界面的骨架决定了信息在页面上的排布比如按钮放哪里搜索框放哪里导航怎么设计结构层这是产品的流程和信息架构用户从一个页面如何跳转到另一个页面产品的功能模块是如何组织的范围层这是产品具体包含了哪些功能和内容比如一个电商它的范围层就包括了商品展示购物车订单支付等一系列功能战略层这是最核心的产品的商业目标和用户需求是什么它为什么要做这个产品当我用这五个层次去分析一个竞品时我看到的就不再是一个个孤立的界面而是其背后完整的产品思考和商业逻辑竞品分析的适用场景我做竞品分析从不是为了分析而分析一定是带有明确目的的以下是我认为最有价值的几个应用场景了解行业当我刚进入一个新领域时我会把市面上的竞品用五要素模型完整地分析一遍这是我快速了解行业格局用户现状和主流玩法的最佳途径产品设计在设计某个具体功能时比如购物车我一定会去体验至少个主流的购物车是怎么设计的我的目的不是抄而是去归纳总结了解业界成熟的设计模式避免重复造轮子和踩坑寻找差异化通过对主要竞品的优劣势分析比如使用模型我可以清晰地看到市场上的空白地带和未被满足的需求这对于我们制定差异化竞争策略找到自己的生态位至关重要方案验证如果我的某个直接竞品上线了一个新功能并且获得了很好的市场反馈那它在某种程度上帮我验证了这个功能背后的用户需求是真实存在的反之如果竞品的功能失败了那它也等于免费给我上了一课用户访谈在我看来数据能告诉我用户做了什么但只有用户访谈能告诉我他们为什么这么做它是产品经理建立用户同理心挖掘深层次需求的终极武器没有任何工具可以替代用户访谈的定义与流程用户访谈的定义对我而言它是一场有目的有结构的一对一的深度对话我通过这场对话来探寻用户在特定场景下的行为动机态度和痛点它是一种定性研究方法我追求的是洞察的深度而非样本的数量一场专业的访谈绝不是一次随意的聊天它需要我进行精心的策划和准备我通常会遵循一个六步走的流程确定访谈形式首先我要决定访谈的方式是成本较高但信息丰富的线下面对面还是高效便捷的电话线上视频明确访谈目的在开始前我必须能用一句话说清楚我这次访谈想解决的核心问题是什么例如探究用户在深夜场景下点外卖的核心决策因素用户筛选我需要找到对的人根据我的访谈目的我会设定清晰的用户标准比如年龄使用频率所在城市等然后通过问卷或后台数据进行筛选设计访谈问题这是访谈的灵魂我会提前准备一份访谈提纲里面包含了一系列精心设计的开放式问题邀请用户访谈我会正式地联系并邀请筛选出来的用户说明我们的目的时长并通常会提供一些小礼品如礼品卡代金券作为答谢结果汇总与分析访谈结束后我会立刻整理访谈记录然后将多次访谈的结果放在一起寻找其中反复出现的模式观点和痛点最终提炼出有价值的洞察访谈问题设计要点访谈的成败很大程度上取决于我问题的质量我设计问题时会重点把握以下几点问题设置的方向我的访谈问题通常会遵循现状痛点方案的逻辑顺序展开层层递进现状类问题用于破冰了解用户当下的行为和场景如能带我回忆一下您上一次点外卖的全过程吗痛点类问题用于挖掘用户的挫折和不满如在刚才您描述的整个过程中有没有哪个环节让您觉得特别麻烦或者不爽方案期望类问题用于探寻用户的期望和潜在需求如如果抛开所有限制您心目中最理想的外卖应该是什么样的问题设计的方式多问开放式问题我从不问你喜欢我们的吗这类可以用是否回答的问题我会问关于我们的你有什么样的使用体验和感受不断追问当用户提到一个关键点时我最常使用的工具就是追问为什么可以再多讲讲吗后来呢这能帮我挖得更深避免引导性提问我绝不会问你是不是觉得我们的红包功能很难找这会把我的观点强加给用户我会问您平时会使用我们的红包功能吗可以聊聊您使用它的过程吗用户访谈示例我们还用外卖平台的案例假设我的访谈目的是了解白领用户在办公室选择午餐外卖的决策过程我的问题可能会这样设计现状您可以回忆一下昨天中午点外卖的经历吗从你想到要点外卖到最后拿到外卖都发生了什么痛点在挑选餐厅和菜品的过程中有没有哪个环节让您觉得很纠结或者浪费时间追问您刚才提到选来选去最后还是点了常吃的那家为什么会这样呢期望如果我们可以帮您解决选择困难这个问题您希望我们怎么做注意事项及适用场景我的注意事项多听少说访谈的主角是用户我的任务是引导和倾听保持中立无论用户怎么夸奖或吐槽我的产品我都要保持客观不争辩不解释事实与观点分离记录时要严格区分哪些是用户说的事实哪些是我自己的分析和判断适用场景我通常会在项目的早期探索阶段大量使用用户访谈因为这时我对用户和问题还很模糊需要建立认知此外在新功能的构思和验证阶段我也会通过访谈向用户展示原型或概念来快速获取反馈为了方便我们回顾我将用户访谈的核心要点总结在下面的表格里核心环节我的关键动作访谈前明确目的筛选用户设计开放式问题提纲访谈中多听少说像海绵一样吸收信息通过不断追问来深挖保持中立不评判访谈后及时整理笔记寻找共性将零散的观点提炼为洞察实地调研如果说用户访谈是听其言那么实地调研就是观其行在我看来这是两种方法最大的区别很多时候用户说的和他实际做的并不完全一致而实地调研就是让我有机会亲眼去见证这种差异发现那些连用户自己都未曾察觉的隐性需求实地调研的定义对我而言实地调研就是产品经理亲自进入用户的真实物理场景中通过近距离观察和亲身体验来理解用户行为和背后动机的一种研究方法它包含两种核心形式观察法我像一个隐形人在不打扰用户的前提下静静地观察他在特定场景下是如何与环境工具包括我们的产品进行互动的实地体验我亲自扮演用户的角色走一遍他完整的任务流程切身感受他在每个环节的顺畅与阻碍如何进行实地调研一次有效的实地调研需要我像导演一样精心设计整个过程我通常会遵循以下四个步骤进入场景这是第一步也是最关键的一步比如我要研究餐厅后厨的采购流程那我就必须真的穿上工作服走进那个潮湿繁忙的后厨而不是坐在办公室里想象用户角色进入场景后我要明确我的角色我是作为一名旁观者去观察还是亲自上手作为一名学徒去体验整个下单验货入库的流程观察体会在场景中我的所有感官都要打开我会重点观察用户在做什么他使用了什么工具他与其他人是如何协作的他在哪个环节面露难色哪个环节的效率特别低如果是我自己体验我会记录下每一步的感受持续进行一次调研是远远不够的我会选择不同时间不同类型的场景比如高峰期与平峰期的餐厅后厨进行多次调研以确保我看到的不是偶然现象而是普遍存在的问题实地调研适用场景实地调研虽然强大但成本也很高所以我必须在最需要它的地方对症下药以下是我最常使用它的几个场景挖掘需求当我要为一个全新的领域比如智慧农业设计产品时我对用户的真实作业环境一无所知这时实地调研是建立基础认知的唯一途径理解需求当用户向我提了一个我无法理解的需求时比如你们的扫码枪不好用我会直接去他的仓库看他到底是怎么用的问题出在哪里效果验证我的新功能上线后我会去现场观察用户是如何使用它的是否符合我的设计预期有没有出现我没想到的问题寻找问题当我的产品数据出现异常比如某个环节转化率突然下降我会去实地观察看看是不是用户的线下操作流程发生了变化从而导致了线上的问题实地调研是我们产品经理走出办公室拥抱真实世界的最佳方式我把它总结为以下要点核心问题我的关键动作什么是实地调研亲自进入用户的真实场景通过观察和体验来理解用户的真实行为如何进行实地调研进入场景代入角色细心体会持续进行这是一个完整的闭环需求管理对我来说如果说需求收集是狩猎那么需求管理就是庖丁解牛和精细烹饪我需要一个系统化的流程和工具来处理我收集到的所有食材需求确保最有价值的部分能被优先端上餐桌进入开发这个系统的核心我称之为需求池学习目标在本节中我的目标是带大家学会如何搭建和维护一个健康高效的需求池我将分享需求池应该包含哪些关键信息需求在池子里会经历怎样的生命周期以及我始终坚持的管理原则学完本节我希望我们都能成为一名思路清晰的需求管家需求池定义需求池顾名思义就是一个用来汇集和管理所有需求的池子我把它定义为一个用于统一记录跟踪和评估产品所有相关需求的中央数据库它是我管理产品的唯一事实来源我通常会用或一个功能强大的表格来搭建它为了让这个池子有效运转我记录的每一条需求都必须包含一些标准化的信息字段其中最重要的包括产品模块这个需求属于哪个功能板块如登录注册订单流程需求描述用清晰的语言描述用户场景痛点和期望优先级这个需求有多重要我常用来划分需求提出人这条需求来自谁如用户销售部老板需求类型这是一个新功能体验优化还是技术需求需求状态进入我需求池的每一条需求都不会石沉大海它会拥有一个清晰的生命周期我会通过状态这个字段来追踪它的进展一个标准的需求生命周期流程如下待确认这是需求的入口所有新收集到的未经我详细分析的需求都先放在这里已确认经过我的分析确认这是一个真实有价值的需求但还没想好什么时候做规划中需求已通过评审并被正式排入某个版本迭代的开发计划中已完成需求已开发测试上线这是它旅程的终点已拒绝经过分析我认为这个需求价值不大或与产品方向不符决定不做给需求一个明确的死亡结果同样非常重要需求池的作用我之所以如此看重需求池是因为它为我为整个团队都带来了巨大的价值管理需求它是所有需求的统一入口和视图避免了需求散落在邮件微信会议纪要里造成遗忘和混乱维护需求我可以随时查看任何一个需求的状态优先级和负责人对整个产品的迭代节奏了如指掌回溯需求当未来有人问我们当初为什么要做这个功能时我可以立刻从需求池里调出当时的背景分析和决策过程它是我们产品决策的历史档案需求池管理原则一个只进不出的需求池很快就会变成一个令人绝望的需求坟场为了保持它的活力和价值我始终坚守两条管理原则有进有出需求池必须是流动的我需要定期比如每周审视池中的需求推动它们的状态向前流转要么进入规划要么明确拒绝绝不能让大量需求长期停滞在待确认状态宽进严出对于需求的进入我持开放态度鼓励各方提出想法所以入口要宽但对于需求的输出即进入开发我必须严格把关基于用户价值商业目标投入产出比等因素进行严苛的筛选和排序我将需求管理的核心要点总结在下面的表格中核心概念我的实践要点需求池建立一个包含优先级状态等关键字段的中央数据库作为唯一事实来源需求状态用待确认已完成已拒绝的清晰流程追踪每条需求的生命周期管理原则宽进严出鼓励收集严格筛选有进有出保持流动拒绝僵化本章总结在这里我附上需求池模板供读者使用引用站外地址需求池模板最后我们来回顾一下整个第二章的核心脉络我认为一名合格的产品经理在处理需求时必须走完这三个密不可分的步骤认知需求首先我们要能透过现象看本质深刻理解什么是需求它不是用户说的原话而是能解决用户在特定场景下本质痛点的方案收集需求其次我们要主动出击运用竞品分析用户访谈实地调研等多种手段从内外部多个渠道系统地如何收集需求管理需求最后我们要建立并维护一个动态健康的需求池对所有需求进行科学的需求管理确保我们永远在做最有价值的事掌握从认知收集到管理的完整闭环是我们做出成功产品的基石",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-21 14:51:21",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E7%AB%A0%EF%BC%9A%E9%9C%80%E6%B1%82%E6%94%B6%E9%9B%86%E4%B8%8E%E7%AE%A1%E7%90%86"><span class="toc-text">第二章：需求收集与管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#2-1-%E4%BB%80%E4%B9%88%E6%98%AF%E9%9C%80%E6%B1%82"><span class="toc-text">2.1 什么是需求</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">2.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-2-%E5%85%AC%E5%8F%B8%E8%83%8C%E6%99%AF%E4%B8%8E%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-text">2.1.2 公司背景与项目背景</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-3-%E4%BB%80%E4%B9%88%E6%98%AF%E9%9C%80%E6%B1%82%EF%BC%88%E6%A1%88%E4%BE%8B%EF%BC%89"><span class="toc-text">2.1.3 什么是需求（案例）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-4-%E9%9C%80%E6%B1%82%E7%9A%84%E5%B8%B8%E8%A7%81%E5%BD%A2%E5%BC%8F"><span class="toc-text">2.1.4 需求的常见形式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-5-%E9%9C%80%E6%B1%82%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-text">2.1.5 需求的定义</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-2-%E9%9C%80%E6%B1%82%E5%A6%82%E4%BD%95%E6%94%B6%E9%9B%86"><span class="toc-text">2.2 需求如何收集</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">2.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-2-%E9%9C%80%E6%B1%82%E7%9A%84%E6%9D%A5%E6%BA%90"><span class="toc-text">2.2.2 需求的来源</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-3-%E9%9C%80%E6%B1%82%E6%94%B6%E9%9B%86%E6%96%B9%E5%BC%8F%E5%88%86%E7%B1%BB"><span class="toc-text">2.2.3 需求收集方式分类</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-4-%E5%B8%B8%E8%A7%81%E7%9A%84%E9%9C%80%E6%B1%82%E6%94%B6%E9%9B%86%E6%96%B9%E6%B3%95"><span class="toc-text">2.2.4 常见的需求收集方法</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-5-%E7%AB%9E%E5%93%81%E5%88%86%E6%9E%90"><span class="toc-text">2.2.5 竞品分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%AB%9E%E5%93%81%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E5%88%86%E7%B1%BB"><span class="toc-text">1. 竞品的定义与分类</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%AB%9E%E5%93%81%E5%88%86%E6%9E%90%E6%96%B9%E6%B3%95"><span class="toc-text">2. 竞品分析方法</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%AB%9E%E5%93%81%E5%88%86%E6%9E%90%E7%9A%84%E9%80%82%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">3. 竞品分析的适用场景</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-6-%E7%94%A8%E6%88%B7%E8%AE%BF%E8%B0%88"><span class="toc-text">2.2.6 用户访谈</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%94%A8%E6%88%B7%E8%AE%BF%E8%B0%88%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E6%B5%81%E7%A8%8B"><span class="toc-text">1. 用户访谈的定义与流程</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%AE%BF%E8%B0%88%E9%97%AE%E9%A2%98%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">2. 访谈问题设计要点</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%94%A8%E6%88%B7%E8%AE%BF%E8%B0%88%E7%A4%BA%E4%BE%8B"><span class="toc-text">3. 用户访谈示例</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9%E5%8F%8A%E9%80%82%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">4. 注意事项及适用场景</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-7-%E5%AE%9E%E5%9C%B0%E8%B0%83%E7%A0%94"><span class="toc-text">2.2.7 实地调研</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%AE%9E%E5%9C%B0%E8%B0%83%E7%A0%94%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-text">1. 实地调研的定义</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%A6%82%E4%BD%95%E8%BF%9B%E8%A1%8C%E5%AE%9E%E5%9C%B0%E8%B0%83%E7%A0%94"><span class="toc-text">2. 如何进行实地调研</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%AE%9E%E5%9C%B0%E8%B0%83%E7%A0%94%E9%80%82%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">3. 实地调研适用场景</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-3-%E9%9C%80%E6%B1%82%E7%AE%A1%E7%90%86"><span class="toc-text">2.3 需求管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">2.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-2-%E9%9C%80%E6%B1%82%E6%B1%A0%E5%AE%9A%E4%B9%89"><span class="toc-text">2.3.2 需求池定义</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-3-%E9%9C%80%E6%B1%82%E7%8A%B6%E6%80%81"><span class="toc-text">2.3.3 需求状态</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-4-%E9%9C%80%E6%B1%82%E6%B1%A0%E7%9A%84%E4%BD%9C%E7%94%A8"><span class="toc-text">2.3.4 需求池的作用</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-5-%E9%9C%80%E6%B1%82%E6%B1%A0%E7%AE%A1%E7%90%86%E5%8E%9F%E5%88%99"><span class="toc-text">2.3.5 需求池管理原则</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-4-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">2.4 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理入门（二）：第二章：需求收集与管理</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-20T09:13:45.000Z" title="发表于 2025-07-20 17:13:45">2025-07-20</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-21T06:51:21.650Z" title="更新于 2025-07-21 14:51:21">2025-07-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">7.9k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>22分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理入门（二）：第二章：需求收集与管理"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/56262.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/56262.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理入门（二）：第二章：需求收集与管理</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-20T09:13:45.000Z" title="发表于 2025-07-20 17:13:45">2025-07-20</time><time itemprop="dateCreated datePublished" datetime="2025-07-21T06:51:21.650Z" title="更新于 2025-07-21 14:51:21">2025-07-21</time></header><div id="postchat_postcontent"><h1 id="第二章：需求收集与管理"><a href="#第二章：需求收集与管理" class="headerlink" title="第二章：需求收集与管理"></a>第二章：需求收集与管理</h1><h2 id="2-1-什么是需求"><a href="#2-1-什么是需求" class="headerlink" title="2.1 什么是需求"></a>2.1 什么是需求</h2><p>在我的产品经理生涯中，我始终认为，一切工作的起点和终点都是“需求”。如果我们对需求的理解出现了偏差，那么后续无论多精美的设计、多优秀的技术，都只是在错误的地基上建造楼阁，最终难免会坍塌。那么，到底什么是需求？让我们一起深入地探索它的本质。</p><h3 id="2-1-1-学习目标"><a href="#2-1-1-学习目标" class="headerlink" title="2.1.1 学习目标"></a>2.1.1 学习目标</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214644630.png" alt="image-20250718214644630"></p><p>在本节中，我的核心目标是帮助我们建立起对“需求”的深度认知。我希望在本节学习结束后，我们都能具备一种“穿透”能力——能够穿透用户表面的只言片语，直达他们内心深处真正的、未被言说的渴望与痛点。</p><h3 id="2-1-2-公司背景与项目背景"><a href="#2-1-2-公司背景与项目背景" class="headerlink" title="2.1.2 公司背景与项目背景"></a>2.1.2 公司背景与项目背景</h3><p>在讨论理论之前，我习惯先设定一个场景，因为脱离了场景谈需求，就如同无源之水。让我们虚构一个案例背景，以便更好地代入思考。</p><ol><li><p><strong>公司背景</strong><br>我们是一家B2B生鲜食材供应商，通过自研的线上平台，为全国数千家餐厅提供每日的食材采购与配送服务。</p></li><li><p><strong>项目背景</strong><br>近期，平台的客服部门收到了大量餐厅采购员的抱怨，普遍反映我们的下单流程烦琐、效率低下。因此，公司决定立项，由我来负责优化平台的下单体验，提升客户满意度和下单频次。</p></li></ol><h3 id="2-1-3-什么是需求（案例）"><a href="#2-1-3-什么是需求（案例）" class="headerlink" title="2.1.3 什么是需求（案例）"></a>2.1.3 什么是需求（案例）</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214737611.png" alt="image-20250718214737611"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214833975.png" alt="image-20250718214833975"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214842638.png" alt="image-20250718214842638"></p><p>好了，背景就绪。现在，作为这个项目的PM，我开始接触来自一线用户的声音。在我的经验里，这些声音（也就是最初始的需求）通常以三种面貌出现。</p><ol><li><strong>案例1：用户以“提问题”方式给出的需求</strong><br>一位用户在App的反馈区留言：“每天都不知道我要吃啥，我怎么才能知道今天应该点什么外卖呢？”<ul><li><strong>我的解读</strong>：用户提出的“问题”，是他目前面临的问题，或许我们可以推出一个简单的小插件入口，随机抽取今天应该吃什么？。</li></ul></li><li><strong>案例2：用户以“提目的”方式给出的需求</strong><br>一位用户反馈说：“我每天吃饭的预算有限，希望平台能够让我自己快速点到20块钱以内的外卖。”<ul><li><strong>我的解读</strong>：这位用户给了我一个非常明确的“目的”——“快速找到20元以内的外卖”。这是一个清晰的、待满足的诉求。我的工作就是思考如何最好地帮他达成这个目的，是增加一个价格区间筛选？还是专门开辟一个“平价专区”？</li></ul></li><li><strong>案例3：用户以“提方案”方式给出的需求</strong><br>一位用户希望我们做一个“智能推荐”功能。他描述道：“只要一点就进入这个推荐，就提供价钱选择，还有结合自己的口味和当下季节以及哪个地方的给推荐性价比口碑最好的外卖，并且支持点击一下就自动付款下单的功能。”<ul><li><strong>我的解读</strong>：这是一位“高阶”用户，他不仅提了目的，甚至帮我把完整的产品“方案”都设计好了。这恰恰是最需要我警惕和深度分析的情况。他这个宏大的方案里，其实包含了多个本质目的的集合：“我懒得选（要智能推荐）”、“我要省钱（要性价比）”、“我要好吃（要口碑好）”、“我要方便（要一键下单）”。我的职责不是照抄这个方案，而是将这些本质目的拆解开来，评估实现难度和用户价值，设计出更合理、更可落地的产品方案。</li></ul></li></ol><hr><h3 id="2-1-4-需求的常见形式"><a href="#2-1-4-需求的常见形式" class="headerlink" title="2.1.4 需求的常见形式"></a>2.1.4 需求的常见形式</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718215247811.png" alt="image-20250718215247811"></p><p>通过上面三个小案例，我们可以总结出，用户最原始的需求通常以三种形式传递给我们。我把它们整理成了下面的表格，方便我们记忆。</p><table><thead><tr><th align="left"><strong>形式</strong></th><th align="left"><strong>用户表达方式</strong></th><th align="left"><strong>我的解读 &amp; 应对思路</strong></th></tr></thead><tbody><tr><td align="left"><strong>提问题</strong></td><td align="left">“为什么……？” “怎么……？”</td><td align="left">用户在某个操作中遇到了困难，感到困惑。我需要追问，定位他被卡住的场景和具体痛点。</td></tr><tr><td align="left"><strong>提目的</strong></td><td align="left">“我想要……” “我希望能……”</td><td align="left">用户明确表达了期望达成的效果。我需要思考，达成这个目的，有哪些可能的路径和方案？</td></tr><tr><td align="left"><strong>提方案</strong></td><td align="left">“你应该加个……” “只要做个……”</td><td align="left">用户给出了自认为的解决方案。我需要“翻译”——这个方案是为了解决什么问题？有没有更好的方案？</td></tr></tbody></table><h3 id="2-1-5-需求的定义"><a href="#2-1-5-需求的定义" class="headerlink" title="2.1.5 需求的定义"></a>2.1.5 需求的定义</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718215610820.png" alt="image-20250718215610820"></p><p>那么，综合以上所有，我来给出我对“需求”的最终定义。我认为需要区分两个概念：</p><ol><li><strong>原始需求</strong><br>即用户直接表达出来的，未经加工的“问题”、“目的”或“方案”。它是我们工作的输入和起点。</li><li><strong>产品需求</strong><br>这是我们产品经理经过分析、挖掘、转化后，真正应该去做的东西。我对它的定义是：<ul><li><strong>在特定场景下，为满足用户的本质目的，我们所设计出的一套完整的解决方案。</strong></li></ul></li></ol><p>所以，我的工作，从来不是对用户的“原始需求”照单全收，而是要经历一个“<strong>原始需求 → 本质目的 → 产品需求</strong>”的深度转化过程。这趟旅程的质量，决定了我们最终产品的成败。</p><hr><h2 id="2-2-需求如何收集"><a href="#2-2-需求如何收集" class="headerlink" title="2.2 需求如何收集"></a>2.2 需求如何收集</h2><p>我们已经深刻理解了“需求”的本质，知道它藏在用户的只言片语背后。那接下来的问题就是，我们该去哪里、以及如何才能高效地把这些“藏着”的需求挖掘出来？</p><p>这就是需求收集的工作。在我看来，这绝不是一个被动等待的过程，而是一项需要我们主动出击、运用多种侦查手段的系统工程。</p><h3 id="2-2-1-学习目标"><a href="#2-2-1-学习目标" class="headerlink" title="2.2.1 学习目标"></a>2.2.1 学习目标</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220021847.png" alt="image-20250718220021847"></p><p>在这一节，我的目标是为我们装备一套实用的“需求挖掘工具箱”。我将带大家梳理需求的来源，并详细介绍几种我最常用且行之有效的收集方法，包括但不限于竞品分析、用户访谈等。学完本节，我希望我们都能根据不同的目的和场景，自信地选择并运用最合适的工具。</p><h3 id="2-2-2-需求的来源"><a href="#2-2-2-需求的来源" class="headerlink" title="2.2.2 需求的来源"></a>2.2.2 需求的来源</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220043430.png" alt="image-20250718220043430"></p><p>在动手挖掘之前，我们先要画出“藏宝图”，明确需求可能藏在哪些地方。我习惯将所有的需求来源归为两大类：<strong>外部需求</strong>和<strong>内部需求</strong>。一名优秀的产品经理，必须同时对这两个方向保持敏锐。</p><ol><li><p><strong>外部需求</strong><br>这类需求来自于我们公司“围墙”之外，是市场和用户的直接声音。它包括：</p><ul><li><strong>用户</strong>：通过用户访谈、反馈、调研等直接获取。</li><li><strong>客户</strong>：对于B端产品，这是指付费客户提出的具体要求。</li><li><strong>竞品</strong>：通过分析竞争对手的动向和功能。</li><li><strong>市场/行业</strong>：宏观的政策变化、技术趋势、社会热点等。</li></ul></li><li><p><strong>内部需求</strong><br>这类需求源自于公司内部的各个协作方，通常服务于公司的战略和商业目标。它包括：</p><ul><li><strong>老板/管理层</strong>：基于公司战略发展提出的方向性要求。</li><li><strong>运营/市场团队</strong>：为支撑某项运营活动或营销策略而提出的产品需求。</li><li><strong>销售/客服团队</strong>：来自一线炮火声，为解决客户问题或促进销售而提出的需求。</li><li><strong>技术/设计团队</strong>：出于提升系统性能、优化架构、统一设计规范等内部优化目的提出的需求。</li></ul></li></ol><h3 id="2-2-3-需求收集方式分类"><a href="#2-2-3-需求收集方式分类" class="headerlink" title="2.2.3 需求收集方式分类"></a>2.2.3 需求收集方式分类</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220157829.png" alt="image-20250718220157829"></p><p>了解了需求的来源，我们就要选择具体的“挖掘工具”了。在此之前，我先把这些工具（也就是收集方法）从性质上分为两类：<strong>定性方式</strong>和<strong>定量方式</strong>。想清楚用哪类方式，能让我们的目标更明确。</p><ul><li><p><strong>定性方式（Qualitative）</strong><br>我用它来回答“<strong>为什么</strong>”。当我需要深入探索用户的动机、感受、行为背后的原因时，我会采用定性方式。它的特点是<strong>样本小但洞察深</strong>。比如，我可以通过访谈，真正理解一个用户<strong>为什么</strong>对我的产品感到“不爽”。</p></li><li><p><strong>定量方式（Quantitative）</strong><br>我用它来回答“<strong>是什么</strong>”和“<strong>有多少</strong>”。当我想验证一个假设、或者了解某个现象的普遍性时，我会采用定量方式。它的特点是<strong>样本大且结果可以被统计</strong>，能反映普遍规律。比如，我可以通过问卷，了解到底有百分之多少的用户认为我的产品“不好用”。</p></li></ul><p>为了方便你理解，我总结了下面的表格：</p><table><thead><tr><th align="left"><strong>方式分类</strong></th><th align="left"><strong>核心目的</strong></th><th align="left"><strong>特点</strong></th><th align="left"><strong>常用方法举例</strong></th></tr></thead><tbody><tr><td align="left"><strong>定性方式</strong></td><td align="left">探究“为什么？”</td><td align="left">深入、有背景、样本小、无法统计</td><td align="left">用户访谈、实地调研、可用性测试</td></tr><tr><td align="left"><strong>定量方式</strong></td><td align="left">度量“是什么/有多少？”</td><td align="left">广泛、可统计、样本大、结论客观</td><td align="left">问卷调查、数据分析、A/B测试</td></tr></tbody></table><h3 id="2-2-4-常见的需求收集方法"><a href="#2-2-4-常见的需求收集方法" class="headerlink" title="2.2.4 常见的需求收集方法"></a>2.2.4 常见的需求收集方法</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220406802.png" alt="image-20250718220406802"></p><p>在我的工具箱里，有许多种具体的需求收集方法。经过多年的实践，我筛选出了最高效、最常用的一批，它们几乎能覆盖我工作中90%的场景。这些方法包括：</p><ul><li>用户访谈：通过对于用户的访谈掌握需求</li><li>问卷调查：通过发放调查问卷来调查</li><li>竞品分析：通过竞争对手身上所知</li><li>头脑风暴：与团队进行奇思妙想</li><li>观察法：观察身边的情况</li><li>实地体验（观察法）：对于实际地点去实地调研</li><li>数据分析：通过已有或网络的现成数据进行分析</li></ul><p>在接下来的小节中，我将重点挑选其中几个最为核心的方法，为大家进行详细的拆解和说明。</p><hr><h3 id="2-2-5-竞品分析"><a href="#2-2-5-竞品分析" class="headerlink" title="2.2.5 竞品分析"></a>2.2.5 竞品分析</h3><p>我有一个观点：<strong>我们永远不应该闭门造车</strong>。竞品分析，对我来说，不是为了抄袭，而是为了站在巨人的肩膀上，洞察我们所处的“战场”格局，从他人的成败中学习，最终找到我们自己独特的取胜之道。</p><h4 id="1-竞品的定义与分类"><a href="#1-竞品的定义与分类" class="headerlink" title="1. 竞品的定义与分类"></a>1. 竞品的定义与分类</h4><p>首先，我们要明确谁是我们的“竞品”。我的定义很简单：<strong>任何正在与我们争夺同一批目标用户的时间或金钱的产品，都是我们的竞品</strong>。</p><p>在分析时，我不会把所有竞品混为一谈，而是习惯将他们分为三类，采取不同的应对策略。</p><ul><li><strong>直接竞品</strong>：这是最显而易见的对手。我们的目标用户、产品形态和核心功能都高度重叠。比如，如果我是做“美团外卖”的PM，那“饿了么”就是我的直接竞品。我们是在同一个赛道里进行着刺刀见红的肉搏。</li><li><strong>间接竞品</strong>：他们的目标用户和我们有重叠，但是满足用户需求的产品形态或解决方案不同。比如，对于外卖平台，“方便蜂”“7-11”等便利店，甚至“叮咚买菜”这样的生鲜电商，都是我的间接竞品。他们都在解决用户“足不出户解决吃饭问题”这个需求。</li><li><strong>潜在竞品</strong>：这类产品目前和我们没有直接竞争，但未来有可能凭借其资源、技术或用户规模，跨界进入我们的领域。比如，一个拥有海量流量的社交巨头，如果某天宣布要大力发展本地生活服务，那它就会立刻成为我最警惕的潜在竞品。</li></ul><p>为了方便我们快速识别，我总结了下面的表格：</p><table><thead><tr><th align="left"><strong>竞品分类</strong></th><th align="left"><strong>核心特征</strong></th><th align="left"><strong>举例（假设我们是“微信”）</strong></th></tr></thead><tbody><tr><td align="left"><strong>直接竞品</strong></td><td align="left">目标用户、产品形态、核心功能都高度相似。</td><td align="left">QQ、钉钉（在办公场景下）</td></tr><tr><td align="left"><strong>间接竞品</strong></td><td align="left">满足用户的同一类核心需求，但方案不同。</td><td align="left">抖音（争夺用户时长）、电话/短信（解决通信需求）</td></tr><tr><td align="left"><strong>潜在竞品</strong></td><td align="left">目前无竞争，但未来可能进入市场的重量级玩家。</td><td align="left">一个新兴的、技术驱动的社交创业公司</td></tr></tbody></table><hr><h4 id="2-竞品分析方法"><a href="#2-竞品分析方法" class="headerlink" title="2. 竞品分析方法"></a>2. 竞品分析方法</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718221149966.png" alt="image-20250718221149966"></p><p>明确了要分析谁，下一步就是“如何分析”。仅仅是截几张图、看几个功能是远远不够的。我需要一个框架来保证分析的系统性和深度。我个人最推崇的，是<strong>用户体验五要素模型</strong>。它能帮我像剥洋葱一样，从表到里地把一个产品彻底解构。</p><ol><li><strong>表现层 (Surface)</strong>：这是最表层的，用户能直接感知的视觉设计。包括配色、字体、图标、布局的美感等。</li><li><strong>框架层 (Skeleton)</strong>：这是界面的骨架，决定了信息在页面上的排布。比如按钮放哪里，搜索框放哪里，导航怎么设计。</li><li><strong>结构层 (Structure)</strong>：这是产品的流程和信息架构。用户从一个页面如何跳转到另一个页面？产品的功能模块是如何组织的？</li><li><strong>范围层 (Scope)</strong>：这是产品具体包含了哪些功能和内容。比如，一个电商App，它的范围层就包括了商品展示、购物车、订单、支付等一系列功能。</li><li><strong>战略层 (Strategy)</strong>：这是最核心的，产品的商业目标和用户需求是什么？它为什么要做这个产品？</li></ol><p>当我用这五个层次去分析一个竞品时，我看到的就不再是一个个孤立的界面，而是其背后完整的产品思考和商业逻辑。</p><hr><h4 id="3-竞品分析的适用场景"><a href="#3-竞品分析的适用场景" class="headerlink" title="3. 竞品分析的适用场景"></a>3. 竞品分析的适用场景</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718221340808.png" alt="image-20250718221340808"></p><p>我做竞品分析，从不是为了分析而分析，一定是带有明确目的的。以下是我认为最有价值的几个应用场景：</p><ul><li><strong>了解行业</strong>：当我刚进入一个新领域时，我会把市面上Top3的竞品，用五要素模型完整地分析一遍。这是我快速了解行业格局、用户现状和主流玩法的最佳途径。</li><li><strong>产品设计</strong>：在设计某个具体功能时，比如“购物车”，我一定会去体验至少5个主流App的购物车是怎么设计的。我的目的不是抄，而是去归纳总结，了解业界成熟的设计模式，避免重复造轮子和踩坑。</li><li><strong>寻找差异化</strong>：通过对主要竞品的优劣势分析（比如使用SWOT模型），我可以清晰地看到市场上的空白地带和未被满足的需求。这对于我们制定差异化竞争策略、找到自己的生态位至关重要。</li><li><strong>方案验证</strong>：如果我的某个直接竞品上线了一个新功能，并且获得了很好的市场反馈，那它在某种程度上帮我验证了这个功能背后的用户需求是真实存在的。反之，如果竞品的功能失败了，那它也等于免费给我上了一课。</li></ul><hr><h3 id="2-2-6-用户访谈"><a href="#2-2-6-用户访谈" class="headerlink" title="2.2.6 用户访谈"></a>2.2.6 用户访谈</h3><p>在我看来，数据能告诉我用户“做了什么”，但只有用户访谈能告诉我，他们“为什么这么做”。它是产品经理建立用户同理心、挖掘深层次需求的终极武器，没有任何工具可以替代。</p><h4 id="1-用户访谈的定义与流程"><a href="#1-用户访谈的定义与流程" class="headerlink" title="1. 用户访谈的定义与流程"></a>1. 用户访谈的定义与流程</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719083410461.png" alt="image-20250719083410461"></p><p><strong>用户访谈的定义</strong>：对我而言，它是一场有目的、有结构的、一对一的深度对话。我通过这场对话，来探寻用户在特定场景下的行为、动机、态度和痛点。它是一种定性研究方法，我追求的是洞察的深度，而非样本的数量。</p><p>一场专业的访谈绝不是一次随意的聊天，它需要我进行精心的策划和准备。我通常会遵循一个六步走的流程：</p><ol><li><strong>确定访谈形式</strong>：首先，我要决定访谈的方式。是成本较高但信息丰富的<strong>线下面对面</strong>？还是高效便捷的<strong>电话/线上视频</strong>？</li><li><strong>明确访谈目的</strong>：在开始前，我必须能用一句话说清楚“我这次访谈想解决的核心问题是什么？”例如：“探究用户在深夜场景下点外卖的核心决策因素。”</li><li><strong>用户筛选</strong>：我需要找到“对”的人。根据我的访谈目的，我会设定清晰的用户标准（比如年龄、使用频率、所在城市等），然后通过问卷或后台数据进行筛选。</li><li><strong>设计访谈问题</strong>：这是访谈的灵魂。我会提前准备一份访谈提纲，里面包含了一系列精心设计的开放式问题。</li><li><strong>邀请用户访谈</strong>：我会正式地联系并邀请筛选出来的用户，说明我们的目的、时长，并通常会提供一些小礼品（如礼品卡、代金券）作为答谢。</li><li><strong>结果汇总与分析</strong>：访谈结束后，我会立刻整理访谈记录，然后将多次访谈的结果放在一起，寻找其中反复出现的模式、观点和痛点，最终提炼出有价值的洞察。</li></ol><hr><h4 id="2-访谈问题设计要点"><a href="#2-访谈问题设计要点" class="headerlink" title="2. 访谈问题设计要点"></a>2. 访谈问题设计要点</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719083541359.png" alt="image-20250719083541359"></p><p>访谈的成败，很大程度上取决于我问题的质量。我设计问题时，会重点把握以下几点：</p><p><strong>问题设置的方向：</strong><br>我的访谈问题通常会遵循“现状 → 痛点 → 方案”的逻辑顺序展开，层层递进。</p><ul><li><strong>现状类问题</strong>：用于“破冰”，了解用户当下的行为和场景。如：“能带我回忆一下，您上一次点外卖的全过程吗？”</li><li><strong>痛点类问题</strong>：用于挖掘用户的挫折和不满。如：“在刚才您描述的整个过程中，有没有哪个环节让您觉得特别麻烦或者不爽？”</li><li><strong>方案/期望类问题</strong>：用于探寻用户的期望和潜在需求。如：“如果抛开所有限制，您心目中最理想的外卖App应该是什么样的？”</li></ul><p><strong>问题设计的方式：</strong></p><ul><li><strong>多问开放式问题</strong>：我从不问“你喜欢我们的App吗？”这类可以用“是/否”回答的问题。我会问：“关于我们的App，你有什么样的使用体验和感受？”</li><li><strong>不断追问</strong>：当用户提到一个关键点时，我最常使用的工具就是追问“为什么？”“可以再多讲讲吗？”“后来呢？”。这能帮我挖得更深。</li><li><strong>避免引导性提问</strong>：我绝不会问“你是不是觉得我们的红包功能很难找？”。这会把我的观点强加给用户。我会问：“您平时会使用我们的红包功能吗？可以聊聊您使用它的过程吗？”</li></ul><hr><h4 id="3-用户访谈示例"><a href="#3-用户访谈示例" class="headerlink" title="3. 用户访谈示例"></a>3. 用户访谈示例</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084031980.png" alt="image-20250719084031980"></p><p>我们还用外卖平台的案例。假设我的访谈目的是“了解白领用户在办公室选择午餐外卖的决策过程”。我的问题可能会这样设计：</p><ul><li><strong>（现状）</strong> “您可以回忆一下昨天中午点外卖的经历吗？从你想到要点外卖，到最后拿到外卖，都发生了什么？”</li><li><strong>（痛点）</strong> “在挑选餐厅和菜品的过程中，有没有哪个环节让您觉得很纠结或者浪费时间？”</li><li><strong>（追问）</strong> “您刚才提到‘选来选去最后还是点了常吃的那家’，为什么会这样呢？”</li><li><strong>（期望）</strong> “如果我们可以帮您解决‘选择困难’这个问题，您希望我们怎么做？”</li></ul><hr><h4 id="4-注意事项及适用场景"><a href="#4-注意事项及适用场景" class="headerlink" title="4. 注意事项及适用场景"></a>4. 注意事项及适用场景</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719083846814.png" alt="image-20250719083846814"></p><ul><li><p><strong>我的注意事项</strong>：</p><ol><li><strong>多听少说</strong>：访谈的主角是用户，我的任务是引导和倾听。</li><li><strong>保持中立</strong>：无论用户怎么夸奖或吐槽我的产品，我都要保持客观，不争辩，不解释。</li><li><strong>事实与观点分离</strong>：记录时，要严格区分哪些是用户说的“事实”，哪些是我自己的“分析和判断”。</li></ol></li><li><p><strong>适用场景</strong>：<br>我通常会在项目的<strong>早期探索阶段</strong>大量使用用户访谈，因为这时我对用户和问题还很模糊，需要建立认知。此外，在<strong>新功能的构思和验证阶段</strong>，我也会通过访谈，向用户展示原型或概念，来快速获取反馈。</p></li></ul><hr><p>为了方便我们回顾，我将用户访谈的核心要点总结在下面的表格里：</p><table><thead><tr><th align="left"><strong>核心环节</strong></th><th align="left"><strong>我的关键动作</strong></th></tr></thead><tbody><tr><td align="left"><strong>访谈前</strong></td><td align="left"><strong>明确目的</strong>、<strong>筛选用户</strong>、<strong>设计开放式问题提纲</strong></td></tr><tr><td align="left"><strong>访谈中</strong></td><td align="left"><strong>多听少说</strong>，像海绵一样吸收信息；通过<strong>不断追问</strong>来深挖；<strong>保持中立</strong>，不评判。</td></tr><tr><td align="left"><strong>访谈后</strong></td><td align="left"><strong>及时整理</strong>笔记，<strong>寻找共性</strong>，将零散的观点<strong>提炼为洞察</strong>。</td></tr></tbody></table><hr><h3 id="2-2-7-实地调研"><a href="#2-2-7-实地调研" class="headerlink" title="2.2.7 实地调研"></a>2.2.7 实地调研</h3><p>如果说用户访谈是“听其言”，那么实地调研就是“观其行”。在我看来，这是两种方法最大的区别。很多时候，用户说的和他实际做的并不完全一致，而实地调研，就是让我有机会亲眼去见证这种差异，发现那些连用户自己都未曾察觉的隐性需求。</p><h4 id="1-实地调研的定义"><a href="#1-实地调研的定义" class="headerlink" title="1. 实地调研的定义"></a>1. 实地调研的定义</h4><p>对我而言，<strong>实地调研就是产品经理亲自进入用户的真实物理场景中，通过近距离观察和亲身体验，来理解用户行为和背后动机的一种研究方法。</strong></p><p>它包含两种核心形式：</p><ul><li><strong>观察法</strong>：我像一个“隐形人”，在不打扰用户的前提下，静静地观察他在特定场景下是如何与环境、工具（包括我们的产品）进行互动的。</li><li><strong>实地体验</strong>：我亲自扮演用户的角色，走一遍他完整的任务流程，切身感受他在每个环节的顺畅与阻碍。</li></ul><hr><h4 id="2-如何进行实地调研"><a href="#2-如何进行实地调研" class="headerlink" title="2. 如何进行实地调研"></a>2. 如何进行实地调研</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084246476.png" alt="image-20250719084246476"></p><p>一次有效的实地调研，需要我像导演一样，精心设计整个过程。我通常会遵循以下四个步骤：</p><ol><li><strong>进入场景</strong>：这是第一步，也是最关键的一步。比如，我要研究餐厅后厨的采购流程，那我就必须真的穿上工作服，走进那个潮湿、繁忙的后厨，而不是坐在办公室里想象。</li><li><strong>用户角色</strong>：进入场景后，我要明确我的角色。我是作为一名旁观者去“观察”？还是亲自上手，作为一名“学徒”去“体验”整个下单、验货、入库的流程？</li><li><strong>观察体会</strong>：在场景中，我的所有感官都要打开。我会重点观察：用户在做什么？他使用了什么工具？他与其他人是如何协作的？他在哪个环节面露难色？哪个环节的效率特别低？如果是我自己体验，我会记录下每一步的感受。</li><li><strong>持续进行</strong>：一次调研是远远不够的。我会选择不同时间、不同类型的场景（比如高峰期与平峰期的餐厅后厨）进行多次调研，以确保我看到的不是偶然现象，而是普遍存在的问题。</li></ol><hr><h4 id="3-实地调研适用场景"><a href="#3-实地调研适用场景" class="headerlink" title="3. 实地调研适用场景"></a>3. 实地调研适用场景</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084347265.png" alt="image-20250719084347265"></p><p>实地调研虽然强大，但成本也很高，所以我必须在最需要它的地方“对症下药”。以下是我最常使用它的几个场景：</p><ul><li><strong>挖掘需求</strong>：当我要为一个全新的领域（比如智慧农业）设计产品时，我对用户的真实作业环境一无所知，这时实地调研是建立基础认知的唯一途径。</li><li><strong>理解需求</strong>：当用户向我提了一个我无法理解的需求时，比如“你们的扫码枪不好用”，我会直接去他的仓库，看他到底是怎么用的，问题出在哪里。</li><li><strong>效果验证</strong>：我的新功能上线后，我会去现场观察用户是如何使用它的，是否符合我的设计预期，有没有出现我没想到的问题。</li><li><strong>寻找问题</strong>：当我的产品数据出现异常，比如某个环节转化率突然下降，我会去实地观察，看看是不是用户的线下操作流程发生了变化，从而导致了线上的问题。</li></ul><p>实地调研是我们产品经理走出办公室，拥抱真实世界的最佳方式。我把它总结为以下要点：</p><table><thead><tr><th align="left"><strong>核心问题</strong></th><th align="left"><strong>我的关键动作</strong></th></tr></thead><tbody><tr><td align="left"><strong>1. 什么是实地调研？</strong></td><td align="left">亲自<strong>进入用户的真实场景</strong>，通过<strong>观察</strong>和<strong>体验</strong>来理解用户的真实行为。</td></tr><tr><td align="left"><strong>2. 如何进行实地调研？</strong></td><td align="left"><strong>进入场景 → 代入角色 → 细心体会 → 持续进行</strong>，这是一个完整的闭环。</td></tr></tbody></table><hr><h2 id="2-3-需求管理"><a href="#2-3-需求管理" class="headerlink" title="2.3 需求管理"></a>2.3 需求管理</h2><p>对我来说，如果说需求收集是“狩猎”，那么需求管理就是“庖丁解牛”和“精细烹饪”。我需要一个系统化的流程和工具，来处理我收集到的所有“食材”（需求），确保最有价值的部分能被优先端上“餐桌”（进入开发）。这个系统的核心，我称之为**“需求池”**。</p><h3 id="2-3-1-学习目标"><a href="#2-3-1-学习目标" class="headerlink" title="2.3.1 学习目标"></a>2.3.1 学习目标</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084741571.png" alt="image-20250719084741571"></p><p>在本节中，我的目标是带大家学会如何搭建和维护一个健康、高效的需求池。我将分享需求池应该包含哪些关键信息，需求在池子里会经历怎样的生命周期，以及我始终坚持的管理原则。学完本节，我希望我们都能成为一名思路清晰的“需求管家”。</p><h3 id="2-3-2-需求池定义"><a href="#2-3-2-需求池定义" class="headerlink" title="2.3.2 需求池定义"></a>2.3.2 需求池定义</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084852379.png" alt="image-20250719084852379"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084802024.png" alt="image-20250719084802024"></p><p><strong>需求池</strong>，顾名思义，就是一个用来汇集和管理所有需求的“池子”。我把它定义为：<strong>一个用于统一记录、跟踪和评估产品所有相关需求的中央数据库</strong>。它是我管理产品的“唯一事实来源”，我通常会用Jira、Trello或一个功能强大的Excel表格来搭建它。</p><p>为了让这个池子有效运转，我记录的每一条需求，都必须包含一些标准化的信息字段，其中最重要的包括：</p><ul><li><strong>产品模块</strong>：这个需求属于哪个功能板块？（如：登录注册、订单流程）</li><li><strong>需求描述</strong>：用清晰的语言描述用户场景、痛点和期望。（What &amp; Why）</li><li><strong>优先级</strong>：这个需求有多重要？（我常用P0/P1/P2/P3来划分）</li><li><strong>需求提出人</strong>：这条需求来自谁？（如：用户A、销售部、老板）</li><li><strong>需求类型</strong>：这是一个新功能、体验优化、Bug还是技术需求？</li></ul><h3 id="2-3-3-需求状态"><a href="#2-3-3-需求状态" class="headerlink" title="2.3.3 需求状态"></a>2.3.3 需求状态</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719085530024.png" alt="image-20250719085530024"></p><p>进入我需求池的每一条需求，都不会石沉大海，它会拥有一个清晰的生命周期。我会通过“状态”这个字段来追踪它的进展。一个标准的需求生命周期流程如下：</p><ol><li><strong>待确认</strong>：这是需求的入口。所有新收集到的、未经我详细分析的需求，都先放在这里。</li><li><strong>已确认</strong>：经过我的分析，确认这是一个真实、有价值的需求，但还没想好什么时候做。</li><li><strong>规划中</strong>：需求已通过评审，并被正式排入某个版本迭代的开发计划中。</li><li><strong>已完成</strong>：需求已开发、测试、上线。这是它旅程的终点。</li><li><strong>已拒绝</strong>：经过分析，我认为这个需求价值不大、或与产品方向不符，决定不做。给需求一个明确的“死亡”结果，同样非常重要。</li></ol><h3 id="2-3-4-需求池的作用"><a href="#2-3-4-需求池的作用" class="headerlink" title="2.3.4 需求池的作用"></a>2.3.4 需求池的作用</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719085617473.png" alt="image-20250719085617473"></p><p>我之所以如此看重需求池，是因为它为我、为整个团队都带来了巨大的价值。</p><ul><li><strong>管理需求</strong>：它是所有需求的统一入口和视图，避免了需求散落在邮件、微信、会议纪要里，造成遗忘和混乱。</li><li><strong>维护需求</strong>：我可以随时查看任何一个需求的状态、优先级和负责人，对整个产品的迭代节奏了如指掌。</li><li><strong>回溯需求</strong>：当未来有人问“我们当初为什么要做这个功能？”时，我可以立刻从需求池里调出当时的背景、分析和决策过程。它是我们产品决策的“历史档案”。</li></ul><h3 id="2-3-5-需求池管理原则"><a href="#2-3-5-需求池管理原则" class="headerlink" title="2.3.5 需求池管理原则"></a>2.3.5 需求池管理原则</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719085705428.png" alt="image-20250719085705428"></p><p>一个只进不出的需求池，很快就会变成一个令人绝望的“需求坟场”。为了保持它的活力和价值，我始终坚守两条管理原则：</p><ol><li><strong>有进有出</strong>：需求池必须是流动的。我需要定期（比如每周）审视池中的需求，推动它们的状态向前流转。要么进入规划，要么明确拒绝，绝不能让大量需求长期停滞在“待确认”状态。</li><li><strong>宽进严出</strong>：对于需求的“进入”，我持开放态度，鼓励各方提出想法，所以入口要“宽”。但对于需求的“输出”（即进入开发），我必须严格把关，基于用户价值、商业目标、投入产出比等因素进行严苛的筛选和排序。</li></ol><p>我将需求管理的核心要点，总结在下面的表格中：</p><table><thead><tr><th align="left"><strong>核心概念</strong></th><th align="left"><strong>我的实践要点</strong></th></tr></thead><tbody><tr><td align="left"><strong>需求池</strong></td><td align="left">建立一个包含“优先级、状态”等关键字段的<strong>中央数据库</strong>，作为唯一事实来源。</td></tr><tr><td align="left"><strong>需求状态</strong></td><td align="left">用“待确认 → 已完成/已拒绝”的<strong>清晰流程</strong>，追踪每条需求的生命周期。</td></tr><tr><td align="left"><strong>管理原则</strong></td><td align="left"><strong>宽进严出</strong>：鼓励收集，严格筛选。<br><strong>有进有出</strong>：保持流动，拒绝僵化。</td></tr></tbody></table><hr><h2 id="2-4-本章总结"><a href="#2-4-本章总结" class="headerlink" title="2.4 本章总结"></a>2.4 本章总结</h2><p>在这里我附上需求池模板供读者使用</p><div calss="anzhiyu-tag-link"><a class="tag-Link" target="_blank" href="/go.html?u=aHR0cHM6Ly9wcm9yaXNlLWJsb2cub3NzLWNuLWd1YW5nemhvdS5hbGl5dW5jcy5jb20vQUklRTQlQkElQTclRTUlOTMlODElRTclQkIlOEYlRTclOTAlODYvMDElRTklOUMlODAlRTYlQjElODIlRTYlQjElQTAueGxzeA" rel="external nofollow noopener noreferrer"><div class="tag-link-tips">引用站外地址</div><div class="tag-link-bottom"><div class="tag-link-left" style="background-image:url(https://bu.dusays.com/2025/07/19/687b0b6b83e43.png)"><i class="anzhiyufont anzhiyu-icon-link" style="display:none"></i></div><div class="tag-link-right"><div class="tag-link-title">需求池模板.xlsx</div><div class="tag-link-sitename">Prorise</div></div><i class="anzhiyufont anzhiyu-icon-angle-right"></i></div></a></div><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719105224592.png" alt="image-20250719105224592"></p><p>最后，我们来回顾一下整个第二章的核心脉络。我认为，一名合格的产品经理在处理需求时，必须走完这三个密不可分的步骤：</p><ol><li><strong>认知需求</strong>：首先，我们要能透过现象看本质，深刻理解<code>“什么是需求”</code>——它不是用户说的原话，而是能解决用户在特定场景下本质痛点的方案。</li><li><strong>收集需求</strong>：其次，我们要主动出击，运用竞品分析、用户访谈、实地调研等多种手段，从内外部多个渠道，系统地<code>“如何收集需求”</code>。</li><li><strong>管理需求</strong>：最后，我们要建立并维护一个动态、健康的需求池，对所有需求进行科学的<code>“需求管理”</code>，确保我们永远在做最有价值的事。</li></ol><p>掌握从认知、收集到管理的完整闭环，是我们做出成功产品的基石。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/56262.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/56262.html&quot;)">产品经理入门（二）：第二章：需求收集与管理</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/56262.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理入门（二）：第二章：需求收集与管理&amp;url=https://prorise666.site/posts/56262.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/10477.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div class="next-post pull-right"><a href="/posts/59297.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理入门（三）：第三章：需求分析</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理入门（二）：第二章：需求收集与管理",date:"2025-07-20 17:13:45",updated:"2025-07-21 14:51:21",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第二章：需求收集与管理\n\n## 2.1 什么是需求\n\n在我的产品经理生涯中，我始终认为，一切工作的起点和终点都是“需求”。如果我们对需求的理解出现了偏差，那么后续无论多精美的设计、多优秀的技术，都只是在错误的地基上建造楼阁，最终难免会坍塌。那么，到底什么是需求？让我们一起深入地探索它的本质。\n\n### 2.1.1 学习目标\n\n![image-20250718214644630](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214644630.png)\n\n在本节中，我的核心目标是帮助我们建立起对“需求”的深度认知。我希望在本节学习结束后，我们都能具备一种“穿透”能力——能够穿透用户表面的只言片语，直达他们内心深处真正的、未被言说的渴望与痛点。\n\n### 2.1.2 公司背景与项目背景\n\n在讨论理论之前，我习惯先设定一个场景，因为脱离了场景谈需求，就如同无源之水。让我们虚构一个案例背景，以便更好地代入思考。\n\n1.  **公司背景**\n    我们是一家B2B生鲜食材供应商，通过自研的线上平台，为全国数千家餐厅提供每日的食材采购与配送服务。\n\n2.  **项目背景**\n    近期，平台的客服部门收到了大量餐厅采购员的抱怨，普遍反映我们的下单流程烦琐、效率低下。因此，公司决定立项，由我来负责优化平台的下单体验，提升客户满意度和下单频次。\n\n### 2.1.3 什么是需求（案例）\n\n![image-20250718214737611](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214737611.png)\n\n\n\n\n\n![image-20250718214833975](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214833975.png)\n\n![image-20250718214842638](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214842638.png)\n\n好了，背景就绪。现在，作为这个项目的PM，我开始接触来自一线用户的声音。在我的经验里，这些声音（也就是最初始的需求）通常以三种面貌出现。\n\n1.  **案例1：用户以“提问题”方式给出的需求**\n    一位用户在App的反馈区留言：“每天都不知道我要吃啥，我怎么才能知道今天应该点什么外卖呢？”\n    * **我的解读**：用户提出的“问题”，是他目前面临的问题，或许我们可以推出一个简单的小插件入口，随机抽取今天应该吃什么？。\n2.  **案例2：用户以“提目的”方式给出的需求**\n    一位用户反馈说：“我每天吃饭的预算有限，希望平台能够让我自己快速点到20块钱以内的外卖。”\n    * **我的解读**：这位用户给了我一个非常明确的“目的”——“快速找到20元以内的外卖”。这是一个清晰的、待满足的诉求。我的工作就是思考如何最好地帮他达成这个目的，是增加一个价格区间筛选？还是专门开辟一个“平价专区”？\n3.  **案例3：用户以“提方案”方式给出的需求**\n    一位用户希望我们做一个“智能推荐”功能。他描述道：“只要一点就进入这个推荐，就提供价钱选择，还有结合自己的口味和当下季节以及哪个地方的给推荐性价比口碑最好的外卖，并且支持点击一下就自动付款下单的功能。”\n    * **我的解读**：这是一位“高阶”用户，他不仅提了目的，甚至帮我把完整的产品“方案”都设计好了。这恰恰是最需要我警惕和深度分析的情况。他这个宏大的方案里，其实包含了多个本质目的的集合：“我懒得选（要智能推荐）”、“我要省钱（要性价比）”、“我要好吃（要口碑好）”、“我要方便（要一键下单）”。我的职责不是照抄这个方案，而是将这些本质目的拆解开来，评估实现难度和用户价值，设计出更合理、更可落地的产品方案。\n\n---\n\n### 2.1.4 需求的常见形式\n\n![image-20250718215247811](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718215247811.png)\n\n通过上面三个小案例，我们可以总结出，用户最原始的需求通常以三种形式传递给我们。我把它们整理成了下面的表格，方便我们记忆。\n\n| **形式** | **用户表达方式** | **我的解读 & 应对思路** |\n| :--- | :--- | :--- |\n| **提问题** | “为什么……？” “怎么……？” | 用户在某个操作中遇到了困难，感到困惑。我需要追问，定位他被卡住的场景和具体痛点。 |\n| **提目的** | “我想要……” “我希望能……” | 用户明确表达了期望达成的效果。我需要思考，达成这个目的，有哪些可能的路径和方案？ |\n| **提方案** | “你应该加个……” “只要做个……” | 用户给出了自认为的解决方案。我需要“翻译”——这个方案是为了解决什么问题？有没有更好的方案？ |\n\n### 2.1.5 需求的定义\n\n![image-20250718215610820](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718215610820.png)\n\n那么，综合以上所有，我来给出我对“需求”的最终定义。我认为需要区分两个概念：\n\n1.  **原始需求**\n    即用户直接表达出来的，未经加工的“问题”、“目的”或“方案”。它是我们工作的输入和起点。\n2.  **产品需求**\n    这是我们产品经理经过分析、挖掘、转化后，真正应该去做的东西。我对它的定义是：\n    - **在特定场景下，为满足用户的本质目的，我们所设计出的一套完整的解决方案。**\n\n所以，我的工作，从来不是对用户的“原始需求”照单全收，而是要经历一个“**原始需求 → 本质目的 → 产品需求**”的深度转化过程。这趟旅程的质量，决定了我们最终产品的成败。\n\n\n---\n\n## 2.2 需求如何收集\n\n我们已经深刻理解了“需求”的本质，知道它藏在用户的只言片语背后。那接下来的问题就是，我们该去哪里、以及如何才能高效地把这些“藏着”的需求挖掘出来？\n\n这就是需求收集的工作。在我看来，这绝不是一个被动等待的过程，而是一项需要我们主动出击、运用多种侦查手段的系统工程。\n\n### 2.2.1 学习目标\n\n![image-20250718220021847](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220021847.png)\n\n在这一节，我的目标是为我们装备一套实用的“需求挖掘工具箱”。我将带大家梳理需求的来源，并详细介绍几种我最常用且行之有效的收集方法，包括但不限于竞品分析、用户访谈等。学完本节，我希望我们都能根据不同的目的和场景，自信地选择并运用最合适的工具。\n\n### 2.2.2 需求的来源\n\n![image-20250718220043430](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220043430.png)\n\n在动手挖掘之前，我们先要画出“藏宝图”，明确需求可能藏在哪些地方。我习惯将所有的需求来源归为两大类：**外部需求**和**内部需求**。一名优秀的产品经理，必须同时对这两个方向保持敏锐。\n\n1.  **外部需求**\n    这类需求来自于我们公司“围墙”之外，是市场和用户的直接声音。它包括：\n    * **用户**：通过用户访谈、反馈、调研等直接获取。\n    * **客户**：对于B端产品，这是指付费客户提出的具体要求。\n    * **竞品**：通过分析竞争对手的动向和功能。\n    * **市场/行业**：宏观的政策变化、技术趋势、社会热点等。\n\n2.  **内部需求**\n    这类需求源自于公司内部的各个协作方，通常服务于公司的战略和商业目标。它包括：\n    * **老板/管理层**：基于公司战略发展提出的方向性要求。\n    * **运营/市场团队**：为支撑某项运营活动或营销策略而提出的产品需求。\n    * **销售/客服团队**：来自一线炮火声，为解决客户问题或促进销售而提出的需求。\n    * **技术/设计团队**：出于提升系统性能、优化架构、统一设计规范等内部优化目的提出的需求。\n\n### 2.2.3 需求收集方式分类\n\n![image-20250718220157829](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220157829.png)\n\n了解了需求的来源，我们就要选择具体的“挖掘工具”了。在此之前，我先把这些工具（也就是收集方法）从性质上分为两类：**定性方式**和**定量方式**。想清楚用哪类方式，能让我们的目标更明确。\n\n* **定性方式（Qualitative）**\n    我用它来回答“**为什么**”。当我需要深入探索用户的动机、感受、行为背后的原因时，我会采用定性方式。它的特点是**样本小但洞察深**。比如，我可以通过访谈，真正理解一个用户**为什么**对我的产品感到“不爽”。\n\n* **定量方式（Quantitative）**\n    我用它来回答“**是什么**”和“**有多少**”。当我想验证一个假设、或者了解某个现象的普遍性时，我会采用定量方式。它的特点是**样本大且结果可以被统计**，能反映普遍规律。比如，我可以通过问卷，了解到底有百分之多少的用户认为我的产品“不好用”。\n\n为了方便你理解，我总结了下面的表格：\n\n| **方式分类** | **核心目的** | **特点** | **常用方法举例** |\n| :--- | :--- | :--- | :--- |\n| **定性方式** | 探究“为什么？” | 深入、有背景、样本小、无法统计 | 用户访谈、实地调研、可用性测试 |\n| **定量方式** | 度量“是什么/有多少？” | 广泛、可统计、样本大、结论客观 | 问卷调查、数据分析、A/B测试 |\n\n### 2.2.4 常见的需求收集方法\n\n![image-20250718220406802](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220406802.png)\n\n在我的工具箱里，有许多种具体的需求收集方法。经过多年的实践，我筛选出了最高效、最常用的一批，它们几乎能覆盖我工作中90%的场景。这些方法包括：\n\n* 用户访谈：通过对于用户的访谈掌握需求\n* 问卷调查：通过发放调查问卷来调查\n* 竞品分析：通过竞争对手身上所知\n* 头脑风暴：与团队进行奇思妙想\n* 观察法：观察身边的情况\n* 实地体验（观察法）：对于实际地点去实地调研\n* 数据分析：通过已有或网络的现成数据进行分析\n\n在接下来的小节中，我将重点挑选其中几个最为核心的方法，为大家进行详细的拆解和说明。\n\n\n\n\n\n---\n\n### 2.2.5 竞品分析\n\n我有一个观点：**我们永远不应该闭门造车**。竞品分析，对我来说，不是为了抄袭，而是为了站在巨人的肩膀上，洞察我们所处的“战场”格局，从他人的成败中学习，最终找到我们自己独特的取胜之道。\n\n#### 1. 竞品的定义与分类\n\n首先，我们要明确谁是我们的“竞品”。我的定义很简单：**任何正在与我们争夺同一批目标用户的时间或金钱的产品，都是我们的竞品**。\n\n在分析时，我不会把所有竞品混为一谈，而是习惯将他们分为三类，采取不同的应对策略。\n\n* **直接竞品**：这是最显而易见的对手。我们的目标用户、产品形态和核心功能都高度重叠。比如，如果我是做“美团外卖”的PM，那“饿了么”就是我的直接竞品。我们是在同一个赛道里进行着刺刀见红的肉搏。\n* **间接竞品**：他们的目标用户和我们有重叠，但是满足用户需求的产品形态或解决方案不同。比如，对于外卖平台，“方便蜂”“7-11”等便利店，甚至“叮咚买菜”这样的生鲜电商，都是我的间接竞品。他们都在解决用户“足不出户解决吃饭问题”这个需求。\n* **潜在竞品**：这类产品目前和我们没有直接竞争，但未来有可能凭借其资源、技术或用户规模，跨界进入我们的领域。比如，一个拥有海量流量的社交巨头，如果某天宣布要大力发展本地生活服务，那它就会立刻成为我最警惕的潜在竞品。\n\n为了方便我们快速识别，我总结了下面的表格：\n\n| **竞品分类** | **核心特征** | **举例（假设我们是“微信”）** |\n| :--- | :--- | :--- |\n| **直接竞品** | 目标用户、产品形态、核心功能都高度相似。 | QQ、钉钉（在办公场景下） |\n| **间接竞品** | 满足用户的同一类核心需求，但方案不同。 | 抖音（争夺用户时长）、电话/短信（解决通信需求） |\n| **潜在竞品** | 目前无竞争，但未来可能进入市场的重量级玩家。 | 一个新兴的、技术驱动的社交创业公司 |\n\n---\n\n#### 2. 竞品分析方法\n\n![image-20250718221149966](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718221149966.png)\n\n明确了要分析谁，下一步就是“如何分析”。仅仅是截几张图、看几个功能是远远不够的。我需要一个框架来保证分析的系统性和深度。我个人最推崇的，是**用户体验五要素模型**。它能帮我像剥洋葱一样，从表到里地把一个产品彻底解构。\n\n1.  **表现层 (Surface)**：这是最表层的，用户能直接感知的视觉设计。包括配色、字体、图标、布局的美感等。\n2.  **框架层 (Skeleton)**：这是界面的骨架，决定了信息在页面上的排布。比如按钮放哪里，搜索框放哪里，导航怎么设计。\n3.  **结构层 (Structure)**：这是产品的流程和信息架构。用户从一个页面如何跳转到另一个页面？产品的功能模块是如何组织的？\n4.  **范围层 (Scope)**：这是产品具体包含了哪些功能和内容。比如，一个电商App，它的范围层就包括了商品展示、购物车、订单、支付等一系列功能。\n5.  **战略层 (Strategy)**：这是最核心的，产品的商业目标和用户需求是什么？它为什么要做这个产品？\n\n当我用这五个层次去分析一个竞品时，我看到的就不再是一个个孤立的界面，而是其背后完整的产品思考和商业逻辑。\n\n---\n\n#### 3. 竞品分析的适用场景\n\n![image-20250718221340808](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718221340808.png)\n\n我做竞品分析，从不是为了分析而分析，一定是带有明确目的的。以下是我认为最有价值的几个应用场景：\n\n* **了解行业**：当我刚进入一个新领域时，我会把市面上Top3的竞品，用五要素模型完整地分析一遍。这是我快速了解行业格局、用户现状和主流玩法的最佳途径。\n* **产品设计**：在设计某个具体功能时，比如“购物车”，我一定会去体验至少5个主流App的购物车是怎么设计的。我的目的不是抄，而是去归纳总结，了解业界成熟的设计模式，避免重复造轮子和踩坑。\n* **寻找差异化**：通过对主要竞品的优劣势分析（比如使用SWOT模型），我可以清晰地看到市场上的空白地带和未被满足的需求。这对于我们制定差异化竞争策略、找到自己的生态位至关重要。\n* **方案验证**：如果我的某个直接竞品上线了一个新功能，并且获得了很好的市场反馈，那它在某种程度上帮我验证了这个功能背后的用户需求是真实存在的。反之，如果竞品的功能失败了，那它也等于免费给我上了一课。\n\n\n\n\n---\n\n### 2.2.6 用户访谈\n\n在我看来，数据能告诉我用户“做了什么”，但只有用户访谈能告诉我，他们“为什么这么做”。它是产品经理建立用户同理心、挖掘深层次需求的终极武器，没有任何工具可以替代。\n\n#### 1. 用户访谈的定义与流程\n\n![image-20250719083410461](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719083410461.png)\n\n**用户访谈的定义**：对我而言，它是一场有目的、有结构的、一对一的深度对话。我通过这场对话，来探寻用户在特定场景下的行为、动机、态度和痛点。它是一种定性研究方法，我追求的是洞察的深度，而非样本的数量。\n\n一场专业的访谈绝不是一次随意的聊天，它需要我进行精心的策划和准备。我通常会遵循一个六步走的流程：\n\n1.  **确定访谈形式**：首先，我要决定访谈的方式。是成本较高但信息丰富的**线下面对面**？还是高效便捷的**电话/线上视频**？\n2.  **明确访谈目的**：在开始前，我必须能用一句话说清楚“我这次访谈想解决的核心问题是什么？”例如：“探究用户在深夜场景下点外卖的核心决策因素。”\n3.  **用户筛选**：我需要找到“对”的人。根据我的访谈目的，我会设定清晰的用户标准（比如年龄、使用频率、所在城市等），然后通过问卷或后台数据进行筛选。\n4.  **设计访谈问题**：这是访谈的灵魂。我会提前准备一份访谈提纲，里面包含了一系列精心设计的开放式问题。\n5.  **邀请用户访谈**：我会正式地联系并邀请筛选出来的用户，说明我们的目的、时长，并通常会提供一些小礼品（如礼品卡、代金券）作为答谢。\n6.  **结果汇总与分析**：访谈结束后，我会立刻整理访谈记录，然后将多次访谈的结果放在一起，寻找其中反复出现的模式、观点和痛点，最终提炼出有价值的洞察。\n\n---\n\n#### 2. 访谈问题设计要点\n\n![image-20250719083541359](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719083541359.png)\n\n访谈的成败，很大程度上取决于我问题的质量。我设计问题时，会重点把握以下几点：\n\n**问题设置的方向：**\n我的访谈问题通常会遵循“现状 → 痛点 → 方案”的逻辑顺序展开，层层递进。\n\n* **现状类问题**：用于“破冰”，了解用户当下的行为和场景。如：“能带我回忆一下，您上一次点外卖的全过程吗？”\n* **痛点类问题**：用于挖掘用户的挫折和不满。如：“在刚才您描述的整个过程中，有没有哪个环节让您觉得特别麻烦或者不爽？”\n* **方案/期望类问题**：用于探寻用户的期望和潜在需求。如：“如果抛开所有限制，您心目中最理想的外卖App应该是什么样的？”\n\n**问题设计的方式：**\n* **多问开放式问题**：我从不问“你喜欢我们的App吗？”这类可以用“是/否”回答的问题。我会问：“关于我们的App，你有什么样的使用体验和感受？”\n* **不断追问**：当用户提到一个关键点时，我最常使用的工具就是追问“为什么？”“可以再多讲讲吗？”“后来呢？”。这能帮我挖得更深。\n* **避免引导性提问**：我绝不会问“你是不是觉得我们的红包功能很难找？”。这会把我的观点强加给用户。我会问：“您平时会使用我们的红包功能吗？可以聊聊您使用它的过程吗？”\n\n---\n\n#### 3. 用户访谈示例\n\n![image-20250719084031980](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084031980.png)\n\n我们还用外卖平台的案例。假设我的访谈目的是“了解白领用户在办公室选择午餐外卖的决策过程”。我的问题可能会这样设计：\n\n* **（现状）** “您可以回忆一下昨天中午点外卖的经历吗？从你想到要点外卖，到最后拿到外卖，都发生了什么？”\n* **（痛点）** “在挑选餐厅和菜品的过程中，有没有哪个环节让您觉得很纠结或者浪费时间？”\n* **（追问）** “您刚才提到‘选来选去最后还是点了常吃的那家’，为什么会这样呢？”\n* **（期望）** “如果我们可以帮您解决‘选择困难’这个问题，您希望我们怎么做？”\n\n---\n\n#### 4. 注意事项及适用场景\n\n![image-20250719083846814](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719083846814.png)\n\n* **我的注意事项**：\n    1.  **多听少说**：访谈的主角是用户，我的任务是引导和倾听。\n    2.  **保持中立**：无论用户怎么夸奖或吐槽我的产品，我都要保持客观，不争辩，不解释。\n    3.  **事实与观点分离**：记录时，要严格区分哪些是用户说的“事实”，哪些是我自己的“分析和判断”。\n\n* **适用场景**：\n    我通常会在项目的**早期探索阶段**大量使用用户访谈，因为这时我对用户和问题还很模糊，需要建立认知。此外，在**新功能的构思和验证阶段**，我也会通过访谈，向用户展示原型或概念，来快速获取反馈。\n\n---\n\n为了方便我们回顾，我将用户访谈的核心要点总结在下面的表格里：\n\n| **核心环节** | **我的关键动作** |\n| :--- | :--- |\n| **访谈前** | **明确目的**、**筛选用户**、**设计开放式问题提纲** |\n| **访谈中** | **多听少说**，像海绵一样吸收信息；通过**不断追问**来深挖；**保持中立**，不评判。 |\n| **访谈后** | **及时整理**笔记，**寻找共性**，将零散的观点**提炼为洞察**。 |\n\n\n\n---\n\n### 2.2.7 实地调研\n\n如果说用户访谈是“听其言”，那么实地调研就是“观其行”。在我看来，这是两种方法最大的区别。很多时候，用户说的和他实际做的并不完全一致，而实地调研，就是让我有机会亲眼去见证这种差异，发现那些连用户自己都未曾察觉的隐性需求。\n\n#### 1. 实地调研的定义\n\n对我而言，**实地调研就是产品经理亲自进入用户的真实物理场景中，通过近距离观察和亲身体验，来理解用户行为和背后动机的一种研究方法。**\n\n它包含两种核心形式：\n* **观察法**：我像一个“隐形人”，在不打扰用户的前提下，静静地观察他在特定场景下是如何与环境、工具（包括我们的产品）进行互动的。\n* **实地体验**：我亲自扮演用户的角色，走一遍他完整的任务流程，切身感受他在每个环节的顺畅与阻碍。\n\n---\n\n#### 2. 如何进行实地调研\n\n![image-20250719084246476](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084246476.png)\n\n一次有效的实地调研，需要我像导演一样，精心设计整个过程。我通常会遵循以下四个步骤：\n\n1.  **进入场景**：这是第一步，也是最关键的一步。比如，我要研究餐厅后厨的采购流程，那我就必须真的穿上工作服，走进那个潮湿、繁忙的后厨，而不是坐在办公室里想象。\n2.  **用户角色**：进入场景后，我要明确我的角色。我是作为一名旁观者去“观察”？还是亲自上手，作为一名“学徒”去“体验”整个下单、验货、入库的流程？\n3.  **观察体会**：在场景中，我的所有感官都要打开。我会重点观察：用户在做什么？他使用了什么工具？他与其他人是如何协作的？他在哪个环节面露难色？哪个环节的效率特别低？如果是我自己体验，我会记录下每一步的感受。\n4.  **持续进行**：一次调研是远远不够的。我会选择不同时间、不同类型的场景（比如高峰期与平峰期的餐厅后厨）进行多次调研，以确保我看到的不是偶然现象，而是普遍存在的问题。\n\n---\n\n#### 3. 实地调研适用场景\n\n![image-20250719084347265](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084347265.png)\n\n实地调研虽然强大，但成本也很高，所以我必须在最需要它的地方“对症下药”。以下是我最常使用它的几个场景：\n\n* **挖掘需求**：当我要为一个全新的领域（比如智慧农业）设计产品时，我对用户的真实作业环境一无所知，这时实地调研是建立基础认知的唯一途径。\n* **理解需求**：当用户向我提了一个我无法理解的需求时，比如“你们的扫码枪不好用”，我会直接去他的仓库，看他到底是怎么用的，问题出在哪里。\n* **效果验证**：我的新功能上线后，我会去现场观察用户是如何使用它的，是否符合我的设计预期，有没有出现我没想到的问题。\n* **寻找问题**：当我的产品数据出现异常，比如某个环节转化率突然下降，我会去实地观察，看看是不是用户的线下操作流程发生了变化，从而导致了线上的问题。\n\n实地调研是我们产品经理走出办公室，拥抱真实世界的最佳方式。我把它总结为以下要点：\n\n| **核心问题** | **我的关键动作** |\n| :--- | :--- |\n| **1. 什么是实地调研？** | 亲自**进入用户的真实场景**，通过**观察**和**体验**来理解用户的真实行为。 |\n| **2. 如何进行实地调研？** | **进入场景 → 代入角色 → 细心体会 → 持续进行**，这是一个完整的闭环。 |\n\n\n\n\n-----\n\n## 2.3 需求管理\n\n对我来说，如果说需求收集是“狩猎”，那么需求管理就是“庖丁解牛”和“精细烹饪”。我需要一个系统化的流程和工具，来处理我收集到的所有“食材”（需求），确保最有价值的部分能被优先端上“餐桌”（进入开发）。这个系统的核心，我称之为\\*\\*“需求池”\\*\\*。\n\n### 2.3.1 学习目标\n\n![image-20250719084741571](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084741571.png)\n\n在本节中，我的目标是带大家学会如何搭建和维护一个健康、高效的需求池。我将分享需求池应该包含哪些关键信息，需求在池子里会经历怎样的生命周期，以及我始终坚持的管理原则。学完本节，我希望我们都能成为一名思路清晰的“需求管家”。\n\n### 2.3.2 需求池定义\n\n![image-20250719084852379](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084852379.png)\n\n\n\n![image-20250719084802024](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084802024.png)\n\n**需求池**，顾名思义，就是一个用来汇集和管理所有需求的“池子”。我把它定义为：**一个用于统一记录、跟踪和评估产品所有相关需求的中央数据库**。它是我管理产品的“唯一事实来源”，我通常会用Jira、Trello或一个功能强大的Excel表格来搭建它。\n\n为了让这个池子有效运转，我记录的每一条需求，都必须包含一些标准化的信息字段，其中最重要的包括：\n\n  * **产品模块**：这个需求属于哪个功能板块？（如：登录注册、订单流程）\n  * **需求描述**：用清晰的语言描述用户场景、痛点和期望。（What & Why）\n  * **优先级**：这个需求有多重要？（我常用P0/P1/P2/P3来划分）\n  * **需求提出人**：这条需求来自谁？（如：用户A、销售部、老板）\n  * **需求类型**：这是一个新功能、体验优化、Bug还是技术需求？\n\n### 2.3.3 需求状态\n\n![image-20250719085530024](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719085530024.png)\n\n进入我需求池的每一条需求，都不会石沉大海，它会拥有一个清晰的生命周期。我会通过“状态”这个字段来追踪它的进展。一个标准的需求生命周期流程如下：\n\n1.  **待确认**：这是需求的入口。所有新收集到的、未经我详细分析的需求，都先放在这里。\n2.  **已确认**：经过我的分析，确认这是一个真实、有价值的需求，但还没想好什么时候做。\n3.  **规划中**：需求已通过评审，并被正式排入某个版本迭代的开发计划中。\n4.  **已完成**：需求已开发、测试、上线。这是它旅程的终点。\n5.  **已拒绝**：经过分析，我认为这个需求价值不大、或与产品方向不符，决定不做。给需求一个明确的“死亡”结果，同样非常重要。\n\n### 2.3.4 需求池的作用\n\n![image-20250719085617473](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719085617473.png)\n\n我之所以如此看重需求池，是因为它为我、为整个团队都带来了巨大的价值。\n\n  * **管理需求**：它是所有需求的统一入口和视图，避免了需求散落在邮件、微信、会议纪要里，造成遗忘和混乱。\n  * **维护需求**：我可以随时查看任何一个需求的状态、优先级和负责人，对整个产品的迭代节奏了如指掌。\n  * **回溯需求**：当未来有人问“我们当初为什么要做这个功能？”时，我可以立刻从需求池里调出当时的背景、分析和决策过程。它是我们产品决策的“历史档案”。\n\n### 2.3.5 需求池管理原则\n\n![image-20250719085705428](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719085705428.png)\n\n一个只进不出的需求池，很快就会变成一个令人绝望的“需求坟场”。为了保持它的活力和价值，我始终坚守两条管理原则：\n\n1.  **有进有出**：需求池必须是流动的。我需要定期（比如每周）审视池中的需求，推动它们的状态向前流转。要么进入规划，要么明确拒绝，绝不能让大量需求长期停滞在“待确认”状态。\n2.  **宽进严出**：对于需求的“进入”，我持开放态度，鼓励各方提出想法，所以入口要“宽”。但对于需求的“输出”（即进入开发），我必须严格把关，基于用户价值、商业目标、投入产出比等因素进行严苛的筛选和排序。\n\n我将需求管理的核心要点，总结在下面的表格中：\n\n| **核心概念** | **我的实践要点** |\n| :--- | :--- |\n| **需求池** | 建立一个包含“优先级、状态”等关键字段的**中央数据库**，作为唯一事实来源。 |\n| **需求状态** | 用“待确认 → 已完成/已拒绝”的**清晰流程**，追踪每条需求的生命周期。 |\n| **管理原则** | **宽进严出**：鼓励收集，严格筛选。<br>**有进有出**：保持流动，拒绝僵化。 |\n\n\n\n\n\n\n\n-----\n\n## 2.4 本章总结\n\n在这里我附上需求池模板供读者使用\n\n\n{% link 需求池模板.xlsx,Prorise,https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/AI%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/01%E9%9C%80%E6%B1%82%E6%B1%A0.xlsx,https://bu.dusays.com/2025/07/19/687b0b6b83e43.png %}\n\n\n\n\n![image-20250719105224592](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719105224592.png)\n\n最后，我们来回顾一下整个第二章的核心脉络。我认为，一名合格的产品经理在处理需求时，必须走完这三个密不可分的步骤：\n\n1.  **认知需求**：首先，我们要能透过现象看本质，深刻理解`“什么是需求”`——它不是用户说的原话，而是能解决用户在特定场景下本质痛点的方案。\n2.  **收集需求**：其次，我们要主动出击，运用竞品分析、用户访谈、实地调研等多种手段，从内外部多个渠道，系统地`“如何收集需求”`。\n3.  **管理需求**：最后，我们要建立并维护一个动态、健康的需求池，对所有需求进行科学的`“需求管理”`，确保我们永远在做最有价值的事。\n\n掌握从认知、收集到管理的完整闭环，是我们做出成功产品的基石。\n\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E7%AB%A0%EF%BC%9A%E9%9C%80%E6%B1%82%E6%94%B6%E9%9B%86%E4%B8%8E%E7%AE%A1%E7%90%86"><span class="toc-number">1.</span> <span class="toc-text">第二章：需求收集与管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#2-1-%E4%BB%80%E4%B9%88%E6%98%AF%E9%9C%80%E6%B1%82"><span class="toc-number">1.1.</span> <span class="toc-text">2.1 什么是需求</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.1.</span> <span class="toc-text">2.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-2-%E5%85%AC%E5%8F%B8%E8%83%8C%E6%99%AF%E4%B8%8E%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-number">1.1.2.</span> <span class="toc-text">2.1.2 公司背景与项目背景</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-3-%E4%BB%80%E4%B9%88%E6%98%AF%E9%9C%80%E6%B1%82%EF%BC%88%E6%A1%88%E4%BE%8B%EF%BC%89"><span class="toc-number">1.1.3.</span> <span class="toc-text">2.1.3 什么是需求（案例）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-4-%E9%9C%80%E6%B1%82%E7%9A%84%E5%B8%B8%E8%A7%81%E5%BD%A2%E5%BC%8F"><span class="toc-number">1.1.4.</span> <span class="toc-text">2.1.4 需求的常见形式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-5-%E9%9C%80%E6%B1%82%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-number">1.1.5.</span> <span class="toc-text">2.1.5 需求的定义</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-2-%E9%9C%80%E6%B1%82%E5%A6%82%E4%BD%95%E6%94%B6%E9%9B%86"><span class="toc-number">1.2.</span> <span class="toc-text">2.2 需求如何收集</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.2.1.</span> <span class="toc-text">2.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-2-%E9%9C%80%E6%B1%82%E7%9A%84%E6%9D%A5%E6%BA%90"><span class="toc-number">1.2.2.</span> <span class="toc-text">2.2.2 需求的来源</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-3-%E9%9C%80%E6%B1%82%E6%94%B6%E9%9B%86%E6%96%B9%E5%BC%8F%E5%88%86%E7%B1%BB"><span class="toc-number">1.2.3.</span> <span class="toc-text">2.2.3 需求收集方式分类</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-4-%E5%B8%B8%E8%A7%81%E7%9A%84%E9%9C%80%E6%B1%82%E6%94%B6%E9%9B%86%E6%96%B9%E6%B3%95"><span class="toc-number">1.2.4.</span> <span class="toc-text">2.2.4 常见的需求收集方法</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-5-%E7%AB%9E%E5%93%81%E5%88%86%E6%9E%90"><span class="toc-number">1.2.5.</span> <span class="toc-text">2.2.5 竞品分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%AB%9E%E5%93%81%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E5%88%86%E7%B1%BB"><span class="toc-number">1.2.5.1.</span> <span class="toc-text">1. 竞品的定义与分类</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%AB%9E%E5%93%81%E5%88%86%E6%9E%90%E6%96%B9%E6%B3%95"><span class="toc-number">1.2.5.2.</span> <span class="toc-text">2. 竞品分析方法</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%AB%9E%E5%93%81%E5%88%86%E6%9E%90%E7%9A%84%E9%80%82%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.2.5.3.</span> <span class="toc-text">3. 竞品分析的适用场景</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-6-%E7%94%A8%E6%88%B7%E8%AE%BF%E8%B0%88"><span class="toc-number">1.2.6.</span> <span class="toc-text">2.2.6 用户访谈</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%94%A8%E6%88%B7%E8%AE%BF%E8%B0%88%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E6%B5%81%E7%A8%8B"><span class="toc-number">1.2.6.1.</span> <span class="toc-text">1. 用户访谈的定义与流程</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%AE%BF%E8%B0%88%E9%97%AE%E9%A2%98%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.2.6.2.</span> <span class="toc-text">2. 访谈问题设计要点</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%94%A8%E6%88%B7%E8%AE%BF%E8%B0%88%E7%A4%BA%E4%BE%8B"><span class="toc-number">1.2.6.3.</span> <span class="toc-text">3. 用户访谈示例</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9%E5%8F%8A%E9%80%82%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.2.6.4.</span> <span class="toc-text">4. 注意事项及适用场景</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-7-%E5%AE%9E%E5%9C%B0%E8%B0%83%E7%A0%94"><span class="toc-number">1.2.7.</span> <span class="toc-text">2.2.7 实地调研</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%AE%9E%E5%9C%B0%E8%B0%83%E7%A0%94%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-number">1.2.7.1.</span> <span class="toc-text">1. 实地调研的定义</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%A6%82%E4%BD%95%E8%BF%9B%E8%A1%8C%E5%AE%9E%E5%9C%B0%E8%B0%83%E7%A0%94"><span class="toc-number">1.2.7.2.</span> <span class="toc-text">2. 如何进行实地调研</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%AE%9E%E5%9C%B0%E8%B0%83%E7%A0%94%E9%80%82%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.2.7.3.</span> <span class="toc-text">3. 实地调研适用场景</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-3-%E9%9C%80%E6%B1%82%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.</span> <span class="toc-text">2.3 需求管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.3.1.</span> <span class="toc-text">2.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-2-%E9%9C%80%E6%B1%82%E6%B1%A0%E5%AE%9A%E4%B9%89"><span class="toc-number">1.3.2.</span> <span class="toc-text">2.3.2 需求池定义</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-3-%E9%9C%80%E6%B1%82%E7%8A%B6%E6%80%81"><span class="toc-number">1.3.3.</span> <span class="toc-text">2.3.3 需求状态</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-4-%E9%9C%80%E6%B1%82%E6%B1%A0%E7%9A%84%E4%BD%9C%E7%94%A8"><span class="toc-number">1.3.4.</span> <span class="toc-text">2.3.4 需求池的作用</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-5-%E9%9C%80%E6%B1%82%E6%B1%A0%E7%AE%A1%E7%90%86%E5%8E%9F%E5%88%99"><span class="toc-number">1.3.5.</span> <span class="toc-text">2.3.5 需求池管理原则</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-4-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.4.</span> <span class="toc-text">2.4 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>