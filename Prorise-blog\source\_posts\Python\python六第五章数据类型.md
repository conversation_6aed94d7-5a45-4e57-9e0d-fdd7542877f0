---
title: Python（六）：第五章：数据类型
categories:
  - 后端技术
  - Python
tags:
  - Python基础知识总汇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp'
comments: true
toc: true
ai: true
abbrlink: 50189
date: 2025-04-18 21:13:45
---

## 第五章：数据类型

### 5.1 字符串类型(str)

#### 字符串输出方式

| `占位符类型` | 说明                                            |
| ------------ | ----------------------------------------------- |
| `%s`         | 字符串（使用 `str()` 方法转换任何 Python 对象） |
| `%d`         | 十进制整数                                      |
| `%f`         | 十进制浮点数(小数), 自动保留六位小数。          |

```python
# 占位符方式
print('我的名字是 %s, 我的年龄是 %d岁' % ('小明', 28))

#    
print('我的名字是 {}, 我的年龄是 {}岁'.format('小明', 28))
# format带索引
print('我的名字是 {1}, 我的年龄是 {0}岁'.format(28, '小明'))

# f-string方式(推荐)
name = '小明'
age = 28
print(f'我的名字是 {name}, 我的年龄是 {age}岁')
```

#### 字符串常用方法

| 方法         | 描述                       | 示例                          |
| ------------ | -------------------------- | ----------------------------- |
| startswith() | 判断是否以指定内容开头     | `str.startswith('hello')`     |
| endswith()   | 判断是否以指定内容结尾     | `str.endswith('world')`       |
| isdigit()    | 判断是否为数字组成         | `'12345'.isdigit()`           |
| isalpha()    | 判断是否为文字组成         | `'hello'.isalpha()`           |
| count()      | 统计元素出现次数           | `'hello'.count('l')`          |
| find()       | 查找子串位置，未找到返回-1 | `'hello world'.find('world')` |
| upper()      | 转为大写                   | `'hello'.upper()`             |
| lower()      | 转为小写                   | `'HELLO'.lower()`             |
| replace()    | 替换字符串                 | `'hello'.replace('h', 'H')`   |
| split()      | 分割字符串为列表           | `'a,b,c'.split(',')`          |
| join()       | 拼接列表为字符串           | `','.join(['a', 'b', 'c'])`   |
| strip()      | 去除两端空格或指定字符     | `' hello '.strip()`           |
| lstrip()     | 去除左侧空格或指定字符     | `' hello'.lstrip()`           |
| rstrip()     | 去除右侧空格或指定字符     | `'hello '.rstrip()`           |
| title()      | 将字符串标题化             | `'hello world'.title()`       |

```python
# ======== 字符串方法示例 ========

# startswith() - 判断是否以指定内容开头
text = "Hello, World!"
print(f"'Hello, World!' 是否以'Hello'开头: {text.startswith('Hello')}")  # True
print(f"'Hello, World!' 是否以'World'开头: {text.startswith('World')}")  # False

# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========

# endswith() - 判断是否以指定内容结尾
print(f"'Hello, World!' 是否以'!'结尾: {text.endswith('!')}")  # True
print(f"'Hello, World!' 是否以'Hello'结尾: {text.endswith('Hello')}")  # False
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# isdigit() - 判断是否为数字组成
num_str = "12345"
print(f"'12345' 是否全为数字: {num_str.isdigit()}")  # True
print(f"'Hello' 是否全为数字: {'Hello'.isdigit()}")  # False
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# isalpha() - 判断是否为文字组成
alpha_str = "hello"
print(f"'hello' 是否全为字母: {alpha_str.isalpha()}")  # True
print(f"'hello123' 是否全为字母: {'hello123'.isalpha()}")  # False
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# count() - 统计元素出现次数
print(f"'hello' 中 'l' 出现的次数: {'hello'.count('l')}")  # 2
print(f"'Mississippi' 中 's' 出现的次数: {'Mississippi'.count('s')}")  # 4
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# find() - 查找子串位置，未找到返回-1
print(f"'hello world' 中 'world' 的位置: {'hello world'.find('world')}")  # 6
print(f"'hello world' 中 'python' 的位置: {'hello world'.find('python')}")  # -1
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# upper() - 转为大写
print(f"'hello' 转大写: {'hello'.upper()}")  # HELLO
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# lower() - 转为小写
print(f"'HELLO' 转小写: {'HELLO'.lower()}")  # hello
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# replace() - 替换字符串
print(f"'hello' 替换 'h' 为 'H': {'hello'.replace('h', 'H')}")  # Hello
print(f"'hello hello' 替换所有 'l' 为 'L': {'hello hello'.replace('l', 'L')}")  # heLLo heLLo
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# split() - 分割字符串为列表
print(f"'a,b,c' 按逗号分割: {'a,b,c'.split(',')}")  # ['a', 'b', 'c']
print(f"'hello world' 按空格分割: {'hello world'.split()}")  # ['hello', 'world']
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# join() - 拼接列表为字符串
print(f"用逗号连接 ['a', 'b', 'c']: {','.join(['a', 'b', 'c'])}")  # a,b,c
print(f"用空格连接 ['hello', 'world']: {' '.join(['hello', 'world'])}")  # hello world
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# strip() - 去除两端空格或指定字符
print(f"' hello ' 去除两端空格: {' hello '.strip()}")  # hello
print(f"'xxxhelloxxx' 去除两端的x: {'xxxhelloxxx'.strip('x')}")  # hello
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# lstrip() - 去除左侧空格或指定字符
print(f"' hello' 去除左侧空格: {' hello'.lstrip()}")  # hello
print(f"'xxxhello' 去除左侧的x: {'xxxhello'.lstrip('x')}")  # hello
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# rstrip() - 去除右侧空格或指定字符
print(f"'hello ' 去除右侧空格: {'hello '.rstrip()}")  # hello
print(f"'helloxxx' 去除右侧的x: {'helloxxx'.rstrip('x')}")  # hello
# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========
# title() - 将字符串标题化（每个单词首字母大写）
print(f"'hello world' 标题化: {'hello world'.title()}")  # Hello World
```

### 5.2 列表类型(list)

#### 添加元素
```python
# append()：在列表末尾添加元素
l = ['Python', 'C++', 'Java']
l.append('PHP')  # ['Python', 'C++', 'Java', 'PHP']

# extend()：批量添加元素，逐一添加
l = ['Python', 'C++', 'Java']
l.extend(['C#', 'C', 'JavaScript'])  # ['Python', 'C++', 'Java', 'C#', 'C', 'JavaScript']

# insert()：在指定位置插入元素
l = ['Python', 'C++', 'Java']
l.insert(1, 'C')  # ['Python', 'C', 'C++', 'Java']
```

#### 删除元素
```python
# remove()：删除指定元素（第一个匹配项）
nums = [40, 36, 89, 2, 36, 100, 7]
nums.remove(36)  # [40, 89, 2, 36, 100, 7]

# pop()：删除指定索引元素，返回被删除的元素
nums = [40, 36, 89, 2, 36, 100, 7]
nums.pop(3)  # [40, 36, 89, 36, 100, 7]
nums.pop()   # 不指定索引，默认删除最后一个

# clear()：清空列表
nums = [40, 36, 89, 2, 36, 100, 7]
nums.clear()  # []

# del：删除指定元素或切片
nums = [40, 36, 89, 2, 36, 100, 7]
del nums[2]    # 删除单个元素
del nums[:2]   # 删除切片范围的元素
```

#### 查找元素
```python
# index()：查找元素索引
nums = [40, 36, 89, 2, 36, 100]
print(nums.index(36))  # 返回第一个匹配元素的索引：1

# in 运算符：判断元素是否存在
if 89 in nums:
    print("元素存在")
    
# enumerate()：同时获取索引和元素
for index, value in enumerate(nums):
    print(f"索引 {index}: 值 {value}")
    
# count()：统计元素出现次数
print(nums.count(36))  # 返回元素出现次数：2
```

#### 排序
```python
# sort()：原地排序
nums = [40, 36, 89, 2, 100, 7]
nums.sort()  # [2, 7, 36, 40, 89, 100]
nums.sort(reverse=True)  # 降序：[100, 89, 40, 36, 7, 2]

# sorted()：返回新列表，原列表不变
nums = [40, 36, 89, 2, 100, 7]
sorted_nums = sorted(nums)  # [2, 7, 36, 40, 89, 100]
print(nums)  # 原列表不变：[40, 36, 89, 2, 100, 7]

# 自定义排序（按字符串长度）,key可以编写一个匿名函数作为条件
words = ['apple', 'banana', 'cherry', 'date']
sorted_words = sorted(words, key=len)  # ['date', 'apple', 'cherry', 'banana']

# 使用sorted函数结合lambda函数进行复杂排序
# 创建一个包含学生信息的列表（姓名、年龄、成绩、是否参加课外活动）
students = [
    {"name": "张三", "age": 18, "score": 85, "extracurricular": True},
    {"name": "李四", "age": 17, "score": 92, "extracurricular": False},
    {"name": "王五", "age": 19, "score": 78, "extracurricular": True},
    {"name": "赵六", "age": 18, "score": 85, "extracurricular": False},
    {"name": "钱七", "age": 20, "score": 90, "extracurricular": True}
]

# 基本排序：按照学生成绩从高到低排序
# lambda x: x["score"] - 定义一个匿名函数，接收一个参数x（列表中的每个元素），返回x的score值
# reverse=True - 表示降序排序（从高到低）
sorted_by_score = sorted(students, key=lambda x: x["score"], reverse=True)
print("按照学生成绩从高到低排序：")
for student in sorted_by_score:
    print(f"姓名：{student['name']}，年龄：{student['age']}，学生成绩：{student['score']}")


```

#### 访问嵌套列表
```python
nested_list = ['prorise', 185, True, [1, 2, 3]]
print(nested_list[3][1])  # 访问嵌套列表的元素：2
```

### 5.3 元组类型(tuple)

#### 元组的创建方式

```python
# 1. 使用小括号创建
t1 = (1, 2, 3, 4, 5)

# 2. 省略小括号创建
t2 = 1, 2, 3, 4, 5

# 3. 单元素元组（必须加逗号，否则只是普通值，不是元组）
t3 = (42,)   # 正确：这是一个元组
t4 = (42)    # 错误：这是一个整数，不是元组
t5 = 42,     # 正确：这也是一个元组

# 4. 使用tuple()函数从其他可迭代对象创建
t6 = tuple([1, 2, 3])        # 从列表创建
t7 = tuple("hello")          # 从字符串创建: ('h', 'e', 'l', 'l', 'o')
t8 = tuple({1: 'a', 2: 'b'}) # 从字典创建: (1, 2) - 只保留键

# 5. 创建空元组
empty_tuple = ()
also_empty = tuple()

# 6. 嵌套元组
nested = (1, (2, 3), (4, 5, 6))

# 7. 不同类型的元素
mixed = (1, "hello", True, None, 3.14)

```

#### 元组的特性与操作

##### 不可变性

```python
t = (1, 2, 3)
# t[0] = 5  # TypeError: 'tuple' object does not support item assignment

# 但可以通过连接创建新元组
t = t + (4, 5)  # 新元组: (1, 2, 3, 4, 5)
t = t * 2       # 重复元组: (1, 2, 3, 4, 5, 1, 2, 3, 4, 5)

# 元组的不可变性让它可以作为字典的键
d = {(1, 2): "tuple as key"}
# d[[1, 2]] = "list as key"  # TypeError: unhashable type: 'list'
```

元组中可变对象的行为

```python
# 元组中的可变对象（如列表）的内容可以修改
t = (1, [2, 3], 4)
t[1].append(5)  # 正确：修改元组中列表的内容
print(t)        # (1, [2, 3, 5], 4)

# t[1] = [6, 7]  # TypeError: 'tuple' object does not support item assignment
```

**重要说明**：元组的不可变性只适用于元组本身的结构（元素的标识符不能改变），而不适用于元组中所包含的可变对象的内容。

#### 访问与切片

```python
t = (0, 1, 2, 3, 4, 5)

# 索引访问（从0开始）
print(t[0])    # 0
print(t[-1])   # 5 (负索引从末尾开始)

# 切片
print(t[1:4])  # (1, 2, 3)
print(t[:3])   # (0, 1, 2)
print(t[3:])   # (3, 4, 5)
print(t[::2])  # (0, 2, 4) - 步长为2
print(t[::-1]) # (5, 4, 3, 2, 1, 0) - 逆序
```

#### 元组解包

```python
# 基本解包
t = (1, 2, 3)
a, b, c = t
print(a, b, c)  # 1 2 3

# 使用星号（*）收集多余的元素（Python 3.x）
t = (1, 2, 3, 4, 5)
a, b, *rest = t
print(a, b, rest)  # 1 2 [3, 4, 5]

first, *middle, last = t
print(first, middle, last)  # 1 [2, 3, 4] 5

# 忽略某些值（使用下划线作为惯例）
a, _, c = (1, 2, 3)
print(a, c)  # 1 3

# 交换变量值
a, b = 10, 20
a, b = b, a  # 使用元组解包交换值
print(a, b)  # 20 10
```

#### 元组方法和内置函数

元组的方法比列表少，因为它是不可变的。主要方法有：

```python
t = (1, 2, 3, 2, 4, 2)

# 1. count() - 计算元素出现的次数
print(t.count(2))  # 3

# 2. index() - 返回元素首次出现的索引
print(t.index(3))  # 2
# print(t.index(5))  # ValueError: tuple.index(x): x not in tuple

# 3. 使用内置函数
print(len(t))      # 6 - 元组长度
print(min(t))      # 1 - 最小元素
print(max(t))      # 4 - 最大元素
print(sum(t))      # 14 - 元素和
print(sorted(t))   # [1, 2, 2, 2, 3, 4] - 返回排序后的列表（非元组）
```



#### 元组的应用场景

##### 作为函数的返回值

```python
def get_user_info():
    # 返回多个值
    return "Alice", 30, "<EMAIL>"

# 调用函数并解包返回值
# 判断返回值是否为一个元组
if isinstance(get_user_info(), tuple):
    name, age, email = get_user_info()
    print(f"姓名: {name}, 年龄: {age}, 邮箱: {email}") # 输出姓名: Alice, 年龄: 30, 邮箱: <EMAIL>
else:
    print("返回值不是一个元组")
```

##### 作为不可变集合

```python
# 使用元组作为字典键
locations = {
    (40.730610, -73.935242): "New York",
    (34.052235, -118.243683): "Los Angeles",
    (41.878113, -87.629799): "Chicago"
}

# 更新字典，不更改元组
locations[(51.507351, -0.127758)] = "London"
```

##### 确保数据不被修改

```python
def process_config(config):
    # config是一个元组，确保处理过程中不被修改
    print("处理配置:", config)
    # 安全的操作...

# 使用
settings = ("debug", "verbose", "log_level=info")
process_config(settings)
```

##### 



### 5.4 字典类型(dict)

```python
# 创建字典
person = {"name": "张三", "age": 25, "city": "北京"}

# 访问值
print(person["name"])  # 张三

# 设置/修改值
person["age"] = 26
person["email"] = "<EMAIL>"  # 添加新键值对

# 删除键值对
del person["city"]
```

#### 字典常用方法

| 方法         | 描述                         | 示例                             |
| ------------ | ---------------------------- | -------------------------------- |
| get()        | 获取值，键不存在时返回默认值 | `dict.get('key', 默认值)`        |
| keys()       | 获取所有键                   | `dict.keys()`                    |
| values()     | 获取所有值                   | `dict.values()`                  |
| items()      | 获取所有键值对               | `dict.items()`                   |
| setdefault() | 获取值，键不存在则设置默认值 | `dict.setdefault('key', 默认值)` |
| update()     | 更新字典                     | `dict1.update(dict2)`            |
| pop()        | 移除指定键值对并返回值       | `dict.pop('key')`                |

### 5.5 集合类型(set)

```python
# 创建集合
s1 = {1, 2, 3, 4, 5}
s2 = set([1, 2, 3, 3, 2, 1])  # 结果：{1, 2, 3}

# 添加元素
s1.add(6)

# 删除元素
s1.discard(3)  # 元素不存在不会报错
s1.remove(2)   # 元素不存在会报错
```

#### 集合操作

| 操作 | 描述                            | 示例       |
| ---- | ------------------------------- | ---------- |
| &    | 交集（共同元素）                | `s1 & s2`  |
| \|   | 并集（去重合并）                | `s1 \| s2` |
| -    | 差集（属于 s1 不属于 s2）       | `s1 - s2`  |
| ^    | 对称差集（不同时属于 s1 和 s2） | `s1 ^ s2`  |

```python
# 求两个集合的交集(共同元素)
s1 = {1, 2, 3, 4, 5}
s2 = {1, 3, 5, 7, 9}
print(s1 & s2)  # {1, 3, 5}

# 求两个集合的并集（去重后在合并）
print(s1 | s2)  # {1, 2, 3, 4, 5, 7, 9}

# 求两个集合的差集（s1中有而s2中没有的元素）
print(s1 - s2)  # {2, 4}

# 求两个集合的对称差集（s1和s2中不同时存在的元素）
print(s1 ^ s2)  # {2, 4, 7, 9}
```