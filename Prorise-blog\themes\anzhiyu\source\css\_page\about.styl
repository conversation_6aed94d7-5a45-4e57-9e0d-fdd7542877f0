body[data-type='about'] #page {
  border: 0;
  box-shadow: none !important;
  padding: 0 !important;
  background: transparent !important;
}

body[data-type='about'] #page .page-title {
  display: none;
}

body[data-type='about'] .page #footer-wrap {
  opacity: 1;
  overflow: visible;
  height: auto !important;
  min-height: 16px;
  color: #666;
}

body[data-type='about'] #web_bg {
  background: var(--anzhiyu-background);
}

#about-page
  padding-top: 1rem;
  .author-box
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 0 16px 0;
    .author-tag-left
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      +maxWidth768()
        display: none
      .author-tag:first-child, 
      .author-tag:last-child
        margin-right: -16px
    .author-tag-right
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      +maxWidth768()
        display: none
      .author-tag:first-child, 
      .author-tag:last-child
        margin-left: -16px
    .author-tag
      transform: translate(0,-4px);
      padding: 1px 8px;
      background: var(--anzhiyu-card-bg);
      border: var(--style-border-always);
      border-radius: 40px;
      margin-top: 6px;
      font-size: 14px;
      font-weight: 700;
      box-shadow: var(--anzhiyu-shadow-lightblack);
      animation: 6s ease-in-out 0s infinite normal none running floating;
      &:nth-child(1)
        animation-delay: 0s;
      &:nth-child(2)
        animation-delay: .6s;
      &:nth-child(3)
        animation-delay: 1.2s;
      &:nth-child(4)
        animation-delay: 1.8s;

    .author-img
      margin: 0 30px;
      border-radius: 50%;
      width: 180px;
      height: 180px;
      position: relative;
      background: var(--anzhiyu-secondbg);
      user-select: none;
      transition: .3s;
      +maxWidth768()
        width: 120px;
        height: 120px;
      img
        border-radius: 50%;
        overflow: hidden;
        width: 180px;
        height: 180px;
        +maxWidth768()
          width: 120px;
          height: 120px;
      &:hover
        transform: scale(1.1);
      &:before
        content: '';
        transition: 1s;
        width: 30px;
        height: 30px;
        background: var(--anzhiyu-green);
        position: absolute;
        border-radius: 50%;
        border: 5px solid var(--anzhiyu-background);
        bottom: 5px;
        right: 10px;
        z-index: 2;
        +maxWidth768()
          bottom: -5px;
          right: -5px;

.author-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  margin-top: 1rem;
}

#about-page .myInfoAndSayHello {
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: var(--anzhiyu-white);
  background: linear-gradient(120deg, #5b27ff 0, #00d4ff 100%);
  background-size: 200%;
  animation: gradient 15s ease infinite;
  width: 59%;
}

.author-content-item {
  width: 49%;
  border-radius: 24px;
  background: var(--anzhiyu-card-bg);
  border: var(--style-border-always);
  box-shadow: var(--anzhiyu-shadow-border);
  position: relative;
  padding: 1rem 2rem;
  overflow: hidden;
}

#about-page .myInfoAndSayHello .title1 {
  opacity: 0.8;
  line-height: 1.3;
}

#about-page .myInfoAndSayHello .title2 {
  font-size: 36px;
  font-weight: 700;
  line-height: 1.1;
  margin: 0.5rem 0;
}

.inline-word {
  word-break: keep-all;
  white-space: nowrap;
}

#about-page .myInfoAndSayHello .title1 {
  opacity: 0.8;
  line-height: 1.3;
}

.author-content-item.aboutsiteTips {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  width: 39%;
}

.aboutsiteTips h2 {
  margin-right: auto;
  font-size: 36px;
  font-family: Helvetica;
  line-height: 1.06;
  letter-spacing: -0.02em;
  color: var(--font-color);
  margin-top: 0;
}

.aboutsiteTips .mask {
  height: 36px;
  position: relative;
  overflow: hidden;
  margin-top: 4px;
}

.aboutsiteTips .mask span {
  display: block;
  box-sizing: border-box;
  position: absolute;
  top: 36px;
  padding-bottom: var(--offset);
  background-size: 100% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-repeat: no-repeat;
}

.aboutsiteTips .mask span[data-show] {
  transform: translateY(-100%);
  transition: 0.5s transform ease-in-out;
}

.aboutsiteTips .mask span[data-up] {
  transform: translateY(-200%);
  transition: 0.5s transform ease-in-out;
}

.aboutsiteTips .mask span:nth-child(1) {
  background-image: linear-gradient(45deg, #0ecffe 50%, #07a6f1);
}

.aboutsiteTips .mask span:nth-child(2) {
  background-image: linear-gradient(45deg, #18e198 50%, #0ec15d);
}

.aboutsiteTips .mask span:nth-child(3) {
  background-image: linear-gradient(45deg, #8a7cfb 50%, #633e9c);
}

.aboutsiteTips .mask span:nth-child(4) {
  background-image: linear-gradient(45deg, #fa7671 50%, #f45f7f);
}

@media screen and (max-width: 768px) {
  .author-content-item.map {
    margin-bottom: 0;
  }
}

#about-page .about-statistic {
  min-height: 380px;
  width: 39%;
  background-size: cover;
  color: var(--anzhiyu-white);
  overflow: hidden;
}

#about-page .about-statistic::after {
  box-shadow: 0 -159px 173px 71px #0c1c2c inset;
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.author-content-item .card-content {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  flex-direction: column;
  padding: 1rem 2rem;
}

.author-content-item .card-content .author-content-item-title {
  margin-bottom: 0.5rem;
}

.author-content-item .author-content-item-title {
  font-size: 36px;
  font-weight: 700;
  line-height: 1;
}

#statistic {
  font-size: 16px;
  border-radius: 15px;
  width: 100%;
  color: var(--anzhiyu-white);
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 1rem;
  margin-bottom: 2rem;
}

#statistic div span:first-child {
  opacity: 0.8;
  font-size: 12px;
}

#statistic div span:last-child {
  font-weight: 700;
  font-size: 34px;
  line-height: 1;
  white-space: nowrap;
}

#statistic div {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  width: 50%;
  margin-bottom: 0.5rem;
}

.post-tips {
  color: var(--anzhiyu-gray);
  font-size: 14px;
  position: absolute;
  bottom: 1rem;
  left: 2rem;
}

.post-tips a {
  color: var(--anzhiyu-gray) !important;
  border: none !important;
}

.author-content-item .card-content .banner-button-group {
  position: absolute;
  bottom: 1.5rem;
  right: 2rem;
}

.author-content-item .card-content .banner-button-group .banner-button {
  height: 40px;
  border-radius: 20px;
  justify-content: center;
  background: var(--anzhiyu-white-op);
  color: var(--anzhiyu-white);
  display: inline-flex;
  align-items: center;
  z-index: 1;
  transition: 0.3s;
  cursor: pointer;
  border-bottom: 0 !important;
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transform: translateZ(0);
}

.author-content-item .card-content .banner-button-group .banner-button i, .author-content-item .card-content .banner-button-group .banner-button svg {
  margin-right: 0.25rem;
  font-size: 22px;
}

.author-content-item .card-content .banner-button-group .banner-button .banner-button-text {
  margin-left: 4px;
}

#about-page .author-content-item .card-content .banner-button-group .banner-button i, #about-page .author-content-item .card-content .banner-button-group .banner-button svg {
  font-size: 22px;
  margin-right: 0.25rem;
  height: 40px;
  max-width: 40px;
  display: flex;
  border-radius: 50px;
  align-items: center;
  justify-content: center;
}

.author-content-item .card-content .banner-button-group .banner-button:hover {
  background: var(--anzhiyu-main);
  color: var(--anzhiyu-white);
  border-radius: 20px !important;
}

.author-content-item .card-content .banner-button-group .banner-button:hover i, .author-content-item .card-content .banner-button-group .banner-button:hover svg .author-content-item.personalities {
  background: none !important;
}

.author-content-item.personalities {
  width: 59%;
}

.author-content-item.personalities .image {
  position: absolute;
  right: 30px;
  top: 10px;
  width: 220px;
  transition: transform 2s cubic-bezier(0.13, 0.45, 0.21, 1.02);
}

.author-content-item.personalities .image img {
  display: block;
  margin: 0 auto 20px;
  max-width: 100%;
  transition: filter 375ms ease-in 0.2s;
}

.author-content-item.personalities:hover .image {
  transform: rotate(-10deg);
}

.author-content-item.myphoto {
  height: 60%;
  min-height: 240px;
  position: relative;
  overflow: hidden;
  width: 39%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.author-content-item.myphoto img {
  position: absolute;
  min-width: 100%;
  object-fit: cover;
  transition: 0.6s;
  animation: coverIn 2s ease-out forwards;
  transition: transform 2s ease-out !important;
}

.author-content-item.myphoto:hover img {
  transform: scale(1.1);
}

.author-content-item:hover .card-background-icon {
  transform: rotate(20deg);
}

.author-content-item.personalities .title2 {
  font-size: 36px;
  font-weight: 700;
  line-height: 1.1;
}

.author-content-item.map {
  min-height: 160px;
  max-height: 400px;
  position: relative;
  overflow: hidden;
  margin-bottom: 0.5rem;
  height: 60%;
  transition: 1s ease-in-out;
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.author-content-item.single {
  width: 100%;
}

.author-content-item.map .map-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  color: var(--font-color);
  background: var(--anzhiyu-maskbg);
  padding: 0.5rem 2rem;
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transform: translateZ(0);
  transition: all 1s, color 0s ease-in-out;
  font-size: 20px;
}

.author-content-item.map .map-title b {
  color: var(--font-color);
}

.author-content-item.selfInfo {
  display: flex;
  min-height: 100px;
  max-height: 400px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  height: -webkit-fill-available;
  height: 40%;
}

.author-content-item.selfInfo div {
  display: flex;
  flex-direction: column;
  margin: 0.5rem 2rem 0.5rem 0;
  min-width: 120px;
}

.author-content-item.selfInfo .selfInfo-title {
  opacity: 0.8;
  font-size: 12px;
  line-height: 1;
  margin-bottom: 8px;
}

.author-content-item.selfInfo .selfInfo-content {
  font-weight: 700;
  font-size: 34px;
  line-height: 1;
}

.author-content-item-group.column.mapAndInfo {
  width: 59%;
}

.author-content-item.map:hover {
  background-size: 120%;
  transition: 4s ease-in-out;
  background-position-x: 0;
  background-position-y: 36%;
}

.author-content-item.map:hover .map-title {
  bottom: -100%;
}

.author-content-item-group.column {
  display: flex;
  flex-direction: column;
  width: 49%;
  justify-content: space-between;
}

.post-tips a:hover {
  color: var(--anzhiyu-main) !important;
  background: none !important;
}

.author-content-item.single.reward .reward-list-updateDate {
  color: var(--anzhiyu-gray);
  font-size: 14px;
}

.author-content-item.maxim {
  font-size: 36px;
  font-weight: 700;
  line-height: 1.1;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
  width: 39%;
}

.author-content-item .author-content-item-tips {
  opacity: 0.8;
  font-size: 12px;
  margin-bottom: 0.5rem;
}

.author-content-item.maxim .maxim-title {
  display: flex;
  flex-direction: column;
}

.author-content-item.buff {
  height: 200px;
  font-size: 36px;
  font-weight: 700;
  line-height: 1.1;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
  background: linear-gradient(120deg, #ff27e8 0, #ff8000 100%);
  color: var(--anzhiyu-white);
  background-size: 200%;
  animation: gradient 15s ease infinite;
  min-height: 200px;
  height: fit-content;
  width: 59%;
}

.author-content-item.buff .card-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.author-content-item.buff .buff-title {
  display: flex;
  flex-direction: column;
}

.card-background-icon {
  font-size: 12rem;
  opacity: 0.2;
  position: absolute;
  right: 0;
  bottom: -40%;
  transform: rotate(30deg);
  transition: 2s ease-in-out;
}

.card-background-icon i {
  font-size: 12rem;
}

.author-content-item.game-yuanshen {
  background-size: cover;
  min-height: 300px;
  overflow: hidden;
  color: var(--anzhiyu-white);
  width: 59%;
}

.author-content-item .content-bottom {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.author-content-item .content-bottom .icon-group {
  display: flex;
  position: relative;
}

.author-content-item .content-bottom .icon-group i {
  display: inline-block;
  width: 143px;
  height: 18px;
  margin-right: 0.5rem;
}

.author-content-item.game-yuanshen::after {
  box-shadow: 0 -69px 203px 11px #575d8b inset;
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.author-content-item.comic-content {
  width: 39%;
  min-height: 300px;
  overflow: hidden;
}

.author-content-item.comic-content .comic-box {
  display: flex;
  width: 120%;
  height: 100%;
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.author-content-item.comic-content .author-content-item-tips, .author-content-item.comic-content .author-content-item-title {
  z-index: 2;
  color: var(--anzhiyu-white);
  pointer-events: none;
}

.author-content-item.comic-content .comic-item {
  height: 100%;
  color: white;
  width: 20%;
  transform: skew(-10deg, 0deg);
  transition: 0.8s;
  position: relative;
  overflow: hidden;
}

.author-content-item.comic-content .comic-item:hover {
  width: 46%;
}

.author-content-item.comic-content .comic-item:hover .comic-item-cover {
  left: 16%;
  transform: skew(10deg, 0deg) scale(1.6);
}

.author-content-item.comic-content .comic-item .comic-item-cover {
  position: absolute;
  top: 0;
  left: -50%;
  height: 100%;
  transform: skew(10deg, 0deg);
  object-fit: cover;
  transition: scale 0.2s, all 0.8s;
}

.author-content-item.comic-content .comic-item .comic-item-cover img {
  height: 100%;
  transition: 0.8s;
  max-width: none;
  border-radius: 0px;
}

.author-content-item.comic-content::after {
  box-shadow: 0 -48px 203px 11px #fbe9b8 inset;
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.author-content-item.like-technology {
  min-height: 230px;
  color: var(--anzhiyu-white);
}

.author-content-item.like-music {
  min-height: 400px;
  color: var(--anzhiyu-white);
  overflow: hidden;
}

.author-content-item.like-music::after {
  box-shadow: 0 -69px 203px 11px #453e38 inset;
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

@media screen and (max-width: 1200px) {
  .author-content-item.personalities .image {
    width: 180px;
  }
}

@media screen and (max-width: 768px) {
  #gitcalendar {
    display: none;
  }

  [data-theme='dark'] .author-content-item .card-content .banner-button-group .banner-button {
    color: var(--anzhiyu-black) !important;
  }

  .author-content-item .card-content .banner-button-group .banner-button:hover {
    background: none !important;
  }

  .author-content-item.game-yuanshen .content-bottom {
    padding-bottom: 10px;
  }

  .author-content-item.game-yuanshen .game-yuanshen-uid {
    display: none;
  }

  .author-content {
    margin-top: 0;
  }

  .author-content-item {
    width: 100% !important;
    margin-top: 1rem;
    padding: 1rem;
  }

  .author-content-item.map {
    margin-bottom: 0;
  }

  .author-content-item-group.column {
    width: 100% !important;
  }

  .author-content-item.selfInfo {
    height: 95%;
  }

  .author-content-item.personalities {
    height: 200px;
  }

  .post-tips {
    left: auto;
  }

  .author-content-item.personalities .image {
    width: 125px;
  }

  .site-card-group > a {
    width: 100% !important;
  }

  body[data-type='about'] .post-reward {
    display: none;
  }

  .reward-list-item {
    width: 100% !important;
  }

  .author-content-item .card-content .banner-button-group .banner-button-text {
    display: none;
  }

  .author-content-item .card-content .banner-button-group {
    right: 1rem;
    bottom: 1rem;
  }

  .author-content-item .card-content .banner-button-group .banner-button {
    background: none;
    padding: 0;
    width: auto;
    backdrop-filter: unset;
  }

  .author-content-item .card-content .banner-button-group .banner-button i, .author-content-item .card-content .banner-button-group .banner-button svg {
    margin-right: 0.25rem;
    font-size: 1.5rem;
    background: white;
    border-radius: 50%;
    padding: 6px;
    margin-left: 80px;
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--anzhiyu-fontcolor);
  }

  .author-content-item .card-content .banner-button-group .banner-button:hover i {
    background: var(--anzhiyu-background) !important;
    color: var(--anzhiyu-theme);
  }

  #selfInfo-content-year {
    width: 90px;
  }

  /* 移动端技能列表优化 */
  .skills .skills-list {
    max-height: 250px;
    padding-right: 4px;
  }

  .skills .skills-list::-webkit-scrollbar {
    width: 4px;
  }

  /* 移动端分类样式优化 */
  .skills .skill-category {
    margin-bottom: 1rem;
  }

  .skills .skill-category-title {
    font-size: 14px;
    padding: 6px 12px;
    margin-bottom: 8px;
  }

  .skills .skill-category-content {
    gap: 6px;
  }
}

@media screen and (min-width: 768px) and (max-width: 896px) {
  .author-content-item.like-music .content-bottom .tips {
    display: none;
  }
}

/* 赞赏的css */
.reward-list-all {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.reward-list-item {
  padding: 1rem;
  border-radius: 12px;
  border: var(--style-border-always);
  width: calc((100% / 5) - 0.5rem);
  margin: 0 0.25rem 0.5rem 0.25rem;
  box-shadow: var(--anzhiyu-shadow-border);

  +maxWidth1200() {
    width: calc((100% / 3) - 0.5rem);
  }
}

.reward-list-item .reward-list-item-name {
  font-size: 1rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.reward-list-item .reward-list-bottom-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reward-list-item .reward-list-item-money {
  padding: 4px;
  background: var(--font-color);
  color: var(--card-bg);
  font-size: 12px;
  line-height: 1;
  border-radius: 4px;
  margin-right: 4px;
  white-space: nowrap;
}

.reward-list-item .reward-list-item-time {
  font-size: 12px;
  color: var(--anzhiyu-secondtext);
  white-space: nowrap;
}

/* 技能 */
.skills {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  width: 50%;
  min-height: 450px;
}

.skills .skill-icon {
  width: 32px;
  height: 32px;
  border-radius: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.skills .skills-list {
  display: flex;
  opacity: 0;
  transition: 0.3s;
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  flex-wrap: wrap;
  flex-direction: row;
  margin-top: 10px;
  max-height: 350px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
}

/* 自定义滚动条样式 */
.skills .skills-list::-webkit-scrollbar {
  width: 6px;
}

.skills .skills-list::-webkit-scrollbar-track {
  background: var(--anzhiyu-secondbg);
  border-radius: 3px;
}

.skills .skills-list::-webkit-scrollbar-thumb {
  background: var(--anzhiyu-main);
  border-radius: 3px;
  transition: background 0.3s;
}

.skills .skills-list::-webkit-scrollbar-thumb:hover {
  background: var(--anzhiyu-main);
  opacity: 0.8;
}

/* 技能分类样式 */
.skills .skill-category {
  width: 100%;
  margin-bottom: 1.5rem;
}

.skills .skill-category:last-child {
  margin-bottom: 0;
}

.skills .skill-category-title {
  font-size: 16px;
  font-weight: 700;
  color: var(--anzhiyu-white);
  background: linear-gradient(135deg, var(--anzhiyu-main) 0%, var(--anzhiyu-theme) 100%);
  padding: 8px 16px;
  border-radius: 20px;
  margin-bottom: 12px;
  display: inline-block;
  box-shadow: var(--anzhiyu-shadow-border);
  position: relative;
  z-index: 1;
}

.skills .skill-category-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--anzhiyu-main) 0%, var(--anzhiyu-theme) 100%);
  border-radius: 20px;
  opacity: 0.8;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.skills .skill-category:hover .skill-category-title::before {
  opacity: 1;
}

.skills .skill-category-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skills .skill-info {
  display: flex;
  align-items: center;
  background: var(--anzhiyu-background);
  border-radius: 40px;
  padding: 4px 12px 4px 8px;
  border: var(--style-border);
  box-shadow: var(--anzhiyu-shadow-border);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.skills .skill-info:hover {
  transform: translateY(-2px);
  box-shadow: var(--anzhiyu-shadow-main);
}

.skills .skill-icon img {
  width: 18px;
  height: 18px;
  margin: 0 auto !important;
}

.skills .skills-style-group {
  position: relative;
}

.author-content-item.skills .skills-style-group {
  position: relative;
}

.skills:hover .skills-style-group #skills-tags-group-all {
  opacity: 0;
}

.skills:hover .skills-style-group .skills-list {
  opacity: 1;
}

.author-content-item.careers {
  min-height: 400px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position-x: right;
  background-position-y: bottom;
}

.author-content-item.careers .careers-group {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 12px;
  margin-bottom: 12px;
}

.author-content-item.careers .career-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.author-content-item.careers .career-item .circle {
  width: 16px;
  height: 16px;
  margin-top: 3px;
  margin-right: 8px;
  border-radius: 50%;
}

.author-content-item.careers .career-item .text {
  color: var(--anzhiyu-secondtext);
}

.author-content-item.careers .careers-item {
  display: flex;
  align-items: center;
}

.author-content-item.careers .careers-item .circle {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border-radius: 16px;
}

.author-content-item.careers .careers-item .name {
  color: var(--anzhiyu-secondtext);
}

.author-content-item.careers img {
  position: absolute;
  left: 0;
  bottom: 20px;
  width: 100%;
  transition: 0.6s;
  z-index: -1;
}

:root {
  --loadingbar-background-color: #2c2b30;
  --loadingbar-prospect-color: #ece5d8;
}

.loading-bar {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 500px;
  height: 62.5px;
  transform: translate(-25%, -50%) scale(0.5);
  transition: all 0.5s;
  user-select: none;
  overflow: hidden;
}

.loading-bar::after {
  content: '';
  position: absolute;
  top: 500px;
  left: 0;
  filter: drop-shadow(0 -500px 0 var(--loadingbar-prospect-color));
  width: 500px;
  height: 62.5px;
  background: url('https://yuanshen.site/imgs/loading-bar.png') no-repeat left 100%;
  background-size: 500px 62.5px;
  background-position-x: 0;
}

.author-content-item.game-yuanshen:hover .loading-bar::after {
  animation: loading-bar 3.5s cubic-bezier(0.28, 0.11, 0.32, 1) infinite forwards;
}

@media screen and (max-width: 719px) {
  .loading-bar .loading-bar {
    display: none;
  }
}

@media screen and (max-width: 719px) and (orientation: landscape) {
  .loading-bar .loading-bar {
    display: block !important;
    transform: translate(-50%, -50%) scale(0.7) !important;
  }
}

@supports not (filter: drop-shadow(0 0 0 #fff)) {
  .loading-bar:before {
    content: 'Your browser does not support the animation';
  }
}

@keyframes loading-bar {
  0% {
    width: 0;
    background-size: 500px 62.5px;
  }

  16.6% {
  }

  33.2% {
  }

  49.8% {
  }

  66.4% {
  }

  83% {
    width: 475px;
  }

  83.1% {
    width: 475px;
  }

  83.2% {
    width: 475px;
  }

  83.3% {
    width: 475px;
  }

  83.4% {
    width: 475px;
  }

  83.5% {
    width: 475px;
  }

  83.6% {
    width: 475px;
  }

  83.7% {
    width: 475px;
  }

  83.8% {
    width: 475px;
  }

  83.9% {
    width: 475px;
  }

  84% {
    width: 475px;
  }

  85% {
    width: 475px;
  }

  86% {
    width: 475px;
  }

  87% {
    width: 475px;
  }

  100% {
    width: 500px;
  }
}

.hello-about {
  margin: 20px auto;
  border-radius: 24px;
  background: var(--anzhiyu-card-bg);
  border: var(--style-border-always);
  box-shadow: var(--anzhiyu-shadow-border);
  position: relative;
  overflow: hidden;
  user-select: none;
}

.shapes {
  position: relative;
  height: 315px;
  width: 100%;
  background: #2128bd;
  overflow: hidden;
}

.shape {
  will-change: transform;
  position: absolute;
  border-radius: 50%;
}

.shape.shape-1 {
  background: #005ffe;
  width: 650px;
  height: 650px;
  margin: -325px 0 0 -325px;
}

.shape.shape-2 {
  background: #ffe5e3;
  width: 440px;
  height: 440px;
  margin: -220px 0 0 -220px;
}

.shape.shape-3 {
  background: #ffcc57;
  width: 270px;
  height: 270px;
  margin: -135px 0 0 -135px;
}

.hello-about .content {
  top: 0;
  left: 0;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 315px;
  width: 100%;
  background: #fff;
  mix-blend-mode: screen;
}

[data-theme='dark'] .hello-about .content {
  background: transparent;
}

[data-theme='dark'] .hello-about h1 {
  color: var(--anzhiyu-white);
}

.hello-about h1 {
  font-size: 200px;
  color: #000;
  margin: 0;
  text-align: center;
  font: inherit;
  vertical-align: baseline;
  line-height: 1;
  font-size: calc(0.0989119683 * 100vw + 58.5558852621px);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 419px) {
  .hello-about h1 {
    font-size: calc(0.0989119683 * 100vw + 58.5558852621px);
  }
}

@media (max-width: 768px) {
  .hello-about {
    margin: 20px 0 auto;
  }
}

.cursor {
  position: absolute;
  background: #2128bd;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border-radius: 50%;
  will-change: transform;
  user-select: none;
  pointer-events: none;
  z-index: 3;
}

if (hexo-config('LA.enable')) {
  body: 1;
} else {
  #about-page {
    .author-content-item-group.column.mapAndInfo {
      width: 100%;
    }

    .author-content-item-group.column {
      flex-direction: row;
    }

    .author-content-item.map {
      width: 50%;
    }

    .author-content-item.selfInfo {
      height: 100%;
      width: 49%;
    }
  }
}
