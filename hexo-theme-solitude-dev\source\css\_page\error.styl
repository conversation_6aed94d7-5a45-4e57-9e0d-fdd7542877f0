#body-wrap.error
  display flex
  flex-direction column
  justify-content center
  align-items center
  padding 50px 1rem 1rem
  position relative

  +maxWidth768()
    padding-top 0

  #error-wrap
    display flex
    justify-content center
    margin 0 1rem
    width 100%
    position relative

    .error-content
      display flex
      flex-direction row
      justify-content center
      align-items center
      margin 0 1rem
      height 18rem
      max-width 800px
      background var(--efu-card-bg)
      transition all .3s ease 0s
      border var(--style-border-always)
      position relative
      width 100%
      box-shadow none
      border-radius 12px

      +maxWidth768()
        flex-direction column
        margin 0
        height 25rem
        width 100%

      .error-img
        flex 1 1 0
        height 100%
        width 600px
        border-radius 8px 0 0 8px
        background-color var(--efu-main)
        background-position center
        background-size cover

        +maxWidth768()
          flex 1 1 0
          width 100%
          border-radius 12px

      .error-info
        flex 1 1 0
        padding .5rem
        text-align center
        font-size 14px
        font-family $font-family

        +maxWidth768()
          width 100%
          padding-bottom 2rem

        .error_title
          font-size 9em
          line-height 1

          +maxWidth768()
            font-size 4rem

        .error_subtitle
          word-break break-word
          font-size 1.6em
          -webkit-line-clamp 2

        a
          display inline-block
          margin-top .5rem
          padding .3rem 1.5rem
          background var(--btn-bg)
          color var(--btn-color)

          &:hover
            background var(--efu-theme)

          i
            font-size 16px
            padding-right .3rem

  .aside-list
    display flex
    flex-direction row
    flex-wrap nowrap
    margin 1rem
    max-width 100%

    +maxWidth768()
      padding-top 0
      margin 0

    .aside-list-group
      display flex
      flex-direction row
      flex-wrap wrap
      max-width 800px
      margin 0 auto
      justify-content space-between

    .aside-list-item
      padding .5rem 0
      width 49%

      img
        width 100%
        object-fit cover
        border-radius 12px

      .thumbnail
        overflow hidden
        width 100%
        height 200px
        background var(--efu-card-bg)
        display flex

        +maxWidth768()
          height 100px

      .content .title
        -webkit-line-clamp 2
        overflow hidden
        display -webkit-box
        -webkit-box-orient vertical
        line-height 1.5
        justify-content center
        align-items flex-end
        align-content center
        padding-top .5rem
        font-size 16px
        font-weight 700

      .content time
        display none
