.card-widget.card-recent-post
    .item-headline
        i.solitude.fas.fa-map
        span= _p('aside.newpost')
    .aside-list
        each post in site.posts.data.sort((a, b) => b.date < a.date ? -1 : 1).slice(0, 5)
            a.aside-list-item(href=url_for(post.path) title=post.title)
                if post.cover
                    .thumbnail
                        img(alt=post.title src=url_for(post.cover))
                .content
                    span.title(href=url_for(post.path) title=post.title)= post.title
                    if post.categories.data[0]
                        span.categories(href=url_for(post.path))= post.categories.data[0].name