<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Java（五）：5.0 [元编程] 反射、注解 | Prorise的小站</title><meta name="keywords" content="Java基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Java（五）：5.0 [元编程] 反射、注解"><meta name="application-name" content="Java（五）：5.0 [元编程] 反射、注解"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="Java（五）：5.0 [元编程] 反射、注解"><meta property="og:url" content="https://prorise666.site/posts/62133.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="5.0 [元编程] 反射、注解本章将带您深入Java的“元编程”世界。元编程是指程序在运行时能够审视并操作自身结构的能力。我们将从这一切的基石——类加载机制讲起，然后深入学习实现元编程的核心技术——反射，并最终探讨其最广泛的应用——注解与Junit单元测试。 5.1 [基础] 类加载机制与 Clas"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"><meta name="description" content="5.0 [元编程] 反射、注解本章将带您深入Java的“元编程”世界。元编程是指程序在运行时能够审视并操作自身结构的能力。我们将从这一切的基石——类加载机制讲起，然后深入学习实现元编程的核心技术——反射，并最终探讨其最广泛的应用——注解与Junit单元测试。 5.1 [基础] 类加载机制与 Clas"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/62133.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"Java（五）：5.0 [元编程] 反射、注解",postAI:"true",pageFillDescription:"5.0 [元编程] 反射、注解, 5.1 [基础] 类加载机制与 ClassLoader, 面试题引入, 类的生命周期, 类加载器 (ClassLoaders) 体系, [核心] 双亲委派模型 (Parent-Delegation Model), 代码示例：获取类加载器并查看其层级, 5.2 [核心] 反射：运行时动态操控的艺术, 5.2.1 什么是反射及其应用场景, 面试题引入, 核心概念, 优缺点, 应用场景, 5.2.2 反射的基石：java.lang.Class 对象, 获取Class对象的三种主要方式, 代码示例：获取Class对象, 5.2.3 通过反射操作类的成员, 1. 操作构造器 (Constructor), 代码示例：调用不同的构造器, 2. 操作方法 (Method), 代码示例：调用各种方法, 3. 操作字段 (Field), 代码示例：读写字段值, 5.2.4 [实战] 反射的应用：迷你Spring框架, 5.3 [应用] 注解：为代码嵌入元数据, 5.3.1 注解的核心思想, 面试题引入, 注解 vs. 注释, 注解的重要性：现代框架的基石, 5.3.2 Java 内置注解, 5.3.3 自定义注解, 定义注解与属性, 代码示例：定义一个复杂的数据库信息注解, 使用注解与属性赋值, 5.3.4 元注解：注解的配置, 5.3.5 反射解析注解：读取元数据的API, 面试题引入, 核心接口：AnnotatedElement, 核心方法速查表, 代码示例：系统性地解析一个类上的所有注解, 5.3.6 [终极实战] 结合反射构建迷你ORM框架, 步骤一：定义自定义注解 (@Table 和 @Column), 步骤二：创建被注解的实体类 (POJO), 步骤三：编写注解处理器（核心逻辑）元编程反射注解本章将带您深入的元编程世界元编程是指程序在运行时能够审视并操作自身结构的能力我们将从这一切的基石类加载机制讲起然后深入学习实现元编程的核心技术反射并最终探讨其最广泛的应用注解与单元测试基础类加载机制与面试题引入请简述一下的类加载过程以及双亲委派模型类的生命周期一个源文件变成可以在中运行的程序其对应的文件需要经历一个完整的生命周期这个过程主要分为加载链接和初始化三个阶段加载通过类加载器找到对应的文件读取其二进制数据并在方法区中创建一个对象链接验证确保被加载的类文件符合规范没有安全问题准备为类的静态变量分配内存并设置其类型的默认值如为为注意此时并非执行程序员指定的初始值解析将类中的符号引用如类名方法名替换为直接的内存地址引用初始化这是类加载的最后一步会执行类的初始化方法这个方法由编译器自动收集类中所有静态变量的赋值动作和静态代码块中的语句合并而成只有到这一步静态变量才会被赋予我们代码中指定的初始值类加载器体系通过一个层级分明的类加载器体系来完成类的加载工作主要有三类加载器启动类加载器的顶层加载器由实现是自身的一部分负责加载最核心的库如里的等在代码中尝试获取它的引用会返回扩展类加载器负责加载的扩展库位于目录下它的父加载器是启动类加载器应用程序类加载器也称为系统类加载器是我们最常打交道的加载器负责加载用户类路径上我们自己编写的类和第三方库的包它的父加载器是扩展类加载器核心双亲委派模型这是类加载器设计的核心原则也是面试中的绝对高频考点工作流程当一个类加载器收到加载类的请求时它不会自己先去尝试加载而是会首先把这个请求委派给它的父加载器去完成每一层的加载器都是如此只有当父加载器在自己的搜索范围内找不到指定的类无法完成加载请求时子加载器才会自己去尝试加载为何如此设计避免类的重复加载通过委派机制一个类最终只会被一个加载器加载一次确保了该类在中的唯一性保证核心库的安全这是最重要的目的它防止了的核心被恶意或无意地篡改例如你无法自己编写一个类来替代系统的类因为当加载请求传递到最顶层的启动类加载器时它会找到并加载自带的真正的类加载过程至此结束你编写的假类将永远没有机会被加载代码示例获取类加载器并查看其层级获取当前自定义类的加载器应用程序类加载器获取其父加载器扩展类加载器扩展类加载器获取扩展类加载器的父加载器启动类加载器因为启动类加载器是实现的无法在中获取其实体所以返回启动类加载器核心类的加载器尝试获取类的加载器类由启动类加载器加载因此在层面获取不到返回的加载器输出结果应用程序类加载器扩展类加载器启动类加载器核心类的加载器的加载器这个输出完美地验证了类加载器的层级关系和启动类加载器的特殊性核心反射运行时动态操控的艺术在了解了代码如何被加载到中之后我们现在来学习一个中非常强大也是所有主流框架如基石的特性反射什么是反射及其应用场景面试题引入什么是反射它有哪些优缺点和应用场景核心概念反射是语言提供的一种在运行时动态地间接地检查分析和操作自身结构与行为的能力我们可以用一个比喻来理解常规编程就像我们拿到一本说明书类的代码我们严格按照说明书上的指示方法调用来操作一个设备对象我们在写代码的时候就知道这个设备有什么按钮每个按钮叫什么反射编程就像我们没有说明书只有一个密封的黑盒设备但是我们拿到了一套万能检测和操控工具即反射通过这套工具我们可以在程序运行时去探测这个黑盒它有哪些按钮方法有哪些内部零件字段它的型号是什么类名甚至我们可以强行按下那些没有在外部暴露的内部按钮调用私有方法优缺点优点动态性与灵活性这是反射最大的优点它允许我们编写非常通用的代码可以操作在编译时完全未知的类所有主流框架的依赖注入等核心功能都深度依赖反射缺点性能开销反射操作如方法查找比直接代码调用要慢得多因为它涉及更多的查找和检查步骤并且绕过了编译器的许多优化因此在性能敏感的核心路径上应避免使用破坏封装通过可以访问和修改类的私有成员这违背了面向对象的封装原则类型不安全编译器无法对反射代码进行类型检查可能将潜在的等错误从编译期推迟到运行时应用场景框架开发的容器通过反射动态创建和注入动态代理在运行时为一个或多个接口动态地生成实现类注解处理在运行时读取注解信息并执行相应逻辑单元测试等测试框架通过反射查找并执行被注解的方法反射的基石对象要对一个类进行反射操作第一步永远是获取代表这个类的对象它是反射所有操作的入口获取对象的三种主要方式通过类名获取最简单最安全的方式在编译时就会受到检查通过对象实例获取当你已经拥有一个该类的对象时使用通过类的全限定名获取最动态的方式可以在运行时根据一个字符串来加载任意类常用于框架加载配置文件中指定的类代码示例获取对象方式一语法方式二方式三三种方式获取到的都是同一个对象实例通过反射操作类的成员获取到对象后我们就可以像操作说明书一样获取并操作它的所有部分操作构造器核心带有字样的方法可以获取到所有包括的成员不带的只能获取成员此规则对方法和字段同样适用代码示例调用不同的构造器默认学生私有构造器调用公共无参构造器调用无参构造器调用公共有参构造器调用有参构造器调用私有构造器暴力反射必须设置可访问否则会抛出调用私有构造器操作方法核心返回类中所有公共方法的数组包括继承的方法返回类中指定的公共方法参数为方法名和参数类型类对象数组返回类中声明的所有方法的数组包括私有保护和默认访问权限的方法但不包括继承的方法返回类中声明的指定方法参数为方法名和参数类型类对象数组用于调用对象的指定方法参数为对象实例方法对象和方法的参数数组代码示例调用各种方法私有方法被调用注意我们默认没有指定构造器的修饰符那么他就是的必须通过来获取调用公共方法调用的结果调用私有方法调用私有方法时必须授权调用静态方法调用静态方法时第一个参数对象实例传入即可操作字段核心返回类中所有公共字段的数组包括继承的字段返回类中指定的公共字段参数为字段名返回类中声明的所有字段的数组包括私有保护和默认访问权限的字段但不包括继承的字段返回类中声明的指定字段参数为字段名用于获取对象指定字段的值参数为对象实例和字段对象用于设置对象指定字段的值参数为对象实例字段对象和要设置的值代码示例读写字段值操作公共字段张三获取公共字段操作私有字段获取私有字段实战反射的应用迷你框架场景编写一个简单的框架它可以根据一个配置文件动态地创建并执行指定的对象和方法创建文件放在或目录下创建业务类这是一个被框架调用的业务类用户服务正在执行登录逻辑用户服务正在执行注册逻辑编写框架主类这是一个被框架调用的业务类用户服务正在执行登录逻辑用户服务正在执行注册逻辑从配置中获取类名和方法名使用反射动态执行加载类创建对象获取方法调用方法这个简单的例子就揭示了等现代框架实现其强大动态能力的核心原理应用注解为代码嵌入元数据注解是中一种强大的元编程工具它允许我们在不改变代码本身逻辑的前提下为类方法字段等程序元素添加标签或元数据这些元数据可以被编译器或运行时环境读取从而实现各种自动化配置化和框架化的功能注解的核心思想面试题引入注解是什么它和注释有什么本质区别注解注释注释是写给程序员看的用于解释代码提高可读性编译器会完全忽略注释注解是写给程序编译器框架工具看的它是一种元数据程序可以根据这个元数据来决定不同的处理方式注解的重要性现代框架的基石理解现代框架的实现原理有一个公认的公式框架反射注解设计模式的依赖注入的映射的单元测试其核心都是通过反射来查找并处理开发者定义的注解从而实现自动化配置和功能的内置注解预置了一些非常重要的注解用于辅助编译器进行检查标记一个方法意图重写父类的方法这是给编译器的承诺书如果该方法并未正确重写如方法名拼写错误编译器将报错标记一个元素类方法字段已过时不推荐使用调用被此注解标记的元素时编译器会发出警告压制编译器警告在明确知道警告无害的情况下使用可以使代码更整洁例如标记一个接口为函数式接口即该接口有且仅有一个抽象方法这是编译器层面的约束确保该接口可以被表达式所使用自定义注解我们可以使用关键字来定义自己的注解定义注解与属性注解的属性定义形式为类型属性名可以为属性提供默认值属性支持的类型所有基本数据类型等注解类型以上所有类型的一维数组形式代码示例定义一个复杂的数据库信息注解一个用于模拟数据库信息的注解带有默认值的属性使用注解与属性赋值基本语法注解名属性名值属性名值属性简写如果一个注解只有一个名为的属性在使用时可以省略数组属性简写如果数组属性只有一个元素可以省略花括号使用自定义注解并为属性赋值数组属性只有一个元素省略了花括号元注解注解的配置元注解是用于注解的注解它们定义了我们自定义的注解将如何工作决定注解能用在哪里类方法字段等决定注解的生命周期是关键它让注解在运行时能被反射读取注解只在源代码级别保留编译后不会包含在字节码文件中注解在源代码级别和编译后的字节码文件中保留但在运行时不会保留注解在源代码级别编译后的字节码文件中保留并且在运行时也可以通过反射读取允许子类继承父类上的注解让注解信息能被工具提取到文档中允许同一个注解在同一个位置上重复使用例如我们熟知的注解用在方法上生命周期为源代码级别反射解析注解读取元数据的面试题引入如何通过反射在运行时获取到一个类方法或字段上的注解信息核心接口的反射体系中等所有可以被注解的程序元素都实现了接口这个接口是所有注解解析操作的入口它提供了统一的用于读取注解的核心方法核心方法速查表方法签名功能描述判断当前元素上是否存在指定类型的注解获取当前元素上指定类型的注解对象如果不存在则返回获取当前元素上所有的注解对象数组获取直接在当前元素上声明的注解不包括从父类继承的代码示例系统性地解析一个类上的所有注解场景我们定义一个类在其类字段方法上都使用自定义注解然后编写一个解析器来读取所有这些元数据步骤一定义几个用于演示的注解步骤二在一个类中使用这些注解用户资料实体类步骤三编写反射解析器获取的对象解析类上的注解类文档注解解析字段上的注解遍历所有已声明的字段字段需要从注入值解析方法上的注解遍历所有已声明的方法方法需要被日志记录输出结果解析类上的注解类文档注解用户资料实体类解析字段上的注解字段需要从注入值字段需要从注入值解析方法上的注解方法需要被日志记录终极实战结合反射构建迷你框架这是注解与反射最经典的结合应用它模拟了等框架的核心原理目标编写一个程序能够扫描指定包下的所有类并为那些被和注解标记的类自动生成的语句步骤一定义自定义注解和注解用于类上指定表名用属性来接收表名注解用于字段上指定列名和类型默认类型为步骤二创建被注解的实体类指定该类对应的表名为字段没有注解将被忽略步骤三编写注解处理器核心逻辑获取对象检查类上是否有注解如果没有则不是实体类直接返回获取注解并读取表名遍历所有字段查找注解获取注解并读取列名和类型移除最后一个多余的逗号和换行符自动生成的表输出结果自动生成的表这个综合案例完美展示了如何通过注解定义元数据反射读取元数据的模式来构建强大灵活的自动化框架",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-14 17:01:36",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#5-0-%E5%85%83%E7%BC%96%E7%A8%8B-%E5%8F%8D%E5%B0%84%E3%80%81%E6%B3%A8%E8%A7%A3"><span class="toc-text">5.0 [元编程] 反射、注解</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-%E5%9F%BA%E7%A1%80-%E7%B1%BB%E5%8A%A0%E8%BD%BD%E6%9C%BA%E5%88%B6%E4%B8%8E-ClassLoader"><span class="toc-text">5.1 [基础] 类加载机制与 ClassLoader</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5"><span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E7%9A%84%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F"><span class="toc-text">类的生命周期</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E5%8A%A0%E8%BD%BD%E5%99%A8-ClassLoaders-%E4%BD%93%E7%B3%BB"><span class="toc-text">类加载器 (ClassLoaders) 体系</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83-%E5%8F%8C%E4%BA%B2%E5%A7%94%E6%B4%BE%E6%A8%A1%E5%9E%8B-Parent-Delegation-Model"><span class="toc-text">[核心] 双亲委派模型 (Parent-Delegation Model)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%8E%B7%E5%8F%96%E7%B1%BB%E5%8A%A0%E8%BD%BD%E5%99%A8%E5%B9%B6%E6%9F%A5%E7%9C%8B%E5%85%B6%E5%B1%82%E7%BA%A7"><span class="toc-text">代码示例：获取类加载器并查看其层级</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-2-%E6%A0%B8%E5%BF%83-%E5%8F%8D%E5%B0%84%EF%BC%9A%E8%BF%90%E8%A1%8C%E6%97%B6%E5%8A%A8%E6%80%81%E6%93%8D%E6%8E%A7%E7%9A%84%E8%89%BA%E6%9C%AF"><span class="toc-text">5.2 [核心] 反射：运行时动态操控的艺术</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#5-2-1-%E4%BB%80%E4%B9%88%E6%98%AF%E5%8F%8D%E5%B0%84%E5%8F%8A%E5%85%B6%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">5.2.1 什么是反射及其应用场景</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-1"><span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5"><span class="toc-text">核心概念</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BC%98%E7%BC%BA%E7%82%B9"><span class="toc-text">优缺点</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">应用场景</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-2-2-%E5%8F%8D%E5%B0%84%E7%9A%84%E5%9F%BA%E7%9F%B3%EF%BC%9Ajava-lang-Class-%E5%AF%B9%E8%B1%A1"><span class="toc-text">5.2.2 反射的基石：java.lang.Class 对象</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%8E%B7%E5%8F%96Class%E5%AF%B9%E8%B1%A1%E7%9A%84%E4%B8%89%E7%A7%8D%E4%B8%BB%E8%A6%81%E6%96%B9%E5%BC%8F"><span class="toc-text">获取Class对象的三种主要方式</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%8E%B7%E5%8F%96Class%E5%AF%B9%E8%B1%A1"><span class="toc-text">代码示例：获取Class对象</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-2-3-%E9%80%9A%E8%BF%87%E5%8F%8D%E5%B0%84%E6%93%8D%E4%BD%9C%E7%B1%BB%E7%9A%84%E6%88%90%E5%91%98"><span class="toc-text">5.2.3 通过反射操作类的成员</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E6%93%8D%E4%BD%9C%E6%9E%84%E9%80%A0%E5%99%A8-Constructor"><span class="toc-text">1. 操作构造器 (Constructor)</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%B0%83%E7%94%A8%E4%B8%8D%E5%90%8C%E7%9A%84%E6%9E%84%E9%80%A0%E5%99%A8"><span class="toc-text">代码示例：调用不同的构造器</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E6%93%8D%E4%BD%9C%E6%96%B9%E6%B3%95-Method"><span class="toc-text">2. 操作方法 (Method)</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%B0%83%E7%94%A8%E5%90%84%E7%A7%8D%E6%96%B9%E6%B3%95"><span class="toc-text">代码示例：调用各种方法</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E6%93%8D%E4%BD%9C%E5%AD%97%E6%AE%B5-Field"><span class="toc-text">3. 操作字段 (Field)</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%AF%BB%E5%86%99%E5%AD%97%E6%AE%B5%E5%80%BC"><span class="toc-text">代码示例：读写字段值</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-2-4-%E5%AE%9E%E6%88%98-%E5%8F%8D%E5%B0%84%E7%9A%84%E5%BA%94%E7%94%A8%EF%BC%9A%E8%BF%B7%E4%BD%A0Spring%E6%A1%86%E6%9E%B6"><span class="toc-text">5.2.4 [实战] 反射的应用：迷你Spring框架</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-%E5%BA%94%E7%94%A8-%E6%B3%A8%E8%A7%A3%EF%BC%9A%E4%B8%BA%E4%BB%A3%E7%A0%81%E5%B5%8C%E5%85%A5%E5%85%83%E6%95%B0%E6%8D%AE"><span class="toc-text">5.3 [应用] 注解：为代码嵌入元数据</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-1-%E6%B3%A8%E8%A7%A3%E7%9A%84%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3"><span class="toc-text">5.3.1 注解的核心思想</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-2"><span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B3%A8%E8%A7%A3-vs-%E6%B3%A8%E9%87%8A"><span class="toc-text">注解 vs. 注释</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B3%A8%E8%A7%A3%E7%9A%84%E9%87%8D%E8%A6%81%E6%80%A7%EF%BC%9A%E7%8E%B0%E4%BB%A3%E6%A1%86%E6%9E%B6%E7%9A%84%E5%9F%BA%E7%9F%B3"><span class="toc-text">注解的重要性：现代框架的基石</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-2-Java-%E5%86%85%E7%BD%AE%E6%B3%A8%E8%A7%A3"><span class="toc-text">5.3.2 Java 内置注解</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-3-%E8%87%AA%E5%AE%9A%E4%B9%89%E6%B3%A8%E8%A7%A3"><span class="toc-text">5.3.3 自定义注解</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%AE%9A%E4%B9%89%E6%B3%A8%E8%A7%A3%E4%B8%8E%E5%B1%9E%E6%80%A7"><span class="toc-text">定义注解与属性</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E5%AE%9A%E4%B9%89%E4%B8%80%E4%B8%AA%E5%A4%8D%E6%9D%82%E7%9A%84%E6%95%B0%E6%8D%AE%E5%BA%93%E4%BF%A1%E6%81%AF%E6%B3%A8%E8%A7%A3"><span class="toc-text">代码示例：定义一个复杂的数据库信息注解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BD%BF%E7%94%A8%E6%B3%A8%E8%A7%A3%E4%B8%8E%E5%B1%9E%E6%80%A7%E8%B5%8B%E5%80%BC"><span class="toc-text">使用注解与属性赋值</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-4-%E5%85%83%E6%B3%A8%E8%A7%A3%EF%BC%9A%E6%B3%A8%E8%A7%A3%E7%9A%84%E2%80%9C%E9%85%8D%E7%BD%AE%E2%80%9D"><span class="toc-text">5.3.4 元注解：注解的“配置”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-5-%E5%8F%8D%E5%B0%84%E8%A7%A3%E6%9E%90%E6%B3%A8%E8%A7%A3%EF%BC%9A%E8%AF%BB%E5%8F%96%E5%85%83%E6%95%B0%E6%8D%AE%E7%9A%84API"><span class="toc-text">5.3.5 反射解析注解：读取元数据的API</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-3"><span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%8E%A5%E5%8F%A3%EF%BC%9AAnnotatedElement"><span class="toc-text">核心接口：AnnotatedElement</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-text">核心方法速查表</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E7%B3%BB%E7%BB%9F%E6%80%A7%E5%9C%B0%E8%A7%A3%E6%9E%90%E4%B8%80%E4%B8%AA%E7%B1%BB%E4%B8%8A%E7%9A%84%E6%89%80%E6%9C%89%E6%B3%A8%E8%A7%A3"><span class="toc-text">代码示例：系统性地解析一个类上的所有注解</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-6-%E7%BB%88%E6%9E%81%E5%AE%9E%E6%88%98-%E7%BB%93%E5%90%88%E5%8F%8D%E5%B0%84%E6%9E%84%E5%BB%BA%E8%BF%B7%E4%BD%A0ORM%E6%A1%86%E6%9E%B6"><span class="toc-text">5.3.6 [终极实战] 结合反射构建迷你ORM框架</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A4%E4%B8%80%EF%BC%9A%E5%AE%9A%E4%B9%89%E8%87%AA%E5%AE%9A%E4%B9%89%E6%B3%A8%E8%A7%A3-Table-%E5%92%8C-Column"><span class="toc-text">步骤一：定义自定义注解 (@Table 和 @Column)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A4%E4%BA%8C%EF%BC%9A%E5%88%9B%E5%BB%BA%E8%A2%AB%E6%B3%A8%E8%A7%A3%E7%9A%84%E5%AE%9E%E4%BD%93%E7%B1%BB-POJO"><span class="toc-text">步骤二：创建被注解的实体类 (POJO)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A4%E4%B8%89%EF%BC%9A%E7%BC%96%E5%86%99%E6%B3%A8%E8%A7%A3%E5%A4%84%E7%90%86%E5%99%A8%EF%BC%88%E6%A0%B8%E5%BF%83%E9%80%BB%E8%BE%91%EF%BC%89"><span class="toc-text">步骤三：编写注解处理器（核心逻辑）</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Java（五）：5.0 [元编程] 反射、注解</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-05-08T12:13:45.000Z" title="发表于 2025-05-08 20:13:45">2025-05-08</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-14T09:01:36.907Z" title="更新于 2025-07-14 17:01:36">2025-07-14</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">6.2k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>23分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Java（五）：5.0 [元编程] 反射、注解"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/62133.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/62133.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Java基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Java（五）：5.0 [元编程] 反射、注解</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-05-08T12:13:45.000Z" title="发表于 2025-05-08 20:13:45">2025-05-08</time><time itemprop="dateCreated datePublished" datetime="2025-07-14T09:01:36.907Z" title="更新于 2025-07-14 17:01:36">2025-07-14</time></header><div id="postchat_postcontent"><h2 id="5-0-元编程-反射、注解"><a href="#5-0-元编程-反射、注解" class="headerlink" title="5.0 [元编程] 反射、注解"></a><strong>5.0 [元编程] 反射、注解</strong></h2><p>本章将带您深入Java的“元编程”世界。元编程是指程序在运行时能够审视并操作自身结构的能力。我们将从这一切的基石——<strong>类加载机制</strong>讲起，然后深入学习实现元编程的核心技术——<strong>反射</strong>，并最终探讨其最广泛的应用——<strong>注解</strong>与<strong>Junit单元测试</strong>。</p><h3 id="5-1-基础-类加载机制与-ClassLoader"><a href="#5-1-基础-类加载机制与-ClassLoader" class="headerlink" title="5.1 [基础] 类加载机制与 ClassLoader"></a><strong>5.1 [基础] 类加载机制与 <code>ClassLoader</code></strong></h3><h5 id="面试题引入"><a href="#面试题引入" class="headerlink" title="面试题引入"></a><strong>面试题引入</strong></h5><blockquote><p>“请简述一下Java的类加载过程，以及双亲委派模型。”</p></blockquote><h5 id="类的生命周期"><a href="#类的生命周期" class="headerlink" title="类的生命周期"></a><strong>类的生命周期</strong></h5><p>一个<code>.java</code>源文件变成可以在JVM中运行的程序，其对应的<code>.class</code>文件需要经历一个完整的生命周期。这个过程主要分为</p><p><strong>加载（Loading）</strong>、**链接（Linking）<strong>和</strong>初始化（Initialization）**三个阶段。</p><ol><li><strong>加载</strong>：JVM通过类加载器（ClassLoader）找到对应的<code>.class</code>文件，读取其二进制数据，并在方法区中创建一个<code>java.lang.Class</code>对象。</li><li>**链接 **：<ul><li><strong>验证</strong>：确保被加载的类文件符合JVM规范，没有安全问题。</li><li><strong>准备</strong>：为类的<strong>静态变量</strong>分配内存，并设置其<strong>类型的默认值</strong>（如<code>int</code>为0，<code>Object</code>为<code>null</code>）。注意，此时并非执行程序员指定的初始值。</li><li><strong>解析</strong>：将类中的符号引用（如类名、方法名）替换为直接的内存地址引用。</li></ul></li><li><strong>初始化</strong>：这是类加载的最后一步。JVM会执行类的初始化方法<code>&lt;clinit&gt;()</code>。这个方法由编译器自动收集类中所有<strong>静态变量的赋值动作</strong>和**静态代码块（<code>static{}</code>）**中的语句合并而成。只有到这一步，静态变量才会被赋予我们代码中指定的初始值。</li></ol><h5 id="类加载器-ClassLoaders-体系"><a href="#类加载器-ClassLoaders-体系" class="headerlink" title="类加载器 (ClassLoaders) 体系"></a><strong>类加载器 (ClassLoaders) 体系</strong></h5><p>Java通过一个层级分明的类加载器体系来完成类的加载工作。主要有三类加载器：</p><ol><li><p><strong>启动类加载器 (Bootstrap ClassLoader)</strong>：</p><ul><li>JVM的顶层加载器，由C++实现，是JVM自身的一部分。</li><li>负责加载Java最核心的库（如<code>rt.jar</code>里的<code>java.lang.*</code>、<code>java.util.*</code>等）。</li><li>在Java代码中尝试获取它的引用会返回<code>null</code>。</li></ul></li><li><p><strong>扩展类加载器 (Extension ClassLoader)</strong>：</p><ul><li>负责加载Java的扩展库（位于<code>jre/lib/ext</code>目录下）。</li><li>它的父加载器是启动类加载器。</li></ul></li><li><p><strong>应用程序类加载器 (Application ClassLoader)</strong>：</p><ul><li>也称为系统类加载器，是我们最常打交道的加载器。</li><li>负责加载用户类路径（Classpath）上我们自己编写的类和第三方库的JAR包。</li><li>它的父加载器是扩展类加载器。</li></ul></li></ol><h5 id="核心-双亲委派模型-Parent-Delegation-Model"><a href="#核心-双亲委派模型-Parent-Delegation-Model" class="headerlink" title="[核心] 双亲委派模型 (Parent-Delegation Model)"></a><strong>[核心] 双亲委派模型 (Parent-Delegation Model)</strong></h5><p>这是Java类加载器设计的核心原则，也是面试中的绝对高频考点。</p><ul><li><strong>工作流程</strong>：当一个类加载器收到加载类的请求时，它<strong>不会</strong>自己先去尝试加载，而是会<strong>首先把这个请求委派给它的父加载器</strong>去完成。每一层的加载器都是如此。只有当父加载器在自己的搜索范围内找不到指定的类，无法完成加载请求时，子加载器才会自己去尝试加载。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.smartcis.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250714170101480.png" alt="image-20250714170101480"></p><ul><li><strong>为何如此设计？</strong><ol><li><strong>避免类的重复加载</strong>：通过委派机制，一个类最终只会被一个加载器加载一次，确保了该类在JVM中的唯一性。</li><li><strong>保证核心库的安全</strong>：这是最重要的目的。它防止了Java的核心API被恶意或无意地篡改。例如，你无法自己编写一个<code>java.lang.String</code>类来替代系统的<code>String</code>类。因为当加载请求传递到最顶层的启动类加载器时，它会找到并加载JDK自带的、真正的<code>String</code>类，加载过程至此结束，你编写的“假”<code>String</code>类将永远没有机会被加载。</li></ol></li></ul><h5 id="代码示例：获取类加载器并查看其层级"><a href="#代码示例：获取类加载器并查看其层级" class="headerlink" title="代码示例：获取类加载器并查看其层级"></a><strong>代码示例：获取类加载器并查看其层级</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 1. 获取当前自定义类的加载器</span></span><br><span class="line">        <span class="type">ClassLoader</span> <span class="variable">appClassLoader</span> <span class="operator">=</span> Main.class.getClassLoader();</span><br><span class="line">        System.out.println(<span class="string">"应用程序类加载器 (AppClassLoader): "</span> + appClassLoader);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. 获取其父加载器 -&gt; 扩展类加载器</span></span><br><span class="line">        <span class="type">ClassLoader</span> <span class="variable">extClassLoader</span> <span class="operator">=</span> appClassLoader.getParent();</span><br><span class="line">        System.out.println(<span class="string">"扩展类加载器 (ExtClassLoader): "</span> + extClassLoader);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. 获取扩展类加载器的父加载器 -&gt; 启动类加载器</span></span><br><span class="line">        <span class="type">ClassLoader</span> <span class="variable">bootstrapClassLoader</span> <span class="operator">=</span> extClassLoader.getParent();</span><br><span class="line">        <span class="comment">// 因为启动类加载器是C++实现的，无法在Java中获取其实体，所以返回null</span></span><br><span class="line">        System.out.println(<span class="string">"启动类加载器 (BootstrapClassLoader): "</span> + bootstrapClassLoader);</span><br><span class="line">        </span><br><span class="line">        System.out.println(<span class="string">"--- JDK核心类的加载器 ---"</span>);</span><br><span class="line">        </span><br><span class="line">        <span class="comment">// 4. 尝试获取String类的加载器</span></span><br><span class="line">        <span class="comment">// String类由启动类加载器加载，因此在Java层面获取不到，返回null</span></span><br><span class="line">        <span class="type">ClassLoader</span> <span class="variable">stringClassLoader</span> <span class="operator">=</span> String.class.getClassLoader();</span><br><span class="line">        System.out.println(<span class="string">"String.class的加载器: "</span> + stringClassLoader);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>输出结果</strong>:</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">应用程序类加载器 (AppClassLoader): sun.misc.Launcher$AppClassLoader@18b4aac2</span><br><span class="line">扩展类加载器 (ExtClassLoader): sun.misc.Launcher$ExtClassLoader@1b6d3586</span><br><span class="line">启动类加载器 (BootstrapClassLoader): null</span><br><span class="line">--- JDK核心类的加载器 ---</span><br><span class="line">String.class的加载器: null</span><br></pre></td></tr></tbody></table></figure><p>这个输出完美地验证了类加载器的层级关系和启动类加载器的特殊性。</p><hr><h3 id="5-2-核心-反射：运行时动态操控的艺术"><a href="#5-2-核心-反射：运行时动态操控的艺术" class="headerlink" title="5.2 [核心] 反射：运行时动态操控的艺术"></a><strong>5.2 [核心] 反射：运行时动态操控的艺术</strong></h3><p>在了解了Java代码如何被加载到JVM中之后，我们现在来学习一个Java中非常强大、也是所有主流框架（如Spring,<br>MyBatis）基石的特性——<strong>反射</strong>。</p><h4 id="5-2-1-什么是反射及其应用场景"><a href="#5-2-1-什么是反射及其应用场景" class="headerlink" title="5.2.1 什么是反射及其应用场景"></a><strong>5.2.1 什么是反射及其应用场景</strong></h4><h5 id="面试题引入-1"><a href="#面试题引入-1" class="headerlink" title="面试题引入"></a><strong>面试题引入</strong></h5><blockquote><p>“什么是反射？它有哪些优缺点和应用场景？”</p></blockquote><h5 id="核心概念"><a href="#核心概念" class="headerlink" title="核心概念"></a><strong>核心概念</strong></h5><p>反射（Reflection）是Java语言提供的一种<strong>在运行时，动态地、间接地检查、分析和操作自身结构与行为</strong>的能力。</p><p>我们可以用一个比喻来理解：</p><ul><li><strong>常规编程</strong>：就像我们拿到一本说明书（类的代码），我们严格按照说明书上的指示（方法调用）来操作一个设备（对象）。我们在写代码的时候，就知道这个设备有什么按钮，每个按钮叫什么。</li><li><strong>反射编程</strong>：就像我们没有说明书，只有一个密封的黑盒设备。但是我们拿到了一套“万能检测和操控工具”（即反射API）。通过这套工具，我们可以在<strong>程序运行时</strong>去探测这个黑盒：它有哪些按钮（方法）？有哪些内部零件（字段）？它的型号是什么（类名）？甚至，我们可以强行按下那些没有在外部暴露的内部按钮（调用私有方法）。</li></ul><h5 id="优缺点"><a href="#优缺点" class="headerlink" title="优缺点"></a><strong>优缺点</strong></h5><ul><li><p><strong>优点</strong>：</p><ul><li><strong>动态性与灵活性</strong>：这是反射最大的优点。它允许我们编写非常通用的代码，可以操作在编译时完全未知的类。所有主流框架的依赖注入（DI）、AOP等核心功能，都深度依赖反射。</li></ul></li><li><p><strong>缺点</strong>：</p><ol><li><strong>性能开销</strong>：反射操作（如方法查找）比直接代码调用要慢得多，因为它涉及更多的查找和检查步骤，并且绕过了JIT编译器的许多优化。因此，在性能敏感的核心路径上应避免使用。</li><li><strong>破坏封装</strong>：通过<code>setAccessible(true)</code>可以访问和修改类的私有成员，这违背了面向对象的封装原则。</li><li><strong>类型不安全</strong>：编译器无法对反射代码进行类型检查，可能将潜在的<code>ClassCastException</code>等错误从编译期推迟到运行时。</li></ol></li></ul><h5 id="应用场景"><a href="#应用场景" class="headerlink" title="应用场景"></a><strong>应用场景</strong></h5><ul><li><strong>框架开发</strong>：Spring的IoC/DI容器通过反射动态创建和注入Bean。</li><li><strong>动态代理</strong>：在运行时为一个或多个接口动态地生成实现类。</li><li><strong>注解处理</strong>：在运行时读取注解信息并执行相应逻辑。</li><li><strong>单元测试</strong>：Junit等测试框架通过反射查找并执行被<code>@Test</code>注解的方法。</li></ul><h4 id="5-2-2-反射的基石：java-lang-Class-对象"><a href="#5-2-2-反射的基石：java-lang-Class-对象" class="headerlink" title="5.2.2 反射的基石：java.lang.Class 对象"></a><strong>5.2.2 反射的基石：<code>java.lang.Class</code> 对象</strong></h4><p>要对一个类进行反射操作，第一步永远是获取代表这个类的<code>java.lang.Class</code>对象。它是反射所有操作的入口。</p><h5 id="获取Class对象的三种主要方式"><a href="#获取Class对象的三种主要方式" class="headerlink" title="获取Class对象的三种主要方式"></a><strong>获取<code>Class</code>对象的三种主要方式</strong></h5><ol><li><strong>通过类名获取</strong>：<code>ClassName.class</code><ul><li>最简单、最安全的方式，在编译时就会受到检查。</li></ul></li><li><strong>通过对象实例获取</strong>：<code>object.getClass()</code><ul><li>当你已经拥有一个该类的对象时使用。</li></ul></li><li><strong>通过类的全限定名获取</strong>：<code>Class.forName("com.example.MyClass")</code><ul><li>最动态的方式，可以在运行时根据一个字符串来加载任意类。常用于框架加载配置文件中指定的类。</li></ul></li></ol><h5 id="代码示例：获取Class对象"><a href="#代码示例：获取Class对象" class="headerlink" title="代码示例：获取Class对象"></a><strong>代码示例：获取<code>Class</code>对象</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">User</span> {}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> <span class="keyword">throws</span> ClassNotFoundException {</span><br><span class="line">        <span class="comment">// 方式一: .class 语法</span></span><br><span class="line">        Class&lt;User&gt; clazz1 = User.class;</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 方式二: object.getClass()</span></span><br><span class="line">        <span class="type">User</span> <span class="variable">user</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">User</span>();</span><br><span class="line">        Class&lt;?&gt; clazz2 = user.getClass();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 方式三: Class.forName()</span></span><br><span class="line">        Class&lt;?&gt; clazz3 = Class.forName(<span class="string">"com.example.User"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 三种方式获取到的都是同一个Class对象实例</span></span><br><span class="line">        System.out.println(<span class="string">"clazz1 == clazz2 : "</span> + (clazz1 == clazz2)); <span class="comment">// true</span></span><br><span class="line">        System.out.println(<span class="string">"clazz1 == clazz3 : "</span> + (clazz1 == clazz3)); <span class="comment">// true</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="5-2-3-通过反射操作类的成员"><a href="#5-2-3-通过反射操作类的成员" class="headerlink" title="5.2.3 通过反射操作类的成员"></a><strong>5.2.3 通过反射操作类的成员</strong></h4><p>获取到<code>Class</code>对象后，我们就可以像操作说明书一样，获取并操作它的所有部分。</p><h5 id="1-操作构造器-Constructor"><a href="#1-操作构造器-Constructor" class="headerlink" title="1. 操作构造器 (Constructor)"></a><strong>1. 操作构造器 (<code>Constructor</code>)</strong></h5><ul><li><strong>核心API</strong>：<code>getConstructors()</code>, <code>getConstructor(...)</code>,<br><code>getDeclaredConstructors()</code>, <code>getDeclaredConstructor(...)</code>,<br><code>newInstance(...)</code>。</li><li><strong><code>getDeclared...</code> vs.<br><code>get...</code></strong>：带有<code>Declared</code>字样的方法可以获取到<strong>所有</strong>（包括<code>private</code>）的成员；不带的只能获取<code>public</code>成员。此规则对方法和字段同样适用。</li></ul><h6 id="代码示例：调用不同的构造器"><a href="#代码示例：调用不同的构造器" class="headerlink" title="代码示例：调用不同的构造器"></a><strong>代码示例：调用不同的构造器</strong></h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.lang.reflect.Constructor;</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Student</span> {</span><br><span class="line">    <span class="keyword">private</span> String name;</span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> age;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">Student</span><span class="params">()</span> {</span><br><span class="line">        <span class="built_in">this</span>.name = <span class="string">"默认学生"</span>;</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">Student</span><span class="params">(String name, <span class="type">int</span> age)</span> {</span><br><span class="line">        <span class="built_in">this</span>.name = name;</span><br><span class="line">        <span class="built_in">this</span>.age = age;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="title function_">Student</span><span class="params">(String name)</span> { <span class="comment">// 私有构造器</span></span><br><span class="line">        <span class="built_in">this</span>.name = name;</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">toString</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> <span class="string">"Student{name='"</span> + name + <span class="string">"', age="</span> + age + <span class="string">"}"</span>;</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> <span class="keyword">throws</span> Exception {</span><br><span class="line">        Class&lt;Student&gt; clazz = Student.class;</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 1. 调用公共无参构造器</span></span><br><span class="line">        Constructor&lt;Student&gt; c1 = clazz.getConstructor();</span><br><span class="line">        <span class="type">Student</span> <span class="variable">s1</span> <span class="operator">=</span> c1.newInstance();</span><br><span class="line">        System.out.println(<span class="string">"调用无参构造器: "</span> + s1);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. 调用公共有参构造器</span></span><br><span class="line">        Constructor&lt;Student&gt; c2 = clazz.getConstructor(String.class, <span class="type">int</span>.class);</span><br><span class="line">        <span class="type">Student</span> <span class="variable">s2</span> <span class="operator">=</span> c2.newInstance(<span class="string">"Alice"</span>, <span class="number">18</span>);</span><br><span class="line">        System.out.println(<span class="string">"调用有参构造器: "</span> + s2);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. 调用私有构造器（暴力反射）</span></span><br><span class="line">        Constructor&lt;Student&gt; c3 = clazz.getDeclaredConstructor(String.class);</span><br><span class="line">        c3.setAccessible(<span class="literal">true</span>); <span class="comment">// 必须设置可访问，否则会抛出IllegalAccessException</span></span><br><span class="line">        <span class="type">Student</span> <span class="variable">s3</span> <span class="operator">=</span> c3.newInstance(<span class="string">"Bob"</span>);</span><br><span class="line">        System.out.println(<span class="string">"调用私有构造器: "</span> + s3);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="2-操作方法-Method"><a href="#2-操作方法-Method" class="headerlink" title="2. 操作方法 (Method)"></a><strong>2. 操作方法 (<code>Method</code>)</strong></h5><ul><li><strong>核心API</strong>：</li></ul><ul><li><strong>getMethods()</strong>：返回类中所有公共（public）方法的数组，包括继承的方法。</li><li><strong>getMethod(…)</strong>：返回类中指定的公共方法，参数为方法名和参数类型类对象数组。</li><li><strong>getDeclaredMethods()</strong>：返回类中声明的所有方法的数组，包括私有（private）、保护（protected）和默认（default）访问权限的方法，但不包括继承的方法。</li><li><strong>getDeclaredMethod(…)</strong>：返回类中声明的指定方法，参数为方法名和参数类型类对象数组。</li><li><strong>invoke(…)</strong>：用于调用对象的指定方法，参数为对象实例、方法对象和方法的参数数组。</li></ul><h6 id="代码示例：调用各种方法"><a href="#代码示例：调用各种方法" class="headerlink" title="代码示例：调用各种方法"></a><strong>代码示例：调用各种方法</strong></h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.lang.reflect.Method;</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Calculator</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="type">int</span> <span class="title function_">add</span><span class="params">(<span class="type">int</span> a, <span class="type">int</span> b)</span> {</span><br><span class="line">        <span class="keyword">return</span> a + b;</span><br><span class="line">    }</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">void</span> <span class="title function_">showInfo</span><span class="params">(String message)</span> {</span><br><span class="line">        System.out.println(<span class="string">"私有方法被调用: "</span> + message);</span><br><span class="line">    }</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">printVersion</span><span class="params">()</span> {</span><br><span class="line">        System.out.println(<span class="string">"Calculator Version 1.0"</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> <span class="keyword">throws</span> Exception {</span><br><span class="line">        Class&lt;Calculator&gt; clazz = Calculator.class;</span><br><span class="line">        <span class="comment">// 注意，我们默认没有指定构造器的修饰符，那么他就是default的，必须通过Declared来获取</span></span><br><span class="line">        <span class="type">Calculator</span> <span class="variable">calculator</span> <span class="operator">=</span> clazz.getDeclaredConstructor().newInstance();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 1. 调用公共方法</span></span><br><span class="line">        <span class="type">Method</span> <span class="variable">addMethod</span> <span class="operator">=</span> clazz.getMethod(<span class="string">"add"</span>, <span class="type">int</span>.class, <span class="type">int</span>.class);</span><br><span class="line">        <span class="type">Object</span> <span class="variable">result</span> <span class="operator">=</span> addMethod.invoke(calculator, <span class="number">10</span>, <span class="number">20</span>);</span><br><span class="line">        System.out.println(<span class="string">"调用add(10, 20)的结果: "</span> + result);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. 调用私有方法</span></span><br><span class="line">        <span class="type">Method</span> <span class="variable">showInfoMethod</span> <span class="operator">=</span> clazz.getDeclaredMethod(<span class="string">"showInfo"</span>, String.class);</span><br><span class="line">        <span class="comment">// 调用私有方法时必须授权</span></span><br><span class="line">        showInfoMethod.setAccessible(<span class="literal">true</span>);</span><br><span class="line">        showInfoMethod.invoke(calculator, <span class="string">"Hello Reflection"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. 调用静态方法</span></span><br><span class="line">        <span class="type">Method</span> <span class="variable">printVersionMethod</span> <span class="operator">=</span> clazz.getMethod(<span class="string">"printVersion"</span>);</span><br><span class="line">        <span class="comment">// 调用静态方法时，第一个参数（对象实例）传入null即可</span></span><br><span class="line">        printVersionMethod.invoke(<span class="literal">null</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="3-操作字段-Field"><a href="#3-操作字段-Field" class="headerlink" title="3. 操作字段 (Field)"></a><strong>3. 操作字段 (<code>Field</code>)</strong></h5><ul><li><strong>核心API</strong>：</li><li><strong>getFields()</strong>：返回类中所有公共（public）字段的数组，包括继承的字段。</li><li><strong>getField(…)</strong>：返回类中指定的公共字段，参数为字段名。</li><li><strong>getDeclaredFields()</strong>：返回类中声明的所有字段的数组，包括私有（private）、保护（protected）和默认（default）访问权限的字段，但不包括继承的字段。</li><li><strong>getDeclaredField(…)</strong>：返回类中声明的指定字段，参数为字段名。</li><li><strong>get(…)</strong>：用于获取对象指定字段的值，参数为对象实例和字段对象。</li><li><strong>set(…)</strong>：用于设置对象指定字段的值，参数为对象实例、字段对象和要设置的值。</li></ul><h6 id="代码示例：读写字段值"><a href="#代码示例：读写字段值" class="headerlink" title="代码示例：读写字段值"></a><strong>代码示例：读写字段值</strong></h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.lang.reflect.Field;</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Person</span> {</span><br><span class="line">    <span class="keyword">public</span> String name;</span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> <span class="variable">age</span> <span class="operator">=</span> <span class="number">20</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> <span class="keyword">throws</span> Exception {</span><br><span class="line">        Class&lt;Person&gt; clazz = Person.class;</span><br><span class="line">        <span class="type">Person</span> <span class="variable">person</span> <span class="operator">=</span> clazz.getDeclaredConstructor().newInstance();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 1. 操作公共字段</span></span><br><span class="line">        <span class="type">Field</span> <span class="variable">nameField</span> <span class="operator">=</span> clazz.getField(<span class="string">"name"</span>);</span><br><span class="line">        nameField.set(person, <span class="string">"张三"</span>);</span><br><span class="line">        System.out.println(<span class="string">"获取公共字段name: "</span> + nameField.get(person));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. 操作私有字段</span></span><br><span class="line">        <span class="type">Field</span> <span class="variable">ageField</span> <span class="operator">=</span> clazz.getDeclaredField(<span class="string">"age"</span>);</span><br><span class="line">        ageField.setAccessible(<span class="literal">true</span>);</span><br><span class="line">        ageField.set(person, <span class="number">30</span>);</span><br><span class="line">        System.out.println(<span class="string">"获取私有字段age: "</span> + ageField.get(person));</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="5-2-4-实战-反射的应用：迷你Spring框架"><a href="#5-2-4-实战-反射的应用：迷你Spring框架" class="headerlink" title="5.2.4 [实战] 反射的应用：迷你Spring框架"></a><strong>5.2.4 [实战] 反射的应用：迷你Spring框架</strong></h4><blockquote><p><strong>场景</strong>：编写一个简单的框架，它可以根据一个<code>app.properties</code>配置文件，动态地创建并执行指定的对象和方法。</p></blockquote><p><strong>1. 创建 <code>app.properties</code> 文件</strong> (放在<code>src</code>或<code>resources</code>目录下)</p><figure class="highlight properties"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">className</span>=<span class="string">com.example.UserService</span></span><br><span class="line"><span class="attr">methodName</span>=<span class="string">login</span></span><br></pre></td></tr></tbody></table></figure><p><strong>2. 创建业务类</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 这是一个被框架调用的业务类</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">UserService</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">login</span><span class="params">()</span> {</span><br><span class="line">        System.out.println(<span class="string">"用户服务：正在执行登录逻辑..."</span>);</span><br><span class="line">    }</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">register</span><span class="params">()</span> {</span><br><span class="line">        System.out.println(<span class="string">"用户服务：正在执行注册逻辑..."</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>3. 编写框架主类</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"><span class="keyword">import</span> java.io.InputStream;</span><br><span class="line"><span class="keyword">import</span> java.lang.reflect.Method;</span><br><span class="line"><span class="keyword">import</span> java.util.Properties;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 这是一个被框架调用的业务类</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">UserService</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">login</span><span class="params">()</span> {</span><br><span class="line">        System.out.println(<span class="string">"用户服务：正在执行登录逻辑..."</span>);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">register</span><span class="params">()</span> {</span><br><span class="line">        System.out.println(<span class="string">"用户服务：正在执行注册逻辑..."</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> <span class="keyword">throws</span> Exception {</span><br><span class="line">        <span class="type">Properties</span> <span class="variable">props</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">Properties</span>();</span><br><span class="line">        <span class="type">InputStream</span> <span class="variable">in</span> <span class="operator">=</span> Main.class.getClassLoader().getResourceAsStream(<span class="string">"app.properties"</span>);</span><br><span class="line">        props.load(in);</span><br><span class="line">        <span class="comment">// 2. 从配置中获取类名和方法名</span></span><br><span class="line">        <span class="type">String</span> <span class="variable">className</span> <span class="operator">=</span> props.getProperty(<span class="string">"className"</span>);</span><br><span class="line">        <span class="type">String</span> <span class="variable">methodName</span> <span class="operator">=</span> props.getProperty(<span class="string">"methodName"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. 使用反射动态执行</span></span><br><span class="line">        <span class="comment">// a. 加载类</span></span><br><span class="line">        Class&lt;?&gt; clazz = Class.forName(className);</span><br><span class="line">        <span class="comment">// b. 创建对象</span></span><br><span class="line">        <span class="type">Object</span> <span class="variable">instance</span> <span class="operator">=</span> clazz.getDeclaredConstructor().newInstance();</span><br><span class="line">        <span class="comment">// c. 获取方法</span></span><br><span class="line">        <span class="type">Method</span> <span class="variable">method</span> <span class="operator">=</span> clazz.getMethod(methodName);</span><br><span class="line">        <span class="comment">// d. 调用方法</span></span><br><span class="line">        method.invoke(instance);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p>这个简单的例子，就揭示了Spring等现代框架实现其强大动态能力的核心原理。</p><hr><h3 id="5-3-应用-注解：为代码嵌入元数据"><a href="#5-3-应用-注解：为代码嵌入元数据" class="headerlink" title="5.3 [应用] 注解：为代码嵌入元数据"></a><strong>5.3 [应用] 注解：为代码嵌入元数据</strong></h3><p>注解是Java中一种强大的元编程工具，它允许我们在不改变代码本身逻辑的前提下，为类、方法、字段等程序元素添加“标签”或“元数据”。这些元数据可以被编译器或运行时环境读取，从而实现各种自动化、配置化和框架化的功能。</p><h4 id="5-3-1-注解的核心思想"><a href="#5-3-1-注解的核心思想" class="headerlink" title="5.3.1 注解的核心思想"></a><strong>5.3.1 注解的核心思想</strong></h4><h5 id="面试题引入-2"><a href="#面试题引入-2" class="headerlink" title="面试题引入"></a><strong>面试题引入</strong></h5><blockquote><p>“注解（Annotation）是什么？它和注释（Comment）有什么本质区别？”</p></blockquote><h5 id="注解-vs-注释"><a href="#注解-vs-注释" class="headerlink" title="注解 vs. 注释"></a><strong>注解 vs. 注释</strong></h5><ul><li><strong>注释 (<code>//</code>, <code>/*...*/</code>)</strong>:<br>是写给<strong>程序员</strong>看的，用于解释代码，提高可读性。编译器会<strong>完全忽略</strong>注释。</li><li><strong>注解 (<code>@...</code>)</strong>:<br>是写给**程序（编译器、框架、工具）**看的。它是一种元数据，程序可以根据这个元数据来决定不同的处理方式。</li></ul><h5 id="注解的重要性：现代框架的基石"><a href="#注解的重要性：现代框架的基石" class="headerlink" title="注解的重要性：现代框架的基石"></a><strong>注解的重要性：现代框架的基石</strong></h5><p>理解现代Java框架的实现原理，有一个公认的公式：<strong>框架 = 反射 + 注解 +<br>设计模式</strong>。Spring的依赖注入、MyBatis的SQL映射、Junit的单元测试，其核心都是通过反射来查找并处理开发者定义的注解，从而实现自动化配置和功能的。</p><h4 id="5-3-2-Java-内置注解"><a href="#5-3-2-Java-内置注解" class="headerlink" title="5.3.2 Java 内置注解"></a><strong>5.3.2 Java 内置注解</strong></h4><p>Java预置了一些非常重要的注解，用于辅助编译器进行检查。</p><ul><li><strong><code>@Override</code></strong>:<br>标记一个方法意图重写父类的方法。这是给编译器的“承诺书”，如果该方法并未正确重写（如方法名拼写错误），编译器将报错。</li><li><strong><code>@Deprecated</code></strong>:<br>标记一个元素（类、方法、字段）已过时，不推荐使用。调用被此注解标记的元素时，编译器会发出警告。</li><li><strong><code>@SuppressWarnings</code></strong>:<br>压制编译器警告。在明确知道警告无害的情况下使用，可以使代码更整洁。例如<code>@SuppressWarnings("deprecation")</code>。</li><li><strong><code>@FunctionalInterface</code> (Java 8+)</strong>:<br>标记一个接口为“函数式接口”，即该接口有且仅有一个抽象方法。这是编译器层面的约束，确保该接口可以被Lambda表达式所使用。</li></ul><h4 id="5-3-3-自定义注解"><a href="#5-3-3-自定义注解" class="headerlink" title="5.3.3 自定义注解"></a><strong>5.3.3 自定义注解</strong></h4><p>我们可以使用<code>@interface</code>关键字来定义自己的注解。</p><h5 id="定义注解与属性"><a href="#定义注解与属性" class="headerlink" title="定义注解与属性"></a><strong>定义注解与属性</strong></h5><p>注解的属性定义形式为 <code>类型 属性名();</code>。可以为属性提供<code>default</code>默认值。</p><ul><li><strong>属性支持的类型</strong>：<ul><li>所有基本数据类型 (<code>int</code>, <code>double</code>等)</li><li><code>String</code>, <code>Class</code>, <code>enum</code></li><li>注解类型</li><li>以上所有类型的一维数组形式</li></ul></li></ul><h5 id="代码示例：定义一个复杂的数据库信息注解"><a href="#代码示例：定义一个复杂的数据库信息注解" class="headerlink" title="代码示例：定义一个复杂的数据库信息注解"></a><strong>代码示例：定义一个复杂的数据库信息注解</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.custom;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 一个用于模拟数据库信息的注解</span></span><br><span class="line"><span class="keyword">public</span> <span class="meta">@interface</span> DataBaseInfo {</span><br><span class="line">    String <span class="title function_">url</span><span class="params">()</span>;</span><br><span class="line"></span><br><span class="line">    String <span class="title function_">user</span><span class="params">()</span>;</span><br><span class="line"></span><br><span class="line">    String <span class="title function_">password</span><span class="params">()</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 带有默认值的属性</span></span><br><span class="line">    String <span class="title function_">driver</span><span class="params">()</span> <span class="keyword">default</span> <span class="string">"com.mysql.cj.jdbc.Driver"</span>;</span><br><span class="line"></span><br><span class="line">    <span class="type">int</span> <span class="title function_">port</span><span class="params">()</span> <span class="keyword">default</span> <span class="number">3306</span>;</span><br><span class="line"></span><br><span class="line">    String[] characterSets() <span class="keyword">default</span> {<span class="string">"UTF-8"</span>, <span class="string">"GBK"</span>};</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="使用注解与属性赋值"><a href="#使用注解与属性赋值" class="headerlink" title="使用注解与属性赋值"></a><strong>使用注解与属性赋值</strong></h5><ul><li><strong>基本语法</strong>：<code>@注解名(属性名1=值1, 属性名2=值2, ...)</code></li><li><strong><code>value</code>属性简写</strong>：如果一个注解只有一个名为<code>value</code>的属性，在使用时可以省略<code>value=</code>。</li><li><strong>数组属性简写</strong>：如果数组属性只有一个元素，可以省略花括号<code>{}</code>。</li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"><span class="keyword">import</span> com.example.custom.DataBaseInfo;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="comment">// 使用自定义注解并为属性赋值</span></span><br><span class="line">    <span class="meta">@DataBaseInfo(</span></span><br><span class="line"><span class="meta">        url = "**********************",</span></span><br><span class="line"><span class="meta">        user = "root",</span></span><br><span class="line"><span class="meta">        password = "password123",</span></span><br><span class="line"><span class="meta">        characterSets = "UTF-8" // 数组属性只有一个元素，省略了花括号</span></span><br><span class="line"><span class="meta">    )</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">connectToDatabase</span><span class="params">()</span> {</span><br><span class="line">        <span class="comment">// ...</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="5-3-4-元注解：注解的“配置”"><a href="#5-3-4-元注解：注解的“配置”" class="headerlink" title="5.3.4 元注解：注解的“配置”"></a><strong>5.3.4 元注解：注解的“配置”</strong></h4><p>元注解是“用于注解的注解”，它们定义了我们自定义的注解将如何工作。</p><ul><li><strong><code>@Target</code></strong>: 决定注解能用在哪里（类、方法、字段等）。</li><li><strong><code>@Retention</code></strong>:<br>决定注解的生命周期。<code>RetentionPolicy.RUNTIME</code>是关键，它让注解在运行时能被反射读取。<ul><li><strong>SOURCE</strong>：注解只在源代码级别保留，编译后不会包含在字节码文件中。</li><li><strong>CLASS</strong>：注解在源代码级别和编译后的字节码文件中保留，但在运行时不会保留。</li><li><strong>RUNTIME</strong>：注解在源代码级别、编译后的字节码文件中保留，并且在运行时也可以通过反射读取。</li></ul></li><li><strong><code>@Inherited</code></strong>: 允许子类继承父类上的注解。</li><li><strong><code>@Documented</code></strong>: 让注解信息能被<code>javadoc</code>工具提取到API文档中。</li><li><strong><code>@Repeatable</code> (Java 8+)</strong>: 允许同一个注解在同一个位置上重复使用。</li></ul><p>例如我们熟知的Override注解</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">@Target(ElementType.METHOD)</span> <span class="comment">// 用在方法上</span></span><br><span class="line"><span class="meta">@Retention(RetentionPolicy.SOURCE)</span> <span class="comment">// 生命周期为源代码级别</span></span><br><span class="line"><span class="keyword">public</span> <span class="meta">@interface</span> Override {</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><hr><h4 id="5-3-5-反射解析注解：读取元数据的API"><a href="#5-3-5-反射解析注解：读取元数据的API" class="headerlink" title="5.3.5 反射解析注解：读取元数据的API"></a><strong>5.3.5 反射解析注解：读取元数据的API</strong></h4><h5 id="面试题引入-3"><a href="#面试题引入-3" class="headerlink" title="面试题引入"></a><strong>面试题引入</strong></h5><blockquote><p>“如何通过反射在运行时获取到一个类、方法或字段上的注解信息？”</p></blockquote><h5 id="核心接口：AnnotatedElement"><a href="#核心接口：AnnotatedElement" class="headerlink" title="核心接口：AnnotatedElement"></a><strong>核心接口：<code>AnnotatedElement</code></strong></h5><p>Java的反射体系中，<code>Class</code>, <code>Method</code>, <code>Field</code>, <code>Constructor</code><br>等所有可以被注解的程序元素，都实现了<code>java.lang.reflect.AnnotatedElement</code>接口。这个接口是所有注解解析操作的入口，它提供了统一的、用于读取注解的核心方法。</p><h5 id="核心方法速查表"><a href="#核心方法速查表" class="headerlink" title="核心方法速查表"></a><strong>核心方法速查表</strong></h5><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>boolean isAnnotationPresent(Class annotation)</code></td><td align="left">判断当前元素上是否存在<strong>指定类型</strong>的注解。</td></tr><tr><td align="left"><code>&lt;T extends Annotation&gt; T getAnnotation(Class&lt;T&gt; annotation)</code></td><td align="left">获取当前元素上<strong>指定类型</strong>的注解对象，如果不存在则返回<code>null</code>。</td></tr><tr><td align="left"><code>Annotation[] getAnnotations()</code></td><td align="left">获取当前元素上<strong>所有</strong>的注解对象数组。</td></tr><tr><td align="left"><code>Annotation[] getDeclaredAnnotations()</code></td><td align="left">获取<strong>直接在</strong>当前元素上声明的注解（不包括从父类继承的）。</td></tr></tbody></table><h5 id="代码示例：系统性地解析一个类上的所有注解"><a href="#代码示例：系统性地解析一个类上的所有注解" class="headerlink" title="代码示例：系统性地解析一个类上的所有注解"></a><strong>代码示例：系统性地解析一个类上的所有注解</strong></h5><blockquote><p><strong>场景</strong>：我们定义一个<code>UserProfile</code>类，在其类、字段、方法上都使用自定义注解，然后编写一个解析器来读取所有这些元数据。</p></blockquote><ul><li><p><strong>步骤一：定义几个用于演示的注解</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.annotation;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.lang.annotation.*;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Retention(RetentionPolicy.RUNTIME)</span></span><br><span class="line"><span class="meta">@Target(ElementType.TYPE)</span></span><br><span class="line"><span class="meta">@interface</span> ApiDoc {String <span class="title function_">value</span><span class="params">()</span>;}</span><br><span class="line"></span><br><span class="line"><span class="meta">@Retention(RetentionPolicy.RUNTIME)</span></span><br><span class="line"><span class="meta">@Target(ElementType.FIELD)</span></span><br><span class="line"><span class="meta">@interface</span> InjectValue {String <span class="title function_">source</span><span class="params">()</span> <span class="keyword">default</span> <span class="string">"config.properties"</span>;}</span><br><span class="line"></span><br><span class="line"><span class="meta">@Retention(RetentionPolicy.RUNTIME)</span></span><br><span class="line"><span class="meta">@Target(ElementType.METHOD)</span></span><br><span class="line"><span class="meta">@interface</span> Loggable {}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>步骤二：在一个类中使用这些注解</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.model;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.example.annotation.ApiDoc;</span><br><span class="line"><span class="keyword">import</span> com.example.annotation.InjectValue;</span><br><span class="line"><span class="keyword">import</span> com.example.annotation.Loggable;</span><br><span class="line"></span><br><span class="line"><span class="meta">@ApiDoc("用户资料实体类")</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">UserProfile</span> {</span><br><span class="line"></span><br><span class="line">    <span class="meta">@InjectValue(source = "db.properties")</span></span><br><span class="line">    <span class="keyword">public</span> String username;</span><br><span class="line"></span><br><span class="line">    <span class="meta">@InjectValue</span></span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> age;</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Loggable</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">displayProfile</span><span class="params">()</span> {</span><br><span class="line">        System.out.println(<span class="string">"Displaying user profile..."</span>);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">setAge</span><span class="params">(<span class="type">int</span> age)</span> {</span><br><span class="line">        <span class="built_in">this</span>.age = age;</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>步骤三：编写反射解析器</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br></pre></td></tr></tbody></table></figure><p>import com.example.annotation.ApiDoc;<br>import com.example.annotation.InjectValue;<br>import com.example.annotation.Loggable;<br>import com.example.model.UserProfile;</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br></pre></td><td class="code"><pre><span class="line"></span><br><span class="line">import java.lang.reflect.Field; import java.lang.reflect.Method;</span><br><span class="line"></span><br><span class="line">    public class Main {</span><br><span class="line">        public static void main(String[] args) {</span><br><span class="line">        // 获取UserProfile的Class对象</span><br><span class="line">            Class&lt;UserProfile&gt; clazz = UserProfile.class;</span><br><span class="line"></span><br><span class="line">            System.out.println("--- 1. 解析类上的注解 ---");</span><br><span class="line">            if (clazz.isAnnotationPresent(ApiDoc.class)) {</span><br><span class="line">                ApiDoc apiDoc = clazz.getAnnotation(ApiDoc.class);</span><br><span class="line">            System.out.println("类文档注解: " + apiDoc.value());</span><br><span class="line">            }</span><br><span class="line"></span><br><span class="line">            System.out.println("\n--- 2. 解析字段上的注解 ---");</span><br><span class="line">            // 遍历所有已声明的字段</span><br><span class="line">            for (Field field : clazz.getDeclaredFields()) {</span><br><span class="line">                if (field.isAnnotationPresent(InjectValue.class)) {</span><br><span class="line">                    InjectValue injectValue = field.getAnnotation(InjectValue.class);</span><br><span class="line">                    System.out.println("字段 '" + field.getName() + "' 需要从 '" +</span><br><span class="line">                            injectValue.source() + "' 注入值。");</span><br><span class="line">            }</span><br><span class="line">            }</span><br><span class="line"></span><br><span class="line">            System.out.println("\n--- 3. 解析方法上的注解 ---");</span><br><span class="line">            // 遍历所有已声明的方法</span><br><span class="line">            for (Method method : clazz.getDeclaredMethods()) {</span><br><span class="line">                if (method.isAnnotationPresent(Loggable.class)) {</span><br><span class="line">                    System.out.println("方法 '" + method.getName() + "' 需要被日志记录。");</span><br><span class="line">                }</span><br><span class="line">            }</span><br><span class="line">        }</span><br><span class="line">    }</span><br></pre></td></tr></tbody></table></figure></li></ul><p><strong>输出结果</strong>:</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line">--- 1. 解析类上的注解 ---</span><br><span class="line">类文档注解: 用户资料实体类</span><br><span class="line"></span><br><span class="line">--- 2. 解析字段上的注解 ---</span><br><span class="line">字段 'username' 需要从 'db.properties' 注入值。</span><br><span class="line">字段 'age' 需要从 'config.properties' 注入值。</span><br><span class="line"></span><br><span class="line">--- 3. 解析方法上的注解 ---</span><br><span class="line">方法 'displayProfile' 需要被日志记录。</span><br></pre></td></tr></tbody></table></figure><hr><h4 id="5-3-6-终极实战-结合反射构建迷你ORM框架"><a href="#5-3-6-终极实战-结合反射构建迷你ORM框架" class="headerlink" title="5.3.6 [终极实战] 结合反射构建迷你ORM框架"></a><strong>5.3.6 [终极实战] 结合反射构建迷你ORM框架</strong></h4><p>这是注解与反射最经典的结合应用，它模拟了MyBatis等ORM框架的核心原理。</p><blockquote><p><strong>目标</strong>：编写一个程序，能够扫描指定包下的所有类，并为那些被<code>@Table</code>和<code>@Column</code>注解标记的类，自动生成SQL的<br><code>CREATE TABLE</code> 语句。</p></blockquote><h5 id="步骤一：定义自定义注解-Table-和-Column"><a href="#步骤一：定义自定义注解-Table-和-Column" class="headerlink" title="步骤一：定义自定义注解 (@Table 和 @Column)"></a><strong>步骤一：定义自定义注解 (<code>@Table</code> 和 <code>@Column</code>)</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.annotation;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.lang.annotation.*;</span><br><span class="line"></span><br><span class="line"><span class="comment">// @Table注解，用于类上，指定表名</span></span><br><span class="line"><span class="meta">@Target(ElementType.TYPE)</span></span><br><span class="line"><span class="meta">@Retention(RetentionPolicy.RUNTIME)</span></span><br><span class="line"><span class="keyword">public</span> <span class="meta">@interface</span> Table {</span><br><span class="line">    String <span class="title function_">value</span><span class="params">()</span>; <span class="comment">// 用value属性来接收表名</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.orm.annotations;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.lang.annotation.*;</span><br><span class="line"></span><br><span class="line"><span class="comment">// @Column注解，用于字段上，指定列名和类型</span></span><br><span class="line"><span class="meta">@Target(ElementType.FIELD)</span></span><br><span class="line"><span class="meta">@Retention(RetentionPolicy.RUNTIME)</span></span><br><span class="line"><span class="keyword">public</span> <span class="meta">@interface</span> Column {</span><br><span class="line">    String <span class="title function_">name</span><span class="params">()</span>;</span><br><span class="line">    String <span class="title function_">type</span><span class="params">()</span> <span class="keyword">default</span> <span class="string">"varchar(255)"</span>; <span class="comment">// 默认类型为varchar</span></span><br><span class="line">    <span class="type">int</span> <span class="title function_">length</span><span class="params">()</span> <span class="keyword">default</span> <span class="number">255</span>;</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="步骤二：创建被注解的实体类-POJO"><a href="#步骤二：创建被注解的实体类-POJO" class="headerlink" title="步骤二：创建被注解的实体类 (POJO)"></a><strong>步骤二：创建被注解的实体类 (POJO)</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.orm.entities;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.example.orm.annotations.*;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Table("t_user")</span> <span class="comment">// 指定该类对应的表名为 t_user</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">User</span> {</span><br><span class="line">    <span class="meta">@Column(name = "id", type = "int")</span></span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> id;</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Column(name = "user_name")</span></span><br><span class="line">    <span class="keyword">private</span> String username;</span><br><span class="line">    </span><br><span class="line">    <span class="meta">@Column(name = "user_age", type = "int")</span></span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> age;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// email字段没有注解，将被忽略</span></span><br><span class="line">    <span class="keyword">private</span> String email;</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="步骤三：编写注解处理器（核心逻辑）"><a href="#步骤三：编写注解处理器（核心逻辑）" class="headerlink" title="步骤三：编写注解处理器（核心逻辑）"></a><strong>步骤三：编写注解处理器（核心逻辑）</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.example.annotation.Column;</span><br><span class="line"><span class="keyword">import</span> com.example.annotation.Table;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.lang.reflect.Field;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> String <span class="title function_">generateCreateTableSql</span><span class="params">(String className)</span> <span class="keyword">throws</span> Exception {</span><br><span class="line">        <span class="comment">// 1. 获取Class对象</span></span><br><span class="line">        Class&lt;?&gt; clazz = Class.forName(className);</span><br><span class="line">        <span class="comment">// 2. 检查类上是否有@Table注解</span></span><br><span class="line">        <span class="keyword">if</span> (!clazz.isAnnotationPresent(Table.class)) {</span><br><span class="line">            <span class="keyword">return</span> <span class="literal">null</span>; <span class="comment">// 如果没有，则不是实体类，直接返回</span></span><br><span class="line">        }</span><br><span class="line">        <span class="comment">// 3. 获取@Table注解并读取表名</span></span><br><span class="line">        <span class="type">Table</span> <span class="variable">tableAnnotation</span> <span class="operator">=</span> clazz.getAnnotation(Table.class);</span><br><span class="line">        <span class="type">String</span> <span class="variable">tableName</span> <span class="operator">=</span> tableAnnotation.value();</span><br><span class="line"></span><br><span class="line">        <span class="type">StringBuilder</span> <span class="variable">sql</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">StringBuilder</span>();</span><br><span class="line">        sql.append(<span class="string">"CREATE TABLE "</span>).append(tableName).append(<span class="string">" (\n"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 4. 遍历所有字段，查找@Column注解</span></span><br><span class="line">        <span class="keyword">for</span> (Field field : clazz.getDeclaredFields()) {</span><br><span class="line">            <span class="keyword">if</span> (field.isAnnotationPresent(Column.class)) {</span><br><span class="line">                <span class="comment">// 5. 获取@Column注解并读取列名和类型</span></span><br><span class="line">                <span class="type">Column</span> <span class="variable">columnAnnotation</span> <span class="operator">=</span> field.getAnnotation(Column.class);</span><br><span class="line">                <span class="type">String</span> <span class="variable">columnName</span> <span class="operator">=</span> columnAnnotation.name();</span><br><span class="line">                <span class="type">String</span> <span class="variable">columnType</span> <span class="operator">=</span> columnAnnotation.type();</span><br><span class="line"></span><br><span class="line">                sql.append(<span class="string">"    "</span>).append(columnName).append(<span class="string">" "</span>).append(columnType).append(<span class="string">",\n"</span>);</span><br><span class="line">            }</span><br><span class="line">        }</span><br><span class="line">        <span class="comment">// 移除最后一个多余的逗号和换行符</span></span><br><span class="line">        <span class="keyword">if</span> (sql.toString().contains(<span class="string">","</span>)) {</span><br><span class="line">            sql.delete(sql.length() - <span class="number">2</span>, sql.length());</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        sql.append(<span class="string">"\n);"</span>);</span><br><span class="line">        <span class="keyword">return</span> sql.toString();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> <span class="keyword">throws</span> Exception {</span><br><span class="line">        <span class="type">String</span> <span class="variable">userSql</span> <span class="operator">=</span> generateCreateTableSql(<span class="string">"com.example.entities.User"</span>);</span><br><span class="line">        System.out.println(<span class="string">"--- 自动生成的User表SQL ---"</span>);</span><br><span class="line">        System.out.println(userSql);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>输出结果</strong>：</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">--- 自动生成的User表SQL ---</span><br><span class="line">CREATE TABLE t_user (</span><br><span class="line">    id int,</span><br><span class="line">    user_name varchar(255),s</span><br><span class="line">    user_age int</span><br><span class="line">);</span><br></pre></td></tr></tbody></table></figure><p>这个综合案例完美展示了如何通过“<strong>注解定义元数据 +<br>反射读取元数据</strong>”的模式，来构建强大、灵活的自动化框架。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/62133.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/62133.html&quot;)">Java（五）：5.0 [元编程] 反射、注解</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/62133.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=Java（五）：5.0 [元编程] 反射、注解&amp;url=https://prorise666.site/posts/62133.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java基础知识总汇<span class="tagsPageCount">9</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/6760.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Java（四）：4.0 [核心] Java I/O 流体系与实战</div></div></a></div><div class="next-post pull-right"><a href="/posts/19824.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Java（六）：6.0 Java核心开发库</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/30645.html" title="Java（一）：1.0 Java语言概述与核心生态"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（一）：1.0 Java语言概述与核心生态</div></div></a></div><div><a href="/posts/35626.html" title="Java（二）：2.0 Java基础"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（二）：2.0 Java基础</div></div></a></div><div><a href="/posts/43523.html" title="Java（9）：9.0 JavaWeb核心知识点速查"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/814899.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-09</div><div class="title">Java（9）：9.0 JavaWeb核心知识点速查</div></div></a></div><div><a href="/posts/42235.html" title="Java（三）：3.0 [核心] 面向对象编程"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（三）：3.0 [核心] 面向对象编程</div></div></a></div><div><a href="/posts/6760.html" title="Java（四）：4.0 [核心] Java I/O 流体系与实战"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（四）：4.0 [核心] Java I/O 流体系与实战</div></div></a></div><div><a href="/posts/14501.html" title="Java（八）：8.0 Java新语法总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（八）：8.0 Java新语法总结</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Java（五）：5.0 [元编程] 反射、注解",date:"2025-05-08 20:13:45",updated:"2025-07-14 17:01:36",tags:["Java基础知识总汇"],categories:["后端技术","Java"],content:'\n## **5.0 [元编程] 反射、注解**\n\n本章将带您深入Java的“元编程”世界。元编程是指程序在运行时能够审视并操作自身结构的能力。我们将从这一切的基石——**类加载机制**讲起，然后深入学习实现元编程的核心技术——**反射**，并最终探讨其最广泛的应用——**注解**与**Junit单元测试**。\n\n### **5.1 [基础] 类加载机制与 `ClassLoader`**\n\n##### **面试题引入**\n\n> “请简述一下Java的类加载过程，以及双亲委派模型。”\n\n##### **类的生命周期**\n\n一个`.java`源文件变成可以在JVM中运行的程序，其对应的`.class`文件需要经历一个完整的生命周期。这个过程主要分为\n\n**加载（Loading）**、**链接（Linking）**和**初始化（Initialization）**三个阶段。\n\n1. **加载**：JVM通过类加载器（ClassLoader）找到对应的`.class`文件，读取其二进制数据，并在方法区中创建一个`java.lang.Class`对象。\n2. **链接 **：\n   - **验证**：确保被加载的类文件符合JVM规范，没有安全问题。\n   - **准备**：为类的**静态变量**分配内存，并设置其**类型的默认值**（如`int`为0，`Object`为`null`）。注意，此时并非执行程序员指定的初始值。\n   - **解析**：将类中的符号引用（如类名、方法名）替换为直接的内存地址引用。\n3. **初始化**：这是类加载的最后一步。JVM会执行类的初始化方法`<clinit>()`。这个方法由编译器自动收集类中所有**静态变量的赋值动作**和**静态代码块（`static{}`）**中的语句合并而成。只有到这一步，静态变量才会被赋予我们代码中指定的初始值。\n\n##### **类加载器 (ClassLoaders) 体系**\n\nJava通过一个层级分明的类加载器体系来完成类的加载工作。主要有三类加载器：\n\n1. **启动类加载器 (Bootstrap ClassLoader)**：\n\n   - JVM的顶层加载器，由C++实现，是JVM自身的一部分。\n   - 负责加载Java最核心的库（如`rt.jar`里的`java.lang.*`、`java.util.*`等）。\n   - 在Java代码中尝试获取它的引用会返回`null`。\n\n2. **扩展类加载器 (Extension ClassLoader)**：\n\n   - 负责加载Java的扩展库（位于`jre/lib/ext`目录下）。\n   - 它的父加载器是启动类加载器。\n\n3. **应用程序类加载器 (Application ClassLoader)**：\n\n   - 也称为系统类加载器，是我们最常打交道的加载器。\n   - 负责加载用户类路径（Classpath）上我们自己编写的类和第三方库的JAR包。\n   - 它的父加载器是扩展类加载器。\n\n##### **[核心] 双亲委派模型 (Parent-Delegation Model)**\n\n这是Java类加载器设计的核心原则，也是面试中的绝对高频考点。\n\n- **工作流程**：当一个类加载器收到加载类的请求时，它**不会**自己先去尝试加载，而是会**首先把这个请求委派给它的父加载器**去完成。每一层的加载器都是如此。只有当父加载器在自己的搜索范围内找不到指定的类，无法完成加载请求时，子加载器才会自己去尝试加载。\n\n![image-20250714170101480](https://cdn.smartcis.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250714170101480.png)\n\n- **为何如此设计？**\n  1. **避免类的重复加载**：通过委派机制，一个类最终只会被一个加载器加载一次，确保了该类在JVM中的唯一性。\n  2. **保证核心库的安全**：这是最重要的目的。它防止了Java的核心API被恶意或无意地篡改。例如，你无法自己编写一个`java.lang.String`类来替代系统的`String`类。因为当加载请求传递到最顶层的启动类加载器时，它会找到并加载JDK自带的、真正的`String`类，加载过程至此结束，你编写的“假”`String`类将永远没有机会被加载。\n\n##### **代码示例：获取类加载器并查看其层级**\n\n```java\npackage com.example;\n\npublic class Main {\n    public static void main(String[] args) {\n        // 1. 获取当前自定义类的加载器\n        ClassLoader appClassLoader = Main.class.getClassLoader();\n        System.out.println("应用程序类加载器 (AppClassLoader): " + appClassLoader);\n\n        // 2. 获取其父加载器 -> 扩展类加载器\n        ClassLoader extClassLoader = appClassLoader.getParent();\n        System.out.println("扩展类加载器 (ExtClassLoader): " + extClassLoader);\n\n        // 3. 获取扩展类加载器的父加载器 -> 启动类加载器\n        ClassLoader bootstrapClassLoader = extClassLoader.getParent();\n        // 因为启动类加载器是C++实现的，无法在Java中获取其实体，所以返回null\n        System.out.println("启动类加载器 (BootstrapClassLoader): " + bootstrapClassLoader);\n        \n        System.out.println("--- JDK核心类的加载器 ---");\n        \n        // 4. 尝试获取String类的加载器\n        // String类由启动类加载器加载，因此在Java层面获取不到，返回null\n        ClassLoader stringClassLoader = String.class.getClassLoader();\n        System.out.println("String.class的加载器: " + stringClassLoader);\n    }\n}\n```\n\n**输出结果**:\n\n```\n应用程序类加载器 (AppClassLoader): sun.misc.Launcher$AppClassLoader@18b4aac2\n扩展类加载器 (ExtClassLoader): sun.misc.Launcher$ExtClassLoader@1b6d3586\n启动类加载器 (BootstrapClassLoader): null\n--- JDK核心类的加载器 ---\nString.class的加载器: null\n```\n\n这个输出完美地验证了类加载器的层级关系和启动类加载器的特殊性。\n\n---\n\n### **5.2 [核心] 反射：运行时动态操控的艺术**\n\n在了解了Java代码如何被加载到JVM中之后，我们现在来学习一个Java中非常强大、也是所有主流框架（如Spring,\nMyBatis）基石的特性——**反射**。\n\n#### **5.2.1 什么是反射及其应用场景**\n\n##### **面试题引入**\n\n> “什么是反射？它有哪些优缺点和应用场景？”\n\n##### **核心概念**\n\n反射（Reflection）是Java语言提供的一种**在运行时，动态地、间接地检查、分析和操作自身结构与行为**的能力。\n\n我们可以用一个比喻来理解：\n\n- **常规编程**：就像我们拿到一本说明书（类的代码），我们严格按照说明书上的指示（方法调用）来操作一个设备（对象）。我们在写代码的时候，就知道这个设备有什么按钮，每个按钮叫什么。\n- **反射编程**：就像我们没有说明书，只有一个密封的黑盒设备。但是我们拿到了一套“万能检测和操控工具”（即反射API）。通过这套工具，我们可以在**程序运行时**去探测这个黑盒：它有哪些按钮（方法）？有哪些内部零件（字段）？它的型号是什么（类名）？甚至，我们可以强行按下那些没有在外部暴露的内部按钮（调用私有方法）。\n\n##### **优缺点**\n\n- **优点**：\n\n  - **动态性与灵活性**：这是反射最大的优点。它允许我们编写非常通用的代码，可以操作在编译时完全未知的类。所有主流框架的依赖注入（DI）、AOP等核心功能，都深度依赖反射。\n\n- **缺点**：\n\n  1. **性能开销**：反射操作（如方法查找）比直接代码调用要慢得多，因为它涉及更多的查找和检查步骤，并且绕过了JIT编译器的许多优化。因此，在性能敏感的核心路径上应避免使用。\n  2. **破坏封装**：通过`setAccessible(true)`可以访问和修改类的私有成员，这违背了面向对象的封装原则。\n  3. **类型不安全**：编译器无法对反射代码进行类型检查，可能将潜在的`ClassCastException`等错误从编译期推迟到运行时。\n\n##### **应用场景**\n\n- **框架开发**：Spring的IoC/DI容器通过反射动态创建和注入Bean。\n- **动态代理**：在运行时为一个或多个接口动态地生成实现类。\n- **注解处理**：在运行时读取注解信息并执行相应逻辑。\n- **单元测试**：Junit等测试框架通过反射查找并执行被`@Test`注解的方法。\n\n#### **5.2.2 反射的基石：`java.lang.Class` 对象**\n\n要对一个类进行反射操作，第一步永远是获取代表这个类的`java.lang.Class`对象。它是反射所有操作的入口。\n\n##### **获取`Class`对象的三种主要方式**\n\n1. **通过类名获取**：`ClassName.class`\n   - 最简单、最安全的方式，在编译时就会受到检查。\n2. **通过对象实例获取**：`object.getClass()`\n   - 当你已经拥有一个该类的对象时使用。\n3. **通过类的全限定名获取**：`Class.forName("com.example.MyClass")`\n   - 最动态的方式，可以在运行时根据一个字符串来加载任意类。常用于框架加载配置文件中指定的类。\n\n##### **代码示例：获取`Class`对象**\n\n```java\npackage com.example;\n\nclass User {}\n\npublic class Main {\n    public static void main(String[] args) throws ClassNotFoundException {\n        // 方式一: .class 语法\n        Class<User> clazz1 = User.class;\n\n        // 方式二: object.getClass()\n        User user = new User();\n        Class<?> clazz2 = user.getClass();\n\n        // 方式三: Class.forName()\n        Class<?> clazz3 = Class.forName("com.example.User");\n\n        // 三种方式获取到的都是同一个Class对象实例\n        System.out.println("clazz1 == clazz2 : " + (clazz1 == clazz2)); // true\n        System.out.println("clazz1 == clazz3 : " + (clazz1 == clazz3)); // true\n    }\n}\n```\n\n#### **5.2.3 通过反射操作类的成员**\n\n获取到`Class`对象后，我们就可以像操作说明书一样，获取并操作它的所有部分。\n\n##### **1. 操作构造器 (`Constructor`)**\n\n- **核心API**：`getConstructors()`, `getConstructor(...)`,\n  `getDeclaredConstructors()`, `getDeclaredConstructor(...)`,\n  `newInstance(...)`。\n- **`getDeclared...` vs.\n  `get...`**：带有`Declared`字样的方法可以获取到**所有**（包括`private`）的成员；不带的只能获取`public`成员。此规则对方法和字段同样适用。\n\n###### **代码示例：调用不同的构造器**\n\n```java\npackage com.example;\n\nimport java.lang.reflect.Constructor;\n\nclass Student {\n    private String name;\n    private int age;\n\n    public Student() {\n        this.name = "默认学生";\n    }\n    \n    public Student(String name, int age) {\n        this.name = name;\n        this.age = age;\n    }\n\n    private Student(String name) { // 私有构造器\n        this.name = name;\n    }\n    \n    @Override\n    public String toString() {\n        return "Student{name=\'" + name + "\', age=" + age + "}";\n    }\n}\n\npublic class Main {\n    public static void main(String[] args) throws Exception {\n        Class<Student> clazz = Student.class;\n\n        // 1. 调用公共无参构造器\n        Constructor<Student> c1 = clazz.getConstructor();\n        Student s1 = c1.newInstance();\n        System.out.println("调用无参构造器: " + s1);\n\n        // 2. 调用公共有参构造器\n        Constructor<Student> c2 = clazz.getConstructor(String.class, int.class);\n        Student s2 = c2.newInstance("Alice", 18);\n        System.out.println("调用有参构造器: " + s2);\n\n        // 3. 调用私有构造器（暴力反射）\n        Constructor<Student> c3 = clazz.getDeclaredConstructor(String.class);\n        c3.setAccessible(true); // 必须设置可访问，否则会抛出IllegalAccessException\n        Student s3 = c3.newInstance("Bob");\n        System.out.println("调用私有构造器: " + s3);\n    }\n}\n```\n\n##### **2. 操作方法 (`Method`)**\n\n- **核心API**：\n\n* **getMethods()**：返回类中所有公共（public）方法的数组，包括继承的方法。\n* **getMethod(...)**：返回类中指定的公共方法，参数为方法名和参数类型类对象数组。\n* **getDeclaredMethods()**：返回类中声明的所有方法的数组，包括私有（private）、保护（protected）和默认（default）访问权限的方法，但不包括继承的方法。\n* **getDeclaredMethod(...)**：返回类中声明的指定方法，参数为方法名和参数类型类对象数组。\n* **invoke(...)**：用于调用对象的指定方法，参数为对象实例、方法对象和方法的参数数组。\n\n###### **代码示例：调用各种方法**\n\n```java\npackage com.example;\n\nimport java.lang.reflect.Method;\n\nclass Calculator {\n    public int add(int a, int b) {\n        return a + b;\n    }\n    private void showInfo(String message) {\n        System.out.println("私有方法被调用: " + message);\n    }\n    public static void printVersion() {\n        System.out.println("Calculator Version 1.0");\n    }\n}\n\npublic class Main {\n    public static void main(String[] args) throws Exception {\n        Class<Calculator> clazz = Calculator.class;\n        // 注意，我们默认没有指定构造器的修饰符，那么他就是default的，必须通过Declared来获取\n        Calculator calculator = clazz.getDeclaredConstructor().newInstance();\n\n        // 1. 调用公共方法\n        Method addMethod = clazz.getMethod("add", int.class, int.class);\n        Object result = addMethod.invoke(calculator, 10, 20);\n        System.out.println("调用add(10, 20)的结果: " + result);\n\n        // 2. 调用私有方法\n        Method showInfoMethod = clazz.getDeclaredMethod("showInfo", String.class);\n        // 调用私有方法时必须授权\n        showInfoMethod.setAccessible(true);\n        showInfoMethod.invoke(calculator, "Hello Reflection");\n\n        // 3. 调用静态方法\n        Method printVersionMethod = clazz.getMethod("printVersion");\n        // 调用静态方法时，第一个参数（对象实例）传入null即可\n        printVersionMethod.invoke(null);\n    }\n}\n```\n\n##### **3. 操作字段 (`Field`)**\n\n- **核心API**：\n- **getFields()**：返回类中所有公共（public）字段的数组，包括继承的字段。\n- **getField(...)**：返回类中指定的公共字段，参数为字段名。\n- **getDeclaredFields()**：返回类中声明的所有字段的数组，包括私有（private）、保护（protected）和默认（default）访问权限的字段，但不包括继承的字段。\n- **getDeclaredField(...)**：返回类中声明的指定字段，参数为字段名。\n- **get(...)**：用于获取对象指定字段的值，参数为对象实例和字段对象。\n- **set(...)**：用于设置对象指定字段的值，参数为对象实例、字段对象和要设置的值。\n\n###### **代码示例：读写字段值**\n\n```java\npackage com.example;\n\nimport java.lang.reflect.Field;\n\nclass Person {\n    public String name;\n    private int age = 20;\n}\n\npublic class Main {\n    public static void main(String[] args) throws Exception {\n        Class<Person> clazz = Person.class;\n        Person person = clazz.getDeclaredConstructor().newInstance();\n\n        // 1. 操作公共字段\n        Field nameField = clazz.getField("name");\n        nameField.set(person, "张三");\n        System.out.println("获取公共字段name: " + nameField.get(person));\n\n        // 2. 操作私有字段\n        Field ageField = clazz.getDeclaredField("age");\n        ageField.setAccessible(true);\n        ageField.set(person, 30);\n        System.out.println("获取私有字段age: " + ageField.get(person));\n    }\n}\n```\n\n#### **5.2.4 [实战] 反射的应用：迷你Spring框架**\n\n> **场景**：编写一个简单的框架，它可以根据一个`app.properties`配置文件，动态地创建并执行指定的对象和方法。\n\n**1. 创建 `app.properties` 文件** (放在`src`或`resources`目录下)\n\n```properties\nclassName=com.example.UserService\nmethodName=login\n```\n\n**2. 创建业务类**\n\n```java\npackage com.example;\n\n// 这是一个被框架调用的业务类\nclass UserService {\n    public void login() {\n        System.out.println("用户服务：正在执行登录逻辑...");\n    }\n    public void register() {\n        System.out.println("用户服务：正在执行注册逻辑...");\n    }\n}\n```\n\n**3. 编写框架主类**\n\n```java\npackage com.example;\n\nimport java.io.IOException;\nimport java.io.InputStream;\nimport java.lang.reflect.Method;\nimport java.util.Properties;\n\n// 这是一个被框架调用的业务类\nclass UserService {\n    public void login() {\n        System.out.println("用户服务：正在执行登录逻辑...");\n    }\n\n    public void register() {\n        System.out.println("用户服务：正在执行注册逻辑...");\n    }\n}\n\npublic class Main {\n    public static void main(String[] args) throws Exception {\n        Properties props = new Properties();\n        InputStream in = Main.class.getClassLoader().getResourceAsStream("app.properties");\n        props.load(in);\n        // 2. 从配置中获取类名和方法名\n        String className = props.getProperty("className");\n        String methodName = props.getProperty("methodName");\n\n        // 3. 使用反射动态执行\n        // a. 加载类\n        Class<?> clazz = Class.forName(className);\n        // b. 创建对象\n        Object instance = clazz.getDeclaredConstructor().newInstance();\n        // c. 获取方法\n        Method method = clazz.getMethod(methodName);\n        // d. 调用方法\n        method.invoke(instance);\n    }\n}\n```\n\n这个简单的例子，就揭示了Spring等现代框架实现其强大动态能力的核心原理。\n\n---\n\n### **5.3 [应用] 注解：为代码嵌入元数据**\n\n注解是Java中一种强大的元编程工具，它允许我们在不改变代码本身逻辑的前提下，为类、方法、字段等程序元素添加“标签”或“元数据”。这些元数据可以被编译器或运行时环境读取，从而实现各种自动化、配置化和框架化的功能。\n\n#### **5.3.1 注解的核心思想**\n\n##### **面试题引入**\n\n> “注解（Annotation）是什么？它和注释（Comment）有什么本质区别？”\n\n##### **注解 vs. 注释**\n\n- **注释 (`//`, `/*...*/`)**:\n  是写给**程序员**看的，用于解释代码，提高可读性。编译器会**完全忽略**注释。\n- **注解 (`@...`)**:\n  是写给**程序（编译器、框架、工具）**看的。它是一种元数据，程序可以根据这个元数据来决定不同的处理方式。\n\n##### **注解的重要性：现代框架的基石**\n\n理解现代Java框架的实现原理，有一个公认的公式：**框架 = 反射 + 注解 +\n设计模式**。Spring的依赖注入、MyBatis的SQL映射、Junit的单元测试，其核心都是通过反射来查找并处理开发者定义的注解，从而实现自动化配置和功能的。\n\n#### **5.3.2 Java 内置注解**\n\nJava预置了一些非常重要的注解，用于辅助编译器进行检查。\n\n- **`@Override`**:\n  标记一个方法意图重写父类的方法。这是给编译器的“承诺书”，如果该方法并未正确重写（如方法名拼写错误），编译器将报错。\n- **`@Deprecated`**:\n  标记一个元素（类、方法、字段）已过时，不推荐使用。调用被此注解标记的元素时，编译器会发出警告。\n- **`@SuppressWarnings`**:\n  压制编译器警告。在明确知道警告无害的情况下使用，可以使代码更整洁。例如`@SuppressWarnings(\"deprecation\")`。\n- **`@FunctionalInterface` (Java 8+)**:\n  标记一个接口为“函数式接口”，即该接口有且仅有一个抽象方法。这是编译器层面的约束，确保该接口可以被Lambda表达式所使用。\n\n#### **5.3.3 自定义注解**\n\n我们可以使用`@interface`关键字来定义自己的注解。\n\n##### **定义注解与属性**\n\n注解的属性定义形式为 `类型 属性名();`。可以为属性提供`default`默认值。\n\n- **属性支持的类型**：\n  - 所有基本数据类型 (`int`, `double`等)\n  - `String`, `Class`, `enum`\n  - 注解类型\n  - 以上所有类型的一维数组形式\n\n##### **代码示例：定义一个复杂的数据库信息注解**\n\n```java\npackage com.example.custom;\n\n// 一个用于模拟数据库信息的注解\npublic @interface DataBaseInfo {\n    String url();\n\n    String user();\n\n    String password();\n\n    // 带有默认值的属性\n    String driver() default \"com.mysql.cj.jdbc.Driver\";\n\n    int port() default 3306;\n\n    String[] characterSets() default {\"UTF-8\", \"GBK\"};\n}\n```\n\n##### **使用注解与属性赋值**\n\n- **基本语法**：`@注解名(属性名1=值1, 属性名2=值2, ...)`\n- **`value`属性简写**：如果一个注解只有一个名为`value`的属性，在使用时可以省略`value=`。\n- **数组属性简写**：如果数组属性只有一个元素，可以省略花括号`{}`。\n\n```java\npackage com.example;\nimport com.example.custom.DataBaseInfo;\n\npublic class Main {\n    // 使用自定义注解并为属性赋值\n    @DataBaseInfo(\n        url = \"**********************\",\n        user = \"root\",\n        password = \"password123\",\n        characterSets = \"UTF-8\" // 数组属性只有一个元素，省略了花括号\n    )\n    public void connectToDatabase() {\n        // ...\n    }\n}\n```\n\n#### **5.3.4 元注解：注解的“配置”**\n\n元注解是“用于注解的注解”，它们定义了我们自定义的注解将如何工作。\n\n- **`@Target`**: 决定注解能用在哪里（类、方法、字段等）。\n- **`@Retention`**:\n  决定注解的生命周期。`RetentionPolicy.RUNTIME`是关键，它让注解在运行时能被反射读取。\n  - **SOURCE**：注解只在源代码级别保留，编译后不会包含在字节码文件中。\n  - **CLASS**：注解在源代码级别和编译后的字节码文件中保留，但在运行时不会保留。\n  - **RUNTIME**：注解在源代码级别、编译后的字节码文件中保留，并且在运行时也可以通过反射读取。\n- **`@Inherited`**: 允许子类继承父类上的注解。\n- **`@Documented`**: 让注解信息能被`javadoc`工具提取到API文档中。\n- **`@Repeatable` (Java 8+)**: 允许同一个注解在同一个位置上重复使用。\n\n例如我们熟知的Override注解\n\n```java\n@Target(ElementType.METHOD) // 用在方法上\n@Retention(RetentionPolicy.SOURCE) // 生命周期为源代码级别\npublic @interface Override {\n}\n```\n\n---\n\n#### **5.3.5 反射解析注解：读取元数据的API**\n\n##### **面试题引入**\n\n> “如何通过反射在运行时获取到一个类、方法或字段上的注解信息？”\n\n##### **核心接口：`AnnotatedElement`**\n\nJava的反射体系中，`Class`, `Method`, `Field`, `Constructor`\n等所有可以被注解的程序元素，都实现了`java.lang.reflect.AnnotatedElement`接口。这个接口是所有注解解析操作的入口，它提供了统一的、用于读取注解的核心方法。\n\n##### **核心方法速查表**\n\n| 方法签名                                                      | 功能描述                                                       |\n| :------------------------------------------------------------ | :------------------------------------------------------------- |\n| `boolean isAnnotationPresent(Class annotation)`               | 判断当前元素上是否存在**指定类型**的注解。                     |\n| `<T extends Annotation> T getAnnotation(Class<T> annotation)` | 获取当前元素上**指定类型**的注解对象，如果不存在则返回`null`。 |\n| `Annotation[] getAnnotations()`                               | 获取当前元素上**所有**的注解对象数组。                         |\n| `Annotation[] getDeclaredAnnotations()`                       | 获取**直接在**当前元素上声明的注解（不包括从父类继承的）。     |\n\n##### **代码示例：系统性地解析一个类上的所有注解**\n\n> **场景**：我们定义一个`UserProfile`类，在其类、字段、方法上都使用自定义注解，然后编写一个解析器来读取所有这些元数据。\n\n- **步骤一：定义几个用于演示的注解**\n\n  ```java\n  package com.example.annotation;\n\n  import java.lang.annotation.*;\n\n  @Retention(RetentionPolicy.RUNTIME)\n  @Target(ElementType.TYPE)\n  @interface ApiDoc {String value();}\n\n  @Retention(RetentionPolicy.RUNTIME)\n  @Target(ElementType.FIELD)\n  @interface InjectValue {String source() default \"config.properties\";}\n\n  @Retention(RetentionPolicy.RUNTIME)\n  @Target(ElementType.METHOD)\n  @interface Loggable {}\n  ```\n\n- **步骤二：在一个类中使用这些注解**\n\n  ```java\n  package com.example.model;\n\n  import com.example.annotation.ApiDoc;\n  import com.example.annotation.InjectValue;\n  import com.example.annotation.Loggable;\n\n  @ApiDoc(\"用户资料实体类\")\n  public class UserProfile {\n\n      @InjectValue(source = \"db.properties\")\n      public String username;\n\n      @InjectValue\n      private int age;\n\n      @Loggable\n      public void displayProfile() {\n          System.out.println(\"Displaying user profile...\");\n      }\n\n      public void setAge(int age) {\n          this.age = age;\n      }\n  }\n  ```\n\n- **步骤三：编写反射解析器**\n\n  ```java\n  package com.example;\n  ```\n\n\n  import com.example.annotation.ApiDoc;\n  import com.example.annotation.InjectValue;\n  import com.example.annotation.Loggable;\n  import com.example.model.UserProfile;\n  ```\n\nimport java.lang.reflect.Field; import java.lang.reflect.Method;\n\n    public class Main {\n        public static void main(String[] args) {\n        // 获取UserProfile的Class对象\n            Class<UserProfile> clazz = UserProfile.class;\n\n            System.out.println(\"--- 1. 解析类上的注解 ---\");\n            if (clazz.isAnnotationPresent(ApiDoc.class)) {\n                ApiDoc apiDoc = clazz.getAnnotation(ApiDoc.class);\n            System.out.println(\"类文档注解: \" + apiDoc.value());\n            }\n\n            System.out.println(\"\\n--- 2. 解析字段上的注解 ---\");\n            // 遍历所有已声明的字段\n            for (Field field : clazz.getDeclaredFields()) {\n                if (field.isAnnotationPresent(InjectValue.class)) {\n                    InjectValue injectValue = field.getAnnotation(InjectValue.class);\n                    System.out.println(\"字段 '\" + field.getName() + \"' 需要从 '\" +\n                            injectValue.source() + \"' 注入值。\");\n            }\n            }\n\n            System.out.println(\"\\n--- 3. 解析方法上的注解 ---\");\n            // 遍历所有已声明的方法\n            for (Method method : clazz.getDeclaredMethods()) {\n                if (method.isAnnotationPresent(Loggable.class)) {\n                    System.out.println(\"方法 '\" + method.getName() + \"' 需要被日志记录。\");\n                }\n            }\n        }\n    }\n  ```\n\n**输出结果**:\n\n```\n--- 1. 解析类上的注解 ---\n类文档注解: 用户资料实体类\n\n--- 2. 解析字段上的注解 ---\n字段 'username' 需要从 'db.properties' 注入值。\n字段 'age' 需要从 'config.properties' 注入值。\n\n--- 3. 解析方法上的注解 ---\n方法 'displayProfile' 需要被日志记录。\n```\n\n---\n\n#### **5.3.6 [终极实战] 结合反射构建迷你ORM框架**\n\n这是注解与反射最经典的结合应用，它模拟了MyBatis等ORM框架的核心原理。\n\n> **目标**：编写一个程序，能够扫描指定包下的所有类，并为那些被`@Table`和`@Column`注解标记的类，自动生成SQL的\n> `CREATE TABLE` 语句。\n\n##### **步骤一：定义自定义注解 (`@Table` 和 `@Column`)**\n\n```java\npackage com.example.annotation;\n\nimport java.lang.annotation.*;\n\n// @Table注解，用于类上，指定表名\n@Target(ElementType.TYPE)\n@Retention(RetentionPolicy.RUNTIME)\npublic @interface Table {\n    String value(); // 用value属性来接收表名\n}\n```\n\n```java\npackage com.example.orm.annotations;\n\nimport java.lang.annotation.*;\n\n// @Column注解，用于字段上，指定列名和类型\n@Target(ElementType.FIELD)\n@Retention(RetentionPolicy.RUNTIME)\npublic @interface Column {\n    String name();\n    String type() default \"varchar(255)\"; // 默认类型为varchar\n    int length() default 255;\n}\n```\n\n##### **步骤二：创建被注解的实体类 (POJO)**\n\n```java\npackage com.example.orm.entities;\n\nimport com.example.orm.annotations.*;\n\n@Table(\"t_user\") // 指定该类对应的表名为 t_user\npublic class User {\n    @Column(name = \"id\", type = \"int\")\n    private int id;\n\n    @Column(name = \"user_name\")\n    private String username;\n    \n    @Column(name = \"user_age\", type = \"int\")\n    private int age;\n\n    // email字段没有注解，将被忽略\n    private String email;\n}\n```\n\n##### **步骤三：编写注解处理器（核心逻辑）**\n\n```java\npackage com.example;\n\nimport com.example.annotation.Column;\nimport com.example.annotation.Table;\n\nimport java.lang.reflect.Field;\n\npublic class Main {\n    public static String generateCreateTableSql(String className) throws Exception {\n        // 1. 获取Class对象\n        Class<?> clazz = Class.forName(className);\n        // 2. 检查类上是否有@Table注解\n        if (!clazz.isAnnotationPresent(Table.class)) {\n            return null; // 如果没有，则不是实体类，直接返回\n        }\n        // 3. 获取@Table注解并读取表名\n        Table tableAnnotation = clazz.getAnnotation(Table.class);\n        String tableName = tableAnnotation.value();\n\n        StringBuilder sql = new StringBuilder();\n        sql.append("CREATE TABLE ").append(tableName).append(" (\\n");\n\n        // 4. 遍历所有字段，查找@Column注解\n        for (Field field : clazz.getDeclaredFields()) {\n            if (field.isAnnotationPresent(Column.class)) {\n                // 5. 获取@Column注解并读取列名和类型\n                Column columnAnnotation = field.getAnnotation(Column.class);\n                String columnName = columnAnnotation.name();\n                String columnType = columnAnnotation.type();\n\n                sql.append("    ").append(columnName).append(" ").append(columnType).append(",\\n");\n            }\n        }\n        // 移除最后一个多余的逗号和换行符\n        if (sql.toString().contains(",")) {\n            sql.delete(sql.length() - 2, sql.length());\n        }\n\n        sql.append("\\n);");\n        return sql.toString();\n    }\n\n    public static void main(String[] args) throws Exception {\n        String userSql = generateCreateTableSql("com.example.entities.User");\n        System.out.println("--- 自动生成的User表SQL ---");\n        System.out.println(userSql);\n    }\n}\n```\n\n**输出结果**：\n\n```\n--- 自动生成的User表SQL ---\nCREATE TABLE t_user (\n    id int,\n    user_name varchar(255),s\n    user_age int\n);\n```\n\n这个综合案例完美展示了如何通过“**注解定义元数据 +\n反射读取元数据**”的模式，来构建强大、灵活的自动化框架。\n\n---\n'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#5-0-%E5%85%83%E7%BC%96%E7%A8%8B-%E5%8F%8D%E5%B0%84%E3%80%81%E6%B3%A8%E8%A7%A3"><span class="toc-number">1.</span> <span class="toc-text">5.0 [元编程] 反射、注解</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-%E5%9F%BA%E7%A1%80-%E7%B1%BB%E5%8A%A0%E8%BD%BD%E6%9C%BA%E5%88%B6%E4%B8%8E-ClassLoader"><span class="toc-number">1.1.</span> <span class="toc-text">5.1 [基础] 类加载机制与 ClassLoader</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5"><span class="toc-number">1.1.0.1.</span> <span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E7%9A%84%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F"><span class="toc-number">1.1.0.2.</span> <span class="toc-text">类的生命周期</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E5%8A%A0%E8%BD%BD%E5%99%A8-ClassLoaders-%E4%BD%93%E7%B3%BB"><span class="toc-number">1.1.0.3.</span> <span class="toc-text">类加载器 (ClassLoaders) 体系</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83-%E5%8F%8C%E4%BA%B2%E5%A7%94%E6%B4%BE%E6%A8%A1%E5%9E%8B-Parent-Delegation-Model"><span class="toc-number">1.1.0.4.</span> <span class="toc-text">[核心] 双亲委派模型 (Parent-Delegation Model)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%8E%B7%E5%8F%96%E7%B1%BB%E5%8A%A0%E8%BD%BD%E5%99%A8%E5%B9%B6%E6%9F%A5%E7%9C%8B%E5%85%B6%E5%B1%82%E7%BA%A7"><span class="toc-number">1.1.0.5.</span> <span class="toc-text">代码示例：获取类加载器并查看其层级</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-2-%E6%A0%B8%E5%BF%83-%E5%8F%8D%E5%B0%84%EF%BC%9A%E8%BF%90%E8%A1%8C%E6%97%B6%E5%8A%A8%E6%80%81%E6%93%8D%E6%8E%A7%E7%9A%84%E8%89%BA%E6%9C%AF"><span class="toc-number">1.2.</span> <span class="toc-text">5.2 [核心] 反射：运行时动态操控的艺术</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#5-2-1-%E4%BB%80%E4%B9%88%E6%98%AF%E5%8F%8D%E5%B0%84%E5%8F%8A%E5%85%B6%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.2.1.</span> <span class="toc-text">5.2.1 什么是反射及其应用场景</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-1"><span class="toc-number">1.2.1.1.</span> <span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5"><span class="toc-number">1.2.1.2.</span> <span class="toc-text">核心概念</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BC%98%E7%BC%BA%E7%82%B9"><span class="toc-number">1.2.1.3.</span> <span class="toc-text">优缺点</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.2.1.4.</span> <span class="toc-text">应用场景</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-2-2-%E5%8F%8D%E5%B0%84%E7%9A%84%E5%9F%BA%E7%9F%B3%EF%BC%9Ajava-lang-Class-%E5%AF%B9%E8%B1%A1"><span class="toc-number">1.2.2.</span> <span class="toc-text">5.2.2 反射的基石：java.lang.Class 对象</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%8E%B7%E5%8F%96Class%E5%AF%B9%E8%B1%A1%E7%9A%84%E4%B8%89%E7%A7%8D%E4%B8%BB%E8%A6%81%E6%96%B9%E5%BC%8F"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">获取Class对象的三种主要方式</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%8E%B7%E5%8F%96Class%E5%AF%B9%E8%B1%A1"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">代码示例：获取Class对象</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-2-3-%E9%80%9A%E8%BF%87%E5%8F%8D%E5%B0%84%E6%93%8D%E4%BD%9C%E7%B1%BB%E7%9A%84%E6%88%90%E5%91%98"><span class="toc-number">1.2.3.</span> <span class="toc-text">5.2.3 通过反射操作类的成员</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E6%93%8D%E4%BD%9C%E6%9E%84%E9%80%A0%E5%99%A8-Constructor"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">1. 操作构造器 (Constructor)</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%B0%83%E7%94%A8%E4%B8%8D%E5%90%8C%E7%9A%84%E6%9E%84%E9%80%A0%E5%99%A8"><span class="toc-number">1.2.3.1.1.</span> <span class="toc-text">代码示例：调用不同的构造器</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E6%93%8D%E4%BD%9C%E6%96%B9%E6%B3%95-Method"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">2. 操作方法 (Method)</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%B0%83%E7%94%A8%E5%90%84%E7%A7%8D%E6%96%B9%E6%B3%95"><span class="toc-number">1.2.3.2.1.</span> <span class="toc-text">代码示例：调用各种方法</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E6%93%8D%E4%BD%9C%E5%AD%97%E6%AE%B5-Field"><span class="toc-number">1.2.3.3.</span> <span class="toc-text">3. 操作字段 (Field)</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%AF%BB%E5%86%99%E5%AD%97%E6%AE%B5%E5%80%BC"><span class="toc-number">1.2.3.3.1.</span> <span class="toc-text">代码示例：读写字段值</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-2-4-%E5%AE%9E%E6%88%98-%E5%8F%8D%E5%B0%84%E7%9A%84%E5%BA%94%E7%94%A8%EF%BC%9A%E8%BF%B7%E4%BD%A0Spring%E6%A1%86%E6%9E%B6"><span class="toc-number">1.2.4.</span> <span class="toc-text">5.2.4 [实战] 反射的应用：迷你Spring框架</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-%E5%BA%94%E7%94%A8-%E6%B3%A8%E8%A7%A3%EF%BC%9A%E4%B8%BA%E4%BB%A3%E7%A0%81%E5%B5%8C%E5%85%A5%E5%85%83%E6%95%B0%E6%8D%AE"><span class="toc-number">1.3.</span> <span class="toc-text">5.3 [应用] 注解：为代码嵌入元数据</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-1-%E6%B3%A8%E8%A7%A3%E7%9A%84%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3"><span class="toc-number">1.3.1.</span> <span class="toc-text">5.3.1 注解的核心思想</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-2"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B3%A8%E8%A7%A3-vs-%E6%B3%A8%E9%87%8A"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">注解 vs. 注释</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B3%A8%E8%A7%A3%E7%9A%84%E9%87%8D%E8%A6%81%E6%80%A7%EF%BC%9A%E7%8E%B0%E4%BB%A3%E6%A1%86%E6%9E%B6%E7%9A%84%E5%9F%BA%E7%9F%B3"><span class="toc-number">1.3.1.3.</span> <span class="toc-text">注解的重要性：现代框架的基石</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-2-Java-%E5%86%85%E7%BD%AE%E6%B3%A8%E8%A7%A3"><span class="toc-number">1.3.2.</span> <span class="toc-text">5.3.2 Java 内置注解</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-3-%E8%87%AA%E5%AE%9A%E4%B9%89%E6%B3%A8%E8%A7%A3"><span class="toc-number">1.3.3.</span> <span class="toc-text">5.3.3 自定义注解</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%AE%9A%E4%B9%89%E6%B3%A8%E8%A7%A3%E4%B8%8E%E5%B1%9E%E6%80%A7"><span class="toc-number">1.3.3.1.</span> <span class="toc-text">定义注解与属性</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E5%AE%9A%E4%B9%89%E4%B8%80%E4%B8%AA%E5%A4%8D%E6%9D%82%E7%9A%84%E6%95%B0%E6%8D%AE%E5%BA%93%E4%BF%A1%E6%81%AF%E6%B3%A8%E8%A7%A3"><span class="toc-number">1.3.3.2.</span> <span class="toc-text">代码示例：定义一个复杂的数据库信息注解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BD%BF%E7%94%A8%E6%B3%A8%E8%A7%A3%E4%B8%8E%E5%B1%9E%E6%80%A7%E8%B5%8B%E5%80%BC"><span class="toc-number">1.3.3.3.</span> <span class="toc-text">使用注解与属性赋值</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-4-%E5%85%83%E6%B3%A8%E8%A7%A3%EF%BC%9A%E6%B3%A8%E8%A7%A3%E7%9A%84%E2%80%9C%E9%85%8D%E7%BD%AE%E2%80%9D"><span class="toc-number">1.3.4.</span> <span class="toc-text">5.3.4 元注解：注解的“配置”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-5-%E5%8F%8D%E5%B0%84%E8%A7%A3%E6%9E%90%E6%B3%A8%E8%A7%A3%EF%BC%9A%E8%AF%BB%E5%8F%96%E5%85%83%E6%95%B0%E6%8D%AE%E7%9A%84API"><span class="toc-number">1.3.5.</span> <span class="toc-text">5.3.5 反射解析注解：读取元数据的API</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-3"><span class="toc-number">1.3.5.1.</span> <span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%8E%A5%E5%8F%A3%EF%BC%9AAnnotatedElement"><span class="toc-number">1.3.5.2.</span> <span class="toc-text">核心接口：AnnotatedElement</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-number">1.3.5.3.</span> <span class="toc-text">核心方法速查表</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E7%B3%BB%E7%BB%9F%E6%80%A7%E5%9C%B0%E8%A7%A3%E6%9E%90%E4%B8%80%E4%B8%AA%E7%B1%BB%E4%B8%8A%E7%9A%84%E6%89%80%E6%9C%89%E6%B3%A8%E8%A7%A3"><span class="toc-number">1.3.5.4.</span> <span class="toc-text">代码示例：系统性地解析一个类上的所有注解</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-3-6-%E7%BB%88%E6%9E%81%E5%AE%9E%E6%88%98-%E7%BB%93%E5%90%88%E5%8F%8D%E5%B0%84%E6%9E%84%E5%BB%BA%E8%BF%B7%E4%BD%A0ORM%E6%A1%86%E6%9E%B6"><span class="toc-number">1.3.6.</span> <span class="toc-text">5.3.6 [终极实战] 结合反射构建迷你ORM框架</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A4%E4%B8%80%EF%BC%9A%E5%AE%9A%E4%B9%89%E8%87%AA%E5%AE%9A%E4%B9%89%E6%B3%A8%E8%A7%A3-Table-%E5%92%8C-Column"><span class="toc-number">1.3.6.1.</span> <span class="toc-text">步骤一：定义自定义注解 (@Table 和 @Column)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A4%E4%BA%8C%EF%BC%9A%E5%88%9B%E5%BB%BA%E8%A2%AB%E6%B3%A8%E8%A7%A3%E7%9A%84%E5%AE%9E%E4%BD%93%E7%B1%BB-POJO"><span class="toc-number">1.3.6.2.</span> <span class="toc-text">步骤二：创建被注解的实体类 (POJO)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A4%E4%B8%89%EF%BC%9A%E7%BC%96%E5%86%99%E6%B3%A8%E8%A7%A3%E5%A4%84%E7%90%86%E5%99%A8%EF%BC%88%E6%A0%B8%E5%BF%83%E9%80%BB%E8%BE%91%EF%BC%89"><span class="toc-number">1.3.6.3.</span> <span class="toc-text">步骤三：编写注解处理器（核心逻辑）</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>