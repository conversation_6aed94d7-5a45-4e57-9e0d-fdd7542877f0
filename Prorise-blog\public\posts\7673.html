<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理入门（十）：第十章：产品研发全流程管理 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理入门（十）：第十章：产品研发全流程管理"><meta name="application-name" content="产品经理入门（十）：第十章：产品研发全流程管理"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理入门（十）：第十章：产品研发全流程管理"><meta property="og:url" content="https://prorise666.site/posts/7673.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第十章：产品研发全流程管理到目前为止，我们已经作为产品经理，完成了从需求分析到方案设计的核心工作。一个包含了 PRD 和交互原型的完整方案，已经躺在了我们的电脑里。 那么接下来的问题是：然后呢？我们要如何推动这个方案，一步步地变成一个能被用户真实使用的、上线的活产品？ 这就是本章要解决的问题。在这里"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta name="description" content="第十章：产品研发全流程管理到目前为止，我们已经作为产品经理，完成了从需求分析到方案设计的核心工作。一个包含了 PRD 和交互原型的完整方案，已经躺在了我们的电脑里。 那么接下来的问题是：然后呢？我们要如何推动这个方案，一步步地变成一个能被用户真实使用的、上线的活产品？ 这就是本章要解决的问题。在这里"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/7673.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理入门（十）：第十章：产品研发全流程管理",postAI:"true",pageFillDescription:"第十章：产品研发全流程管理, 10.1 产品生产发布流程, 10.1.1 学习目标, 10.1.2 团队协作与成员职责（产品、UI、开发、测试）, 1. 产品经理 (Product Manager) - 流程的大脑与发起者, 2. UI 设计师 (UI Designer) - 产品的化妆师, 3. 研发工程师 (Developer) - 产品的建造者, 4. 测试工程师 (Tester) - 产品的守门员, 10.1.3 文档交付与评审流程, 10.1.4 测试流程（冒烟测试、回归测试等）, 10.1.5 验收方式与上线流程, 1. 产品验收, 2. 上线流程, 10.1.6 本节小结, 10.2 项目管理, 10.2.1 学习目标, 10.2.2 项目管理的定义与目标（时间与质量）, 10.2.3 管理方式（例会、里程碑、进度检查）, 1. 每日例会, 2. 里程碑, 3. 进度检查, 10.2.4 项目管理工具（甘特图、TAPD、禅道等）, 1. 甘特图 (Gantt Chart), 2. TAPD x2F 禅道 x2F Jira, 10.2.5 本节小结, 10.3 产品需求评审, 10.3.1 学习目标, 10.3.2 需求类型（业务需求、功能需求）, 10.3.3 会议流程（准备、过程、会后跟踪）, 10.3.4 评审结构与讲解内容（背景、原型、其他需求）, 10.3.5 评审要点（时间与节奏控制、控场与主见、结论收尾）, 10.4 本章总结, 10.4.1 课程内容回顾第十章产品研发全流程管理到目前为止我们已经作为产品经理完成了从需求分析到方案设计的核心工作一个包含了和交互原型的完整方案已经躺在了我们的电脑里那么接下来的问题是然后呢我们要如何推动这个方案一步步地变成一个能被用户真实使用的上线的活产品这就是本章要解决的问题在这里我将带大家跳出产品经理的单一角色以一个项目指挥官的视角来学习如何驱动一个完整的团队协同作战最终打赢一场漂亮的产品发布战役产品生产发布流程产品的生产发布从来不是一个人的战斗而是一场需要多兵种角色协同的接力赛我面前的这张泳道图就是我们这场接力赛的核心作战地图它清晰地展示了一个产品从诞生到上线需要经历方案设计产品研发验收上线这三大阶段以及产品经理设计师程序员测试这四个核心角色是如何在这场接力赛中依次交棒紧密协作的学习目标在本节中我的目标是带大家清晰地理解这场接力赛的规则我们将深入学习团队中每一个核心角色的职责并重点掌握我作为产品经理是如何与他们进行高效协作以及如何管理关键的文档交付与评审流程的团队协作与成员职责产品开发测试要打赢一场仗首先要了解我们的战友产品经理流程的大脑与发起者我的角色是整个流程的和的定义者我的职责正如泳道图所示整个流程由我发起我负责收集分析需求并最终输出方案和原型我是产品方向的掌舵人是所有后续工作的需求源头设计师产品的化妆师设计师是产品长什么样的专家他的职责是把我的低保真原型产品的骨架进行专业的视觉设计输出包含色彩图标字体的高保真视觉效果图产品的皮肤让产品变得美观有吸引力我与的协作我与设计师的协作遵循讲解验收两步走讲解在交付原型时我必须召开会议向他详细讲解我的设计背后的需求背景业务目的和核心逻辑验收在稿完成后我需要严格地进行视觉验收确保他的设计不仅美观更重要的是完全符合我想要传达的产品目标和用户体验研发工程师产品的建造者研发工程师是产品如何工作的实现者他的职责他们是把我们的设计图纸用一行行代码变成一个真实可用的产品的建筑师他们通常分为前端开发负责实现用户能直接看到和交互的界面后端开发负责实现支撑前端运转的服务器数据库和业务逻辑我与研发的协作我与研发工程师的协作是整个流程中最核心最需要严谨性的环节文档交付我必须提供清晰完整无歧义的和原型评审排期我必须组织正式的需求评审会确保所有研发人员都对需求理解一致评审通过后再共同制定开发排期项目管理在开发过程中我需要持续跟进进度解答疑问管理变更测试工程师产品的守门员测试工程师是产品是否正确的捍卫者他的职责在产品开发完成后他们会严格地按照我的对产品进行全面的产品测试找出所有潜在的和与需求不符的地方是保障产品质量的最后一道也是最重要的一道防线文档交付与评审流程在我完成了和原型的撰写后就进入了至关重要的交棒环节最传统的模式是一种单线程瀑布流我做完方案交给做设计设计完再一起开评审会然后开发才开始工作这种模式虽然稳妥但它的弊端也很明显效率低下各个角色之间是串行等待浪费了大量时间因此在我的实践中我极力推行一种更高效的并行开发模式我完成和低保真原型后立刻组织需求评审会评审会通过后工作就可以兵分两路同时进行设计师开始基于我的低保真原型进行高保真视觉设计后端开发工程师完全不需要等待稿他们可以根据我的和低保真原型中的逻辑立刻开始进行接口开发和数据库设计前端开发工程师可以先根据和接口文档搭建前端项目的框架等待稿一到就可以立刻填充页面并与后端进行接口联调这种并行模式能极大地缩短项目周期而实现它的核心就在于一份清晰无歧义的以及一场成功的需求评审会测试流程冒烟测试回归测试等当开发工程师完成了一个功能模块的开发并提交到测试环境后我们的守门员测试工程师就要登场了我的拓展设计模块化测试对于一个较大的版本我不会等到所有功能都开发完再统一移交测试我会和技术负责人一起将整个版本拆分为几个独立的模块开发团队每完成一个模块就立刻移交给测试团队进行测试这样开发一个测试一个的模式能让测试工作尽早介入提前暴露和解决问题避免所有问题都堆积到项目后期导致项目延期在整个测试环节我会特别关注两种核心的测试类型冒烟测试这是测试的第一步当开发同学部署了一个新的测试版本后测试同学会花很短的时间比如分钟对这个版本最核心最基本的功能如登录首页加载等进行一次快速验证我的理解冒烟测试就像我们拿到一个电器先插上电看看会不会冒烟如果连最基本的功能都跑不通直冒烟那这个版本就是不合格的会立刻打回给开发无需浪费时间进行更详细的测试回归测试这是保障产品质量最关键的一环当开发同学修复了一个或者增加了一个新功能后测试同学不仅要测试这个修改点还必须重新去测试那些原本没有问题的相关的旧功能我的理解回归测试的目的是为了防止按下葫芦浮起瓢我们要确保新的代码没有意外地破坏掉旧代码的正常逻辑验收方式与上线流程当测试团队确认产品已达到上线标准即没有严重的后就轮到我这个产品经理进行最后一道关卡的把控产品验收产品验收验收方式口头验收对于一些非常小的非核心的改动我可能会在测试通过后自己快速体验一下然后在工作群里回复一句确认即可文档验收对于核心功能或重要版本我一定会按照整理出一份详细的验收清单然后逐项地严格地进行验收测试验收结果验收通过功能符合的核心要求没有重大问题一些不影响主流程的微小的体验瑕疵我可以同意放到下个版本再优化验收不通过功能的核心逻辑流程与我的设计严重不符此时我有权打回重做要求研发团队返工直到满足需求为止上线流程当我验收通过给出的指令后正式的上线流程就启动了这是一个需要多方协作的过程我的职责产品侧确定版本号为即将上线的新版本确定一个唯一的符合规范的版本号如确定更新内容文案撰写将在应用商店里展示给用户看的更新日志可选组织培训撰写手册如果功能比较复杂我还需要为客服或运营同事准备培训材料或使用手册研发的职责开发侧提交应用商店审核由研发同学将最终的安装包提交给苹果华为应用市场等各大渠道进行审核择期发布在应用商店审核通过后我们会共同商定一个合适的时机比如用户活跃度较低的凌晨进行正式的线上发布本节小结阶段我的核心角色与职责交付与评审作为讲解员组织需求评审会确保团队对需求理解一致并推动更高效的并行开发模式测试作为信息枢纽关注测试进度特别是冒烟测试和回归测试的结果确保产品质量验收与上线作为最终决策者进行产品验收并准备好版本号更新文案等上线所需材料打好临门一脚项目管理在很多公司项目管理和产品管理是两个独立的岗位但在更多敏捷的互联网团队里我作为产品经理通常也需要承担起项目经理的职责即便有专门的项目经理我作为产品的依然是项目成败的最终负责人因此掌握项目管理的基本方法和工具是我的必备技能学习目标在本节中我的目标是带大家掌握产品经理视角下的项目管理核心我们将学习项目管理的目标以及我最常用来达成这些目标的管理方式和管理工具项目管理的定义与目标时间与质量我理解的项目管理就是在产品开发过程中监督和管理整个研发团队包括协调资源处理矛盾监督工期等以确保项目能按期按需高质量地成功上线在整个过程中我最核心的两个目标就是守护好时间和质量这两个生命线时间确保项目按照我们共同制定的排期表准时交付质量确保最终交付的产品功能完整体验流畅严格符合中的要求管理方式例会里程碑进度检查为了管好时间和质量我不会等到项目快结束时才去关心而是会通过一系列的管理仪式将管理工作贯穿于整个研发周期每日例会这是敏捷开发中最核心的仪式每天早上我会把开发和测试的核心成员召集起来开一个不超过分钟的站会每个人只需要回答三个问题昨天做了什么今天准备做什么遇到了什么困难需要我协调解决每日例会是我获取项目一线信息发现潜在风险的最重要的途径里程碑对于一个超过两周的项目我一定会将它拆解为几个关键的里程碑里程碑不是一个简单的日期而是一个明确的可交付的阶段性成果定义清晰的里程碑能帮助我从宏观上把控项目的整体节奏也便于我向管理层汇报进度进度检查这是我日常的持续性的工作它包括与团队成员进行一对一的沟通在项目管理工具上检查任务的完成状态主动识别可能导致延期的风险并尽我所能地为团队扫清障碍项目管理工具甘特图禅道等要落地上述的管理方式我必须借助专业的工具甘特图甘特图是我进行项目长期规划和排期的首选工具它是一个强大的时间轴视图能让我清晰地看到项目包含哪些任务每个任务的开始和结束时间任务之间的依赖关系是怎样的比如任务不完成任务就无法开始每个任务的负责人是谁我通常会在项目启动时和技术负责人一起制定出一份详细的甘特图作为我们整个项目的时间规划蓝图禅道这类工具是我进行日常微观的任务跟踪的核心甘特图告诉我们长期的路要怎么走而禅道这类工具则告诉我们今天的每一步要怎么走我主要用它们来实现创建和分配任务将中的功能点拆解为一个个具体的开发任务并指派给对应的工程师追踪任务状态通过任务看板的形式将所有任务的状态如待处理进行中已完成可视化团队进展一目了然管理测试团队会在这里提交指派和跟踪所有的修复过程文档协作作为我们文档等核心文档的存放和协作平台本节小结管理维度我的核心方法我常用的工具日常同步每日例会禅道的任务看板长期规划里程碑规划甘特图风险控制持续的进度检查项目周报一对一沟通产品需求评审在我看来产品需求评审会是整个研发流程中最重要的一个沟通仪式它是我作为产品经理将我的作战计划和原型正式地全面地同步给我的作战部队设计研发测试团队的起点一场成功的评审会能让整个团队对目标形成统一清晰的认知从而极大地提升后续的研发效率避免返工而一场失败的评审会则会埋下无数的坑导致后续开发过程中的无尽扯皮和延期学习目标在本节中我的目标是带大家掌握如何组织和主导一场成功的需求评审会我们将学习评审会的不同类型标准的会议流程高效的讲解内容结构以及我作为会议主持人必须掌握的控场技巧和要点需求类型业务需求功能需求在组织评审会前我首先要明确这次会议的类型我通常会把需求评审分为两种业务需求评审这通常发生在项目的极早期参会人员是老板业务负责人技术负责人等高阶决策者会议的核心是评审和探讨本次需求的商业价值需求范围技术可行性版本规划等战略层面的问题功能需求评审这通常发生在我们已经完成详细和原型即将进入研发阶段时参会人员是开发测试设计师等一线的执行团队会议的核心是讲解功能实现的每一个细节确保团队对需求理解无误我们本节后续讨论的主要就是功能需求评审会议流程准备过程会后跟踪一场成功的会议其功夫往往在会前和会后我严格遵循一个四阶段的会议流程会议阶段我的关键动作预约会议我会至少提前天发出会议邀请并必须在邀请中附上本次评审的和原型链接要求所有参会者务必会前阅读会前准备我会提前进入会议室确保投影网络等设备一切正常并将我的讲解材料准备就绪会议过程这是我的主场我会严格按照预设的结构和节奏进行讲解和讨论具体见下文会后跟踪会议结束后半小时内我会发出会议纪要清晰地列出会议结论遗留问题和下一步的行动计划及负责人并持续跟踪这些问题的解决评审结构与讲解内容背景原型其他需求在评审会中我的讲解会像讲一个故事一样遵循一个清晰有吸引力的结构需求背景目的我总是从为什么开始用分钟清晰地向团队交代本次需求的来源要解决的用户痛点和期望达成的商业目标这能让团队在后续的讨论中始终与初心对齐流程结构接着我会快速地展示本次需求相关的流程图和结构图让团队对这个功能在整个产品中的位置和骨架有一个宏观的认知原型讲解这是会议的核心部分我会打开我的交互原型从第一个页面开始逐一地详细地讲解每一个页面的布局每一个控件的交互和其背后的所有业务规则其他需求最后我会讲解中定义的非功能性需求比如性能要求数据埋点需求兼容性要求等评审要点时间与节奏控制控场与主见结论收尾作为会议的主持人我的控场能力直接决定了会议的成败我时刻关注以下几点我的控场要点具体做法时间与节奏控制我会严格将会议控制在小时内在讲解中我会每隔分钟就主动停下来问到这里大家有什么问题吗以保持互动避免我一个人一言堂控场与主次划分我会时刻注意评审的主题当讨论陷入过深的技术细节或跑偏时我会礼貌地打断并建议这个问题非常好我们线下再拉个小会深入讨论然后把会议拉回主线我会重点讲解流程复杂或有争议的地方讨论与主见我会鼓励团队提出质疑这是发现方案漏洞的好机会但对于已经深思熟虑关系到核心需求的点我也会有理有据地坚持自己的主见不能被轻易带偏收尾确定会议结束前我必须得到一个明确的结论本次评审是通过通过但有待办项还是不通过需重大修改并明确后续的和时间点绝不能开成一个没有结论的聊天会本章总结课程内容回顾在本章我们学习了如何将一个已经设计好的产品方案一步步地推向最终的成功发布产品生产发布流程我们了解了产品开发测试这四个核心角色的职责以及他们之间环环相扣的协作流程项目管理我们学习了作为产品经理如何通过例会里程碑等方式以及甘特图等工具来管理好项目的时间和质量产品需求评审我们深入地学习了如何组织和主导一场专业高效的需求评审会这是我们作为产品经理最重要的软技能之一到这里我们已经走完了从一个模糊的想法到一个上线产品的全过程恭喜你完成了本次的学习",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-21 14:52:13",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E7%AB%A0%EF%BC%9A%E4%BA%A7%E5%93%81%E7%A0%94%E5%8F%91%E5%85%A8%E6%B5%81%E7%A8%8B%E7%AE%A1%E7%90%86"><span class="toc-text">第十章：产品研发全流程管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#10-1-%E4%BA%A7%E5%93%81%E7%94%9F%E4%BA%A7%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B"><span class="toc-text">10.1 产品生产发布流程</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">10.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-2-%E5%9B%A2%E9%98%9F%E5%8D%8F%E4%BD%9C%E4%B8%8E%E6%88%90%E5%91%98%E8%81%8C%E8%B4%A3%EF%BC%88%E4%BA%A7%E5%93%81%E3%80%81UI%E3%80%81%E5%BC%80%E5%8F%91%E3%80%81%E6%B5%8B%E8%AF%95%EF%BC%89"><span class="toc-text">10.1.2 团队协作与成员职责（产品、UI、开发、测试）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86-Product-Manager-%E6%B5%81%E7%A8%8B%E7%9A%84%E2%80%9C%E5%A4%A7%E8%84%91%E2%80%9D%E4%B8%8E%E2%80%9C%E5%8F%91%E8%B5%B7%E8%80%85%E2%80%9D"><span class="toc-text">1. 产品经理 (Product Manager) - 流程的“大脑”与“发起者”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-UI-%E8%AE%BE%E8%AE%A1%E5%B8%88-UI-Designer-%E4%BA%A7%E5%93%81%E7%9A%84%E2%80%9C%E5%8C%96%E5%A6%86%E5%B8%88%E2%80%9D"><span class="toc-text">2. UI 设计师 (UI Designer) - 产品的“化妆师”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%A0%94%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88-Developer-%E4%BA%A7%E5%93%81%E7%9A%84%E2%80%9C%E5%BB%BA%E9%80%A0%E8%80%85%E2%80%9D"><span class="toc-text">3. 研发工程师 (Developer) - 产品的“建造者”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E6%B5%8B%E8%AF%95%E5%B7%A5%E7%A8%8B%E5%B8%88-Tester-%E4%BA%A7%E5%93%81%E7%9A%84%E2%80%9C%E5%AE%88%E9%97%A8%E5%91%98%E2%80%9D"><span class="toc-text">4. 测试工程师 (Tester) - 产品的“守门员”</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-3-%E6%96%87%E6%A1%A3%E4%BA%A4%E4%BB%98%E4%B8%8E%E8%AF%84%E5%AE%A1%E6%B5%81%E7%A8%8B"><span class="toc-text">10.1.3 文档交付与评审流程</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-4-%E6%B5%8B%E8%AF%95%E6%B5%81%E7%A8%8B%EF%BC%88%E5%86%92%E7%83%9F%E6%B5%8B%E8%AF%95%E3%80%81%E5%9B%9E%E5%BD%92%E6%B5%8B%E8%AF%95%E7%AD%89%EF%BC%89"><span class="toc-text">10.1.4 测试流程（冒烟测试、回归测试等）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-5-%E9%AA%8C%E6%94%B6%E6%96%B9%E5%BC%8F%E4%B8%8E%E4%B8%8A%E7%BA%BF%E6%B5%81%E7%A8%8B"><span class="toc-text">10.1.5 验收方式与上线流程</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%A7%E5%93%81%E9%AA%8C%E6%94%B6"><span class="toc-text">1. 产品验收</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%B8%8A%E7%BA%BF%E6%B5%81%E7%A8%8B"><span class="toc-text">2. 上线流程</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-6-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">10.1.6 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-2-%E9%A1%B9%E7%9B%AE%E7%AE%A1%E7%90%86"><span class="toc-text">10.2 项目管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">10.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-2-%E9%A1%B9%E7%9B%AE%E7%AE%A1%E7%90%86%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E7%9B%AE%E6%A0%87%EF%BC%88%E6%97%B6%E9%97%B4%E4%B8%8E%E8%B4%A8%E9%87%8F%EF%BC%89"><span class="toc-text">10.2.2 项目管理的定义与目标（时间与质量）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-3-%E7%AE%A1%E7%90%86%E6%96%B9%E5%BC%8F%EF%BC%88%E4%BE%8B%E4%BC%9A%E3%80%81%E9%87%8C%E7%A8%8B%E7%A2%91%E3%80%81%E8%BF%9B%E5%BA%A6%E6%A3%80%E6%9F%A5%EF%BC%89"><span class="toc-text">10.2.3 管理方式（例会、里程碑、进度检查）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%AF%8F%E6%97%A5%E4%BE%8B%E4%BC%9A"><span class="toc-text">1. 每日例会</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E9%87%8C%E7%A8%8B%E7%A2%91"><span class="toc-text">2. 里程碑</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E8%BF%9B%E5%BA%A6%E6%A3%80%E6%9F%A5"><span class="toc-text">3. 进度检查</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-4-%E9%A1%B9%E7%9B%AE%E7%AE%A1%E7%90%86%E5%B7%A5%E5%85%B7%EF%BC%88%E7%94%98%E7%89%B9%E5%9B%BE%E3%80%81TAPD%E3%80%81%E7%A6%85%E9%81%93%E7%AD%89%EF%BC%89"><span class="toc-text">10.2.4 项目管理工具（甘特图、TAPD、禅道等）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%94%98%E7%89%B9%E5%9B%BE-Gantt-Chart"><span class="toc-text">1. 甘特图 (Gantt Chart)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-TAPD-%E7%A6%85%E9%81%93-Jira"><span class="toc-text">2. TAPD / 禅道 / Jira</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">10.2.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-3-%E4%BA%A7%E5%93%81%E9%9C%80%E6%B1%82%E8%AF%84%E5%AE%A1"><span class="toc-text">10.3 产品需求评审</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">10.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-2-%E9%9C%80%E6%B1%82%E7%B1%BB%E5%9E%8B%EF%BC%88%E4%B8%9A%E5%8A%A1%E9%9C%80%E6%B1%82%E3%80%81%E5%8A%9F%E8%83%BD%E9%9C%80%E6%B1%82%EF%BC%89"><span class="toc-text">10.3.2 需求类型（业务需求、功能需求）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-3-%E4%BC%9A%E8%AE%AE%E6%B5%81%E7%A8%8B%EF%BC%88%E5%87%86%E5%A4%87%E3%80%81%E8%BF%87%E7%A8%8B%E3%80%81%E4%BC%9A%E5%90%8E%E8%B7%9F%E8%B8%AA%EF%BC%89"><span class="toc-text">10.3.3 会议流程（准备、过程、会后跟踪）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-4-%E8%AF%84%E5%AE%A1%E7%BB%93%E6%9E%84%E4%B8%8E%E8%AE%B2%E8%A7%A3%E5%86%85%E5%AE%B9%EF%BC%88%E8%83%8C%E6%99%AF%E3%80%81%E5%8E%9F%E5%9E%8B%E3%80%81%E5%85%B6%E4%BB%96%E9%9C%80%E6%B1%82%EF%BC%89"><span class="toc-text">10.3.4 评审结构与讲解内容（背景、原型、其他需求）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-5-%E8%AF%84%E5%AE%A1%E8%A6%81%E7%82%B9%EF%BC%88%E6%97%B6%E9%97%B4%E4%B8%8E%E8%8A%82%E5%A5%8F%E6%8E%A7%E5%88%B6%E3%80%81%E6%8E%A7%E5%9C%BA%E4%B8%8E%E4%B8%BB%E8%A7%81%E3%80%81%E7%BB%93%E8%AE%BA%E6%94%B6%E5%B0%BE%EF%BC%89"><span class="toc-text">10.3.5 评审要点（时间与节奏控制、控场与主见、结论收尾）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-4-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">10.4 本章总结</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-1-%E8%AF%BE%E7%A8%8B%E5%86%85%E5%AE%B9%E5%9B%9E%E9%A1%BE"><span class="toc-text">10.4.1 课程内容回顾</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5f2a23">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#277340">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#c72008">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#11a7a2">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#276d10">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#6d6a95">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理入门（十）：第十章：产品研发全流程管理</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-20T17:13:45.000Z" title="发表于 2025-07-21 01:13:45">2025-07-21</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:13.515Z" title="更新于 2025-07-21 14:52:13">2025-07-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">5.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>16分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理入门（十）：第十章：产品研发全流程管理"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/7673.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/7673.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理入门（十）：第十章：产品研发全流程管理</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-20T17:13:45.000Z" title="发表于 2025-07-21 01:13:45">2025-07-21</time><time itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:13.515Z" title="更新于 2025-07-21 14:52:13">2025-07-21</time></header><div id="postchat_postcontent"><h1 id="第十章：产品研发全流程管理"><a href="#第十章：产品研发全流程管理" class="headerlink" title="第十章：产品研发全流程管理"></a>第十章：产品研发全流程管理</h1><p>到目前为止，我们已经作为产品经理，完成了从需求分析到方案设计的核心工作。一个包含了 PRD 和交互原型的完整方案，已经躺在了我们的电脑里。</p><p>那么接下来的问题是：<strong>然后呢？我们要如何推动这个方案，一步步地变成一个能被用户真实使用的、上线的活产品？</strong></p><p>这就是本章要解决的问题。在这里，我将带大家跳出产品经理的单一角色，以一个“<strong>项目指挥官</strong>”的视角，来学习如何驱动一个完整的团队，协同作战，最终打赢一场漂亮的产品发布战役。</p><h2 id="10-1-产品生产发布流程"><a href="#10-1-产品生产发布流程" class="headerlink" title="10.1 产品生产发布流程"></a>10.1 产品生产发布流程</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140058419.png" alt="image-20250721140058419"></p><p>产品的生产发布，从来不是一个人的战斗，而是一场需要多兵种（角色）协同的“<strong>接力赛</strong>”。我面前的这张“泳道图”，就是我们这场接力赛的 <strong>核心作战地图</strong>。</p><p>它清晰地展示了，一个产品从诞生到上线，需要经历 <strong>方案设计、产品研发、验收上线</strong> 这三大阶段，以及 <strong>产品经理、UI 设计师、程序员、测试</strong> 这四个核心角色，是如何在这场接力赛中，依次交棒、紧密协作的。</p><h3 id="10-1-1-学习目标"><a href="#10-1-1-学习目标" class="headerlink" title="10.1.1 学习目标"></a>10.1.1 学习目标</h3><p>在本节中，我的目标是带大家清晰地理解这场“接力赛”的规则。我们将深入学习团队中每一个核心角色的职责，并重点掌握我作为产品经理，是如何与他们进行高效协作，以及如何管理关键的文档交付与评审流程的。</p><h3 id="10-1-2-团队协作与成员职责（产品、UI、开发、测试）"><a href="#10-1-2-团队协作与成员职责（产品、UI、开发、测试）" class="headerlink" title="10.1.2 团队协作与成员职责（产品、UI、开发、测试）"></a>10.1.2 团队协作与成员职责（产品、UI、开发、测试）</h3><p>要打赢一场仗，首先要了解我们的战友。</p><h4 id="1-产品经理-Product-Manager-流程的“大脑”与“发起者”"><a href="#1-产品经理-Product-Manager-流程的“大脑”与“发起者”" class="headerlink" title="1. 产品经理 (Product Manager) - 流程的“大脑”与“发起者”"></a>1. 产品经理 (Product Manager) - 流程的“大脑”与“发起者”</h4><p>我的角色，是整个流程的 <strong>“Why”和“What”的定义者</strong>。</p><ul><li><strong>我的职责</strong>：正如泳道图所示，整个流程由我发起。我负责 <code>收集分析需求</code>，并最终 <code>输出方案</code>（PRD 和原型）。我是产品方向的掌舵人，是所有后续工作的“需求源头”。</li></ul><h4 id="2-UI-设计师-UI-Designer-产品的“化妆师”"><a href="#2-UI-设计师-UI-Designer-产品的“化妆师”" class="headerlink" title="2. UI 设计师 (UI Designer) - 产品的“化妆师”"></a>2. UI 设计师 (UI Designer) - 产品的“化妆师”</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140204539.png" alt="image-20250721140204539"></p><p>UI 设计师，是 <strong>“产品长什么样（How it Looks）”的专家</strong>。</p><ul><li><strong>他的职责</strong>：是把我的低保真原型（产品的“骨架”），进行专业的视觉设计，输出包含色彩、图标、字体的 <strong>高保真视觉效果图</strong>（产品的“皮肤”），让产品变得美观、有吸引力。</li><li><strong>我与 UI 的协作</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140235402.png" alt="image-20250721140235402"><br>我与 UI 设计师的协作，遵循“讲解-验收”两步走：<ol><li><strong>讲解</strong>：在交付原型时，我必须召开会议，向他详细讲解我的设计背后的 <strong>需求背景、业务目的和核心逻辑</strong>。</li><li><strong>验收</strong>：在 UI 稿完成后，我需要严格地进行 <strong>视觉验收</strong>，确保他的设计不仅美观，更重要的是，完全符合我想要传达的产品目标和用户体验。</li></ol></li></ul><h4 id="3-研发工程师-Developer-产品的“建造者”"><a href="#3-研发工程师-Developer-产品的“建造者”" class="headerlink" title="3. 研发工程师 (Developer) - 产品的“建造者”"></a>3. 研发工程师 (Developer) - 产品的“建造者”</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140327008.png" alt="image-20250721140327008"></p><p>研发工程师，是 <strong>“产品如何工作（How it Works）”的实现者</strong>。</p><ul><li><strong>他的职责</strong>：他们是把我们的设计图纸，用一行行代码，变成一个真实可用的产品的“建筑师”。他们通常分为：<ul><li><strong>前端开发</strong>：负责实现用户能直接看到和交互的界面。</li><li><strong>后端开发</strong>：负责实现支撑前端运转的服务器、数据库和业务逻辑。</li></ul></li><li><strong>我与研发的协作</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140352470.png" alt="image-20250721140352470"><br>我与研发工程师的协作，是整个流程中最核心、最需要严谨性的环节：<ol><li><strong>文档交付</strong>：我必须提供清晰、完整、无歧义的 PRD 和原型。</li><li><strong>评审排期</strong>：我必须组织正式的“<strong>需求评审会</strong>”，确保所有研发人员都对需求理解一致。评审通过后，再共同制定开发排期。</li><li><strong>项目管理</strong>：在开发过程中，我需要持续跟进进度，解答疑问，管理变更。</li></ol></li></ul><h4 id="4-测试工程师-Tester-产品的“守门员”"><a href="#4-测试工程师-Tester-产品的“守门员”" class="headerlink" title="4. 测试工程师 (Tester) - 产品的“守门员”"></a>4. 测试工程师 (Tester) - 产品的“守门员”</h4><p>测试工程师，是 <code>“产品是否正确（Is it Right）”</code> 的捍卫者。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140748356.png" alt="image-20250721140748356"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140450191.png" alt="image-20250721140450191"></p><ul><li><strong>他的职责</strong>：在产品开发完成后，他们会严格地按照我的 PRD，对产品进行全面的 <strong>产品测试</strong>，找出所有潜在的 Bug 和与需求不符的地方，是保障产品质量的最后一道，也是最重要的一道防线。</li></ul><hr><h3 id="10-1-3-文档交付与评审流程"><a href="#10-1-3-文档交付与评审流程" class="headerlink" title="10.1.3 文档交付与评审流程"></a>10.1.3 文档交付与评审流程</h3><p>在我完成了 PRD 和原型的撰写后，就进入了至关重要的“<strong>交棒</strong>”环节。</p><p>最传统的模式，是一种“<strong>单线程瀑布流</strong>”：我做完方案 -&gt; 交给 UI 做设计 -&gt; UI 设计完 -&gt; 再一起开评审会 -&gt; 然后开发才开始工作。这种模式虽然稳妥，但它的弊端也很明显：<strong>效率低下</strong>，各个角色之间是“串行”等待，浪费了大量时间。</p><p>因此，在我的实践中，我极力推行一种更高效的“<strong>并行开发</strong>”模式：</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721141211224.png" alt="image-20250721141211224"></p><ol><li><strong>我完成 PRD 和“低保真”原型后，立刻组织需求评审会</strong>。</li><li>评审会通过后，工作就可以兵分两路、同时进行：<ul><li><strong>UI 设计师</strong>：开始基于我的低保真原型，进行高保真视觉设计。</li><li><strong>后端开发工程师</strong>：完全不需要等待 UI 稿。他们可以根据我的 PRD 和低保真原型中的逻辑，<strong>立刻开始进行接口开发和数据库设计</strong>。</li></ul></li><li><strong>前端开发工程师</strong>：可以先根据 PRD 和接口文档，搭建前端项目的框架，等待 UI 稿一到，就可以立刻“填充”页面，并与后端进行接口联调。</li></ol><p>这种并行模式，能极大地缩短项目周期。而实现它的核心，就在于一份 <strong>清晰、无歧义的 PRD</strong>，以及一场 <strong>成功的需求评审会</strong>。</p><h3 id="10-1-4-测试流程（冒烟测试、回归测试等）"><a href="#10-1-4-测试流程（冒烟测试、回归测试等）" class="headerlink" title="10.1.4 测试流程（冒烟测试、回归测试等）"></a>10.1.4 测试流程（冒烟测试、回归测试等）</h3><p>当开发工程师完成了一个功能模块的开发，并提交到测试环境后，我们的“守门员”——<strong>测试工程师</strong>——就要登场了。</p><ul><li><p><strong>我的拓展设计（模块化测试）</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140507686.png" alt="image-20250721140507686"></p><p>对于一个较大的版本，我不会等到所有功能都开发完，再统一移交测试。我会和技术负责人一起，将整个版本 <strong>拆分为几个独立的模块</strong>。开发团队每完成一个模块，就立刻移交给测试团队进行测试。<br>这样“<strong>开发一个，测试一个</strong>”的模式，能让测试工作尽早介入，提前暴露和解决问题，避免所有问题都堆积到项目后期，导致项目延期。</p></li></ul><p>在整个测试环节，我会特别关注两种核心的测试类型：</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image.png" alt="image"></p><ol><li><p><strong>冒烟测试 (Smoke Testing)</strong><br>这是测试的第一步。当开发同学部署了一个新的测试版本后，测试同学会花很短的时间（比如 15-30 分钟），对这个版本最核心、最基本的功能（如登录、首页加载等）进行一次快速验证。</p><ul><li><strong>我的理解</strong>：冒烟测试就像我们拿到一个电器，先插上电，看看会不会冒烟。如果连最基本的功能都跑不通（“直冒烟”），那这个版本就是不合格的，会立刻“打回”给开发，无需浪费时间进行更详细的测试。</li></ul></li><li><p><strong>回归测试 (Regression Testing)</strong><br>这是保障产品质量最关键的一环。当开发同学修复了一个 Bug，或者增加了一个新功能后，测试同学不仅要测试这个“修改点”，还必须 <strong>重新去测试那些原本没有问题的、相关的旧功能</strong>。</p><ul><li><strong>我的理解</strong>：回归测试的目的，是为了防止“<strong>按下葫芦浮起瓢</strong>”。我们要确保，新的代码，没有意外地破坏掉旧代码的正常逻辑。</li></ul></li></ol><h3 id="10-1-5-验收方式与上线流程"><a href="#10-1-5-验收方式与上线流程" class="headerlink" title="10.1.5 验收方式与上线流程"></a>10.1.5 验收方式与上线流程</h3><p>当测试团队确认，产品已达到上线标准（即没有严重的 Bug）后，就轮到我这个产品经理，进行最后一道关卡的把控——<strong>产品验收（UAT）</strong>。</p><h4 id="1-产品验收"><a href="#1-产品验收" class="headerlink" title="1. 产品验收"></a>1. 产品验收</h4><ul><li><p><strong>验收方式</strong>：</p><ul><li><strong>口头验收</strong>：对于一些非常小的、非核心的改动，我可能会在测试通过后，自己快速体验一下，然后在工作群里回复一句“确认 OK”，即可。</li><li><strong>文档验收</strong>：对于核心功能或重要版本，我一定会按照 PRD，整理出一份详细的“<strong>UAT 验收清单</strong>”，然后逐项地、严格地进行验收测试。</li></ul></li><li><p><strong>验收结果</strong>：</p><ul><li><strong>验收通过</strong>：功能符合 PRD 的核心要求，没有重大问题。一些不影响主流程的、微小的体验瑕疵，我可以同意放到下个版本再优化。</li><li><strong>验收不通过</strong>：功能的核心逻辑/流程，与我的 PRD 设计严重不符。此时，我有权“<strong>打回重做</strong>”，要求研发团队返工，直到满足需求为止。</li></ul></li></ul><h4 id="2-上线流程"><a href="#2-上线流程" class="headerlink" title="2. 上线流程"></a>2. 上线流程</h4><p>当我验收通过，给出“Go Live”的指令后，正式的上线流程就启动了。这是一个需要多方协作的过程。</p><ul><li><p><strong>我的职责（产品侧）</strong>：</p><ul><li><strong>确定版本号</strong>：为即将上线的新版本，确定一个唯一的、符合规范的版本号（如：V2.5.0）。</li><li><strong>确定更新内容文案</strong>：撰写将在应用商店里，展示给用户看的“更新日志（Release Notes）”。</li><li><strong>（可选）组织培训/撰写手册</strong>：如果功能比较复杂，我还需要为客服或运营同事，准备培训材料或使用手册。</li></ul></li><li><p><strong>研发的职责（开发侧）</strong>：</p><ul><li><strong>提交应用商店审核</strong>：由研发同学，将最终的安装包，提交给苹果 App Store、华为应用市场等各大渠道进行审核。</li><li><strong>择期发布</strong>：在应用商店审核通过后，我们会共同商定一个合适的时机（比如用户活跃度较低的凌晨），进行正式的线上发布。</li></ul></li></ul><h3 id="10-1-6-本节小结"><a href="#10-1-6-本节小结" class="headerlink" title="10.1.6 本节小结"></a>10.1.6 本节小结</h3><table><thead><tr><th align="left"><strong>阶段</strong></th><th align="left"><strong>我的核心角色与职责</strong></th></tr></thead><tbody><tr><td align="left"><strong>交付与评审</strong></td><td align="left">作为“<strong>讲解员</strong>”，组织需求评审会，确保团队对需求理解 100%一致，并推动更高效的并行开发模式。</td></tr><tr><td align="left"><strong>测试</strong></td><td align="left">作为“<strong>信息枢纽</strong>”，关注测试进度，特别是冒烟测试和回归测试的结果，确保产品质量。</td></tr><tr><td align="left"><strong>验收与上线</strong></td><td align="left">作为“<strong>最终决策者</strong>”，进行产品验收（UAT），并准备好版本号、更新文案等上线所需材料，打好“临门一脚”。</td></tr></tbody></table><hr><h2 id="10-2-项目管理"><a href="#10-2-项目管理" class="headerlink" title="10.2 项目管理"></a>10.2 项目管理</h2><p>在很多公司，项目管理（Project Management）和产品管理（Product Management）是两个独立的岗位。但在更多敏捷的互联网团队里，我作为产品经理，通常也需要承担起项目经理的职责。</p><p>即便有专门的项目经理，我作为产品的“owner”，依然是项目成败的最终负责人。因此，掌握项目管理的基本方法和工具，是我的必备技能。</p><h3 id="10-2-1-学习目标"><a href="#10-2-1-学习目标" class="headerlink" title="10.2.1 学习目标"></a>10.2.1 学习目标</h3><p>在本节中，我的目标是带大家掌握产品经理视角下的项目管理核心。我们将学习项目管理的目标，以及我最常用来达成这些目标的<strong>管理方式</strong>和<strong>管理工具</strong>。</p><h3 id="10-2-2-项目管理的定义与目标（时间与质量）"><a href="#10-2-2-项目管理的定义与目标（时间与质量）" class="headerlink" title="10.2.2 项目管理的定义与目标（时间与质量）"></a>10.2.2 项目管理的定义与目标（时间与质量）</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143219781.png" alt="image-20250721143219781"></p><p>我理解的项目管理，就是：<strong>在产品开发过程中，监督和管理整个研发团队，包括协调资源、处理矛盾、监督工期等，以确保项目能按期、按需、高质量地成功上线。</strong></p><p>在整个过程中，我最核心的两个目标，就是守护好<strong>时间</strong>和<strong>质量</strong>这两个生命线。</p><ul><li><strong>时间 (Time)</strong>：确保项目按照我们共同制定的排期表，准时交付。</li><li><strong>质量 (Quality)</strong>：确保最终交付的产品，功能完整、体验流畅，严格符合PRD中的要求。</li></ul><h3 id="10-2-3-管理方式（例会、里程碑、进度检查）"><a href="#10-2-3-管理方式（例会、里程碑、进度检查）" class="headerlink" title="10.2.3 管理方式（例会、里程碑、进度检查）"></a>10.2.3 管理方式（例会、里程碑、进度检查）</h3><p>为了管好时间和质量，我不会等到项目快结束时才去关心，而是会通过一系列的管理“仪式”，将管理工作贯穿于整个研发周期。</p><h4 id="1-每日例会"><a href="#1-每日例会" class="headerlink" title="1. 每日例会"></a>1. 每日例会</h4><p>这是敏捷开发中最核心的仪式。每天早上，我会把开发和测试的核心成员召集起来，开一个不超过15分钟的站会。每个人只需要回答三个问题：</p><ul><li><strong>昨天做了什么？</strong></li><li><strong>今天准备做什么？</strong></li><li><strong>遇到了什么困难（需要我协调解决）？</strong></li></ul><p>每日例会，是我获取项目一线信息、发现潜在风险的最重要的途径。</p><h4 id="2-里程碑"><a href="#2-里程碑" class="headerlink" title="2. 里程碑"></a>2. 里程碑</h4><p>对于一个超过两周的项目，我一定会将它拆解为几个关键的<strong>里程碑</strong>。里程碑不是一个简单的日期，而是一个明确的、可交付的阶段性成果。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143433284.png" alt="image-20250721143433284"></p><p>定义清晰的里程碑，能帮助我从宏观上把控项目的整体节奏，也便于我向管理层汇报进度。</p><h4 id="3-进度检查"><a href="#3-进度检查" class="headerlink" title="3. 进度检查"></a>3. 进度检查</h4><p>这是我日常的、持续性的工作。它包括与团队成员进行一对一的沟通，在项目管理工具上检查任务的完成状态，主动识别可能导致延期的风险，并尽我所能地为团队扫清障碍。</p><h3 id="10-2-4-项目管理工具（甘特图、TAPD、禅道等）"><a href="#10-2-4-项目管理工具（甘特图、TAPD、禅道等）" class="headerlink" title="10.2.4 项目管理工具（甘特图、TAPD、禅道等）"></a>10.2.4 项目管理工具（甘特图、TAPD、禅道等）</h3><p>要落地上述的管理方式，我必须借助专业的工具。</p><h4 id="1-甘特图-Gantt-Chart"><a href="#1-甘特图-Gantt-Chart" class="headerlink" title="1. 甘特图 (Gantt Chart)"></a>1. 甘特图 (Gantt Chart)</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143544979.png" alt="image-20250721143544979"></p><p><strong>甘特图</strong>，是我进行项目<strong>长期规划</strong>和<strong>排期</strong>的首选工具。<br>它是一个强大的时间轴视图，能让我清晰地看到：</p><ul><li>项目包含哪些任务？</li><li>每个任务的开始和结束时间？</li><li>任务之间的依赖关系是怎样的？（比如：A任务不完成，B任务就无法开始）</li><li>每个任务的负责人是谁？</li></ul><p>我通常会在项目启动时，和技术负责人一起，制定出一份详细的甘特图，作为我们整个项目的时间规划蓝图。</p><h4 id="2-TAPD-禅道-Jira"><a href="#2-TAPD-禅道-Jira" class="headerlink" title="2. TAPD / 禅道 / Jira"></a>2. TAPD / 禅道 / Jira</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143754665.png" alt="image-20250721143754665"></p><p>这类工具，是我进行<strong>日常、微观的任务跟踪</strong>的核心。甘特图告诉我们“长期的路要怎么走”，而TAPD/禅道这类工具，则告诉我们“今天的每一步要怎么走”。<br>我主要用它们来实现：</p><ul><li><strong>创建和分配任务</strong>：将PRD中的功能点，拆解为一个个具体的开发任务，并指派给对应的工程师。</li><li><strong>追踪任务状态</strong>：通过“<strong>任务看板</strong>”的形式，将所有任务的状态（如：待处理、进行中、已完成）可视化，团队进展一目了然。</li><li><strong>管理Bug</strong>：测试团队会在这里提交、指派和跟踪所有Bug的修复过程。</li><li><strong>文档协作</strong>：作为我们PRD、API文档等核心文档的存放和协作平台。</li></ul><h3 id="10-2-5-本节小结"><a href="#10-2-5-本节小结" class="headerlink" title="10.2.5 本节小结"></a>10.2.5 本节小结</h3><table><thead><tr><th align="left"><strong>管理维度</strong></th><th align="left"><strong>我的核心方法</strong></th><th align="left"><strong>我常用的工具</strong></th></tr></thead><tbody><tr><td align="left"><strong>日常同步</strong></td><td align="left"><strong>每日例会</strong></td><td align="left"><strong>TAPD / 禅道</strong> 的任务看板</td></tr><tr><td align="left"><strong>长期规划</strong></td><td align="left"><strong>里程碑规划</strong></td><td align="left"><strong>甘特图</strong></td></tr><tr><td align="left"><strong>风险控制</strong></td><td align="left">持续的<strong>进度检查</strong></td><td align="left">项目周报、一对一沟通</td></tr></tbody></table><hr><h2 id="10-3-产品需求评审"><a href="#10-3-产品需求评审" class="headerlink" title="10.3 产品需求评审"></a>10.3 产品需求评审</h2><p>在我看来，<strong>产品需求评审会</strong>，是整个研发流程中<strong>最重要</strong>的一个沟通仪式。</p><p>它是我作为产品经理，将我的“作战计划”（PRD和原型），正式地、全面地同步给我的“作战部队”（设计、研发、测试团队）的起点。</p><p>一场成功的评审会，能让整个团队对目标形成统一、清晰的认知，从而极大地提升后续的研发效率，避免返工；</p><p>而一场失败的评审会，则会埋下无数的“坑”，导致后续开发过程中的无尽扯皮和延期。</p><h3 id="10-3-1-学习目标"><a href="#10-3-1-学习目标" class="headerlink" title="10.3.1 学习目标"></a>10.3.1 学习目标</h3><p>在本节中，我的目标是带大家掌握如何组织和主导一场成功的需求评审会。我们将学习评审会的不同类型、标准的会议流程、高效的讲解内容结构，以及我作为会议主持人，必须掌握的控场技巧和要点。</p><h3 id="10-3-2-需求类型（业务需求、功能需求）"><a href="#10-3-2-需求类型（业务需求、功能需求）" class="headerlink" title="10.3.2 需求类型（业务需求、功能需求）"></a>10.3.2 需求类型（业务需求、功能需求）</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144207121.png" alt="image-20250721144207121"></p><p>在组织评审会前，我首先要明确这次会议的“类型”。我通常会把需求评审分为两种：</p><ol><li><p><strong>业务需求评审</strong>：这通常发生在项目的<strong>极早期</strong>。参会人员是<strong>老板、业务负责人、技术负责人</strong>等高阶决策者。会议的核心，是评审和探讨本次需求的<strong>商业价值、需求范围、技术可行性、版本规划</strong>等战略层面的问题。</p></li><li><p><strong>功能需求评审</strong>：这通常发生在我们已经完成详细PRD和原型，<strong>即将进入研发阶段</strong>时。</p><p>参会人员是<strong>开发、测试、设计师</strong>等一线的执行团队。会议的核心，是<strong>讲解功能实现的每一个细节，确保团队对需求理解无误</strong>。</p></li></ol><p>我们本节后续讨论的，主要就是“<strong>功能需求评审</strong>”。</p><h3 id="10-3-3-会议流程（准备、过程、会后跟踪）"><a href="#10-3-3-会议流程（准备、过程、会后跟踪）" class="headerlink" title="10.3.3 会议流程（准备、过程、会后跟踪）"></a>10.3.3 会议流程（准备、过程、会后跟踪）</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144246357.png" alt="image-20250721144246357"></p><p>一场成功的会议，其功夫往往在“会前”和“会后”。我严格遵循一个四阶段的会议流程：</p><table><thead><tr><th align="left"><strong>会议阶段</strong></th><th align="left"><strong>我的关键动作</strong></th></tr></thead><tbody><tr><td align="left"><strong>1. 预约会议</strong></td><td align="left">我会<strong>至少提前1天</strong>发出会议邀请，并<strong>必须</strong>在邀请中，附上本次评审的PRD和原型链接，要求所有参会者“<strong>务必会前阅读</strong>”。</td></tr><tr><td align="left"><strong>2. 会前准备</strong></td><td align="left">我会提前进入会议室，确保投影、网络等设备一切正常，并将我的讲解材料准备就绪。</td></tr><tr><td align="left"><strong>3. 会议过程</strong></td><td align="left">这是我的“主场”。我会严格按照预设的结构和节奏进行讲解和讨论（具体见下文）。</td></tr><tr><td align="left"><strong>4. 会后跟踪</strong></td><td align="left">会议结束后半小时内，我会发出<strong>会议纪要（Minutes）</strong>，清晰地列出会议结论、遗留问题和下一步的行动计划（Action Items）及负责人。并持续跟踪这些问题的解决。</td></tr></tbody></table><h3 id="10-3-4-评审结构与讲解内容（背景、原型、其他需求）"><a href="#10-3-4-评审结构与讲解内容（背景、原型、其他需求）" class="headerlink" title="10.3.4 评审结构与讲解内容（背景、原型、其他需求）"></a>10.3.4 评审结构与讲解内容（背景、原型、其他需求）</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144338478.png" alt="image-20250721144338478"></p><p>在评审会中，我的讲解会像讲一个故事一样，遵循一个清晰、有吸引力的结构：</p><ol><li><strong>需求背景目的 (Why)</strong>：我总是从“为什么”开始。用5-10分钟，清晰地向团队交代本次需求的来源、要解决的用户痛点和期望达成的商业目标。这能让团队在后续的讨论中，始终与“初心”对齐。</li><li><strong>流程结构 (What - a high level view)</strong>：接着，我会快速地展示本次需求相关的流程图和结构图，让团队对这个功能在整个产品中的“位置”和“骨架”，有一个宏观的认知。</li><li><strong>原型讲解 (What - a detailed view)</strong>：这是会议的核心部分。我会打开我的交互原型，从第一个页面开始，逐一地、详细地讲解每一个页面的布局、每一个控件的交互和其背后的所有业务规则。</li><li><strong>其他需求 (How)</strong>：最后，我会讲解PRD中定义的非功能性需求，比如性能要求、数据埋点需求、兼容性要求等。</li></ol><h3 id="10-3-5-评审要点（时间与节奏控制、控场与主见、结论收尾）"><a href="#10-3-5-评审要点（时间与节奏控制、控场与主见、结论收尾）" class="headerlink" title="10.3.5 评审要点（时间与节奏控制、控场与主见、结论收尾）"></a>10.3.5 评审要点（时间与节奏控制、控场与主见、结论收尾）</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144424894.png" alt="image-20250721144424894"></p><p>作为会议的主持人，我的控场能力，直接决定了会议的成败。我时刻关注以下几点：</p><table><thead><tr><th align="left"><strong>我的控场要点</strong></th><th align="left"><strong>具体做法</strong></th></tr></thead><tbody><tr><td align="left"><strong>时间与节奏控制</strong></td><td align="left">我会严格将会议控制在<strong>1小时</strong>内。在讲解中，我会每隔10-15分钟就主动停下来，问“<strong>到这里，大家有什么问题吗？</strong>”，以保持互动，避免我一个人“一言堂”。</td></tr><tr><td align="left"><strong>控场与主次划分</strong></td><td align="left">我会时刻注意评审的主题。当讨论陷入过深的技术细节或跑偏时，我会礼貌地打断，并建议“<strong>这个问题非常好，我们线下再拉个小会深入讨论</strong>”，然后把会议拉回主线。我会重点讲解流程复杂或有争议的地方。</td></tr><tr><td align="left"><strong>讨论与主见</strong></td><td align="left">我会鼓励团队提出质疑，这是发现方案漏洞的好机会。但对于已经深思熟虑、关系到核心需求的点，我也会<strong>有理有据地坚持自己的主见</strong>，不能被轻易带偏。</td></tr><tr><td align="left"><strong>收尾确定</strong></td><td align="left">会议结束前，我必须得到一个明确的结论：本次评审是“<strong>通过</strong>”、“<strong>通过但有待办项</strong>”还是“<strong>不通过，需重大修改</strong>”？并明确后续的Action Items和时间点。绝不能开成一个没有结论的“聊天会”。</td></tr></tbody></table><h2 id="10-4-本章总结"><a href="#10-4-本章总结" class="headerlink" title="10.4 本章总结"></a>10.4 本章总结</h2><h3 id="10-4-1-课程内容回顾"><a href="#10-4-1-课程内容回顾" class="headerlink" title="10.4.1 课程内容回顾"></a>10.4.1 课程内容回顾</h3><p>在本章，我们学习了如何将一个已经设计好的产品方案，一步步地推向最终的成功发布。</p><ul><li><strong>产品生产发布流程</strong>：我们了解了产品、UI、开发、测试这四个核心角色的职责，以及他们之间环环相扣的协作流程。</li><li><strong>项目管理</strong>：我们学习了作为产品经理，如何通过例会、里程碑等方式，以及甘特图、TAPD等工具，来管理好项目的时间和质量。</li><li><strong>产品需求评审</strong>：我们深入地学习了如何组织和主导一场专业、高效的需求评审会，这是我们作为产品经理，最重要的“软技能”之一。</li></ul><p>到这里，我们已经走完了从一个模糊的想法，到一个上线产品的全过程。恭喜你，完成了本次的学习！</p></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/7673.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/7673.html&quot;)">产品经理入门（十）：第十章：产品研发全流程管理</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/7673.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理入门（十）：第十章：产品研发全流程管理&amp;url=https://prorise666.site/posts/7673.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/38041.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div class="next-post pull-right"><a href="/posts/30404.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">2️⃣ 电商实战（上）</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理入门（十）：第十章：产品研发全流程管理",date:"2025-07-21 01:13:45",updated:"2025-07-21 14:52:13",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第十章：产品研发全流程管理\n\n到目前为止，我们已经作为产品经理，完成了从需求分析到方案设计的核心工作。一个包含了 PRD 和交互原型的完整方案，已经躺在了我们的电脑里。\n\n那么接下来的问题是：**然后呢？我们要如何推动这个方案，一步步地变成一个能被用户真实使用的、上线的活产品？**\n\n这就是本章要解决的问题。在这里，我将带大家跳出产品经理的单一角色，以一个“**项目指挥官**”的视角，来学习如何驱动一个完整的团队，协同作战，最终打赢一场漂亮的产品发布战役。\n\n## 10.1 产品生产发布流程\n\n![image-20250721140058419](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140058419.png)\n\n产品的生产发布，从来不是一个人的战斗，而是一场需要多兵种（角色）协同的“**接力赛**”。我面前的这张“泳道图”，就是我们这场接力赛的 **核心作战地图**。\n\n它清晰地展示了，一个产品从诞生到上线，需要经历 **方案设计、产品研发、验收上线** 这三大阶段，以及 **产品经理、UI 设计师、程序员、测试** 这四个核心角色，是如何在这场接力赛中，依次交棒、紧密协作的。\n\n### 10.1.1 学习目标\n\n在本节中，我的目标是带大家清晰地理解这场“接力赛”的规则。我们将深入学习团队中每一个核心角色的职责，并重点掌握我作为产品经理，是如何与他们进行高效协作，以及如何管理关键的文档交付与评审流程的。\n\n### 10.1.2 团队协作与成员职责（产品、UI、开发、测试）\n\n要打赢一场仗，首先要了解我们的战友。\n\n#### 1. 产品经理 (Product Manager) - 流程的“大脑”与“发起者”\n\n我的角色，是整个流程的 **“Why”和“What”的定义者**。\n\n* **我的职责**：正如泳道图所示，整个流程由我发起。我负责 `收集分析需求`，并最终 `输出方案`（PRD 和原型）。我是产品方向的掌舵人，是所有后续工作的“需求源头”。\n\n#### 2. UI 设计师 (UI Designer) - 产品的“化妆师”\n\n![image-20250721140204539](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140204539.png)\n\nUI 设计师，是 **“产品长什么样（How it Looks）”的专家**。\n\n* **他的职责**：是把我的低保真原型（产品的“骨架”），进行专业的视觉设计，输出包含色彩、图标、字体的 **高保真视觉效果图**（产品的“皮肤”），让产品变得美观、有吸引力。\n* **我与 UI 的协作**\n    ![image-20250721140235402](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140235402.png)\n    我与 UI 设计师的协作，遵循“讲解-验收”两步走：\n    1. **讲解**：在交付原型时，我必须召开会议，向他详细讲解我的设计背后的 **需求背景、业务目的和核心逻辑**。\n    2. **验收**：在 UI 稿完成后，我需要严格地进行 **视觉验收**，确保他的设计不仅美观，更重要的是，完全符合我想要传达的产品目标和用户体验。\n\n#### 3. 研发工程师 (Developer) - 产品的“建造者”\n\n![image-20250721140327008](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140327008.png)\n\n研发工程师，是 **“产品如何工作（How it Works）”的实现者**。\n\n* **他的职责**：他们是把我们的设计图纸，用一行行代码，变成一个真实可用的产品的“建筑师”。他们通常分为：\n  * **前端开发**：负责实现用户能直接看到和交互的界面。\n  * **后端开发**：负责实现支撑前端运转的服务器、数据库和业务逻辑。\n* **我与研发的协作**\n    ![image-20250721140352470](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140352470.png)\n    我与研发工程师的协作，是整个流程中最核心、最需要严谨性的环节：\n    1. **文档交付**：我必须提供清晰、完整、无歧义的 PRD 和原型。\n    2. **评审排期**：我必须组织正式的“**需求评审会**”，确保所有研发人员都对需求理解一致。评审通过后，再共同制定开发排期。\n    3. **项目管理**：在开发过程中，我需要持续跟进进度，解答疑问，管理变更。\n\n#### 4. 测试工程师 (Tester) - 产品的“守门员”\n\n测试工程师，是 `“产品是否正确（Is it Right）”` 的捍卫者。\n\n![image-20250721140748356](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140748356.png)\n\n![image-20250721140450191](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140450191.png)\n\n* **他的职责**：在产品开发完成后，他们会严格地按照我的 PRD，对产品进行全面的 **产品测试**，找出所有潜在的 Bug 和与需求不符的地方，是保障产品质量的最后一道，也是最重要的一道防线。\n\n---\n\n### 10.1.3 文档交付与评审流程\n\n在我完成了 PRD 和原型的撰写后，就进入了至关重要的“**交棒**”环节。\n\n最传统的模式，是一种“**单线程瀑布流**”：我做完方案 -> 交给 UI 做设计 -> UI 设计完 -> 再一起开评审会 -> 然后开发才开始工作。这种模式虽然稳妥，但它的弊端也很明显：**效率低下**，各个角色之间是“串行”等待，浪费了大量时间。\n\n因此，在我的实践中，我极力推行一种更高效的“**并行开发**”模式：\n\n![image-20250721141211224](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721141211224.png)\n\n1. **我完成 PRD 和“低保真”原型后，立刻组织需求评审会**。\n2. 评审会通过后，工作就可以兵分两路、同时进行：\n    * **UI 设计师**：开始基于我的低保真原型，进行高保真视觉设计。\n    * **后端开发工程师**：完全不需要等待 UI 稿。他们可以根据我的 PRD 和低保真原型中的逻辑，**立刻开始进行接口开发和数据库设计**。\n3. **前端开发工程师**：可以先根据 PRD 和接口文档，搭建前端项目的框架，等待 UI 稿一到，就可以立刻“填充”页面，并与后端进行接口联调。\n\n这种并行模式，能极大地缩短项目周期。而实现它的核心，就在于一份 **清晰、无歧义的 PRD**，以及一场 **成功的需求评审会**。\n\n### 10.1.4 测试流程（冒烟测试、回归测试等）\n\n当开发工程师完成了一个功能模块的开发，并提交到测试环境后，我们的“守门员”——**测试工程师**——就要登场了。\n\n* **我的拓展设计（模块化测试）**\n    ![image-20250721140507686](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140507686.png)\n\n    对于一个较大的版本，我不会等到所有功能都开发完，再统一移交测试。我会和技术负责人一起，将整个版本 **拆分为几个独立的模块**。开发团队每完成一个模块，就立刻移交给测试团队进行测试。\n    这样“**开发一个，测试一个**”的模式，能让测试工作尽早介入，提前暴露和解决问题，避免所有问题都堆积到项目后期，导致项目延期。\n\n在整个测试环节，我会特别关注两种核心的测试类型：\n\n![image](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image.png)\n\n1. **冒烟测试 (Smoke Testing)**\n    这是测试的第一步。当开发同学部署了一个新的测试版本后，测试同学会花很短的时间（比如 15-30 分钟），对这个版本最核心、最基本的功能（如登录、首页加载等）进行一次快速验证。\n    * **我的理解**：冒烟测试就像我们拿到一个电器，先插上电，看看会不会冒烟。如果连最基本的功能都跑不通（“直冒烟”），那这个版本就是不合格的，会立刻“打回”给开发，无需浪费时间进行更详细的测试。\n\n2. **回归测试 (Regression Testing)**\n    这是保障产品质量最关键的一环。当开发同学修复了一个 Bug，或者增加了一个新功能后，测试同学不仅要测试这个“修改点”，还必须 **重新去测试那些原本没有问题的、相关的旧功能**。\n    * **我的理解**：回归测试的目的，是为了防止“**按下葫芦浮起瓢**”。我们要确保，新的代码，没有意外地破坏掉旧代码的正常逻辑。\n\n### 10.1.5 验收方式与上线流程\n\n当测试团队确认，产品已达到上线标准（即没有严重的 Bug）后，就轮到我这个产品经理，进行最后一道关卡的把控——**产品验收（UAT）**。\n\n#### 1. 产品验收\n\n* **验收方式**：\n  * **口头验收**：对于一些非常小的、非核心的改动，我可能会在测试通过后，自己快速体验一下，然后在工作群里回复一句“确认 OK”，即可。\n  * **文档验收**：对于核心功能或重要版本，我一定会按照 PRD，整理出一份详细的“**UAT 验收清单**”，然后逐项地、严格地进行验收测试。\n\n* **验收结果**：\n  * **验收通过**：功能符合 PRD 的核心要求，没有重大问题。一些不影响主流程的、微小的体验瑕疵，我可以同意放到下个版本再优化。\n  * **验收不通过**：功能的核心逻辑/流程，与我的 PRD 设计严重不符。此时，我有权“**打回重做**”，要求研发团队返工，直到满足需求为止。\n\n#### 2. 上线流程\n\n当我验收通过，给出“Go Live”的指令后，正式的上线流程就启动了。这是一个需要多方协作的过程。\n\n* **我的职责（产品侧）**：\n  * **确定版本号**：为即将上线的新版本，确定一个唯一的、符合规范的版本号（如：V2.5.0）。\n  * **确定更新内容文案**：撰写将在应用商店里，展示给用户看的“更新日志（Release Notes）”。\n  * **（可选）组织培训/撰写手册**：如果功能比较复杂，我还需要为客服或运营同事，准备培训材料或使用手册。\n\n* **研发的职责（开发侧）**：\n  * **提交应用商店审核**：由研发同学，将最终的安装包，提交给苹果 App Store、华为应用市场等各大渠道进行审核。\n  * **择期发布**：在应用商店审核通过后，我们会共同商定一个合适的时机（比如用户活跃度较低的凌晨），进行正式的线上发布。\n\n### 10.1.6 本节小结\n\n| **阶段** | **我的核心角色与职责** |\n| :--- | :--- |\n| **交付与评审** | 作为“**讲解员**”，组织需求评审会，确保团队对需求理解 100%一致，并推动更高效的并行开发模式。 |\n| **测试** | 作为“**信息枢纽**”，关注测试进度，特别是冒烟测试和回归测试的结果，确保产品质量。 |\n| **验收与上线**| 作为“**最终决策者**”，进行产品验收（UAT），并准备好版本号、更新文案等上线所需材料，打好“临门一脚”。 |\n\n\n---\n## 10.2 项目管理\n\n在很多公司，项目管理（Project Management）和产品管理（Product Management）是两个独立的岗位。但在更多敏捷的互联网团队里，我作为产品经理，通常也需要承担起项目经理的职责。\n\n即便有专门的项目经理，我作为产品的“owner”，依然是项目成败的最终负责人。因此，掌握项目管理的基本方法和工具，是我的必备技能。\n\n### 10.2.1 学习目标\n\n在本节中，我的目标是带大家掌握产品经理视角下的项目管理核心。我们将学习项目管理的目标，以及我最常用来达成这些目标的**管理方式**和**管理工具**。\n\n### 10.2.2 项目管理的定义与目标（时间与质量）\n\n![image-20250721143219781](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143219781.png)\n\n我理解的项目管理，就是：**在产品开发过程中，监督和管理整个研发团队，包括协调资源、处理矛盾、监督工期等，以确保项目能按期、按需、高质量地成功上线。**\n\n在整个过程中，我最核心的两个目标，就是守护好**时间**和**质量**这两个生命线。\n* **时间 (Time)**：确保项目按照我们共同制定的排期表，准时交付。\n* **质量 (Quality)**：确保最终交付的产品，功能完整、体验流畅，严格符合PRD中的要求。\n\n### 10.2.3 管理方式（例会、里程碑、进度检查）\n\n为了管好时间和质量，我不会等到项目快结束时才去关心，而是会通过一系列的管理“仪式”，将管理工作贯穿于整个研发周期。\n\n#### 1. 每日例会\n\n这是敏捷开发中最核心的仪式。每天早上，我会把开发和测试的核心成员召集起来，开一个不超过15分钟的站会。每个人只需要回答三个问题：\n* **昨天做了什么？**\n* **今天准备做什么？**\n* **遇到了什么困难（需要我协调解决）？**\n\n每日例会，是我获取项目一线信息、发现潜在风险的最重要的途径。\n\n#### 2. 里程碑\n\n对于一个超过两周的项目，我一定会将它拆解为几个关键的**里程碑**。里程碑不是一个简单的日期，而是一个明确的、可交付的阶段性成果。\n\n![image-20250721143433284](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143433284.png)\n\n定义清晰的里程碑，能帮助我从宏观上把控项目的整体节奏，也便于我向管理层汇报进度。\n\n#### 3. 进度检查\n\n这是我日常的、持续性的工作。它包括与团队成员进行一对一的沟通，在项目管理工具上检查任务的完成状态，主动识别可能导致延期的风险，并尽我所能地为团队扫清障碍。\n\n### 10.2.4 项目管理工具（甘特图、TAPD、禅道等）\n\n要落地上述的管理方式，我必须借助专业的工具。\n\n#### 1. 甘特图 (Gantt Chart)\n\n![image-20250721143544979](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143544979.png)\n\n**甘特图**，是我进行项目**长期规划**和**排期**的首选工具。\n它是一个强大的时间轴视图，能让我清晰地看到：\n\n* 项目包含哪些任务？\n* 每个任务的开始和结束时间？\n* 任务之间的依赖关系是怎样的？（比如：A任务不完成，B任务就无法开始）\n* 每个任务的负责人是谁？\n\n我通常会在项目启动时，和技术负责人一起，制定出一份详细的甘特图，作为我们整个项目的时间规划蓝图。\n\n#### 2. TAPD / 禅道 / Jira\n\n![image-20250721143754665](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143754665.png)\n\n这类工具，是我进行**日常、微观的任务跟踪**的核心。甘特图告诉我们“长期的路要怎么走”，而TAPD/禅道这类工具，则告诉我们“今天的每一步要怎么走”。\n我主要用它们来实现：\n\n* **创建和分配任务**：将PRD中的功能点，拆解为一个个具体的开发任务，并指派给对应的工程师。\n* **追踪任务状态**：通过“**任务看板**”的形式，将所有任务的状态（如：待处理、进行中、已完成）可视化，团队进展一目了然。\n* **管理Bug**：测试团队会在这里提交、指派和跟踪所有Bug的修复过程。\n* **文档协作**：作为我们PRD、API文档等核心文档的存放和协作平台。\n\n### 10.2.5 本节小结\n\n| **管理维度** | **我的核心方法** | **我常用的工具** |\n| :--- | :--- | :--- |\n| **日常同步** | **每日例会** | **TAPD / 禅道** 的任务看板 |\n| **长期规划** | **里程碑规划** | **甘特图** |\n| **风险控制** | 持续的**进度检查** | 项目周报、一对一沟通 |\n\n\n\n\n\n---\n## 10.3 产品需求评审\n\n在我看来，**产品需求评审会**，是整个研发流程中**最重要**的一个沟通仪式。\n\n它是我作为产品经理，将我的“作战计划”（PRD和原型），正式地、全面地同步给我的“作战部队”（设计、研发、测试团队）的起点。\n\n一场成功的评审会，能让整个团队对目标形成统一、清晰的认知，从而极大地提升后续的研发效率，避免返工；\n\n而一场失败的评审会，则会埋下无数的“坑”，导致后续开发过程中的无尽扯皮和延期。\n\n### 10.3.1 学习目标\n\n在本节中，我的目标是带大家掌握如何组织和主导一场成功的需求评审会。我们将学习评审会的不同类型、标准的会议流程、高效的讲解内容结构，以及我作为会议主持人，必须掌握的控场技巧和要点。\n\n### 10.3.2 需求类型（业务需求、功能需求）\n\n![image-20250721144207121](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144207121.png)\n\n在组织评审会前，我首先要明确这次会议的“类型”。我通常会把需求评审分为两种：\n\n1. **业务需求评审**：这通常发生在项目的**极早期**。参会人员是**老板、业务负责人、技术负责人**等高阶决策者。会议的核心，是评审和探讨本次需求的**商业价值、需求范围、技术可行性、版本规划**等战略层面的问题。\n\n2. **功能需求评审**：这通常发生在我们已经完成详细PRD和原型，**即将进入研发阶段**时。\n\n\t参会人员是**开发、测试、设计师**等一线的执行团队。会议的核心，是**讲解功能实现的每一个细节，确保团队对需求理解无误**。\n\n我们本节后续讨论的，主要就是“**功能需求评审**”。\n\n### 10.3.3 会议流程（准备、过程、会后跟踪）\n\n![image-20250721144246357](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144246357.png)\n\n一场成功的会议，其功夫往往在“会前”和“会后”。我严格遵循一个四阶段的会议流程：\n\n| **会议阶段** | **我的关键动作** |\n| :--- | :--- |\n| **1. 预约会议** | 我会**至少提前1天**发出会议邀请，并**必须**在邀请中，附上本次评审的PRD和原型链接，要求所有参会者“**务必会前阅读**”。 |\n| **2. 会前准备** | 我会提前进入会议室，确保投影、网络等设备一切正常，并将我的讲解材料准备就绪。 |\n| **3. 会议过程** | 这是我的“主场”。我会严格按照预设的结构和节奏进行讲解和讨论（具体见下文）。 |\n| **4. 会后跟踪**| 会议结束后半小时内，我会发出**会议纪要（Minutes）**，清晰地列出会议结论、遗留问题和下一步的行动计划（Action Items）及负责人。并持续跟踪这些问题的解决。 |\n\n### 10.3.4 评审结构与讲解内容（背景、原型、其他需求）\n\n![image-20250721144338478](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144338478.png)\n\n在评审会中，我的讲解会像讲一个故事一样，遵循一个清晰、有吸引力的结构：\n\n1.  **需求背景目的 (Why)**：我总是从“为什么”开始。用5-10分钟，清晰地向团队交代本次需求的来源、要解决的用户痛点和期望达成的商业目标。这能让团队在后续的讨论中，始终与“初心”对齐。\n2.  **流程结构 (What - a high level view)**：接着，我会快速地展示本次需求相关的流程图和结构图，让团队对这个功能在整个产品中的“位置”和“骨架”，有一个宏观的认知。\n3.  **原型讲解 (What - a detailed view)**：这是会议的核心部分。我会打开我的交互原型，从第一个页面开始，逐一地、详细地讲解每一个页面的布局、每一个控件的交互和其背后的所有业务规则。\n4.  **其他需求 (How)**：最后，我会讲解PRD中定义的非功能性需求，比如性能要求、数据埋点需求、兼容性要求等。\n\n### 10.3.5 评审要点（时间与节奏控制、控场与主见、结论收尾）\n\n![image-20250721144424894](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144424894.png)\n\n作为会议的主持人，我的控场能力，直接决定了会议的成败。我时刻关注以下几点：\n\n| **我的控场要点** | **具体做法** |\n| :--- | :--- |\n| **时间与节奏控制** | 我会严格将会议控制在**1小时**内。在讲解中，我会每隔10-15分钟就主动停下来，问“**到这里，大家有什么问题吗？**”，以保持互动，避免我一个人“一言堂”。 |\n| **控场与主次划分** | 我会时刻注意评审的主题。当讨论陷入过深的技术细节或跑偏时，我会礼貌地打断，并建议“**这个问题非常好，我们线下再拉个小会深入讨论**”，然后把会议拉回主线。我会重点讲解流程复杂或有争议的地方。 |\n| **讨论与主见** | 我会鼓励团队提出质疑，这是发现方案漏洞的好机会。但对于已经深思熟虑、关系到核心需求的点，我也会**有理有据地坚持自己的主见**，不能被轻易带偏。 |\n| **收尾确定** | 会议结束前，我必须得到一个明确的结论：本次评审是“**通过**”、“**通过但有待办项**”还是“**不通过，需重大修改**”？并明确后续的Action Items和时间点。绝不能开成一个没有结论的“聊天会”。 |\n\n\n\n## 10.4 本章总结\n\n### 10.4.1 课程内容回顾\n\n在本章，我们学习了如何将一个已经设计好的产品方案，一步步地推向最终的成功发布。\n* **产品生产发布流程**：我们了解了产品、UI、开发、测试这四个核心角色的职责，以及他们之间环环相扣的协作流程。\n* **项目管理**：我们学习了作为产品经理，如何通过例会、里程碑等方式，以及甘特图、TAPD等工具，来管理好项目的时间和质量。\n* **产品需求评审**：我们深入地学习了如何组织和主导一场专业、高效的需求评审会，这是我们作为产品经理，最重要的“软技能”之一。\n\n到这里，我们已经走完了从一个模糊的想法，到一个上线产品的全过程。恭喜你，完成了本次的学习！"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E7%AB%A0%EF%BC%9A%E4%BA%A7%E5%93%81%E7%A0%94%E5%8F%91%E5%85%A8%E6%B5%81%E7%A8%8B%E7%AE%A1%E7%90%86"><span class="toc-number">1.</span> <span class="toc-text">第十章：产品研发全流程管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#10-1-%E4%BA%A7%E5%93%81%E7%94%9F%E4%BA%A7%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B"><span class="toc-number">1.1.</span> <span class="toc-text">10.1 产品生产发布流程</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.1.</span> <span class="toc-text">10.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-2-%E5%9B%A2%E9%98%9F%E5%8D%8F%E4%BD%9C%E4%B8%8E%E6%88%90%E5%91%98%E8%81%8C%E8%B4%A3%EF%BC%88%E4%BA%A7%E5%93%81%E3%80%81UI%E3%80%81%E5%BC%80%E5%8F%91%E3%80%81%E6%B5%8B%E8%AF%95%EF%BC%89"><span class="toc-number">1.1.2.</span> <span class="toc-text">10.1.2 团队协作与成员职责（产品、UI、开发、测试）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86-Product-Manager-%E6%B5%81%E7%A8%8B%E7%9A%84%E2%80%9C%E5%A4%A7%E8%84%91%E2%80%9D%E4%B8%8E%E2%80%9C%E5%8F%91%E8%B5%B7%E8%80%85%E2%80%9D"><span class="toc-number">1.1.2.1.</span> <span class="toc-text">1. 产品经理 (Product Manager) - 流程的“大脑”与“发起者”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-UI-%E8%AE%BE%E8%AE%A1%E5%B8%88-UI-Designer-%E4%BA%A7%E5%93%81%E7%9A%84%E2%80%9C%E5%8C%96%E5%A6%86%E5%B8%88%E2%80%9D"><span class="toc-number">1.1.2.2.</span> <span class="toc-text">2. UI 设计师 (UI Designer) - 产品的“化妆师”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%A0%94%E5%8F%91%E5%B7%A5%E7%A8%8B%E5%B8%88-Developer-%E4%BA%A7%E5%93%81%E7%9A%84%E2%80%9C%E5%BB%BA%E9%80%A0%E8%80%85%E2%80%9D"><span class="toc-number">1.1.2.3.</span> <span class="toc-text">3. 研发工程师 (Developer) - 产品的“建造者”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E6%B5%8B%E8%AF%95%E5%B7%A5%E7%A8%8B%E5%B8%88-Tester-%E4%BA%A7%E5%93%81%E7%9A%84%E2%80%9C%E5%AE%88%E9%97%A8%E5%91%98%E2%80%9D"><span class="toc-number">1.1.2.4.</span> <span class="toc-text">4. 测试工程师 (Tester) - 产品的“守门员”</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-3-%E6%96%87%E6%A1%A3%E4%BA%A4%E4%BB%98%E4%B8%8E%E8%AF%84%E5%AE%A1%E6%B5%81%E7%A8%8B"><span class="toc-number">1.1.3.</span> <span class="toc-text">10.1.3 文档交付与评审流程</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-4-%E6%B5%8B%E8%AF%95%E6%B5%81%E7%A8%8B%EF%BC%88%E5%86%92%E7%83%9F%E6%B5%8B%E8%AF%95%E3%80%81%E5%9B%9E%E5%BD%92%E6%B5%8B%E8%AF%95%E7%AD%89%EF%BC%89"><span class="toc-number">1.1.4.</span> <span class="toc-text">10.1.4 测试流程（冒烟测试、回归测试等）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-5-%E9%AA%8C%E6%94%B6%E6%96%B9%E5%BC%8F%E4%B8%8E%E4%B8%8A%E7%BA%BF%E6%B5%81%E7%A8%8B"><span class="toc-number">1.1.5.</span> <span class="toc-text">10.1.5 验收方式与上线流程</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%A7%E5%93%81%E9%AA%8C%E6%94%B6"><span class="toc-number">1.1.5.1.</span> <span class="toc-text">1. 产品验收</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%B8%8A%E7%BA%BF%E6%B5%81%E7%A8%8B"><span class="toc-number">1.1.5.2.</span> <span class="toc-text">2. 上线流程</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-6-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.1.6.</span> <span class="toc-text">10.1.6 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-2-%E9%A1%B9%E7%9B%AE%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.</span> <span class="toc-text">10.2 项目管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.2.1.</span> <span class="toc-text">10.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-2-%E9%A1%B9%E7%9B%AE%E7%AE%A1%E7%90%86%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E7%9B%AE%E6%A0%87%EF%BC%88%E6%97%B6%E9%97%B4%E4%B8%8E%E8%B4%A8%E9%87%8F%EF%BC%89"><span class="toc-number">1.2.2.</span> <span class="toc-text">10.2.2 项目管理的定义与目标（时间与质量）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-3-%E7%AE%A1%E7%90%86%E6%96%B9%E5%BC%8F%EF%BC%88%E4%BE%8B%E4%BC%9A%E3%80%81%E9%87%8C%E7%A8%8B%E7%A2%91%E3%80%81%E8%BF%9B%E5%BA%A6%E6%A3%80%E6%9F%A5%EF%BC%89"><span class="toc-number">1.2.3.</span> <span class="toc-text">10.2.3 管理方式（例会、里程碑、进度检查）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%AF%8F%E6%97%A5%E4%BE%8B%E4%BC%9A"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">1. 每日例会</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E9%87%8C%E7%A8%8B%E7%A2%91"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">2. 里程碑</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E8%BF%9B%E5%BA%A6%E6%A3%80%E6%9F%A5"><span class="toc-number">1.2.3.3.</span> <span class="toc-text">3. 进度检查</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-4-%E9%A1%B9%E7%9B%AE%E7%AE%A1%E7%90%86%E5%B7%A5%E5%85%B7%EF%BC%88%E7%94%98%E7%89%B9%E5%9B%BE%E3%80%81TAPD%E3%80%81%E7%A6%85%E9%81%93%E7%AD%89%EF%BC%89"><span class="toc-number">1.2.4.</span> <span class="toc-text">10.2.4 项目管理工具（甘特图、TAPD、禅道等）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%94%98%E7%89%B9%E5%9B%BE-Gantt-Chart"><span class="toc-number">1.2.4.1.</span> <span class="toc-text">1. 甘特图 (Gantt Chart)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-TAPD-%E7%A6%85%E9%81%93-Jira"><span class="toc-number">1.2.4.2.</span> <span class="toc-text">2. TAPD / 禅道 / Jira</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.2.5.</span> <span class="toc-text">10.2.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-3-%E4%BA%A7%E5%93%81%E9%9C%80%E6%B1%82%E8%AF%84%E5%AE%A1"><span class="toc-number">1.3.</span> <span class="toc-text">10.3 产品需求评审</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.3.1.</span> <span class="toc-text">10.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-2-%E9%9C%80%E6%B1%82%E7%B1%BB%E5%9E%8B%EF%BC%88%E4%B8%9A%E5%8A%A1%E9%9C%80%E6%B1%82%E3%80%81%E5%8A%9F%E8%83%BD%E9%9C%80%E6%B1%82%EF%BC%89"><span class="toc-number">1.3.2.</span> <span class="toc-text">10.3.2 需求类型（业务需求、功能需求）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-3-%E4%BC%9A%E8%AE%AE%E6%B5%81%E7%A8%8B%EF%BC%88%E5%87%86%E5%A4%87%E3%80%81%E8%BF%87%E7%A8%8B%E3%80%81%E4%BC%9A%E5%90%8E%E8%B7%9F%E8%B8%AA%EF%BC%89"><span class="toc-number">1.3.3.</span> <span class="toc-text">10.3.3 会议流程（准备、过程、会后跟踪）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-4-%E8%AF%84%E5%AE%A1%E7%BB%93%E6%9E%84%E4%B8%8E%E8%AE%B2%E8%A7%A3%E5%86%85%E5%AE%B9%EF%BC%88%E8%83%8C%E6%99%AF%E3%80%81%E5%8E%9F%E5%9E%8B%E3%80%81%E5%85%B6%E4%BB%96%E9%9C%80%E6%B1%82%EF%BC%89"><span class="toc-number">1.3.4.</span> <span class="toc-text">10.3.4 评审结构与讲解内容（背景、原型、其他需求）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-5-%E8%AF%84%E5%AE%A1%E8%A6%81%E7%82%B9%EF%BC%88%E6%97%B6%E9%97%B4%E4%B8%8E%E8%8A%82%E5%A5%8F%E6%8E%A7%E5%88%B6%E3%80%81%E6%8E%A7%E5%9C%BA%E4%B8%8E%E4%B8%BB%E8%A7%81%E3%80%81%E7%BB%93%E8%AE%BA%E6%94%B6%E5%B0%BE%EF%BC%89"><span class="toc-number">1.3.5.</span> <span class="toc-text">10.3.5 评审要点（时间与节奏控制、控场与主见、结论收尾）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-4-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.4.</span> <span class="toc-text">10.4 本章总结</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-1-%E8%AF%BE%E7%A8%8B%E5%86%85%E5%AE%B9%E5%9B%9E%E9%A1%BE"><span class="toc-number">1.4.1.</span> <span class="toc-text">10.4.1 课程内容回顾</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>