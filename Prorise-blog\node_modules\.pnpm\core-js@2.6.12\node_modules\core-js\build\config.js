module.exports = {
  list: [
    'es6.symbol',
    'es6.object.define-property',
    'es6.object.define-properties',
    'es6.object.get-own-property-descriptor',
    'es6.object.create',
    'es6.object.get-prototype-of',
    'es6.object.keys',
    'es6.object.get-own-property-names',
    'es6.object.freeze',
    'es6.object.seal',
    'es6.object.prevent-extensions',
    'es6.object.is-frozen',
    'es6.object.is-sealed',
    'es6.object.is-extensible',
    'es6.object.assign',
    'es6.object.is',
    'es6.object.set-prototype-of',
    'es6.object.to-string',
    'es6.function.bind',
    'es6.function.name',
    'es6.function.has-instance',
    'es6.number.constructor',
    'es6.number.to-fixed',
    'es6.number.to-precision',
    'es6.number.epsilon',
    'es6.number.is-finite',
    'es6.number.is-integer',
    'es6.number.is-nan',
    'es6.number.is-safe-integer',
    'es6.number.max-safe-integer',
    'es6.number.min-safe-integer',
    'es6.number.parse-float',
    'es6.number.parse-int',
    'es6.parse-int',
    'es6.parse-float',
    'es6.math.acosh',
    'es6.math.asinh',
    'es6.math.atanh',
    'es6.math.cbrt',
    'es6.math.clz32',
    'es6.math.cosh',
    'es6.math.expm1',
    'es6.math.fround',
    'es6.math.hypot',
    'es6.math.imul',
    'es6.math.log10',
    'es6.math.log1p',
    'es6.math.log2',
    'es6.math.sign',
    'es6.math.sinh',
    'es6.math.tanh',
    'es6.math.trunc',
    'es6.string.from-code-point',
    'es6.string.raw',
    'es6.string.trim',
    'es6.string.code-point-at',
    'es6.string.ends-with',
    'es6.string.includes',
    'es6.string.repeat',
    'es6.string.starts-with',
    'es6.string.iterator',
    'es6.string.anchor',
    'es6.string.big',
    'es6.string.blink',
    'es6.string.bold',
    'es6.string.fixed',
    'es6.string.fontcolor',
    'es6.string.fontsize',
    'es6.string.italics',
    'es6.string.link',
    'es6.string.small',
    'es6.string.strike',
    'es6.string.sub',
    'es6.string.sup',
    'es6.array.is-array',
    'es6.array.from',
    'es6.array.of',
    'es6.array.join',
    'es6.array.slice',
    'es6.array.sort',
    'es6.array.for-each',
    'es6.array.map',
    'es6.array.filter',
    'es6.array.some',
    'es6.array.every',
    'es6.array.reduce',
    'es6.array.reduce-right',
    'es6.array.index-of',
    'es6.array.last-index-of',
    'es6.array.copy-within',
    'es6.array.fill',
    'es6.array.find',
    'es6.array.find-index',
    'es6.array.iterator',
    'es6.array.species',
    'es6.regexp.constructor',
    'es6.regexp.exec',
    'es6.regexp.to-string',
    'es6.regexp.flags',
    'es6.regexp.match',
    'es6.regexp.replace',
    'es6.regexp.search',
    'es6.regexp.split',
    'es6.promise',
    'es6.map',
    'es6.set',
    'es6.weak-map',
    'es6.weak-set',
    'es6.reflect.apply',
    'es6.reflect.construct',
    'es6.reflect.define-property',
    'es6.reflect.delete-property',
    'es6.reflect.enumerate',
    'es6.reflect.get',
    'es6.reflect.get-own-property-descriptor',
    'es6.reflect.get-prototype-of',
    'es6.reflect.has',
    'es6.reflect.is-extensible',
    'es6.reflect.own-keys',
    'es6.reflect.prevent-extensions',
    'es6.reflect.set',
    'es6.reflect.set-prototype-of',
    'es6.date.now',
    'es6.date.to-json',
    'es6.date.to-iso-string',
    'es6.date.to-string',
    'es6.date.to-primitive',
    'es6.typed.array-buffer',
    'es6.typed.data-view',
    'es6.typed.int8-array',
    'es6.typed.uint8-array',
    'es6.typed.uint8-clamped-array',
    'es6.typed.int16-array',
    'es6.typed.uint16-array',
    'es6.typed.int32-array',
    'es6.typed.uint32-array',
    'es6.typed.float32-array',
    'es6.typed.float64-array',
    'es7.array.includes',
    'es7.array.flat-map',
    'es7.array.flatten',
    'es7.string.at',
    'es7.string.pad-start',
    'es7.string.pad-end',
    'es7.string.trim-left',
    'es7.string.trim-right',
    'es7.string.match-all',
    'es7.symbol.async-iterator',
    'es7.symbol.observable',
    'es7.object.get-own-property-descriptors',
    'es7.object.values',
    'es7.object.entries',
    'es7.object.define-getter',
    'es7.object.define-setter',
    'es7.object.lookup-getter',
    'es7.object.lookup-setter',
    'es7.map.to-json',
    'es7.set.to-json',
    'es7.map.of',
    'es7.set.of',
    'es7.weak-map.of',
    'es7.weak-set.of',
    'es7.map.from',
    'es7.set.from',
    'es7.weak-map.from',
    'es7.weak-set.from',
    'es7.global',
    'es7.system.global',
    'es7.error.is-error',
    'es7.math.clamp',
    'es7.math.deg-per-rad',
    'es7.math.degrees',
    'es7.math.fscale',
    'es7.math.iaddh',
    'es7.math.isubh',
    'es7.math.imulh',
    'es7.math.rad-per-deg',
    'es7.math.radians',
    'es7.math.scale',
    'es7.math.umulh',
    'es7.math.signbit',
    'es7.promise.finally',
    'es7.promise.try',
    'es7.reflect.define-metadata',
    'es7.reflect.delete-metadata',
    'es7.reflect.get-metadata',
    'es7.reflect.get-metadata-keys',
    'es7.reflect.get-own-metadata',
    'es7.reflect.get-own-metadata-keys',
    'es7.reflect.has-metadata',
    'es7.reflect.has-own-metadata',
    'es7.reflect.metadata',
    'es7.asap',
    'es7.observable',
    'web.immediate',
    'web.dom.iterable',
    'web.timers',
    'core.dict',
    'core.get-iterator-method',
    'core.get-iterator',
    'core.is-iterable',
    'core.delay',
    'core.function.part',
    'core.object.is-object',
    'core.object.classof',
    'core.object.define',
    'core.object.make',
    'core.number.iterator',
    'core.regexp.escape',
    'core.string.escape-html',
    'core.string.unescape-html',
  ],
  experimental: [
  ],
  libraryBlacklist: [
    'es6.object.to-string',
    'es6.function.name',
    'es6.regexp.constructor',
    'es6.regexp.to-string',
    'es6.regexp.flags',
    'es6.regexp.match',
    'es6.regexp.replace',
    'es6.regexp.search',
    'es6.regexp.split',
    'es6.number.constructor',
    'es6.date.to-string',
    'es6.date.to-primitive',
  ],
  es5SpecialCase: [
    'es6.object.create',
    'es6.object.define-property',
    'es6.object.define-properties',
    'es6.object.get-own-property-descriptor',
    'es6.object.get-prototype-of',
    'es6.object.keys',
    'es6.object.get-own-property-names',
    'es6.object.freeze',
    'es6.object.seal',
    'es6.object.prevent-extensions',
    'es6.object.is-frozen',
    'es6.object.is-sealed',
    'es6.object.is-extensible',
    'es6.function.bind',
    'es6.array.is-array',
    'es6.array.join',
    'es6.array.slice',
    'es6.array.sort',
    'es6.array.for-each',
    'es6.array.map',
    'es6.array.filter',
    'es6.array.some',
    'es6.array.every',
    'es6.array.reduce',
    'es6.array.reduce-right',
    'es6.array.index-of',
    'es6.array.last-index-of',
    'es6.number.to-fixed',
    'es6.number.to-precision',
    'es6.date.now',
    'es6.date.to-iso-string',
    'es6.date.to-json',
    'es6.string.trim',
    'es6.regexp.to-string',
    'es6.parse-int',
    'es6.parse-float',
  ],
  banner: '/**\n' +
          ' * core-js ' + require('../package').version + '\n' +
          ' * https://github.com/zloirock/core-js\n' +
          ' * License: http://rock.mit-license.org\n' +
          ' * © ' + new Date().getFullYear() + ' Denis Pushkarev\n' +
          ' */',
};
