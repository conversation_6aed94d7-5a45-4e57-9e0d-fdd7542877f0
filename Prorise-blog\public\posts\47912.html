<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇) | Prorise的小站</title><meta name="keywords" content="Java微服务篇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)"><meta name="application-name" content="SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)"><meta property="og:url" content="https://prorise666.site/posts/47912.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)如果我们把上一章的 Embedding 比作将书本（数据）的内容提炼成的知识卡片（向量），那么向量数据库（Vector Store） 就是存放这些卡片的、拥有超凡检索能力的巨大图书馆。它的核心价值在于，能基于向量间的“空"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta name="description" content="8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)如果我们把上一章的 Embedding 比作将书本（数据）的内容提炼成的知识卡片（向量），那么向量数据库（Vector Store） 就是存放这些卡片的、拥有超凡检索能力的巨大图书馆。它的核心价值在于，能基于向量间的“空"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/47912.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)",postAI:"true",pageFillDescription:"8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇), 8.1 核心 API 深度解析, 8.1.1 VectorStore：统一的向量操作门面, 8.1.2 SearchRequest：构建你的精确检索意图, 8.1.3 元数据过滤：大海捞针的利器, 8.2 Redis 向量存储实战, 8.2.1 环境准备：启动 Redis Stack, 8.2.2 Spring Boot 自动配置集成, 8.2.3 升级现有服务：将向量存入Redis, 8.2.4 效果检验：在 Navicat 中查看数据构建的长期记忆实战篇如果我们把上一章的比作将书本数据的内容提炼成的知识卡片向量那么向量数据库就是存放这些卡片的拥有超凡检索能力的巨大图书馆它的核心价值在于能基于向量间的空间距离进行高效的相似性搜索这种能力是构建任何高级知识库或检索增强生成应用不可或缺的记忆体本章我们将以官方文档为蓝本深入剖析的设计并专注于将我们的知识库服务无缝对接到一个业界领先的高性能内存数据库上并通过完整的代码实战和数据验证来检验我们的成果核心深度解析在开始编码前我们必须先深入理解提供的接口及其相关组件的最新设计统一的向量操作门面是设计的用于屏蔽底层不同向量数据库实现差异的统一接口无论我们后端使用的是还是上层业务代码的调用方式都保持一致核心方法简要描述数据入库将文档列表批量存入数据库数据检索根据搜索请求执行相似度搜索按删除根据文档列表精确删除按条件删除根据元数据过滤表达式批量删除方法这是数据写入的唯一入口一个关键特性是此方法会自动调用我们项目配置好的在后台将的文本内容转换为向量然后连同元数据一起存入数据库开发者无需手动进行向量化方法这是数据检索的核心它接收一个对象该对象封装了所有的查询条件包括查询文本期望返回的数量相似度阈值以及元数据过滤器方法提供了两种删除方式按删除非常高效适用于精确操作按条件删除则更为灵活是实现数据管理版本控制等复杂逻辑的基础构建你的精确检索意图是一个功能强大的请求构建器它允许我们精细地控制搜索行为根据最新官方其方法没有前缀方法作用必需设置用于语义搜索的查询文本设置返回最相似结果的最大数量设置相似度得分阈值到关键应用元数据过滤表达式这是搜索的灵魂通常是用户的原始问题或一段用于匹配的文本控制返回结果的数量在应用中这个值的设定需要权衡太小可能导致上下文不足太大则可能引入不相关噪声并超出模型的上下文窗口限制通常从一个较小的值如或开始实验这是一个过滤器只有相似度得分高于此阈值的文档才会被返回在对结果相关性要求极高的场景下设置一个较高的值如以上能有效提升结果质量这是实现混合搜索的关键它允许我们在进行语义搜索的同时对文档的结构化元数据进行子句式的精确过滤极大地提升了搜索的精准度元数据过滤大海捞针的利器元数据过滤允许我们在语义搜索的同时对文档的结构化元数据如类别年份标签等进行精确的条件过滤其表达式语法非常直观类别支持的操作符比较操作符等于不等于大于大于等于小于小于等于集合操作符包含于不包含于逻辑操作符或或场景应用文档版本管理官方文档提供了一个极佳的用例管理文档版本当我们需要发布新版文档时一个安全的操作是先删除旧版再添加新版我们可以通过元数据轻松实现这一点这是一个在层中演示版本管理的示例假设新旧文档通过元数据中的关联并通过区分构造一个精确的过滤器表达式定位到旧版本的文档使用该表达式删除旧版本添加新版本的文档向量存储实战作为家喻户晓的高性能内存数据库通过其模块提供了强大的向量搜索能力它非常适合需要将缓存消息队列和向量搜索等功能整合在同一个技术栈中并追求极致读写性能的场景环境准备启动要解锁的向量搜索功能必须使用包含模块的版本而非普通的规避端口冲突一个常见的开发陷阱是很多开发者本地已经运行了一个标准的服务占用了默认的端口为了避免冲突我们将特意为我们的应用在端口上启动一个新的实例使用启动推荐在项目根目录下创建文件将容器的端口映射到主机的端口管理后台的端口保持不变确保你的桌面版或服务已经启动然后在文件所在的目录运行命令即可在后台启动服务自动配置集成第一步添加依赖在中加入为提供的请确保您也已经添加了的例如我们项目中使用的智谱第二步配置在配置文件中我们提供的连接信息并对进行参数配置注意将端口号修改为配置连接信息关键连接到我们为应用指定的端口的专属配置区指定在中创建的索引名称便于管理关键在开发时设为让在应用启动时自动检查并创建向量索引在生产环境中推荐设为并手动管理索引以获得更好的控制升级现有服务将向量存入当您调用时在其内部为您执行了以下一系列操作接收对象方法接收到您传入的列表提取文本内容它会遍历列表中的每一个对象并取出其属性调用内部在被自动创建时已经将您配置好的在我们的例子中是注入到了它自己的内部此时它会调用这个内部持有的实例将提取出的所有文本内容进行批量向量化关联向量与文档将生成的一批向量与原始的对象一一对应起来写入最后它连接到为每一个创建一个并将文档的以及刚刚生成的向量作为不同的字段一并写入到这个中现在我们对上一章的进行一次关键的升级它的职责不再仅仅是向量化而是将向量化后的文档持久化存储到中目标修改注入并在处理文件后调用方法代码实现导入注入会根据配置自动创建实例升级版将上传的文件内容向量化并存入用户上传的文件包含文档和成功信息的字符串创建一个对象这是操作的基本单位我们可以将文件名等信息作为元数据一并存入准备将文档添加到调用方法完成向量化和入库的全部操作文档已成功存入返回更详细的成功信息文件已成功存入知识库文档为异常处理保持不变效果检验在中查看数据现在我们服务的核心能力已经升级虽然前端界面和上一章一样但每次上传文件后数据都会被永久地存入让我们来亲眼验证一下重新上传文件启动应用像上一章一样通过前端界面或上传一个文件连接打开点击连接在连接设置中主机填写端口填写我们指定的点击连接测试成功后保存连接查找并检视数据双击打开连接会列出中的所有默认会为存入的每个创建一个类型的这个的命名格式通常是根据我们的配置前缀通常是所以你应该能找到类似的双击打开这个你将看到一个清晰的哈希表结构字段存储了你上传文件的完整原始内容字段存储了二进制格式的向量数据在中可能显示为乱码或十六进制这是正常的我们自定义的元数据我们自定义的元数据附录附录",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-08 13:53:41",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#8-Vector-Stores%EF%BC%9A%E6%9E%84%E5%BB%BA-AI-%E7%9A%84%E9%95%BF%E6%9C%9F%E8%AE%B0%E5%BF%86-Redis-%E5%AE%9E%E6%88%98%E7%AF%87"><span class="toc-text">8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-%E6%A0%B8%E5%BF%83-API-%E6%B7%B1%E5%BA%A6%E8%A7%A3%E6%9E%90"><span class="toc-text">8.1 核心 API 深度解析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-1-VectorStore%EF%BC%9A%E7%BB%9F%E4%B8%80%E7%9A%84%E5%90%91%E9%87%8F%E6%93%8D%E4%BD%9C%E9%97%A8%E9%9D%A2"><span class="toc-text">8.1.1 VectorStore：统一的向量操作门面</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-2-SearchRequest%EF%BC%9A%E6%9E%84%E5%BB%BA%E4%BD%A0%E7%9A%84%E7%B2%BE%E7%A1%AE%E6%A3%80%E7%B4%A2%E6%84%8F%E5%9B%BE"><span class="toc-text">8.1.2 SearchRequest：构建你的精确检索意图</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-3-%E5%85%83%E6%95%B0%E6%8D%AE%E8%BF%87%E6%BB%A4%EF%BC%9A%E5%A4%A7%E6%B5%B7%E6%8D%9E%E9%92%88%E7%9A%84%E5%88%A9%E5%99%A8"><span class="toc-text">8.1.3 元数据过滤：大海捞针的利器</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-Redis-%E5%90%91%E9%87%8F%E5%AD%98%E5%82%A8%E5%AE%9E%E6%88%98"><span class="toc-text">8.2 Redis 向量存储实战</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-1-%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87%EF%BC%9A%E5%90%AF%E5%8A%A8-Redis-Stack"><span class="toc-text">8.2.1 环境准备：启动 Redis Stack</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-2-Spring-Boot-%E8%87%AA%E5%8A%A8%E9%85%8D%E7%BD%AE%E9%9B%86%E6%88%90"><span class="toc-text">8.2.2 Spring Boot 自动配置集成</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-3-%E5%8D%87%E7%BA%A7%E7%8E%B0%E6%9C%89%E6%9C%8D%E5%8A%A1%EF%BC%9A%E5%B0%86%E5%90%91%E9%87%8F%E5%AD%98%E5%85%A5Redis"><span class="toc-text">8.2.3 升级现有服务：将向量存入Redis</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-4-%E6%95%88%E6%9E%9C%E6%A3%80%E9%AA%8C%EF%BC%9A%E5%9C%A8-Navicat-%E4%B8%AD%E6%9F%A5%E7%9C%8B%E6%95%B0%E6%8D%AE"><span class="toc-text">8.2.4 效果检验：在 Navicat 中查看数据</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5f2a23">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#277340">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#c72008">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#11a7a2">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#276d10">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#6d6a95">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java微服务篇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-03-21T05:13:45.000Z" title="发表于 2025-03-21 13:13:45">2025-03-21</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:41.185Z" title="更新于 2025-07-08 13:53:41">2025-07-08</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>10分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/47912.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/47912.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url">Java微服务篇</a><h1 id="CrawlerTitle" itemprop="name headline">SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-03-21T05:13:45.000Z" title="发表于 2025-03-21 13:13:45">2025-03-21</time><time itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:41.185Z" title="更新于 2025-07-08 13:53:41">2025-07-08</time></header><div id="postchat_postcontent"><h2 id="8-Vector-Stores：构建-AI-的长期记忆-Redis-实战篇"><a href="#8-Vector-Stores：构建-AI-的长期记忆-Redis-实战篇" class="headerlink" title="8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)"></a>8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</h2><p>如果我们把上一章的 Embedding 比作将书本（数据）的内容提炼成的知识卡片（向量），那么<strong>向量数据库（Vector Store）</strong> 就是存放这些卡片的、拥有超凡检索能力的巨大图书馆。它的核心价值在于，能基于向量间的“空间距离”进行高效的<strong>相似性搜索</strong>。这种能力是构建任何高级 AI 知识库或<strong>检索增强生成（RAG）</strong> 应用不可或缺的“记忆体”。</p><p>本章，我们将以官方文档为蓝本，深入剖析 <code>VectorStore</code> 的 API 设计，并专注于将我们的知识库服务无缝对接到一个业界领先的高性能内存数据库——<strong>Redis</strong>上，并通过完整的代码实战和数据验证来检验我们的成果。</p><h3 id="8-1-核心-API-深度解析"><a href="#8-1-核心-API-深度解析" class="headerlink" title="8.1 核心 API 深度解析"></a>8.1 核心 API 深度解析</h3><p>在开始编码前，我们必须先深入理解 Spring AI 提供的 <code>VectorStore</code> 接口及其相关组件的最新设计。</p><h4 id="8-1-1-VectorStore：统一的向量操作门面"><a href="#8-1-1-VectorStore：统一的向量操作门面" class="headerlink" title="8.1.1 VectorStore：统一的向量操作门面"></a>8.1.1 <code>VectorStore</code>：统一的向量操作门面</h4><p><code>VectorStore</code> 是 Spring AI 设计的、用于屏蔽底层不同向量数据库实现差异的统一接口。无论我们后端使用的是 Redis、PGVector 还是 Chroma，上层业务代码的调用方式都保持一致。</p><table><thead><tr><th align="left"><code>VectorStore</code> 核心方法</th><th align="left">简要描述</th></tr></thead><tbody><tr><td align="left"><code>void add(List&lt;Document&gt; documents)</code></td><td align="left"><strong>数据入库</strong>：将文档列表批量存入数据库。</td></tr><tr><td align="left"><code>List&lt;Document&gt; similaritySearch(SearchRequest request)</code></td><td align="left"><strong>数据检索</strong>：根据搜索请求执行相似度搜索。</td></tr><tr><td align="left"><code>void delete(List&lt;String&gt; idList)</code></td><td align="left"><strong>按 ID 删除</strong>：根据文档 ID 列表精确删除。</td></tr><tr><td align="left"><code>void delete(Filter.Expression filterExpression)</code></td><td align="left"><strong>按条件删除</strong>：根据元数据过滤表达式批量删除。</td></tr></tbody></table><ul><li><strong><code>add</code> 方法</strong>: 这是数据写入的唯一入口。一个关键特性是，此方法会自动调用我们项目配置好的 <code>EmbeddingModel</code>，在后台将 <code>Document</code> 的文本内容转换为向量，然后连同元数据一起存入数据库。开发者无需手动进行向量化。</li><li><strong><code>similaritySearch</code> 方法</strong>: 这是数据检索的核心。它接收一个 <code>SearchRequest</code> 对象，该对象封装了所有的查询条件，包括查询文本、期望返回的数量（topK）、相似度阈值以及元数据过滤器。</li><li><strong><code>delete</code> 方法</strong>: 提供了两种删除方式。按 ID 删除非常高效，适用于精确操作；按条件（Filter）删除则更为灵活，是实现数据管理、版本控制等复杂逻辑的基础。</li></ul><h4 id="8-1-2-SearchRequest：构建你的精确检索意图"><a href="#8-1-2-SearchRequest：构建你的精确检索意图" class="headerlink" title="8.1.2 SearchRequest：构建你的精确检索意图"></a>8.1.2 <code>SearchRequest</code>：构建你的精确检索意图</h4><p><code>SearchRequest</code> 是一个功能强大的请求构建器 (Builder)，它允许我们精细地控制搜索行为。根据最新官方 API，其 Builder 方法<strong>没有 <code>with</code> 前缀</strong>。</p><table><thead><tr><th align="left">Builder 方法</th><th align="left">作用</th></tr></thead><tbody><tr><td align="left"><code>query(String query)</code></td><td align="left"><strong>(必需)</strong> 设置用于语义搜索的查询文本。</td></tr><tr><td align="left"><code>topK(int topK)</code></td><td align="left">设置返回最相似结果的最大数量。</td></tr><tr><td align="left"><code>similarityThreshold(double threshold)</code></td><td align="left">设置相似度得分阈值 (0.0 到 1.0)。</td></tr><tr><td align="left"><code>filterExpression(String expression)</code></td><td align="left"><strong>(关键)</strong> 应用元数据过滤表达式。</td></tr></tbody></table><ul><li><strong><code>query(String)</code></strong>: 这是搜索的“灵魂”，通常是用户的原始问题或一段用于匹配的文本。</li><li><strong><code>topK(int)</code></strong>: 控制返回结果的数量。在 RAG 应用中，这个值的设定需要权衡——太小可能导致上下文不足，太大则可能引入不相关噪声并超出模型的上下文窗口限制。通常从一个较小的值 (如 3 或 5) 开始实验。</li><li><strong><code>similarityThreshold(double)</code></strong>: 这是一个过滤器，只有相似度得分高于此阈值的文档才会被返回。在对结果相关性要求极高的场景下，设置一个较高的值 (如 0.75 以上) 能有效提升结果质量。</li><li><strong><code>filterExpression(String)</code></strong>: 这是实现“混合搜索”的关键，它允许我们在进行语义搜索的同时，对文档的结构化元数据进行 SQL <code>WHERE</code> 子句式的精确过滤，极大地提升了搜索的精准度。</li></ul><h4 id="8-1-3-元数据过滤：大海捞针的利器"><a href="#8-1-3-元数据过滤：大海捞针的利器" class="headerlink" title="8.1.3 元数据过滤：大海捞针的利器"></a>8.1.3 元数据过滤：大海捞针的利器</h4><p><strong>元数据过滤</strong>允许我们在语义搜索的同时，对文档的结构化元数据（如类别、年份、标签等）进行精确的条件过滤。其表达式语法非常直观。</p><table><thead><tr><th align="left">类别</th><th align="left">支持的操作符</th></tr></thead><tbody><tr><td align="left"><strong>比较操作符</strong></td><td align="left"><code>==</code> (等于), <code>!=</code> (不等于), <code>&gt;</code> (大于), <code>&gt;=</code> (大于等于), <code>&lt;</code> (小于), <code>&lt;=</code> (小于等于)</td></tr><tr><td align="left"><strong>集合操作符</strong></td><td align="left"><code>IN</code> (包含于), <code>NIN</code> (不包含于)</td></tr><tr><td align="left"><strong>逻辑操作符</strong></td><td align="left"><code>AND</code> (或 <code>&amp;&amp;</code>), <code>OR</code> (或 `</td></tr></tbody></table><ul><li><p><strong>场景应用：文档版本管理</strong><br>官方文档提供了一个极佳的用例：管理文档版本。当我们需要发布新版文档时，一个安全的操作是先删除旧版，再添加新版。我们可以通过元数据轻松实现这一点。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 这是一个在 Service 层中演示版本管理的示例</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">updateDocument</span><span class="params">(Document newVersionDocument)</span> {</span><br><span class="line">    <span class="comment">// 假设新旧文档通过元数据中的 'docId' 关联，并通过 'version' 区分</span></span><br><span class="line">    <span class="type">String</span> <span class="variable">docId</span> <span class="operator">=</span> (String) newVersionDocument.getMetadata().get(<span class="string">"docId"</span>);</span><br><span class="line">    <span class="type">String</span> <span class="variable">oldVersion</span> <span class="operator">=</span> <span class="string">"1.0"</span>; </span><br><span class="line"></span><br><span class="line">    <span class="comment">// 1. 构造一个精确的过滤器表达式，定位到旧版本的文档</span></span><br><span class="line">    <span class="type">String</span> <span class="variable">filterExpression</span> <span class="operator">=</span> String.format(<span class="string">"docId == '%s' AND version == '%s'"</span>, docId, oldVersion);</span><br><span class="line">    </span><br><span class="line">    <span class="comment">// 2. 使用该表达式删除旧版本</span></span><br><span class="line">    vectorStore.delete(filterExpression);</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 3. 添加新版本的文档</span></span><br><span class="line">    vectorStore.add(List.of(newVersionDocument));</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h3 id="8-2-Redis-向量存储实战"><a href="#8-2-Redis-向量存储实战" class="headerlink" title="8.2 Redis 向量存储实战"></a>8.2 Redis 向量存储实战</h3><p>Redis，作为家喻户晓的高性能内存数据库，通过其 <strong>RediSearch</strong> 模块提供了强大的向量搜索能力。它非常适合需要将缓存、消息队列和向量搜索等功能整合在同一个技术栈中，并追求极致读写性能的场景。</p><h4 id="8-2-1-环境准备：启动-Redis-Stack"><a href="#8-2-1-环境准备：启动-Redis-Stack" class="headerlink" title="8.2.1 环境准备：启动 Redis Stack"></a>8.2.1 环境准备：启动 Redis Stack</h4><p>要解锁 Redis 的向量搜索功能，必须使用包含 <code>RediSearch</code> 模块的 <strong>Redis Stack</strong> 版本，而非普通的 Redis。</p><ul><li><p><strong>规避端口冲突</strong><br>一个常见的开发陷阱是，很多开发者本地已经运行了一个标准的 Redis 服务，占用了默认的 <code>6379</code> 端口。为了避免冲突，我们将特意为我们的 AI 应用在 <code>6380</code> 端口上启动一个新的 Redis Stack 实例。</p></li><li><p><strong>使用 Docker 启动 (推荐)</strong><br>在项目根目录下创建 <code>docker-compose.yml</code> 文件。</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># docker-compose.yml</span></span><br><span class="line"><span class="attr">version:</span> <span class="string">'3.8'</span></span><br><span class="line"><span class="attr">services:</span></span><br><span class="line">  <span class="attr">redis-stack-for-ai:</span></span><br><span class="line">    <span class="attr">image:</span> <span class="string">redis/redis-stack:latest</span></span><br><span class="line">    <span class="attr">container_name:</span> <span class="string">redis-stack-for-ai</span></span><br><span class="line">    <span class="attr">ports:</span></span><br><span class="line">      <span class="comment"># 将容器的 6379 端口映射到主机的 6380 端口</span></span><br><span class="line">      <span class="bullet">-</span> <span class="string">"6380:6379"</span></span><br><span class="line">      <span class="comment"># RedisInsight 管理后台的端口保持不变</span></span><br><span class="line">      <span class="bullet">-</span> <span class="string">"8001:8001"</span> </span><br></pre></td></tr></tbody></table></figure><p>确保你的Docker桌面版（Windows）或Docker服务已经启动，然后在 <code>docker-compose.yml</code> 文件所在的目录运行 <code>docker-compose up -d</code> 命令即可在后台启动服务。</p></li></ul><h4 id="8-2-2-Spring-Boot-自动配置集成"><a href="#8-2-2-Spring-Boot-自动配置集成" class="headerlink" title="8.2.2 Spring Boot 自动配置集成"></a>8.2.2 Spring Boot 自动配置集成</h4><ul><li><p><strong>第一步：添加 Maven 依赖</strong><br>在 <code>pom.xml</code> 中加入 Spring AI 为 Redis 提供的 Starter。请确保您也已经添加了 Embedding Model 的 Starter，例如我们项目中使用的智谱 AI。</p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-starter-vector-store-redis<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line"></span><br><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-starter-model-zhipuai<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>第二步：<code>application.yml</code> 配置</strong><br>在配置文件中，我们提供 Redis 的连接信息，并对 <code>VectorStore</code> 进行参数配置，<strong>注意将端口号修改为 <code>6380</code></strong>。</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># src/main/resources/application.yml</span></span><br><span class="line"></span><br><span class="line"><span class="attr">spring:</span></span><br><span class="line">  <span class="comment"># 配置 Redis 连接信息</span></span><br><span class="line">  <span class="attr">data:</span></span><br><span class="line">    <span class="attr">redis:</span></span><br><span class="line">      <span class="attr">host:</span> <span class="string">localhost</span></span><br><span class="line">      <span class="comment"># 关键：连接到我们为AI应用指定的 6380 端口</span></span><br><span class="line">      <span class="attr">port:</span> <span class="number">6380</span></span><br><span class="line">  </span><br><span class="line">  <span class="attr">ai:</span></span><br><span class="line">    <span class="comment"># VectorStore 的专属配置区</span></span><br><span class="line">    <span class="attr">vector-store:</span></span><br><span class="line">      <span class="attr">redis:</span></span><br><span class="line">        <span class="comment"># 指定在 Redis 中创建的索引名称，便于管理</span></span><br><span class="line">        <span class="attr">index-name:</span> <span class="string">ai-copilot-kb-index</span></span><br><span class="line">        <span class="comment"># 关键：在开发时设为true，让Spring AI在应用启动时自动检查并创建向量索引。</span></span><br><span class="line">        <span class="comment"># 在生产环境中，推荐设为 false，并手动管理索引以获得更好的控制。</span></span><br><span class="line">        <span class="attr">initialize-schema:</span> <span class="literal">true</span></span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="8-2-3-升级现有服务：将向量存入Redis"><a href="#8-2-3-升级现有服务：将向量存入Redis" class="headerlink" title="8.2.3 升级现有服务：将向量存入Redis"></a>8.2.3 升级现有服务：将向量存入Redis</h4><p>当您调用 <code>vectorStore.add()</code> 时，Spring AI 在其内部为您执行了以下一系列操作：</p><ol><li><strong>接收 <code>Document</code> 对象</strong>：<code>add</code> 方法接收到您传入的 <code>Document</code> 列表。</li><li><strong>提取文本内容</strong>：它会遍历列表中的每一个 <code>Document</code> 对象，并取出其 <code>content</code> 属性。</li><li><strong>调用内部 <code>EmbeddingModel</code></strong>：<code>RedisVectorStore</code> 在被 Spring 自动创建时，已经将您配置好的 <code>EmbeddingModel</code>（在我们的例子中是 <code>ZhiPuAiEmbeddingModel</code>）<strong>注入</strong>到了它自己的内部。此时，它会调用这个内部持有的 <code>embeddingModel</code> 实例，将提取出的所有文本内容进行批量向量化。</li><li><strong>关联向量与文档</strong>：将生成的一批向量与原始的 <code>Document</code> 对象一一对应起来。</li><li><strong>写入 Redis</strong>：最后，它连接到 Redis，为每一个 <code>Document</code> 创建一个 Hash，并将文档的 <code>content</code>、<code>metadata</code> 以及刚刚生成的<strong>向量 <code>embedding</code></strong> 作为不同的字段，一并写入到这个 Hash 中。`</li></ol><p>现在，我们对上一章的 <code>DocumentEmbeddingService</code> 进行一次关键的升级。它的职责不再仅仅是向量化，而是<strong>将向量化后的文档持久化存储到 Redis 中</strong>。</p><ul><li><p><strong>目标</strong>：修改 <code>DocumentEmbeddingService</code>，注入 <code>VectorStore</code>，并在处理文件后调用 <code>vectorStore.add()</code> 方法。</p></li><li><p><strong>代码实现</strong> (<code>service/DocumentEmbeddingService.java</code>):</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/service/DocumentEmbeddingService.java</span></span><br><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.validation.FileValidator;</span><br><span class="line"><span class="keyword">import</span> lombok.RequiredArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> lombok.extern.slf4j.Slf4j;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.document.Document;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.VectorStore; <span class="comment">// 1. 导入 VectorStore</span></span><br><span class="line"><span class="keyword">import</span> org.springframework.stereotype.Service;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.multipart.MultipartFile;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"><span class="keyword">import</span> java.nio.charset.StandardCharsets;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Slf4j</span></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="meta">@RequiredArgsConstructor</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">DocumentEmbeddingService</span> {</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 2. 注入 VectorStore，Spring AI 会根据配置自动创建 RedisVectorStore 实例</span></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> VectorStore vectorStore;</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * [升级版] 将上传的文件内容向量化并存入 Redis VectorStore。</span></span><br><span class="line"><span class="comment">     *</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> file 用户上传的文件</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 包含文档ID和成功信息的字符串</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">embedDocument</span><span class="params">(MultipartFile file)</span> {</span><br><span class="line">        FileValidator.validateFile(file);</span><br><span class="line"></span><br><span class="line">        <span class="keyword">try</span> {</span><br><span class="line">            <span class="type">String</span> <span class="variable">content</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">String</span>(file.getBytes(), StandardCharsets.UTF_8);</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 3. 创建一个 Document 对象，这是 VectorStore 操作的基本单位</span></span><br><span class="line">            <span class="comment">// 我们可以将文件名等信息作为元数据一并存入</span></span><br><span class="line">            <span class="type">Document</span> <span class="variable">document</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">Document</span>(content, Map.of(</span><br><span class="line">                <span class="string">"filename"</span>, file.getOriginalFilename(),</span><br><span class="line">                <span class="string">"size"</span>, file.getSize()</span><br><span class="line">            ));</span><br><span class="line"></span><br><span class="line">            log.info(<span class="string">"准备将文档 '{}' 添加到 VectorStore..."</span>, file.getOriginalFilename());</span><br><span class="line"></span><br><span class="line">            <span class="comment">// 4. 调用 vectorStore.add() 方法，完成向量化和入库的全部操作</span></span><br><span class="line">            vectorStore.add(List.of(document));</span><br><span class="line"></span><br><span class="line">            log.info(<span class="string">"文档 '{}' 已成功存入 Redis，ID: {}"</span>, file.getOriginalFilename(), document.getId());</span><br><span class="line"></span><br><span class="line">            <span class="comment">// 5. 返回更详细的成功信息</span></span><br><span class="line">            <span class="keyword">return</span> String.format(<span class="string">"文件 '%s' 已成功存入知识库！文档ID为: %s"</span>, </span><br><span class="line">                                 file.getOriginalFilename(), document.getId());</span><br><span class="line"></span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            <span class="comment">// ... 异常处理保持不变 ...</span></span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="8-2-4-效果检验：在-Navicat-中查看数据"><a href="#8-2-4-效果检验：在-Navicat-中查看数据" class="headerlink" title="8.2.4 效果检验：在 Navicat 中查看数据"></a>8.2.4 效果检验：在 Navicat 中查看数据</h4><p>现在，我们服务的核心能力已经升级。虽然前端界面和上一章一样，但每次上传文件后，数据都会被永久地存入 Redis。让我们来亲眼验证一下。</p><ol><li><p><strong>重新上传文件</strong>：启动应用，像上一章一样，通过前端界面或 Postman 上传一个 <code>.txt</code> 文件。</p></li><li><p><strong>连接 Navicat for Redis</strong>：</p><ul><li>打开 Navicat，点击“连接” -&gt; “Redis…”。</li><li>在连接设置中，<strong>主机</strong>填写 <code>localhost</code>，<strong>端口</strong>填写我们指定的 <code>6380</code>。</li><li>点击“连接测试”，成功后保存连接。</li></ul></li><li><p><strong>查找并检视数据</strong>：</p><ul><li><p>双击打开连接，Navicat 会列出 Redis 中的所有 key。</p></li><li><p>Spring AI 默认会为存入的每个 <code>Document</code> 创建一个 Redis <code>Hash</code> 类型的 key。这个 key 的命名格式通常是 <code>prefix:id</code>。根据我们的配置 (<code>index-name: ai-copilot-kb-index</code>)，前缀通常是 <code>doc:ai-copilot-kb-index</code>。所以你应该能找到类似 <code>doc:ai-copilot-kb-index:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</code> 的 key。</p></li><li><p>双击打开这个 key，你将看到一个清晰的哈希表结构：</p><ul><li><strong><code>content</code></strong>: 字段存储了你上传文件的完整原始内容。</li><li><strong><code>embedding</code></strong>: 字段存储了二进制格式的向量数据（在 Navicat 中可能显示为乱码或十六进制，这是正常的）。</li><li><strong><code>metadata_filename</code></strong>: 我们自定义的元数据 <code>filename</code>。</li><li><strong><code>metadata_size</code></strong>: 我们自定义的元数据 <code>size</code>。</li></ul><figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">    <span class="attr">"filename"</span><span class="punctuation">:</span> <span class="string">"附录.md"</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"size"</span><span class="punctuation">:</span> <span class="number">3496</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"embedding"</span><span class="punctuation">:</span> <span class="punctuation">[</span> - <span class="number">0.009663644</span><span class="punctuation">,</span> <span class="number">0.0067672133</span><span class="punctuation">,</span>  - <span class="number">0.026408637</span><span class="punctuation">,</span> <span class="number">0.0010488915</span><span class="punctuation">]</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"content"</span><span class="punctuation">:</span> <span class="string">"---\ntitle: 附录\nc...."</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li></ul></li></ol><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/47912.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/47912.html&quot;)">SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/47912.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)&amp;url=https://prorise666.site/posts/47912.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java微服务篇<span class="tagsPageCount">11</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/5770.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">SpringAI（七）：7. Embedding Models：万物皆可向量化</div></div></a></div><div class="next-post pull-right"><a href="/posts/22322.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/59358.html" title="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</div></div></a></div><div><a href="/posts/5770.html" title="SpringAI（七）：7. Embedding Models：万物皆可向量化"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（七）：7. Embedding Models：万物皆可向量化</div></div></a></div><div><a href="/posts/60609.html" title="SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南</div></div></a></div><div><a href="/posts/52289.html" title="SpringAI（三）：3. 会话核心 API 深度解析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（三）：3. 会话核心 API 深度解析</div></div></a></div><div><a href="/posts/22322.html" title="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</div></div></a></div><div><a href="/posts/18714.html" title="SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)",date:"2025-03-21 13:13:45",updated:"2025-07-08 13:53:41",tags:["Java微服务篇"],categories:["后端技术","Java"],content:'\n## 8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)\n\n如果我们把上一章的 Embedding 比作将书本（数据）的内容提炼成的知识卡片（向量），那么**向量数据库（Vector Store）** 就是存放这些卡片的、拥有超凡检索能力的巨大图书馆。它的核心价值在于，能基于向量间的“空间距离”进行高效的**相似性搜索**。这种能力是构建任何高级 AI 知识库或**检索增强生成（RAG）** 应用不可或缺的“记忆体”。\n\n本章，我们将以官方文档为蓝本，深入剖析 `VectorStore` 的 API 设计，并专注于将我们的知识库服务无缝对接到一个业界领先的高性能内存数据库——**Redis**上，并通过完整的代码实战和数据验证来检验我们的成果。\n\n### 8.1 核心 API 深度解析\n\n在开始编码前，我们必须先深入理解 Spring AI 提供的 `VectorStore` 接口及其相关组件的最新设计。\n\n#### 8.1.1 `VectorStore`：统一的向量操作门面\n\n`VectorStore` 是 Spring AI 设计的、用于屏蔽底层不同向量数据库实现差异的统一接口。无论我们后端使用的是 Redis、PGVector 还是 Chroma，上层业务代码的调用方式都保持一致。\n\n| `VectorStore` 核心方法 | 简要描述 |\n| :--- | :--- |\n| `void add(List<Document> documents)` | **数据入库**：将文档列表批量存入数据库。 |\n| `List<Document> similaritySearch(SearchRequest request)` | **数据检索**：根据搜索请求执行相似度搜索。 |\n| `void delete(List<String> idList)` | **按 ID 删除**：根据文档 ID 列表精确删除。 |\n| `void delete(Filter.Expression filterExpression)` | **按条件删除**：根据元数据过滤表达式批量删除。 |\n\n  * **`add` 方法**: 这是数据写入的唯一入口。一个关键特性是，此方法会自动调用我们项目配置好的 `EmbeddingModel`，在后台将 `Document` 的文本内容转换为向量，然后连同元数据一起存入数据库。开发者无需手动进行向量化。\n  * **`similaritySearch` 方法**: 这是数据检索的核心。它接收一个 `SearchRequest` 对象，该对象封装了所有的查询条件，包括查询文本、期望返回的数量（topK）、相似度阈值以及元数据过滤器。\n  * **`delete` 方法**: 提供了两种删除方式。按 ID 删除非常高效，适用于精确操作；按条件（Filter）删除则更为灵活，是实现数据管理、版本控制等复杂逻辑的基础。\n\n#### 8.1.2 `SearchRequest`：构建你的精确检索意图\n\n`SearchRequest` 是一个功能强大的请求构建器 (Builder)，它允许我们精细地控制搜索行为。根据最新官方 API，其 Builder 方法**没有 `with` 前缀**。\n\n| Builder 方法 | 作用 |\n| :--- | :--- |\n| `query(String query)` | **(必需)** 设置用于语义搜索的查询文本。 |\n| `topK(int topK)` | 设置返回最相似结果的最大数量。 |\n| `similarityThreshold(double threshold)`| 设置相似度得分阈值 (0.0 到 1.0)。 |\n| `filterExpression(String expression)` | **(关键)** 应用元数据过滤表达式。 |\n\n  * **`query(String)`**: 这是搜索的“灵魂”，通常是用户的原始问题或一段用于匹配的文本。\n  * **`topK(int)`**: 控制返回结果的数量。在 RAG 应用中，这个值的设定需要权衡——太小可能导致上下文不足，太大则可能引入不相关噪声并超出模型的上下文窗口限制。通常从一个较小的值 (如 3 或 5) 开始实验。\n  * **`similarityThreshold(double)`**: 这是一个过滤器，只有相似度得分高于此阈值的文档才会被返回。在对结果相关性要求极高的场景下，设置一个较高的值 (如 0.75 以上) 能有效提升结果质量。\n  * **`filterExpression(String)`**: 这是实现“混合搜索”的关键，它允许我们在进行语义搜索的同时，对文档的结构化元数据进行 SQL `WHERE` 子句式的精确过滤，极大地提升了搜索的精准度。\n\n#### 8.1.3 元数据过滤：大海捞针的利器\n\n**元数据过滤**允许我们在语义搜索的同时，对文档的结构化元数据（如类别、年份、标签等）进行精确的条件过滤。其表达式语法非常直观。\n\n| 类别 | 支持的操作符 |\n| :--- | :--- |\n| **比较操作符** | `==` (等于), `!=` (不等于), `>` (大于), `>=` (大于等于), `<` (小于), `<=` (小于等于) |\n| **集合操作符** | `IN` (包含于), `NIN` (不包含于) |\n| **逻辑操作符** | `AND` (或 `&&`), `OR` (或 `||`) |\n\n  * **场景应用：文档版本管理**\n    官方文档提供了一个极佳的用例：管理文档版本。当我们需要发布新版文档时，一个安全的操作是先删除旧版，再添加新版。我们可以通过元数据轻松实现这一点。\n\n    ```java\n    // 这是一个在 Service 层中演示版本管理的示例\n    public void updateDocument(Document newVersionDocument) {\n        // 假设新旧文档通过元数据中的 \'docId\' 关联，并通过 \'version\' 区分\n        String docId = (String) newVersionDocument.getMetadata().get("docId");\n        String oldVersion = "1.0"; \n\n        // 1. 构造一个精确的过滤器表达式，定位到旧版本的文档\n        String filterExpression = String.format("docId == \'%s\' AND version == \'%s\'", docId, oldVersion);\n        \n        // 2. 使用该表达式删除旧版本\n        vectorStore.delete(filterExpression);\n\n        // 3. 添加新版本的文档\n        vectorStore.add(List.of(newVersionDocument));\n    }\n    ```\n\n### 8.2 Redis 向量存储实战\n\nRedis，作为家喻户晓的高性能内存数据库，通过其 **RediSearch** 模块提供了强大的向量搜索能力。它非常适合需要将缓存、消息队列和向量搜索等功能整合在同一个技术栈中，并追求极致读写性能的场景。\n\n#### 8.2.1 环境准备：启动 Redis Stack\n\n要解锁 Redis 的向量搜索功能，必须使用包含 `RediSearch` 模块的 **Redis Stack** 版本，而非普通的 Redis。\n\n  * **规避端口冲突**\n    一个常见的开发陷阱是，很多开发者本地已经运行了一个标准的 Redis 服务，占用了默认的 `6379` 端口。为了避免冲突，我们将特意为我们的 AI 应用在 `6380` 端口上启动一个新的 Redis Stack 实例。\n\n  * **使用 Docker 启动 (推荐)**\n    在项目根目录下创建 `docker-compose.yml` 文件。\n\n    ```yaml\n    # docker-compose.yml\n    version: \'3.8\'\n    services:\n      redis-stack-for-ai:\n        image: redis/redis-stack:latest\n        container_name: redis-stack-for-ai\n        ports:\n          # 将容器的 6379 端口映射到主机的 6380 端口\n          - "6380:6379"\n          # RedisInsight 管理后台的端口保持不变\n          - "8001:8001" \n    ```\n\n    确保你的Docker桌面版（Windows）或Docker服务已经启动，然后在 `docker-compose.yml` 文件所在的目录运行 `docker-compose up -d` 命令即可在后台启动服务。\n\n#### 8.2.2 Spring Boot 自动配置集成\n\n  * **第一步：添加 Maven 依赖**\n    在 `pom.xml` 中加入 Spring AI 为 Redis 提供的 Starter。请确保您也已经添加了 Embedding Model 的 Starter，例如我们项目中使用的智谱 AI。\n\n    ```xml\n    <dependency>\n        <groupId>org.springframework.ai</groupId>\n        <artifactId>spring-ai-starter-vector-store-redis</artifactId>\n    </dependency>\n\n    <dependency>\n        <groupId>org.springframework.ai</groupId>\n        <artifactId>spring-ai-starter-model-zhipuai</artifactId>\n    </dependency>\n    ```\n\n  * **第二步：`application.yml` 配置**\n    在配置文件中，我们提供 Redis 的连接信息，并对 `VectorStore` 进行参数配置，**注意将端口号修改为 `6380`**。\n\n    ```yaml\n    # src/main/resources/application.yml\n\n    spring:\n      # 配置 Redis 连接信息\n      data:\n        redis:\n          host: localhost\n          # 关键：连接到我们为AI应用指定的 6380 端口\n          port: 6380\n      \n      ai:\n        # VectorStore 的专属配置区\n        vector-store:\n          redis:\n            # 指定在 Redis 中创建的索引名称，便于管理\n            index-name: ai-copilot-kb-index\n            # 关键：在开发时设为true，让Spring AI在应用启动时自动检查并创建向量索引。\n            # 在生产环境中，推荐设为 false，并手动管理索引以获得更好的控制。\n            initialize-schema: true\n    ```\n\n#### 8.2.3 升级现有服务：将向量存入Redis\n\n当您调用 `vectorStore.add()` 时，Spring AI 在其内部为您执行了以下一系列操作：\n\n1. **接收 `Document` 对象**：`add` 方法接收到您传入的 `Document` 列表。\n2. **提取文本内容**：它会遍历列表中的每一个 `Document` 对象，并取出其 `content` 属性。\n3. **调用内部 `EmbeddingModel`**：`RedisVectorStore` 在被 Spring 自动创建时，已经将您配置好的 `EmbeddingModel`（在我们的例子中是 `ZhiPuAiEmbeddingModel`）**注入**到了它自己的内部。此时，它会调用这个内部持有的 `embeddingModel` 实例，将提取出的所有文本内容进行批量向量化。\n4. **关联向量与文档**：将生成的一批向量与原始的 `Document` 对象一一对应起来。\n5. **写入 Redis**：最后，它连接到 Redis，为每一个 `Document` 创建一个 Hash，并将文档的 `content`、`metadata` 以及刚刚生成的**向量 `embedding`** 作为不同的字段，一并写入到这个 Hash 中。`\n\n现在，我们对上一章的 `DocumentEmbeddingService` 进行一次关键的升级。它的职责不再仅仅是向量化，而是**将向量化后的文档持久化存储到 Redis 中**。\n\n  * **目标**：修改 `DocumentEmbeddingService`，注入 `VectorStore`，并在处理文件后调用 `vectorStore.add()` 方法。\n\n  * **代码实现** (`service/DocumentEmbeddingService.java`):\n\n    ```java\n    // src/main/java/com/copilot/aicopilotbackend/service/DocumentEmbeddingService.java\n    package com.copilot.aicopilotbackend.service;\n    \n    import com.copilot.aicopilotbackend.validation.FileValidator;\n    import lombok.RequiredArgsConstructor;\n    import lombok.extern.slf4j.Slf4j;\n    import org.springframework.ai.document.Document;\n    import org.springframework.ai.vectorstore.VectorStore; // 1. 导入 VectorStore\n    import org.springframework.stereotype.Service;\n    import org.springframework.web.multipart.MultipartFile;\n    \n    import java.io.IOException;\n    import java.nio.charset.StandardCharsets;\n    import java.util.List;\n    import java.util.Map;\n    \n    @Slf4j\n    @Service\n    @RequiredArgsConstructor\n    public class DocumentEmbeddingService {\n    \n        // 2. 注入 VectorStore，Spring AI 会根据配置自动创建 RedisVectorStore 实例\n        private final VectorStore vectorStore;\n    \n        /**\n         * [升级版] 将上传的文件内容向量化并存入 Redis VectorStore。\n         *\n         * @param file 用户上传的文件\n         * @return 包含文档ID和成功信息的字符串\n         */\n        public String embedDocument(MultipartFile file) {\n            FileValidator.validateFile(file);\n    \n            try {\n                String content = new String(file.getBytes(), StandardCharsets.UTF_8);\n                \n                // 3. 创建一个 Document 对象，这是 VectorStore 操作的基本单位\n                // 我们可以将文件名等信息作为元数据一并存入\n                Document document = new Document(content, Map.of(\n                    "filename", file.getOriginalFilename(),\n                    "size", file.getSize()\n                ));\n    \n                log.info("准备将文档 \'{}\' 添加到 VectorStore...", file.getOriginalFilename());\n    \n                // 4. 调用 vectorStore.add() 方法，完成向量化和入库的全部操作\n                vectorStore.add(List.of(document));\n    \n                log.info("文档 \'{}\' 已成功存入 Redis，ID: {}", file.getOriginalFilename(), document.getId());\n    \n                // 5. 返回更详细的成功信息\n                return String.format("文件 \'%s\' 已成功存入知识库！文档ID为: %s", \n                                     file.getOriginalFilename(), document.getId());\n    \n            } catch (IOException e) {\n                // ... 异常处理保持不变 ...\n            }\n        }\n    }\n    ```\n\n\n#### 8.2.4 效果检验：在 Navicat 中查看数据\n\n现在，我们服务的核心能力已经升级。虽然前端界面和上一章一样，但每次上传文件后，数据都会被永久地存入 Redis。让我们来亲眼验证一下。\n\n1.  **重新上传文件**：启动应用，像上一章一样，通过前端界面或 Postman 上传一个 `.txt` 文件。\n\n2.  **连接 Navicat for Redis**：\n\n      * 打开 Navicat，点击“连接” -\\> “Redis...”。\n      * 在连接设置中，**主机**填写 `localhost`，**端口**填写我们指定的 `6380`。\n      * 点击“连接测试”，成功后保存连接。\n\n3.  **查找并检视数据**：\n\n      * 双击打开连接，Navicat 会列出 Redis 中的所有 key。\n\n      * Spring AI 默认会为存入的每个 `Document` 创建一个 Redis `Hash` 类型的 key。这个 key 的命名格式通常是 `prefix:id`。根据我们的配置 (`index-name: ai-copilot-kb-index`)，前缀通常是 `doc:ai-copilot-kb-index`。所以你应该能找到类似 `doc:ai-copilot-kb-index:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx` 的 key。\n\n      * 双击打开这个 key，你将看到一个清晰的哈希表结构：\n\n        * **`content`**: 字段存储了你上传文件的完整原始内容。\n        * **`embedding`**: 字段存储了二进制格式的向量数据（在 Navicat 中可能显示为乱码或十六进制，这是正常的）。\n        * **`metadata_filename`**: 我们自定义的元数据 `filename`。\n        * **`metadata_size`**: 我们自定义的元数据 `size`。\n\n        ```json\n        {\n            "filename": "附录.md",\n            "size": 3496,\n            "embedding": [ - 0.009663644, 0.0067672133,  - 0.026408637, 0.0010488915],\n            "content": "---\\ntitle: 附录\\nc...."\n        }\n        ```\n\n\n\n\n---'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#8-Vector-Stores%EF%BC%9A%E6%9E%84%E5%BB%BA-AI-%E7%9A%84%E9%95%BF%E6%9C%9F%E8%AE%B0%E5%BF%86-Redis-%E5%AE%9E%E6%88%98%E7%AF%87"><span class="toc-number">1.</span> <span class="toc-text">8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-%E6%A0%B8%E5%BF%83-API-%E6%B7%B1%E5%BA%A6%E8%A7%A3%E6%9E%90"><span class="toc-number">1.1.</span> <span class="toc-text">8.1 核心 API 深度解析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-1-VectorStore%EF%BC%9A%E7%BB%9F%E4%B8%80%E7%9A%84%E5%90%91%E9%87%8F%E6%93%8D%E4%BD%9C%E9%97%A8%E9%9D%A2"><span class="toc-number">1.1.1.</span> <span class="toc-text">8.1.1 VectorStore：统一的向量操作门面</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-2-SearchRequest%EF%BC%9A%E6%9E%84%E5%BB%BA%E4%BD%A0%E7%9A%84%E7%B2%BE%E7%A1%AE%E6%A3%80%E7%B4%A2%E6%84%8F%E5%9B%BE"><span class="toc-number">1.1.2.</span> <span class="toc-text">8.1.2 SearchRequest：构建你的精确检索意图</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-3-%E5%85%83%E6%95%B0%E6%8D%AE%E8%BF%87%E6%BB%A4%EF%BC%9A%E5%A4%A7%E6%B5%B7%E6%8D%9E%E9%92%88%E7%9A%84%E5%88%A9%E5%99%A8"><span class="toc-number">1.1.3.</span> <span class="toc-text">8.1.3 元数据过滤：大海捞针的利器</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-Redis-%E5%90%91%E9%87%8F%E5%AD%98%E5%82%A8%E5%AE%9E%E6%88%98"><span class="toc-number">1.2.</span> <span class="toc-text">8.2 Redis 向量存储实战</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-1-%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87%EF%BC%9A%E5%90%AF%E5%8A%A8-Redis-Stack"><span class="toc-number">1.2.1.</span> <span class="toc-text">8.2.1 环境准备：启动 Redis Stack</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-2-Spring-Boot-%E8%87%AA%E5%8A%A8%E9%85%8D%E7%BD%AE%E9%9B%86%E6%88%90"><span class="toc-number">1.2.2.</span> <span class="toc-text">8.2.2 Spring Boot 自动配置集成</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-3-%E5%8D%87%E7%BA%A7%E7%8E%B0%E6%9C%89%E6%9C%8D%E5%8A%A1%EF%BC%9A%E5%B0%86%E5%90%91%E9%87%8F%E5%AD%98%E5%85%A5Redis"><span class="toc-number">1.2.3.</span> <span class="toc-text">8.2.3 升级现有服务：将向量存入Redis</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-4-%E6%95%88%E6%9E%9C%E6%A3%80%E9%AA%8C%EF%BC%9A%E5%9C%A8-Navicat-%E4%B8%AD%E6%9F%A5%E7%9C%8B%E6%95%B0%E6%8D%AE"><span class="toc-number">1.2.4.</span> <span class="toc-text">8.2.4 效果检验：在 Navicat 中查看数据</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>