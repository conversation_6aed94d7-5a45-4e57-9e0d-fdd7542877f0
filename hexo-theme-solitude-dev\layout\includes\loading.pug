div(id="loading-box" onclick="preloader.endLoading();" style="zoom:1")
    div.loading-bg
        img.loading-img.nolazyload(src=url_for(theme.loading.favicon || theme.site.icon), alt="loading image")

script.
    const preloader = {
        isLoaded: false,
        endLoading: () => {
            if (!preloader.isLoaded) {
                document.getElementById('loading-box').classList.add('loaded');
                preloader.isLoaded = true;
                preloader.togglePaceDone();
            }
        },
        initLoading: () => {
            document.getElementById('loading-box').classList.remove('loaded');
            preloader.isLoaded = false;
            preloader.togglePaceDone();
        },
        togglePaceDone: () => {
            document.getElementById('body').className = preloader.isLoaded ? 'pace-done' : '';
        }
    }

    window.addEventListener('load', () => {
        preloader.endLoading();
    });

    window.addEventListener('pjax:send', () => {
        preloader.initLoading();
    });

    document.addEventListener('pjax:complete', () => {
        preloader.endLoading();
    });

    setTimeout(() => {
        preloader.endLoading();
    }, 5000);
