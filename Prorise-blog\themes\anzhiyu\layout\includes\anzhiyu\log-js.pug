//- 定义Pug变量，这些行通常没有缩进
- let author = config.author
- let launch_time = theme.footer.runtime.launch_time
- let since = theme.footer.owner.since
- let version = " 1.0.0"

//- script 标签，注意它后面的点 '.'
script.
  //- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
  // 消除控制台打印
  var HoldLog = console.log;
  console.log = function () {};
  let now1 = new Date();
  queueMicrotask(() => {
    const Log = function () {
      HoldLog.apply(console, arguments);
    }; //在恢复前输出日志
    const grt = new Date("#{launch_time}"); // 此处读取您配置中的建站时间
    now1.setTime(now1.getTime() + 250);
    const days = (now1 - grt) / 1000 / 60 / 60 / 24;
    const dnum = Math.floor(days);
    
    // --- 已将您的专属LOGO和个人信息集成到这里 ---
    const ascll = [
      `欢迎访问 Prorise 的数字空间!`,
      `代码构建世界，思想驱动未来`,
      "已稳定运行",
      dnum,
      "天",
      `©#{since} By #{author}#{version}`,
    ];
    
    const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

    setTimeout(
      Log.bind(
        console,
        `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
        "color:#425AEF",
        "",
        "color:#425AEF",
        "color:#425AEF"
      )
    );

    setTimeout(
      Log.bind(
        console,
        `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
        "color:white; background-color:#4fd953",
        "",
        "",
        'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
      )
    );

    setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

    setTimeout(
      console.warn.bind(
        console,
        "%c ⚡ Created by #{author} %c 你正在访问 Prorise 的博客.",
        "color:white; background-color:#f0ad4e",
        ""
      )
    );

    setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

    setTimeout(
      console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
    );
  });