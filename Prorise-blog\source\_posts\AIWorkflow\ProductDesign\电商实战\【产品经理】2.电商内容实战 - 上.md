---
title: 2️⃣ 电商实战（上）
categories:
  - 产品经理实战
tags:
  - 产品经理实战
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp'
comments: true
toc: true
ai: true
abbrlink: 30404
date: 2025-07-21 15:9:45
---

---
# 第一章：电商基础

欢迎来到我们关于电商产品管理的新篇章。电子商务，无疑是过去二十年间，最深刻地改变了我们生活方式和商业形态的领域之一。作为这个领域的从业者，我们就是现代数字商场的“建筑师”。

在第一章，我将带大家一起，为我们即将开始的“建筑”生涯，打下最坚实的地基。我们将从零售最本质的源头开始，理解我们所处的这个波澜壮阔的行业。

## 1.1 学习目标

![image-20250721152555400](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721152555400.png)

在本章中，我的核心目标是，帮助我们建立起对电子商务领域的全局性、结构化的认知。我们将一起追溯零售行业的发展脉络，掌握驱动所有商业运转的“零售三要素”，并清晰地辨别当前主流电商平台的各种运营、交易及盈利模式。

## 1.2 零售行业的发展

要理解什么是“电子商务”，我们必须先理解什么是“商务”，也就是“零售”。在我看来，电子商务并非一个全新的物种，而是传统零售在数字时代的一次**伟大进化**。

### 1.2.1 传统零售

![image-20250721152624549](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721152624549.png)

**传统零售**，指的是所有**以实体门店为主要交易场所**的商业活动。

* **它的形态**：可以是我们楼下的小`杂货店`，可以是市中心的`百货商店`，也可以是大型的`连锁超市`和`购物中心`。
* **它的本质**：是“一手交钱，一手交货”的面对面交易。
* **它的核心限制**：是它无法摆脱**时间和空间**的束缚。一家商店，必须在固定的营业时间（时间限制），在固定的地理位置（空间限制），才能服务于能够到达这里的顾客。

### 1.2.2 电子商务

![image-20250721152732714](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721152732714.png)

**电子商务（E-commerce）**，则是**以互联网为媒介进行商品交易**的全新零售形态。

* **它的形态**：是我们手机里无处不在的`淘宝`、`京东`、`拼多多`等购物App。
* **它的本质**：是通过信息流、资金流、物流的线上整合，来完成交易。
* **它的核心突破**：是它彻底**打破了传统零售在时间和空间上的限制**。我可以在任何时间（深夜或凌晨）、任何地点（家里或路上），购买到来自世界各地的商品。

正是这一根本性的突破，创造出了一个无比巨大的、全新的数字商业世界，也为我们产品经理，提供了施展才华的广阔舞台。

---
我将这两者的核心区别，总结在下面的表格里，这能帮助我们更清晰地理解这次“进化”。

| **零售形态** | **交易场所** | **核心限制** | **典型代表** |
| :--- | :--- | :--- | :--- |
| **传统零售** | 实体门店 | **受限于时间和空间** | 连锁超市、百货商场 |
| **电子商务** | 互联网平台 | **打破了时间和空间的限制** | 淘宝、京东、拼多多 |




---
## 1.3 零售三要素

![image-20250721153351348](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721153351348.png)

我们已经清楚了传统零售和电子商务的区别。

那么，一个更深层次的问题是：抛开线上、线下的外在形式，所有“**交易**”这件事，有没有一些共通的、永恒不变的内在要素？

答案是肯定的。在我看来，所有零售的底层逻辑，都可以被拆解为三个最基本的核心要素。掌握了它们，就等于掌握了理解一切商业模式的钥匙。

### 1.3.1 核心概念：人、货、场

![image-20250721153419012](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721153419012.png)

这三个永恒不变的核心要素，我称之为“**零售三要素**”：
* **人 (People)**：即我们的消费者、用户。
* **货 (Goods)**：即我们所销售的商品或服务。
* **场 (Place)**：即交易发生的场景或场所。

从传统零售到电子商务的进化，并不是发明了新的要素，而是用**信息化**的手段，对这三个要素的**形态**，进行了一次彻底的、颠覆性的重构。

下面这张表格，是我认为对这场“重构”最精辟的总结：

| **零售形态** | **人 (People)** | **货 (Goods)** | **场 (Place)** | **核心基础** |
| :--- | :--- | :--- | :--- | :--- |
| **传统零售** | 面对面的顾客 | 实体商品 | 实体门店 | **依赖实体** |
| **电子商务** | **账号** | **商品信息** | **线上平台** | **信息化** |

### 1.3.2 零售三要素在电商的体现

现在，我们来深入理解，这信息化重构后的“新人、新货、新场”，对我们产品经理来说，具体意味着什么。

#### 1. “人”在电商的体现（如账号、用户画像）

在电商的世界里，“人”不再是商店里一个个模糊的面孔，而是被数字化为了一个唯一的“**账号 (Account)**”。

这个账号，是我们识别、理解、服务用户的“数字容器”。它不仅包含了用户的`手机号`、`昵称`、`年龄`、`性别`等基础信息，更重要的是，它记录了用户在我们平台的一切**行为数据**——他浏览过什么、搜索过什么、购买过什么、收藏过什么。

我正是通过分析这些账号数据，才能为用户建立起精准的“**用户画像**”，从而为他提供个性化的商品推荐和购物体验。

#### 2. “货”在电商的体现（商品形式与来源）

在电商的世界里，“货”不再是货架上可以触摸的实体，而是被数字化为了详尽的“**商品信息 (Product Information)**”。

我作为产品经理，最重要的工作之一，就是设计好承载这些信息的“**商品详情页**”。我需要通过组合运用`文字描述`、`高清图片`、`展示视频`、`用户评价`等多种信息形态，在用户无法亲身接触到商品的情况下，最大化地还原商品的真实样貌和价值，激发用户的购买欲望。

#### 3. “场”在电商的体现（平台构建与整合）

在电商的世界里，“场”不再是具体的物理门店，而是我们所构建的“**线上平台 (Online Platform)**”本身。

从我们App的`首页`、`分类页`、`搜索结果页`，到`商品详情页`，再到`购物车`和`支付流程`，这整个用户走过的路径，共同构成了这个全新的、数字化的“商场”。

我作为产品经理，就是这个商场的“总设计师”。我的职责，是确保这个商场的“动线”是流畅的，“导购”是智能的，“收银”是便捷的，为每一个数字化的“人”，提供一次完美的、购买数字化“货”的旅程。



---
## 1.4 电商平台运营模式

![image-20250721154619607](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154619607.png)

我们已经知道了电商的“人货场”是信息化的。那么，一个电商公司具体是如何组织和管理这信息化的“人货场”的呢？这就由它的**运营模式**所决定。

在我看来，要剖析任何一个电商平台的运营模式，都离不开一个最经典、最底层的商业框架——“**进销存**”。

* **进 (Procurement)**：商品是从哪里来的？（**商品来源**）
* **销 (Sales)**：商品是在哪里卖的？（**销售渠道**）
* **存 (Inventory)**：商品由谁来仓储和配送？（**仓储配送**）

**进、销、存这三个环节，由谁来主导、由谁来负责，就共同决定了电商平台的四种主流运营模式。**

### 1.4.1 自营模式

![image-20250721154659803](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154659803.png)

**自营模式**，我把它理解为“**线上品牌连锁超市**”。平台自己既是“超市老板”，也是唯一的“售货员”。

* **进（货源）**：平台自己负责采购商品。或是通过买手团队向品牌方**自采**，或是自己贴牌**自产**。
* **销（销售）**：平台搭建自己的电商网站或App，直接面向消费者进行销售。
* **存（仓储）**：平台自己建立仓库（或租赁），自己管理库存和订单履约，自己负责（或统一外包）物流配送。
* **我的解读**：在这种模式下，平台对**商品、价格、服务、体验**有绝对的掌控权。**京东自营**就是最典型的代表。它的**优点**是能提供极致的用户体验和品质保障，从而建立起强大的品牌信任。但**缺点**也非常明显，这是一个“重资产”模式，平台需要承担巨大的采购成本和库存风险。

### 1.4.2 招商模式

![image-20250721154744575](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154744575.png)

**招商模式**，我把它理解为“**线上购物中心**”或“**集市**”。平台是“商场房东”，负责搭建和维护商场，并吸引商家进来开店。

* **进（货源）**：平台的“采购”，实际上是“**招商**”。通过签约，邀请成千上万的第三方商家入驻开店。
* **销（销售）**：所有商家共享平台这个统一的销售渠道。
* **存（仓储）**：**商家各自负责**自己的商品仓储、打包和发货。
* **我的解读**：在这种模式下，平台的核心是“搭台子”和“定规则”。**淘宝**就是最典型的代表。它的**优点**是“轻资产”运营，可以快速地、低成本地极大丰富平台上的商品数量（SKU）。但**缺点**是平台对商品质量和服务的掌控力较弱，用户体验参差不齐。

### 1.4.3 联营模式

![image-20250721154834614](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154834614.png)

**联营模式**，是介于自营和招商之间的一种合作模式，我称之为“**线上品牌专柜**”。

* **进（货源）**：与招商模式类似，平台邀请品牌方或大型供应商入驻。
* **销（销售）**：同样在统一的平台上进行销售。
* **存（仓储）**：这是最关键的区别。在联营模式下，虽然货权依然属于品牌方，但品牌方会将一部分或全部商品，**放入平台自建的仓库中，由平台来统一负责仓储和配送**。
* **我的解读**：这种模式，最典型的就是**天猫超市**或**京东上的大部分品牌旗舰店**。品牌方提供货，但用户享受到的是平台（如京东物流）的配送服务。它试图结合招商模式的“货品丰富”和自营模式的“体验保障”，是一种强强联合的模式。

### 1.4.4 混合模式

![image-20250721154910305](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721154910305.png)

**混合模式**，就是字面意思，即平台**同时运营着自营和招商/联营业务**。

* **我的解读**：如今，几乎所有的电商巨头，都是混合模式的运营者。最典型的就是**京东**和**亚马逊**。它们既有“自营”板块，来保证核心品类的体验和口碑；又有庞大的“开放平台（招商）”板块，来丰富商品的多样性。这能让它们最大化地满足不同用户的需求，实现优势互补。

---
我将这四种模式的核心区别，总结在下面的表格里：

| **运营模式** | **平台角色** | **核心优势** | **核心劣势** | **典型代表** |
| :--- | :--- | :--- | :--- | :--- |
| **自营模式** | **零售商** | 体验、品控、物流极佳 | 模式重、成本高、SKU有限 | 京东自营 |
| **招商模式** | **商场房东** | 模式轻、SKU极其丰富 | 体验、品控难统一 | 淘宝C店 |
| **联营模式** | **代销商** | 品控+物流体验好，SKU较丰富 | 运营模式复杂 | 天猫、京东旗舰店 |
| **混合模式** | **零售商 + 房东** | 优势互补、覆盖面广 | 内部资源协调复杂 | 亚马逊、京东 |




---
## 1.5 电商平台交易模式

![image-20250721160205118](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160205118.png)

在我们上一节学习的“运营模式”中，我们关心的是平台“**如何组织货源和履约**”。而在“**交易模式**”中，我关心的是“**谁在和谁做生意**”。

一个电商平台的交易模式，本质上是由**商品卖家**和**商品买家**这两方的**角色属性**来决定的。我们常说的B2B、B2C、C2C，其实就是一种简写，它的命名规则是：
**卖方角色英文首字母 + 2(to) + 买方角色英文首字母**

我们来详细拆解几种最主流的交易模式。

### 1.5.1 B2B（企业对企业）

![image-20250721160318526](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160318526.png)

**B2B (Business to Business)**，即**企业与企业之间**的电子商务。我把它理解为，是将传统的企业间贸易、批发、采购等行为，搬到了线上。

* **核心特征**：
    * **大批量、低频次**：B2B交易的订单金额通常很大，但交易频次相对较低。
    * **决策理性且复杂**：采购方通常需要经过内部的多部门（如采购、技术、财务）审批，决策周期长，非常理性。
    * **价格非标、流程复杂**：价格通常不是固定的，需要经过`报价`、`询价`、`订购`等多个环节，并涉及到复杂的合同、发票和物流流程。
* **典型代表**：**阿里巴巴(1688.com)**，它就是连接上游工厂/供应商，和下游零售商家的典型B2B平台。

### 1.5.2 B2C（企业对个人）

![image-20250721160339627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160339627.png)

**B2C (Business to Customer)**，即**企业与个人消费者之间**的电子商务。这是我们日常生活中，接触最多、也最熟悉的一种模式。

* **核心特征**：
    * **小批量、高频次**：用户通常是购买单件或少量商品，但购买行为可能会非常频繁。
    * **决策感性且迅速**：购买决策通常由个人做出，很容易受到品牌、营销、促销、评价等因素影响，决策路径短。
    * **价格标准、体验为王**：商品价格是标准化的，所有消费者看到的都一样。平台竞争的核心，在于品牌、营销、用户体验和客户服务。
* **典型代表**：**天猫、京东、唯品会、拼多多**等，都是典型的B2C平台。

### 1.5.3 C2C（个人对个人）

![image-20250721160404064](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160404064.png)

**C2C (Customer to Customer)**，即**个人与个人之间**的电子商务。我把它理解为一个“**线上跳蚤市场**”或“**个人闲置物品交易集市**”。

* **核心特征**：
    * **平台是中介**：平台本身不卖货，而是作为第三方，为买卖双方提供一个信息发布、在线沟通、交易担保的场所。
    * **依赖生态服务**：C2C模式的成立，强依赖于成熟的**第三方支付工具**（如支付宝，解决信任问题）和**物流公司**（解决物品交付问题）。
    * **非标品为主**：交易的商品大多是二手的、非标准化的。
* **典型代表**：目前国内最成功的C2C平台，就是阿里的**闲鱼**。

### 1.5.4 F2C（工厂对个人）等

**F2C (Factory to Customer)**，即**工厂与个人消费者之间**的电子商务。这是一种旨在“**去中间化**”的模式。

* **核心特征**：
    * **短路经济**：它砍掉了传统零售中的经销商、代理商等所有中间环节，让消费者可以直接从工厂下单购买。
    * **高性价比**：因为没有了中间商赚差价，理论上能为消费者提供更低的价格。
    * **C2M模式**：它常常与C2M（Customer-to-Manufacturer，用户直连制造）模式结合，即工厂根据用户的订单和需求来进行生产，实现零库存。
* **典型代表**：**必要App**就是F2C/C2M模式的知名探索者。

---
我将这几种主流的交易模式，总结在下面的表格中，方便我们对比：

| **交易模式** | **卖方 (Seller)** | **买方 (Buyer)** | **核心特征** | **典型代表** |
| :--- | :--- | :--- | :--- | :--- |
| **B2B** | 企业 (Business) | 企业 (Business) | 订单量大、决策复杂、价格多为协商 | 1688.com |
| **B2C** | 企业 (Business) | 个人 (Consumer) | 品牌化、体验为王、价格标准化 | 天猫、京东 |
| **C2C** | 个人 (Consumer) | 个人 (Consumer) | 平台撮合、依赖第三方支付与物流 | 闲鱼 |
| **F2C** | 工厂 (Factory) | 个人 (Consumer) | 去中间化、高性价比、按需生产 | 必要 |




---

## 1.6 电商平台盈利模式

![image-20250721160704848](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160704848.png)

我们前面讨论了电商的运营模式、交易模式，这些模式虽然千差万别，但它们都服务于一个最终的、共同的目的——**盈利**。

作为产品经理，我必须深刻地理解我所在平台的盈利模式，因为这直接决定了我的**工作重心**和**产品设计的方向**。我的每一个功能设计，最终都应该直接或间接地，为平台的盈利目标服务。

![image-20250721160725452](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721160725452.png)

我们来看一下，像天猫、京东这些巨头，它们是如何通过多种方式，来构建自己强大的盈利能力的。我将这些复杂的方式，归纳为三大主流的盈利模式。

### 1.6.1 销售收入

这是最直接，也是最古老的盈利模式。我把它简单地理解为“**赚差价**”。

* **核心逻辑**：这种模式主要应用于**自营型**电商。平台首先以一个“采购价”从供应商那里把商品买断，然后在自己的平台上以一个“零售价”卖给消费者。这两者之间的差价，扣除仓储、物流、人力等成本后，就是平台的利润。
* **我的设计思考**：如果我所在平台的核心收入是销售收入，那么我的产品设计，就会**高度聚焦于提升交易转化率和销售额（GMV）**。比如，我会重点优化商品推荐算法、简化购物车到支付的流程、设计各种优惠券和促销活动功能等，一切为了让用户“买得更多，买得更快”。

### 1.6.2 广告收入

当一个平台拥有了巨大的用户流量，那么它的流量本身，就成了可以售卖的商品。我把这种模式，理解为“**卖流量**”或“**卖广告位**”。

* **核心逻辑**：这种模式是**招商平台**最核心的收入来源。平台本身不靠卖货赚钱，而是通过向希望在平台上获得曝光的商家，出售广告位来盈利。
* **我的拓展设计**：广告产品的设计，本身就是一个复杂的产品领域。常见的广告形式包括：
    * **关键词竞价**：商家对某个搜索关键词（如“运动鞋”）进行出价，出价高者，其商品就会排在搜索结果的前列。这是淘宝/天猫最核心的收入来源。
    * **展示广告**：平台将首页的Banner、分类页的固定位置等，作为“广告位”，按时间或曝光量，售卖给品牌商家。
    * **信息流广告**：在“猜你喜欢”等个性化推荐信息流中，穿插一些看起来像普通内容的广告商品。

### 1.6.3 平台服务费

这也是**招商平台（平台模式）**的另一种重要收入，我把它理解为“**收租金和佣金**”。就像一个大型购物中心，向入驻的品牌专柜收取各种费用一样。

我把它细分为以下几种：

#### 1. 店铺租金
也叫“**平台年费**”。商家需要每年向平台缴纳一笔固定的费用，才能获得在平台上开设店铺的资格。这就像是实体店的“年租金”。

#### 2. 技术服务费
平台为商家提供了一整套复杂的开店、交易、管理、数据分析的系统，为此，平台会收取一定的“**技术服务费**”，作为软件系统和数据服务的费用。

#### 3. 交易提成
这是最常见的一种方式。平台会从商家的每一笔成功交易的流水中，抽取一个固定比例的佣金，通常在1%-5%不等。**商家卖得越多，平台赚得越多**，这种模式将平台和商家的利益进行了深度绑定。

---

## 1.7 本章总结

到这里，我们关于“电商基础”的学习就告一段落了。我们来快速回顾一下本章的核心知识地图：

| **知识模块** | **核心内容** |
| :--- | :--- |
| **零售行业的发展** | 我们理解了从**传统零售**到**电子商务**的进化，是商业在**时间和空间**上的伟大突破。 |
| **零售三要素** | 我们掌握了所有商业的底层逻辑——**人、货、场**，以及它们在电商时代，如何被**信息化**为**账号、商品信息、线上平台**。 |
| **电商平台运营模式** | 我们学习了通过“**进销存**”框架，来区分**自营、招商、联营、混合**这四种主流的运营模式。 |
| **电商平台交易模式** | 我们学习了通过“**买卖双方的角色**”，来区分**B2B, B2C, C2C, F2C**等不同的交易模式。 |
| **电商平台盈利模式** | 我们学习了电商最核心的三大盈利来源：**赚差价（销售收入）、卖流量（广告收入）、收佣金（平台服务费）**。 |

掌握了这些最底层的概念和框架，我们就拥有了一双能看透所有复杂电商产品的“慧眼”。在下一章，我们将开始进入更具体的实战设计中。



---



# 第二章：电商项目立项

在这一章，我们将正式代入一个实战角色：我们是 **“大P超级电商有限公司”** 的一名产品经理。

**我们的项目背景是：**
> 公司作为集团的子公司，手上有两大王牌资源：一是集团积累的**丰富B端商家资源**；
>
> 二是我们之前搭建的内容资讯类项目，已经吸引了**上千万C端用户**，其中80%是消费能力和意愿都很强的“90后”。
>
> 现在，公司高层已经拍板，决定正式进军电商领域，搭建一个全新的电商平台。并且，基于我们“手有商家、心中有用户”的现状，初步确定V1.0版本将采用**招商模式**，主营数码、服装、家电、快消品等类目。

作为这个项目的核心产品经理，我的第一项任务，就是要**正式地把这个项目“立”起来**。

## 2.1 项目立项流程概述

![image-20250721193939182](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721193939182.png)

一个产品的诞生，不是一蹴而就的，它遵循着一个清晰的生命周期。上图就展示了一个经典的产品流程，它包含**启动、规划、执行、跟进、上线**这五个核心阶段。

我们这一章要聚焦的“项目立项”，就是这个流程的第一步，也是最关键的“**启动**”阶段。

### 2.1.1 学习目标

在本节中，我的目标是带大家清晰地理解项目“启动”阶段的核心工作。

我们将学习一个标准的项目流程是怎样的，并重点理解“**立项评审会**”在整个流程中的关键节点作用。

### 2.1.2 项目流程与启动节点

在我看来，图中的“**启动**”阶段，包含了`行业调研`、`市场调研`等一系列前期研究工作。而这个阶段的终点，和下一“**规划**”阶段的起点，就是由一个标志性的事件来连接的，这个事件就是“**立项评审会议**”。

**立项评审会**，就是整个项目能否正式启动的“**发令枪**”。只有在这场会议上，我的立项方案得到了公司决策层（老板、各部门负责人）的认可和批准，这个项目才算真正“活了过来”，可以正式地进入后续的规划和执行阶段，获得公司资源的支持。

### 2.1.3 立项评审会议的作用

![image-20250721194117623](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194117623.png)

为什么这场会议如此重要？因为我作为产品经理，必须在这场会议上，像一名“创业者”面对投资人一样，清晰、有力地回答一系列关于这个项目的灵魂拷问。

我必须为我们的“**大P超级电商**”项目，准备好以下问题的答案：
| **决策层最关心的问题** | **我（作为PM）需要给出的回答方向** |
| :--- | :--- |
| **1. 这个产品做出来给谁用？能帮他们什么？** | **（WHO & WHY）** 给我们现有的千万级“90后”用户，帮他们在一个信得过的平台方便地购物；给我们已有的B端商家，帮他们找到新的、精准的销售渠道。 |
| **2. 这个产品预计能带来多大的营收？** | **（HOW MUCH）** 初期采用招商模式，我们的盈利将主要来自商家的**交易提成**和**平台服务费**。基于现有用户规模，我们预计第一年能实现几亿的GMV（商品交易总额），带来XX万的平台收入。 |
| **3. 现在市面上竞争对手有哪些？我们有什么竞争力？** | **（COMPETITION）** 我们的对手是淘宝、京东等巨头。但我们的核心竞争力在于，我们**已经拥有了一个庞大的、画像清晰的年轻用户群体**，这能为我们的入驻商家，提供更精准的“人货匹配”，降低他们的获客成本。 |
| **4. 这个产品怎么做？核心功能是什么？** | **（WHAT）** V1.0的核心功能，将围绕招商模式展开，包括：商家入驻与店铺管理系统、商品发布与管理系统、用户端交易流程（浏览-下单-支付）、平台运营后台。 |
| **5. 这个产品大概要多久做出来？节奏计划是怎样的？**| **（WHEN）** 我们计划用6个月的时间，分三个大的里程碑，完成V1.0的上线。第一个里程碑的目标，是在2个月内，完成商家后台的核心功能，让第一批种子商家成功入驻。 |

而我用来承载以上所有问题答案的、我在会前精心准备的“**关键性文件**”，就是我们下一节要学习的“**立项说明书**”。




---
## 2.2 立项说明书的核心构成

![image-20250721194740655](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194740655.png)

在我开始撰写立项说明书时，我不会长篇大论。一份好的立项说明书，应该是**简洁、有力、逻辑清晰**的。它的核心目的，是在最短的时间内，让决策者理解并认同我的项目价值。

因此，我通常会将整个文档，划分为三大核心模块：**产品概述、市场分析、产品规划**。这三个模块，层层递进，分别回答了

“**我们要做什么？**”、“**我们为什么能做？**”和“**我们准备怎么做？**”这三个终极问题。

现在，我们先来完成第一个，也是最重要的模块。

### 2.2.1 产品概述

这一部分，是整个立项说明书的“门面”，是我向决策者展示项目核心价值的“电梯演讲”。我必须用最精炼的语言，把产品的定位、目标用户和主要功能说清楚。

#### 1. 产品定位

![image-20250721194856262](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194856262.png)

我做的第一件事，就是用一句话，给我们的产品下一个清晰的**定义**。我习惯使用下面这个公式：
**为【目标用户】，搭建一个【什么样的平台】，提供【哪些核心功能】，来满足他们的【什么核心需求】。**

现在，我们把这个公式，应用到我们“**大P超级电商**”的项目中：

> **我们的产品定位是：**
> **为**我们平台已有的千万级“90后”年轻用户，**搭建**一个内容与消费深度融合的**招商模式电商平台**，**提供**商品搜索、智能推荐、达人直播、担保交易**等功能**，来**满足**他们追求品质、潮流与个性化购物体验的**核心需求**。

#### 2. 目标用户

![image-20250721194947268](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194947268.png)

在明确了产品定位后，我需要对我们的“**目标用户**”和“**目标市场**”，进行更具体的描述。

* **目标人群**：
    * **核心人群**：我们内容资讯平台已有的**上千万“90后”用户**。
    * **人群特征**：互联网原住民，消费意愿强，是潮流和个性的追随者；信任KOL和社区的推荐，习惯于在娱乐和内容消费中，完成“种草”和“拔草”。

* **目标市场**：
    * 我们将切入主流的、面向年轻消费者的**B2C综合电商市场**，V1.0版本主营类目将覆盖**数码、服装、家电、快消品**等。

#### 3. 主要功能

![image-20250721195036522](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195036522.png)

最后，我需要罗列出，为了实现我们的产品定位，在V1.0版本中，我们计划提供的**主要功能模块**。这并不是一份详尽的功能清单，而是一个高层级的“功能蓝图”。

* **1. 商品导购功能**：包括商品搜索、多级分类、品牌馆等，帮助用户高效发现商品。
* **2. 核心交易功能**：包括购物车、下单、集成第三方支付（微信/支付宝）、订单管理等，构成完整的交易闭环。
* **3. 商家店铺功能**：为我们的B端商家提供店铺装修、商品上下架、订单管理、营销工具等后台能力。
* **4. 内容与社交功能**：这是我们的差异化优势。包括引入达人直播、好物推荐、用户评价社区等，将内容与电商深度结合。
* **5. 基础会员功能**：包括用户注册登录、个人中心、地址管理、售后服务等。



---
### 2.2.2 市场分析

![image-20250721195532876](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195532876.png)

在我完成了对产品的初步构想（产品概述）之后，我必须用**冷静、客观的数据和分析**，来向决策者证明：我们这个构想，不是空中楼阁，而是建立在坚实的市场机会之上的。

**市场分析**，就是我用来提供这份“证据”的核心模块。我通常会从三个维度，层层递进地展开我的论证：**目标市场有多大？目标用户是谁？主要对手是谁？**

#### 1. 目标市场现状（规模、趋势、结论）







首先，我会从宏观视角，来描绘我们即将进入的“战场”的全貌。

![image-20250721195631374](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195631374.png)

* **市场规模 (Market Scale)**：这个市场的“盘子”有多大？我会引用权威的行业报告数据（如艾瑞、易观、国家统计局），来展示中国网络零售市场的总交易额（GMV）、总用户数等，证明这是一个万亿级的、足够大的市场。

![image-20250721195653877](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195653877.png)

* **市场趋势 (Market Trends)**：这个市场是在增长还是萎缩？未来的风口在哪里？我会分析近几年的数据，指出移动电商用户规模增速虽然放缓，但存量巨大。同时，我也会特别指出，“90后”乃至“00后”已经成为线上消费的主力军，他们的消费习惯（如兴趣驱动、信任KOL）是市场最大的新趋势。

![image-20250721195704847](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195704847.png)

* **分析结论 (Analysis Conclusion)**：
  
    > **我的结论是**：中国电商市场已从增量竞争，进入存量竞争时代。未来的机会，在于**对特定人群的深度运营**。我们“大P超级电商”项目，所拥有的“千万级90后用户”，恰好是这个时代最具价值的核心消费人群。因此，我们进入这个市场，具备天然的、精准的用户基础，**市场时机完全吻合**。

#### 2. 目标用户分析（分类、特征、需求）

![image-20250721202152110](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202152110.png)

在描绘了宏观市场之后，我需要将镜头拉近，聚焦到我们“**具体要服务的人**”身上。

* **用户分类 (User Classification)**：我会基于我们现有的用户数据，将“90后”这个庞大的群体，进一步细分为几个典型的用户画像（Persona）。
* **用户特征 (User Characteristics)**：我会描述每个分类用户的关键特征。
* **用户需求 (User Needs)**：我会提炼出每个分类用户，在“电商购物”这个场景下的核心需求。

| **用户分类** | **用户特征** | **核心电商需求** |
| :--- | :--- | :--- |
| **潮流大学生** | 无固定收入，追求性价比和潮流新品，极易受KOL和社区内容“种草”影响。 | 寻找高性价比的潮流服饰、数码产品；需要分期付款等金融工具；渴望通过商品彰显个性。 |
| **职场新人** | 有一定的可支配收入，工作繁忙，注重效率和生活品质，愿意为兴趣和“悦己”买单。 | 需要一站式购齐生活快消品；追求品牌和品质；愿意为提升效率和体验的服务付费；购物决策受内容推荐影响大。 |
| **内容创作者/KOL** | 我们平台上的意见领袖，拥有自己的粉丝群体，有将自身影响力变现的强烈需求。 | 需要一个便捷的、与内容深度结合的“带货”渠道，将自己推荐的商品，高效地销售给粉丝。 |

#### 3. 主要竞争对手（竞品分析、总结）

![image-20250721202259417](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202259417.png)

最后，我需要理性地分析“战场”上已经存在的“强大敌人”。

![image-20250721202310828](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202310828.png)

* **选择竞品**：我会选择市场上最主流的、与我们目标用户重合度最高的平台作为我们的主要竞争对手，即**淘宝**和**京东**。

![image-20250721202400825](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202400825.png)

* **分析内容**：我会从“零售三要素”等维度，对竞品进行拆解，并与我们自身进行对比。

* **分析总结**：得出我们与竞品相比的优势、劣势，并最终找到我们的“**差异化突破口**”。

| **对比维度** | **淘宝 (Taobao)** | **京东 (JD.com)** | **我们 (大P超级电商) 的机会** |
| :--- | :--- | :--- | :--- |
| **产品定位** | 万能的商品市场 | 品质家电数码、高效物流 | **内容驱动的潮流社区电商** |
| **核心优势** | SKU极其丰富、生态成熟 | 自营品控、物流体验无与伦比 | **已拥有千万级精准的年轻用户流量，获客成本低** |
| **核心劣势**| C2C模式品控难，用户决策成本高 | 平台模式的商品丰富度不足，用户群体偏成熟 | 商业和物流体系需要从0到1搭建，品牌心智未建立 |

**我的分析总结是**：我们无法在“多”上胜过淘宝，也无法在“快”上胜过京东。

但我们可以在“**精**”和“**准**”上建立优势。我们的突破口，就是**深度服务好我们已有的这群年轻用户**，通过将我们擅长的**内容生态**与**电商交易**进行无缝融合，打造一个“**最懂年轻人的内容电商社区**”，以此来建立我们独特的竞争壁垒。




---
### 2.2.3 产品规划与架构

在我完成了产品概述和市场分析，向决策者们清晰地阐述了“**我们要做什么**”和“**为什么我们能做**”之后，就必须回答最后一个，也是最关键的问题：“**我们具体准备怎么做？**”

**产品规划与架构**这一部分，就是我用来回答这个问题的“施工蓝图”。正如思考题所提示的，在立项会议上，一份长长的功能清单，往往会让领导感到乏味和困惑。我需要用更直观、更结构化的方式，来呈现我的产品规划。

#### 1. 认识产品架构

![image-20250721203548034](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721203548034.png)

在我开始画图之前，我必须先澄清两个非常重要、但极易混淆的概念：**产品架构图**和**产品结构图**。

![image-20250721203749198](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721203749198.png)

* **产品架构图**
    我把它定义为，我们产品的“**城市规划总览图**”。
    * **它的核心**：是表达整个产品业务的**宏观框架**。它通过层级划分和模块组合，来呈现产品包含哪些大的业务板块和核心系统。
    * **它的特点**：**高度抽象，重在框架，忽略细节**。它的读者主要是老板、业务负责人等决策层，目的是让他们在30秒内，就能看懂我们整个产品的版图。


* **产品结构图 (Product Structure Diagram)**
    我把它定义为，我们产品的“**单栋建筑施工图**”。
    * **它的核心**：是将某一个具体的产品模块（比如用户端App），所包含的**所有功能和信息**，进行一次彻底、详细的拆解。
    * **它的特点**：**非常具体，巨细靡遗**。它就像是原型的一种“简化表现方式”，是给我们的项目团队（设计师、开发、测试）看的，确保大家对某个模块的功能范围，有全面、统一的认知。

在“**立项说明书**”这个阶段，我主要使用的是“**产品架构图**”，因为它更能服务于我向决策层汇报的宏观视角。


---

#### 2. 构建方法与流程：划分角色、需求推导、功能归类

那么，我是如何为我们的“大P超级电商”项目，从零开始，一步步地构建出它的产品架构图的呢？我遵循一个非常严谨的、自顶向下的三步推导思路。

![image-20250721210258871](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210258871.png)

**第一步：划分角色，确定“端”**

在开始任何功能规划之前，我首先要识别出我们这个电商生态系统中的核心“玩家”是谁。根据我们“招商模式”的业务定位，我将所有参与者，明确地划分为三大角色，而这三大角色，也直接对应了我们需要建设的三个产品“端”：

* **用户 (User)**：对应我们需要开发的 **用户端** App，服务于我们千万级的C端消费者。
* **商家 (Merchant)**：对应我们需要开发的 **商家端** 后台，服务于入驻我们平台的B端商家。
* **平台 (Platform)**：对应我们需要开发的 **平台端** 后台，服务于我们自己公司的运营和管理人员。

**第二步：分析需求，推导功能**

确定了“端”之后，我就需要深入到每一个“端”的内部，站在对应角色的视角，去分析他们在具体场景下的核心需求，并从中推导出我们必须为他们提供的功能。

![image-20250721210755456](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210755456.png)

* **对于用户端**：我需要思考，一个普通消费者在使用我们App时，他的核心诉求是什么？
    * 他“想买个手机，要找一个颜值高、性能比较高的”，这个需求就推导出我们需要提供强大的 **商品搜索** 与 **商品筛选** 功能。
    * 他“看到一个东西好像不错，想看看买过的人有没有说过这个好用”，这个需求就推导出我们需要建立 **种草** 社区或完善的 **用户评价** 体系。
    * 他“付钱时不想用支付宝和微信支付，想用银行信用卡”，这个需求就推导出我们的 **支付** 模块，需要支持多种主流的支付方式。


![image-20250721210806766](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210806766.png)

* **对于商家端**：我需要思考，一个入驻商家，他的核心诉求是什么？
    * 他发现“这个商品不卖了，不要再让用户下单了”，这个需求就推导出我们需要提供便捷的 **商品管理** 功能（如：商品上下架）。
    * 他想知道“这段时间生意不错，看看最近卖了多少单？”，这个需求就推导出我们需要提供清晰的 **订单统计** 功能。
    * 他需要“看看今天下单的有没有没发货，要不要取消订单”，这个需求就推导出我们需要提供高效的 **订单管理** 功能（如：发货管理）。

![image-20250721210819557](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210819557.png)

* **对于平台端**：我需要思考，我们作为平台的管理者，核心诉令是什么？
    * “有些店铺入驻后违规操作，需要处理下”，这个诉求就推导出我们需要 **店铺管理** 功能（如：封禁店铺）。
    * “有些用户价格敏感，得做一些促销活动”，这个诉求就推导出我们需要 **营销管理** 功能（如：优惠券配置）。
    * “公司员工有人负责审核店铺，有人负责审核商品，系统功能不能乱”，这个诉求就推导出我们需要严谨的 **权限管理** 功能。

**第三步：功能归类，绘制架构**

![image-20250721210949605](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210949605.png)

最后一步，就是将我们在第二步中，为各个“端”推导出的所有功能，进行系统性的梳理和归类，最终汇总成一张清晰的、宏观的“**产品架构图**”。

这张图，就是我们整个电商平台V1.0版本的“总设计蓝图”。它直观地展示了用户端、商家端、平台端这三大系统，各自包含了哪些核心的功能模块，明确了我们本次立项需要投入资源进行建设的全部范围。

#### 3. 核心流程图

除了用“架构图”来展示静态的“**有什么**”，我还会附上一到两张最核心的“**业务流程图**”，来展示动态的“**怎么用**”。

对于我们的电商项目，我至少会提供：

![业务流程图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B%E5%9B%BE.png)

* **用户核心交易流程图**：从用户浏览商品，到加入购物车，再到下单、支付的完整流程。

* **商家核心操作流程图**：从商家入驻，到发布商品，再到处理订单的完整流程。

#### 4. 初始功能清单

最后，作为架构图和流程图的补充，我会提供一份相对详细的“**初始功能清单（Function List）**”。

它通常是一份Excel表格，会比架构图更具体一些，将大的功能模块，初步拆解到二级或三级功能点。这份清单，是对我`2.2.1`节中“主要功能”的进一步细化，也是我们下一节“制定迭代计划”的基础。

| 一级模块   | 二级模块 | 三级功能点 | 功能描述                 | 优先级 | 备注                               |
| :--------- | :------- | :--------- | :----------------------- | :----- | :--------------------------------- |
| **用户端** | 商品导购 | 商品搜索   | 用户可通过关键词搜索商品 | P0     | 需支持模糊搜索                     |
| **商家端** | 商品管理 | 商品列表   | 商品上下架               | P0     | 商家可控制商品的在售状态           |
| **平台端** | 商家管理 | 商家审核   | 查看审核列表             | P0     | 运营可查看所有待审核的商家入驻申请 |

---

## 2.3 `产品设计思路[核心]`

![image-20250721212555967](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721212555967.png)

在我看来，一个专业的产品设计，绝不是天马行空的艺术创作，而是一个**严谨的、结构化的逻辑推演过程**。为了确保我的设计不偏离用户价值和商业目标，我始终围绕着`“产品设计四要素”`来进行思考：**角色、流程、功能、信息**。

这个框架，能帮助我把一个模糊的需求，层层剖析，最终转化为一个清晰、完整、可执行的产品方案。

### 1. 角色 (Role) - 我们在为谁而设计？

这是我所有思考的**绝对起点**。
* **核心问题**：“**这个设计是给谁用的？**”
* **我的实践**：我从不为一个抽象的、模糊的“用户”做设计。我必须清晰地定义出**具体的操作角色及其职责**。例如，在我们“大P超级电商”平台中，`消费者`、`商家`和`平台运营`就是三个完全不同的角色。他们的目标、诉求、使用场景、甚至专业能力都截然不同。一个为`商家`设计的、追求效率和数据丰富的“订单管理”后台，和一个为`消费者`设计的、追求简洁和美观的“我的订单”页面，其设计思路必然是天壤之别。**深刻地理解“角色”，是做好用户中心设计的第一步。**

### 2. 流程 (Process) - 他们如何完成任务？

明确了“为谁设计”，下一步就是思考“**他要如何完成他的任务**”。
* **核心问题**：“**各个角色的操作顺序和规则是怎样的？**”
* **我的实践**：我通常会从两个角度来梳理流程：
    * **从目标出发**：思考角色要达成的最终目标是什么。比如，`消费者`的目标是“成功购买到心仪的商品”。我就会围绕这个目标，去绘制他从“浏览商品”到“下单支付”的完整流程图。
    * **从角色的分工出发**：当一个业务流程涉及到多个角色时（比如一笔完整的交易），我必须梳理清楚他们之间的**协作关系和任务交接**。比如，`消费者`“下单付款”后，流程就交接给了`商家`去“审核订单并发货”，这就需要我绘制“泳道图”来清晰地表达。

### 3. 功能 (Function) - 我们需要提供什么界面？

当角色的流程被梳理清楚后，“功能”的设计就成了水到渠成的事情。
* **核心问题**：“**为了支撑上述流程的每一步，我们需要提供什么样的功能界面？**”
* **我的实践**：“功能”是我们为用户搭建的“桥梁”，用来帮助他们走通“流程”。流程中的每一个“动作节点”，都对应着一个或多个“功能”。比如，为了支撑`商家`“审核订单并发货”这个流程节点，我就需要为他设计一个“**订单管理**”的功能界面。
* **我的拓展思考**：在设计功能时，我必须时刻带着“角色”的视角。**功能是为角色服务的**，因此，我需要为不同角色，合理地规划他们可见的功能路径及操作**权限**。比如，一个`商家`的“主账号”可以看到“财务报表”功能，而“客服子账号”则无权查看。

### 4. 信息 (Information) - 需要管理哪些数据？

这是四个要素中，最偏向于技术实现，也最容易被产品经理忽略，但却至关重要的一环。
* **核心问题**：“**为了让功能运转起来，我们需要管理什么数据？**”
* **我的实践**：任何一个功能界面，其本质都是在对后台的“**数据**”进行增、删、改、查。在设计功能时，我必须同步思考其背后的“信息”结构。
    * **有哪些字段**：这个功能需要展示和编辑哪些数据字段？比如，“订单管理”功能，就需要处理`订单ID`、`商品名称`、`价格`、`收货人地址`等字段。
    * **有哪些状态**：这些数据有哪些不同的状态？比如，一个`订单`，它会有`待付款`、`待发货`、`已发货`、`已完成`、`已取消`等多种状态。我必须定义清楚所有状态，以及它们之间流转的规则。
    * **关联的其他数据**：这些数据还和哪些其他数据有关联？比如，一个`订单`，它必然关联着一个`用户`数据和一个或多个`商品`数据。

我始终将“**角色 → 流程 → 功能 → 信息**”这四要素，作为一个密不可分的整体来进行思考。它能保证我的产品设计，既有血肉（服务于真实的角色和流程），又有骨架（由清晰的功能和信息构成）。

---
## 2.4 制定迭代计划

在我们完成了立项说明书的撰写，并获得了决策层的认可之后，我们就拥有了一份包含了海量功能点的“**总功能清单**”。

但是，我们不可能、也绝不应该，在第一个版本里，就把所有功能都做完。这样做周期太长、风险太高。因此，我必须将这个庞大的清单，进行**拆解、排序、分批**，最终制定出一份清晰、合理、分阶段的**迭代计划（Roadmap）**。

### 2.4.1 学习目标

在本节中，我的目标是带大家掌握一套从“总功能清单”到“V1.0版本计划”的完整编制流程。我们将学习如何拆解需求、设定优先级，并最终确立清晰的版本目标和版本功能。

### 2.4.2 迭代计划编制流程

我编制一份迭代计划，通常会遵循一个严谨的、四步走的流程。

**第一步：明确总体目标**
![image-20250721214148288](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214148288.png)
首先，我会为整个项目，设定一个最高层级的、带有时间限制的目标。对于我们的“大P超级电商”项目，这个目标就是：

> **在6个月内，搭建并上线一个能让用户顺利购物、商家可以正常经营的招商模式电商平台V1.0。**

**第二步：基于目标拆解需求**
![image-20250721214213738](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214213738.png)
然后，我会围绕这个总体目标，将我们在`2.2.3`节中制定的“初始功能清单”，进行进一步的细化和补充，确保它包含了支撑用户和商家“顺利购物”、“正常经营”所需要的全部功能点。

**第三步：功能拆解与优先级设定**
![image-20250721214248935](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214248935.png)
这是整个流程中最考验产品经理功力的一步。面对长长的功能清单，我需要对它们进行“**分类**”和“**排序**”。

我通常会将所有功能，分为两大类：
* **基础共性需求（桌子腿）**：这些是整个行业的“标配”功能，用户已经习以为常，我们“**不能没有**”。比如，对于电商平台，`购物车`、`在线支付`、`商品搜索`、`用户评价`，就属于这类需求。没有它们，平台的核心流程都跑不通。
* **差异化需求（亮点）**：这些是体现我们产品特色、构建我们核心竞争力的功能，是我们“**有了会更好**”的部分。比如，在我们项目中，`达人直播`、`内容种草社区`、`VR购物功能`（如示例中提到的），就属于这类需求。

我的MVP（最小可行产品）优先级排序原则是：**优先做完所有“桌子腿”，先让桌子能稳稳地站起来（即，核心流程能跑通）。**

**第四步：制定迭代计划**
![image-20250721214346994](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214346994.png)
最后，我会将排序后的功能，分批地“装入”到不同的版本（Version）中去。

* **第一阶段/第一版本（MVP）**：我会把所有优先级最高的“基础共性需求”打包进来，**形成一个能满足最核心业务流程的最小闭环**。
    * 正如示例中所示，“满足基本购物业务流程”需要一大堆功能，但“第一版本”可能只实现了其中最核心的`商品列表`、`立即购买`、`支付`、`查看物流`、`退货`。
* **后续版本（V1.1, V2.0...）**：在MVP的基础上，我再逐步地、有节奏地，去增加那些能体现我们特色的“差异化需求”，以及一些次要的“基础需求”。

### 2.4.3 确立版本目标与功能

![image-20250721214520120](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214520120.png)

迭代计划中的每一个“版本”，我都会用三个要素，来对它进行清晰的定义。

**1. 版本号 (Version Number)**
我采用行业标准的“**语义化版本**”命名规范：`大版本号.小版本号.临时版本号` (如: 1.2.1)
* **大版本号**：当我上线了重量级的新模块（比如，我们未来上线了“直播”功能），我就会提升大版本号（如：从1.x 升级到 2.0.0）。
* **小版本号**：当我只是增加了一些新功能或对现有模块进行优化时，我就会提升小版本号（如：从1.0.0 升级到 1.1.0）。
* **临时版本号**：通常用于发布一些紧急的Bug修复（如：从1.0.0 升级到 1.0.1）。

**2. 版本目标 (Version Goal)**
每一个版本，都必须有一个清晰、聚焦的使命。比如，我们“大P超级电商”的第一个版本：
> **V1.0.0 版本目标**：上线一个稳定、可用的招商模式电商平台。核心目标是**跑通用户的核心交易流程**（从浏览到支付）和**商家的核心履约流程**（从发布商品到订单发货）。

**3. 版本功能 (Version Features)**
这是为了达成上述目标，我最终决定纳入这个版本的功能清单。它是我们总功能清单的一个“**子集**”。


---

## 2.5 本章总结

至此，我们已经完整地学习了电商项目从0到1的“**立项**”阶段的全部工作。
* **项目立项流程概述**：我们了解了项目启动的标志性节点——**立项评审会**，以及我们作为产品经理，需要在会上回答的核心问题。
* **立项说明书的核心构成**：我们系统地学习了立项说明书的三大核心模块——**产品概述**（确立“是什么”）、**市场分析**（论证“为什么能”）、**产品规划与架构**（描绘“怎么做”的蓝图）。
* **制定迭代计划**：我们掌握了如何将宏伟的蓝-图，拆解为一份**分阶段、有重点、可执行**的迭代计划，明确了我们MVP版本的方向。

当我们带着这份经过深度思考、并获得决策层认可的“立项说明书”和“V1.0迭代计划”，走出立项评审会时，我们的电商项目，才算真正地、正式地，扬帆起航了。


最后，我们附上立项说明书模板快速完成立项需求的任务

{% link 产品立项说明书模板.docx,Prorise,https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E4%BA%A7%E5%93%81%E7%AB%8B%E9%A1%B9%E8%AF%B4%E6%98%8E%E4%B9%A6%E6%A8%A1%E6%9D%BF.docx,https://bu.dusays.com/2025/07/19/687b2cf24c5db.png %}



---
# 第三章：电商用户端产品设计

欢迎来到第三章。在这一章，我们将真正地以“建筑师”的身份，从地基开始，一砖一瓦地搭建起我们电商产品的“用户端大楼”。

我们将系统性地学习，从用户首次进入产品的“大门”（产品形态），到在“商场”中闲逛（浏览商品）、挑选结账（下单支付）、寻求服务（售后），再到参与“广场”讨论（商品种草）和回到“私人房间”（个人中心）的全过程设计。

## 3.1 学习目标

在本节中，我的核心目标是，带大家掌握电商用户端设计的两大基石：**产品形态选择**和**核心设计思路**。

我们将学会对比不同产品形态（App/小程序等）的优劣，并能以微信小程序为例，掌握其独特的设计规范。



## 3.2 用户端产品形态选择

![image-20250721220851552](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721220851552.png)

在项目正式启动后，我面临的第一个重大的技术和战略决策就是：**我们主要应该为用户，打造一个什么样的“场”？**

是开发一个功能强大的独立**App**？还是一个便于在微信里传播的**小程序**？亦或是一个灵活轻便的**H5**网页？

正如我们看到的，像淘宝、京东这样成熟的平台，通常是“全都要”，在每一种形态上都有布局。但对于启动期的我们，必须做出取舍，选择最适合我们当前战略目标的形态。

### 3.2.1 常见产品形态对比 (App / 小程序 / H5 / Web端)

为了做出正确的决策，我通常会用下面这张对比表格，来系统地分析不同形态的优劣。

![image-20250721220811815](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721220811815.png)

| 产品形态 | 是否需安装 | 开发投入 | 用户体验 | 我的选择考量（适用场景） |
| :--- | :--- | :--- | :--- | :--- |
| **App** | **是** | **高** (需安卓/iOS分别开发) | **完善** | 当我的产品功能极其复杂、需要调用手机底层能力（如GPS、蓝牙）、且追求极致性能和体验时，我会选择App。它的推广和获客成本最高。 |
| **微信小程序**| **否** | **中** (前端开发) | **好** | 当我希望**借助微信的社交生态进行裂变传播和获客**时，小程序是我的不二之选。它体验好、开发快，特别适合电商、本地生活等需要社交分享的场景。 |
| **H5** | **否** | **低** (前端开发) | **中等** | 当我需要最大化的**灵活性和传播范围**时，我会选择H5。它不受任何平台限制，一个链接就可以走天下，特别适合制作营销活动页、内容文章页。 |
| **Web端**| **否** | **中** (前端开发) | **一般** | 当我的核心场景是**需要用户在电脑上进行复杂操作**时，我会选择Web端。比如，我们为商家设计的后台管理系统，就必须是Web端。 |

对于我们的“大P超级电商”项目，结合我们拥有海量C端用户的背景，**优先开发一个微信小程序**来承接和转化这部分流量，是一个非常明智的启动策略。

### 3.2.2 设计规范：以微信小程序为例

既然我们选择了小程序，那我就必须深入理解它的“游戏规则”。虽然小程序的设计在整体上与App非常类似，但因为它“寄生”于微信这个超级生态之上，所以也带来了一些独特的设计规范和特殊功能。

#### 1. 页面结构

* **官方小程序胶囊**：我必须时刻牢记，小程序页面的右上角，有一个官方的、包含“关闭/更多”等功能的“胶囊”按钮，这个区域是**不可设计的**，我的页面布局必须为它留出空间。

![image-20250721221058059](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221058059.png)

* **顶部导航栏**：小程序的顶部导航栏，由微信官方统一样式，我们只能定义中间的`标题区`文字和左侧`导航区`的返回逻辑。

![image-20250721221137650](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221137650.png)

* **标签栏 (Tab Bar)**：小程序底部的标签栏，有严格的数量限制：**最少2个，最多5个**。

![image-20250721221430247](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221430247.png)

* **尺寸规范**：在绘制原型时，我依然会采用和App一致的**375x667px**作为基准画板，并遵循`状态栏(22px)`、`导航栏(44px)`、`标签栏(49px)`的标准高度。

#### 2. 特殊功能与限制

小程序最大的魅力，在于它能调用微信生态的独有能力。假设我们需要实现“获取手机号”和“推送消息”，在小程序中的实现方式就与App完全不同。



![image-20250721221318459](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221318459.png)

* **获取微信手机号**：在App中，我需要用户手动输入手机号，再通过短信验证，流程繁琐。而在小程序里，我可以直接放置一个“**微信用户一键授权**”的按钮。用户点击后，会拉起微信的官方授权弹窗，用户只需点击“允许”，我们就能安全地获取到他绑定在微信上的手机号，**极大提升了注册/登录的转化率**。

![image-20250721221356124](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221356124.png)

* **订阅消息**：在App中，只要用户允许，我可以相对自由地向他推送消息。但在小程序中，则严格得多。我**不能主动向用户推送营销消息**。用户必须**主动“订阅”**某一个服务通知（比如“发货通知”、“降价提醒”），我才能向他发送一条对应的服务消息。这是一种对用户打扰更小的“**一次性授权**”模式，我在设计运营功能时必须充分考虑这个限制。


---
## 3.3 用户端产品设计思路

在我们选定了产品形态（如：微信小程序）之后，我不会立刻开始绘制具体的页面。我会先退一步，从更高维度，建立起整个用户端产品的“**设计思路和骨架**”。

虽然我们之前学习过内容产品的设计，但电商用户端，有其自身独特的业务核心和功能侧重。在这一节，我将带大家明确我们电商产品的核心业务，并掌握指导我们进行界面布局的两大基本设计原则。

### 3.3.1 核心业务与功能模块

![image-20250722091703261](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091703261.png)

我的第一步，是**将用户的完整购物旅程，拆解为几个核心的业务模块**。这能帮助我确保，我的产品设计，覆盖了用户从“认知”到“购后”的每一个关键环节。

对于一个典型的电商产品，我将核心业务拆解为以下六大模块：
1.  **注册登录**：这是用户“获取身份”的入口。
2.  **浏览商品**：这是用户“逛商场”的核心环节，包括有目的的搜索和无目的的闲逛。
3.  **下单支付**：这是电商的“收银台”，是完成交易的核心闭环。
4.  **订单与售后**：这是用户购后的“服务中心”，负责履约和处理问题。
5.  **商品种草**：这是我们产品特色的“内容社区”，负责吸引用户、建立信任。
6.  **个人中心**：这是用户在我们平台的“家”，负责汇总个人数据和产品全局设置。

![image-20250722091746923](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091746923.png)

这六大业务模块，就构成了我们用户端的“**核心功能架构**”。图中的“用户端核心架构”就是一个很好的示例，它将具体的、零散的功能点（如：搜索栏、金刚区），清晰地归类到了它所属的业务模块之下（如：首页）。这个架构，就是我们后续进行详细页面设计的“总纲”。

### 3.3.2 核心设计原则

有了功能架构的“骨架”，我接下来需要思考，如何为这个骨架“填充血肉”？也就是说，在具体的页面上，我应该如何组织和排布那些繁多的功能和信息，才能让用户觉得界面清晰、易于理解？

在这里，我会借助格式塔心理学（Gestalt Psychology）中，两个最基础、也最强大的视觉设计原则。

#### 1. 接近法则 (Law of Proximity)

![image-20250722091947975](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091947975.png)

* **法则定义**：我们的大脑，会本能地，将物理空间上**彼此靠近**的元素，视为一个**整体**。
* **我的设计应用**：在界面设计中，“**间距**”是我最有力的设计工具之一。
    * 我会把**相关**的元素（比如，一张商品图和它的商品标题）紧紧地放在一起，让它们在视觉上自然地成为一组。
    * 我会用**留白**，来拉开**不相关**的元素或组之间的距离，形成清晰的视觉区块。

#### 2. 相似法则 (Law of Similarity)

![image-20250722092013020](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092013201.png)

* **法则定义**：我们的大脑，会本能地，将那些在**视觉特征（如形状、颜色、大小）上相似**的元素，视为**同类**。

* **我的设计应用**：在界面设计中，“**一致性**”是建立用户认知的关键。
  
    * 我会确保所有**功能相同或相近**的元素，在视觉上保持**相似**。比如，所有“可点击”的按钮，都用同一种颜色和形状；所有“可输入”的文本框，都用同一种样式。
    
    ![image-20250722092053425](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092053425.png)
    
    * 正如“常用功能”案例所示，尽管每一个图标的图案都不同，但它们统一的尺寸、颜色和圆角风格，都在强烈地向用户暗示：“我们都属于一类，我们都是可以点击的功能入口”。

**总结**：在后续的页面设计中，我将综合运用“**接近法则**”来**组织页面布局、划分区块**，并用“**相似法则**”来**统一控件样式、建立操作认知**。这是让我们的设计变得“专业”和“易用”的秘诀。



-----

## 3.4 浏览商品

[此处放置“首页思考”的图片 (`image_2d6800.png`)]

我们电商产品的“商场”已经建好，现在，当用户走进这扇“大门”时，他们首先看到的，就是我们的“**商场大堂**”——**首页**。

首页，是用户对我们产品形成第一印象、也是我们引导用户走向各个“专柜”的最核心的枢纽。我设计首页时，始终围绕着一个核心问题：**它需要同时满足谁的需求？达成什么样的目的？**

### 3.4.1 首页设计

#### 1\. 首页的核心目的

我设计首页，必须同时扮演好“**服务员**”（满足用户需求）和“**商场经理**”（达成公司目标）的双重角色。

  * **从用户角度：满足多样化的“逛街”心态**
    ![image-20250722092825580](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092825580.png)

    我需要为进入首页、心态各不相同的用户，都提供最高效的解决方案。

| 用户心态 | 我的设计方案 |
| :--- | :--- |
| **“我明确知道要买什么”** | 在页面最顶部，提供一个**高效、精准的搜索栏**。 |
| **“我知道大概要买什么品类”** | 提供一套**清晰、易懂的商品分类入口**。 |
| **“我就是想随便逛逛，看有啥好东西”** | 提供一个无限下拉的、引人入胜的**个性化商品推荐列表**。 |
| **“我想看看有啥便宜可以占”** | 提供突出、有吸引力的**促销/活动专区**，如秒杀、百亿补贴等。 |

  * **从公司角度：实现平台的商业目标**
    ![image-20250722092907876](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092907876.png)

    同时，我也需要利用首页这个“寸土寸金”的场地，来达成我们的商业目的。

| 公司目标 | 我的设计方案 |
| :--- | :--- |
| **帮助商家促销引流** | 在首页的核心位置，为付费的商家提供**Banner广告位**和**活动入口**。 |
| **帮助自营店铺促销引流** | （若有自营业务）为自营的重点商品或活动，提供专属的曝光区域。 |
| **展现平台调性** | 整个首页的**视觉风格（UI）、文案、推荐内容**，都必须严格符合我们“内容驱动的潮流社区电商”的定位。 |

#### 2\. 常见首页模块解析

![image-20250722092959787](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092959787.png)

为了同时满足上述的用户和公司需求，经过多年的演化，电商首页已经形成了一套相对成熟的模块化布局。我会通过分析竞品，来借鉴和思考我们自己的设计。

| **核心模块** | **我的设计解读与应用** |
| :--- | :--- |
| **搜索栏** | **雷打不动的第一模块**，必须始终固定在页面最顶部，服务于目的性最强的用户。 |
| **金刚区** | ![image-20250722093051105](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722093051105.png)<br> 这是指搜索栏下方的一组**图标网格导航**。我把它看作是**核心业务的“一级入口”**。我会把我们最重要的商品分类（如“潮流服饰”、“数码新品”）和特色业务（如“达人直播”、“好物种草”）放在这里。 |
| **Banner / 促销区** | 这是首页最**黄金的广告和活动展示位**。我会用它来推广平台的重大活动，或将其作为重要的广告收入来源。 |
| **商品推荐列表** | 这是首页占据面积最大、也是留住“闲逛”用户的**核心内容区**。我会采用“**瀑布流**”的形式，通过个性化推荐算法，为每个用户呈现一个独一无二的、无限延伸的商品列表。 |

#### 3\. 我们“大P超级电商”的首页设计思路

最后，结合对竞品的分析和我们自身的定位，我为我们“大P超级电商”的首页，确立了以下设计思路：

1.  **UI风格**：界面要简洁、留白充分，营造出“呼吸感”，整体视觉风格要年轻、时尚，符合“90后”的审美。
2.  **金刚区设计**：必须体现我们“内容+电商”的特色。除了`服装`、`数码`等品类入口，必须包含`直播`、`种草`等内容社区的入口。

我们在设计首页时一定会遇到的经典决策：“金刚区”到底应该放几个图标？上面的文字应该怎么写？

| 图标 (示意) | 文字标签     | 我的设计思路                                                 |
| ----------- | ------------ | ------------------------------------------------------------ |
| 📱           | **手机数码** | “90后”核心关注的高价值品类，属于**品类入口**。               |
| 👚           | **潮流服饰** | 贴合我们“潮流”的平台调性，属于**品类入口**。                 |
| 💄           | **美妆个护** | 年轻用户，特别是女性用户的高频消费品类，属于**品类入口**。   |
| 📺           | **达人直播** | 我们的**核心差异化**业务，必须给予最强的曝光，属于**功能入口**。 |
| 💸           | **百亿补贴** | 电商平台“拉新促活”的标配，用明确的利益点吸引用户，属于**活动入口**。 |
| 🧾           | **领券中心** | 培养用户“先领券再购物”的习惯，提升转化率，属于**功能入口**。 |
| 📦           | **我的订单** | 用户最高频使用的查询功能之一，提供一个快捷入口，属于**功能入口**。 |
| ➡️           | **全部分类** | “渐进式呈现”原则的应用，收纳所有其他品类。                   |

3.**推荐算法**：商品推荐列表的算法，除了考虑用户的浏览和购买行为，还必须**高度重视用户的社交和内容偏好**。比如，优先推荐“用户关注的KOL正在推荐的商品”

最后我们产出的商品低保真原型原型，大致是这样的：

![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/c2f3be4f-3f73-4c54-a40b-d41760dc006b.png)


---
### 3.4.2 商品分类设计（无分类、一级、多级）

在设计好首页之后，我们需要为那些有大概购物方向的用户，提供一套清晰的“货架导引”系统，这个系统就是**商品分类**。它的核心目的，是满足用户高效缩小寻找范围的需求。

我给商品类目的定义是：按照商品的用途、特征等维度，并且根据一定的管理目的，把相似的商品归为一类的行为。并且在类别当中又会存在细分的类型。

我设计分类体系的复杂度，完全取决于我们平台商品的数量和丰富度。我通常会根据平台的体量，考虑三种不同的分类形式。

![image-20250722101139313](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101139313.png)

这三种形式，分别适用于不同的业务阶段和规模，我们可以通过上面的案例直观地感受它们的差异。对于我们“大P超级电商”这样的综合性平台，一个清晰的“多级分类”体系是必不可少的设计。

### 3.4.3 商品列表与详情页设计（图文、参数、评价、推荐）

当用户通过首页、搜索或分类，最终都会来到两个核心的页面：**商品列表页（PLP）**和**商品详情页（PDP）**。

我设计这两个页面时，脑海里始终装着三类典型用户：目的明确的“小风”、犹豫不决的“中风”、以及纯粹闲逛的“大风”。

![image-20250722101836348](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101836348.png)

**1. 商品列表页 (Product List Page - PLP)**

商品列表页是我们商场的“**货架**”。它的核心设计目标，是让用户能**高效地筛选和对比**。虽然列表的内容来源可能不同（如搜索结果、推荐、分类），但其页面结构和设计要点是共通的。

![image-20250722101931329](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101931329.png)

一个优秀的商品列表页，必须包含清晰的**商品卡片**（展示图、文、价等核心信息），以及强大的**筛选与排序**功能，来帮助用户快速从海量商品中，找到自己心仪的目标。

![image-20250722105209159](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722105209159.png)



**2. 商品详情页 (Product Detail Page - PDP)**

商品详情页是我们商场的“**金牌销售员**”，它的核心设计目标是**打消用户的所有疑虑，促成最终的购买**。我通常会将页面上半部分，用来满足理性用户的决策需求。

![image-20250722101959967](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101959967.png)

页面的首屏要点，在于简明扼要地表现出产品的核心信息，让用户直接判断出“这个产品是什么”。我会通过**商品展示区**（高清图/视频）、**商品属性区**（规格参数）和**用户评价区**（社群证明），来分别满足用户对“颜值”、“内涵”和“口碑”的确认需求。

页面的下半部分，我则用来服务那些还在犹豫、或者纯粹闲逛的用户，通过更丰富的内容，对他们进行深度“种草”。

![image-20250722102009141](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722102009141.png)

这部分通常包括图文并茂的**详情介绍**，用来吸引用户；以及**问答模块**，用来对评价功能进行进一步强化，打消用户的购买疑虑。

在详情页的底部，我一定会设计一个“**智能推荐**”模块。它的核心目的，是在用户对当前商品不满意、准备离开时，为他提供更多相关的选择，**形成一个流量的闭环**，增加用户在我们平台留存和成交的机会。

![image-20250722102020748](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722102020748.png)




---
## 3.5 下单支付

在用户完成了“逛”和“选”，将心仪的商品加入购物车之后，我们就进入了整个电商流程的“**核心交易环节**”。

我把这个环节的设计，看作是引导用户走过一条“**信任与效率的走廊**”。这条走廊上的任何一个障碍、一丝疑虑，都可能导致用户在最后关头放弃购买。因此，我的设计目标，必须是**极致的顺滑、清晰与安全**。

### 3.5.1 下单流程与页面设计（提交页、支付页）

我通常会将整个下单支付流程，拆解为两个核心的页面来进行设计：**订单提交页**和**支付页（收银台）**。

#### 1. 订单提交页 (Order Submission Page)

当用户在商品详情页点击“立即购买”，或在购物车点击“去结算”后，并不会直接进入付款环节，而是会先来到**订单提交页**。

![image-20250722110142381](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110142381.png)

这个页面的核心作用，我把它定义为用户的“**最后一次确认**”。在用户真正掏钱之前，我必须为他提供一个清晰的、所有交易信息的汇总页面，让他进行最后的检查和确认。

![image-20250722110204985](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110204985.png)

我设计的订单提交页，必须让用户能够清晰地完成三件事：
* **确认商品**：清晰地罗列出本次将要购买的所有商品信息（名称、SKU、数量、价格）。
* **确认地址**：提供默认收货地址，并允许用户方便地选择或新增其他地址。
* **确认价格**：清晰地展示商品总额、运费、优惠券抵扣、最终实付金额等所有价格明细。



#### 2. 支付页/收银台 (Payment Page / Cashier)

![image-20250722110039262](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110039262.png)

当用户在订单提交页，点击“提交订单”后，他才真正进入了“**支付页**”，我常称之为“**收银台**”。

![image-20250722105741343](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722105741343.png)

这个页面的设计，我追求的是**极致的简洁和安全感**。它的核心作用只有三个：
1.  **确认实付金额**：醒目地展示最终需要支付的金额。
2.  **选择支付方式**：提供用户选择支付渠道（如微信、支付宝）的入口。
3.  **完成支付**：一个清晰、唯一的“确认支付”按钮。

**我的拓展设计（支付异常处理）**：
![image-20250722110255711](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110255711.png)

设计支付页时，我必须考虑一个最常见的异常场景：**用户进入了收银台，但因为种种原因，没有完成支付就退出了**。
一个糟糕的设计，可能会让用户之前提交的订单直接消失。而一个优秀的设计，正如案例所示，应该**将这份订单，自动保存为一张“待支付”的订单**。用户可以在“我的订单”中随时找到它，并重新发起支付。这个小小的设计，能为我们挽回大量可能流失的销售额。

#### 3. 支付成功后的流程

![image-20250722110528929](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110528929.png)

当用户成功支付后，这笔“交易”在后台就正式生成了，并进入了它的生命周期。我会用一组“**订单状态**”，来清晰地标记它在流程中所处的节点。支付成功，就是订单状态从“**待支付**”流转到“**待发货**”的触发器。

![image-20250722110510755](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110510755.png)

此时，用户可以在“个人中心”的“**我的订单**”列表中，看到这笔订单，并查看到它“待发货”的状态。当商家发货后，用户最关心的“**物流信息**”就会在这里出现。

![image-20250722110805038](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110805038.png)

>`思考：“物流信息是从哪来的🤔”`

![image-20250722110821135](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110821135.png)

![image-20250722110923521](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110923521.png)

它并不是由我们的商家手动更新的。在我设计的后台，商家发货时，只需要选择**快递公司**并输入**快递单号**。随后，我们的**后端服务器**，就会通过API接口，**定时地向第三方快递查询平台（如“申通”）发起查询请求**，获取最新的物流轨迹，然后将这些信息，展示在用户端的订单详情页上。

![image-20250722111007040](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111007040.png)

我在撰写PRD时，必须将这个技术方案的逻辑，清晰地描述出来。




---
### 3.5.2 支付方式（微信、支付宝、银联、聚合支付）

在设计好“收银台”页面后，我需要做的最重要的决策，就是为这个收银台，配备哪些“**收款设备**”，也就是我们常说的**支付方式**。

在今天的中国市场，只提供一种支付方式是远远不够的。为了最大化地提升支付成功率，我至少需要为用户提供微信支付和支付宝这两种主流选择。

#### 1. 微信支付 & 支付宝 & 银联（独立渠道对接）

![image-20250722111349078](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111349078.png)



![image-20250722111359741](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111359741.png)

![image-20250722111408567](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111408567.png)

理论上，我们可以分别独立地去对接每一个支付渠道。

**我需要告诉开发的关键点（以微信支付为例）：**
要接入微信支付，不是一个纯粹的技术工作，它需要产品、运营和技术共同协作。我需要了解并推动以下几个步骤：

1.  **商户号申请（运营/商务负责）**：首先，我们需要由公司的运营或商务同事，前往“**微信支付商户平台**”，提交我们公司的营业执照等资质，申请一个“商户号（mch_id）”。这个过程，支付宝和银联也完全一样，都需要我们先拥有一个官方认证的“商家身份”。
2.  **获取开发凭证（研发负责人）**：当我们的商户号被批准后，技术负责人需要登录这个商户平台，去获取进行技术开发所必需的“**身份凭证**”。这通常包括`AppID`、`API证书`、`API密钥`等。我把它们理解为，我们公司服务器与微信支付服务器之间，进行加密通信的“账号和密码”。
3.  **技术对接（研发负责）**：拿到凭证后，研发同学才会真正开始写代码。
    * **后端开发**：需要按照微信/支付宝的官方开发文档，开发服务端接口。这些接口主要负责创建“预支付订单”、接收支付成功或失败的“异步通知”等。
    * **前端开发（App）**：需要集成微信/支付宝的官方SDK（软件开发工具包）。这个SDK的主要作用，是在我们的App里，能够“拉起”用户手机上已经安装的微信或支付宝App，来进行最终的密码/指纹输入。

#### 2. 聚合支付（我的推荐方案）

![image-20250722111626234](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111626234.png)

在我们理解了独立对接的流程后，两个核心痛点就浮现出来了：
* **接入成本高**：我要支持微信、支付宝、银联三种支付，我的研发团队就需要把上面的流程，**重复做三遍**。这需要耗费巨大的研发资源。
* **财务管理难**：每天，我的财务同事，需要分别登录三个不同的商户后台，去下载三份不同的对账单，再进行手动的汇总和核对，极其繁琐且容易出错。

![image-20250722111642616](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111642616.png)

为了解决这两个痛点，一个更聪明的、也是我强烈推荐的方案，就是使用“**聚合支付**”。

![image-20250722111748908](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111748908.png)

**聚合支付**服务商（如 **Ping++、Adapay**），就是支付领域的“**万能转换插头**”。它已经提前帮我们，把市面上所有的主流支付渠道（微信、支付宝、银联、各类银行卡等），都预先集成好了。

**我需要告诉开发的关键点（技术视角）：**
* **一次对接，全部拥有**：我们不再需要去对接微信、支付宝等多个上游渠道。我的研发团队，只需要按照聚合支付服务商提供的一份开发文档，**进行一次技术对接**即可。
* **统一的API和SDK**：聚合支付会为我们提供**一套统一的API和SDK**。当用户在我们的App里选择用微信支付时，我们的App调用的，是聚合支付的SDK；我们的服务器，也只请求聚合支付的API。后续由聚合支付的服务，来和微信的服务器进行通信。
* **统一的后台和对账单**：我的财务同事，只需要登录聚合支付这**一个后台**，就可以看到所有渠道的交易流水，并下载一份统一的对-账单。

**我的决策权衡：**
使用聚合支付的唯一“缺点”，是它会在每个渠道的原有费率基础上，再收取一点点的服务费。但考虑到它能为我们**节省巨大的研发成本和财务管理成本**，对于绝大多数公司（特别是初创和中型公司）而言，这笔服务费，都是一笔**极具性价比**的投资。

>`聚合支付是不是代表不用哪些营业证书之类的，直接通过付钱就能接入了🤔`
>
>答案是：**不是的，聚合支付并不能免除您提供公司资质（如营业执照）的义务。**
>
>您可以把聚合支付服务商，看作是一个“**超级代办员**”或“**技术外包服务商**”，而不是一个“**资质豁免机构**”。他们的核心价值在于**简化技术对接和财务管理**，而不是绕过金融监管。
>
>我为您梳理一下实际的流程：
>
>1. **您与聚合支付签约**：您首先需要选择一家聚合支付服务商（如Ping++），并在他们的平台上注册账户。
>2. **您向聚合支付提交资质**：在注册过程中，您**仍然需要**向聚合支付服务商，提交您自己公司的全套有效资质，包括但不限于：
>	- **营业执照**
>	- **法人身份证信息**
>	- **对公银行账户**
>	- **网站/App的ICP备案信息**（如果适用）
>3. **聚合支付为您代办申请**：聚合支付服务商在收到您的资质后，会作为您的“代办员”，拿着您的这些材料，去分别向微信支付、支付宝、银联等官方渠道，为您**集中申请**开通各个支付渠道的商户权限。
>4. **最终结果**：审批通过后，您最终获得的，依然是**您自己公司名下的、在微信和支付宝备案的合法商户号**。聚合支付只是为您提供了一个统一的技术接口和管理后台来操作它们。

| **支付方案** | **我的解读** | **优点** | **缺点** |
| :--- | :--- | :--- | :--- |
| **逐个渠道对接** | 我们分别与微信、支付宝等签约并进行技术开发。 | 费率可能略低，资金直接到账。 | 开发成本极高，财务对账繁琐。 |
| **使用聚合支付** | 我们只与一家聚合支付服务商签约和开发。 | **开发成本极低（只需一次对接）**，财务对账简单。 | 费率略高，资金需要经过聚合服务商中转。 |





---
### 3.5.3 库存管理与问题应对

我们都可能有过这样的经历：在一个平台下单后，不急着付款，过了一会儿想起来去支付，发现订单依然有效；

而在另一个平台，同样的操作，回来支付时却被告知“商品已售罄”。

**这是为什么呢？**
这背后，就反映了不同电商平台，对于“**库存扣减**”这个核心问题，采取了两种截然不同的产品策略。

#### 1. 库存扣减方式（拍下减 vs 付款减）

在我设计交易系统时，我必须与我的技术和业务负责人，共同做出一个关键决策：**我们的库存，到底应该在哪一个节点扣减？** 这个决策，没有绝对的好坏，只有不同选择下的利弊权衡。

| 扣减方式 | 核心逻辑 | 用户体验 | 主要风险 | 我的选择考量 |
| :--- | :--- | :--- | :--- | :--- |
| **拍下减** | 当用户点击“**提交订单**”的瞬间，无论是否付款，系统都会立即为他预留这份库存。 | **好**。用户会感觉“只要我下单了，这个货就是我的了”，体验非常安心。 | **恶拍** | 我通常会在普通商品的销售中，采用此方式，因为它能提供最佳的用户体验。 |
| **付款减** | 只有当用户**成功完成支付**的瞬间，系统才会去扣减实际的物理库存。 | **一般**。用户下单后，可能会因为犹豫了几分钟，回来支付时发现商品已被别人买走，导致体验不佳和用户流失。 | **超卖** | 我通常只在库存极少、瞬时流量极大的“秒杀”等营销活动中，才会谨慎采用此方式。 |

作为产品经理，我的工作，就是**选择一种方式，并设计一套完整的机制，来最大化地规避它的潜在风险**。

#### 2. 问题与应对（恶意拍单、超卖）

##### **应对“恶拍”**

![image-20250722112931696](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722112931696.png)

如果我选择了“**拍下减库存**”，那我的头号敌人，就是“**恶拍**”——即，竞争对手或黄牛，恶意地大量下单但不支付，以此来锁死我的库存，让真实用户无法购买。

为了应对它，我必须建立一套“**组合防御**”体系：
1.  **减少库存保留时间**：这是我最核心的武器。我会设计一个“**订单自动取消**”的规则。比如，**下单后15分钟内未支付**，系统将自动取消这笔订单，并将预留的库存，**重新释放**回公共库存池中，供其他用户购买。
2.  **限购**：对于一些热门或促销商品，我会在产品层面，增加“**限购**”规则。比如，规定“**单个ID限购1件**”，这能有效防止单一恶意用户锁死大量库存。
3.  **安全策略**：我还会和风控团队合作，建立监控机制。当发现某个用户ID，在短时间内，有大量“下单后又取消”的异常行为时，系统可以暂时限制他的下单权限。

##### **应对“超卖”**

![image-20250722113011849](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113011849.png)

如果我选择了“**付款减库存**”，那我最大的噩梦，就是“**超卖**”——即，我们实际卖出的商品数量，超过了我们的真实库存。这会引发严重的客诉，极大地损害平台信誉。

为了应对它，我同样需要一套“**组合防御**”体系：
1.  **技术角度解决**：这主要依赖于研发团队。我会要求我的技术负责人，必须在技术层面，通过**数据库锁**或**分布式队列**等技术，来处理高并发场景下的库存扣减请求，确保对最后一件库存的扣减操作，是“**原子性**”的（即，在同一瞬间，只能有一个请求能成功）。
2.  **提示用户**：在产品体验层面，为了管理用户预期，当某个商品的库存数量很少时（比如，少于10件），我会在商品详情页和购物车中，明确地展示“**库存紧张**”或“**仅剩X件**”的提示文案。
3.  **设置安全库存**：这是我最常用的一个运营策略。如果一个商品的物理库存有1000件，我会在电商后台的“可售卖库存”中，只填写**950**件。那剩下的50件，就成了我的“**安全库存**”。它就像一个缓冲垫，既能消化掉极端情况下，因技术原因产生的少量超卖，也能用来应对用户“退货换货”的需求。





-----

### 3.5.4 拆单逻辑（父子订单、半拆单）

当用户在我们的购物车里，同时选中了来自不同商家、或者满足某些特殊条件的多个商品，然后点击“去结算”时，一个复杂的问题就摆在了我面前：

**后台应该如何处理这张“大单”？是把它当作一个订单，还是多个订单？**

![image-20250722110016603](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110016603.png)

“如果从购物车（多个店铺多个商品）进入结算，需要考虑什么？” 在这种情况下，我的订单提交页，必须进行“**拆单**”展示。我会**按照不同的店铺，将商品进行分组**。每个店铺的商品，会形成一个独立的“包裹”，分别计算运费和优惠，最终汇总成一个总的支付金额。这种清晰的结构，是解决多商家同时结算场景的最佳实践。

#### 1\. 为什么要拆单？

我设计拆单逻辑，主要是为了应对以下五种常见的业务场景：

| **我的设计考量** | **拆单因素** |
| :--- | :--- |
| ![image-20250722113550199](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113550199.png) <br> 这是最常见的拆单原因。不同商家的商品，其**货权、发货地、财务结算主体**都不同，因此必须拆分为独立的订单，分别进行处理。 | **店铺 (Store)** |
| ![image-20250722113814579](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113814579.png)<br> 即便用户购买的是同一个自营商家的多件商品，这些商品也可能存放在**全国不同的仓库**。为了最高效地完成履约，系统需要按仓库，将订单拆分给不同的仓储中心进行打包发货。 | **仓库 (Warehouse)** |
| ![image-20250722113838115](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113838115.png) <br> 不同的快递公司，对单个包裹的**重量和体积**都有上限。当用户购买的商品总重量或总体积超过限制时，就需要拆分为多个包裹，对应生成多个订单。 | **物流 (Logistics)** |
| ![image-20250722113859980](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113859980.png)<br> 某些**特殊品类**的商品需要单独处理。比如，`易碎品`需要特殊包装，`超大件`（如轮胎）无法与普通商品合并打包，都需要独立成单。 | **品类 (Category)** |
| ![image-20250722113920499](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113920499.png) <br> 这主要应用于**跨境海淘**业务。根据国家政策，跨境零售进口商品的单次交易限值为5000元。当用户的单笔订单超过这个限值时，系统必须将其拆分为多个订单，以符合清关和税务要求。 | **商品价值 (Value)** |

#### 2\. 拆单的两种主流模式

![image-20250722113943020](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113943020.png)

明确了“为什么拆”，我们再来看“怎么拆”。行业内，主要有两种主流的拆单模式，它们最核心的区别，在于**拆单发生的时机**。

##### **父子订单模式 (Parent-Child Order Model)**

![image-20250722114113042](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722114113042.png)

  * **核心逻辑**：**先支付，后拆分**。
  * **我的解读**：在这种模式下，用户从下单到支付完成，始终面对的是一个统一的“**父订单**”。他只需要付一次总的款项。当支付成功后，我们的后端系统，才会根据拆单规则，将这个父订单，在后台默默地拆分为多个“**子订单**”，分别推送给不同的仓库或商家去履约。
  * **用户感知**：用户在“我的订单”列表中，会看到一个父订单，点进去之后，才能看到下面包含的多个子订单，每个子订单都有独立的物流和状态。
  * **典型代表**：**京东**。
  * **优点**：用户支付体验统一、流畅。能有效避免下面要讲的“优惠券漏洞”。
  * **缺点**：后端系统的处理逻辑相对更复杂。

##### **半拆单模式 (Semi-split Order Model)**

![image-20250722132926059](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722132926059.png)

  * **核心逻辑**：**先拆分，后支付**。

  * **我的解读**：在这种模式下，当用户从购物车点击“去结算”时，系统在进入“**订单提交页**”的那一刻，就已经**完成了拆分**。页面上会直接按照店铺等维度，将商品展示为**多个独立的订单**。用户需要对这些独立的订单，进行统一支付（或者也可以选择只支付其中一部分）。

  * **用户感知**：用户在支付前，就已经明确知道自己的购物车，被分成了几笔不同的订单。

  * **典型代表**：**淘宝**。

  * **优点**：业务逻辑相对简单清晰。

  * **缺点（及我的应对）**：
    ![image-20250722133014820](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133014820.png)

    这种模式存在一个著名的“**薅羊毛**”漏洞。比如，平台有一个“跨店满300减50”的活动。用户可以从A店选200元商品，B店选100元商品，凑成一单。在订单提交页，系统会把优惠按比例分摊到两个独立的订单上。此时，用户如果只支付A店的那个订单，就等于用不到200元的价格，享受到了满减优惠。

    我作为产品经理，**必须设计规则来规避这个漏洞**。比如，我会定义：
    
    “**对于参与跨店满减活动的组合订单，若用户在规定时间内未完成所有相关订单的支付，则所有订单将被自动取消，优惠券也将退回。**”

---
### 3.5.5 购物车功能设计（信息展示、库存监控、结算等）

在设计下单流程时，我首先要面临一个战略性的选择：**我们的电商产品，到底需不需要购物车？**

这并不是一个理所当然的问题。购物车的设计，必须服务于我们产品的核心交易模式。

#### 1. 购物车的作用与决策

![image-20250722133533377](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133533377.png)

![image-20250722133601731](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133601731.png)

![image-20250722133608676](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133608676.png)


* **什么时候我“需要”购物车？**
    对于像我们“大P超级电商”这样的**招商模式/混合模式**平台，购物车是**必须品**。因为用户会在不同的店铺之间“逛”，它的核心作用是：
    1.  **凑单与比价**：让用户可以把来自不同店铺的、感兴趣的商品，先放在一个地方，进行统一的比较和筛选。
    2.  **跨店促销**：是实现“跨店满减”等复杂促销活动的**技术基础**。

* **什么时候我“不需要”购物车？**
    1.  **C2C二手交易模式（如：闲鱼）**：二手商品大多是“孤品”（库存只有1件），交易前通常需要买卖双方进行沟通议价，流程复杂。购物车这种“先暂存再统一结算”的模式，会增加无效库存的锁定，并打断沟通流程，反而降低交易效率。
    2.  **拼团模式（如：拼多多）**：拼多多的核心是“低价爆款、冲动消费”。它希望用户看到一个商品，立刻就完成下单转化。购物车的存在，会让用户“冷静下来”、进行“反复比价”，这与它的核心商业模式是相悖的。

**结论**：对于我们的“大P超级电商”，购物车是用户完成多商品、跨店铺购买的核心功能，我们必须精心设计。

#### 2. 购物车核心功能设计

![image-20250722133729846](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133729846.png)

我设计购物车，会围绕“**进入**”、“**使用**”、“**离开**”这三个场景，来规划它的核心功能。

* **信息展示 (Information Display)**
    ![image-20250722133750028](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133750028.png)
    这是购物车最基础，也是最重要的部分。
    * **状态区分**：我需要设计两种状态。**未登录**时，购物车应为空，并展示商品推荐，引导用户去逛；**登录**后，则展示用户已添加的商品。
    * **分组与排序**：为了让信息清晰，我必须将商品按“**店铺**”进行分组。在店铺内部，商品会按照“**添加时间**”的倒序排列，最新添加的在最上方。
    * **营销信息**：我会清晰地展示每个商品适用的优惠信息（如“满减”、“优惠券”），以及店铺整体的促销活动，刺激用户凑单。

* **库存监控 (Inventory Monitoring)**
    ![image-20250722133856218](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133856218.png)
    我必须在购物车里，就向用户提供实时的库存状态，避免他到了提交订单的最后一步，才发现商品已售罄。我会设计三种库存状态的展示：
    1.  **有货**：正常显示。
    2.  **库存紧张**：当库存很少时（如＜5件），用红字等醒目的方式，提示用户“仅剩X件”，制造稀缺感，促进转化。
    3.  **无货**：当商品售罄时，商品必须被置灰，数量选择器变为不可用状态，并清晰地提示“已售罄”或“无货”。

* **编辑功能 (Editing)**
    ![image-20250722133941901](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133941901.png)
    我必须赋予用户对购物车的完全掌控权。这包括：
    * **修改商品数量**：提供简单易用的加、减数量选择器。
    * **修改商品规格**：允许用户直接在购物车，切换商品的SKU（如颜色、尺码）。
    * **删除商品**：提供删除单个商品，以及在“编辑”模式下，批量删除多个商品的功能。

* **结算功能 (Checkout)**
    ![image-20250722134009275](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134009275.png)
    这是购物车最终的“使命召唤”按钮。我会在页面底部，设计一个常驻的结算栏，它必须清晰地展示：
    * **已勾选商品的总计金额**
    * **优惠减免的金额明细**
    * **一个色彩鲜明、吸引点击的“去结算”按钮**，并标明已选商品的数量。




---
## 3.6 订单评价及售后

用户的购物旅程，在支付成功的那一刻，其实才刚刚过半。

**从“支付成功”到“满意使用”**，这“最后一公里”的体验，我称之为**购后体验**，它直接决定了用户是否会成为我们的回头客。

本节，我们就来设计购后体验中，最重要的两个环节：**订单评价**和**售后流程**。

### 3.6.1 订单评价维度（商品质量、服务态度等）

我始终认为，**订单评价**，是电商平台**信任体系的基石**。它既是后续用户的“购买决策参考”，也是平台用来“管理商家”的重要数据来源。

![image-20250722134457500](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134457500.png)

一个设计良好的评价体系，能极大地促进平台的健康循环。正如流程图所示，**查看商品评价**，是用户在“购买决策”前的关键一步，直接影响着平台的转化率。

![image-20250722134535203](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134535203.png)

我设计评价体系，核心是定义好“**评价维度**”，即，我希望用户从哪些方面，来对这次交易进行评价。

![image-20250722134626963](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134626963.png)

一个专业、全面的评价功能，至少应该包含以下两部分：

**1. 多维度评分**
为了得到可量化的、能用于商家考核的数据，我不会只让用户打一个“总分”，而是会将评分，拆解为几个核心的维度。

![image-20250722134658540](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134658540.png)

| **评价维度** | **我的设计说明** |
| :--- | :--- |
| **商品质量** | 核心维度，直接反映了“货”的品质。 |
| **发货速度**| 反映商家履约环节的“物流”效率。 |
| **服务态度**| 反映商家在售前、售中、售后环节的“服务”质量。 |

我会将这几个维度，都设计为“**1-5星**”的评分形式，这能让我非常直观地，计算出商家的综合服务评分。

**2. 图文评价**
除了量化的评分，我还需要提供一个让用户能自由表达、分享购物体验的“内容创作区”。

![image-20250722134741503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134741503.png)

这个功能的设计，我会包含：
* **文字评价**：一个开放的文本输入框，让用户可以详细描述购物心得。
* **图片/视频评价**：提供图片/视频的上传功能。“有图有真相”，带图的评价，是所有评价中，对其他用户参考价值最高、最可信的。

### 3.6.2 售后流程设计（取消、退款退货、换货等）

即便我们尽了最大努力，交易过程中也难免会出现各种问题。一套**清晰、合理、公正**的售后流程，是我们在用户遇到问题时，挽回他们信任的最后机会。我把这个流程，也称为“**逆向流程**”。

我设计售后流程，最核心的原则是：**在订单的不同生命周期（状态）下，为用户提供不同的、符合当前场景的售后操作**。

![image-20250722134857703](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134857703.png)


| **订单状态** | **可执行的售后操作** | **我的设计说明** |
| :--- | :--- | :--- |
| `待支付` | **取消订单** | 用户未付款，可无理由取消。 |
| `待发货` | **仅退款** | 用户已付款但未发货，可直接申请退款（无需退货）。 |
| `待收货` | **申请退款** | 用户可申请退款，触发包裹拦截。**退款成功需待拦截成功或用户拒收。** 建议收货后再发起换货。 |
| `交易成功`| **申请售后** | 在售后保障期内，可申请退款退货、换货或维修。 |




---
## 3.7 商品种草（社区化设计）

我们已经设计完了电商平台最核心的“**交易链路**”。现在，我们要开始为我们的产品，构建真正的“**护城河**”——**商品种草**，也就是**社区化设计**。

这部分的设计，将直接体现我们“**内容驱动的潮流社区电商**”的核心定位，是我们区别于传统货架式电商、吸引和留存年轻用户的关键。

### 3.7.1 种草定义与场景（内容推荐、发布与互动）

首先，我来定义一下“种草”。在我看来，它是一种**基于真实体验和信任关系的内容化商品推荐行为**。它包含两个方面：
* **被种草**：我通过看别人的分享，发现了一款好物，并产生了购买的欲望。
* **去种草**：我因为使用了一款好物，自发地去分享我的使用心得，推荐给别人。

![image-20250722154101263](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154101263.png)

在我们的平台上，用户的“种草”旅程，也分为“**看帖**”和“**发帖**”这两条核心路径。基于这两条路径，我提炼出了三大核心用户场景，以及支撑这些场景的必备功能。

![image-20250722154203318](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154203318.png)

1.  **发布购物心得**：这是“去种草”的场景，需要我们提供`发布心得`的功能。
2.  **查看他人购物心得**：这是“被种草”的场景，需要我们提供一个`种草社区`（信息流）。
3.  **针对购物心得互动**：这是社区活跃的保障，需要我们提供`点赞`、`收藏`、`分享`、`评论`等功能。

### 3.7.2 社区功能结构（搜索、发布、瀑布流、话题标签）

现在，我们来具体设计支撑上述场景的核心功能界面。

**1. 种草社区 (信息流)**

![image-20250722154335398](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154335398.png)

这是用户“被种草”的核心场所，我设计的要点如下：
* **瀑布流布局**：为了最大化地突出图片这种强视觉冲击力的内容，我会采用**瀑布流**的布局形式来呈现“种草”笔记列表。
* **关键词搜索**：在顶部，我必须提供一个强大的**搜索**功能，让用户可以根据关键词，精准地查找自己感兴趣的“种草”内容。
* **分类/话题查看**：提供按不同**分类**或**话题**，来筛选和浏览“种草”笔记的功能，满足用户宽泛的浏览需求。

**2. 发布种草 (内容发布页)**

![image-20250722155905759](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722155905759.png)

这是用户“去种草”的核心工具，我设计的要点如下：
* **图片/视频上传**：提供入口，让用户可以选择手机里的**单张或多张图片/视频**进行上传。
* **编写心得内容**：提供一个富文本编辑器，让用户可以**撰写**自己的使用心得和推荐理由。
* **关联商品**：**这是连接“内容”与“电商”最关键的一步**。我必须提供一个功能，让用户可以在发布笔记时，方便地**关联**到我们平台上正在售卖的**具体商品**。这就在“种草”和“拔草”之间，建立起了最短的转化路径。
* **选择话题标签**：允许用户为自己的笔记，选择或创建**话题标签**，这既能表达自己的内容核心，也便于被有相同兴趣的用户发现。





---
## 3.8 个人中心

当用户在我们的“商场”里完成了浏览、购买、评价等一系列行为后，他们需要一个地方，来存放他们的“战利品”（订单）、“会员卡”（个人信息）和“购物小票”（历史记录）。这个地方，就是**个人中心**。

在我看来，个人中心是**用户在我们平台上的“数字资产”管理中心**，是提升用户归属感、提供深度服务的核心枢纽。

### 3.8.1 核心功能版块设计（我的订单、设置、推荐、快捷入口等）

![image-20250722160633138](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722160633138.png)

我设计个人中心，不会简单地把所有功能堆砌在一起。我会像整理房间一样，将功能进行**逻辑分区**，让用户能快速找到自己想要的东西。根据淘宝这类成熟产品的经验，我通常会将个人中心，划分为以下四大版块：

**1. 用户数据与资产**
这是整个页面的“门面”，是用户最核心的个人信息和资产的展示区。
* **个人信息**：最顶部，清晰地展示用户的`头像`和`昵称`，并提供一个入口，可以跳转到更详细的“个人资料页”进行编辑。
* **业务数据**：将用户最关心的几个动态数据，进行可视化展示，比如`商品收藏`、`店铺收藏`、`浏览足迹`的数量。
* **核心资产入口**：提供用户最重要的“资产”的快捷入口。对于我们平台，最重要的就是`我的订单`和我们特色功能的`我的种草`。

**2. 快捷功能入口**
这是一个灵活的、网格布局的区域，我用它来聚合一些**使用频率相对较高**的功能或运营活动入口。比如`我的优惠券`、`客服中心`、`地址管理`、`每日签到`等。

**3. 应用全局设置**
这个版块，我通常会把它放在页面的下半部分，或者收纳到一个统一的“**设置**”入口里。它包含的是一些低频、但必要的全局性功能，比如`账号与安全`、`支付设置`、`关于我们`，以及最重要的`退出登录`按钮。

**4. 个性化推荐**
个人中心是一个高度个性化的页面，因此，它也是进行**精准商品推荐**的绝佳场所。在页面的底部，我会设计一个“**为你推荐**”的模块，根据用户的历史购买、收藏和浏览记录，为他推荐可能感兴趣的商品，以创造更多的交叉销售机会。

![image-20250722160650480](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722160650480.png)

我们为“大P超级电商”设计的这份个人中心线框图，就是上述设计思路的一个具体体现。它结构清晰、主次分明，将用户最关心的“我的订单”和“我的种草”放在了最核心的位置，确保了用户体验的便捷。

## 3.9 本章总结

至此，我们已经完整地设计出了一个电商产品用户端的所有核心模块。让我们最后回顾一下本章的整个设计旅程：

| **设计模块** | **核心产出与学习要点** |
| :--- | :--- |
| **产品形态选择**| 我们对比了**App/小程序/H5/Web**的优劣，并深入学习了**微信小程序**独特的设计规范与特殊功能。 |
| **用户端设计思路**| 我们确立了电商的**六大核心业务模块**，并掌握了指导界面布局的**接近法则**与**相似法则**。 |
| **浏览商品**| 我们设计了**首页、商品分类、商品列表页**和**商品详情页**，构建了用户“逛”和“选”的核心路径。 |
| **下单支付**| 我们设计了**订单提交页、支付页**和**购物车**，并深入探讨了**库存管理、拆单逻辑**等复杂的后端策略。 |
| **订单评价及售后**| 我们设计了购后体验的**评价体系**和基于订单状态的**售后流程**，以建立用户信任。 |
| **商品种草**| 我们设计了产品的差异化模块——**社区**，通过**信息流**和**发布**功能，打通内容与电商。 |
| **个人中心**| 我们为用户设计了一个清晰、有序的“家”，聚合了**用户资产、快捷入口**和**全局设置**。 |

通过这一章的实战，我们已经将电商用户端的理论，全部转化为了具体、可视的产品设计方案。我们已经拥有了一份足以交付给UI和开发团队的、完整的“建筑蓝图”。






---
# 第四章：电商后台产品设计

欢迎来到第四章。在上一章，我们已经为用户，设计出了一套完整、华丽的“**前台**”购物体验。但支撑这一切前台体验能够顺利运转的，是一个我们普通用户永远看不到的、强大而复杂的“**后台**”系统。

后台，就是我们电商平台的“**中央厨房**”与“**指挥中心**”。本章，我们就将学习如何设计这个核心系统。

## 4.1 学习目标

在本节中，我的核心目标是，带大家建立起对电商后台的**宏观架构认知**。我们将学习后台的核心作用，并重点理解，在招商模式下，如何清晰地划分“**商家业务**”与“**平台业务**”，并最终绘制出我们整个电商生态的“**业务模块架构图**”。

## 4.2 电商后台核心作用及架构

### 4.2.1 后台对前台业务的支撑作用

![image-20250722161730158](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722161730158.png)

我理解的电商后台，其最核心、最根本的作用，就是**为前台的所有业务，提供支撑**。
* 前台用户能看到的每一个**商品**，都是由后台的“**商品中心**”来管理的。
* 用户下的每一笔**订单**，都是由后台的“**订单中心**”和“**支付中心**”来处理的。
* 订单能被顺利**配送**，则依赖于后台的“**物流中心**”和“**WMS（仓储管理系统）**”。

前台是光鲜亮丽的“餐厅”，而后台，就是保证餐厅能正常运转的“厨房”、“仓库”和“收银系统”的总和。

### 4.2.2 商家业务 vs 平台运营业务划分

![image-20250722161835579](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722161835579.png)

对于我们“大P超级电商”这样的**招商模式**平台，我需要特别澄清一点：我们的“后台”，其实并不是一个单一的系统，而是**两个既相互独立、又紧密关联的系统**：

**1. 商家后台**
这是我设计给**入驻商家**使用的后台。它是商家在我们平台上“开店经营”的专属工作台。其核心的业务方向，是服务于商家的日常经营，包括：

* `入驻平台/管理店铺`
* `发布/管理商品`
* `订单跟进`
* `售后处理`

**2. 平台运营后台**
这是我设计给我们**自己公司内部员工**（运营、审核、客服等）使用的后台。它是我们管理整个平台的“上帝视角”控制台。其核心的业务方向，是**管理和监督**，包括：

* `审核商家入驻`
* `审核商家发布的商品`
* `订单跟进（仲裁）`
* `管理平台所有的商家和用户`

清晰地划分这两套后台的业务边界，是我进行后台架构设计的第一步。

### 4.2.3 整体业务模块架构

![image-20250722162014848](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722162014848.png)

最后，我会将我们整个电商生态的所有功能模块，汇总成一张“**整体业务模块架构图**”。这张图，就是我们整个项目的“总蓝图”。

如案例图所示，这张架构图，清晰地划分出了我们项目的三大组成部分：

* **用户端**：即我们在第三章设计的，面向C端消费者的前台App。
* **商家端**：即我们即将设计的，面向B端商家的后台管理系统。
* **平台端**：即我们即将设计的，面向我们自己内部员工的后台管理系统。

在每一个“端”的下面，我会罗列出它所包含的核心功能模块。比如，`平台端`就包含了`商品管理`、`订单管理`、`系统管理`、`店铺管理`等模块。

![image-20250722162110994](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722162110994.png)

这张架构图，不仅能让所有项目成员，对我们产品的全貌一目了然，更能清晰地揭示出，前台的每一个核心业务，是由后台的哪一个模块来提供支撑的。它是我们后续进行详细设计和技术架构设计的“最高纲领”。




---
## 4.3 平台运营后台核心设计

现在，我们开始设计我们自己公司内部员工使用的“**上帝视角**”后台——**平台运营后台**。

这个系统的设计，我最看重的三个原则是：**安全、高效、可追溯**。因为在这里的每一个操作，都可能会对我们平台上的用户、商家，乃至整个平台的声誉，产生直接的影响。

本节，我们重点设计其中两个最高频、也最重要的模块：**C端用户管理**和**商家管理**。

### 4.3.1 C端用户管理（用户状态与权限操作）

这个模块，赋予了我们的运营和客服同事，管理平台上所有C端消费者（买家）的权力。

#### 1. 用户列表与查询

![image-20250722170937934](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722170937934.png)

正如案例图所示，这个界面的核心，是一个完整的用户列表。为了让我的运营同事，能从数以万计的用户中，精准地找到目标，我必须为他们设计一套强大的查询和筛选功能。

| 筛选字段 | 我的设计说明 |
| :--- | :--- |
| **用户信息** | 提供一个统一的输入框，支持按`用户昵称`、`手机号`、甚至`用户ID`进行模糊或精确搜索。 |
| **注册时间** | 提供一个日期范围选择器，方便运营分析特定时期内（如：某次活动期间）的新增用户。 |
| **用户状态**| **这是最重要的筛选条件**。提供一个下拉菜单，让客服或风控团队能快速筛选出所有`正常`、`已禁用`的用户，进行集中的管理。 |

#### 2. 用户状态与权限操作

“**操作**”列，是这个模块“权力”的体现。一个严谨的设计，是**根据用户当前的状态，来提供不同的操作权限**。

* **当用户状态为“正常”时**：
    我提供的核心操作是 `查看`、`编辑`、`禁用`。
    * **我的拓展设计**：点击“**禁用**”时，我不会让它立刻生效。我会设计一个弹窗，要求操作员必须**选择一个禁用的时长**（如：7天、30天、永久）并**填写禁用的原因**。这个原因，一部分会展示给用户，另一部分则会作为内部日志，记录在案，以备查验。

* **当用户状态为“禁用”时**：
    “禁用”按钮，则会变为“**解禁**”按钮。操作员点击后，二次确认，即可将该用户恢复为正常状态。

这种“**基于状态的权限设计**”，能极大地避免运营人员的误操作，让后台管理更规范、更安全。

### 4.3.2 商家入驻审核与店铺管理

管理商家（卖家），比管理用户要复杂得多，因为它直接关系到我们平台的商品供给、服务质量和核心收入。

#### 1. 商家入驻审核

![image-20250722171315184](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171315184.png)

这是我们平台的“**第一道门卫**”。所有想在我们平台开店的商家，都必须经过这一环节的资质审核。我会把它设计成一个独立的工作台。
* **待审核列表**：页面的默认视图，是一个“**待审核**”队列，展示了所有提交了入驻申请，但尚未被处理的商家。
* **审核详情页**：运营点击“审核”后，会进入一个详情页，能看到该商家提交的所有信息，包括`营业执照`、`法人信息`、`品牌授权书`等。在这个页面，只有两个核心的操作按钮：“**通过**”和“**驳回**”。
* **我的拓展设计**：点击“**驳回**”时，我同样会要求运营，必须从一个预设的“驳回原因列表”（如：资质不全、信息不符）中选择一项，或手动输入驳回原因。这个原因，将通过短信或站内信，清晰地告知申请者，提升平台的专业度。

#### 2. 店铺管理

![image-20250722171327591](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171327591.png)

当商家审核通过，拥有了自己的店铺后，他们就会进入到“**店铺管理**”列表中。
* **核心信息**：这个列表，除了展示商家的基本信息外，更需要展示`店铺名称`、`主营类目`、`入驻时间`、`店铺状态`等运营信息。
* **核心操作**：针对“店铺”这个主体，我设计的操作权限，会比针对“用户”的权限，更谨慎、层级更高。
    * `查看店铺`：可以一键跳转到该店铺的前台页面。
    * `冻结店铺`：当商家出现较严重的违规行为（如售假）时，运营可以暂时“**冻结**”其店铺。冻结期间，店铺所有商品都会被下架，商家也无法登录后台。
    * `关闭店铺`：这是最严厉的处罚。意味着与该商家终止合作，将其清退出平台。这个操作，我通常会设计为，需要“**运营主管**”及以上级别的角色，才能执行。



---
## 4.4 商家后台核心设计

在上一节，我们设计了我们自己内部团队使用的“平台运营后台”。现在，我们要来设计一个同样重要，但使用者完全不同的系统——**商家后台**。

这是我们为所有入驻我们“大P超级电商”平台的第三方商家，提供的专属“**线上店铺办公室**”。这个后台的体验好坏，直接决定了我们能否吸引和留住优质的商家，是保障我们平台商品丰富度和供给侧质量的生命线。

### 4.4.1 商家入驻流程与类型（京东、淘宝/天猫、自营/非自营）

在我设计商家后台的第一个功能“**商家入驻**”时，我首先要思考一个战略问题：**我们希望吸引什么样的商家？**
* 像**淘宝**一样，追求“让天下没有难做的生意”，允许个人和企业“**免费开店**”，降低门槛，最大化地丰富商品供给？
* 还是像**天猫**一样，只面向具备一定**品质和资质的企业**，抬高门槛，保障平台的品牌调性？

![image-20250722171019089](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171019089.png)

这个决策，决定了我们入驻流程的复杂度和审核的严格度。对于我们 V1.0 的招商模式，我们将主要设计一个**非自营（POP）商家的入驻流程**。

![image-20250722171035591](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171035591.png)

一个专业的商家入驻流程，远比用户注册要复杂。我设计的流程，通常包含以下几个核心阶段：
1.  **入驻前准备**：在申请入口，我会清晰地告知商家，需要提前准备好哪些资质文件。
2.  **在线申请**：引导商家填写一系列的申请信息。
3.  **平台审核**：商家提交后，申请单会进入我们上一节设计的“平台运营后台-商家审核”模块，由我们的运营同事进行审核。
4.  **开店任务**：审核通过后，我会引导商家完成一系列的开店准备工作，如**缴纳费用、签署在线合同**等。

![image-20250722171136628](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171136628.png)

在“在线申请”这个环节，我设计的表单，会清晰地区分“**个人类型**”和“**企业类型**”的商家，因为他们需要提交的`个人信息`和`公司信息`（如营业执照）是完全不同的。

![image-20250722171206667](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171206667.png)

在“开店任务”环节，我设计的流程，必须清晰地向商家展示需要缴纳的费用。这通常包括：
* **平台使用费/年费**：按年收取的技术服务费。
* **保证金**：一笔押金，用于在商家出现违规行为、损害消费者利益时，对消费者进行赔付。

### 4.4.2 商家子账号设置与权限划分

![image-20250722171544019](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171544019.png)

当一个商家的店铺成功开张，生意越做越大，一个新需求就出现了：**店主（主账号）一个人忙不过来了**，他需要让手下的员工（如：客服、运营、库管）来一起管理店铺。但是，他又不能把最高权限的“主账号”密码，告诉所有人。

为了解决这个痛点，我必须为商家后台，设计一个“**子账号**”管理功能。这本质上，是为每一个商家，提供了一套**迷你的、店铺内部的RBAC（基于角色的访问控制）系统**。

我的设计，主要包含两个核心功能：

![image-20250722171852869](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171852869.png)

1.  **角色管理**：我会允许“**主账号**”，在后台**创建不同的角色**（如：“客服专员”、“运营专员”），并为这些角色，**勾选分配不同的操作权限**（如：“客服专员”只能查看订单和回复咨询，但无权修改商品价格）。
2.  **员工管理**：主账号可以在这里，为他的每一位员工，**创建一个子账号**，并为这个子账号，**指定一个我们上面创建好的角色**。

这个功能，是区分一个“玩具级”和一个“企业级”商家后台的重要标志。

## 4.5 本章总结

在本章，我们深入到了电商“冰山”的水下部分，系统地学习了后台的设计。
* **核心作用与架构**：我们明确了后台是为前台服务的“指挥中心”，并学会了如何划分**商家业务**和**平台业务**，搭建起**用户端、商家端、平台端**三位一体的宏观产品架构。
* **平台运营后台**：我们设计了平台自己使用的核心模块，包括如何进行**C端用户管理**（特别是状态控制），以及如何建立一套严谨的**商家入驻审核与店铺管理**流程。
* **商家后台**：我们设计了服务于商家的核心模块，包括如何根据不同平台定位，设计差异化的**商家入驻流程**，以及如何通过**子账号**功能，来满足商家的团队协作与权限管理需求。

---

# 第五章：电商后台 - 商品管理

在上一章，我们已经设计好了商家入驻的流程，让第一批商家成功进入了我们的平台。现在，他们最迫切的需求就是：**我应该如何，把我的商品，发布到平台上进行售卖？**

本章，我们就将为商家，以及我们自己平台的运营，设计一套完整、专业、可扩展的商品管理系统。

## 5.1 学习目标

![image-20250722174132300](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722174132300.png)

在本章中，我的核心目标是，带大家掌握电商后台商品管理模块的完整设计。我们将首先从宏观上，**推导出商品从“录入”到“呈现”的核心业务流程**，然后再深入到微观，学习**SPU/SKU、类目、属性**等构建商品体系的“原子”概念，并最终将它们组合成一个完整的“**商品发布功能**”。

---
## 5.2 商品发布流程推导

在我动手设计“商品发布”这个后台功能之前，我一定会先将支撑这个功能的**端到端业务流程**，梳理得一清二楚。

### 1. 从“最小商品模型”开始思考

我的思考，会先从“终局”出发，即，从一个**普通用户**的视角来看：**要让我能看懂一件商品，至少需要呈现哪些信息？**

![image-20250722180034500](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180034500.png)

这个问题的答案，构成了我们电商系统的“**最小商品模型**”。它至少需要包含以下五个核心信息：
1.  **标题**：商品叫什么名字。
2.  **图片**：商品长什么样。
3.  **价格**：商品卖多少钱。
4.  **库存**：商品还有没有货。
5.  **描述**：商品的详细介绍。

![image-20250722180117296](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180117296.png)

### 2. 用“特殊场景”挑战简单模型

那么，基于这个最小模型，一个最简单的设计思路就是：我只需要给商家提供一个包含这五个字段的表单，让他填完提交，不就可以了吗？

**这样够吗？**
当我把这个简单的模型，放入真实的、复杂的电商场景中去检验时，会立刻发现它的不足。

![image-20250722180223346](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180223346.png)

我必须考虑到以下这些“**特殊场景**”：
* **分类场景**：任何一件商品，都需要被归属到一个明确的“**商品分类**”下（如：电脑/办公 -> 电脑组件 -> 硬盘），这样用户才能通过分类导航找到它。

![image-20250722180312976](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180312976.png)

* **品牌场景**：用户也常常会通过“**品牌**”的维度来找商品（如：联想品牌馆）。因此，商品也需要和品牌进行关联。



![image-20250722180255028](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180255028.png)

* **属性场景**：不同品类的商品，其需要展示的“**商品参数**”是完全不同的。比如，硬盘需要展示`容量`、`接口`等参数；而一件衣服，则需要展示`材质`、`适用季节`等参数。一个固定的、简单的表单，是无法满足这种多样性的。

### 3. 推导出核心发布流程

![image-20250722180337574](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180337574.png)

为了解决上述所有特殊场景带来的问题，我推导出的、一个专业的“**商家发布商品核心流程**”，必须是一个**结构化的、分步骤**的流程：



![image-20250722180649979](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180649979.png)

1.  **第一步：选择商品类目**
    这是商家发布商品的**起点**。我会让商家，先从我们后台预设的“商品类目”树中，精准地选择他将要发布的商品，属于哪一个最细分的叶子类目。
    * **我的设计思考**：这一步至关重要，因为**用户选择的类目，将直接决定了下一步他需要填写的“商品属性”**。

2.  **第二步：选择商品品牌**
    在确定了类目后，商家需要选择该商品所属的“品牌”。

3.  **第三步：设置商品信息**
    只有在完成了前两步之后，系统才会展示出最终的“商品信息设置”页面。这个页面，除了包含我们前面提到的“最小商品模型”（标题、价格、图片、库存、描述）的填写区域外，还会根据第一步选择的类目，**动态地**加载出该类目专属的“**商品属性**”填写项。

### 4. 流程总结

至此，我们就完成了一次完整的流程推导。这个“**先选类目 -> 再选品牌 -> 最后填写信息**”的三步走流程，就是我为商家设计的、既能满足复杂场景，又能保证后台数据结构化、规范化的核心解决方案。

这个流程，完美地嵌入到了我们之前梳理的“**商家录入 -> 平台审核 -> 用户浏览**”的宏观业务流程中，构成了其中“**商家录入**”这一环节的具体实现。



---
## 5.3 商品类目

在我推导出的“**先选类目 -> 再选品牌 -> 最后填写信息**”的商品发布流程中，“**选择商品类目**”是所有流程的起点。

因此，**商品类目**体系，是我在设计商品管理后台时，第一个要搭建的、也是最底层的“**地基**”。一个清晰、稳定、可扩展的类目体系，是整个电商平台有序运转的保障。

### 1. 商品类目的核心作用

![image-20250722180926020](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180926020.png)

我设计商品类目体系，是因为它同时为我们生态中的三大核心角色，都提供了不可或缺的价值。

| 角色 | 核心作用 | 我的解读 |
| :--- | :--- | :--- |
| **平台** | **确定服务范围，进行监管** | 我后台设置的类目，直接定义了“**我们平台允许卖什么，不允许卖什么**”。这是我进行平台治理、控制风险、明确业务边界的根本。 |
| **商家**| **分门别类，便于管理** | 我为商家提供了一套标准化的“**商品货架**”。商家只需要按照我的类目规范，将商品“上架”到对应的位置，就能实现规范化的管理。 |
| **用户**| **方便查找商品** | 我为用户提供了一套清晰的“**商场导览图**”。用户可以通过分类导航，快速地找到自己想要逛的“区域”，极大地提升了购物效率。 |

### 2. 从角色需求到产品功能

![image-20250722181002478](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181002478.png)

基于上述的作用，我可以清晰地提炼出不同角色的产品需求，并推导出我们需要提供的核心功能。

这个推导的结论非常清晰，也是我设计的核心原则：“**平台来管理类目，商家使用类目**”。
* **平台的需求**是“限定范围、监管”，这要求我必须设计一套强大的“**后台类目管理**”功能。
* **商家的需求**是“加载类目模板、方便操作”，这要求我必须在“**商品发布**”流程中，提供一个易用的“**类目选择**”功能。

### 3. 平台端：类目管理功能设计

![image-20250722181719285](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181719285.png)

这是我为平台运营人员设计的“**类目配置中心**”。
* **多级类目结构**：我设计的后台，必须支持**多级类目**的创建和管理。如案例图所示，一个“笔记本”商品，它的类目层级可能是“数码家电（一级）” -> “家电（二级）” -> “笔记本（三级）”。
* **基础管理操作**：后台必须提供对每一级类目的**新增、编辑、删除、查询**等基础操作。
* **我的拓展设计（属性与品牌关联）**：这是后台类目管理最核心的、也是最高阶的功能。在运营人员新增或编辑一个“叶子类目”（即，不能再往下分的最后一级类目，如“笔记本”）时，我设计的后台，**必须允许他，将这个类目，与一组特定的“商品属性”和“品牌”进行关联**。
    * 例如，在配置“笔记本”这个类目时，运营就要为它关联上“屏幕尺寸”、“内存”、“CPU型号”等**属性**，并关联上“联想”、“华为”、“苹果”等**品牌**。

### 4. 商家端与用户端：类目的使用

我们平台运营在后台辛辛苦苦搭建好的这套类目体系，最终会在商家端和用户端，被“使用”和“呈现”。

![image-20250722181801927](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181801927.png)

* **在商家端**：当商家发布新商品时，我们流程的**第一步**，就是让他从我们后台预设好的类目树中，选择一个。当他选择了“笔记本”之后，系统就会因为我们后台的“关联”配置，而**动态地**为他加载出需要填写的“屏幕尺寸”、“内存”等属性。

![image-20250722181837353](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181837353.png)

* **在用户端**：用户则会在我们App的“**分类**”Tab页，看到我们后台配置好的类目树结构，并可以逐级点击，进行商品的浏览和筛选。

一个后台设计得清晰、合理的类目体系，是前台商家发布体验流畅、用户浏览体验清晰的根本保障。






---
## 5.4 品牌管理

在我看来，如果说“类目”是商品的**物理属性**分类，那么“**品牌**”就是商品的**心智属性**分类。

用户在购买决策时，品牌是影响他们信任和选择的、极其重要的一个因素。

因此，我必须在后台，建立一套完善的、由平台强管控的品牌管理体系。

### 1. 品牌管理的核心价值

![image-20250722213811999](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213811999.png)

我设计品牌管理系统，同样是为了服务好我们生态中的三大核心角色。

| **角色** | **核心需求** | **我需要提供的产品能力** |
| :--- | :--- | :--- |
| **平台** | **规避假冒伪劣、山寨产品**，保证平台的商品品质和声誉。 | 必须建立一套**品牌的审核与认证机制**，确保只有合规的、真实的品牌才能在平台上被售卖。 |
| **商家** | 能够清晰地标明自己所售卖商品的**品牌归属**。 | 我需要在商品发布流程中，为商家提供一个清晰、准确的**品牌选择器**。 |
| **用户** | **“我只想看某个品牌的商品”**，能够通过品牌维度，快速找到自己想要的商品。| 我需要在用户端，提供**按品牌进行搜索和筛选**的功能。 |

![image-20250722213850767](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213850767.png)

基于上述的需求，我推导出的核心功能是：
* **平台端**：必须有后台**品牌管理**（增删改查）功能。
* **商家端**：必须在商品发布时，有**品牌选择**功能。
* **用户端**：必须有**品牌搜索/筛选**功能。

### 2. 平台端：品牌库管理功能设计

![image-20250722213944716](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213944716.png)

所有品牌功能的核心，在于我们平台运营后台，必须建立一个“**品牌库（Brand Library）**”。这个品牌库，是由我们**平台统一进行维护**的，它是我们平台上所有“合法品牌”的唯一真实来源。

我设计的品牌库后台，主要包含以下功能：
* **品牌信息字段**：在新增一个品牌时，运营需要填写该品牌的`Logo`、`中文名`、`英文名`、`简介`等信息。
* **基础管理功能**：运营可以对品牌库中的品牌，进行常规的**新增、编辑、查询**操作。
* **状态管理**：每个品牌，都有“**启用/停用**”两种状态。当某个品牌出现问题（如：被曝出重大质量问题、品牌方与我们合作终止）时，运营可以将其状态，设置为“停用”。设置为“停用”后，商家在发布商品时，就无法再选择这个品牌了。

### 3. 特殊流程：自主品牌入驻

这时，一个非常常见的业务场景就出现了：**如果一个商家，想售卖一个我们品牌库里，还没有收录的新品牌，怎么办？**

我不能让商家随意地、手动地填写品牌名称，这会导致品牌库数据混乱，出现大量山寨和无效品牌。因此，我必须设计一套严谨的“**新品牌入驻审核**”流程。

![image-20250722214029207](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214029207.png)

我的设计流程如下：
1.  **商家提交申请**：在商家后台，我会提供一个“**新增品牌申请**”的入口。商家需要在这个页面，填写新品牌的基础信息，并**必须上传该品牌的《商标注册证》**作为资质证明。
2.  **平台审核**：商家的申请，会进入我们平台运营后台的“品牌审核”列表中。运营同事的核心工作，是**核实《商标注册证》的真实性**和有效性。
3.  **品牌入库**：审核通过后，运营同事，会将这个新品牌的信息，正式录入到我们的“品牌库”中，并设置为“启用”状态。
4.  **商家选用**：一旦品牌成功入库，商家（以及其他所有获得了该品牌授权的商家），就可以在发布商品时，从品牌选择器中，选择这个新的品牌了。

通过这套流程，我既满足了商家引入新品牌的需求，又确保了平台对所有品牌的“强管控”，保证了我们电商品牌生态的健康。

---
## 5.5 SKU与SPU

在我设计任何电商后台的商品系统时，我的第一个思考，就是要清晰地定义**SPU**和**SKU**。这两个概念，是整个商品世界的“**基本粒子**”，理解了它们，就理解了所有复杂商品体系的构成。

### 1. 核心定义

![image-20250722214844754](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214844754.png)

* **SPU - 标准产品单元 (Standard Product Unit)**
    我把它理解为“**一款商品**”。它是一组具有共同的、标准化的核心属性的商品的集合，是商品信息聚合的最小单位。
    * **例如**：“iPhone 11”就是一个SPU。它代表了“iPhone 11”这个产品系列，与它的颜色、内存大小无关。我们通常用SPU，来做商品的通用性描述、展示和搜索。

* **SKU - 库存量单位 (Stock Keeping Unit)**
    我把它理解为“**一件货品**”。它是库存控制的最小可用单位，是真正物理存在的、可以被用户购买的最小单元。
    * **例如**：“一台白色的、内存为64G的iPhone 11”，就是一个SKU。“一台红色的、内存为128G的iPhone 11”，则是另一个完全不同的SKU。**每一个SKU，都有自己独立的库存和价格**。

### 2. SPU与SKU的关系

![image-20250722214946907](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214946907.png)

SPU和SKU之间，是一个“**一对多**”的层级关系。一个SPU，通过不同的“**销售属性**”的组合，可以衍生出多个不同的SKU。

**销售属性**，就是那些能影响到商品最终售价和库存的属性，比如`颜色`、`尺码`、`内存大小`、`套餐类型`等。

所以，它们之间的关系公式是：
> **SKU = SPU + 一组确定的销售属性**

![image-20250722215055577](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215055577.png)

我们在京东看到的这个iPhone 11商品详情页，就是一个完美的现实案例。
* **SPU**：是“Apple iPhone 11”这个商品本身。
* **销售属性**：是“选择颜色”和“选择版本”这两个维度。
* **SKU**：当用户选择了“黑色”和“128GB”之后，页面上展示的特定价格`¥5269.00`和库存状态，就是属于这个唯一SKU的。

### 3. 技术实现浅谈

那么，为了实现这套逻辑，我作为产品经理，需要如何与我的研发同学沟通呢？我需要向他们讲清楚**后端的数据模型**和**前端的交互逻辑**。

* **后端设计（数据模型）**
    在后台数据库中，我们至少需要设计几张表，来清晰地表达SPU和SKU的关系。

| **数据表** | **我的设计说明** |
| :--- | :--- |
| **SPU表 (spu_table)** | 这张表，用来存放“iPhone 11”这个SPU的通用信息，比如`商品名称`、`商品描述`、`品牌`、`类目`等。 |
| **销售属性名表 (attribute_name_table)** | 这张表，用来定义SPU有哪些销售属性。比如，它会记录：“iPhone 11”这个SPU，有“颜色”和“内存”这两个销售属性。 |
| **销售属性值表 (attribute_value_table)**| 这张表，用来定义每个销售属性有哪些可选值。比如，它会记录：“颜色”这个属性，有“黑色”、“白色”、“红色”等可选值。 |
| **SKU表 (sku_table)** | **这是最核心的表**。它用来存放每一个具体的“货品”。比如，“iPhone 11 黑色 128GB”就是这张表里的一行记录。这行记录里，会包含它**自己专属的`价格`、`库存`**，并会关联到它的父级SPU，以及它所对应的属性值（“黑色”、“128GB”）。 |

* **前端设计（交互逻辑）**
    当用户打开一个商品详情页时，前端与后端的交互流程是这样的：
    1.  前端App向后端服务器，发送一个请求，告诉它：“我需要SPU ID为‘iPhone 11’的商品数据”。
    2.  后端服务器收到请求后，会把**SPU表**里的通用信息（描述、主图等），以及**与这个SPU关联的所有SKU表里的记录**（比如：黑色64G的价格/库存、白色64G的价格/库存、黑色128G的价格/库存……），一次性地，全部返回给前端App。
    3.  前端App拿到这些数据后，就会在页面上，**动态地渲染**出“颜色”和“内存”这两个维度的、所有可点击的**选择按钮**。
    4.  当用户点击“黑色”和“128GB”这两个按钮时，**前端App会直接在本地已经拿到的数据中，查找到对应的那个SKU**，然后**瞬间**将页面上的价格和库存，更新为这个SKU专属的价格和库存。这个过程，通常**不需要再次请求后端服务器**，因此用户会感觉体验非常流畅。

---
## 5.6 商品属性

在上一节，我们学习了SPU和SKU。我们知道，一个“黑色、128G的iPhone 13”是一个SKU。但这立刻引出了一个核心问题：系统是怎么知道“iPhone 13”会有“颜色”和“内存”这两个选项的？商家在发布商品时，并不是随意填写这些信息的。

这背后的答案，就是我们这一节要学习的，一套由平台预先定义好的、结构化的数据体系——**商品属性**。

### 1. 属性的存在方式：属性名 + 属性值

![image-20250722215725696](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215725696.png)

首先，我们要明确一个“属性”最基本的构成。任何一个属性，都是由一个“**属性名**”和一个“**属性值**”配对组成的。
* **属性名**：相当于“问题”，比如：`型号`、`颜色`、`传输速度`。
* **属性值**：相当于“答案”，比如：`CR111`、`黑色`、`1000Mbps`。

我设计的整个商品信息体系，就是由成千上万个这样的“**键值对**”构成的。

### 2. 属性的分类

![image-20250722215842159](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215842159.png)

为了让这成千上万的属性，能够被系统有序地管理和使用，我必须对它们进行**分类**。在我的电商产品设计中，我会严格地将所有属性，划分为以下三种类型：


| 属性分类 | 简单来说 | 案例（以“iPhone 13”为例） |
| :--- | :--- | :--- |
| **关键属性** | 就像商品的“身份证”，**确定是哪一款商品**，不会变，也不能选。 | “iPhone 13”这个名字本身，以及它的**具体型号**（比如A2634）。 |
| **销售属性** | 决定你**最终买哪个具体商品**，你**必须选**，选了之后**价格或库存可能就不同**。 | **颜色**（星光色、午夜色等）；**存储空间**（128GB、256GB等）。 |
| **其他属性** | 就是商品的**各种特点介绍**，**看看就行，不能选**，也不影响价格。 | 处理器型号 (A15)、屏幕是OLED、防水级别是IP68等等。 |


![image-20250722220341997](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220341997.png)

我们可以通过这张网卡的案例图，清晰地看到这三类属性，在真实商品详情页上的分布。

### 3. 属性池：平台端的统一管理

下一个关键问题是：这么多属性名（如`颜色`、`尺寸`、`CPU型号`），它们是从哪里来的？

为了保证数据的规范和统一（比如，避免商家A填写“颜色”，商家B填写“色彩”）

我必须在平台运营后台，建立一个**由平台统一管理**的`属性池`。

![image-20250722220452045](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220452045.png)

“属性池”，是我为平台运营人员设计的、一个用来**集中管理所有“属性名”**的后台功能。在这里，运营同事可以对平台中可能出现的所有属性名，进行**增、删、改、查**。

* **我的拓展设计（属性与类目的关联）**
    这个“属性池”并不是孤立存在的。它设计的精髓，在于和我们`5.3`节学习的“**商品类目**”进行**深度绑定**。
    
    在我设计的“**类目管理**”后台，当运营人员在编辑“笔记本电脑”这个类目时，他就可以从“属性池”中，为这个类目，勾选上它应该具备的属性，比如`CPU型号`、`内存容量`、`屏幕尺寸`、`硬盘容量`等。
    
    这样一来，当商家在发布商品、第一步选择了“笔记本电脑”这个类目后，系统就会自动地、智能地，为他加载出需要填写的`CPU型号`、`内存容量`等属性，从而实现了整个商品发布流程的结构化和智能化。




---
## 5.7 商品发布功能设计

![image-20250722220910285](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220910285.png)

在前面的小节中，我们已经将“商品”这个复杂的概念，拆解为了`类目`、`品牌`、`SPU`、`SKU`、`属性`等一系列结构化的“原子”部件。

现在，我们的任务，就是**将这些“原子”部件，重新组合起来**，为我们的商家，设计一个功能强大、体验流畅的“**商品发布**”功能。这个功能，就是商家后台的“**核心生产力工具**”。

### 1. 页面信息结构

![image-20250722220935810](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220935810.png)

在我设计这个复杂的“发布商品”页面时，我首先会对需要商家填写的**信息**，进行一次高层级的**结构化分类**。我会把整个页面，划分为三大信息模块：

1.  **商品的基本信息**：即SPU层级的通用信息，如商品名称、图片等。
2.  **商品的属性**：包括决定SKU的销售属性，以及其他描述性属性。
3.  **商品的详情**：即图文并茂的、用于营销的“长图文”描述。

### 2. 功能模块详细设计

现在，我们来逐一设计承载这三类信息的功能模块。

#### **模块一：填写商品基本信息**

![image-20250722221017448](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221017448.png)

这是商家进入发布流程后，看到的第一个表单区域。它用来采集这件商品（SPU）最基础的信息。

| **字段** | **我的设计说明** |
| :--- | :--- |
| **商品分类** | 这个字段通常是**只读**的，它会显示商家在上一个步骤（`5.2`节推导的流程）中所选择的类目。 |
| **商品名称** | 文本输入框，用于填写SPU的标题。 |
| **商品品牌** | 一个**下拉选择框**。我设计的逻辑是：这个下拉框里，只会出现我们后台**与该“商品分类”相关联**的品牌，而不是全部的品牌。这是一种智能化的设计。 |
| **商品价格** | 商家可以填写商品的“市场价”或“划线价”。每个SKU的具体售价，会在下一步设置。 |
| **商品展示图** | 一个图片上传控件。我会明确地标注出，**最多可以上传几张**，以及推荐的**尺寸和格式**，以保证前端展示效果的统一。 |

#### **模块二：设置销售属性与SKU**

![image-20250722221242154](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221242154.png)

这是整个商品发布功能中，**技术和交互最复杂，但也最核心**的一个模块。我把它分为两步：

**第一步：定义销售属性**
我会提供一个交互区域，让商家可以为他的商品，添加“**销售属性**”。
* **属性名**：通过一个下拉框，让商家从该类目下，我们预设好的属性（如`颜色`、`尺寸`）中进行选择。
* **属性值**：在选定了属性名后，商家可以手动地，添加多个属性值（如`红色`、`蓝色`；`S`、`M`、`L`）。

**第二步：生成SKU并填写明细**
当商家定义好所有的销售属性和属性值后，我设计的后台，最智能的地方就体现出来了：**系统会自动地，将这些属性值进行“笛卡尔积”组合，生成一个完整的SKU列表**。

商家的工作，不是去手动组合SKU，而是在这个自动生成的表格里，“**做填空题**”。他只需要为每一个SKU，填写它专属的`销售价格`和`销售库存`即可。这个设计，极大地降低了商家的操作复杂度和出错率。

#### **模块三：编辑商品描述**

![image-20250722221359012](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221359012.png)

这是页面的最后一个模块，用于上传商品的“长图文”详情。

* **我的拓展设计（分端描述）**：为了追求极致的用户体验，一个专业的电商后台，应该允许商家，为**PC端**和**移动端**，分别上传和编辑**两套不同**的商品描述。
* **为什么？** 因为PC端屏幕大，可以展示更丰富、更复杂的图文内容；而移动端屏幕小，则需要更简洁、加载速度更快的图片和文字。提供两个独立的“**富文本编辑器**”，能让有能力的商家，为不同设备的用户，提供最优的浏览体验。

通过将这三大模块，有机地组合在一个页面中，我们就为商家，提供了一个功能强大、逻辑清晰、体验智能的商品发布功能。


---
## 5.8 类目关联的相关场景

在前面的小节中，我们已经独立地设计了`商品类目`、`品牌`和`商品属性`这三个核心的数据模块。但如果它们只是三个孤立的列表，那我们的后台依然是“**笨拙**”的。

一个智能的后台，必须能理解这三者之间的**内在关联**。本节，我们就来设计这套“**关联系统**”。

### 1. 类目与属性的关联

我们首先思考一个场景：我们的“**属性池**”里，包含了`颜色`、`尺码`，也包含了`CPU型号`、`屏幕尺寸`。当一个商家来发布一件“T恤”时，如果我们在“设置属性”的环节，把所有这些属性都展示给他，那将是一场灾难。

![image-20250722222404342](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222404342.png)



**我的解决方案**：我必须为商品属性，打上“**类目**”的烙印，即创建“**类目属性**”。

![image-20250722222453447](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222453447.png)

**我的设计思路如下：**

![image-20250722222550441](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222550441.png)

1.  **在平台端（后台）进行关联**：
    在我设计的“**类目管理**”后台，当运营同事在编辑一个“叶子类目”（如：“T恤”）时，我必须提供一个功能，让他可以从“属性池”中，**勾选**出所有与“T恤”相关的属性（如：`颜色`、`尺码`、`材质`、`适用季节`），并将它们**与“T恤”这个类目进行绑定**。

2.  **在商家端（后台）智能调用**：
    经过了后台的“绑定”操作后，商家在发布商品时，当他在第一步选择了“T恤”这个类目，那么在后续的“设置商品属性”环节，系统就会**只加载并显示**出`颜色`、`尺码`、`材质`等这几个已经绑定好的属性，供他填写。

3.  **在用户端（前台）精准呈现**：
    这个设计，最终会惠及我们的用户。当用户在前台浏览“T恤”这个分类列表时，页面左侧的“**筛选器**”，也同样只会展示出`颜色`、`尺码`、`材质`等这些与T恤强相关的、有意义的筛选条件。

![image-20250722222826573](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222826573.png)

### 2. 类目与品牌的关联

同样的逻辑，也完全适用于**品牌管理**。当商家发布一件“NIKE”的T恤时，如果让他从一个包含“海尔”、“华为”等上千个品牌的总列表里去寻找，体验会非常糟糕。

![image-20250722222903391](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222903391.png)

**我的解决方案**：我同样需要在后台，建立**品牌与类目的关联**。这样做的好处是：
* 提升商家发布商品的**便捷性**，避免出错。
* 让我们的品牌管理更**标准化**。
* 让用户在前台按分类+品牌进行**筛选时，速度更快**。

**我的设计思路如下：**

![image-20250722222919119](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222919119.png)

1.  **在平台端（后台）进行关联**：
    在“**类目管理**”后台，当运营编辑“T恤”这个类目时，除了关联属性，他还需要**关联品牌**。他会从“品牌库”中，勾选出所有属于“服饰”类目的品牌（如：`NIKE`、`阿迪达斯`、`优衣库`）。

2.  **在商家端（后台）智能调用**：
    当商家发布商品，选择了“T恤”类目后，他在“选择品牌”的下拉菜单里，看到的，就将是一个被**智能筛选**过的、只包含“`NIKE`”、“`阿迪达斯`”等服饰品牌的短列表。

**总结**：
“**类目-属性-品牌**”的后台关联设计，是我认为的电商后台商品管理系统中，**最能体现设计功力**的一环。它是一个“**后台配置一小步，前台体验一大步**”的经典设计，能让我们的整个商品体系，变得井然有序、充满智慧。


---
## 5.9 属性管理特殊规则

在我们`5.6`节的设计中，我们确立了“**类目-属性关联**”的核心思想。但在面对一个拥有成千上万类目和属性的大型电商平台时，简单的关联会带来两个新的问题：**一是商家端填写体验杂乱，二是平台端配置效率低下**。

为了解决这两个问题，我必须在我的设计中，引入两个高级的特殊规则：**属性分组**和**属性继承**。

### 1. 属性分组 - 让信息更有序

* **遇到的问题**：
    一个“笔记本电脑”类目，可能会关联几十个属性。如果我在商家发布商品的页面，把这几十个属性输入框，从上到下平铺直叙地排列下来，整个页面会显得极其冗长和混乱，商家很难快速找到自己要填写的项目。

![image-20250722223727689](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223727689.png)

* **我的解决方案：**
    我会引入“**属性组**”的概念。正如我们看到的大部分商品详情页一样，属性信息天然就是可以被“**分组**”的（如：显示器参数、处理器、内存、硬盘等）。

* **我的后台设计：**
    ![image-20250722223657032](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223657032.png)

    1.  **第一步：创建属性组**。在平台运营后台，我会设计一个独立的“**属性组管理**”功能。在这里，运营同事可以创建不同的“属性组”（比如，创建一个名为“`CPU组`”的分组），然后从我们的“属性池”中，将相关的属性（如`CPU型号`、`CPU核心数`）添加进这个组里。
    2.  **第二步：类目关联属性组**。在“类目管理”后台，运营同事在为类目关联属性时，他关联的，就不再是一个个零散的属性，而是一个个已经打包好的“**属性组**”。

* **最终效果：**
    ![image-20250722223907299](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223907299.png)

    经过后台的这一番配置，商家在发布商品，选择了“笔记本电脑”类目后，他看到的属性填写区，就不再是混乱的长列表，而是像图中这样，被清晰地规整在“`处理器`”、“`内存`”、“`硬盘`”等区块之下，一目了然，填写体验大幅提升。

### 2. 属性继承 - 让配置更高效

![image-20250722223601055](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223601055.png)

* **遇到的问题**：
    我们后台的类目，是树状的多级结构。有一些属性，是非常通用的，比如“`商品毛重`”，它几乎适用于所有实物商品。如果按照我们现有的逻辑，我的运营同事，需要手动地，为成百上千个“叶子类目”，都去重复地关联一次“`商品毛重`”这个属性，这无疑是一场噩梦。

* **我的解决方案：**
    我会为我们的类目-属性关联系统，设计一个“**属性继承**”的规则。

![image-20250722224101627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224101627.png)

* **规则定义**：**任何一个子类目，都会自动地，继承其所有父级类目所关联的全部属性**。

* **我的设计应用**：
    ![image-20250722224138541](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224138541.png)

    有了这条继承规则，我的运营同事的工作，就变得极其高效了：
    1.  他只需要将“`商品毛重`”这个通用属性，关联到最顶级的“**一级分类**”（如：“数码”）上。
    2.  那么，所有属于“数码”下的“**二级分类**”（如：“摄影摄像”）和“**三级分类**”（如：“单反相机”），就都**自动地、无需任何操作地，拥有了**“`商品毛重`”这个属性。
    3.  最终，一个“单反相机”类目下的商品，它所需要填写的属性，就等于“**单反相机”自己关联的属性 + 它继承自“摄影摄像”的属性 + 它继承自“数码”的属性**的总和。

![image-20250722224321109](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224321109.png)

这个“**属性继承**”的设计，极大地减少了后台运营的重复配置工作量，并保证了整个商品体系属性的规范性和一致性。



---
## 5.10 运费模板

当用户在我们的电商平台下单时，除了商品价格，他最关心的另一个问题就是：**这件商品，寄到我这里，需要多少运费？**

这个问题的背后，对我们产品经理来说，则是另一个问题：**商家是如何，为成千上万、发往全国各地的商品，去设定如此复杂的运费规则的？**

答案，就是我们必须为商家，设计一套功能强大、体验灵活的“**运费模板**”系统。

### 1. 什么是运费模板？

我给**运费模板**的定义是：**一套由商家预先设置并保存好的、包含了复杂运费计算规则的“配置方案”**。

它的核心价值在于“**一次配置，多次复用**”。商家只需要根据自己的物流合作方和商品特性，创建好几套模板（比如：“大件商品-德邦模板”、“小件商品-顺丰模板”），就可以方便地，将这些模板，应用到成千上万的商品上。这种“**一对多**”的设计，能极大地提升商家的运营效率。

### 2. 运费模板功能设计

我设计运费模板功能，主要包含两个核心模块：**模板的创建与管理**，和**商品对模板的应用**。

#### **模块一：创建与管理运费模板**

![image-20250723095615173](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095615173.png)

在商家后台，我需要提供一个“运费管理”的模块，让商家可以“**新建运费模板**”。这个新建模板的表单，是我设计的核心，它必须包含以下配置项：

| **配置项** | **我的设计说明** |
| :--- | :--- |
| **模板名称** | 一个自定义的名称，方便商家自己识别。比如：“顺丰-江浙沪包邮”。 |
| **是否包邮**| 一个简单的单选：**卖家承担运费（即包邮）**或**买家承担运费**。如果选择“包邮”，则下方的复杂规则可以被简化。 |
| **计费规则**| 这是运费计算的基础。通常分为三种：**按件数**、**按重量**、**按体积**。 |
| **默认运费规则**| **这是必填项**。用于设置一个“通用”的运费规则，它适用于所有未被“指定地区”规则覆盖的地区。这能有效避免因漏设地区而导致无法下单的问题。 |
| **指定地区运费规则**| **这是最核心、最灵活的功能**。我需要提供一个入口，让商家可以“**为指定城市设置运费**”。商家可以框选出特定的省市（如：江浙沪），为它们设定一套独立的、不同于“默认规则”的运费。一个模板可以添加多条指定地区的规则。 |
| **规则详情**| 每一条运费规则（无论是默认还是指定），都由“**首件/首重**”和“**续件/续重**”的费用构成。例如：“**1** 件内，**10** 元；每增加 **1** 件，增加运费 **5** 元”。 |

![image-20250723095749331](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095749331.png)

所有创建好的模板，都会在一个“**运费模板列表**”中进行展示，商家可以在这里，对已有的模板，进行**查看、编辑和删除**。

#### **模块二：商品关联运费模板**

![image-20250723095812700](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095812700.png)

当商家在后台创建好了运费模板，最后一步，就是将它“**应用**”到具体的商品上。

在我设计的“**发布/编辑商品**”页面（`5.7`节）中，我会增加一个名为“**运费模板**”的**下拉选择框**。这个下拉框的选项，就是该商家在后台创建的所有运费模板。

商家只需要在这里，为这件商品，选择一个合适的模板。那么，当用户在前台下单购买这件商品时，系统就会根据用户的收货地址，自动地、智能地，匹配上运费模板中对应的规则，计算出最终的、精准的运费。




---
## 5.11 本章总结

在本章，我们深入到了电商后台系统的“**发动机舱**”，系统性地学习和设计了整个**商品管理**模块。这套系统的设计优劣，直接决定了我们电商平台“**货**”这个核心要素的规范性、丰富性和可扩展性。

我们的设计旅程，是一次从“原子”到“分子”，再到“系统”的构建过程：
* **解构“原子”**：我们的旅程，是从解构一件“商品”最基本的“**原子**”开始的。我们深刻地理解了`SPU`（一款商品）与`SKU`（一件货品）的本质区别，并掌握了构成它们的三种核心`商品属性`——**关键属性、销售属性、其他属性**。
* **搭建“书架”**：接着，我们为这些“原子”，搭建了用于收纳和组织的“**书架**”。我们设计了平台的“**商品类目**”体系，它就像是图书馆的分类法；我们还设计了“**品牌管理**”体系，它就像是出版社的陈列柜。
* **赋予“智能”**：然后，我们为这套系统，注入了“**智能**”。通过设计“**类目关联**”和“**属性继承**”等特殊规则，我们让“书架”和“书”之间，产生了聪明的联动。
* **打造“工具”**：在所有底层数据模型都设计完毕后，我们最终将它们，组合成了一个面向商家的、功能强大的“**生产力工具**”——**商品发布功能**，并为它设计了必不可少的配套功能——**运费模板**。

我将本章最核心的几个概念，总结在下面的表格中：

| **核心概念** | **我的核心理解** |
| :--- | :--- |
| **SPU与SKU** | SPU是“一款商品”，SKU是“一件货品”。这是商品数据建模的绝对核心。 |
| **类目/品牌/属性**| 这是构成SPU和SKU的“原材料”。平台必须在后台对它们进行**集中、统一、结构化**的管理。 |
| **关联与继承** | 这是让后台变“聪明”的关键。通过**类目关联**，我们为商家提供了智能化的发布体验；通过**属性继承**，我们为运营提升了配置效率。 |
| **商品发布功能**| 这是所有后台数据模型最终的应用场景。一个好的发布流程，能引导商家，录入规范、准确、完整的商品数据。 |

到这里，我们电商产品关于“**用户**”（用户端）、“**后台**”（平台端与商家端）的设计，就已经全部完成了。我们已经拥有了一份足以应对复杂电商业务的、完整的“建筑蓝图”。



---
# 第六章：电商后台 - 运营与交易管理

## 6.1 学习目标

在本章中，我的核心目标是，带大家掌握电商运营后台最核心的几个模块的设计。我们将学习如何设计**营销位管理**系统（如Banner）、**支付与订单**管理系统，以及**评价管理**系统，为我们的运营同事，打造一套强大、高效的“指挥中心”。

## 6.2 营销位管理

我们已经在用户端首页，为Banner、金刚区等营销模块，预留了最好的“广告位”。但这些广告位里，**具体要展示什么内容？链接到哪里去？什么时候上下线？** 这些都不能由程序员写死在代码里，而必须由我们的运营同事，在后台进行灵活、实时的配置。

**营销位管理**后台，就是我们为运营同事，设计的这个“**展位内容配置中心**”。

### 6.2.1 首页Banner管理

![image-20250723100716311](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100716311.png)

首页Banner，是我们平台最宝贵、最核心的流量入口。我必须为它设计一套功能完善的后台管理系统。

![image-20250723100737050](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100737050.png)

我设计这个后台，会从前台的用户体验，反推后台需要具备的功能。
* **前台需要**：图片能轮播、有顺序、能跳转。
* **运营需要**：Banner能定期、自动地更换。
* **推导出后台功能**：基于此，我设计的后台，就必须包含**Banner列表**、**Banner新增/编辑**、**审核**和**自动上下线**等核心功能。

![image-20250723100808952](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100808952.png)

“**新增/编辑Banner**”的表单，是这个模块的核心。我的设计会包含以下配置项：

| **配置项** | **我的设计说明** |
| :--- | :--- |
| `Banner名称` | 这是给运营看的内部名称，方便识别。例如：“2025年双十一主会场Banner-1”。 |
| `封面图` | 提供图片上传入口。我会在PRD中，严格规定**图片的尺寸、大小和格式**，确保前端展示效果。 |
| `Banner状态`| 提供一个“**上线/下线**”的开关。运营可以手动控制该Banner是否展示。 |
| `起止时间` | **这是最重要的功能**。提供日期和时间选择器，让运营可以预设Banner的“**自动上线时间**”和“**自动下线时间**”，实现无人值守的活动更新。 |
| `跳转页面 (Link)` | 即，用户点击这张Banner后，会跳转到哪里。 |
| `显示顺序`| 一个数字输入框，用来控制这张Banner，在首页轮播图中的排序。数字越小，排得越前。 |

![image-20250723100835858](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100835858.png)

对于“**跳转页面**”这个配置项，我设计的后台，至少要支持两种链接类型：
1.  **H5链接**：运营可以填写一个任意的网址（URL）。
2.  **原生页面链接**：运营可以选择跳转到App内部的某个特定页面（如：某个商品详情页、某个分类列表页）。

所有配置好的Banner，都会在一个列表中进行统一的管理和查看。



---
### 6.2.2 推荐位管理

![image_004](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_004.png)

除了首页Banner，我们的App中，还有很多其他的**推荐位**，比如首页金刚区、商品详情页的“为你推荐”等。

如果我为每一个推荐位，都设计一个独立的后台，那整个系统将变得无比臃肿和混乱。因此，我的设计思路是，建立一个**统一的、平台级的“活动管理中心”**，用一套通用的逻辑，来管理所有不同位置的推荐内容。

我们看到的这张“电子渠道运营管理平台”的截图，就是一个很好的例子。它将`首页`、`消息推送`、`banner`等所有运营活动，都放在一个列表中进行管理。

![image_006](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_006.png)

我设计的“**新建/编辑推荐位内容**”的后台表单，会包含以下这些核心的、可复用的配置项：

| **配置项** | **我的设计说明** |
| :--- | :--- |
| **活动/推荐名称** | 给本次推荐内容，起一个内部识的别名称。 |
| **运营位 & 显示位置** | **这是最重要的字段**。我会提供两个下拉框，让运营同事，可以精确地选择，这条内容要投放到**哪一个页面（运营位）**的**哪一个具体位置（显示位置）**。比如：`运营位：首页`，`显示位置：金刚区-第2位`。 |
| **权重/排序值** | 一个数字。当同一个推荐位下，有多条内容同时在线时，系统会根据这个值的大小，来决定它们的显示顺序。 |
| **内容元素** | 提供`图片/ICON上传`、`标题`和`副标题`输入框。 |
| **跳转链接** | 配置用户点击这个推荐位后，要跳转的目标页面（H5或App原生页面）。 |
| **排期** | 提供“**开始时间**”和“**结束时间**”选择器，用于内容的自动上下线。 |
| **审核流程** | 所有新增或修改的推荐内容，都必须经过“**提交审核**”的操作，由上级主管审批通过后，才能正式发布上线，确保安全。 |

![image_005](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_005.png)

### 6.2.3 活动会场管理

**活动会场**，是一个比单个“推荐位”，要复杂得多的营销模块。它通常是一个**由多个不同模块（如Banner、商品列表、优惠券入口等）聚合而成的专题页面**。

![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_008.png)

要实现这种高度灵活的页面，我需要为我的运营同事，设计一个“**可视化**”的页面搭建工具，也就是一个轻量级的**CMS（内容管理系统）**。

我们看到的这张“复杂业务管理”的后台截图，就是一个很好的范例。我设计一个活动会场搭建后台，会包含以下核心思路：

1.  **基础信息配置**：在表单的顶部，让运营可以先设置好整个会场的`活动名称`、`开始/结束时间`、`分享标题`，并上传最顶部的`头图`。
2.  **模块化组件**：我会预先定义好一套“**标准组件库**”，运营同事可以像“搭积木”一样，自由地选择和组合这些组件，来搭建自己的页面。
    * **我的拓展设计（常见组件）**：这个组件库通常会包括：
        * `商品列表组件`：运营可以选择一批商品，并选择不同的展示样式（如：一行两个、列表等）。
        * `优惠券组件`：运营可以关联几张优惠券，让用户在页面上直接领取。
        * `图片组件`：可以上传任意图片，作为楼层分隔或视觉点缀。
        * `文本组件`：可以添加富文本内容。
3.  **可视化编排**：一个更理想的设计，是让运营可以通过“**拖拽**”的方式，来自由地调整页面上各个组件的上下顺序。
4.  **统一的管理与审核**：所有搭建好的活动会场页面，都会生成一个唯一的URL。运营同事可以将这个URL，配置到我们`6.2.2`节设计的“推荐位”的跳转链接中去，从而实现引流。


---
## 6.3 支付管理

![image-20250723103028460](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723103028460.png)

我们在`3.5.2`节，已经从“技术实现”的视角，探讨了如何接入多种支付方式。现在，我们回到“**平台运营**”的视角来思考。

当我们的平台，已经同时接入了微信支付、支付宝、银联等多种渠道后，我就必须为我的运营和财务同事，提供一个后台，来**管理这些支付渠道**。比如，因为某个渠道的费率调整或系统维护，我们需要暂时关闭它，这个操作，就必须能在后台，一键完成。

### 6.3.1 支付渠道管理

这是支付管理后台的核心。我设计的这套功能，能让运营同事，像控制“开关”一样，灵活地管理前端向用户展示的支付方式。

#### 1. 支付渠道列表

![image-20250723103136091](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723103136091.png)这个模块的首页，是一个“**支付渠道列表**”。它清晰地列出了我们平台已经完成技术对接的所有支付渠道。

| **列表字段** | **我的设计说明** |
| :--- | :--- |
| `支付方式` | 清晰地展示支付渠道的名称和官方图标。 |
| **`状态`**| **这是最重要的运营开关**。运营同事可以通过一个“**启用/禁用**”的开关，来实时控制这个支付渠道，是否在前台对用户可见。 |
| `排序` | 一个数字输入框。运营可以通过调整数字的大小，来控制各个支付方式，在前台收银台的**显示顺序**。 |
| **`操作`**| 提供一个“**配置**”按钮，点击后，可以进入该渠道的参数配置页。 |

#### 2. 支付渠道配置

点击“配置”后，就会进入“**支付渠道配置页**”。这个页面的核心作用，是为我们的技术人员，提供一个**安全地、结构化地，存储和管理各个支付渠道API凭证**的地方。

![image-20250723103306061](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723103306061.png)

正如我们在`3.5.2`节提到的，我们从微信、支付宝等官方申请下来的商户资质，会包含一系列用于API通信的“账号密码”。这个配置页，就是用来填写和保存它们的。
* **AppID**：我们应用在支付渠道的唯一标识。
* **mchid (商户号)**：我们公司的商家身份编号。
* **商户API证书/密钥**：用于我们服务器与支付渠道服务器之间，进行安全加密通信的“密码”。

通过这套“**列表（控制开关）+配置（填写参数）**”的设计，我就将复杂的“技术对接”过程，与日常的“运营开关”过程，完美地解耦了。

### 6.3.2 交易流水与对账（拓展设计）

一个更完整的支付管理后台，除了上述的“渠道管理”，还应该包含以下两个核心模块：
1.  **交易流水查询**：我需要设计一个功能，让我的财务和客服同事，可以查询到通过我们平台的**每一笔**支付记录。这个流水列表，需要支持按订单号、用户ID、交易时间、支付渠道、支付状态等多个维度，进行复杂的查询和筛选。
2.  **对账与结算管理**：这是一个更高级的财务功能。我需要设计一个“自动对账”的系统。它能每天自动地，从微信、支付宝等渠道，拉取官方的结算账单，然后与我们自己系统的订单记录，进行逐条的、自动化的比对，并将差异项标记出来，供财务人员处理。这能极大地提升我们公司的财务管理效率。



---
## 6.4 订单管理

我们已经设计了营销和支付。当用户完成支付后，一个“**订单**”数据就正式诞生了。订单，是连接用户、商家、平台三方的“**契约**”，是整个电商交易流程中，最核心、最根本的**信息载体**。

![image-20250723105200805](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723105200805.png)

**订单管理**模块，就是我们为不同角色，提供的查看和操作这份“契约”的后台功能。它的设计，必须同时服务于**商家**（履约）和**用户**（查询）。

![image-20250723104828982](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723104828982.png)

### 6.4.1 商家端订单管理

![image-20250723104848091](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723104848091.png)

我们先设计**商家端**的订单管理后台。它的核心目标，是为商家内部的**运营、仓储、打包**等多个协同部门，提供一套**高效、准确、流程化**的线上作业工具，帮助他们顺利地完成从“**接收订单**”到“**发货**”的全过程。

![image-20250723105002093](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723105002093.png)

#### 1. 强大的查询与筛选区

为了应对商家在日常运营中，需要从海量订单里寻找特定订单的复杂场景（如：处理客诉、核对问题订单），我必须在页面的最顶部，设计一个功能极其强大的“**查询与筛选区**”。

**订单列表**，是商家运营人员每天上班后，第一个要打开的页面。它是所有订单处理工作的“**总任务看板**”。

![image-20250723105252266](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723105252266.png)

| 筛选字段 | 我的设计说明 |
| :--- | :--- |
| `订单编号` | 支持按唯一的订单ID，进行精准查询。 |
| `商品名称` / `SKU编码` | 支持按商品维度，查找所有包含该商品的订单。 |
| `下单时间` | 提供日期范围选择器，方便商家查询特定时间段内的订单。 |
| `客户姓名` / `电话` / `账号` | 当出现客诉时，方便客服人员快速定位到该用户的所有订单。 |
| `支付方式` / `订单来源` | 便于财务或运营人员，进行渠道和业务来源的数据分析。 |

#### 2. 按状态分类的工作队列

在筛选区的下方，我会设计一组“**状态Tab页**”。它的作用，是为商家预设好最高频使用的筛选器，将订单列表，直接划分为几个独立的“**工作队列**”。
* **`待出库`**：这是商家最重要的工作队列，所有已付款、等待打包发货的订单，都会在这里出现。
* **`未付款`**：商家可以在这里，看到那些已经拍下但还未付款的订单，可以进行催付等运营操作。
* **`已出库`、`已完成`、`已取消`**：方便商家查询历史订单和问题订单。

#### 3. 结构化的订单信息列表

列表的主体部分，我会以“**一个订单包裹**”为一行，进行结构化的信息展示，确保商家能在一行内，获取到这笔订单最核心的信息。
* **核心信息**：如截图所示，必须包含`商品信息`（缩略图、标题）、`单价/数量`、`货款金额`、`下单账号`、`订单状态`等。
* **物流信息**：对于已发货的订单，需要清晰地展示`快递公司`和`快递单号`。

#### 4. 丰富的操作项

列表最右侧的“**操作**”列，是商家进行订单处理的核心入口。我需要根据订单的不同状态，提供对应的高频操作。
* **`订单详情`**：这是所有状态下，都必须有的入口。
* **`出库`**：这是“待出库”状态下，最核心的操作。点击后，会进入发货操作流程（如：填写快递单号）。
* **`修改物流单` / `修改地址`**：这是为应对异常情况，提供的必要“补救”功能。
* **`延迟发货提醒`**：当商家无法按时发货时，可以通过这个功能，主动向用户发送一条安抚性的提醒。


#### 5. 订单详情与状态流转
![image-20250723111453299](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723111453299.png)
点击订单列表中的任一订单，即可进入**订单详情页**。这是关于这笔交易的“**唯一事实凭证**”，它必须完整、清晰地展示所有相关信息。

![image-20250723111508768](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723111508768.png)
订单的生命周期，是由“**订单状态**”来驱动的。在商家后台，商家执行的每一个核心操作，都会驱动订单状态向前流转。

![image-20250723111555824](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723111555824.png)
例如，当商家在后台点击“**发货**”并填写完快递单号后，这笔订单的状态，就会从`待发货`自动变更为`待收货`。

#### 6. 异常订单与售后订单管理

* **异常订单处理**：我需要为商家，设计处理异常情况的功能，比如因库存不足而需要“**拒单**”，或在发货前应用户要求“**修改订单**”（特别是收货地址）。
* **售后订单管理**：当用户发起“退款/退货”申请时，这些申请需要进入商家后台的一个独立“**售后订单**”工作队列中，供商家进行“**同意退款**”、“**拒绝申请**”等操作。

---
### 6.4.2 平台端订单管理

“对于平台而言，它也有订单管理模块...”

现在，我们来设计我们自己**内部运营和客服同事**使用的**平台端订单管理**后台。

#### 1. 核心设计差异

我设计平台端，与设计商家端的核心思路差异在于：
* **视角不同**：商家端只能看到**自己的**订单；而平台端，必须能看到**全平台所有商家**的订单。
* **职责不同**：商家的核心职责是**操作**（如：发货）；而我们平台的核心职责，更多的是**监督**和**客服仲裁**。

#### 2. 平台端订单列表

![image-20250723112351314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723112351314.png)

基于上述差异，我设计的平台端订单列表，与商家端相比，有以下不同：
* **增加“店铺”维度**：在列表的筛选器和表头中，我必须增加“**店铺名称**”这一维度。这能让我的客服同事，在接到用户电话时，可以快速定位到是哪个店铺的订单出了问题。
* **简化操作项**：在“操作”列，平台端的操作会更精简。主要以“**查看订单详情**”为主，而不会包含“发货”这类应由商家执行的操作。

#### 3. 平台端订单详情

![image-20250723112452278](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723112452278.png)

平台端的订单详情页，在信息展示上，与商家端需要保持完整和一致。
* **我的设计**：我会额外在页面顶部，增加一个“**店铺信息**”模块，清晰地展示出这笔订单所属商家的`店铺名称`、`联系方式`等。当出现交易纠纷时，这能方便我的客服同事，快速地联系上对应的商家，进行沟通和处理。




-----

## 6.5 订单统计

订单管理，解决的是“**处理单笔交易**”的问题。而订单统计，解决的是“**洞察整体业务**”的问题。

我设计的订单统计模块，其核心目标，是为我们的运营、市场和管理团队，提供一个**数据驱动的决策中心**。它不是简单的数据罗列，而是要能通过数据，清晰地回答三个核心的业务问题：

1.  **我们的“生意”做得怎么样？（交易维度）**
2.  **我们什么“商品”卖得好？（商品维度）**
3.  **我们的“客人”从哪里来？（订单来源维度）**

![image-20250723113907620](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723113907620.png)



---

### 6.5.1 核心统计维度

在设计数据看板时，我会围绕**三个核心维度**，分别构建不同的数据分析模块：

#### 1. 交易维度 - “我们生意做得怎么样？”

这是**最高层级**的模块，用于评估平台整体经营状况。

##### 核心指标及解读：

| **核心指标**        | **我的解读**                                                                                                                                         |
| --------------- | ------------------------------------------------------------------------------------------------------------------------------------------------ |
| **订单销售额 (GMV)** | 即“GMV”，特定时间段内用户下单的总金额，是衡量平台体量的最核心指标。                                                                                                             |
| **订单量**         | 特定时间段内的总订单数量。                                                                                                                                    |
| **客单价 (AOV)**   | 总销售额 ÷ 总订单数，反映用户的平均购买力。                                                                                                                          |
| **下单/支付用户数**    | ![下单与支付用户数对比图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114340925.png)<br>两者的对比可计算出“**支付转化率**”，评估下单后支付流程是否顺畅。      |
| **订单金额分布**      | ![订单金额分布图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114628770.png)<br>将订单金额分为如“0-50元”、“51-100元”等区间，帮助分析用户的核心消费力区间。 |
| **新老客交易构成**     | ![新老客交易构成图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114404565.png)<br>分析新客和老客各自贡献的销售额与订单量，是衡量平台用户健康度的重要指标。        |

---

##### 💡 支付转化率的两种计算方式：

###### ✅ 1. 订单转化率

**定义：** 衡量**完成支付的独立访客**占**总访客**的比例。

**计算公式：**

```
订单转化率 = (完成支付的订单数 ÷ 访问网站/App的总用户数) × 100%
```

**解释：**

* **完成支付的订单数**：指成功付款的订单数量。
* **访问总用户数**：独立访客或会话数（依据数据分析工具）。

**适用场景：** 衡量从访问到购买的**整体效率**，反映电商平台的直接销售能力。

---

###### ✅ 2. 支付成功率

**定义：** 衡量**成功支付交易**占**尝试支付交易**的比例。

**计算公式：**

```
支付成功率 = (成功完成支付的交易数 ÷ 发起支付的交易总数) × 100%
```

**解释：**

* **成功支付交易数**：用户付款成功的交易。
* **发起支付交易数**：用户点击支付后实际发起的请求（无论成功与否）。

**适用场景：** 用于评估**支付接口或渠道的稳定性**和用户体验。

----

>`二者对比如下：`

* **订单转化率** → 衡量**用户意愿与行为**。
* **支付成功率** → 衡量**支付能力实现与顺畅程度**。

两个指标都很关键，分别代表“转化意愿”和“支付执行”的两个环节。

---

#### 2. 商品维度 - “我们什么东西卖得好？”

这个模块帮助我与品类运营同事洞察商品表现，优化选品与库存。

##### 核心指标及解读：

| **核心指标**       | **我的解读**                                |
| -------------- | --------------------------------------- |
| **商品浏览量 (PV)** | 商品详情页被浏览的次数，反映商品的**吸引力**。               |
| **商品销量**       | 商品的实际销售数量，反映**市场接受度**。                  |
| **商品转化率**      | 商品支付订单数 ÷ 商品浏览量，是衡量商品从吸引到成交的“**黄金指标**”。 |
| **加购物车数**      | 反映了用户对该商品的“**潜在兴趣**”。                   |

我会基于这些数据设计如：

* 热销商品排行榜
* 高转化率商品榜
* 品类销售额占比等数据榜单

---

#### 3. 订单来源维度 - “我们的客人从哪里来？”

这个模块帮助我与市场运营同事评估**流量渠道**与**营销活动**效果，以优化预算投入。

![订单来源分析图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114753992.png)


| **核心指标**   | **我的解读**                                                         |
| ---------- | ---------------------------------------------------------------- |
| **用户渠道来源** | 分析用户产生订单时，其最初来自哪个渠道：如 App、H5 商城、PC 端、微信小程序等。                     |
| **转化入口来源** | 分析用户通过哪个“**营销位**”下单（如首页 Banner、活动入口等），用于计算每个广告位的 **ROI（投入产出比）**。 |

---

## 6.6 评价管理

我们都知道，在电商购物中，**用户评价**是影响购买决策的最重要的因素之一。它是一种强大的“**社会认同（Social Proof）**”机制。因此，为平台设计一套**公平、透明、高效**的评价管理体系，是我工作的重中之重。

![image-20250723133910689](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723133910689.png)

我设计评价管理后台，需要同时服务于**商家**和**平台**这两个角色，他们的核心需求是不同的：
* **商家**：更关注**用户反馈**，希望管理店铺声誉。
* **平台**：更关注**内容监管**，希望维护社区环境的健康。

因此，我通常会为他们，设计两套功能各有侧重的后台。

### 6.6.1 评价列表与审核

![image-20250723133952088](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723133952088.png)

首先，我需要明确评价的**入口时机**。在我的流程设计中，只有当订单状态流转为“**待评价**”（通常在用户“确认收货”后），用户端才会出现“评价”按钮。用户提交评价后，订单状态则变为“**已完成**”，形成闭环。

![image-20250723134151545](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723134151545.png)

**1. 平台端：内容审核与监管**
用户提交的所有评价，都会首先进入我们**平台端的评价管理后台**。

* **核心职责**：平台运营的核心职责，是**内容审核**。他们需要快速地筛选和处理所有评价，特别是检查其中是否包含**敏感词汇、违规图片**等不良信息。
* **核心功能**：因此，平台端的评价列表，功能相对纯粹。列表需要展示`订单编号`、`用户昵称`、`评价内容`等全局信息。而最核心的操作，就是“**删除**”。对于违规的评价，平台拥有最高权限，可以将其直接删除。

![image-20250723134141388](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723134141388.png)

**2. 商家端：查看与互动**
当一条评价通过了平台的审核，它才会出现在**商家端的评价管理后台**。

* **核心职责**：商家的核心职责，是**与用户互动，维护自己的店铺声誉**。
* **核心功能**：因此，商家端的后台，操作更丰富：
    * **`回复`**：商家可以回复用户的评价，这是最重要的客户关系维护功能。
    * **`置顶`**：商家可以将优质的、图文并茂的好评，在自己商品详情页的评价区进行置顶，作为“买家秀”的典范。
    * **`申诉`**：当商家认为自己遭遇了恶意差评时，可以向平台发起申诉。

### 6.6.2 评价申诉处理

“**评价申诉**”，是我为保障商家权益，设计的平台仲裁流程。
* **商家发起申诉**：商家在后台，点击某条差评旁的“申诉”按钮。
* **提交申诉理由**：我会设计一个弹窗，让商家可以填写申诉的理由，并上传相关的证据（如：与用户的聊天记录截图）。
* **平台介入仲裁**：这条申诉，会进入我们平台端后台的一个“**申诉处理**”队列中。
* **平台做出判决**：由我们的平台运营，作为**中立的第三方**，来对申诉进行判决。最终的判决结果，可能是“**维持原评价**”，也可能是“**隐藏/删除评价**”。

这个申诉流程，是维护平台公平公正、调解用户与商家矛盾的关键机制。

## 6.7 本章总结

在本章，我们完整地设计了电商后台中，负责“**让生意转起来**”的几个核心运营与交易模块。

| **设计模块** | **我的核心设计思考** |
| :--- | :--- |
| **营销位管理**| 为运营同事，提供了可以**灵活配置**首页`Banner`、`推荐位`、`活动会场`等营销资源的“弹药库”。 |
| **支付管理**| 我们设计了后台的“**支付渠道开关**”，并了解了独立对接与**聚合支付**在技术和商务上的区别。 |
| **订单管理**| 我们分别为**商家端**（侧重**履约操作**）和**平台端**（侧重**全局监督**），设计了功能职责各有侧重的订单管理后台。 |
| **订单统计**| 我们为平台，设计了一个包含**交易、商品、来源**三大维度的“数据驾驶舱”，用于业务的宏观洞察。 |
| **评价管理**| 我们为平台设计了**内容审核**后台，为商家设计了**互动与申诉**后台，共同维护了一个健康的评价生态。 |




