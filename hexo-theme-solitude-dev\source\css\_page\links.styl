.flink#banners
  margin-bottom: .5rem

.tags-group-title
  font-size 14px
  color var(--efu-card-bg)
  background var(--efu-lighttext)
  position absolute
  width 100%
  height 100%
  display flex
  border-radius 120px
  align-items center
  justify-content center
  opacity 0
  transition .3s

.flink .tags-group-icon:hover .tags-group-title
  opacity 1
  backdrop-filter saturate(180%) blur(20px)

.flink.article-container
  margin-top 1rem

.article-container

  .flink-desc
    margin 0
    color var(--efu-secondtext)

  .site-card-tag
    position absolute
    top 0
    left 0
    padding 4px 8px
    background-color var(--efu-blue)
    box-shadow var(--efu-shadow-blue)
    color var(--efu-white)
    z-index 1
    border-radius 0 0 12px 0
    transition .3s
    font-size .6rem
    overflow hidden

    .light
      cursor pointer
      position absolute
      top 0
      width 100px
      height 50px
      background-image -webkit-linear-gradient(0deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, .5), rgba(255, 255, 255, 0))
      animation light_tag 4s both infinite

    &.vip
      background-color var(--efu-vip)

    &.speed
      background-color var(--efu-speed)

  .site-card-group
    padding 20px 0
    display flex
    flex-wrap wrap
    justify-content flex-start
    margin -8px
    align-items stretch

    .site-card
      margin 8px
      display block
      line-height 1.4
      border var(--style-border)
      border-radius 12px
      transition .3s
      transition-timing-function ease-in-out
      overflow hidden
      height 200px
      position relative
      width calc(100% / 7 - 16px)
      background var(--efu-card-bg)
      box-shadow var(--efu-shadow-border)

      +maxWidth1200()
        width calc(20% - 16px)

      +maxWidth1024()
        width calc(25% - 16px)

      +maxWidth768()
        width calc(100% / 3 - 16px)

      +maxWidth600()
        width calc(100% / 2 - 16px)

      .img
        border-radius 0
        height 120px
        width 100%
        display flex
        border none
        padding 0
        transition all .3s ease 0s
        overflow hidden

        img
          border-radius 0
          transform scale(1.03)
          transition .3s
          margin 0
          max-width 100%
          width 100%
          height 100%
          object-fit cover

      .img-alt
        display none

      .info
        display flex
        border none
        padding 0.5rem
        width 100%
        height 90px
        margin 0
        border-radius 0 0 12px 12px

        img
          border-radius 32px
          transition all .3s ease-out
          margin 2px 8px 0 0
          width 20px
          height 20px
          min-width 20px
          min-height 20px
          transform scale(1.1)
          background var(--efu-secondbg)

        .site-card-text
          display flex
          flex-direction column
          align-items flex-start

          .title
            color var(--efu-fontcolor)
            text-align left
            font-weight 600
            display -webkit-box
            -webkit-box-orient vertical
            overflow hidden
            -webkit-line-clamp 1
            transition all .3s ease 0s

          .desc
            font-size .7rem
            color var(--efu-fontcolor)
            opacity .7
            transition all .3s
            text-align left
            overflow-wrap break-word
            line-height 1.2
            display -webkit-box
            -webkit-box-orient vertical
            overflow hidden
            -webkit-line-clamp 2

      &:hover
        border var(--style-border-hover)
        box-shadow var(--efu-shadow-main)
        background var(--efu-main)

        .site-card-tag
          left: -60px

        .img
          transform scale(1)
          filter brightness(.3)
          border-radius 12px
          background var(--efu-lighttext)
          background var(--efu-main)
          +minWidth768()
            height 80px

            img
              transform scale(1.1)

        a
          color var(--efu-white)
          background var(--efu-main)
          box-shadow var(--efu-shadow-main)

        .info
          height 120px
          background-color var(--efu-theme)

          img
            width 0
            height 0
            opacity 0
            min-width 0
            min-height 0

          .title
            color var(--efu-white)

          .desc
            color var(--efu-white)
            text-align left
            -webkit-line-clamp 4
            width 100%

  .flink-list
    padding 0
    margin 0.5rem -6px 1rem -6px
    overflow-x hidden
    text-align center

    &.mini
      > .flink-list-item
        height 60px

        a
          img
            width 30px
            height 30px
            min-width 30px
            min-height 30px

          .flink-item-desc
            display none

  .flink-list-item
    margin 6px 6px
    transition .3s
    border-radius 12px
    transition-timing-function ease-in-out
    position relative
    width calc(20% - 12px)
    border var(--style-border-always)
    box-shadow var(--efu-shadow-border)
    background var(--efu-card-bg)
    display flex
    float left
    overflow hidden
    height 90px
    line-height 17px
    transform translateZ(0)

    +maxWidth1200()
      width calc(25% - 12px)

    +maxWidth1024()
      width calc(33.3333% - 12px)

    +maxWidth768()
      width calc(50% - 12px)
      height: 120px

      a
        flex-direction: column

    a
      display flex
      width 100%
      height 100%
      border none
      align-items center

      .img-alt
        display none

      img
        border-radius 32px
        margin 15px 20px 15px 15px
        transition .3s
        background var(--efu-background)
        min-width 60px
        min-height 60px
        float left
        width 60px
        height 60px

      .flink-item-info
        display flex
        flex-direction column
        justify-content center
        width calc(100% - 90px)
        height fit-content
        padding 0 4px

        +maxWidth768()
          align-items center
          width 100%

          .flink-item-name
            padding: 0
            width: 100%
            text-align: center
            font-size: 14px

          .flink-item-desc
            display: none

        span
          transition .3s

      .flink-item-name
        text-align left
        font-size 19px
        line-height 20px
        color var(--efu-fontcolor)
        display block
        padding 0 10px 0 0
        font-weight 700
        max-width calc(100% - 12px)
        overflow hidden
        text-overflow ellipsis
        white-space nowrap

      .flink-item-desc
        white-space normal
        padding 5px 10px 16px 0
        color var(--efu-fontcolor)
        text-align left
        height 40px
        text-overflow ellipsis
        opacity .7
        display -webkit-box
        overflow hidden
        -webkit-box-orient vertical
        -webkit-line-clamp 2
        font-size .93em

    &:hover
      background var(--efu-theme)

      .site-card-tag
        left: -50px

      a
        background 0 0

        img
          transition .6s
          width 0
          height 0
          opacity 0
          margin 5px
          min-width 0
          min-height 0

      .flink-item-info
        min-width calc(100% - 20px)

        .flink-item-name
          color var(--efu-card-bg)

        .flink-item-desc
          color var(--efu-card-bg)
          overflow hidden
          width 100%

@keyframes light_tag
  0%
    transform skewx(0)
    left -150px
  99%
    transform skewx(-25deg)
    left 50px
