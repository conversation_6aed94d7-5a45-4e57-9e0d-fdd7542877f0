<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理进阶（五）：第五章：电商后台 - 商品管理 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理进阶（五）：第五章：电商后台 - 商品管理"><meta name="application-name" content="产品经理进阶（五）：第五章：电商后台 - 商品管理"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理进阶（五）：第五章：电商后台 - 商品管理"><meta property="og:url" content="https://prorise666.site/posts/4512.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第五章：电商后台 - 商品管理在上一章，我们已经设计好了商家入驻的流程，让第一批商家成功进入了我们的平台。现在，他们最迫切的需求就是：我应该如何，把我的商品，发布到平台上进行售卖？ 本章，我们就将为商家，以及我们自己平台的运营，设计一套完整、专业、可扩展的商品管理系统。 5.1 学习目标 在本章中，"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta name="description" content="第五章：电商后台 - 商品管理在上一章，我们已经设计好了商家入驻的流程，让第一批商家成功进入了我们的平台。现在，他们最迫切的需求就是：我应该如何，把我的商品，发布到平台上进行售卖？ 本章，我们就将为商家，以及我们自己平台的运营，设计一套完整、专业、可扩展的商品管理系统。 5.1 学习目标 在本章中，"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/4512.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理进阶（五）：第五章：电商后台 - 商品管理",postAI:"true",pageFillDescription:"第五章：电商后台 - 商品管理, 5.1 学习目标, 5.2 商品发布流程推导, 1. 从最小商品模型开始思考, 2. 用特殊场景挑战简单模型, 3. 推导出核心发布流程, 4. 流程总结, 5.3 商品类目, 1. 商品类目的核心作用, 2. 从角色需求到产品功能, 3. 平台端：类目管理功能设计, 4. 商家端与用户端：类目的使用, 5.4 品牌管理, 1. 品牌管理的核心价值, 2. 平台端：品牌库管理功能设计, 3. 特殊流程：自主品牌入驻, 5.5 SKU与SPU, 1. 核心定义, 2. SPU与SKU的关系, 3. 技术实现浅谈, 5.6 商品属性, 1. 属性的存在方式：属性名 + 属性值, 2. 属性的分类, 3. 属性池：平台端的统一管理, 5.7 商品发布功能设计, 1. 页面信息结构, 2. 功能模块详细设计, 模块一：填写商品基本信息, 模块二：设置销售属性与SKU, 模块三：编辑商品描述, 5.8 类目关联的相关场景, 1. 类目与属性的关联, 2. 类目与品牌的关联, 5.9 属性管理特殊规则, 1. 属性分组 - 让信息更有序, 2. 属性继承 - 让配置更高效, 5.10 运费模板, 1. 什么是运费模板？, 2. 运费模板功能设计, 模块一：创建与管理运费模板, 模块二：商品关联运费模板, 5.11 本章总结第五章电商后台商品管理在上一章我们已经设计好了商家入驻的流程让第一批商家成功进入了我们的平台现在他们最迫切的需求就是我应该如何把我的商品发布到平台上进行售卖本章我们就将为商家以及我们自己平台的运营设计一套完整专业可扩展的商品管理系统学习目标在本章中我的核心目标是带大家掌握电商后台商品管理模块的完整设计我们将首先从宏观上推导出商品从录入到呈现的核心业务流程然后再深入到微观学习类目属性等构建商品体系的原子概念并最终将它们组合成一个完整的商品发布功能商品发布流程推导在我动手设计商品发布这个后台功能之前我一定会先将支撑这个功能的端到端业务流程梳理得一清二楚从最小商品模型开始思考我的思考会先从终局出发即从一个普通用户的视角来看要让我能看懂一件商品至少需要呈现哪些信息这个问题的答案构成了我们电商系统的最小商品模型它至少需要包含以下五个核心信息标题商品叫什么名字图片商品长什么样价格商品卖多少钱库存商品还有没有货描述商品的详细介绍用特殊场景挑战简单模型那么基于这个最小模型一个最简单的设计思路就是我只需要给商家提供一个包含这五个字段的表单让他填完提交不就可以了吗这样够吗当我把这个简单的模型放入真实的复杂的电商场景中去检验时会立刻发现它的不足我必须考虑到以下这些特殊场景分类场景任何一件商品都需要被归属到一个明确的商品分类下如电脑办公电脑组件硬盘这样用户才能通过分类导航找到它品牌场景用户也常常会通过品牌的维度来找商品如联想品牌馆因此商品也需要和品牌进行关联属性场景不同品类的商品其需要展示的商品参数是完全不同的比如硬盘需要展示容量接口等参数而一件衣服则需要展示材质适用季节等参数一个固定的简单的表单是无法满足这种多样性的推导出核心发布流程为了解决上述所有特殊场景带来的问题我推导出的一个专业的商家发布商品核心流程必须是一个结构化的分步骤的流程第一步选择商品类目这是商家发布商品的起点我会让商家先从我们后台预设的商品类目树中精准地选择他将要发布的商品属于哪一个最细分的叶子类目我的设计思考这一步至关重要因为用户选择的类目将直接决定了下一步他需要填写的商品属性第二步选择商品品牌在确定了类目后商家需要选择该商品所属的品牌第三步设置商品信息只有在完成了前两步之后系统才会展示出最终的商品信息设置页面这个页面除了包含我们前面提到的最小商品模型标题价格图片库存描述的填写区域外还会根据第一步选择的类目动态地加载出该类目专属的商品属性填写项流程总结至此我们就完成了一次完整的流程推导这个先选类目再选品牌最后填写信息的三步走流程就是我为商家设计的既能满足复杂场景又能保证后台数据结构化规范化的核心解决方案这个流程完美地嵌入到了我们之前梳理的商家录入平台审核用户浏览的宏观业务流程中构成了其中商家录入这一环节的具体实现商品类目在我推导出的先选类目再选品牌最后填写信息的商品发布流程中选择商品类目是所有流程的起点因此商品类目体系是我在设计商品管理后台时第一个要搭建的也是最底层的地基一个清晰稳定可扩展的类目体系是整个电商平台有序运转的保障商品类目的核心作用我设计商品类目体系是因为它同时为我们生态中的三大核心角色都提供了不可或缺的价值角色核心作用我的解读平台确定服务范围进行监管我后台设置的类目直接定义了我们平台允许卖什么不允许卖什么这是我进行平台治理控制风险明确业务边界的根本商家分门别类便于管理我为商家提供了一套标准化的商品货架商家只需要按照我的类目规范将商品上架到对应的位置就能实现规范化的管理用户方便查找商品我为用户提供了一套清晰的商场导览图用户可以通过分类导航快速地找到自己想要逛的区域极大地提升了购物效率从角色需求到产品功能基于上述的作用我可以清晰地提炼出不同角色的产品需求并推导出我们需要提供的核心功能这个推导的结论非常清晰也是我设计的核心原则平台来管理类目商家使用类目平台的需求是限定范围监管这要求我必须设计一套强大的后台类目管理功能商家的需求是加载类目模板方便操作这要求我必须在商品发布流程中提供一个易用的类目选择功能平台端类目管理功能设计这是我为平台运营人员设计的类目配置中心多级类目结构我设计的后台必须支持多级类目的创建和管理如案例图所示一个笔记本商品它的类目层级可能是数码家电一级家电二级笔记本三级基础管理操作后台必须提供对每一级类目的新增编辑删除查询等基础操作我的拓展设计属性与品牌关联这是后台类目管理最核心的也是最高阶的功能在运营人员新增或编辑一个叶子类目即不能再往下分的最后一级类目如笔记本时我设计的后台必须允许他将这个类目与一组特定的商品属性和品牌进行关联例如在配置笔记本这个类目时运营就要为它关联上屏幕尺寸内存型号等属性并关联上联想华为苹果等品牌商家端与用户端类目的使用我们平台运营在后台辛辛苦苦搭建好的这套类目体系最终会在商家端和用户端被使用和呈现在商家端当商家发布新商品时我们流程的第一步就是让他从我们后台预设好的类目树中选择一个当他选择了笔记本之后系统就会因为我们后台的关联配置而动态地为他加载出需要填写的屏幕尺寸内存等属性在用户端用户则会在我们的分类页看到我们后台配置好的类目树结构并可以逐级点击进行商品的浏览和筛选一个后台设计得清晰合理的类目体系是前台商家发布体验流畅用户浏览体验清晰的根本保障品牌管理在我看来如果说类目是商品的物理属性分类那么品牌就是商品的心智属性分类用户在购买决策时品牌是影响他们信任和选择的极其重要的一个因素因此我必须在后台建立一套完善的由平台强管控的品牌管理体系品牌管理的核心价值我设计品牌管理系统同样是为了服务好我们生态中的三大核心角色角色核心需求我需要提供的产品能力平台规避假冒伪劣山寨产品保证平台的商品品质和声誉必须建立一套品牌的审核与认证机制确保只有合规的真实的品牌才能在平台上被售卖商家能够清晰地标明自己所售卖商品的品牌归属我需要在商品发布流程中为商家提供一个清晰准确的品牌选择器用户我只想看某个品牌的商品能够通过品牌维度快速找到自己想要的商品我需要在用户端提供按品牌进行搜索和筛选的功能基于上述的需求我推导出的核心功能是平台端必须有后台品牌管理增删改查功能商家端必须在商品发布时有品牌选择功能用户端必须有品牌搜索筛选功能平台端品牌库管理功能设计所有品牌功能的核心在于我们平台运营后台必须建立一个品牌库这个品牌库是由我们平台统一进行维护的它是我们平台上所有合法品牌的唯一真实来源我设计的品牌库后台主要包含以下功能品牌信息字段在新增一个品牌时运营需要填写该品牌的中文名英文名简介等信息基础管理功能运营可以对品牌库中的品牌进行常规的新增编辑查询操作状态管理每个品牌都有启用停用两种状态当某个品牌出现问题如被曝出重大质量问题品牌方与我们合作终止时运营可以将其状态设置为停用设置为停用后商家在发布商品时就无法再选择这个品牌了特殊流程自主品牌入驻这时一个非常常见的业务场景就出现了如果一个商家想售卖一个我们品牌库里还没有收录的新品牌怎么办我不能让商家随意地手动地填写品牌名称这会导致品牌库数据混乱出现大量山寨和无效品牌因此我必须设计一套严谨的新品牌入驻审核流程我的设计流程如下商家提交申请在商家后台我会提供一个新增品牌申请的入口商家需要在这个页面填写新品牌的基础信息并必须上传该品牌的商标注册证作为资质证明平台审核商家的申请会进入我们平台运营后台的品牌审核列表中运营同事的核心工作是核实商标注册证的真实性和有效性品牌入库审核通过后运营同事会将这个新品牌的信息正式录入到我们的品牌库中并设置为启用状态商家选用一旦品牌成功入库商家以及其他所有获得了该品牌授权的商家就可以在发布商品时从品牌选择器中选择这个新的品牌了通过这套流程我既满足了商家引入新品牌的需求又确保了平台对所有品牌的强管控保证了我们电商品牌生态的健康与在我设计任何电商后台的商品系统时我的第一个思考就是要清晰地定义和这两个概念是整个商品世界的基本粒子理解了它们就理解了所有复杂商品体系的构成核心定义标准产品单元我把它理解为一款商品它是一组具有共同的标准化的核心属性的商品的集合是商品信息聚合的最小单位例如就是一个它代表了这个产品系列与它的颜色内存大小无关我们通常用来做商品的通用性描述展示和搜索库存量单位我把它理解为一件货品它是库存控制的最小可用单位是真正物理存在的可以被用户购买的最小单元例如一台白色的内存为的就是一个一台红色的内存为的则是另一个完全不同的每一个都有自己独立的库存和价格与的关系和之间是一个一对多的层级关系一个通过不同的销售属性的组合可以衍生出多个不同的销售属性就是那些能影响到商品最终售价和库存的属性比如颜色尺码内存大小套餐类型等所以它们之间的关系公式是一组确定的销售属性我们在京东看到的这个商品详情页就是一个完美的现实案例是这个商品本身销售属性是选择颜色和选择版本这两个维度当用户选择了黑色和之后页面上展示的特定价格和库存状态就是属于这个唯一的技术实现浅谈那么为了实现这套逻辑我作为产品经理需要如何与我的研发同学沟通呢我需要向他们讲清楚后端的数据模型和前端的交互逻辑后端设计数据模型在后台数据库中我们至少需要设计几张表来清晰地表达和的关系数据表我的设计说明表这张表用来存放这个的通用信息比如商品名称商品描述品牌类目等销售属性名表这张表用来定义有哪些销售属性比如它会记录这个有颜色和内存这两个销售属性销售属性值表这张表用来定义每个销售属性有哪些可选值比如它会记录颜色这个属性有黑色白色红色等可选值表这是最核心的表它用来存放每一个具体的货品比如黑色就是这张表里的一行记录这行记录里会包含它自己专属的价格库存并会关联到它的父级以及它所对应的属性值黑色前端设计交互逻辑当用户打开一个商品详情页时前端与后端的交互流程是这样的前端向后端服务器发送一个请求告诉它我需要为的商品数据后端服务器收到请求后会把表里的通用信息描述主图等以及与这个关联的所有表里的记录比如黑色的价格库存白色的价格库存黑色的价格库存一次性地全部返回给前端前端拿到这些数据后就会在页面上动态地渲染出颜色和内存这两个维度的所有可点击的选择按钮当用户点击黑色和这两个按钮时前端会直接在本地已经拿到的数据中查找到对应的那个然后瞬间将页面上的价格和库存更新为这个专属的价格和库存这个过程通常不需要再次请求后端服务器因此用户会感觉体验非常流畅商品属性在上一节我们学习了和我们知道一个黑色的是一个但这立刻引出了一个核心问题系统是怎么知道会有颜色和内存这两个选项的商家在发布商品时并不是随意填写这些信息的这背后的答案就是我们这一节要学习的一套由平台预先定义好的结构化的数据体系商品属性属性的存在方式属性名属性值首先我们要明确一个属性最基本的构成任何一个属性都是由一个属性名和一个属性值配对组成的属性名相当于问题比如型号颜色传输速度属性值相当于答案比如黑色我设计的整个商品信息体系就是由成千上万个这样的键值对构成的属性的分类为了让这成千上万的属性能够被系统有序地管理和使用我必须对它们进行分类在我的电商产品设计中我会严格地将所有属性划分为以下三种类型属性分类简单来说案例以为例关键属性就像商品的身份证确定是哪一款商品不会变也不能选这个名字本身以及它的具体型号比如销售属性决定你最终买哪个具体商品你必须选选了之后价格或库存可能就不同颜色星光色午夜色等存储空间等其他属性就是商品的各种特点介绍看看就行不能选也不影响价格处理器型号屏幕是防水级别是等等我们可以通过这张网卡的案例图清晰地看到这三类属性在真实商品详情页上的分布属性池平台端的统一管理下一个关键问题是这么多属性名如颜色尺寸型号它们是从哪里来的为了保证数据的规范和统一比如避免商家填写颜色商家填写色彩我必须在平台运营后台建立一个由平台统一管理的属性池属性池是我为平台运营人员设计的一个用来集中管理所有属性名的后台功能在这里运营同事可以对平台中可能出现的所有属性名进行增删改查我的拓展设计属性与类目的关联这个属性池并不是孤立存在的它设计的精髓在于和我们节学习的商品类目进行深度绑定在我设计的类目管理后台当运营人员在编辑笔记本电脑这个类目时他就可以从属性池中为这个类目勾选上它应该具备的属性比如型号内存容量屏幕尺寸硬盘容量等这样一来当商家在发布商品第一步选择了笔记本电脑这个类目后系统就会自动地智能地为他加载出需要填写的型号内存容量等属性从而实现了整个商品发布流程的结构化和智能化商品发布功能设计在前面的小节中我们已经将商品这个复杂的概念拆解为了类目品牌属性等一系列结构化的原子部件现在我们的任务就是将这些原子部件重新组合起来为我们的商家设计一个功能强大体验流畅的商品发布功能这个功能就是商家后台的核心生产力工具页面信息结构在我设计这个复杂的发布商品页面时我首先会对需要商家填写的信息进行一次高层级的结构化分类我会把整个页面划分为三大信息模块商品的基本信息即层级的通用信息如商品名称图片等商品的属性包括决定的销售属性以及其他描述性属性商品的详情即图文并茂的用于营销的长图文描述功能模块详细设计现在我们来逐一设计承载这三类信息的功能模块模块一填写商品基本信息这是商家进入发布流程后看到的第一个表单区域它用来采集这件商品最基础的信息字段我的设计说明商品分类这个字段通常是只读的它会显示商家在上一个步骤节推导的流程中所选择的类目商品名称文本输入框用于填写的标题商品品牌一个下拉选择框我设计的逻辑是这个下拉框里只会出现我们后台与该商品分类相关联的品牌而不是全部的品牌这是一种智能化的设计商品价格商家可以填写商品的市场价或划线价每个的具体售价会在下一步设置商品展示图一个图片上传控件我会明确地标注出最多可以上传几张以及推荐的尺寸和格式以保证前端展示效果的统一模块二设置销售属性与这是整个商品发布功能中技术和交互最复杂但也最核心的一个模块我把它分为两步第一步定义销售属性我会提供一个交互区域让商家可以为他的商品添加销售属性属性名通过一个下拉框让商家从该类目下我们预设好的属性如颜色尺寸中进行选择属性值在选定了属性名后商家可以手动地添加多个属性值如红色蓝色第二步生成并填写明细当商家定义好所有的销售属性和属性值后我设计的后台最智能的地方就体现出来了系统会自动地将这些属性值进行笛卡尔积组合生成一个完整的列表商家的工作不是去手动组合而是在这个自动生成的表格里做填空题他只需要为每一个填写它专属的销售价格和销售库存即可这个设计极大地降低了商家的操作复杂度和出错率模块三编辑商品描述这是页面的最后一个模块用于上传商品的长图文详情我的拓展设计分端描述为了追求极致的用户体验一个专业的电商后台应该允许商家为端和移动端分别上传和编辑两套不同的商品描述为什么因为端屏幕大可以展示更丰富更复杂的图文内容而移动端屏幕小则需要更简洁加载速度更快的图片和文字提供两个独立的富文本编辑器能让有能力的商家为不同设备的用户提供最优的浏览体验通过将这三大模块有机地组合在一个页面中我们就为商家提供了一个功能强大逻辑清晰体验智能的商品发布功能类目关联的相关场景在前面的小节中我们已经独立地设计了商品类目品牌和商品属性这三个核心的数据模块但如果它们只是三个孤立的列表那我们的后台依然是笨拙的一个智能的后台必须能理解这三者之间的内在关联本节我们就来设计这套关联系统类目与属性的关联我们首先思考一个场景我们的属性池里包含了颜色尺码也包含了型号屏幕尺寸当一个商家来发布一件恤时如果我们在设置属性的环节把所有这些属性都展示给他那将是一场灾难我的解决方案我必须为商品属性打上类目的烙印即创建类目属性我的设计思路如下在平台端后台进行关联在我设计的类目管理后台当运营同事在编辑一个叶子类目如恤时我必须提供一个功能让他可以从属性池中勾选出所有与恤相关的属性如颜色尺码材质适用季节并将它们与恤这个类目进行绑定在商家端后台智能调用经过了后台的绑定操作后商家在发布商品时当他在第一步选择了恤这个类目那么在后续的设置商品属性环节系统就会只加载并显示出颜色尺码材质等这几个已经绑定好的属性供他填写在用户端前台精准呈现这个设计最终会惠及我们的用户当用户在前台浏览恤这个分类列表时页面左侧的筛选器也同样只会展示出颜色尺码材质等这些与恤强相关的有意义的筛选条件类目与品牌的关联同样的逻辑也完全适用于品牌管理当商家发布一件的恤时如果让他从一个包含海尔华为等上千个品牌的总列表里去寻找体验会非常糟糕我的解决方案我同样需要在后台建立品牌与类目的关联这样做的好处是提升商家发布商品的便捷性避免出错让我们的品牌管理更标准化让用户在前台按分类品牌进行筛选时速度更快我的设计思路如下在平台端后台进行关联在类目管理后台当运营编辑恤这个类目时除了关联属性他还需要关联品牌他会从品牌库中勾选出所有属于服饰类目的品牌如阿迪达斯优衣库在商家端后台智能调用当商家发布商品选择了恤类目后他在选择品牌的下拉菜单里看到的就将是一个被智能筛选过的只包含阿迪达斯等服饰品牌的短列表总结类目属性品牌的后台关联设计是我认为的电商后台商品管理系统中最能体现设计功力的一环它是一个后台配置一小步前台体验一大步的经典设计能让我们的整个商品体系变得井然有序充满智慧属性管理特殊规则在我们节的设计中我们确立了类目属性关联的核心思想但在面对一个拥有成千上万类目和属性的大型电商平台时简单的关联会带来两个新的问题一是商家端填写体验杂乱二是平台端配置效率低下为了解决这两个问题我必须在我的设计中引入两个高级的特殊规则属性分组和属性继承属性分组让信息更有序遇到的问题一个笔记本电脑类目可能会关联几十个属性如果我在商家发布商品的页面把这几十个属性输入框从上到下平铺直叙地排列下来整个页面会显得极其冗长和混乱商家很难快速找到自己要填写的项目我的解决方案我会引入属性组的概念正如我们看到的大部分商品详情页一样属性信息天然就是可以被分组的如显示器参数处理器内存硬盘等我的后台设计第一步创建属性组在平台运营后台我会设计一个独立的属性组管理功能在这里运营同事可以创建不同的属性组比如创建一个名为组的分组然后从我们的属性池中将相关的属性如型号核心数添加进这个组里第二步类目关联属性组在类目管理后台运营同事在为类目关联属性时他关联的就不再是一个个零散的属性而是一个个已经打包好的属性组最终效果经过后台的这一番配置商家在发布商品选择了笔记本电脑类目后他看到的属性填写区就不再是混乱的长列表而是像图中这样被清晰地规整在处理器内存硬盘等区块之下一目了然填写体验大幅提升属性继承让配置更高效遇到的问题我们后台的类目是树状的多级结构有一些属性是非常通用的比如商品毛重它几乎适用于所有实物商品如果按照我们现有的逻辑我的运营同事需要手动地为成百上千个叶子类目都去重复地关联一次商品毛重这个属性这无疑是一场噩梦我的解决方案我会为我们的类目属性关联系统设计一个属性继承的规则规则定义任何一个子类目都会自动地继承其所有父级类目所关联的全部属性我的设计应用有了这条继承规则我的运营同事的工作就变得极其高效了他只需要将商品毛重这个通用属性关联到最顶级的一级分类如数码上那么所有属于数码下的二级分类如摄影摄像和三级分类如单反相机就都自动地无需任何操作地拥有了商品毛重这个属性最终一个单反相机类目下的商品它所需要填写的属性就等于单反相机自己关联的属性它继承自摄影摄像的属性它继承自数码的属性的总和这个属性继承的设计极大地减少了后台运营的重复配置工作量并保证了整个商品体系属性的规范性和一致性运费模板当用户在我们的电商平台下单时除了商品价格他最关心的另一个问题就是这件商品寄到我这里需要多少运费这个问题的背后对我们产品经理来说则是另一个问题商家是如何为成千上万发往全国各地的商品去设定如此复杂的运费规则的答案就是我们必须为商家设计一套功能强大体验灵活的运费模板系统什么是运费模板我给运费模板的定义是一套由商家预先设置并保存好的包含了复杂运费计算规则的配置方案它的核心价值在于一次配置多次复用商家只需要根据自己的物流合作方和商品特性创建好几套模板比如大件商品德邦模板小件商品顺丰模板就可以方便地将这些模板应用到成千上万的商品上这种一对多的设计能极大地提升商家的运营效率运费模板功能设计我设计运费模板功能主要包含两个核心模块模板的创建与管理和商品对模板的应用模块一创建与管理运费模板在商家后台我需要提供一个运费管理的模块让商家可以新建运费模板这个新建模板的表单是我设计的核心它必须包含以下配置项配置项我的设计说明模板名称一个自定义的名称方便商家自己识别比如顺丰江浙沪包邮是否包邮一个简单的单选卖家承担运费即包邮或买家承担运费如果选择包邮则下方的复杂规则可以被简化计费规则这是运费计算的基础通常分为三种按件数按重量按体积默认运费规则这是必填项用于设置一个通用的运费规则它适用于所有未被指定地区规则覆盖的地区这能有效避免因漏设地区而导致无法下单的问题指定地区运费规则这是最核心最灵活的功能我需要提供一个入口让商家可以为指定城市设置运费商家可以框选出特定的省市如江浙沪为它们设定一套独立的不同于默认规则的运费一个模板可以添加多条指定地区的规则规则详情每一条运费规则无论是默认还是指定都由首件首重和续件续重的费用构成例如件内元每增加件增加运费元所有创建好的模板都会在一个运费模板列表中进行展示商家可以在这里对已有的模板进行查看编辑和删除模块二商品关联运费模板当商家在后台创建好了运费模板最后一步就是将它应用到具体的商品上在我设计的发布编辑商品页面节中我会增加一个名为运费模板的下拉选择框这个下拉框的选项就是该商家在后台创建的所有运费模板商家只需要在这里为这件商品选择一个合适的模板那么当用户在前台下单购买这件商品时系统就会根据用户的收货地址自动地智能地匹配上运费模板中对应的规则计算出最终的精准的运费本章总结在本章我们深入到了电商后台系统的发动机舱系统性地学习和设计了整个商品管理模块这套系统的设计优劣直接决定了我们电商平台货这个核心要素的规范性丰富性和可扩展性我们的设计旅程是一次从原子到分子再到系统的构建过程解构原子我们的旅程是从解构一件商品最基本的原子开始的我们深刻地理解了一款商品与一件货品的本质区别并掌握了构成它们的三种核心商品属性关键属性销售属性其他属性搭建书架接着我们为这些原子搭建了用于收纳和组织的书架我们设计了平台的商品类目体系它就像是图书馆的分类法我们还设计了品牌管理体系它就像是出版社的陈列柜赋予智能然后我们为这套系统注入了智能通过设计类目关联和属性继承等特殊规则我们让书架和书之间产生了聪明的联动打造工具在所有底层数据模型都设计完毕后我们最终将它们组合成了一个面向商家的功能强大的生产力工具商品发布功能并为它设计了必不可少的配套功能运费模板我将本章最核心的几个概念总结在下面的表格中核心概念我的核心理解与是一款商品是一件货品这是商品数据建模的绝对核心类目品牌属性这是构成和的原材料平台必须在后台对它们进行集中统一结构化的管理关联与继承这是让后台变聪明的关键通过类目关联我们为商家提供了智能化的发布体验通过属性继承我们为运营提升了配置效率商品发布功能这是所有后台数据模型最终的应用场景一个好的发布流程能引导商家录入规范准确完整的商品数据到这里我们电商产品关于用户用户端后台平台端与商家端的设计就已经全部完成了我们已经拥有了一份足以应对复杂电商业务的完整的建筑蓝图",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-25 11:05:48",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0-%E5%95%86%E5%93%81%E7%AE%A1%E7%90%86"><span class="toc-text">第五章：电商后台 - 商品管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#5-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">5.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-2-%E5%95%86%E5%93%81%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B%E6%8E%A8%E5%AF%BC"><span class="toc-text">5.2 商品发布流程推导</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%BB%8E%E2%80%9C%E6%9C%80%E5%B0%8F%E5%95%86%E5%93%81%E6%A8%A1%E5%9E%8B%E2%80%9D%E5%BC%80%E5%A7%8B%E6%80%9D%E8%80%83"><span class="toc-text">1. 从“最小商品模型”开始思考</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E7%94%A8%E2%80%9C%E7%89%B9%E6%AE%8A%E5%9C%BA%E6%99%AF%E2%80%9D%E6%8C%91%E6%88%98%E7%AE%80%E5%8D%95%E6%A8%A1%E5%9E%8B"><span class="toc-text">2. 用“特殊场景”挑战简单模型</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E6%8E%A8%E5%AF%BC%E5%87%BA%E6%A0%B8%E5%BF%83%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B"><span class="toc-text">3. 推导出核心发布流程</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E6%B5%81%E7%A8%8B%E6%80%BB%E7%BB%93"><span class="toc-text">4. 流程总结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-3-%E5%95%86%E5%93%81%E7%B1%BB%E7%9B%AE"><span class="toc-text">5.3 商品类目</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%95%86%E5%93%81%E7%B1%BB%E7%9B%AE%E7%9A%84%E6%A0%B8%E5%BF%83%E4%BD%9C%E7%94%A8"><span class="toc-text">1. 商品类目的核心作用</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E4%BB%8E%E8%A7%92%E8%89%B2%E9%9C%80%E6%B1%82%E5%88%B0%E4%BA%A7%E5%93%81%E5%8A%9F%E8%83%BD"><span class="toc-text">2. 从角色需求到产品功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%B9%B3%E5%8F%B0%E7%AB%AF%EF%BC%9A%E7%B1%BB%E7%9B%AE%E7%AE%A1%E7%90%86%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-text">3. 平台端：类目管理功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E5%95%86%E5%AE%B6%E7%AB%AF%E4%B8%8E%E7%94%A8%E6%88%B7%E7%AB%AF%EF%BC%9A%E7%B1%BB%E7%9B%AE%E7%9A%84%E4%BD%BF%E7%94%A8"><span class="toc-text">4. 商家端与用户端：类目的使用</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-4-%E5%93%81%E7%89%8C%E7%AE%A1%E7%90%86"><span class="toc-text">5.4 品牌管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%93%81%E7%89%8C%E7%AE%A1%E7%90%86%E7%9A%84%E6%A0%B8%E5%BF%83%E4%BB%B7%E5%80%BC"><span class="toc-text">1. 品牌管理的核心价值</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%B9%B3%E5%8F%B0%E7%AB%AF%EF%BC%9A%E5%93%81%E7%89%8C%E5%BA%93%E7%AE%A1%E7%90%86%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-text">2. 平台端：品牌库管理功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E7%89%B9%E6%AE%8A%E6%B5%81%E7%A8%8B%EF%BC%9A%E8%87%AA%E4%B8%BB%E5%93%81%E7%89%8C%E5%85%A5%E9%A9%BB"><span class="toc-text">3. 特殊流程：自主品牌入驻</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-5-SKU%E4%B8%8ESPU"><span class="toc-text">5.5 SKU与SPU</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E5%AE%9A%E4%B9%89"><span class="toc-text">1. 核心定义</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-SPU%E4%B8%8ESKU%E7%9A%84%E5%85%B3%E7%B3%BB"><span class="toc-text">2. SPU与SKU的关系</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E6%8A%80%E6%9C%AF%E5%AE%9E%E7%8E%B0%E6%B5%85%E8%B0%88"><span class="toc-text">3. 技术实现浅谈</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-6-%E5%95%86%E5%93%81%E5%B1%9E%E6%80%A7"><span class="toc-text">5.6 商品属性</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%B1%9E%E6%80%A7%E7%9A%84%E5%AD%98%E5%9C%A8%E6%96%B9%E5%BC%8F%EF%BC%9A%E5%B1%9E%E6%80%A7%E5%90%8D-%E5%B1%9E%E6%80%A7%E5%80%BC"><span class="toc-text">1. 属性的存在方式：属性名 + 属性值</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%B1%9E%E6%80%A7%E7%9A%84%E5%88%86%E7%B1%BB"><span class="toc-text">2. 属性的分类</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%B1%9E%E6%80%A7%E6%B1%A0%EF%BC%9A%E5%B9%B3%E5%8F%B0%E7%AB%AF%E7%9A%84%E7%BB%9F%E4%B8%80%E7%AE%A1%E7%90%86"><span class="toc-text">3. 属性池：平台端的统一管理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-7-%E5%95%86%E5%93%81%E5%8F%91%E5%B8%83%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-text">5.7 商品发布功能设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E9%A1%B5%E9%9D%A2%E4%BF%A1%E6%81%AF%E7%BB%93%E6%9E%84"><span class="toc-text">1. 页面信息结构</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97%E8%AF%A6%E7%BB%86%E8%AE%BE%E8%AE%A1"><span class="toc-text">2. 功能模块详细设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%B8%80%EF%BC%9A%E5%A1%AB%E5%86%99%E5%95%86%E5%93%81%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AF"><span class="toc-text">模块一：填写商品基本信息</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%BA%8C%EF%BC%9A%E8%AE%BE%E7%BD%AE%E9%94%80%E5%94%AE%E5%B1%9E%E6%80%A7%E4%B8%8ESKU"><span class="toc-text">模块二：设置销售属性与SKU</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%B8%89%EF%BC%9A%E7%BC%96%E8%BE%91%E5%95%86%E5%93%81%E6%8F%8F%E8%BF%B0"><span class="toc-text">模块三：编辑商品描述</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-8-%E7%B1%BB%E7%9B%AE%E5%85%B3%E8%81%94%E7%9A%84%E7%9B%B8%E5%85%B3%E5%9C%BA%E6%99%AF"><span class="toc-text">5.8 类目关联的相关场景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E7%B1%BB%E7%9B%AE%E4%B8%8E%E5%B1%9E%E6%80%A7%E7%9A%84%E5%85%B3%E8%81%94"><span class="toc-text">1. 类目与属性的关联</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E7%B1%BB%E7%9B%AE%E4%B8%8E%E5%93%81%E7%89%8C%E7%9A%84%E5%85%B3%E8%81%94"><span class="toc-text">2. 类目与品牌的关联</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-9-%E5%B1%9E%E6%80%A7%E7%AE%A1%E7%90%86%E7%89%B9%E6%AE%8A%E8%A7%84%E5%88%99"><span class="toc-text">5.9 属性管理特殊规则</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%B1%9E%E6%80%A7%E5%88%86%E7%BB%84-%E8%AE%A9%E4%BF%A1%E6%81%AF%E6%9B%B4%E6%9C%89%E5%BA%8F"><span class="toc-text">1. 属性分组 - 让信息更有序</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%B1%9E%E6%80%A7%E7%BB%A7%E6%89%BF-%E8%AE%A9%E9%85%8D%E7%BD%AE%E6%9B%B4%E9%AB%98%E6%95%88"><span class="toc-text">2. 属性继承 - 让配置更高效</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-10-%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF"><span class="toc-text">5.10 运费模板</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%BB%80%E4%B9%88%E6%98%AF%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF%EF%BC%9F"><span class="toc-text">1. 什么是运费模板？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-text">2. 运费模板功能设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%B8%80%EF%BC%9A%E5%88%9B%E5%BB%BA%E4%B8%8E%E7%AE%A1%E7%90%86%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF"><span class="toc-text">模块一：创建与管理运费模板</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%BA%8C%EF%BC%9A%E5%95%86%E5%93%81%E5%85%B3%E8%81%94%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF"><span class="toc-text">模块二：商品关联运费模板</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-11-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">5.11 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理进阶（五）：第五章：电商后台 - 商品管理</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-24T12:13:45.000Z" title="发表于 2025-07-24 20:13:45">2025-07-24</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.644Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">9.7k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>28分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理进阶（五）：第五章：电商后台 - 商品管理"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/4512.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/4512.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理进阶（五）：第五章：电商后台 - 商品管理</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-24T12:13:45.000Z" title="发表于 2025-07-24 20:13:45">2025-07-24</time><time itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.644Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></header><div id="postchat_postcontent"><h1 id="第五章：电商后台-商品管理"><a href="#第五章：电商后台-商品管理" class="headerlink" title="第五章：电商后台 - 商品管理"></a>第五章：电商后台 - 商品管理</h1><p>在上一章，我们已经设计好了商家入驻的流程，让第一批商家成功进入了我们的平台。现在，他们最迫切的需求就是：<strong>我应该如何，把我的商品，发布到平台上进行售卖？</strong></p><p>本章，我们就将为商家，以及我们自己平台的运营，设计一套完整、专业、可扩展的商品管理系统。</p><h2 id="5-1-学习目标"><a href="#5-1-学习目标" class="headerlink" title="5.1 学习目标"></a>5.1 学习目标</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722174132300.png" alt="image-20250722174132300"></p><p>在本章中，我的核心目标是，带大家掌握电商后台商品管理模块的完整设计。我们将首先从宏观上，<strong>推导出商品从“录入”到“呈现”的核心业务流程</strong>，然后再深入到微观，学习<strong>SPU/SKU、类目、属性</strong>等构建商品体系的“原子”概念，并最终将它们组合成一个完整的“<strong>商品发布功能</strong>”。</p><hr><h2 id="5-2-商品发布流程推导"><a href="#5-2-商品发布流程推导" class="headerlink" title="5.2 商品发布流程推导"></a>5.2 商品发布流程推导</h2><p>在我动手设计“商品发布”这个后台功能之前，我一定会先将支撑这个功能的<strong>端到端业务流程</strong>，梳理得一清二楚。</p><h3 id="1-从“最小商品模型”开始思考"><a href="#1-从“最小商品模型”开始思考" class="headerlink" title="1. 从“最小商品模型”开始思考"></a>1. 从“最小商品模型”开始思考</h3><p>我的思考，会先从“终局”出发，即，从一个<strong>普通用户</strong>的视角来看：<strong>要让我能看懂一件商品，至少需要呈现哪些信息？</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180034500.png" alt="image-20250722180034500"></p><p>这个问题的答案，构成了我们电商系统的“<strong>最小商品模型</strong>”。它至少需要包含以下五个核心信息：</p><ol><li><strong>标题</strong>：商品叫什么名字。</li><li><strong>图片</strong>：商品长什么样。</li><li><strong>价格</strong>：商品卖多少钱。</li><li><strong>库存</strong>：商品还有没有货。</li><li><strong>描述</strong>：商品的详细介绍。</li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180117296.png" alt="image-20250722180117296"></p><h3 id="2-用“特殊场景”挑战简单模型"><a href="#2-用“特殊场景”挑战简单模型" class="headerlink" title="2. 用“特殊场景”挑战简单模型"></a>2. 用“特殊场景”挑战简单模型</h3><p>那么，基于这个最小模型，一个最简单的设计思路就是：我只需要给商家提供一个包含这五个字段的表单，让他填完提交，不就可以了吗？</p><p><strong>这样够吗？</strong><br>当我把这个简单的模型，放入真实的、复杂的电商场景中去检验时，会立刻发现它的不足。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180223346.png" alt="image-20250722180223346"></p><p>我必须考虑到以下这些“<strong>特殊场景</strong>”：</p><ul><li><strong>分类场景</strong>：任何一件商品，都需要被归属到一个明确的“<strong>商品分类</strong>”下（如：电脑/办公 -&gt; 电脑组件 -&gt; 硬盘），这样用户才能通过分类导航找到它。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180312976.png" alt="image-20250722180312976"></p><ul><li><strong>品牌场景</strong>：用户也常常会通过“<strong>品牌</strong>”的维度来找商品（如：联想品牌馆）。因此，商品也需要和品牌进行关联。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180255028.png" alt="image-20250722180255028"></p><ul><li><strong>属性场景</strong>：不同品类的商品，其需要展示的“<strong>商品参数</strong>”是完全不同的。比如，硬盘需要展示<code>容量</code>、<code>接口</code>等参数；而一件衣服，则需要展示<code>材质</code>、<code>适用季节</code>等参数。一个固定的、简单的表单，是无法满足这种多样性的。</li></ul><h3 id="3-推导出核心发布流程"><a href="#3-推导出核心发布流程" class="headerlink" title="3. 推导出核心发布流程"></a>3. 推导出核心发布流程</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180337574.png" alt="image-20250722180337574"></p><p>为了解决上述所有特殊场景带来的问题，我推导出的、一个专业的“<strong>商家发布商品核心流程</strong>”，必须是一个<strong>结构化的、分步骤</strong>的流程：</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180649979.png" alt="image-20250722180649979"></p><ol><li><p><strong>第一步：选择商品类目</strong><br>这是商家发布商品的<strong>起点</strong>。我会让商家，先从我们后台预设的“商品类目”树中，精准地选择他将要发布的商品，属于哪一个最细分的叶子类目。</p><ul><li><strong>我的设计思考</strong>：这一步至关重要，因为<strong>用户选择的类目，将直接决定了下一步他需要填写的“商品属性”</strong>。</li></ul></li><li><p><strong>第二步：选择商品品牌</strong><br>在确定了类目后，商家需要选择该商品所属的“品牌”。</p></li><li><p><strong>第三步：设置商品信息</strong><br>只有在完成了前两步之后，系统才会展示出最终的“商品信息设置”页面。这个页面，除了包含我们前面提到的“最小商品模型”（标题、价格、图片、库存、描述）的填写区域外，还会根据第一步选择的类目，<strong>动态地</strong>加载出该类目专属的“<strong>商品属性</strong>”填写项。</p></li></ol><h3 id="4-流程总结"><a href="#4-流程总结" class="headerlink" title="4. 流程总结"></a>4. 流程总结</h3><p>至此，我们就完成了一次完整的流程推导。这个“<strong>先选类目 -&gt; 再选品牌 -&gt; 最后填写信息</strong>”的三步走流程，就是我为商家设计的、既能满足复杂场景，又能保证后台数据结构化、规范化的核心解决方案。</p><p>这个流程，完美地嵌入到了我们之前梳理的“<strong>商家录入 -&gt; 平台审核 -&gt; 用户浏览</strong>”的宏观业务流程中，构成了其中“<strong>商家录入</strong>”这一环节的具体实现。</p><hr><h2 id="5-3-商品类目"><a href="#5-3-商品类目" class="headerlink" title="5.3 商品类目"></a>5.3 商品类目</h2><p>在我推导出的“<strong>先选类目 -&gt; 再选品牌 -&gt; 最后填写信息</strong>”的商品发布流程中，“<strong>选择商品类目</strong>”是所有流程的起点。</p><p>因此，<strong>商品类目</strong>体系，是我在设计商品管理后台时，第一个要搭建的、也是最底层的“<strong>地基</strong>”。一个清晰、稳定、可扩展的类目体系，是整个电商平台有序运转的保障。</p><h3 id="1-商品类目的核心作用"><a href="#1-商品类目的核心作用" class="headerlink" title="1. 商品类目的核心作用"></a>1. 商品类目的核心作用</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180926020.png" alt="image-20250722180926020"></p><p>我设计商品类目体系，是因为它同时为我们生态中的三大核心角色，都提供了不可或缺的价值。</p><table><thead><tr><th align="left">角色</th><th align="left">核心作用</th><th align="left">我的解读</th></tr></thead><tbody><tr><td align="left"><strong>平台</strong></td><td align="left"><strong>确定服务范围，进行监管</strong></td><td align="left">我后台设置的类目，直接定义了“<strong>我们平台允许卖什么，不允许卖什么</strong>”。这是我进行平台治理、控制风险、明确业务边界的根本。</td></tr><tr><td align="left"><strong>商家</strong></td><td align="left"><strong>分门别类，便于管理</strong></td><td align="left">我为商家提供了一套标准化的“<strong>商品货架</strong>”。商家只需要按照我的类目规范，将商品“上架”到对应的位置，就能实现规范化的管理。</td></tr><tr><td align="left"><strong>用户</strong></td><td align="left"><strong>方便查找商品</strong></td><td align="left">我为用户提供了一套清晰的“<strong>商场导览图</strong>”。用户可以通过分类导航，快速地找到自己想要逛的“区域”，极大地提升了购物效率。</td></tr></tbody></table><h3 id="2-从角色需求到产品功能"><a href="#2-从角色需求到产品功能" class="headerlink" title="2. 从角色需求到产品功能"></a>2. 从角色需求到产品功能</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181002478.png" alt="image-20250722181002478"></p><p>基于上述的作用，我可以清晰地提炼出不同角色的产品需求，并推导出我们需要提供的核心功能。</p><p>这个推导的结论非常清晰，也是我设计的核心原则：“<strong>平台来管理类目，商家使用类目</strong>”。</p><ul><li><strong>平台的需求</strong>是“限定范围、监管”，这要求我必须设计一套强大的“<strong>后台类目管理</strong>”功能。</li><li><strong>商家的需求</strong>是“加载类目模板、方便操作”，这要求我必须在“<strong>商品发布</strong>”流程中，提供一个易用的“<strong>类目选择</strong>”功能。</li></ul><h3 id="3-平台端：类目管理功能设计"><a href="#3-平台端：类目管理功能设计" class="headerlink" title="3. 平台端：类目管理功能设计"></a>3. 平台端：类目管理功能设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181719285.png" alt="image-20250722181719285"></p><p>这是我为平台运营人员设计的“<strong>类目配置中心</strong>”。</p><ul><li><strong>多级类目结构</strong>：我设计的后台，必须支持<strong>多级类目</strong>的创建和管理。如案例图所示，一个“笔记本”商品，它的类目层级可能是“数码家电（一级）” -&gt; “家电（二级）” -&gt; “笔记本（三级）”。</li><li><strong>基础管理操作</strong>：后台必须提供对每一级类目的<strong>新增、编辑、删除、查询</strong>等基础操作。</li><li><strong>我的拓展设计（属性与品牌关联）</strong>：这是后台类目管理最核心的、也是最高阶的功能。在运营人员新增或编辑一个“叶子类目”（即，不能再往下分的最后一级类目，如“笔记本”）时，我设计的后台，<strong>必须允许他，将这个类目，与一组特定的“商品属性”和“品牌”进行关联</strong>。<ul><li>例如，在配置“笔记本”这个类目时，运营就要为它关联上“屏幕尺寸”、“内存”、“CPU型号”等<strong>属性</strong>，并关联上“联想”、“华为”、“苹果”等<strong>品牌</strong>。</li></ul></li></ul><h3 id="4-商家端与用户端：类目的使用"><a href="#4-商家端与用户端：类目的使用" class="headerlink" title="4. 商家端与用户端：类目的使用"></a>4. 商家端与用户端：类目的使用</h3><p>我们平台运营在后台辛辛苦苦搭建好的这套类目体系，最终会在商家端和用户端，被“使用”和“呈现”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181801927.png" alt="image-20250722181801927"></p><ul><li><strong>在商家端</strong>：当商家发布新商品时，我们流程的<strong>第一步</strong>，就是让他从我们后台预设好的类目树中，选择一个。当他选择了“笔记本”之后，系统就会因为我们后台的“关联”配置，而<strong>动态地</strong>为他加载出需要填写的“屏幕尺寸”、“内存”等属性。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181837353.png" alt="image-20250722181837353"></p><ul><li><strong>在用户端</strong>：用户则会在我们App的“<strong>分类</strong>”Tab页，看到我们后台配置好的类目树结构，并可以逐级点击，进行商品的浏览和筛选。</li></ul><p>一个后台设计得清晰、合理的类目体系，是前台商家发布体验流畅、用户浏览体验清晰的根本保障。</p><hr><h2 id="5-4-品牌管理"><a href="#5-4-品牌管理" class="headerlink" title="5.4 品牌管理"></a>5.4 品牌管理</h2><p>在我看来，如果说“类目”是商品的<strong>物理属性</strong>分类，那么“<strong>品牌</strong>”就是商品的<strong>心智属性</strong>分类。</p><p>用户在购买决策时，品牌是影响他们信任和选择的、极其重要的一个因素。</p><p>因此，我必须在后台，建立一套完善的、由平台强管控的品牌管理体系。</p><h3 id="1-品牌管理的核心价值"><a href="#1-品牌管理的核心价值" class="headerlink" title="1. 品牌管理的核心价值"></a>1. 品牌管理的核心价值</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213811999.png" alt="image-20250722213811999"></p><p>我设计品牌管理系统，同样是为了服务好我们生态中的三大核心角色。</p><table><thead><tr><th align="left"><strong>角色</strong></th><th align="left"><strong>核心需求</strong></th><th align="left"><strong>我需要提供的产品能力</strong></th></tr></thead><tbody><tr><td align="left"><strong>平台</strong></td><td align="left"><strong>规避假冒伪劣、山寨产品</strong>，保证平台的商品品质和声誉。</td><td align="left">必须建立一套<strong>品牌的审核与认证机制</strong>，确保只有合规的、真实的品牌才能在平台上被售卖。</td></tr><tr><td align="left"><strong>商家</strong></td><td align="left">能够清晰地标明自己所售卖商品的<strong>品牌归属</strong>。</td><td align="left">我需要在商品发布流程中，为商家提供一个清晰、准确的<strong>品牌选择器</strong>。</td></tr><tr><td align="left"><strong>用户</strong></td><td align="left"><strong>“我只想看某个品牌的商品”</strong>，能够通过品牌维度，快速找到自己想要的商品。</td><td align="left">我需要在用户端，提供<strong>按品牌进行搜索和筛选</strong>的功能。</td></tr></tbody></table><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213850767.png" alt="image-20250722213850767"></p><p>基于上述的需求，我推导出的核心功能是：</p><ul><li><strong>平台端</strong>：必须有后台<strong>品牌管理</strong>（增删改查）功能。</li><li><strong>商家端</strong>：必须在商品发布时，有<strong>品牌选择</strong>功能。</li><li><strong>用户端</strong>：必须有<strong>品牌搜索/筛选</strong>功能。</li></ul><h3 id="2-平台端：品牌库管理功能设计"><a href="#2-平台端：品牌库管理功能设计" class="headerlink" title="2. 平台端：品牌库管理功能设计"></a>2. 平台端：品牌库管理功能设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213944716.png" alt="image-20250722213944716"></p><p>所有品牌功能的核心，在于我们平台运营后台，必须建立一个“<strong>品牌库（Brand Library）</strong>”。这个品牌库，是由我们<strong>平台统一进行维护</strong>的，它是我们平台上所有“合法品牌”的唯一真实来源。</p><p>我设计的品牌库后台，主要包含以下功能：</p><ul><li><strong>品牌信息字段</strong>：在新增一个品牌时，运营需要填写该品牌的<code>Logo</code>、<code>中文名</code>、<code>英文名</code>、<code>简介</code>等信息。</li><li><strong>基础管理功能</strong>：运营可以对品牌库中的品牌，进行常规的<strong>新增、编辑、查询</strong>操作。</li><li><strong>状态管理</strong>：每个品牌，都有“<strong>启用/停用</strong>”两种状态。当某个品牌出现问题（如：被曝出重大质量问题、品牌方与我们合作终止）时，运营可以将其状态，设置为“停用”。设置为“停用”后，商家在发布商品时，就无法再选择这个品牌了。</li></ul><h3 id="3-特殊流程：自主品牌入驻"><a href="#3-特殊流程：自主品牌入驻" class="headerlink" title="3. 特殊流程：自主品牌入驻"></a>3. 特殊流程：自主品牌入驻</h3><p>这时，一个非常常见的业务场景就出现了：<strong>如果一个商家，想售卖一个我们品牌库里，还没有收录的新品牌，怎么办？</strong></p><p>我不能让商家随意地、手动地填写品牌名称，这会导致品牌库数据混乱，出现大量山寨和无效品牌。因此，我必须设计一套严谨的“<strong>新品牌入驻审核</strong>”流程。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214029207.png" alt="image-20250722214029207"></p><p>我的设计流程如下：</p><ol><li><strong>商家提交申请</strong>：在商家后台，我会提供一个“<strong>新增品牌申请</strong>”的入口。商家需要在这个页面，填写新品牌的基础信息，并**必须上传该品牌的《商标注册证》**作为资质证明。</li><li><strong>平台审核</strong>：商家的申请，会进入我们平台运营后台的“品牌审核”列表中。运营同事的核心工作，是<strong>核实《商标注册证》的真实性</strong>和有效性。</li><li><strong>品牌入库</strong>：审核通过后，运营同事，会将这个新品牌的信息，正式录入到我们的“品牌库”中，并设置为“启用”状态。</li><li><strong>商家选用</strong>：一旦品牌成功入库，商家（以及其他所有获得了该品牌授权的商家），就可以在发布商品时，从品牌选择器中，选择这个新的品牌了。</li></ol><p>通过这套流程，我既满足了商家引入新品牌的需求，又确保了平台对所有品牌的“强管控”，保证了我们电商品牌生态的健康。</p><hr><h2 id="5-5-SKU与SPU"><a href="#5-5-SKU与SPU" class="headerlink" title="5.5 SKU与SPU"></a>5.5 SKU与SPU</h2><p>在我设计任何电商后台的商品系统时，我的第一个思考，就是要清晰地定义<strong>SPU</strong>和<strong>SKU</strong>。这两个概念，是整个商品世界的“<strong>基本粒子</strong>”，理解了它们，就理解了所有复杂商品体系的构成。</p><h3 id="1-核心定义"><a href="#1-核心定义" class="headerlink" title="1. 核心定义"></a>1. 核心定义</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214844754.png" alt="image-20250722214844754"></p><ul><li><p><strong>SPU - 标准产品单元 (Standard Product Unit)</strong><br>我把它理解为“<strong>一款商品</strong>”。它是一组具有共同的、标准化的核心属性的商品的集合，是商品信息聚合的最小单位。</p><ul><li><strong>例如</strong>：“iPhone 11”就是一个SPU。它代表了“iPhone 11”这个产品系列，与它的颜色、内存大小无关。我们通常用SPU，来做商品的通用性描述、展示和搜索。</li></ul></li><li><p><strong>SKU - 库存量单位 (Stock Keeping Unit)</strong><br>我把它理解为“<strong>一件货品</strong>”。它是库存控制的最小可用单位，是真正物理存在的、可以被用户购买的最小单元。</p><ul><li><strong>例如</strong>：“一台白色的、内存为64G的iPhone 11”，就是一个SKU。“一台红色的、内存为128G的iPhone 11”，则是另一个完全不同的SKU。<strong>每一个SKU，都有自己独立的库存和价格</strong>。</li></ul></li></ul><h3 id="2-SPU与SKU的关系"><a href="#2-SPU与SKU的关系" class="headerlink" title="2. SPU与SKU的关系"></a>2. SPU与SKU的关系</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214946907.png" alt="image-20250722214946907"></p><p>SPU和SKU之间，是一个“<strong>一对多</strong>”的层级关系。一个SPU，通过不同的“<strong>销售属性</strong>”的组合，可以衍生出多个不同的SKU。</p><p><strong>销售属性</strong>，就是那些能影响到商品最终售价和库存的属性，比如<code>颜色</code>、<code>尺码</code>、<code>内存大小</code>、<code>套餐类型</code>等。</p><p>所以，它们之间的关系公式是：</p><blockquote><p><strong>SKU = SPU + 一组确定的销售属性</strong></p></blockquote><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215055577.png" alt="image-20250722215055577"></p><p>我们在京东看到的这个iPhone 11商品详情页，就是一个完美的现实案例。</p><ul><li><strong>SPU</strong>：是“Apple iPhone 11”这个商品本身。</li><li><strong>销售属性</strong>：是“选择颜色”和“选择版本”这两个维度。</li><li><strong>SKU</strong>：当用户选择了“黑色”和“128GB”之后，页面上展示的特定价格<code>¥5269.00</code>和库存状态，就是属于这个唯一SKU的。</li></ul><h3 id="3-技术实现浅谈"><a href="#3-技术实现浅谈" class="headerlink" title="3. 技术实现浅谈"></a>3. 技术实现浅谈</h3><p>那么，为了实现这套逻辑，我作为产品经理，需要如何与我的研发同学沟通呢？我需要向他们讲清楚<strong>后端的数据模型</strong>和<strong>前端的交互逻辑</strong>。</p><ul><li><strong>后端设计（数据模型）</strong><br>在后台数据库中，我们至少需要设计几张表，来清晰地表达SPU和SKU的关系。</li></ul><table><thead><tr><th align="left"><strong>数据表</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><strong>SPU表 (spu_table)</strong></td><td align="left">这张表，用来存放“iPhone 11”这个SPU的通用信息，比如<code>商品名称</code>、<code>商品描述</code>、<code>品牌</code>、<code>类目</code>等。</td></tr><tr><td align="left"><strong>销售属性名表 (attribute_name_table)</strong></td><td align="left">这张表，用来定义SPU有哪些销售属性。比如，它会记录：“iPhone 11”这个SPU，有“颜色”和“内存”这两个销售属性。</td></tr><tr><td align="left"><strong>销售属性值表 (attribute_value_table)</strong></td><td align="left">这张表，用来定义每个销售属性有哪些可选值。比如，它会记录：“颜色”这个属性，有“黑色”、“白色”、“红色”等可选值。</td></tr><tr><td align="left"><strong>SKU表 (sku_table)</strong></td><td align="left"><strong>这是最核心的表</strong>。它用来存放每一个具体的“货品”。比如，“iPhone 11 黑色 128GB”就是这张表里的一行记录。这行记录里，会包含它<strong>自己专属的<code>价格</code>、<code>库存</code></strong>，并会关联到它的父级SPU，以及它所对应的属性值（“黑色”、“128GB”）。</td></tr></tbody></table><ul><li><strong>前端设计（交互逻辑）</strong><br>当用户打开一个商品详情页时，前端与后端的交互流程是这样的：<ol><li>前端App向后端服务器，发送一个请求，告诉它：“我需要SPU ID为‘iPhone 11’的商品数据”。</li><li>后端服务器收到请求后，会把<strong>SPU表</strong>里的通用信息（描述、主图等），以及<strong>与这个SPU关联的所有SKU表里的记录</strong>（比如：黑色64G的价格/库存、白色64G的价格/库存、黑色128G的价格/库存……），一次性地，全部返回给前端App。</li><li>前端App拿到这些数据后，就会在页面上，<strong>动态地渲染</strong>出“颜色”和“内存”这两个维度的、所有可点击的<strong>选择按钮</strong>。</li><li>当用户点击“黑色”和“128GB”这两个按钮时，<strong>前端App会直接在本地已经拿到的数据中，查找到对应的那个SKU</strong>，然后<strong>瞬间</strong>将页面上的价格和库存，更新为这个SKU专属的价格和库存。这个过程，通常<strong>不需要再次请求后端服务器</strong>，因此用户会感觉体验非常流畅。</li></ol></li></ul><hr><h2 id="5-6-商品属性"><a href="#5-6-商品属性" class="headerlink" title="5.6 商品属性"></a>5.6 商品属性</h2><p>在上一节，我们学习了SPU和SKU。我们知道，一个“黑色、128G的iPhone 13”是一个SKU。但这立刻引出了一个核心问题：系统是怎么知道“iPhone 13”会有“颜色”和“内存”这两个选项的？商家在发布商品时，并不是随意填写这些信息的。</p><p>这背后的答案，就是我们这一节要学习的，一套由平台预先定义好的、结构化的数据体系——<strong>商品属性</strong>。</p><h3 id="1-属性的存在方式：属性名-属性值"><a href="#1-属性的存在方式：属性名-属性值" class="headerlink" title="1. 属性的存在方式：属性名 + 属性值"></a>1. 属性的存在方式：属性名 + 属性值</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215725696.png" alt="image-20250722215725696"></p><p>首先，我们要明确一个“属性”最基本的构成。任何一个属性，都是由一个“<strong>属性名</strong>”和一个“<strong>属性值</strong>”配对组成的。</p><ul><li><strong>属性名</strong>：相当于“问题”，比如：<code>型号</code>、<code>颜色</code>、<code>传输速度</code>。</li><li><strong>属性值</strong>：相当于“答案”，比如：<code>CR111</code>、<code>黑色</code>、<code>1000Mbps</code>。</li></ul><p>我设计的整个商品信息体系，就是由成千上万个这样的“<strong>键值对</strong>”构成的。</p><h3 id="2-属性的分类"><a href="#2-属性的分类" class="headerlink" title="2. 属性的分类"></a>2. 属性的分类</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215842159.png" alt="image-20250722215842159"></p><p>为了让这成千上万的属性，能够被系统有序地管理和使用，我必须对它们进行<strong>分类</strong>。在我的电商产品设计中，我会严格地将所有属性，划分为以下三种类型：</p><table><thead><tr><th align="left">属性分类</th><th align="left">简单来说</th><th align="left">案例（以“iPhone 13”为例）</th></tr></thead><tbody><tr><td align="left"><strong>关键属性</strong></td><td align="left">就像商品的“身份证”，<strong>确定是哪一款商品</strong>，不会变，也不能选。</td><td align="left">“iPhone 13”这个名字本身，以及它的<strong>具体型号</strong>（比如A2634）。</td></tr><tr><td align="left"><strong>销售属性</strong></td><td align="left">决定你<strong>最终买哪个具体商品</strong>，你<strong>必须选</strong>，选了之后<strong>价格或库存可能就不同</strong>。</td><td align="left"><strong>颜色</strong>（星光色、午夜色等）；<strong>存储空间</strong>（128GB、256GB等）。</td></tr><tr><td align="left"><strong>其他属性</strong></td><td align="left">就是商品的<strong>各种特点介绍</strong>，<strong>看看就行，不能选</strong>，也不影响价格。</td><td align="left">处理器型号 (A15)、屏幕是OLED、防水级别是IP68等等。</td></tr></tbody></table><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220341997.png" alt="image-20250722220341997"></p><p>我们可以通过这张网卡的案例图，清晰地看到这三类属性，在真实商品详情页上的分布。</p><h3 id="3-属性池：平台端的统一管理"><a href="#3-属性池：平台端的统一管理" class="headerlink" title="3. 属性池：平台端的统一管理"></a>3. 属性池：平台端的统一管理</h3><p>下一个关键问题是：这么多属性名（如<code>颜色</code>、<code>尺寸</code>、<code>CPU型号</code>），它们是从哪里来的？</p><p>为了保证数据的规范和统一（比如，避免商家A填写“颜色”，商家B填写“色彩”）</p><p>我必须在平台运营后台，建立一个<strong>由平台统一管理</strong>的<code>属性池</code>。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220452045.png" alt="image-20250722220452045"></p><p>“属性池”，是我为平台运营人员设计的、一个用来<strong>集中管理所有“属性名”<strong>的后台功能。在这里，运营同事可以对平台中可能出现的所有属性名，进行</strong>增、删、改、查</strong>。</p><ul><li><p><strong>我的拓展设计（属性与类目的关联）</strong><br>这个“属性池”并不是孤立存在的。它设计的精髓，在于和我们<code>5.3</code>节学习的“<strong>商品类目</strong>”进行<strong>深度绑定</strong>。</p><p>在我设计的“<strong>类目管理</strong>”后台，当运营人员在编辑“笔记本电脑”这个类目时，他就可以从“属性池”中，为这个类目，勾选上它应该具备的属性，比如<code>CPU型号</code>、<code>内存容量</code>、<code>屏幕尺寸</code>、<code>硬盘容量</code>等。</p><p>这样一来，当商家在发布商品、第一步选择了“笔记本电脑”这个类目后，系统就会自动地、智能地，为他加载出需要填写的<code>CPU型号</code>、<code>内存容量</code>等属性，从而实现了整个商品发布流程的结构化和智能化。</p></li></ul><hr><h2 id="5-7-商品发布功能设计"><a href="#5-7-商品发布功能设计" class="headerlink" title="5.7 商品发布功能设计"></a>5.7 商品发布功能设计</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220910285.png" alt="image-20250722220910285"></p><p>在前面的小节中，我们已经将“商品”这个复杂的概念，拆解为了<code>类目</code>、<code>品牌</code>、<code>SPU</code>、<code>SKU</code>、<code>属性</code>等一系列结构化的“原子”部件。</p><p>现在，我们的任务，就是<strong>将这些“原子”部件，重新组合起来</strong>，为我们的商家，设计一个功能强大、体验流畅的“<strong>商品发布</strong>”功能。这个功能，就是商家后台的“<strong>核心生产力工具</strong>”。</p><h3 id="1-页面信息结构"><a href="#1-页面信息结构" class="headerlink" title="1. 页面信息结构"></a>1. 页面信息结构</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220935810.png" alt="image-20250722220935810"></p><p>在我设计这个复杂的“发布商品”页面时，我首先会对需要商家填写的<strong>信息</strong>，进行一次高层级的<strong>结构化分类</strong>。我会把整个页面，划分为三大信息模块：</p><ol><li><strong>商品的基本信息</strong>：即SPU层级的通用信息，如商品名称、图片等。</li><li><strong>商品的属性</strong>：包括决定SKU的销售属性，以及其他描述性属性。</li><li><strong>商品的详情</strong>：即图文并茂的、用于营销的“长图文”描述。</li></ol><h3 id="2-功能模块详细设计"><a href="#2-功能模块详细设计" class="headerlink" title="2. 功能模块详细设计"></a>2. 功能模块详细设计</h3><p>现在，我们来逐一设计承载这三类信息的功能模块。</p><h4 id="模块一：填写商品基本信息"><a href="#模块一：填写商品基本信息" class="headerlink" title="模块一：填写商品基本信息"></a><strong>模块一：填写商品基本信息</strong></h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221017448.png" alt="image-20250722221017448"></p><p>这是商家进入发布流程后，看到的第一个表单区域。它用来采集这件商品（SPU）最基础的信息。</p><table><thead><tr><th align="left"><strong>字段</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><strong>商品分类</strong></td><td align="left">这个字段通常是<strong>只读</strong>的，它会显示商家在上一个步骤（<code>5.2</code>节推导的流程）中所选择的类目。</td></tr><tr><td align="left"><strong>商品名称</strong></td><td align="left">文本输入框，用于填写SPU的标题。</td></tr><tr><td align="left"><strong>商品品牌</strong></td><td align="left">一个<strong>下拉选择框</strong>。我设计的逻辑是：这个下拉框里，只会出现我们后台<strong>与该“商品分类”相关联</strong>的品牌，而不是全部的品牌。这是一种智能化的设计。</td></tr><tr><td align="left"><strong>商品价格</strong></td><td align="left">商家可以填写商品的“市场价”或“划线价”。每个SKU的具体售价，会在下一步设置。</td></tr><tr><td align="left"><strong>商品展示图</strong></td><td align="left">一个图片上传控件。我会明确地标注出，<strong>最多可以上传几张</strong>，以及推荐的<strong>尺寸和格式</strong>，以保证前端展示效果的统一。</td></tr></tbody></table><h4 id="模块二：设置销售属性与SKU"><a href="#模块二：设置销售属性与SKU" class="headerlink" title="模块二：设置销售属性与SKU"></a><strong>模块二：设置销售属性与SKU</strong></h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221242154.png" alt="image-20250722221242154"></p><p>这是整个商品发布功能中，<strong>技术和交互最复杂，但也最核心</strong>的一个模块。我把它分为两步：</p><p><strong>第一步：定义销售属性</strong><br>我会提供一个交互区域，让商家可以为他的商品，添加“<strong>销售属性</strong>”。</p><ul><li><strong>属性名</strong>：通过一个下拉框，让商家从该类目下，我们预设好的属性（如<code>颜色</code>、<code>尺寸</code>）中进行选择。</li><li><strong>属性值</strong>：在选定了属性名后，商家可以手动地，添加多个属性值（如<code>红色</code>、<code>蓝色</code>；<code>S</code>、<code>M</code>、<code>L</code>）。</li></ul><p><strong>第二步：生成SKU并填写明细</strong><br>当商家定义好所有的销售属性和属性值后，我设计的后台，最智能的地方就体现出来了：<strong>系统会自动地，将这些属性值进行“笛卡尔积”组合，生成一个完整的SKU列表</strong>。</p><p>商家的工作，不是去手动组合SKU，而是在这个自动生成的表格里，“<strong>做填空题</strong>”。他只需要为每一个SKU，填写它专属的<code>销售价格</code>和<code>销售库存</code>即可。这个设计，极大地降低了商家的操作复杂度和出错率。</p><h4 id="模块三：编辑商品描述"><a href="#模块三：编辑商品描述" class="headerlink" title="模块三：编辑商品描述"></a><strong>模块三：编辑商品描述</strong></h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221359012.png" alt="image-20250722221359012"></p><p>这是页面的最后一个模块，用于上传商品的“长图文”详情。</p><ul><li><strong>我的拓展设计（分端描述）</strong>：为了追求极致的用户体验，一个专业的电商后台，应该允许商家，为<strong>PC端</strong>和<strong>移动端</strong>，分别上传和编辑<strong>两套不同</strong>的商品描述。</li><li><strong>为什么？</strong> 因为PC端屏幕大，可以展示更丰富、更复杂的图文内容；而移动端屏幕小，则需要更简洁、加载速度更快的图片和文字。提供两个独立的“<strong>富文本编辑器</strong>”，能让有能力的商家，为不同设备的用户，提供最优的浏览体验。</li></ul><p>通过将这三大模块，有机地组合在一个页面中，我们就为商家，提供了一个功能强大、逻辑清晰、体验智能的商品发布功能。</p><hr><h2 id="5-8-类目关联的相关场景"><a href="#5-8-类目关联的相关场景" class="headerlink" title="5.8 类目关联的相关场景"></a>5.8 类目关联的相关场景</h2><p>在前面的小节中，我们已经独立地设计了<code>商品类目</code>、<code>品牌</code>和<code>商品属性</code>这三个核心的数据模块。但如果它们只是三个孤立的列表，那我们的后台依然是“<strong>笨拙</strong>”的。</p><p>一个智能的后台，必须能理解这三者之间的<strong>内在关联</strong>。本节，我们就来设计这套“<strong>关联系统</strong>”。</p><h3 id="1-类目与属性的关联"><a href="#1-类目与属性的关联" class="headerlink" title="1. 类目与属性的关联"></a>1. 类目与属性的关联</h3><p>我们首先思考一个场景：我们的“<strong>属性池</strong>”里，包含了<code>颜色</code>、<code>尺码</code>，也包含了<code>CPU型号</code>、<code>屏幕尺寸</code>。当一个商家来发布一件“T恤”时，如果我们在“设置属性”的环节，把所有这些属性都展示给他，那将是一场灾难。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222404342.png" alt="image-20250722222404342"></p><p><strong>我的解决方案</strong>：我必须为商品属性，打上“<strong>类目</strong>”的烙印，即创建“<strong>类目属性</strong>”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222453447.png" alt="image-20250722222453447"></p><p><strong>我的设计思路如下：</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222550441.png" alt="image-20250722222550441"></p><ol><li><p><strong>在平台端（后台）进行关联</strong>：<br>在我设计的“<strong>类目管理</strong>”后台，当运营同事在编辑一个“叶子类目”（如：“T恤”）时，我必须提供一个功能，让他可以从“属性池”中，<strong>勾选</strong>出所有与“T恤”相关的属性（如：<code>颜色</code>、<code>尺码</code>、<code>材质</code>、<code>适用季节</code>），并将它们<strong>与“T恤”这个类目进行绑定</strong>。</p></li><li><p><strong>在商家端（后台）智能调用</strong>：<br>经过了后台的“绑定”操作后，商家在发布商品时，当他在第一步选择了“T恤”这个类目，那么在后续的“设置商品属性”环节，系统就会<strong>只加载并显示</strong>出<code>颜色</code>、<code>尺码</code>、<code>材质</code>等这几个已经绑定好的属性，供他填写。</p></li><li><p><strong>在用户端（前台）精准呈现</strong>：<br>这个设计，最终会惠及我们的用户。当用户在前台浏览“T恤”这个分类列表时，页面左侧的“<strong>筛选器</strong>”，也同样只会展示出<code>颜色</code>、<code>尺码</code>、<code>材质</code>等这些与T恤强相关的、有意义的筛选条件。</p></li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222826573.png" alt="image-20250722222826573"></p><h3 id="2-类目与品牌的关联"><a href="#2-类目与品牌的关联" class="headerlink" title="2. 类目与品牌的关联"></a>2. 类目与品牌的关联</h3><p>同样的逻辑，也完全适用于<strong>品牌管理</strong>。当商家发布一件“NIKE”的T恤时，如果让他从一个包含“海尔”、“华为”等上千个品牌的总列表里去寻找，体验会非常糟糕。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222903391.png" alt="image-20250722222903391"></p><p><strong>我的解决方案</strong>：我同样需要在后台，建立<strong>品牌与类目的关联</strong>。这样做的好处是：</p><ul><li>提升商家发布商品的<strong>便捷性</strong>，避免出错。</li><li>让我们的品牌管理更<strong>标准化</strong>。</li><li>让用户在前台按分类+品牌进行<strong>筛选时，速度更快</strong>。</li></ul><p><strong>我的设计思路如下：</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222919119.png" alt="image-20250722222919119"></p><ol><li><p><strong>在平台端（后台）进行关联</strong>：<br>在“<strong>类目管理</strong>”后台，当运营编辑“T恤”这个类目时，除了关联属性，他还需要<strong>关联品牌</strong>。他会从“品牌库”中，勾选出所有属于“服饰”类目的品牌（如：<code>NIKE</code>、<code>阿迪达斯</code>、<code>优衣库</code>）。</p></li><li><p><strong>在商家端（后台）智能调用</strong>：<br>当商家发布商品，选择了“T恤”类目后，他在“选择品牌”的下拉菜单里，看到的，就将是一个被<strong>智能筛选</strong>过的、只包含“<code>NIKE</code>”、“<code>阿迪达斯</code>”等服饰品牌的短列表。</p></li></ol><p><strong>总结</strong>：<br>“<strong>类目-属性-品牌</strong>”的后台关联设计，是我认为的电商后台商品管理系统中，<strong>最能体现设计功力</strong>的一环。它是一个“<strong>后台配置一小步，前台体验一大步</strong>”的经典设计，能让我们的整个商品体系，变得井然有序、充满智慧。</p><hr><h2 id="5-9-属性管理特殊规则"><a href="#5-9-属性管理特殊规则" class="headerlink" title="5.9 属性管理特殊规则"></a>5.9 属性管理特殊规则</h2><p>在我们<code>5.6</code>节的设计中，我们确立了“<strong>类目-属性关联</strong>”的核心思想。但在面对一个拥有成千上万类目和属性的大型电商平台时，简单的关联会带来两个新的问题：<strong>一是商家端填写体验杂乱，二是平台端配置效率低下</strong>。</p><p>为了解决这两个问题，我必须在我的设计中，引入两个高级的特殊规则：<strong>属性分组</strong>和<strong>属性继承</strong>。</p><h3 id="1-属性分组-让信息更有序"><a href="#1-属性分组-让信息更有序" class="headerlink" title="1. 属性分组 - 让信息更有序"></a>1. 属性分组 - 让信息更有序</h3><ul><li><strong>遇到的问题</strong>：<br>一个“笔记本电脑”类目，可能会关联几十个属性。如果我在商家发布商品的页面，把这几十个属性输入框，从上到下平铺直叙地排列下来，整个页面会显得极其冗长和混乱，商家很难快速找到自己要填写的项目。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223727689.png" alt="image-20250722223727689"></p><ul><li><p><strong>我的解决方案：</strong><br>我会引入“<strong>属性组</strong>”的概念。正如我们看到的大部分商品详情页一样，属性信息天然就是可以被“<strong>分组</strong>”的（如：显示器参数、处理器、内存、硬盘等）。</p></li><li><p><strong>我的后台设计：</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223657032.png" alt="image-20250722223657032"></p><ol><li><strong>第一步：创建属性组</strong>。在平台运营后台，我会设计一个独立的“<strong>属性组管理</strong>”功能。在这里，运营同事可以创建不同的“属性组”（比如，创建一个名为“<code>CPU组</code>”的分组），然后从我们的“属性池”中，将相关的属性（如<code>CPU型号</code>、<code>CPU核心数</code>）添加进这个组里。</li><li><strong>第二步：类目关联属性组</strong>。在“类目管理”后台，运营同事在为类目关联属性时，他关联的，就不再是一个个零散的属性，而是一个个已经打包好的“<strong>属性组</strong>”。</li></ol></li><li><p><strong>最终效果：</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223907299.png" alt="image-20250722223907299"></p><p>经过后台的这一番配置，商家在发布商品，选择了“笔记本电脑”类目后，他看到的属性填写区，就不再是混乱的长列表，而是像图中这样，被清晰地规整在“<code>处理器</code>”、“<code>内存</code>”、“<code>硬盘</code>”等区块之下，一目了然，填写体验大幅提升。</p></li></ul><h3 id="2-属性继承-让配置更高效"><a href="#2-属性继承-让配置更高效" class="headerlink" title="2. 属性继承 - 让配置更高效"></a>2. 属性继承 - 让配置更高效</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223601055.png" alt="image-20250722223601055"></p><ul><li><p><strong>遇到的问题</strong>：<br>我们后台的类目，是树状的多级结构。有一些属性，是非常通用的，比如“<code>商品毛重</code>”，它几乎适用于所有实物商品。如果按照我们现有的逻辑，我的运营同事，需要手动地，为成百上千个“叶子类目”，都去重复地关联一次“<code>商品毛重</code>”这个属性，这无疑是一场噩梦。</p></li><li><p><strong>我的解决方案：</strong><br>我会为我们的类目-属性关联系统，设计一个“<strong>属性继承</strong>”的规则。</p></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224101627.png" alt="image-20250722224101627"></p><ul><li><p><strong>规则定义</strong>：<strong>任何一个子类目，都会自动地，继承其所有父级类目所关联的全部属性</strong>。</p></li><li><p><strong>我的设计应用</strong>：<br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224138541.png" alt="image-20250722224138541"></p><p>有了这条继承规则，我的运营同事的工作，就变得极其高效了：</p><ol><li>他只需要将“<code>商品毛重</code>”这个通用属性，关联到最顶级的“<strong>一级分类</strong>”（如：“数码”）上。</li><li>那么，所有属于“数码”下的“<strong>二级分类</strong>”（如：“摄影摄像”）和“<strong>三级分类</strong>”（如：“单反相机”），就都<strong>自动地、无需任何操作地，拥有了</strong>“<code>商品毛重</code>”这个属性。</li><li>最终，一个“单反相机”类目下的商品，它所需要填写的属性，就等于“<strong>单反相机”自己关联的属性 + 它继承自“摄影摄像”的属性 + 它继承自“数码”的属性</strong>的总和。</li></ol></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224321109.png" alt="image-20250722224321109"></p><p>这个“<strong>属性继承</strong>”的设计，极大地减少了后台运营的重复配置工作量，并保证了整个商品体系属性的规范性和一致性。</p><hr><h2 id="5-10-运费模板"><a href="#5-10-运费模板" class="headerlink" title="5.10 运费模板"></a>5.10 运费模板</h2><p>当用户在我们的电商平台下单时，除了商品价格，他最关心的另一个问题就是：<strong>这件商品，寄到我这里，需要多少运费？</strong></p><p>这个问题的背后，对我们产品经理来说，则是另一个问题：<strong>商家是如何，为成千上万、发往全国各地的商品，去设定如此复杂的运费规则的？</strong></p><p>答案，就是我们必须为商家，设计一套功能强大、体验灵活的“<strong>运费模板</strong>”系统。</p><h3 id="1-什么是运费模板？"><a href="#1-什么是运费模板？" class="headerlink" title="1. 什么是运费模板？"></a>1. 什么是运费模板？</h3><p>我给<strong>运费模板</strong>的定义是：<strong>一套由商家预先设置并保存好的、包含了复杂运费计算规则的“配置方案”</strong>。</p><p>它的核心价值在于“<strong>一次配置，多次复用</strong>”。商家只需要根据自己的物流合作方和商品特性，创建好几套模板（比如：“大件商品-德邦模板”、“小件商品-顺丰模板”），就可以方便地，将这些模板，应用到成千上万的商品上。这种“<strong>一对多</strong>”的设计，能极大地提升商家的运营效率。</p><h3 id="2-运费模板功能设计"><a href="#2-运费模板功能设计" class="headerlink" title="2. 运费模板功能设计"></a>2. 运费模板功能设计</h3><p>我设计运费模板功能，主要包含两个核心模块：<strong>模板的创建与管理</strong>，和<strong>商品对模板的应用</strong>。</p><h4 id="模块一：创建与管理运费模板"><a href="#模块一：创建与管理运费模板" class="headerlink" title="模块一：创建与管理运费模板"></a><strong>模块一：创建与管理运费模板</strong></h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095615173.png" alt="image-20250723095615173"></p><p>在商家后台，我需要提供一个“运费管理”的模块，让商家可以“<strong>新建运费模板</strong>”。这个新建模板的表单，是我设计的核心，它必须包含以下配置项：</p><table><thead><tr><th align="left"><strong>配置项</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><strong>模板名称</strong></td><td align="left">一个自定义的名称，方便商家自己识别。比如：“顺丰-江浙沪包邮”。</td></tr><tr><td align="left"><strong>是否包邮</strong></td><td align="left">一个简单的单选：<strong>卖家承担运费（即包邮）<strong>或</strong>买家承担运费</strong>。如果选择“包邮”，则下方的复杂规则可以被简化。</td></tr><tr><td align="left"><strong>计费规则</strong></td><td align="left">这是运费计算的基础。通常分为三种：<strong>按件数</strong>、<strong>按重量</strong>、<strong>按体积</strong>。</td></tr><tr><td align="left"><strong>默认运费规则</strong></td><td align="left"><strong>这是必填项</strong>。用于设置一个“通用”的运费规则，它适用于所有未被“指定地区”规则覆盖的地区。这能有效避免因漏设地区而导致无法下单的问题。</td></tr><tr><td align="left"><strong>指定地区运费规则</strong></td><td align="left"><strong>这是最核心、最灵活的功能</strong>。我需要提供一个入口，让商家可以“<strong>为指定城市设置运费</strong>”。商家可以框选出特定的省市（如：江浙沪），为它们设定一套独立的、不同于“默认规则”的运费。一个模板可以添加多条指定地区的规则。</td></tr><tr><td align="left"><strong>规则详情</strong></td><td align="left">每一条运费规则（无论是默认还是指定），都由“<strong>首件/首重</strong>”和“<strong>续件/续重</strong>”的费用构成。例如：“<strong>1</strong> 件内，<strong>10</strong> 元；每增加 <strong>1</strong> 件，增加运费 <strong>5</strong> 元”。</td></tr></tbody></table><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095749331.png" alt="image-20250723095749331"></p><p>所有创建好的模板，都会在一个“<strong>运费模板列表</strong>”中进行展示，商家可以在这里，对已有的模板，进行<strong>查看、编辑和删除</strong>。</p><h4 id="模块二：商品关联运费模板"><a href="#模块二：商品关联运费模板" class="headerlink" title="模块二：商品关联运费模板"></a><strong>模块二：商品关联运费模板</strong></h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095812700.png" alt="image-20250723095812700"></p><p>当商家在后台创建好了运费模板，最后一步，就是将它“<strong>应用</strong>”到具体的商品上。</p><p>在我设计的“<strong>发布/编辑商品</strong>”页面（<code>5.7</code>节）中，我会增加一个名为“<strong>运费模板</strong>”的<strong>下拉选择框</strong>。这个下拉框的选项，就是该商家在后台创建的所有运费模板。</p><p>商家只需要在这里，为这件商品，选择一个合适的模板。那么，当用户在前台下单购买这件商品时，系统就会根据用户的收货地址，自动地、智能地，匹配上运费模板中对应的规则，计算出最终的、精准的运费。</p><hr><h2 id="5-11-本章总结"><a href="#5-11-本章总结" class="headerlink" title="5.11 本章总结"></a>5.11 本章总结</h2><p>在本章，我们深入到了电商后台系统的“<strong>发动机舱</strong>”，系统性地学习和设计了整个<strong>商品管理</strong>模块。这套系统的设计优劣，直接决定了我们电商平台“<strong>货</strong>”这个核心要素的规范性、丰富性和可扩展性。</p><p>我们的设计旅程，是一次从“原子”到“分子”，再到“系统”的构建过程：</p><ul><li><strong>解构“原子”</strong>：我们的旅程，是从解构一件“商品”最基本的“<strong>原子</strong>”开始的。我们深刻地理解了<code>SPU</code>（一款商品）与<code>SKU</code>（一件货品）的本质区别，并掌握了构成它们的三种核心<code>商品属性</code>——<strong>关键属性、销售属性、其他属性</strong>。</li><li><strong>搭建“书架”</strong>：接着，我们为这些“原子”，搭建了用于收纳和组织的“<strong>书架</strong>”。我们设计了平台的“<strong>商品类目</strong>”体系，它就像是图书馆的分类法；我们还设计了“<strong>品牌管理</strong>”体系，它就像是出版社的陈列柜。</li><li><strong>赋予“智能”</strong>：然后，我们为这套系统，注入了“<strong>智能</strong>”。通过设计“<strong>类目关联</strong>”和“<strong>属性继承</strong>”等特殊规则，我们让“书架”和“书”之间，产生了聪明的联动。</li><li><strong>打造“工具”</strong>：在所有底层数据模型都设计完毕后，我们最终将它们，组合成了一个面向商家的、功能强大的“<strong>生产力工具</strong>”——<strong>商品发布功能</strong>，并为它设计了必不可少的配套功能——<strong>运费模板</strong>。</li></ul><p>我将本章最核心的几个概念，总结在下面的表格中：</p><table><thead><tr><th align="left"><strong>核心概念</strong></th><th align="left"><strong>我的核心理解</strong></th></tr></thead><tbody><tr><td align="left"><strong>SPU与SKU</strong></td><td align="left">SPU是“一款商品”，SKU是“一件货品”。这是商品数据建模的绝对核心。</td></tr><tr><td align="left"><strong>类目/品牌/属性</strong></td><td align="left">这是构成SPU和SKU的“原材料”。平台必须在后台对它们进行<strong>集中、统一、结构化</strong>的管理。</td></tr><tr><td align="left"><strong>关联与继承</strong></td><td align="left">这是让后台变“聪明”的关键。通过<strong>类目关联</strong>，我们为商家提供了智能化的发布体验；通过<strong>属性继承</strong>，我们为运营提升了配置效率。</td></tr><tr><td align="left"><strong>商品发布功能</strong></td><td align="left">这是所有后台数据模型最终的应用场景。一个好的发布流程，能引导商家，录入规范、准确、完整的商品数据。</td></tr></tbody></table><p>到这里，我们电商产品关于“<strong>用户</strong>”（用户端）、“<strong>后台</strong>”（平台端与商家端）的设计，就已经全部完成了。我们已经拥有了一份足以应对复杂电商业务的、完整的“建筑蓝图”。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/4512.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/4512.html&quot;)">产品经理进阶（五）：第五章：电商后台 - 商品管理</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/4512.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理进阶（五）：第五章：电商后台 - 商品管理&amp;url=https://prorise666.site/posts/4512.html&amp;pic=https://bu.dusays.com/2025/07/25/6882f31a48223.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/26490.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理进阶（四）：第四章：电商后台产品设计</div></div></a></div><div class="next-post pull-right"><a href="/posts/37507.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理进阶（六）：第六章：电商后台 - 运营与交易管理</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理进阶（五）：第五章：电商后台 - 商品管理",date:"2025-07-24 20:13:45",updated:"2025-07-25 11:05:48",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第五章：电商后台 - 商品管理\n\n在上一章，我们已经设计好了商家入驻的流程，让第一批商家成功进入了我们的平台。现在，他们最迫切的需求就是：**我应该如何，把我的商品，发布到平台上进行售卖？**\n\n本章，我们就将为商家，以及我们自己平台的运营，设计一套完整、专业、可扩展的商品管理系统。\n\n## 5.1 学习目标\n\n![image-20250722174132300](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722174132300.png)\n\n在本章中，我的核心目标是，带大家掌握电商后台商品管理模块的完整设计。我们将首先从宏观上，**推导出商品从“录入”到“呈现”的核心业务流程**，然后再深入到微观，学习**SPU/SKU、类目、属性**等构建商品体系的“原子”概念，并最终将它们组合成一个完整的“**商品发布功能**”。\n\n---\n## 5.2 商品发布流程推导\n\n在我动手设计“商品发布”这个后台功能之前，我一定会先将支撑这个功能的**端到端业务流程**，梳理得一清二楚。\n\n### 1. 从“最小商品模型”开始思考\n\n我的思考，会先从“终局”出发，即，从一个**普通用户**的视角来看：**要让我能看懂一件商品，至少需要呈现哪些信息？**\n\n![image-20250722180034500](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180034500.png)\n\n这个问题的答案，构成了我们电商系统的“**最小商品模型**”。它至少需要包含以下五个核心信息：\n1.  **标题**：商品叫什么名字。\n2.  **图片**：商品长什么样。\n3.  **价格**：商品卖多少钱。\n4.  **库存**：商品还有没有货。\n5.  **描述**：商品的详细介绍。\n\n![image-20250722180117296](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180117296.png)\n\n### 2. 用“特殊场景”挑战简单模型\n\n那么，基于这个最小模型，一个最简单的设计思路就是：我只需要给商家提供一个包含这五个字段的表单，让他填完提交，不就可以了吗？\n\n**这样够吗？**\n当我把这个简单的模型，放入真实的、复杂的电商场景中去检验时，会立刻发现它的不足。\n\n![image-20250722180223346](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180223346.png)\n\n我必须考虑到以下这些“**特殊场景**”：\n* **分类场景**：任何一件商品，都需要被归属到一个明确的“**商品分类**”下（如：电脑/办公 -> 电脑组件 -> 硬盘），这样用户才能通过分类导航找到它。\n\n![image-20250722180312976](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180312976.png)\n\n* **品牌场景**：用户也常常会通过“**品牌**”的维度来找商品（如：联想品牌馆）。因此，商品也需要和品牌进行关联。\n\n\n\n![image-20250722180255028](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180255028.png)\n\n* **属性场景**：不同品类的商品，其需要展示的“**商品参数**”是完全不同的。比如，硬盘需要展示`容量`、`接口`等参数；而一件衣服，则需要展示`材质`、`适用季节`等参数。一个固定的、简单的表单，是无法满足这种多样性的。\n\n### 3. 推导出核心发布流程\n\n![image-20250722180337574](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180337574.png)\n\n为了解决上述所有特殊场景带来的问题，我推导出的、一个专业的“**商家发布商品核心流程**”，必须是一个**结构化的、分步骤**的流程：\n\n\n\n![image-20250722180649979](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180649979.png)\n\n1.  **第一步：选择商品类目**\n    这是商家发布商品的**起点**。我会让商家，先从我们后台预设的“商品类目”树中，精准地选择他将要发布的商品，属于哪一个最细分的叶子类目。\n    * **我的设计思考**：这一步至关重要，因为**用户选择的类目，将直接决定了下一步他需要填写的“商品属性”**。\n\n2.  **第二步：选择商品品牌**\n    在确定了类目后，商家需要选择该商品所属的“品牌”。\n\n3.  **第三步：设置商品信息**\n    只有在完成了前两步之后，系统才会展示出最终的“商品信息设置”页面。这个页面，除了包含我们前面提到的“最小商品模型”（标题、价格、图片、库存、描述）的填写区域外，还会根据第一步选择的类目，**动态地**加载出该类目专属的“**商品属性**”填写项。\n\n### 4. 流程总结\n\n至此，我们就完成了一次完整的流程推导。这个“**先选类目 -> 再选品牌 -> 最后填写信息**”的三步走流程，就是我为商家设计的、既能满足复杂场景，又能保证后台数据结构化、规范化的核心解决方案。\n\n这个流程，完美地嵌入到了我们之前梳理的“**商家录入 -> 平台审核 -> 用户浏览**”的宏观业务流程中，构成了其中“**商家录入**”这一环节的具体实现。\n\n\n\n---\n## 5.3 商品类目\n\n在我推导出的“**先选类目 -> 再选品牌 -> 最后填写信息**”的商品发布流程中，“**选择商品类目**”是所有流程的起点。\n\n因此，**商品类目**体系，是我在设计商品管理后台时，第一个要搭建的、也是最底层的“**地基**”。一个清晰、稳定、可扩展的类目体系，是整个电商平台有序运转的保障。\n\n### 1. 商品类目的核心作用\n\n![image-20250722180926020](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180926020.png)\n\n我设计商品类目体系，是因为它同时为我们生态中的三大核心角色，都提供了不可或缺的价值。\n\n| 角色 | 核心作用 | 我的解读 |\n| :--- | :--- | :--- |\n| **平台** | **确定服务范围，进行监管** | 我后台设置的类目，直接定义了“**我们平台允许卖什么，不允许卖什么**”。这是我进行平台治理、控制风险、明确业务边界的根本。 |\n| **商家**| **分门别类，便于管理** | 我为商家提供了一套标准化的“**商品货架**”。商家只需要按照我的类目规范，将商品“上架”到对应的位置，就能实现规范化的管理。 |\n| **用户**| **方便查找商品** | 我为用户提供了一套清晰的“**商场导览图**”。用户可以通过分类导航，快速地找到自己想要逛的“区域”，极大地提升了购物效率。 |\n\n### 2. 从角色需求到产品功能\n\n![image-20250722181002478](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181002478.png)\n\n基于上述的作用，我可以清晰地提炼出不同角色的产品需求，并推导出我们需要提供的核心功能。\n\n这个推导的结论非常清晰，也是我设计的核心原则：“**平台来管理类目，商家使用类目**”。\n* **平台的需求**是“限定范围、监管”，这要求我必须设计一套强大的“**后台类目管理**”功能。\n* **商家的需求**是“加载类目模板、方便操作”，这要求我必须在“**商品发布**”流程中，提供一个易用的“**类目选择**”功能。\n\n### 3. 平台端：类目管理功能设计\n\n![image-20250722181719285](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181719285.png)\n\n这是我为平台运营人员设计的“**类目配置中心**”。\n* **多级类目结构**：我设计的后台，必须支持**多级类目**的创建和管理。如案例图所示，一个“笔记本”商品，它的类目层级可能是“数码家电（一级）” -> “家电（二级）” -> “笔记本（三级）”。\n* **基础管理操作**：后台必须提供对每一级类目的**新增、编辑、删除、查询**等基础操作。\n* **我的拓展设计（属性与品牌关联）**：这是后台类目管理最核心的、也是最高阶的功能。在运营人员新增或编辑一个“叶子类目”（即，不能再往下分的最后一级类目，如“笔记本”）时，我设计的后台，**必须允许他，将这个类目，与一组特定的“商品属性”和“品牌”进行关联**。\n    * 例如，在配置“笔记本”这个类目时，运营就要为它关联上“屏幕尺寸”、“内存”、“CPU型号”等**属性**，并关联上“联想”、“华为”、“苹果”等**品牌**。\n\n### 4. 商家端与用户端：类目的使用\n\n我们平台运营在后台辛辛苦苦搭建好的这套类目体系，最终会在商家端和用户端，被“使用”和“呈现”。\n\n![image-20250722181801927](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181801927.png)\n\n* **在商家端**：当商家发布新商品时，我们流程的**第一步**，就是让他从我们后台预设好的类目树中，选择一个。当他选择了“笔记本”之后，系统就会因为我们后台的“关联”配置，而**动态地**为他加载出需要填写的“屏幕尺寸”、“内存”等属性。\n\n![image-20250722181837353](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181837353.png)\n\n* **在用户端**：用户则会在我们App的“**分类**”Tab页，看到我们后台配置好的类目树结构，并可以逐级点击，进行商品的浏览和筛选。\n\n一个后台设计得清晰、合理的类目体系，是前台商家发布体验流畅、用户浏览体验清晰的根本保障。\n\n\n\n\n\n\n---\n## 5.4 品牌管理\n\n在我看来，如果说“类目”是商品的**物理属性**分类，那么“**品牌**”就是商品的**心智属性**分类。\n\n用户在购买决策时，品牌是影响他们信任和选择的、极其重要的一个因素。\n\n因此，我必须在后台，建立一套完善的、由平台强管控的品牌管理体系。\n\n### 1. 品牌管理的核心价值\n\n![image-20250722213811999](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213811999.png)\n\n我设计品牌管理系统，同样是为了服务好我们生态中的三大核心角色。\n\n| **角色** | **核心需求** | **我需要提供的产品能力** |\n| :--- | :--- | :--- |\n| **平台** | **规避假冒伪劣、山寨产品**，保证平台的商品品质和声誉。 | 必须建立一套**品牌的审核与认证机制**，确保只有合规的、真实的品牌才能在平台上被售卖。 |\n| **商家** | 能够清晰地标明自己所售卖商品的**品牌归属**。 | 我需要在商品发布流程中，为商家提供一个清晰、准确的**品牌选择器**。 |\n| **用户** | **“我只想看某个品牌的商品”**，能够通过品牌维度，快速找到自己想要的商品。| 我需要在用户端，提供**按品牌进行搜索和筛选**的功能。 |\n\n![image-20250722213850767](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213850767.png)\n\n基于上述的需求，我推导出的核心功能是：\n* **平台端**：必须有后台**品牌管理**（增删改查）功能。\n* **商家端**：必须在商品发布时，有**品牌选择**功能。\n* **用户端**：必须有**品牌搜索/筛选**功能。\n\n### 2. 平台端：品牌库管理功能设计\n\n![image-20250722213944716](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213944716.png)\n\n所有品牌功能的核心，在于我们平台运营后台，必须建立一个“**品牌库（Brand Library）**”。这个品牌库，是由我们**平台统一进行维护**的，它是我们平台上所有“合法品牌”的唯一真实来源。\n\n我设计的品牌库后台，主要包含以下功能：\n* **品牌信息字段**：在新增一个品牌时，运营需要填写该品牌的`Logo`、`中文名`、`英文名`、`简介`等信息。\n* **基础管理功能**：运营可以对品牌库中的品牌，进行常规的**新增、编辑、查询**操作。\n* **状态管理**：每个品牌，都有“**启用/停用**”两种状态。当某个品牌出现问题（如：被曝出重大质量问题、品牌方与我们合作终止）时，运营可以将其状态，设置为“停用”。设置为“停用”后，商家在发布商品时，就无法再选择这个品牌了。\n\n### 3. 特殊流程：自主品牌入驻\n\n这时，一个非常常见的业务场景就出现了：**如果一个商家，想售卖一个我们品牌库里，还没有收录的新品牌，怎么办？**\n\n我不能让商家随意地、手动地填写品牌名称，这会导致品牌库数据混乱，出现大量山寨和无效品牌。因此，我必须设计一套严谨的“**新品牌入驻审核**”流程。\n\n![image-20250722214029207](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214029207.png)\n\n我的设计流程如下：\n1.  **商家提交申请**：在商家后台，我会提供一个“**新增品牌申请**”的入口。商家需要在这个页面，填写新品牌的基础信息，并**必须上传该品牌的《商标注册证》**作为资质证明。\n2.  **平台审核**：商家的申请，会进入我们平台运营后台的“品牌审核”列表中。运营同事的核心工作，是**核实《商标注册证》的真实性**和有效性。\n3.  **品牌入库**：审核通过后，运营同事，会将这个新品牌的信息，正式录入到我们的“品牌库”中，并设置为“启用”状态。\n4.  **商家选用**：一旦品牌成功入库，商家（以及其他所有获得了该品牌授权的商家），就可以在发布商品时，从品牌选择器中，选择这个新的品牌了。\n\n通过这套流程，我既满足了商家引入新品牌的需求，又确保了平台对所有品牌的“强管控”，保证了我们电商品牌生态的健康。\n\n---\n## 5.5 SKU与SPU\n\n在我设计任何电商后台的商品系统时，我的第一个思考，就是要清晰地定义**SPU**和**SKU**。这两个概念，是整个商品世界的“**基本粒子**”，理解了它们，就理解了所有复杂商品体系的构成。\n\n### 1. 核心定义\n\n![image-20250722214844754](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214844754.png)\n\n* **SPU - 标准产品单元 (Standard Product Unit)**\n    我把它理解为“**一款商品**”。它是一组具有共同的、标准化的核心属性的商品的集合，是商品信息聚合的最小单位。\n    * **例如**：“iPhone 11”就是一个SPU。它代表了“iPhone 11”这个产品系列，与它的颜色、内存大小无关。我们通常用SPU，来做商品的通用性描述、展示和搜索。\n\n* **SKU - 库存量单位 (Stock Keeping Unit)**\n    我把它理解为“**一件货品**”。它是库存控制的最小可用单位，是真正物理存在的、可以被用户购买的最小单元。\n    * **例如**：“一台白色的、内存为64G的iPhone 11”，就是一个SKU。“一台红色的、内存为128G的iPhone 11”，则是另一个完全不同的SKU。**每一个SKU，都有自己独立的库存和价格**。\n\n### 2. SPU与SKU的关系\n\n![image-20250722214946907](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214946907.png)\n\nSPU和SKU之间，是一个“**一对多**”的层级关系。一个SPU，通过不同的“**销售属性**”的组合，可以衍生出多个不同的SKU。\n\n**销售属性**，就是那些能影响到商品最终售价和库存的属性，比如`颜色`、`尺码`、`内存大小`、`套餐类型`等。\n\n所以，它们之间的关系公式是：\n> **SKU = SPU + 一组确定的销售属性**\n\n![image-20250722215055577](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215055577.png)\n\n我们在京东看到的这个iPhone 11商品详情页，就是一个完美的现实案例。\n* **SPU**：是“Apple iPhone 11”这个商品本身。\n* **销售属性**：是“选择颜色”和“选择版本”这两个维度。\n* **SKU**：当用户选择了“黑色”和“128GB”之后，页面上展示的特定价格`¥5269.00`和库存状态，就是属于这个唯一SKU的。\n\n### 3. 技术实现浅谈\n\n那么，为了实现这套逻辑，我作为产品经理，需要如何与我的研发同学沟通呢？我需要向他们讲清楚**后端的数据模型**和**前端的交互逻辑**。\n\n* **后端设计（数据模型）**\n    在后台数据库中，我们至少需要设计几张表，来清晰地表达SPU和SKU的关系。\n\n| **数据表** | **我的设计说明** |\n| :--- | :--- |\n| **SPU表 (spu_table)** | 这张表，用来存放“iPhone 11”这个SPU的通用信息，比如`商品名称`、`商品描述`、`品牌`、`类目`等。 |\n| **销售属性名表 (attribute_name_table)** | 这张表，用来定义SPU有哪些销售属性。比如，它会记录：“iPhone 11”这个SPU，有“颜色”和“内存”这两个销售属性。 |\n| **销售属性值表 (attribute_value_table)**| 这张表，用来定义每个销售属性有哪些可选值。比如，它会记录：“颜色”这个属性，有“黑色”、“白色”、“红色”等可选值。 |\n| **SKU表 (sku_table)** | **这是最核心的表**。它用来存放每一个具体的“货品”。比如，“iPhone 11 黑色 128GB”就是这张表里的一行记录。这行记录里，会包含它**自己专属的`价格`、`库存`**，并会关联到它的父级SPU，以及它所对应的属性值（“黑色”、“128GB”）。 |\n\n* **前端设计（交互逻辑）**\n    当用户打开一个商品详情页时，前端与后端的交互流程是这样的：\n    1.  前端App向后端服务器，发送一个请求，告诉它：“我需要SPU ID为‘iPhone 11’的商品数据”。\n    2.  后端服务器收到请求后，会把**SPU表**里的通用信息（描述、主图等），以及**与这个SPU关联的所有SKU表里的记录**（比如：黑色64G的价格/库存、白色64G的价格/库存、黑色128G的价格/库存……），一次性地，全部返回给前端App。\n    3.  前端App拿到这些数据后，就会在页面上，**动态地渲染**出“颜色”和“内存”这两个维度的、所有可点击的**选择按钮**。\n    4.  当用户点击“黑色”和“128GB”这两个按钮时，**前端App会直接在本地已经拿到的数据中，查找到对应的那个SKU**，然后**瞬间**将页面上的价格和库存，更新为这个SKU专属的价格和库存。这个过程，通常**不需要再次请求后端服务器**，因此用户会感觉体验非常流畅。\n\n---\n## 5.6 商品属性\n\n在上一节，我们学习了SPU和SKU。我们知道，一个“黑色、128G的iPhone 13”是一个SKU。但这立刻引出了一个核心问题：系统是怎么知道“iPhone 13”会有“颜色”和“内存”这两个选项的？商家在发布商品时，并不是随意填写这些信息的。\n\n这背后的答案，就是我们这一节要学习的，一套由平台预先定义好的、结构化的数据体系——**商品属性**。\n\n### 1. 属性的存在方式：属性名 + 属性值\n\n![image-20250722215725696](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215725696.png)\n\n首先，我们要明确一个“属性”最基本的构成。任何一个属性，都是由一个“**属性名**”和一个“**属性值**”配对组成的。\n* **属性名**：相当于“问题”，比如：`型号`、`颜色`、`传输速度`。\n* **属性值**：相当于“答案”，比如：`CR111`、`黑色`、`1000Mbps`。\n\n我设计的整个商品信息体系，就是由成千上万个这样的“**键值对**”构成的。\n\n### 2. 属性的分类\n\n![image-20250722215842159](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215842159.png)\n\n为了让这成千上万的属性，能够被系统有序地管理和使用，我必须对它们进行**分类**。在我的电商产品设计中，我会严格地将所有属性，划分为以下三种类型：\n\n\n| 属性分类 | 简单来说 | 案例（以“iPhone 13”为例） |\n| :--- | :--- | :--- |\n| **关键属性** | 就像商品的“身份证”，**确定是哪一款商品**，不会变，也不能选。 | “iPhone 13”这个名字本身，以及它的**具体型号**（比如A2634）。 |\n| **销售属性** | 决定你**最终买哪个具体商品**，你**必须选**，选了之后**价格或库存可能就不同**。 | **颜色**（星光色、午夜色等）；**存储空间**（128GB、256GB等）。 |\n| **其他属性** | 就是商品的**各种特点介绍**，**看看就行，不能选**，也不影响价格。 | 处理器型号 (A15)、屏幕是OLED、防水级别是IP68等等。 |\n\n\n![image-20250722220341997](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220341997.png)\n\n我们可以通过这张网卡的案例图，清晰地看到这三类属性，在真实商品详情页上的分布。\n\n### 3. 属性池：平台端的统一管理\n\n下一个关键问题是：这么多属性名（如`颜色`、`尺寸`、`CPU型号`），它们是从哪里来的？\n\n为了保证数据的规范和统一（比如，避免商家A填写“颜色”，商家B填写“色彩”）\n\n我必须在平台运营后台，建立一个**由平台统一管理**的`属性池`。\n\n![image-20250722220452045](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220452045.png)\n\n“属性池”，是我为平台运营人员设计的、一个用来**集中管理所有“属性名”**的后台功能。在这里，运营同事可以对平台中可能出现的所有属性名，进行**增、删、改、查**。\n\n* **我的拓展设计（属性与类目的关联）**\n    这个“属性池”并不是孤立存在的。它设计的精髓，在于和我们`5.3`节学习的“**商品类目**”进行**深度绑定**。\n    \n    在我设计的“**类目管理**”后台，当运营人员在编辑“笔记本电脑”这个类目时，他就可以从“属性池”中，为这个类目，勾选上它应该具备的属性，比如`CPU型号`、`内存容量`、`屏幕尺寸`、`硬盘容量`等。\n    \n    这样一来，当商家在发布商品、第一步选择了“笔记本电脑”这个类目后，系统就会自动地、智能地，为他加载出需要填写的`CPU型号`、`内存容量`等属性，从而实现了整个商品发布流程的结构化和智能化。\n\n\n\n\n---\n## 5.7 商品发布功能设计\n\n![image-20250722220910285](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220910285.png)\n\n在前面的小节中，我们已经将“商品”这个复杂的概念，拆解为了`类目`、`品牌`、`SPU`、`SKU`、`属性`等一系列结构化的“原子”部件。\n\n现在，我们的任务，就是**将这些“原子”部件，重新组合起来**，为我们的商家，设计一个功能强大、体验流畅的“**商品发布**”功能。这个功能，就是商家后台的“**核心生产力工具**”。\n\n### 1. 页面信息结构\n\n![image-20250722220935810](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220935810.png)\n\n在我设计这个复杂的“发布商品”页面时，我首先会对需要商家填写的**信息**，进行一次高层级的**结构化分类**。我会把整个页面，划分为三大信息模块：\n\n1.  **商品的基本信息**：即SPU层级的通用信息，如商品名称、图片等。\n2.  **商品的属性**：包括决定SKU的销售属性，以及其他描述性属性。\n3.  **商品的详情**：即图文并茂的、用于营销的“长图文”描述。\n\n### 2. 功能模块详细设计\n\n现在，我们来逐一设计承载这三类信息的功能模块。\n\n#### **模块一：填写商品基本信息**\n\n![image-20250722221017448](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221017448.png)\n\n这是商家进入发布流程后，看到的第一个表单区域。它用来采集这件商品（SPU）最基础的信息。\n\n| **字段** | **我的设计说明** |\n| :--- | :--- |\n| **商品分类** | 这个字段通常是**只读**的，它会显示商家在上一个步骤（`5.2`节推导的流程）中所选择的类目。 |\n| **商品名称** | 文本输入框，用于填写SPU的标题。 |\n| **商品品牌** | 一个**下拉选择框**。我设计的逻辑是：这个下拉框里，只会出现我们后台**与该“商品分类”相关联**的品牌，而不是全部的品牌。这是一种智能化的设计。 |\n| **商品价格** | 商家可以填写商品的“市场价”或“划线价”。每个SKU的具体售价，会在下一步设置。 |\n| **商品展示图** | 一个图片上传控件。我会明确地标注出，**最多可以上传几张**，以及推荐的**尺寸和格式**，以保证前端展示效果的统一。 |\n\n#### **模块二：设置销售属性与SKU**\n\n![image-20250722221242154](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221242154.png)\n\n这是整个商品发布功能中，**技术和交互最复杂，但也最核心**的一个模块。我把它分为两步：\n\n**第一步：定义销售属性**\n我会提供一个交互区域，让商家可以为他的商品，添加“**销售属性**”。\n* **属性名**：通过一个下拉框，让商家从该类目下，我们预设好的属性（如`颜色`、`尺寸`）中进行选择。\n* **属性值**：在选定了属性名后，商家可以手动地，添加多个属性值（如`红色`、`蓝色`；`S`、`M`、`L`）。\n\n**第二步：生成SKU并填写明细**\n当商家定义好所有的销售属性和属性值后，我设计的后台，最智能的地方就体现出来了：**系统会自动地，将这些属性值进行“笛卡尔积”组合，生成一个完整的SKU列表**。\n\n商家的工作，不是去手动组合SKU，而是在这个自动生成的表格里，“**做填空题**”。他只需要为每一个SKU，填写它专属的`销售价格`和`销售库存`即可。这个设计，极大地降低了商家的操作复杂度和出错率。\n\n#### **模块三：编辑商品描述**\n\n![image-20250722221359012](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221359012.png)\n\n这是页面的最后一个模块，用于上传商品的“长图文”详情。\n\n* **我的拓展设计（分端描述）**：为了追求极致的用户体验，一个专业的电商后台，应该允许商家，为**PC端**和**移动端**，分别上传和编辑**两套不同**的商品描述。\n* **为什么？** 因为PC端屏幕大，可以展示更丰富、更复杂的图文内容；而移动端屏幕小，则需要更简洁、加载速度更快的图片和文字。提供两个独立的“**富文本编辑器**”，能让有能力的商家，为不同设备的用户，提供最优的浏览体验。\n\n通过将这三大模块，有机地组合在一个页面中，我们就为商家，提供了一个功能强大、逻辑清晰、体验智能的商品发布功能。\n\n\n---\n## 5.8 类目关联的相关场景\n\n在前面的小节中，我们已经独立地设计了`商品类目`、`品牌`和`商品属性`这三个核心的数据模块。但如果它们只是三个孤立的列表，那我们的后台依然是“**笨拙**”的。\n\n一个智能的后台，必须能理解这三者之间的**内在关联**。本节，我们就来设计这套“**关联系统**”。\n\n### 1. 类目与属性的关联\n\n我们首先思考一个场景：我们的“**属性池**”里，包含了`颜色`、`尺码`，也包含了`CPU型号`、`屏幕尺寸`。当一个商家来发布一件“T恤”时，如果我们在“设置属性”的环节，把所有这些属性都展示给他，那将是一场灾难。\n\n![image-20250722222404342](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222404342.png)\n\n\n\n**我的解决方案**：我必须为商品属性，打上“**类目**”的烙印，即创建“**类目属性**”。\n\n![image-20250722222453447](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222453447.png)\n\n**我的设计思路如下：**\n\n![image-20250722222550441](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222550441.png)\n\n1.  **在平台端（后台）进行关联**：\n    在我设计的“**类目管理**”后台，当运营同事在编辑一个“叶子类目”（如：“T恤”）时，我必须提供一个功能，让他可以从“属性池”中，**勾选**出所有与“T恤”相关的属性（如：`颜色`、`尺码`、`材质`、`适用季节`），并将它们**与“T恤”这个类目进行绑定**。\n\n2.  **在商家端（后台）智能调用**：\n    经过了后台的“绑定”操作后，商家在发布商品时，当他在第一步选择了“T恤”这个类目，那么在后续的“设置商品属性”环节，系统就会**只加载并显示**出`颜色`、`尺码`、`材质`等这几个已经绑定好的属性，供他填写。\n\n3.  **在用户端（前台）精准呈现**：\n    这个设计，最终会惠及我们的用户。当用户在前台浏览“T恤”这个分类列表时，页面左侧的“**筛选器**”，也同样只会展示出`颜色`、`尺码`、`材质`等这些与T恤强相关的、有意义的筛选条件。\n\n![image-20250722222826573](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222826573.png)\n\n### 2. 类目与品牌的关联\n\n同样的逻辑，也完全适用于**品牌管理**。当商家发布一件“NIKE”的T恤时，如果让他从一个包含“海尔”、“华为”等上千个品牌的总列表里去寻找，体验会非常糟糕。\n\n![image-20250722222903391](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222903391.png)\n\n**我的解决方案**：我同样需要在后台，建立**品牌与类目的关联**。这样做的好处是：\n* 提升商家发布商品的**便捷性**，避免出错。\n* 让我们的品牌管理更**标准化**。\n* 让用户在前台按分类+品牌进行**筛选时，速度更快**。\n\n**我的设计思路如下：**\n\n![image-20250722222919119](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222919119.png)\n\n1.  **在平台端（后台）进行关联**：\n    在“**类目管理**”后台，当运营编辑“T恤”这个类目时，除了关联属性，他还需要**关联品牌**。他会从“品牌库”中，勾选出所有属于“服饰”类目的品牌（如：`NIKE`、`阿迪达斯`、`优衣库`）。\n\n2.  **在商家端（后台）智能调用**：\n    当商家发布商品，选择了“T恤”类目后，他在“选择品牌”的下拉菜单里，看到的，就将是一个被**智能筛选**过的、只包含“`NIKE`”、“`阿迪达斯`”等服饰品牌的短列表。\n\n**总结**：\n“**类目-属性-品牌**”的后台关联设计，是我认为的电商后台商品管理系统中，**最能体现设计功力**的一环。它是一个“**后台配置一小步，前台体验一大步**”的经典设计，能让我们的整个商品体系，变得井然有序、充满智慧。\n\n\n---\n## 5.9 属性管理特殊规则\n\n在我们`5.6`节的设计中，我们确立了“**类目-属性关联**”的核心思想。但在面对一个拥有成千上万类目和属性的大型电商平台时，简单的关联会带来两个新的问题：**一是商家端填写体验杂乱，二是平台端配置效率低下**。\n\n为了解决这两个问题，我必须在我的设计中，引入两个高级的特殊规则：**属性分组**和**属性继承**。\n\n### 1. 属性分组 - 让信息更有序\n\n* **遇到的问题**：\n    一个“笔记本电脑”类目，可能会关联几十个属性。如果我在商家发布商品的页面，把这几十个属性输入框，从上到下平铺直叙地排列下来，整个页面会显得极其冗长和混乱，商家很难快速找到自己要填写的项目。\n\n![image-20250722223727689](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223727689.png)\n\n* **我的解决方案：**\n    我会引入“**属性组**”的概念。正如我们看到的大部分商品详情页一样，属性信息天然就是可以被“**分组**”的（如：显示器参数、处理器、内存、硬盘等）。\n\n* **我的后台设计：**\n    ![image-20250722223657032](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223657032.png)\n\n    1.  **第一步：创建属性组**。在平台运营后台，我会设计一个独立的“**属性组管理**”功能。在这里，运营同事可以创建不同的“属性组”（比如，创建一个名为“`CPU组`”的分组），然后从我们的“属性池”中，将相关的属性（如`CPU型号`、`CPU核心数`）添加进这个组里。\n    2.  **第二步：类目关联属性组**。在“类目管理”后台，运营同事在为类目关联属性时，他关联的，就不再是一个个零散的属性，而是一个个已经打包好的“**属性组**”。\n\n* **最终效果：**\n    ![image-20250722223907299](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223907299.png)\n\n    经过后台的这一番配置，商家在发布商品，选择了“笔记本电脑”类目后，他看到的属性填写区，就不再是混乱的长列表，而是像图中这样，被清晰地规整在“`处理器`”、“`内存`”、“`硬盘`”等区块之下，一目了然，填写体验大幅提升。\n\n### 2. 属性继承 - 让配置更高效\n\n![image-20250722223601055](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223601055.png)\n\n* **遇到的问题**：\n    我们后台的类目，是树状的多级结构。有一些属性，是非常通用的，比如“`商品毛重`”，它几乎适用于所有实物商品。如果按照我们现有的逻辑，我的运营同事，需要手动地，为成百上千个“叶子类目”，都去重复地关联一次“`商品毛重`”这个属性，这无疑是一场噩梦。\n\n* **我的解决方案：**\n    我会为我们的类目-属性关联系统，设计一个“**属性继承**”的规则。\n\n![image-20250722224101627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224101627.png)\n\n* **规则定义**：**任何一个子类目，都会自动地，继承其所有父级类目所关联的全部属性**。\n\n* **我的设计应用**：\n    ![image-20250722224138541](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224138541.png)\n\n    有了这条继承规则，我的运营同事的工作，就变得极其高效了：\n    1.  他只需要将“`商品毛重`”这个通用属性，关联到最顶级的“**一级分类**”（如：“数码”）上。\n    2.  那么，所有属于“数码”下的“**二级分类**”（如：“摄影摄像”）和“**三级分类**”（如：“单反相机”），就都**自动地、无需任何操作地，拥有了**“`商品毛重`”这个属性。\n    3.  最终，一个“单反相机”类目下的商品，它所需要填写的属性，就等于“**单反相机”自己关联的属性 + 它继承自“摄影摄像”的属性 + 它继承自“数码”的属性**的总和。\n\n![image-20250722224321109](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224321109.png)\n\n这个“**属性继承**”的设计，极大地减少了后台运营的重复配置工作量，并保证了整个商品体系属性的规范性和一致性。\n\n\n\n---\n## 5.10 运费模板\n\n当用户在我们的电商平台下单时，除了商品价格，他最关心的另一个问题就是：**这件商品，寄到我这里，需要多少运费？**\n\n这个问题的背后，对我们产品经理来说，则是另一个问题：**商家是如何，为成千上万、发往全国各地的商品，去设定如此复杂的运费规则的？**\n\n答案，就是我们必须为商家，设计一套功能强大、体验灵活的“**运费模板**”系统。\n\n### 1. 什么是运费模板？\n\n我给**运费模板**的定义是：**一套由商家预先设置并保存好的、包含了复杂运费计算规则的“配置方案”**。\n\n它的核心价值在于“**一次配置，多次复用**”。商家只需要根据自己的物流合作方和商品特性，创建好几套模板（比如：“大件商品-德邦模板”、“小件商品-顺丰模板”），就可以方便地，将这些模板，应用到成千上万的商品上。这种“**一对多**”的设计，能极大地提升商家的运营效率。\n\n### 2. 运费模板功能设计\n\n我设计运费模板功能，主要包含两个核心模块：**模板的创建与管理**，和**商品对模板的应用**。\n\n#### **模块一：创建与管理运费模板**\n\n![image-20250723095615173](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095615173.png)\n\n在商家后台，我需要提供一个“运费管理”的模块，让商家可以“**新建运费模板**”。这个新建模板的表单，是我设计的核心，它必须包含以下配置项：\n\n| **配置项** | **我的设计说明** |\n| :--- | :--- |\n| **模板名称** | 一个自定义的名称，方便商家自己识别。比如：“顺丰-江浙沪包邮”。 |\n| **是否包邮**| 一个简单的单选：**卖家承担运费（即包邮）**或**买家承担运费**。如果选择“包邮”，则下方的复杂规则可以被简化。 |\n| **计费规则**| 这是运费计算的基础。通常分为三种：**按件数**、**按重量**、**按体积**。 |\n| **默认运费规则**| **这是必填项**。用于设置一个“通用”的运费规则，它适用于所有未被“指定地区”规则覆盖的地区。这能有效避免因漏设地区而导致无法下单的问题。 |\n| **指定地区运费规则**| **这是最核心、最灵活的功能**。我需要提供一个入口，让商家可以“**为指定城市设置运费**”。商家可以框选出特定的省市（如：江浙沪），为它们设定一套独立的、不同于“默认规则”的运费。一个模板可以添加多条指定地区的规则。 |\n| **规则详情**| 每一条运费规则（无论是默认还是指定），都由“**首件/首重**”和“**续件/续重**”的费用构成。例如：“**1** 件内，**10** 元；每增加 **1** 件，增加运费 **5** 元”。 |\n\n![image-20250723095749331](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095749331.png)\n\n所有创建好的模板，都会在一个“**运费模板列表**”中进行展示，商家可以在这里，对已有的模板，进行**查看、编辑和删除**。\n\n#### **模块二：商品关联运费模板**\n\n![image-20250723095812700](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095812700.png)\n\n当商家在后台创建好了运费模板，最后一步，就是将它“**应用**”到具体的商品上。\n\n在我设计的“**发布/编辑商品**”页面（`5.7`节）中，我会增加一个名为“**运费模板**”的**下拉选择框**。这个下拉框的选项，就是该商家在后台创建的所有运费模板。\n\n商家只需要在这里，为这件商品，选择一个合适的模板。那么，当用户在前台下单购买这件商品时，系统就会根据用户的收货地址，自动地、智能地，匹配上运费模板中对应的规则，计算出最终的、精准的运费。\n\n\n\n\n---\n## 5.11 本章总结\n\n在本章，我们深入到了电商后台系统的“**发动机舱**”，系统性地学习和设计了整个**商品管理**模块。这套系统的设计优劣，直接决定了我们电商平台“**货**”这个核心要素的规范性、丰富性和可扩展性。\n\n我们的设计旅程，是一次从“原子”到“分子”，再到“系统”的构建过程：\n* **解构“原子”**：我们的旅程，是从解构一件“商品”最基本的“**原子**”开始的。我们深刻地理解了`SPU`（一款商品）与`SKU`（一件货品）的本质区别，并掌握了构成它们的三种核心`商品属性`——**关键属性、销售属性、其他属性**。\n* **搭建“书架”**：接着，我们为这些“原子”，搭建了用于收纳和组织的“**书架**”。我们设计了平台的“**商品类目**”体系，它就像是图书馆的分类法；我们还设计了“**品牌管理**”体系，它就像是出版社的陈列柜。\n* **赋予“智能”**：然后，我们为这套系统，注入了“**智能**”。通过设计“**类目关联**”和“**属性继承**”等特殊规则，我们让“书架”和“书”之间，产生了聪明的联动。\n* **打造“工具”**：在所有底层数据模型都设计完毕后，我们最终将它们，组合成了一个面向商家的、功能强大的“**生产力工具**”——**商品发布功能**，并为它设计了必不可少的配套功能——**运费模板**。\n\n我将本章最核心的几个概念，总结在下面的表格中：\n\n| **核心概念** | **我的核心理解** |\n| :--- | :--- |\n| **SPU与SKU** | SPU是“一款商品”，SKU是“一件货品”。这是商品数据建模的绝对核心。 |\n| **类目/品牌/属性**| 这是构成SPU和SKU的“原材料”。平台必须在后台对它们进行**集中、统一、结构化**的管理。 |\n| **关联与继承** | 这是让后台变“聪明”的关键。通过**类目关联**，我们为商家提供了智能化的发布体验；通过**属性继承**，我们为运营提升了配置效率。 |\n| **商品发布功能**| 这是所有后台数据模型最终的应用场景。一个好的发布流程，能引导商家，录入规范、准确、完整的商品数据。 |\n\n到这里，我们电商产品关于“**用户**”（用户端）、“**后台**”（平台端与商家端）的设计，就已经全部完成了。我们已经拥有了一份足以应对复杂电商业务的、完整的“建筑蓝图”。\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0-%E5%95%86%E5%93%81%E7%AE%A1%E7%90%86"><span class="toc-number">1.</span> <span class="toc-text">第五章：电商后台 - 商品管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#5-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.</span> <span class="toc-text">5.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-2-%E5%95%86%E5%93%81%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B%E6%8E%A8%E5%AF%BC"><span class="toc-number">1.2.</span> <span class="toc-text">5.2 商品发布流程推导</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%BB%8E%E2%80%9C%E6%9C%80%E5%B0%8F%E5%95%86%E5%93%81%E6%A8%A1%E5%9E%8B%E2%80%9D%E5%BC%80%E5%A7%8B%E6%80%9D%E8%80%83"><span class="toc-number">1.2.1.</span> <span class="toc-text">1. 从“最小商品模型”开始思考</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E7%94%A8%E2%80%9C%E7%89%B9%E6%AE%8A%E5%9C%BA%E6%99%AF%E2%80%9D%E6%8C%91%E6%88%98%E7%AE%80%E5%8D%95%E6%A8%A1%E5%9E%8B"><span class="toc-number">1.2.2.</span> <span class="toc-text">2. 用“特殊场景”挑战简单模型</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E6%8E%A8%E5%AF%BC%E5%87%BA%E6%A0%B8%E5%BF%83%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B"><span class="toc-number">1.2.3.</span> <span class="toc-text">3. 推导出核心发布流程</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E6%B5%81%E7%A8%8B%E6%80%BB%E7%BB%93"><span class="toc-number">1.2.4.</span> <span class="toc-text">4. 流程总结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-3-%E5%95%86%E5%93%81%E7%B1%BB%E7%9B%AE"><span class="toc-number">1.3.</span> <span class="toc-text">5.3 商品类目</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%95%86%E5%93%81%E7%B1%BB%E7%9B%AE%E7%9A%84%E6%A0%B8%E5%BF%83%E4%BD%9C%E7%94%A8"><span class="toc-number">1.3.1.</span> <span class="toc-text">1. 商品类目的核心作用</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E4%BB%8E%E8%A7%92%E8%89%B2%E9%9C%80%E6%B1%82%E5%88%B0%E4%BA%A7%E5%93%81%E5%8A%9F%E8%83%BD"><span class="toc-number">1.3.2.</span> <span class="toc-text">2. 从角色需求到产品功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%B9%B3%E5%8F%B0%E7%AB%AF%EF%BC%9A%E7%B1%BB%E7%9B%AE%E7%AE%A1%E7%90%86%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.3.</span> <span class="toc-text">3. 平台端：类目管理功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E5%95%86%E5%AE%B6%E7%AB%AF%E4%B8%8E%E7%94%A8%E6%88%B7%E7%AB%AF%EF%BC%9A%E7%B1%BB%E7%9B%AE%E7%9A%84%E4%BD%BF%E7%94%A8"><span class="toc-number">1.3.4.</span> <span class="toc-text">4. 商家端与用户端：类目的使用</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-4-%E5%93%81%E7%89%8C%E7%AE%A1%E7%90%86"><span class="toc-number">1.4.</span> <span class="toc-text">5.4 品牌管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%93%81%E7%89%8C%E7%AE%A1%E7%90%86%E7%9A%84%E6%A0%B8%E5%BF%83%E4%BB%B7%E5%80%BC"><span class="toc-number">1.4.1.</span> <span class="toc-text">1. 品牌管理的核心价值</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%B9%B3%E5%8F%B0%E7%AB%AF%EF%BC%9A%E5%93%81%E7%89%8C%E5%BA%93%E7%AE%A1%E7%90%86%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.4.2.</span> <span class="toc-text">2. 平台端：品牌库管理功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E7%89%B9%E6%AE%8A%E6%B5%81%E7%A8%8B%EF%BC%9A%E8%87%AA%E4%B8%BB%E5%93%81%E7%89%8C%E5%85%A5%E9%A9%BB"><span class="toc-number">1.4.3.</span> <span class="toc-text">3. 特殊流程：自主品牌入驻</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-5-SKU%E4%B8%8ESPU"><span class="toc-number">1.5.</span> <span class="toc-text">5.5 SKU与SPU</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E5%AE%9A%E4%B9%89"><span class="toc-number">1.5.1.</span> <span class="toc-text">1. 核心定义</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-SPU%E4%B8%8ESKU%E7%9A%84%E5%85%B3%E7%B3%BB"><span class="toc-number">1.5.2.</span> <span class="toc-text">2. SPU与SKU的关系</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E6%8A%80%E6%9C%AF%E5%AE%9E%E7%8E%B0%E6%B5%85%E8%B0%88"><span class="toc-number">1.5.3.</span> <span class="toc-text">3. 技术实现浅谈</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-6-%E5%95%86%E5%93%81%E5%B1%9E%E6%80%A7"><span class="toc-number">1.6.</span> <span class="toc-text">5.6 商品属性</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%B1%9E%E6%80%A7%E7%9A%84%E5%AD%98%E5%9C%A8%E6%96%B9%E5%BC%8F%EF%BC%9A%E5%B1%9E%E6%80%A7%E5%90%8D-%E5%B1%9E%E6%80%A7%E5%80%BC"><span class="toc-number">1.6.1.</span> <span class="toc-text">1. 属性的存在方式：属性名 + 属性值</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%B1%9E%E6%80%A7%E7%9A%84%E5%88%86%E7%B1%BB"><span class="toc-number">1.6.2.</span> <span class="toc-text">2. 属性的分类</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%B1%9E%E6%80%A7%E6%B1%A0%EF%BC%9A%E5%B9%B3%E5%8F%B0%E7%AB%AF%E7%9A%84%E7%BB%9F%E4%B8%80%E7%AE%A1%E7%90%86"><span class="toc-number">1.6.3.</span> <span class="toc-text">3. 属性池：平台端的统一管理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-7-%E5%95%86%E5%93%81%E5%8F%91%E5%B8%83%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.7.</span> <span class="toc-text">5.7 商品发布功能设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E9%A1%B5%E9%9D%A2%E4%BF%A1%E6%81%AF%E7%BB%93%E6%9E%84"><span class="toc-number">1.7.1.</span> <span class="toc-text">1. 页面信息结构</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97%E8%AF%A6%E7%BB%86%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.7.2.</span> <span class="toc-text">2. 功能模块详细设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%B8%80%EF%BC%9A%E5%A1%AB%E5%86%99%E5%95%86%E5%93%81%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AF"><span class="toc-number">1.7.2.1.</span> <span class="toc-text">模块一：填写商品基本信息</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%BA%8C%EF%BC%9A%E8%AE%BE%E7%BD%AE%E9%94%80%E5%94%AE%E5%B1%9E%E6%80%A7%E4%B8%8ESKU"><span class="toc-number">1.7.2.2.</span> <span class="toc-text">模块二：设置销售属性与SKU</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%B8%89%EF%BC%9A%E7%BC%96%E8%BE%91%E5%95%86%E5%93%81%E6%8F%8F%E8%BF%B0"><span class="toc-number">1.7.2.3.</span> <span class="toc-text">模块三：编辑商品描述</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-8-%E7%B1%BB%E7%9B%AE%E5%85%B3%E8%81%94%E7%9A%84%E7%9B%B8%E5%85%B3%E5%9C%BA%E6%99%AF"><span class="toc-number">1.8.</span> <span class="toc-text">5.8 类目关联的相关场景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E7%B1%BB%E7%9B%AE%E4%B8%8E%E5%B1%9E%E6%80%A7%E7%9A%84%E5%85%B3%E8%81%94"><span class="toc-number">1.8.1.</span> <span class="toc-text">1. 类目与属性的关联</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E7%B1%BB%E7%9B%AE%E4%B8%8E%E5%93%81%E7%89%8C%E7%9A%84%E5%85%B3%E8%81%94"><span class="toc-number">1.8.2.</span> <span class="toc-text">2. 类目与品牌的关联</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-9-%E5%B1%9E%E6%80%A7%E7%AE%A1%E7%90%86%E7%89%B9%E6%AE%8A%E8%A7%84%E5%88%99"><span class="toc-number">1.9.</span> <span class="toc-text">5.9 属性管理特殊规则</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%B1%9E%E6%80%A7%E5%88%86%E7%BB%84-%E8%AE%A9%E4%BF%A1%E6%81%AF%E6%9B%B4%E6%9C%89%E5%BA%8F"><span class="toc-number">1.9.1.</span> <span class="toc-text">1. 属性分组 - 让信息更有序</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%B1%9E%E6%80%A7%E7%BB%A7%E6%89%BF-%E8%AE%A9%E9%85%8D%E7%BD%AE%E6%9B%B4%E9%AB%98%E6%95%88"><span class="toc-number">1.9.2.</span> <span class="toc-text">2. 属性继承 - 让配置更高效</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-10-%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF"><span class="toc-number">1.10.</span> <span class="toc-text">5.10 运费模板</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%BB%80%E4%B9%88%E6%98%AF%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF%EF%BC%9F"><span class="toc-number">1.10.1.</span> <span class="toc-text">1. 什么是运费模板？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.10.2.</span> <span class="toc-text">2. 运费模板功能设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%B8%80%EF%BC%9A%E5%88%9B%E5%BB%BA%E4%B8%8E%E7%AE%A1%E7%90%86%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF"><span class="toc-number">1.10.2.1.</span> <span class="toc-text">模块一：创建与管理运费模板</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A8%A1%E5%9D%97%E4%BA%8C%EF%BC%9A%E5%95%86%E5%93%81%E5%85%B3%E8%81%94%E8%BF%90%E8%B4%B9%E6%A8%A1%E6%9D%BF"><span class="toc-number">1.10.2.2.</span> <span class="toc-text">模块二：商品关联运费模板</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-11-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.11.</span> <span class="toc-text">5.11 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>