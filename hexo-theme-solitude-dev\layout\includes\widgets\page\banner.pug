.author-content.author-content-item.single.sharePage(style=`background: url(${page.cover}) no-repeat center; background-size: cover;`)
    .card-content
        .author-content-item-tips= page.desc
        span.author-content-item-title= page.title
        .content-bottom
            if page.leftend
                .tips= page.leftend
            if page.rightend
                .tips= page.rightend
        if page.rightbtn
            .banner-button-group
                - const isInternalLink = page.rightbtnlink.startsWith('/')
                a.banner-button(onclick=isInternalLink ? `pjax.loadUrl("${page.rightbtnlink}")`: null, href=isInternalLink ? null : url_for(page.rightbtnlink), target=isInternalLink ? null : "_blank")
                    i.solitude.fas.fa-circle-chevron-right
                    span.banner-button-text= page.rightbtn
