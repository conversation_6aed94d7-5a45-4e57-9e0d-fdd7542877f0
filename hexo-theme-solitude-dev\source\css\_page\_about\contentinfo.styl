#about-page
  .myInfoAndWittyWord
    display flex
    flex-direction column
    justify-content center
    color var(--efu-white)
    background linear-gradient(120deg, #5b27ff 0, #00d4ff 100%)
    background-size 200%
    animation gradient 15s ease infinite
    flex 4

    .title1
      opacity .8
      line-height 1.3

    .title2
      font-size 36px
      font-weight 700
      line-height 1.1
      margin .5rem 0

    .inline-word
      word-break keep-all
      white-space nowrap

  .aboutsiteTips
    display flex
    justify-content center
    align-items flex-start
    flex-direction column
    flex 3

    h2
      margin-right auto
      width 100%
      font-size 36px
      font-family $font-family
      line-height 1.06
      letter-spacing -.02em
      color var(--efu-fontcolor)
      margin-top 0

    .mask
      height 36px
      position relative
      overflow hidden
      margin-top 4px

      span
        display block
        box-sizing border-box
        position absolute
        top 36px
        padding-bottom var(--offset)
        background-size 100% 100%
        -webkit-background-clip text
        background-clip text
        -webkit-text-fill-color transparent
        background-repeat no-repeat

        &[data-show]
          transform translateY(-100%)
          transition .5s transform ease-in-out

        &[data-up]
          transform translateY(-200%)
          transition .5s transform ease-in-out

      span:nth-child(1)
        background-image linear-gradient(45deg, #0ecffe 50%, #07a6f1)

      span:nth-child(2)
        background-image linear-gradient(45deg, #18e198 50%, #0ec15d)

      span:nth-child(3)
        background-image linear-gradient(45deg, #8a7cfb 50%, #633e9c)

      span:nth-child(4)
        background-image linear-gradient(45deg, #fa7671 50%, #f45f7f)