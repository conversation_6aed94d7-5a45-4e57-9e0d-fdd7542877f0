---
title: 第二部分：Hexo 基础搭建与配置
categories:
  - 框架技术
  - Hexo
tags:
  - 博客搭建教程
cover: 'https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp'
comments: true
toc: true
ai: true
abbrlink: 9132
date: 2025-06-19 18:13:45
---

## 第二部分：Hexo 基础搭建与配置

在上一部分，我们已经完成了 Hexo 的环境准备和基本项目的初始化。现在，我将带您进一步了解 Hexo 的基础使用，包括核心命令以及对站点全局配置文件的初步认识。

### 1. Hexo 常用基础命令

掌握 Hexo 的基础命令是高效管理博客的关键。以下是一些最常用的命令：

>如果您想在本地查看草稿的效果，普通的 `hexo server` 命令是看不到的，您必须使用一个特殊的命令：`hexo server --drafts` (或者简写 `hexo s --drafts`)，

| 命令                | 简写 | 功能描述                                                     | 常用场景                                   | 备注                                                         |
| :------------------ | :--- | :----------------------------------------------------------- | :----------------------------------------- | :----------------------------------------------------------- |
| `hexo init [folder]`| -    | 初始化一个新博客项目到指定文件夹。如果文件夹不存在，Hexo 会创建它。 | 首次创建博客项目。                         | 通常在空目录下执行。                                         |
| `hexo new <layout> <title>` | `hexo n` | 创建一篇新文件。`<layout>` 可以是 `post` (文章)、`page` (页面) 或 `draft` (草稿)，默认是 `post`。`<title>` 是文件名（不含扩展名）。 | 撰写新文章、创建关于页面等。               | 会根据 `scaffolds` 目录下的模板生成文件。                    |
| `hexo generate`     | `hexo g` | 生成静态网站文件到 `public` 目录。                           | 内容更新后准备部署前；本地预览前（`hexo s` 通常包含此步骤）。 | 会处理 `source` 目录下的 Markdown 文件、主题和配置。         |
| `hexo server`       | `hexo s` | 启动本地预览服务器。                                         | 在本地查看博客效果、调试主题或内容。       | 默认地址 `http://localhost:4000`，支持热重载。             |
| `hexo deploy`       | `hexo d` | 将生成的静态网站文件部署到远程服务器（如 GitHub Pages）。    | 将本地博客发布到线上。                     | 需要先安装对应的部署插件并配置 `_config.yml` 中的 `deploy` 部分。 |
| `hexo clean`        | -    | 清理 `public` 目录下的生成文件和 `db.json` 缓存文件。        | 遇到生成错误或需要强制完全重新生成时。     | 有助于解决一些缓存问题或文件冲突。                           |
| `hexo version`      | `hexo v` | 显示 Hexo、Node.js 和 npm 的版本信息。                       | 检查环境是否符合要求；排查版本兼容性问题。 |                                                              |

**常用命令组合：**

*   **撰写并预览：** `hexo new post "我的第一篇文章"` -> 编写内容 -> `hexo clean && hexo server` -> 浏览器预览。
*   **更新并部署：** 编写/修改内容 -> `hexo clean && hexo generate && hexo deploy` (或者更简单的 `hexo deploy -g`)。

> **场景化解释：**
> - 当我写完一篇新文章或者对已有文章进行了修改，我通常会先运行 `hexo clean` 清理掉旧的生成文件和缓存，然后运行 `hexo generate` 重新生成最新的静态网站。最后，为了验证修改是否正确，我会在本地运行 `hexo server` 进行预览。
> - 当我确认本地预览无误，准备发布到线上时，我只需要运行 `hexo deploy` 命令（如果配置了自动生成，可以使用 `hexo deploy -g`）。这个命令会将 `public` 目录下的所有文件推送到我配置好的托管平台（比如 GitHub Pages）。

### 2. Hexo 根目录配置文件 (`_config.yml`)

`_config.yml` 文件位于 Hexo 项目的根目录下，它是站点的全局配置文件。大部分重要的全局设置，如网站标题、副标题、作者、语言、URL、文章链接格式、主题等，都在这里进行配置。

这个文件采用 YAML 格式。YAML 格式使用缩进表示层级关系，键值对之间用冒号 `:` 分隔`（注意冒号后面需要加两个小空格`）。请务必注意缩进，Hexo 对 YAML 的缩进非常敏感，通常使用两个空格进行缩进。

默认的文件是英文的以下是 `_config.yml` 文件中的中文翻译

```yaml
# Hexo Configuration
## Docs: https://hexo.io/docs/configuration.html
## Source: https://github.com/hexojs/hexo/

# --- 网站信息 (Site) ---
# 这部分定义了您博客的基础信息，会显示在网站的各个位置。
# -----------------------------------------------------------
# 网站主标题，会显示在浏览器标签页和主题的显眼位置。
title: Hexo 
# 网站副标题，通常显示在主标题下方。
subtitle: ''
# 网站描述，主要用于SEO，告诉搜索引擎您的网站是关于什么内容的。
description: ''
# 网站关键词，用于SEO，多个关键词用英文逗号隔开。
keywords:
# 您的名字或昵称。
author: John Doe
# 网站语言。对于中文博客，强烈建议修改为 'zh-CN'。
language: en
# 网站时区。建议设置为您所在的时区，以确保文章发布时间的准确性。
# 例如：'Asia/Shanghai' (中国) 或 'Asia/Tokyo' (日本)。
# Hexo 默认使用您电脑的时区，但明确指定更好。
timezone: ''


# --- 网址 (URL) ---
# 这部分配置与您网站的链接结构（URL）密切相关，非常重要。
# -----------------------------------------------------------
# 【重要】请务必修改为您的网站最终的访问网址！
# 例如，如果您使用 GitHub Pages，它可能是 'https://yourname.github.io'。
# 这个配置会影响网站所有资源的绝对路径，如果错误，可能导致CSS、JS、图片加载失败。
url: http://example.com
# 文章的永久链接格式。
# :year, :month, :day, :i_month, :i_day, :hour, :minute, :second, :title, :name, :post_title, :id, :category
# 示例:
#   :year/:month/:day/:title/  (默认值，例如 2025/06/08/hello-world/)
#   :title.html               (例如 hello-world.html，非常简洁)
#   :category/:title/          (例如 tech/hello-world/)
# 推荐使用 hexo-abbrlink 插件生成短链接，对SEO友好且不会因修改标题而改变： permalink: posts/:abbrlink.html
permalink: :year/:month/:day/:title/
# 永久链接中各部分的默认值。
permalink_defaults:
# URL 美化选项。
pretty_urls:
  # 是否移除永久链接末尾的 'index.html'。通常保持默认。
  trailing_index: true 
  # 是否移除永久链接末尾的 '.html'。通常保持默认。
  trailing_html: true 


# --- 目录 (Directory) ---
# 这部分定义了您项目中的各个核心文件夹的名称，通常无需修改。
# -----------------------------------------------------------
# 源文件夹，您创作内容的地方（文章、图片等）。
source_dir: source
# 公共文件夹，存放最终生成的静态网站文件（最终部署到服务器上的内容）。
public_dir: public
# 标签页面的目录名。例如: yoursite.com/tags/
tag_dir: tags
# 归档页面的目录名。例如: yoursite.com/archives/
archive_dir: archives
# 分类页面的目录名。例如: yoursite.com/categories/
category_dir: categories
# 代码下载目录（如果您使用代码下载功能）。
code_dir: downloads/code
# 国际化（i18n）语言文件的目录。
i18n_dir: :lang
# 跳过渲染指定的文件或文件夹。您可以在这里列出不希望被Hexo处理的文件路径。
skip_render:


# --- 写作 (Writing) ---
# 这部分配置与您撰写文章时的行为相关。
# -----------------------------------------------------------
# 新文章的文件名格式。:title 是文章标题。
new_post_name: :title.md 
# 新建文件的默认布局，通常是 'post' (文章) 或 'draft' (草稿)。
default_layout: post
# 是否将文章标题转换为 "Title Case" (首字母大写)。建议 'false'，保持原文案。
titlecase: false 
# 外部链接设置。
external_link:
  # 是否在新标签页中打开外部链接。建议 'true'，以保留用户在您的网站上。
  enable: true 
  # 应用范围。'site' 表示全站，'post' 表示仅文章内容。
  field: site 
  # 在这里列出的域名将不会被当作外部链接处理。例如: 'exclude: yoursite.com'
  exclude: ''
# 文件名大小写转换。0: 无变化; 1: 小写; 2: 大写。
filename_case: 0
# 是否渲染 'source/_drafts' 文件夹中的草稿。'false' 表示默认不渲染。
render_drafts: false
# 是否启用文章资源文件夹。
# 如果设为 'true'，当您用 `hexo new post "xxx"` 创建文章时，
# 会在 `source/_posts` 目录下同时创建一个名为 "xxx" 的文件夹，方便您存放该文章专属的图片等资源。
post_asset_folder: false
# 是否将链接转换为与根目录的相对路径。通常保持 'false'。
relative_link: false
# 是否渲染发布日期在未来的文章。'true' 表示会渲染。
future: true
# 代码高亮引擎。可选值: 'highlight.js' 或 'prismjs'。
# Butterfly 等现代主题通常有自己的高亮方案，可能会覆盖此设置。
syntax_highlighter: highlight.js
# highlight.js 的具体配置。
highlight:
  # 是否显示行号。
  line_number: true
  # 是否自动检测语言。建议 'false' 以获得更好的性能和准确性。
  auto_detect: false
  # 用什么字符替换 Tab。
  tab_replace: ''
  # 是否用 `<table>` 包裹代码块以实现复杂的行号显示。
  wrap: true
  # 是否启用 highlight.js 内置的样式。通常主题会有自己的样式，所以设为 'false'。
  hljs: false
# prismjs 的具体配置。
prismjs:
  # 是否在预处理阶段进行语法高亮。
  preprocess: true
  # 是否显示行号。
  line_number: true
  # 用什么字符替换 Tab。
  tab_replace: ''


# --- 主页设置 (Home page setting) ---
# 这部分控制您博客首页的文章列表行为。
# -----------------------------------------------------------
index_generator:
  # 首页的路径。空字符串 '' 表示网站根目录。
  path: ''
  # 每页显示的文章数量。0 表示禁用分页。
  per_page: 10
  # 文章排序方式。'-date' 表示按日期降序（最新的在最前），'date' 表示升序。
  order_by: -date


# --- 分类与标签 (Category & Tag) ---
# -----------------------------------------------------------
# 默认分类。当文章没有指定分类时，会使用此分类。
default_category: uncategorized
# 分类别名。例如: 'cate_alias: my-cate'
category_map:
# 标签别名。例如: 'tag_alias: my-tag'
tag_map:


# --- 元数据 (Metadata elements) ---
# -----------------------------------------------------------
# 是否在HTML头部注入 Hexo 的 meta generator 标签。有助于进行网站技术栈统计，建议保留。
meta_generator: true


# --- 日期与时间格式 (Date / Time format) ---
# Hexo 使用 Moment.js 库来处理时间格式。
# 格式定义: http://momentjs.com/docs/#/displaying/format/
# -----------------------------------------------------------
# 日期显示格式。
date_format: YYYY-MM-DD
# 时间显示格式。
time_format: HH:mm:ss
# 文章更新时间的选项。
# 'mtime': 使用文件的最后修改时间作为更新时间 (推荐)。
# 'date': 使用 Front-matter 中的 'date' 字段作为更新时间。
# 'empty': 不使用更新时间。
updated_option: 'mtime'


# --- 分页 (Pagination) ---
# 归档页（如分类页、标签页）的分页设置。
# -----------------------------------------------------------
# 每页显示的文章数量。0 表示禁用分页。
per_page: 10
# 分页的目录。例如: yoursite.com/page/2/
pagination_dir: page


# --- 包含与排除文件 (Include / Exclude file(s)) ---
# 这些选项仅对 'source/' 文件夹生效。
# -----------------------------------------------------------
# Hexo 默认会忽略隐藏文件和以 '_' 或 '#' 开头的文件/文件夹。
# include: [.well-known] # 如果您需要 Hexo 处理某些被忽略的文件，可以在这里列出。
include:
# exclude: [temp/] # 如果您希望 Hexo 忽略 'source/' 下的某些文件或文件夹，可以在这里列出。
exclude:
# ignore: [*.log] # 全局忽略规则。
ignore:


# --- 扩展 (Extensions) ---
# -----------------------------------------------------------
## 插件: https://hexo.io/plugins/
## 主题: https://hexo.io/themes/
# [重要] 当前使用的主题名称。请确保 'themes' 文件夹下有对应名称的主题文件夹。
# 例如，要使用 Butterfly 主题，请修改为: 'theme: butterfly'
theme: landscape


# --- 部署 (Deployment) ---
# `hexo deploy` 命令的配置。
# Docs: https://hexo.io/docs/one-command-deployment
# -----------------------------------------------------------
deploy:
  # 部署类型。例如，部署到 GitHub Pages，需要安装 `hexo-deployer-git` 插件，并将类型设为 'git'。
  # 示例:
  #   type: git
  #   repo: **************:yourname/yourname.github.io.git
  #   branch: main
  type: ''
```

> **场景化解释：**
> - 修改 `title`, `subtitle`, `description`, `author`, `language` 会直接影响博客的全局信息，比如在浏览器标签页、搜索引擎结果以及主题的某些位置显示。当我第一次搭建博客时，会在这里填入我的博客名称、简介、作者信息等。
> - `url` 配置项至关重要，它告诉 Hexo 您的博客最终部署到哪个网址。如果此处配置错误，可能导致生成的静态文件中资源路径错误（比如 CSS/JS 加载失败），使得网站只有文字而没有样式。
> - `permalink` 决定了您文章的 URL 形式。我个人比较喜欢简洁的 `:category/:title.html` 或 `:year/:month/:day/:title/` 格式，方便记忆和分享。

在开始自定义主题之前，我建议您根据自己的信息修改 `_config.yml` 文件中的 `title`、`author` 和 `url` 等基本信息。`language` 建议修改为 `zh-CN` 以便更好地显示中文内容和使用中文主题。

至此，我们已经完成了 Hexo 的基础搭建，了解了核心命令，并对站点配置文件有了初步认识。在下一部分，我们将正式安装并配置 魔改的Butterfly 主题，让您的博客焕然一新。



### `3.页面配置（重点）`

**Front-matter 的基本认识**

`Front-matter` 是 `markdown` 文件最上方以 `---` 分隔的区域，用于指定个别档案的变数。其中又分为两种 markdown 里

##### 1.Page Front-matter 用于页面配置

```yaml
---
# ===================================================================
#                          必需字段 (Required)
# ===================================================================

# 【默认通过hexo可以生成】页面的标题。
# 它会显示在浏览器标签页、文章列表以及页面顶部。
title: 友情链接

# 【默认通过hexo可以生成】页面的创建日期。
# 推荐格式为：YYYY-MM-DD HH:mm:ss。
date: 2025-06-09 20:00:00

# 【必需】页面的类型或布局。
# 这个字段至关重要，它会告诉主题该如何渲染这个页面。
# 您需要根据所用主题的文档来填写，常见值有：
# - tags: 标签页
# - categories: 分类页
# - about: 关于页
# - link: 友情链接页
# - photos: 相册页
# - moment / friends: 朋友圈或即刻页
type: link


# ===================================================================
#                      可选字段 - 内容与SEO (Optional)
# ===================================================================

# 【可选】页面的最后更新日期。
# 如果忽略，其值通常会默认等于创建日期 `date`。
updated: 2025-06-09 21:30:00

# 【可选】页面的描述。
# 这段文字主要用于 SEO，会显示在搜索引擎的结果摘要中，强烈建议认真填写。
description: "这里是我的个人博客的友情链接页面，汇集了许多优秀博主的网站，欢迎大家参观和交换友链！"

# 【可选】页面的关键词，多个关键词用英文逗号隔开。
# 同样主要用于 SEO，帮助搜索引擎理解页面内容。
keywords: "友情链接, 博客圈, 技术博客, 生活分享, Hexo"


# ===================================================================
#                      可选字段 - 功能开关 (Optional)
# ===================================================================

# 【可选】是否在此页面显示评论模块。
# true 为显示（默认），false 为关闭。
comments: true

# 【可选】是否为本页面单独开启数学公式渲染。
# 仅当主题全局配置中 per_page 设置为 false 时，这两个选项才需要在此处单独开启。
mathjax: false
katex: false

# 【可选】是否显示侧边栏。
# true 为显示（默认），false 为隐藏。若想打造沉浸感强的页面可设为 false。
aside: true

# 【可选】是否在本页面加载 APlayer 音乐播放器。
# 设置为 true 后，还需在页面正文中通过特定标签来配置播放列表。
aplayer: false

# 【可选】是否折叠本页面的所有代码块。
# true 为折叠，false 为展开。可用于覆盖主题的全局设置。
highlight_shrink: false


# ===================================================================
#                    可选字段 - 样式与外观 (Optional)
# ===================================================================

# 【可选】页面的顶部大图（Banner）。
# 路径是相对于博客 `source` 目录的绝对路径。
top_img: /img/banners/friends.jpg

# 【可选】为某些特殊页面的顶部模块设置背景图片。
# 这通常是平铺或覆盖的背景，而非 Banner 大图，用于增强设计感。
top_single_background: /img/backgrounds/simple_sky.png

---

# 这里是页面的正文 Markdown 内容...
# ...
```

##### 2.Post Front-matter 用于文章页配置

```yaml
---
# ===================================================================
#                      基本信息 (Required & Core)
# ===================================================================

# 【必需】文章的标题。
title: 深入解析PWA核心技术与未来展望

# 【必需】文章的创建日期。
# 格式为 YYYY-MM-DD HH:mm:ss。
date: 2025-06-09 18:30:00

# 【可选】文章的最后更新日期。
# 如果您修改了旧文章，强烈建议更新此时间，有助于SEO。
updated: 2025-06-09 20:15:00


# ===================================================================
#                      内容分类 (Categories & Tags)
# ===================================================================

# 【可选】文章分类。
# 分类具有层级关系（父子关系），适合用于组织结构化的内容。
# - 父分类
#   - 子分类
categories:
  - 前端技术
  - Web App

# 【可选】文章标签。
# 标签是扁平化的，没有层级关系，适合用于描述文章的关键词。
tags:
  - PWA
  - Hexo
  - Service Worker


# ===================================================================
#                      SEO 与摘要 (SEO & Description)
# ===================================================================

# 【可选】文章的关键词，用英文逗号隔开。主要用于SEO。
keywords: "PWA, Progressive Web App, Service Worker, Hexo博客"

# 【可选】文章的摘要或描述。
# 会显示在首页文章卡片和搜索引擎结果中，是吸引点击的关键。
description: "本文将从 Service Worker、Manifest 等核心组件出发，深入探讨PWA的工作原理，并展望其在未来的应用场景。"

# 【可选】文章 AI 摘要功能的开关。
# 具体作用需参考您所用主题的文档。
ai: true


# ===================================================================
#                      页面展示与样式 (Display & Style)
# ===================================================================

# 【可选】文章顶部的横幅大图。
top_img: /img/banners/pwa-banner.png

# 【可选】文章的缩略图（封面）。
# 1. 填写图片地址，则首页和文章页都用此图。
# 2. 设为 false，则不显示缩略图。
# 3. 若不设置此项，但设置了 `top_img`，则通常会用 `top_img` 作为封面。
cover: /img/covers/pwa-cover.png

# 【可选】文章的主色调。
# 必须是6位的16进制颜色值，如 #123456，不能缩写为 #123。
main_color: "#005af0"


# ===================================================================
#                首页推荐 (Homepage Features)
# ===================================================================

# 【可选】配置文章在首页轮播图中显示。
# 数字越小，位置越靠前。设置此项即可让文章进入轮播。
swiper_index: 1

# 【可选】配置文章在首页右侧推荐卡片组中显示。
# 数字越小，位置越靠前。
top_group_index: 2


# ===================================================================
#                     功能开关 (Feature Toggles)
# ===================================================================

# 【可选】是否显示文章的评论模块。默认为 true。
comments: true

# 【可选】TOC (Table of Contents) 目录的相关设置。
# 用于覆盖主题的全局默认配置。
toc: true # 是否显示目录
toc_number: false # 目录是否显示编号
toc_style_simple: true # 是否使用简洁样式的目录

# 【可选】文章版权模块的详细设置。
# 您可以在此覆盖全局的版权信息，为特定文章（如转载）设置不同的版权。
copyright: true # 是否显示版权模块
copyright_author: "特邀作者 张三" # 自定义文章作者
copyright_author_href: "https://example.com/zhangsan" # 自定义作者链接
copyright_url: "https://example.com/original-post" # 自定义文章源链接
copyright_info: "本文为特邀作者原创，转载请联系作者获得授权。" # 自定义版权声明文字

# 【可选】高级功能开关。
# 通常用于在全局关闭某功能时，为特定文章单独开启。
mathjax: false
katex: false
aplayer: false
highlight_shrink: true # true 代表本页代码块默认折叠
aside: true # true 代表本页显示侧边栏


---
```