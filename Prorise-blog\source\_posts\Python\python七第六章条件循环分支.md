---
title: Python（七）：第六章：条件循环分支
categories:
  - 后端技术
  - Python
tags:
  - Python基础知识总汇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp'
comments: true
toc: true
ai: true
abbrlink: 45310
date: 2025-04-18 22:13:45
---

## 第六章：条件循环分支

### 条件语句的高级用法

#### 链式比较

Python 支持数学风格的链式比较，使得条件表达式更简洁明了。

```python
# 传统方式
if x > 0 and x < 10:
    print("x在0到10之间")

# 链式比较
if 0 < x < 10:
    print("x在0到10之间")
    
# 多重链式比较
if 10 <= x < 20 <= y < 30:
    print("x在10到20之间且y在20到30之间")
```

#### 短路逻辑评估

Python 使用短路逻辑评估条件表达式，即一旦表达式的真假已经确定，后续部分不再执行。

```python
# 短路与(and)
if expensive_function() and rare_condition():
    # 如果expensive_function()返回False，不会执行rare_condition()
    do_something()

# 短路或(or)
if quick_check() or expensive_operation():
    # 如果quick_check()返回True，不会执行expensive_operation()
    do_something()
```

#### 条件表达式的嵌套

可以在条件表达式内部嵌套其他条件表达式，创建复杂的决策树。

```python
# 复杂嵌套的三元表达式
result = (
    "高分" if score >= 90 else 
    "良好" if score >= 80 else 
    "及格" if score >= 60 else 
    "不及格"
)

# 更复杂的嵌套条件
category = (
    "儿童" if age < 12 else
    "青少年" if age < 18 else
    "成人" if age < 65 else
    "老年人"
)
```

#### 模式匹配（Python 3.10+）

Python 3.10 引入了结构化模式匹配，类似于其他语言的 switch/case 语句，但功能更强大。

```python
# 基本模式匹配
def analyze_type(data):
    match data:
        case int():
            return "整数"
        case float():
            return "浮点数"
        case str():
            return "字符串"
        case list():
            return "列表"
        case dict():
            return "字典"
        case _:
            return "其他类型"
#===========#===========#===========#===========#===========#===========#===========#===========
# 结构匹配和变量绑定
def process_point(point):
    match point:
        case (0, 0):
            return "原点"
        case (0, y):
            return f"Y轴上的点 y={y}"
        case (x, 0):
            return f"X轴上的点 x={x}"
        case (x, y) if x == y:
            return f"对角线上的点 ({x}, {y})"
        case (x, y):
            return f"普通点 ({x}, {y})"

# 匹配对象属性
class Point:
    def __init__(self, x, y):
        self.x = x
        self.y = y

def analyze_point(point):
    match point:
        case Point(x=0, y=0):
            return "原点"
        case Point(x=0, y=y):
            return f"Y轴上的点 y={y}"
        case Point(x=x, y=0):
            return f"X轴上的点 x={x}"
        case Point(x=x, y=y) if x == y:
            return f"对角线上的点 ({x}, {y})"
        case Point():
            return f"普通点 ({point.x}, {point.y})"
```

### 循环的高级技巧

#### 循环与迭代器协议

理解迭代器协议有助于更高效地编写循环代码：

```python
class CountDown:
    def __init__(self, start):
        self.start = start

    def __iter__(self):
        """
        返回迭代器对象
        
        这个方法使CountDown类的实例成为一个可迭代对象。
        当使用for循环遍历该对象时，Python会自动调用这个方法获取迭代器。
        
        返回:
            self: 返回自身作为迭代器
        """
        return self

    def __next__(self):
        """
        获取迭代器中的下一个值
        
        这个方法定义了迭代过程中如何获取下一个元素。
        每次调用时，计数器减1并返回减1前的值。
        当计数器减到0时，抛出StopIteration异常表示迭代结束。
        """
        if self.start <= 0:  # 如果当前值小于等于0，表示迭代已结束
            raise StopIteration
        current = self.start  # 保存当前值
        self.start -= 1       # 计数器减1
        return current        # 返回减1前的值

    
# 测试代码
for i in CountDown(5):
    print(i)
```

#### 生成器表达式代替列表推导式

生成器表达式在处理大量数据时更节省内存：

```python
import sys
# 列表推导式 - 一次性创建所有元素
sum_of_squares = sys.getsizeof(sum([x*x for x in range(1000000)]))  # 占用大量内存
print(f"占用内存: {sum_of_squares} bytes") # 占用内存: 32 bytes

# 生成器表达式 - 按需生成元素
sum_of_squares = sys.getsizeof(sum(x*x for x in range(1000000)))    # 内存效率高
print(f"占用内存: {sys.getsizeof(sum_of_squares)} bytes") # 占用内存: 28 bytes
```

#### 使用 enumerate 获取索引

```python
# 传统方式
fruits = ['apple', 'banana', 'cherry']
for i in range(len(fruits)):
    print(f"{i + 1}. {fruits[i]}")

# 使用enumerate更优雅（并可指定起始索引）
for i, fruit in enumerate(fruits, 1):  # 从1开始计数
    print(f"{i}. {fruit}")
```

#### 并行迭代多个序列

```python
# 使用zip()并行迭代
names = ['Alice', 'Bob', 'Charlie']
ages = [24, 32, 28]
cities = ['New York', 'Boston', 'Chicago']

for name, age, city in zip(names, ages, cities):
    print(f"{name}, {age}岁, 来自{city}")
```

### 流程控制的高级模式

#### 嵌套循环的优化

```python
# 嵌套循环的替代方案
import itertools

# 传统嵌套循环
results = []
for x in range(3):
    for y in range(3):
        for z in range(3):
            results.append((x, y, z))

# 使用product代替嵌套循环
results = list(itertools.product(range(3), range(3), range(3)))
```



#### 递归与循环

有些问题使用递归比循环更直观：

> 通过下列的代码处理思想，对于常见的树结构都能以递归的思想去解决问题

```python
# 递归处理嵌套结构
def process_nested_list(nested_list, depth=0):
    result = []
    for item in nested_list:
        if isinstance(item, list):
            # 递归处理子列表
            sub_result = process_nested_list(item, depth + 1)
            result.extend(sub_result)
        else:
            # 处理叶节点
            result.append((depth, item))
    return result


# 递归执行逻辑解析:
# 1. 函数接收一个嵌套列表和当前深度(默认为0)
# 2. 遍历列表中的每个元素:
#    - 如果元素是列表，递归调用自身处理该子列表，深度+1
#    - 如果元素不是列表，将(深度,元素值)添加到结果中
# 3. 最终返回包含所有元素及其深度信息的扁平列表
#
# 例如处理 [1, [2, [3, 4], 5], 6]:
# - 1不是列表，添加(0,1)
# - [2,[3,4],5]是列表，递归处理:
#   - 2不是列表，添加(1,2)
#   - [3,4]是列表，递归处理:
#     - 3不是列表，添加(2,3)
#     - 4不是列表，添加(2,4)
#   - 5不是列表，添加(1,5)
# - 6不是列表，添加(0,6)
# 最终结果: [(0,1), (1,2), (2,3), (2,4), (1,5), (0,6)]

# 使用示例
data = [1, [2, [3, 4], 5], 6]
print(process_nested_list(data))  # [(0, 1), (1, 2), (2, 3), (2, 4), (1, 5), (0, 6)]
```