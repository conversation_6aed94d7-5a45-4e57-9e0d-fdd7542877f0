/* --- 评论框输入提示 CSS --- */
/* 设置文字内容 :nth-child(1)的作用是选择第几个输入框 */
.el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(1):before {
    content: '输入QQ号会自动获取昵称和头像🐧';
}

.el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(2):before {
    content: '收到回复将会发送到您的邮箱📧';
}

.el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(3):before {
    content: '可以通过昵称访问您的网站🔗';
}

/* 当用户点击输入框时显示提示 */
.el-input.el-input--small.el-input-group.el-input-group--prepend:focus-within::before,
.el-input.el-input--small.el-input-group.el-input-group--prepend:focus-within::after {
    display: block;
}

/* 提示气泡的主体样式 */
.el-input.el-input--small.el-input-group.el-input-group--prepend::before {
    display: none;
    position: absolute;
    top: -60px;
    white-space: nowrap;
    border-radius: 10px;
    left: 50%;
    transform: translate(-50%);
    padding: 14px 18px;
    background: #444;
    color: #fff;
    z-index: 10; /* 确保在顶层 */
}

/* 提示气泡的小三角 */
.el-input.el-input--small.el-input-group.el-input-group--prepend::after {
    display: none;
    content: '';
    position: absolute;
    border: 12px solid transparent;
    border-top-color: #444;
    left: 50%;
    transform: translate(-50%, -48px);
    z-index: 10; /* 确保在顶层 */
}


/* --- 表情悬停放大 CSS --- */
#owo-big {
    position: fixed;
    display: none;
    align-items: center;
    background-color: rgb(255, 255, 255);
    border: 1px #aaa solid;
    border-radius: 10px;
    z-index: 9999;
    transform: translate(0, -105%);
    overflow: hidden;
    animation: owoIn 0.3s cubic-bezier(0.42, 0, 0.3, 1.11);
}

[data-theme=dark] #owo-big {
    background-color: #4a4a4a
}

#owo-big img {
    width: 100%;
}

/* 动画效果代码由 Heo 提供 */
@keyframes owoIn {
    0% {
        transform: translate(0, -95%);
        opacity: 0;
    }
    100% {
        transform: translate(0, -105%);
        opacity: 1;
    }
}