/* --- 定义英文字体 (包含数字和常用标点) --- */
@font-face {
    font-family: 'MyHybridFont'; /* 【重要】给这个“混合字体”起一个统一的名字 */
    font-display: swap;
    src: url('/fonts/custom_en.woff2') format('woff2');
    /* 指定该字体只应用于ASCII字符、数字和基本标点符号 */
    unicode-range: U+0020-007E, U+00A0-00FF; 
  }
  
  /* --- 定义中文字体 --- */
  @font-face {
    font-family: 'MyHybridFont'; /* 【重要】使用和上面完全相同的名字 */
    font-display: swap;
    src: url('/fonts/custom_cn.woff2') format('woff2');
    /* 指定该字体只应用于中文字符区域 (这里涵盖了大部分常用汉字和全角符号) */
    unicode-range: U+4E00-9FFF, U+3000-303F, U+FF00-FFEF;
  }