---
title: SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 60609
date: 2025-03-21 04:13:45
---

## **5. 连接AI大脑：Chat Models 对接指南**

在前面的章节中，我们已经掌握了与AI交互的“软件”——`ChatClient`和Prompt工程。现在，是时候为我们的 AI-Copilot 应用挑选并安装一个强大的“硬件”——也就是背后的大语言模型（LLM）了。

本章将作为一份**快速参考手册**，我们将聚焦于国内领先的AI服务提供商**智谱AI (ZhiPu AI)**，并完整保留对其他主流模型的介绍。我们的核心实战任务是：为 AI-Copilot 应用增加一个**“深度思考”模式**，允许用户在**通用模型**和**旗舰推理模型**之间自由切换，并让前端界面能够实时响应这种变化。

### **5.1 模型选型总览**

Spring AI 的可移植性设计意味着，无论你选择哪家模型，你的核心业务代码（如 `ChatService`）几乎不需要改变。切换模型主要通过修改 `pom.xml` 依赖和 `application.yml` 配置文件来完成。

#### **5.1.1 聊天模型功能总览**

下表比较了 Spring AI 支持的各种主流聊天模型，并详细介绍了它们的核心功能：

| 提供商 (Provider) | 多模态 (图像/音视频) | 工具/函数调用 | 流式响应 | JSON 输出 | 本地部署 | OpenAI API 兼容 |
| :--- | :---: | :---: | :---: | :---: | :---: | :---: |
| **智谱AI (ZhiPu AI)** | ✅ (GLM-4V) | ✅ | ✅ | ✅ | ❌ | ❌ |
| **文心千帆 (QianFan)** | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| **Ollama** | ✅ (LLaVA等模型) | ✅ (部分模型) | ✅ | ✅ | ✅ | ✅ |
| **Vertex AI Gemini** | ✅ (图像,音频,视频,PDF) | ✅ | ✅ | ✅ | ❌ | ❌ |
| **OpenAI** | ✅ (图像,音频) | ✅ | ✅ | ✅ | ❌ | ✅ |
| **Azure OpenAI** | ✅ (图像) | ✅ | ✅ | ✅ | ❌ | ✅ |

#### **5.1.2 如何选择？**

| 考量维度 | 商业 SaaS 模型 (如智谱, Gemini) | 开源本地化模型 (通过 Ollama) |
| :--- | :--- | :--- |
| **性能与能力** | 通常处行业顶尖，功能最全面 | 性能取决于模型和硬件，特定任务上已足够优秀 |
| **成本** | 按Token量计费，需关注成本控制 | 无API费用，但有硬件、电力和维护成本 |
| **数据隐私** | 数据需发送至云端，需信任服务商 | **数据完全本地化**，拥有最高级别控制权 |

### **5.2 重点讲解：智谱 AI 模型家族**

作为我们项目的核心选型，智谱AI提供了丰富多样的模型，以满足从通用聊天到复杂推理的各种场景需求。

#### **5.2.1 模型对比与应用场景解析**

| 模型系列 | 模型编码 (部分) | 核心能力与特长 | 推荐使用场景 |
| :--- | :--- | :--- | :--- |
| **旗舰语言 (`GLM-4-Plus`)** | `glm-4-plus` | **最强旗舰，性能卓越**，在推理、长文本、语言能力上全面领先。| 高质量内容创作、复杂翻译、风险评估报告。 |
| **通用语言 (`GLM-4`)** | `glm-4-air`, `glm-4-airx` | 平衡性能、速度与成本，具备强大的通用对话和文本能力。 | **AI-Copilot默认模型**、通用聊天、文本摘要。 |
| **推理 (`GLM-Z1`)** | `glm-z1-air`, `glm-z1-airx` | **逻辑推理、数学、编程能力突出**，支持代码执行。 | 代码生成与解释、科学计算、金融分析。 |
| **视觉理解 (`GLM-4V`)** | `glm-4v-plus-0111` | **图文理解（多模态）**，能够理解和描述图像/视频内容。 | 图像问答、视觉内容分析、视频描述。 |

  * **通用语言模型 (GLM-4 系列)**: 是我们 **AI-Copilot** 项目的**默认聊天模型**，用于处理绝大多数日常对话。
  * **旗舰/推理模型 (`GLM-4-Plus` / `GLM-Z1`)**: 是我们“深度思考”模式的备选。当用户需要更高质量、更具逻辑性的回答时，我们将动态切换到这些模型。

### **5.3 实战：为 AI-Copilot 集成“深度思考”模式**

#### **5.3.1 后端实现：动态模型切换**

我们的目标是让一个API端点能够根据前端传递的参数，智能地选择使用哪个智谱模型。

1.  **添加 Maven 依赖与 `application.yml` 配置**

      * **`pom.xml`**: 确保已添加 `spring-ai-starter-model-zhipuai`。
      * **`application.yml`**:
        ```yaml
        spring:
          ai:
            zhipuai:
              api-key: "YOUR_ZHIPUAI_API_KEY"
              chat:
                options:
                  # 设置一个通用、高性价比的模型作为默认值
                  model: glm-4-air 
        ```








### **5.4 其他模型快速参考手册（完整版）**

#### **5.4.1 OpenAI (及兼容模型)**

  * **Maven**: `spring-ai-starter-model-openai`
  * **YAML**:
    ```yaml
    spring:
      ai:
        openai:
          base-url: https://api.deepseek.com
          api-key: ${DEEPSEEK_API_KEY}
          chat:
            options: { model: deepseek-chat, temperature: 0.7 }
    ```
  * **代码实践**:
    可在运行时通过 `OpenAiChatOptions` 覆盖 `temperature` 等参数，以适应不同场景（如创意写作 vs. 严谨问答）。

#### **5.4.2 Azure OpenAI**

  * **Maven**: `spring-ai-starter-model-azure-openai`
  * **YAML**:
    ```yaml
    spring:
      ai:
        azure:
          openai:
            endpoint: https://your-azure-resource.openai.azure.com/
            api-key: ${AZURE_OPENAI_API_KEY}
            chat:
              options:
                deployment-name: my-gpt4o-deployment
    ```
  * **核心优势**: 企业级的**安全与合规性**，支持私有网络（VNet）部署和 AAD 认证。

#### **5.4.3 Google Vertex AI Gemini**

  * **Maven**: `spring-ai-starter-model-vertex-ai-gemini`
  * **YAML**:
    ```yaml
    spring:
      ai:
        vertex:
          ai:
            gemini:
              project-id: "your-gcp-project-id"
              location: "us-central1"
              chat:
                options: { model: gemini-1.5-flash-latest }
    ```
  * **特色功能**: 强大的**多模态输入**能力，可在 `UserMessage` 中轻松集成图像、PDF等。

#### **5.4.4 Ollama (本地化部署)**

  * **Maven**: `spring-ai-starter-model-ollama`
  * **YAML**:
    ```yaml
    spring:
      ai:
        ollama:
          base-url: http://localhost:11434
          chat:
            options: { model: qwen:7b }
    ```
  * **特色功能**: 支持**模型自动拉取**，极大地方便了开发和测试。

-----

至此，我们不仅深入了解了智谱AI模型家族并实现了动态切换的实战功能，还完整地回顾了如何集成其他主流模型。我们的 AI-Copilot 应用现在拥有了前所未有的灵活性，可以根据任务需求，随时调用最合适的“大脑”。



-----