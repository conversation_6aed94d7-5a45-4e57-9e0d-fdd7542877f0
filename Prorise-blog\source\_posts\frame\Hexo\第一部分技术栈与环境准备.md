---
title: 第一部分：技术栈与环境准备
categories:
  - 框架技术
  - Hexo
tags:
  - 博客搭建教程
cover: 'https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp'
comments: true
toc: true
ai: true
abbrlink: 33216
date: 2025-06-19 17:13:45
---

## 第一部分：技术栈与环境准备

搭建 Hexo 博客需要准备以下基础环境：

1.  **Node.js**：Hexo 是一个基于 Node.js 的静态博客框架，所以 Node.js 是必须安装的。Node.js 内置了 npm (Node Package Manager)，我们将用它来安装 Hexo 和其他依赖。
2.  **Git**：Git 是一个分布式版本控制系统，我们将使用它来管理博客的源文件，并方便地部署博客到 GitHub Pages 或其他平台。

这些工具在搭建 Hexo 博客的工作流中扮演着核心角色。

### 1. 安装 Node.js

我建议您安装 Node.js 的 **LTS (长期支持)** 版本，这个版本稳定性最好，适合多数用户。

- **实际应用场景：** 首次接触 Node.js 或需要稳定开发环境的用户。

访问 Node.js 官方网站的下载页面：

![Node.js官方LTS版本下载页面截图](https://miro.medium.com/v2/resize:fit:1400/1*NVrQ1i3cPL8Vhaxgkk13AA.png)

根据您的操作系统（Windows、macOS 或 Linux）下载对应的 LTS 版本安装包，并按照安装向导提示完成安装。安装过程通常只需要一路“下一步”即可。

安装完成后，打开您的命令行工具（Windows 用户推荐使用 Git Bash 或 PowerShell，macOS/Linux 用户使用自带的 Terminal），输入以下命令验证 Node.js 和 npm 是否安装成功以及版本：

```bash
node -v # 查看 Node.js 版本
npm -v  # 查看 npm 版本
```

![在命令行中成功安装Node.js和Git，并验证版本的截图](https://miro.medium.com/v2/resize:fit:1400/1*dmgagL8FW-AayKMN4XKP4g.png)

如果正确显示版本号，说明 Node.js 和 npm 已成功安装并配置到系统环境变量中。

### 2. 安装 Git

Git 是用于版本控制和后续部署的关键工具。

- **实际应用场景：** 需要进行代码版本管理、与远程仓库同步（如部署到 GitHub Pages）的用户。

访问 Git 官方网站的下载页面：

![Git官方下载页面截图](https://shaileshjha.com/wp-content/uploads/2020/03/git_scm_webpage_v_2_30_0.jpg)

根据您的操作系统下载对应的安装包。

-   **Windows 用户：** 推荐下载 Git for Windows，安装时可以选择安装 Git Bash，它是一个独立的命令行工具，提供了 Linux 风格的命令环境，使用起来更方便。安装过程中大部分选项保持默认即可，但在选择默认编辑器时，如果您不熟悉 Vim，可以改为 VS Code 或其他您常用的编辑器。
-   **macOS 用户：** 可以通过 Homebrew 安装 (`brew install git`)，或者直接下载官方安装包。
-   **Linux 用户：** 大多数发行版可以通过包管理器安装 (`sudo apt-get install git` 或 `sudo yum install git`)。

安装完成后，同样在命令行中输入以下命令验证 Git 是否安装成功：

```bash
git --version # 查看 Git 版本
```

如果显示版本号，说明 Git 已成功安装。

为了在后续部署到 GitHub/Gitee 等平台时正确标识您的身份，我建议您配置 Git 的全局用户名和邮箱：

```bash
git config --global user.name "您的用户名" # 设置全局用户名
git config --global user.email "您的邮箱" # 设置全局邮箱
```

> **场景化解释：** 配置全局用户名和邮箱非常重要，它们会出现在您每一次使用 Git 提交（commit）代码时的记录中，告诉其他人（和您自己）是谁进行了这次修改。这对于版本追踪和协作至关重要。

### 3. 安装 Hexo CLI

环境准备就绪后，我们就可以安装 Hexo 的命令行工具 (`hexo-cli`) 了。`hexo-cli` 是 Hexo 提供的脚手架工具，用于快速创建、生成和部署博客。

在命令行中执行以下命令进行全局安装：

```bash
npm install -g hexo-cli # 通过 npm 全局安装 Hexo 命令行工具
```

全局安装后，`hexo` 命令就可以在系统的任何位置使用了。

### 4. 初始化您的博客项目

现在，我们来创建一个新的 Hexo 博客项目。选择一个您喜欢的目录，比如 `D:\my-blog` 或 `/Users/<USER>/Documents/my-blog`，然后在命令行中进入这个目录（如果目录不存在，可以先创建它）。

使用 `hexo init` 命令初始化项目：

```bash
# 切换到您想要创建博客项目的目录，例如：
# cd D:\ 或 cd /Users/<USER>/Documents/

hexo init Prorise-blog # 初始化一个名为 my-hexo-blog 的新博客项目
cd Prorise-blog # 进入新创建的博客目录

# 安装项目依赖
npm install # Hexo 初始化后会创建 package.json，npm install 会根据它安装所有必需的 Node.js 模块
```

> **场景化解释：** `hexo init <folder>` 命令会在当前目录下创建一个新的文件夹 `<folder>`，并在其中生成一个完整的 Hexo 博客框架所需的所有文件和文件夹。`cd <folder>` 命令是进入到这个新创建的博客项目目录中，后续所有的 Hexo 命令都需要在这个目录下执行。`npm install` 则是下载 Hexo 运行所需的各种库文件，确保项目能够正常构建和生成。

初始化完成后，您的项目目录会包含以下基本结构：

```
Prorise-blog/
│
├── .github/          # 存放与 GitHub 相关配置的文件夹。最常见的用途是配置 GitHub Actions，用于实现 CI/CD（持续集成/持续部署），例如在您推送代码后自动构建和部署您的博客。
│
├── node_modules/     # 存放项目所有依赖模块的文件夹。当您运行 `npm install` 或 `pnpm install` 时，所有在 `package.json` 中定义的依赖包（如 Hexo 核心、插件、渲染器等）都会被下载到这里。这个文件夹通常体积巨大，并被 `.gitignore` 文件排除在版本控制之外。
│
├── scaffolds/        # “脚手架”或“模板”文件夹。当您使用 `hexo new <layout> <title>` 命令创建新文章或页面时，Hexo 会使用此文件夹下的对应模板（如 `post.md`, `page.md`）来生成新文件的初始内容，特别是预设的 Front-matter。
│
├── source/           # “源文件”文件夹，这是您博客的核心内容区，您的大部分工作都在这里进行。
│   ├── _drafts/      # (默认不创建) 草稿文件夹，里面的文章不会被生成，除非在 `hexo g` 时加上 `--drafts` 参数。
│   └── _posts/       # 文章文件夹，您所有的博客文章（.md 文件）都存放在这里。
│   │   └─ ...        # 您的 Markdown 文章文件
│   └── ...           # 您还可以创建其他页面（如 about/index.md）或存放静态资源（如 images/, css/）。
│
├── themes/           # “主题”文件夹，存放您博客的外观主题。每个子文件夹都代表一个独立的主题（例如，默认的 `landscape` 主题就存放在 `themes/landscape/`）。您可以在 `_config.yml` 中切换使用哪个主题。
│
├── .gitignore        # Git 的忽略配置文件。它告诉 Git 哪些文件或文件夹不需要进行版本控制，例如 `node_modules/` 和最终生成的 `public/` 文件夹。
│
├── _config.landscape.yml # 这是 `landscape` 主题的专属配置文件。当您使用某个主题时，可以将该主题的配置文件复制到根目录并重命名（如此文件），以便于配置和版本管理，避免在更新主题时被覆盖。
│
├── _config.yml       # [最重要] 站点的全局配置文件。您博客的标题、作者、URL、语言、使用哪个主题、部署信息等所有核心配置都在这里设置。
│
├── package.json      # Node.js 项目的清单文件。它记录了项目的基本信息、依赖包列表（dependencies）以及可执行的脚本命令（scripts）。
│
└── pnpm-lock.yaml    # 由 `pnpm` 包管理器生成的锁定文件。它会精确地锁定项目中每个依赖包及其子依赖的版本，确保在任何环境下安装依赖时，都能得到完全相同的版本，保证了项目的一致性和可复现性。（如果使用 npm，则此文件为 `package-lock.json`）

```

**总结一下每个文件夹的核心作用：**

* `.github/`: 用于**自动化部署**。
* `node_modules/`: 存放**项目依赖**，您基本不用管它。
* `scaffolds/`: 定义**新文章的模板**。
* `source/`: 您**创作内容**的地方（写文章、放图片等）。
* `themes/`: 存放博客的**外观皮肤**。

### Hexo 项目核心目录结构

Hexo 项目的目录结构清晰，理解每个文件和文件夹的作用对于后续的配置和管理至关重要：

| 目录/文件名         | 作用                                                         | 重要文件举例                      | 备注                                                     |
| :------------------ | :----------------------------------------------------------- | :-------------------------------- | :------------------------------------------------------- |
| `_config.yml`       | **站点配置文件**，用于配置博客的各项全局设置，如网站标题、URL、主题、部署等。 | `title`, `url`, `theme`, `deploy` | **最核心的配置文件之一**，几乎所有全局设置都在这里。 |
| `package.json`      | **项目依赖文件**，记录了 Hexo 项目所需的各种 Node.js 模块及其版本信息。 | `dependencies`, `devDependencies` | 通过 `npm install` 根据此文件安装依赖。                  |
| `scaffolds`         | **模板文件夹**，用于存放各种类型文件（如文章、页面）的默认模板。 | `post.md`, `page.md`, `draft.md`  | 新建文件时会根据模板生成内容。                             |
| `source`            | **源文件文件夹**，用于存放您撰写的 Markdown 文件（文章、页面）以及其他静态资源（如图片、CSS、JS文件）。 | `_posts/`, `_drafts/`, `about/`, `images/`, `css/` | **您创作内容的主要存放地**，Markdown 文件在此会被 Hexo 处理。 |
| `themes`            | **主题文件夹**，存放 Hexo 主题。每个子文件夹代表一个主题。     | `landscape/` (默认), `butterfly/` | 您安装的主题会存放在这里。                               |
| `db.json`           | **缓存文件**，存储源文件（source）的生成信息，用于提升生成速度。 | N/A                               | 可通过 `hexo clean` 清理。                               |
| `node_modules/`     | **依赖模块文件夹**，存放 `npm install` 安装的所有 Node.js 模块。 | N/A                               | 项目运行所需的所有库文件都在这里，通常体积较大。         |
| `public/`           | **公共文件夹**，存放 Hexo 生成的最终静态网页文件。             | `index.html`, `css/`, `js/`, `images/`, `archives/` | **这是最终部署到服务器上的内容**，包含所有 HTML, CSS, JS, 图片等。 |

> **备注：** `db.json` 和 `public/` 文件夹在首次运行 `hexo generate` 命令后才会生成。`node_modules/` 文件夹在运行 `npm install` 命令后生成。

### 5. 启动本地预览服务器

在正式部署之前，我强烈建议您在本地启动一个服务器来预览博客的效果。这有助于我们在修改配置或撰写文章后，快速查看更改并进行调试。

在博客项目的根目录下，执行以下命令：

```bash
hexo server # 启动本地预览服务器，默认地址为 http://localhost:4000
# 或者简写形式
# hexo s
```

执行命令后，Hexo 会生成网站文件并启动服务器。在命令行输出中，您会看到服务器启动的提示信息，通常是 `Hexo is running at http://localhost:4000/. Press Ctrl+C to stop.`

> **场景化解释：** `hexo server` 命令会做两件事：首先执行一次生成操作，将 `source` 目录下的 Markdown 文件和主题、配置等信息处理成静态的 HTML、CSS、JS 文件存放在 `public` 目录；然后启动一个轻量级的 Web 服务器，监听默认的 4000 端口，让您可以通过浏览器访问 `http://localhost:4000` 来查看博客在本地的效果。这个服务器还带有热重载功能，您修改 Markdown 文件或配置后，保存即可在浏览器中看到更新，无需手动刷新页面或重启服务器。

在浏览器中访问 `http://localhost:4000`，您应该能看到 Hexo 默认主题（通常是 Landscape）的博客界面。

现在，您已经成功搭建了一个最基础的 Hexo 博客并可以在本地预览了！





### 6.重点提示

由于Hexo采用的是静态开发，很多相关包的安装请都使用原生npm，若npm速度过慢可以切换国内源（具体可以去搜一下教程），尽量不要使用pnpm或其他包管理器避免踩坑！