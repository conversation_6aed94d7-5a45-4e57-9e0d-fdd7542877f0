-
    if (theme.search.enable) {
        var localSearch = 'undefined';
        var algolia = 'undefined';
        switch (theme.search.type) {
            case 'local':
                localSearch = JSON.stringify({
                    preload: theme.search.local.preload,
                    path: theme?.search?.local?.CDN || '/search.xml'
                });
                break;
            case 'algolia':
                algolia = JSON.stringify({
                    appId: config.algolia.appId || config.algolia.applicationID,
                    apiKey: config.algolia.apiKey,
                    indexName: config.algolia.indexName,
                    hits: {
                        per_page: theme?.search?.algolia?.hits?.per_page || 10
                    }
                });
                break;
        }
    }

    const witty_words = theme.aside?.my_card?.witty_words?.map(item => `"${item}"`) || [];
    const [hello_prefix, hello_back] = [_p('aside.hello'), _p('aside.back')];

    let comment = false

    if (theme.comment.use && theme.comment.commentBarrage) {
        comment = {
            avatar: theme.comment.avatar,
            commentBarrage: theme.comment.commentBarrage,
        }
    }

    let copyright = false;
    if (theme.copy.enable && theme.copy.copyright.enable) {
        copyright = JSON.stringify({
            limit: theme.copy.copyright.limit,
            author: _p("copy_copyright.author") + ': ' + config.author,
            link: _p("copy_copyright.link") + ': ',
            source: _p("copy_copyright.source") + ': ' + config.title,
            info: _p("copy_copyright.info")
        })
    }

    let lang = false
    lang = {
        theme: {
            dark: _p('theme.dark'),
            light: _p('theme.light'),
        },
        copy: {
            success: _p('copy.success'),
            error: _p('copy.error'),
        },
        backtop: _p('nav.backtop'),
        time: {
            day: _p('time.day'),
            hour: _p('time.hour'),
            just: _p('time.just'),
            min: _p('time.min'),
            month: _p('time.month')
        },
        day: _p('day'),
        f12: _p('f12'),
        totalk: _p('totalk')
    }

    if (theme.search.enable) {
        lang.search = {
            empty: _p('search.empty'),
            hit: _p('search.hit'),
            placeholder: _p('search.placeholder'),
            count: _p('search.count'),
            loading: _p('search.load'),
        }
    }

    if (theme.comment.use && theme.comment.commentBarrage) {
        lang.barrage = {
            title: _p('commentBarrage.title'),
        }
    }

    let rightMenu = false
    if (theme.right_menu.enable) {
        rightMenu = {
            mode: {
                dark: _p('right_menu.light'),
                light: _p('right_menu.dark'),
            },
            img_error: _p('right_menu.img_error')
        }
        if (theme.capsule.enable) {
            rightMenu.music = {
                start: _p('right_menu.music.start'),
                stop: _p('right_menu.music.stop'),
            }
        }
        if (theme.comment.use && theme.comment.commentBarrage) {
            rightMenu.barrage = {
                open: _p('right_menu.barrage.open'),
                close: _p('right_menu.barrage.close')
            }
        }
        if (theme.translate.enable) {
            rightMenu.translate = theme.right_menu.translate
        }
        if (theme.right_menu.ctrlOriginalMenu)
            rightMenu.ctrlOriginalMenu = _p('right_menu.ctrl_original_menu')
    }

    let translate = false
    if (theme.translate.enable) {
        translate = {
            translateDelay: theme.translate.translateDelay,
            defaultEncoding: theme.translate.defaultEncoding
        }
    }

    let highlight = false
    if (theme.highlight.enable) {
        highlight = {
            limit: theme.highlight.limit,
            expand: theme.highlight.expand,
            copy: theme.highlight.copy,
            syntax: config.syntax_highlighter
        }
    }

    let lure = false
    if (theme.lure.enable) {
        lure = {
            jump: theme.lure.jump,
            back: theme.lure.back,
        }
    }

    let expire = false
    if (theme.expire.enable) {
        expire = {
            time: theme.expire.time,
            position: theme.expire.position,
            text_prev: theme.expire.text_prev,
            text_next: theme.expire.text_next,
        }
    }

script.
    const GLOBAL_CONFIG = {
        root: '!{config.root}',
        algolia: !{algolia ? algolia : 'undefined'},
        localsearch: !{localSearch ? localSearch : 'undefined'},
        runtime: '!{theme.aside.siteinfo.runtimeenable ? theme.aside.siteinfo.runtime : false}',
        lazyload: {
            enable: !{theme.lazyload.enable},
            error: '!{theme.lazyload.errorimg}'
        },
        copyright: !{copyright},
        highlight: !{highlight ? JSON.stringify(highlight) : false},
        randomlink: !{theme.footer.randomlink},
        lang: !{JSON.stringify(lang)},
        aside: {
            state: {
                morning: "!{theme.aside.my_card.state.morning}",
                noon: "!{theme.aside.my_card.state.noon}",
                afternoon: "!{theme.aside.my_card.state.afternoon}",
                night: "!{theme.aside.my_card.state.night}",
                goodnight: "!{theme.aside.my_card.state.goodnight}",
            },
            witty_words: [!{witty_words}],
            witty_comment: {
                prefix: '!{hello_prefix}',
                back: '!{hello_back}',
            },
        },
        covercolor: {
            enable: !{theme.post.covercolor.enable}
        },
        comment: !{comment ? JSON.stringify(comment) : false},
        lightbox: '!{ theme.mediumZoom ? "mediumZoom" : (theme.fancybox ? "fancybox" : "null")}',
        right_menu: !{rightMenu ? JSON.stringify(rightMenu) : false},
        translate: !{translate ? JSON.stringify(translate) : false},
        lure: !{lure ? JSON.stringify(lure) : false},
        expire: !{expire ? JSON.stringify(expire) : false},
    };