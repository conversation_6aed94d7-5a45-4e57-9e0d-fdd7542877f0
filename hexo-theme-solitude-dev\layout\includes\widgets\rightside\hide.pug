.rs_hide
    if theme.rightside.hide.translate
        button.translate(type='button' title=_p('rightside.hide.translate'))= '简'
    if theme.rightside.hide.mode 
        button.mode(type='button' title=_p('rightside.hide.mode') onclick='sco.switchDarkMode()')
            i.fas.fa-circle-half-stroke
    if theme.rightside.hide.aside && theme.console.enable
        button.aside.pc(type='button' title=_p('rightside.hide.aside') onclick='sco.switchHideAside()')
            i.fas.fa-arrows-alt-h
    if theme.keyboard.enable
        button.keyboard.pc(type='button' title=_p('console.switch_keyboard') onclick="sco.switchKeyboard()")
            i.fas.fa-keyboard