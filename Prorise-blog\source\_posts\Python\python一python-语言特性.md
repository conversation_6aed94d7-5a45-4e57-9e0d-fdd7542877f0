---
title: Python（一）：Python 语言特性
categories:
  - 后端技术
  - Python
tags:
  - Python基础知识总汇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp'
comments: true
toc: true
ai: true
abbrlink: 17730
date: 2025-04-18 16:13:45
---

## Python 语言特性

Python 是一门 **解释型** 的 **弱类型动态** 语言

### 编译型 vs 解释型
- **编译型语言**：先编译后运行，速度快，适合大型项目
- **解释型语言**：边解释边运行，灵活易改，适合快速开发

### 强类型 vs 弱类型
- **强类型**：类型检查严格，变量需声明类型
- **弱类型**：类型检查宽松，变量隐式转换

### 动态型 vs 静态型
- **动态型语言**：运行时确定数据类型，灵活但效率较低
- **静态型语言**：编译时确定数据类型，高效但灵活性较差

### 类型提示
```python
def func(name: str, age: int) -> str:
    return f"{name}说，我今年{age}years old."

print(func('张三', 25))
print(func.__annotations__)  # 查看函数的类型注解
```

> 注意：Python 的类型提示不会强制执行类型检查，仅作为开发辅助和文档。

-----