---
title: 7.主题魔改：侧滑气泡式评论区
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 41598
date: 2025-07-10 23:13:45
---

### **7.主题魔改：侧滑气泡式评论区**

###### **前言：功能介绍与重要提示**

![image-20250719194248458](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719194248458.png)

本指南将，引导您实现一个功能与美化并存的“侧滑气泡式”评论区。最终效果为：
1.  点击右下角的评论按钮后，整个评论区会从侧边平滑地滑出，覆盖部分页面。
2.  滑出的评论区内，每一条评论都将以类似QQ或微信的“聊天气泡”样式展示。

> **警告：** 这是一项涉及多个文件修改的“魔改”操作。在开始前，**强烈建议您备份整个 `themes/anzhiyu` 文件夹**，以便在出现问题时可以随时恢复。

---
###### **第一步：创建集成的自定义CSS文件**

为了方便管理，我们将把“侧滑面板”和“气泡样式”这两种美化所需的全部CSS代码，都放入一个新建的文件中。

1.  **创建CSS文件**
  
* 在 `themes/anzhiyu/source/css/` 目录下，新建一个文件，命名为 `custom-comment.css`。
  
2.  **粘贴CSS代码**
    * 将下面的**整合并优化过**的CSS代码，完整复制到您刚创建的 `custom-comment.css` 文件中。
    ```css
    /* 自定义twikoo评论输入框高度 */
    .tk-input[data-v-619b4c52] .el-textarea__inner {
        height: 130px !important;
      }
      /* 输入评论时自动隐藏输入框背景图片 */
      .tk-input[data-v-619b4c52] .el-textarea__inner:focus {
        background-image: none !important;
      }
    
      .tk-row {
        flex:1;
        display: flex;
        flex-direction: row;
        justify-content: flex-start!important;
      }
    
      .tk-tag-green {
        color: #fff!important;
        background-color: #52616b!important;
        border-color: #1e2022!important;
      }
    
      .tk-row:has(.tk-tag-green) {
        justify-content: flex-end!important;
      }
    
      .tk-extras:has(.tk-tag-green) {
        justify-content: flex-end!important;
      }
      
      
      /* 头像样式调整 */
      .tk-avatar {
        border-radius: 50% !important;
        width: 2.5rem !important;
        height: 2.5rem !important;
      }
      .tk-avatar-img {
        width: 2.5rem !important;
        height: 2.5rem !important;
    border-radius: 50% !important;
      }
      
      /* 评论内容气泡化样式 */
      .tk-content {
        background: #c9d6df !important; /* 访客气泡配色 */
        color: #1e2022 !important; /* 访客气泡字体配色 */
        border-radius: 10px !important;
        padding: 8px 12px !important;
        margin: 3px 0 !important;
        position: relative !important;
        width: fit-content !important;
        max-width: 80% !important;
        font-size: 16px !important;
        line-height: 0.4 !important;
        word-wrap: break-word !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
      }
      
      /* 访客气泡小三角 */
      .tk-content::before {
        content: '' !important;
        position: absolute !important;
        top: 15px !important;
        left: -8px !important;
        width: 0 !important;
        height: 0 !important;
        border-top: 8px solid transparent !important;
        border-right: 8px solid #c9d6df !important;
    border-bottom: 8px solid transparent !important;
        border-left: 0 !important;
      }
      
      /* 访客气泡装饰小角 */
      .tk-content::after {
        content: '' !important;
        position: absolute !important;
        top: -5px !important;
        right: 10px !important;
        width: 0 !important;
        height: 0 !important;
        border-left: 5px solid transparent !important;
        border-right: 5px solid transparent !important;
        border-bottom: 5px solid #c9d6df !important;
      }
      
      /* 博主评论样式 */
      .tk-master .tk-content {
        background: #52616b !important; /* 博主气泡配色 */
        color: #f0f5f9 !important; /* 博主气泡字体配色 */
        margin-left: auto !important;
        margin-right: 0 !important;
        max-width: 65% !important;
        line-height: 0.4 !important;
      }
      
      /* 博主气泡小三角 */
      .tk-master .tk-content::before {
        left: auto !important;
        right: -8px !important;
        border-left: 8px solid #52616b !important;
        border-right: 0 !important;
      }
      
      /* 博主气泡装饰小角 */
      .tk-master .tk-content::after {
        right: auto !important;
        left: 10px !important;
        border-bottom-color: #52616b !important;
      }
      
      /* 超链接样式 */
      .tk-content a {
        color: #52616b !important;
        text-decoration: underline !important;
      }
      .tk-master .tk-content a {
        color: #c9d6df !important;
      }
      
      /* 图片样式修复 */
      .tk-content img {
        max-width: 100% !important;
        border-radius: 5px !important;
      }
      
      /* 代码块样式修复 */
      .tk-content pre {
        white-space: pre-wrap !important;
        word-wrap: break-word !important;
        background: rgba(0, 0, 0, 0.1) !important;
        padding: 8px !important;
        border-radius: 5px !important;
      }
      
      /* 楼中楼回复样式 */
      .tk-replies {
        margin-left: 40px !important;
        margin-top: 10px !important;
        border-left: 2px solid #e0e0e0 !important;
        padding-left: 20px !important;
      }
      
      /* 评论布局调整 */
      .tk-comment {
        margin-bottom: 20px !important;
      }
      
      .tk-main {
        margin-left: 10px !important;
      }
      
      /* 博主评论右对齐 */
      .tk-master .tk-main {
        margin-left: 0 !important;
        margin-right: 10px !important;
        text-align: right !important;
      }
      
      .tk-master .tk-avatar {
        order: 2 !important;
      }
      
      /* 元信息样式 */
      .tk-extras {
        opacity: 0.6 !important;
        font-size: 12px !important;
        margin-top: 5px !important;
        transition: opacity 0.3s ease !important;
      }
      
      .tk-content:hover + .tk-extras,
      .tk-extras:hover {
        opacity: 1 !important;
      }
      
      /* 夜间模式配色 */
      [data-theme="dark"] .tk-content {
        background: #1e2022 !important;
        color: #f0f5f9 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
      }
      
      [data-theme="dark"] .tk-content::before {
        border-right-color: #1e2022 !important;
      }
      
      [data-theme="dark"] .tk-content::after {
        border-bottom-color: #1e2022 !important;
      }
      
      [data-theme="dark"] .tk-master .tk-content {
        background: #52616b !important;
        color: #f0f5f9 !important;
      }
      
      [data-theme="dark"] .tk-master .tk-content::before {
        border-left-color: #52616b !important;
      }
      
      [data-theme="dark"] .tk-master .tk-content::after {
        border-bottom-color: #52616b !important;
      }
      
      [data-theme="dark"] .tk-content a {
        color: #c9d6df !important;
      }
      
      [data-theme="dark"] .tk-master .tk-content a {
        color: #f0f5f9 !important;
      }
      
      [data-theme="dark"] .tk-replies {
        border-left-color: #444 !important;
      }
      
      /* 移动端适配 */
      @media screen and (max-width: 768px) {
        .tk-content {
          max-width: 85% !important;
        }
        .tk-master .tk-content {
          max-width: 75% !important;
        }
        .tk-replies {
          margin-left: 20px !important;
        }
      }
      
      /* 大屏幕优化 */
      @media screen and (min-width: 1024px) {
        .tk-content {
          max-width: 75% !important;
        }
        .tk-master .tk-content {
          max-width: 60% !important;
        }
      }
      
      /* 评论区固定弹窗样式 */
      div#post-comment.fixedcomment {
        position: fixed;
        top: 0;
        width: 60%;
        right: 0;
        padding: 25px 30px 20px 20px;
        height: 100vh;
        overflow: scroll;
        z-index: 90;
        background: rgba(222, 222, 222, 0.95);
        box-shadow: 3px 2px 14px #464340;
        animation: fixedright 0.5s linear;
      }
      
      div#post-comment.fixedcomment::-webkit-scrollbar {
        width: 0;
      }
      
      div#quit-board {
        display: none;
      }
      
      div#quit-board.fixedcomment {
        position: fixed;
        display: block !important;
        left: 0;
        top: 0;
        width: 40%;
        height: 100vh;
        z-index: 89 !important;
        background: rgba(25, 25, 25, 0.3);
        filter: blur(4px) !important;
        animation: fixedleft 0.5s linear;
      }
      
      /* 手机端样式适配 */
      @media screen and (max-width: 768px) {
        div#post-comment.fixedcomment {
          width: 90%;
          right: 0;
        }
        div#quit-board.fixedcomment {
          width: 10%;
        }
      }
      
      /* 动画效果 */
      @keyframes fixedright {
        from { right: -50%; }
        to { right: 0; }
      }
      
      @keyframes fixedleft {
        from { left: -50%; }
        to { left: 0; }
      }
      
      /* 夜间模式弹窗适配 */
      [data-theme="dark"] div#post-comment.fixedcomment {
        background: rgba(35, 35, 35, 0.95);
        box-shadow: 3px 2px 12px #90a1a4;
      }
      
      [data-theme="dark"] div#quit-board.fixedcomment {
        background: rgba(147, 146, 128, 0.3);
      }
    ```

---
###### **第二步：创建侧滑功能JS文件**

1.  **创建JS文件**
  
* 在 `themes/anzhiyu/source/js/` 目录下，新建一个文件，命名为 `fixed_comment.js`。
  
2.  **粘贴JS代码**
    * 将下面的JavaScript代码完整复制到您刚创建的 `fixed_comment.js` 文件中。
    ```javascript
    // 移除FixedComment类，保持原生样式，确保不与最新评论跳转冲突
    function RemoveFixedComment() {
      var activedItems = document.querySelectorAll('.fixedcomment');
      if (activedItems) {
        for (i = 0; i < activedItems.length; i++) {
          activedItems[i].classList.remove('fixedcomment');
        }
      }
    }
    // 给post-comment添加fixedcomment类
    function AddFixedComment(){
      var commentBoard = document.getElementById('post-comment');
      var quitBoard = document.getElementById('quit-board');
      commentBoard.classList.add('fixedcomment');
      quitBoard.classList.add('fixedcomment');
    }
    // 创建一个蒙版，作为退出键使用
    function CreateQuitBoard(){
      // 检查是否已存在蒙版，避免重复创建
      if (document.getElementById('quit-board')) return;
      var quitBoardHTML = `<div id="quit-board" onclick="RemoveFixedComment()"></div>`
      var commentBoard = document.getElementById('post-comment');
      // 确保评论区存在再操作
      if (commentBoard) {
        commentBoard.insertAdjacentHTML("beforebegin", quitBoardHTML);
      }
    }

    function FixedCommentBtn(){
      var commentBoard = document.getElementById('post-comment');
      if (commentBoard) {
          if (commentBoard.className.indexOf('fixedcomment') > -1){
            RemoveFixedComment();
          } else {
            CreateQuitBoard();
            AddFixedComment();
          }
      } else {
          // 如果当前页面没有评论区，则跳转到留言板页面
          if (window.pjax){
            pjax.loadUrl("/comments/#post-comment");
          } else {
            window.location.href = "/comments/#post-comment";
          }
      }
    }
    // 切换页面先初始化一遍，确保开始时是原生状态。所以要加pjax重载。
    RemoveFixedComment();
    ```

---
###### **第三步：修改侧边栏按钮模板**

我们需要修改右下角“直达评论”按钮的行为，让它触发我们的侧滑功能。

1.  **找到目标文件**：
    `themes/anzhiyu/layout/includes/rightside.pug`

2.  **修改内容**：
    * 在文件中找到包含 `a#to_comment` 的那一行。
    * 将其从一个普通的 `<a>` 标签，改成一个调用我们JS函数的 `<button>`。

    **修改指引：**
    ```diff
    - a#to_comment(href="#post-comment" title=_p("rightside.scroll_to_comment"))
    + button#to_comment(type="button" title=_p("rightside.scroll_to_comment") onclick="FixedCommentBtn();")
    ```
    *(您只需将原来的那行替换为以 `+` 号开头的新行即可)*

---
###### **第四步：在主题配置中注入新文件**

1.  **打开主题配置文件** (`themes/anzhiyu/_config.yml`)。
2.  **找到 `inject:` 配置项**，添加我们新建的CSS和JS文件。
    ```yaml
    inject:
      head:
        # - 其他 head 内容
        - '<link rel="stylesheet" href="/css/custom-comment.css">'
      bottom:
        # - 其他 bottom 内容
        - '<script src="/js/fixed_comment.js"></script>'
    ```


---
