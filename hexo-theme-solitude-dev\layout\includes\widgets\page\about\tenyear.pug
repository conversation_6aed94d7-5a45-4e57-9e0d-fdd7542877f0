- var tenyear = site.data.about.tenyear
if tenyear
    .author-content#tenyear
        .create-site-post.author-content-item.single
            .author-content-item-tips= tenyear.tips
            .author-content-item-title= tenyear.title
            p= tenyear.text
            .tenyear-timeline
                .progress
                .past-time
                .percentage-label
            .time-labels
                .start-time#tenyear-start-time= new Date(tenyear.start).toLocaleDateString()
                .end-time#tenyear-end-time= new Date(tenyear.end).toLocaleDateString()

    script.
        function updateTenYearProgress() {
            let progressElement = document.querySelector(".progress");
            let pastTimeElement = document.querySelector(".past-time");
            let percentageLabelElement = document.querySelector(".percentage-label");
            let startTimeElement = document.getElementById("tenyear-start-time");
            let endTimeElement = document.getElementById("tenyear-end-time");
            let startTime = new Date(startTimeElement.textContent).getTime();
            let endTime = new Date(endTimeElement.textContent).getTime();

            const currentTime = new Date().getTime();
            const progress = ((currentTime - startTime) / (endTime - startTime) * 100);
            const progressPercentage = Math.min(progress, 100) + "%";

            pastTimeElement.style.setProperty("--past-time-percentage", progress + "%");
            progressElement.style.setProperty("--progress-percentage", progressPercentage);
            if (progress > 5) {
                percentageLabelElement.textContent = `${progress.toFixed(0)}%`;
                percentageLabelElement.style.left = `calc(${progress}% - 35px)`;
            }

            percentageLabelElement.style.visibility = "visible";
        }

        if (document.getElementById("tenyear")) {
            updateTenYearProgress();
        }