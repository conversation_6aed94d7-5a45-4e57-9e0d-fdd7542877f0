#category-bar
  padding .4rem .7rem
  background var(--efu-card-bg)
  border-radius 12px
  display flex
  white-space nowrap
  overflow hidden
  border var(--style-border)
  transition 0.3s
  height 50px
  width 100%
  justify-content space-between
  user-select none
  align-items center
  margin-bottom .5rem

  +minWidth1300()
    &:hover
      border var(--style-border-hover)
      box-shadow var(--efu-shadow-main)

  +minWidth1200()
    animation: slide-in .6s .3s backwards;

  +maxWidth768()
    margin-bottom .5rem
    margin-top .5rem
    border-radius 0
    background var(--efu-background)
    padding 0 1rem
    border none

  .category-bar-right
    display flex
    align-items center

    +maxWidth768()
      display none

    .category-bar-next
      margin-left 8px
      cursor pointer
      height 30px
      width 30px
      display flex
      border-radius 8px
      align-items center
      justify-content center
      transition .3s

    .category-bar-more
      margin-left 4px
      font-weight 700
      border-radius 8px
      padding 0 8px

    div:hover, a:hover
      background var(--efu-secondbg-bg)
      color var(--efu-lighttext)

  .category-bar-items
    white-space nowrap
    overflow-x scroll
    overflow-y hidden
    display flex
    border-radius 8px
    align-items center
    height 30px
    scrollbar-width: none
    +maxWidth768()
      height 44px
      gap 6px

    &::-webkit-scrollbar
      display: none

    .category-bar-item
      a:hover
        background var(--efu-main)
        color var(--efu-white)

      a
        padding .1rem .5rem
        margin-right 6px
        font-weight 700
        border-radius 8px
        display flex
        align-items center
        height 30px

        +maxWidth768()
          border-radius 50px
          background var(--efu-card-bg)
          border var(--style-border-always)
          padding 21px 16px

      &.select
        order -1

        a
          background var(--efu-theme)
          color var(--efu-white)