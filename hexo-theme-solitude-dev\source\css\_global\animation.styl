@keyframes barrageIn
  0%
    transform translateY(20px)
    opacity 0
  100%
    transform translateY(0)
    opacity 1

@keyframes barrageOut
  0%
    transform translateY(0)
    opacity 1
  100%
    transform translateY(20px)
    opacity 0

@keyframes scroll-down-effect
  0%
    top 0
    opacity .4
  50%
    top -16px
    opacity 1
  100%
    top 0
    opacity .4

@keyframes header-effect
  0%
    opacity 0
    transform translateY(-50px)
  100%
    opacity 1
    transform translateY(0)

@keyframes headerNoOpacity
  0%
    transform translateY(-50px)
  100%
    transform translateY(0)

@keyframes bottom-top
  0%
    opacity 0
    margin-top 50px
  100%
    opacity 1
    margin-top 0

@keyframes titlescale
  0%
    opacity 0
    transform scale(.7)
  100%
    opacity 1
    transform scale(1)

@keyframes search_close
  0%
    transform translateY(0)
    opacity 1
  100%
    transform translateY(20px)
    opacity 0

@keyframes to_show
  0%
    opacity 0
  100%
    opacity 1

@keyframes to_hide
  0%
    opacity 1
  100%
    opacity 0

@keyframes ribbon_to_show
  0%
    opacity 0
  100%
    opacity .6

@keyframes avatar_turn_around
  0%
    transform rotate(0)
  100%
    transform rotate(360deg)

@keyframes sub_menus
  0%
    opacity 0
    transform translateY(10px)
  100%
    opacity 1
    transform translateY(0)

@keyframes donate_effcet
  0%
    opacity 0
    transform translateY(-20px)
  100%
    opacity 1
    transform translateY(0)

@keyframes announ_animation
  0%, 100%
    transform scale(1)
  50%
    transform scale(1.2)
    filter blur(20px)

@keyframes sidebarItem
  0%
    transform translateX(200px)
  100%
    transform translateX(0)

@keyframes st-spin
  0%
    transform rotate(0)
  100%
    transform rotate(360deg)

@keyframes code-expand-key
  0%
    opacity .6
  50%
    opacity .1
  100%
    opacity .6

@keyframes slide-in
  from
    transform translateY(20px)
    opacity 0
  to
    transform translateY(0)
    opacity 1

@keyframes slide-in-op
  from
    opacity 0
  to
    opacity 1

@keyframes more-btn-move
  0%, 100%
    transform translateX(0)
  50%
    transform translateX(3px)

@keyframes toc-open
  0%
    transform scale(.7)
  100%
    transform scale(1)

@keyframes toc-close
  0%
    transform scale(1)
  100%
    transform scale(.7)

@keyframes configure-clockwise
  0%
    transform rotate(0)
  25%
    transform rotate(90deg)
  50%
    transform rotate(180deg)
  75%
    transform rotate(270deg)
  100%
    transform rotate(360deg)

@keyframes configure-xclockwise
  0%
    transform rotate(45deg)
  25%
    transform rotate(-45deg)
  50%
    transform rotate(-135deg)
  75%
    transform rotate(-225deg)
  100%
    transform rotate(-315deg)

@keyframes tabshow
  0%
    transform translateY(15px)
  100%
    transform translateY(0)

@keyframes snackbar-progress
  from
    width 0
  to
    width 100%

@keyframes move-forever
  0%
    transform translate3d(-90px, 0, 0)
  100%
    transform translate3d(85px, 0, 0)

@keyframes rowup
  from
    transform translateY(0)
  to
    transform translateY(-50%)

@keyframes rowleft
  from
    transform translateX(0)
  to
    transform translateX(-50%)

@keyframes rowleft-quarter
  from
    transform translateX(-25%)
  to
    transform translateX(-75%)

@keyframes gradient
  0%
    background-position 0 50%
  50%
    background-position 100% 50%
  100%
    background-position 0 50%

@keyframes commonTipsIn
  0%
    top -50px
    opacity 0
  100%
    top -60px
    opacity 1

@keyframes commonTriangleIn
  0%
    transform translate(-50%, -36px)
    opacity 0
  100%
    transform translate(-50%, -46px)
    opacity 1

@keyframes owoIn
  0%
    transform translate(0, -95%)
    opacity 0
  100%
    transform translate(0, -105%)
    opacity 1

@keyframes light_tag
  0%
    transform skewx(0)
    left -150px
  99%
    transform skewx(-25deg)
    left 50px

@keyframes floating
  0%
    transform translate(0, -4px)
  50%
    transform translate(0, 4px)
  100%
    transform translate(0, -4px)

@media screen and (min-width: 768px)
  @keyframes showCover
    from
      opacity 0
      transform rotate(10deg) translateY(-8%) scale(1.8)
    to
      opacity 0.5
      transform rotate(10deg) translateY(-10%) scale(2)

@keyframes blinking-cursor
  0%
    transform scale(.6)
  25%
    transform scale(1)
  50%
    transform scale(.6)
  75%
    transform scale(1)
  100%
    transform scale(.6)

@keyframes AILoading
  0%
    opacity 1
  25%
    opacity .3
  50%
    opacity 1
  75%
    opacity .3
  100%
    opacity 1