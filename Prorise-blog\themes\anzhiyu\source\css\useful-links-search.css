/**
 * 实用网站导航搜索功能样式
 * 适配 AnZhiYu 主题
 */

/* 搜索容器 */
#useful-links-search-container {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--anzhiyu-card-bg);
  border-radius: 12px;
  border: var(--style-border);
  box-shadow: var(--anzhiyu-shadow-border);
  transition: all 0.3s ease;
}

#useful-links-search-container:hover {
  border: var(--style-border-hover);
  box-shadow: var(--anzhiyu-shadow-theme);
}

/* 搜索头部 */
.search-header {
  margin-bottom: 1rem;
}

/* 搜索框样式 */
.search-box {
  position: relative;
  margin-bottom: 1rem;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  border: var(--style-border);
  border-radius: 8px;
  background: var(--anzhiyu-secondbg);
  color: var(--anzhiyu-fontcolor);
  font-size: 1rem;
  transition: all 0.3s ease;
  outline: none;
}

.search-box input:focus {
  border: var(--style-border-hover);
  box-shadow: 0 0 0 3px rgba(var(--anzhiyu-main-rgb), 0.1);
}

.search-box input::placeholder {
  color: var(--anzhiyu-secondtext);
  opacity: 0.8;
}

/* 搜索图标 */
.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--anzhiyu-secondtext);
  font-size: 1.1rem;
  pointer-events: none;
  transition: color 0.3s ease;
}

.search-box input:focus + .search-icon {
  color: var(--anzhiyu-main);
}

/* 分类过滤器 */
.category-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.category-filter {
  padding: 0.5rem 1rem;
  background: var(--anzhiyu-secondbg);
  border: var(--style-border);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  color: var(--anzhiyu-fontcolor);
  user-select: none;
  white-space: nowrap;
}

.category-filter:hover {
  background: var(--anzhiyu-main);
  color: var(--anzhiyu-white);
  border-color: var(--anzhiyu-main);
  transform: translateY(-1px);
}

.category-filter.active {
  background: var(--anzhiyu-main);
  color: var(--anzhiyu-white);
  border-color: var(--anzhiyu-main);
  box-shadow: 0 2px 8px rgba(var(--anzhiyu-main-rgb), 0.3);
}

/* 搜索统计信息 */
.search-stats {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: var(--style-border);
}

#search-results-count {
  color: var(--anzhiyu-secondtext);
  font-size: 0.9rem;
  font-weight: 500;
}

/* 搜索结果高亮 */
.search-highlight {
  background: var(--anzhiyu-main);
  color: var(--anzhiyu-white);
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: bold;
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

/* 隐藏不匹配的项目 */
.flink-list-item.hidden {
  display: none !important;
}

.flink h2.hidden {
  display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #useful-links-search-container {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .search-box input {
    padding: 0.6rem 2.5rem 0.6rem 0.8rem;
    font-size: 0.9rem;
  }
  
  .search-icon {
    right: 0.8rem;
    font-size: 1rem;
  }
  
  .category-filter {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
  }
  
  .category-filters {
    gap: 0.4rem;
  }
  
  #search-results-count {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  #useful-links-search-container {
    padding: 0.8rem;
  }
  
  .search-box input {
    padding: 0.5rem 2rem 0.5rem 0.6rem;
    font-size: 0.85rem;
  }
  
  .category-filter {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
  }
}

/* 深色模式适配 */
[data-theme='dark'] #useful-links-search-container {
  border-color: var(--style-border);
}

[data-theme='dark'] .search-box input {
  background: var(--anzhiyu-secondbg);
  border-color: var(--style-border);
  color: var(--anzhiyu-fontcolor);
}

[data-theme='dark'] .category-filter {
  background: var(--anzhiyu-secondbg);
  border-color: var(--style-border);
  color: var(--anzhiyu-fontcolor);
}

/* 动画效果 */
.category-filter {
  position: relative;
  overflow: hidden;
}

.category-filter::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.category-filter:hover::before {
  left: 100%;
}

/* 搜索框聚焦动画 */
.search-box {
  position: relative;
}

.search-box::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--anzhiyu-main);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.search-box input:focus ~ ::after {
  width: 100%;
}

/* 加载状态 */
.search-loading {
  opacity: 0.6;
  pointer-events: none;
}

.search-loading .search-icon::before {
  content: '\e6cd'; /* loading icon */
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: translateY(-50%) rotate(0deg); }
  to { transform: translateY(-50%) rotate(360deg); }
}

/* 无结果状态 */
.no-results-message {
  text-align: center;
  padding: 2rem;
  color: var(--anzhiyu-secondtext);
  font-size: 1rem;
}

.no-results-message i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* 滚动到顶部按钮增强（搜索时显示） */
.search-active #nav-totop {
  opacity: 1 !important;
  transform: translateX(-58px) !important;
}

/* 分类标题动画 */
.flink h2 {
  transition: all 0.3s ease;
}

.flink h2[style*="display: none"] {
  opacity: 0;
  transform: translateY(-10px);
}

/* 搜索结果项目动画 */
.flink-list-item {
  transition: all 0.3s ease;
}

.flink-list-item.hidden {
  opacity: 0;
  transform: scale(0.9) translateY(-10px);
  pointer-events: none;
}

/* 高亮动画 */
.search-highlight {
  animation: highlight-pulse 1s ease-in-out;
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--anzhiyu-main-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(var(--anzhiyu-main-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--anzhiyu-main-rgb), 0);
  }
} 