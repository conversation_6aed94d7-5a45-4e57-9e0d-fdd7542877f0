[data-theme=dark]
  --efu-theme $dark_theme
  --efu-theme-op $dark_theme_op
  --efu-theme-op-deep $dark_theme_op_deep
  --efu-theme-none $dark_theme_none
  --efu-blue #0084ff
  --efu-red #ff3842
  --efu-pink #d44040
  --efu-green #3e9f50
  --efu-purple #7a60d2
  --efu-yellow #ffc93e
  --efu-yellow-op #ffc93e30
  --efu-orange #ff953e
  --efu-fontcolor #f7f7fa
  --efu-reverse #fff
  --efu-maskbg rgba(0, 0, 0, 0.6)
  --efu-maskbgdeep rgba(0, 0, 0, 0.85)
  --efu-hovertext #0a84ff
  --efu-ahoverbg #fff
  --efu-lighttext var(--efu-theme)
  --efu-secondtext #a1a2b8
  --efu-scrollbar rgba(200, 200, 223, 0.4)
  --efu-card-btn-bg #30343f
  --efu-post-blockquote-bg #000
  --efu-post-tabs-bg #121212
  --efu-secondbg #30343f
  --efu-secondbg-bg #21232a
  --efu-shadow-nav 0 5px 20px 0px rgba(28, 28, 28, 0.4)
  --efu-card-bg #1b1c20
  --efu-card-bg-op var(--efu-white-op)
  --efu-card-bg-none #1d1b2600
  --efu-shadow-lightblack 0 5px 12px -5px rgba(102, 68, 68, 0.0)
  --efu-shadow-light2black 0 5px 12px -5px rgba(102, 68, 68, 0.0)
  --efu-card-border #3d3d3f
  --efu-shadow-border 0 8px 16px -4px #00000050
  --style-border 1px solid var(--efu-card-border)
  --style-border-always 1px solid var(--efu-card-border)
  --style-border-hover 1px solid var(--efu-theme)
  --style-border-hover-always 1px solid var(--efu-theme)
  --style-border-dashed 1px dashed var(--efu-theme-op)
  --style-border-forever 2px solid var(--efu-lighttext)
  --efu-hl-bg $hl_bg_dark
  --efu-hltools-bg $hltools_bg_dark

  if hexo-config('background.enable')
    --efu-background rgba(24, 23, 29, 0.3)
  else
    --efu-background #18171d

[data-theme=light]
  --efu-theme $light_theme
  --efu-theme-op $light_theme_op
  --efu-theme-op-deep $light_theme_op_deep
  --efu-theme-op-light #4259ef0d
  --efu-theme-none $light_theme_none
  --efu-blue #425aef
  --efu-red #f04a63
  --efu-pink #ff7c7c
  --efu-green #57bd6a
  --efu-yellow #c28b00
  --efu-yellow-op #d99c001a
  --efu-orange #e38100
  --efu-purple #7a60d2
  --efu-fontcolor #363636
  --efu-reverse #000
  --efu-maskbg rgba(255, 255, 255, 0.6)
  --efu-maskbgdeep rgba(255, 255, 255, 0.85)
  --efu-hovertext var(--efu-main)
  --efu-ahoverbg #f7f7fa
  --efu-lighttext var(--efu-main)
  --efu-secondtext rgba(60, 60, 67, 0.8)
  --efu-scrollbar rgba(60, 60, 67, 0.4)
  --efu-card-btn-bg #edf0f7
  --efu-post-blockquote-bg #fafcff
  --efu-post-tabs-bg #f2f5f8
  --efu-secondbg #f7f7f9
  --efu-secondbg-bg var(--efu-secondbg)
  --efu-shadow-nav 0 5px 12px -5px rgba(102, 68, 68, 0.05)
  --efu-card-bg #fff
  --efu-card-bg-op var(--efu-black-op)
  --efu-card-bg-none rgba(255, 255, 255, 0)
  --efu-shadow-lightblack 0 5px 12px -5px rgba(102, 68, 68, 0.00)
  --efu-shadow-light2black 0 5px 12px -5px rgba(102, 68, 68, 0.00)
  --efu-card-border #e3e8f7
  --efu-shadow-border 0 8px 16px -4px #2c2d300c
  --style-border 1px solid var(--efu-card-border)
  --style-border-always 1px solid var(--efu-card-border)
  --style-border-hover 1px solid var(--efu-main)
  --style-border-hover-always 1px solid var(--efu-main)
  --style-border-dashed 1px dashed var(--efu-theme-op)
  --style-border-forever 2px solid var(--efu-main)
  --efu-navbg var(--efu-theme-op)
  --efu-hl-bg $hl_bg_light
  --efu-hltools-bg $hltools_bg_light

  if hexo-config('background.enable')
    --efu-background #f7f9fe
  else
    --efu-background #f7f9fe

:root
  +maxWidth768()
    --style-border 0px solid var(--efu-none)
    --style-border-hover 0px solid var(--efu-main)