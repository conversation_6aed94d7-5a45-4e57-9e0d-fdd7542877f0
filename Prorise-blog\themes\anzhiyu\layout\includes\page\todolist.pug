#todolist-box
    - let todo_background = page.top_background
    .author-content.author-content-item.todolist.single(style=`${todo_background ? `background: url(${todo_background}) top / cover no-repeat;` : ""}`)
        .card-content
            .author-content-item-tips Todo
            span.author-content-item-title 待办清单
            .content-bottom
            .tips 
                i.fa-solid.fa-quote-left.fa-fw
                span 耽误太多时间，事情可就做不完了
                i.fa-solid.fa-quote-right.fa-fw
            .banner-button-group
              a.banner-button(onclick='pjax.loadUrl("/about/")')
                i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.5rem')
                span.banner-button-text 我的更多
    
    #todolist-filter
        .filter-title
            i.fa-solid.fa-filter
            span 分类筛选
        .filter-buttons
            button.filter-btn.active(data-filter="all") 全部
            - let categories = []
            each i in site.data.todolist
                - if(!categories.includes(i.class_type)) categories.push(i.class_type || '未分类')
            each category in categories
                button.filter-btn(data-filter=category)= category
    
    #todolist-main.todolist-grid
        each i in site.data.todolist
            - const categoryClass = i.class_type ? `todo-category-${i.class_type}` : 'todo-category-undefined'
            .todolist-item(class=categoryClass, data-category=i.class_type || '未分类')
                h3.todolist-title
                    i.fa-solid.fa-list-check
                    span= i.class_name
                    .task-count
                        - let completedCount = 0
                        each item in i.todo_list
                            - if(item.completed) completedCount++
                        span= completedCount + '/' + i.todo_list.length
                ul.todolist-ul
                    each item in i.todo_list
                        - var listItemClass = item.completed ? 'todolist-li-done' : 'todolist-li'
                        li(class=listItemClass)
                            if item.completed
                                i.fa-regular.fa-circle-check
                            else 
                                i.fa-regular.fa-circle
                            span= item.content
                .progress-bar
                    - let progress = i.todo_list.length > 0 ? (completedCount / i.todo_list.length) * 100 : 0
                    .progress(style=`width: ${progress}%`)
    
    #todolist-pagination
        .pagination-container
            button#prev-page.page-btn(disabled)
                i.fa-solid.fa-angle-left
            #page-numbers
            button#next-page.page-btn(disabled)
                i.fa-solid.fa-angle-right

script.
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化过滤器
        const filterBtns = document.querySelectorAll('.filter-btn');
        const todoItems = document.querySelectorAll('.todolist-item');
        
        // 分页相关变量
        const itemsPerPage = 6; // 每页显示卡片数
        let currentPage = 1;
        let filteredItems = Array.from(todoItems);
            
        // 应用过滤器
        function applyFilter(filter) {
            filteredItems = Array.from(todoItems);
            
            if (filter !== 'all') {
                filteredItems = filteredItems.filter(item => item.getAttribute('data-category') === filter);
            }
            
            // 显示过滤后的项目
            todoItems.forEach(item => {
                item.style.display = 'none';
            });
            
            // 更新分页
            setupPagination();
            goToPage(1);
        }
        
        // 设置分页
        function setupPagination() {
            const pageCount = Math.ceil(filteredItems.length / itemsPerPage);
            const pageNumbers = document.getElementById('page-numbers');
            pageNumbers.innerHTML = '';
            
            if (pageCount <= 1) {
                document.getElementById('todolist-pagination').style.display = 'none';
                showItems(filteredItems);
                return;
            }
            
            document.getElementById('todolist-pagination').style.display = 'block';
            
            // 添加页码按钮
            for (let i = 1; i <= pageCount; i++) {
                const btn = document.createElement('button');
                btn.classList.add('page-number');
                if (i === 1) btn.classList.add('active');
                btn.textContent = i;
                btn.addEventListener('click', function() {
                    goToPage(i);
                });
                pageNumbers.appendChild(btn);
            }
            
            // 设置上一页/下一页按钮
            document.getElementById('prev-page').disabled = true;
            document.getElementById('next-page').disabled = pageCount <= 1;
            
            document.getElementById('prev-page').addEventListener('click', function() {
                if (currentPage > 1) goToPage(currentPage - 1);
            });
            
            document.getElementById('next-page').addEventListener('click', function() {
                const pageCount = Math.ceil(filteredItems.length / itemsPerPage);
                if (currentPage < pageCount) goToPage(currentPage + 1);
            });
        }
        
        // 跳转到指定页码
        function goToPage(page) {
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const itemsToShow = filteredItems.slice(startIndex, endIndex);
            
            // 更新页码按钮状态
            const pageButtons = document.querySelectorAll('.page-number');
            pageButtons.forEach((btn, index) => {
                btn.classList.toggle('active', index + 1 === page);
            });
            
            // 更新上一页/下一页按钮状态
            document.getElementById('prev-page').disabled = page === 1;
            document.getElementById('next-page').disabled = page === Math.ceil(filteredItems.length / itemsPerPage);
            
            // 显示当前页项目
            todoItems.forEach(item => {
                item.style.display = 'none';
            });
            
            showItems(itemsToShow);
            currentPage = page;
        }
        
        // 显示项目
        function showItems(items) {
            items.forEach(item => {
                item.style.display = 'block';
            });
        }
        
        // 绑定过滤器点击事件
        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                filterBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                applyFilter(this.getAttribute('data-filter'));
            });
        });
        
        // 初始化
        setupPagination();
        goToPage(1);
    });