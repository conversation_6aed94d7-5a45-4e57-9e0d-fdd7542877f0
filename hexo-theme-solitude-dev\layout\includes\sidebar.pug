div#sidebar(style="zoom: 1;")
    div#menu-mask(style="display: none;")
    div#sidebar-menus
        .site-data
            .data-item.is-center
                .data-item-link
                    a(href=url_for('/archives/'))
                        .headline=_p('page.archives')
                        .length-num=site.posts.find({ parent: { $exists: false } }).length
            .data-item.is-center
                .data-item-link
                    a(href=url_for('/categories/'))
                        .headline=_p('page.category')
                        .length-num=site.categories.find({parent: {$exists: false}}).length
            .data-item.is-center
                .data-item-link
                    a(href=url_for('/tags/'))
                        .headline=_p('page.tag')
                        .length-num=site.tags.find({parent: {$exists: false}}).length
            if theme.comment.sidebar
                .data-item.is-center
                    .data-item-link
                        a(href=url_for(theme.recent_comments.page))
                            .headline=_p('comment.title')
                            include ./widgets/sidebar/comment
        span.sidebar-menu-item-title= __('sidebar.function')
        div.sidebar-menu-item
            span.darkmode_switchbutton.menu-child(onclick="sco.switchDarkMode()")
                i.solitude.fas.fa-circle-half-stroke
                span= __('sidebar.darkmode')
        include ./widgets/nav/group
        include ./widgets/nav/menu.pug
        span.sidebar-menu-item-title= __('page.tag')
        div.card-tags
            div.card-tag-cloud
                each tag in site.tags.find({ parent: { $exists: false } }).data
                    a(href=url_for(tag.path))= tag.name
                        sup= tag.length
        if theme.wordcount && theme.aside.siteinfo.wordcount
            span.sidebar-menu-item-title= __('sidebar.siteinfo')
            .webinfo
                .webinfo-item
                    .item-name= __('aside.wordcount')
                    .item-count= totalcount(site)