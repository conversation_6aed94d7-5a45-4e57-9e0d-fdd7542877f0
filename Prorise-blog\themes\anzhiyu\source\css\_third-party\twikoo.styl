if hexo-config('comments.use') == 'Twikoo'
  .OwO .OwO-body
    min-width: 31.25rem
  .twikoo svg
    color: var(--anzhiyu-fontcolor);
  /* 评论区表情放大 */
  @keyframes owoIn
    0% {
      transform: translate(0, -95%);
      opacity: 0;
    }
    100% {
      transform: translate(0, -112%);
      opacity: 1;
    }

  #owo-big
    position: fixed;
    align-items: center;
    background-color: rgb(255, 255, 255);
    border: 1px #aaa solid;
    border-radius: 10px;
    z-index: 9999;
    display: none;
    transform: translate(0, -112%);
    overflow: hidden;
    animation: owoIn 0.3s cubic-bezier(0.42, 0, 0.3, 1.11);
    img
      width: 100%;

  .tk-expand 
    width: 100%;
    cursor: pointer;
    padding: 0.75em;
    text-align: center;
    transition: all 0.5s;
    border: var(--style-border);
    box-shadow: 0 8px 16px -4px #2c2d300c;
    border-radius: 50px;
    letter-spacing: 5px;
    background-color: var(--anzhiyu-card-bg);

  #twikoo 
    .tk-comments > .tk-submit
      overflow: visible !important;
    .tk-comments
      .OwO .OwO-body
        border: var(--style-border-always) !important;
        border-radius: 8px !important;
        overflow: hidden;
        background-color: var(--anzhiyu-maskbg) !important;
        backdrop-filter: saturate(180%) blur(10px);
        cursor: auto;
        top: 2.1em !important;
        transform: translateZ(0);
        animation: .3s ease .1s 1 normal both running donate_effcet;
        .OwO-items-show
          margin: 12px 8px;
      button.el-button.tk-cancel.el-button--default.el-button--small
        background: var(--anzhiyu-secondbg);
        border-radius: 8px;
        color: var(--anzhiyu-fontcolor);
        &:hover
          background: var(--anzhiyu-lighttext);
          color: var(--anzhiyu-white);
      a.tk-submit-action-icon.__markdown
        display: none;
      &>div.tk-submit>div.tk-row.actions>a
        display: none
      .el-button.tk-preview
        display: none;
      .el-button--primary.is-disabled, .el-button--primary.is-disabled:active, .el-button--primary.is-disabled:focus, .el-button--primary.is-disabled:hover
        opacity .2
      .el-button--primary
        border-color: var(--anzhiyu-fontcolor);
        color: var(--anzhiyu-card-bg);
        border-radius: 12px;
        box-shadow: var(--anzhiyu-shadow-black);
        transition: .3s;
        width: 6.25rem;
        position: absolute;
        top: -43px;
        right: 0;
        margin-left: 0.5rem!important;
        height: 32px;
      .tk-input 
        .el-textarea__inner
          min-height: 130px !important;
          border-radius: 15px;
          display: block;
          resize: vertical;
          padding: 16px 16px 40px 16px;
          line-height: 1.5;
          box-sizing: border-box;
          width: 100%;
          font-size: inherit;
          color: var(--anzhiyu-fontcolor);
          background-color: var(--anzhiyu-secondbg);
          border: var(--style-border-always);
          transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

      .el-input__inner
        background: var(--anzhiyu-secondbg)!important;
        border: none!important;
        color: var(--anzhiyu-fontcolor)!important;
        padding-left: 8px;
      .el-input__inner:focus
        border: none;
      .el-input-group__append, .el-input-group__prepend
        background-color: var(--anzhiyu-card-bg);
        color: var(--anzhiyu-fontcolor);
        border-color: var(--anzhiyu-card-border);
        border: none;
        font-weight: 700;
      .el-input-group--prepend .el-input__inner, .el-input-group__append
        border-radius: 12px;
      .el-input--small .el-input__inner
        padding: 8px;
        padding-left: 16px;
      .el-input-group--prepend .el-input__inner, .el-input-group__append
        border-left-width: 0 !important;

      .tk-meta-input
        position: relative;
        margin-top: 8px;
        width: calc(100% - 6.875rem);
        .el-input.el-input--small.el-input-group.el-input-group--prepend
          border-radius: 12px;
          background: var(--anzhiyu-secondbg);
          border: var(--style-border-always);
        .el-input .el-input-group__prepend
          user-select: none;
          border-radius: 12px 0 0 12px;
        .el-input--small.el-input-group.el-input-group--prepend:focus-within
          border: var(--style-border-hover);

      .tk-row 
        .tk-avatar
          display: none
        .tk-col
          flex-direction: column-reverse;
        &.actions
          margin-bottom: 0;
          margin-left: 0;
          margin-top: 0;
          justify-content: space-around;
      .tk-admin
        backdrop-filter: blur(5px);
      .el-button
        background-color: var(--anzhiyu-fontcolor);
        border: 0 solid var(--anzhiyu-main);
        color: var(--anzhiyu-background);
      .tk-tag-green
        background-color: var(--anzhiyu-main);
        border: none;
        border-radius: 4px;
        color: var(--anzhiyu-white);
      .tk-action-icon
        color: var(--anzhiyu-main)
        cursor pointer
      .tk-icon.__comments
        color: var(--anzhiyu-main)
      .tk-actions
        a
        cursor pointer
      .tk-nick
        line-height: 40px;
      .tk-extras
        margin-top: .5rem;
        padding-bottom: .5rem;
      .tk-expand
        &:hover
          color: #fff;
          background-color: var(--anzhiyu-main);
          border: var(--style-border-none);

      .tk-content 
        p
          margin: 0;
      .tk-admin-config-input 
        .el-input__inner
          background: transparent !important;
    pre code
      background: none;
    code
      padding: 2px 4px;
      background: var(--anzhiyu-secondbg);
      color: #f47466;
    .tk-comment .tk-submit .tk-avatar, .tk-replies .tk-avatar
      height: 2.5rem !important;
      width: 2.5rem !important;
    .tk-comment
      pre
        background: #272822;
        padding: 1em;
        margin: 0.5em 0;
        overflow: auto;
        border-radius: 0.3em;
    
    +maxWidth768()
      .tk-comments-container .tk-comment
        padding: 1rem;
        border: var(--style-border-always);
        box-shadow: var(--anzhiyu-shadow-border);
        background: var(--anzhiyu-card-bg);
      .tk-replies .tk-comment
        border: none

    .tk-avatar 
      border-radius: 50px;
      .tk-avatar-img
        height: 2.5rem !important;

  .tk-replies
    max-height: 10rem !important
    &.tk-replies-expand
      max-height: none !important
    .tk-comment
      border-top: var(--style-border-dashed);
      border-radius: 12px;
      padding: 1rem 0px 0px;
      margin-top: 0;
      transition: all 0.3s ease 0s;
    .tk-content span:first-child:not(.token)
      font-size: .75rem;
      color: var(--anzhiyu-secondtext);


  [data-theme="dark"] #owo-big
    background-color: #4a4a4a;
  .tk-comments-container .tk-submit
      opacity: 1;
      height: auto;
      overflow: visible;
  /* 输入提示 */
  /* 设置文字内容 :nth-child(1)的作用是选择第几个 */
  .el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(1):before {
    content: "输入QQ号会自动获取昵称和头像🐧";
  }

  .el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(2):before {
    content: "收到回复将会发送到您的邮箱📧";
  }

  .el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(3):before {
    content: "可以通过昵称访问您的网站🔗";
  }

  /* 当用户点击输入框时显示 */
  .el-input.el-input--small.el-input-group.el-input-group--prepend:focus-within::before
    display: block;
    animation: commonTipsIn .3s
    z-index: 2

  .el-input.el-input--small.el-input-group.el-input-group--prepend:focus-within::after
    display: block;
    animation: commonTriangleIn .3s
    // z-index: 2

  /* 主内容区 */
  .el-input.el-input--small.el-input-group.el-input-group--prepend::before {
    display: none;
    position: absolute;
    top: -60px;
    white-space: nowrap;
    border-radius: 10px;
    left: 50%;
    transform: translate(-50%);
    padding: 14px 18px;
    background: #444;
    color: #fff;
    z-index: 100;
  }

  /* 小角标 */
  .el-input.el-input--small.el-input-group.el-input-group--prepend::after {
    display: none;
    content: '';
    position: absolute;
    border: 12px solid transparent;
    border-top-color: #444;
    left: 50%;
    transform: translate(-50%,-46px);
  }

  /* 评论框  */
  .vwrap {
    box-shadow: 2px 2px 5px #bbb;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 30px;
    margin: 30px 0px 30px 0px;
  }

  /* 设置评论框 */
  .vcard {
    box-shadow: 2px 2px 5px #bbb;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 30px;
    margin: 30px 0px 0px 0px;
  }

  #twikoo .tk-extra {
    background: var(--anzhiyu-card-bg);
    border: var(--style-border-always);
    padding: 4px 8px;
    border-radius: 8px;
    margin-right: 4px;
    color: var(--anzhiyu-secondtext);
    margin-top: 6px;
    font-size: 0.8rem;
  }
  #twikoo .tk-extra-text {
    font-size: .75rem;
  }
  #twikoo .tk-replies .tk-content {
    font-size: 0.9rem;
  }
  #twikoo .tk-content {
    margin-top: 0;
  }
  .tk-content span a:not([data-fancybox="gallery"]) {
    font-weight: 500;
    border-bottom: solid 2px var(--anzhiyu-lighttext);
    color: var(--anzhiyu-fontcolor);
    padding: 0 0.2em;
    text-decoration: none;
  }
  .tk-content span a:not([data-fancybox="gallery"]):hover {
    color: var(--anzhiyu-white);
    background-color: var(--anzhiyu-theme);
    border-radius: 4px;
  }
  .tk-main .tk-content span > a {
    border-bottom: none;
  }
  #post-comment .comment-head {
    font-size: 0.8em !important;
    margin-bottom: 0.5rem;
  }

  @keyframes commonTipsIn
    0%
      top: -50px;
      opacity: 0
    100% 
      top: -60px;
      opacity: 1

  @keyframes commonTriangleIn
    0%
      transform: translate(-50%,-36px);
      opacity: 0
    100%
      transform: translate(-50%,-46px);
      opacity: 1
  @keyframes donate_effcet
    0%
      opacity: 0;
      transform: translateY(-20px);
    100%
      opacity: 1;
      filter: none;
      transform: translateY(0);


  #body-wrap.page .el-input__inner {
    background: var(--anzhiyu-card-bg);
    box-shadow: var(--anzhiyu-shadow-border);
    color: var(--anzhiyu-fontcolor);
  }
  #body-wrap.page .tk-admin-config .el-input__inner{
    color: currentColor;
  }

  #twikoo.twikoo .el-input__inner:focus,
  #twikoo.twikoo .el-textarea__inner:focus {
    border-color: var(--anzhiyu-main);
  }

  .tk-comments-container > .tk-comment {
    margin-top: 0 !important;
    margin-bottom: 1rem !important;
    transition: 0.3s;
    border-radius: 12px;
    padding: 0;
    padding-top: 1rem;
    border: none;
    border-top: var(--style-border-dashed);
  }

  #post-comment .comment-tips {
    background-color: rgba(103, 194, 58, 0.13);
    border: var(--style-border-always);
    border-color: var(--anzhiyu-green);
    color: var(--anzhiyu-green);
    border-radius: 8px;
    padding: 8px 12px;
    margin-top: 0.5rem;
    display: none;
    width: 100%;
  }

  #post-comment .comment-tips.show {
    display: flex;
  }

  #page .tk-comments-container > .tk-comment
    background: var(--anzhiyu-card-bg);
    padding: 1rem;
    padding-bottom: 1rem;
    border: var(--style-border);
    border-top: var(--style-border);
    box-shadow: var(--anzhiyu-shadow-border);
    if hexo-config('dynamicEffect.pageCommentsRollZoom')
      animation: animate-in-and-out 1s linear forwards;
      animation-timeline: view();
      &:has(.OwO-open)
        z-index: 1

  .tk-content {
    margin-top: 0.5rem;
    overflow: auto;
    max-height: 500px;
  }

  .tk-comments .tk-row-actions-start {
    position: absolute;
    top: -84px;
    left: 17px;
  }

  @media screen and (max-width: 768px) {
    .OwO .OwO-body {
      min-width: 260px;
    }
    .tk-comments .tk-row-actions-start {
      top: -176px;
    }
    #twikoo .tk-comments .tk-submit .el-button--primary {
      height: 122px;
      top: -126px;
    }
    #twikoo .el-textarea__inner {
      background: var(--anzhiyu-card-bg)!important;
      overflow: hidden;
      resize: none!important;
    }

    .tk-comments button.el-button.tk-preview.el-button--default.el-button--small {
      display: none;
    }
    .tk-comments .tk-main .tk-submit .tk-row.actions {
      justify-content: center;
    }
    .tk-comments button.el-button.tk-send,
    .tk-comments button.el-button.tk-cancel {
      width: 100%;
    }
    .tk-comments .tk-row-actions-start {
      position: absolute;
    }
  }

  .OwO .OwO-body .OwO-items .OwO-item:hover {
    box-shadow: var(--anzhiyu-shadow-lightblack) !important;
    border-radius: 8px;
  }
