# completed: 控制该事项是否已完成 (true 表示已完成，会显示对勾和删除线)
# class_type: 用于分类筛选功能，同类型的待办事项会在筛选时一起显示

- class_name: "前端进阶"
  class_type: "前端"
  todo_list:
    - content: "前端开发：TypeScript"
      completed: true
    - content: "前端开发：Vue3 + Vite + Pinia"
      completed: true
    - content: "前端开发：组件库：Ant Design Vue/primevue"
      completed: false
    - content: "前端开发：React + Hooks + Redux Toolkit"
      completed: false
    - content: "前端开发：Next.js/Nuxt.js服务端渲染"
      completed: false
    - content: "前端开发：低代码框架：qiankun/wujie/wasp"
      completed: false

- class_name: "后端开发"
  class_type: "后端"
  todo_list:
    - content: "后端开发：Java：Spring全家桶"
      completed: false
    - content: "后端开发：Node.js/NestJS"
      completed: false
    - content: "后端开发：Python：FastAPI"
      completed: false
    - content: "后端开发：GraphQL/gRPC接口设计"
      completed: false

- class_name: "AI开发"
  class_type: "AI"
  todo_list:
    - content: "AI基础：AIGC + AI全栈开发 + AI平台应用"
      completed: false
    - content: "AI基础：数学基础：….一大堆数学知识"
      completed: false

- class_name: "运维技术"
  class_type: "运维"
  todo_list:
    - content: "运维技术：Linux系统管理与Shell脚本"
      completed: true
    - content: "运维技术：Nginx配置与优化"
      completed: false
    - content: "运维技术：Docker容器化"
      completed: false
    - content: "运维技术：CI/CD：Jenkins/GitHub Actions"
      completed: false
    - content: "运维技术：云服务：阿里云/腾讯云/AWS"
      completed: true

- class_name: "UI设计"
  class_type: "UI"
  todo_list:
    - content: "UI设计：Figma/Sketch/Photoshop"
      completed: false
    - content: "UI设计：设计规范：Material Design/Ant Design/PrimeVue"
      completed: false
    - content: "UI设计：设计模式：MVVM/MVC/MVP"
      completed: false

- class_name: "产品设计"
  class_type: "产品"
  todo_list:
    - content: "产品设计：文档精读"
