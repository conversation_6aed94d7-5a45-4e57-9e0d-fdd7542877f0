extends includes/layout.pug

block content
    main.layout#content-inner
        #archive
            .article-sort-title #{__('page.archives')}<sup>#{site.posts.find({parent: {$exists: false}}).length}</sup>
            case theme.page.archives
                when 0
                    .article-sort
                        include includes/mixins/articleSort
                    include includes/mixins/pagination
                when 1
                    .recent-posts#recent-posts
                        each post,index in page.posts.find({ parent: { $exists: false } }).data
                            include includes/widgets/home/<USER>
                        include includes/mixins/pagination
                    if theme.comment.hot_tip.enable
                        include ./includes/widgets/third-party/hot/index.pug
        include includes/widgets/aside/aside
