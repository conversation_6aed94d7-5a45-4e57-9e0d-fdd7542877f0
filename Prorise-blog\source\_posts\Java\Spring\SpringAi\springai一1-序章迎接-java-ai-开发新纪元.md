---
title: SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 59358
date: 2025-03-20 16:13:45
---

## 1. 序章：迎接 Java AI 开发新纪元

你好，亲爱的读者。在开始这段旅程之前，我想先与你分享我撰写这本教程的初衷。作为一名在 Java 生态中耕耘多年的开发者，我亲眼见证了 Spring 框架如何一次次地简化我们的开发工作，从最初的依赖注入到后来的 Spring Boot，它始终是企业级应用开发的基石。而今，我们正处在一个由人工智能，特别是大语言模型（LLM）引领的全新技术浪潮之巅。

在这场变革中，Python 生态凭借其先发优势，涌现出了像 LangChain、LlamaIndex 这样的优秀框架，它们极大地推动了 AI 应用的落地。然而，对于广大的 Java 开发者和海量的存量企业级系统而言，我们不禁要问：我们该如何拥抱这股浪潮？难道要放弃我们熟悉的、健壮的 Java 生态，转向一个全新的技术栈吗？

答案显然是否定的。正是基于这样的背景，Spring AI 应运而生。它不是对 Python 框架的简单复制，而是 Spring 团队深思熟虑后，为我们 Java/Spring 开发者量身打造的一柄利器。它承载着一个清晰的使命：**将构建生成式 AI 应用的复杂性，封装在开发者们熟悉的 Spring 模式之下，让每一位 Java 开发者都能快速、低门槛地成为 AI 应用的构建者。**

我希望通过这本教程，不仅能教会你如何使用 Spring AI 的 API，更能与你一同深入理解其背后的设计哲学，探索它如何将企业级的稳定性、可移植性和强大的生态整合能力，注入到 AI 应用开发中。让我们一起，迎接并开创属于 Java 的 AI 开发新纪元。

### 1.1 为何需要 Spring AI？

#### 1.1.1 问题背景：Java 开发者的挑战与机遇

AI 技术的爆发，特别是以 ChatGPT 为代表的大语言模型（LLM）的出现，为软件行业带来了颠覆性的变革。它们不再仅仅是特定领域的算法工具，而是能够理解自然语言、生成内容、进行逻辑推理的通用能力平台。这意味着，未来的软件开发，将不再仅仅是编写精确的代码指令，更多地会涉及到如何与 AI 进行高效“沟通”和“协作”。

对于我们 Java 开发者而言，这既是挑战也是机遇：
-   **挑战**：传统的开发模式和技术栈，并未针对与 LLM 的交互进行优化。如何管理复杂的 Prompt、如何将外部知识（如企业内部文档）融入 AI 的回答、如何让 AI 调用已有的业务 API、如何在不同的 AI 服务商之间平滑切换……这些都成了摆在我们面前的现实难题。
-   **机遇**：全球绝大多数的企业级核心应用和数据都构建在 Java 技术栈之上。如果能将 AI 的强大能力与这些现有的、经过生产环境严苛考验的系统无缝融合，将催生出巨大的商业价值。例如，为传统的 CRM 系统增加一个能理解客户意图的智能客服；让 ERP 系统能够根据自然语言指令生成复杂的财务报表。

#### 1.1.2 Python 生态的启示

在 AI 应用开发领域，Python 生态无疑走在了前列。以 LangChain 和 LlamaIndex 为代表的框架，通过提供一系列标准化的组件和链式（Chain）调用模式，极大地简化了构建 LLM 应用的流程。它们的成功揭示了一个关键点：在应用层面，开发者需要的不是从零开始研究模型算法，而是一个**高效的“胶水层”或“编排框架”**，用来粘合业务逻辑、数据和底层的 AI 模型。

这些框架的核心思想包括：
*   **模型I/O封装**：将与不同 LLM 的 API 交互统一化。
*   **Prompt 管理**：提供模板化、可复用的 Prompt 工程能力。
*   **数据连接**：轻松加载、转换和向量化外部文档，为 RAG（检索增强生成）提供支持。
*   **链与代理**：将多个调用步骤组合成一个连贯的工作流，甚至赋予 AI 自主规划和使用工具的能力。

#### 1.1.3 Spring AI 的诞生

Spring AI 正是在深刻理解了 Java 开发者的痛点和借鉴了 Python 生态成功经验的基础上诞生的。它并非要成为 LangChain 的 Java 克隆版，而是要成为 **Spring 生态原生的 AI 应用开发框架**。这意味着它将 AI 能力的集成，完全融入了 Spring 的核心理念之中，为 Java 开发者提供了一条熟悉、平滑且强大的 AI 应用开发路径。

> Spring AI 的使命，是让 AI 应用的开发过程变得“Spring 化”——即通过自动配置、依赖注入和统一的编程模型，将复杂的底层实现隐藏起来，让开发者能聚焦于业务创新本身。

### 1.2 Spring AI 的核心设计哲学

Spring AI 的强大之处，并不仅仅在于它提供了哪些功能，更在于其背后遵循的一系列深刻的设计哲学。这些哲学确保了用它构建的应用不仅能快速开发，更能满足企业级的严苛要求。

#### 1.2.1 可移植性

这是 Spring AI 最核心的设计原则之一。在当前 AI 模型服务百家争鸣的时代，将应用与某一个特定的 AI 提供商（如 OpenAI）深度绑定，是极具风险的。未来你可能因为成本、性能或特定功能的需求，需要切换到 Azure OpenAI、Anthropic Claude、Google Gemini 或是某个开源的本地模型。

Spring AI 通过定义一套**统一的、可移植的 API**（如 `ChatClient`, `EmbeddingClient`, `VectorStore`）来解决这个问题。你的业务代码只与这些接口交互，完全感知不到底层具体是哪个模型在提供服务。切换 AI 提供商，在绝大多数情况下，仅仅是更换一个 Maven 依赖和修改几行配置文件的事情，业务代码无需任何改动。

* **场景示例**：你的应用最初使用 OpenAI 的模型。后来，公司出于数据合规要求，需要切换到部署在私有云的 Azure OpenAI 服务。使用 Spring AI，你只需要将 `spring-boot-starter-openai` 依赖更换为 `spring-boot-starter-azure-openai`，并更新 `application.yml` 中的配置即可，整个过程可能只需要几分钟。

#### 1.2.2 模块化

Spring AI 遵循 Spring Boot 的“按需引入”原则，将不同的功能拆分到独立的模块化 Starter 中。你的应用需要什么功能，就引入对应的依赖，绝不强制你引入一整个庞大而臃肿的全家桶。

*   需要与聊天模型交互？引入 `spring-ai-openai-spring-boot-starter`。
*   需要使用向量数据库？引入 `spring-ai-pgvector-store-spring-boot-starter`。
*   需要文生图功能？引入 `spring-ai-image-models-spring-boot-starter`。

这种模块化的设计，使得你的应用可以保持轻量和整洁，只包含你真正需要的功能。

#### 1.2.3 Spring 原生体验

Spring AI 不是一个孤立的库，它与 Spring 生态系统是血肉相连的。它充分利用了 Spring 框架的强大能力，为开发者提供了无与伦比的便利性。

-   **自动配置**：你只需要在配置文件中提供 API Key 等少量信息，Spring AI 就能自动为你创建并配置好 `ChatClient` 等核心组件的 Bean。
-   **依赖注入** 你可以在任何 Spring 组件（如 `@Service`, `@RestController`）中，通过 `@Autowired` 直接注入 `ChatClient` 并使用，完全符合 Spring 的开发习惯。
-   **AOP 与其他 Spring 特性**: 你可以像对其他 Spring Bean 一样，对 AI 相关的 Bean 应用 AOP（如添加日志、事务）、进行精细化的配置（`@ConfigurationProperties`）等。

#### 1.2.4 企业级特性

除了开发便利性，Spring AI 还深刻理解企业级应用对**稳定性、可观测性和安全性**的诉求。

-   **可观测性**：Spring AI 内置了对 Micrometer 的支持，能够自动暴露与 AI 调用相关的核心指标，如 Token 消耗、请求延迟、错误率等。你可以轻松地将这些指标对接到 Prometheus & Grafana 等监控系统中，实现对 AI 服务成本和性能的精细化度量。
-   **生产环境部署**: Spring AI 从设计之初就考虑到了云原生和高性能场景，支持虚拟线程以提升 I/O 密集型 AI 调用的吞吐量，并兼容 GraalVM 原生镜像，实现应用的快速启动和低内存占用。

### 1.3 Spring AI 在 AI 技术栈中的定位

为了更清晰地理解 Spring AI 的角色，我们可以通过一段简述来描绘它在整个 AI 技术栈中的位置。



1.  **向上支撑业务应用**：为上层业务逻辑提供一套稳定、统一、易用的 AI 能力调用接口。业务开发者无需关心底层 AI 模型的具体实现细节和 API 差异。
2.  **向下连接 AI 生态**：它作为适配器，连接并管理着各种底层服务，包括：
    *   **AI 模型服务**：如 OpenAI, Azure OpenAI, Google Vertex AI, Anthropic, Ollama 等。
    *   **数据源与存储**：特别是向量数据库（Vector Stores），如 PGVector, Milvus, Redis, Chroma 等，它们是实现 RAG（检索增强生成）模式的关键。

> **核心定位**：Spring AI **专注于应用集成与编排，而非模型训练**。它旨在帮助开发者“使用”好 AI 模型，将 AI 的通用能力与具体的业务场景相结合，创造出实际的应用价值。

#### 1.3.1 与 LangChain4j 等框架的对比

在 Java 的 AI 开发生态中，除了 Spring AI，也存在其他优秀的框架，如 LangChain4j。了解它们之间的异同，有助于我们做出更合适的选型。

| 特性 | Spring AI | LangChain4j |
| :--- | :--- | :--- |
| **核心理念** | **深度融入 Spring 生态**，提供原生的 Spring Boot 开发体验。 | **作为通用的 Java AI 库**，可以独立使用，也可与其他框架（如 Quarkus, Micronaut）集成。 |
| **配置方式** | 强依赖 Spring Boot 的自动配置 (`application.properties`/`yml`)。 | 提供灵活的编程式构建器 (Builder)，配置更自由。 |
| **生态整合** | 与 Spring Data, Spring Batch, Spring Cloud 等生态组件有天然的、深度的整合潜力。 | 更加独立，与特定框架的整合需要开发者自行完成。 |
| **目标用户** | **Spring/Spring Boot 开发者**，特别是企业级应用开发者。 | 更广泛的 Java 开发者，包括对 Spring 不熟悉的开发者。 |
| **优势** | 开发体验对 Spring 用户极其平滑，企业级特性（如可观测性）集成度高。 | 灵活性高，不锁定于任何一个框架，学习曲线可能对非 Spring 用户更平缓。 |

**结论**：两者都是非常优秀的框架。如果你的技术栈是基于 Spring Boot 的，或者你正在构建复杂的企业级 AI 应用，**Spring AI 几乎是你的不二之选**，因为它能为你提供无与伦比的生态整合能力和开发便利性。如果你需要一个更轻量、更独立的 Java AI 库，或者你的项目未使用 Spring，那么 LangChain4j 会是一个非常好的选择。

### 1.4 本教程导览

本教程将带领你从零开始，逐步深入 Spring AI 的世界。无论你是 AI 领域的新手，还是希望将 AI 能力引入现有 Java 项目的资深开发者，都能在这里找到清晰的学习路径。

#### 1.4.1 前置知识要求

为了更好地跟上本教程的节奏，我希望你具备以下基础：
*   熟练掌握 **Java** 编程语言（JDK 17+）。
*   具备 **Spring Boot** 的基础开发经验，了解依赖注入、Bean、配置文件等核心概念。
*   了解 **Maven** 或 **Gradle** 的基本使用。

你不需要有任何 AI 或机器学习的背景知识，教程中涉及到的所有 AI 概念，我都会用通俗易懂的方式进行解释。





### **1.5 项目愿景与技术栈**

我们的目标是构建一个名为 **“AI-Copilot”** 的企业级 AI 聊天应用。这是一个全栈项目，技术选型如下：

| 层面 | 技术栈 | 说明 |
| :--- | :--- | :--- |
| **后端** | Spring Boot 3.3+, Spring AI 1.0+, Java 17+, Maven, MySQL | 健壮、可靠的企业级后端，负责所有 AI 逻辑和业务处理。 |
| **前端** | Vue 3, Vite, Tailwind CSS 4, DaisyUI, `element-plus-x` | 现代、高效、美观的前端界面，专注于提供流畅的聊天体验。 |

#### **1.5.1 后端先行：初始化 Spring AI 项目**

我们首先搭建后端服务。请确保您的环境中已安装：

  * **JDK**: 17 或 21 (推荐)
  * **Maven**: 3.8+
  * **IDE**: IntelliJ IDEA 或 VS Code (已安装 Java 和 Spring Boot 插件)

**第一步：使用 Spring Initializr 创建项目**

1.  访问官方项目生成器：[start.spring.io](https://start.spring.io/)
2.  填写项目元数据，参照下表配置：

| 配置项 | 值 | 说明 |
| :--- | :--- | :--- |
| **Project** | `Maven` | 构建工具 |
| **Language** | `Java` | 开发语言 |
| **Spring Boot**| `3.3.1` (或更高稳定版) | 核心框架版本 |
| **Group** | `com.copilot` | 公司或组织域名反向 |
| **Artifact** | `ai-copilot-backend` | 项目名 |
| **Packaging** | `Jar` | 打包方式 |
| **Java** | `17` | JDK 版本 |

3.  在 **Dependencies** 部分，点击 "ADD DEPENDENCIES..." 添加以下依赖：

      * `Spring Web`: 用于构建 RESTful API。
      * `Spring AI OpenAI Support`: 提供与 OpenAI 及其兼容 API (如 DeepSeek) 的集成。
      * `Spring Data JDBC`: 提供基础的数据库访问能力。
      * `MySQL Driver`: 连接 MySQL 数据库。
      

> **`注意：`** Lombok不要在依赖里面添加，而是手动引入，这样才能避免很多编译问题

3.  点击 "GENERATE" 下载项目压缩包，解压后用您的 IDE 打开。

**第二步：辨析核心依赖并配置**

打开 `pom.xml`，我们加上额外的Lombok配置：

```xml
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
```

**第三步：配置 `application.yml`**

将 `src/main/resources/application.properties` 重命名为 `application.yml`，并填入以下基础配置。我们将使用 DeepSeek API 作为入门，因为它提供了免费额度且与 OpenAI API 兼容。

> **安全提示**：请先前往 [DeepSeek 开放平台](https://platform.deepseek.com/api_keys) 注册并获取您的 API Key。切勿将密钥直接提交到代码仓库，最佳实践是使用环境变量。

```yaml
# src/main/resources/application.yml

server:
  port: 8080

spring:
  # AI 配置
  ai:
    # 使用 DeepSeek (兼容 OpenAI 协议)
    openai:
      # 注意: 根据 DeepSeek 最新规范, 此处不加 /v1 后缀
      base-url: https://api.deepseek.com
      # 从环境变量中读取 API Key, 如果没有则使用下面的默认值 (仅供测试)
      api-key: ${DEEPSEEK_API_KEY:sk-your-deepseek-api-key}
      chat:
        options:
          # 指定默认使用的模型
          model: deepseek-chat
          # 控制输出的创造性，0.7 是一个较为平衡的值
          temperature: 0.7
  
  # 数据库连接配置 (为后续章节做准备)
  datasource:
    url: **************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

# MyBatis-Plus 配置 (为后续章节做准备)
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
```

**第四步：运行后端**

在 IDE 中找到主启动类 `AiCopilotBackendApplication.java` 并运行，或在项目根目录执行 `./mvnw spring-boot:run`。看到 Spring Boot 启动日志表示后端已成功运行。

-----

