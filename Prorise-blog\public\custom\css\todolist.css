/* build time:Sun Jul 27 2025 10:31:58 GMT+0800 (中国标准时间)*/
body[data-type=todolist] #web_bg{background:var(--anzhiyu-background)}body[data-type=todolist] #page{border:0;box-shadow:none!important;padding:0!important;background:0 0!important}body[data-type=todolist] #page .page-title{display:none}#todolist-box{margin:0 auto;max-width:1200px;padding:0 15px}.author-content.todolist{border-radius:12px;overflow:hidden;transition:all .3s ease;position:relative;margin-bottom:20px}.author-content .tips{display:flex;align-items:center;gap:.5rem;color:var(--anzhiyu-fontcolor);opacity:.75}.author-content .tips i{color:var(--anzhiyu-theme);font-size:.8rem}#todolist-filter{margin:20px 0;padding:15px;background:var(--anzhiyu-card-bg);border-radius:12px;box-shadow:var(--anzhiyu-shadow-border)}.filter-title{display:flex;align-items:center;gap:.5rem;margin-bottom:12px;color:var(--anzhiyu-fontcolor);font-weight:600}.filter-title i{color:var(--anzhiyu-theme)}.filter-buttons{display:flex;flex-wrap:wrap;gap:8px}.filter-btn{background:var(--anzhiyu-card-bg);border:1px solid var(--anzhiyu-theme-op);color:var(--anzhiyu-fontcolor);border-radius:20px;padding:5px 15px;cursor:pointer;transition:all .3s ease;font-size:.9rem}.filter-btn:hover{transform:translateY(-2px);border-color:var(--anzhiyu-theme)}.filter-btn.active{background:var(--anzhiyu-theme);color:#fff}#todolist-main{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px;margin:20px 0}.todolist-item{position:relative;background:var(--anzhiyu-card-bg);border-radius:12px;padding:1.2rem;box-shadow:var(--anzhiyu-shadow-border);transition:all .3s ease;overflow:hidden;border-left:4px solid var(--anzhiyu-main);height:100%;display:flex;flex-direction:column;animation:fadeIn .5s ease forwards}.todolist-item:hover{transform:translateY(-5px);box-shadow:var(--anzhiyu-shadow-main)}h3.todolist-title{position:relative;margin:0 0 1rem 0!important;padding-bottom:.8rem;font-size:1.25rem;color:var(--anzhiyu-fontcolor);border-bottom:2px dashed var(--anzhiyu-theme-op);display:flex;align-items:center;justify-content:space-between}h3.todolist-title i{color:var(--anzhiyu-theme);font-size:1.1rem;margin-right:.5rem}h3.todolist-title span{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.task-count{background:var(--anzhiyu-theme-op);color:var(--anzhiyu-theme);padding:2px 8px;border-radius:12px;font-size:.8rem;font-weight:600}.todolist-ul{margin:0;padding:0;position:relative;flex:1}.todolist-ul li{list-style:none;padding:.75rem .5rem;margin-bottom:.5rem;border-radius:8px;transition:all .3s ease;display:flex;align-items:center;gap:.75rem;position:relative;background-color:transparent}.todolist-ul li:last-child{margin-bottom:0}.todolist-ul li:hover{background:linear-gradient(90deg,var(--anzhiyu-theme-op-deep) 0,transparent 100%);padding-left:1rem}li.todolist-li{color:var(--anzhiyu-fontcolor)}li.todolist-li i{color:var(--anzhiyu-theme);font-size:1.1rem;transition:all .3s ease;display:flex;align-items:center;justify-content:center;min-width:1.2em}li.todolist-li:hover i{transform:scale(1.2);filter:drop-shadow(0 0 2px var(--anzhiyu-theme-op))}li.todolist-li-done{color:var(--anzhiyu-secondtext)}li.todolist-li-done span{text-decoration:line-through;opacity:.8}li.todolist-li-done i{color:var(--anzhiyu-green);font-size:1.1rem;transition:transform .3s ease;display:flex;align-items:center;justify-content:center;min-width:1.2em;animation:checkmark .5s ease-out}li.todolist-li-done:hover i{transform:scale(1.1) rotate(5deg)}.todolist-li span,.todolist-li-done span{flex:1;position:relative;line-height:1.5}.progress-bar{height:4px;background-color:var(--anzhiyu-theme-op);border-radius:2px;margin-top:1rem;overflow:hidden}.progress{height:100%;background:linear-gradient(90deg,var(--anzhiyu-theme) 0,var(--anzhiyu-main) 100%);border-radius:2px;transition:width .5s ease}#todolist-pagination{display:flex;justify-content:center;margin:30px 0}.pagination-container{display:flex;align-items:center;gap:5px}.page-btn,.page-number{min-width:36px;height:36px;border:1px solid var(--anzhiyu-card-border);background:var(--anzhiyu-card-bg);border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;color:var(--anzhiyu-fontcolor)}.page-btn:hover:not(:disabled),.page-number:hover:not(.active){border-color:var(--anzhiyu-theme);color:var(--anzhiyu-theme)}.page-number.active{background:var(--anzhiyu-theme);color:#fff;border-color:var(--anzhiyu-theme)}.page-btn:disabled{opacity:.5;cursor:not-allowed}#page-numbers{display:flex;gap:5px}@media screen and (max-width:768px){#todolist-box{margin:0;padding:0 10px}.todolist-item{padding:1rem}h3.todolist-title{font-size:1.1rem}.filter-buttons{overflow-x:auto;padding-bottom:5px;flex-wrap:nowrap}}[data-theme=dark] .todolist-item{background:var(--anzhiyu-card-bg);border-left-color:var(--anzhiyu-main)}[data-theme=dark] .todolist-ul li:hover{background:linear-gradient(90deg,rgba(255,255,255,.08) 0,transparent 100%)}[data-theme=dark] .filter-btn{background:var(--anzhiyu-card-bg);border-color:var(--anzhiyu-theme-op)}@keyframes fadeIn{from{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.todolist-grid .todolist-item:nth-child(3n+1){animation-delay:.1s}.todolist-grid .todolist-item:nth-child(3n+2){animation-delay:.2s}.todolist-grid .todolist-item:nth-child(3n+3){animation-delay:.3s}@keyframes checkmark{0%{transform:scale(0)}50%{transform:scale(1.2)}100%{transform:scale(1)}}
/* rebuild by neat */