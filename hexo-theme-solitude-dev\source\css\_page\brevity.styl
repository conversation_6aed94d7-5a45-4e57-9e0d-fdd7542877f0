if hexo-config('brevity.home_mini')
  #bbTimeList
    background var(--efu-card-bg)
    border-radius 12px
    display flex
    white-space nowrap
    overflow hidden
    border var(--style-border)
    transition .3s
    height 50px
    width 100%
    justify-content space-between
    user-select none
    align-items center
    padding .5rem 1rem
    animation slide-in .6s 0s backwards
    margin-bottom .5rem

    &.more-page
      margin-bottom 0

    +minWidth1300()
      &:hover
        border var(--style-border-hover)
        box-shadow var(--efu-shadow-main)

    +maxWidth768()
      margin 0
      background var(--efu-background)
      border none
      padding 0 .2rem

  i.bber-logo,
  i.bber-gotobb
    transition .3s
    cursor pointer

    &:hover
      opacity .8
      color var(--efu-theme)

  i.bber-logo
    font-size 1.2rem
    margin-right 1rem

  i.bber-gotobb
    font-size 16px
    margin-left 1rem

if hexo-config('brevity.enable')
  #bber
    margin-top .5rem
    width 100%

    if hexo-config('brevity.style') == 2
      .meta
        display flex
        align-items center
        line-height 1.5
        position relative
        width 100%
        margin-bottom 4px

        .avatar
          margin 0
          width 50px
          height 50px
          border-radius 10px

        .info
          display flex
          flex-direction column
          margin-left 10px

          span.bber_nick
            color var(--efu-main)
            font-size .9rem

          time.bber_date
            font-size 14px
            opacity .6

        .goComment
          position absolute
          top 0
          right 0

    div
      &.bber-content
        display flex
        flex-direction initial
        flex-wrap wrap
        border-radius 12px
        width 100%
        height 100%

    if hexo-config('brevity.style') == 1
      .bber-bottom
        display flex
        justify-content space-between
        width 100%
        user-select none

      .bber-info
        display flex
        align-items center

        .fa-rectangles-mixed
          margin-right 8px

        .bber-info-time, .bber-info-location
          margin-right .5rem
          color var(--efu-fontcolor)
          font-size .7rem
          background-color var(--efu-gray-op)
          padding 0 8px
          border-radius 20px
          cursor default
          display flex
          align-items center

          i
            margin-right 8px
            font-size 16px

          .datafrom
            order 2
            color var(--efu-secondtext)
            font-size .7rem
            margin-left 8px

            small
              font-size .7rem

        .bber-content-link
          display flex
          margin-right .5rem
          color var(--efu-secondtext)
          font-size .7rem
          align-items center
          background-color rgba(245, 108, 108, .13)
          padding 0 8px
          border-radius 20px

          &:hover
            background-color var(--efu-main)
            color var(--efu-white)

          i
            margin-right 3px
            font-size 16px

    .count
      color var(--efu-secondtext)
      font-size .8rem

    p
      margin 0

    .datafrom
      i
        margin-right 4px

    if hexo-config('brevity.music')
      .bber-music
        width 100%
        height 90px
        margin .5rem 0
        border-radius 8px
        overflow hidden
        border var(--style-border-always)
        background var(--efu-secondbg)

        .aplayer-lrc
          display none

      .aplayer
        margin 0

        .aplayer-body
          background-color var(--efu-card-bg)

        .aplayer-info
          .aplayer-music
            height 23px

            .aplayer-title
              font-size .8rem
              font-weight 700
              margin 0
              color var(--efu-fontcolor)

          .aplayer-controller
            align-items center

            .aplayer-bar-wrap
              padding 0

              .aplayer-bar
                background var(--efu-gray)
                height 8px
                border-radius 12px
                transition .3s
                overflow hidden

                .aplayer-played
                  height 100%
                  border-radius 12px

                  .aplayer-thumb
                    display none

                .aplayer-loaded
                  height 100%
                  border-radius 12px

              &:hover
                .aplayer-bar
                  height 12px

            .aplayer-time
              position initial

        &.aplayer-withlrc
          .aplayer-pic
            height 82px
            width 82px
            margin 4px
            border-radius 4px

    .bber-video
      position relative
      padding 30% 50%
      margin 0.5rem 0

    .bber-content-img
      height 100px
      margin auto
      margin-top 0.2rem
      margin-bottom 0.3rem
      margin-left 0
      border-radius 12px
      overflow hidden
      display flex
      position relative

      img
        margin-right 10px
        object-fit cover
        max-height 100%
        border-radius 12px
        min-height 100px
        height 100px

    .bber-content
      .datacont
        order 0
        font-size .8rem
        font-weight 700
        color var(--efu-fontcolor)
        width 100%
        line-height 1.38
        border-radius 12px
        margin-bottom .5rem
        display flex
        flex-direction column
        text-align justify

    .timeline
       ul
        li
          &.item
            position relative
            width calc(33% - .06rem)
            border var(--style-border-always)
            border-radius 12px
            padding 1rem 1rem .5rem
            transition .3s
            display flex
            flex-direction column
            flex-wrap nowrap
            justify-content space-between
            align-items flex-start
            background var(--efu-card-bg)
            margin-right .5rem
            margin-bottom .5rem
            box-shadow var(--efu-shadow-border)

            +maxWidth1300()
              width 49.5%
              margin-right 1%

            +maxWidth768()
              width 100%
              margin-right 0
              padding 16px 20px

            hr
              display flex
              position relative
              margin 8px 0
              border 1px dashed var(--efu-theme-op)
              width 100%

            &:hover
              border var(--style-border-hover)

    ul
      &.list
        display flex
        flex-direction row
        flex-wrap wrap

    li
      &.item
        display flex
        flex-direction column
        flex-wrap nowrap
        align-items flex-start

  #bber .bber-video video,
  #bber .bber-video iframe
    position absolute
    width 100%
    height 100%
    left 0
    top 0
    margin 0
    border-radius 8px
    border var(--style-border)

  #bber-tips
    font-size 14px
    display flex
    justify-content center
    margin-top 1rem
    color var(--efu-secondtext)

  #bber-talk
    width 100%
    height 25px
    line-height 25px
    display flex
    flex-direction column

    .li-style
      width auto
      max-width 100%
      height 25px
      text-align center
      overflow hidden
      text-overflow ellipsis
      transition .3s
      align-items center
      font-weight 700
      margin auto
      cursor pointer
      white-space nowrap

      .solitude
        font-size 1rem
        margin-left .5rem

  #bbtalk
    overflow hidden
    width 100%
    text-overflow ellipsis
    white-space nowrap

  #bbTimeList
    .li-style
      &:hover
        color var(--efu-theme)
        transition .3s
