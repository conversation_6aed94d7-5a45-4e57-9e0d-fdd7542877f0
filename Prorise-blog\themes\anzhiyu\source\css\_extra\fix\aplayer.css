/* 音乐播放器 */

.aplayer.aplayer-narrow .aplayer-body,
.aplayer.aplayer-narrow .aplayer-pic {
  height: 66px;
  width: 66px;
}

/* 导航栏音乐 */
@media screen and (max-width: 1200px) {
  #nav-music {
    display: none !important;
  }
}

#nav-music {
  display: flex;
  align-items: center;
  z-index: 9;
  position: fixed;
  bottom: 20px;
  left: 20px;
  cursor: pointer;
  transition: all 0.5s, left 0s;
  transform-origin: left bottom;
  box-shadow: var(--anzhiyu-shadow-border);
  border-radius: 40px;
  overflow: hidden;
}

#nav-music:active {
  transform: scale(0.97);
}

#nav-music.playing {
  border: var(--style-border);
  box-shadow: 0 0px 12px -3px var(--anzhiyu-none);
  animation: playingShadow 5s linear infinite;
}

@keyframes playingShadow {
  0% {
    box-shadow: 0 0px 12px -3px var(--anzhiyu-none);
  }

  50% {
    box-shadow: 0 0px 12px 0px var(--anzhiyu-main);
  }

  100% {
    box-shadow: 0 0px 12px -3px var(--anzhiyu-none);
  }
}

#nav-music .aplayer.aplayer-withlrc .aplayer-pic {
  height: 25px;
  width: 25px;
  border-radius: 40px;
  z-index: 1;
  transition: 0.3s;
  transform: rotate(0deg) scale(1);
  border: var(--style-border-always);
  animation: changeright 24s linear infinite;
  animation-play-state: paused;
}

#nav-music.playing .aplayer.aplayer-withlrc .aplayer-pic {
  box-shadow: 0 0 14px #ffffffa6;
  transform: rotate(0deg) scale(1.1);
  border-color: var(--anzhiyu-white);
  animation-play-state: running;
}

@keyframes changeright {
  0% {
    transform: rotate(0deg) scale(1.1);
    box-shadow: 0 0 2px #ffffff00;
  }

  25% {
    transform: rotate(90deg) scale(1.1);
    box-shadow: 0 0 14px #ffffff;
  }

  50% {
    transform: rotate(180deg) scale(1.1);
    box-shadow: 0 0 2px #ffffff00;
  }

  75% {
    transform: rotate(270deg) scale(1.1);
    box-shadow: 0 0 14px #ffffff;
  }

  100% {
    transform: rotate(360deg) scale(1.1);
    box-shadow: 0 0 2px #ffffff00;
  }
}

#nav-music .aplayer.aplayer-withlrc .aplayer-info {
  height: 100%;
  color: var(--anzhiyu-fontcolor);
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
}

#nav-music.playing .aplayer.aplayer-withlrc .aplayer-info {
  color: var(--anzhiyu-white);
}

#nav-music.playing #nav-music-hoverTips {
  width: 0;
  opacity: 0;
}
#nav-music #nav-music-hoverTips {
  color: var(--anzhiyu-white);
  background: var(--anzhiyu-main);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  display: flex;
  border-radius: 40px;
  opacity: 0;
  font-size: 12px;
  z-index: 2;
  transition: 0.3s;
}

#nav-music:hover:not(.playing) #nav-music-hoverTips {
  opacity: 1;
}

#nav-music
  .aplayer
  .aplayer-info
  .aplayer-controller
  .aplayer-bar-wrap:hover
  .aplayer-bar
  .aplayer-played
  .aplayer-thumb {
  display: none;
}

#nav-music .aplayer {
  background: var(--card-bg);
  border-radius: 60px;
  height: 41px;
  display: flex;
  margin: 0;
  transition: 0.3s;
  border: var(--style-border);
  box-shadow: none;
}

#nav-music.playing .aplayer {
  background: var(--anzhiyu-main);
  border: var(--style-border-hover);
  backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: blur(20px);
  transform: translateZ(0);
}

#nav-music .aplayer .aplayer-notice {
  display: none;
}

#nav-music .aplayer .aplayer-miniswitcher {
  display: none;
}

#nav-music .aplayer .aplayer-body {
  position: relative;
  display: flex;
  align-items: center;
}

#nav-music .aplayer-list {
  display: none;
}

#nav-music .aplayer .aplayer-info .aplayer-music {
  margin: 0;
  display: flex;
  align-items: center;
  padding: 0 12px 0 8px;
  cursor: pointer;
  z-index: 1;
  height: 100%;
}

#nav-music .aplayer .aplayer-info .aplayer-controller .aplayer-time {
  display: none;
}

#nav-music .aplayer .aplayer-info .aplayer-music .aplayer-author {
  display: none;
}

#nav-music .aplayer.aplayer-withlist .aplayer-info {
  border: none;
}

#nav-music .aplayer .aplayer-pic .aplayer-button {
  bottom: 50%;
  right: 50%;
  transform: translate(50%, 50%);
  margin: 0;
  transition: 0.3s;
}
#nav-music .aplayer .aplayer-pic:has(.aplayer-button.aplayer-play) {
  animation-play-state: paused;
  transform: rotate(0deg) scale(1) !important;
}
#nav-music .aplayer.aplayer-withlrc .aplayer-pic {
  margin-left: 8px;
}
#nav-music .aplayer .aplayer-info .aplayer-music .aplayer-title {
  cursor: pointer;
  line-height: 1;
  display: inline-block;
  white-space: nowrap;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: 0.3s;
  user-select: none;
}

#nav-music .aplayer .aplayer-info .aplayer-controller {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

#nav-music .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap {
  margin: 0;
  padding: 0;
}

#nav-music .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar {
  height: 100%;
  background: 0 0;
}

#nav-music .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-loaded {
  display: none;
}

#nav-music .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played {
  height: 100%;
  opacity: 0.1;
  background-color: var(--anzhiyu-white) !important;
  animation: lightBar 5s ease infinite;
  animation-play-state: paused;
}

#nav-music.playing .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played {
  animation-play-state: running;
}

@keyframes lightBar {
  0% {
    opacity: 0.1;
  }

  60% {
    opacity: 0.3;
  }

  100% {
    opacity: 0.1;
  }
}

/* 歌词 */
#nav-music .aplayer.aplayer-withlrc .aplayer-lrc {
  width: 0;
  opacity: 0;
  transition: 0.3s;
  margin-top: -2px;
  padding: 5px 0;
}
#nav-music.stretch .aplayer.aplayer-withlrc .aplayer-lrc {
  width: 200px;
  margin-left: 8px;
  opacity: 1;
}

#nav-music .aplayer .aplayer-lrc p.aplayer-lrc-current {
  color: var(--anzhiyu-white);
  border: none;
  min-height: 20px;
}

#nav-music .aplayer .aplayer-lrc:after,
#nav-music .aplayer .aplayer-lrc:before {
  display: none;
}

#nav-music .aplayer .aplayer-lrc p {
  color: #ffffffb3;
  line-height: 40px !important;
  height: 40px !important;
  margin: 0px 0;
  vertical-align: top;
  /* display: inline-block; */
}

#nav-music .aplayer .aplayer-pic {
  pointer-events: none;
}
#nav-music .aplayer .aplayer-pic .aplayer-button {
  pointer-events: all;
}
