---
title: SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 18714
date: 2025-03-20 19:13:45
---

## **2. 快速入门：构建你的第一个 AI 应用**

在正式启航构建 "AI-Copilot" 应用的核心功能之前，我们必须先统一思想，建立一套贯穿整个项目生命周期的开发规范。本章将首先完整呈现我们在后端（Spring Boot）和前端（Vue 3）开发中需要共同遵守的最佳实践和编码标准。随后，我们将立即应用这些规范，完成前后端的首次通信，实现一个完全符合企业级标准的聊天功能。

### **2.1 我们的开发法规：企业级应用最佳实践**

#### **2.1.1 后端开发规范 (Spring Boot)**

这份规范涵盖了从代码结构、设计模式到性能、安全、测试等各个方面，是我们构建企业级、高可用后端服务的指导方针。

**1. 代码组织与结构**

  * **目录结构**: 采用分层架构来分离关注点，提高可维护性。我们项目的目录结构将遵循：

    ```
    src/
     ├── main/
     │   ├── java/
     │   │   └── com/copilot/aicopilotbackend/
     │   │       ├── AiCopilotBackendApplication.java
     │   │       ├── config/
     │   │       ├── controller/
     │   │       ├── service/
     │   │       ├── repository/
     │   │       ├── dto/
     │   │       │   ├── request/
     │   │       │   └── response/
     │   │       ├── entity/
     │   │       └── exception/
     │   └── resources/
     │       └── application.yml
     └── test/
         └── ...
    ```

  * **文件命名约定**:

      * **类 (Classes)**: 帕斯卡命名法 (PascalCase), e.g., `ChatController`.
      * **方法 (Methods)**: 驼峰命名法 (camelCase), e.g., `getChatResponse`.

**2. 通用模式与反模式**

  * **依赖注入 (DI)**: **强制使用构造器注入**。
  * **服务层模式 (Service Layer)**: **业务逻辑必须封装在 Service 层**，保持 Controller 轻薄。
  * **DTO 模式**: **强制使用 DTO** 在各层之间传递数据。
  * **反模式**: 严禁使用字段注入；严禁在 Controller 中编写业务逻辑；严禁硬编码。
  * **代码简洁性**: 使用 Lombok 注解减少样板代码，专注核心业务逻辑。

**3. 性能、安全与测试**

  * **安全**: 对所有外部输入进行严格校验。密码等敏感信息必须使用强哈希算法加盐存储。
  * **测试**: 对 Service 层进行单元测试，对 Controller 层进行集成测试。
  * **日志记录**: 使用 Lombok @Slf4j 注解，记录关键信息，采用结构化日志格式。

### **2.2 企业级异常处理与日志系统**

在构建聊天应用之前，我们需要建立一套完善的异常处理和日志系统。这不仅能提高应用的稳定性，还能为后续的运维和调试提供有力支持。

#### **2.2.1 技术栈与设计理念**

我们采用以下技术栈来实现企业级的异常处理：

- **Spring Boot 3.5.3** - 核心框架
- **Lombok** - 减少样板代码，提高开发效率
- **SLF4J** - 统一日志接口
- **Spring AI** - AI 服务集成

设计理念：
- 分层异常处理（业务异常、系统异常）
- 统一的错误码体系
- 标准化API响应格式
- 简洁的代码结构

#### **2.2.2 错误码体系设计**

建立统一的错误码枚举，采用分类编码方式：

```java
package com.copilot.aicopilotbackend.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 错误码枚举
 * 使用分类编码：A开头为客户端错误，B开头为业务错误，C开头为系统错误
 */
@Getter
@AllArgsConstructor
public enum ErrorCode {
    
    // 成功
    SUCCESS("00000", "操作成功"),
    
    // A开头 - 客户端错误
    MISSING_PARAMETER("A0001", "缺少必要参数"),
    INVALID_PARAMETER("A0002", "参数格式不正确"),
    PARAMETER_TOO_LONG("A0003", "参数长度超出限制"),
    
    // B开头 - 业务错误  
    AI_RESPONSE_EMPTY("B0001", "AI服务返回空响应"),
    AI_RESPONSE_TIMEOUT("B0002", "AI服务响应超时"),
    
    // C开头 - 系统错误
    SYSTEM_ERROR("C0001", "系统内部错误"),
    AI_SERVICE_UNAVAILABLE("C0002", "AI服务不可用"),
    NETWORK_ERROR("C0003", "网络连接错误");
    
    private final String code;
    private final String message;
}
```

#### **2.2.3 统一API响应格式**

设计标准化的响应格式，支持泛型和时间戳：

```java
package com.copilot.aicopilotbackend.dto.response;

import com.copilot.aicopilotbackend.exception.ErrorCode;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 统一API响应格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    private String code;
    private String message;
    private T data;
    private LocalDateTime timestamp;
    
    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.code = ErrorCode.SUCCESS.getCode();
        response.message = ErrorCode.SUCCESS.getMessage();
        response.data = data;
        response.timestamp = LocalDateTime.now();
        return response;
    }
    
    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> fail(ErrorCode errorCode) {
        ApiResponse<T> response = new ApiResponse<>();
        response.code = errorCode.getCode();
        response.message = errorCode.getMessage();
        response.timestamp = LocalDateTime.now();
        return response;
    }
    
    /**
     * 失败响应（自定义消息）
     */
    public static <T> ApiResponse<T> fail(ErrorCode errorCode, String customMessage) {
        ApiResponse<T> response = new ApiResponse<>();
        response.code = errorCode.getCode();
        response.message = customMessage;
        response.timestamp = LocalDateTime.now();
        return response;
    }
}
```

#### **2.2.4 分层异常处理**

**业务异常类**：
```java
package com.copilot.aicopilotbackend.exception;

import lombok.Getter;

/**
 * 业务异常类
 */
@Getter
public class BusinessException extends RuntimeException {
    
    private final ErrorCode errorCode;
    private final String traceId;
    
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.traceId = null;
    }
    
    public BusinessException(ErrorCode errorCode, String customMessage) {
        super(customMessage);
        this.errorCode = errorCode;
        this.traceId = null;
    }
    
    // 其他构造器...
}
```

**系统异常类**：
```java
package com.copilot.aicopilotbackend.exception;

import lombok.Getter;

/**
 * 系统异常类
 */
@Getter
public class SystemException extends RuntimeException {
    
    private final ErrorCode errorCode;
    private final String traceId;
    
    public SystemException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.traceId = null;
    }
    
    public SystemException(ErrorCode errorCode, String customMessage) {
        super(customMessage);
        this.errorCode = errorCode;
        this.traceId = null;
    }
    
    // 其他构造器...
}
```

#### **2.2.5 全局异常拦截器**

```java
package com.copilot.aicopilotbackend.exception;

import com.copilot.aicopilotbackend.dto.response.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

/**
 * 全局异常处理器
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Void>> handleBusinessException(BusinessException e, WebRequest request) {
        log.warn("业务异常: {} - {}", e.getErrorCode().getCode(), e.getMessage());
        
        ApiResponse<Void> response = ApiResponse.fail(e.getErrorCode(), e.getMessage());
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(SystemException.class)
    public ResponseEntity<ApiResponse<Void>> handleSystemException(SystemException e, WebRequest request) {
        log.error("系统异常: {} - {}", e.getErrorCode().getCode(), e.getMessage(), e);
        
        ApiResponse<Void> response = ApiResponse.fail(e.getErrorCode(), e.getMessage());
        return ResponseEntity.internalServerError().body(response);
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleGenericException(Exception e, WebRequest request) {
        log.error("未知异常: {}", e.getMessage(), e);
        
        ApiResponse<Void> response = ApiResponse.fail(ErrorCode.SYSTEM_ERROR);
        return ResponseEntity.internalServerError().body(response);
    }
}
```

-----

