<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元 | Prorise的小站</title><meta name="keywords" content="Java微服务篇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><meta name="application-name" content="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><meta property="og:url" content="https://prorise666.site/posts/59358.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="1. 序章：迎接 Java AI 开发新纪元你好，亲爱的读者。在开始这段旅程之前，我想先与你分享我撰写这本教程的初衷。作为一名在 Java 生态中耕耘多年的开发者，我亲眼见证了 Spring 框架如何一次次地简化我们的开发工作，从最初的依赖注入到后来的 Spring Boot，它始终是企业级应用开发"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta name="description" content="1. 序章：迎接 Java AI 开发新纪元你好，亲爱的读者。在开始这段旅程之前，我想先与你分享我撰写这本教程的初衷。作为一名在 Java 生态中耕耘多年的开发者，我亲眼见证了 Spring 框架如何一次次地简化我们的开发工作，从最初的依赖注入到后来的 Spring Boot，它始终是企业级应用开发"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/59358.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元",postAI:"true",pageFillDescription:"1. 序章：迎接 Java AI 开发新纪元, 1.1 为何需要 Spring AI？, 1.1.1 问题背景：Java 开发者的挑战与机遇, 1.1.2 Python 生态的启示, 1.1.3 Spring AI 的诞生, 1.2 Spring AI 的核心设计哲学, 1.2.1 可移植性, 1.2.2 模块化, 1.2.3 Spring 原生体验, 1.2.4 企业级特性, 1.3 Spring AI 在 AI 技术栈中的定位, 1.3.1 与 LangChain4j 等框架的对比, 1.4 本教程导览, 1.4.1 前置知识要求, 1.5 项目愿景与技术栈, 1.5.1 后端先行：初始化 Spring AI 项目序章迎接开发新纪元你好亲爱的读者在开始这段旅程之前我想先与你分享我撰写这本教程的初衷作为一名在生态中耕耘多年的开发者我亲眼见证了框架如何一次次地简化我们的开发工作从最初的依赖注入到后来的它始终是企业级应用开发的基石而今我们正处在一个由人工智能特别是大语言模型引领的全新技术浪潮之巅在这场变革中生态凭借其先发优势涌现出了像这样的优秀框架它们极大地推动了应用的落地然而对于广大的开发者和海量的存量企业级系统而言我们不禁要问我们该如何拥抱这股浪潮难道要放弃我们熟悉的健壮的生态转向一个全新的技术栈吗答案显然是否定的正是基于这样的背景应运而生它不是对框架的简单复制而是团队深思熟虑后为我们开发者量身打造的一柄利器它承载着一个清晰的使命将构建生成式应用的复杂性封装在开发者们熟悉的模式之下让每一位开发者都能快速低门槛地成为应用的构建者我希望通过这本教程不仅能教会你如何使用的更能与你一同深入理解其背后的设计哲学探索它如何将企业级的稳定性可移植性和强大的生态整合能力注入到应用开发中让我们一起迎接并开创属于的开发新纪元为何需要问题背景开发者的挑战与机遇技术的爆发特别是以为代表的大语言模型的出现为软件行业带来了颠覆性的变革它们不再仅仅是特定领域的算法工具而是能够理解自然语言生成内容进行逻辑推理的通用能力平台这意味着未来的软件开发将不再仅仅是编写精确的代码指令更多地会涉及到如何与进行高效沟通和协作对于我们开发者而言这既是挑战也是机遇挑战传统的开发模式和技术栈并未针对与的交互进行优化如何管理复杂的如何将外部知识如企业内部文档融入的回答如何让调用已有的业务如何在不同的服务商之间平滑切换这些都成了摆在我们面前的现实难题机遇全球绝大多数的企业级核心应用和数据都构建在技术栈之上如果能将的强大能力与这些现有的经过生产环境严苛考验的系统无缝融合将催生出巨大的商业价值例如为传统的系统增加一个能理解客户意图的智能客服让系统能够根据自然语言指令生成复杂的财务报表生态的启示在应用开发领域生态无疑走在了前列以和为代表的框架通过提供一系列标准化的组件和链式调用模式极大地简化了构建应用的流程它们的成功揭示了一个关键点在应用层面开发者需要的不是从零开始研究模型算法而是一个高效的胶水层或编排框架用来粘合业务逻辑数据和底层的模型这些框架的核心思想包括模型封装将与不同的交互统一化管理提供模板化可复用的工程能力数据连接轻松加载转换和向量化外部文档为检索增强生成提供支持链与代理将多个调用步骤组合成一个连贯的工作流甚至赋予自主规划和使用工具的能力的诞生正是在深刻理解了开发者的痛点和借鉴了生态成功经验的基础上诞生的它并非要成为的克隆版而是要成为生态原生的应用开发框架这意味着它将能力的集成完全融入了的核心理念之中为开发者提供了一条熟悉平滑且强大的应用开发路径的使命是让应用的开发过程变得化即通过自动配置依赖注入和统一的编程模型将复杂的底层实现隐藏起来让开发者能聚焦于业务创新本身的核心设计哲学的强大之处并不仅仅在于它提供了哪些功能更在于其背后遵循的一系列深刻的设计哲学这些哲学确保了用它构建的应用不仅能快速开发更能满足企业级的严苛要求可移植性这是最核心的设计原则之一在当前模型服务百家争鸣的时代将应用与某一个特定的提供商如深度绑定是极具风险的未来你可能因为成本性能或特定功能的需求需要切换到或是某个开源的本地模型通过定义一套统一的可移植的如来解决这个问题你的业务代码只与这些接口交互完全感知不到底层具体是哪个模型在提供服务切换提供商在绝大多数情况下仅仅是更换一个依赖和修改几行配置文件的事情业务代码无需任何改动场景示例你的应用最初使用的模型后来公司出于数据合规要求需要切换到部署在私有云的服务使用你只需要将依赖更换为并更新中的配置即可整个过程可能只需要几分钟模块化遵循的按需引入原则将不同的功能拆分到独立的模块化中你的应用需要什么功能就引入对应的依赖绝不强制你引入一整个庞大而臃肿的全家桶需要与聊天模型交互引入需要使用向量数据库引入需要文生图功能引入这种模块化的设计使得你的应用可以保持轻量和整洁只包含你真正需要的功能原生体验不是一个孤立的库它与生态系统是血肉相连的它充分利用了框架的强大能力为开发者提供了无与伦比的便利性自动配置你只需要在配置文件中提供等少量信息就能自动为你创建并配置好等核心组件的依赖注入你可以在任何组件如中通过直接注入并使用完全符合的开发习惯与其他特性你可以像对其他一样对相关的应用如添加日志事务进行精细化的配置等企业级特性除了开发便利性还深刻理解企业级应用对稳定性可观测性和安全性的诉求可观测性内置了对的支持能够自动暴露与调用相关的核心指标如消耗请求延迟错误率等你可以轻松地将这些指标对接到等监控系统中实现对服务成本和性能的精细化度量生产环境部署从设计之初就考虑到了云原生和高性能场景支持虚拟线程以提升密集型调用的吞吐量并兼容原生镜像实现应用的快速启动和低内存占用在技术栈中的定位为了更清晰地理解的角色我们可以通过一段简述来描绘它在整个技术栈中的位置向上支撑业务应用为上层业务逻辑提供一套稳定统一易用的能力调用接口业务开发者无需关心底层模型的具体实现细节和差异向下连接生态它作为适配器连接并管理着各种底层服务包括模型服务如等数据源与存储特别是向量数据库如等它们是实现检索增强生成模式的关键核心定位专注于应用集成与编排而非模型训练它旨在帮助开发者使用好模型将的通用能力与具体的业务场景相结合创造出实际的应用价值与等框架的对比在的开发生态中除了也存在其他优秀的框架如了解它们之间的异同有助于我们做出更合适的选型特性核心理念深度融入生态提供原生的开发体验作为通用的库可以独立使用也可与其他框架如集成配置方式强依赖的自动配置提供灵活的编程式构建器配置更自由生态整合与等生态组件有天然的深度的整合潜力更加独立与特定框架的整合需要开发者自行完成目标用户开发者特别是企业级应用开发者更广泛的开发者包括对不熟悉的开发者优势开发体验对用户极其平滑企业级特性如可观测性集成度高灵活性高不锁定于任何一个框架学习曲线可能对非用户更平缓结论两者都是非常优秀的框架如果你的技术栈是基于的或者你正在构建复杂的企业级应用几乎是你的不二之选因为它能为你提供无与伦比的生态整合能力和开发便利性如果你需要一个更轻量更独立的库或者你的项目未使用那么会是一个非常好的选择本教程导览本教程将带领你从零开始逐步深入的世界无论你是领域的新手还是希望将能力引入现有项目的资深开发者都能在这里找到清晰的学习路径前置知识要求为了更好地跟上本教程的节奏我希望你具备以下基础熟练掌握编程语言具备的基础开发经验了解依赖注入配置文件等核心概念了解或的基本使用你不需要有任何或机器学习的背景知识教程中涉及到的所有概念我都会用通俗易懂的方式进行解释项目愿景与技术栈我们的目标是构建一个名为的企业级聊天应用这是一个全栈项目技术选型如下层面技术栈说明后端健壮可靠的企业级后端负责所有逻辑和业务处理前端现代高效美观的前端界面专注于提供流畅的聊天体验后端先行初始化项目我们首先搭建后端服务请确保您的环境中已安装或推荐或已安装和插件第一步使用创建项目访问官方项目生成器填写项目元数据参照下表配置配置项值说明构建工具开发语言或更高稳定版核心框架版本公司或组织域名反向项目名打包方式版本在部分点击添加以下依赖用于构建提供与及其兼容如的集成提供基础的数据库访问能力连接数据库注意不要在依赖里面添加而是手动引入这样才能避免很多编译问题点击下载项目压缩包解压后用您的打开第二步辨析核心依赖并配置打开我们加上额外的配置第三步配置将重命名为并填入以下基础配置我们将使用作为入门因为它提供了免费额度且与兼容安全提示请先前往开放平台注册并获取您的切勿将密钥直接提交到代码仓库最佳实践是使用环境变量配置使用兼容协议注意根据最新规范此处不加后缀从环境变量中读取如果没有则使用下面的默认值仅供测试指定默认使用的模型控制输出的创造性是一个较为平衡的值数据库连接配置为后续章节做准备配置为后续章节做准备第四步运行后端在中找到主启动类并运行或在项目根目录执行看到启动日志表示后端已成功运行",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-08 13:53:50",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#1-%E5%BA%8F%E7%AB%A0%EF%BC%9A%E8%BF%8E%E6%8E%A5-Java-AI-%E5%BC%80%E5%8F%91%E6%96%B0%E7%BA%AA%E5%85%83"><span class="toc-text">1. 序章：迎接 Java AI 开发新纪元</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-1-%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81-Spring-AI%EF%BC%9F"><span class="toc-text">1.1 为何需要 Spring AI？</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-1-1-%E9%97%AE%E9%A2%98%E8%83%8C%E6%99%AF%EF%BC%9AJava-%E5%BC%80%E5%8F%91%E8%80%85%E7%9A%84%E6%8C%91%E6%88%98%E4%B8%8E%E6%9C%BA%E9%81%87"><span class="toc-text">1.1.1 问题背景：Java 开发者的挑战与机遇</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-1-2-Python-%E7%94%9F%E6%80%81%E7%9A%84%E5%90%AF%E7%A4%BA"><span class="toc-text">1.1.2 Python 生态的启示</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-1-3-Spring-AI-%E7%9A%84%E8%AF%9E%E7%94%9F"><span class="toc-text">1.1.3 Spring AI 的诞生</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-Spring-AI-%E7%9A%84%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1%E5%93%B2%E5%AD%A6"><span class="toc-text">1.2 Spring AI 的核心设计哲学</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-2-1-%E5%8F%AF%E7%A7%BB%E6%A4%8D%E6%80%A7"><span class="toc-text">1.2.1 可移植性</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-2-2-%E6%A8%A1%E5%9D%97%E5%8C%96"><span class="toc-text">1.2.2 模块化</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-2-3-Spring-%E5%8E%9F%E7%94%9F%E4%BD%93%E9%AA%8C"><span class="toc-text">1.2.3 Spring 原生体验</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-2-4-%E4%BC%81%E4%B8%9A%E7%BA%A7%E7%89%B9%E6%80%A7"><span class="toc-text">1.2.4 企业级特性</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-3-Spring-AI-%E5%9C%A8-AI-%E6%8A%80%E6%9C%AF%E6%A0%88%E4%B8%AD%E7%9A%84%E5%AE%9A%E4%BD%8D"><span class="toc-text">1.3 Spring AI 在 AI 技术栈中的定位</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-3-1-%E4%B8%8E-LangChain4j-%E7%AD%89%E6%A1%86%E6%9E%B6%E7%9A%84%E5%AF%B9%E6%AF%94"><span class="toc-text">1.3.1 与 LangChain4j 等框架的对比</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-%E6%9C%AC%E6%95%99%E7%A8%8B%E5%AF%BC%E8%A7%88"><span class="toc-text">1.4 本教程导览</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-4-1-%E5%89%8D%E7%BD%AE%E7%9F%A5%E8%AF%86%E8%A6%81%E6%B1%82"><span class="toc-text">1.4.1 前置知识要求</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-%E9%A1%B9%E7%9B%AE%E6%84%BF%E6%99%AF%E4%B8%8E%E6%8A%80%E6%9C%AF%E6%A0%88"><span class="toc-text">1.5 项目愿景与技术栈</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-5-1-%E5%90%8E%E7%AB%AF%E5%85%88%E8%A1%8C%EF%BC%9A%E5%88%9D%E5%A7%8B%E5%8C%96-Spring-AI-%E9%A1%B9%E7%9B%AE"><span class="toc-text">1.5.1 后端先行：初始化 Spring AI 项目</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java微服务篇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-03-20T08:13:45.000Z" title="发表于 2025-03-20 16:13:45">2025-03-20</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:50.320Z" title="更新于 2025-07-08 13:53:50">2025-07-08</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.9k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>13分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/59358.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/59358.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url">Java微服务篇</a><h1 id="CrawlerTitle" itemprop="name headline">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-03-20T08:13:45.000Z" title="发表于 2025-03-20 16:13:45">2025-03-20</time><time itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:50.320Z" title="更新于 2025-07-08 13:53:50">2025-07-08</time></header><div id="postchat_postcontent"><h2 id="1-序章：迎接-Java-AI-开发新纪元"><a href="#1-序章：迎接-Java-AI-开发新纪元" class="headerlink" title="1. 序章：迎接 Java AI 开发新纪元"></a>1. 序章：迎接 Java AI 开发新纪元</h2><p>你好，亲爱的读者。在开始这段旅程之前，我想先与你分享我撰写这本教程的初衷。作为一名在 Java 生态中耕耘多年的开发者，我亲眼见证了 Spring 框架如何一次次地简化我们的开发工作，从最初的依赖注入到后来的 Spring Boot，它始终是企业级应用开发的基石。而今，我们正处在一个由人工智能，特别是大语言模型（LLM）引领的全新技术浪潮之巅。</p><p>在这场变革中，Python 生态凭借其先发优势，涌现出了像 LangChain、LlamaIndex 这样的优秀框架，它们极大地推动了 AI 应用的落地。然而，对于广大的 Java 开发者和海量的存量企业级系统而言，我们不禁要问：我们该如何拥抱这股浪潮？难道要放弃我们熟悉的、健壮的 Java 生态，转向一个全新的技术栈吗？</p><p>答案显然是否定的。正是基于这样的背景，Spring AI 应运而生。它不是对 Python 框架的简单复制，而是 Spring 团队深思熟虑后，为我们 Java/Spring 开发者量身打造的一柄利器。它承载着一个清晰的使命：<strong>将构建生成式 AI 应用的复杂性，封装在开发者们熟悉的 Spring 模式之下，让每一位 Java 开发者都能快速、低门槛地成为 AI 应用的构建者。</strong></p><p>我希望通过这本教程，不仅能教会你如何使用 Spring AI 的 API，更能与你一同深入理解其背后的设计哲学，探索它如何将企业级的稳定性、可移植性和强大的生态整合能力，注入到 AI 应用开发中。让我们一起，迎接并开创属于 Java 的 AI 开发新纪元。</p><h3 id="1-1-为何需要-Spring-AI？"><a href="#1-1-为何需要-Spring-AI？" class="headerlink" title="1.1 为何需要 Spring AI？"></a>1.1 为何需要 Spring AI？</h3><h4 id="1-1-1-问题背景：Java-开发者的挑战与机遇"><a href="#1-1-1-问题背景：Java-开发者的挑战与机遇" class="headerlink" title="1.1.1 问题背景：Java 开发者的挑战与机遇"></a>1.1.1 问题背景：Java 开发者的挑战与机遇</h4><p>AI 技术的爆发，特别是以 ChatGPT 为代表的大语言模型（LLM）的出现，为软件行业带来了颠覆性的变革。它们不再仅仅是特定领域的算法工具，而是能够理解自然语言、生成内容、进行逻辑推理的通用能力平台。这意味着，未来的软件开发，将不再仅仅是编写精确的代码指令，更多地会涉及到如何与 AI 进行高效“沟通”和“协作”。</p><p>对于我们 Java 开发者而言，这既是挑战也是机遇：</p><ul><li><strong>挑战</strong>：传统的开发模式和技术栈，并未针对与 LLM 的交互进行优化。如何管理复杂的 Prompt、如何将外部知识（如企业内部文档）融入 AI 的回答、如何让 AI 调用已有的业务 API、如何在不同的 AI 服务商之间平滑切换……这些都成了摆在我们面前的现实难题。</li><li><strong>机遇</strong>：全球绝大多数的企业级核心应用和数据都构建在 Java 技术栈之上。如果能将 AI 的强大能力与这些现有的、经过生产环境严苛考验的系统无缝融合，将催生出巨大的商业价值。例如，为传统的 CRM 系统增加一个能理解客户意图的智能客服；让 ERP 系统能够根据自然语言指令生成复杂的财务报表。</li></ul><h4 id="1-1-2-Python-生态的启示"><a href="#1-1-2-Python-生态的启示" class="headerlink" title="1.1.2 Python 生态的启示"></a>1.1.2 Python 生态的启示</h4><p>在 AI 应用开发领域，Python 生态无疑走在了前列。以 LangChain 和 LlamaIndex 为代表的框架，通过提供一系列标准化的组件和链式（Chain）调用模式，极大地简化了构建 LLM 应用的流程。它们的成功揭示了一个关键点：在应用层面，开发者需要的不是从零开始研究模型算法，而是一个<strong>高效的“胶水层”或“编排框架”</strong>，用来粘合业务逻辑、数据和底层的 AI 模型。</p><p>这些框架的核心思想包括：</p><ul><li><strong>模型I/O封装</strong>：将与不同 LLM 的 API 交互统一化。</li><li><strong>Prompt 管理</strong>：提供模板化、可复用的 Prompt 工程能力。</li><li><strong>数据连接</strong>：轻松加载、转换和向量化外部文档，为 RAG（检索增强生成）提供支持。</li><li><strong>链与代理</strong>：将多个调用步骤组合成一个连贯的工作流，甚至赋予 AI 自主规划和使用工具的能力。</li></ul><h4 id="1-1-3-Spring-AI-的诞生"><a href="#1-1-3-Spring-AI-的诞生" class="headerlink" title="1.1.3 Spring AI 的诞生"></a>1.1.3 Spring AI 的诞生</h4><p>Spring AI 正是在深刻理解了 Java 开发者的痛点和借鉴了 Python 生态成功经验的基础上诞生的。它并非要成为 LangChain 的 Java 克隆版，而是要成为 <strong>Spring 生态原生的 AI 应用开发框架</strong>。这意味着它将 AI 能力的集成，完全融入了 Spring 的核心理念之中，为 Java 开发者提供了一条熟悉、平滑且强大的 AI 应用开发路径。</p><blockquote><p>Spring AI 的使命，是让 AI 应用的开发过程变得“Spring 化”——即通过自动配置、依赖注入和统一的编程模型，将复杂的底层实现隐藏起来，让开发者能聚焦于业务创新本身。</p></blockquote><h3 id="1-2-Spring-AI-的核心设计哲学"><a href="#1-2-Spring-AI-的核心设计哲学" class="headerlink" title="1.2 Spring AI 的核心设计哲学"></a>1.2 Spring AI 的核心设计哲学</h3><p>Spring AI 的强大之处，并不仅仅在于它提供了哪些功能，更在于其背后遵循的一系列深刻的设计哲学。这些哲学确保了用它构建的应用不仅能快速开发，更能满足企业级的严苛要求。</p><h4 id="1-2-1-可移植性"><a href="#1-2-1-可移植性" class="headerlink" title="1.2.1 可移植性"></a>1.2.1 可移植性</h4><p>这是 Spring AI 最核心的设计原则之一。在当前 AI 模型服务百家争鸣的时代，将应用与某一个特定的 AI 提供商（如 OpenAI）深度绑定，是极具风险的。未来你可能因为成本、性能或特定功能的需求，需要切换到 Azure OpenAI、Anthropic Claude、Google Gemini 或是某个开源的本地模型。</p><p>Spring AI 通过定义一套<strong>统一的、可移植的 API</strong>（如 <code>ChatClient</code>, <code>EmbeddingClient</code>, <code>VectorStore</code>）来解决这个问题。你的业务代码只与这些接口交互，完全感知不到底层具体是哪个模型在提供服务。切换 AI 提供商，在绝大多数情况下，仅仅是更换一个 Maven 依赖和修改几行配置文件的事情，业务代码无需任何改动。</p><ul><li><strong>场景示例</strong>：你的应用最初使用 OpenAI 的模型。后来，公司出于数据合规要求，需要切换到部署在私有云的 Azure OpenAI 服务。使用 Spring AI，你只需要将 <code>spring-boot-starter-openai</code> 依赖更换为 <code>spring-boot-starter-azure-openai</code>，并更新 <code>application.yml</code> 中的配置即可，整个过程可能只需要几分钟。</li></ul><h4 id="1-2-2-模块化"><a href="#1-2-2-模块化" class="headerlink" title="1.2.2 模块化"></a>1.2.2 模块化</h4><p>Spring AI 遵循 Spring Boot 的“按需引入”原则，将不同的功能拆分到独立的模块化 Starter 中。你的应用需要什么功能，就引入对应的依赖，绝不强制你引入一整个庞大而臃肿的全家桶。</p><ul><li>需要与聊天模型交互？引入 <code>spring-ai-openai-spring-boot-starter</code>。</li><li>需要使用向量数据库？引入 <code>spring-ai-pgvector-store-spring-boot-starter</code>。</li><li>需要文生图功能？引入 <code>spring-ai-image-models-spring-boot-starter</code>。</li></ul><p>这种模块化的设计，使得你的应用可以保持轻量和整洁，只包含你真正需要的功能。</p><h4 id="1-2-3-Spring-原生体验"><a href="#1-2-3-Spring-原生体验" class="headerlink" title="1.2.3 Spring 原生体验"></a>1.2.3 Spring 原生体验</h4><p>Spring AI 不是一个孤立的库，它与 Spring 生态系统是血肉相连的。它充分利用了 Spring 框架的强大能力，为开发者提供了无与伦比的便利性。</p><ul><li><strong>自动配置</strong>：你只需要在配置文件中提供 API Key 等少量信息，Spring AI 就能自动为你创建并配置好 <code>ChatClient</code> 等核心组件的 Bean。</li><li><strong>依赖注入</strong> 你可以在任何 Spring 组件（如 <code>@Service</code>, <code>@RestController</code>）中，通过 <code>@Autowired</code> 直接注入 <code>ChatClient</code> 并使用，完全符合 Spring 的开发习惯。</li><li><strong>AOP 与其他 Spring 特性</strong>: 你可以像对其他 Spring Bean 一样，对 AI 相关的 Bean 应用 AOP（如添加日志、事务）、进行精细化的配置（<code>@ConfigurationProperties</code>）等。</li></ul><h4 id="1-2-4-企业级特性"><a href="#1-2-4-企业级特性" class="headerlink" title="1.2.4 企业级特性"></a>1.2.4 企业级特性</h4><p>除了开发便利性，Spring AI 还深刻理解企业级应用对<strong>稳定性、可观测性和安全性</strong>的诉求。</p><ul><li><strong>可观测性</strong>：Spring AI 内置了对 Micrometer 的支持，能够自动暴露与 AI 调用相关的核心指标，如 Token 消耗、请求延迟、错误率等。你可以轻松地将这些指标对接到 Prometheus &amp; Grafana 等监控系统中，实现对 AI 服务成本和性能的精细化度量。</li><li><strong>生产环境部署</strong>: Spring AI 从设计之初就考虑到了云原生和高性能场景，支持虚拟线程以提升 I/O 密集型 AI 调用的吞吐量，并兼容 GraalVM 原生镜像，实现应用的快速启动和低内存占用。</li></ul><h3 id="1-3-Spring-AI-在-AI-技术栈中的定位"><a href="#1-3-Spring-AI-在-AI-技术栈中的定位" class="headerlink" title="1.3 Spring AI 在 AI 技术栈中的定位"></a>1.3 Spring AI 在 AI 技术栈中的定位</h3><p>为了更清晰地理解 Spring AI 的角色，我们可以通过一段简述来描绘它在整个 AI 技术栈中的位置。</p><ol><li><strong>向上支撑业务应用</strong>：为上层业务逻辑提供一套稳定、统一、易用的 AI 能力调用接口。业务开发者无需关心底层 AI 模型的具体实现细节和 API 差异。</li><li><strong>向下连接 AI 生态</strong>：它作为适配器，连接并管理着各种底层服务，包括：<ul><li><strong>AI 模型服务</strong>：如 OpenAI, Azure OpenAI, Google Vertex AI, Anthropic, Ollama 等。</li><li><strong>数据源与存储</strong>：特别是向量数据库（Vector Stores），如 PGVector, Milvus, Redis, Chroma 等，它们是实现 RAG（检索增强生成）模式的关键。</li></ul></li></ol><blockquote><p><strong>核心定位</strong>：Spring AI <strong>专注于应用集成与编排，而非模型训练</strong>。它旨在帮助开发者“使用”好 AI 模型，将 AI 的通用能力与具体的业务场景相结合，创造出实际的应用价值。</p></blockquote><h4 id="1-3-1-与-LangChain4j-等框架的对比"><a href="#1-3-1-与-LangChain4j-等框架的对比" class="headerlink" title="1.3.1 与 LangChain4j 等框架的对比"></a>1.3.1 与 LangChain4j 等框架的对比</h4><p>在 Java 的 AI 开发生态中，除了 Spring AI，也存在其他优秀的框架，如 LangChain4j。了解它们之间的异同，有助于我们做出更合适的选型。</p><table><thead><tr><th align="left">特性</th><th align="left">Spring AI</th><th align="left">LangChain4j</th></tr></thead><tbody><tr><td align="left"><strong>核心理念</strong></td><td align="left"><strong>深度融入 Spring 生态</strong>，提供原生的 Spring Boot 开发体验。</td><td align="left"><strong>作为通用的 Java AI 库</strong>，可以独立使用，也可与其他框架（如 Quarkus, Micronaut）集成。</td></tr><tr><td align="left"><strong>配置方式</strong></td><td align="left">强依赖 Spring Boot 的自动配置 (<code>application.properties</code>/<code>yml</code>)。</td><td align="left">提供灵活的编程式构建器 (Builder)，配置更自由。</td></tr><tr><td align="left"><strong>生态整合</strong></td><td align="left">与 Spring Data, Spring Batch, Spring Cloud 等生态组件有天然的、深度的整合潜力。</td><td align="left">更加独立，与特定框架的整合需要开发者自行完成。</td></tr><tr><td align="left"><strong>目标用户</strong></td><td align="left"><strong>Spring/Spring Boot 开发者</strong>，特别是企业级应用开发者。</td><td align="left">更广泛的 Java 开发者，包括对 Spring 不熟悉的开发者。</td></tr><tr><td align="left"><strong>优势</strong></td><td align="left">开发体验对 Spring 用户极其平滑，企业级特性（如可观测性）集成度高。</td><td align="left">灵活性高，不锁定于任何一个框架，学习曲线可能对非 Spring 用户更平缓。</td></tr></tbody></table><p><strong>结论</strong>：两者都是非常优秀的框架。如果你的技术栈是基于 Spring Boot 的，或者你正在构建复杂的企业级 AI 应用，<strong>Spring AI 几乎是你的不二之选</strong>，因为它能为你提供无与伦比的生态整合能力和开发便利性。如果你需要一个更轻量、更独立的 Java AI 库，或者你的项目未使用 Spring，那么 LangChain4j 会是一个非常好的选择。</p><h3 id="1-4-本教程导览"><a href="#1-4-本教程导览" class="headerlink" title="1.4 本教程导览"></a>1.4 本教程导览</h3><p>本教程将带领你从零开始，逐步深入 Spring AI 的世界。无论你是 AI 领域的新手，还是希望将 AI 能力引入现有 Java 项目的资深开发者，都能在这里找到清晰的学习路径。</p><h4 id="1-4-1-前置知识要求"><a href="#1-4-1-前置知识要求" class="headerlink" title="1.4.1 前置知识要求"></a>1.4.1 前置知识要求</h4><p>为了更好地跟上本教程的节奏，我希望你具备以下基础：</p><ul><li>熟练掌握 <strong>Java</strong> 编程语言（JDK 17+）。</li><li>具备 <strong>Spring Boot</strong> 的基础开发经验，了解依赖注入、Bean、配置文件等核心概念。</li><li>了解 <strong>Maven</strong> 或 <strong>Gradle</strong> 的基本使用。</li></ul><p>你不需要有任何 AI 或机器学习的背景知识，教程中涉及到的所有 AI 概念，我都会用通俗易懂的方式进行解释。</p><h3 id="1-5-项目愿景与技术栈"><a href="#1-5-项目愿景与技术栈" class="headerlink" title="1.5 项目愿景与技术栈"></a><strong>1.5 项目愿景与技术栈</strong></h3><p>我们的目标是构建一个名为 <strong>“AI-Copilot”</strong> 的企业级 AI 聊天应用。这是一个全栈项目，技术选型如下：</p><table><thead><tr><th align="left">层面</th><th align="left">技术栈</th><th align="left">说明</th></tr></thead><tbody><tr><td align="left"><strong>后端</strong></td><td align="left">Spring Boot 3.3+, Spring AI 1.0+, Java 17+, Maven, MySQL</td><td align="left">健壮、可靠的企业级后端，负责所有 AI 逻辑和业务处理。</td></tr><tr><td align="left"><strong>前端</strong></td><td align="left">Vue 3, Vite, Tailwind CSS 4, DaisyUI, <code>element-plus-x</code></td><td align="left">现代、高效、美观的前端界面，专注于提供流畅的聊天体验。</td></tr></tbody></table><h4 id="1-5-1-后端先行：初始化-Spring-AI-项目"><a href="#1-5-1-后端先行：初始化-Spring-AI-项目" class="headerlink" title="1.5.1 后端先行：初始化 Spring AI 项目"></a><strong>1.5.1 后端先行：初始化 Spring AI 项目</strong></h4><p>我们首先搭建后端服务。请确保您的环境中已安装：</p><ul><li><strong>JDK</strong>: 17 或 21 (推荐)</li><li><strong>Maven</strong>: 3.8+</li><li><strong>IDE</strong>: IntelliJ IDEA 或 VS Code (已安装 Java 和 Spring Boot 插件)</li></ul><p><strong>第一步：使用 Spring Initializr 创建项目</strong></p><ol><li>访问官方项目生成器：<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly9zdGFydC5zcHJpbmcuaW8v">start.spring.io</a></li><li>填写项目元数据，参照下表配置：</li></ol><table><thead><tr><th align="left">配置项</th><th align="left">值</th><th align="left">说明</th></tr></thead><tbody><tr><td align="left"><strong>Project</strong></td><td align="left"><code>Maven</code></td><td align="left">构建工具</td></tr><tr><td align="left"><strong>Language</strong></td><td align="left"><code>Java</code></td><td align="left">开发语言</td></tr><tr><td align="left"><strong>Spring Boot</strong></td><td align="left"><code>3.3.1</code> (或更高稳定版)</td><td align="left">核心框架版本</td></tr><tr><td align="left"><strong>Group</strong></td><td align="left"><code>com.copilot</code></td><td align="left">公司或组织域名反向</td></tr><tr><td align="left"><strong>Artifact</strong></td><td align="left"><code>ai-copilot-backend</code></td><td align="left">项目名</td></tr><tr><td align="left"><strong>Packaging</strong></td><td align="left"><code>Jar</code></td><td align="left">打包方式</td></tr><tr><td align="left"><strong>Java</strong></td><td align="left"><code>17</code></td><td align="left">JDK 版本</td></tr></tbody></table><ol start="3"><li><p>在 <strong>Dependencies</strong> 部分，点击 “ADD DEPENDENCIES…” 添加以下依赖：</p><ul><li><code>Spring Web</code>: 用于构建 RESTful API。</li><li><code>Spring AI OpenAI Support</code>: 提供与 OpenAI 及其兼容 API (如 DeepSeek) 的集成。</li><li><code>Spring Data JDBC</code>: 提供基础的数据库访问能力。</li><li><code>MySQL Driver</code>: 连接 MySQL 数据库。</li></ul></li></ol><blockquote><p><strong><code>注意：</code></strong> Lombok不要在依赖里面添加，而是手动引入，这样才能避免很多编译问题</p></blockquote><ol start="3"><li>点击 “GENERATE” 下载项目压缩包，解压后用您的 IDE 打开。</li></ol><p><strong>第二步：辨析核心依赖并配置</strong></p><p>打开 <code>pom.xml</code>，我们加上额外的Lombok配置：</p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.projectlombok<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>lombok<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">scope</span>&gt;</span>provided<span class="tag">&lt;/<span class="name">scope</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure><p><strong>第三步：配置 <code>application.yml</code></strong></p><p>将 <code>src/main/resources/application.properties</code> 重命名为 <code>application.yml</code>，并填入以下基础配置。我们将使用 DeepSeek API 作为入门，因为它提供了免费额度且与 OpenAI API 兼容。</p><blockquote><p><strong>安全提示</strong>：请先前往 <a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly9wbGF0Zm9ybS5kZWVwc2Vlay5jb20vYXBpX2tleXM">DeepSeek 开放平台</a> 注册并获取您的 API Key。切勿将密钥直接提交到代码仓库，最佳实践是使用环境变量。</p></blockquote><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># src/main/resources/application.yml</span></span><br><span class="line"></span><br><span class="line"><span class="attr">server:</span></span><br><span class="line">  <span class="attr">port:</span> <span class="number">8080</span></span><br><span class="line"></span><br><span class="line"><span class="attr">spring:</span></span><br><span class="line">  <span class="comment"># AI 配置</span></span><br><span class="line">  <span class="attr">ai:</span></span><br><span class="line">    <span class="comment"># 使用 DeepSeek (兼容 OpenAI 协议)</span></span><br><span class="line">    <span class="attr">openai:</span></span><br><span class="line">      <span class="comment"># 注意: 根据 DeepSeek 最新规范, 此处不加 /v1 后缀</span></span><br><span class="line">      <span class="attr">base-url:</span> <span class="string">https://api.deepseek.com</span></span><br><span class="line">      <span class="comment"># 从环境变量中读取 API Key, 如果没有则使用下面的默认值 (仅供测试)</span></span><br><span class="line">      <span class="attr">api-key:</span> <span class="string">${DEEPSEEK_API_KEY:sk-your-deepseek-api-key}</span></span><br><span class="line">      <span class="attr">chat:</span></span><br><span class="line">        <span class="attr">options:</span></span><br><span class="line">          <span class="comment"># 指定默认使用的模型</span></span><br><span class="line">          <span class="attr">model:</span> <span class="string">deepseek-chat</span></span><br><span class="line">          <span class="comment"># 控制输出的创造性，0.7 是一个较为平衡的值</span></span><br><span class="line">          <span class="attr">temperature:</span> <span class="number">0.7</span></span><br><span class="line">  </span><br><span class="line">  <span class="comment"># 数据库连接配置 (为后续章节做准备)</span></span><br><span class="line">  <span class="attr">datasource:</span></span><br><span class="line">    <span class="attr">url:</span> <span class="string">**********************************************************************************************************</span></span><br><span class="line">    <span class="attr">username:</span> <span class="string">root</span></span><br><span class="line">    <span class="attr">password:</span> <span class="string">root</span></span><br><span class="line">    <span class="attr">driver-class-name:</span> <span class="string">com.mysql.cj.jdbc.Driver</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># MyBatis-Plus 配置 (为后续章节做准备)</span></span><br><span class="line"><span class="attr">mybatis-plus:</span></span><br><span class="line">  <span class="attr">configuration:</span></span><br><span class="line">    <span class="attr">map-underscore-to-camel-case:</span> <span class="literal">true</span></span><br><span class="line">    <span class="attr">log-impl:</span> <span class="string">org.apache.ibatis.logging.stdout.StdOutImpl</span></span><br></pre></td></tr></tbody></table></figure><p><strong>第四步：运行后端</strong></p><p>在 IDE 中找到主启动类 <code>AiCopilotBackendApplication.java</code> 并运行，或在项目根目录执行 <code>./mvnw spring-boot:run</code>。看到 Spring Boot 启动日志表示后端已成功运行。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/59358.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/59358.html&quot;)">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/59358.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元&amp;url=https://prorise666.site/posts/59358.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java微服务篇<span class="tagsPageCount">11</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="next-post pull-full"><a href="/posts/18714.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/5770.html" title="SpringAI（七）：7. Embedding Models：万物皆可向量化"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（七）：7. Embedding Models：万物皆可向量化</div></div></a></div><div><a href="/posts/52289.html" title="SpringAI（三）：3. 会话核心 API 深度解析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（三）：3. 会话核心 API 深度解析</div></div></a></div><div><a href="/posts/22322.html" title="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</div></div></a></div><div><a href="/posts/60609.html" title="SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南</div></div></a></div><div><a href="/posts/18714.html" title="SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用</div></div></a></div><div><a href="/posts/29776.html" title="SpringAI（六）：6. AI 的创造力：集成文生图能力"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（六）：6. AI 的创造力：集成文生图能力</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元",date:"2025-03-20 16:13:45",updated:"2025-07-08 13:53:50",tags:["Java微服务篇"],categories:["后端技术","Java"],content:'\n## 1. 序章：迎接 Java AI 开发新纪元\n\n你好，亲爱的读者。在开始这段旅程之前，我想先与你分享我撰写这本教程的初衷。作为一名在 Java 生态中耕耘多年的开发者，我亲眼见证了 Spring 框架如何一次次地简化我们的开发工作，从最初的依赖注入到后来的 Spring Boot，它始终是企业级应用开发的基石。而今，我们正处在一个由人工智能，特别是大语言模型（LLM）引领的全新技术浪潮之巅。\n\n在这场变革中，Python 生态凭借其先发优势，涌现出了像 LangChain、LlamaIndex 这样的优秀框架，它们极大地推动了 AI 应用的落地。然而，对于广大的 Java 开发者和海量的存量企业级系统而言，我们不禁要问：我们该如何拥抱这股浪潮？难道要放弃我们熟悉的、健壮的 Java 生态，转向一个全新的技术栈吗？\n\n答案显然是否定的。正是基于这样的背景，Spring AI 应运而生。它不是对 Python 框架的简单复制，而是 Spring 团队深思熟虑后，为我们 Java/Spring 开发者量身打造的一柄利器。它承载着一个清晰的使命：**将构建生成式 AI 应用的复杂性，封装在开发者们熟悉的 Spring 模式之下，让每一位 Java 开发者都能快速、低门槛地成为 AI 应用的构建者。**\n\n我希望通过这本教程，不仅能教会你如何使用 Spring AI 的 API，更能与你一同深入理解其背后的设计哲学，探索它如何将企业级的稳定性、可移植性和强大的生态整合能力，注入到 AI 应用开发中。让我们一起，迎接并开创属于 Java 的 AI 开发新纪元。\n\n### 1.1 为何需要 Spring AI？\n\n#### 1.1.1 问题背景：Java 开发者的挑战与机遇\n\nAI 技术的爆发，特别是以 ChatGPT 为代表的大语言模型（LLM）的出现，为软件行业带来了颠覆性的变革。它们不再仅仅是特定领域的算法工具，而是能够理解自然语言、生成内容、进行逻辑推理的通用能力平台。这意味着，未来的软件开发，将不再仅仅是编写精确的代码指令，更多地会涉及到如何与 AI 进行高效“沟通”和“协作”。\n\n对于我们 Java 开发者而言，这既是挑战也是机遇：\n-   **挑战**：传统的开发模式和技术栈，并未针对与 LLM 的交互进行优化。如何管理复杂的 Prompt、如何将外部知识（如企业内部文档）融入 AI 的回答、如何让 AI 调用已有的业务 API、如何在不同的 AI 服务商之间平滑切换……这些都成了摆在我们面前的现实难题。\n-   **机遇**：全球绝大多数的企业级核心应用和数据都构建在 Java 技术栈之上。如果能将 AI 的强大能力与这些现有的、经过生产环境严苛考验的系统无缝融合，将催生出巨大的商业价值。例如，为传统的 CRM 系统增加一个能理解客户意图的智能客服；让 ERP 系统能够根据自然语言指令生成复杂的财务报表。\n\n#### 1.1.2 Python 生态的启示\n\n在 AI 应用开发领域，Python 生态无疑走在了前列。以 LangChain 和 LlamaIndex 为代表的框架，通过提供一系列标准化的组件和链式（Chain）调用模式，极大地简化了构建 LLM 应用的流程。它们的成功揭示了一个关键点：在应用层面，开发者需要的不是从零开始研究模型算法，而是一个**高效的“胶水层”或“编排框架”**，用来粘合业务逻辑、数据和底层的 AI 模型。\n\n这些框架的核心思想包括：\n*   **模型I/O封装**：将与不同 LLM 的 API 交互统一化。\n*   **Prompt 管理**：提供模板化、可复用的 Prompt 工程能力。\n*   **数据连接**：轻松加载、转换和向量化外部文档，为 RAG（检索增强生成）提供支持。\n*   **链与代理**：将多个调用步骤组合成一个连贯的工作流，甚至赋予 AI 自主规划和使用工具的能力。\n\n#### 1.1.3 Spring AI 的诞生\n\nSpring AI 正是在深刻理解了 Java 开发者的痛点和借鉴了 Python 生态成功经验的基础上诞生的。它并非要成为 LangChain 的 Java 克隆版，而是要成为 **Spring 生态原生的 AI 应用开发框架**。这意味着它将 AI 能力的集成，完全融入了 Spring 的核心理念之中，为 Java 开发者提供了一条熟悉、平滑且强大的 AI 应用开发路径。\n\n> Spring AI 的使命，是让 AI 应用的开发过程变得“Spring 化”——即通过自动配置、依赖注入和统一的编程模型，将复杂的底层实现隐藏起来，让开发者能聚焦于业务创新本身。\n\n### 1.2 Spring AI 的核心设计哲学\n\nSpring AI 的强大之处，并不仅仅在于它提供了哪些功能，更在于其背后遵循的一系列深刻的设计哲学。这些哲学确保了用它构建的应用不仅能快速开发，更能满足企业级的严苛要求。\n\n#### 1.2.1 可移植性\n\n这是 Spring AI 最核心的设计原则之一。在当前 AI 模型服务百家争鸣的时代，将应用与某一个特定的 AI 提供商（如 OpenAI）深度绑定，是极具风险的。未来你可能因为成本、性能或特定功能的需求，需要切换到 Azure OpenAI、Anthropic Claude、Google Gemini 或是某个开源的本地模型。\n\nSpring AI 通过定义一套**统一的、可移植的 API**（如 `ChatClient`, `EmbeddingClient`, `VectorStore`）来解决这个问题。你的业务代码只与这些接口交互，完全感知不到底层具体是哪个模型在提供服务。切换 AI 提供商，在绝大多数情况下，仅仅是更换一个 Maven 依赖和修改几行配置文件的事情，业务代码无需任何改动。\n\n* **场景示例**：你的应用最初使用 OpenAI 的模型。后来，公司出于数据合规要求，需要切换到部署在私有云的 Azure OpenAI 服务。使用 Spring AI，你只需要将 `spring-boot-starter-openai` 依赖更换为 `spring-boot-starter-azure-openai`，并更新 `application.yml` 中的配置即可，整个过程可能只需要几分钟。\n\n#### 1.2.2 模块化\n\nSpring AI 遵循 Spring Boot 的“按需引入”原则，将不同的功能拆分到独立的模块化 Starter 中。你的应用需要什么功能，就引入对应的依赖，绝不强制你引入一整个庞大而臃肿的全家桶。\n\n*   需要与聊天模型交互？引入 `spring-ai-openai-spring-boot-starter`。\n*   需要使用向量数据库？引入 `spring-ai-pgvector-store-spring-boot-starter`。\n*   需要文生图功能？引入 `spring-ai-image-models-spring-boot-starter`。\n\n这种模块化的设计，使得你的应用可以保持轻量和整洁，只包含你真正需要的功能。\n\n#### 1.2.3 Spring 原生体验\n\nSpring AI 不是一个孤立的库，它与 Spring 生态系统是血肉相连的。它充分利用了 Spring 框架的强大能力，为开发者提供了无与伦比的便利性。\n\n-   **自动配置**：你只需要在配置文件中提供 API Key 等少量信息，Spring AI 就能自动为你创建并配置好 `ChatClient` 等核心组件的 Bean。\n-   **依赖注入** 你可以在任何 Spring 组件（如 `@Service`, `@RestController`）中，通过 `@Autowired` 直接注入 `ChatClient` 并使用，完全符合 Spring 的开发习惯。\n-   **AOP 与其他 Spring 特性**: 你可以像对其他 Spring Bean 一样，对 AI 相关的 Bean 应用 AOP（如添加日志、事务）、进行精细化的配置（`@ConfigurationProperties`）等。\n\n#### 1.2.4 企业级特性\n\n除了开发便利性，Spring AI 还深刻理解企业级应用对**稳定性、可观测性和安全性**的诉求。\n\n-   **可观测性**：Spring AI 内置了对 Micrometer 的支持，能够自动暴露与 AI 调用相关的核心指标，如 Token 消耗、请求延迟、错误率等。你可以轻松地将这些指标对接到 Prometheus & Grafana 等监控系统中，实现对 AI 服务成本和性能的精细化度量。\n-   **生产环境部署**: Spring AI 从设计之初就考虑到了云原生和高性能场景，支持虚拟线程以提升 I/O 密集型 AI 调用的吞吐量，并兼容 GraalVM 原生镜像，实现应用的快速启动和低内存占用。\n\n### 1.3 Spring AI 在 AI 技术栈中的定位\n\n为了更清晰地理解 Spring AI 的角色，我们可以通过一段简述来描绘它在整个 AI 技术栈中的位置。\n\n\n\n1.  **向上支撑业务应用**：为上层业务逻辑提供一套稳定、统一、易用的 AI 能力调用接口。业务开发者无需关心底层 AI 模型的具体实现细节和 API 差异。\n2.  **向下连接 AI 生态**：它作为适配器，连接并管理着各种底层服务，包括：\n    *   **AI 模型服务**：如 OpenAI, Azure OpenAI, Google Vertex AI, Anthropic, Ollama 等。\n    *   **数据源与存储**：特别是向量数据库（Vector Stores），如 PGVector, Milvus, Redis, Chroma 等，它们是实现 RAG（检索增强生成）模式的关键。\n\n> **核心定位**：Spring AI **专注于应用集成与编排，而非模型训练**。它旨在帮助开发者“使用”好 AI 模型，将 AI 的通用能力与具体的业务场景相结合，创造出实际的应用价值。\n\n#### 1.3.1 与 LangChain4j 等框架的对比\n\n在 Java 的 AI 开发生态中，除了 Spring AI，也存在其他优秀的框架，如 LangChain4j。了解它们之间的异同，有助于我们做出更合适的选型。\n\n| 特性 | Spring AI | LangChain4j |\n| :--- | :--- | :--- |\n| **核心理念** | **深度融入 Spring 生态**，提供原生的 Spring Boot 开发体验。 | **作为通用的 Java AI 库**，可以独立使用，也可与其他框架（如 Quarkus, Micronaut）集成。 |\n| **配置方式** | 强依赖 Spring Boot 的自动配置 (`application.properties`/`yml`)。 | 提供灵活的编程式构建器 (Builder)，配置更自由。 |\n| **生态整合** | 与 Spring Data, Spring Batch, Spring Cloud 等生态组件有天然的、深度的整合潜力。 | 更加独立，与特定框架的整合需要开发者自行完成。 |\n| **目标用户** | **Spring/Spring Boot 开发者**，特别是企业级应用开发者。 | 更广泛的 Java 开发者，包括对 Spring 不熟悉的开发者。 |\n| **优势** | 开发体验对 Spring 用户极其平滑，企业级特性（如可观测性）集成度高。 | 灵活性高，不锁定于任何一个框架，学习曲线可能对非 Spring 用户更平缓。 |\n\n**结论**：两者都是非常优秀的框架。如果你的技术栈是基于 Spring Boot 的，或者你正在构建复杂的企业级 AI 应用，**Spring AI 几乎是你的不二之选**，因为它能为你提供无与伦比的生态整合能力和开发便利性。如果你需要一个更轻量、更独立的 Java AI 库，或者你的项目未使用 Spring，那么 LangChain4j 会是一个非常好的选择。\n\n### 1.4 本教程导览\n\n本教程将带领你从零开始，逐步深入 Spring AI 的世界。无论你是 AI 领域的新手，还是希望将 AI 能力引入现有 Java 项目的资深开发者，都能在这里找到清晰的学习路径。\n\n#### 1.4.1 前置知识要求\n\n为了更好地跟上本教程的节奏，我希望你具备以下基础：\n*   熟练掌握 **Java** 编程语言（JDK 17+）。\n*   具备 **Spring Boot** 的基础开发经验，了解依赖注入、Bean、配置文件等核心概念。\n*   了解 **Maven** 或 **Gradle** 的基本使用。\n\n你不需要有任何 AI 或机器学习的背景知识，教程中涉及到的所有 AI 概念，我都会用通俗易懂的方式进行解释。\n\n\n\n\n\n### **1.5 项目愿景与技术栈**\n\n我们的目标是构建一个名为 **“AI-Copilot”** 的企业级 AI 聊天应用。这是一个全栈项目，技术选型如下：\n\n| 层面 | 技术栈 | 说明 |\n| :--- | :--- | :--- |\n| **后端** | Spring Boot 3.3+, Spring AI 1.0+, Java 17+, Maven, MySQL | 健壮、可靠的企业级后端，负责所有 AI 逻辑和业务处理。 |\n| **前端** | Vue 3, Vite, Tailwind CSS 4, DaisyUI, `element-plus-x` | 现代、高效、美观的前端界面，专注于提供流畅的聊天体验。 |\n\n#### **1.5.1 后端先行：初始化 Spring AI 项目**\n\n我们首先搭建后端服务。请确保您的环境中已安装：\n\n  * **JDK**: 17 或 21 (推荐)\n  * **Maven**: 3.8+\n  * **IDE**: IntelliJ IDEA 或 VS Code (已安装 Java 和 Spring Boot 插件)\n\n**第一步：使用 Spring Initializr 创建项目**\n\n1.  访问官方项目生成器：[start.spring.io](https://start.spring.io/)\n2.  填写项目元数据，参照下表配置：\n\n| 配置项 | 值 | 说明 |\n| :--- | :--- | :--- |\n| **Project** | `Maven` | 构建工具 |\n| **Language** | `Java` | 开发语言 |\n| **Spring Boot**| `3.3.1` (或更高稳定版) | 核心框架版本 |\n| **Group** | `com.copilot` | 公司或组织域名反向 |\n| **Artifact** | `ai-copilot-backend` | 项目名 |\n| **Packaging** | `Jar` | 打包方式 |\n| **Java** | `17` | JDK 版本 |\n\n3.  在 **Dependencies** 部分，点击 "ADD DEPENDENCIES..." 添加以下依赖：\n\n      * `Spring Web`: 用于构建 RESTful API。\n      * `Spring AI OpenAI Support`: 提供与 OpenAI 及其兼容 API (如 DeepSeek) 的集成。\n      * `Spring Data JDBC`: 提供基础的数据库访问能力。\n      * `MySQL Driver`: 连接 MySQL 数据库。\n      \n\n> **`注意：`** Lombok不要在依赖里面添加，而是手动引入，这样才能避免很多编译问题\n\n3.  点击 "GENERATE" 下载项目压缩包，解压后用您的 IDE 打开。\n\n**第二步：辨析核心依赖并配置**\n\n打开 `pom.xml`，我们加上额外的Lombok配置：\n\n```xml\n        <dependency>\n            <groupId>org.projectlombok</groupId>\n            <artifactId>lombok</artifactId>\n            <scope>provided</scope>\n        </dependency>\n```\n\n**第三步：配置 `application.yml`**\n\n将 `src/main/resources/application.properties` 重命名为 `application.yml`，并填入以下基础配置。我们将使用 DeepSeek API 作为入门，因为它提供了免费额度且与 OpenAI API 兼容。\n\n> **安全提示**：请先前往 [DeepSeek 开放平台](https://platform.deepseek.com/api_keys) 注册并获取您的 API Key。切勿将密钥直接提交到代码仓库，最佳实践是使用环境变量。\n\n```yaml\n# src/main/resources/application.yml\n\nserver:\n  port: 8080\n\nspring:\n  # AI 配置\n  ai:\n    # 使用 DeepSeek (兼容 OpenAI 协议)\n    openai:\n      # 注意: 根据 DeepSeek 最新规范, 此处不加 /v1 后缀\n      base-url: https://api.deepseek.com\n      # 从环境变量中读取 API Key, 如果没有则使用下面的默认值 (仅供测试)\n      api-key: ${DEEPSEEK_API_KEY:sk-your-deepseek-api-key}\n      chat:\n        options:\n          # 指定默认使用的模型\n          model: deepseek-chat\n          # 控制输出的创造性，0.7 是一个较为平衡的值\n          temperature: 0.7\n  \n  # 数据库连接配置 (为后续章节做准备)\n  datasource:\n    url: ****************************************************************************************************    username: root\n    password: root\n    driver-class-name: com.mysql.cj.jdbc.Driver\n\n# MyBatis-Plus 配置 (为后续章节做准备)\nmybatis-plus:\n  configuration:\n    map-underscore-to-camel-case: true\n    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl\n```\n\n**第四步：运行后端**\n\n在 IDE 中找到主启动类 `AiCopilotBackendApplication.java` 并运行，或在项目根目录执行 `./mvnw spring-boot:run`。看到 Spring Boot 启动日志表示后端已成功运行。\n\n-----\n\n'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#1-%E5%BA%8F%E7%AB%A0%EF%BC%9A%E8%BF%8E%E6%8E%A5-Java-AI-%E5%BC%80%E5%8F%91%E6%96%B0%E7%BA%AA%E5%85%83"><span class="toc-number">1.</span> <span class="toc-text">1. 序章：迎接 Java AI 开发新纪元</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-1-%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81-Spring-AI%EF%BC%9F"><span class="toc-number">1.1.</span> <span class="toc-text">1.1 为何需要 Spring AI？</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-1-1-%E9%97%AE%E9%A2%98%E8%83%8C%E6%99%AF%EF%BC%9AJava-%E5%BC%80%E5%8F%91%E8%80%85%E7%9A%84%E6%8C%91%E6%88%98%E4%B8%8E%E6%9C%BA%E9%81%87"><span class="toc-number">1.1.1.</span> <span class="toc-text">1.1.1 问题背景：Java 开发者的挑战与机遇</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-1-2-Python-%E7%94%9F%E6%80%81%E7%9A%84%E5%90%AF%E7%A4%BA"><span class="toc-number">1.1.2.</span> <span class="toc-text">1.1.2 Python 生态的启示</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-1-3-Spring-AI-%E7%9A%84%E8%AF%9E%E7%94%9F"><span class="toc-number">1.1.3.</span> <span class="toc-text">1.1.3 Spring AI 的诞生</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-Spring-AI-%E7%9A%84%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1%E5%93%B2%E5%AD%A6"><span class="toc-number">1.2.</span> <span class="toc-text">1.2 Spring AI 的核心设计哲学</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-2-1-%E5%8F%AF%E7%A7%BB%E6%A4%8D%E6%80%A7"><span class="toc-number">1.2.1.</span> <span class="toc-text">1.2.1 可移植性</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-2-2-%E6%A8%A1%E5%9D%97%E5%8C%96"><span class="toc-number">1.2.2.</span> <span class="toc-text">1.2.2 模块化</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-2-3-Spring-%E5%8E%9F%E7%94%9F%E4%BD%93%E9%AA%8C"><span class="toc-number">1.2.3.</span> <span class="toc-text">1.2.3 Spring 原生体验</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#1-2-4-%E4%BC%81%E4%B8%9A%E7%BA%A7%E7%89%B9%E6%80%A7"><span class="toc-number">1.2.4.</span> <span class="toc-text">1.2.4 企业级特性</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-3-Spring-AI-%E5%9C%A8-AI-%E6%8A%80%E6%9C%AF%E6%A0%88%E4%B8%AD%E7%9A%84%E5%AE%9A%E4%BD%8D"><span class="toc-number">1.3.</span> <span class="toc-text">1.3 Spring AI 在 AI 技术栈中的定位</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-3-1-%E4%B8%8E-LangChain4j-%E7%AD%89%E6%A1%86%E6%9E%B6%E7%9A%84%E5%AF%B9%E6%AF%94"><span class="toc-number">1.3.1.</span> <span class="toc-text">1.3.1 与 LangChain4j 等框架的对比</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-4-%E6%9C%AC%E6%95%99%E7%A8%8B%E5%AF%BC%E8%A7%88"><span class="toc-number">1.4.</span> <span class="toc-text">1.4 本教程导览</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-4-1-%E5%89%8D%E7%BD%AE%E7%9F%A5%E8%AF%86%E8%A6%81%E6%B1%82"><span class="toc-number">1.4.1.</span> <span class="toc-text">1.4.1 前置知识要求</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-5-%E9%A1%B9%E7%9B%AE%E6%84%BF%E6%99%AF%E4%B8%8E%E6%8A%80%E6%9C%AF%E6%A0%88"><span class="toc-number">1.5.</span> <span class="toc-text">1.5 项目愿景与技术栈</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-5-1-%E5%90%8E%E7%AB%AF%E5%85%88%E8%A1%8C%EF%BC%9A%E5%88%9D%E5%A7%8B%E5%8C%96-Spring-AI-%E9%A1%B9%E7%9B%AE"><span class="toc-number">1.5.1.</span> <span class="toc-text">1.5.1 后端先行：初始化 Spring AI 项目</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>