---
title: 产品经理入门（五）：第五章：产品设计与原型制作
categories:
  - 产品经理
tags:
  - 产品经理教程
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp'
comments: true
toc: true
ai: true
abbrlink: 23264
date: 2025-07-20 20:13:45
---

# 第五章：产品设计与原型制作

在前面的章节里，我们投入了大量精力去“听”和“想”，我们学会了如何收集、分析、管理需求，这些都属于“**问题域**”的范畴——即，**我们应该解决什么问题**。

从这一章开始，我们将进入“**解决方案域**”——即，**我们应该如何设计产品，来优雅地解决这些问题**。我会带大家走完从梳理设计思路，到最终绘制出可交互原型的全过程。

## 5.1 产品设计思路

![image-20250720095709353](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720095709353.png)

在我正式开始画任何一个线框图（Wireframe）之前，我的脑海里必须有一套清晰、结构化的设计思路。这能确保我设计出的功能，是源于真实的用户场景，并且逻辑是通顺的。

我的这套思路，可以用一个公式来表达：**在明确的【角色】和【场景】下，为了达成用户的【目的】，他需要走通一个怎样的【流程】，而我们需要提供什么样的【功能】来支撑这个流程。**

下面，我们就用一个完整的案例，来贯穿这套设计思路。

### 5.1.2 案例：直播间需求分析

我们机构的某个毕业学员 P小M 入职了一家做在线英语培训的公司，公司有很多外籍教师，学员大都为中国学生。目前公司的产品经理在每周对需求池当中的原始需求进行整理分析时，发现之前有个叫Zoe的外籍老师上周提出了如下需求：
**“希望可以在上课时在网页版的直播间里可以打字，进行答疑，并且在直播课里最好提供举手、邀请某人语音的功能”**
当在公司当中遇到了这样的一个需求，你会怎样去考虑呢？

### 5.1.3 产品设计流程

我会严格按照“角色 → 场景 → 目的 → 流程 → 功能”这五个步骤，来一步步地推导出我的产品方案。

#### 1. 角色 (Role)

首先，我需要明确，这个场景下，我的核心用户是谁？
* **Zoe**：外籍老师，她的一个关键特征是**不认识中文**。
* **直播间学员**：中国学员，他们的特征是需要在直播中与老师互动。

#### 2. 场景 (Scene)

![image-20250720095921040](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720095921040.png)

这两个角色，在“直播间上课”这个大场景下，会发生哪些具体的交互子场景？
* **子场景1：老师答疑**。老师在讲课过程中，学员随时会产生疑问，需要老师解答。
* **子场景2：点学员回答问题**。老师为了增强互动，需要主动挑选一位学员来回答问题。
* **其他场景**：比如老师需要在直播间内布置作业等。

#### 3. 目的 (Goal)

![image-20250720100201566](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100201566.png)

在这些具体的子场景下，各个角色的核心目的是什么？
* **老师答疑场景的目的**：方便老师能**及时地**对学员提出的问题进行答疑。
* **点学员回答问题场景的目的**：方便**不认识中文昵称**的外籍老师，能**方便地**挑选学生回答问题。

#### 4. 流程 (Flow)

![image-20250720100036943](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100036943.png)

为了达成上述目的，一个理想化的操作流程是怎样的？
* **老师答疑的流程**：老师讲课 → 学员产生疑问 → 学员通过某个方式提出问题 → 老师看到问题并解答。
* **点学员回答问题的流程**：老师想提问 → 老师通过某个方式主动挑选学生 → 被选中的学生通过某个方式回答问题。

#### 5. 功能 (Function)

![image-20250720100113753](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100113753.png)

最后，也是最关键的一步：为了支撑上述流程的顺畅运转，我们需要提供哪些核心功能？
* **支撑“老师答疑”流程**：最直接的功能就是**提供一个聊天区**，让学生和老师都可以用文字进行实时的提问和回答。
* **支撑“点学员回答问题”流程**：
    1.  针对“老师方便挑选”：我们可以**提供一个“举手”功能**，想回答问题的学生可以“举手”，老师就能从举手的学生里选。
    2.  针对“学生方便回答”：我们可以**提供一个“拉上麦”功能**，老师可以直接点击举手学生的头像，邀请他上麦进行语音回答。

到此为止，我们就通过一套严谨的思路，把一个模糊的需求，推导出了三个具体、可执行的功能点：**聊天区、举手、拉上麦**。

---

### 5.1.4 功能清单

![image-20250720100327337](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100327337.png)

当我通过上述流程推导出多个功能点后，我会把它们整理成一份**“功能清单（Function List）”**。这份清单详细地列出了为了满足本次产品目标，我们需要开发的所有功能模块和子功能。它是我们后续进行原型设计和与开发团队沟通的基础。

### 5.1.5 功能清单与需求池的区别

我需要强调一下“功能清单”和我们之前提过的“需求池”的区别。

| **对比维度** | **需求池 (Requirement Pool)** | **功能清单 (Function List)** |
| :--- | :--- | :--- |
| **内容** | **未经处理的“原始需求”集合**。包含了各种想法、问题、建议，是发散的。 | **经过分析和设计后，得出的“产品解决方案”**。是明确、收敛、可执行的功能项。 |
| **阶段** | 处于**“问题域”**，是我们分析的起点。 | 处于**“解决方案域”**，是我们设计和开发的起点。 |

简单来说，**需求池是“原材料仓库”，而功能清单是“加工图纸”**。

### 5.1.6 产品设计思路工具运用方式

我们前面学过的很多工具，都会在这个阶段被综合运用。

![image-20250720100442872](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100442872.png)

* 我会用**用户访谈**来明确角色、场景和目的。
* 我会用**流程图**来梳理和表达流程。
* 我会用**结构图**（特别是功能结构图）来整理我的功能清单。




---

## 5.2 原型的概念及分类

对我来说，原型是连接“需求文档”与“最终产品”之间最重要的一座桥梁。它是产品想法的第一次可视化、具象化的表达。

### 5.2.1 学习目标

在本节中，我的目标是带大家清晰地理解原型的不同“保真度”（Fidelity）的概念。我们将学习区分草图、低保真原型和高保真原型的差异，以及我会在什么样的情况下，选择使用哪一种原型。

### 5.2.2 原型的概念及分类

![image-20250720100747254](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100747254.png)

我给原型的定义是：**用线条、图形绘制出的产品框架，也称线框图，是需求和功能的具体化表象。**

在我的工作中，我从不会把原型看作是“一个东西”，而是根据项目的不同阶段和沟通目的，把它分为三种不同的类型。

#### 1. 草图原型 (Sketch)

![image-20250720100813975](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100813975.png)

这是我进行产品设计的第一步，也是最快速、最低成本的一种方式。

* **特点**：顾名思义，它就是用笔和纸（或者在白板上）随手画出的草稿。我画草图时，**核心是梳理逻辑框架和页面流程**，完全不讲究排版、对齐和美观，也不需要表达出所有的页面元素。
* **我的适用场景**：我通常在个人进行**方案构思**的阶段，或者在**团队内部进行头脑风暴**时，大量使用草图。它的使命就是快速表达、快速讨论、快速迭代，画完就可以随时扔掉，没有任何心理负担。

#### 2. 低保真原型 (Low-fidelity Prototype)

![image-20250720100947720](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100947720.png)

当我的思路通过草图基本确定后，我就会使用Axure、墨刀等专业工具，来绘制正式的**低保真原型**。这在我的日常工作中，是产出最多、也最重要的一类原型。

* **特点**：它要求**绘图整齐、布局规范**。我通常只使用黑、白、灰三种颜色，用简单的线框和色块来表示图片、文字和各类组件。虽然它看起来很朴素，但它必须**完整、准确地表达出产品方案**，页面上所有的功能、按钮、文案、跳转关系都必须清晰无误。
* **我的适用场景**：低保真原型是我用来进行**正式方案交付**的“文档”。我会用它来召开**需求评审会**，并把它作为最终交付给开发和测试工程师的**研发依据**。它剥离了所有视觉干扰，让大家都能聚焦在功能和流程本身。

#### 3. 高保真原型 (High-fidelity Prototype)

![image-20250720101038793](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720101038793.png)

这是保真度最高的原型，它在视觉上已经和最终的线上产品非常接近了。

* **特点**：它不仅**绘图规范、排版求真**，还包含了丰富的视觉元素，如配色、图标、字体、图片等。更重要的是，它通常是**可以交互的**，用户可以像使用真实App一样在上面点击、跳转，来模拟真实的使用体验。
* **我的适用场景**：因为制作成本很高，我只在特定的场景下才会制作高保真原型。比如，需要向老板或投资人进行**路演宣传**时；或者，在产品上线前，需要进行**用户体验测试（Usability Testing）**时；以及，有些公司的管理流程，要求在开发前必须有高保真原型用于最终决策。



---

## 5.3 原型绘制工具

### 5.3.1 学习目标

我的目标是带大家熟悉一款现代化的原型工具的核心使用逻辑。我们将了解原型工具的界面通常是如何分布的，并掌握那些最常用的基础元件（也就是我们画原型时的“砖块”）应该在什么场景下使用。

### 5.3.2 原型绘制工具介绍及作用

在上一节，我们明确了原型有草图、低保真、高保真之分。要绘制出规范的低保真和高保真原型，我们就必须借助专业的工具。这些工具能帮助我们高效地搭建页面结构、添加交互，并方便地进行分享和评审。

### 5.3.3 常用原型绘制工具

正如我们之前讨论并达成共识的，在众多工具中，我个人非常推荐像 **墨刀 (MockingBot)** 这样集设计、原型、协作为一体的在线平台。它功能强大、上手简单，非常适合我们当前的学习和未来的团队协作。

**接下来的内容，我会以通用原型工具的核心逻辑进行讲解，其中的概念和操作，您都可以在我们选定的“墨刀”中找到并熟练应用。**

### 5.3.4 原型工具的核心工作区

无论我们使用哪款工具，其主界面通常都由几个核心的“工作区”构成。我将这些区域的功能总结如下，这能帮助我们快速熟悉任何一款新工具的布局。

**菜单与工具栏 (Menu & Toolbar)** 通常在界面的最上方。这里集成了软件的通用功能，比如文件操作（新建、保存、导出）、常用工具（选择、放大、缩小）等。

**页面管理区 (Page Management Area)** 通常在左侧。这是我们整个项目的“目录树”，我在这里管理原型的所有页面，可以进行新增、删除、重命名和调整层级。

**元件库 (Widget/Component Library)** 这是我们的“工具箱”和“素材库”，通常也在左侧。里面包含了我们绘制原型需要的所有“砖块”，如按钮、文本框、图片等，我只需要把它们拖拽到画布上即可使用。

**画布 (Canvas)** 这是界面中心最大的一块区域，是我们的“画板”。我们所有的设计工作都在这里完成。

**检视区 (Inspector Panel)** 通常在右侧。这是我用来精细调整元件的“属性面板”。当我选中画布上的任何一个元件时，都可以在这里修改它的尺寸、颜色、字体、边框，以及为它添加交互效果。

![image-20250720101923527](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720101923527.png)

**概要/图层区 (Outline/Layers Area)** 这个区域会以列表的形式，显示出当前画布上所有的元件及其层级关系。当页面变得复杂、元件相互重叠时，我通过这里可以非常方便地选中和管理它们。

**母版/组件区 (Masters/Components Area)** 这是一个进阶但非常有用的功能。对于那些需要在多个页面重复使用的元素（比如导航栏、页脚），我会把它们创建为“母版”或“公共组件”。这样，我只需要修改一次母版，所有引用了它的页面都会同步更新，极大地提升了效率。

![image-20250720102108869](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102108869.png)



### 5.3.5 常见元件的使用场景

![image-20250720102252484](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102252484.png)

掌握了工作区布局后，我们就要来认识一下“元件库”里那些最常用的“砖块”了。熟悉它们各自的用途，是画好原型的基础。

| 元件 (Widget) | 我的使用场景说明 |
| :--- | :--- |
| **矩形/图片/占位符** | 这是我用来搭建页面基本骨架的“积木”。我用它们来快速划分页面区域、表示图片或Banner等内容占位。 |
| **按钮 (各类)** | 用于触发核心操作，是用户与系统交互最直接、最重要的途径。比如“登录”、“提交”、“购买”等。 |
| **标题/文本** | 用于构建页面的信息层级，清晰地传达各类文字内容，是页面的“血肉”。 |
| **文本框/文本域** | 当需要用户**输入**单行或多行文字时使用。比如：用户名输入框、搜索框、评论输入区。 |
| **下拉列表/单选/复选** | 当需要用户从一组**固定的选项**中进行选择时使用。单选只能选一项，复选可以选多项。 |
| **表格/列表** | 用于结构化地、清晰地**展示大量数据**或信息。比如后台管理系统的数据列表。 |
| **`热区`** | 这是一个“隐形”的矩形。当我想让一张图片或一组元素实现整体点击跳转时，我就会在上面覆盖一个热区来添加交互，它本身在预览时是看不见的。 |








---

## 5.4 原型设计规范

在我看来，画原型绝不仅仅是把各种元件拖到画布上就完事了。为了让我的原型图清晰、专业、具备可交付性，我必须遵循一套严格的**设计规范**。

这套规范，不是为了限制我们的创意，恰恰相反，它是为了**提升我们整个团队的沟通效率**。一个遵循规范的原型，就像一篇字迹工整、标点清晰的文章，能让读它的人（设计师、开发、测试）一目了然。

### 5.4.1 学习目标

在本节中，我的目标是带大家掌握我绘制原型时所遵循的几项基本规范。我们将学习Web端和移动端的标准尺寸、常见的页面结构，以及能让你的原型图专业度瞬间提升的五大注意事项。

### 5.4.2 尺寸规范

在我开始绘制任何页面之前，我首先要确定的，就是我的“画板”尺寸。

#### 1. Web端尺寸规范

![image-20250720102904090](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102904090.png)

对于Web端的网页原型，现在主流的显示器分辨率是1920*1080。因此，我的画布宽度通常会设置为1920px或更高。

但更重要的一个概念是“**版心**”。版心指的是网页上承载核心内容的有效显示区域。为了保证在不同尺寸的宽屏显示器上，内容都清晰易读、不会过分拉伸，我通常会将**版心的宽度控制在1000px到1200px之间**，并让它在页面上水平居中。

#### 2. 移动端尺寸规范

![image-20250720103150325](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103150325.png)

对于移动端的App原型，为了保持所有页面的一致性，我会选择一个基准尺寸来作图。目前，我以及行业内最通用的低保真原型尺寸，是基于iPhone 6/7/8的逻辑分辨率：**375 x 667 px**。

在这个基准尺寸内，我还对几个系统级的区域高度，严格遵守规范：

* **状态栏 (Status Bar)**：就是手机最顶部显示信号、时间、电量的那一条。它的标准高度是 **20px**。
* **导航栏 (Navigation Bar)**：是页面顶部的、包含页面标题和返回按钮的区域。它的标准高度是 **44px**。
* **标签栏 (Tab Bar)**：是App底部的主菜单导航。它的标准高度是 **49px**。

从一开始就遵循这些尺寸规范，能让我的原型图显得非常专业，也便于后续UI设计师进行视觉稿的还原。

### 5.4.3 结构规范



尺寸确定后，我会思考页面的整体布局结构。

* **Web端**：最常见的两种结构是**左右布局**（左侧为导航，右侧为内容区，常见于后台管理系统）和**居中布局**（导航和内容区都在页面中心，常见于官网、博客等）。

![image-20250720103106633](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103106633.png)

* **移动端**：一个典型的App页面，其结构通常由上至下由“**状态栏 + 导航栏 + 内容区 + 标签栏**”这几个固定的区块构成。熟悉这些通用结构，能帮我快速、规范地搭建页面。

![image-20250720103247946](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103247946.png)

### 5.4.4 原型设计规范注意事项

![image-20250720103507688](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103507688.png)

最后，也是最重要的，我总结了我个人在绘制原型时，一定会遵守的“五大黄金法则”。做好这五点，你的原型图就能立刻和“业余”拉开差距。

1.  **页面结构**：在原型工具中，我会用文件夹和清晰的命名，来组织我的页面层级，让整个项目的结构一目了然。
2.  **框架比例**：我会先定好页面的基础布局和比例，并在所有页面中保持一致，这能带来稳定、舒适的视觉感受。
3.  **间距一致**：这是专业性的关键体现。我会确保元素与元素之间的“间距”是有规律且统一的。比如，卡片与卡片的间距是16px，那在所有地方都应该是16px。
4.  **位置对齐**：我要求自己做到“像素眼”，借助工具的对齐功能，确保页面上所有的元素，要么左对齐，要么居中对齐，要么右对齐。绝不允许出现肉眼可见的错位。
5.  **元件大小**：相同类型的元件，尺寸必须保持一致。比如，所有主要按钮的高度都是44px，所有正文的字号都是14px。这能让界面看起来更和谐、更具秩序感。

### 5.4.5 原型设计规范小结

我将原型设计的核心规范，总结为下面这张自检表：

| **规范维度** | **我的核心原则** |
| :--- | :--- |
| **尺寸 (Size)** | Web端关注**1200px版心**，移动端以**375x667**为基准。 |
| **结构 (Structure)** | 采用**通用布局**（如Web居中布局，App上下导航结构）。 |
| **注意事项** | **对齐、间距、大小、比例、结构**，五大要素在整个原型中，必须保持高度**一致性**。 |






---

## 5.5 墨刀制作基础交互

一个只会展示、不能点击的原型，就像一张没有灵魂的皮囊。而**交互**，就是我们为这具皮囊注入灵魂的过程。它能把一张张孤立的页面，串联成一个完整、可体验的产品故事。

在这一节，我将带大家学习交互设计的基本逻辑，并掌握如何使用我们选定的工具——**墨刀 (MockingBot)**，来制作几种最常见、最核心的交互效果。

### 5.5.1 学习目标

我的目标是，让我们掌握交互设计的核心公式，并能熟练运用墨刀，独立制作出页面跳转、弹窗、悬浮提示和轮播图这四种基础但至关重要的交互效果。

### 5.5.2 什么是交互

我理解的“交互”，就是**用户与产品之间的一场对话**。用户通过点击、滑动、输入等行为“说话”，而产品则通过页面变化、动画、提示等方式来“回应”。

![image-20250720110600853](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110600853.png)

### 5.5.3 什么是交互设计

![image-20250720110625509](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110625509.png)

那么，“交互设计”，就是我们作为产品经理，去**预设这场对话的规则和剧本**。

在墨刀这样的原型工具里，这个剧本的创作遵循着一个万能公式，这也是交互设计的核心：
**交互 = 事件 (Event) + 动作 (Action)**

* **事件**：就是“**当用户做什么的时候**”。这是触发器。比如：`当用户单击时`、`当鼠标移入时`、`当页面加载时`。
* **动作**：就是“**产品应该发生什么变化**”。这是响应。比如：`链接到某个页面`、`显示/隐藏某个元素`、`改变某个元件的状态`。

我们所有的交互设计，都是围绕着“在什么事件下，执行什么动作”来展开的。

### 5.5.4 常见交互设计

![image-20250720110650638](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110650638.png)

掌握了“事件+动作”这个核心公式，我们就可以组合出千变万化的交互。以下是我在工作中最高频使用的四种。

1.  **跳转**：这是最基础的交互，它将页面串联起来。在墨刀里，我选中一个按钮，为它添加一个“**单击**”的**事件**，再选择“**链接到页面**”这个**动作**，并指定目标页面即可。
2.  **显示/隐藏**：常用于制作弹窗和下拉菜单。我先将要弹出的内容（比如一个弹窗）设置为默认隐藏。然后给一个触发按钮添加“**单击**”**事件**，并选择“**显示/隐藏**”**动作**，作用于那个隐藏的弹窗。
3.  **悬浮显示**：常用于制作提示信息（Tooltip）。我会给目标元件添加“**鼠标移入**”**事件**，触发“**显示**”某个提示框的**动作**；同时再添加一个“**鼠标移出**”**事件**，触发“**隐藏**”这个提示框的**动作**。
4.  **动态面板/轮播**：用于制作轮播图等效果。在墨刀里，这个交互被简化了。我可以直接使用它自带的“**轮播**”组件，把几张图片放进去，它就能自动实现切换效果。其背后的逻辑，就是通过“**延时**”这个**事件**，来触发“**切换到下一状态**”的**动作**。

### 5.5.5 墨刀制作基础交互案例展示

我们来看这四种交互在真实场景下的应用，我已经预设做好了两个模板，分别为微信的原型图以及对应的聊天详情，如下：

![image-20250720123723714](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720123723714.png)

1.  **跳转页面案例**
    就像微信的聊天列表，当我要实现点击某个好友，就进入和他聊天的页面时，我就会为列表里的每一项，都添加一个“单击”事件，并分别链接到对应的聊天页面。

![image-20250720123838731](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720123838731.png)

**实现效果如下：**

![](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/PixPin_2025-07-20_12-39-15.webp)







2.  **弹框提示案例**
  
    ![image-20250720125649096](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720125649096.png)
  
    当用户点击某个按钮时，为了响应他们，我需要弹出一个确认框。这个确认框，我会在墨刀里提前画好并设置为隐藏。然后给对应按钮添加“单击”事件，动作为“显示”这个确认框。
    
    ![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/PixPin_2025-07-20_12-57-24.webp)



1.  **悬浮显示案例**
    ![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/e0990e1c-08c9-42c9-81a6-443e3b2ef0cc.png)
    当鼠标移到一个被缩略的标题上，我希望显示完整的标题。我就会做一个隐藏的、包含完整标题的文本框，然后通过“鼠标移入/移出”事件，来控制它的显示和隐藏。

---