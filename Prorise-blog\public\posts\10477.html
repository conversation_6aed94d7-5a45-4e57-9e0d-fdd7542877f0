<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理入门（一）：第一章：内容产品模型 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理入门（一）：第一章：内容产品模型"><meta name="application-name" content="产品经理入门（一）：第一章：内容产品模型"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理入门（一）：第一章：内容产品模型"><meta property="og:url" content="https://prorise666.site/posts/10477.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第一章：内容产品模型1.1 内容产品概述1.1.1 学习目标 1.1.2 什么是内容产品？内容产品，就是以图文、视频&amp;#x2F;直播、音频等形式为用户提供内容服务的产品形态。 这句话揭示了内容产品的两个核心要素：  核心载体: 内容。这可以是引人深思的一段文字、一张精美的图片、一段动听的音频，或是一"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta name="description" content="第一章：内容产品模型1.1 内容产品概述1.1.1 学习目标 1.1.2 什么是内容产品？内容产品，就是以图文、视频&amp;#x2F;直播、音频等形式为用户提供内容服务的产品形态。 这句话揭示了内容产品的两个核心要素：  核心载体: 内容。这可以是引人深思的一段文字、一张精美的图片、一段动听的音频，或是一"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/10477.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理入门（一）：第一章：内容产品模型",postAI:"true",pageFillDescription:"第一章：内容产品模型, 1.1 内容产品概述, 1.1.1 学习目标, 1.1.2 什么是内容产品？, 1.1.3 内容产品架构与生态, 1.2 内容产品设计模型, 1.2.1 学习目标, 1.2.2 内容生产, 1. PGC（Professionally-generated Content）, 2. UGC（User Generated Content）, 1.2.3 内容审核, 1. 文本内容审核, 2. 图片x2F音视频内容审核, 1.2.4 内容分发, 1.内容分发的本质, 2. 常见的内容分发方式, 一、 编辑分发, 二、 订阅分发, 三、 社交分发, 四、 算法分发, 1.2.5 内容消费, 一、 免费消费模式, 二、 付费消费模式第一章内容产品模型内容产品概述学习目标什么是内容产品内容产品就是以图文视频直播音频等形式为用户提供内容服务的产品形态这句话揭示了内容产品的两个核心要素核心载体内容这可以是引人深思的一段文字一张精美的图片一段动听的音频或是一段引人入胜的视频最终目的服务通过这些内容满足用户在信息获取学习提升娱乐消遣情感共鸣社交连接等方面的需求因此我们可以这样理解内容产品本质上是一个围绕内容进行价值交换的系统在这个系统中用户付出他们的时间注意力和金钱来换取平台提供的信息价值娱乐价值和情感价值图文类视频类音频类综合社区类微信公众号抖音喜马拉雅小红书知乎微博今日头条各类播客豆瓣内容产品架构与生态首先用户端是我们最熟悉的部分它直接服务于图下方的用户这是我们产品的脸面和橱窗我们所有的努力比如设计更沉浸的播放体验更精准的信息流推荐都是为了让用户在这里能高效愉悦地消费内容并且愿意留下来其次自媒体端是服务于图上方内容生产者的创作工坊根据我的经验一个产品的内容生态能否繁荣很大程度上取决于自媒体端的体验我们是否提供了足够便捷的编辑器数据分析是否足够清晰能帮助创作者优化内容这些决定了创作者是否愿意持续为我们提供内容弹药最后是隐藏在幕后的中央系统平台端它对应着图中平台这个中央枢纽的后台能力这部分是产品运营和管理人员使用的我们通过它来审核内容配置推荐策略管理用户对接广告主它保证了整个生态的秩序和商业目标的实现架构组成核心服务对象对应图中角色核心目标关键功能举例用户端用户满足内容消费和互动需求提升留存与时长信息流搜索播放阅读页点赞评论分享个人主页自媒端内容生产者满足内容生产和管理需求提升创作效率与意愿文章视频编辑器内容发布数据看板粉丝管理收益中心平台端平台运营管理者管理整个平台的用户与内容确保生态健康与商业运转内容审核系统用户管理推荐算法配置广告系统数据监控后台现在再回过头提供的那张生态图一切就都清晰了我们作为产品经理设计的内部架构表格内容完美地服务和驱动了外部的商业生态我们设计的每一个功能都应该能清晰地回答它属于哪个端口它在为生态中的哪个角色解决什么问题内容产品设计模型学习目标内容生产在内容产品的世界里我们首先遇到的就是我喜欢称之为专业生产内容顾名思义它的核心在于专业二字是一种相对传统的内容生产方式它的创作者通常是平台官方的工作人员如编辑记者与平台签约的领域专家或是专业的机构看图中的新闻截图无论是报道国家大事还是分析财经动态都体现出高度的专业性规范性和权威性这就是最典型的作为产品经理我在规划内容生态时非常看重的价值它最大的优势在于质量和规范可控因为生产者是专业的我们可以确保内容的准确性深度和品牌调性相符这在产品冷启动阶段尤其重要高质量的内容能够为平台快速树立起专业可信的品牌形象并为整个内容生态定下基调然而的劣势也同样致命那就是数量不足难以满足消费需求专业内容的生产成本高周期长一个编辑团队无论多么高效其产出量终究是有限的这就像一家米其林餐厅虽然菜品精致但无法满足全城人一日三餐的需求当平台用户规模扩大后仅靠是远远无法满足用户五花八门海量的内容消费需求的为了让你能快速把握的要点我把它总结成了下面这张表维度描述生产者平台官方签约专家合作机构媒体等专业团体或个人内容特点垂直深度制作精良标准化权威性高核心优势质量与规范可控是平台打造品牌建立信任的基石核心劣势生产成本高内容数量和多样性有限难以规模化适用场景平台冷启动核心栏目深度专题官方活动付费课程等总而言之我认为是内容平台不可或缺的定海神针它负责为平台打造高品质的门面和样板间但要让平台真正繁荣起来光靠这支正规军是远远不够的我们必须引入另一股更庞大更多元的力量这就是我们接下来要讲的如果说是内容生态里的正规军那么就是汪洋大海般的人民战争它的出现彻底改变了内容生产的游戏规则是一种全新的内容生产方式它的核心理念是内容产品的使用者也是产品内容的生产者这意味着平台上每一个普通的匿名的用户都可以成为内容的创作者你我他只要愿意都可以发布一条动态写一篇点评录一段视频发一条弹幕从我作为产品经理的视角来看最大的优势在于它用一种极低成本的方式解决了无法解决的两个核心问题内容量大和满足多样消费需求成千上万的用户自发地产出内容其数量和覆盖面的广度是任何专业团队都无法比拟的从美食探店到萌宠日常从游戏攻略到情感树洞能够满足用户几乎所有长尾细分的需求这是构建一个繁荣活跃社区的绝对基础然而这种自由创作的模式也带来了它最致命的劣势那就是内容质量不均需要审核机制当任何人都可以发布内容时内容的质量就变得不可控低质灌水甚至违规的内容会大量涌现因此对于一个以为主的平台建立强大高效的审核机制包括机器审核和人工审核就不是一个可选项而是一个生死攸关的必选项这是我们作为产品经理必须承担的责任同样我为你准备了一张表格来清晰地对比和的差异维度描述生产者平台的任何普通用户通常是匿名的非专业的内容特点数量巨大形式多样生活化互动性强质量参差不齐核心优势生产成本极低内容量和多样性极大能满足用户的长尾需求核心劣势内容质量不可控平台需要投入巨大的审核和运营成本来维护社区环境适用场景社交媒体微博社区小红书知乎点评大众点评短视频抖音快手等我的思考与总结在实际的产品工作中我们极少会遇到一个平台是纯或纯的更常见的情况是和是相辅相成的一个健康的策略往往是用来定义平台的调性树立质量标杆正规军打样板再用来丰富平台的内容生态提升社区的活跃度人民战争促繁荣如何设计一套好的机制让这两股力量和谐共存互相促进是我们产品经理需要不断探索的艺术内容审核文本内容审核我们刚刚聊完带来的海量内容那紧接着一个必须面对的问题就是如何管理这些内容确保社区的干净这就是内容审核的范畴它是维持平台健康生态的生命线我们先从最基础也是应用最广泛的文本内容审核开始在我的经验里处理文本内容最直接的方式就是自动审核特别是基于敏感词的过滤系统这里的基本逻辑非常简单就像这张图里展示的到的过程第一步图中的是设置敏感词我会和我的运营法务同事一起在平台的后台系统里共同维护一个敏感词词库这个词库是动态更新的我们会持续地把涉及色情暴力赌博政治广告人身攻击等各类违规的词语加进去这相当于我们为平台的安保系统配置好了要抓捕的黑名单第二步图中的是识别处理当用户发布任何文本内容时比如一篇帖子一条评论我们的系统会自动将这段文本与后台的敏感词库进行秒级匹配一旦命中词库里的某个词系统就会立刻执行我们预设好的处理动作这个处理动作具体是什么是我们作为产品经理需要精心设计的简单的用星号替换只是其中一种根据违规词语的严重等级我通常会设计一套组合策略我把这些常见的处理方式整理成了一个表格方便我们理解处理方式描述我通常应用的场景替换将命中的敏感词替换为等符号内容本身依然可以发出对用户体验影响最小适用于一般性的不文明用语脏话等拦截直接阻止该内容的发布并明确告知用户违规原因适用于垃圾广告恶意导流联系方式等明确的违规行为仅作者可见内容成功发布但只有作者自己能看到同时自动进入人工审核队列适用于内容疑似违规但不确定需要人工介入判断的情况给用户一种已发出的错觉可以减少其申诉和修改绕过的行为先审后发内容发布后不会立即对外展示必须等待人工审核通过后才可见适用于高风险场景如金融医疗内容的发布或针对有过多次违规记录的用户我的总结我要强调一点单纯的关键词审核只是万里长征的第一步现在的用户非常聪明会用谐音拆字加符号等各种方式来绕过审核比如信威信等等因此在如今的平台上敏感词系统通常只是作为基础的第一道防线更成熟的平台会在此之上结合基于机器学习的文本分类模型用户行为分析等多种技术手段来构建一个立体智能的审核系统图片音视频内容审核如果说文本审核是普通难度那么图片音频和视频的审核就是困难模式这类非结构化内容的审核其技术门槛和复杂度要高出几个量级在我的职业生涯中对于这类审核我的原则非常明确除非公司是专做视觉技术的否则我们坚决不自研而是选择购买服务为什么因为从零到一自研一套图片音视频识别模型需要顶尖的算法团队海量的标注数据和强大的计算资源这对于绝大多数公司来说投入产出比极低这张截图里的阿里云图片涉政暴恐识别服务就完美印证了我的观点目前阿里百度腾讯等一线云服务大厂都已经把这项能力做成了非常成熟的商业化服务我们作为产品开发者只需要付费调用他们的接口就能获得世界一流的审核能力这个过程对我们来说就像是把专业的事交给了最专业的人我来为你拆解一下我们作为产品经理在这里需要做的事情技术选型我们会对几家主流服务商如阿里云腾讯云等进行评估对比他们的识别准确率支持的审核维度调用价格服务稳定性等选择最适合我们业务的合作伙伴策略制定这是我们的核心工作调用后第三方服务会返回给我们一个包含各类标签和置信度分数的结果比如一张图片可能会返回色情暴恐广告这样的数据我们需要根据业务的风险容忍度来制定处理规则例如色情分数直接拦截广告分数在之间转人工审核所有分数都自动通过这个阈值的设定需要我们不断地根据实际运营情况去调整和优化成本控制要牢记需要付费这类服务是按调用次数如张数或时长如分钟收费的如果我们的产品有海量的图片视频上传这笔审核开销会非常巨大因此我必须在产品设计之初就将这部分成本纳入运营预算并持续监控其开销为了让你更清晰地掌握要点我整理了下面的表格维度描述核心逻辑不自研底层技术通过成熟的第三方云服务来解决专业问题主要服务商阿里云腾讯云百度智能云七牛云等常见审核维度涉黄涉政暴恐广告灌水违禁品公众人物识别等的核心工作技术选型策略制定设置阈值成本控制内容分发在我看来如果我们把内容生产比作做饭内容审核比作品控那么内容分发就是上菜菜做得再好品控再严如果不能精准高效地送到想吃这道菜的食客面前那一切都是徒劳内容分发的本质所以内容分发的本质我总结为一句话实现内容与用户之间最高效最合适的匹配我们作为产品经理设计的一切分发机制无论是信息流推荐位还是热榜都是为了这个终极目标服务常见的内容分发方式在产品的不同发展阶段和不同场景下我会运用不同的分发策略经过多年的演化我主要将它们归为以下四种主流方式一编辑分发这是最古老最经典的分发方式我称之为总编辑模式它的核心是由平台内部的编辑团队基于其专业判断和平台调性人工挑选出他们认为的好内容并决定这些内容展示在哪个位置展示给多少人我们早期看到的各大门户网站首页就是最典型的编辑分发我的解读优点在于质量可控和价值引导在产品初期我可以通过编辑精选快速为产品树立起高质量有调性的品牌形象告诉用户我们这里有什么缺点是中心化的用户的选择是被动的并且它极度依赖编辑的个人能力成本高效率低无法满足海量用户的个性化需求二订阅分发如果说编辑分发是平台喂给你什么你就看什么那么订阅分发就是把选择权交还给了用户我称之为报刊亭模式在这种模式下用户可以主动关注或订阅自己感兴趣的创作者专栏或话题这样用户就构建了属于自己的信息获取渠道我们关注微信公众号站主都属于订阅分发但这种需要平台需要有基础也就是发展初期是不太适合做订阅分发难以招揽到大牌博主入驻我的解读优点用户是主动选择因此粘性非常高容易围绕构建起私域流量用户忠诚度强缺点对用户的发现能力要求高用户需要自己去找到值得订阅的目标对于新用户和新创作者来说冷启动会比较困难三社交分发社交分发是一种极具爆发力的分发方式我称之为内容不再仅仅依赖平台或创作者而是通过用户自身的社交关系链进行传播我的朋友在朋友圈分享了一篇文章我认为有价值再次转发这样一传十十传百地扩散出去我的解读优点基于信任代理朋友推荐的内容我更愿意相信和打开传播速度快容易产生裂变效应引爆话题缺点内容分发的广度和速度变得不可控并且非常依赖内容的社交属性即是否足够有趣有槽点有共鸣能激发用户的分享欲四算法分发这是当下最主流也是技术含量最高的方式我称之为私人助理模式平台通过强大的算法模型分析用户的历史行为点击点赞停留时长等理解其兴趣偏好然后从海量内容池中为每一个用户量身定制一个独一无二的信息流今日头条和抖音是算法分发的集大成者我的解读优点极致的个性化和高效率它能让用户沉迷其中因为每一条都是你可能感兴趣的极大地提升了用户粘性和使用时长缺点容易形成信息茧房让用户视野越来越窄同时它对平台的内容总量和用户规模有很高的要求小平台很难玩转为了方便我们整体回顾我将这四种方式的核心特点总结在了一张表里分发方式核心逻辑用户角色优点缺点编辑分发编辑人工精选被动接收者质量调性可控效率低非个性化订阅分发用户主动关注主动选择者粘性高关系强发现效率低启动难社交分发好友分享推荐传播节点信任度高裂变快不可控依赖内容社交性算法分发机器智能推荐沉浸体验者高效率个性化易产生信息茧房在我的实际工作中现代内容产品几乎不会只采用单一的分发模式而是将这四种方式进行混合比如一个信息流里既有我订阅的也有算法推荐的还可能夹杂着编辑精选的热点如何调配好这四种模式的比例和权重正是我们产品经理需要不断探索和优化的核心策略内容消费在我看来内容产品的商业模式本质上都是在内容消费这个环节做文章我们将消费模式分为两种最基本的形式免费消费和付费消费它们不是对立的而往往是共存的互为补充的一免费消费模式这是互联网内容平台的基石也是我们用来吸引海量用户的核心手段我始终认为免费是为了更好地收费在免费模式下我们向用户提供大量有价值的免费内容其核心目的有两个降低用户门槛让尽可能多的用户无成本地体验我们的产品并留存下来建立信任和展示价值通过免费内容让用户了解我们的实力认可我们平台的价值图中那个漏斗模型非常形象地表达了我的想法免费用户就是漏斗最顶端的巨大流量池我们的工作就是通过产品设计和运营手段筛选并引导其中一小部分认可我们价值的用户一步步地走向漏斗下方的付费环节完成转化二付费消费模式当用户愿意付费时意味着我们要提供远超免费内容的超额价值什么样的内容才具备这种价值呢我通常会从以下四个特性去评估即时性人无我有的第一手信息比如付费的财经社群里比公开市场更早发布的独家分析或者粉丝付费后可以比普通用户提前一周看到创作者的最新视频专业性高度体系化结构化的深度知识这是知识付费最核心的逻辑用户付费购买的不是零散的知识点而是一位专家经过系统梳理后的完整知识体系比如一门精心设计的在线课程个性化服务针对个人情况提供的专属服务比如付费的一对一咨询个人化的学习路径规划有问必答的社群服务等唯一性只有这里才能获得的独特资源或体验比如某个创作者独家的不对外公开的创作手稿或者只有付费会员才能进入的私密社群光有好的付费内容还不够如何设计付费环节直接决定了用户的付费转化率在这方面我总结了三个必须做到的设计要点凸显服务价值在用户付费前我们必须用尽一切办法让他清晰地感知到我将获得什么这包括精美的课程介绍页详细的课程大纲来自其他用户的真实好评免费的试听试读章节等价值感塑造得越好用户的付费意愿就越强明确服务有效期这是建立信任的基础我们必须在最显眼的位置清楚地告诉用户这次购买是一次性买断永久有效的还是按月按年订阅的服务的具体范围和期限是什么任何模糊不清的描述都是在扼杀交易简化支付流程用户从下定决心到完成支付的路径每增加一个步骤都会流失一批用户因此我追求的是让支付流程如丝般顺滑这包括接入主流的支付方式微信支付宝支持一键支付减少不必要的表单填写等最后我将内容消费环节的核心思考总结为下面这张简表这也是我在做商业化设计时反复会问自己的两个问题核心问题我的思考要点什么内容值得用户付费内容必须具备稀缺性至少符合即时性专业性个性化唯一性中的一种付费功能应该如何设计价值前置让用户未付费就感知到价值信息透明权益期限一目了然流程极简让支付成为一种轻松无障碍的体验",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-21 14:52:24",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E7%AB%A0%EF%BC%9A%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E6%A8%A1%E5%9E%8B"><span class="toc-text">第一章：内容产品模型</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#1-1-%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E6%A6%82%E8%BF%B0"><span class="toc-text">1.1 内容产品概述</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">1.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-1-2-%E4%BB%80%E4%B9%88%E6%98%AF%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%EF%BC%9F"><span class="toc-text">1.1.2 什么是内容产品？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-1-3-%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E6%9E%B6%E6%9E%84%E4%B8%8E%E7%94%9F%E6%80%81"><span class="toc-text">1.1.3 内容产品架构与生态</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-2-%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%9E%8B"><span class="toc-text">1.2 内容产品设计模型</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">1.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-2-%E5%86%85%E5%AE%B9%E7%94%9F%E4%BA%A7"><span class="toc-text">1.2.2 内容生产</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-PGC%EF%BC%88Professionally-generated-Content%EF%BC%89"><span class="toc-text">1. PGC（Professionally-generated Content）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-UGC%EF%BC%88User-Generated-Content%EF%BC%89"><span class="toc-text">2. UGC（User Generated Content）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-3-%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8"><span class="toc-text">1.2.3 内容审核</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%96%87%E6%9C%AC%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8"><span class="toc-text">1. 文本内容审核</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9B%BE%E7%89%87-%E9%9F%B3%E8%A7%86%E9%A2%91%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8"><span class="toc-text">2. 图片/音视频内容审核</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-4-%E5%86%85%E5%AE%B9%E5%88%86%E5%8F%91"><span class="toc-text">1.2.4 内容分发</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%86%85%E5%AE%B9%E5%88%86%E5%8F%91%E7%9A%84%E6%9C%AC%E8%B4%A8"><span class="toc-text">1.内容分发的本质</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E7%9A%84%E5%86%85%E5%AE%B9%E5%88%86%E5%8F%91%E6%96%B9%E5%BC%8F"><span class="toc-text">2. 常见的内容分发方式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%80%E3%80%81-%E7%BC%96%E8%BE%91%E5%88%86%E5%8F%91"><span class="toc-text">一、 编辑分发</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BA%8C%E3%80%81-%E8%AE%A2%E9%98%85%E5%88%86%E5%8F%91"><span class="toc-text">二、 订阅分发</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%89%E3%80%81-%E7%A4%BE%E4%BA%A4%E5%88%86%E5%8F%91"><span class="toc-text">三、 社交分发</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9B%9B%E3%80%81-%E7%AE%97%E6%B3%95%E5%88%86%E5%8F%91"><span class="toc-text">四、 算法分发</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-5-%E5%86%85%E5%AE%B9%E6%B6%88%E8%B4%B9"><span class="toc-text">1.2.5 内容消费</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%B8%80%E3%80%81-%E5%85%8D%E8%B4%B9%E6%B6%88%E8%B4%B9%E6%A8%A1%E5%BC%8F"><span class="toc-text">一、 免费消费模式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%BA%8C%E3%80%81-%E4%BB%98%E8%B4%B9%E6%B6%88%E8%B4%B9%E6%A8%A1%E5%BC%8F"><span class="toc-text">二、 付费消费模式</span></a></li></ol></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理入门（一）：第一章：内容产品模型</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-20T08:13:45.000Z" title="发表于 2025-07-20 16:13:45">2025-07-20</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:24.527Z" title="更新于 2025-07-21 14:52:24">2025-07-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">6.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>18分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理入门（一）：第一章：内容产品模型"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/10477.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/10477.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理入门（一）：第一章：内容产品模型</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-20T08:13:45.000Z" title="发表于 2025-07-20 16:13:45">2025-07-20</time><time itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:24.527Z" title="更新于 2025-07-21 14:52:24">2025-07-21</time></header><div id="postchat_postcontent"><h1 id="第一章：内容产品模型"><a href="#第一章：内容产品模型" class="headerlink" title="第一章：内容产品模型"></a>第一章：内容产品模型</h1><h2 id="1-1-内容产品概述"><a href="#1-1-内容产品概述" class="headerlink" title="1.1 内容产品概述"></a>1.1 内容产品概述</h2><h3 id="1-1-1-学习目标"><a href="#1-1-1-学习目标" class="headerlink" title="1.1.1 学习目标"></a>1.1.1 学习目标</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718204934616.png" alt="image-20250718204934616"></p><h3 id="1-1-2-什么是内容产品？"><a href="#1-1-2-什么是内容产品？" class="headerlink" title="1.1.2 什么是内容产品？"></a>1.1.2 什么是内容产品？</h3><p><strong>内容产品，就是以图文、视频/直播、音频等形式为用户提供内容服务的产品形态。</strong></p><p>这句话揭示了内容产品的两个核心要素：</p><ol><li><strong>核心载体:</strong> 内容。这可以是引人深思的一段文字、一张精美的图片、一段动听的音频，或是一段引人入胜的视频。</li><li><strong>最终目的 :</strong> 服务。通过这些内容，满足用户在信息获取、学习提升、娱乐消遣、情感共鸣、社交连接等方面的需求。</li></ol><p>因此，我们可以这样理解：<strong>内容产品本质上是一个围绕“内容”进行价值交换的系统。</strong> 在这个系统中，用户付出他们的<strong>时间、注意力和金钱</strong>，来换取平台提供的<strong>信息价值、娱乐价值和情感价值</strong>。</p><table><thead><tr><th align="left"></th><th align="left"></th><th align="left"></th><th align="left"></th></tr></thead><tbody><tr><td align="left"><strong>图文类</strong></td><td align="left"><strong>视频类</strong></td><td align="left"><strong>音频类</strong></td><td align="left"><strong>综合/社区类</strong></td></tr><tr><td align="left">微信公众号</td><td align="left">抖音</td><td align="left">喜马拉雅</td><td align="left">小红书</td></tr><tr><td align="left">知乎</td><td align="left">Bilibili</td><td align="left">Spotify</td><td align="left">微博</td></tr><tr><td align="left">今日头条</td><td align="left">YouTube</td><td align="left">各类播客App</td><td align="left">豆瓣</td></tr></tbody></table><h3 id="1-1-3-内容产品架构与生态"><a href="#1-1-3-内容产品架构与生态" class="headerlink" title="1.1.3 内容产品架构与生态"></a><strong>1.1.3 内容产品架构与生态</strong></h3><p>首先，<strong>用户端</strong>是我们最熟悉的部分，它直接服务于图下方的“用户”。这是我们产品的“脸面”和“橱窗”，我们所有的努力，比如设计更沉浸的播放体验、更精准的信息流推荐，都是为了让用户在这里能高效、愉悦地消费内容，并且愿意留下来。</p><p>其次，<strong>自媒体端</strong>是服务于图上方“内容生产”者的“创作工坊”。根据我的经验，一个产品的内容生态能否繁荣，很大程度上取决于自媒体端的体验。我们是否提供了足够便捷的编辑器？数据分析是否足够清晰，能帮助创作者优化内容？这些决定了创作者是否愿意持续为我们提供“内容弹药”。</p><p>最后，是隐藏在幕后的“中央系统”——<strong>平台端</strong>。它对应着图中“平台”这个中央枢纽的后台能力。这部分是产品运营和管理人员使用的，我们通过它来审核内容、配置推荐策略、管理用户、对接广告主，它保证了整个生态的秩序和商业目标的实现。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718210401085.png" alt="image-20250718210401085"></p><table><thead><tr><th align="left"><strong>架构组成</strong></th><th align="left"><strong>核心服务对象 (对应图中角色)</strong></th><th align="left"><strong>核心目标</strong></th><th align="left"><strong>关键功能举例</strong></th></tr></thead><tbody><tr><td align="left">用户端</td><td align="left">用户</td><td align="left">满足内容<strong>消费</strong>和互动需求，提升留存与时长。</td><td align="left">信息流、搜索、播放/阅读页、点赞、评论、分享、个人主页</td></tr><tr><td align="left">自媒端</td><td align="left">内容生产者</td><td align="left">满足内容<strong>生产</strong>和管理需求，提升创作效率与意愿。</td><td align="left">文章/视频编辑器、内容发布、数据看板、粉丝管理、收益中心</td></tr><tr><td align="left">平台端</td><td align="left"><strong>平台运营管理者</strong></td><td align="left"><strong>管理</strong>整个平台的用户与内容，确保生态健康与商业运转。</td><td align="left">内容审核系统、用户管理、推荐算法配置、广告系统、数据监控后台</td></tr></tbody></table><p>现在，再回过头提供的那张生态图，一切就都清晰了。我们作为产品经理，设计的内部架构（表格内容），完美地服务和驱动了外部的商业生态。我们设计的每一个功能，都应该能清晰地回答：它属于哪个端口？它在为生态中的哪个角色解决什么问题？</p><h2 id="1-2-内容产品设计模型"><a href="#1-2-内容产品设计模型" class="headerlink" title="1.2 内容产品设计模型"></a>1.2 内容产品设计模型</h2><h3 id="1-2-1-学习目标"><a href="#1-2-1-学习目标" class="headerlink" title="1.2.1 学习目标"></a>1.2.1 学习目标</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718210534714.png" alt="image-20250718210534714"></p><h3 id="1-2-2-内容生产"><a href="#1-2-2-内容生产" class="headerlink" title="1.2.2 内容生产"></a>1.2.2 内容生产</h3><h4 id="1-PGC（Professionally-generated-Content）"><a href="#1-PGC（Professionally-generated-Content）" class="headerlink" title="1. PGC（Professionally-generated Content）"></a>1. PGC（Professionally-generated Content）</h4><p>在内容产品的世界里，我们首先遇到的就是PGC（Professionally-generated Content），我喜欢称之为“专业生产内容”。顾名思义，它的核心在于**“专业”**二字。</p><p>PGC是一种相对传统的内容生产方式。它的创作者通常是平台官方的工作人员（如编辑、记者）、与平台签约的领域专家，或是专业的MCN机构。</p><p>看图中的新闻App截图，无论是报道国家大事，还是分析财经动态，都体现出高度的专业性、规范性和权威性，这就是最典型的PGC。</p><p>作为产品经理，我在规划内容生态时，非常看重PGC的价值。它最大的<strong>优势在于“质量和规范可控”</strong>。因为生产者是专业的，我们可以确保内容的准确性、深度和品牌调性相符。这在产品冷启动阶段尤其重要，高质量的PGC内容能够为平台快速树立起专业、可信的品牌形象，并为整个内容生态定下基调。</p><p>然而，PGC的<strong>劣势</strong>也同样致命，那就是**“数量不足，难以满足消费需求”**。专业内容的生产成本高、周期长，一个编辑团队无论多么高效，其产出量终究是有限的。</p><p>这就像一家米其林餐厅，虽然菜品精致，但无法满足全城人一日三餐的需求。当平台用户规模扩大后，仅靠PGC是远远无法满足用户五花八门、海量的内容消费需求的。</p><p>为了让你能快速把握PGC的要点，我把它总结成了下面这张表：</p><table><thead><tr><th align="left"><strong>维度</strong></th><th align="left"><strong>描述</strong></th></tr></thead><tbody><tr><td align="left"><strong>生产者</strong></td><td align="left">平台官方、签约专家、合作机构、媒体等专业团体或个人。</td></tr><tr><td align="left"><strong>内容特点</strong></td><td align="left">垂直深度、制作精良、标准化、权威性高。</td></tr><tr><td align="left"><strong>核心优势</strong></td><td align="left"><strong>质量与规范可控</strong>，是平台打造品牌、建立信任的基石。</td></tr><tr><td align="left"><strong>核心劣势</strong></td><td align="left"><strong>生产成本高，内容数量和多样性有限</strong>，难以规模化。</td></tr><tr><td align="left"><strong>适用场景</strong></td><td align="left">平台冷启动、核心栏目、深度专题、官方活动、付费课程等。</td></tr></tbody></table><p>总而言之，我认为PGC是内容平台不可或缺的“定海神针”。它负责为平台打造高品质的“门面”和“样板间”。但要让平台真正繁荣起来，光靠这支“正规军”是远远不够的，我们必须引入另一股更庞大、更多元的力量，这就是我们接下来要讲的UGC。</p><h4 id="2-UGC（User-Generated-Content）"><a href="#2-UGC（User-Generated-Content）" class="headerlink" title="2. UGC（User Generated Content）"></a>2. UGC（User Generated Content）</h4><p>如果说PGC是内容生态里的“正规军”，那么UGC（User Generated Content）就是汪洋大海般的“人民战争”。它的出现，彻底改变了内容生产的游戏规则。</p><p>UGC是一种全新的内容生产方式，它的核心理念是：<strong>“内容产品的使用者，也是产品内容的生产者”</strong>。</p><p>这意味着，平台上每一个普通的、匿名的用户，都可以成为内容的创作者。你我他，只要愿意，都可以发布一条动态、写一篇点评、录一段视频、发一条弹幕。</p><p>从我作为产品经理的视角来看，UGC最大的<strong>优势</strong>，在于它用一种极低成本的方式，解决了PGC无法解决的两个核心问题：<strong>“内容量大”<strong>和</strong>“满足多样消费需求”</strong>。成千上万的用户自发地产出内容，其数量和覆盖面的广度是任何专业团队都无法比拟的。</p><p>从美食探店到萌宠日常，从游戏攻略到情感树洞，UGC能够满足用户几乎所有长尾、细分的需求，这是构建一个繁荣、活跃社区的绝对基础。</p><p>然而，这种自由创作的模式也带来了它最致命的<strong>劣势</strong>，那就是**“内容质量不均，需要审核机制”**。当任何人都可以发布内容时，内容的质量就变得不可控，低质、灌水、甚至违规的内容会大量涌现。因此，对于一个以UGC为主的平台，<strong>建立强大、高效的审核机制（包括机器审核和人工审核）就不是一个可选项，而是一个生死攸关的必选项</strong>。这是我们作为产品经理必须承担的责任。</p><p>同样，我为你准备了一张表格，来清晰地对比UGC和PGC的差异。</p><table><thead><tr><th align="left"><strong>维度</strong></th><th align="left"><strong>描述</strong></th></tr></thead><tbody><tr><td align="left"><strong>生产者</strong></td><td align="left">平台的任何普通用户，通常是匿名的、非专业的。</td></tr><tr><td align="left"><strong>内容特点</strong></td><td align="left">数量巨大、形式多样、生活化、互动性强、质量参差不齐。</td></tr><tr><td align="left"><strong>核心优势</strong></td><td align="left"><strong>生产成本极低，内容量和多样性极大</strong>，能满足用户的长尾需求。</td></tr><tr><td align="left"><strong>核心劣势</strong></td><td align="left"><strong>内容质量不可控</strong>，平台需要投入巨大的审核和运营成本来维护社区环境。</td></tr><tr><td align="left"><strong>适用场景</strong></td><td align="left">社交媒体（微博）、社区（小红书/知乎）、点评（大众点评）、短视频（抖音/快手）等。</td></tr></tbody></table><p><strong>我的思考与总结：</strong></p><p>在实际的产品工作中，我们极少会遇到一个平台是纯PGC或纯UGC的。更常见的情况是，<strong>PGC和UGC是相辅相成的</strong>。</p><p>一个健康的策略往往是：<strong>用PGC来定义平台的调性、树立质量标杆（“正规军”打样板），再用UGC来丰富平台的内容生态、提升社区的活跃度（“人民战争”促繁荣）</strong>。如何设计一套好的机制，让这两股力量和谐共存、互相促进，是我们产品经理需要不断探索的艺术。</p><h3 id="1-2-3-内容审核"><a href="#1-2-3-内容审核" class="headerlink" title="1.2.3 内容审核"></a>1.2.3 内容审核</h3><h4 id="1-文本内容审核"><a href="#1-文本内容审核" class="headerlink" title="1. 文本内容审核"></a>1. 文本内容审核</h4><p>我们刚刚聊完UGC带来的海量内容，那紧接着一个必须面对的问题就是：如何管理这些内容，确保社区的“干净”？这就是内容审核的范畴，它是维持平台健康生态的生命线。</p><p>我们先从最基础、也是应用最广泛的<strong>文本内容审核</strong>开始。</p><p>在我的经验里，处理文本内容最直接的方式就是<strong>自动审核</strong>，特别是基于敏感词的过滤系统。这里的基本逻辑非常简单，就像这张图里展示的A到B的过程。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718211313829.png" alt="image-20250718211313829"></p><p><strong>第一步（图中的A），是“设置敏感词”</strong>。我会和我的运营、法务同事一起，在平台的后台系统里，共同维护一个“敏感词词库”。这个词库是动态更新的，我们会持续地把涉及色情、暴力、赌博、政治、广告、人身攻击等各类违规的词语加进去。这相当于我们为平台的“安保系统”配置好了要抓捕的“黑名单”。</p><p><strong>第二步（图中的B），是“识别处理”</strong>。当用户发布任何文本内容时，比如一篇帖子、一条评论，我们的系统会自动将这段文本与后台的敏感词库进行秒级匹配。一旦命中词库里的某个词，系统就会立刻执行我们预设好的处理动作。</p><p>这个“处理动作”具体是什么，是我们作为产品经理需要精心设计的。简单的用星号（<code>*</code>）替换只是其中一种。根据违规词语的严重等级，我通常会设计一套组合策略。我把这些常见的处理方式整理成了一个表格，方便我们理解。</p><table><thead><tr><th align="left"><strong>处理方式</strong></th><th align="left"><strong>描述</strong></th><th align="left"><strong>我通常应用的场景</strong></th></tr></thead><tbody><tr><td align="left"><strong>替换</strong></td><td align="left">将命中的敏感词替换为<code>*</code>等符号，内容本身依然可以发出。</td><td align="left">对用户体验影响最小，适用于一般性的不文明用语、脏话等。</td></tr><tr><td align="left"><strong>拦截</strong></td><td align="left">直接阻止该内容的发布，并明确告知用户违规原因。</td><td align="left">适用于垃圾广告、恶意导流、联系方式等明确的违规行为。</td></tr><tr><td align="left"><strong>仅作者可见</strong></td><td align="left">内容成功发布，但只有作者自己能看到，同时自动进入人工审核队列。</td><td align="left">适用于内容疑似违规，但不确定，需要人工介入判断的情况。给用户一种“已发出”的错觉，可以减少其申诉和修改绕过的行为。</td></tr><tr><td align="left"><strong>先审后发</strong></td><td align="left">内容发布后，不会立即对外展示，必须等待人工审核通过后才可见。</td><td align="left">适用于高风险场景，如金融、医疗内容的发布，或针对有过多次违规记录的用户。</td></tr></tbody></table><p><strong>我的总结：</strong></p><p>我要强调一点，单纯的关键词审核只是万里长征的第一步。现在的用户非常聪明，会用谐音、拆字、加符号等各种方式来绕过审核，比如“V信”“威信”“vievie”等等。因此，在如今的平台上，敏感词系统通常只是作为基础的、第一道防线。更成熟的平台，会在此之上，结合基于机器学习的文本分类模型、用户行为分析等多种技术手段，来构建一个立体、智能的审核系统。</p><h4 id="2-图片-音视频内容审核"><a href="#2-图片-音视频内容审核" class="headerlink" title="2. 图片/音视频内容审核"></a>2. 图片/音视频内容审核</h4><p>如果说文本审核是“普通难度”，那么图片、音频和视频的审核就是“困难模式”。这类非结构化内容的审核，其技术门槛和复杂度要高出几个量级。</p><p>在我的职业生涯中，对于这类审核，我的原则非常明确：<strong>除非公司是专做AI视觉技术的，否则我们坚决不自研，而是选择“购买服务”</strong>。</p><p>为什么？因为从零到一自研一套图片/音视频识别AI模型，需要顶尖的算法团队、海量的标注数据和强大的计算资源，这对于绝大多数公司来说，投入产出比极低。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718211731684.png" alt="image-20250718211731684"></p><p>这张截图里的阿里云“图片涉政暴恐识别”服务，就完美印证了我的观点。目前，<strong>阿里、百度、腾讯</strong>等一线云服务大厂，都已经把这项能力做成了非常成熟的商业化服务。我们作为产品开发者，只需要付费调用他们的API接口，就能获得世界一流的审核能力。</p><p>这个过程对我们来说，就像是把专业的事交给了最专业的人。我来为你拆解一下我们作为产品经理，在这里需要做的事情：</p><ol><li><p><strong>技术选型</strong>：我们会对几家主流服务商（如阿里云、腾讯云等）进行评估，对比他们的识别准确率、支持的审核维度、API调用价格、服务稳定性等，选择最适合我们业务的合作伙伴。</p></li><li><p><strong>策略制定</strong>：这是我们的核心工作。调用API后，第三方服务会返回给我们一个包含各类标签和“置信度”分数的结果。比如，一张图片可能会返回<code>{“色情”: 0.98, “暴恐”: 0.15, “广告”: 0.4}</code>这样的数据。我们需要根据业务的风险容忍度，来制定处理规则。例如：</p><ul><li>色情分数 &gt; 0.95，<strong>直接拦截</strong>。</li><li>广告分数在 0.7-0.9 之间，<strong>转人工审核</strong>。</li><li>所有分数都 &lt; 0.3，<strong>自动通过</strong>。<br>这个阈值的设定，需要我们不断地根据实际运营情况去调整和优化。</li></ul></li><li><p><strong>成本控制</strong>：要牢记，——<strong>“需要付费”</strong>。这类服务是按调用次数（如张数）或时长（如分钟）收费的。如果我们的产品有海量的图片/视频上传，这笔审核开销会非常巨大。因此，我必须在产品设计之初，就将这部分成本纳入运营预算，并持续监控其开销。</p></li></ol><p>为了让你更清晰地掌握要点，我整理了下面的表格：</p><table><thead><tr><th align="left"><strong>维度</strong></th><th align="left"><strong>描述</strong></th></tr></thead><tbody><tr><td align="left"><strong>核心逻辑</strong></td><td align="left"><strong>不自研底层技术</strong>，通过成熟的第三方云服务API来解决专业问题。</td></tr><tr><td align="left"><strong>主要服务商</strong></td><td align="left">阿里云、腾讯云、百度智能云、七牛云等。</td></tr><tr><td align="left"><strong>常见审核维度</strong></td><td align="left">涉黄、涉政、暴恐、广告、灌水、违禁品、公众人物识别等。</td></tr><tr><td align="left"><strong>PM的核心工作</strong></td><td align="left"><strong>技术选型、策略制定（设置阈值）、成本控制</strong>。</td></tr></tbody></table><hr><h3 id="1-2-4-内容分发"><a href="#1-2-4-内容分发" class="headerlink" title="1.2.4 内容分发"></a><strong>1.2.4 内容分发</strong></h3><p>在我看来，如果我们把内容生产比作“做饭”，内容审核比作“品控”，那么<strong>内容分发就是“上菜”</strong>。菜做得再好，品控再严，如果不能精准、高效地送到想吃这道菜的食客面前，那一切都是徒劳。</p><h4 id="1-内容分发的本质"><a href="#1-内容分发的本质" class="headerlink" title="1.内容分发的本质"></a><strong>1.内容分发的本质</strong></h4><p>所以，内容分发的本质，我总结为一句话：<strong>实现内容与用户之间，最高效、最合适的匹配</strong>。我们作为产品经理，设计的一切分发机制，无论是信息流、推荐位还是热榜，都是为了这个终极目标服务。</p><h4 id="2-常见的内容分发方式"><a href="#2-常见的内容分发方式" class="headerlink" title="2. 常见的内容分发方式"></a><strong>2. 常见的内容分发方式</strong></h4><p>在产品的不同发展阶段和不同场景下，我会运用不同的分发策略。经过多年的演化，我主要将它们归为以下四种主流方式。</p><hr><h5 id="一、-编辑分发"><a href="#一、-编辑分发" class="headerlink" title="一、 编辑分发"></a><strong>一、 编辑分发</strong></h5><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212239607.png" alt="image-20250718212239607"></p><p>这是最古老、最经典的分发方式，我称之为<code>“总编辑模式”</code>。</p><p>它的核心是由平台内部的编辑团队，基于其专业判断和平台调性，人工挑选出他们认为的“好内容”，并决定这些内容展示在哪个位置、展示给多少人。我们早期看到的各大门户网站首页，就是最典型的编辑分发。</p><p><strong>我的解读</strong>：</p><ul><li><strong>优点</strong>：在于<strong>质量可控</strong>和<strong>价值引导</strong>。在产品初期，我可以通过编辑精选，快速为产品树立起高质量、有调性的品牌形象，告诉用户“我们这里有什么”。</li><li><strong>缺点</strong>：是<strong>中心化</strong>的，用户的选择是被动的。并且，它极度依赖编辑的个人能力，成本高、效率低，无法满足海量用户的个性化需求。</li></ul><hr><h5 id="二、-订阅分发"><a href="#二、-订阅分发" class="headerlink" title="二、 订阅分发"></a>二、 订阅分发</h5><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212322555.png" alt="image-20250718212322555"></p><p>如果说编辑分发是“平台喂给你什么，你就看什么”，那么订阅分发就是把选择权交还给了用户，我称之为<code>"报刊亭模式"</code>。</p><p>在这种模式下，用户可以主动“关注”或“订阅”自己感兴趣的创作者（KOL）、专栏或话题。这样，用户就构建了属于自己的信息获取渠道。我们关注微信公众号、B站UP主，都属于订阅分发。</p><p>但这种需要平台需要有KOL基础,也就是发展初期是不太适合做订阅分发,难以招揽到大牌博主入驻</p><p><strong>我的解读</strong>：</p><ul><li><strong>优点</strong>：用户是主动选择，因此粘性非常高，容易围绕KOL构建起<code>“私域流量”</code>，用户忠诚度强。</li><li><strong>缺点</strong>：对用户的“发现能力”要求高，用户需要自己去找到值得订阅的目标。对于新用户和新创作者来说，冷启动会比较困难。</li></ul><hr><h5 id="三、-社交分发"><a href="#三、-社交分发" class="headerlink" title="三、 社交分发"></a><strong>三、 社交分发</strong></h5><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212412442.png" alt="image-20250718212412442"></p><p>社交分发是一种极具爆发力的分发方式，我称之为``。</p><p>内容不再仅仅依赖平台或创作者，而是通过用户自身的社交关系链进行传播。我的朋友在朋友圈分享了一篇文章，我认为有价值，再次转发，这样一传十、十传百地扩散出去。</p><p><strong>我的解读</strong>：</p><ul><li><strong>优点</strong>：基于<code>“信任代理”</code>，朋友推荐的内容我更愿意相信和打开。传播速度快，容易产生裂变效应，引爆话题。</li><li><strong>缺点</strong>：内容分发的广度和速度变得不可控，并且非常依赖内容的“社交属性”，即是否足够有趣、有槽点、有共鸣，能激发用户的分享欲。</li></ul><hr><h5 id="四、-算法分发"><a href="#四、-算法分发" class="headerlink" title="四、 算法分发"></a>四、 算法分发</h5><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212636209.png" alt="image-20250718212636209"></p><p>这是当下最主流，也是技术含量最高的方式，我称之为<code>“私人助理模式”</code>。</p><p>平台通过强大的算法模型，分析用户的历史行为（点击、点赞、停留时长等），理解其兴趣偏好，然后从海量内容池中，为每一个用户“量身定制”一个独一无二的信息流。今日头条和抖音是算法分发的集大成者。</p><p><strong>我的解读</strong>：</p><ul><li><strong>优点</strong>：<strong>极致的个性化和高效率</strong>。它能让用户“沉迷”其中，因为每一条都是你可能感兴趣的，极大地提升了用户粘性和使用时长。</li><li><strong>缺点</strong>：容易形成<code>“信息茧房”</code>，让用户视野越来越窄。同时，它对平台的内容总量和用户规模有很高的要求，小平台很难玩转。</li></ul><p>为了方便我们整体回顾，我将这四种方式的核心特点总结在了一张表里：</p><table><thead><tr><th align="left"><strong>分发方式</strong></th><th align="left"><strong>核心逻辑</strong></th><th align="left"><strong>用户角色</strong></th><th align="left"><strong>优点</strong></th><th align="left"><strong>缺点</strong></th></tr></thead><tbody><tr><td align="left"><strong>编辑分发</strong></td><td align="left">编辑人工精选</td><td align="left">被动接收者</td><td align="left">质量、调性可控</td><td align="left">效率低、非个性化</td></tr><tr><td align="left"><strong>订阅分发</strong></td><td align="left">用户主动关注</td><td align="left">主动选择者</td><td align="left">粘性高、关系强</td><td align="left">发现效率低、启动难</td></tr><tr><td align="left"><strong>社交分发</strong></td><td align="left">好友分享推荐</td><td align="left">传播节点</td><td align="left">信任度高、裂变快</td><td align="left">不可控、依赖内容社交性</td></tr><tr><td align="left"><strong>算法分发</strong></td><td align="left">机器智能推荐</td><td align="left">沉浸体验者</td><td align="left">高效率、个性化</td><td align="left">易产生信息茧房</td></tr></tbody></table><p>在我的实际工作中，现代内容产品几乎不会只采用单一的分发模式，而是将这四种方式进行<strong>混合</strong>。比如，一个信息流里，既有我订阅的，也有算法推荐的，还可能夹杂着编辑精选的热点。如何调配好这四种模式的比例和权重，正是我们产品经理需要不断探索和优化的核心策略。</p><h3 id="1-2-5-内容消费"><a href="#1-2-5-内容消费" class="headerlink" title="1.2.5 内容消费"></a><strong>1.2.5 内容消费</strong></h3><p>在我看来，内容产品的商业模式，本质上都是在<strong>内容消费</strong>这个环节做文章。我们将消费模式分为两种最基本的形式：<strong>免费消费</strong>和<strong>付费消费</strong>。它们不是对立的，而往往是共存的、互为补充的。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213151675.png" alt="image-20250718213151675"></p><hr><h4 id="一、-免费消费模式"><a href="#一、-免费消费模式" class="headerlink" title="一、 免费消费模式"></a>一、 免费消费模式</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213203871.png" alt="image-20250718213203871"></p><p>这是互联网内容平台的基石，也是我们用来吸引海量用户的核心手段。我始终认为，<strong>免费是为了更好地收费</strong>。</p><p>在免费模式下，我们向用户提供大量有价值的免费内容，其核心目的有两个：</p><ol><li><strong>降低用户门槛</strong>：让尽可能多的用户无成本地体验我们的产品，并留存下来。</li><li><strong>建立信任和展示价值</strong>：通过免费内容，让用户了解我们的“实力”，认可我们平台的价值。</li></ol><p>图中那个<strong>漏斗模型</strong>非常形象地表达了我的想法。免费用户就是漏斗最顶端的巨大流量池。我们的工作，就是通过产品设计和运营手段，筛选并引导其中一小部分认可我们价值的用户，一步步地走向漏斗下方的付费环节，完成转化。</p><hr><h4 id="二、-付费消费模式"><a href="#二、-付费消费模式" class="headerlink" title="二、 付费消费模式"></a>二、 付费消费模式</h4><p>当用户愿意付费时，意味着我们要提供远超免费内容的“超额价值”。什么样的内容才具备这种价值呢？我通常会从以下四个特性去评估。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213242810.png" alt="image-20250718213242810"></p><ul><li><strong>1. 即时性</strong>：人无我有的第一手信息。比如，付费的财经社群里，比公开市场更早发布的独家分析；或者，粉丝付费后可以比普通用户提前一周看到创作者的最新视频。</li><li><strong>2. 专业性</strong>：高度体系化、结构化的深度知识。这是知识付费最核心的逻辑。用户付费购买的不是零散的知识点，而是一位专家经过系统梳理后的完整知识体系，比如一门精心设计的在线课程。</li><li><strong>3. 个性化服务</strong>：针对个人情况提供的专属服务。比如，付费的一对一咨询、个人化的学习路径规划、有问必答的社群服务等。</li><li><strong>4. 唯一性</strong>：只有这里才能获得的独特资源或体验。比如，某个创作者独家的、不对外公开的创作手稿；或者，只有付费会员才能进入的私密社群。</li></ul><hr><p>光有好的付费内容还不够，如何设计付费环节，直接决定了用户的付费转化率。在这方面，我总结了三个必须做到的设计要点。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213331617.png" alt="image-20250718213331617"></p><ul><li><strong>1. 凸显服务价值</strong>：在用户付费前，我们必须用尽一切办法让他清晰地感知到“我将获得什么”。这包括精美的课程介绍页、详细的课程大纲、来自其他用户的真实好评、免费的试听/试读章节等。价值感塑造得越好，用户的付费意愿就越强。</li><li><strong>2. 明确服务有效期</strong>：这是建立信任的基础。我们必须在最显眼的位置清楚地告诉用户：这次购买是一次性买断、永久有效的？还是按月/按年订阅的？服务的具体范围和期限是什么？任何模糊不清的描述都是在扼杀交易。</li><li><strong>3. 简化支付流程</strong>：用户从下定决心到完成支付的路径，每增加一个步骤，都会流失一批用户。因此，我追求的是让支付流程“如丝般顺滑”。这包括接入主流的支付方式（微信/支付宝）、支持一键支付、减少不必要的表单填写等。</li></ul><p>最后，我将内容消费环节的核心思考，总结为下面这张简表，这也是我在做商业化设计时，反复会问自己的两个问题。</p><table><thead><tr><th align="left"><strong>核心问题</strong></th><th align="left"><strong>我的思考要点</strong></th></tr></thead><tbody><tr><td align="left"><strong>1. 什么内容值得用户付费？</strong></td><td align="left">内容必须具备稀缺性，至少符合<strong>即时性、专业性、个性化、唯一性</strong>中的一种。</td></tr><tr><td align="left"><strong>2. 付费功能应该如何设计？</strong></td><td align="left"><strong>价值前置</strong>：让用户未付费就感知到价值。<br><strong>信息透明</strong>：权益、期限一目了然。<br><strong>流程极简</strong>：让支付成为一种轻松、无障碍的体验。</td></tr></tbody></table><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/10477.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/10477.html&quot;)">产品经理入门（一）：第一章：内容产品模型</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/10477.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理入门（一）：第一章：内容产品模型&amp;url=https://prorise666.site/posts/10477.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/30401.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">1️⃣ 内容产品模型实战</div></div></a></div><div class="next-post pull-right"><a href="/posts/56262.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理入门（一）：第一章：内容产品模型",date:"2025-07-20 16:13:45",updated:"2025-07-21 14:52:24",tags:["产品经理教程"],categories:["产品经理"],content:'\n# 第一章：内容产品模型\n\n## 1.1 内容产品概述\n\n### 1.1.1 学习目标\n\n![image-20250718204934616](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718204934616.png)\n\n### 1.1.2 什么是内容产品？\n\n**内容产品，就是以图文、视频/直播、音频等形式为用户提供内容服务的产品形态。**\n\n这句话揭示了内容产品的两个核心要素：\n\n1.  **核心载体:** 内容。这可以是引人深思的一段文字、一张精美的图片、一段动听的音频，或是一段引人入胜的视频。\n2.  **最终目的 :** 服务。通过这些内容，满足用户在信息获取、学习提升、娱乐消遣、情感共鸣、社交连接等方面的需求。\n\n因此，我们可以这样理解：**内容产品本质上是一个围绕“内容”进行价值交换的系统。** 在这个系统中，用户付出他们的**时间、注意力和金钱**，来换取平台提供的**信息价值、娱乐价值和情感价值**。\n\n| | | | |\n| :--- | :--- | :--- | :--- |\n| **图文类** | **视频类** | **音频类** | **综合/社区类** |\n| 微信公众号 | 抖音 | 喜马拉雅 | 小红书 |\n| 知乎 | Bilibili | Spotify | 微博 |\n| 今日头条 | YouTube | 各类播客App | 豆瓣 |\n\n\n\n\n\n### **1.1.3 内容产品架构与生态**\n\n首先，**用户端**是我们最熟悉的部分，它直接服务于图下方的“用户”。这是我们产品的“脸面”和“橱窗”，我们所有的努力，比如设计更沉浸的播放体验、更精准的信息流推荐，都是为了让用户在这里能高效、愉悦地消费内容，并且愿意留下来。\n\n其次，**自媒体端**是服务于图上方“内容生产”者的“创作工坊”。根据我的经验，一个产品的内容生态能否繁荣，很大程度上取决于自媒体端的体验。我们是否提供了足够便捷的编辑器？数据分析是否足够清晰，能帮助创作者优化内容？这些决定了创作者是否愿意持续为我们提供“内容弹药”。\n\n最后，是隐藏在幕后的“中央系统”——**平台端**。它对应着图中“平台”这个中央枢纽的后台能力。这部分是产品运营和管理人员使用的，我们通过它来审核内容、配置推荐策略、管理用户、对接广告主，它保证了整个生态的秩序和商业目标的实现。\n\n![image-20250718210401085](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718210401085.png)\n\n| **架构组成** | **核心服务对象 (对应图中角色)** | **核心目标** | **关键功能举例** |\n| :--- | :--- | :--- | :--- |\n| 用户端 | 用户 | 满足内容**消费**和互动需求，提升留存与时长。 | 信息流、搜索、播放/阅读页、点赞、评论、分享、个人主页 |\n| 自媒端 | 内容生产者 | 满足内容**生产**和管理需求，提升创作效率与意愿。 | 文章/视频编辑器、内容发布、数据看板、粉丝管理、收益中心 |\n| 平台端 | **平台运营管理者** | **管理**整个平台的用户与内容，确保生态健康与商业运转。 | 内容审核系统、用户管理、推荐算法配置、广告系统、数据监控后台 |\n\n现在，再回过头提供的那张生态图，一切就都清晰了。我们作为产品经理，设计的内部架构（表格内容），完美地服务和驱动了外部的商业生态。我们设计的每一个功能，都应该能清晰地回答：它属于哪个端口？它在为生态中的哪个角色解决什么问题？\n\n\n\n\n\n\n\n\n\n## 1.2 内容产品设计模型\n\n### 1.2.1 学习目标\n\n![image-20250718210534714](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718210534714.png)\n\n### 1.2.2 内容生产\n\n#### 1. PGC（Professionally-generated Content）\n\n在内容产品的世界里，我们首先遇到的就是PGC（Professionally-generated Content），我喜欢称之为“专业生产内容”。顾名思义，它的核心在于**“专业”**二字。\n\nPGC是一种相对传统的内容生产方式。它的创作者通常是平台官方的工作人员（如编辑、记者）、与平台签约的领域专家，或是专业的MCN机构。\n\n看图中的新闻App截图，无论是报道国家大事，还是分析财经动态，都体现出高度的专业性、规范性和权威性，这就是最典型的PGC。\n\n作为产品经理，我在规划内容生态时，非常看重PGC的价值。它最大的**优势在于“质量和规范可控”**。因为生产者是专业的，我们可以确保内容的准确性、深度和品牌调性相符。这在产品冷启动阶段尤其重要，高质量的PGC内容能够为平台快速树立起专业、可信的品牌形象，并为整个内容生态定下基调。\n\n然而，PGC的**劣势**也同样致命，那就是**“数量不足，难以满足消费需求”**。专业内容的生产成本高、周期长，一个编辑团队无论多么高效，其产出量终究是有限的。\n\n这就像一家米其林餐厅，虽然菜品精致，但无法满足全城人一日三餐的需求。当平台用户规模扩大后，仅靠PGC是远远无法满足用户五花八门、海量的内容消费需求的。\n\n为了让你能快速把握PGC的要点，我把它总结成了下面这张表：\n\n| **维度** | **描述** |\n| :--- | :--- |\n| **生产者** | 平台官方、签约专家、合作机构、媒体等专业团体或个人。 |\n| **内容特点** | 垂直深度、制作精良、标准化、权威性高。 |\n| **核心优势** | **质量与规范可控**，是平台打造品牌、建立信任的基石。 |\n| **核心劣势** | **生产成本高，内容数量和多样性有限**，难以规模化。 |\n| **适用场景** | 平台冷启动、核心栏目、深度专题、官方活动、付费课程等。 |\n\n总而言之，我认为PGC是内容平台不可或缺的“定海神针”。它负责为平台打造高品质的“门面”和“样板间”。但要让平台真正繁荣起来，光靠这支“正规军”是远远不够的，我们必须引入另一股更庞大、更多元的力量，这就是我们接下来要讲的UGC。\n\n\n\n#### 2. UGC（User Generated Content）\n\n如果说PGC是内容生态里的“正规军”，那么UGC（User Generated Content）就是汪洋大海般的“人民战争”。它的出现，彻底改变了内容生产的游戏规则。\n\nUGC是一种全新的内容生产方式，它的核心理念是：**“内容产品的使用者，也是产品内容的生产者”**。\n\n这意味着，平台上每一个普通的、匿名的用户，都可以成为内容的创作者。你我他，只要愿意，都可以发布一条动态、写一篇点评、录一段视频、发一条弹幕。\n\n从我作为产品经理的视角来看，UGC最大的**优势**，在于它用一种极低成本的方式，解决了PGC无法解决的两个核心问题：**“内容量大”**和**“满足多样消费需求”**。成千上万的用户自发地产出内容，其数量和覆盖面的广度是任何专业团队都无法比拟的。\n\n从美食探店到萌宠日常，从游戏攻略到情感树洞，UGC能够满足用户几乎所有长尾、细分的需求，这是构建一个繁荣、活跃社区的绝对基础。\n\n然而，这种自由创作的模式也带来了它最致命的**劣势**，那就是**“内容质量不均，需要审核机制”**。当任何人都可以发布内容时，内容的质量就变得不可控，低质、灌水、甚至违规的内容会大量涌现。因此，对于一个以UGC为主的平台，**建立强大、高效的审核机制（包括机器审核和人工审核）就不是一个可选项，而是一个生死攸关的必选项**。这是我们作为产品经理必须承担的责任。\n\n同样，我为你准备了一张表格，来清晰地对比UGC和PGC的差异。\n\n| **维度** | **描述** |\n| :--- | :--- |\n| **生产者** | 平台的任何普通用户，通常是匿名的、非专业的。 |\n| **内容特点** | 数量巨大、形式多样、生活化、互动性强、质量参差不齐。 |\n| **核心优势** | **生产成本极低，内容量和多样性极大**，能满足用户的长尾需求。 |\n| **核心劣势** | **内容质量不可控**，平台需要投入巨大的审核和运营成本来维护社区环境。 |\n| **适用场景** | 社交媒体（微博）、社区（小红书/知乎）、点评（大众点评）、短视频（抖音/快手）等。 |\n\n**我的思考与总结：**\n\n在实际的产品工作中，我们极少会遇到一个平台是纯PGC或纯UGC的。更常见的情况是，**PGC和UGC是相辅相成的**。\n\n一个健康的策略往往是：**用PGC来定义平台的调性、树立质量标杆（“正规军”打样板），再用UGC来丰富平台的内容生态、提升社区的活跃度（“人民战争”促繁荣）**。如何设计一套好的机制，让这两股力量和谐共存、互相促进，是我们产品经理需要不断探索的艺术。\n\n\n\n### 1.2.3 内容审核\n\n#### 1. 文本内容审核\n\n我们刚刚聊完UGC带来的海量内容，那紧接着一个必须面对的问题就是：如何管理这些内容，确保社区的“干净”？这就是内容审核的范畴，它是维持平台健康生态的生命线。\n\n我们先从最基础、也是应用最广泛的**文本内容审核**开始。\n\n在我的经验里，处理文本内容最直接的方式就是**自动审核**，特别是基于敏感词的过滤系统。这里的基本逻辑非常简单，就像这张图里展示的A到B的过程。\n\n![image-20250718211313829](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718211313829.png)\n\n**第一步（图中的A），是“设置敏感词”**。我会和我的运营、法务同事一起，在平台的后台系统里，共同维护一个“敏感词词库”。这个词库是动态更新的，我们会持续地把涉及色情、暴力、赌博、政治、广告、人身攻击等各类违规的词语加进去。这相当于我们为平台的“安保系统”配置好了要抓捕的“黑名单”。\n\n**第二步（图中的B），是“识别处理”**。当用户发布任何文本内容时，比如一篇帖子、一条评论，我们的系统会自动将这段文本与后台的敏感词库进行秒级匹配。一旦命中词库里的某个词，系统就会立刻执行我们预设好的处理动作。\n\n这个“处理动作”具体是什么，是我们作为产品经理需要精心设计的。简单的用星号（`*`）替换只是其中一种。根据违规词语的严重等级，我通常会设计一套组合策略。我把这些常见的处理方式整理成了一个表格，方便我们理解。\n\n| **处理方式** | **描述** | **我通常应用的场景** |\n| :--- | :--- | :--- |\n| **替换** | 将命中的敏感词替换为`*`等符号，内容本身依然可以发出。 | 对用户体验影响最小，适用于一般性的不文明用语、脏话等。 |\n| **拦截** | 直接阻止该内容的发布，并明确告知用户违规原因。 | 适用于垃圾广告、恶意导流、联系方式等明确的违规行为。 |\n| **仅作者可见** | 内容成功发布，但只有作者自己能看到，同时自动进入人工审核队列。 | 适用于内容疑似违规，但不确定，需要人工介入判断的情况。给用户一种“已发出”的错觉，可以减少其申诉和修改绕过的行为。 |\n| **先审后发** | 内容发布后，不会立即对外展示，必须等待人工审核通过后才可见。 | 适用于高风险场景，如金融、医疗内容的发布，或针对有过多次违规记录的用户。 |\n\n**我的总结：**\n\n我要强调一点，单纯的关键词审核只是万里长征的第一步。现在的用户非常聪明，会用谐音、拆字、加符号等各种方式来绕过审核，比如“V信”“威信”“vievie”等等。因此，在如今的平台上，敏感词系统通常只是作为基础的、第一道防线。更成熟的平台，会在此之上，结合基于机器学习的文本分类模型、用户行为分析等多种技术手段，来构建一个立体、智能的审核系统。\n\n#### 2. 图片/音视频内容审核\n\n如果说文本审核是“普通难度”，那么图片、音频和视频的审核就是“困难模式”。这类非结构化内容的审核，其技术门槛和复杂度要高出几个量级。\n\n在我的职业生涯中，对于这类审核，我的原则非常明确：**除非公司是专做AI视觉技术的，否则我们坚决不自研，而是选择“购买服务”**。\n\n为什么？因为从零到一自研一套图片/音视频识别AI模型，需要顶尖的算法团队、海量的标注数据和强大的计算资源，这对于绝大多数公司来说，投入产出比极低。\n\n![image-20250718211731684](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718211731684.png)\n\n这张截图里的阿里云“图片涉政暴恐识别”服务，就完美印证了我的观点。目前，**阿里、百度、腾讯**等一线云服务大厂，都已经把这项能力做成了非常成熟的商业化服务。我们作为产品开发者，只需要付费调用他们的API接口，就能获得世界一流的审核能力。\n\n这个过程对我们来说，就像是把专业的事交给了最专业的人。我来为你拆解一下我们作为产品经理，在这里需要做的事情：\n\n1.  **技术选型**：我们会对几家主流服务商（如阿里云、腾讯云等）进行评估，对比他们的识别准确率、支持的审核维度、API调用价格、服务稳定性等，选择最适合我们业务的合作伙伴。\n\n2.  **策略制定**：这是我们的核心工作。调用API后，第三方服务会返回给我们一个包含各类标签和“置信度”分数的结果。比如，一张图片可能会返回`{“色情”: 0.98, “暴恐”: 0.15, “广告”: 0.4}`这样的数据。我们需要根据业务的风险容忍度，来制定处理规则。例如：\n    * 色情分数 > 0.95，**直接拦截**。\n    * 广告分数在 0.7-0.9 之间，**转人工审核**。\n    * 所有分数都 < 0.3，**自动通过**。\n    这个阈值的设定，需要我们不断地根据实际运营情况去调整和优化。\n\n3.  **成本控制**：要牢记，——**“需要付费”**。这类服务是按调用次数（如张数）或时长（如分钟）收费的。如果我们的产品有海量的图片/视频上传，这笔审核开销会非常巨大。因此，我必须在产品设计之初，就将这部分成本纳入运营预算，并持续监控其开销。\n\n为了让你更清晰地掌握要点，我整理了下面的表格：\n\n| **维度** | **描述** |\n| :--- | :--- |\n| **核心逻辑** | **不自研底层技术**，通过成熟的第三方云服务API来解决专业问题。 |\n| **主要服务商** | 阿里云、腾讯云、百度智能云、七牛云等。 |\n| **常见审核维度** | 涉黄、涉政、暴恐、广告、灌水、违禁品、公众人物识别等。 |\n| **PM的核心工作** | **技术选型、策略制定（设置阈值）、成本控制**。 |\n\n\n\n\n\n\n\n---\n\n### **1.2.4 内容分发**\n\n在我看来，如果我们把内容生产比作“做饭”，内容审核比作“品控”，那么**内容分发就是“上菜”**。菜做得再好，品控再严，如果不能精准、高效地送到想吃这道菜的食客面前，那一切都是徒劳。\n\n#### **1.内容分发的本质**\n\n所以，内容分发的本质，我总结为一句话：**实现内容与用户之间，最高效、最合适的匹配**。我们作为产品经理，设计的一切分发机制，无论是信息流、推荐位还是热榜，都是为了这个终极目标服务。\n\n#### **2. 常见的内容分发方式**\n\n在产品的不同发展阶段和不同场景下，我会运用不同的分发策略。经过多年的演化，我主要将它们归为以下四种主流方式。\n\n---\n\n##### **一、 编辑分发**\n\n![image-20250718212239607](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212239607.png)\n\n这是最古老、最经典的分发方式，我称之为`“总编辑模式”`。\n\n它的核心是由平台内部的编辑团队，基于其专业判断和平台调性，人工挑选出他们认为的“好内容”，并决定这些内容展示在哪个位置、展示给多少人。我们早期看到的各大门户网站首页，就是最典型的编辑分发。\n\n**我的解读**：\n* **优点**：在于**质量可控**和**价值引导**。在产品初期，我可以通过编辑精选，快速为产品树立起高质量、有调性的品牌形象，告诉用户“我们这里有什么”。\n* **缺点**：是**中心化**的，用户的选择是被动的。并且，它极度依赖编辑的个人能力，成本高、效率低，无法满足海量用户的个性化需求。\n\n---\n\n##### 二、 订阅分发\n\n![image-20250718212322555](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212322555.png)\n\n如果说编辑分发是“平台喂给你什么，你就看什么”，那么订阅分发就是把选择权交还给了用户，我称之为`"报刊亭模式"`。\n\n在这种模式下，用户可以主动“关注”或“订阅”自己感兴趣的创作者（KOL）、专栏或话题。这样，用户就构建了属于自己的信息获取渠道。我们关注微信公众号、B站UP主，都属于订阅分发。\n\n但这种需要平台需要有KOL基础,也就是发展初期是不太适合做订阅分发,难以招揽到大牌博主入驻\n\n**我的解读**：\n\n* **优点**：用户是主动选择，因此粘性非常高，容易围绕KOL构建起`“私域流量”`，用户忠诚度强。\n* **缺点**：对用户的“发现能力”要求高，用户需要自己去找到值得订阅的目标。对于新用户和新创作者来说，冷启动会比较困难。\n\n---\n\n##### **三、 社交分发**\n\n![image-20250718212412442](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212412442.png)\n\n社交分发是一种极具爆发力的分发方式，我称之为``。\n\n内容不再仅仅依赖平台或创作者，而是通过用户自身的社交关系链进行传播。我的朋友在朋友圈分享了一篇文章，我认为有价值，再次转发，这样一传十、十传百地扩散出去。\n\n**我的解读**：\n* **优点**：基于`“信任代理”`，朋友推荐的内容我更愿意相信和打开。传播速度快，容易产生裂变效应，引爆话题。\n* **缺点**：内容分发的广度和速度变得不可控，并且非常依赖内容的“社交属性”，即是否足够有趣、有槽点、有共鸣，能激发用户的分享欲。\n\n---\n\n##### 四、 算法分发\n\n![image-20250718212636209](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212636209.png)\n\n这是当下最主流，也是技术含量最高的方式，我称之为`“私人助理模式”`。\n\n平台通过强大的算法模型，分析用户的历史行为（点击、点赞、停留时长等），理解其兴趣偏好，然后从海量内容池中，为每一个用户“量身定制”一个独一无二的信息流。今日头条和抖音是算法分发的集大成者。\n\n**我的解读**：\n\n* **优点**：**极致的个性化和高效率**。它能让用户“沉迷”其中，因为每一条都是你可能感兴趣的，极大地提升了用户粘性和使用时长。\n* **缺点**：容易形成`“信息茧房”`，让用户视野越来越窄。同时，它对平台的内容总量和用户规模有很高的要求，小平台很难玩转。\n\n为了方便我们整体回顾，我将这四种方式的核心特点总结在了一张表里：\n\n| **分发方式** | **核心逻辑** | **用户角色** | **优点** | **缺点** |\n| :--- | :--- | :--- | :--- | :--- |\n| **编辑分发** | 编辑人工精选 | 被动接收者 | 质量、调性可控 | 效率低、非个性化 |\n| **订阅分发** | 用户主动关注 | 主动选择者 | 粘性高、关系强 | 发现效率低、启动难 |\n| **社交分发** | 好友分享推荐 | 传播节点 | 信任度高、裂变快 | 不可控、依赖内容社交性 |\n| **算法分发** | 机器智能推荐 | 沉浸体验者 | 高效率、个性化 | 易产生信息茧房 |\n\n在我的实际工作中，现代内容产品几乎不会只采用单一的分发模式，而是将这四种方式进行**混合**。比如，一个信息流里，既有我订阅的，也有算法推荐的，还可能夹杂着编辑精选的热点。如何调配好这四种模式的比例和权重，正是我们产品经理需要不断探索和优化的核心策略。\n\n\n### **1.2.5 内容消费**\n\n在我看来，内容产品的商业模式，本质上都是在**内容消费**这个环节做文章。我们将消费模式分为两种最基本的形式：**免费消费**和**付费消费**。它们不是对立的，而往往是共存的、互为补充的。\n\n![image-20250718213151675](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213151675.png)\n\n-----\n\n#### 一、 免费消费模式\n\n![image-20250718213203871](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213203871.png)\n\n这是互联网内容平台的基石，也是我们用来吸引海量用户的核心手段。我始终认为，**免费是为了更好地收费**。\n\n在免费模式下，我们向用户提供大量有价值的免费内容，其核心目的有两个：\n\n1.  **降低用户门槛**：让尽可能多的用户无成本地体验我们的产品，并留存下来。\n2.  **建立信任和展示价值**：通过免费内容，让用户了解我们的“实力”，认可我们平台的价值。\n\n图中那个**漏斗模型**非常形象地表达了我的想法。免费用户就是漏斗最顶端的巨大流量池。我们的工作，就是通过产品设计和运营手段，筛选并引导其中一小部分认可我们价值的用户，一步步地走向漏斗下方的付费环节，完成转化。\n\n-----\n\n#### 二、 付费消费模式\n\n当用户愿意付费时，意味着我们要提供远超免费内容的“超额价值”。什么样的内容才具备这种价值呢？我通常会从以下四个特性去评估。\n\n![image-20250718213242810](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213242810.png)\n\n  * **1. 即时性**：人无我有的第一手信息。比如，付费的财经社群里，比公开市场更早发布的独家分析；或者，粉丝付费后可以比普通用户提前一周看到创作者的最新视频。\n  * **2. 专业性**：高度体系化、结构化的深度知识。这是知识付费最核心的逻辑。用户付费购买的不是零散的知识点，而是一位专家经过系统梳理后的完整知识体系，比如一门精心设计的在线课程。\n  * **3. 个性化服务**：针对个人情况提供的专属服务。比如，付费的一对一咨询、个人化的学习路径规划、有问必答的社群服务等。\n  * **4. 唯一性**：只有这里才能获得的独特资源或体验。比如，某个创作者独家的、不对外公开的创作手稿；或者，只有付费会员才能进入的私密社群。\n\n-----\n\n光有好的付费内容还不够，如何设计付费环节，直接决定了用户的付费转化率。在这方面，我总结了三个必须做到的设计要点。\n\n![image-20250718213331617](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213331617.png)\n\n  * **1. 凸显服务价值**：在用户付费前，我们必须用尽一切办法让他清晰地感知到“我将获得什么”。这包括精美的课程介绍页、详细的课程大纲、来自其他用户的真实好评、免费的试听/试读章节等。价值感塑造得越好，用户的付费意愿就越强。\n  * **2. 明确服务有效期**：这是建立信任的基础。我们必须在最显眼的位置清楚地告诉用户：这次购买是一次性买断、永久有效的？还是按月/按年订阅的？服务的具体范围和期限是什么？任何模糊不清的描述都是在扼杀交易。\n  * **3. 简化支付流程**：用户从下定决心到完成支付的路径，每增加一个步骤，都会流失一批用户。因此，我追求的是让支付流程“如丝般顺滑”。这包括接入主流的支付方式（微信/支付宝）、支持一键支付、减少不必要的表单填写等。\n\n最后，我将内容消费环节的核心思考，总结为下面这张简表，这也是我在做商业化设计时，反复会问自己的两个问题。\n\n| **核心问题** | **我的思考要点** |\n| :--- | :--- |\n| **1. 什么内容值得用户付费？** | 内容必须具备稀缺性，至少符合**即时性、专业性、个性化、唯一性**中的一种。 |\n| **2. 付费功能应该如何设计？** | **价值前置**：让用户未付费就感知到价值。<br>**信息透明**：权益、期限一目了然。<br>**流程极简**：让支付成为一种轻松、无障碍的体验。 |\n\n\n\n\n---'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E7%AB%A0%EF%BC%9A%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E6%A8%A1%E5%9E%8B"><span class="toc-number">1.</span> <span class="toc-text">第一章：内容产品模型</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#1-1-%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E6%A6%82%E8%BF%B0"><span class="toc-number">1.1.</span> <span class="toc-text">1.1 内容产品概述</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.1.</span> <span class="toc-text">1.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-1-2-%E4%BB%80%E4%B9%88%E6%98%AF%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%EF%BC%9F"><span class="toc-number">1.1.2.</span> <span class="toc-text">1.1.2 什么是内容产品？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-1-3-%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E6%9E%B6%E6%9E%84%E4%B8%8E%E7%94%9F%E6%80%81"><span class="toc-number">1.1.3.</span> <span class="toc-text">1.1.3 内容产品架构与生态</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#1-2-%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%9E%8B"><span class="toc-number">1.2.</span> <span class="toc-text">1.2 内容产品设计模型</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.2.1.</span> <span class="toc-text">1.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-2-%E5%86%85%E5%AE%B9%E7%94%9F%E4%BA%A7"><span class="toc-number">1.2.2.</span> <span class="toc-text">1.2.2 内容生产</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-PGC%EF%BC%88Professionally-generated-Content%EF%BC%89"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">1. PGC（Professionally-generated Content）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-UGC%EF%BC%88User-Generated-Content%EF%BC%89"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">2. UGC（User Generated Content）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-3-%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8"><span class="toc-number">1.2.3.</span> <span class="toc-text">1.2.3 内容审核</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%96%87%E6%9C%AC%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">1. 文本内容审核</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9B%BE%E7%89%87-%E9%9F%B3%E8%A7%86%E9%A2%91%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">2. 图片/音视频内容审核</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-4-%E5%86%85%E5%AE%B9%E5%88%86%E5%8F%91"><span class="toc-number">1.2.4.</span> <span class="toc-text">1.2.4 内容分发</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%86%85%E5%AE%B9%E5%88%86%E5%8F%91%E7%9A%84%E6%9C%AC%E8%B4%A8"><span class="toc-number">1.2.4.1.</span> <span class="toc-text">1.内容分发的本质</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E7%9A%84%E5%86%85%E5%AE%B9%E5%88%86%E5%8F%91%E6%96%B9%E5%BC%8F"><span class="toc-number">1.2.4.2.</span> <span class="toc-text">2. 常见的内容分发方式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%80%E3%80%81-%E7%BC%96%E8%BE%91%E5%88%86%E5%8F%91"><span class="toc-number">1.2.4.2.1.</span> <span class="toc-text">一、 编辑分发</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BA%8C%E3%80%81-%E8%AE%A2%E9%98%85%E5%88%86%E5%8F%91"><span class="toc-number">1.2.4.2.2.</span> <span class="toc-text">二、 订阅分发</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%89%E3%80%81-%E7%A4%BE%E4%BA%A4%E5%88%86%E5%8F%91"><span class="toc-number">1.2.4.2.3.</span> <span class="toc-text">三、 社交分发</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9B%9B%E3%80%81-%E7%AE%97%E6%B3%95%E5%88%86%E5%8F%91"><span class="toc-number">1.2.4.2.4.</span> <span class="toc-text">四、 算法分发</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#1-2-5-%E5%86%85%E5%AE%B9%E6%B6%88%E8%B4%B9"><span class="toc-number">1.2.5.</span> <span class="toc-text">1.2.5 内容消费</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%B8%80%E3%80%81-%E5%85%8D%E8%B4%B9%E6%B6%88%E8%B4%B9%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.2.5.1.</span> <span class="toc-text">一、 免费消费模式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%BA%8C%E3%80%81-%E4%BB%98%E8%B4%B9%E6%B6%88%E8%B4%B9%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.2.5.2.</span> <span class="toc-text">二、 付费消费模式</span></a></li></ol></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>