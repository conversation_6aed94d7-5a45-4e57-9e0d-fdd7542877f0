- const { placeholder, docsearch: { appId, apiKey, indexName, option } } = theme.search

.docsearch-wrap
  #docsearch(style="display:none")
  script(src=url_for(theme.cdn.docsearch_js))
  script.
    (() => {
      docsearch(Object.assign({
        appId: '!{appId}',
        apiKey: '!{apiKey}',
        indexName: '!{indexName}',
        container: '#docsearch',
        placeholder: '!{ placeholder || _p("search.input_placeholder")}',
      }, !{JSON.stringify(option)}))

      const handleClick = () => {
        document.querySelector('.DocSearch-Button').click()
      }

      const searchClickFn = () => {
        utils.addEventListenerPjax(document.querySelector('#search-button > .search'), 'click', handleClick)
      }

      searchClickFn()
      window.addEventListener('pjax:complete', searchClickFn)
    })()