extends includes/layout.pug

block content
  if theme.category_ui == 'index'
    include ./includes/mixins/post-ui.pug
    #recent-posts.recent-posts.category_ui
      +postUI
      include includes/pagination.pug
  else
    include ./includes/mixins/article-sort.pug
    #category
      #catalog-bar
        i.anzhiyufont.anzhiyu-icon-shapes
        #catalog-list
          .catalog-list-item
            a(href="/") 首页
          !=catalog_list("categories")
        .category-bar-next#category-bar-next(onclick="anzhiyu.scrollCategoryBarToRight()")
          i.anzhiyufont.anzhiyu-icon-angle-double-right
        a.catalog-more(href="/categories/")!= '更多'
      .article-sort-title= _p('page.category') + ' - ' + page.category
      +articleSort(page.posts, page.current)
      include includes/pagination.pug