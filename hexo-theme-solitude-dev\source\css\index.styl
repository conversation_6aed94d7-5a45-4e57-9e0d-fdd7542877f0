@charset "UTF-8"

if hexo-config('css_prefix')
  @import 'nib'

@import "third_party/snackbar.min.css"
// project
@import "var.styl"
@import '_global/*'
@import '_layout/*'
@import '_page/index.styl'
@import '_post/index.styl'
@import '_mode/*'

// highlight
@import '_highlight/index'

// search
if hexo-config('search.enable')
  if hexo-config('search.type') == 'algolia'
    @import '_search/algolia-search'
  else if hexo-config('search.type') == 'local'
    @import '_search/local-search'

// tags plugin
@import "_tags/*"

// comment
if hexo-config('comment.use')
  @import '_comments/comment.styl'

// components
@import '_components/index'