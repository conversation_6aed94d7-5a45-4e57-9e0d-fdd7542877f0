script.
    (() => {
        const changeContent = (content) => {
            if (content === '') return content;

            const replacements = [
                {regex: /<img.*?src="(.*?)"?[^\>]+>/ig, replacement: '[!{_p("console.newest_comment.image")}]'},
                {
                    regex: /<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi,
                    replacement: '[!{_p("console.newest_comment.link")}]'
                },
                {regex: /<pre><code>.*?<\/pre>/gi, replacement: '[!{_p("console.newest_comment.code")}]'},
                {regex: /<[^>]+>/g, replacement: ""}
            ];

            content = replacements.reduce((str, {regex, replacement}) => str.replace(regex, replacement), content);

            return content.length > 150 ? content.substring(0, 100) + '...' : content;
        }

        const $asideList = document.querySelector('.card-recent-comment .aside-list')
        const newestCommentInit = () => {
            if (!document.querySelector('.card-recent-comment')) return
            const data = utils.saveToLocal.get('waline-newest-comment')
            if (data) {
                generateHtml(data)
            } else {
                getComment()
            }
        }

        const getComment = async () => {
            await fetch('!{theme.waline.envId}/api/comment?type=recent&count=!{limit}', {method: 'GET'}).then(async res => {
                const result = await res.json()
                const walineArray = result.data.map(e => {
                    return {
                        'content': changeContent(e.comment),
                        'avatar': e.avatar,
                        'nick': e.nick,
                        'url': e.url + '#' + e.objectId,
                        'date': new Date(e.time || e.insertedAt)
                    }
                })
                utils.saveToLocal.set('waline-newest-comment', walineArray, !{theme.comment.newest_comment.storage})
                generateHtml(walineArray)
            }).catch(error => {
                console.error(error)
                $asideList.textContent = "!{_p('newest_comment.error')}"
            })
        }

        const generateHtml = array => {
            const $dom = document.querySelector('.card-recent-comment .aside-list')
            $dom.innerHTML = array.length ? array.map(item => `
                <div class='aside-list-item'>
                  <div onclick='pjax.loadUrl("${item.url}")' class='thumbnail'>
                    <img src='${item.avatar}' alt='${item.nick}'>
                    <div class='name'><span>${item.nick}</span></div>
                  </div>
                  <div class='content'>
                    <div class='comment' onclick='pjax.loadUrl("${item.url}")'>${item.content}</div>
                    <time class="datetime" datetime="${item.date}"></time>
                  </div>
                </div>
              `).join('') : "!{_p('newest_comment.zero')}"
            window.lazyLoadInstance?.update()
            window.pjax?.refresh()
            sco?.changeTimeFormat(document.querySelectorAll('.aside-list-item time'))
        }

        window.addEventListener('DOMContentLoaded', newestCommentInit, false)
        window.addEventListener('pjax:complete', newestCommentInit, false);
    })()