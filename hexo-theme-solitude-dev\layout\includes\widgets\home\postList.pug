mixin cover
    a(href=url_for(post.path), title=post.title)
        img.post_bg(src=url_for(post.cover), alt=post.title)

mixin info
    div.recent-post-info-top
        div.recent-post-info-top-tips
            if post.sticky && is_home()
                span.article-meta.sticky-warp
                    i.solitude.fas.fa-thumbtack.sticky.orange
                    span.sticky.orange= _p('home.sticky')
            each category in post.categories.data
                span.original= category.name

            if post.prev == null && is_home()
                span.original= _p('home.new')

            a.unvisited-post(href=url_for(post.path))= _p('home.read')

        a.article-title(href=url_for(post.path), title=post.title)= post.title

    div.article-meta-wrap
        span.article-meta.tags
            each tag in post.tags.data
                a.article-meta__tags(href=url_for(tag.path), onclick="event.stopPropagation();")
                    span.tags-punctuation
                        i.solitude.fas.fa-hashtag
                        =tag.name

        span.post-meta-date
            time(datetime=moment(post.date))

- const { direction, cover } = theme.index_post_list

if direction === 'row'
    - var position = cover === 'both' ? index % 2 === 0 ? 0 : 1 : cover === 'left' ? 0 : 1

div.recent-post-item(onclick="pjax.loadUrl('" + url_for(post.path) + "')")
    if direction === 'column'
        div.post_cover
            +cover()
        div.recent-post-info
            +info()
    else
        case position
            when 0
                if post.cover
                    div.post_cover
                        +cover()
                div.recent-post-info
                    +info()
            when 1
                div.recent-post-info
                    +info()
                if post.cover
                    div.post_cover
                        +cover()


if theme.google_adsense && theme.google_adsense.enable && theme.google_adsense.auto_ads === false && theme.google_adsense.post_card
    - var ads_height = theme.index_post_list.direction === 'column' ? '401px;' : '280px;'
    if (index + 1) % 3 === 0
        div.recent-post-item.google-ads-warp
            ins.adsbygoogle(style="display:block; text-align:center; min-width: 100%; height:" + ads_height, data-ad-layout="in-article", data-ad-format="fluid", hide-unfilled="true", data-ad-client=theme.google_adsense.client, data-ad-slot=theme.google_adsense.slot)
            script.
                (adsbygoogle = window.adsbygoogle || []).push({});
