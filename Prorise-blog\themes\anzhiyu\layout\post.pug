extends includes/layout.pug

block content
  #post
    if top_img === false || page.bilibili_bg
      include includes/header/post-info.pug
    if theme.post_head_ai_description
      include includes/anzhiyu/ai-info.pug
    - let url = page.copyright_url || page.permalink
    article#article-container.post-content(itemscope itemtype=url_for(url))
      header
        if (theme.post_meta.post.categories && page.categories && page.categories.data.length > 0)
          each item, index in page.categories.data
            a(href=url_for(item.path) itemprop="url").post-meta-categories #[=item.name]
        if (theme.post_meta.page.tags)
          each item, index in page.tags.data
            a(href=url_for(item.path) tabindex="-1" itemprop="url")=item.name
        h1#CrawlerTitle(itemprop="name headline")= page.title || _p('no_title')
        - let author = page.copyright_author || config.author
        span(itemprop="author" itemscope itemtype="http://schema.org/Person")=author
        if (theme.post_meta.post.date_type)
          if (theme.post_meta.post.date_type === 'both')
            time(itemprop="dateCreated datePublished" datetime=date_xml(page.date) title=_p('post.created') + ' ' + full_date(page.date))=date(page.date, config.date_format)
            time(itemprop="dateCreated datePublished" datetime=date_xml(page.updated) title=_p('post.updated') + ' ' + full_date(page.updated))=date(page.updated, config.date_format)
          else
            - let data_type_update = theme.post_meta.post.date_type === 'updated'
            - let date_type = data_type_update ? 'updated' : 'date'
            time(itemprop="dateCreated datePublished" datetime=date_xml(page[date_type]) title=date_title + ' ' + full_date(page[date_type]))=date(page[date_type], config.date_format)
      !=page.content

    include includes/post/post-copyright.pug

    //- ad
    if theme.ad && theme.ad.post
      .ads-wrap!=theme.ad.post

    if theme.post_pagination
      include includes/pagination.pug
    if theme.related_post && theme.related_post.enable
      != related_posts(page,site.posts)

    if page.comments !== false && theme.comments && theme.comments.use
      - var commentsJsLoad = true
      !=partial('includes/third-party/comments/index', {}, {cache: true})

    //- 文章源文件下载功能：嵌入文章数据
    script.
      window.postData = {
        title: "#{page.title || ''}",
        date: "#{page.date ? page.date.format('YYYY-MM-DD HH:mm:ss') : ''}",
        updated: "#{page.updated ? page.updated.format('YYYY-MM-DD HH:mm:ss') : (page.date ? page.date.format('YYYY-MM-DD HH:mm:ss') : '')}",
        tags: !{JSON.stringify(page.tags ? page.tags.map(tag => tag.name) : [])},
        categories: !{JSON.stringify(page.categories ? page.categories.map(cat => cat.name) : [])},
        content: !{JSON.stringify(page._content || '')}
      };
      