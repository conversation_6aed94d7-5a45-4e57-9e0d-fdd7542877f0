<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>1.首页主题魔改：顶部视频播放 | Prorise的小站</title><meta name="keywords" content="博客搭建教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="1.首页主题魔改：顶部视频播放"><meta name="application-name" content="1.首页主题魔改：顶部视频播放"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="1.首页主题魔改：顶部视频播放"><meta property="og:url" content="https://prorise666.site/posts/22096.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="1.首页主题魔改：顶部视频播放功能介绍与重要提示本指南将主题的静态首页顶部图，升级为一个支持视频背景、能够根据横竖屏自适应切换、并带有视差和滚动渐变等高级特效的动态媒体背景。  警告： 这是一项“魔改”级别的定制，涉及到覆盖主题的核心模板文件。在开始前，强烈建议您备份整个 themes&amp;#x2F;anzhiy"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"><meta name="description" content="1.首页主题魔改：顶部视频播放功能介绍与重要提示本指南将主题的静态首页顶部图，升级为一个支持视频背景、能够根据横竖屏自适应切换、并带有视差和滚动渐变等高级特效的动态媒体背景。  警告： 这是一项“魔改”级别的定制，涉及到覆盖主题的核心模板文件。在开始前，强烈建议您备份整个 themes&amp;#x2F;anzhiy"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/22096.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"1.首页主题魔改：顶部视频播放",postAI:"true",pageFillDescription:"1.首页主题魔改：顶部视频播放, 功能介绍与重要提示, 第一步：替换核心布局文件, 第二步：添加自定义样式与脚本文件, 第三步：修改主题配置并注入新文件首页主题魔改顶部视频播放功能介绍与重要提示本指南将主题的静态首页顶部图升级为一个支持视频背景能够根据横竖屏自适应切换并带有视差和滚动渐变等高级特效的动态媒体背景警告这是一项魔改级别的定制涉及到覆盖主题的核心模板文件在开始前强烈建议您备份整个文件夹以防出现意外情况可以随时恢复第一步替换核心布局文件此修改需要替换掉主题中负责渲染页面头部的核心文件找到并打开文件替换内容将这个文件的全部内容用下面提供的完整代码进行覆盖优先级控制逻辑独立作用域首页专用媒体声明路径处理首页专用路径媒体容器第二步添加自定义样式与脚本文件创建文件在目录下新建一个文件命名为将下面的代码完整复制进去改为固定定位添加底部向上渐变遮罩同步改为固定定位添加透明度过渡自定义加载动画容器确保在视频上方防止阻挡视频交互淡出动画加载动画元素修复尺寸问题修复改为全屏覆盖模式改为确保铺满屏幕移动端优化移动端也保持模式呼吸动画效果创建文件在目录下新建一个文件命名为横竖屏自适应背景媒体加载器记录上一次的方向状态防止重复初始化的状态锁新增滚动渐变效果函数节流函数优化性能处理滚动时的透明度变化计算透明度从完全不透明到完全透明当滚动到一屏高度时透明度变为限制在范围节流处理滚动事件每检查一次添加滚动监听初始化时执行一次存储当前滚动处理器以便后续移除滚动渐变效果函数结束新增底部遮罩层控制函数节流函数优化性能处理滚动时的遮罩变化计算遮罩高度动态设置遮罩层高度节流处理滚动事件每检查一次添加滚动监听初始化时执行一次返回处理器以便后续移除检查是否正在初始化如果正在初始化则直接退出防止冲突加锁解锁检测当前屏幕方向如果方向未改变则直接返回更新方向记录清除现有媒体元素和加载动画根据方向选择资源背景加载器未找到有效媒体资源背景加载器使用资源类型创建媒体元素设置初始透明度在媒体容器添加媒体元素后调用效果函数添加新功能背景加载器媒体元素已创建创建自定义加载动画容器创建加载动画元素设置加载动画样式使用视频特殊处理增强循环播放机制备用处理背景加载器视频播放结束重新开始播放重新播放失败多源支持处理自动播放限制背景加载器自动播放被阻止视频加载完成后移除加载动画淡出动画持续时间解锁图片加载完成后移除加载动画解锁错误处理背景加载器资源加载失败解锁尝试回退到备用类型背景加载器尝试回退到备用媒体背景加载器使用备用资源背景加载器媒体元素已创建初始化滚动渐变效果获取当前方向竖屏模式下固定放大检测是否为设备如果是设备直接禁用所有视差效果视差效果在设备上禁用所有视差效果直接返回不初始化任何视差效果添加缩放动画效果初始放大在视频加载完成后触发缩放动画竖屏模式保持缩放不需要动画横屏模式执行缩放动画到正常大小添加视差效果鼠标陀螺仪视差效果参数新增陀螺仪支持检测陀螺仪支持需要权限和其他支持设备设置陀螺仪监听处理陀螺仪数据竖屏模式使用基础缩放获取陀螺仪数据前后倾斜左右倾斜前后倾斜到左右倾斜到将角度转换为百分比偏移归一化处理到应用视差效果鼠标视差效果根据设备类型初始化检测移动设备移动设备优先使用陀螺仪不支持陀螺仪则回退到触摸事件设备使用鼠标事件触摸事件回退方案移动强度减半缩放强度减半性能优化页面不可见时暂停陀螺仪在函数中调用新功能添加调用添加调用执行入口防抖处理窗口变化计算当前方向状态只有方向实际改变时才执行重载背景加载器窗口大小变化重新加载媒体背景加载器窗口大小变化但方向未改变方向未变时重置透明度页面可见性变化处理背景加载器页面恢复可见重新播放视频视频恢复播放失败页面恢复可见时重置透明度新增修复代码直接加在现有代码后面缓存恢复检测核心修复修复检测到缓存恢复主页强制重置缓存恢复时重置透明度路由变化监听兼容修复检测到返回主页检查媒体元素是否存在返回主页时重置透明度延迟确保更新媒体状态自检兜底方案修复自检发现媒体丢失媒体自检时重置透明度每秒检查一次轻量级检测第三步修改主题配置并注入新文件打开主题配置文件修改首页顶部图配置找到配置项将其删除或注释掉然后添加下面这个新的和结构首页媒体配置注意和的只能有一个为如果想用图片背景请将此项设为并将下面的的设为您的横屏图片链接您的竖屏图片链接为竖屏设备显示的图片启用视频背景您的横屏视频链接例如您的横屏视频加载动画链接视频加载出来前的占位图您的竖屏视频链接您的竖屏视频加载动画链接注入新添加的和文件在同一个主题配置文件中找到部分在列表的末尾添加一行用于引入我们新建的和文件其他内容其他内容注意为便于主题管理我们放在了和中并在这里使用对应的路径引入",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-19 19:25:44",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E9%A6%96%E9%A1%B5%E4%B8%BB%E9%A2%98%E9%AD%94%E6%94%B9%EF%BC%9A%E9%A1%B6%E9%83%A8%E8%A7%86%E9%A2%91%E6%92%AD%E6%94%BE"><span class="toc-text">1.首页主题魔改：顶部视频播放</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D%E4%B8%8E%E9%87%8D%E8%A6%81%E6%8F%90%E7%A4%BA"><span class="toc-text">功能介绍与重要提示</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E6%9B%BF%E6%8D%A2%E6%A0%B8%E5%BF%83%E5%B8%83%E5%B1%80%E6%96%87%E4%BB%B6"><span class="toc-text">第一步：替换核心布局文件</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E6%B7%BB%E5%8A%A0%E8%87%AA%E5%AE%9A%E4%B9%89%E6%A0%B7%E5%BC%8F%E4%B8%8E%E8%84%9A%E6%9C%AC%E6%96%87%E4%BB%B6"><span class="toc-text">第二步：添加自定义样式与脚本文件</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E4%BF%AE%E6%94%B9%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E5%B9%B6%E6%B3%A8%E5%85%A5%E6%96%B0%E6%96%87%E4%BB%B6"><span class="toc-text">第三步：修改主题配置并注入新文件</span></a></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/" itemprop="url">魔改</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>博客搭建教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">1.首页主题魔改：顶部视频播放</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-10T08:13:45.000Z" title="发表于 2025-07-10 16:13:45">2025-07-10</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-19T11:25:44.271Z" title="更新于 2025-07-19 19:25:44">2025-07-19</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">4.1k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>19分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="1.首页主题魔改：顶部视频播放"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/22096.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/22096.html"><header><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/" itemprop="url">魔改</a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">博客搭建教程</a><h1 id="CrawlerTitle" itemprop="name headline">1.首页主题魔改：顶部视频播放</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-10T08:13:45.000Z" title="发表于 2025-07-10 16:13:45">2025-07-10</time><time itemprop="dateCreated datePublished" datetime="2025-07-19T11:25:44.271Z" title="更新于 2025-07-19 19:25:44">2025-07-19</time></header><div id="postchat_postcontent"><h3 id="1-首页主题魔改：顶部视频播放"><a href="#1-首页主题魔改：顶部视频播放" class="headerlink" title="1.首页主题魔改：顶部视频播放"></a><strong>1.首页主题魔改：顶部视频播放</strong></h3><h6 id="功能介绍与重要提示"><a href="#功能介绍与重要提示" class="headerlink" title="功能介绍与重要提示"></a><strong>功能介绍与重要提示</strong></h6><p>本指南将主题的静态首页顶部图，升级为一个支持<strong>视频背景</strong>、能够根据<strong>横竖屏自适应切换</strong>、并带有<strong>视差和滚动渐变</strong>等高级特效的动态媒体背景。</p><blockquote><p><strong>警告：</strong> 这是一项“魔改”级别的定制，涉及到覆盖主题的核心模板文件。在开始前，<strong>强烈建议您备份整个 <code>themes/anzhiyu</code> 文件夹</strong>，以防出现意外情况可以随时恢复。</p></blockquote><hr><h6 id="第一步：替换核心布局文件"><a href="#第一步：替换核心布局文件" class="headerlink" title="第一步：替换核心布局文件"></a><strong>第一步：替换核心布局文件</strong></h6><p>此修改需要替换掉主题中负责渲染页面头部的核心文件。</p><ol><li><p><strong>找到并打开文件</strong>：<br><code>themes/anzhiyu/layout/includes/header/index.pug</code></p></li><li><p><strong>替换内容</strong>：<br>将这个文件的<strong>全部内容</strong>，用下面提供的<strong>完整代码</strong>进行覆盖。</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br></pre></td><td class="code"><pre><span class="line">// 优先级控制逻辑（独立作用域）</span><br><span class="line">if !theme.disable_top_img &amp;&amp; page.top_img !== false</span><br><span class="line">  if is_post()</span><br><span class="line">    - var top_img = page.top_img || page.cover || page.randomcover</span><br><span class="line">  else if is_page()</span><br><span class="line">    - var top_img = page.top_img || theme.default_top_img</span><br><span class="line">  else if is_home()</span><br><span class="line">    // 首页专用媒体声明</span><br><span class="line">    - var home_index_img = theme.index_img?.enable ? theme.index_img.path : false</span><br><span class="line">    - var home_index_video = theme.index_video?.enable ? theme.index_video.path : false</span><br><span class="line">    - var top_img = home_index_img || home_index_video || theme.default_top_img</span><br><span class="line">  else</span><br><span class="line">    - var top_img = page.top_img || theme.default_top_img</span><br><span class="line"></span><br><span class="line">  if top_img !== false</span><br><span class="line">    // 路径处理</span><br><span class="line">    - var imgSource = top_img &amp;&amp; top_img.indexOf('/') !== -1 ? url_for(top_img) : top_img</span><br><span class="line">    // 首页专用路径</span><br><span class="line">    - var homeImg = home_index_img ? url_for(home_index_img) : ''</span><br><span class="line">    - var homeVideo = home_index_video ? url_for(home_index_video) : ''</span><br><span class="line">    - var bg_img = is_home() ? (home_index_img || home_index_video) : imgSource</span><br><span class="line"></span><br><span class="line">    - var site_title = page.title || page.tag || page.category || config.title</span><br><span class="line">    - var isHomeClass = is_home() ? 'full_page' : 'not-home-page'</span><br><span class="line">    - is_post() ? isHomeClass = 'post-bg' : isHomeClass</span><br><span class="line">  else</span><br><span class="line">    - var isHomeClass = 'not-top-img'</span><br><span class="line">else</span><br><span class="line">  - var top_img = false</span><br><span class="line">  - var isHomeClass = 'not-top-img'</span><br><span class="line"></span><br><span class="line">header#page-header(class=`${isHomeClass}`)</span><br><span class="line">  !=partial('includes/header/nav', {}, {cache: true})</span><br><span class="line">  if top_img !== false</span><br><span class="line">    if is_post()</span><br><span class="line">      if page.bilibili_bg</span><br><span class="line">        !=partial('includes/bili-banner/index')</span><br><span class="line">      else</span><br><span class="line">        include ./post-info.pug</span><br><span class="line">        if theme.dynamicEffect &amp;&amp; theme.dynamicEffect.postTopWave</span><br><span class="line">          section.main-hero-waves-area.waves-area</span><br><span class="line">            svg.waves-svg(xmlns='http://www.w3.org/2000/svg', xlink='http://www.w3.org/1999/xlink', viewBox='0 24 150 28', preserveAspectRatio='none', shape-rendering='auto')</span><br><span class="line">              defs</span><br><span class="line">                path#gentle-wave(d='M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z')</span><br><span class="line">              g.parallax</span><br><span class="line">                use(href='#gentle-wave', x='48', y='0')</span><br><span class="line">                use(href='#gentle-wave', x='48', y='3')</span><br><span class="line">                use(href='#gentle-wave', x='48', y='5')</span><br><span class="line">                use(href='#gentle-wave', x='48', y='7')</span><br><span class="line">      #post-top-cover</span><br><span class="line">        img#post-top-bg(class='nolazyload' src=bg_img)</span><br><span class="line">    else if is_home()</span><br><span class="line">      // 媒体容器</span><br><span class="line">      #home-media-container(</span><br><span class="line">        data-landscape-img=home_index_img ? homeImg : ''</span><br><span class="line">        data-portrait-img=home_index_img &amp;&amp; theme.index_img.vpath ? url_for(theme.index_img.vpath) : ''</span><br><span class="line">        data-landscape-video=home_index_video ? homeVideo : ''</span><br><span class="line">        data-portrait-video=home_index_video &amp;&amp; theme.index_video.vpath ? url_for(theme.index_video.vpath) : ''</span><br><span class="line">        data-landscape-poster=home_index_video &amp;&amp; theme.index_video.poster ? url_for(theme.index_video.poster) : ''</span><br><span class="line">        data-portrait-poster=home_index_video &amp;&amp; theme.index_video.vposter ? url_for(theme.index_video.vposter) : ''</span><br><span class="line">        style="height:100%;background-attachment:fixed;z-index:0"</span><br><span class="line">      )</span><br><span class="line">      #site-info</span><br><span class="line">        h1#site-title=site_title</span><br><span class="line">        if theme.subtitle.enable</span><br><span class="line">          - var loadSubJs = true</span><br><span class="line">          #site-subtitle</span><br><span class="line">            span#subtitle</span><br><span class="line">        if(theme.social)</span><br><span class="line">          #site_social_icons</span><br><span class="line">            !=fragment_cache('social', function(){return partial('includes/header/social')})</span><br><span class="line">      #scroll-down</span><br><span class="line">        i.anzhiyufont.anzhiyu-icon-angle-down.scroll-down-effects</span><br><span class="line">    else</span><br><span class="line">      #page-site-info(style=`background-image: url(${imgSource})`)</span><br><span class="line">        h1#site-title=site_title</span><br></pre></td></tr></tbody></table></figure></li></ol><hr><h6 id="第二步：添加自定义样式与脚本文件"><a href="#第二步：添加自定义样式与脚本文件" class="headerlink" title="第二步：添加自定义样式与脚本文件"></a><strong>第二步：添加自定义样式与脚本文件</strong></h6><ol><li><p><strong>创建CSS文件</strong></p><ul><li>在 <code>themes/anzhiyu/source/css/</code> 目录下，新建一个文件，命名为 <code>index_media.css</code>。</li><li>将下面的CSS代码完整复制进去：</li></ul><figure class="highlight css"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/* index */</span></span><br><span class="line"></span><br><span class="line"><span class="selector-id">#home-media-container</span> {</span><br><span class="line">  <span class="attribute">position</span>: fixed; <span class="comment">/* 改为固定定位 */</span></span><br><span class="line">  <span class="attribute">top</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">left</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">height</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">overflow</span>: hidden;</span><br><span class="line">  <span class="attribute">z-index</span>: <span class="number">0</span>;</span><br><span class="line">  </span><br><span class="line">  <span class="comment">/* 添加底部向上渐变遮罩 */</span></span><br><span class="line">  -webkit-<span class="attribute">mask-image</span>: <span class="built_in">linear-gradient</span>(to top, transparent <span class="number">0%</span>, black <span class="number">0%</span>);</span><br><span class="line">  <span class="attribute">mask-image</span>: <span class="built_in">linear-gradient</span>(to top, transparent <span class="number">0%</span>, black <span class="number">0%</span>);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.home-media</span> {</span><br><span class="line">  <span class="attribute">position</span>: fixed; <span class="comment">/* 同步改为固定定位 */</span></span><br><span class="line">  <span class="attribute">top</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">left</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">height</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">object-fit</span>: cover;</span><br><span class="line">  </span><br><span class="line">  <span class="comment">/* 添加透明度过渡 */</span></span><br><span class="line">  <span class="attribute">transition</span>: opacity <span class="number">0.5s</span> ease;</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">1</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 自定义加载动画容器 */</span></span><br><span class="line"><span class="selector-class">.custom-loader</span> {</span><br><span class="line">  <span class="attribute">position</span>: absolute;</span><br><span class="line">  <span class="attribute">top</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">left</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">height</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">  <span class="attribute">justify-content</span>: center;</span><br><span class="line">  <span class="attribute">align-items</span>: center;</span><br><span class="line">  <span class="attribute">z-index</span>: <span class="number">10</span>; <span class="comment">/* 确保在视频上方 */</span></span><br><span class="line">  <span class="attribute">pointer-events</span>: none; <span class="comment">/* 防止阻挡视频交互 */</span></span><br><span class="line">  <span class="attribute">transition</span>: opacity <span class="number">0.5s</span> ease; <span class="comment">/* 淡出动画 */</span></span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 加载动画元素 - 修复尺寸问题 */</span></span><br><span class="line"><span class="selector-class">.loader-animation</span> {</span><br><span class="line">  <span class="comment">/* 修复：改为全屏覆盖模式 */</span></span><br><span class="line">  <span class="attribute">position</span>: absolute;</span><br><span class="line">  <span class="attribute">top</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">left</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">height</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">background-size</span>: cover; <span class="comment">/* 改为cover确保铺满屏幕 */</span></span><br><span class="line">  <span class="attribute">background-position</span>: center;</span><br><span class="line">  <span class="attribute">background-repeat</span>: no-repeat;</span><br><span class="line">  <span class="attribute">animation</span>: pulse <span class="number">1.5s</span> infinite ease-in-out;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 移动端优化 */</span></span><br><span class="line"><span class="keyword">@media</span> screen <span class="keyword">and</span> (<span class="attribute">max-width</span>: <span class="number">768px</span>) {</span><br><span class="line">  <span class="selector-class">.loader-animation</span> {</span><br><span class="line">    <span class="attribute">background-size</span>: cover; <span class="comment">/* 移动端也保持cover模式 */</span></span><br><span class="line">  }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 呼吸动画效果 */</span></span><br><span class="line"><span class="keyword">@keyframes</span> pulse {</span><br><span class="line">  <span class="number">0%</span> { <span class="attribute">transform</span>: <span class="built_in">scale</span>(<span class="number">1</span>); <span class="attribute">opacity</span>: <span class="number">0.8</span>; }</span><br><span class="line">  <span class="number">50%</span> { <span class="attribute">transform</span>: <span class="built_in">scale</span>(<span class="number">1.02</span>); <span class="attribute">opacity</span>: <span class="number">1</span>; }</span><br><span class="line">  <span class="number">100%</span> { <span class="attribute">transform</span>: <span class="built_in">scale</span>(<span class="number">1</span>); <span class="attribute">opacity</span>: <span class="number">0.8</span>; }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>创建JS文件</strong></p><ul><li>在 <code>themes/anzhiyu/source/js/</code> 目录下，新建一个文件，命名为 <code>index_media.js</code>。</li></ul><figure class="highlight js"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br><span class="line">197</span><br><span class="line">198</span><br><span class="line">199</span><br><span class="line">200</span><br><span class="line">201</span><br><span class="line">202</span><br><span class="line">203</span><br><span class="line">204</span><br><span class="line">205</span><br><span class="line">206</span><br><span class="line">207</span><br><span class="line">208</span><br><span class="line">209</span><br><span class="line">210</span><br><span class="line">211</span><br><span class="line">212</span><br><span class="line">213</span><br><span class="line">214</span><br><span class="line">215</span><br><span class="line">216</span><br><span class="line">217</span><br><span class="line">218</span><br><span class="line">219</span><br><span class="line">220</span><br><span class="line">221</span><br><span class="line">222</span><br><span class="line">223</span><br><span class="line">224</span><br><span class="line">225</span><br><span class="line">226</span><br><span class="line">227</span><br><span class="line">228</span><br><span class="line">229</span><br><span class="line">230</span><br><span class="line">231</span><br><span class="line">232</span><br><span class="line">233</span><br><span class="line">234</span><br><span class="line">235</span><br><span class="line">236</span><br><span class="line">237</span><br><span class="line">238</span><br><span class="line">239</span><br><span class="line">240</span><br><span class="line">241</span><br><span class="line">242</span><br><span class="line">243</span><br><span class="line">244</span><br><span class="line">245</span><br><span class="line">246</span><br><span class="line">247</span><br><span class="line">248</span><br><span class="line">249</span><br><span class="line">250</span><br><span class="line">251</span><br><span class="line">252</span><br><span class="line">253</span><br><span class="line">254</span><br><span class="line">255</span><br><span class="line">256</span><br><span class="line">257</span><br><span class="line">258</span><br><span class="line">259</span><br><span class="line">260</span><br><span class="line">261</span><br><span class="line">262</span><br><span class="line">263</span><br><span class="line">264</span><br><span class="line">265</span><br><span class="line">266</span><br><span class="line">267</span><br><span class="line">268</span><br><span class="line">269</span><br><span class="line">270</span><br><span class="line">271</span><br><span class="line">272</span><br><span class="line">273</span><br><span class="line">274</span><br><span class="line">275</span><br><span class="line">276</span><br><span class="line">277</span><br><span class="line">278</span><br><span class="line">279</span><br><span class="line">280</span><br><span class="line">281</span><br><span class="line">282</span><br><span class="line">283</span><br><span class="line">284</span><br><span class="line">285</span><br><span class="line">286</span><br><span class="line">287</span><br><span class="line">288</span><br><span class="line">289</span><br><span class="line">290</span><br><span class="line">291</span><br><span class="line">292</span><br><span class="line">293</span><br><span class="line">294</span><br><span class="line">295</span><br><span class="line">296</span><br><span class="line">297</span><br><span class="line">298</span><br><span class="line">299</span><br><span class="line">300</span><br><span class="line">301</span><br><span class="line">302</span><br><span class="line">303</span><br><span class="line">304</span><br><span class="line">305</span><br><span class="line">306</span><br><span class="line">307</span><br><span class="line">308</span><br><span class="line">309</span><br><span class="line">310</span><br><span class="line">311</span><br><span class="line">312</span><br><span class="line">313</span><br><span class="line">314</span><br><span class="line">315</span><br><span class="line">316</span><br><span class="line">317</span><br><span class="line">318</span><br><span class="line">319</span><br><span class="line">320</span><br><span class="line">321</span><br><span class="line">322</span><br><span class="line">323</span><br><span class="line">324</span><br><span class="line">325</span><br><span class="line">326</span><br><span class="line">327</span><br><span class="line">328</span><br><span class="line">329</span><br><span class="line">330</span><br><span class="line">331</span><br><span class="line">332</span><br><span class="line">333</span><br><span class="line">334</span><br><span class="line">335</span><br><span class="line">336</span><br><span class="line">337</span><br><span class="line">338</span><br><span class="line">339</span><br><span class="line">340</span><br><span class="line">341</span><br><span class="line">342</span><br><span class="line">343</span><br><span class="line">344</span><br><span class="line">345</span><br><span class="line">346</span><br><span class="line">347</span><br><span class="line">348</span><br><span class="line">349</span><br><span class="line">350</span><br><span class="line">351</span><br><span class="line">352</span><br><span class="line">353</span><br><span class="line">354</span><br><span class="line">355</span><br><span class="line">356</span><br><span class="line">357</span><br><span class="line">358</span><br><span class="line">359</span><br><span class="line">360</span><br><span class="line">361</span><br><span class="line">362</span><br><span class="line">363</span><br><span class="line">364</span><br><span class="line">365</span><br><span class="line">366</span><br><span class="line">367</span><br><span class="line">368</span><br><span class="line">369</span><br><span class="line">370</span><br><span class="line">371</span><br><span class="line">372</span><br><span class="line">373</span><br><span class="line">374</span><br><span class="line">375</span><br><span class="line">376</span><br><span class="line">377</span><br><span class="line">378</span><br><span class="line">379</span><br><span class="line">380</span><br><span class="line">381</span><br><span class="line">382</span><br><span class="line">383</span><br><span class="line">384</span><br><span class="line">385</span><br><span class="line">386</span><br><span class="line">387</span><br><span class="line">388</span><br><span class="line">389</span><br><span class="line">390</span><br><span class="line">391</span><br><span class="line">392</span><br><span class="line">393</span><br><span class="line">394</span><br><span class="line">395</span><br><span class="line">396</span><br><span class="line">397</span><br><span class="line">398</span><br><span class="line">399</span><br><span class="line">400</span><br><span class="line">401</span><br><span class="line">402</span><br><span class="line">403</span><br><span class="line">404</span><br><span class="line">405</span><br><span class="line">406</span><br><span class="line">407</span><br><span class="line">408</span><br><span class="line">409</span><br><span class="line">410</span><br><span class="line">411</span><br><span class="line">412</span><br><span class="line">413</span><br><span class="line">414</span><br><span class="line">415</span><br><span class="line">416</span><br><span class="line">417</span><br><span class="line">418</span><br><span class="line">419</span><br><span class="line">420</span><br><span class="line">421</span><br><span class="line">422</span><br><span class="line">423</span><br><span class="line">424</span><br><span class="line">425</span><br><span class="line">426</span><br><span class="line">427</span><br><span class="line">428</span><br><span class="line">429</span><br><span class="line">430</span><br><span class="line">431</span><br><span class="line">432</span><br><span class="line">433</span><br><span class="line">434</span><br><span class="line">435</span><br><span class="line">436</span><br><span class="line">437</span><br><span class="line">438</span><br><span class="line">439</span><br><span class="line">440</span><br><span class="line">441</span><br><span class="line">442</span><br><span class="line">443</span><br><span class="line">444</span><br><span class="line">445</span><br><span class="line">446</span><br><span class="line">447</span><br><span class="line">448</span><br><span class="line">449</span><br><span class="line">450</span><br><span class="line">451</span><br><span class="line">452</span><br><span class="line">453</span><br><span class="line">454</span><br><span class="line">455</span><br><span class="line">456</span><br><span class="line">457</span><br><span class="line">458</span><br><span class="line">459</span><br><span class="line">460</span><br><span class="line">461</span><br><span class="line">462</span><br><span class="line">463</span><br><span class="line">464</span><br><span class="line">465</span><br><span class="line">466</span><br><span class="line">467</span><br><span class="line">468</span><br><span class="line">469</span><br><span class="line">470</span><br><span class="line">471</span><br><span class="line">472</span><br><span class="line">473</span><br><span class="line">474</span><br><span class="line">475</span><br><span class="line">476</span><br><span class="line">477</span><br><span class="line">478</span><br><span class="line">479</span><br><span class="line">480</span><br><span class="line">481</span><br><span class="line">482</span><br><span class="line">483</span><br><span class="line">484</span><br><span class="line">485</span><br><span class="line">486</span><br><span class="line">487</span><br><span class="line">488</span><br><span class="line">489</span><br><span class="line">490</span><br><span class="line">491</span><br><span class="line">492</span><br><span class="line">493</span><br><span class="line">494</span><br><span class="line">495</span><br><span class="line">496</span><br><span class="line">497</span><br><span class="line">498</span><br><span class="line">499</span><br><span class="line">500</span><br><span class="line">501</span><br><span class="line">502</span><br><span class="line">503</span><br><span class="line">504</span><br><span class="line">505</span><br><span class="line">506</span><br><span class="line">507</span><br><span class="line">508</span><br><span class="line">509</span><br><span class="line">510</span><br><span class="line">511</span><br><span class="line">512</span><br><span class="line">513</span><br><span class="line">514</span><br><span class="line">515</span><br><span class="line">516</span><br><span class="line">517</span><br><span class="line">518</span><br><span class="line">519</span><br><span class="line">520</span><br><span class="line">521</span><br><span class="line">522</span><br><span class="line">523</span><br><span class="line">524</span><br><span class="line">525</span><br><span class="line">526</span><br><span class="line">527</span><br><span class="line">528</span><br><span class="line">529</span><br><span class="line">530</span><br><span class="line">531</span><br><span class="line">532</span><br><span class="line">533</span><br><span class="line">534</span><br><span class="line">535</span><br><span class="line">536</span><br><span class="line">537</span><br><span class="line">538</span><br><span class="line">539</span><br><span class="line">540</span><br><span class="line">541</span><br><span class="line">542</span><br><span class="line">543</span><br><span class="line">544</span><br><span class="line">545</span><br><span class="line">546</span><br><span class="line">547</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// ======================= 横竖屏自适应背景媒体加载器 =======================</span></span><br><span class="line"><span class="keyword">let</span> lastOrientation = <span class="literal">null</span>; <span class="comment">// 记录上一次的方向状态</span></span><br><span class="line"><span class="keyword">let</span> isMediaInitializing = <span class="literal">false</span>; <span class="comment">// 防止重复初始化的状态锁</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// ================= 新增滚动渐变效果函数 =================</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">initScrollFadeEffect</span>(<span class="params"></span>) {</span><br><span class="line">  <span class="keyword">const</span> mediaContainer = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'home-media-container'</span>);</span><br><span class="line">  <span class="keyword">if</span> (!mediaContainer) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">  <span class="keyword">const</span> mediaElement = mediaContainer.<span class="title function_">querySelector</span>(<span class="string">'.home-media'</span>);</span><br><span class="line">  <span class="keyword">if</span> (!mediaElement) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 节流函数优化性能</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">throttle</span>(<span class="params">func, limit</span>) {</span><br><span class="line">    <span class="keyword">let</span> lastFunc, lastRan;</span><br><span class="line">    <span class="keyword">return</span> <span class="keyword">function</span> (<span class="params"></span>) {</span><br><span class="line">      <span class="keyword">const</span> context = <span class="variable language_">this</span>;</span><br><span class="line">      <span class="keyword">const</span> args = <span class="variable language_">arguments</span>;</span><br><span class="line">      <span class="keyword">if</span> (!lastRan) {</span><br><span class="line">        func.<span class="title function_">apply</span>(context, args);</span><br><span class="line">        lastRan = <span class="title class_">Date</span>.<span class="title function_">now</span>();</span><br><span class="line">      } <span class="keyword">else</span> {</span><br><span class="line">        <span class="built_in">clearTimeout</span>(lastFunc);</span><br><span class="line">        lastFunc = <span class="built_in">setTimeout</span>(<span class="keyword">function</span> (<span class="params"></span>) {</span><br><span class="line">          <span class="keyword">if</span> ((<span class="title class_">Date</span>.<span class="title function_">now</span>() - lastRan) &gt;= limit) {</span><br><span class="line">            func.<span class="title function_">apply</span>(context, args);</span><br><span class="line">            lastRan = <span class="title class_">Date</span>.<span class="title function_">now</span>();</span><br><span class="line">          }</span><br><span class="line">        }, limit - (<span class="title class_">Date</span>.<span class="title function_">now</span>() - lastRan));</span><br><span class="line">      }</span><br><span class="line">    }</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 处理滚动时的透明度变化</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">handleScrollFade</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="keyword">const</span> scrollY = <span class="variable language_">window</span>.<span class="property">scrollY</span>;</span><br><span class="line">    <span class="keyword">const</span> windowHeight = <span class="variable language_">window</span>.<span class="property">innerHeight</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 计算透明度：从1（完全不透明）到0（完全透明）</span></span><br><span class="line">    <span class="comment">// 当滚动到一屏高度时，透明度变为0</span></span><br><span class="line">    <span class="keyword">let</span> opacity = <span class="number">1</span> - (scrollY / windowHeight);</span><br><span class="line">    opacity = <span class="title class_">Math</span>.<span class="title function_">max</span>(<span class="number">0</span>, <span class="title class_">Math</span>.<span class="title function_">min</span>(<span class="number">1</span>, opacity)); <span class="comment">// 限制在0-1范围</span></span><br><span class="line"></span><br><span class="line">    mediaElement.<span class="property">style</span>.<span class="property">opacity</span> = opacity;</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 节流处理滚动事件（每50ms检查一次）</span></span><br><span class="line">  <span class="keyword">const</span> throttledScrollHandler = <span class="title function_">throttle</span>(handleScrollFade, <span class="number">50</span>);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 添加滚动监听</span></span><br><span class="line">  <span class="variable language_">window</span>.<span class="title function_">addEventListener</span>(<span class="string">'scroll'</span>, throttledScrollHandler);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 初始化时执行一次</span></span><br><span class="line">  <span class="title function_">handleScrollFade</span>();</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 存储当前滚动处理器以便后续移除</span></span><br><span class="line">  <span class="keyword">return</span> throttledScrollHandler;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment">// ================= 滚动渐变效果函数结束 =================</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// ================= 新增底部遮罩层控制函数 =================</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">initScrollMaskEffect</span>(<span class="params"></span>) {</span><br><span class="line">  <span class="keyword">const</span> mediaContainer = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'home-media-container'</span>);</span><br><span class="line">  <span class="keyword">if</span> (!mediaContainer) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 节流函数优化性能</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">throttle</span>(<span class="params">func, limit</span>) {</span><br><span class="line">    <span class="keyword">let</span> lastFunc, lastRan;</span><br><span class="line">    <span class="keyword">return</span> <span class="keyword">function</span> (<span class="params"></span>) {</span><br><span class="line">      <span class="keyword">const</span> context = <span class="variable language_">this</span>;</span><br><span class="line">      <span class="keyword">const</span> args = <span class="variable language_">arguments</span>;</span><br><span class="line">      <span class="keyword">if</span> (!lastRan) {</span><br><span class="line">        func.<span class="title function_">apply</span>(context, args);</span><br><span class="line">        lastRan = <span class="title class_">Date</span>.<span class="title function_">now</span>();</span><br><span class="line">      } <span class="keyword">else</span> {</span><br><span class="line">        <span class="built_in">clearTimeout</span>(lastFunc);</span><br><span class="line">        lastFunc = <span class="built_in">setTimeout</span>(<span class="keyword">function</span> (<span class="params"></span>) {</span><br><span class="line">          <span class="keyword">if</span> ((<span class="title class_">Date</span>.<span class="title function_">now</span>() - lastRan) &gt;= limit) {</span><br><span class="line">            func.<span class="title function_">apply</span>(context, args);</span><br><span class="line">            lastRan = <span class="title class_">Date</span>.<span class="title function_">now</span>();</span><br><span class="line">          }</span><br><span class="line">        }, limit - (<span class="title class_">Date</span>.<span class="title function_">now</span>() - lastRan));</span><br><span class="line">      }</span><br><span class="line">    }</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 处理滚动时的遮罩变化</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">handleScrollMask</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="keyword">const</span> scrollY = <span class="variable language_">window</span>.<span class="property">scrollY</span>;</span><br><span class="line">    <span class="keyword">const</span> windowHeight = <span class="variable language_">window</span>.<span class="property">innerHeight</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 计算遮罩高度（0-100%）</span></span><br><span class="line">    <span class="keyword">let</span> maskHeight = (scrollY / windowHeight) * <span class="number">100</span>;</span><br><span class="line">    maskHeight = <span class="title class_">Math</span>.<span class="title function_">min</span>(<span class="number">100</span>, <span class="title class_">Math</span>.<span class="title function_">max</span>(<span class="number">0</span>, maskHeight));</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 动态设置遮罩层高度</span></span><br><span class="line">    mediaContainer.<span class="property">style</span>.<span class="title function_">setProperty</span>(<span class="string">'--mask-height'</span>, <span class="string">`<span class="subst">${maskHeight}</span>%`</span>);</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 节流处理滚动事件（每50ms检查一次）</span></span><br><span class="line">  <span class="keyword">const</span> throttledScrollHandler = <span class="title function_">throttle</span>(handleScrollMask, <span class="number">50</span>);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 添加滚动监听</span></span><br><span class="line">  <span class="variable language_">window</span>.<span class="title function_">addEventListener</span>(<span class="string">'scroll'</span>, throttledScrollHandler);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 初始化时执行一次</span></span><br><span class="line">  <span class="title function_">handleScrollMask</span>();</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 返回处理器以便后续移除</span></span><br><span class="line">  <span class="keyword">return</span> throttledScrollHandler;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">initResponsiveBackground</span>(<span class="params"></span>) {</span><br><span class="line">  <span class="comment">// 检查是否正在初始化</span></span><br><span class="line">  <span class="keyword">if</span> (isMediaInitializing) {</span><br><span class="line">    <span class="keyword">return</span>; <span class="comment">// 如果正在初始化，则直接退出，防止冲突</span></span><br><span class="line">  }</span><br><span class="line">  isMediaInitializing = <span class="literal">true</span>; <span class="comment">// 加锁</span></span><br><span class="line"></span><br><span class="line">  <span class="keyword">const</span> mediaContainer = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'home-media-container'</span>);</span><br><span class="line">  <span class="keyword">if</span> (!mediaContainer) {</span><br><span class="line">    isMediaInitializing = <span class="literal">false</span>; <span class="comment">// 解锁</span></span><br><span class="line">    <span class="keyword">return</span>;</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 检测当前屏幕方向</span></span><br><span class="line">  <span class="keyword">const</span> currentIsPortrait = <span class="variable language_">window</span>.<span class="property">innerHeight</span> &gt; <span class="variable language_">window</span>.<span class="property">innerWidth</span>;</span><br><span class="line">  <span class="keyword">const</span> currentOrientation = currentIsPortrait ? <span class="string">'portrait'</span> : <span class="string">'landscape'</span>;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 如果方向未改变，则直接返回</span></span><br><span class="line">  <span class="keyword">if</span> (lastOrientation === currentOrientation) {</span><br><span class="line">    <span class="keyword">return</span>;</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 更新方向记录</span></span><br><span class="line">  lastOrientation = currentOrientation;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 清除现有媒体元素和加载动画</span></span><br><span class="line">  <span class="keyword">const</span> existingMedia = mediaContainer.<span class="title function_">querySelector</span>(<span class="string">'.home-media'</span>);</span><br><span class="line">  <span class="keyword">const</span> existingLoader = mediaContainer.<span class="title function_">querySelector</span>(<span class="string">'.custom-loader'</span>);</span><br><span class="line">  <span class="keyword">if</span> (existingMedia) existingMedia.<span class="title function_">remove</span>();</span><br><span class="line">  <span class="keyword">if</span> (existingLoader) existingLoader.<span class="title function_">remove</span>();</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 根据方向选择资源</span></span><br><span class="line">  <span class="keyword">let</span> mediaSrc, posterSrc, mediaType;</span><br><span class="line">  <span class="keyword">if</span> (currentIsPortrait) {</span><br><span class="line">    mediaSrc = mediaContainer.<span class="property">dataset</span>.<span class="property">portraitVideo</span> || mediaContainer.<span class="property">dataset</span>.<span class="property">portraitImg</span>;</span><br><span class="line">    posterSrc = mediaContainer.<span class="property">dataset</span>.<span class="property">portraitPoster</span>;</span><br><span class="line">    mediaType = mediaContainer.<span class="property">dataset</span>.<span class="property">portraitVideo</span> ? <span class="string">'video'</span> : <span class="string">'img'</span>;</span><br><span class="line">  } <span class="keyword">else</span> {</span><br><span class="line">    mediaSrc = mediaContainer.<span class="property">dataset</span>.<span class="property">landscapeVideo</span> || mediaContainer.<span class="property">dataset</span>.<span class="property">landscapeImg</span>;</span><br><span class="line">    posterSrc = mediaContainer.<span class="property">dataset</span>.<span class="property">landscapePoster</span>;</span><br><span class="line">    mediaType = mediaContainer.<span class="property">dataset</span>.<span class="property">landscapeVideo</span> ? <span class="string">'video'</span> : <span class="string">'img'</span>;</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="keyword">if</span> (!mediaSrc) {</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">error</span>(<span class="string">'[背景加载器] 未找到有效媒体资源'</span>);</span><br><span class="line">    <span class="keyword">return</span>;</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">`[背景加载器] 使用资源: <span class="subst">${mediaSrc}</span> (类型: <span class="subst">${mediaType}</span>)`</span>);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 创建媒体元素</span></span><br><span class="line">  <span class="keyword">const</span> mediaElement = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(mediaType);</span><br><span class="line">  mediaElement.<span class="property">className</span> = <span class="string">'home-media'</span>;</span><br><span class="line">  mediaElement.<span class="property">style</span>.<span class="property">cssText</span> = <span class="string">'width:100%;height:100%;object-fit:cover'</span>;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// ================= 设置初始透明度 =================</span></span><br><span class="line">  mediaElement.<span class="property">style</span>.<span class="property">opacity</span> = <span class="string">'1'</span>;</span><br><span class="line">  mediaElement.<span class="property">style</span>.<span class="property">transition</span> = <span class="string">'opacity 0.5s ease'</span>;</span><br><span class="line">  <span class="comment">// ================================================</span></span><br><span class="line"></span><br><span class="line">  <span class="comment">// 在媒体容器添加媒体元素后调用效果函数</span></span><br><span class="line">  mediaContainer.<span class="title function_">appendChild</span>(mediaElement);</span><br><span class="line">  <span class="title function_">addMediaEffects</span>(mediaElement, mediaType); <span class="comment">// 添加新功能</span></span><br><span class="line"></span><br><span class="line">  <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[背景加载器] 媒体元素已创建'</span>);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 创建自定义加载动画容器</span></span><br><span class="line">  <span class="keyword">const</span> loaderContainer = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">'div'</span>);</span><br><span class="line">  loaderContainer.<span class="property">className</span> = <span class="string">'custom-loader'</span>;</span><br><span class="line">  mediaContainer.<span class="title function_">prepend</span>(loaderContainer);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 创建加载动画元素</span></span><br><span class="line">  <span class="keyword">const</span> loaderElement = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">'div'</span>);</span><br><span class="line">  loaderElement.<span class="property">className</span> = <span class="string">'loader-animation'</span>;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 设置加载动画样式（使用GIF）</span></span><br><span class="line">  loaderElement.<span class="property">style</span>.<span class="property">backgroundImage</span> = <span class="string">`url(<span class="subst">${posterSrc}</span>)`</span>;</span><br><span class="line">  loaderContainer.<span class="title function_">appendChild</span>(loaderElement);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 视频特殊处理</span></span><br><span class="line">  <span class="keyword">if</span> (mediaType === <span class="string">'video'</span>) {</span><br><span class="line">    mediaElement.<span class="property">autoplay</span> = <span class="literal">true</span>;</span><br><span class="line">    mediaElement.<span class="property">muted</span> = <span class="literal">true</span>;</span><br><span class="line">    mediaElement.<span class="property">loop</span> = <span class="literal">true</span>;</span><br><span class="line">    mediaElement.<span class="property">playsInline</span> = <span class="literal">true</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 增强循环播放机制 - 备用处理</span></span><br><span class="line">    mediaElement.<span class="title function_">addEventListener</span>(<span class="string">'ended'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[背景加载器] 视频播放结束，重新开始播放'</span>);</span><br><span class="line">      mediaElement.<span class="property">currentTime</span> = <span class="number">0</span>;</span><br><span class="line">      mediaElement.<span class="title function_">play</span>().<span class="title function_">catch</span>(<span class="function"><span class="params">e</span> =&gt;</span> <span class="variable language_">console</span>.<span class="title function_">warn</span>(<span class="string">'重新播放失败:'</span>, e));</span><br><span class="line">    });</span><br><span class="line">    mediaElement.<span class="title function_">setAttribute</span>(<span class="string">'playsinline'</span>, <span class="string">''</span>);</span><br><span class="line">    mediaElement.<span class="title function_">setAttribute</span>(<span class="string">'webkit-playsinline'</span>, <span class="string">''</span>);</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 多源支持</span></span><br><span class="line">    <span class="keyword">const</span> source = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">'source'</span>);</span><br><span class="line">    source.<span class="property">src</span> = mediaSrc;</span><br><span class="line">    source.<span class="property">type</span> = <span class="string">'video/mp4'</span>;</span><br><span class="line">    mediaElement.<span class="title function_">appendChild</span>(source);</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 处理自动播放限制</span></span><br><span class="line">    <span class="keyword">const</span> playPromise = mediaElement.<span class="title function_">play</span>();</span><br><span class="line">    <span class="keyword">if</span> (playPromise !== <span class="literal">undefined</span>) {</span><br><span class="line">      playPromise.<span class="title function_">catch</span>(<span class="function"><span class="params">error</span> =&gt;</span> {</span><br><span class="line">        <span class="variable language_">console</span>.<span class="title function_">warn</span>(<span class="string">'[背景加载器] 自动播放被阻止:'</span>, error);</span><br><span class="line">        mediaElement.<span class="property">muted</span> = <span class="literal">true</span>;</span><br><span class="line">        mediaElement.<span class="title function_">play</span>();</span><br><span class="line">      });</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 视频加载完成后移除加载动画</span></span><br><span class="line">    mediaElement.<span class="title function_">addEventListener</span>(<span class="string">'loadeddata'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">      loaderContainer.<span class="property">style</span>.<span class="property">opacity</span> = <span class="string">'0'</span>;</span><br><span class="line">      <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">        <span class="keyword">if</span> (loaderContainer.<span class="property">parentNode</span>) {</span><br><span class="line">          loaderContainer.<span class="property">parentNode</span>.<span class="title function_">removeChild</span>(loaderContainer);</span><br><span class="line">        }</span><br><span class="line">      }, <span class="number">500</span>); <span class="comment">// 淡出动画持续时间</span></span><br><span class="line">      isMediaInitializing = <span class="literal">false</span>; <span class="comment">// 解锁</span></span><br><span class="line">    });</span><br><span class="line">  } <span class="keyword">else</span> {</span><br><span class="line">    mediaElement.<span class="property">src</span> = mediaSrc;</span><br><span class="line">    mediaElement.<span class="property">loading</span> = <span class="string">'eager'</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 图片加载完成后移除加载动画</span></span><br><span class="line">    mediaElement.<span class="title function_">addEventListener</span>(<span class="string">'load'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">      loaderContainer.<span class="property">style</span>.<span class="property">opacity</span> = <span class="string">'0'</span>;</span><br><span class="line">      <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">        <span class="keyword">if</span> (loaderContainer.<span class="property">parentNode</span>) {</span><br><span class="line">          loaderContainer.<span class="property">parentNode</span>.<span class="title function_">removeChild</span>(loaderContainer);</span><br><span class="line">        }</span><br><span class="line">      }, <span class="number">500</span>);</span><br><span class="line">      isMediaInitializing = <span class="literal">false</span>; <span class="comment">// 解锁</span></span><br><span class="line">    });</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 错误处理</span></span><br><span class="line">  mediaElement.<span class="property">onerror</span> = <span class="keyword">function</span> (<span class="params"></span>) {</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">error</span>(<span class="string">`[背景加载器] 资源加载失败: <span class="subst">${mediaSrc}</span>`</span>);</span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">style</span>.<span class="property">display</span> = <span class="string">'none'</span>;</span><br><span class="line">    isMediaInitializing = <span class="literal">false</span>; <span class="comment">// 解锁</span></span><br><span class="line"></span><br><span class="line">    <span class="comment">// 尝试回退到备用类型</span></span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">warn</span>(<span class="string">'[背景加载器] 尝试回退到备用媒体'</span>);</span><br><span class="line">    <span class="keyword">const</span> fallbackType = mediaType === <span class="string">'video'</span> ? <span class="string">'img'</span> : <span class="string">'video'</span>;</span><br><span class="line">    <span class="keyword">const</span> fallbackSrc = currentIsPortrait ?</span><br><span class="line">      (mediaContainer.<span class="property">dataset</span>.<span class="property">portraitImg</span> || mediaContainer.<span class="property">dataset</span>.<span class="property">portraitVideo</span>) :</span><br><span class="line">      (mediaContainer.<span class="property">dataset</span>.<span class="property">landscapeImg</span> || mediaContainer.<span class="property">dataset</span>.<span class="property">landscapeVideo</span>);</span><br><span class="line"></span><br><span class="line">    <span class="keyword">if</span> (fallbackSrc &amp;&amp; fallbackSrc !== mediaSrc) {</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">`[背景加载器] 使用备用资源: <span class="subst">${fallbackSrc}</span>`</span>);</span><br><span class="line">      mediaElement.<span class="property">src</span> = fallbackSrc;</span><br><span class="line">      mediaElement.<span class="property">style</span>.<span class="property">display</span> = <span class="string">'block'</span>;</span><br><span class="line">    }</span><br><span class="line">  };</span><br><span class="line"></span><br><span class="line">  mediaContainer.<span class="title function_">appendChild</span>(mediaElement);</span><br><span class="line">  <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[背景加载器] 媒体元素已创建'</span>);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// ================= 初始化滚动渐变效果 =================</span></span><br><span class="line">  <span class="title function_">initScrollFadeEffect</span>();</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">addMediaEffects</span>(<span class="params">mediaElement, mediaType</span>) {</span><br><span class="line">  <span class="keyword">if</span> (mediaType === <span class="string">'video'</span>) {</span><br><span class="line">    <span class="comment">// 获取当前方向</span></span><br><span class="line">    <span class="keyword">const</span> currentIsPortrait = <span class="variable language_">window</span>.<span class="property">innerHeight</span> &gt; <span class="variable language_">window</span>.<span class="property">innerWidth</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 竖屏模式下固定放大105%</span></span><br><span class="line">    <span class="keyword">const</span> baseScale = currentIsPortrait ? <span class="number">1.05</span> : <span class="number">1.2</span>;</span><br><span class="line">    mediaElement.<span class="property">style</span>.<span class="property">transform</span> = <span class="string">`scale(<span class="subst">${baseScale}</span>)`</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 检测是否为iOS设备</span></span><br><span class="line">    <span class="keyword">function</span> <span class="title function_">isIOS</span>(<span class="params"></span>) {</span><br><span class="line">      <span class="keyword">return</span> <span class="regexp">/iPad|iPhone|iPod/</span>.<span class="title function_">test</span>(navigator.<span class="property">userAgent</span>) &amp;&amp; !<span class="variable language_">window</span>.<span class="property">MSStream</span>;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 如果是iOS设备，直接禁用所有视差效果</span></span><br><span class="line">    <span class="keyword">if</span> (<span class="title function_">isIOS</span>()) {</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[视差效果] 在iOS设备上，禁用所有视差效果'</span>);</span><br><span class="line">      <span class="keyword">return</span>; <span class="comment">// 直接返回，不初始化任何视差效果</span></span><br><span class="line">    }</span><br><span class="line">    <span class="comment">// 1. 添加缩放动画效果</span></span><br><span class="line">    mediaElement.<span class="property">style</span>.<span class="property">transform</span> = <span class="string">'scale(1.2)'</span>; <span class="comment">// 初始放大110%</span></span><br><span class="line">    mediaElement.<span class="property">style</span>.<span class="property">transition</span> = <span class="string">'transform 0.5s ease-out'</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 在视频加载完成后触发缩放动画</span></span><br><span class="line">    mediaElement.<span class="title function_">addEventListener</span>(<span class="string">'loadeddata'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">      <span class="comment">// 竖屏模式保持105%缩放，不需要动画</span></span><br><span class="line">      <span class="keyword">if</span> (currentIsPortrait) {</span><br><span class="line">        mediaElement.<span class="property">style</span>.<span class="property">transform</span> = <span class="string">'scale(1.05)'</span>;</span><br><span class="line">      }</span><br><span class="line">      <span class="comment">// 横屏模式执行缩放动画到正常大小</span></span><br><span class="line">      <span class="keyword">else</span> {</span><br><span class="line">        <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">          mediaElement.<span class="property">style</span>.<span class="property">transform</span> = <span class="string">'scale(1)'</span>;</span><br><span class="line">        }, <span class="number">100</span>);</span><br><span class="line">      }</span><br><span class="line">    });</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 2. 添加视差效果（鼠标/陀螺仪）</span></span><br><span class="line">    <span class="keyword">const</span> mediaContainer = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'page-header'</span>);</span><br><span class="line">    mediaContainer.<span class="property">style</span>.<span class="property">overflow</span> = <span class="string">'hidden'</span>;</span><br><span class="line">    mediaElement.<span class="property">style</span>.<span class="property">transformOrigin</span> = <span class="string">'center center'</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 视差效果参数</span></span><br><span class="line">    <span class="keyword">const</span> parallaxIntensity = <span class="number">0.05</span>;</span><br><span class="line">    <span class="keyword">const</span> scaleIntensity = <span class="number">0.05</span>;</span><br><span class="line">    <span class="keyword">let</span> isGyroActive = <span class="literal">false</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// ================= 新增陀螺仪支持 =================</span></span><br><span class="line">    <span class="comment">// 检测陀螺仪支持</span></span><br><span class="line">    <span class="keyword">function</span> <span class="title function_">initGyroParallax</span>(<span class="params"></span>) {</span><br><span class="line">      <span class="keyword">if</span> (<span class="keyword">typeof</span> <span class="title class_">DeviceOrientationEvent</span> !== <span class="string">'undefined'</span> &amp;&amp; <span class="keyword">typeof</span> <span class="title class_">DeviceOrientationEvent</span>.<span class="property">requestPermission</span> === <span class="string">'function'</span>) {</span><br><span class="line">        <span class="comment">// iOS 13+ 需要权限</span></span><br><span class="line">        <span class="title class_">DeviceOrientationEvent</span>.<span class="title function_">requestPermission</span>()</span><br><span class="line">          .<span class="title function_">then</span>(<span class="function"><span class="params">permissionState</span> =&gt;</span> {</span><br><span class="line">            <span class="keyword">if</span> (permissionState === <span class="string">'granted'</span>) {</span><br><span class="line">              <span class="title function_">setupGyroListeners</span>();</span><br><span class="line">              isGyroActive = <span class="literal">true</span>;</span><br><span class="line">            }</span><br><span class="line">          })</span><br><span class="line">          .<span class="title function_">catch</span>(<span class="variable language_">console</span>.<span class="property">error</span>);</span><br><span class="line">      } <span class="keyword">else</span> <span class="keyword">if</span> (<span class="string">'DeviceOrientationEvent'</span> <span class="keyword">in</span> <span class="variable language_">window</span>) {</span><br><span class="line">        <span class="comment">// Android和其他支持设备</span></span><br><span class="line">        <span class="title function_">setupGyroListeners</span>();</span><br><span class="line">        isGyroActive = <span class="literal">true</span>;</span><br><span class="line">      }</span><br><span class="line"></span><br><span class="line">      <span class="keyword">return</span> isGyroActive;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 设置陀螺仪监听</span></span><br><span class="line">    <span class="keyword">function</span> <span class="title function_">setupGyroListeners</span>(<span class="params"></span>) {</span><br><span class="line">      <span class="variable language_">window</span>.<span class="title function_">addEventListener</span>(<span class="string">'deviceorientation'</span>, handleOrientation);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 处理陀螺仪数据</span></span><br><span class="line">    <span class="keyword">function</span> <span class="title function_">handleOrientation</span>(<span class="params">event</span>) {</span><br><span class="line">      <span class="comment">// 竖屏模式使用105%基础缩放</span></span><br><span class="line">      <span class="keyword">const</span> baseScaleValue = currentIsPortrait ? <span class="number">1.05</span> : <span class="number">1</span>;</span><br><span class="line">      <span class="keyword">if</span> (!isGyroActive) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">      <span class="comment">// 获取陀螺仪数据（beta: 前后倾斜, gamma: 左右倾斜）</span></span><br><span class="line">      <span class="keyword">const</span> beta = event.<span class="property">beta</span> || <span class="number">0</span>;  <span class="comment">// 前后倾斜（-180到180）</span></span><br><span class="line">      <span class="keyword">const</span> gamma = event.<span class="property">gamma</span> || <span class="number">0</span>; <span class="comment">// 左右倾斜（-90到90）</span></span><br><span class="line"></span><br><span class="line">      <span class="comment">// 将角度转换为百分比偏移（归一化处理）</span></span><br><span class="line">      <span class="keyword">const</span> moveX = (gamma / <span class="number">90</span>) * parallaxIntensity * <span class="number">100</span>; <span class="comment">// -100% 到 100%</span></span><br><span class="line">      <span class="keyword">const</span> moveY = (beta / <span class="number">180</span>) * parallaxIntensity * <span class="number">100</span>;</span><br><span class="line"></span><br><span class="line">      <span class="comment">// 应用视差效果</span></span><br><span class="line">      mediaElement.<span class="property">style</span>.<span class="property">transform</span> = <span class="string">`</span></span><br><span class="line"><span class="string">        translate(<span class="subst">${moveX}</span>%, <span class="subst">${moveY}</span>%)</span></span><br><span class="line"><span class="string">        scale(<span class="subst">${baseScaleValue + scaleIntensity}</span>)</span></span><br><span class="line"><span class="string">      `</span>;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// ================= 鼠标视差效果 =================</span></span><br><span class="line">    <span class="keyword">function</span> <span class="title function_">initMouseParallax</span>(<span class="params"></span>) {</span><br><span class="line">      mediaContainer.<span class="title function_">addEventListener</span>(<span class="string">'mousemove'</span>, <span class="function">(<span class="params">e</span>) =&gt;</span> {</span><br><span class="line">        <span class="keyword">const</span> rect = mediaContainer.<span class="title function_">getBoundingClientRect</span>();</span><br><span class="line">        <span class="keyword">const</span> x = (e.<span class="property">clientX</span> - rect.<span class="property">left</span>) / rect.<span class="property">width</span>;</span><br><span class="line">        <span class="keyword">const</span> y = (e.<span class="property">clientY</span> - rect.<span class="property">top</span>) / rect.<span class="property">height</span>;</span><br><span class="line"></span><br><span class="line">        <span class="keyword">const</span> moveX = (x - <span class="number">0.5</span>) * parallaxIntensity * <span class="number">100</span>;</span><br><span class="line">        <span class="keyword">const</span> moveY = (y - <span class="number">0.5</span>) * parallaxIntensity * <span class="number">100</span>;</span><br><span class="line"></span><br><span class="line">        mediaElement.<span class="property">style</span>.<span class="property">transform</span> = <span class="string">`</span></span><br><span class="line"><span class="string">          translate(<span class="subst">${moveX}</span>%, <span class="subst">${moveY}</span>%)</span></span><br><span class="line"><span class="string">          scale(<span class="subst">${<span class="number">1</span> + scaleIntensity}</span>)</span></span><br><span class="line"><span class="string">        `</span>;</span><br><span class="line">      });</span><br><span class="line"></span><br><span class="line">      mediaContainer.<span class="title function_">addEventListener</span>(<span class="string">'mouseleave'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">        mediaElement.<span class="property">style</span>.<span class="property">transform</span> = <span class="string">'scale(1)'</span>;</span><br><span class="line">      });</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// ================= 根据设备类型初始化 =================</span></span><br><span class="line">    <span class="comment">// 检测移动设备</span></span><br><span class="line">    <span class="keyword">const</span> isMobile = <span class="regexp">/Mobi|Android/i</span>.<span class="title function_">test</span>(navigator.<span class="property">userAgent</span>);</span><br><span class="line"></span><br><span class="line">    <span class="keyword">if</span> (isMobile) {</span><br><span class="line">      <span class="comment">// 移动设备优先使用陀螺仪</span></span><br><span class="line">      <span class="keyword">if</span> (!<span class="title function_">initGyroParallax</span>()) {</span><br><span class="line">        <span class="comment">// 不支持陀螺仪则回退到触摸事件</span></span><br><span class="line">        <span class="title function_">initTouchParallax</span>();</span><br><span class="line">      }</span><br><span class="line">    } <span class="keyword">else</span> {</span><br><span class="line">      <span class="comment">// PC设备使用鼠标事件</span></span><br><span class="line">      <span class="title function_">initMouseParallax</span>();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// ================= 触摸事件回退方案 =================</span></span><br><span class="line">    <span class="keyword">function</span> <span class="title function_">initTouchParallax</span>(<span class="params"></span>) {</span><br><span class="line">      mediaContainer.<span class="title function_">addEventListener</span>(<span class="string">'touchmove'</span>, <span class="function">(<span class="params">e</span>) =&gt;</span> {</span><br><span class="line">        e.<span class="title function_">preventDefault</span>();</span><br><span class="line">        <span class="keyword">const</span> touch = e.<span class="property">touches</span>[<span class="number">0</span>];</span><br><span class="line">        <span class="keyword">const</span> rect = mediaContainer.<span class="title function_">getBoundingClientRect</span>();</span><br><span class="line">        <span class="keyword">const</span> x = (touch.<span class="property">clientX</span> - rect.<span class="property">left</span>) / rect.<span class="property">width</span>;</span><br><span class="line">        <span class="keyword">const</span> y = (touch.<span class="property">clientY</span> - rect.<span class="property">top</span>) / rect.<span class="property">height</span>;</span><br><span class="line"></span><br><span class="line">        <span class="keyword">const</span> moveX = (x - <span class="number">0.5</span>) * parallaxIntensity * <span class="number">50</span>; <span class="comment">// 移动强度减半</span></span><br><span class="line">        <span class="keyword">const</span> moveY = (y - <span class="number">0.5</span>) * parallaxIntensity * <span class="number">50</span>;</span><br><span class="line"></span><br><span class="line">        mediaElement.<span class="property">style</span>.<span class="property">transform</span> = <span class="string">`</span></span><br><span class="line"><span class="string">          translate(<span class="subst">${moveX}</span>%, <span class="subst">${moveY}</span>%)</span></span><br><span class="line"><span class="string">          scale(<span class="subst">${<span class="number">1</span> + scaleIntensity * <span class="number">0.5</span>}</span>) // 缩放强度减半</span></span><br><span class="line"><span class="string">        `</span>;</span><br><span class="line">      });</span><br><span class="line"></span><br><span class="line">      mediaContainer.<span class="title function_">addEventListener</span>(<span class="string">'touchend'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">        mediaElement.<span class="property">style</span>.<span class="property">transform</span> = <span class="string">'scale(1)'</span>;</span><br><span class="line">      });</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// ================= 性能优化 =================</span></span><br><span class="line">    <span class="comment">// 页面不可见时暂停陀螺仪</span></span><br><span class="line">    <span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'visibilitychange'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">      <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="property">visibilityState</span> === <span class="string">'hidden'</span>) {</span><br><span class="line">        isGyroActive = <span class="literal">false</span>;</span><br><span class="line">      } <span class="keyword">else</span> <span class="keyword">if</span> (isMobile) {</span><br><span class="line">        isGyroActive = <span class="title function_">initGyroParallax</span>();</span><br><span class="line">      }</span><br><span class="line">    });</span><br><span class="line">  }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 在initMedia函数中调用新功能</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">initMedia</span>(<span class="params"></span>) {</span><br><span class="line">  <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="property">readyState</span> === <span class="string">'loading'</span>) {</span><br><span class="line">    <span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'DOMContentLoaded'</span>, <span class="keyword">function</span> (<span class="params"></span>) {</span><br><span class="line">      <span class="title function_">initResponsiveBackground</span>();</span><br><span class="line">      <span class="title function_">initScrollFadeEffect</span>(); <span class="comment">// 添加调用</span></span><br><span class="line">    });</span><br><span class="line">  } <span class="keyword">else</span> {</span><br><span class="line">    <span class="title function_">initResponsiveBackground</span>();</span><br><span class="line">    <span class="title function_">initScrollFadeEffect</span>(); <span class="comment">// 添加调用</span></span><br><span class="line">  }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment">// ======================= 执行入口 =======================</span></span><br><span class="line"><span class="title function_">initMedia</span>();</span><br><span class="line"></span><br><span class="line"><span class="comment">// 防抖处理窗口变化</span></span><br><span class="line"><span class="keyword">let</span> resizeTimer;</span><br><span class="line"><span class="variable language_">window</span>.<span class="title function_">addEventListener</span>(<span class="string">'resize'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">  <span class="built_in">clearTimeout</span>(resizeTimer);</span><br><span class="line">  resizeTimer = <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">    <span class="comment">// 计算当前方向状态</span></span><br><span class="line">    <span class="keyword">const</span> currentIsPortrait = <span class="variable language_">window</span>.<span class="property">innerHeight</span> &gt; <span class="variable language_">window</span>.<span class="property">innerWidth</span>;</span><br><span class="line">    <span class="keyword">const</span> currentOrientation = currentIsPortrait ? <span class="string">'portrait'</span> : <span class="string">'landscape'</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 只有方向实际改变时才执行重载</span></span><br><span class="line">    <span class="keyword">if</span> (lastOrientation !== currentOrientation) {</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[背景加载器] 窗口大小变化，重新加载媒体'</span>);</span><br><span class="line">      <span class="title function_">initResponsiveBackground</span>();</span><br><span class="line">    } <span class="keyword">else</span> {</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[背景加载器] 窗口大小变化但方向未改变'</span>);</span><br><span class="line">      <span class="comment">// ================= 方向未变时重置透明度 =================</span></span><br><span class="line">      <span class="title function_">initScrollFadeEffect</span>();</span><br><span class="line">    }</span><br><span class="line">  }, <span class="number">500</span>);</span><br><span class="line">});</span><br><span class="line"></span><br><span class="line"><span class="comment">// 页面可见性变化处理</span></span><br><span class="line"><span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'visibilitychange'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">  <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="property">visibilityState</span> === <span class="string">'visible'</span>) {</span><br><span class="line">    <span class="keyword">const</span> video = <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#home-media-container video'</span>);</span><br><span class="line">    <span class="keyword">if</span> (video &amp;&amp; video.<span class="property">paused</span>) {</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[背景加载器] 页面恢复可见，重新播放视频'</span>);</span><br><span class="line">      video.<span class="title function_">play</span>().<span class="title function_">catch</span>(<span class="function"><span class="params">e</span> =&gt;</span> <span class="variable language_">console</span>.<span class="title function_">warn</span>(<span class="string">'视频恢复播放失败:'</span>, e));</span><br><span class="line">    }</span><br><span class="line">    <span class="comment">// ================= 页面恢复可见时重置透明度 =================</span></span><br><span class="line">    <span class="title function_">initScrollFadeEffect</span>();</span><br><span class="line">  }</span><br><span class="line">});</span><br><span class="line"></span><br><span class="line"><span class="comment">// ========== 新增修复代码（直接加在现有代码后面） ========== //</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 1. 缓存恢复检测（核心修复）</span></span><br><span class="line"><span class="variable language_">window</span>.<span class="title function_">addEventListener</span>(<span class="string">'pageshow'</span>, <span class="function"><span class="params">event</span> =&gt;</span> {</span><br><span class="line">  <span class="keyword">if</span> (event.<span class="property">persisted</span> &amp;&amp; location.<span class="property">pathname</span> === <span class="string">'/'</span>) {</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[修复] 检测到缓存恢复主页，强制重置'</span>);</span><br><span class="line">    lastOrientation = <span class="literal">null</span>;</span><br><span class="line">    <span class="title function_">initResponsiveBackground</span>();</span><br><span class="line">    <span class="comment">// ================= 缓存恢复时重置透明度 =================</span></span><br><span class="line">    <span class="built_in">setTimeout</span>(initScrollFadeEffect, <span class="number">300</span>);</span><br><span class="line">  }</span><br><span class="line">});</span><br><span class="line"></span><br><span class="line"><span class="comment">// 2. 路由变化监听（SPA兼容）</span></span><br><span class="line"><span class="variable language_">window</span>.<span class="title function_">addEventListener</span>(<span class="string">'popstate'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">  <span class="keyword">if</span> (location.<span class="property">pathname</span> === <span class="string">'/'</span>) {</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[修复] 检测到返回主页'</span>);</span><br><span class="line">    <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">      <span class="comment">// 检查媒体元素是否存在</span></span><br><span class="line">      <span class="keyword">const</span> container = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'home-media-container'</span>);</span><br><span class="line">      <span class="keyword">if</span> (!container?.<span class="title function_">querySelector</span>(<span class="string">'.home-media'</span>)) {</span><br><span class="line">        lastOrientation = <span class="literal">null</span>;</span><br><span class="line">        <span class="title function_">initResponsiveBackground</span>();</span><br><span class="line">      }</span><br><span class="line">      <span class="comment">// ================= 返回主页时重置透明度 =================</span></span><br><span class="line">      <span class="title function_">initScrollFadeEffect</span>();</span><br><span class="line">    }, <span class="number">300</span>); <span class="comment">// 延迟确保DOM更新</span></span><br><span class="line">  }</span><br><span class="line">});</span><br><span class="line"></span><br><span class="line"><span class="comment">// 3. 媒体状态自检（兜底方案）</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">checkMediaStatus</span>(<span class="params"></span>) {</span><br><span class="line">  <span class="keyword">if</span> (location.<span class="property">pathname</span> !== <span class="string">'/'</span>) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">  <span class="keyword">const</span> container = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'home-media-container'</span>);</span><br><span class="line">  <span class="keyword">if</span> (!container) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">  <span class="keyword">const</span> hasMedia = container.<span class="title function_">querySelector</span>(<span class="string">'.home-media'</span>);</span><br><span class="line">  <span class="keyword">if</span> (!hasMedia) {</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'[修复] 自检发现媒体丢失'</span>);</span><br><span class="line">    lastOrientation = <span class="literal">null</span>;</span><br><span class="line">    <span class="title function_">initResponsiveBackground</span>();</span><br><span class="line">  }</span><br><span class="line">  <span class="comment">// ================= 媒体自检时重置透明度 =================</span></span><br><span class="line">  <span class="title function_">initScrollFadeEffect</span>();</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 每0.5秒检查一次（轻量级检测）</span></span><br><span class="line"><span class="built_in">setInterval</span>(checkMediaStatus, <span class="number">500</span>);</span><br><span class="line"></span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure></li></ol><hr><h6 id="第三步：修改主题配置并注入新文件"><a href="#第三步：修改主题配置并注入新文件" class="headerlink" title="第三步：修改主题配置并注入新文件"></a><strong>第三步：修改主题配置并注入新文件</strong></h6><ol><li><p><strong>打开主题配置文件</strong> (<code>themes/anzhiyu/_config.yml</code>)。</p></li><li><p><strong>修改首页顶部图配置</strong></p><ul><li>找到 <code>index_img</code> 配置项，将其删除或注释掉。</li><li>然后，添加下面这个新的 <code>index_img</code> 和 <code>index_video</code> 结构。</li></ul><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 首页媒体配置</span></span><br><span class="line"><span class="comment"># 注意：index_img 和 index_video 的 enable 只能有一个为 true</span></span><br><span class="line"><span class="attr">index_img:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">false</span> <span class="comment"># 如果想用图片背景，请将此项设为 true，并将下面的 index_video 的 enable 设为 false</span></span><br><span class="line">  <span class="attr">path:</span> <span class="string">'[您的横屏图片链接]'</span></span><br><span class="line">  <span class="attr">vpath:</span> <span class="string">'[您的竖屏图片链接]'</span> <span class="comment"># vpath 为竖屏设备显示的图片</span></span><br><span class="line"></span><br><span class="line"><span class="attr">index_video:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span> <span class="comment"># ✅ 启用视频背景</span></span><br><span class="line">  <span class="attr">path:</span> <span class="string">'[您的横屏视频链接]'</span> <span class="comment"># 例如 https://yun.ayakasuki.com/...</span></span><br><span class="line">  <span class="attr">poster:</span> <span class="string">'[您的横屏视频加载动画GIF链接]'</span> <span class="comment"># 视频加载出来前的占位图</span></span><br><span class="line">  <span class="attr">vpath:</span> <span class="string">'[您的竖屏视频链接]'</span></span><br><span class="line">  <span class="attr">vposter:</span> <span class="string">'[您的竖屏视频加载动画GIF链接]'</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>注入新添加的CSS和JS文件</strong></p><ul><li>在同一个主题配置文件中，找到 <code>inject:</code> 部分。</li><li>在 <code>head:</code> 列表的末尾添加一行，用于引入我们新建的CSS和JS文件。</li></ul><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">inject:</span></span><br><span class="line">  <span class="attr">head:</span></span><br><span class="line">    <span class="comment"># - 其他 head 内容</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/css/index_media.css"&gt;</span></span><br><span class="line">  <span class="attr">bottom:</span></span><br><span class="line">    <span class="comment"># - 其他 bottom 内容</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="/js/index_media.js"&gt;&lt;/script&gt;</span></span><br></pre></td></tr></tbody></table></figure><p><em>(<strong>注意</strong>：为便于主题管理，我们放在了 <code>source/css</code> 和 <code>source/js</code> 中，并在这里使用对应的路径引入)</em></p></li></ol><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/22096.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/22096.html&quot;)">1.首页主题魔改：顶部视频播放</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/22096.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=1.首页主题魔改：顶部视频播放&amp;url=https://prorise666.site/posts/22096.html&amp;pic=https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>框架技术<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Hexo<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>魔改<span class="categoryesPageCount">23</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>博客搭建教程<span class="tagsPageCount">31</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/17934.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">第八部分：内容创作与长期维护</div></div></a></div><div class="next-post pull-right"><a href="/posts/41365.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">2.首页主题魔改：文章容器布局</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/57565.html" title="12.Twikoo 美化：自定义评论回复邮件模板"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">12.Twikoo 美化：自定义评论回复邮件模板</div></div></a></div><div><a href="/posts/24286.html" title="10.内容扩展：添加“安全跳转”中间页"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">10.内容扩展：添加“安全跳转”中间页</div></div></a></div><div><a href="/posts/65188.html" title="11.Twikoo 美化：添加自定义表情包"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">11.Twikoo 美化：添加自定义表情包</div></div></a></div><div><a href="/posts/20246.html" title="13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）</div></div></a></div><div><a href="/posts/34091.html" title="15.主题魔改：自定义全站字体"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">15.主题魔改：自定义全站字体</div></div></a></div><div><a href="/posts/11486.html" title="17.内容扩展：添加“前端代码实时预览”沙箱"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">17.内容扩展：添加“前端代码实时预览”沙箱</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData = {
  title: "1.首页主题魔改：顶部视频播放",
  date: "2025-07-10 16:13:45",
  updated: "2025-07-19 19:25:44",
  tags: ["博客搭建教程"],
  categories: ["框架技术","Hexo","魔改"],
  content: "\n### **1.首页主题魔改：顶部视频播放**\n\n###### **功能介绍与重要提示**\n\n本指南将主题的静态首页顶部图，升级为一个支持**视频背景**、能够根据**横竖屏自适应切换**、并带有**视差和滚动渐变**等高级特效的动态媒体背景。\n\n> **警告：** 这是一项“魔改”级别的定制，涉及到覆盖主题的核心模板文件。在开始前，**强烈建议您备份整个 `themes/anzhiyu` 文件夹**，以防出现意外情况可以随时恢复。\n\n---\n###### **第一步：替换核心布局文件**\n\n此修改需要替换掉主题中负责渲染页面头部的核心文件。\n\n1.  **找到并打开文件**：\n    `themes/anzhiyu/layout/includes/header/index.pug`\n\n2.  **替换内容**：\n    将这个文件的**全部内容**，用下面提供的**完整代码**进行覆盖。\n\n    ```pug\n    // 优先级控制逻辑（独立作用域）\n    if !theme.disable_top_img && page.top_img !== false\n      if is_post()\n        - var top_img = page.top_img || page.cover || page.randomcover\n      else if is_page()\n        - var top_img = page.top_img || theme.default_top_img\n      else if is_home()\n        // 首页专用媒体声明\n        - var home_index_img = theme.index_img?.enable ? theme.index_img.path : false\n        - var home_index_video = theme.index_video?.enable ? theme.index_video.path : false\n        - var top_img = home_index_img || home_index_video || theme.default_top_img\n      else\n        - var top_img = page.top_img || theme.default_top_img\n\n      if top_img !== false\n        // 路径处理\n        - var imgSource = top_img && top_img.indexOf('/') !== -1 ? url_for(top_img) : top_img\n        // 首页专用路径\n        - var homeImg = home_index_img ? url_for(home_index_img) : ''\n        - var homeVideo = home_index_video ? url_for(home_index_video) : ''\n        - var bg_img = is_home() ? (home_index_img || home_index_video) : imgSource\n\n        - var site_title = page.title || page.tag || page.category || config.title\n        - var isHomeClass = is_home() ? 'full_page' : 'not-home-page'\n        - is_post() ? isHomeClass = 'post-bg' : isHomeClass\n      else\n        - var isHomeClass = 'not-top-img'\n    else\n      - var top_img = false\n      - var isHomeClass = 'not-top-img'\n\n    header#page-header(class=`${isHomeClass}`)\n      !=partial('includes/header/nav', {}, {cache: true})\n      if top_img !== false\n        if is_post()\n          if page.bilibili_bg\n            !=partial('includes/bili-banner/index')\n          else\n            include ./post-info.pug\n            if theme.dynamicEffect && theme.dynamicEffect.postTopWave\n              section.main-hero-waves-area.waves-area\n                svg.waves-svg(xmlns='http://www.w3.org/2000/svg', xlink='http://www.w3.org/1999/xlink', viewBox='0 24 150 28', preserveAspectRatio='none', shape-rendering='auto')\n                  defs\n                    path#gentle-wave(d='M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z')\n                  g.parallax\n                    use(href='#gentle-wave', x='48', y='0')\n                    use(href='#gentle-wave', x='48', y='3')\n                    use(href='#gentle-wave', x='48', y='5')\n                    use(href='#gentle-wave', x='48', y='7')\n          #post-top-cover\n            img#post-top-bg(class='nolazyload' src=bg_img)\n        else if is_home()\n          // 媒体容器\n          #home-media-container(\n            data-landscape-img=home_index_img ? homeImg : ''\n            data-portrait-img=home_index_img && theme.index_img.vpath ? url_for(theme.index_img.vpath) : ''\n            data-landscape-video=home_index_video ? homeVideo : ''\n            data-portrait-video=home_index_video && theme.index_video.vpath ? url_for(theme.index_video.vpath) : ''\n            data-landscape-poster=home_index_video && theme.index_video.poster ? url_for(theme.index_video.poster) : ''\n            data-portrait-poster=home_index_video && theme.index_video.vposter ? url_for(theme.index_video.vposter) : ''\n            style=\"height:100%;background-attachment:fixed;z-index:0\"\n          )\n          #site-info\n            h1#site-title=site_title\n            if theme.subtitle.enable\n              - var loadSubJs = true\n              #site-subtitle\n                span#subtitle\n            if(theme.social)\n              #site_social_icons\n                !=fragment_cache('social', function(){return partial('includes/header/social')})\n          #scroll-down\n            i.anzhiyufont.anzhiyu-icon-angle-down.scroll-down-effects\n        else\n          #page-site-info(style=`background-image: url(${imgSource})`)\n            h1#site-title=site_title\n    ```\n\n---\n###### **第二步：添加自定义样式与脚本文件**\n\n1.  **创建CSS文件**\n    * 在 `themes/anzhiyu/source/css/` 目录下，新建一个文件，命名为 `index_media.css`。\n    * 将下面的CSS代码完整复制进去：\n    ```css\n    /* index */\n    \n    #home-media-container {\n      position: fixed; /* 改为固定定位 */\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      overflow: hidden;\n      z-index: 0;\n      \n      /* 添加底部向上渐变遮罩 */\n      -webkit-mask-image: linear-gradient(to top, transparent 0%, black 0%);\n      mask-image: linear-gradient(to top, transparent 0%, black 0%);\n    }\n    \n    .home-media {\n      position: fixed; /* 同步改为固定定位 */\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      \n      /* 添加透明度过渡 */\n      transition: opacity 0.5s ease;\n      opacity: 1;\n    }\n    \n    /* 自定义加载动画容器 */\n    .custom-loader {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 10; /* 确保在视频上方 */\n      pointer-events: none; /* 防止阻挡视频交互 */\n      transition: opacity 0.5s ease; /* 淡出动画 */\n    }\n    \n    /* 加载动画元素 - 修复尺寸问题 */\n    .loader-animation {\n      /* 修复：改为全屏覆盖模式 */\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-size: cover; /* 改为cover确保铺满屏幕 */\n      background-position: center;\n      background-repeat: no-repeat;\n      animation: pulse 1.5s infinite ease-in-out;\n    }\n    \n    /* 移动端优化 */\n    @media screen and (max-width: 768px) {\n      .loader-animation {\n        background-size: cover; /* 移动端也保持cover模式 */\n      }\n    }\n    \n    /* 呼吸动画效果 */\n    @keyframes pulse {\n      0% { transform: scale(1); opacity: 0.8; }\n      50% { transform: scale(1.02); opacity: 1; }\n      100% { transform: scale(1); opacity: 0.8; }\n    }\n    ```\n2.  **创建JS文件**\n    * 在 `themes/anzhiyu/source/js/` 目录下，新建一个文件，命名为 `index_media.js`。\n    \n    ```js\n    // ======================= 横竖屏自适应背景媒体加载器 =======================\n    let lastOrientation = null; // 记录上一次的方向状态\n    let isMediaInitializing = false; // 防止重复初始化的状态锁\n    \n    // ================= 新增滚动渐变效果函数 =================\n    function initScrollFadeEffect() {\n      const mediaContainer = document.getElementById('home-media-container');\n      if (!mediaContainer) return;\n    \n      const mediaElement = mediaContainer.querySelector('.home-media');\n      if (!mediaElement) return;\n    \n      // 节流函数优化性能\n      function throttle(func, limit) {\n        let lastFunc, lastRan;\n        return function () {\n          const context = this;\n          const args = arguments;\n          if (!lastRan) {\n            func.apply(context, args);\n            lastRan = Date.now();\n          } else {\n            clearTimeout(lastFunc);\n            lastFunc = setTimeout(function () {\n              if ((Date.now() - lastRan) >= limit) {\n                func.apply(context, args);\n                lastRan = Date.now();\n              }\n            }, limit - (Date.now() - lastRan));\n          }\n        }\n      }\n    \n      // 处理滚动时的透明度变化\n      function handleScrollFade() {\n        const scrollY = window.scrollY;\n        const windowHeight = window.innerHeight;\n    \n        // 计算透明度：从1（完全不透明）到0（完全透明）\n        // 当滚动到一屏高度时，透明度变为0\n        let opacity = 1 - (scrollY / windowHeight);\n        opacity = Math.max(0, Math.min(1, opacity)); // 限制在0-1范围\n    \n        mediaElement.style.opacity = opacity;\n      }\n    \n      // 节流处理滚动事件（每50ms检查一次）\n      const throttledScrollHandler = throttle(handleScrollFade, 50);\n    \n      // 添加滚动监听\n      window.addEventListener('scroll', throttledScrollHandler);\n    \n      // 初始化时执行一次\n      handleScrollFade();\n    \n      // 存储当前滚动处理器以便后续移除\n      return throttledScrollHandler;\n    }\n    \n    \n    // ================= 滚动渐变效果函数结束 =================\n    \n    // ================= 新增底部遮罩层控制函数 =================\n    function initScrollMaskEffect() {\n      const mediaContainer = document.getElementById('home-media-container');\n      if (!mediaContainer) return;\n    \n      // 节流函数优化性能\n      function throttle(func, limit) {\n        let lastFunc, lastRan;\n        return function () {\n          const context = this;\n          const args = arguments;\n          if (!lastRan) {\n            func.apply(context, args);\n            lastRan = Date.now();\n          } else {\n            clearTimeout(lastFunc);\n            lastFunc = setTimeout(function () {\n              if ((Date.now() - lastRan) >= limit) {\n                func.apply(context, args);\n                lastRan = Date.now();\n              }\n            }, limit - (Date.now() - lastRan));\n          }\n        }\n      }\n    \n      // 处理滚动时的遮罩变化\n      function handleScrollMask() {\n        const scrollY = window.scrollY;\n        const windowHeight = window.innerHeight;\n    \n        // 计算遮罩高度（0-100%）\n        let maskHeight = (scrollY / windowHeight) * 100;\n        maskHeight = Math.min(100, Math.max(0, maskHeight));\n    \n        // 动态设置遮罩层高度\n        mediaContainer.style.setProperty('--mask-height', `${maskHeight}%`);\n      }\n    \n      // 节流处理滚动事件（每50ms检查一次）\n      const throttledScrollHandler = throttle(handleScrollMask, 50);\n    \n      // 添加滚动监听\n      window.addEventListener('scroll', throttledScrollHandler);\n    \n      // 初始化时执行一次\n      handleScrollMask();\n    \n      // 返回处理器以便后续移除\n      return throttledScrollHandler;\n    }\n    \n    \n    function initResponsiveBackground() {\n      // 检查是否正在初始化\n      if (isMediaInitializing) {\n        return; // 如果正在初始化，则直接退出，防止冲突\n      }\n      isMediaInitializing = true; // 加锁\n    \n      const mediaContainer = document.getElementById('home-media-container');\n      if (!mediaContainer) {\n        isMediaInitializing = false; // 解锁\n        return;\n      }\n    \n      // 检测当前屏幕方向\n      const currentIsPortrait = window.innerHeight > window.innerWidth;\n      const currentOrientation = currentIsPortrait ? 'portrait' : 'landscape';\n    \n      // 如果方向未改变，则直接返回\n      if (lastOrientation === currentOrientation) {\n        return;\n      }\n    \n      // 更新方向记录\n      lastOrientation = currentOrientation;\n    \n      // 清除现有媒体元素和加载动画\n      const existingMedia = mediaContainer.querySelector('.home-media');\n      const existingLoader = mediaContainer.querySelector('.custom-loader');\n      if (existingMedia) existingMedia.remove();\n      if (existingLoader) existingLoader.remove();\n    \n      // 根据方向选择资源\n      let mediaSrc, posterSrc, mediaType;\n      if (currentIsPortrait) {\n        mediaSrc = mediaContainer.dataset.portraitVideo || mediaContainer.dataset.portraitImg;\n        posterSrc = mediaContainer.dataset.portraitPoster;\n        mediaType = mediaContainer.dataset.portraitVideo ? 'video' : 'img';\n      } else {\n        mediaSrc = mediaContainer.dataset.landscapeVideo || mediaContainer.dataset.landscapeImg;\n        posterSrc = mediaContainer.dataset.landscapePoster;\n        mediaType = mediaContainer.dataset.landscapeVideo ? 'video' : 'img';\n      }\n    \n      if (!mediaSrc) {\n        console.error('[背景加载器] 未找到有效媒体资源');\n        return;\n      }\n    \n      console.log(`[背景加载器] 使用资源: ${mediaSrc} (类型: ${mediaType})`);\n    \n      // 创建媒体元素\n      const mediaElement = document.createElement(mediaType);\n      mediaElement.className = 'home-media';\n      mediaElement.style.cssText = 'width:100%;height:100%;object-fit:cover';\n    \n      // ================= 设置初始透明度 =================\n      mediaElement.style.opacity = '1';\n      mediaElement.style.transition = 'opacity 0.5s ease';\n      // ================================================\n    \n      // 在媒体容器添加媒体元素后调用效果函数\n      mediaContainer.appendChild(mediaElement);\n      addMediaEffects(mediaElement, mediaType); // 添加新功能\n    \n      console.log('[背景加载器] 媒体元素已创建');\n    \n      // 创建自定义加载动画容器\n      const loaderContainer = document.createElement('div');\n      loaderContainer.className = 'custom-loader';\n      mediaContainer.prepend(loaderContainer);\n    \n      // 创建加载动画元素\n      const loaderElement = document.createElement('div');\n      loaderElement.className = 'loader-animation';\n    \n      // 设置加载动画样式（使用GIF）\n      loaderElement.style.backgroundImage = `url(${posterSrc})`;\n      loaderContainer.appendChild(loaderElement);\n    \n      // 视频特殊处理\n      if (mediaType === 'video') {\n        mediaElement.autoplay = true;\n        mediaElement.muted = true;\n        mediaElement.loop = true;\n        mediaElement.playsInline = true;\n    \n        // 增强循环播放机制 - 备用处理\n        mediaElement.addEventListener('ended', () => {\n          console.log('[背景加载器] 视频播放结束，重新开始播放');\n          mediaElement.currentTime = 0;\n          mediaElement.play().catch(e => console.warn('重新播放失败:', e));\n        });\n        mediaElement.setAttribute('playsinline', '');\n        mediaElement.setAttribute('webkit-playsinline', '');\n    \n        // 多源支持\n        const source = document.createElement('source');\n        source.src = mediaSrc;\n        source.type = 'video/mp4';\n        mediaElement.appendChild(source);\n    \n        // 处理自动播放限制\n        const playPromise = mediaElement.play();\n        if (playPromise !== undefined) {\n          playPromise.catch(error => {\n            console.warn('[背景加载器] 自动播放被阻止:', error);\n            mediaElement.muted = true;\n            mediaElement.play();\n          });\n        }\n    \n        // 视频加载完成后移除加载动画\n        mediaElement.addEventListener('loadeddata', () => {\n          loaderContainer.style.opacity = '0';\n          setTimeout(() => {\n            if (loaderContainer.parentNode) {\n              loaderContainer.parentNode.removeChild(loaderContainer);\n            }\n          }, 500); // 淡出动画持续时间\n          isMediaInitializing = false; // 解锁\n        });\n      } else {\n        mediaElement.src = mediaSrc;\n        mediaElement.loading = 'eager';\n    \n        // 图片加载完成后移除加载动画\n        mediaElement.addEventListener('load', () => {\n          loaderContainer.style.opacity = '0';\n          setTimeout(() => {\n            if (loaderContainer.parentNode) {\n              loaderContainer.parentNode.removeChild(loaderContainer);\n            }\n          }, 500);\n          isMediaInitializing = false; // 解锁\n        });\n      }\n    \n      // 错误处理\n      mediaElement.onerror = function () {\n        console.error(`[背景加载器] 资源加载失败: ${mediaSrc}`);\n        this.style.display = 'none';\n        isMediaInitializing = false; // 解锁\n    \n        // 尝试回退到备用类型\n        console.warn('[背景加载器] 尝试回退到备用媒体');\n        const fallbackType = mediaType === 'video' ? 'img' : 'video';\n        const fallbackSrc = currentIsPortrait ?\n          (mediaContainer.dataset.portraitImg || mediaContainer.dataset.portraitVideo) :\n          (mediaContainer.dataset.landscapeImg || mediaContainer.dataset.landscapeVideo);\n    \n        if (fallbackSrc && fallbackSrc !== mediaSrc) {\n          console.log(`[背景加载器] 使用备用资源: ${fallbackSrc}`);\n          mediaElement.src = fallbackSrc;\n          mediaElement.style.display = 'block';\n        }\n      };\n    \n      mediaContainer.appendChild(mediaElement);\n      console.log('[背景加载器] 媒体元素已创建');\n    \n      // ================= 初始化滚动渐变效果 =================\n      initScrollFadeEffect();\n    }\n    \n    function addMediaEffects(mediaElement, mediaType) {\n      if (mediaType === 'video') {\n        // 获取当前方向\n        const currentIsPortrait = window.innerHeight > window.innerWidth;\n    \n        // 竖屏模式下固定放大105%\n        const baseScale = currentIsPortrait ? 1.05 : 1.2;\n        mediaElement.style.transform = `scale(${baseScale})`;\n    \n        // 检测是否为iOS设备\n        function isIOS() {\n          return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\n        }\n    \n        // 如果是iOS设备，直接禁用所有视差效果\n        if (isIOS()) {\n          console.log('[视差效果] 在iOS设备上，禁用所有视差效果');\n          return; // 直接返回，不初始化任何视差效果\n        }\n        // 1. 添加缩放动画效果\n        mediaElement.style.transform = 'scale(1.2)'; // 初始放大110%\n        mediaElement.style.transition = 'transform 0.5s ease-out';\n    \n        // 在视频加载完成后触发缩放动画\n        mediaElement.addEventListener('loadeddata', () => {\n          // 竖屏模式保持105%缩放，不需要动画\n          if (currentIsPortrait) {\n            mediaElement.style.transform = 'scale(1.05)';\n          }\n          // 横屏模式执行缩放动画到正常大小\n          else {\n            setTimeout(() => {\n              mediaElement.style.transform = 'scale(1)';\n            }, 100);\n          }\n        });\n    \n        // 2. 添加视差效果（鼠标/陀螺仪）\n        const mediaContainer = document.getElementById('page-header');\n        mediaContainer.style.overflow = 'hidden';\n        mediaElement.style.transformOrigin = 'center center';\n    \n        // 视差效果参数\n        const parallaxIntensity = 0.05;\n        const scaleIntensity = 0.05;\n        let isGyroActive = false;\n    \n        // ================= 新增陀螺仪支持 =================\n        // 检测陀螺仪支持\n        function initGyroParallax() {\n          if (typeof DeviceOrientationEvent !== 'undefined' && typeof DeviceOrientationEvent.requestPermission === 'function') {\n            // iOS 13+ 需要权限\n            DeviceOrientationEvent.requestPermission()\n              .then(permissionState => {\n                if (permissionState === 'granted') {\n                  setupGyroListeners();\n                  isGyroActive = true;\n                }\n              })\n              .catch(console.error);\n          } else if ('DeviceOrientationEvent' in window) {\n            // Android和其他支持设备\n            setupGyroListeners();\n            isGyroActive = true;\n          }\n    \n          return isGyroActive;\n        }\n    \n        // 设置陀螺仪监听\n        function setupGyroListeners() {\n          window.addEventListener('deviceorientation', handleOrientation);\n        }\n    \n        // 处理陀螺仪数据\n        function handleOrientation(event) {\n          // 竖屏模式使用105%基础缩放\n          const baseScaleValue = currentIsPortrait ? 1.05 : 1;\n          if (!isGyroActive) return;\n    \n          // 获取陀螺仪数据（beta: 前后倾斜, gamma: 左右倾斜）\n          const beta = event.beta || 0;  // 前后倾斜（-180到180）\n          const gamma = event.gamma || 0; // 左右倾斜（-90到90）\n    \n          // 将角度转换为百分比偏移（归一化处理）\n          const moveX = (gamma / 90) * parallaxIntensity * 100; // -100% 到 100%\n          const moveY = (beta / 180) * parallaxIntensity * 100;\n    \n          // 应用视差效果\n          mediaElement.style.transform = `\n            translate(${moveX}%, ${moveY}%)\n            scale(${baseScaleValue + scaleIntensity})\n          `;\n        }\n    \n        // ================= 鼠标视差效果 =================\n        function initMouseParallax() {\n          mediaContainer.addEventListener('mousemove', (e) => {\n            const rect = mediaContainer.getBoundingClientRect();\n            const x = (e.clientX - rect.left) / rect.width;\n            const y = (e.clientY - rect.top) / rect.height;\n    \n            const moveX = (x - 0.5) * parallaxIntensity * 100;\n            const moveY = (y - 0.5) * parallaxIntensity * 100;\n    \n            mediaElement.style.transform = `\n              translate(${moveX}%, ${moveY}%)\n              scale(${1 + scaleIntensity})\n            `;\n          });\n    \n          mediaContainer.addEventListener('mouseleave', () => {\n            mediaElement.style.transform = 'scale(1)';\n          });\n        }\n    \n        // ================= 根据设备类型初始化 =================\n        // 检测移动设备\n        const isMobile = /Mobi|Android/i.test(navigator.userAgent);\n    \n        if (isMobile) {\n          // 移动设备优先使用陀螺仪\n          if (!initGyroParallax()) {\n            // 不支持陀螺仪则回退到触摸事件\n            initTouchParallax();\n          }\n        } else {\n          // PC设备使用鼠标事件\n          initMouseParallax();\n        }\n    \n        // ================= 触摸事件回退方案 =================\n        function initTouchParallax() {\n          mediaContainer.addEventListener('touchmove', (e) => {\n            e.preventDefault();\n            const touch = e.touches[0];\n            const rect = mediaContainer.getBoundingClientRect();\n            const x = (touch.clientX - rect.left) / rect.width;\n            const y = (touch.clientY - rect.top) / rect.height;\n    \n            const moveX = (x - 0.5) * parallaxIntensity * 50; // 移动强度减半\n            const moveY = (y - 0.5) * parallaxIntensity * 50;\n    \n            mediaElement.style.transform = `\n              translate(${moveX}%, ${moveY}%)\n              scale(${1 + scaleIntensity * 0.5}) // 缩放强度减半\n            `;\n          });\n    \n          mediaContainer.addEventListener('touchend', () => {\n            mediaElement.style.transform = 'scale(1)';\n          });\n        }\n    \n        // ================= 性能优化 =================\n        // 页面不可见时暂停陀螺仪\n        document.addEventListener('visibilitychange', () => {\n          if (document.visibilityState === 'hidden') {\n            isGyroActive = false;\n          } else if (isMobile) {\n            isGyroActive = initGyroParallax();\n          }\n        });\n      }\n    }\n    \n    // 在initMedia函数中调用新功能\n    function initMedia() {\n      if (document.readyState === 'loading') {\n        document.addEventListener('DOMContentLoaded', function () {\n          initResponsiveBackground();\n          initScrollFadeEffect(); // 添加调用\n        });\n      } else {\n        initResponsiveBackground();\n        initScrollFadeEffect(); // 添加调用\n      }\n    }\n    \n    \n    // ======================= 执行入口 =======================\n    initMedia();\n    \n    // 防抖处理窗口变化\n    let resizeTimer;\n    window.addEventListener('resize', () => {\n      clearTimeout(resizeTimer);\n      resizeTimer = setTimeout(() => {\n        // 计算当前方向状态\n        const currentIsPortrait = window.innerHeight > window.innerWidth;\n        const currentOrientation = currentIsPortrait ? 'portrait' : 'landscape';\n    \n        // 只有方向实际改变时才执行重载\n        if (lastOrientation !== currentOrientation) {\n          console.log('[背景加载器] 窗口大小变化，重新加载媒体');\n          initResponsiveBackground();\n        } else {\n          console.log('[背景加载器] 窗口大小变化但方向未改变');\n          // ================= 方向未变时重置透明度 =================\n          initScrollFadeEffect();\n        }\n      }, 500);\n    });\n    \n    // 页面可见性变化处理\n    document.addEventListener('visibilitychange', () => {\n      if (document.visibilityState === 'visible') {\n        const video = document.querySelector('#home-media-container video');\n        if (video && video.paused) {\n          console.log('[背景加载器] 页面恢复可见，重新播放视频');\n          video.play().catch(e => console.warn('视频恢复播放失败:', e));\n        }\n        // ================= 页面恢复可见时重置透明度 =================\n        initScrollFadeEffect();\n      }\n    });\n    \n    // ========== 新增修复代码（直接加在现有代码后面） ========== //\n    \n    // 1. 缓存恢复检测（核心修复）\n    window.addEventListener('pageshow', event => {\n      if (event.persisted && location.pathname === '/') {\n        console.log('[修复] 检测到缓存恢复主页，强制重置');\n        lastOrientation = null;\n        initResponsiveBackground();\n        // ================= 缓存恢复时重置透明度 =================\n        setTimeout(initScrollFadeEffect, 300);\n      }\n    });\n    \n    // 2. 路由变化监听（SPA兼容）\n    window.addEventListener('popstate', () => {\n      if (location.pathname === '/') {\n        console.log('[修复] 检测到返回主页');\n        setTimeout(() => {\n          // 检查媒体元素是否存在\n          const container = document.getElementById('home-media-container');\n          if (!container?.querySelector('.home-media')) {\n            lastOrientation = null;\n            initResponsiveBackground();\n          }\n          // ================= 返回主页时重置透明度 =================\n          initScrollFadeEffect();\n        }, 300); // 延迟确保DOM更新\n      }\n    });\n    \n    // 3. 媒体状态自检（兜底方案）\n    function checkMediaStatus() {\n      if (location.pathname !== '/') return;\n    \n      const container = document.getElementById('home-media-container');\n      if (!container) return;\n    \n      const hasMedia = container.querySelector('.home-media');\n      if (!hasMedia) {\n        console.log('[修复] 自检发现媒体丢失');\n        lastOrientation = null;\n        initResponsiveBackground();\n      }\n      // ================= 媒体自检时重置透明度 =================\n      initScrollFadeEffect();\n    }\n    \n    // 每0.5秒检查一次（轻量级检测）\n    setInterval(checkMediaStatus, 500);\n    \n    \n    ```\n\n---\n###### **第三步：修改主题配置并注入新文件**\n\n1.  **打开主题配置文件** (`themes/anzhiyu/_config.yml`)。\n\n2.  **修改首页顶部图配置**\n    * 找到 `index_img` 配置项，将其删除或注释掉。\n    * 然后，添加下面这个新的 `index_img` 和 `index_video` 结构。\n\n    ```yaml\n    # 首页媒体配置\n    # 注意：index_img 和 index_video 的 enable 只能有一个为 true\n    index_img:\n      enable: false # 如果想用图片背景，请将此项设为 true，并将下面的 index_video 的 enable 设为 false\n      path: '[您的横屏图片链接]'\n      vpath: '[您的竖屏图片链接]' # vpath 为竖屏设备显示的图片\n\n    index_video:\n      enable: true # ✅ 启用视频背景\n      path: '[您的横屏视频链接]' # 例如 https://yun.ayakasuki.com/...\n      poster: '[您的横屏视频加载动画GIF链接]' # 视频加载出来前的占位图\n      vpath: '[您的竖屏视频链接]'\n      vposter: '[您的竖屏视频加载动画GIF链接]'\n    ```\n\n3.  **注入新添加的CSS和JS文件**\n    * 在同一个主题配置文件中，找到 `inject:` 部分。\n    * 在 `head:` 列表的末尾添加一行，用于引入我们新建的CSS和JS文件。\n\n    ```yaml\n    inject:\n      head:\n        # - 其他 head 内容\n        - <link rel=\"stylesheet\" href=\"/css/index_media.css\">\n      bottom:\n        # - 其他 bottom 内容\n        - <script src=\"/js/index_media.js\"></script>\n ```\n *(**注意**：为便于主题管理，我们放在了 `source/css` 和 `source/js` 中，并在这里使用对应的路径引入)*\n\n---" };</div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E9%A6%96%E9%A1%B5%E4%B8%BB%E9%A2%98%E9%AD%94%E6%94%B9%EF%BC%9A%E9%A1%B6%E9%83%A8%E8%A7%86%E9%A2%91%E6%92%AD%E6%94%BE"><span class="toc-number">1.</span> <span class="toc-text">1.首页主题魔改：顶部视频播放</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D%E4%B8%8E%E9%87%8D%E8%A6%81%E6%8F%90%E7%A4%BA"><span class="toc-number">1.0.0.1.</span> <span class="toc-text">功能介绍与重要提示</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E6%9B%BF%E6%8D%A2%E6%A0%B8%E5%BF%83%E5%B8%83%E5%B1%80%E6%96%87%E4%BB%B6"><span class="toc-number">1.0.0.2.</span> <span class="toc-text">第一步：替换核心布局文件</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E6%B7%BB%E5%8A%A0%E8%87%AA%E5%AE%9A%E4%B9%89%E6%A0%B7%E5%BC%8F%E4%B8%8E%E8%84%9A%E6%9C%AC%E6%96%87%E4%BB%B6"><span class="toc-number">1.0.0.3.</span> <span class="toc-text">第二步：添加自定义样式与脚本文件</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E4%BF%AE%E6%94%B9%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E5%B9%B6%E6%B3%A8%E5%85%A5%E6%96%B0%E6%96%87%E4%BB%B6"><span class="toc-number">1.0.0.4.</span> <span class="toc-text">第三步：修改主题配置并注入新文件</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>