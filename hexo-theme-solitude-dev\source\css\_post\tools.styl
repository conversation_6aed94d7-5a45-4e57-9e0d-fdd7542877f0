#post
  .post-tools
    display: flex
    width: 100%
    justify-content: center
    margin-top: 8px
    flex-wrap: wrap
    user-select: none

    .post-tools-left
      white-space: nowrap
      display: flex
      gap: .5rem
      text-overflow: ellipsis
      justify-content: center
      flex-wrap: wrap

      #quit-box
        position: fixed
        width: 100vw
        height: 100vh
        background: rgba(0, 0, 0, .2)
        top: 0
        left: 0
        opacity: .01
        display: none
        z-index: 101
        margin: 0

        +maxWidth768()
          height: calc(100vh - 380px)

      // reward
      /.post-reward
        position: relative
        text-align: center
        display: flex
        justify-content: center

        &:hover
          > .reward-main
            display: block

          +minWidth768()
            > .reward-main
              display: flex
              justify-content: left

          .reward-button
            color: var(--efu-white)
            background: var(--efu-theme)
            box-shadow: none

        .reward-button
          background: var(--efu-red)
          color: var(--efu-white)
          padding: 0 16px
          height: 40px
          border-radius: 8px
          line-height: 39px
          box-shadow: var(--efu-shadow-red)
          display: flex
          align-items: center
          cursor: pointer
          transition: all .4s ease 0s

          i
            margin-right: 4px
            
          &:hover
            background-color: var(--efu-theme)

        .reward-main
          animation: .3s ease .1s 1 normal both running donate_effcet
          position: absolute
          bottom: 40px
          left: -96px
          z-index: 100
          display: none
          padding: 0 0 15px
          width: fit-content

          +maxWidth768()
            justify-content: center !important
            position: fixed
            bottom: 0
            left: 0
            right: 0
            z-index: 1003
            width: 100%
            margin: auto
            padding: 0

          .reward-all
            margin: 0
            padding: .8rem
            border-radius: 12px
            background: var(--efu-card-bg)
            border: var(--style-border-always)
            display: flex
            box-shadow: var(--efu-shadow-border)
            flex-direction: column
            align-items: center

            +maxWidth768()
              width: 100%
              display: flex
              flex-wrap: wrap
              justify-content: space-around
              padding: 30px 10px 60px
              border-radius: 12px 12px 0 0

            &::before
              position: absolute
              bottom: -10px
              left: 0
              width: 100%
              height: 20px
              content: ""

            .reward-title
              font-weight: 700
              color: var(--efu-red)
              text-wrap: wrap

            .reward-group
              display: flex
              margin-top: .5rem

              .reward-item
                display: inline-block
                padding: 0 8px
                list-style-type: none
                vertical-align: top

                img
                  width: 130px
                  height: 130px

                .post-qr-code-desc
                  padding-top: .4rem
                  width: 130px
                  color: var(--efu-gray)
                  margin-top: -8px
                  margin-bottom: 8px

                .post-qr-code-img
                  box-shadow: var(--efu-shadow-lightblack)
                  border-radius: 12px
                  border: var(--style-border-always)
                  image-rendering: pixelated

            .reward-main-btn
              background: var(--efu-secondbg)
              color: var(--efu-fontcolor)
              display: flex
              flex-direction: column
              border-radius: 12px
              padding: 4px 0
              border: var(--style-border-always)
              width: 100%

              &:hover
                color: var(--efu-white)
                background: var(--efu-red)
                background-image: url(/img/flower.gif)
                box-shadow: var(--efu-shadow-red)

              .reward-text
                margin-bottom: 0
                font-weight: 700

              .reward-dec
                font-size: .6rem
                text-wrap: wrap

      // rss

      .reward-link.mode
        background: var(--efu-green)
        color: var(--efu-white)
        padding: 0 16px
        height: 40px
        line-height: 39px
        box-shadow: var(--efu-shadow-green)
        display: flex
        align-items: center
        border-radius: 8px
        cursor: pointer
        text-align: center
        transition: .3s

        a
          color: var(--efu-white)

        i
          margin-right: 4px

        &:hover
          background: var(--efu-theme)
          box-shadow: none


  .post-tools-right
    display: flex
    align-items: center
    flex-direction: row
    justify-content: space-between
    flex-wrap: wrap
    padding 0 2rem
    +maxWidth768()
      padding: 0 .5rem

    .tag_share
      .post-meta__tag-list
        display: flex
        padding: 0
        width: 100%
        flex-wrap: wrap
        flex-direction: row

        a
          margin-bottom: 8px
          margin-right: 8px

        .post-meta__tags
          background: var(--efu-card-bg)
          border: var(--style-border-always)
          color: var(--efu-fontcolor)
          border-radius: 8px
          display: flex
          align-items: center
          white-space: nowrap
          height: 40px
          padding: 0 .6rem
          width: fit-content
          font-size: .85em
          transition: all .2s ease-in-out 0s

          &:hover
            background: var(--efu-lighttext)
            box-shadow: var(--efu-shadow-main)
            color: var(--efu-white)

            span.tagsPageCount
              color: var(--efu-lighttext)
              background: var(--efu-maskbgdeep)
              transition: all .2s ease-in-out 0s

          span.tags-punctuation
            font-size: 12px
            margin-right: 1px
            display: flex
            align-items: center

          .tagsPageCount, /  #tag-page-tags .tagsPageCount
            padding: 2px
            background: var(--efu-fontcolor)
            border: var(--style-border-always)
            min-width: 22.5px
            display: inline-block
            border-radius: 4px
            text-align: center
            font-size: .6rem
            color: var(--efu-card-bg)
            margin-left: 4px