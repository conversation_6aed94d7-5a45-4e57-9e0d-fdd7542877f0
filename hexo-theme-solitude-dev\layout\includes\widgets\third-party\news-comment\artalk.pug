- const { server, site, option } = theme.artalk
- const avatarCdn = theme.comment.avatar

script(pjax).
    (() => {
        const changeContent = content => {
            if (content === '') return content;
            const replacements = [
                {
                    regex: /<img.*?src="(.*?)"?[^\>]+>/ig,
                    replacement: '[!{_p("console.newest_comment.image")}]'
                },
                {
                    regex: /<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi,
                    replacement: '[!{_p("console.newest_comment.link")}]'
                },
                {
                    regex: /<pre><code>.*?<\/pre>/gi,
                    replacement: '[!{_p("console.newest_comment.code")}]'
                },
                {
                    regex: /<[^>]+>/g,
                    replacement: ''
                }
            ];
            content = replacements.reduce((str, {regex, replacement}) => str.replace(regex, replacement), content);
            return content.length > 150 ? content.substring(0, 100) + '...' : content;
        };

        const generateHtml = (array, asideList) => {
            asideList.innerHTML = array.length ? array.map(item => `
                <div class='aside-list-item'>
                    <div class='thumbnail'>
                        <img src='${item.avatar}' alt='${item.nick}'>
                    </div>
                    <div class='content'>
                        <div class='comment' onclick='pjax.loadUrl("${item.url}")'>${item.content}</div>
                        <time class="datetime" datetime="${item.date}"></time>
                    </div>
                </div>
            `).join('') : "!{_p('newest_comment.zero')}";
            window.lazyLoadInstance?.update();
            window.pjax?.refresh();
            sco?.changeTimeFormat(document.querySelectorAll('.aside-list-item time'));
        };

        const getSetting = async () => {
            try {
                const res = await fetch('!{server}/api/v2/conf', {method: 'GET'});
                return await res.json();
            } catch (e) {
                console.error(e);
                return null;
            }
        };

        const getComment = async (asideList) => {
            const searchParams = new URLSearchParams({'site_name': "!{site}", 'limit': '6'});
            await fetch(`!{server}/api/v2/stats/latest_comments?${searchParams}`, {method: 'GET'}).then(async res => {
                const result = await res.json();
                const avatarConfig = await getSetting();
                const avatarCdn = '!{avatarCdn}' + '/avatar/';
                const params = avatarConfig?.gravatar?.params || '';
                const artalk = result.data.map(e => ({
                    avatar: avatarCdn + e.email_encrypted + '?' + params,
                    content: changeContent(e.content_marked),
                    nick: e.nick,
                    url: e.page_key + `#atk-comment-${e.id}`,
                    date: e.date,
                })).slice(0, !{limit});
                utils.saveToLocal.set('artalk-newest-comments', artalk, !{theme.comment.newest_comment.storage});
                generateHtml(artalk, asideList);
            }).catch(error => {
                console.error(error);
                asideList.textContent = "!{_p('newest_comment.error')}";
            });
        };

        const newestCommentInit = (asideList) => {
            if (!asideList) return;
            const data = utils.saveToLocal.get('artalk-newest-comments');
            if (data) {
                generateHtml(data, asideList);
            } else {
                getComment(asideList);
            }
        };

        const $asideList = document.querySelector('.card-recent-comment .aside-list');
        window.addEventListener('DOMContentLoaded', () => newestCommentInit($asideList), false)
        window.addEventListener('pjax:complete', () => newestCommentInit(document.querySelector('.card-recent-comment .aside-list')), false);
    })();