---
title: 1️⃣ 内容产品模型实战
categories:
  - 产品经理实战
tags:
  - 产品经理实战
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp'
comments: true
toc: true
ai: true
abbrlink: 30401
date: 2025-07-18 16:13:45
---

# 第一章：内容产品模型

## 1.1 内容产品概述

### 1.1.1 学习目标

![image-20250718204934616](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718204934616.png)

### 1.1.2 什么是内容产品？

**内容产品，就是以图文、视频/直播、音频等形式为用户提供内容服务的产品形态。**

这句话揭示了内容产品的两个核心要素：

1.  **核心载体:** 内容。这可以是引人深思的一段文字、一张精美的图片、一段动听的音频，或是一段引人入胜的视频。
2.  **最终目的 :** 服务。通过这些内容，满足用户在信息获取、学习提升、娱乐消遣、情感共鸣、社交连接等方面的需求。

因此，我们可以这样理解：**内容产品本质上是一个围绕“内容”进行价值交换的系统。** 在这个系统中，用户付出他们的**时间、注意力和金钱**，来换取平台提供的**信息价值、娱乐价值和情感价值**。

| | | | |
| :--- | :--- | :--- | :--- |
| **图文类** | **视频类** | **音频类** | **综合/社区类** |
| 微信公众号 | 抖音 | 喜马拉雅 | 小红书 |
| 知乎 | Bilibili | Spotify | 微博 |
| 今日头条 | YouTube | 各类播客App | 豆瓣 |





### **1.1.3 内容产品架构与生态**

首先，**用户端**是我们最熟悉的部分，它直接服务于图下方的“用户”。这是我们产品的“脸面”和“橱窗”，我们所有的努力，比如设计更沉浸的播放体验、更精准的信息流推荐，都是为了让用户在这里能高效、愉悦地消费内容，并且愿意留下来。

其次，**自媒体端**是服务于图上方“内容生产”者的“创作工坊”。根据我的经验，一个产品的内容生态能否繁荣，很大程度上取决于自媒体端的体验。我们是否提供了足够便捷的编辑器？数据分析是否足够清晰，能帮助创作者优化内容？这些决定了创作者是否愿意持续为我们提供“内容弹药”。

最后，是隐藏在幕后的“中央系统”——**平台端**。它对应着图中“平台”这个中央枢纽的后台能力。这部分是产品运营和管理人员使用的，我们通过它来审核内容、配置推荐策略、管理用户、对接广告主，它保证了整个生态的秩序和商业目标的实现。

![image-20250718210401085](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718210401085.png)

| **架构组成** | **核心服务对象 (对应图中角色)** | **核心目标** | **关键功能举例** |
| :--- | :--- | :--- | :--- |
| 用户端 | 用户 | 满足内容**消费**和互动需求，提升留存与时长。 | 信息流、搜索、播放/阅读页、点赞、评论、分享、个人主页 |
| 自媒端 | 内容生产者 | 满足内容**生产**和管理需求，提升创作效率与意愿。 | 文章/视频编辑器、内容发布、数据看板、粉丝管理、收益中心 |
| 平台端 | **平台运营管理者** | **管理**整个平台的用户与内容，确保生态健康与商业运转。 | 内容审核系统、用户管理、推荐算法配置、广告系统、数据监控后台 |

现在，再回过头提供的那张生态图，一切就都清晰了。我们作为产品经理，设计的内部架构（表格内容），完美地服务和驱动了外部的商业生态。我们设计的每一个功能，都应该能清晰地回答：它属于哪个端口？它在为生态中的哪个角色解决什么问题？









## 1.2 内容产品设计模型

### 1.2.1 学习目标

![image-20250718210534714](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718210534714.png)

### 1.2.2 内容生产

#### 1. PGC（Professionally-generated Content）

在内容产品的世界里，我们首先遇到的就是PGC（Professionally-generated Content），我喜欢称之为“专业生产内容”。顾名思义，它的核心在于**“专业”**二字。

PGC是一种相对传统的内容生产方式。它的创作者通常是平台官方的工作人员（如编辑、记者）、与平台签约的领域专家，或是专业的MCN机构。

看图中的新闻App截图，无论是报道国家大事，还是分析财经动态，都体现出高度的专业性、规范性和权威性，这就是最典型的PGC。

作为产品经理，我在规划内容生态时，非常看重PGC的价值。它最大的**优势在于“质量和规范可控”**。因为生产者是专业的，我们可以确保内容的准确性、深度和品牌调性相符。这在产品冷启动阶段尤其重要，高质量的PGC内容能够为平台快速树立起专业、可信的品牌形象，并为整个内容生态定下基调。

然而，PGC的**劣势**也同样致命，那就是**“数量不足，难以满足消费需求”**。专业内容的生产成本高、周期长，一个编辑团队无论多么高效，其产出量终究是有限的。

这就像一家米其林餐厅，虽然菜品精致，但无法满足全城人一日三餐的需求。当平台用户规模扩大后，仅靠PGC是远远无法满足用户五花八门、海量的内容消费需求的。

为了让你能快速把握PGC的要点，我把它总结成了下面这张表：

| **维度** | **描述** |
| :--- | :--- |
| **生产者** | 平台官方、签约专家、合作机构、媒体等专业团体或个人。 |
| **内容特点** | 垂直深度、制作精良、标准化、权威性高。 |
| **核心优势** | **质量与规范可控**，是平台打造品牌、建立信任的基石。 |
| **核心劣势** | **生产成本高，内容数量和多样性有限**，难以规模化。 |
| **适用场景** | 平台冷启动、核心栏目、深度专题、官方活动、付费课程等。 |

总而言之，我认为PGC是内容平台不可或缺的“定海神针”。它负责为平台打造高品质的“门面”和“样板间”。但要让平台真正繁荣起来，光靠这支“正规军”是远远不够的，我们必须引入另一股更庞大、更多元的力量，这就是我们接下来要讲的UGC。



#### 2. UGC（User Generated Content）

如果说PGC是内容生态里的“正规军”，那么UGC（User Generated Content）就是汪洋大海般的“人民战争”。它的出现，彻底改变了内容生产的游戏规则。

UGC是一种全新的内容生产方式，它的核心理念是：**“内容产品的使用者，也是产品内容的生产者”**。

这意味着，平台上每一个普通的、匿名的用户，都可以成为内容的创作者。你我他，只要愿意，都可以发布一条动态、写一篇点评、录一段视频、发一条弹幕。

从我作为产品经理的视角来看，UGC最大的**优势**，在于它用一种极低成本的方式，解决了PGC无法解决的两个核心问题：**“内容量大”**和**“满足多样消费需求”**。成千上万的用户自发地产出内容，其数量和覆盖面的广度是任何专业团队都无法比拟的。

从美食探店到萌宠日常，从游戏攻略到情感树洞，UGC能够满足用户几乎所有长尾、细分的需求，这是构建一个繁荣、活跃社区的绝对基础。

然而，这种自由创作的模式也带来了它最致命的**劣势**，那就是**“内容质量不均，需要审核机制”**。当任何人都可以发布内容时，内容的质量就变得不可控，低质、灌水、甚至违规的内容会大量涌现。因此，对于一个以UGC为主的平台，**建立强大、高效的审核机制（包括机器审核和人工审核）就不是一个可选项，而是一个生死攸关的必选项**。这是我们作为产品经理必须承担的责任。

同样，我为你准备了一张表格，来清晰地对比UGC和PGC的差异。

| **维度** | **描述** |
| :--- | :--- |
| **生产者** | 平台的任何普通用户，通常是匿名的、非专业的。 |
| **内容特点** | 数量巨大、形式多样、生活化、互动性强、质量参差不齐。 |
| **核心优势** | **生产成本极低，内容量和多样性极大**，能满足用户的长尾需求。 |
| **核心劣势** | **内容质量不可控**，平台需要投入巨大的审核和运营成本来维护社区环境。 |
| **适用场景** | 社交媒体（微博）、社区（小红书/知乎）、点评（大众点评）、短视频（抖音/快手）等。 |

**我的思考与总结：**

在实际的产品工作中，我们极少会遇到一个平台是纯PGC或纯UGC的。更常见的情况是，**PGC和UGC是相辅相成的**。

一个健康的策略往往是：**用PGC来定义平台的调性、树立质量标杆（“正规军”打样板），再用UGC来丰富平台的内容生态、提升社区的活跃度（“人民战争”促繁荣）**。如何设计一套好的机制，让这两股力量和谐共存、互相促进，是我们产品经理需要不断探索的艺术。



### 1.2.3 内容审核

#### 1. 文本内容审核

我们刚刚聊完UGC带来的海量内容，那紧接着一个必须面对的问题就是：如何管理这些内容，确保社区的“干净”？这就是内容审核的范畴，它是维持平台健康生态的生命线。

我们先从最基础、也是应用最广泛的**文本内容审核**开始。

在我的经验里，处理文本内容最直接的方式就是**自动审核**，特别是基于敏感词的过滤系统。这里的基本逻辑非常简单，就像这张图里展示的A到B的过程。

![image-20250718211313829](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718211313829.png)

**第一步（图中的A），是“设置敏感词”**。我会和我的运营、法务同事一起，在平台的后台系统里，共同维护一个“敏感词词库”。这个词库是动态更新的，我们会持续地把涉及色情、暴力、赌博、政治、广告、人身攻击等各类违规的词语加进去。这相当于我们为平台的“安保系统”配置好了要抓捕的“黑名单”。

**第二步（图中的B），是“识别处理”**。当用户发布任何文本内容时，比如一篇帖子、一条评论，我们的系统会自动将这段文本与后台的敏感词库进行秒级匹配。一旦命中词库里的某个词，系统就会立刻执行我们预设好的处理动作。

这个“处理动作”具体是什么，是我们作为产品经理需要精心设计的。简单的用星号（`*`）替换只是其中一种。根据违规词语的严重等级，我通常会设计一套组合策略。我把这些常见的处理方式整理成了一个表格，方便我们理解。

| **处理方式** | **描述** | **我通常应用的场景** |
| :--- | :--- | :--- |
| **替换** | 将命中的敏感词替换为`*`等符号，内容本身依然可以发出。 | 对用户体验影响最小，适用于一般性的不文明用语、脏话等。 |
| **拦截** | 直接阻止该内容的发布，并明确告知用户违规原因。 | 适用于垃圾广告、恶意导流、联系方式等明确的违规行为。 |
| **仅作者可见** | 内容成功发布，但只有作者自己能看到，同时自动进入人工审核队列。 | 适用于内容疑似违规，但不确定，需要人工介入判断的情况。给用户一种“已发出”的错觉，可以减少其申诉和修改绕过的行为。 |
| **先审后发** | 内容发布后，不会立即对外展示，必须等待人工审核通过后才可见。 | 适用于高风险场景，如金融、医疗内容的发布，或针对有过多次违规记录的用户。 |

**我的总结：**

我要强调一点，单纯的关键词审核只是万里长征的第一步。现在的用户非常聪明，会用谐音、拆字、加符号等各种方式来绕过审核，比如“V信”“威信”“vievie”等等。因此，在如今的平台上，敏感词系统通常只是作为基础的、第一道防线。更成熟的平台，会在此之上，结合基于机器学习的文本分类模型、用户行为分析等多种技术手段，来构建一个立体、智能的审核系统。

#### 2. 图片/音视频内容审核

如果说文本审核是“普通难度”，那么图片、音频和视频的审核就是“困难模式”。这类非结构化内容的审核，其技术门槛和复杂度要高出几个量级。

在我的职业生涯中，对于这类审核，我的原则非常明确：**除非公司是专做AI视觉技术的，否则我们坚决不自研，而是选择“购买服务”**。

为什么？因为从零到一自研一套图片/音视频识别AI模型，需要顶尖的算法团队、海量的标注数据和强大的计算资源，这对于绝大多数公司来说，投入产出比极低。

![image-20250718211731684](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718211731684.png)

这张截图里的阿里云“图片涉政暴恐识别”服务，就完美印证了我的观点。目前，**阿里、百度、腾讯**等一线云服务大厂，都已经把这项能力做成了非常成熟的商业化服务。我们作为产品开发者，只需要付费调用他们的API接口，就能获得世界一流的审核能力。

这个过程对我们来说，就像是把专业的事交给了最专业的人。我来为你拆解一下我们作为产品经理，在这里需要做的事情：

1.  **技术选型**：我们会对几家主流服务商（如阿里云、腾讯云等）进行评估，对比他们的识别准确率、支持的审核维度、API调用价格、服务稳定性等，选择最适合我们业务的合作伙伴。

2.  **策略制定**：这是我们的核心工作。调用API后，第三方服务会返回给我们一个包含各类标签和“置信度”分数的结果。比如，一张图片可能会返回`{“色情”: 0.98, “暴恐”: 0.15, “广告”: 0.4}`这样的数据。我们需要根据业务的风险容忍度，来制定处理规则。例如：
    * 色情分数 > 0.95，**直接拦截**。
    * 广告分数在 0.7-0.9 之间，**转人工审核**。
    * 所有分数都 < 0.3，**自动通过**。
    这个阈值的设定，需要我们不断地根据实际运营情况去调整和优化。

3.  **成本控制**：要牢记，——**“需要付费”**。这类服务是按调用次数（如张数）或时长（如分钟）收费的。如果我们的产品有海量的图片/视频上传，这笔审核开销会非常巨大。因此，我必须在产品设计之初，就将这部分成本纳入运营预算，并持续监控其开销。

为了让你更清晰地掌握要点，我整理了下面的表格：

| **维度** | **描述** |
| :--- | :--- |
| **核心逻辑** | **不自研底层技术**，通过成熟的第三方云服务API来解决专业问题。 |
| **主要服务商** | 阿里云、腾讯云、百度智能云、七牛云等。 |
| **常见审核维度** | 涉黄、涉政、暴恐、广告、灌水、违禁品、公众人物识别等。 |
| **PM的核心工作** | **技术选型、策略制定（设置阈值）、成本控制**。 |







---

### **1.2.4 内容分发**

在我看来，如果我们把内容生产比作“做饭”，内容审核比作“品控”，那么**内容分发就是“上菜”**。菜做得再好，品控再严，如果不能精准、高效地送到想吃这道菜的食客面前，那一切都是徒劳。

#### **1.内容分发的本质**

所以，内容分发的本质，我总结为一句话：**实现内容与用户之间，最高效、最合适的匹配**。我们作为产品经理，设计的一切分发机制，无论是信息流、推荐位还是热榜，都是为了这个终极目标服务。

#### **2. 常见的内容分发方式**

在产品的不同发展阶段和不同场景下，我会运用不同的分发策略。经过多年的演化，我主要将它们归为以下四种主流方式。

---

##### **一、 编辑分发**

![image-20250718212239607](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212239607.png)

这是最古老、最经典的分发方式，我称之为`“总编辑模式”`。

它的核心是由平台内部的编辑团队，基于其专业判断和平台调性，人工挑选出他们认为的“好内容”，并决定这些内容展示在哪个位置、展示给多少人。我们早期看到的各大门户网站首页，就是最典型的编辑分发。

**我的解读**：
* **优点**：在于**质量可控**和**价值引导**。在产品初期，我可以通过编辑精选，快速为产品树立起高质量、有调性的品牌形象，告诉用户“我们这里有什么”。
* **缺点**：是**中心化**的，用户的选择是被动的。并且，它极度依赖编辑的个人能力，成本高、效率低，无法满足海量用户的个性化需求。

---

##### 二、 订阅分发

![image-20250718212322555](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212322555.png)

如果说编辑分发是“平台喂给你什么，你就看什么”，那么订阅分发就是把选择权交还给了用户，我称之为`"报刊亭模式"`。

在这种模式下，用户可以主动“关注”或“订阅”自己感兴趣的创作者（KOL）、专栏或话题。这样，用户就构建了属于自己的信息获取渠道。我们关注微信公众号、B站UP主，都属于订阅分发。

但这种需要平台需要有KOL基础,也就是发展初期是不太适合做订阅分发,难以招揽到大牌博主入驻

**我的解读**：

* **优点**：用户是主动选择，因此粘性非常高，容易围绕KOL构建起`“私域流量”`，用户忠诚度强。
* **缺点**：对用户的“发现能力”要求高，用户需要自己去找到值得订阅的目标。对于新用户和新创作者来说，冷启动会比较困难。

---

##### **三、 社交分发**

![image-20250718212412442](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212412442.png)

社交分发是一种极具爆发力的分发方式，我称之为``。

内容不再仅仅依赖平台或创作者，而是通过用户自身的社交关系链进行传播。我的朋友在朋友圈分享了一篇文章，我认为有价值，再次转发，这样一传十、十传百地扩散出去。

**我的解读**：
* **优点**：基于`“信任代理”`，朋友推荐的内容我更愿意相信和打开。传播速度快，容易产生裂变效应，引爆话题。
* **缺点**：内容分发的广度和速度变得不可控，并且非常依赖内容的“社交属性”，即是否足够有趣、有槽点、有共鸣，能激发用户的分享欲。

---

##### 四、 算法分发

![image-20250718212636209](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718212636209.png)

这是当下最主流，也是技术含量最高的方式，我称之为`“私人助理模式”`。

平台通过强大的算法模型，分析用户的历史行为（点击、点赞、停留时长等），理解其兴趣偏好，然后从海量内容池中，为每一个用户“量身定制”一个独一无二的信息流。今日头条和抖音是算法分发的集大成者。

**我的解读**：

* **优点**：**极致的个性化和高效率**。它能让用户“沉迷”其中，因为每一条都是你可能感兴趣的，极大地提升了用户粘性和使用时长。
* **缺点**：容易形成`“信息茧房”`，让用户视野越来越窄。同时，它对平台的内容总量和用户规模有很高的要求，小平台很难玩转。

为了方便我们整体回顾，我将这四种方式的核心特点总结在了一张表里：

| **分发方式** | **核心逻辑** | **用户角色** | **优点** | **缺点** |
| :--- | :--- | :--- | :--- | :--- |
| **编辑分发** | 编辑人工精选 | 被动接收者 | 质量、调性可控 | 效率低、非个性化 |
| **订阅分发** | 用户主动关注 | 主动选择者 | 粘性高、关系强 | 发现效率低、启动难 |
| **社交分发** | 好友分享推荐 | 传播节点 | 信任度高、裂变快 | 不可控、依赖内容社交性 |
| **算法分发** | 机器智能推荐 | 沉浸体验者 | 高效率、个性化 | 易产生信息茧房 |

在我的实际工作中，现代内容产品几乎不会只采用单一的分发模式，而是将这四种方式进行**混合**。比如，一个信息流里，既有我订阅的，也有算法推荐的，还可能夹杂着编辑精选的热点。如何调配好这四种模式的比例和权重，正是我们产品经理需要不断探索和优化的核心策略。


### **1.2.5 内容消费**

在我看来，内容产品的商业模式，本质上都是在**内容消费**这个环节做文章。我们将消费模式分为两种最基本的形式：**免费消费**和**付费消费**。它们不是对立的，而往往是共存的、互为补充的。

![image-20250718213151675](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213151675.png)

-----

#### 一、 免费消费模式

![image-20250718213203871](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213203871.png)

这是互联网内容平台的基石，也是我们用来吸引海量用户的核心手段。我始终认为，**免费是为了更好地收费**。

在免费模式下，我们向用户提供大量有价值的免费内容，其核心目的有两个：

1.  **降低用户门槛**：让尽可能多的用户无成本地体验我们的产品，并留存下来。
2.  **建立信任和展示价值**：通过免费内容，让用户了解我们的“实力”，认可我们平台的价值。

图中那个**漏斗模型**非常形象地表达了我的想法。免费用户就是漏斗最顶端的巨大流量池。我们的工作，就是通过产品设计和运营手段，筛选并引导其中一小部分认可我们价值的用户，一步步地走向漏斗下方的付费环节，完成转化。

-----

#### 二、 付费消费模式

当用户愿意付费时，意味着我们要提供远超免费内容的“超额价值”。什么样的内容才具备这种价值呢？我通常会从以下四个特性去评估。

![image-20250718213242810](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213242810.png)

  * **1. 即时性**：人无我有的第一手信息。比如，付费的财经社群里，比公开市场更早发布的独家分析；或者，粉丝付费后可以比普通用户提前一周看到创作者的最新视频。
  * **2. 专业性**：高度体系化、结构化的深度知识。这是知识付费最核心的逻辑。用户付费购买的不是零散的知识点，而是一位专家经过系统梳理后的完整知识体系，比如一门精心设计的在线课程。
  * **3. 个性化服务**：针对个人情况提供的专属服务。比如，付费的一对一咨询、个人化的学习路径规划、有问必答的社群服务等。
  * **4. 唯一性**：只有这里才能获得的独特资源或体验。比如，某个创作者独家的、不对外公开的创作手稿；或者，只有付费会员才能进入的私密社群。

-----

光有好的付费内容还不够，如何设计付费环节，直接决定了用户的付费转化率。在这方面，我总结了三个必须做到的设计要点。

![image-20250718213331617](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718213331617.png)

  * **1. 凸显服务价值**：在用户付费前，我们必须用尽一切办法让他清晰地感知到“我将获得什么”。这包括精美的课程介绍页、详细的课程大纲、来自其他用户的真实好评、免费的试听/试读章节等。价值感塑造得越好，用户的付费意愿就越强。
  * **2. 明确服务有效期**：这是建立信任的基础。我们必须在最显眼的位置清楚地告诉用户：这次购买是一次性买断、永久有效的？还是按月/按年订阅的？服务的具体范围和期限是什么？任何模糊不清的描述都是在扼杀交易。
  * **3. 简化支付流程**：用户从下定决心到完成支付的路径，每增加一个步骤，都会流失一批用户。因此，我追求的是让支付流程“如丝般顺滑”。这包括接入主流的支付方式（微信/支付宝）、支持一键支付、减少不必要的表单填写等。

最后，我将内容消费环节的核心思考，总结为下面这张简表，这也是我在做商业化设计时，反复会问自己的两个问题。

| **核心问题** | **我的思考要点** |
| :--- | :--- |
| **1. 什么内容值得用户付费？** | 内容必须具备稀缺性，至少符合**即时性、专业性、个性化、唯一性**中的一种。 |
| **2. 付费功能应该如何设计？** | **价值前置**：让用户未付费就感知到价值。<br>**信息透明**：权益、期限一目了然。<br>**流程极简**：让支付成为一种轻松、无障碍的体验。 |




---




# 第二章：需求收集与管理

## 2.1 什么是需求

在我的产品经理生涯中，我始终认为，一切工作的起点和终点都是“需求”。如果我们对需求的理解出现了偏差，那么后续无论多精美的设计、多优秀的技术，都只是在错误的地基上建造楼阁，最终难免会坍塌。那么，到底什么是需求？让我们一起深入地探索它的本质。

### 2.1.1 学习目标

![image-20250718214644630](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214644630.png)

在本节中，我的核心目标是帮助我们建立起对“需求”的深度认知。我希望在本节学习结束后，我们都能具备一种“穿透”能力——能够穿透用户表面的只言片语，直达他们内心深处真正的、未被言说的渴望与痛点。

### 2.1.2 公司背景与项目背景

在讨论理论之前，我习惯先设定一个场景，因为脱离了场景谈需求，就如同无源之水。让我们虚构一个案例背景，以便更好地代入思考。

1.  **公司背景**
    我们是一家B2B生鲜食材供应商，通过自研的线上平台，为全国数千家餐厅提供每日的食材采购与配送服务。

2.  **项目背景**
    近期，平台的客服部门收到了大量餐厅采购员的抱怨，普遍反映我们的下单流程烦琐、效率低下。因此，公司决定立项，由我来负责优化平台的下单体验，提升客户满意度和下单频次。

### 2.1.3 什么是需求（案例）

![image-20250718214737611](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214737611.png)





![image-20250718214833975](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214833975.png)

![image-20250718214842638](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718214842638.png)

好了，背景就绪。现在，作为这个项目的PM，我开始接触来自一线用户的声音。在我的经验里，这些声音（也就是最初始的需求）通常以三种面貌出现。

1.  **案例1：用户以“提问题”方式给出的需求**
    一位用户在App的反馈区留言：“每天都不知道我要吃啥，我怎么才能知道今天应该点什么外卖呢？”
    * **我的解读**：用户提出的“问题”，是他目前面临的问题，或许我们可以推出一个简单的小插件入口，随机抽取今天应该吃什么？。
2.  **案例2：用户以“提目的”方式给出的需求**
    一位用户反馈说：“我每天吃饭的预算有限，希望平台能够让我自己快速点到20块钱以内的外卖。”
    * **我的解读**：这位用户给了我一个非常明确的“目的”——“快速找到20元以内的外卖”。这是一个清晰的、待满足的诉求。我的工作就是思考如何最好地帮他达成这个目的，是增加一个价格区间筛选？还是专门开辟一个“平价专区”？
3.  **案例3：用户以“提方案”方式给出的需求**
    一位用户希望我们做一个“智能推荐”功能。他描述道：“只要一点就进入这个推荐，就提供价钱选择，还有结合自己的口味和当下季节以及哪个地方的给推荐性价比口碑最好的外卖，并且支持点击一下就自动付款下单的功能。”
    * **我的解读**：这是一位“高阶”用户，他不仅提了目的，甚至帮我把完整的产品“方案”都设计好了。这恰恰是最需要我警惕和深度分析的情况。他这个宏大的方案里，其实包含了多个本质目的的集合：“我懒得选（要智能推荐）”、“我要省钱（要性价比）”、“我要好吃（要口碑好）”、“我要方便（要一键下单）”。我的职责不是照抄这个方案，而是将这些本质目的拆解开来，评估实现难度和用户价值，设计出更合理、更可落地的产品方案。

---

### 2.1.4 需求的常见形式

![image-20250718215247811](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718215247811.png)

通过上面三个小案例，我们可以总结出，用户最原始的需求通常以三种形式传递给我们。我把它们整理成了下面的表格，方便我们记忆。

| **形式** | **用户表达方式** | **我的解读 & 应对思路** |
| :--- | :--- | :--- |
| **提问题** | “为什么……？” “怎么……？” | 用户在某个操作中遇到了困难，感到困惑。我需要追问，定位他被卡住的场景和具体痛点。 |
| **提目的** | “我想要……” “我希望能……” | 用户明确表达了期望达成的效果。我需要思考，达成这个目的，有哪些可能的路径和方案？ |
| **提方案** | “你应该加个……” “只要做个……” | 用户给出了自认为的解决方案。我需要“翻译”——这个方案是为了解决什么问题？有没有更好的方案？ |

### 2.1.5 需求的定义

![image-20250718215610820](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718215610820.png)

那么，综合以上所有，我来给出我对“需求”的最终定义。我认为需要区分两个概念：

1.  **原始需求**
    即用户直接表达出来的，未经加工的“问题”、“目的”或“方案”。它是我们工作的输入和起点。
2.  **产品需求**
    这是我们产品经理经过分析、挖掘、转化后，真正应该去做的东西。我对它的定义是：
    - **在特定场景下，为满足用户的本质目的，我们所设计出的一套完整的解决方案。**

所以，我的工作，从来不是对用户的“原始需求”照单全收，而是要经历一个“**原始需求 → 本质目的 → 产品需求**”的深度转化过程。这趟旅程的质量，决定了我们最终产品的成败。


---

## 2.2 需求如何收集

我们已经深刻理解了“需求”的本质，知道它藏在用户的只言片语背后。那接下来的问题就是，我们该去哪里、以及如何才能高效地把这些“藏着”的需求挖掘出来？

这就是需求收集的工作。在我看来，这绝不是一个被动等待的过程，而是一项需要我们主动出击、运用多种侦查手段的系统工程。

### 2.2.1 学习目标

![image-20250718220021847](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220021847.png)

在这一节，我的目标是为我们装备一套实用的“需求挖掘工具箱”。我将带大家梳理需求的来源，并详细介绍几种我最常用且行之有效的收集方法，包括但不限于竞品分析、用户访谈等。学完本节，我希望我们都能根据不同的目的和场景，自信地选择并运用最合适的工具。

### 2.2.2 需求的来源

![image-20250718220043430](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220043430.png)

在动手挖掘之前，我们先要画出“藏宝图”，明确需求可能藏在哪些地方。我习惯将所有的需求来源归为两大类：**外部需求**和**内部需求**。一名优秀的产品经理，必须同时对这两个方向保持敏锐。

1.  **外部需求**
    这类需求来自于我们公司“围墙”之外，是市场和用户的直接声音。它包括：
    * **用户**：通过用户访谈、反馈、调研等直接获取。
    * **客户**：对于B端产品，这是指付费客户提出的具体要求。
    * **竞品**：通过分析竞争对手的动向和功能。
    * **市场/行业**：宏观的政策变化、技术趋势、社会热点等。

2.  **内部需求**
    这类需求源自于公司内部的各个协作方，通常服务于公司的战略和商业目标。它包括：
    * **老板/管理层**：基于公司战略发展提出的方向性要求。
    * **运营/市场团队**：为支撑某项运营活动或营销策略而提出的产品需求。
    * **销售/客服团队**：来自一线炮火声，为解决客户问题或促进销售而提出的需求。
    * **技术/设计团队**：出于提升系统性能、优化架构、统一设计规范等内部优化目的提出的需求。

### 2.2.3 需求收集方式分类

![image-20250718220157829](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220157829.png)

了解了需求的来源，我们就要选择具体的“挖掘工具”了。在此之前，我先把这些工具（也就是收集方法）从性质上分为两类：**定性方式**和**定量方式**。想清楚用哪类方式，能让我们的目标更明确。

* **定性方式（Qualitative）**
    我用它来回答“**为什么**”。当我需要深入探索用户的动机、感受、行为背后的原因时，我会采用定性方式。它的特点是**样本小但洞察深**。比如，我可以通过访谈，真正理解一个用户**为什么**对我的产品感到“不爽”。

* **定量方式（Quantitative）**
    我用它来回答“**是什么**”和“**有多少**”。当我想验证一个假设、或者了解某个现象的普遍性时，我会采用定量方式。它的特点是**样本大且结果可以被统计**，能反映普遍规律。比如，我可以通过问卷，了解到底有百分之多少的用户认为我的产品“不好用”。

为了方便你理解，我总结了下面的表格：

| **方式分类** | **核心目的** | **特点** | **常用方法举例** |
| :--- | :--- | :--- | :--- |
| **定性方式** | 探究“为什么？” | 深入、有背景、样本小、无法统计 | 用户访谈、实地调研、可用性测试 |
| **定量方式** | 度量“是什么/有多少？” | 广泛、可统计、样本大、结论客观 | 问卷调查、数据分析、A/B测试 |

### 2.2.4 常见的需求收集方法

![image-20250718220406802](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718220406802.png)

在我的工具箱里，有许多种具体的需求收集方法。经过多年的实践，我筛选出了最高效、最常用的一批，它们几乎能覆盖我工作中90%的场景。这些方法包括：

* 用户访谈：通过对于用户的访谈掌握需求
* 问卷调查：通过发放调查问卷来调查
* 竞品分析：通过竞争对手身上所知
* 头脑风暴：与团队进行奇思妙想
* 观察法：观察身边的情况
* 实地体验（观察法）：对于实际地点去实地调研
* 数据分析：通过已有或网络的现成数据进行分析

在接下来的小节中，我将重点挑选其中几个最为核心的方法，为大家进行详细的拆解和说明。





---

### 2.2.5 竞品分析

我有一个观点：**我们永远不应该闭门造车**。竞品分析，对我来说，不是为了抄袭，而是为了站在巨人的肩膀上，洞察我们所处的“战场”格局，从他人的成败中学习，最终找到我们自己独特的取胜之道。

#### 1. 竞品的定义与分类

首先，我们要明确谁是我们的“竞品”。我的定义很简单：**任何正在与我们争夺同一批目标用户的时间或金钱的产品，都是我们的竞品**。

在分析时，我不会把所有竞品混为一谈，而是习惯将他们分为三类，采取不同的应对策略。

* **直接竞品**：这是最显而易见的对手。我们的目标用户、产品形态和核心功能都高度重叠。比如，如果我是做“美团外卖”的PM，那“饿了么”就是我的直接竞品。我们是在同一个赛道里进行着刺刀见红的肉搏。
* **间接竞品**：他们的目标用户和我们有重叠，但是满足用户需求的产品形态或解决方案不同。比如，对于外卖平台，“方便蜂”“7-11”等便利店，甚至“叮咚买菜”这样的生鲜电商，都是我的间接竞品。他们都在解决用户“足不出户解决吃饭问题”这个需求。
* **潜在竞品**：这类产品目前和我们没有直接竞争，但未来有可能凭借其资源、技术或用户规模，跨界进入我们的领域。比如，一个拥有海量流量的社交巨头，如果某天宣布要大力发展本地生活服务，那它就会立刻成为我最警惕的潜在竞品。

为了方便我们快速识别，我总结了下面的表格：

| **竞品分类** | **核心特征** | **举例（假设我们是“微信”）** |
| :--- | :--- | :--- |
| **直接竞品** | 目标用户、产品形态、核心功能都高度相似。 | QQ、钉钉（在办公场景下） |
| **间接竞品** | 满足用户的同一类核心需求，但方案不同。 | 抖音（争夺用户时长）、电话/短信（解决通信需求） |
| **潜在竞品** | 目前无竞争，但未来可能进入市场的重量级玩家。 | 一个新兴的、技术驱动的社交创业公司 |

---

#### 2. 竞品分析方法

![image-20250718221149966](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718221149966.png)

明确了要分析谁，下一步就是“如何分析”。仅仅是截几张图、看几个功能是远远不够的。我需要一个框架来保证分析的系统性和深度。我个人最推崇的，是**用户体验五要素模型**。它能帮我像剥洋葱一样，从表到里地把一个产品彻底解构。

1.  **表现层 (Surface)**：这是最表层的，用户能直接感知的视觉设计。包括配色、字体、图标、布局的美感等。
2.  **框架层 (Skeleton)**：这是界面的骨架，决定了信息在页面上的排布。比如按钮放哪里，搜索框放哪里，导航怎么设计。
3.  **结构层 (Structure)**：这是产品的流程和信息架构。用户从一个页面如何跳转到另一个页面？产品的功能模块是如何组织的？
4.  **范围层 (Scope)**：这是产品具体包含了哪些功能和内容。比如，一个电商App，它的范围层就包括了商品展示、购物车、订单、支付等一系列功能。
5.  **战略层 (Strategy)**：这是最核心的，产品的商业目标和用户需求是什么？它为什么要做这个产品？

当我用这五个层次去分析一个竞品时，我看到的就不再是一个个孤立的界面，而是其背后完整的产品思考和商业逻辑。

---

#### 3. 竞品分析的适用场景

![image-20250718221340808](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250718221340808.png)

我做竞品分析，从不是为了分析而分析，一定是带有明确目的的。以下是我认为最有价值的几个应用场景：

* **了解行业**：当我刚进入一个新领域时，我会把市面上Top3的竞品，用五要素模型完整地分析一遍。这是我快速了解行业格局、用户现状和主流玩法的最佳途径。
* **产品设计**：在设计某个具体功能时，比如“购物车”，我一定会去体验至少5个主流App的购物车是怎么设计的。我的目的不是抄，而是去归纳总结，了解业界成熟的设计模式，避免重复造轮子和踩坑。
* **寻找差异化**：通过对主要竞品的优劣势分析（比如使用SWOT模型），我可以清晰地看到市场上的空白地带和未被满足的需求。这对于我们制定差异化竞争策略、找到自己的生态位至关重要。
* **方案验证**：如果我的某个直接竞品上线了一个新功能，并且获得了很好的市场反馈，那它在某种程度上帮我验证了这个功能背后的用户需求是真实存在的。反之，如果竞品的功能失败了，那它也等于免费给我上了一课。




---

### 2.2.6 用户访谈

在我看来，数据能告诉我用户“做了什么”，但只有用户访谈能告诉我，他们“为什么这么做”。它是产品经理建立用户同理心、挖掘深层次需求的终极武器，没有任何工具可以替代。

#### 1. 用户访谈的定义与流程

![image-20250719083410461](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719083410461.png)

**用户访谈的定义**：对我而言，它是一场有目的、有结构的、一对一的深度对话。我通过这场对话，来探寻用户在特定场景下的行为、动机、态度和痛点。它是一种定性研究方法，我追求的是洞察的深度，而非样本的数量。

一场专业的访谈绝不是一次随意的聊天，它需要我进行精心的策划和准备。我通常会遵循一个六步走的流程：

1.  **确定访谈形式**：首先，我要决定访谈的方式。是成本较高但信息丰富的**线下面对面**？还是高效便捷的**电话/线上视频**？
2.  **明确访谈目的**：在开始前，我必须能用一句话说清楚“我这次访谈想解决的核心问题是什么？”例如：“探究用户在深夜场景下点外卖的核心决策因素。”
3.  **用户筛选**：我需要找到“对”的人。根据我的访谈目的，我会设定清晰的用户标准（比如年龄、使用频率、所在城市等），然后通过问卷或后台数据进行筛选。
4.  **设计访谈问题**：这是访谈的灵魂。我会提前准备一份访谈提纲，里面包含了一系列精心设计的开放式问题。
5.  **邀请用户访谈**：我会正式地联系并邀请筛选出来的用户，说明我们的目的、时长，并通常会提供一些小礼品（如礼品卡、代金券）作为答谢。
6.  **结果汇总与分析**：访谈结束后，我会立刻整理访谈记录，然后将多次访谈的结果放在一起，寻找其中反复出现的模式、观点和痛点，最终提炼出有价值的洞察。

---

#### 2. 访谈问题设计要点

![image-20250719083541359](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719083541359.png)

访谈的成败，很大程度上取决于我问题的质量。我设计问题时，会重点把握以下几点：

**问题设置的方向：**
我的访谈问题通常会遵循“现状 → 痛点 → 方案”的逻辑顺序展开，层层递进。

* **现状类问题**：用于“破冰”，了解用户当下的行为和场景。如：“能带我回忆一下，您上一次点外卖的全过程吗？”
* **痛点类问题**：用于挖掘用户的挫折和不满。如：“在刚才您描述的整个过程中，有没有哪个环节让您觉得特别麻烦或者不爽？”
* **方案/期望类问题**：用于探寻用户的期望和潜在需求。如：“如果抛开所有限制，您心目中最理想的外卖App应该是什么样的？”

**问题设计的方式：**
* **多问开放式问题**：我从不问“你喜欢我们的App吗？”这类可以用“是/否”回答的问题。我会问：“关于我们的App，你有什么样的使用体验和感受？”
* **不断追问**：当用户提到一个关键点时，我最常使用的工具就是追问“为什么？”“可以再多讲讲吗？”“后来呢？”。这能帮我挖得更深。
* **避免引导性提问**：我绝不会问“你是不是觉得我们的红包功能很难找？”。这会把我的观点强加给用户。我会问：“您平时会使用我们的红包功能吗？可以聊聊您使用它的过程吗？”

---

#### 3. 用户访谈示例

![image-20250719084031980](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084031980.png)

我们还用外卖平台的案例。假设我的访谈目的是“了解白领用户在办公室选择午餐外卖的决策过程”。我的问题可能会这样设计：

* **（现状）** “您可以回忆一下昨天中午点外卖的经历吗？从你想到要点外卖，到最后拿到外卖，都发生了什么？”
* **（痛点）** “在挑选餐厅和菜品的过程中，有没有哪个环节让您觉得很纠结或者浪费时间？”
* **（追问）** “您刚才提到‘选来选去最后还是点了常吃的那家’，为什么会这样呢？”
* **（期望）** “如果我们可以帮您解决‘选择困难’这个问题，您希望我们怎么做？”

---

#### 4. 注意事项及适用场景

![image-20250719083846814](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719083846814.png)

* **我的注意事项**：
    1.  **多听少说**：访谈的主角是用户，我的任务是引导和倾听。
    2.  **保持中立**：无论用户怎么夸奖或吐槽我的产品，我都要保持客观，不争辩，不解释。
    3.  **事实与观点分离**：记录时，要严格区分哪些是用户说的“事实”，哪些是我自己的“分析和判断”。

* **适用场景**：
    我通常会在项目的**早期探索阶段**大量使用用户访谈，因为这时我对用户和问题还很模糊，需要建立认知。此外，在**新功能的构思和验证阶段**，我也会通过访谈，向用户展示原型或概念，来快速获取反馈。

---

为了方便我们回顾，我将用户访谈的核心要点总结在下面的表格里：

| **核心环节** | **我的关键动作** |
| :--- | :--- |
| **访谈前** | **明确目的**、**筛选用户**、**设计开放式问题提纲** |
| **访谈中** | **多听少说**，像海绵一样吸收信息；通过**不断追问**来深挖；**保持中立**，不评判。 |
| **访谈后** | **及时整理**笔记，**寻找共性**，将零散的观点**提炼为洞察**。 |



---

### 2.2.7 实地调研

如果说用户访谈是“听其言”，那么实地调研就是“观其行”。在我看来，这是两种方法最大的区别。很多时候，用户说的和他实际做的并不完全一致，而实地调研，就是让我有机会亲眼去见证这种差异，发现那些连用户自己都未曾察觉的隐性需求。

#### 1. 实地调研的定义

对我而言，**实地调研就是产品经理亲自进入用户的真实物理场景中，通过近距离观察和亲身体验，来理解用户行为和背后动机的一种研究方法。**

它包含两种核心形式：
* **观察法**：我像一个“隐形人”，在不打扰用户的前提下，静静地观察他在特定场景下是如何与环境、工具（包括我们的产品）进行互动的。
* **实地体验**：我亲自扮演用户的角色，走一遍他完整的任务流程，切身感受他在每个环节的顺畅与阻碍。

---

#### 2. 如何进行实地调研

![image-20250719084246476](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084246476.png)

一次有效的实地调研，需要我像导演一样，精心设计整个过程。我通常会遵循以下四个步骤：

1.  **进入场景**：这是第一步，也是最关键的一步。比如，我要研究餐厅后厨的采购流程，那我就必须真的穿上工作服，走进那个潮湿、繁忙的后厨，而不是坐在办公室里想象。
2.  **用户角色**：进入场景后，我要明确我的角色。我是作为一名旁观者去“观察”？还是亲自上手，作为一名“学徒”去“体验”整个下单、验货、入库的流程？
3.  **观察体会**：在场景中，我的所有感官都要打开。我会重点观察：用户在做什么？他使用了什么工具？他与其他人是如何协作的？他在哪个环节面露难色？哪个环节的效率特别低？如果是我自己体验，我会记录下每一步的感受。
4.  **持续进行**：一次调研是远远不够的。我会选择不同时间、不同类型的场景（比如高峰期与平峰期的餐厅后厨）进行多次调研，以确保我看到的不是偶然现象，而是普遍存在的问题。

---

#### 3. 实地调研适用场景

![image-20250719084347265](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084347265.png)

实地调研虽然强大，但成本也很高，所以我必须在最需要它的地方“对症下药”。以下是我最常使用它的几个场景：

* **挖掘需求**：当我要为一个全新的领域（比如智慧农业）设计产品时，我对用户的真实作业环境一无所知，这时实地调研是建立基础认知的唯一途径。
* **理解需求**：当用户向我提了一个我无法理解的需求时，比如“你们的扫码枪不好用”，我会直接去他的仓库，看他到底是怎么用的，问题出在哪里。
* **效果验证**：我的新功能上线后，我会去现场观察用户是如何使用它的，是否符合我的设计预期，有没有出现我没想到的问题。
* **寻找问题**：当我的产品数据出现异常，比如某个环节转化率突然下降，我会去实地观察，看看是不是用户的线下操作流程发生了变化，从而导致了线上的问题。

实地调研是我们产品经理走出办公室，拥抱真实世界的最佳方式。我把它总结为以下要点：

| **核心问题** | **我的关键动作** |
| :--- | :--- |
| **1. 什么是实地调研？** | 亲自**进入用户的真实场景**，通过**观察**和**体验**来理解用户的真实行为。 |
| **2. 如何进行实地调研？** | **进入场景 → 代入角色 → 细心体会 → 持续进行**，这是一个完整的闭环。 |




-----

## 2.3 需求管理

对我来说，如果说需求收集是“狩猎”，那么需求管理就是“庖丁解牛”和“精细烹饪”。我需要一个系统化的流程和工具，来处理我收集到的所有“食材”（需求），确保最有价值的部分能被优先端上“餐桌”（进入开发）。这个系统的核心，我称之为\*\*“需求池”\*\*。

### 2.3.1 学习目标

![image-20250719084741571](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084741571.png)

在本节中，我的目标是带大家学会如何搭建和维护一个健康、高效的需求池。我将分享需求池应该包含哪些关键信息，需求在池子里会经历怎样的生命周期，以及我始终坚持的管理原则。学完本节，我希望我们都能成为一名思路清晰的“需求管家”。

### 2.3.2 需求池定义

![image-20250719084852379](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084852379.png)



![image-20250719084802024](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719084802024.png)

**需求池**，顾名思义，就是一个用来汇集和管理所有需求的“池子”。我把它定义为：**一个用于统一记录、跟踪和评估产品所有相关需求的中央数据库**。它是我管理产品的“唯一事实来源”，我通常会用Jira、Trello或一个功能强大的Excel表格来搭建它。

为了让这个池子有效运转，我记录的每一条需求，都必须包含一些标准化的信息字段，其中最重要的包括：

  * **产品模块**：这个需求属于哪个功能板块？（如：登录注册、订单流程）
  * **需求描述**：用清晰的语言描述用户场景、痛点和期望。（What & Why）
  * **优先级**：这个需求有多重要？（我常用P0/P1/P2/P3来划分）
  * **需求提出人**：这条需求来自谁？（如：用户A、销售部、老板）
  * **需求类型**：这是一个新功能、体验优化、Bug还是技术需求？

### 2.3.3 需求状态

![image-20250719085530024](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719085530024.png)

进入我需求池的每一条需求，都不会石沉大海，它会拥有一个清晰的生命周期。我会通过“状态”这个字段来追踪它的进展。一个标准的需求生命周期流程如下：

1.  **待确认**：这是需求的入口。所有新收集到的、未经我详细分析的需求，都先放在这里。
2.  **已确认**：经过我的分析，确认这是一个真实、有价值的需求，但还没想好什么时候做。
3.  **规划中**：需求已通过评审，并被正式排入某个版本迭代的开发计划中。
4.  **已完成**：需求已开发、测试、上线。这是它旅程的终点。
5.  **已拒绝**：经过分析，我认为这个需求价值不大、或与产品方向不符，决定不做。给需求一个明确的“死亡”结果，同样非常重要。

### 2.3.4 需求池的作用

![image-20250719085617473](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719085617473.png)

我之所以如此看重需求池，是因为它为我、为整个团队都带来了巨大的价值。

  * **管理需求**：它是所有需求的统一入口和视图，避免了需求散落在邮件、微信、会议纪要里，造成遗忘和混乱。
  * **维护需求**：我可以随时查看任何一个需求的状态、优先级和负责人，对整个产品的迭代节奏了如指掌。
  * **回溯需求**：当未来有人问“我们当初为什么要做这个功能？”时，我可以立刻从需求池里调出当时的背景、分析和决策过程。它是我们产品决策的“历史档案”。

### 2.3.5 需求池管理原则

![image-20250719085705428](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719085705428.png)

一个只进不出的需求池，很快就会变成一个令人绝望的“需求坟场”。为了保持它的活力和价值，我始终坚守两条管理原则：

1.  **有进有出**：需求池必须是流动的。我需要定期（比如每周）审视池中的需求，推动它们的状态向前流转。要么进入规划，要么明确拒绝，绝不能让大量需求长期停滞在“待确认”状态。
2.  **宽进严出**：对于需求的“进入”，我持开放态度，鼓励各方提出想法，所以入口要“宽”。但对于需求的“输出”（即进入开发），我必须严格把关，基于用户价值、商业目标、投入产出比等因素进行严苛的筛选和排序。

我将需求管理的核心要点，总结在下面的表格中：

| **核心概念** | **我的实践要点** |
| :--- | :--- |
| **需求池** | 建立一个包含“优先级、状态”等关键字段的**中央数据库**，作为唯一事实来源。 |
| **需求状态** | 用“待确认 → 已完成/已拒绝”的**清晰流程**，追踪每条需求的生命周期。 |
| **管理原则** | **宽进严出**：鼓励收集，严格筛选。<br>**有进有出**：保持流动，拒绝僵化。 |







-----

## 2.4 本章总结

在这里我附上需求池模板供读者使用


{% link 需求池模板.xlsx,Prorise,https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/AI%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/01%E9%9C%80%E6%B1%82%E6%B1%A0.xlsx,https://bu.dusays.com/2025/07/19/687b0b6b83e43.png %}




![image-20250719105224592](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719105224592.png)

最后，我们来回顾一下整个第二章的核心脉络。我认为，一名合格的产品经理在处理需求时，必须走完这三个密不可分的步骤：

1.  **认知需求**：首先，我们要能透过现象看本质，深刻理解`“什么是需求”`——它不是用户说的原话，而是能解决用户在特定场景下本质痛点的方案。
2.  **收集需求**：其次，我们要主动出击，运用竞品分析、用户访谈、实地调研等多种手段，从内外部多个渠道，系统地`“如何收集需求”`。
3.  **管理需求**：最后，我们要建立并维护一个动态、健康的需求池，对所有需求进行科学的`“需求管理”`，确保我们永远在做最有价值的事。

掌握从认知、收集到管理的完整闭环，是我们做出成功产品的基石。




---



# 第三章：需求分析

在上一章，我们学会了如何像一名侦探一样，通过各种手段去“收集”需求的线索。但这些线索往往是零散的、模糊的，甚至带有误导性。如果我们不加处理就直接采纳，很可能会做出南辕北辙的产品。

因此，**需求分析**就是我们作为产品经理，对这些原始线索进行“勘察、推理、定案”的关键过程。这是整个产品工作中，最能体现我们逻辑思辨和深度思考能力的核心环节。

## 3.1 需求分析的定义

我们先从最根本的问题开始：到底什么才叫“需求分析”,我们可以来看一个案例：

![image-20250719112130263](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719112130263.png)

上面这个案例，我们不难看出，当我们接到需求后，没有去了解需求的背景，深挖需求。很容易导致我们做出来的方案是不符合要求，导致大量的人力、时间、资源的浪费，我们应当去拆解用户的需求，如下：

![image-20250719112244196](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719112244196.png)

### 3.1.1 什么是需求分析

在我看来，要理解需求分析，我们需要抓住它的“本质”和“过程”。

#### 1. 需求分析的本质

我理解的需求分析，其本质是一个**“解构”与“重构”**的过程。
* **解构**：是把用户提出的原始需求（无论是问题、目的还是方案）打碎、拆解，深入挖掘其背后真正的动机和未被满足的痛点。
* **重构**：是在我们完全理解了本质痛点之后，重新组合信息，设计出一个真正能有效解决该问题的、合理的、可落地的产品解决方案。

简单来说，就是**先想“为什么”，再想“怎么办”**。

#### 2. 原始需求与产品需求的转换

基于这个本质，我给需求分析一个最直接的定义：**所谓需求分析，就是将“用户的原始需求”，转化为“可执行的产品需求”的全过程。**

![image-20250719112050347](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719112050347.png)

我们再来回顾一下这两个概念：
* **原始需求**：是用户直接给我们的东西，是“用户想要什么”。它可能是“我想要个一键下单按钮”，也可能是“我希望能快速找到便宜的外卖”。它是我们工作的**输入**。
* **产品需求**：是我们经过分析、甄别、权衡之后，最终决定要做的东西，是“我们应该为用户做什么”。它是一个包含了用户场景、核心问题、解决方案和验收标准的完整方案。它是我们工作的**输出**。

所以，需求分析就是连接这两者的桥梁，是那个至关重要的“转化”步骤。没有这个转化过程，我们就只是一个需求的“传声筒”，而不是一个创造价值的“产品经理”。

为了让这个区别更清晰，我总结了下面的对比表：

| **对比维度** | **原始需求** | **产品需求** |
| :--- | :--- | :--- |
| **来源** | 用户直接表达 | 产品经理分析转化 |
| **形式** | 通常是模糊、零散、未经验证的 | 是清晰、结构化、经过验证的 |
| **关注点** | “我想要一个XX功能”（what） | “为了解决用户XX问题，我们需要XX方案”（why & how）|
| **我的角色** | 聆听者、记录员 | 分析师、决策者、方案设计师 |




---

## 3.2 需求分析的时机

在我看来，需求分析并不是一个孤立的、只在特定阶段才进行的“仪式”。它应该像呼吸一样，贯穿我们产品工作的始终。不过，从实践上，我主要会在两个关键的时间点，以不同的方式来开展这项工作：**1️⃣收集需求时** 和 **2️⃣收集需求后**。

![image-20250719113814345](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113814345.png)

### 3.2.1 收集需求时的需求分析

#### 1. 直接沟通进行需求分析

我把这个过程称为“实时分析”。当我在进行用户访谈、或是与业务方开会时，我绝不只做一个被动的记录员。我的大脑会高速运转，对接收到的每一个信息点，当场进行第一轮的分析、澄清和追问。

这种方式的好处是，我可以在信息最新鲜、上下文最完整的时刻，抓住机会深挖下去，及时地探究用户“为什么”这么想，而不是等会议结束、记忆模糊后，再自己去猜测。

#### 2. 案例：物流公司时效需求分析

![image-20250719113104681](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113104681.png)

这个案例能很好地说明实时分析的价值。

* **场景**：我作为一家物流平台的PM，正在访谈一位重要的企业客户。
* **原始需求**：客户告诉我：“我希望你们的平台能提供一个功能，让我能在地图上看到我们货物的运输车辆的实时GPS位置。”
* **我的实时分析与追问**：听到这个“方案”后，我没有立刻记下来就完事，而是当场追问：“这个想法很有意思。可以和我聊聊吗，您为什么需要看到车辆的实时位置？这个功能能帮您解决什么具体问题呢？”
* **挖掘出的本质目的**：通过追问，我发现，客户并不真的关心车辆在哪条路上，他关心的是“货物到底什么时候能到”。因为他的下游客户总是在催问他送达时间，他需要一个准确的预期，来安抚客户、安排接货。
* **转化后的产品需求**：因此，我将这个需求从“提供车辆实时定位”，转化为了“在订单详情页提供一个精准、动态的**预计送达时间（ETA）**，并支持在临近送达时，向收货人发送提醒”。后者显然是价值高得多、也更贴近问题本质的解决方案。

### 3.2.2 收集需求后的需求分析

![image-20250719113442021](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113442021.png)

#### 1. 集中分析需求

当然，光有实时分析是远远不够的。在完成了当周的用户访谈、回收了所有的调查问卷、或者整理完用户反馈后，我会专门安排时间，进行一次“集中分析”。

在这个阶段，我会把所有零散的、原始的需求信息汇总到一起，像一个侦探把所有线索都钉在白板上一样。我会开始寻找它们之间的关联、共性，试图发现那个隐藏在多个表象之下的、更宏观的、系统性的问题或机会。

#### 2. 案例：用户反馈需求分析

![image-20250719113602157](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113602157.png)

* **场景**：我作为一款社交App的PM，每周都会定期整理来自应用商店、社区的用户反馈。
* **原始需求（集合）**：我看到了大量看似独立的用户抱怨。
	* 用户A说：“你们的视频加载太慢了！”；
	* 用户B说：“视频看着看着就卡住了”；
	* 用户C说：“上传一个视频要等半天”。
* **我的集中分析**：如果我只看单条反馈，可能会分别给技术团队提“加载慢”、“播放卡”、“上传慢”这三个独立的、零散的问题。但当我把它们放在一起集中分析时，我发现了一个共性——**我们产品的整体视频处理和分发链路，可能存在系统性的性能瓶颈**。
* **转化后的产品需求**：基于这个判断，我最终定义的产品需求，就不是一个个小修小补，而是一个系统性的优化项目：“**优化视频处理架构，提升视频在不同网络环境下的加载和播放流畅度，将平均起播时间缩短30%**”。这个需求，显然比解决单个用户的抱怨要有价值得多。

---
总而言之，这两种时机的分析，各有侧重，缺一不可。我将它们的区别总结如下：

| **分析时机** | **核心特点** | **我的目标** |
| :--- | :--- | :--- |
| **收集时（实时分析）** | 互动性强、有上下文、聚焦于个体 | 快速探究单个原始需求背后的“**为什么**”。 |
| **收集后（集中分析）** | 宏观、全面、寻找关联 | 发现多个原始需求背后共同指向的“**系统性问题或机会**”。 |




---

## 3.3 需求分析的步骤

到目前为止，我们已经定义了需求分析，也明确了进行分析的时机。那具体到一项原始需求，我究竟是如何一步步把它“解剖”清楚的呢？

在我的工作流中，这个过程被严格地划分为三个步骤：

- **第一步：需求澄清；**
- **第二步 ：需求甄别；**
- **第三步：需求的优先级排序**。这三个步骤，层层递进，缺一不可。

我们先来看第一步，也是所有分析的基础——需求澄清。

### 3.3.1 需求澄清

![image-20250719130637070](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719130637070.png)

每当一个原始需求摆在我面前时，我从不急于判断它的好坏、真伪，我做的第一件事，永远是：**把它弄清楚**。

需求澄清，对就我像一名侦探在审视案发现场，我需要通过反复地询问和探究，把这个需求的所有模糊、不确定的信息，都变得清晰、明确。为了系统化地做到这一点，我随身携带着一个强大的思维工具——**“WHY-WHO-WHAT-HOW”框架**。

我们以外卖平台的案例来逐一拆解这四个关键问题：假设我收到的一个原始需求是“我想要一个‘智能营养餐’功能”。

#### 1. WHY（为什么）

这是我首先要问的问题：**我们为什么要做这个需求？** 它能为用户带来什么核心价值？它又能为我们的公司带来什么商业价值？

* 对于“智能营养餐”这个需求，WHY可能是：
    * **用户价值**：帮助对健康有追求的用户，解决“不知道怎么吃才健康”以及“计算热量和营养成分很麻烦”的痛点。
    * **商业价值**：通过差异化的健康服务，吸引高价值用户，提升平台的品牌形象和用户粘性。

如果一个需求的“WHY”我都答不出来，那它基本上就可以被直接否决了。

#### 2. WHO（用户是谁）

第二个问题：**这个需求我们是为谁而做的？** 我需要清晰地描绘出目标用户的画像。

* “智能营养餐”的WHO，绝不是“所有用户”。它的核心用户画像可能是：
    * 一线城市的年轻白领；
    * 有健身习惯或正在减脂的人群；
    * 关注生活品质、愿意为健康付出一定溢价的用户。
* 明确WHO，能帮助我在后续的设计中，始终围绕着这群核心用户的审美和习惯来进行。

#### 3. WHAT（什么问题）

第三个问题：**我们具体要解决一个什么问题？** 我需要把用户的痛点用清晰、无歧义的语言描述出来。

* 对于“智能营养餐”，WHAT不是“用户想要一个功能”，而是要解决用户的本质问题：
    * “用户因缺乏专业知识，**难以判断**不同外卖的营养成分和热量是否满足自己的健康需求。”
    * “用户因工作繁忙，**没有时间**去自己计算和搭配每日的营养摄入。”

#### 4. HOW（现状如何）

最后一个问题：**用户现在是如何解决这个问题的？** 了解用户当前的“野生”解决方案，能帮我判断痛点的强度，并为我的设计提供灵感。

* 对于“智能营养餐”这个需求，用户当下的HOW可能是：
    * 自己去网上搜索食物热量表，估算着点餐。
    * 下载专门的健康App，手动记录自己点的外卖，再查看营养分析。
    * 干脆放弃点外卖，选择自己做饭或吃价格昂贵的成品健康餐。
* 这些笨拙、耗时、昂贵的现状，恰恰反证了我们这个新功能潜在的巨大价值。

---
我将这个澄清框架总结为一张表，它是我分析任何需求前的“必填清单”：

| **澄清问题** | **我的核心关注点** | **案例应用（智能营养餐）** |
| :--- | :--- | :--- |
| **WHY (为什么做)** | 探究需求的**商业与用户价值** | 满足健康需求，提升用户粘性与品牌价值。 |
| **WHO (为谁而做)** | 定义精准的**目标用户画像** | 追求健康的都市白领、健身人群。 |
| **WHAT (解决什么问题)** | 识别并定义用户的**本质痛点** | 解决用户“不懂如何健康搭配”和“没时间计算营养”的问题。 |
| **HOW (现状如何)** | 了解用户当前的**解决方案或替代方案** | 用户目前通过手动查询、使用其他App等方式，过程繁琐且不准确。 |

只有把这四个问题都回答清楚了，我才会认为，这个需求已经被我“澄清”了。接下来，我才会进入分析的第二步：需求甄别。



### 3.3.2 需求甄别

我把需求分析的第二步称为**“需求甄别”**，或者叫“真伪需求判定”。我的核心任务，是判断这个被澄清后的需求，到底是一个能为多数用户创造巨大价值的“真需求”，还是一个看似有理、实则虚幻的“伪需求”。

投入资源去做一个伪需求，是我认为对团队最大的浪费。

#### 1. 需求真伪判定的三个标准



![image-20250719131258692](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719131258692.png)

为了避免凭感觉做判断，我建立了一套自己的“甄别滤网”，它由三个核心标准构成。一个有价值的真需求，通常需要至少满足其中两个，甚至是全部三个标准。

* **普遍性 (Universality)**
    这个问题是否具有广泛的代表性？在我的目标用户群体中，遇到这个问题的用户规模有多大？是只有一小撮人有这个特殊的毛病，还是绝大多数用户共同的困扰？我追求的，是能让尽可能多的目标用户受益的需求。

* **痛点性 (Painfulness)**
    这个问题给用户带来的“痛感”有多强？如果这个问题不被解决，用户是否会感到非常沮丧、烦躁，甚至愿意付费来解决它？我常用“是‘痒点’还是‘痛点’”来区分。挠痒痒的需求可做可不做，但治病止痛的需求，用户才会真正买单。

* **高频性 (High Frequency)**
    用户遇到这个问题的频率有多高？是每天、每周都会遇到，还是每年甚至几年才会遇到一次？高频的需求，意味着我们的解决方案能被用户频繁使用，这对于培养用户习惯、提升产品粘性至关重要。

---

#### 2. 伪需求判断案例分析

我们来看一个经典的案例，学习如何运用这三个标准来甄别伪需求。

* **场景**：我是一家“母婴社区”App的产品经理，我们的核心业务是为新手爸妈提供育儿知识交流和社交的平台。
* **原始需求**：社区里有一位非常活跃且有影响力的用户，她强烈建议我们增加一个“儿童防走丢手表”的功能。她的设想是，用户可以在我们的App里购买一款儿童手表，并随时查看孩子的位置。
* **我的甄别过程**：这个需求听起来非常“刚需”，因为儿童安全是天大的事。但我们必须冷静地用三个标准来审视它。
    1.  **普遍性分析**：所有家长都关心孩子安全，这是一个普遍的情感。但是，“需要通过一个App内嵌的硬件功能来随时追踪孩子位置”，这还是一个普遍的需求吗？我的判断是，只有其中一部分极度焦虑的家长才会有此强需求。因此，需求的**普遍性较低**。
    2.  **痛点性分析**：“孩子走丢”这个场景，痛不痛？当然痛，这是天塌下来的痛。所以，**痛点性极高**。
    3.  **高频性分析**：一个正常的孩子，在父母的看护下，“走丢”这件事发生的频率有多高？谢天谢地，这是一个极低极低的概率。所以，需求的**高频性极低**。

* **我的最终结论**：这是一个“**低频、非普适的超强痛点**”需求。更重要的是，它涉及到硬件、供应链、地图服务等，这与我们“社区内容”的核心能力相去甚远。因此，尽管它听起来很有吸引力，但我会判定，对于我们这个母婴社区App而言，这是一个“**伪需求**”。它是一个真实存在的问题，但它不应该由我们这个产品来解决。

---
我将这三个标准总结为一张自检表，每当我分析需求时，都会在心里为它打分：

| **甄别标准** | **我问自己的问题** | **强需求特征** |
| :--- | :--- | :--- |
| **普遍性** | 我的目标用户中，有多大比例的人会遇到这个问题？ | 广大目标用户都存在 |
| **痛点性** | 如果不解决，用户会有多“痛”？他们愿意为此做什么？ | 痛感强烈，用户愿意付费或付出代价解决 |
| **高频性** | 用户多久会遇到一次这个问题？每天？每周？还是几乎遇不到？ | 每日或每周多次遇到 |

只有通过了这道严格的“安检门”，一个需求才有资格进入我分析流程的最后一步：优先级排序。




---

### 3.3.3 需求的优先级

经过了“澄清”和“甄别”，我们现在手上拿到了一份“真需求”清单。但现实是，我们的研发资源（人力、时间、金钱）永远是有限的。我们不可能同时满足所有人的所有需求。

因此，**优先级排序**，就是决定“**我们下一步应该先做什么，再做什么**”的艺术和科学。在我看来，这是产品经理最重要的决策，没有之一。一个正确的优先级决策，能让我们的产品价值最大化。

#### 1. 四象限法则

![image-20250719132049714](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132049714.png)

当我面对一大堆需求，感到千头万绪时，我用来理清思路的第一个工具，就是经典的**“四象限法则”**（也叫艾森豪威尔矩阵）。我通过“重要性”和“紧急性”这两个维度，快速地对需求进行一次粗分类。

* **重要且紧急**：这是最高优先级，是那些“着火了”的需求。比如：线上支付功能出现重大Bug、服务器宕机。我的原则是：**马上做**，调动一切资源，立刻解决。
* **重要不紧急**：这是最能体现我们产品经理价值的区域。这些需求关系到产品的长期发展和核心战略，比如：架构优化、新功能探索、用户体验的系统性提升。它们没有明确的deadline，最容易被我们拖延。我的原则是：**计划做**，必须主动地、有计划地把它们排入我们的产品路线图。
* **紧急不重要**：这些是日常工作中最大的干扰。比如：某个领导临时想要一个不重要的数据、某个非核心客户的一些小报怨。它们看起来很急，但对我们的核心目标贡献不大。我的原则是：**授权做或快速应付**，看能否让团队其他人帮忙，或者用最小的代价快速解决。
* **不重要不紧急**：这些是价值最低的需求。我的原则是：**尽量不做或直接拒绝**。我们必须学会对这类需求说“不”。

![image-20250719132245046](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132245046.png)

#### 2. 优先级确定因素

![image-20250719132330097](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132330097.png)

四象限法则帮我做了初步的划分，尤其区分出了“重要不紧急”这个价值区域。但当我有多个“重要不紧急”的需求时，应该先做哪一个呢？这时，我需要引入更多的因素来综合判断。

* **性价比（成本与价值）**：这是我最看重的因素。我会粗略地估算每个需求的“投入产出比（ROI）”。即，这个需求需要耗费多少开发资源（成本）？它又能带来多大的用户价值和商业价值？我总是在寻找那些“四两拨千斤”的、低成本高价值的需求。
* **符合战略规划**：这个需求是否与我们公司本季度或本年度的战略目标相符？一个再酷的功能，如果脱离了公司的战略主航道，那它就是一个“漂亮的干扰项”。我必须确保我们的开发资源，始终服务于公司的战略大方向。
* **长短期价值**：我需要在“短期见效”和“长期投资”之间做出平衡。有时为了提振士气或达成某个KPI，我会选择一个能快速上线、马上看到效果的需求（短期价值）。有时我也会选择一个用户完全感知不到的“后台架构重构”项目，因为它能为我们未来几年的开发效率打下坚实的基础（长期价值）。

#### 3. 确定需求优先级的注意事项

![image-20250719132422433](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132422433.png)

最后，我想分享几点我在无数次优先级PK中，总结出的经验和教训。

* **需求变动性**：我必须承认，优先级不是一成不变的。市场环境在变，用户需求在变，我们必须保持敏锐和灵活，定期（比如每两周或每月）回顾和调整我们的产品路线图。
* **全局主动性**：我不能只做一个被动接收和排序需求的人。我需要站在整个产品的视角，主动地去规划那些能建立长期壁垒的、系统性的项目，而不是被各个业务方的“紧急”需求牵着鼻子走。
* **真实需求基础**：我的所有优先级判断，都必须建立在我们在3.3.1和3.3.2节所做的“澄清”和“甄别”工作之上，即必须是**真实的、有价值的需求**。绝不能因为某个领导声音大、或者某个客户会吵，就轻易提高他的需求的优先级。
* **需求选择搭配原则**：一个健康的版本迭代，就像一顿营养均衡的饭。我通常会搭配着来，比如“**一个大的新功能 + 几个重要的体验优化 + 一些历史Bug修复**”。这样的版本，既能给用户带来惊喜，又能提升产品的稳定性，还能让团队有成就感。

---
我将优先级排序的要点，总结为下面这张表：

| **核心方法** | **我的实践要点** |
| :--- | :--- |
| **四象限法则** | 快速分类，重点投入**重要不紧急**的价值型需求。 |
| **综合因素判断** | 在价值型需求中，进一步权衡**性价比**、**战略价值**和**长短期收益**。 |
| **注意事项** | 保持**灵活性**，立足**真实需求**，**主动规划**，并**合理搭配**版本内容。 |

到这里，我们需求分析的三个步骤就全部完成了。一个需求，只有经过了澄清、甄别和优先级排序这“三堂会审”，才有资格最终出现在我们的产品路线图上。

最后，我们附上产品需求文件模板供产品设计师快速完成需求分析的任务

{% link 产品需求文档模板.docx,Prorise,https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/AI%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/03%E4%BA%A7%E5%93%81%E9%9C%80%E6%B1%82%E6%96%87%E6%A1%A3%E6%A8%A1%E6%9D%BF.docx,https://bu.dusays.com/2025/07/19/687b2cf24c5db.png %}




---



# 第四章：流程图与结构图

在我看来，如果说需求文档是用文字来描述“做什么”和“为什么做”，那么流程图和结构图就是我用来清晰、无歧义地表达“怎么做”的**视觉语言**。

它们是我与设计师、工程师、测试，甚至是老板和业务方进行高效沟通，确保大家对产品理解一致的最重要的工具。掌握这两种图的绘制，是我们产品经理的基本功。

## 4.1 认识流程图

我们先从流程图开始。我用它来描述一个**动态的过程**，即一系列随时间先后发生的动作和决策。它回答的核心问题是：“接下来会发生什么？”。

### 4.1.1 流程图的定义与分类

![image-20250719215002454](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215002454.png)

#### 1. 流程图的定义

**流程图**，对我而言，就是一种**将一个复杂的做事过程，通过标准化的图形和箭头，进行可视化表达的图示**。它的最大价值，就是能把抽象的逻辑、繁琐的步骤，变得直观、清晰，让团队里的每一个人都能快速理解。

#### 2. 常见流程图类型

在我的日常工作中，根据我要沟通的对象和目的不同，我会绘制三种不同类型的流程图。混淆它们，常常是新手产品经理犯的错误。

* **业务流程图 (Business Flowchart)**
    ![image-20250719215053441](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215053441.png)

    我用它来描述一个**完整的、端到端的业务场景**，特别是当这个场景涉及到多个角色或系统交互时。它聚焦的是业务活动本身，而不是产品内部的具体功能。

    图中的“医院挂号”案例就是绝佳的示范。它清晰地展示了“病人”、“医院服务”、的流程，在项目初期，我会用这种图来和老板、业务方统一对整个商业模式的认知。

* **功能流程图 (Functional Flowchart)**
    ![image-20250719215234409](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215234409.png)

    我用它来详细说明**某一个具体功能内部的、严谨的逻辑**。它的粒度比业务流程图要细得多。

    我们来看这张“在线挂号”的流程图，它就是一个完美的例子。它描述的是“挂号”这**单个功能**内部的完整逻辑。从“选择科室”开始，到系统进行判断“当天是否已约满”，再到用户选择具体时间、确认就诊人，最后系统再次判断“是否符合科室要求”，直到最终“预约成功”或“提示约满”。

    它把所有可能的情况和分支都严谨地表达了出来。我就是用这种图，来和开发、测试工程师沟通一个功能的具体实现规则，确保没有遗漏任何用户场景和异常情况。
    
    
    
* **页面流程图 (Page Flowchart)**
    ![image-20250719215727998](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215727998.png)

    我用它来表达**用户在产品不同界面之间的跳转路径**。它关注的是用户为了完成一个任务，需要“从哪个页面”流转到“哪个页面”。

    图中的从“App首页”到“搜索结果页”再到“商品详情页”的流程，就是一个典型的页面流程图。我用它和UI/UX设计师合作，来保证整个产品的导航体验是顺畅、无断点的，确保用户不会在我们的产品里“迷路”。

---
为了方便我们记忆和区分，我将这三种流程图的核心特点总结在了一张表格里：

| **流程图类型** | **核心描述** | **我用它来回答什么问题？** | **主要沟通对象** |
| :--- | :--- | :--- | :--- |
| **业务流程图** | 描述完整的商业活动，涉及**多角色/系统**。 | “我们的整体业务是如何运转的？” | 老板、业务方、运营 |
| **功能流程图** | 描述**单个功能**的内部逻辑和异常处理。 | “这个功能内部是如何工作的？” | 开发、测试工程师 |
| **页面流程图** | 描述用户在**不同界面**间的跳转路径。 | “用户为了完成任务，需要经过哪些页面？” | UI/UX设计师、开发工程师 |




-----

## 4.2 流程图的绘制

对我来说，画流程图就像在用一种通用的视觉语言写作。要写好，我们得先掌握它的“基本词汇”（元素）和“核心句型”（结构）。

### 4.2.1 流程图常见元素

![image-20250719220137457](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220137457.png)

为了让流程图具有通用性，我始终坚持使用一套标准化的符号。这些符号就是构成流程图的“词汇”。

| **元素样式** | **元素名称** | **我的使用说明** |
| :--- | :--- | :--- |
|  | **开始/结束** | 我用它来明确标识一个流程的**起点**和所有可能的**终点**。一个流程只有一个“开始”，但可以有多个“结束”。 |
|  | **节点/处理** | 这是最常用的符号，代表一个具体的操作、动作或状态。比如“用户输入密码”、“系统保存数据”。 |
|  | **判定** | 代表一个需要做“是/否”或多分支**判断**的地方。菱形必须有至少两个出口，对应不同的判断结果。 |
|  | **子流程** | 当一个流程中的某个步骤本身又是一个复杂的流程时（比如“支付流程”），我用这个符号来表示，可以避免主流程图过于臃肿。 |
|  | **连接线** | 用来连接各个元素，表示流程的**走向**。箭头方向至关重要，我有时还会在连接线上标注文字，比如“是”或“否”。 |

### 4.2.2 流程图常见结构

掌握了基本符号后，我就用它们来组合成三种最基本的“句型”或“结构”。几乎所有复杂的流程，都可以通过这三种基本结构的嵌套和组合来表达。

#### 1\. 顺序结构

![image-20250719220305242](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220305242.png)

这是最简单的结构，表示一组操作**按照时间先后、从上到下地依次执行**，中间没有任何分支或重复。

图中的“发布新闻评论”流程就是一个典型的顺序结构。用户从`浏览新闻`，到`查看新闻详情`，再到`发布评论`，整个过程是一条直线走到底的（其中“是否已登录”是一个选择结构，我们下面会讲）。

#### 2\. 选择结构

这是用来表达“判断”和“分支”的结构。当流程走到某一步需要根据不同情况，走向不同路径时，我就用它。

  * **二元选择结构**
    ![image-20250719220457761](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220457761.png)

    这就是一个简单的“**二选一**”逻辑。流程在决策点上，根据条件“是”或“否”，走向两条不同的道路。

    图中“校验手机号”的例子很清晰：系统判断`手机号是否符合规范？`。如果“是”，流程就继续往下走到`获取验证码`；如果“否”，流程就走另一条路，回到`输入手机号`这一步，让用户重新输入。

  * **多元选择结构**
    ![image-20250719220534751](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220534751.png)

    当一个决策点可能产生**多于两个**的分支时，我就使用多元选择结构。

    图中的`用户选择登录方式`就是一个很好的例子。用户在这里可以做出三种选择，分别走向`手机号登录`、`账号密码登录`、`第三方登录`这三条完全不同的、并行的路径。

#### 3\. 循环结构

![image-20250719220609300](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220609300.png)

当流程中的某一个或几个步骤，需要**被重复执行**，直到某个条件满足为止时，我就使用循环结构。

图中“发送验证码”的例子非常经典：

1.  系统执行`发送验证码`操作。
2.  然后进入判断`是否发送成功？`。
3.  如果“否”，则执行`重新发送`，然后**流程线绕回去**，再次进入`是否发送成功？`的判断。
4.  这个“发送-判断-重发”的过程会一直循环，直到“是否发送成功？”的判断结果为“是”，流程才会跳出这个循环，继续执行下一步`输入验证码`。


---


### 4.2.3 流程图绘制工具

[此处放置“流程图绘制工具”的图片]

“工欲善其事，必先利其器”。虽然理论上用纸笔就能画流程图，但在实际工作中，我一定会使用专业的工具，因为它们更高效、更规范，也便于修改和分享。市面上的工具很多，我将几款主流工具的特点总结在了下面的表格里。

| 工具名称 | 核心特点 | 我推荐的使用场景 |
| :--- | :--- | :--- |
| `墨刀白板` | 国产在线一体化平台，集原型、设计、流程图于一体，协作功能强大，上手快。 | **强烈推荐新手使用**。尤其适合移动端产品团队，需要快速产出原型并进行协作评审的场景。 |
| **Axure RP 9** | 功能强大的专业原型工具，同时内置了流程图功能。 | 当你需要在一个工具里，同时完成高保真原型和详细流程图的绘制时，无缝衔接。 |
| **Visio** | 微软出品，功能全面，模板库强大，非常标准化。 | Windows环境下，需要绘制非常专业、复杂的企业级流程图或网络拓扑图等。 |
| **OmniGraffle** | Mac平台专属，界面精美，交互体验流畅。 | Mac重度用户，对绘图的视觉效果和体验有较高要求。 |
| **ProcessOn** | 国产在线协作绘图工具，专注于流程图、思维导图等。 | 需要多人实时共同编辑一份流程图，进行头脑风暴或在线评审的场景。 |
| **EdrawMax (亿图图示)** | 国产跨平台软件，内置海量模板和素材库。 | 希望快速套用模板，高效产出多种类型图表的用户。 |

**我的建议：**
对于新手，我通常推荐从 **墨刀 (MockingBot)** 这样的在线一体化工具开始，因为它免费、易用，并且集成了我们产品经理最高频使用的多种功能，协作起来也非常方便。它的确太好用了。
### 4.2.4 流程图绘制思路与注意事项

选好了工具，接下来就是最重要的部分——如何思考。画图只是思考结果的表达，图画得好不好，本质上是思路清不清晰。我总结了自己的一套“四步思考法”和“五大注意事项”。

#### 1. 绘制思路

![image-20250720080050483](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080050483.png)

* **明确核心目的**：在动笔前，我一定会先用一句话说清楚：我画这张图，是为了给谁看？想说明白一件什么事？比如，是为了跟开发讲清楚一个功能的逻辑，还是为了跟老板讲明白一个业务模式。
* **先想后画**：我从不直接在软件上拖拽图形。我习惯先在草稿纸或白板上，把关键节点和流程大致地勾勒出来，想清楚了再用工具画，这样效率最高，也避免了在细节上反复修改。
* **先主线后支线**：我总是先把一个流程最理想、最通畅的“主干道”画出来。然后再回头，去补充那些异常情况、判断分支等“小路”。这样能保证我的逻辑主线是清晰的。
* **多思考边界异常**：一个产品经理的价值，很大程度上体现在对异常情况的考虑是否周全。比如，用户输错密码怎么办？网络断了怎么办？库存不足了怎么办？我会尽可能地把这些边界和异常情况都考虑到我的流程图里。

#### 2. 绘制注意事项

![image-20250720080153245](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080153245.png)

* **顺序排列**：尽量保持从上到下、从左到右的统一流向，避免连接线交叉、混乱。
* **开头结尾**：一个完整的流程必须有明确的“开始”和“结束”符号。我绝不允许画一个没有终点的流程。
* **是否闭环**：我要确保流程的每一个分支都有一个明确的去向，最终都能导向一个结束节点或回到主流程，不能出现“断头路”。
* **善用标注**：当图形本身无法完全说清楚逻辑时，我会毫不犹豫地使用文字标注来补充说明，清晰永远是第一位的。
* **化繁为简**：如果一个流程图变得过于巨大和复杂，我会思考是否可以把它拆分成几个子流程来表达。我们的目标是用最简洁的图，说明白最复杂的事。

---

### 练习：绘制“找工作”流程图

![image-20250720080301030](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080301030.png)

现在，我们来做一个练习。请根据我们在图片中看到的“找工作流程图”案例，亲手绘制几张图。这个案例的流程如下：
a. 先在各个招聘网站投简历
b. 公司的HR看到你的简历后，初步评估，如果符合岗位需求，就邀请你去公司面试
c. 接到面试通知后，你就去公司参加面试，先由HR面试，再由该岗位的产品经理给你初面
d. 上面两次面试都通过后，HR会再约你谈薪资，最后确认录用你，就会给你发offer

**【练习任务】**

1.  **任务一：绘制业务流程图**
    请思考一下，这个流程涉及到哪些核心角色？（比如：求职者、HR、用人部门等）。

    请你画一张**业务流程图**，清晰地表达出这些角色以及他们在整个求职过程中的主要交互和行为。
    
    ![未命名白板](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E6%9C%AA%E5%91%BD%E5%90%8D%E7%99%BD%E6%9D%BF.png)

在画的这张图里，所有的动作都放在了一条线上。但实际上，“找工作”这个业务，至少涉及到三个角色：

- **求职者**
- **HR**
- **用人部门** (这里就是产品经理)

这三个角色在不同的时间点，做着不同的事，互相配合才完成了整个流程。而我们画“业务流程图”的核心目的，就是要清晰地展现这种**“跨角色的协作关系”**。

**那么，如何优化呢？**

我推荐使用一种最经典的业务流程图——**泳道图 (Swimlane Diagram)**。

您可以想象一个游泳池，我们为“求职者”、“HR”、“用人部门”这三个角色，分别划分出一条独立的“泳道”。然后，我们把现在画的这些步骤，按照“**这个动作是谁做的**”，放回到对应角色的泳道里。



---
## 4.3 泳道图
在上一节的练习中，我们提到了一个关键概念——**泳道图 (Swimlane Diagram)**，用它来优化我们画的业务流程图。

现在，我们就来系统地学习一下这个我个人非常推崇的、能清晰表达多角色协作关系的强大工具。


### 4.3.1 泳道图定义

![image-20250720082100314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720082100314.png)

#### 1. 跨职能（多角色）流程图

正如它的名字一样，泳道图，我把它就看作是带“泳道”的流程图。它的官方定义是**跨职能流程图**。

它的核心价值在于，它不仅能展示**“要做什么”（What/流程）**，更能清晰地展示**“由谁来做”（Who/角色/部门）**。

#### 2. 多角色协同与多阶段协同

在我的实践中，泳道的划分方式主要有两种：

* **按角色/部门划分**：这是我最常用的一种。就像我们“找工作”案例中的'求职者'、'HR'、'产品经理'。我用它来理清不同的人或团队之间的工作交接关系和职责边界。
* **按阶段划分**：有时，一个流程会经历几个大的阶段，比如“需求阶段”、“设计阶段”、“开发阶段”、“测试阶段”。我也可以用泳道来划分这几个阶段，清晰地展示任务在不同阶段的流转。

不过，在日常工作中，我们绝大多数时候都是**按角色划分**。

### 4.3.2 泳道图绘制思路

![image-20250720082152503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720082152503.png)

绘制泳道图，我的思路比画普通流程图会多几个“规划”步骤，这能确保最终的图清晰、准确。

1.  **明确目标对象**：和画所有图一样，第一步永远是明确我画这张图的目的。我要说明的是一个什么样的流程？
2.  **梳理角色/阶段**：这是泳道图独有的一步。我会把这个流程中涉及到的所有**参与方（角色/部门）**全部罗列出来。这是构建泳道的基础。
3.  **划分归属**：我会把流程中的每一个动作（节点），明确地分配给上一步中罗列出的角色。也就是回答“这件事，到底该归谁管？”这个问题。
4.  **对应绘制**：最后一步才是动手画。我先画好垂直或水平的泳道，然后把上一步中“划分好归属”的动作节点，一个一个放到各自的泳道里，再用流程线将它们连接起来。

---

#### **案例解析：找工作泳道图**

![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/38bd1fda5226237bb446e314a9183d54.png)

理论说完了，我们直接来看上一节练习的“标准答案”——**找工作泳道图案例**。这张图完美地诠释了泳道图的绘制思路和价值。

1.  **第一步：梳理角色。**
    我们看到，这张图清晰地定义了四个泳道，也就是四个核心角色：`求职者`、`HR`、`产品经理`、`产品总监`。

2.  **第二步：划分归属并绘制。**
    我们跟着流程线走一遍，就能清晰地看到动作和角色的对应关系：
    * 流程从 **求职者** 泳道的 `投递简历` 开始。
    * 箭头跨越泳道，流向 **HR** 泳道的 `查看简历` 和 `系统初筛`。
    * 如果通过，流程继续在 **HR** 泳道里走到 `邀请面试`，然后再次跨越泳道，信息流转回 **求职者** 的 `接收信息`。
    * 后续的 `初面` 由 **产品经理** 负责，`复面` 由 **产品总监** 负责，最后的 `薪资沟通` 又回到了 **HR** 这里。

**我的洞察：**
通过这张图，我不仅知道了找工作的完整步骤，更重要的是，我能一眼看清**在每个环节，我应该去找谁，谁是负责人，以及信息和任务是如何在不同角色之间流转交接的**。

这种对“职责”和“协作”的清晰表达，是普通流程图无法给予的。这就是泳道图的威力所在，也是为什么它在表达复杂业务流程时，是我最重要的工具。




---

## 4.4 结构图介绍

我们已经掌握了用来描述**动态“过程”**的流程图。现在，我们来学习与它互补的、用来描述**静态“组成”**的结构图。

如果说流程图是产品的“电影剧本”，那么结构图就是产品的“骨骼X光片”或“解剖图”。它不关心先后顺序，只关心“**这个东西，是由哪些部分构成的？**”

### 4.4.1 结构图的定义与分类

![image-20250720093047853](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093047853.png)

#### 1. 结构图的定义

我给**结构图**的定义是：**一种通过树状或脑图等形式，来表达产品、功能或信息层级关系的可视化图表。**
它的核心作用，就是帮助我把一个复杂、混沌的整体，拆解成一个个清晰、有序、有归属的部分。

#### 2. 常见结构图类型

就像流程图一样，根据我拆解的对象不同，我主要会用到三种结构图。

* **功能结构图 (Functional Structure Diagram)**
当我需要梳理一个产品或模块**“有哪些功能”**时，我就会画功能结构图。它是一个从抽象到具体的功能拆解过程，帮我梳理出完整的功能清单（Function List）。
![image-20250720093116350](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093116350.png)


我们来看“挂号功能结构图”这个案例。它清晰地将“挂号”这个大功能，拆解为`返回首页`、`搜索`、`选择科室`、`医院列表`等子功能，然后又把`医院列表`这个功能，进一步拆解为`筛选`和`查看医院`这两个孙子级功能。通过这张图，我就能确保在设计时，不会遗漏任何一个必要的功能点。

![image-20250720093127402](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093127402.png)
    

​    


* **信息结构图 (Information Structure Diagram)**
  

当我需要梳理一个页面或模块**“要展示哪些信息”**时，我就会画信息结构图。它拆解的不是“功能”，而是“数据和信息”。
    
![image-20250720093358095](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093358095.png)
    
在“挂号信息结构图”这个案例中，我们看到，它把“医院列表”这个模块，拆解为它需要展示的`封面图`、`名称`、`评分`、`地区`、`等级`等信息字段。这张图是我和UI设计师沟通界面内容、以及和开发工程师沟通数据字段时的重要依据。

![image-20250720093411563](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093411563.png)


* **产品结构图 (Product Structure Diagram)**
    产品结构图，在我看来，是**功能结构图和信息结构图的集合体**，是产品最全面、最宏观的一张“鸟瞰地图”。

    ![image-20250720093505990](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093505990.png)

    我们看“挂号产品结构图”这个案例，它既包含了`搜索`这样的**功能模块**，也包含了`科室金刚区`、`查看Banner`这样的**信息模块**和**界面元素**。它是我在进行原型设计之前，用来组织整体产品框架的“总设计图”，能帮我从全局视角思考产品每个部分的构成和关系。

![image-20250720093515445](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093515445.png)

---
## 4.5 结构图的绘制

### 4.5.1 结构图绘制注意事项

绘制结构图虽然比流程图要更自由一些，但我依然会遵循一些基本原则，来保证图表的清晰和易读。

![image-20250720093556927](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093556927.png)

1.  **层级数量**
    我画结构图时，会尽量把层级控制在**3-4层**以内。如果一个分支拆解得过深，说明这个模块可能太复杂了，我会考虑把它单独拎出来，为它画一张新的、更详细的结构图。

2.  **绘制方式**
    我习惯用“**自顶向下，逐层分解**”的方式来画。先确定最顶层的核心主题，然后拆分出第二层的主要构成，再把第二层的每一项继续往下拆，这样能保证逻辑的清晰和结构的完整。

3.  **顺序**
    和流程图不同，结构图同一层级的节点，左右顺序并没有严格的规定。我的原则是“**表达清楚即可**”，有时我会把逻辑上更重要或更核心的模块放在左边或上边，但这并不是硬性要求。



---

### 课习：拆解“视频播放页面”

现在，我们来做一个结构图的练习。请你想象一下，我们正在设计一个类似于YouTube或Bilibili的视频网站，你的任务是，对最重要的**“视频播放页面”**进行结构化拆解。

这个页面通常包含以下元素：
* 主视频播放器窗口
* 视频标题、UP主（上传者）信息（头像、昵称、粉丝数）、订阅按钮
* 点赞、不喜欢、分享、下载、收藏等互动按钮
* 视频简介、播放量、发布日期等数据
* 评论区（包括评论输入框、评论列表）
* 右侧的相关视频推荐列表

**【练习任务】**

1.  **任务：绘制功能结构图**
    请你画一张**功能结构图**，来拆解这个页面上所有**用户可以进行的操作**。
    
* 从顶层的“视频播放页功能”开始，往下拆解出例如“播放器控制”（如：播放/暂停、调节音量、全屏）、“视频互动”（如：点赞、收藏）、“作者互动”（如：订阅）、“评论互动”等几大功能模块，并思考这些模块下还可以有哪些更细分的子功能。
  
    ![未命名白板 (3)](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E6%9C%AA%E5%91%BD%E5%90%8D%E7%99%BD%E6%9D%BF%20(3).png)
    

---



# 第五章：产品设计与原型制作

在前面的章节里，我们投入了大量精力去“听”和“想”，我们学会了如何收集、分析、管理需求，这些都属于“**问题域**”的范畴——即，**我们应该解决什么问题**。

从这一章开始，我们将进入“**解决方案域**”——即，**我们应该如何设计产品，来优雅地解决这些问题**。我会带大家走完从梳理设计思路，到最终绘制出可交互原型的全过程。

## 5.1 产品设计思路

![image-20250720095709353](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720095709353.png)

在我正式开始画任何一个线框图（Wireframe）之前，我的脑海里必须有一套清晰、结构化的设计思路。这能确保我设计出的功能，是源于真实的用户场景，并且逻辑是通顺的。

我的这套思路，可以用一个公式来表达：**在明确的【角色】和【场景】下，为了达成用户的【目的】，他需要走通一个怎样的【流程】，而我们需要提供什么样的【功能】来支撑这个流程。**

下面，我们就用一个完整的案例，来贯穿这套设计思路。

### 5.1.2 案例：直播间需求分析

我们机构的某个毕业学员 P小M 入职了一家做在线英语培训的公司，公司有很多外籍教师，学员大都为中国学生。目前公司的产品经理在每周对需求池当中的原始需求进行整理分析时，发现之前有个叫Zoe的外籍老师上周提出了如下需求：
**“希望可以在上课时在网页版的直播间里可以打字，进行答疑，并且在直播课里最好提供举手、邀请某人语音的功能”**
当在公司当中遇到了这样的一个需求，你会怎样去考虑呢？

### 5.1.3 产品设计流程

我会严格按照“角色 → 场景 → 目的 → 流程 → 功能”这五个步骤，来一步步地推导出我的产品方案。

#### 1. 角色 (Role)

首先，我需要明确，这个场景下，我的核心用户是谁？
* **Zoe**：外籍老师，她的一个关键特征是**不认识中文**。
* **直播间学员**：中国学员，他们的特征是需要在直播中与老师互动。

#### 2. 场景 (Scene)

![image-20250720095921040](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720095921040.png)

这两个角色，在“直播间上课”这个大场景下，会发生哪些具体的交互子场景？
* **子场景1：老师答疑**。老师在讲课过程中，学员随时会产生疑问，需要老师解答。
* **子场景2：点学员回答问题**。老师为了增强互动，需要主动挑选一位学员来回答问题。
* **其他场景**：比如老师需要在直播间内布置作业等。

#### 3. 目的 (Goal)

![image-20250720100201566](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100201566.png)

在这些具体的子场景下，各个角色的核心目的是什么？
* **老师答疑场景的目的**：方便老师能**及时地**对学员提出的问题进行答疑。
* **点学员回答问题场景的目的**：方便**不认识中文昵称**的外籍老师，能**方便地**挑选学生回答问题。

#### 4. 流程 (Flow)

![image-20250720100036943](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100036943.png)

为了达成上述目的，一个理想化的操作流程是怎样的？
* **老师答疑的流程**：老师讲课 → 学员产生疑问 → 学员通过某个方式提出问题 → 老师看到问题并解答。
* **点学员回答问题的流程**：老师想提问 → 老师通过某个方式主动挑选学生 → 被选中的学生通过某个方式回答问题。

#### 5. 功能 (Function)

![image-20250720100113753](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100113753.png)

最后，也是最关键的一步：为了支撑上述流程的顺畅运转，我们需要提供哪些核心功能？
* **支撑“老师答疑”流程**：最直接的功能就是**提供一个聊天区**，让学生和老师都可以用文字进行实时的提问和回答。
* **支撑“点学员回答问题”流程**：
    1.  针对“老师方便挑选”：我们可以**提供一个“举手”功能**，想回答问题的学生可以“举手”，老师就能从举手的学生里选。
    2.  针对“学生方便回答”：我们可以**提供一个“拉上麦”功能**，老师可以直接点击举手学生的头像，邀请他上麦进行语音回答。

到此为止，我们就通过一套严谨的思路，把一个模糊的需求，推导出了三个具体、可执行的功能点：**聊天区、举手、拉上麦**。

---

### 5.1.4 功能清单

![image-20250720100327337](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100327337.png)

当我通过上述流程推导出多个功能点后，我会把它们整理成一份**“功能清单（Function List）”**。这份清单详细地列出了为了满足本次产品目标，我们需要开发的所有功能模块和子功能。它是我们后续进行原型设计和与开发团队沟通的基础。

### 5.1.5 功能清单与需求池的区别

我需要强调一下“功能清单”和我们之前提过的“需求池”的区别。

| **对比维度** | **需求池 (Requirement Pool)** | **功能清单 (Function List)** |
| :--- | :--- | :--- |
| **内容** | **未经处理的“原始需求”集合**。包含了各种想法、问题、建议，是发散的。 | **经过分析和设计后，得出的“产品解决方案”**。是明确、收敛、可执行的功能项。 |
| **阶段** | 处于**“问题域”**，是我们分析的起点。 | 处于**“解决方案域”**，是我们设计和开发的起点。 |

简单来说，**需求池是“原材料仓库”，而功能清单是“加工图纸”**。

### 5.1.6 产品设计思路工具运用方式

我们前面学过的很多工具，都会在这个阶段被综合运用。

![image-20250720100442872](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100442872.png)

* 我会用**用户访谈**来明确角色、场景和目的。
* 我会用**流程图**来梳理和表达流程。
* 我会用**结构图**（特别是功能结构图）来整理我的功能清单。




---

## 5.2 原型的概念及分类

对我来说，原型是连接“需求文档”与“最终产品”之间最重要的一座桥梁。它是产品想法的第一次可视化、具象化的表达。

### 5.2.1 学习目标

在本节中，我的目标是带大家清晰地理解原型的不同“保真度”（Fidelity）的概念。我们将学习区分草图、低保真原型和高保真原型的差异，以及我会在什么样的情况下，选择使用哪一种原型。

### 5.2.2 原型的概念及分类

![image-20250720100747254](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100747254.png)

我给原型的定义是：**用线条、图形绘制出的产品框架，也称线框图，是需求和功能的具体化表象。**

在我的工作中，我从不会把原型看作是“一个东西”，而是根据项目的不同阶段和沟通目的，把它分为三种不同的类型。

#### 1. 草图原型 (Sketch)

![image-20250720100813975](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100813975.png)

这是我进行产品设计的第一步，也是最快速、最低成本的一种方式。

* **特点**：顾名思义，它就是用笔和纸（或者在白板上）随手画出的草稿。我画草图时，**核心是梳理逻辑框架和页面流程**，完全不讲究排版、对齐和美观，也不需要表达出所有的页面元素。
* **我的适用场景**：我通常在个人进行**方案构思**的阶段，或者在**团队内部进行头脑风暴**时，大量使用草图。它的使命就是快速表达、快速讨论、快速迭代，画完就可以随时扔掉，没有任何心理负担。

#### 2. 低保真原型 (Low-fidelity Prototype)

![image-20250720100947720](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100947720.png)

当我的思路通过草图基本确定后，我就会使用Axure、墨刀等专业工具，来绘制正式的**低保真原型**。这在我的日常工作中，是产出最多、也最重要的一类原型。

* **特点**：它要求**绘图整齐、布局规范**。我通常只使用黑、白、灰三种颜色，用简单的线框和色块来表示图片、文字和各类组件。虽然它看起来很朴素，但它必须**完整、准确地表达出产品方案**，页面上所有的功能、按钮、文案、跳转关系都必须清晰无误。
* **我的适用场景**：低保真原型是我用来进行**正式方案交付**的“文档”。我会用它来召开**需求评审会**，并把它作为最终交付给开发和测试工程师的**研发依据**。它剥离了所有视觉干扰，让大家都能聚焦在功能和流程本身。

#### 3. 高保真原型 (High-fidelity Prototype)

![image-20250720101038793](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720101038793.png)

这是保真度最高的原型，它在视觉上已经和最终的线上产品非常接近了。

* **特点**：它不仅**绘图规范、排版求真**，还包含了丰富的视觉元素，如配色、图标、字体、图片等。更重要的是，它通常是**可以交互的**，用户可以像使用真实App一样在上面点击、跳转，来模拟真实的使用体验。
* **我的适用场景**：因为制作成本很高，我只在特定的场景下才会制作高保真原型。比如，需要向老板或投资人进行**路演宣传**时；或者，在产品上线前，需要进行**用户体验测试（Usability Testing）**时；以及，有些公司的管理流程，要求在开发前必须有高保真原型用于最终决策。



---

## 5.3 原型绘制工具

### 5.3.1 学习目标

我的目标是带大家熟悉一款现代化的原型工具的核心使用逻辑。我们将了解原型工具的界面通常是如何分布的，并掌握那些最常用的基础元件（也就是我们画原型时的“砖块”）应该在什么场景下使用。

### 5.3.2 原型绘制工具介绍及作用

在上一节，我们明确了原型有草图、低保真、高保真之分。要绘制出规范的低保真和高保真原型，我们就必须借助专业的工具。这些工具能帮助我们高效地搭建页面结构、添加交互，并方便地进行分享和评审。

### 5.3.3 常用原型绘制工具

正如我们之前讨论并达成共识的，在众多工具中，我个人非常推荐像 **墨刀 (MockingBot)** 这样集设计、原型、协作为一体的在线平台。它功能强大、上手简单，非常适合我们当前的学习和未来的团队协作。

**接下来的内容，我会以通用原型工具的核心逻辑进行讲解，其中的概念和操作，您都可以在我们选定的“墨刀”中找到并熟练应用。**

### 5.3.4 原型工具的核心工作区

无论我们使用哪款工具，其主界面通常都由几个核心的“工作区”构成。我将这些区域的功能总结如下，这能帮助我们快速熟悉任何一款新工具的布局。

**菜单与工具栏 (Menu & Toolbar)** 通常在界面的最上方。这里集成了软件的通用功能，比如文件操作（新建、保存、导出）、常用工具（选择、放大、缩小）等。

**页面管理区 (Page Management Area)** 通常在左侧。这是我们整个项目的“目录树”，我在这里管理原型的所有页面，可以进行新增、删除、重命名和调整层级。

**元件库 (Widget/Component Library)** 这是我们的“工具箱”和“素材库”，通常也在左侧。里面包含了我们绘制原型需要的所有“砖块”，如按钮、文本框、图片等，我只需要把它们拖拽到画布上即可使用。

**画布 (Canvas)** 这是界面中心最大的一块区域，是我们的“画板”。我们所有的设计工作都在这里完成。

**检视区 (Inspector Panel)** 通常在右侧。这是我用来精细调整元件的“属性面板”。当我选中画布上的任何一个元件时，都可以在这里修改它的尺寸、颜色、字体、边框，以及为它添加交互效果。

![image-20250720101923527](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720101923527.png)

**概要/图层区 (Outline/Layers Area)** 这个区域会以列表的形式，显示出当前画布上所有的元件及其层级关系。当页面变得复杂、元件相互重叠时，我通过这里可以非常方便地选中和管理它们。

**母版/组件区 (Masters/Components Area)** 这是一个进阶但非常有用的功能。对于那些需要在多个页面重复使用的元素（比如导航栏、页脚），我会把它们创建为“母版”或“公共组件”。这样，我只需要修改一次母版，所有引用了它的页面都会同步更新，极大地提升了效率。

![image-20250720102108869](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102108869.png)



### 5.3.5 常见元件的使用场景

![image-20250720102252484](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102252484.png)

掌握了工作区布局后，我们就要来认识一下“元件库”里那些最常用的“砖块”了。熟悉它们各自的用途，是画好原型的基础。

| 元件 (Widget) | 我的使用场景说明 |
| :--- | :--- |
| **矩形/图片/占位符** | 这是我用来搭建页面基本骨架的“积木”。我用它们来快速划分页面区域、表示图片或Banner等内容占位。 |
| **按钮 (各类)** | 用于触发核心操作，是用户与系统交互最直接、最重要的途径。比如“登录”、“提交”、“购买”等。 |
| **标题/文本** | 用于构建页面的信息层级，清晰地传达各类文字内容，是页面的“血肉”。 |
| **文本框/文本域** | 当需要用户**输入**单行或多行文字时使用。比如：用户名输入框、搜索框、评论输入区。 |
| **下拉列表/单选/复选** | 当需要用户从一组**固定的选项**中进行选择时使用。单选只能选一项，复选可以选多项。 |
| **表格/列表** | 用于结构化地、清晰地**展示大量数据**或信息。比如后台管理系统的数据列表。 |
| **`热区`** | 这是一个“隐形”的矩形。当我想让一张图片或一组元素实现整体点击跳转时，我就会在上面覆盖一个热区来添加交互，它本身在预览时是看不见的。 |








---

## 5.4 原型设计规范

在我看来，画原型绝不仅仅是把各种元件拖到画布上就完事了。为了让我的原型图清晰、专业、具备可交付性，我必须遵循一套严格的**设计规范**。

这套规范，不是为了限制我们的创意，恰恰相反，它是为了**提升我们整个团队的沟通效率**。一个遵循规范的原型，就像一篇字迹工整、标点清晰的文章，能让读它的人（设计师、开发、测试）一目了然。

### 5.4.1 学习目标

在本节中，我的目标是带大家掌握我绘制原型时所遵循的几项基本规范。我们将学习Web端和移动端的标准尺寸、常见的页面结构，以及能让你的原型图专业度瞬间提升的五大注意事项。

### 5.4.2 尺寸规范

在我开始绘制任何页面之前，我首先要确定的，就是我的“画板”尺寸。

#### 1. Web端尺寸规范

![image-20250720102904090](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102904090.png)

对于Web端的网页原型，现在主流的显示器分辨率是1920*1080。因此，我的画布宽度通常会设置为1920px或更高。

但更重要的一个概念是“**版心**”。版心指的是网页上承载核心内容的有效显示区域。为了保证在不同尺寸的宽屏显示器上，内容都清晰易读、不会过分拉伸，我通常会将**版心的宽度控制在1000px到1200px之间**，并让它在页面上水平居中。

#### 2. 移动端尺寸规范

![image-20250720103150325](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103150325.png)

对于移动端的App原型，为了保持所有页面的一致性，我会选择一个基准尺寸来作图。目前，我以及行业内最通用的低保真原型尺寸，是基于iPhone 6/7/8的逻辑分辨率：**375 x 667 px**。

在这个基准尺寸内，我还对几个系统级的区域高度，严格遵守规范：

* **状态栏 (Status Bar)**：就是手机最顶部显示信号、时间、电量的那一条。它的标准高度是 **20px**。
* **导航栏 (Navigation Bar)**：是页面顶部的、包含页面标题和返回按钮的区域。它的标准高度是 **44px**。
* **标签栏 (Tab Bar)**：是App底部的主菜单导航。它的标准高度是 **49px**。

从一开始就遵循这些尺寸规范，能让我的原型图显得非常专业，也便于后续UI设计师进行视觉稿的还原。

### 5.4.3 结构规范



尺寸确定后，我会思考页面的整体布局结构。

* **Web端**：最常见的两种结构是**左右布局**（左侧为导航，右侧为内容区，常见于后台管理系统）和**居中布局**（导航和内容区都在页面中心，常见于官网、博客等）。

![image-20250720103106633](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103106633.png)

* **移动端**：一个典型的App页面，其结构通常由上至下由“**状态栏 + 导航栏 + 内容区 + 标签栏**”这几个固定的区块构成。熟悉这些通用结构，能帮我快速、规范地搭建页面。

![image-20250720103247946](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103247946.png)

### 5.4.4 原型设计规范注意事项

![image-20250720103507688](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103507688.png)

最后，也是最重要的，我总结了我个人在绘制原型时，一定会遵守的“五大黄金法则”。做好这五点，你的原型图就能立刻和“业余”拉开差距。

1.  **页面结构**：在原型工具中，我会用文件夹和清晰的命名，来组织我的页面层级，让整个项目的结构一目了然。
2.  **框架比例**：我会先定好页面的基础布局和比例，并在所有页面中保持一致，这能带来稳定、舒适的视觉感受。
3.  **间距一致**：这是专业性的关键体现。我会确保元素与元素之间的“间距”是有规律且统一的。比如，卡片与卡片的间距是16px，那在所有地方都应该是16px。
4.  **位置对齐**：我要求自己做到“像素眼”，借助工具的对齐功能，确保页面上所有的元素，要么左对齐，要么居中对齐，要么右对齐。绝不允许出现肉眼可见的错位。
5.  **元件大小**：相同类型的元件，尺寸必须保持一致。比如，所有主要按钮的高度都是44px，所有正文的字号都是14px。这能让界面看起来更和谐、更具秩序感。

### 5.4.5 原型设计规范小结

我将原型设计的核心规范，总结为下面这张自检表：

| **规范维度** | **我的核心原则** |
| :--- | :--- |
| **尺寸 (Size)** | Web端关注**1200px版心**，移动端以**375x667**为基准。 |
| **结构 (Structure)** | 采用**通用布局**（如Web居中布局，App上下导航结构）。 |
| **注意事项** | **对齐、间距、大小、比例、结构**，五大要素在整个原型中，必须保持高度**一致性**。 |






---

## 5.5 墨刀制作基础交互

一个只会展示、不能点击的原型，就像一张没有灵魂的皮囊。而**交互**，就是我们为这具皮囊注入灵魂的过程。它能把一张张孤立的页面，串联成一个完整、可体验的产品故事。

在这一节，我将带大家学习交互设计的基本逻辑，并掌握如何使用我们选定的工具——**墨刀 (MockingBot)**，来制作几种最常见、最核心的交互效果。

### 5.5.1 学习目标

我的目标是，让我们掌握交互设计的核心公式，并能熟练运用墨刀，独立制作出页面跳转、弹窗、悬浮提示和轮播图这四种基础但至关重要的交互效果。

### 5.5.2 什么是交互

我理解的“交互”，就是**用户与产品之间的一场对话**。用户通过点击、滑动、输入等行为“说话”，而产品则通过页面变化、动画、提示等方式来“回应”。

![image-20250720110600853](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110600853.png)

### 5.5.3 什么是交互设计

![image-20250720110625509](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110625509.png)

那么，“交互设计”，就是我们作为产品经理，去**预设这场对话的规则和剧本**。

在墨刀这样的原型工具里，这个剧本的创作遵循着一个万能公式，这也是交互设计的核心：
**交互 = 事件 (Event) + 动作 (Action)**

* **事件**：就是“**当用户做什么的时候**”。这是触发器。比如：`当用户单击时`、`当鼠标移入时`、`当页面加载时`。
* **动作**：就是“**产品应该发生什么变化**”。这是响应。比如：`链接到某个页面`、`显示/隐藏某个元素`、`改变某个元件的状态`。

我们所有的交互设计，都是围绕着“在什么事件下，执行什么动作”来展开的。

### 5.5.4 常见交互设计

![image-20250720110650638](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110650638.png)

掌握了“事件+动作”这个核心公式，我们就可以组合出千变万化的交互。以下是我在工作中最高频使用的四种。

1.  **跳转**：这是最基础的交互，它将页面串联起来。在墨刀里，我选中一个按钮，为它添加一个“**单击**”的**事件**，再选择“**链接到页面**”这个**动作**，并指定目标页面即可。
2.  **显示/隐藏**：常用于制作弹窗和下拉菜单。我先将要弹出的内容（比如一个弹窗）设置为默认隐藏。然后给一个触发按钮添加“**单击**”**事件**，并选择“**显示/隐藏**”**动作**，作用于那个隐藏的弹窗。
3.  **悬浮显示**：常用于制作提示信息（Tooltip）。我会给目标元件添加“**鼠标移入**”**事件**，触发“**显示**”某个提示框的**动作**；同时再添加一个“**鼠标移出**”**事件**，触发“**隐藏**”这个提示框的**动作**。
4.  **动态面板/轮播**：用于制作轮播图等效果。在墨刀里，这个交互被简化了。我可以直接使用它自带的“**轮播**”组件，把几张图片放进去，它就能自动实现切换效果。其背后的逻辑，就是通过“**延时**”这个**事件**，来触发“**切换到下一状态**”的**动作**。

### 5.5.5 墨刀制作基础交互案例展示

我们来看这四种交互在真实场景下的应用，我已经预设做好了两个模板，分别为微信的原型图以及对应的聊天详情，如下：

![image-20250720123723714](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720123723714.png)

1.  **跳转页面案例**
    就像微信的聊天列表，当我要实现点击某个好友，就进入和他聊天的页面时，我就会为列表里的每一项，都添加一个“单击”事件，并分别链接到对应的聊天页面。

![image-20250720123838731](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720123838731.png)

**实现效果如下：**

![](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/PixPin_2025-07-20_12-39-15.webp)







2.  **弹框提示案例**
  
    ![image-20250720125649096](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720125649096.png)
  
    当用户点击某个按钮时，为了响应他们，我需要弹出一个确认框。这个确认框，我会在墨刀里提前画好并设置为隐藏。然后给对应按钮添加“单击”事件，动作为“显示”这个确认框。
    
    ![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/PixPin_2025-07-20_12-57-24.webp)



1.  **悬浮显示案例**
    ![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/e0990e1c-08c9-42c9-81a6-443e3b2ef0cc.png)
    当鼠标移到一个被缩略的标题上，我希望显示完整的标题。我就会做一个隐藏的、包含完整标题的文本框，然后通过“鼠标移入/移出”事件，来控制它的显示和隐藏。

---




# 第六章：产品需求文档（PRD）撰写



![image-20250720133044016](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133044016.png)

到目前为止，我们已经走过了漫长的旅程：从收集需求、分析需求，到梳理设计思路、绘制可交互的原型。现在，我们手上已经有了一套清晰的解决方案。

一个很自然的问题是：**我可以直接把我的原型，丢给开发和设计师，让他们照着做出来就行了吗？**

我的答案是：**绝对不行**。

因为原型，尤其是低保真原型，只能展示“**它看起来长什么样**”，却无法说清楚“**它在各种情况下应该如何工作**”。为了弥补这个信息鸿沟，确保我们的想法能被100%精准地实现，我们就需要产出产品开发流程中，最核心、最正式的一份交付文档

——**产品需求说明文档（Product Requirements Document, PRD）**。

## 6.1 产品需求说明（PRD）概述

在这一节，我将带大家全面地认识PRD——它是什么？为什么如此重要？以及一份专业的PRD，应该包含哪些内容？

### 6.1.1 学习目标

我的目标是，让我们深刻理解PRD在产品研发流程中不可或缺的“契约”作用。学完本节，我希望我们都能明确一份合格PRD的定义、目的、常见形式和核心构成要素。

### 6.1.2 产品需求说明的定义

![image-20250720133145066](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133145066.png)

我给PRD的定义是：**一份针对即将开发的产品功能或方案，进行全面、详细、无歧义的必要说明，以确保方案被完整、准确地实现的正式文档。**

它的本质，就是用文档的形式，**把产品方案的每一个细节都解释得清清楚楚**。

![image-20250720133240188](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133240188.png)

我们看这个“注册登录”的案例。这个页面看起来很简单，但背后隐藏了无数的逻辑规则：密码的格式要求是什么？输入错误时如何提示？手机号已被注册怎么办？连续输错5次密码会怎样？……这些细节，单靠一张原型图是绝对无法表达的，必须通过PRD来详细说明。

### 6.1.3 产品需求说明的目的

我之所以不厌其烦地撰写PRD，是因为它能为我和我的团队，带来三大不可替代的价值：

1.  **确保方案的完整性**：撰写的过程，本身就是我自己对产品方案进行“极限测试”的过程，它会逼迫我去思考所有可能的异常流程和边界情况，确保方案没有漏洞。
2.  **确保团队理解一致**：PRD是研发团队（包括设计、开发、测试）开展工作的“唯一”和“必须”的依据。它能消除信息偏差，避免因为口头沟通带来的误解和返工。
3.  **方便未来回溯和迭代**：当产品上线后，PRD就是一份“历史档案”。未来的产品经理或团队成员，可以通过它，准确地了解当时我们为什么要做这个功能，以及当时的设计思路。

### 6.1.4 产品需求文档常见形式

![image-20250720133353206](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133353206.png)

PRD并没有一个全球统一的格式，在我的工作中，最常见的两种形式是：

* **Word文档格式**：这是最传统、最正式的形式。通过Word或类似的文档工具，撰写一份包含详细目录、图文并茂的说明书。优点是结构清晰、非常全面。
* **原型内嵌格式**：这是一种更敏捷的形式。我直接在原型工具（如Axure、墨刀）中，为每个页面和元件添加详细的文字标注和说明。优点是原型和文档合二为一，查看和理解更直观。

### 6.1.5 产品需求文档维护形式

![image-20250720133417399](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133417399.png)

在团队协作中，PRD的维护和共享方式也很重要。

* **本地化形式**：就是通过邮件、微信等方式，来回发送Word或原型文件。这种方式在小团队或不规范的团队里很常见，但极易造成版本混乱。
* **第三方工具**：这是我极力推荐的现代协作方式。我们将PRD统一维护在一个在线的、团队共享的平台上，比如**Confluence、Tapd、语雀**，甚至是**墨刀**的项目空间里。所有人访问的都是同一个、最新的版本，沟通和反馈都在线上进行，效率极高。

### 6.1.6 产品需求文档常见内容

![image-20250720133446836](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133446836.png)

无论形式如何，一份合格的PRD，在我看来，都必须包含以下六大核心模块：

1.  **文档更新记录**：记录每次文档的修改时间、修改人、修改内容。
2.  **需求背景与目的**：清晰地告诉团队，我们“为什么”要做这次的需求。
3.  **产品流程图**：附上我们在第四章学过的业务流程图、功能流程图等，帮助团队理解用户路径和功能逻辑。
4.  **产品结构图**：附上功能结构图或产品结构图，帮助团队理解产品的功能构成和信息框架。
5.  **产品交互需求说明**：**这是PRD最最核心的部分**。它需要结合原型图，对每一个页面的每一个元素的每一个状态和交互规则，进行详细的说明。
6.  **非功能性说明**：对一些非界面功能的需求进行说明，比如性能要求（页面加载速度）、兼容性要求、数据统计需求等。

### 6.1.7 产品需求说明小结

我将PRD的核心要点总结如下：

| **核心概念** | **我的理解与实践** |
| :--- | :--- |
| **PRD的定义** | 是连接“想法”与“实现”的**唯一、完整、准确**的说明书。 |
| **PRD的目的** | **确保方案完整、团队理解一致、方便未来回溯**。 |
| **PRD的内容** | 必须包含**背景目的、流程结构、交互说明**等六大核心模块。 |


---

## 6.2 产品交互需求说明详解

在我看来，这部分是PRD的“灵魂”。它详细定义了产品的行为和逻辑，是开发和测试工程师工作的直接依据。

我的目标是写出一份“让开发人员无需再问我任何问题”的说明。

### 6.2.1 学习目标

在本节中，我的目标是，让我们掌握撰写一份清晰、无歧义的交互需求说明的方法。

我们将学习说明的两个核心内容方向，并通过一系列真实案例，学会如何为我们原型中最高频出现的8种元件或场景，定义滴水不漏的规则。

### 6.2.2 产品交互需求说明内容方向

![image-20250720133948254](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133948254.png)

我撰写的所有交互说明，都围绕着两大方向展开，这能确保我不会遗漏关键信息：

1.  **交互动作说明 (Interaction Actions)**
    这部分是描述“因果关系”，我习惯用“**When(当…)/If(如果…)/Then(那么…)**”的逻辑来思考：
    * **行为 (When)**：当用户做了什么操作时？（如：单击、滑动）
    * **条件 (If)**：这个操作在什么条件下会触发？（如：用户已登录、输入框有内容）
    * **反馈 (Then)**：系统应该给出什么样的回应？（如：跳转页面、弹出提示）

2.  **字段信息说明 (Field Information)**
    这部分是描述“静态规则”，主要包含两点：
    * **显示**：这个区域默认应该显示什么文案或内容？（如：输入框的提示文字）
    * **规则**：这个字段或内容需要遵循什么规则？（如：最多输入10个字）

### 6.2.3 常见交互功能及需求说明示例

现在，我们就用“交互动作”和“字段信息”这两把“手术刀”，来解剖几个最常见的交互功能。

#### 1. 功能按钮说明

![image-20250720134028596](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134028596.png)

对于一个按钮，我绝不会只说“点击按钮如何”，而是会定义它在不同条件下的状态和反馈。
* **案例：“发表”按钮**
    1.  **条件1**：当输入框内容为空时，按钮置灰，为不可点击状态。
    2.  **条件2**：当输入框有内容时，按钮高亮，为可点击状态。
    3.  **行为**：点击该按钮，**反馈**为：提示“发表成功”1.5秒后自动消失，并跳转到朋友圈首页。

#### 2. 选项选择说明

![image-20250720134051854](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134051854.png)

对于下拉选择、单选/复选框等，我需要定义选项的规则。
* **案例：朋友圈日期选择**
    1.  **选项来源**：选项中的年份和月份，来源于该用户所有发过朋友圈的年份和月份。
    2.  **选项规则**：仅支持单选。
    3.  **默认显示**：默认提示文案为最近发表朋友圈的年限。
    4.  **异常情况**：若用户从未发表过朋友圈，“2022年”选项隐藏。

#### 3. 输入框说明

![image-20250720134215852](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134215852.png)

对于输入框，我需要明确其显示和限制规则。
* **案例：设置名字输入框**
    1.  **默认显示**：提示文案为“请输入20个字以内的名字”。
    2.  **输入规则**：不限制字符类型，但限制数量为1-20个字符（定义：1个中文算2个字符，1个字母/数字算1个字符）。
    3.  **异常处理**：如输入超过20个字符，则无法继续输入。

#### 4. 时间/日期选择说明

![image-20250720134303279](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134303279.png)

对于时间选择器，我需要定义其范围和关联规则。
* **案例：自定义日期选择框**
    1.  **默认显示**：“本年本月01日”到“本年本月当日”。
    2.  **可选范围**：日期选择精确到年月日，不得选择超过当天的日期。
    3.  **关联规则**：开始日期不得超过结束日期；开始和结束日期跨度最长为半年。

#### 5. 内容显示说明

![image-20250720134346210](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134346210.png)

对于列表中的内容，我需要定义其截取和显示规则。
* **案例：快报内容显示**
    1.  **显示内容**：包含时间（时分）和内容简讯。
    2.  **截断规则**：内容简讯取自正文开头部分内容，在列表中最多显示三行，超出三行的部分，末尾显示“...”。

#### 6. 状态类说明

![image-20250720134417496](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134417496.png)

对于有“状态”概念的业务（如订单、任务），我必须定义清楚状态的流转。
* **案例：订单状态**
    1.  **状态定义**：一个订单包含“待付款、待发货、待收货、交易成功”等状态。
    2.  **状态变更逻辑**：
        * 用户付款后，状态由“待付款”变为“待发货”。
        * 用户点击确认收货，或超过最长收货时间（如7天）后，状态自动变为“交易成功”。
    3.  **状态影响**：只有在“交易成功”状态下，用户才能删除订单或申请售后。

#### 7. 数字显示类说明

![image-20250720134444217](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134444217.png)

对于需要计数的数字，我需要定义计算和显示规则。
* **案例：阅读/点赞数显示**
    1.  **计算规则**：用户每产生一次阅读/点赞/评论/分享行为，对应数据实时+1。
    2.  **显示规则**：
        * 当数据为0时，只显示icon，不显示数字。
        * 当数据在1000及以内，显示实际数字。
        * 当数据在1000以上，以千为单位显示，如1k+、10k+。

#### 8. 时间显示类说明

![image-20250720134513602](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134513602.png)

对于时间戳的显示，为了提升用户体验，我需要定义其相对显示规则。
* **案例：信息发布时间显示**
    1.  **显示规则**：
        * 1小时内，以分钟为单位显示，如“xx分钟前”。
        * 当天内，以小时为单位显示，如“xx小时前”。
        * 昨天/前天内，显示“昨天/前天+具体时分”，如“昨天 15:05”。
        * 超过3天，则显示为具体年月日+时分，如“09-09 15:05”。

### 6.2.4 产品交互需求说明小结

撰写交互说明，是一项极其考验产品经理**严谨性**和**同理心**的工作。

| **核心原则** | **我的实践清单** |
| :--- | :--- |
| **杜绝歧义** | 我会穷尽每一个元件、每一个状态、每一个用户操作、每一个异常情况，并为它们都写下清晰的规则。 |
| **结构清晰** | 我总是围绕 **交互动作 (When/If/Then)** 和 **字段信息 (Display/Rules)** 这两大方向来组织我的说明。 |
| **换位思考** | 我会站在开发者的角度思考：我的说明是否清晰到他不需要再来问我任何问题就能直接开始写代码？ |






---

## 6.3 如何撰写产品交互需求说明

我们已经知道了交互说明要包含哪些内容，但从“知道”到“做到”之间，还需要一套行之有效的方法论。在这一节，我将毫无保留地，把我自己撰写交互说明的思路、步骤和技巧分享给大家。

### 6.3.1 学习目标

我的目标是，让我们掌握一套可以被反复使用的、结构化的PRD撰写流程。学完本节，我希望我们都能自信地、有条不紊地，为任何一个复杂的页面，撰写出清晰、完整的交互说明。

### 6.3.2 产品交互需求说明撰写思路

![image-20250720134943468](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134943468.png)

我的核心撰写思路，是**“由外到内，由静到动”**。

1.  **先考虑页面**：在描述任何一个按钮之前，我总是先从整个“页面”的视角出发。这个页面从哪里来？它整体需要遵循什么规则（比如列表的排序规则、分页逻辑等）？
2.  **再考虑控件**：把页面的整体规则定义清楚后，我再“钻”进去，分析页面里的每一个“控件”（也就是元件）。对于每个控件，我同样遵循一个顺序：
    * **静态**：先说明它的静态规则，比如默认的提示文案、输入框的字数限制等。
    * **动态**：再说明它的动态交互，比如正常的交互动作，以及各种异常情况下的交互动作。

### 6.3.3 产品交互需求说明撰写步骤

![image-20250720135827503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720135827503.png)

这个思路，具体落地就变成了我写PRD时雷打不动的三个步骤：

1.  **页面说明**：在文档的开头，我会先对当前页面进行一个整体的介绍，包括页面的主要功能、从哪些页面可以进入此页面、以及该页面的一些全局性规则。
2.  **区域划分**：为了让说明更有条理，我会把一个复杂的页面，划分成几个逻辑区域，比如“顶部导航区”、“内容列表区”、“底部菜单区”。我通常会在原型图上用数字角标，清晰地标注出这些区域。
3.  **详细说明**：这是工作量最大的部分。我会按照区域划分的顺序，逐一地、详细地说明每个区域内，每一个控件的“静态规则”和“动态交互”。

![image-20250720140210704](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720140210704.png)
这种“**在原型图上分区编号，再在文档中分点说明**”的方式，是我认为最清晰、最高效的撰写范式，开发和设计同事都非常喜欢，因为它简单明了，对应关系一目了然。

### 6.3.4 产品交互需求说明撰写案例及练习

![image-20250720140554438](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720140554438.png)





我们来看“注册登录页面”的这份详细说明。它完美地应用了我们刚才讲的三个步骤：

![image-20250720140647069](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720140647069.png)

* 它首先有**页面说明**。
* 然后将页面划分为“**1. 顶部导航栏**”和“**2. 登录区域**”两大块。
* 接着，在“登录区域”内，又详细地拆分说明了“**2.1 手机号输入框**”、“**2.2 验证码输入框**”等每一个控件的详细规则，内容细致到了“光标默认置于输入框”、“键盘用哪种样式”等程度。

这种对细节的极致追求，就是一份专业PRD的体现。这也是我们接下来需要练习达到的标准。

### 6.3.5 产品交互需求全局说明

![image-20250720141030178](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720141030178.png)

这时，一个问题来了：**有一些规则，比如“网络异常时如何提示”，是每个页面都会出现的，难道我需要在每个页面都重复写一遍吗？**

答案是不需要。为了解决这个问题，我会在PRD的开头，建立一个**“全局说明”**的章节。

在这个章节里，我会把所有非某个页面独有的、全产品通用的规则，进行统一的说明。这通常包括：
* **角色/权限说明**：不同角色的用户，在使用功能上有什么权限差异。

* **加载方式**：页面加载、数据加载时的默认动画样式。

* **全局弹层**：产品中统一的弹窗、提示（Toast）的样式和规则。
* **常用字段**：常用字段的统一规则，如昵称、密码的格式要求。
* **网络异常**：在各种网络问题下，产品应该如何向用户反馈。
* **全局交互**：通用的手势操作等。

建立“全局说明”，能极大地减少我的重复工作量，并保证产品体验的一致性。

### 6.3.6 产品交互需求说明撰写注意事项与技巧

![image-20250720143359987](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720143359987.png)

最后，分享四个我多年总结下来的技巧和注意事项：

1.  **先明确产品逻辑**：在写文档前，一定先把产品的流程、结构都想清楚。PRD是思考的结果，而不是思考的过程。
2.  **条件和反馈**：对每一个交互动作，都要像侦探一样，把所有的“条件”和“反馈”都考虑到，特别是异常情况。
3.  **不要边画原型边写说明**：我建议把这两个工作分开。先专注地把低保真原型画完，再进入“贤者时间”，专注地为这个静态的原型撰写说明。一心二用，两边都做不好。
4.  **灵活运用表达方式**：除了大段的文字，我也会大量使用**表格**（比如用来表达状态机）、**流程图**等方式，来更清晰、更简洁地表达复杂的逻辑。

---



# 第七章：用户端设计

在这一章，我们将进入一个完整的实战设计流程。我们将从用户第一次打开App的瞬间开始，一步步地设计出内容产品用户端的每一个核心模块，将我们之前学到的所有理论知识，全部应用到实践中。

## 7.1 引导页 & 启动页 & 闪屏页

当一个新用户满怀期待地下载并首次打开我们的App时，我们有且仅有一次机会，来给他留下一个完美的“第一印象”。这个第一印象，通常是由三个不同的页面共同构成的。我必须清晰地辨别它们，并为它们设计好各自的使命。

### 7.1.1 学习目标

在本节中，我的目标是，带大家清晰地区分**引导页、启动页、闪屏页**这三个极易混淆的概念。我将拆解它们各自的定义和核心作用，确保我们在产品设计中，能为用户的“第一次”，做出最合理的安排。

### 7.1.2 引导页设计

![image-20250720211813703](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720211813703.png)

#### 1. 引导页概念

**引导页（Onboarding Screens）**，是我为**首次**安装并打开我们App的用户，专门准备的一套“欢迎手册”。它通常是由3-5张可滑动的页面构成。

#### 2. 引导页作用

我设计引导页，通常是为了达成以下三个目的：
* **产品功能介绍**：用最简洁的图文，向用户展示我们App最核心、最吸引人的1-3个功能。
* **产品亮点说明**：传达我们产品的核心价值主张，告诉用户“我们是谁，我们能为你带来什么独特的好处”。
* **推广品宣**：通过精美的设计和文案，在用户接触产品的最初几秒钟，就建立起我们产品的品牌调性和情感链接。

### 7.1.3 启动页设计

![image-20250720211951423](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720211951423.png)

#### 1. 启动页概念

**启动页（Launch Screen）**，是用户**每一次**打开App时，都会看到的第一个页面。它是一个短暂展示的静态页面，主要目的是在App后台加载资源时，给用户一个优雅的等待界面，避免出现白屏或黑屏。

#### 2. 启动页作用

启动页的作用非常纯粹，主要是品牌和信息的展示：
* **品牌宣传**：在页面中心，清晰地展示我们产品的Logo和Slogan（品牌口号），在每一次启动中，反复加深用户的品牌认知。
* **版权声明与版本号**：在页面底部，通常会标注公司的版权信息和当前App的版本号。

### 7.1.4 闪屏页设计

#### 1. 闪屏页概念

**闪屏页（Splash Screen / Splash Ad）**，是一个可选的、通常在**启动页之后，首页之前**出现的页面。它本质上是一个全屏的、通常带有“跳过”按钮的广告或运营活动页面，一般会展示3-5秒。

#### 2. 闪屏页作用

闪屏页的作用完全是商业化和运营导向的：
* **广告曝光**：“开屏广告”是App中非常重要的一个广告位，能为我们带来商业收入。
* **活动推广**：我可以用它来推广平台级的、重要的运营活动，为活动进行预热和导流。
* **内容推荐**：对于内容产品，我也可以用它来推荐平台S级的重磅内容，吸引用户点击。

### 7.1.5 本节小结

这三个页面，共同构成了用户打开App的“三部曲”。我将它们的核心区别，总结在了下面的表格里，来帮助我们加深记忆。

| **页面类型** | **出现时机** | **核心目的** | **我的设计思考** |
| :--- | :--- | :--- | :--- |
| **引导页** | **仅在首次**安装启动时 | **教育用户**、介绍核心功能与价值 | 内容要少而精，突出核心亮点，让用户快速了解。 |
| **启动页** | **每一次**启动时（加载期间） | **品牌展示**、传递Logo与Slogan | 界面要极简、干净，加载速度一定要快。 |
| **闪屏页** | **每一次**启动时（加载后，首页前） | **商业运营**、广告曝光与活动推广 | 必须提供清晰的“跳过”按钮，不能强制用户观看。 |

我的设计哲学是，用户的最终目的是进入App使用核心功能。因此，这个“开门”的过程，必须尽可能地快速、流畅。任何不必要的停留，都可能造成用户的流失。


---

## 7.2 用户端设计思路

在动手画任何一个具体页面之前，我必须先建立起整个产品的“设计蓝图”。这个蓝图，就是我的用户端设计思路。它是一个从宏观到微观，从战略到执行的逻辑推演过程，能确保我后续所有的设计决策，都是有据可依、浑然一体的。

### 7.2.1 学习目标

在本节中，我的目标是带大家完整地走一遍这个“设计蓝图”的推演过程。我们将学习如何从一个**需求背景**出发，提炼出产品的核心**角色**，分析他们的**用户场景**，并最终推导出我们需要设计的**核心功能**。

### 7.2.2 需求背景分析

![image-20250720212444403](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212444403.png)

我们假设，通过前期的市场分析和需求收集，我们已经为我们即将开发的“图文类内容产品”，确定了V1.0版本的四大核心策略，这就是我们一切设计的出发点和“宪法”。

1.  **生产模式**：我们将采用 **PGC (专业生产内容) + UGC (用户生产内容)** 的双引擎模式，来保证内容的专业度和丰富度。
2.  **审核方式**：我们将采用 **自动审核 + 人工审核** 的方式，来平衡审核的效率和准确性。
3.  **分发方式**：我们将采用 **算法分发 + 订阅分发** 的方式，来兼顾用户发现新内容和关注老作者的需求。
4.  **消费模式**：在V1.0版本，我们将采用**免费消费**的模式，以最大化地吸引早期用户。

### 7.2.3 需求分析（角色提炼）

![image-20250720212511146](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212511146.png)

基于上述的背景，我首先要提炼出，在这个生态中，到底有哪几类“玩家”？

* 因为有UGC，所以必然有内容的**生产者**，我称之为“**自媒体**”。
* 因为有内容消费，所以必然有内容的**消费者**，我称之为“**普通用户**”。
* 因为有审核和分发，所以必然有**管理者和运营者**，我称之为“**平台**”。

在本章，我们聚焦的用户端设计，主要就是为了服务好“**普通用户**”和“**自媒体**”这两大核心外部角色。

### 7.2.4 需求分析（用户场景）

![image-20250720212618884](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212618884.png)

明确了我们要服务的角色之后，我开始思考：这两类用户，在使用我们产品的过程中，会经历哪些最核心、最典型的场景？我将它们归纳为四大场景：

1.  **获取身份**：无论是想看个性化推荐的“普通用户”，还是想发表内容的“自媒体”，他们都需要先在我们的平台上，拥有一个自己的身份。这就是**注册/登录**的场景。
2.  **发布内容**：“自媒体”角色的核心诉求，就是将自己的图文作品发布到平台上，与大家分享。这就是**内容发布**的场景。
3.  **浏览&互动内容**：“普通用户”的核心诉求，是发现、阅读自己感兴趣的文章，并对内容和作者表达自己的喜好。这就是**内容消费与互动**的场景。
4.  **个人中心**：所有用户，都需要一个地方来管理自己的个人信息、查看自己发布或收藏过的内容。这就是**个人中心管理**的场景。

### 7.2.5 用户端核心功能设计

![image-20250720212728816](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212728816.png)

最后一步，也是从“分析”到“设计”最关键的一步，就是将上述的用户场景，映射为我们产品需要提供的**核心功能模块**。

* 为了支撑“获取身份”场景，我们需要设计 **注册登录** 功能。
* 为了支撑“发布内容”场景，我们需要设计 **内容发布页**。
* 为了支撑“浏览&互动内容”场景，我们需要设计 **内容列表页** 和 **内容详情页**。
* 为了支撑“个人中心”场景，我们需要设计 **个人中心** 模块。

这五大核心功能，就构成了我们内容产品用户端的“骨架”。在接下来的小节中，我们将逐一地，对这个骨架进行“添肉画皮”，完成每一个模块的详细设计。

### 7.2.6 本节小结

我将这个设计思路的推演过程，总结为下面这张表格：

| **思考步骤** | **核心产出** | **我的目的** |
| :--- | :--- | :--- |
| **背景分析** | 四大产品决策 | 确立项目的“宪法”，是所有设计的最高准则。 |
| **角色提炼** | 三大核心角色 | 明确我们到底要为谁服务。 |
| **场景分析** | 四大核心场景 | 梳理出用户的完整旅程和核心诉求。 |
| **功能设计** | 五大核心功能 | 将用户诉求，转化为具体、可设计的产品模块。 |



---

## 7.3 注册登录

在我们的设计思路中，“获取身份”是所有用户要经历的第一个场景。**注册登录**功能，就是支撑这个场景、我们为用户开启的“第一扇门”。

在我看来，这扇门的设计至关重要。一个好的注册登录体验，应该像酒店的自动门一样，让用户安全、无感、顺畅地通过；

而一个糟糕的设计，则像一道生锈的铁门，会在用户进入前，就把他们拒之门外。

从本质上讲，我设计的所有注册登录流程，都是为了完成两件事：**身份识别**（你是谁？）和**门槛验证**（如何证明你是你？）。

### 7.3.1 学习目标

在本节中，我的目标是带大家掌握现代App中最主流的三种注册登录方式的设计。我们将深入分析它们的实现逻辑、优缺点，并探讨如何通过安全验证设计，来保障我们产品的“大门”既方便又安全。

### 7.3.2 注册登录的目的

![image-20250720213340787](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720213340787.png)

在设计之前，我总会先思考：我们为什么需要用户注册登录？

* **从用户的角度：为了“获得身份”**
    一个注册后的身份，意味着用户在我们的产品里，有了一个专属的“数字资产”账户。这能帮助他们：
    * **记录跟随**：可以保存自己的浏览历史、收藏、发布的文章等。
    * **获得个性化服务**：可以接收到我们为他量身定制的内容推荐。
    * **积累个人资产**：可以拥有自己的积分、等级、虚拟财产。

* **从平台的角度：为了“区分用户”**
    用户的注册，能帮助我们平台更好地运营：
    * **精细化运营**：可以针对不同用户群体，推送不同的内容或活动。
    * **信息记录**：可以更好地掌握平台的用户构成和自媒体信息。
    * **信息分发**：能够针对用户的身份和喜好，进行更精准的内容分发。

### 7.3.3 常见注册登录方式介绍

明确了目的，我们来看实现“门槛验证”的三种主流方式。

#### 1. 手机号+验证码

![image-20250720215110314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720215110314.png)

这是目前国内互联网产品最主流、最便捷的方式，它的核心逻辑是“**手机在手，身份我有**”。

* **核心逻辑**：将注册和登录合二为一。用户输入手机号，接收并回填验证码，系统验证通过后，若该手机号未注册，则自动为其创建账户并登录；若已注册，则直接登录。
* **优点**：**方便快捷**，用户无需记忆复杂的密码，操作路径最短。
* **缺点**：有一定**账户信息风险**（如手机号丢失），且平台需要承担较高的**短信成本**。

#### 2. 手机号+验证码+密码

![image-20250720215834551](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720215834551.png)

这是最传统，也是账户体系最稳固的一种方式。

* **核心逻辑**：注册和登录是分离的。用户首次使用时，需要通过“手机号+验证码”验证身份，并**设置一个密码**来完成注册。后续登录时，主要使用“手机号+密码”的方式。
* **优点**：**安全性更高**，登录不受运营商短信通道影响。
* **缺点**：注册流程更长，**操作成本相对较高**，可能会流失一部分没有耐心的用户。

#### 3. 第三方注册登录

![image-20250720220120919](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220120919.png)

这是借助“巨人”的力量，让用户快速进入我们产品的方式。

* **核心逻辑**：用户授权我们App，去获取他在某个第三方大平台（如微信、QQ、微博）上的基本公开信息（如昵称、头像）作为身份标识，从而完成注册或登录。
* **优点**：**门槛极低**，用户一键授权即可，体验非常流畅，能有效提升新用户的注册转化率。
* **缺点**：我们能**获取的用户信息非常有限**，不利于后续的精细化运营。同时，账户的安全性依赖于第三方平台。

### 7.3.4 注册登录安全验证设计

![image-20250720220200195](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220200195.png)

为了防止被机器人恶意、批量地注册或登录（俗称“刷接口”），也为了保护我们宝贵的短信费用，我必须在注册登录流程中，加入一道“**安全验证**”的屏障。

这道屏障，通常出现在用户输入完手机号、点击“获取验证码”按钮之后。常见的验证形式有：
* **智能验证**（如“我不是机器人”勾选框）
* **文字点选验证**（要求用户点选图中指定的文字）
* **拼图验证**（要求用户拖动滑块完成拼图）

此外，对于已注册用户，为了提供更便捷的登录体验，我还会设计支持**指纹、面容ID**等生物识别验证方式。

### 7.3.5 本节小结

在实际设计中，我很少只提供一种登录方式，而是采用组合策略。我将这三种方式的选择思路总结如下：

| **登录方式** | **核心逻辑** | **我的设计策略** |
| :--- | :--- | :--- |
| **手机号+验证码** | 便捷性优先 | 作为默认和首选的登录方式，最大化地降低用户操作成本。 |
| **手机号+验证码+密码** | 安全性优先 | 作为一种可选的账户升级或安全设置，让注重安全的用户可以绑定密码。 |
| **第三方登录** | 借力，信任度优先 | 作为一种重要的补充登录方式，并排放在主登录按钮下方，给用户多一种便捷选择。 |





---

## 7.4 内容发布

当我们的“自媒体”用户，也就是内容创作者，想要把他们的想法和作品分享出来时，他们就需要一个强大、易用的**内容发布**功能。这是连接“创作者”与“平台”的桥梁，这个桥梁的体验，直接决定了我们平台内容的数量和质量。

### 7.4.1 学习目标

在本节中，我的目标是带大家深入研究内容发布页的设计。我们将探讨图文内容常见的两种展现形式，并拆解这两种形式下，内容发布页各自的设计要点和核心元素。

### 7.4.2 图文内容的展现形式

![image-20250720220845581](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220845581.png)

在设计发布页之前，我首先要明确，我们平台的内容，最终将以什么样的形式呈现给用户。这通常决定了我们发布器的形态。最常见的两种形式是：

1.  **图文分离展现形式**：图片和文字是分开展示的。通常是上方为图片（或视频），下方为独立的、大段的文字描述。
2.  **图文混排展现形式**：图片可以自由地插入到文章的任意位置，形成我们常说的“富文本”效果。

### 7.4.3 内容发布页的设计

#### 1. 图文分离形式设计要点

![image-20250720221014583](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221014583.png)

这种形式的发布器，设计上更追求简洁、快速。我通常会关注以下几个设计要点：
* **核心元素**：必须包含**文本输入区**、**图片/视频上传入口**（通常是一个“+”号按钮），以及**发布/取消按钮**。
* **字符长度限制**：需要明确告知用户，正文最多可以输入多少字。
* **图片数量限制**：需要明确告知用户，最多可以上传多少张图片。
* **发布状态变化**：当用户未输入任何内容时，“发布”按钮应为置灰不可用状态，以避免发布空内容。
* **草稿箱功能**：当用户意外退出时，我需要设计一个草稿箱功能，自动保存用户未发布的内容，防止心血白费。

#### 2. 图文混排形式设计要点

![image-20250720221124889](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221124889.png)

这种形式的发布器，功能更强大，类似于一个移动端的“Word编辑器”。除了包含图文分离形式的所有要点外，我还会特别关注：
* **标题输入**：通常会提供一个独立的标题输入框，并有字数限制。
* **富文本编辑**：支持在正文的任意位置插入图片或视频，并提供基础的排版功能，如加粗、对齐等。
* **发布与取消**：这两个按钮必须始终清晰可见。
* **图片/视频上传**：提供清晰的上传入口和进度反馈。

---

## 7.5 内容列表及内容详情

当内容被成功发布后，它就需要被呈现给“普通用户”。这个呈现的过程，主要由两个核心页面来承载：**内容列表页**和**内容详情页**。

### 7.5.1 学习目标

在本节中，我的目标是带大家掌握内容列表页和详情页的设计精髓。我们将学习如何设计一个信息丰富、吸引眼球的列表页，一个沉浸、易读的详情页，以及如何通过巧妙的互动设计，来提升用户的参与感和社区的活跃度。

### 7.5.2 内容列表页设计

![image-20250720221204736](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221204736.png)

内容列表页，是我为用户打造的“内容超市”。它的核心使命，是让用户能在这个超市里，快速地、高效地发现自己可能感兴趣的“商品”（内容）。

#### 1. 列表页元素设计

一个标准的内容卡片，通常包含三类信息：
* **内容基本信息**：标题、封面图（如果有）、内容摘要。
* **发布者信息**：作者的头像、昵称。
* **互动信息**：点赞数、评论数、分享数等。

#### 2. 列表页设计要点

* **图文权重分配**：我需要根据产品定位，来决定图片和文字的权重。左侧的列表形式，更注重文字信息的传递；而右侧的瀑布流形式，则更强调图片的视觉冲击力。
* **内容排列规则**：列表的排序规则是什么？是按照发布时间倒序？还是按照热度排序？我必须定义清晰的规则。

### 7.5.3 内容详情页设计

当用户在列表页对某项内容产生兴趣并点击后，就进入了**内容详情页**。这是用户进行沉浸式阅读和深度消费的核心场所。

#### 1. 图文混排详情页设计要点

![image-20250720221259547](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221259547.png)

* **核心元素**：必须清晰地展示**导航栏、内容标题、作者信息、发布时间、正文（图文混排）**。
* **设计要点**：
    * **支持分段**：长文章必须分段，以提升可读性。
    * **图片可交互**：图片通常需要支持点击查看大图。
    * **视觉权重**：正文的视觉权重最高，其他辅助信息（如作者、时间）则相对弱化。
    * **敏感字过滤**：需要对评论区等UGC内容进行敏感词过滤。

#### 2. 图文分离详情页设计要点

![image-20250720221401530](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221401530.png)

* **核心元素**：与混排类似，但**图片区**和**正文区**是明确分开的。
* **设计要点**：与混排页的设计要点基本一致，同样需要关注分段、图片交互、视觉权重和敏感字过滤。

### 7.5.4 内容互动设计

![image-20250720221435264](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221435264.png)

#### 1. 互动行为的重要性

在我看来，互动是内容产品的“灵魂”。它不仅仅是一些按钮，而是连接**用户、作者、内容、平台**四方的桥梁。
* 对于**消费者**，互动是表达情绪的方式。
* 对于**生产者**，互动是对自己创作的激励。
* 对于**内容**，互动是区分其质量和热度的标尺。
* 对于**平台**，互动是口碑营销和用户监督的手段。

#### 2. 常见互动行为设计要点

![image-20250720221636401](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221636401.png)



* **核心互动**：**点赞**和**分享**，是用户成本最低、我们最希望用户去做的行为。因此，这两个功能的按钮，在页面上必须非常突出和便捷。
* **主要分享渠道**：分享功能，我通常会优先支持微信、朋友圈、QQ和微博这几个主流渠道。
* **次要互动**：对于一些不常用的功能，比如**删除**（作者可见）、**举报**（用户可见），我通常会将它们收纳在右上角的“更多”按钮中，避免干扰主界面的信息。

### 7.5.5 本节小结

| **页面/模块** | **我的核心设计思考** |
| :--- | :--- |
| **内容发布页** | 根据**图文分离/混排**的展现形式，来决定发布器的复杂度和设计要点。 |
| **内容列表页** | 核心是**信息卡片**的设计，需要平衡好图文权重和信息密度。 |
| **内容详情页** | 核心是提供**沉浸、易读**的消费体验，并引导用户进行互动。 |
| **内容互动设计** | **突出核心互动（点赞/分享）**，将次要互动收纳起来，保持界面简洁。 |



---

## 7.6 内容分发

我们的内容，已经通过发布功能进入了平台的“内容库”，详情页也为用户提供了沉浸式的消费体验。但现在，一个核心问题摆在面前：**在海量的内容库里，我们应该把哪些内容，在什么时候，以什么方式，呈现在用户面前？**

这就是内容分发系统需要解决的问题。它是连接“海量内容”与“个性化用户”的桥梁。

### 7.6.1 学习目标

在本节中，我的目标是带大家深入了解内容产品背后最主流的三种分发模式的设计。我们将重点拆解**算法分发**的核心三要素，学习**用户画像**和**标签**的概念，并了解**热度排序**和**订阅分发**的设计逻辑。

### 7.6.2 算法分发设计

![image-20250721084124586](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084124586.png)

#### 1. 算法分发定义与要素

**算法分发**，是我认为的现代内容产品的“发动机”。我把它定义为：**一套能根据用户数据，自动地、个性化地为用户推荐其可能感兴趣的内容的系统**。

要让这台发动机运转起来，我必须为它准备好三个核心要素：
* **用户画像 (User Persona)**：深入地理解我的用户，知道“他是谁，他喜欢什么”。
* **内容画像 (Content Profile)**：深入地理解我的内容，知道“它是什么，它讲了什么”。
* **算法模型 (Algorithm Model)**：建立一套高效的匹配和排序规则，将最合适的内容，推荐给最合适的用户。

#### 2. 用户画像介绍

算法分发的前提，是了解用户的喜好。**用户画像**，就是我用来“了解”用户的工具。

我把它定义为：**根据用户各维度的真实数据，抽象出的一个标签化的用户模型**。
我通常会从以下四个维度，来为用户构建画像：
* **基本属性**：如姓名、性别、年龄、地域等。
* **社会属性**：如职业、收入、公司、文化等。
* **行为属性**：这是最重要的，包括用户在我们产品里的登录、活跃、评论、点赞等一切行为。
* **消费属性**：如果产品有付费点，还包括用户的消费金额、次数等。

#### 3. 标签分类及应用

![image-20250721084235993](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084235993.png)

用户画像和内容画像，都是通过“**标签**”来具体实现的。标签，就是对目标的量化标识和描述。我们的核心工作，就是**为内容和用户，打上同一套标签体系**，从而实现精准匹配。

我把用户标签，分为两大类：
* **静态标签**：指那些在较长时间内，保持稳定不变的标签，通常具有“先天性”。比如用户的**性别、年龄、星座、地域**等。

![image-20250721084734425](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084734425.png)



* **动态标签**：指根据用户的**实时操作行为**，动态变化的标签。比如用户刚刚搜索了什么、点赞了什么、购买了什么。这些动态标签，更能反映用户当下的、即时的兴趣。一个完整的用户画像，是静态标签和动态标签的结合体。

![image-20250721084743710](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084743710.png)

#### 4. 算法分发设计逻辑

![image-20250721084704681](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084704681.png)

有了用户画像和内容画像，我的算法推荐逻辑通常遵循四步走：
1.  **权重设置**：我会为用户的不同行为，赋予不同的权重。比如，“分享”行为的权重，一定高于“点赞”。
2.  **贴标签**：系统自动为内容和用户打上标签。
3.  **匹配推荐**：算法模型开始工作，为用户匹配出，与他自身标签相符的内容。
4.  **排序**：对所有匹配出的内容，根据一个“热度/质量分”公式，进行排序，决定最终呈现给用户的顺序。

我们来看一个具体的**算法分发规则案例**：

![image-20250721084719114](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084719114.png)

* **计算维度与分值**：我们可以定义一个内容的总分值公式，比如：`内容总分 = 分享数*2 + 评论数*2 + 点赞数*1 + 收藏数*1`。
* **推送规则**：
    1.  优先匹配该用户标签权重最高的TOP5的标签内容。
    2.  根据内容总分值排序，分页推送，每页8条。
    3.  下拉刷新时，推送新产生的内容。
    4.  已经推荐过的内容，不再重复推荐。




---

### 7.6.3 热度排序设计

前面我们谈的算法分发，完全是根据“人-内容”的个性化匹配进行推荐的。但这里面可能会存在一个问题：如果推荐出的内容本身质量不高怎么办？

为了过滤掉低质量内容，并让用户感知到“大家都在看什么”，我需要引入一种全局性的排序机制——**热度排序**。

#### 1. 热度排序逻辑与设计要点

![image-20250721085440264](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085440264.png)

我设计热度排序的逻辑，通常遵循四步走：
1.  **梳理维度**：首先，我会梳理出所有能够反映内容质量和受欢迎程度的用户行为维度，比如**分享、点赞、评论、收藏**等。
2.  **权重设置**：其次，我会基于这些维度，为不同的行为设置不同的权重。比如，我认为“分享”和“评论”比“点赞”更能代表用户的认可度，因此会给予更高的分值。
3.  **标准计算**：然后，系统会根据用户产生的实时行为数据，套入我们设定的计分公式，为每一篇内容动态地计算出一个“热度分值”。
4.  **排序**：最后，系统根据计算出的“热度分值”进行排序。这里需要特别注意，为了**避免热度榜被少数爆款内容长期霸占（固化）**，我通常会在公式中加入“时间衰减”因子，让新发布的内容有更多的曝光机会。

#### 2. 热度排序规则示例

![image-20250721085555753](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085555753.png)

我们来看一个更具体、更复杂的规则案例，它巧妙地融合了**个性化推荐**和**热度排序**。

* **内容总分值公式**：`S = 分享数(A)*2 + 点赞数(B)*1 + 评论数(C)*2 + 收藏数(D)*1`
* **推送规则**：
    * **规则1**：筛选出**24小时内**发布的所有内容。
    * **规则2**：筛选出**24小时至72小时前**发布的内容中，热度分值**S>=30**的全部内容。
* **排序与分发逻辑**：
    1.  在信息流中，优先推送满足“规则1”的内容（确保新鲜度），按发布时间由近到远排列；当“规则1”的内容不足时，再推送满足“规则2”的内容（补充高质量老内容），按热度分值由高到低排列。
    2.  每次下拉刷新时，推送新产生的内容，每次最多推送8条。

### 7.6.4 订阅分发设计

#### 1. 订阅分发的核心逻辑

![image-20250721085717235](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085717235.png)

**订阅分发**，是将内容的选择权，完全交还给用户的一种方式。它的逻辑非常简单：“**我只看我关注的人发布的内容**”。这是一种基于“人”的、强关系的分发模式。

它的核心业务流程是：自媒体发布内容 → 用户在看到后选择关注该自媒体 → 系统此后会自动将该自媒体的新内容，分发到该用户的“关注”信息流中 → 用户随时可以查看。

#### 2. 订阅分发实现的关键功能

![image-20250721085801010](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085801010.png)

要实现这个逻辑，我的设计通常会围绕以下三个要点展开：

* **连接 (Connection)**：我必须为用户和作者建立“连接”的桥梁。这通常是在作者的个人主页、内容详情页等位置，提供一个清晰的“**关注**”功能按钮。
* **推荐内容 (Content Delivery)**：我需要为用户提供一个专门的消费场所，也就是一个独立的“**关注**”信息流（Feed）。这个信息流里，只包含用户已关注作者发布的内容。
* **排序 (Ranking)**：这个信息流的排序规则通常非常简单，就是严格按照**内容发布的时间倒序排列**，确保用户看到的永远是最新的内容。

### 7.6.5 本节小结

我将这三种核心的分发方式总结如下，在我的产品设计中，我通常会将它们组合使用，来满足用户不同的内容发现需求。

| **分发方式** | **核心逻辑** | **我的设计要点** |
| :--- | :--- | :--- |
| **算法分发** | **人-内容匹配** | 定义清晰的用户画像、内容标签、推荐与排序规则<br>`内容总分 = 分享数*2 + 评论数*2 + 点赞数*1 + 收藏数*1`。 |
| **热度排序** | **内容热度值计算** | 定义合理的热度计算公式，并考虑时间衰减，避免榜单固化。<br>S = 分享数(A)*2 + 点赞数(B)*1 + 评论数(C)*2 + 收藏数(D)*1 |
| **订阅分发** | **用户主动关注** | 设计好关注/取关功能，并提供独立的“关注”信息流。 |





---

## 7.7 个人中心

当用户在我们的产品里消费、互动、创作，留下了一系列数字足迹之后，他们需要一个“家”，来安放和管理这些属于自己的信息和资产。这个“家”，就是**个人中心**。

对我来说，个人中心是提升用户归属感和粘性的关键模块，它承载了用户的个人身份，也聚合了产品中与“个人”相关的各种高阶功能。

### 7.7.1 学习目标

在本节，我的目标是带大家掌握个人中心模块的完整设计。我们将学习如何设计用户的“名片”——**个人资料页**，并重点拆解个人中心页在**登录与未登录**两种状态下的差异化设计，以及其中常见的功能模块应该如何组织。

### 7.7.2 个人资料页设计

![image-20250721101941163](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721101941163.png)

个人资料页，是用户在我们的产品里，对外展示自己形象的“个人名片”。它的核心是允许用户自定义和编辑自己的个人信息。

#### 1. 常见个人信息展示

我设计个人资料页时，会根据产品定位，来决定需要提供哪些信息字段。对于一个内容产品，最常见的核心字段包括：
* **头像**：用户最具识别性的标识，支持用户从相册上传或拍照。
* **昵称**：用户在社区中行走江湖的“代号”。
* **简介**：一段个性化的自我介绍。
* **性别**
* **生日**
* **地区**

在设计注册流程时，我有一个重要原则：**渐进式信息收集**。即，在最初的注册环节，我只要求用户提供最核心的信息（比如仅需要手机号验证），而将这些详细的个人资料，引导用户在后续有空时，再来个人中心慢慢完善。这能最大化地降低新用户的注册门槛。

### 7.7.3 个人中心常见功能

个人中心这个页面，它的设计比较特殊，因为我必须同时考虑“游客”和“主人”两种完全不同的状态。

#### 1. 登录与未登录状态区别

* **未登录状态**
    当用户未登录时，个人中心这个页面的核心设计目标只有一个：**引导用户去登录或注册**。
    正如案例图所示，此时的页面，我会隐藏掉所有个性化的信息和数据，用一个通用的图标和提示文案（如“点击登录”），来占据视觉中心。大部分功能入口（如“我的收藏”、“历史记录”）也会被隐藏或置灰，用户点击后，会直接跳转到登录页面。

* **登录状态**
    当用户登录后，页面则会完全“变身”为他专属的个人空间。此时的设计核心，是**清晰的个人信息展示**和**便捷的功能入口聚合**。页面的顶部，会展示用户的头像、昵称和核心数据（如作品数、关注数、粉丝数），下方则会罗列出所有与他相关的功能。

#### 2. 常见功能模块介绍

对于登录后的用户，我会把个人中心的功能入口，按照相关性进行逻辑分组，让用户能快速找到自己想要的功能。

* **核心资产类**：这是用户最关心的，他们在我们平台沉淀下的“数字资产”。通常包括：
    * **我的收藏**
    * **浏览历史**
    * **我的作品**（针对创作者）

* **消息与互动类**：
    * **消息通知**（包括系统通知、评论、点赞等）

* **账户与安全类**：
    * **实名认证**
    * 账号与安全设置

* **App通用类**：
    * **用户反馈**
    * **系统设置**（里面通常还包含“关于我们”、“退出登录”等）

### 7.7.4 本节小结

| **模块** | **我的核心设计思考** |
| :--- | :--- |
| **个人资料页** | 提供**头像、昵称、简介**等基础字段的编辑功能，遵循**渐进式**信息收集原则。 |
| **个人中心（未登录）**| 设计核心是**引导登录/注册**，隐藏个性化信息，简化功能入口。 |
| **个人中心（已登录）**| 设计核心是**个人信息展示**和**功能入口聚合**，将功能按逻辑分组（如资产类、账户类、通用类）。 |

---

## 7.8 本章总结

到这里，我们已经完整地设计出了一个内容产品用户端的所有核心模块。让我们最后回顾一下本章的整个设计旅程：

* **开门三板斧**：我们首先设计了`引导页`、`启动页`和`闪屏页`，为用户打造了完美的“第一印象”。
* **确立设计思路**：我们通过`背景分析`→`角色提炼`→`用户场景`→`核心功能`的推演，确立了整个产品的设计“骨架”。
* **设计核心模块**：我们逐一设计了`注册登录`、`内容发布`、`内容列表与详情`、`内容分发`和`个人中心`这几个核心功能模块，为骨架“添上了血肉”。

通过这一章的实战，我们已经将之前学到的所有理论，都转化为了具体、可视的产品设计方案。




---

# 第八章：内容产品自媒体端设计

在第七章，我们为“普通用户”设计了一套完整、流畅的消费体验。但是，一个内容平台的繁荣，离不开持续产出优质内容的创作者，也就是“**自媒体**”。

在这一章，我们将为这些创作者，设计一套专属的“创作工坊”——**自媒体端**。这是一个能让他们方便地发布内容、管理作品、与粉丝互动、并洞察数据，最终帮助他们在我们平台获得成功的后台系统。

## 8.1 自媒体端设计思路

![image-20250721103341813](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103341813.png)

在动手设计之前，我们同样需要先建立一套清晰的设计思路。我们的出发点，依然是产品的顶层战略。我们V1.0的“**生产模式**”是PGC+UGC，这就决定了我们必须服务好“**自媒体**”这个核心角色。

### 8.1.1 学习目标

在本节中，我的目标是带大家一起，完成自媒体端设计的顶层战略思考。我们将从创作者最原始的需求出发，一步步深挖，提炼出我们自媒体端产品的核心价值，并最终推导出我们后台需要设计的核心功能模块。

### 8.1.2 自媒体端的核心价值

![image-20250721103428666](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103428666.png)

我们首先来分析创作者的核心需求。
* **最原始的需求**：作为内容的提供者，创作者最基本、最直接的需求，就是**需要有一个渠道，能够编辑、发布和管理自己的内容**。

但是，我经常会反问自己和团队一个问题：**只提供一个发布和管理工具，就足够了吗？**

![image-20250721103507471](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103507471.png)

一个创作者，他不仅仅是一个“发布者”，他更像是在我们平台上经营着自己“小事业”的创业者。当他的内容被用户消费、订阅、评论后，他的内心一定会产生更深层次的渴望：

* **来自“用户订阅”的渴望**：“有多少人关注了我？他们都是谁？” → 这背后是对**粉丝增长**和**社群归属**的渴望。
* **来自“用户浏览”的渴望**：“我的内容受欢迎吗？哪篇文章的数据最好？” → 这背后是对**内容表现**和**创作反馈**的渴望。
* **来自“用户评论”的渴望**：“我的读者们都在讨论什么？我如何与他们互动？” → 这背后是对**粉丝互动**和**舆论管理**的渴望。

因此，我为我们的自媒体端，定义了它的核心价值：它绝不仅仅是一个“**内容发布工具**”，而是一个“**创作者成功平台**”。我们的使命，是为创作者提供一整套服务，不仅帮助他们创作，更要帮助他们**洞察数据、连接粉丝、获得成长**。

### 8.1.3 设计原则与要点

![image-20250721103535155](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103535155.png)

基于上述的核心价值，我确立了自媒体端的设计原则，并推导出了它必须具备的三大核心功能模块。

* **设计原则**：
    1.  **赋能创作**：提供高效、易用的内容发布与管理工具。
    2.  **数据驱动**：提供清晰、直观的数据反馈，帮助创作者优化内容。
    3.  **连接粉丝**：提供便捷的粉丝互动与管理工具，帮助创作者建立社群。

* **核心功能模块**：
    1.  **入驻登录**：这是创作者进入我们平台的“大门”。
    2.  **内容管理**：这是创作者的“创作车间”，负责内容的生产、编辑和数据监控。
    3.  **粉丝管理**：这是创作者的“社群CRM”，负责粉丝的互动和分析。

这三大模块，就构成了我们自媒体端产品的“骨架”。在接下来的小节中，我们将逐一进行详细的设计。

### 8.1.4 本节小结

| **思考层次** | **核心洞察** | **最终产出** |
| :--- | :--- | :--- |
| **基础需求** | 创作者需要发布和管理内容。 | **核心功能**：内容管理 |
| **深层需求** | 创作者需要获得数据反馈、与粉丝互动、并实现个人成长。 | **核心功能**：粉丝管理、数据分析 |
| **核心价值** | 我们要做的不是一个“工具”，而是一个“**创作者成功平台**”。 | **设计原则**：赋能创作、数据驱动、连接粉丝 |




---

## 8.2 入驻与登录

这是我们为创作者开启的“梦想之门”。与普通用户追求“快速无感”的登录不同，创作者的入驻，更像是一次“签约合作”。因此，我设计的流程，不仅要考虑便捷，更要体现出**专业性、仪式感和契约精神**。

### 8.2.1 学习目标

在本节中，我的目标是带大家设计一个完整、专业的创作者入驻流程。我们将学习如何区分普通用户注册与创作者入驻，并设计出包含资质审核、协议签署等关键环节的 onboarding(新用户引导流程) 流程。

### 8.2.2 创作者入驻流程设计

![入驻流程图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E5%85%A5%E9%A9%BB%E6%B5%81%E7%A8%8B%E5%9B%BE.png)

图中的流程（`登录 → 注册 → 实名认证`）是普通用户升级为创作者的一个高度简化版。在我的实际设计中，我会将这个流程细化为一套更完整的“**入驻（Onboarding）**”流程，它通常发生在用户已经拥有一个普通账户之后。

我设计的专业入驻流程如下：
1.  **基础账户注册/登录**：用户首先需要有一个我们平台的普通账户（通过手机号+验证码等方式）。
2.  **发起入驻申请**：在App的某个位置（比如个人中心），我会放置一个醒目的“**成为创作者**”或“**创作者中心**”的入口。用户点击后，才正式开始入驻流程。
3.  **提交资质信息**：这是最关键的一步，是平台对创作者质量进行初步筛选的环节。我会要求用户提交：
    * **实名认证**：这是政策要求，也是建立信任的基础。
    * **创作者基本信息**：设置公开的创作者昵称、头像。
    * **创作领域选择**：让创作者选择自己擅长的内容领域（如科技、美食、旅行），这便于我们后续的内容分发。
    * **（可选）辅助材料**：对于要求较高的平台，我还会增加“提交其他平台代表作链接”的步骤，用于评估创作者的实力。
4.  **平台协议签署**：用户必须阅读并同意我们的《内容创作协议》和《平台规范》等。
5.  **等待平台审核**：提交申请后，用户的状态会变为“审核中”，我们会通过人工或AI，对其资质进行审核。
6.  **入驻成功**：审核通过后，用户才正式获得“创作者”身份，可以开始使用自媒体端的各项功能。

### 8.2.3 登录与身份切换

当一个用户同时拥有“普通用户”和“创作者”双重身份后，他的登录体验依然是统一的（使用同一个手机号或微信登录）。

我需要设计的是登录后的**身份切换机制**。通常，我会在“个人中心”页面，提供一个清晰的入口，比如“**创作者中心**”，已获得创作者身份的用户，点击后即可进入我们接下来要设计的自媒体后台。

### 8.2.4 本节小结

| **设计环节** | **我的核心设计思考** |
| :--- | :--- |
| **创作者入驻** | 不能等同于普通注册。它是一个包含**资质审核**和**协议签署**的、更正式的**Onboarding**流程，目的是筛选高质量创作者，并建立契约关系。 |
| **登录与切换** | 登录使用统一账户，但在产品内，必须为创作者提供一个**清晰、便捷**的入口，以切换到自媒体后台。 |

---

## 8.3 内容管理

这是自媒体端后台的“心脏”，是创作者最核心的“创作车间”。我设计的首要目标，是让它功能强大，同时体验简洁、高效。

### 8.3.1 学习目标

在本节中，我的目标是带大家设计一个功能完善的内容管理模块。我们将学习如何设计内容的数据概览和列表页，以及如何设计一个体验良好的内容发布与编辑器。

### 8.3.2 内容列表 & 数据概览



当创作者进入后台，他们第一眼最想看到的，一定是自己作品的表现。因此，内容管理模块的首页，我通常会设计成一个集**数据概览**和**内容列表**于一体的Dashboard。

#### 1. 核心数据指标

![image-20250721110551564](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721110551564.png)

在页面的最上方，我会放置一个“**数据概览**”模块，实时展示创作者最关心的核心数据指标，并支持按时间筛选（今日/本周/本月等）。这些指标通常包括：
* **浏览数**：内容被用户看到的次数。
* **评论数**：用户对内容的互动情况。
* **点赞数**：用户对内容的认可度。
* **分享数**：内容被传播的情况。

![image-20250721110609019](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721110609019.png)

#### 2. 列表设计要点

在数据概览下方，是“**内容列表**”，我会用表格的形式，展示创作者发布的所有内容。为了方便管理，这个列表必须具备：
* **关键信息展示**：清晰地展示文章标题、封面图、所属分类、发表时间等。
* **筛选与搜索功能**：提供按标题关键词搜索、按分类筛选、按日期筛选的功能。
* **快捷操作入口**：每一行内容后面，都需要有“编辑”、“删除”、“查看”、“下线”等快捷操作按钮。
* **分页功能**：当内容数量过多时，必须提供分页。

### 8.3.3 内容发布与编辑

![image-20250721110832220](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721110832220.png)

点击“发布文章”或“编辑”按钮，就会进入我们的**内容编辑器**。这是创作者挥洒才华的地方，体验必须流畅。我的设计会包含：
* **基础信息区**：清晰的`文章标题`、`作者署名`等输入框。
* **富文本编辑器**：一个所见即所得的编辑器，至少要支持加粗、列表、插入图片等基础的图文混排功能。
* **分类与标签**：提供`文章分类`的选择功能，并允许作者为文章打上`标签`，这既方便用户理解，也便于我们的算法进行分发。
* **操作区**：提供`预览`、`存草稿`和`发布`等核心操作按钮。

### 8.3.4 内容状态管理

在内容列表中，我还需要一个“**状态**”字段，来清晰地标识每一篇内容的生命周期。至少应包含以下几种状态：
* **草稿**：已保存，但还未提交发布。
* **审核中**：已提交，正在等待平台审核。
* **已发布**：审核通过，所有用户可见。
* **审核驳回**：审核不通过，作者需要修改后重新提交。
* **已下线**：由作者本人或平台主动下架，用户不可见。

### 8.3.5 本节小结

| **模块** | **我的核心设计思考** |
| :--- | :--- |
| **数据概览** | **让数据说话**。第一时间向创作者展示最核心的内容表现数据，给予他们最直接的反馈和激励。 |
| **内容列表** | **高效管理**。提供强大的筛选、搜索和批量操作功能，帮助高产的作者轻松管理自己的百宝箱。 |
| **内容发布** | **沉浸创作**。提供一个稳定、易用的编辑器，让作者可以专注于创作本身，不受工具的干扰。 |





---

## 8.4 评论管理

在我看来，评论区是创作者与粉丝之间最重要的“连接器”。一个活跃、健康的评论区，是内容生命力的延续。因此，为创作者提供一套高效、便捷的评论管理工具，是自媒体后台设计的重中之重。我的设计，主要围绕**过滤、查看、回复**这三个核心动作展开。

### 8.4.1 学习目标

在本节中，我的目标是带大家设计一个功能完善的评论管理系统。我们将学习如何设计一个两层结构的评论列表，并为创作者提供必要的评论处理功能。

### 8.4.2 评论列表设计

![image-20250721112220480](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112220480.png)

为了让创作者能高效地管理海量评论，我通常会设计一个两级结构的列表。

1.  **第一级：文章评论概览**
    后台的入口，首先是一个以“**文章**”为维度的评论列表。这张列表清晰地展示了：
    
    * `内容标题`：是哪篇文章收到了评论。
    * `评论总数` 和 `待回复评论数`：让创作者快速了解整体情况和待办事项。
* `操作`：提供一个“**查看评论**”的入口，点击后进入第二级列表。
  
2. **第二级：单篇评论详情**

    ![image-20250721112306067](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112306067.png)

    点击“查看评论”后，创作者会进入针对**某一篇文章**的详细评论列表。在这里，可以清晰地看到每一条评论的`评论内容`、`用户昵称`、`用户头像`等。同时，我还会提供按`用户昵称`或`评论内容`进行筛选和搜索的功能。

### 8.4.3 评论处理功能（回复/置顶/删除）

在单篇评论详情列表的每一条评论后面，我必须为创作者提供一组管理工具：
* **回复**：这是最重要的功能，是创作者与粉丝直接对话的桥梁。
* **删除**：赋予创作者管理自己评论区环境的权力，可以删除不友善或垃圾评论。
* **（可选）置顶**：这是一个非常好的精细化运营功能。我可以通过置顶优质评论，来引导整个评论区的讨论氛围。

### 8.4.4 本节小结

一个好的评论管理系统，能让创作者感受到自己对社群的“掌控感”，并激励他们更积极地与粉丝互动，从而形成一个正向的社区循环。

---

## 8.5 粉丝管理

如果说内容是创作者的“作品”，那么粉丝就是创作者最宝贵的“资产”。一个优秀的自媒体后台，必须为创作者提供一套轻量级的CRM（客户关系管理）系统，帮助他们了解自己的粉丝，从而创作出更受欢迎的内容。

### 8.5.1 学习目标

在本节中，我的目标是带大家设计一个包含“三视图”（列表、概况、画像）的粉丝管理模块，为创作者提供从宏观到微观的粉丝洞察能力。

### 8.5.2 粉丝列表

![image-20250721112703081](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112703081.png)

这是粉丝管理的“**微观视图**”。它是一个完整的、可搜索、可筛选的粉丝名录。
* **核心功能**：我会以列表的形式，展示每一位粉丝的`昵称`、`头像`、`性别`、`地区`等基础信息。
* **互动设计**：在每一位粉丝后面，我会提供“**私信**”或“**关注**”（回关）等操作按钮，为创作者提供主动触达粉丝的渠道。

### 8.5.3 粉丝概况

![image-20250721112739984](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112739984.png)

这是粉丝管理的“**宏观数据视图**”，它告诉创作者“我的粉丝群体整体发展趋势如何？”。

#### 1. 核心数据指标

在页面的最顶部，我会用数据卡片的形式，展示创作者最关心的几个核心KPI，比如：
* **粉丝累计总数**
* **昨日新增粉丝**
* **昨日取关数**

#### 2. 数据可视化设计

为了让趋势更直观，我会用**折线图**的形式，来展示粉丝总数和每日净增的变化趋势。在图表下方，再附上详细的每日数据表格，供创作者进行深度分析。

### 8.5.4 粉丝画像

![image-20250721112824923](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112824923.png)

这是粉丝管理的“**宏观特征视图**”，它回答了创作者最关心的问题：“**我的粉丝，到底是一群什么样的人？**”

我通过数据可视化的方式，对创作者的所有粉丝，进行群体的、匿名的特征分析，通常包括：
* **性别分布**（男女比例图）
* **年龄分布**（年龄段柱状图）
* **地域分布**（地图热力或省份排行）

这些画像信息，对于创作者判断自己未来的内容方向，具有极高的战略价值。

### 8.5.5 本节小结

| **模块** | **核心视图** | **我的设计目标** |
| :--- | :--- | :--- |
| **粉丝列表** | **微观视图** | 让创作者能看到**每一个**具体的粉丝，并提供互动渠道。 |
| **粉丝概况** | **宏观数据视图** | 让创作者能看到粉丝总量的**增长趋势**和每日变化。 |
| **粉丝画像** | **宏观特征视图** | 让创作者能了解自己粉丝群体的**人口统计学特征**。 |

---

## 8.6 本章总结

### 8.6.1 课程内容回顾

在本章，我们完整地设计了内容产品生态的另一端——**自媒体端**。
* 我们首先确立了**设计思路**，明确了我们的目标是打造一个“创作者成功平台”。
* 我们设计了专业的**入驻登录**流程，为平台筛选优质创作者。
* 我们设计了核心的**内容管理**模块，为创作者提供了集发布、管理、数据分析于一体的“创作车间”。
* 我们设计了**评论管理**功能，赋予创作者与粉丝互动、管理社区的能力。
* 最后，我们设计了包含“三视图”的**粉丝管理**系统，为创作者提供了宝贵的粉丝洞察。



---

# 第九章：平台端设计（用户-内容-运营）

![image-20250721124904426](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721124904426.png)

在前面两个章节，我们已经为产品的“前台”——用户端和自媒体端，设计好了舞台和化妆间。现在，我们需要开始搭建整个剧院的“**后台**”——**平台端（Admin Backend）**。

这是我们作为平台运营和管理人员，进行日常工作的“驾驶舱”，是整个产品生态能够有序、健康运转的中枢神经。我们将从这个后台最基础，也是最重要的模块开始：**用户管理**。

## 9.1 用户管理

我设计用户管理模块的核心思路是“**分类与控制**”。正如思考题所提示的，管理一名普通的内容消费者，和管理一名专业的内容创作者，其关注点和所需要的工具是截然不同的。因此，我的后台设计，必须清晰地将他们分类，并提供差异化的管理能力。

### 9.1.1 学习目标

在本节中，我的目标是带大家掌握一套专业的后台用户管理系统的设计方法。我们将学习如何分别为 **普通用户** 和 **自媒体用户** 设计管理列表，并重点拆解 **自媒体的审核流程**，最后还会介绍一个平台运营中非常实用的高级功能——**马甲管理**。

### 9.1.2 普通用户管理

![image-20250721124938842](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721124938842.png)

这是我们用户管理的基础模块，用于管理平台上所有的内容消费者。

#### 1. 用户列表与查询

一个合格的用户列表，必须具备强大的查询和筛选能力。我需要为运营同事，提供多维度的查询字段，比如：

* **昵称**
* **手机号码**
* **用户状态**（如：正常、已封禁）
* **用户性别**

列表的表头，则需要清晰地展示用户的核心信息，如 `昵称`、`头像`、`性别`、`地区`、`手机号`、`用户状态` 等。

#### 2. 用户详情查看

在操作列，点击“查看”，运营同事可以进入用户的详情页，看到该用户更完整的资料、行为日志、消费记录等。

#### 3. 用户状态管理（封禁/解封）

这是最重要的管理权限。在操作列，我必须提供“**封禁**”功能。当一个用户出现违规行为时，运营同事可以将其账号封禁。当然，也必须提供对应的“**解封**”功能。

### 9.1.3 自媒体用户管理

![image-20250721125236680](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721125236680.png)

管理创作者，我们需要关注比普通用户更多的信息。

#### 1. 创作者列表与查询

创作者列表的设计，在普通用户列表的基础上，我会额外增加几个关键的展示字段和筛选条件：

* **认证类型**：清晰地标识出该创作者是“个人认证”还是“企业认证”。
* **认证时间**：记录其通过审核、正式成为创作者的时间。

#### 2. 自媒体审核

![image-20250721125408677](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721125408677.png)

这是自媒体用户管理中，一个独立且至关重要的工作流，我通常会把它设计成一个单独的页面。

* **待审核列表**：这个页面的默认视图，就是所有提交了入驻申请、正在等待审核的用户列表。
* **审核详情页（通过/驳回）**：运营同事点击“审核”后，可以查看该用户提交的所有资质信息。在这个页面，必须有两个明确的操作按钮：“**通过**”和“**驳回**”。如果选择“驳回”，还需要提供一个填写驳回理由的输入框。
* **审核历史记录**：系统需要记录所有的审核操作，便于未来追溯。



---

### 9.1.4 马甲管理

现在，我们来探讨一个平台运营中，非常实用甚至可以说是必不可少的高阶功能——**马甲管理**。

#### 1. 马甲管理的价值与“鲶鱼效应”

“马甲”，指的就是由我们平台内部运营人员，在后台创建和控制的“虚拟用户”。我设计这个功能，主要是为了在社区运营中，起到“**鲶鱼效应**”——即，通过引入一些活跃的“鲶鱼”（马甲），来激活整个“鱼塘”（真实用户生态）的活力。

它的核心价值主要体现在两方面：

1. **制造争议、热点话题，带节奏**：在社区冷启动或需要引导舆论时，我的运营同事可以使用马甲，发布一些具有话题性的内容，引发用户讨论，掌握社区的话题走向。
2. **灌水，活跃社区氛围**：在社区初期用户较少时，通过马甲发布一些日常内容、进行评论和互动，可以快速地让社区看起来“有人气”，打破“无人区”的尴尬，从而吸引真实用户加入和发言。

#### 2. 添加马甲

![image-20250721131112565](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131112565.png)

为了让运营同事能方便地创建这些虚拟用户，我需要设计一个“**新增马甲**”的功能，它通常是一个后台的弹窗表单。

* **核心字段**：表单中需要包含创建逼真用户所需的核心字段，比如 `马甲昵称`、`头像`、`性别`、`地区` 等。具体需要哪些字段，由我们产品的实际业务决定。
* **归属管理员**：这是我设计中非常关键的一环。为了分工明确、责任到人，**每一个马甲，都必须可以分配给一个指定的管理员**。这样，我们就能清晰地知道，哪个马甲由哪位运营同事负责使用和维护。

#### 3. 马甲列表

![image-20250721131135140](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131135140.png)

所有创建的马甲，都需要在一个单独的“**马甲管理**”列表中进行统一的查看和维护。这个列表，只有特定的、高权限的管理员才能看到。

* **列表设计要点**：
    1. **查询与筛选**：列表必须提供强大的查询功能，特别是要支持按“**归属管理员**”进行筛选，方便运营主管查看自己团队成员名下的马甲。
    2. **信息展示**：列表中需要清晰地展示 `马甲昵称`、`归属管理员`、`状态` 等核心信息。
    3. **技术实现**：一个设计要点是，马甲可以不需要像真实用户一样，拥有完整的账号密码体系。从技术实现上，它可以是一个仅有昵称、头像等信息的“虚拟身份”，能通过后台直接进行内容发布和评论即可。



---

## 9.2 内容管理

![image-20250721131945141](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131945141.png)

在第八章，我们为创作者（自媒体）设计了他们的“创作车间”。但这引出了一个问题：作为平台，我们难道只需要提供工具，然后对海量的内容放任不管吗？

答案显然是 **否定** 的。

一个健康的内容生态，平台绝不能只当一个被动的“房东”，而必须是一个积极的“**秩序维护者**”和“**价值发现者**”。**内容管理** 后台，就是我们履行这个职责的核心工具。

### 9.2.1 学习目标

在本节中，我的目标是带大家设计一个专业、高效的内容管理后台。我们将重点学习 **内容列表** 的设计，掌握如何实现强大的 **查询与筛选**、严谨的 **内容审核** 流程，以及精细化的 **内容推荐与加权** 等高级运营功能。

### 9.2.2 内容列表

![image-20250721132036484](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721132036484.png)

内容管理模块的核心，就是这张包罗万象的 **内容列表**。这是我们的运营和审核同学，日常工作中停留时间最长的页面。我设计的核心目标是 **效率** 和 **掌控力**。

#### 1. 内容查询与筛选

一个内容平台，文章动辄成千上万。如果不能让运营同学在 3 秒内找到他想要的内容，那这个后台就是失败的。因此，我必须设计一套强大的查询与筛选系统。

* **基础筛选字段**：根据图例，至少要包含：
  * `文章标题`
  * `作者昵称`
  * `发表时间`（支持按时间范围筛选）
  * `文章分类`

* **我的拓展设计**：为了让管理更精细，我还会额外增加几个关键的筛选条件：
  * `内容ID`：用于研发同学进行精准的问题定位。
  * `来源`：这是图例中提到的一个设计要点。我必须让运营可以筛选出 **“自媒体发布”** 或 **“普通用户发布”**（如果我们的产品支持）的内容，因为这两者的审核标准和权重可能不同。
  * `审核状态`：这是审核人员最高频使用的筛选条件。他们可以通过筛选“**待审核**”状态，快速进入自己的工作队列。

#### 2. 内容审核（通过/驳回/下架）

这是内容列表的“控制核心”，体现在“**操作**”这一列。一个严谨的后台，其可执行的操作，必须与内容的“**审核状态**”严格绑定。我会将它设计成一个状态机：

* **当内容状态为“待审核”时**：
    操作列应提供：`查看`、`通过`、`驳回`。
  * **设计要点**：点击“驳回”时，必须弹出一个输入框，让审核人员填写驳回理由，这个理由会反馈给创作者。

* **当内容状态为“已通过”（即已上线）时**：
    操作列应变为：`查看`、`下架`、`删除`。
  * **设计要点**：“下架”是一个软删除，内容仅对用户不可见，作者后台依然可见；而“删除”则是一个硬删除，会彻底清除内容。我必须为“删除”操作，设计一个“二次确认”的弹窗，防止误操作。

* **当内容状态为“已驳回”或“已下架”时**：
    操作列可以简化为：`查看`、`删除`。

#### 3. 内容推荐与加权

除了基础的审核，一个优秀的后台，还要能让运营同事对优质内容进行“助推”。我会增加几个高级运营功能：

* **置顶**：如图例所示，这是最常见的运营手段。提供一个“置顶”按钮，可以将优质内容在前端的某个列表（如分类列表、频道首页）顶部固定显示。我还会设计一个“取消置顶”的功能。
* **加权**：这是一个更精细化的运营工具。我会在后台为每篇文章增加一个“**权重值**”输入框（比如 1-100）。我们前端的算法推荐或热度排序公式，就可以把这个“人工权重”作为一个重要的计算因子。这样，我就实现了“**人工编辑意志**”与“**算法推荐**”的完美结合。
* **推送**：对于 S 级的顶级内容，我还会设计一个“**推送**”按钮。运营同事点击后，可以将这篇文章通过 Push（推送通知）的形式，直接触达我们的用户，实现最大化的曝光。


---

### 9.2.3 敏感词管理

在我们的内容列表中，审核人员需要对“待审核”的内容进行人工处理。但面对 UGC 平台海量的内容生产，如果完全依赖人工，审核团队会被瞬间淹没。

因此，我必须设计一套 **自动化的初审过滤系统**，来分担团队的压力，并将最明显的违规内容，拦截在“摇篮”里。这套系统的核心，就是 **敏感词管理**。

![image-20250721132535224](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721132535224.png)

#### 1. 敏感词库维护（增/删/改/查）

我需要为我的运营同事，提供一个简单、易用的后台界面，来持续地维护我们平台的“**敏感词词库**”。这个词库，就是我们自动化审核的“规则库”。

这个后台界面，必须具备以下核心功能：

* **添加敏感词**：提供一个“+ 添加敏感词”的入口，让运营可以随时将新发现的违规词语录入系统。
* **查询与筛选**：当词库变得庞大时，必须提供按“**敏感词**”本身进行搜索，或按“**状态**”进行筛选的功能。
* **编辑与状态管理**：这是我设计中的一个要点。除了编辑和删除，我必须为每个敏感词，增加一个“**上线/下线**”的状态。只有处于“**上线**”状态的词，才会被系统用于前端的匹配过滤。这个设计，能让运营同事在不删除历史词语的情况下，灵活地启用或禁用某些词的过滤规则，特别是在应对一些突发的热点事件时，非常有用。

#### 2. 敏感词分类与处理规则

仅仅有一个词库列表，在我看来，还是一个非常初级的设计。一个专业的敏感词系统，必须具备更“聪明”的、差异化的处理能力。为了实现这一点，我会在我的设计中，再增加两个维度：**敏感词分类** 和 **处理规则**。

* **第一步：为敏感词分类**
    在“添加敏感词”时，我会要求运营同事，必须为这个词选择一个预设的“**分类**”。我会将词库至少分为以下几类：

  * `涉政类`
  * `暴恐类`
  * `色情类`
  * `广告营销类`
* `辱骂攻击类`
  
* **第二步：设定差异化的处理规则**
    完成了分类，我就可以为 **不同类别** 的敏感词，设定 **不同的自动化处理规则**。这才是这个系统智能化的体现。

    | **敏感词分类** | **示例** | **我设定的处理规则** | **对用户的反馈** |
    | :--- | :--- | :--- | :--- |
    | **辱骂攻击类** | “傻 X”、“垃圾”等 | **替换（Masking）** | 系统自动将“傻 X”替换为“**”，内容依然可以发布。 |
    | **广告营销类** | “加 V 信”、“www. ... .com” | **拦截（Block）** | 系统直接阻止该内容的发布，并向用户提示“内容包含违规广告信息，请修改”。 |
    | **涉政/暴恐等高危类**| （一些高度敏感的词语） | **转人工审核（Manual Review）** | 内容发布后，用户自己可见，但其他用户不可见，同时该内容自动进入我们后台的“待审核”列表，由人工进行最终判定。 |

通过这套“**分类+规则**”的组合设计，我们的敏感词管理系统，就从一个简单的“过滤器”，升级为了一个具备初步智能的、能分级处理风险的“**自动化审核引擎**”。



---

### 9.2.4 分类管理

![image-20250721133028297](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133028297.png)

一个很核心的问题是：当创作者发布内容时，他可以选择的那些“分类”，是从哪里来的呢？答案就是由我们平台的运营人员，在这个“**分类管理**”后台中，进行统一的创建和维护。

在我看来，分类管理是为我们的内容产品，搭建一个清晰、有序的“**图书馆目录**”。它将直接决定我们产品前台的 **频道划分** 和 **用户浏览结构**。

我通常将这个“目录”体系，分为两个层级：宏观的“**频道/分类**”和微观的“**标签**”。

#### 1. 频道/分类管理

![image-20250721133051445](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133051445.png)

“频道/分类”是内容最高层级的划分，它通常直接对应着我们 App 首页的 **导航栏**，比如“科技”、“财经”、“体育”、“娱乐”等。

这个后台管理界面，我的设计要点如下：

* **分类的增删改查**：这是最基础的功能。运营人员必须可以方便地 `新增分类`，并对已有的分类进行 `编辑` 和 `删除`。
  * **我的拓展思考**：在设计“删除”功能时，我必须考虑一个边界情况：如果某个分类下已经存在大量内容，那么直接删除这个分类，会导致这些内容成为“孤儿”。因此，一个严谨的删除逻辑应该是：**当分类下存在内容时，禁止删除，并提示运营人员先将该分类下的内容，迁移至其他分类**。
* **状态管理**：和我们之前设计的其他模块一样，我必须为每个分类，提供“**上线/下线**”状态。这能让运营同事从容地去筹备一个新频道，在内容和运营活动都准备好之后，再一键“上线”，呈现给所有用户。
* **排序功能**：我认为 **极其重要** 的一个功能。运营同事必须可以 **手动调整分类的显示顺序**。这个后台的排序，将直接决定前端 App 导航栏的频道顺序。我通常会设计一个支持“上移/下移”或“拖拽排序”的功能。

#### 2. 标签管理

![image-20250721133343391](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133343391.png)

如果说“分类”是图书馆的“区域”（如：A 区-文学），那么“**标签**”就是贴在每一本书上，更精细的“**关键词**”（如：科幻、刘慈欣、三体）。我们为内容打上精准的标签，核心目的就是为了 **喂给我们的算法推荐引擎**，让它能实现更精准的“人-内容”匹配。

这个后台的设计，除了基础的增删改查和状态管理外，我还会重点考虑以下两点：

* **标签分类与层级**：当标签数量达到成千上万时，一个扁平的列表是无法管理的。因此，我必须设计一个 **支持层级** 的标签体系。
  * 如图例所示，一个“`产品经理`”的标签，它的上级分类可能是“`互联网`”。这种树状的层级结构，有两大好处：
        1. **便于管理**：运营同事可以像操作文件夹一样，高效地管理和查找标签。
        2. **便于算法**：能让我们的推荐算法更“聪明”。算法会知道，喜欢“`产品经理`”标签内容的用户，可能也会对“`互联网`”这个更大范畴下的其他内容感兴趣，从而扩大推荐的相关性。


![image-20250721133429415](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133429415.png)

* **标签的创建与合并**：
  * **创建权限**：我需要做一个重要的产品决策：`新标签是由谁来创建的？` 是只能由运营在后台创建？还是允许创作者在发布内容时，自由地创建新标签？前者能保证标签库的规范和整洁，后者则能让标签库的内容更丰富、更接地气。在产品初期，我通常会采用“**运营后台创建为主，创作者可申请为辅**”的策略。
  * **合并功能**：当标签库由多人维护或允许用户创建时，不可避免地会出现语义相同但文字不同的标签（如：“产品经理”、“PM”、“产品策划”）。因此，我必须设计一个“**标签合并**”功能，让运营可以将这几个重复的标签，合并为一个标准标签，并自动更新所有关联了这些标签的内容。



-----

## 9.3 运营管理

![image-20250721134507525](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134507525.png)

我们已经设计了用户和内容的管理后台，但这还不够。一个好的产品，不能只依赖用户“**主动**”地回来使用。在如今这个信息爆炸、App 泛滥的时代，“酒香也怕巷子深”。

我们必须建立一套属于自己的“**广播系统**”，能够主动地、在合适的时机，去触达我们的用户，提醒他们、吸引他们回来。这就是 **运营管理** 模块的核心价值。

### 9.3.1 学习目标

在本节中，我的目标是带大家设计一个专业的运营管理后台。我们将重点学习 **消息推送**、**后台账号与权限** 和 **日志管理** 这三大系统的设计。

### 9.3.2 消息推送

消息推送（Push Notification）是我作为平台运营方，唯一能“**免费**”且“**主动**”触达已安装 App 但未打开用户的渠道。它是拉动用户活跃、进行活动通知、实现用户召回的最强武器。

#### 1\. 推送任务列表与数据统计

![image-20250721134611755](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134611755.png)

这是我运营团队的“**发射控制中心**”。它是一个记录了所有已发送和待发送推送任务的列表。除了展示 `标题`、`目标用户`、`推送时间` 等基础信息外，一个专业后台的核心，在于提供推送效果的数据统计。

| 数据指标 | 说明 |
| :--- | :--- |
| **发送数 (Sent)** | 本次任务，我们总共向多少个设备发送了通知。 |
| **到达数 (Delivered)**| 其中，成功送达到用户设备的数量。（有些可能因网络或卸载失败） |
| **点击数 (Clicks/Opens)**| 最终，有多少用户被我们的文案吸引，点击了这条通知。 |
| **点击率 (CTR)**| **最重要的评估指标（CTR = 点击数 / 到达数）**，直接反映了推送的质量。 |

#### 2\. 新建推送配置

![image-20250721134721627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134721627.png)

这是我为运营同事设计的“**炮弹编辑器**”。这个“新建推送”的表单，必须严谨、清晰，我通常会把它设计为“**推送四要素**”的配置。

| 配置要素 | 核心选项与说明 |
| :--- | :--- |
| **推送对象 (Who)** | **全部用户**：用于平台级的重大公告。<br>**用户分群**：按用户标签（如地域、兴趣）、活跃度等进行精准推送。<br>**指定用户**：通过上传用户 ID 列表，进行点对点推送。 |
| **推送时间 (When)**| **立即推送**：用于突发新闻、热点事件。<br>**定时推送**：用于已规划好的运营活动，可以提前设置。 |
| **推送内容 (What)**| **通知标题**：吸引用户眼球的第一句话，至关重要。<br>**通知内容**：对标题的补充说明，可支持插入用户昵称等个性化变量。 |
| **目标页配置 (Where To)**| **打开应用首页**<br>**打开应用内指定页面**：如某篇文章、某个活动页。<br>**打开一个 H5 链接**：跳转到外部网页。 |

**我的拓展设计（技术实现浅谈）**：
这个功能的技术实现，我们通常不会自己从零搭建，而是会依赖专业的 **第三方推送服务**。对于 iOS 端，我们后台需要接入苹果官方的 **APNs**；对于国内的 Android 端，我通常会选择集成一个像 **极光推送（JPush）或个推** 这样的第三方聚合服务商。我的 PRD 需要明确技术方案，因为不同服务商的能力会影响我的产品设计。

### 9.3.3 账号与权限管理

这个模块，管理的不是我们产品的“用户”，而是我们公司内部使用这个后台系统的“**员工**”。其设计的核心，是确保后台系统的 **安全性** 和 **规范性**。

#### 1\. 核心思想：RBAC 模型

我设计后台权限，普遍采用的是 **RBAC（Role-Based Access Control，基于角色的访问控制）** 模型。

它的逻辑很简单：我不直接给“某个人”分配权限，而是先创建不同的“**角色**”（如：内容审核员、高级运营），为这些“角色”分配权限，最后再把“某个人”归属到某个“角色”里去。

这样做的好处是，当公司人员变动时，我只需要调整这个人的角色，而不需要重新为他配置一遍复杂的权限，管理效率极高。

#### 2\. 角色管理

![image-20250721135012847](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135012847.png)

* **角色列表**：一个展示所有已创建角色的列表，如“超级管理员”、“内容审核”、“数据分析师”等。
* **新建/编辑角色**：提供创建新角色的功能。
* **角色权限配置**：这是核心。我会以“功能菜单树”的形式，列出后台的所有功能点，让管理员可以通过勾选的方式，为每个角色分配它能看到和操作的菜单权限。

#### 3\. 账号管理（后台用户）

这是具体管理员工账号的地方。

![image-20250721135036854](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135036854.png)

* **后台账号列表**：展示所有后台用户的列表。
* **新增/编辑账号**：当我需要为一位新同事开通后台权限时，我会在这里为他新建账号。

| 新增账号字段 | 填写说明 |
| :--- | :--- |
| **登录账号** | 用于后台登录的唯一 ID，建议使用员工的企业邮箱。 |
| **员工姓名** | 账号使用者的真实姓名，用于日志记录和责任追溯。 |
| **所属角色** | 从我们创建的“角色列表”中，为该账号选择一个角色，从而赋予他对应的权限。 |
| **账号状态** | 正常/冻结。当员工离职时，我可以将其账号冻结，而不是直接删除。 |

### 9.3.4 日志管理

日志管理是后台的“**黑匣子**”和“**监控录像**”。它记录了所有管理员在后台的一举一动，是进行安全审计和问题追溯的最后一道防线。

#### 1\. 操作日志的价值

它的价值在于 **安全** 和 **可追溯**。当后台出现误操作或恶意操作时，我可以通过日志，精准地定位到是“谁”，在“什么时间”，对“什么东西”，做了“什么操作”。

#### 2\. 操作日志设计要点

![image-20250721135143985](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135143985.png)

一个合格的操作日志系统，必须清晰地记录以下关键信息，并提供强大的查询功能。

| 记录字段 | 说明 | 示例 |
| :--- | :--- | :--- |
| **操作人** | 执行该操作的后台账号。 | `yunying_xiaowang` |
| **时间戳** | 操作发生的精确时间。 | `2025-07-21 14:42:00` |
| **IP 地址** | 操作人当时使用的 IP 地址。 | `************` |
| **操作行为**| 具体执行了什么动作。 | `内容管理 - 下架文章` |
| **操作对象**| 该动作作用于哪个具体目标。 | `文章ID: 88012` |
| **操作结果**| 本次操作是成功还是失败。 | `成功` |



---

# 第十章：产品研发全流程管理

到目前为止，我们已经作为产品经理，完成了从需求分析到方案设计的核心工作。一个包含了 PRD 和交互原型的完整方案，已经躺在了我们的电脑里。

那么接下来的问题是：**然后呢？我们要如何推动这个方案，一步步地变成一个能被用户真实使用的、上线的活产品？**

这就是本章要解决的问题。在这里，我将带大家跳出产品经理的单一角色，以一个“**项目指挥官**”的视角，来学习如何驱动一个完整的团队，协同作战，最终打赢一场漂亮的产品发布战役。

## 10.1 产品生产发布流程

![image-20250721140058419](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140058419.png)

产品的生产发布，从来不是一个人的战斗，而是一场需要多兵种（角色）协同的“**接力赛**”。我面前的这张“泳道图”，就是我们这场接力赛的 **核心作战地图**。

它清晰地展示了，一个产品从诞生到上线，需要经历 **方案设计、产品研发、验收上线** 这三大阶段，以及 **产品经理、UI 设计师、程序员、测试** 这四个核心角色，是如何在这场接力赛中，依次交棒、紧密协作的。

### 10.1.1 学习目标

在本节中，我的目标是带大家清晰地理解这场“接力赛”的规则。我们将深入学习团队中每一个核心角色的职责，并重点掌握我作为产品经理，是如何与他们进行高效协作，以及如何管理关键的文档交付与评审流程的。

### 10.1.2 团队协作与成员职责（产品、UI、开发、测试）

要打赢一场仗，首先要了解我们的战友。

#### 1. 产品经理 (Product Manager) - 流程的“大脑”与“发起者”

我的角色，是整个流程的 **“Why”和“What”的定义者**。

* **我的职责**：正如泳道图所示，整个流程由我发起。我负责 `收集分析需求`，并最终 `输出方案`（PRD 和原型）。我是产品方向的掌舵人，是所有后续工作的“需求源头”。

#### 2. UI 设计师 (UI Designer) - 产品的“化妆师”

![image-20250721140204539](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140204539.png)

UI 设计师，是 **“产品长什么样（How it Looks）”的专家**。

* **他的职责**：是把我的低保真原型（产品的“骨架”），进行专业的视觉设计，输出包含色彩、图标、字体的 **高保真视觉效果图**（产品的“皮肤”），让产品变得美观、有吸引力。
* **我与 UI 的协作**
    ![image-20250721140235402](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140235402.png)
    我与 UI 设计师的协作，遵循“讲解-验收”两步走：
    1. **讲解**：在交付原型时，我必须召开会议，向他详细讲解我的设计背后的 **需求背景、业务目的和核心逻辑**。
    2. **验收**：在 UI 稿完成后，我需要严格地进行 **视觉验收**，确保他的设计不仅美观，更重要的是，完全符合我想要传达的产品目标和用户体验。

#### 3. 研发工程师 (Developer) - 产品的“建造者”

![image-20250721140327008](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140327008.png)

研发工程师，是 **“产品如何工作（How it Works）”的实现者**。

* **他的职责**：他们是把我们的设计图纸，用一行行代码，变成一个真实可用的产品的“建筑师”。他们通常分为：
  * **前端开发**：负责实现用户能直接看到和交互的界面。
  * **后端开发**：负责实现支撑前端运转的服务器、数据库和业务逻辑。
* **我与研发的协作**
    ![image-20250721140352470](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140352470.png)
    我与研发工程师的协作，是整个流程中最核心、最需要严谨性的环节：
    1. **文档交付**：我必须提供清晰、完整、无歧义的 PRD 和原型。
    2. **评审排期**：我必须组织正式的“**需求评审会**”，确保所有研发人员都对需求理解一致。评审通过后，再共同制定开发排期。
    3. **项目管理**：在开发过程中，我需要持续跟进进度，解答疑问，管理变更。

#### 4. 测试工程师 (Tester) - 产品的“守门员”

测试工程师，是 `“产品是否正确（Is it Right）”` 的捍卫者。

![image-20250721140748356](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140748356.png)

![image-20250721140450191](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140450191.png)

* **他的职责**：在产品开发完成后，他们会严格地按照我的 PRD，对产品进行全面的 **产品测试**，找出所有潜在的 Bug 和与需求不符的地方，是保障产品质量的最后一道，也是最重要的一道防线。

---

### 10.1.3 文档交付与评审流程

在我完成了 PRD 和原型的撰写后，就进入了至关重要的“**交棒**”环节。

最传统的模式，是一种“**单线程瀑布流**”：我做完方案 -> 交给 UI 做设计 -> UI 设计完 -> 再一起开评审会 -> 然后开发才开始工作。这种模式虽然稳妥，但它的弊端也很明显：**效率低下**，各个角色之间是“串行”等待，浪费了大量时间。

因此，在我的实践中，我极力推行一种更高效的“**并行开发**”模式：

![image-20250721141211224](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721141211224.png)

1. **我完成 PRD 和“低保真”原型后，立刻组织需求评审会**。
2. 评审会通过后，工作就可以兵分两路、同时进行：
    * **UI 设计师**：开始基于我的低保真原型，进行高保真视觉设计。
    * **后端开发工程师**：完全不需要等待 UI 稿。他们可以根据我的 PRD 和低保真原型中的逻辑，**立刻开始进行接口开发和数据库设计**。
3. **前端开发工程师**：可以先根据 PRD 和接口文档，搭建前端项目的框架，等待 UI 稿一到，就可以立刻“填充”页面，并与后端进行接口联调。

这种并行模式，能极大地缩短项目周期。而实现它的核心，就在于一份 **清晰、无歧义的 PRD**，以及一场 **成功的需求评审会**。

### 10.1.4 测试流程（冒烟测试、回归测试等）

当开发工程师完成了一个功能模块的开发，并提交到测试环境后，我们的“守门员”——**测试工程师**——就要登场了。

* **我的拓展设计（模块化测试）**
    ![image-20250721140507686](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721140507686.png)

    对于一个较大的版本，我不会等到所有功能都开发完，再统一移交测试。我会和技术负责人一起，将整个版本 **拆分为几个独立的模块**。开发团队每完成一个模块，就立刻移交给测试团队进行测试。
    这样“**开发一个，测试一个**”的模式，能让测试工作尽早介入，提前暴露和解决问题，避免所有问题都堆积到项目后期，导致项目延期。

在整个测试环节，我会特别关注两种核心的测试类型：

![image](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image.png)

1. **冒烟测试 (Smoke Testing)**
    这是测试的第一步。当开发同学部署了一个新的测试版本后，测试同学会花很短的时间（比如 15-30 分钟），对这个版本最核心、最基本的功能（如登录、首页加载等）进行一次快速验证。
    * **我的理解**：冒烟测试就像我们拿到一个电器，先插上电，看看会不会冒烟。如果连最基本的功能都跑不通（“直冒烟”），那这个版本就是不合格的，会立刻“打回”给开发，无需浪费时间进行更详细的测试。

2. **回归测试 (Regression Testing)**
    这是保障产品质量最关键的一环。当开发同学修复了一个 Bug，或者增加了一个新功能后，测试同学不仅要测试这个“修改点”，还必须 **重新去测试那些原本没有问题的、相关的旧功能**。
    * **我的理解**：回归测试的目的，是为了防止“**按下葫芦浮起瓢**”。我们要确保，新的代码，没有意外地破坏掉旧代码的正常逻辑。

### 10.1.5 验收方式与上线流程

当测试团队确认，产品已达到上线标准（即没有严重的 Bug）后，就轮到我这个产品经理，进行最后一道关卡的把控——**产品验收（UAT）**。

#### 1. 产品验收

* **验收方式**：
  * **口头验收**：对于一些非常小的、非核心的改动，我可能会在测试通过后，自己快速体验一下，然后在工作群里回复一句“确认 OK”，即可。
  * **文档验收**：对于核心功能或重要版本，我一定会按照 PRD，整理出一份详细的“**UAT 验收清单**”，然后逐项地、严格地进行验收测试。

* **验收结果**：
  * **验收通过**：功能符合 PRD 的核心要求，没有重大问题。一些不影响主流程的、微小的体验瑕疵，我可以同意放到下个版本再优化。
  * **验收不通过**：功能的核心逻辑/流程，与我的 PRD 设计严重不符。此时，我有权“**打回重做**”，要求研发团队返工，直到满足需求为止。

#### 2. 上线流程

当我验收通过，给出“Go Live”的指令后，正式的上线流程就启动了。这是一个需要多方协作的过程。

* **我的职责（产品侧）**：
  * **确定版本号**：为即将上线的新版本，确定一个唯一的、符合规范的版本号（如：V2.5.0）。
  * **确定更新内容文案**：撰写将在应用商店里，展示给用户看的“更新日志（Release Notes）”。
  * **（可选）组织培训/撰写手册**：如果功能比较复杂，我还需要为客服或运营同事，准备培训材料或使用手册。

* **研发的职责（开发侧）**：
  * **提交应用商店审核**：由研发同学，将最终的安装包，提交给苹果 App Store、华为应用市场等各大渠道进行审核。
  * **择期发布**：在应用商店审核通过后，我们会共同商定一个合适的时机（比如用户活跃度较低的凌晨），进行正式的线上发布。

### 10.1.6 本节小结

| **阶段** | **我的核心角色与职责** |
| :--- | :--- |
| **交付与评审** | 作为“**讲解员**”，组织需求评审会，确保团队对需求理解 100%一致，并推动更高效的并行开发模式。 |
| **测试** | 作为“**信息枢纽**”，关注测试进度，特别是冒烟测试和回归测试的结果，确保产品质量。 |
| **验收与上线**| 作为“**最终决策者**”，进行产品验收（UAT），并准备好版本号、更新文案等上线所需材料，打好“临门一脚”。 |


---
## 10.2 项目管理

在很多公司，项目管理（Project Management）和产品管理（Product Management）是两个独立的岗位。但在更多敏捷的互联网团队里，我作为产品经理，通常也需要承担起项目经理的职责。

即便有专门的项目经理，我作为产品的“owner”，依然是项目成败的最终负责人。因此，掌握项目管理的基本方法和工具，是我的必备技能。

### 10.2.1 学习目标

在本节中，我的目标是带大家掌握产品经理视角下的项目管理核心。我们将学习项目管理的目标，以及我最常用来达成这些目标的**管理方式**和**管理工具**。

### 10.2.2 项目管理的定义与目标（时间与质量）

![image-20250721143219781](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143219781.png)

我理解的项目管理，就是：**在产品开发过程中，监督和管理整个研发团队，包括协调资源、处理矛盾、监督工期等，以确保项目能按期、按需、高质量地成功上线。**

在整个过程中，我最核心的两个目标，就是守护好**时间**和**质量**这两个生命线。
* **时间 (Time)**：确保项目按照我们共同制定的排期表，准时交付。
* **质量 (Quality)**：确保最终交付的产品，功能完整、体验流畅，严格符合PRD中的要求。

### 10.2.3 管理方式（例会、里程碑、进度检查）

为了管好时间和质量，我不会等到项目快结束时才去关心，而是会通过一系列的管理“仪式”，将管理工作贯穿于整个研发周期。

#### 1. 每日例会

这是敏捷开发中最核心的仪式。每天早上，我会把开发和测试的核心成员召集起来，开一个不超过15分钟的站会。每个人只需要回答三个问题：
* **昨天做了什么？**
* **今天准备做什么？**
* **遇到了什么困难（需要我协调解决）？**

每日例会，是我获取项目一线信息、发现潜在风险的最重要的途径。

#### 2. 里程碑

对于一个超过两周的项目，我一定会将它拆解为几个关键的**里程碑**。里程碑不是一个简单的日期，而是一个明确的、可交付的阶段性成果。

![image-20250721143433284](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143433284.png)

定义清晰的里程碑，能帮助我从宏观上把控项目的整体节奏，也便于我向管理层汇报进度。

#### 3. 进度检查

这是我日常的、持续性的工作。它包括与团队成员进行一对一的沟通，在项目管理工具上检查任务的完成状态，主动识别可能导致延期的风险，并尽我所能地为团队扫清障碍。

### 10.2.4 项目管理工具（甘特图、TAPD、禅道等）

要落地上述的管理方式，我必须借助专业的工具。

#### 1. 甘特图 (Gantt Chart)

![image-20250721143544979](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143544979.png)

**甘特图**，是我进行项目**长期规划**和**排期**的首选工具。
它是一个强大的时间轴视图，能让我清晰地看到：

* 项目包含哪些任务？
* 每个任务的开始和结束时间？
* 任务之间的依赖关系是怎样的？（比如：A任务不完成，B任务就无法开始）
* 每个任务的负责人是谁？

我通常会在项目启动时，和技术负责人一起，制定出一份详细的甘特图，作为我们整个项目的时间规划蓝图。

#### 2. TAPD / 禅道 / Jira

![image-20250721143754665](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721143754665.png)

这类工具，是我进行**日常、微观的任务跟踪**的核心。甘特图告诉我们“长期的路要怎么走”，而TAPD/禅道这类工具，则告诉我们“今天的每一步要怎么走”。
我主要用它们来实现：

* **创建和分配任务**：将PRD中的功能点，拆解为一个个具体的开发任务，并指派给对应的工程师。
* **追踪任务状态**：通过“**任务看板**”的形式，将所有任务的状态（如：待处理、进行中、已完成）可视化，团队进展一目了然。
* **管理Bug**：测试团队会在这里提交、指派和跟踪所有Bug的修复过程。
* **文档协作**：作为我们PRD、API文档等核心文档的存放和协作平台。

### 10.2.5 本节小结

| **管理维度** | **我的核心方法** | **我常用的工具** |
| :--- | :--- | :--- |
| **日常同步** | **每日例会** | **TAPD / 禅道** 的任务看板 |
| **长期规划** | **里程碑规划** | **甘特图** |
| **风险控制** | 持续的**进度检查** | 项目周报、一对一沟通 |





---
## 10.3 产品需求评审

在我看来，**产品需求评审会**，是整个研发流程中**最重要**的一个沟通仪式。

它是我作为产品经理，将我的“作战计划”（PRD和原型），正式地、全面地同步给我的“作战部队”（设计、研发、测试团队）的起点。

一场成功的评审会，能让整个团队对目标形成统一、清晰的认知，从而极大地提升后续的研发效率，避免返工；

而一场失败的评审会，则会埋下无数的“坑”，导致后续开发过程中的无尽扯皮和延期。

### 10.3.1 学习目标

在本节中，我的目标是带大家掌握如何组织和主导一场成功的需求评审会。我们将学习评审会的不同类型、标准的会议流程、高效的讲解内容结构，以及我作为会议主持人，必须掌握的控场技巧和要点。

### 10.3.2 需求类型（业务需求、功能需求）

![image-20250721144207121](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144207121.png)

在组织评审会前，我首先要明确这次会议的“类型”。我通常会把需求评审分为两种：

1. **业务需求评审**：这通常发生在项目的**极早期**。参会人员是**老板、业务负责人、技术负责人**等高阶决策者。会议的核心，是评审和探讨本次需求的**商业价值、需求范围、技术可行性、版本规划**等战略层面的问题。

2. **功能需求评审**：这通常发生在我们已经完成详细PRD和原型，**即将进入研发阶段**时。

	参会人员是**开发、测试、设计师**等一线的执行团队。会议的核心，是**讲解功能实现的每一个细节，确保团队对需求理解无误**。

我们本节后续讨论的，主要就是“**功能需求评审**”。

### 10.3.3 会议流程（准备、过程、会后跟踪）

![image-20250721144246357](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144246357.png)

一场成功的会议，其功夫往往在“会前”和“会后”。我严格遵循一个四阶段的会议流程：

| **会议阶段** | **我的关键动作** |
| :--- | :--- |
| **1. 预约会议** | 我会**至少提前1天**发出会议邀请，并**必须**在邀请中，附上本次评审的PRD和原型链接，要求所有参会者“**务必会前阅读**”。 |
| **2. 会前准备** | 我会提前进入会议室，确保投影、网络等设备一切正常，并将我的讲解材料准备就绪。 |
| **3. 会议过程** | 这是我的“主场”。我会严格按照预设的结构和节奏进行讲解和讨论（具体见下文）。 |
| **4. 会后跟踪**| 会议结束后半小时内，我会发出**会议纪要（Minutes）**，清晰地列出会议结论、遗留问题和下一步的行动计划（Action Items）及负责人。并持续跟踪这些问题的解决。 |

### 10.3.4 评审结构与讲解内容（背景、原型、其他需求）

![image-20250721144338478](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144338478.png)

在评审会中，我的讲解会像讲一个故事一样，遵循一个清晰、有吸引力的结构：

1.  **需求背景目的 (Why)**：我总是从“为什么”开始。用5-10分钟，清晰地向团队交代本次需求的来源、要解决的用户痛点和期望达成的商业目标。这能让团队在后续的讨论中，始终与“初心”对齐。
2.  **流程结构 (What - a high level view)**：接着，我会快速地展示本次需求相关的流程图和结构图，让团队对这个功能在整个产品中的“位置”和“骨架”，有一个宏观的认知。
3.  **原型讲解 (What - a detailed view)**：这是会议的核心部分。我会打开我的交互原型，从第一个页面开始，逐一地、详细地讲解每一个页面的布局、每一个控件的交互和其背后的所有业务规则。
4.  **其他需求 (How)**：最后，我会讲解PRD中定义的非功能性需求，比如性能要求、数据埋点需求、兼容性要求等。

### 10.3.5 评审要点（时间与节奏控制、控场与主见、结论收尾）

![image-20250721144424894](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721144424894.png)

作为会议的主持人，我的控场能力，直接决定了会议的成败。我时刻关注以下几点：

| **我的控场要点** | **具体做法** |
| :--- | :--- |
| **时间与节奏控制** | 我会严格将会议控制在**1小时**内。在讲解中，我会每隔10-15分钟就主动停下来，问“**到这里，大家有什么问题吗？**”，以保持互动，避免我一个人“一言堂”。 |
| **控场与主次划分** | 我会时刻注意评审的主题。当讨论陷入过深的技术细节或跑偏时，我会礼貌地打断，并建议“**这个问题非常好，我们线下再拉个小会深入讨论**”，然后把会议拉回主线。我会重点讲解流程复杂或有争议的地方。 |
| **讨论与主见** | 我会鼓励团队提出质疑，这是发现方案漏洞的好机会。但对于已经深思熟虑、关系到核心需求的点，我也会**有理有据地坚持自己的主见**，不能被轻易带偏。 |
| **收尾确定** | 会议结束前，我必须得到一个明确的结论：本次评审是“**通过**”、“**通过但有待办项**”还是“**不通过，需重大修改**”？并明确后续的Action Items和时间点。绝不能开成一个没有结论的“聊天会”。 |



## 10.4 本章总结

### 10.4.1 课程内容回顾

在本章，我们学习了如何将一个已经设计好的产品方案，一步步地推向最终的成功发布。
* **产品生产发布流程**：我们了解了产品、UI、开发、测试这四个核心角色的职责，以及他们之间环环相扣的协作流程。
* **项目管理**：我们学习了作为产品经理，如何通过例会、里程碑等方式，以及甘特图、TAPD等工具，来管理好项目的时间和质量。
* **产品需求评审**：我们深入地学习了如何组织和主导一场专业、高效的需求评审会，这是我们作为产品经理，最重要的“软技能”之一。

到这里，我们已经走完了从一个模糊的想法，到一个上线产品的全过程。恭喜你，完成了本次的学习！