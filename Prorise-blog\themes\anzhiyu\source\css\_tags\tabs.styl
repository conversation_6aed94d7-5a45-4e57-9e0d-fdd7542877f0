
#article-container
  .tabs
    position: relative
    margin: 0 0 20px
    border: 3px solid var(--anzhiyu-secondbg)
    margin: 1rem 0 !important;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--anzhiyu-shadow-border);
    background: var(--anzhiyu-card-bg);
    padding: 8px

    > .nav-tabs
      display: flex
      flex-wrap: wrap
      justify-content: center;
      margin: 0
      padding: 16px;
      background: var(--anzhiyu-card-bg);

      > .tab
        padding: 8px 18px
        background: var(--anzhiyu-secondbg);
        color: var(--anzhiyu-fontcolor);
        line-height: 1
        transition: all .4s
        margin: 4px;
        border: var(--style-border-always);
        border-radius: 8px;

        i
          width: 1.5em

        &.active
          border: var(--style-border-hover-always);
          background: var(--anzhiyu-background);
          border-radius: 8px;
          cursor: default;

        &:not(.active)
          &:hover
            background: var(--anzhiyu-main);
            color: var(--anzhiyu-white);
            transition: .3s;
            border: var(--style-border-hover-always);

      &.no-default
        & ~ .tab-to-top
          display: none

    > .tab-contents
      .tab-item-content
        position: relative
        display: none
        background: var(--anzhiyu-background);
        border: var(--style-border-always);
        padding: 1.2rem 1.2rem;
        border-radius: 8px;

        +maxWidth768()
          padding: 24px 14px

        &.active
          display: block

    > .tab-to-top
      padding: 0 16px 10px 0
      width: 100%
      text-align: right

      button
        color: $tab-to-top-color

        &:hover
          color: $tab-to-top-hover-color

@keyframes tabshow
  0%
    transform: translateY(15px)

  100%
    transform: translateY(0)