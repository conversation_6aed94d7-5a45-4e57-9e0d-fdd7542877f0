---
title: SpringAI（七）：7. Embedding Models：万物皆可向量化
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 5770
date: 2025-03-21 10:13:45
---

## 7\. Embedding Models：万物皆可向量化

在之前的章节中，我们已经与 `ChatModel` 和 `ImageModel` 进行了深入的互动，它们让我们能够与 AI 的“语言能力”和“创造能力”对话。现在，我们将探索 AI 的另一项核心能力——**理解能力**。这项能力的关键在于一个名为 **Embedding** 的概念，它也是我们后续构建高级 RAG（检索增强生成）应用的绝对基石。

那么，究竟什么是 Embedding？

> **Embedding 的核心思想**：将离散的、非结构化的数据（如文本、图片、代码）通过一个深度学习模型，映射到一个连续的、高维的向量空间中。在这个精心设计的空间里，**语义上相似的对象，其对应的向量在几何距离上也更近**。

简单来说，Embedding 模型就是一名“宇宙翻译官”，它能将我们世界中纷繁复杂的信息，统一翻译成计算机能够理解和比较的通用语言——**向量（Vector）**，也就是一长串浮点数。这种翻译是带有“魔力”的，它保留了原始信息的“意义”。例如，一个训练有素的 Embedding 模型可以实现类似 `vector("国王") - vector("男人") + vector("女人")` 的向量运算，其计算结果在向量空间中会惊人地接近 `vector("王后")` 的位置。

这项技术，正是让 AI-Copilot 从一个简单的“对话机器人”升级为“智能知识助手”的关键。

### 7.1 Embedding 在我们项目中的应用场景

在我们构想的 AI-Copilot 蓝图中，Embedding 将扮演至关重要的角色，它能解锁许多激动人心的高级功能。下面的表格简要列出了几个核心应用场景。

| 功能场景 | 核心挑战 | Embedding 解决方案 |
| :--- | :--- | :--- |
| **历史会话语义搜索** | 无法按“模糊印象”搜索 | 对话向量化，进行相似度搜索 |
| **企业知识库问答** | 无法回答内部、私有问题 | 私有文档向量化，实现 RAG |
| **智能代码片段推荐** | 难以发现功能相似的代码 | 代码块向量化，匹配自然语言需求 |

接下来，我们对每个场景进行详细解读：

  * **历史会话语义搜索**:
    传统的关键词搜索非常僵化。如果用户想找“上次那个关于数据库优化的讨论”，但忘记了具体用词，搜索 "数据库" 可能会返回大量不相关的结果。通过 Embedding，我们可以将**每一条**用户的聊天记录都转换为向量存入数据库。当用户输入“数据库优化讨论”时，我们将这句查询也转换为向量，并在数据库中搜索与查询向量“几何距离最近”的聊天记录向量，从而精准定位到语义上最相关的对话。

  * **企业知识库问答 (RAG)**:
    这是 Embedding 最核心的应用。我们的 AI-Copilot 目前只能基于通用知识回答问题，但无法解答关于我们公司内部的、私有的问题，例如：“公司的报销流程是怎样的？”。通过 RAG，我们可以：

    1.  将公司的**所有内部文档**（如《员工手册.pdf》、《报销制度.docx》）进行切片和向量化处理，构建一个内部知识库。
    2.  当用户提问时，先将其问题向量化，并在知识库中检索出最相关的几个文档片段。
    3.  将这些相关的片段作为上下文，连同用户的问题一起，提交给 `ChatModel`，让它基于这些“内部知识”生成精准回答。

  * **智能代码片段推荐**:
    想象一下，开发者在项目中遇到一个功能需求，希望能找到项目内已有的、功能相似的代码作为参考。我们可以将项目中的**所有函数或类**的代码块进行向量化。当开发者用自然语言描述需求（例如：“一个能处理文件上传并保存到S3的函数”）时，系统可以将这段描述向量化，并检索出与需求描述向量最相似的代码块向量，为开发者智能推荐最相关的代码实现。

掌握了 Embedding，我们就等于拥有了将任何信息转化为 AI 可理解语言的钥匙。接下来，我们将深入 Spring AI 1.0+ 全新设计的 `EmbeddingModel` API 体系。

### 7.2 `EmbeddingModel` API 核心解析

与旧版的`EmbeddingClient`相比，Spring AI 1.0+ 引入了以 `EmbeddingModel` 为核心的一系列接口和类，设计上与 `ChatModel` 体系保持了高度一致，遵循了“请求-响应”的模式，更为强大和规范。

#### 7.2.1 核心组件概览

`EmbeddingModel` 的 API 围绕以下几个关键类构建，下表对它们的核心角色进行了概括。

| 类/接口名 | 角色定位 |
| :--- | :--- |
| **`EmbeddingModel`** | 统一模型接口 |
| **`EmbeddingRequest`** | 结构化请求对象 |
| **`EmbeddingResponse`**| 结构化响应对象 |
| **`Embedding`** | 单个向量结果 |
| **`EmbeddingOptions`** | 可移植配置项 |
| **`ZhiPuAiEmbeddingOptions`** | 特定模型配置项 |

下面我们对每个组件进行详细的讲解：

  * **`EmbeddingModel`**: 这是所有 Embedding 操作的**统一入口**。无论我们后端使用的是 OpenAI、智谱 AI 还是本地的 Ollama 模型，我们的业务代码（如 `Service` 层）都只与这个标准接口交互，完全屏蔽了底层实现的差异，这是 Spring AI 可移植性的核心体现。

  * **`EmbeddingRequest`**: 这是一个结构化的请求对象，用于封装一次完整的向量化请求。它主要包含两部分内容：需要被向量化的**文本列表** (`List<String>`)，以及本次请求专用的**生成选项** (`EmbeddingOptions`)。

  * **`EmbeddingResponse`**: 同样是一个结构化的响应对象，它封装了模型返回的全部信息。我们可以从中获取一个或多个 `Embedding` 实例，以及可能的元数据（例如本次调用消耗了多少 Token）。

  * **`Embedding`**: 代表**单个输入文本**生成的最终结果。我们可以从这个对象中获取真正的向量数据（一个浮点数或双精度浮点数数组 `float[]` / `double[]`）和它在原始输入列表中的索引。

  * **`EmbeddingOptions`**: 一个可跨模型移植的配置接口，定义了所有模型都应支持的通用参数，例如 `model`（指定模型名称）、`dimensions`（请求的向量维度）等。

  * **`ZhiPuAiEmbeddingOptions`**: 这是特定于**智谱 AI** 的配置实现类，它继承自通用的 `EmbeddingOptions`，并增加了智谱 AI 独有的配置项，例如 `encoding-format`（指定返回的向量是 `float` 还是 `base64` 格式）。

#### 7.2.2 `EmbeddingModel` 接口方法详解

`EmbeddingModel` 接口为了开发者的便利，提供了多个层次的方法。

| 方法签名 | 功能描述 |
| :--- | :--- |
| `EmbeddingResponse call(EmbeddingRequest request)` | 底层核心方法 |
| `float[] embed(String text)` | 单文本向量化 |
| `List<float[]> embed(List<String> texts)` | 多文本批量向量化 |
| `float[] embed(Document document)` | `Document` 对象向量化 |

  * **`call(EmbeddingRequest request)`**: 这是最底层的核心方法。它功能最强大，允许我们通过构建一个 `EmbeddingRequest` 对象来精细控制所有请求参数（例如，运行时动态指定模型或向量维度），并且其返回的 `EmbeddingResponse` 对象包含了最完整的响应信息，包括元数据，便于我们进行日志记录或成本核算。

  * **`embed(String text)`**: 这是**最常用、最便捷**的方法。当我们只需要对单个文本进行快速向量化，并且不关心额外的元数据时，这个方法是我们的首选。它直接接收一个字符串，返回一个浮点数数组，非常直观。

  * **`embed(List<String> texts)`**: 这是一个为性能优化的批量处理方法。当有多个文本需要一次性向量化时，我们应该使用它。它会将整个列表一次性发送给模型 API，相比于在 `for` 循环中多次调用 `embed(String text)`，网络开销更小，效率更高。

  * **`embed(Document document)`**: 这是为 RAG 场景设计的专用方法。Spring AI 使用 `Document` 对象来表示经过处理的文档片段，此方法可以无缝地对 `Document` 对象进行向量化，简化了 RAG 流程中的代码。

### 7.3 实战：构建文件向量化服务

理论讲完，我们立刻动手。本节我们将为 AI-Copilot 项目添加一个全新的功能模块——**“文档向量化工具”**。用户可以通过一个友好的界面上传文本文件（如 `.txt`, `.md`），后端服务接收到文件后，会立即将其内容解析并转换为向量，最后在控制台打印出这个向量的前几位作为演示。

#### 7.3.1 后端实现：文件处理与向量化API

##### **第一步：依赖与配置**

我们的项目已具备 `spring-boot-starter-web`，这已经包含了处理文件上传所需的一切。我们只需要确保 Embedding 模型的配置是正确的。本实战我们选用**智谱 AI** 的 `embedding-3` 模型。

  * **`pom.xml`**: 首先，确保 `spring-ai-starter-model-zhipuai` 依赖已经存在于您的 `pom.xml` 文件中。

    ```xml
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-zhipuai</artifactId>
    </dependency>
    ```

  * **`application.yml`**: 接下来，在 `application.yml` 中确认智谱 AI 的配置。最关键的是通过 `spring.ai.model.embedding` 属性，全局指定使用 `zhipuai` 作为 `EmbeddingModel` 的实现。同时，我们配置好 Spring MVC 的文件上传参数。

    ```yaml
    spring:
      # Spring AI 核心配置
      ai:
        model:
          # 关键：全局指定使用 zhipuai 作为 EmbeddingModel 的实现
          embedding: zhipuai
        zhipuai:
          # 强烈推荐使用环境变量来管理 API Key
          api-key: ${ZHIPU_API_KEY}
          embedding:
            options:
              # 指定默认使用的嵌入模型
              model: embedding-3

      # Spring MVC 文件上传配置
      servlet:
        multipart:
          enabled: true           # 允许上传
          max-file-size: 10MB     # 单个文件最大 10MB
          max-request-size: 50MB  # 单次请求总文件最大 50MB
    ```

##### **第二步：创建文件处理服务 (`service/DocumentEmbeddingService.java`)**

这个 Service 负责整个核心逻辑：接收文件 -\> 校验文件 -\> 解析文本 -\> 调用模型 -\> 返回结果。

```java
// src/main/java/com/copilot/aicopilotbackend/service/DocumentEmbeddingService.java
package com.copilot.aicopilotbackend.service;

import com.copilot.aicopilotbackend.exception.BusinessException;
import com.copilot.aicopilotbackend.exception.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * 文档向量化服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentEmbeddingService {

    // 注入标准 EmbeddingModel 接口，而不是任何具体实现
    private final EmbeddingModel embeddingModel;

    // 定义允许上传的文件内容类型列表
    private static final List<String> ALLOWED_CONTENT_TYPES = Arrays.asList(
            "text/plain",       // .txt
            "text/markdown"     // .md
            // 未来可以轻松扩展以支持 "application/pdf" 等
    );

    /**
     * 将上传的文件内容向量化，并返回处理结果信息。
     *
     * @param file 用户上传的文件
     * @return 包含文件名和向量维度信息的成功消息字符串
     */
    public String embedDocument(MultipartFile file) {
        // 步骤1: 验证文件是否有效
        validateFile(file);

        try {
            // 步骤2: 从文件中安全地读取文本内容
            String documentContent = new String(file.getBytes(), StandardCharsets.UTF_8);
            if (!StringUtils.hasText(documentContent)) {
                throw new BusinessException(ErrorCode.INVALID_PARAMETER, "文件内容不能为空。");
            }
            log.info("成功解析文件 '{}', 内容长度: {} 字符", file.getOriginalFilename(), documentContent.length());

            // 步骤3: 调用注入的 EmbeddingModel 进行向量化
            log.info("正在调用 Embedding 模型进行向量化...");
            float[] vector = embeddingModel.embed(documentContent);
            log.info("向量化成功！");

            // 步骤4: 为了演示，在控制台清晰地打印向量的预览信息
            log.info("--- 文件 '{}' 的向量预览 ---", file.getOriginalFilename());
            log.info("维度 (Dimensions): {}", vector.length);
            log.info("向量前5位 (Top 5 elements): {}", Arrays.toString(Arrays.copyOfRange(vector, 0, 5)));
            log.info("------------------------------------");
            
            // 步骤5: 返回一个对用户友好的、包含关键信息的成功消息
            return String.format("文件 '%s' 成功向量化！已生成一个 %d 维的向量。", file.getOriginalFilename(), vector.length);

        } catch (IOException e) {
            log.error("读取文件 '{}' 时发生 I/O 错误", file.getOriginalFilename(), e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "读取文件内容时发生错误，请检查文件是否损坏。");
        } catch (Exception e) {
            log.error("向量化文件 '{}' 时发生未知错误", file.getOriginalFilename(), e);
            // 将异常重新抛出，交由全局异常处理器统一格式化为标准 API 响应
            throw e; 
        }
    }

    /**
     * 内部私有方法，用于校验上传的文件是否符合要求。
     * @param file 待校验的文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException(ErrorCode.MISSING_PARAMETER, "未接收到任何文件，请选择一个文件上传。");
        }
        
        String contentType = file.getContentType();
        log.info("接收到文件 '{}'，内容类型: {}", file.getOriginalFilename(), contentType);

        if (!ALLOWED_CONTENT_TYPES.contains(contentType)) {
            throw new BusinessException(ErrorCode.INVALID_PARAMETER, "不支持的文件类型。目前仅支持 .txt 和 .md 文件。");
        }
    }
}
```

##### **第三步：创建 API 控制器 (`controller/DocumentEmbeddingController.java`)**

这个 Controller 非常简单，它的职责就是暴露一个 `POST` 端点来接收 `multipart/form-data` 格式的文件。

```java
// src/main/java/com/copilot/aicopilotbackend/controller/DocumentEmbeddingController.java
package com.copilot.aicopilotbackend.controller;

import com.copilot.aicopilotbackend.dto.response.ApiResponse;
import com.copilot.aicopilotbackend.service.DocumentEmbeddingService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/documents")
@RequiredArgsConstructor
public class DocumentEmbeddingController {

    private final DocumentEmbeddingService documentEmbeddingService;

    /**
     * 处理文档向量化的API端点
     * @param file 前端通过 'file' 字段上传的文件
     * @return 符合项目规范的、包含成功消息的 ApiResponse
     */
    @PostMapping("/embed")
    public ApiResponse<Map<String, String>> embedDocument(@RequestParam("file") MultipartFile file) {
        String resultMessage = documentEmbeddingService.embedDocument(file);
        // 将成功信息包装成我们项目的标准API响应格式
        return ApiResponse.success(Map.of("message", resultMessage));
    }
}
```

-----
