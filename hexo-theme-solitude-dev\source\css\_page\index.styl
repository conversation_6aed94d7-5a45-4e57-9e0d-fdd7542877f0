@import "_home/home"

@import "category.styl"

@import "tag.styl"

@import "error.styl"

if hexo-config('recent_comments.enable') || (hexo-config('console.enable') && hexo-config('console.recentComment.enable'))
  @import "recentcomment.styl"

if hexo-config('brevity.enable')
  @import "brevity.styl"

if hexo-config('music.enable')
  @import "music.styl"

if hexo-config('google_adsense.enable')
  @import "google.styl"

@import "equipment.styl"

@import "links.styl"

@import "_about/about"

@import "other"

@import "message"

#page h1.page-title
  margin .4rem 1rem 1rem
  text-align center

#page
  background 0 0
  border none
  padding 0
  box-shadow none
  min-height calc(100vh - 464px)

  +maxWidth768()
    padding 0 1rem

  > div:not(.author-content-item)
    animation slide-in .6s .2s backwards

+minWidth768()
  > div:nth-child(1) > a::before
    height: 24px
    width: 44px
    content: ""
    background-size: 38px 24px
    position: absolute
    top: 0
    left: 0

  > a:nth-child(0) > i
    padding-right: 8px

+minWidth768()
  .needEndHide.hide
    bottom -200px !important
    opacity 0

+maxWidth768()
  .needEndHide
    display none!important
