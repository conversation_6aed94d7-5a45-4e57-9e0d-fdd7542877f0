- const { envId, region, option ,accessToken } = theme.twikoo
- const { lazyload, count, use,commentBarrage } = theme.comment

script().
    (() => {
        const getCount = () => {
            const ele = document.querySelectorAll('.twikoo-count')
            if (!ele) return
            twikoo.getCommentsCount({
                envId: '!{envId}',
                region: '!{region}',
                urls: [window.location.pathname],
                includeReply: false
            }).then(res => {
                ele.forEach(item => item.textContent = res[0].count)
            }).catch(err => {
                console.error(err)
            })
        }
        const init = () => {
            twikoo.init(Object.assign({
                el: '#twikoo-wrap',
                envId: '!{envId}',
                region: '!{region}',
                path: window.location.pathname,
                onCommentLoaded: () => {
                    GLOBAL_CONFIG.lightbox && utils.lightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
                }
            }, !{JSON.stringify(option)}))

            !{count ? ' && getCount()' : ''}
            sco.owoBig({
                body: '.OwO-body',
                item: '.OwO-items li'
            })

            !{commentBarrage} && barrageTwikoo()
        }

        const loadTwikoo = () => {
            if (typeof twikoo === 'object') setTimeout(init,0)
            else utils.getScript('!{url_for(theme.cdn.twikoo)}').then(init)
        }

        if ('!{use[0]}' === 'Twikoo' || !{lazyload}) {
            if (!{lazyload}) utils.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
            else loadTwikoo()
        } else {
            window.loadTwoComment = loadTwikoo
        }
    })()

if commentBarrage
    script.
        async function barrageTwikoo() {
            await fetch("!{envId}", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    event: "COMMENT_GET",
                    accessToken: "!{accessToken}",
                    url: window.location.pathname
                })
            }).then(async res => {
                if (!res.ok) throw new Error("HTTP error! status: " + res.status)
                const data = await res.json();
                const init = () => {
                    initializeCommentBarrage((data.data).map(item => Object.assign({
                        content: item.comment,
                        nick: item.nick,
                        mailMd5: item.mailMd5,
                        id: item.id
                    })))
                }
                if (typeof initializeCommentBarrage === "undefined") await utils.getScript('!{url_for(theme.cdn.commentBarrage)}').then(init)
                else init()
            }).catch(error => console.error("An error occurred while fetching comments: ", error))
        }