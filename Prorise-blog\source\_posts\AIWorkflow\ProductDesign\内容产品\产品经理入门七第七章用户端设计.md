---
title: 产品经理入门（七）：第七章：用户端设计
categories:
  - 产品经理
tags:
  - 产品经理教程
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp'
comments: true
toc: true
ai: true
abbrlink: 51587
date: 2025-07-20 22:13:45
---

# 第七章：用户端设计

在这一章，我们将进入一个完整的实战设计流程。我们将从用户第一次打开App的瞬间开始，一步步地设计出内容产品用户端的每一个核心模块，将我们之前学到的所有理论知识，全部应用到实践中。

## 7.1 引导页 & 启动页 & 闪屏页

当一个新用户满怀期待地下载并首次打开我们的App时，我们有且仅有一次机会，来给他留下一个完美的“第一印象”。这个第一印象，通常是由三个不同的页面共同构成的。我必须清晰地辨别它们，并为它们设计好各自的使命。

### 7.1.1 学习目标

在本节中，我的目标是，带大家清晰地区分**引导页、启动页、闪屏页**这三个极易混淆的概念。我将拆解它们各自的定义和核心作用，确保我们在产品设计中，能为用户的“第一次”，做出最合理的安排。

### 7.1.2 引导页设计

![image-20250720211813703](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720211813703.png)

#### 1. 引导页概念

**引导页（Onboarding Screens）**，是我为**首次**安装并打开我们App的用户，专门准备的一套“欢迎手册”。它通常是由3-5张可滑动的页面构成。

#### 2. 引导页作用

我设计引导页，通常是为了达成以下三个目的：
* **产品功能介绍**：用最简洁的图文，向用户展示我们App最核心、最吸引人的1-3个功能。
* **产品亮点说明**：传达我们产品的核心价值主张，告诉用户“我们是谁，我们能为你带来什么独特的好处”。
* **推广品宣**：通过精美的设计和文案，在用户接触产品的最初几秒钟，就建立起我们产品的品牌调性和情感链接。

### 7.1.3 启动页设计

![image-20250720211951423](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720211951423.png)

#### 1. 启动页概念

**启动页（Launch Screen）**，是用户**每一次**打开App时，都会看到的第一个页面。它是一个短暂展示的静态页面，主要目的是在App后台加载资源时，给用户一个优雅的等待界面，避免出现白屏或黑屏。

#### 2. 启动页作用

启动页的作用非常纯粹，主要是品牌和信息的展示：
* **品牌宣传**：在页面中心，清晰地展示我们产品的Logo和Slogan（品牌口号），在每一次启动中，反复加深用户的品牌认知。
* **版权声明与版本号**：在页面底部，通常会标注公司的版权信息和当前App的版本号。

### 7.1.4 闪屏页设计

#### 1. 闪屏页概念

**闪屏页（Splash Screen / Splash Ad）**，是一个可选的、通常在**启动页之后，首页之前**出现的页面。它本质上是一个全屏的、通常带有“跳过”按钮的广告或运营活动页面，一般会展示3-5秒。

#### 2. 闪屏页作用

闪屏页的作用完全是商业化和运营导向的：
* **广告曝光**：“开屏广告”是App中非常重要的一个广告位，能为我们带来商业收入。
* **活动推广**：我可以用它来推广平台级的、重要的运营活动，为活动进行预热和导流。
* **内容推荐**：对于内容产品，我也可以用它来推荐平台S级的重磅内容，吸引用户点击。

### 7.1.5 本节小结

这三个页面，共同构成了用户打开App的“三部曲”。我将它们的核心区别，总结在了下面的表格里，来帮助我们加深记忆。

| **页面类型** | **出现时机** | **核心目的** | **我的设计思考** |
| :--- | :--- | :--- | :--- |
| **引导页** | **仅在首次**安装启动时 | **教育用户**、介绍核心功能与价值 | 内容要少而精，突出核心亮点，让用户快速了解。 |
| **启动页** | **每一次**启动时（加载期间） | **品牌展示**、传递Logo与Slogan | 界面要极简、干净，加载速度一定要快。 |
| **闪屏页** | **每一次**启动时（加载后，首页前） | **商业运营**、广告曝光与活动推广 | 必须提供清晰的“跳过”按钮，不能强制用户观看。 |

我的设计哲学是，用户的最终目的是进入App使用核心功能。因此，这个“开门”的过程，必须尽可能地快速、流畅。任何不必要的停留，都可能造成用户的流失。


---

## 7.2 用户端设计思路

在动手画任何一个具体页面之前，我必须先建立起整个产品的“设计蓝图”。这个蓝图，就是我的用户端设计思路。它是一个从宏观到微观，从战略到执行的逻辑推演过程，能确保我后续所有的设计决策，都是有据可依、浑然一体的。

### 7.2.1 学习目标

在本节中，我的目标是带大家完整地走一遍这个“设计蓝图”的推演过程。我们将学习如何从一个**需求背景**出发，提炼出产品的核心**角色**，分析他们的**用户场景**，并最终推导出我们需要设计的**核心功能**。

### 7.2.2 需求背景分析

![image-20250720212444403](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212444403.png)

我们假设，通过前期的市场分析和需求收集，我们已经为我们即将开发的“图文类内容产品”，确定了V1.0版本的四大核心策略，这就是我们一切设计的出发点和“宪法”。

1.  **生产模式**：我们将采用 **PGC (专业生产内容) + UGC (用户生产内容)** 的双引擎模式，来保证内容的专业度和丰富度。
2.  **审核方式**：我们将采用 **自动审核 + 人工审核** 的方式，来平衡审核的效率和准确性。
3.  **分发方式**：我们将采用 **算法分发 + 订阅分发** 的方式，来兼顾用户发现新内容和关注老作者的需求。
4.  **消费模式**：在V1.0版本，我们将采用**免费消费**的模式，以最大化地吸引早期用户。

### 7.2.3 需求分析（角色提炼）

![image-20250720212511146](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212511146.png)

基于上述的背景，我首先要提炼出，在这个生态中，到底有哪几类“玩家”？

* 因为有UGC，所以必然有内容的**生产者**，我称之为“**自媒体**”。
* 因为有内容消费，所以必然有内容的**消费者**，我称之为“**普通用户**”。
* 因为有审核和分发，所以必然有**管理者和运营者**，我称之为“**平台**”。

在本章，我们聚焦的用户端设计，主要就是为了服务好“**普通用户**”和“**自媒体**”这两大核心外部角色。

### 7.2.4 需求分析（用户场景）

![image-20250720212618884](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212618884.png)

明确了我们要服务的角色之后，我开始思考：这两类用户，在使用我们产品的过程中，会经历哪些最核心、最典型的场景？我将它们归纳为四大场景：

1.  **获取身份**：无论是想看个性化推荐的“普通用户”，还是想发表内容的“自媒体”，他们都需要先在我们的平台上，拥有一个自己的身份。这就是**注册/登录**的场景。
2.  **发布内容**：“自媒体”角色的核心诉求，就是将自己的图文作品发布到平台上，与大家分享。这就是**内容发布**的场景。
3.  **浏览&互动内容**：“普通用户”的核心诉求，是发现、阅读自己感兴趣的文章，并对内容和作者表达自己的喜好。这就是**内容消费与互动**的场景。
4.  **个人中心**：所有用户，都需要一个地方来管理自己的个人信息、查看自己发布或收藏过的内容。这就是**个人中心管理**的场景。

### 7.2.5 用户端核心功能设计

![image-20250720212728816](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212728816.png)

最后一步，也是从“分析”到“设计”最关键的一步，就是将上述的用户场景，映射为我们产品需要提供的**核心功能模块**。

* 为了支撑“获取身份”场景，我们需要设计 **注册登录** 功能。
* 为了支撑“发布内容”场景，我们需要设计 **内容发布页**。
* 为了支撑“浏览&互动内容”场景，我们需要设计 **内容列表页** 和 **内容详情页**。
* 为了支撑“个人中心”场景，我们需要设计 **个人中心** 模块。

这五大核心功能，就构成了我们内容产品用户端的“骨架”。在接下来的小节中，我们将逐一地，对这个骨架进行“添肉画皮”，完成每一个模块的详细设计。

### 7.2.6 本节小结

我将这个设计思路的推演过程，总结为下面这张表格：

| **思考步骤** | **核心产出** | **我的目的** |
| :--- | :--- | :--- |
| **背景分析** | 四大产品决策 | 确立项目的“宪法”，是所有设计的最高准则。 |
| **角色提炼** | 三大核心角色 | 明确我们到底要为谁服务。 |
| **场景分析** | 四大核心场景 | 梳理出用户的完整旅程和核心诉求。 |
| **功能设计** | 五大核心功能 | 将用户诉求，转化为具体、可设计的产品模块。 |



---

## 7.3 注册登录

在我们的设计思路中，“获取身份”是所有用户要经历的第一个场景。**注册登录**功能，就是支撑这个场景、我们为用户开启的“第一扇门”。

在我看来，这扇门的设计至关重要。一个好的注册登录体验，应该像酒店的自动门一样，让用户安全、无感、顺畅地通过；

而一个糟糕的设计，则像一道生锈的铁门，会在用户进入前，就把他们拒之门外。

从本质上讲，我设计的所有注册登录流程，都是为了完成两件事：**身份识别**（你是谁？）和**门槛验证**（如何证明你是你？）。

### 7.3.1 学习目标

在本节中，我的目标是带大家掌握现代App中最主流的三种注册登录方式的设计。我们将深入分析它们的实现逻辑、优缺点，并探讨如何通过安全验证设计，来保障我们产品的“大门”既方便又安全。

### 7.3.2 注册登录的目的

![image-20250720213340787](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720213340787.png)

在设计之前，我总会先思考：我们为什么需要用户注册登录？

* **从用户的角度：为了“获得身份”**
    一个注册后的身份，意味着用户在我们的产品里，有了一个专属的“数字资产”账户。这能帮助他们：
    * **记录跟随**：可以保存自己的浏览历史、收藏、发布的文章等。
    * **获得个性化服务**：可以接收到我们为他量身定制的内容推荐。
    * **积累个人资产**：可以拥有自己的积分、等级、虚拟财产。

* **从平台的角度：为了“区分用户”**
    用户的注册，能帮助我们平台更好地运营：
    * **精细化运营**：可以针对不同用户群体，推送不同的内容或活动。
    * **信息记录**：可以更好地掌握平台的用户构成和自媒体信息。
    * **信息分发**：能够针对用户的身份和喜好，进行更精准的内容分发。

### 7.3.3 常见注册登录方式介绍

明确了目的，我们来看实现“门槛验证”的三种主流方式。

#### 1. 手机号+验证码

![image-20250720215110314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720215110314.png)

这是目前国内互联网产品最主流、最便捷的方式，它的核心逻辑是“**手机在手，身份我有**”。

* **核心逻辑**：将注册和登录合二为一。用户输入手机号，接收并回填验证码，系统验证通过后，若该手机号未注册，则自动为其创建账户并登录；若已注册，则直接登录。
* **优点**：**方便快捷**，用户无需记忆复杂的密码，操作路径最短。
* **缺点**：有一定**账户信息风险**（如手机号丢失），且平台需要承担较高的**短信成本**。

#### 2. 手机号+验证码+密码

![image-20250720215834551](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720215834551.png)

这是最传统，也是账户体系最稳固的一种方式。

* **核心逻辑**：注册和登录是分离的。用户首次使用时，需要通过“手机号+验证码”验证身份，并**设置一个密码**来完成注册。后续登录时，主要使用“手机号+密码”的方式。
* **优点**：**安全性更高**，登录不受运营商短信通道影响。
* **缺点**：注册流程更长，**操作成本相对较高**，可能会流失一部分没有耐心的用户。

#### 3. 第三方注册登录

![image-20250720220120919](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220120919.png)

这是借助“巨人”的力量，让用户快速进入我们产品的方式。

* **核心逻辑**：用户授权我们App，去获取他在某个第三方大平台（如微信、QQ、微博）上的基本公开信息（如昵称、头像）作为身份标识，从而完成注册或登录。
* **优点**：**门槛极低**，用户一键授权即可，体验非常流畅，能有效提升新用户的注册转化率。
* **缺点**：我们能**获取的用户信息非常有限**，不利于后续的精细化运营。同时，账户的安全性依赖于第三方平台。

### 7.3.4 注册登录安全验证设计

![image-20250720220200195](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220200195.png)

为了防止被机器人恶意、批量地注册或登录（俗称“刷接口”），也为了保护我们宝贵的短信费用，我必须在注册登录流程中，加入一道“**安全验证**”的屏障。

这道屏障，通常出现在用户输入完手机号、点击“获取验证码”按钮之后。常见的验证形式有：
* **智能验证**（如“我不是机器人”勾选框）
* **文字点选验证**（要求用户点选图中指定的文字）
* **拼图验证**（要求用户拖动滑块完成拼图）

此外，对于已注册用户，为了提供更便捷的登录体验，我还会设计支持**指纹、面容ID**等生物识别验证方式。

### 7.3.5 本节小结

在实际设计中，我很少只提供一种登录方式，而是采用组合策略。我将这三种方式的选择思路总结如下：

| **登录方式** | **核心逻辑** | **我的设计策略** |
| :--- | :--- | :--- |
| **手机号+验证码** | 便捷性优先 | 作为默认和首选的登录方式，最大化地降低用户操作成本。 |
| **手机号+验证码+密码** | 安全性优先 | 作为一种可选的账户升级或安全设置，让注重安全的用户可以绑定密码。 |
| **第三方登录** | 借力，信任度优先 | 作为一种重要的补充登录方式，并排放在主登录按钮下方，给用户多一种便捷选择。 |





---

## 7.4 内容发布

当我们的“自媒体”用户，也就是内容创作者，想要把他们的想法和作品分享出来时，他们就需要一个强大、易用的**内容发布**功能。这是连接“创作者”与“平台”的桥梁，这个桥梁的体验，直接决定了我们平台内容的数量和质量。

### 7.4.1 学习目标

在本节中，我的目标是带大家深入研究内容发布页的设计。我们将探讨图文内容常见的两种展现形式，并拆解这两种形式下，内容发布页各自的设计要点和核心元素。

### 7.4.2 图文内容的展现形式

![image-20250720220845581](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220845581.png)

在设计发布页之前，我首先要明确，我们平台的内容，最终将以什么样的形式呈现给用户。这通常决定了我们发布器的形态。最常见的两种形式是：

1.  **图文分离展现形式**：图片和文字是分开展示的。通常是上方为图片（或视频），下方为独立的、大段的文字描述。
2.  **图文混排展现形式**：图片可以自由地插入到文章的任意位置，形成我们常说的“富文本”效果。

### 7.4.3 内容发布页的设计

#### 1. 图文分离形式设计要点

![image-20250720221014583](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221014583.png)

这种形式的发布器，设计上更追求简洁、快速。我通常会关注以下几个设计要点：
* **核心元素**：必须包含**文本输入区**、**图片/视频上传入口**（通常是一个“+”号按钮），以及**发布/取消按钮**。
* **字符长度限制**：需要明确告知用户，正文最多可以输入多少字。
* **图片数量限制**：需要明确告知用户，最多可以上传多少张图片。
* **发布状态变化**：当用户未输入任何内容时，“发布”按钮应为置灰不可用状态，以避免发布空内容。
* **草稿箱功能**：当用户意外退出时，我需要设计一个草稿箱功能，自动保存用户未发布的内容，防止心血白费。

#### 2. 图文混排形式设计要点

![image-20250720221124889](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221124889.png)

这种形式的发布器，功能更强大，类似于一个移动端的“Word编辑器”。除了包含图文分离形式的所有要点外，我还会特别关注：
* **标题输入**：通常会提供一个独立的标题输入框，并有字数限制。
* **富文本编辑**：支持在正文的任意位置插入图片或视频，并提供基础的排版功能，如加粗、对齐等。
* **发布与取消**：这两个按钮必须始终清晰可见。
* **图片/视频上传**：提供清晰的上传入口和进度反馈。

---

## 7.5 内容列表及内容详情

当内容被成功发布后，它就需要被呈现给“普通用户”。这个呈现的过程，主要由两个核心页面来承载：**内容列表页**和**内容详情页**。

### 7.5.1 学习目标

在本节中，我的目标是带大家掌握内容列表页和详情页的设计精髓。我们将学习如何设计一个信息丰富、吸引眼球的列表页，一个沉浸、易读的详情页，以及如何通过巧妙的互动设计，来提升用户的参与感和社区的活跃度。

### 7.5.2 内容列表页设计

![image-20250720221204736](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221204736.png)

内容列表页，是我为用户打造的“内容超市”。它的核心使命，是让用户能在这个超市里，快速地、高效地发现自己可能感兴趣的“商品”（内容）。

#### 1. 列表页元素设计

一个标准的内容卡片，通常包含三类信息：
* **内容基本信息**：标题、封面图（如果有）、内容摘要。
* **发布者信息**：作者的头像、昵称。
* **互动信息**：点赞数、评论数、分享数等。

#### 2. 列表页设计要点

* **图文权重分配**：我需要根据产品定位，来决定图片和文字的权重。左侧的列表形式，更注重文字信息的传递；而右侧的瀑布流形式，则更强调图片的视觉冲击力。
* **内容排列规则**：列表的排序规则是什么？是按照发布时间倒序？还是按照热度排序？我必须定义清晰的规则。

### 7.5.3 内容详情页设计

当用户在列表页对某项内容产生兴趣并点击后，就进入了**内容详情页**。这是用户进行沉浸式阅读和深度消费的核心场所。

#### 1. 图文混排详情页设计要点

![image-20250720221259547](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221259547.png)

* **核心元素**：必须清晰地展示**导航栏、内容标题、作者信息、发布时间、正文（图文混排）**。
* **设计要点**：
    * **支持分段**：长文章必须分段，以提升可读性。
    * **图片可交互**：图片通常需要支持点击查看大图。
    * **视觉权重**：正文的视觉权重最高，其他辅助信息（如作者、时间）则相对弱化。
    * **敏感字过滤**：需要对评论区等UGC内容进行敏感词过滤。

#### 2. 图文分离详情页设计要点

![image-20250720221401530](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221401530.png)

* **核心元素**：与混排类似，但**图片区**和**正文区**是明确分开的。
* **设计要点**：与混排页的设计要点基本一致，同样需要关注分段、图片交互、视觉权重和敏感字过滤。

### 7.5.4 内容互动设计

![image-20250720221435264](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221435264.png)

#### 1. 互动行为的重要性

在我看来，互动是内容产品的“灵魂”。它不仅仅是一些按钮，而是连接**用户、作者、内容、平台**四方的桥梁。
* 对于**消费者**，互动是表达情绪的方式。
* 对于**生产者**，互动是对自己创作的激励。
* 对于**内容**，互动是区分其质量和热度的标尺。
* 对于**平台**，互动是口碑营销和用户监督的手段。

#### 2. 常见互动行为设计要点

![image-20250720221636401](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221636401.png)



* **核心互动**：**点赞**和**分享**，是用户成本最低、我们最希望用户去做的行为。因此，这两个功能的按钮，在页面上必须非常突出和便捷。
* **主要分享渠道**：分享功能，我通常会优先支持微信、朋友圈、QQ和微博这几个主流渠道。
* **次要互动**：对于一些不常用的功能，比如**删除**（作者可见）、**举报**（用户可见），我通常会将它们收纳在右上角的“更多”按钮中，避免干扰主界面的信息。

### 7.5.5 本节小结

| **页面/模块** | **我的核心设计思考** |
| :--- | :--- |
| **内容发布页** | 根据**图文分离/混排**的展现形式，来决定发布器的复杂度和设计要点。 |
| **内容列表页** | 核心是**信息卡片**的设计，需要平衡好图文权重和信息密度。 |
| **内容详情页** | 核心是提供**沉浸、易读**的消费体验，并引导用户进行互动。 |
| **内容互动设计** | **突出核心互动（点赞/分享）**，将次要互动收纳起来，保持界面简洁。 |



---

## 7.6 内容分发

我们的内容，已经通过发布功能进入了平台的“内容库”，详情页也为用户提供了沉浸式的消费体验。但现在，一个核心问题摆在面前：**在海量的内容库里，我们应该把哪些内容，在什么时候，以什么方式，呈现在用户面前？**

这就是内容分发系统需要解决的问题。它是连接“海量内容”与“个性化用户”的桥梁。

### 7.6.1 学习目标

在本节中，我的目标是带大家深入了解内容产品背后最主流的三种分发模式的设计。我们将重点拆解**算法分发**的核心三要素，学习**用户画像**和**标签**的概念，并了解**热度排序**和**订阅分发**的设计逻辑。

### 7.6.2 算法分发设计

![image-20250721084124586](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084124586.png)

#### 1. 算法分发定义与要素

**算法分发**，是我认为的现代内容产品的“发动机”。我把它定义为：**一套能根据用户数据，自动地、个性化地为用户推荐其可能感兴趣的内容的系统**。

要让这台发动机运转起来，我必须为它准备好三个核心要素：
* **用户画像 (User Persona)**：深入地理解我的用户，知道“他是谁，他喜欢什么”。
* **内容画像 (Content Profile)**：深入地理解我的内容，知道“它是什么，它讲了什么”。
* **算法模型 (Algorithm Model)**：建立一套高效的匹配和排序规则，将最合适的内容，推荐给最合适的用户。

#### 2. 用户画像介绍

算法分发的前提，是了解用户的喜好。**用户画像**，就是我用来“了解”用户的工具。

我把它定义为：**根据用户各维度的真实数据，抽象出的一个标签化的用户模型**。
我通常会从以下四个维度，来为用户构建画像：
* **基本属性**：如姓名、性别、年龄、地域等。
* **社会属性**：如职业、收入、公司、文化等。
* **行为属性**：这是最重要的，包括用户在我们产品里的登录、活跃、评论、点赞等一切行为。
* **消费属性**：如果产品有付费点，还包括用户的消费金额、次数等。

#### 3. 标签分类及应用

![image-20250721084235993](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084235993.png)

用户画像和内容画像，都是通过“**标签**”来具体实现的。标签，就是对目标的量化标识和描述。我们的核心工作，就是**为内容和用户，打上同一套标签体系**，从而实现精准匹配。

我把用户标签，分为两大类：
* **静态标签**：指那些在较长时间内，保持稳定不变的标签，通常具有“先天性”。比如用户的**性别、年龄、星座、地域**等。

![image-20250721084734425](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084734425.png)



* **动态标签**：指根据用户的**实时操作行为**，动态变化的标签。比如用户刚刚搜索了什么、点赞了什么、购买了什么。这些动态标签，更能反映用户当下的、即时的兴趣。一个完整的用户画像，是静态标签和动态标签的结合体。

![image-20250721084743710](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084743710.png)

#### 4. 算法分发设计逻辑

![image-20250721084704681](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084704681.png)

有了用户画像和内容画像，我的算法推荐逻辑通常遵循四步走：
1.  **权重设置**：我会为用户的不同行为，赋予不同的权重。比如，“分享”行为的权重，一定高于“点赞”。
2.  **贴标签**：系统自动为内容和用户打上标签。
3.  **匹配推荐**：算法模型开始工作，为用户匹配出，与他自身标签相符的内容。
4.  **排序**：对所有匹配出的内容，根据一个“热度/质量分”公式，进行排序，决定最终呈现给用户的顺序。

我们来看一个具体的**算法分发规则案例**：

![image-20250721084719114](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084719114.png)

* **计算维度与分值**：我们可以定义一个内容的总分值公式，比如：`内容总分 = 分享数*2 + 评论数*2 + 点赞数*1 + 收藏数*1`。
* **推送规则**：
    1.  优先匹配该用户标签权重最高的TOP5的标签内容。
    2.  根据内容总分值排序，分页推送，每页8条。
    3.  下拉刷新时，推送新产生的内容。
    4.  已经推荐过的内容，不再重复推荐。




---

### 7.6.3 热度排序设计

前面我们谈的算法分发，完全是根据“人-内容”的个性化匹配进行推荐的。但这里面可能会存在一个问题：如果推荐出的内容本身质量不高怎么办？

为了过滤掉低质量内容，并让用户感知到“大家都在看什么”，我需要引入一种全局性的排序机制——**热度排序**。

#### 1. 热度排序逻辑与设计要点

![image-20250721085440264](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085440264.png)

我设计热度排序的逻辑，通常遵循四步走：
1.  **梳理维度**：首先，我会梳理出所有能够反映内容质量和受欢迎程度的用户行为维度，比如**分享、点赞、评论、收藏**等。
2.  **权重设置**：其次，我会基于这些维度，为不同的行为设置不同的权重。比如，我认为“分享”和“评论”比“点赞”更能代表用户的认可度，因此会给予更高的分值。
3.  **标准计算**：然后，系统会根据用户产生的实时行为数据，套入我们设定的计分公式，为每一篇内容动态地计算出一个“热度分值”。
4.  **排序**：最后，系统根据计算出的“热度分值”进行排序。这里需要特别注意，为了**避免热度榜被少数爆款内容长期霸占（固化）**，我通常会在公式中加入“时间衰减”因子，让新发布的内容有更多的曝光机会。

#### 2. 热度排序规则示例

![image-20250721085555753](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085555753.png)

我们来看一个更具体、更复杂的规则案例，它巧妙地融合了**个性化推荐**和**热度排序**。

* **内容总分值公式**：`S = 分享数(A)*2 + 点赞数(B)*1 + 评论数(C)*2 + 收藏数(D)*1`
* **推送规则**：
    * **规则1**：筛选出**24小时内**发布的所有内容。
    * **规则2**：筛选出**24小时至72小时前**发布的内容中，热度分值**S>=30**的全部内容。
* **排序与分发逻辑**：
    1.  在信息流中，优先推送满足“规则1”的内容（确保新鲜度），按发布时间由近到远排列；当“规则1”的内容不足时，再推送满足“规则2”的内容（补充高质量老内容），按热度分值由高到低排列。
    2.  每次下拉刷新时，推送新产生的内容，每次最多推送8条。

### 7.6.4 订阅分发设计

#### 1. 订阅分发的核心逻辑

![image-20250721085717235](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085717235.png)

**订阅分发**，是将内容的选择权，完全交还给用户的一种方式。它的逻辑非常简单：“**我只看我关注的人发布的内容**”。这是一种基于“人”的、强关系的分发模式。

它的核心业务流程是：自媒体发布内容 → 用户在看到后选择关注该自媒体 → 系统此后会自动将该自媒体的新内容，分发到该用户的“关注”信息流中 → 用户随时可以查看。

#### 2. 订阅分发实现的关键功能

![image-20250721085801010](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085801010.png)

要实现这个逻辑，我的设计通常会围绕以下三个要点展开：

* **连接 (Connection)**：我必须为用户和作者建立“连接”的桥梁。这通常是在作者的个人主页、内容详情页等位置，提供一个清晰的“**关注**”功能按钮。
* **推荐内容 (Content Delivery)**：我需要为用户提供一个专门的消费场所，也就是一个独立的“**关注**”信息流（Feed）。这个信息流里，只包含用户已关注作者发布的内容。
* **排序 (Ranking)**：这个信息流的排序规则通常非常简单，就是严格按照**内容发布的时间倒序排列**，确保用户看到的永远是最新的内容。

### 7.6.5 本节小结

我将这三种核心的分发方式总结如下，在我的产品设计中，我通常会将它们组合使用，来满足用户不同的内容发现需求。

| **分发方式** | **核心逻辑** | **我的设计要点** |
| :--- | :--- | :--- |
| **算法分发** | **人-内容匹配** | 定义清晰的用户画像、内容标签、推荐与排序规则<br>`内容总分 = 分享数*2 + 评论数*2 + 点赞数*1 + 收藏数*1`。 |
| **热度排序** | **内容热度值计算** | 定义合理的热度计算公式，并考虑时间衰减，避免榜单固化。<br>S = 分享数(A)*2 + 点赞数(B)*1 + 评论数(C)*2 + 收藏数(D)*1 |
| **订阅分发** | **用户主动关注** | 设计好关注/取关功能，并提供独立的“关注”信息流。 |





---

## 7.7 个人中心

当用户在我们的产品里消费、互动、创作，留下了一系列数字足迹之后，他们需要一个“家”，来安放和管理这些属于自己的信息和资产。这个“家”，就是**个人中心**。

对我来说，个人中心是提升用户归属感和粘性的关键模块，它承载了用户的个人身份，也聚合了产品中与“个人”相关的各种高阶功能。

### 7.7.1 学习目标

在本节，我的目标是带大家掌握个人中心模块的完整设计。我们将学习如何设计用户的“名片”——**个人资料页**，并重点拆解个人中心页在**登录与未登录**两种状态下的差异化设计，以及其中常见的功能模块应该如何组织。

### 7.7.2 个人资料页设计

![image-20250721101941163](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721101941163.png)

个人资料页，是用户在我们的产品里，对外展示自己形象的“个人名片”。它的核心是允许用户自定义和编辑自己的个人信息。

#### 1. 常见个人信息展示

我设计个人资料页时，会根据产品定位，来决定需要提供哪些信息字段。对于一个内容产品，最常见的核心字段包括：
* **头像**：用户最具识别性的标识，支持用户从相册上传或拍照。
* **昵称**：用户在社区中行走江湖的“代号”。
* **简介**：一段个性化的自我介绍。
* **性别**
* **生日**
* **地区**

在设计注册流程时，我有一个重要原则：**渐进式信息收集**。即，在最初的注册环节，我只要求用户提供最核心的信息（比如仅需要手机号验证），而将这些详细的个人资料，引导用户在后续有空时，再来个人中心慢慢完善。这能最大化地降低新用户的注册门槛。

### 7.7.3 个人中心常见功能

个人中心这个页面，它的设计比较特殊，因为我必须同时考虑“游客”和“主人”两种完全不同的状态。

#### 1. 登录与未登录状态区别

* **未登录状态**
    当用户未登录时，个人中心这个页面的核心设计目标只有一个：**引导用户去登录或注册**。
    正如案例图所示，此时的页面，我会隐藏掉所有个性化的信息和数据，用一个通用的图标和提示文案（如“点击登录”），来占据视觉中心。大部分功能入口（如“我的收藏”、“历史记录”）也会被隐藏或置灰，用户点击后，会直接跳转到登录页面。

* **登录状态**
    当用户登录后，页面则会完全“变身”为他专属的个人空间。此时的设计核心，是**清晰的个人信息展示**和**便捷的功能入口聚合**。页面的顶部，会展示用户的头像、昵称和核心数据（如作品数、关注数、粉丝数），下方则会罗列出所有与他相关的功能。

#### 2. 常见功能模块介绍

对于登录后的用户，我会把个人中心的功能入口，按照相关性进行逻辑分组，让用户能快速找到自己想要的功能。

* **核心资产类**：这是用户最关心的，他们在我们平台沉淀下的“数字资产”。通常包括：
    * **我的收藏**
    * **浏览历史**
    * **我的作品**（针对创作者）

* **消息与互动类**：
    * **消息通知**（包括系统通知、评论、点赞等）

* **账户与安全类**：
    * **实名认证**
    * 账号与安全设置

* **App通用类**：
    * **用户反馈**
    * **系统设置**（里面通常还包含“关于我们”、“退出登录”等）

### 7.7.4 本节小结

| **模块** | **我的核心设计思考** |
| :--- | :--- |
| **个人资料页** | 提供**头像、昵称、简介**等基础字段的编辑功能，遵循**渐进式**信息收集原则。 |
| **个人中心（未登录）**| 设计核心是**引导登录/注册**，隐藏个性化信息，简化功能入口。 |
| **个人中心（已登录）**| 设计核心是**个人信息展示**和**功能入口聚合**，将功能按逻辑分组（如资产类、账户类、通用类）。 |

---

## 7.8 本章总结

到这里，我们已经完整地设计出了一个内容产品用户端的所有核心模块。让我们最后回顾一下本章的整个设计旅程：

* **开门三板斧**：我们首先设计了`引导页`、`启动页`和`闪屏页`，为用户打造了完美的“第一印象”。
* **确立设计思路**：我们通过`背景分析`→`角色提炼`→`用户场景`→`核心功能`的推演，确立了整个产品的设计“骨架”。
* **设计核心模块**：我们逐一设计了`注册登录`、`内容发布`、`内容列表与详情`、`内容分发`和`个人中心`这几个核心功能模块，为骨架“添上了血肉”。

通过这一章的实战，我们已经将之前学到的所有理论，都转化为了具体、可视的产品设计方案。




---