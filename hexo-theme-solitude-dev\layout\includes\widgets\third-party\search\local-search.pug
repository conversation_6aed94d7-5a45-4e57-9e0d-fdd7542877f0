#local-search
    div.search-dialog
        nav.search-nav
            span.search-dialog-title=__('nav.search')
            button.search-close-button
                i.solitude.fas.fa-xmark
        div.search-wrap
            div.search-box
                input.search-box-input#search-input(type="text", autocomplete="off", spellcheck="false", autocorrect="off", autocapitalize="off", placeholder=__('search.placeholder'))
            div#search-results
                #search-hits
                    each tag in theme.search.tags || []
                        a.tag-list(href='javascript:void(0);' onclick="pjax.loadUrl('" + url_for('/tags/' + tag) + "/')")=tag
            div#search-pagination
            div#search-tips
    div#search-mask