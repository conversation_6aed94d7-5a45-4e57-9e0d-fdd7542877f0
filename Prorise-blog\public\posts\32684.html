<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理进阶（七）：第七章：售后管理 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理进阶（七）：第七章：售后管理"><meta name="application-name" content="产品经理进阶（七）：第七章：售后管理"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理进阶（七）：第七章：售后管理"><meta property="og:url" content="https://prorise666.site/posts/32684.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第七章：售后管理在我看来，用户下单付款，绝不意味着我们服务的结束，恰恰相反，它开启了我们与用户之间，一段更需要“信任”来维系的长期关系。 如何处理好用户在购后遇到的种种问题，如何设计一套公平、清晰、高效的售后流程，直接决定了我们平台的口碑和用户的复购率。 7.1 售后场景构建在设计任何具体的售后功能"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta name="description" content="第七章：售后管理在我看来，用户下单付款，绝不意味着我们服务的结束，恰恰相反，它开启了我们与用户之间，一段更需要“信任”来维系的长期关系。 如何处理好用户在购后遇到的种种问题，如何设计一套公平、清晰、高效的售后流程，直接决定了我们平台的口碑和用户的复购率。 7.1 售后场景构建在设计任何具体的售后功能"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/32684.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理进阶（七）：第七章：售后管理",postAI:"true",pageFillDescription:"第七章：售后管理, 7.1 售后场景构建, 1. 不同角色的关注点, 2. 核心售后场景提炼, 3. 售后职责与需求提取, 7.2 售后流程分析, 1. 场景一：待付款取消订单, 2. 场景二：待发货取消订单, 3. 场景三：待收货x2F已完成 - 退款退货, 4. 场景四：待收货x2F已完成 - 申请换货, 5.商家端与平台端的售后管理页面, 7.3 交易纠纷处理, 1. 平台介入的原则与时机, 2. 交易纠纷处理流程与功能设计, 3. 证据链条设计第七章售后管理在我看来用户下单付款绝不意味着我们服务的结束恰恰相反它开启了我们与用户之间一段更需要信任来维系的长期关系如何处理好用户在购后遇到的种种问题如何设计一套公平清晰高效的售后流程直接决定了我们平台的口碑和用户的复购率售后场景构建在设计任何具体的售后功能之前我的第一步是构建出所有可能发生的售后场景我需要绘制一张完整的售后地图确保我的设计能够覆盖所有可能的用户求助路径不同角色的关注点售后这件事从来不是一个单方的行为它至少牵动着用户商家平台这三方的心我构建场景会先从理解这三方的不同痛点和关注点开始角色核心关注点用户我买的东西不想要了有问题怎么才能快速方便地退换商家用户退回来的货有没有问题退换货的运费成本谁来承担差评会不会影响我的店铺平台我们的售后规则是否公平如何才能在保障用户体验和控制商家风险之间找到平衡处理这些纠纷需要多少客服人力核心售后场景提炼基于用户的核心关注点我就可以提炼出用户在订单生命周期的不同阶段会发起的几大类核心售后场景这些场景就是我们后续设计流程的需求来源订单状态用户可能发起的售后场景订单未付款取消订单订单已付款待发货取消订单申请仅退款待收货已完成申请退款退货申请换货申请仅退款如商品漏发任意环节交易纠纷申请平台介入售后职责与需求提取场景明确后我需要为这些场景定义清晰的责任边界和业务规则这部分内容将是我后续撰写和设计流程的基础我的设计将遵循以下的基本职责划分用户职责发起者一切售后行为由买家发起我的用户端产品设计必须为用户在订单详情页等位置提供清晰便捷的售后申请入口商家职责处理者商家收到售后申请进行响应商家是处理售后问题的第一责任人我的商家后台设计必须为商家提供处理这些申请的技术支持但具体的处理结果同意拒绝由商家自己决定平台职责保障者与仲裁者当用户与商家发生纠纷时平台介入保障双方权益我的平台运营后台设计必须包含一套纠纷仲裁机制在保障用户交易安全的前提下对纠纷进行公平的判决售后流程分析在我们构建了售后场景明确了各方角色的职责之后下一步就是为每一个具体的场景设计出清晰严谨可执行的业务流程我设计的售后流程就像是一部法律它需要清晰地定义出在某种售后场景下用户和商家各自拥有什么权利需要履行什么义务以及系统应该如何根据他们的操作来自动地流转订单的售后状态我们将逐一分析几种最高频的售后场景场景一待付款取消订单核心逻辑这是最简单的售后场景角色完全由买家单方面发起和完成流程用户在我的订单中找到待付款的订单直接点击取消订单订单状态即变为已取消我的设计思考为什么这个流程商家完全不参与因为在用户付款之前这笔订单尚未进入商家的待办事项即待发货列表中没有对商家产生任何实质性的履约成本因此我设计的流程允许用户在这个阶段自由地无条件地取消订单场景二待发货取消订单核心逻辑这个场景开始涉及到买卖双方的交互角色由买家发起申请但需要商家进行审核流程买家在待发货订单中发起取消申请商家在后台收到申请进行审核若商家同意则订单取消系统自动退款若商家拒绝则订单继续保持待发货状态我的设计思考为什么商家需要审核因为用户付款后订单就已经进入了商家的履约流程商家可能已经在拣货打包甚至已经交给了快递员但还未揽件这个审核的环节就是我留给商家的一个拦截窗口让他去确认这笔订单是否还能从他的发货流程中被成功地拦截下来场景三待收货已完成退款退货这是最复杂的售后场景因为它涉及到逆向物流即用户把商品寄回给商家我必须为这个场景设计一套严谨的多步骤的售后状态机我的流程与状态设计售后状态触发动作下一步状态初始买家在用户端对待收货或已完成的订单发起退款退货申请并填写理由待商家审核待商家审核商家在后台审核通过买家的申请待买家发货待买家发货买家在用户端填写退货的快递单号或上门与自行寄回商家待收货商家待收货商家在后台确认已收到买家寄回的商品且商品完好无损退款中退款成功场景四待收货已完成申请换货换货流程因为涉及到双向物流买家寄回商家寄出所以它的状态机比退货更复杂我的流程与状态设计换货流程的前步与退款退货完全一致从待商家审核到商家待收货在商家确认收到退货后流程继续售后状态触发动作下一步状态商家待收货商家在后台确认收到退货后将新的商品重新发货给买家并填写新的快递单号待买家收货待买家收货买家在用户端确认收到商家换发的商品换货流程结束通过为每一个售后场景都设计这样一套清晰的流程和状态机我就可以确保我们平台用户商家三方在处理复杂的售后问题时都有据可依有路可循商家端与平台端的售后管理页面交易纠纷处理在我们设计的售后流程中大部分的退款退货申请都可以由用户和商家通过协商来顺利解决但是我们必须考虑到一种情况当用户和商家无法达成一致时应该怎么办比如用户申请退货的理由是商品有质量问题并上传了图片而商家则反驳认为是用户人为损坏并拒绝了退款申请此时双方就陷入了交易纠纷如果平台不介入这个矛盾将永远无法解决并会极大地损害用户对平台的信任因此我必须设计一套公平公正透明的平台介入仲裁机制平台介入的原则与时机我设计这套仲裁机制会遵循以下核心原则保障交易安全我设计的平台规则会优先保障用户的合法权益明确介入时机平台介入的触发器非常明确在售后流程中任何一方的合理请求被另一方拒绝时系统就应该为被拒绝的一方提供申请平台介入的入口依赖双方举证平台作为法官绝不偏听偏信我的判决必须建立在双方提供的证据之上交易纠纷处理流程与功能设计整个交易纠纷的处理我将它设计为一个严谨的多方参与的线上流程用户端功能当用户的售后申请被商家拒绝后我会在他的订单详情页提供一个申请平台介入的按钮点击后会进入举证页面用户可以在这里上传他认为能支持自己诉求的文字图片视频等证据商家端功能当用户申请平台介入后这笔售后订单在商家后台的状态就会变为待商家举证商家同样需要在这个订单的详情页进入举证页面上传对他有利的证据如发货前的商品完好视频与用户的聊天记录等平台端功能这是我们内部客服和仲裁团队的法庭维权列表所有用户申请介入的纠纷单都会进入到这个独立的维权列表工作队列中等待我们的客服法官来处理维权详情页这是法官的判案工作台我设计的这个页面会聚合这笔纠纷的所有信息原始的订单信息完整的售后申请记录和双方的沟通日志买家提供的证据卖家提供的证据在页面的最下方我会为法官提供最终的判决功能按钮比如支持买家或支持卖家证据链条设计证据是我们整个仲裁流程的核心因此我设计的举证页面无论是用户端还是商家端都必须支持上传多种形式的证据证据类型我的设计说明文字描述供双方清晰地有条理地陈述事情的经过和自己的诉求图片视频证据这是最直观的证据如商品损坏部位的照片开箱视频证明商品货不对板的截图等凭证类文件包括但不限于与对方的聊天记录发货退货的快递底单甚至是物流公司出具的红章证明等通过这套严谨的申请介入双方举证平台判决的流程我为我们的电商平台建立起了一道能化解交易矛盾保障用户和商家双方合法权益的安全网",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-25 11:05:48",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E7%AB%A0%EF%BC%9A%E5%94%AE%E5%90%8E%E7%AE%A1%E7%90%86"><span class="toc-text">第七章：售后管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#7-1-%E5%94%AE%E5%90%8E%E5%9C%BA%E6%99%AF%E6%9E%84%E5%BB%BA"><span class="toc-text">7.1 售后场景构建</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%B8%8D%E5%90%8C%E8%A7%92%E8%89%B2%E7%9A%84%E5%85%B3%E6%B3%A8%E7%82%B9"><span class="toc-text">1. 不同角色的关注点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E6%A0%B8%E5%BF%83%E5%94%AE%E5%90%8E%E5%9C%BA%E6%99%AF%E6%8F%90%E7%82%BC"><span class="toc-text">2. 核心售后场景提炼</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%94%AE%E5%90%8E%E8%81%8C%E8%B4%A3%E4%B8%8E%E9%9C%80%E6%B1%82%E6%8F%90%E5%8F%96"><span class="toc-text">3. 售后职责与需求提取</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-2-%E5%94%AE%E5%90%8E%E6%B5%81%E7%A8%8B%E5%88%86%E6%9E%90"><span class="toc-text">7.2 售后流程分析</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E5%BE%85%E4%BB%98%E6%AC%BE%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95"><span class="toc-text">1. 场景一：待付款取消订单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E5%BE%85%E5%8F%91%E8%B4%A7%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95"><span class="toc-text">2. 场景二：待发货取消订单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9A%E5%BE%85%E6%94%B6%E8%B4%A7-%E5%B7%B2%E5%AE%8C%E6%88%90-%E9%80%80%E6%AC%BE%E9%80%80%E8%B4%A7"><span class="toc-text">3. 场景三：待收货/已完成 - 退款退货</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E5%9C%BA%E6%99%AF%E5%9B%9B%EF%BC%9A%E5%BE%85%E6%94%B6%E8%B4%A7-%E5%B7%B2%E5%AE%8C%E6%88%90-%E7%94%B3%E8%AF%B7%E6%8D%A2%E8%B4%A7"><span class="toc-text">4. 场景四：待收货/已完成 - 申请换货</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-%E5%95%86%E5%AE%B6%E7%AB%AF%E4%B8%8E%E5%B9%B3%E5%8F%B0%E7%AB%AF%E7%9A%84%E5%94%AE%E5%90%8E%E7%AE%A1%E7%90%86%E9%A1%B5%E9%9D%A2"><span class="toc-text">5.商家端与平台端的售后管理页面</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-3-%E4%BA%A4%E6%98%93%E7%BA%A0%E7%BA%B7%E5%A4%84%E7%90%86"><span class="toc-text">7.3 交易纠纷处理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%B9%B3%E5%8F%B0%E4%BB%8B%E5%85%A5%E7%9A%84%E5%8E%9F%E5%88%99%E4%B8%8E%E6%97%B6%E6%9C%BA"><span class="toc-text">1. 平台介入的原则与时机</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E4%BA%A4%E6%98%93%E7%BA%A0%E7%BA%B7%E5%A4%84%E7%90%86%E6%B5%81%E7%A8%8B%E4%B8%8E%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-text">2. 交易纠纷处理流程与功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E8%AF%81%E6%8D%AE%E9%93%BE%E6%9D%A1%E8%AE%BE%E8%AE%A1"><span class="toc-text">3. 证据链条设计</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理进阶（七）：第七章：售后管理</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-24T14:13:45.000Z" title="发表于 2025-07-24 22:13:45">2025-07-24</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.656Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">2.8k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>8分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理进阶（七）：第七章：售后管理"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/32684.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/32684.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理进阶（七）：第七章：售后管理</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-24T14:13:45.000Z" title="发表于 2025-07-24 22:13:45">2025-07-24</time><time itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.656Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></header><div id="postchat_postcontent"><h1 id="第七章：售后管理"><a href="#第七章：售后管理" class="headerlink" title="第七章：售后管理"></a>第七章：售后管理</h1><p>在我看来，用户下单付款，绝不意味着我们服务的结束，恰恰相反，它开启了我们与用户之间，一段更需要“信任”来维系的长期关系。</p><p>如何处理好用户在购后遇到的种种问题，如何设计一套公平、清晰、高效的售后流程，直接决定了我们平台的口碑和用户的复购率。</p><h2 id="7-1-售后场景构建"><a href="#7-1-售后场景构建" class="headerlink" title="7.1 售后场景构建"></a>7.1 售后场景构建</h2><p>在设计任何具体的售后功能之前，我的第一步，是<strong>构建出所有可能发生的售后场景</strong>。我需要绘制一张完整的“售后地图”，确保我的设计，能够覆盖所有可能的用户求助路径。</p><h3 id="1-不同角色的关注点"><a href="#1-不同角色的关注点" class="headerlink" title="1. 不同角色的关注点"></a>1. 不同角色的关注点</h3><p>“售后”这件事，从来不是一个单方的行为，它至少牵动着<strong>用户、商家、平台</strong>这三方的心。我构建场景，会先从理解这三方的不同“痛点”和“关注点”开始。</p><table><thead><tr><th align="left"><strong>角色</strong></th><th align="left"><strong>核心关注点</strong></th></tr></thead><tbody><tr><td align="left"><strong>用户</strong></td><td align="left">“我买的东西不想要了/有问题，怎么才能快速、方便地退/换？”</td></tr><tr><td align="left"><strong>商家</strong></td><td align="left">“用户退回来的货有没有问题？退换货的运费成本谁来承担？差评会不会影响我的店铺？”</td></tr><tr><td align="left"><strong>平台</strong></td><td align="left">“我们的售后规则是否公平？如何才能在保障用户体验和控制商家风险之间，找到平衡？处理这些纠纷需要多少客服人力？”</td></tr></tbody></table><h3 id="2-核心售后场景提炼"><a href="#2-核心售后场景提炼" class="headerlink" title="2. 核心售后场景提炼"></a>2. 核心售后场景提炼</h3><p>基于用户的核心关注点，我就可以提炼出，用户在订单生命周期的不同阶段，会发起的几大类核心售后场景。</p><p>这些场景，就是我们后续设计流程的“<strong>需求来源</strong>”。</p><table><thead><tr><th align="left"><strong>订单状态</strong></th><th align="left"><strong>用户可能发起的售后场景</strong></th></tr></thead><tbody><tr><td align="left"><strong>订单未付款</strong></td><td align="left"><code>取消订单</code></td></tr><tr><td align="left"><strong>订单已付款、待发货</strong></td><td align="left"><code>取消订单（申请仅退款）</code></td></tr><tr><td align="left"><strong>待收货 / 已完成</strong></td><td align="left"><code>申请退款退货</code>、 <code>申请换货</code>、<code>申请仅退款</code>（如：商品漏发）</td></tr><tr><td align="left"><strong>任意环节</strong></td><td align="left"><code>交易纠纷，申请平台介入</code></td></tr></tbody></table><h3 id="3-售后职责与需求提取"><a href="#3-售后职责与需求提取" class="headerlink" title="3. 售后职责与需求提取"></a>3. 售后职责与需求提取</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723135351237.png" alt="image-20250723135351237"></p><p>场景明确后，我需要为这些场景，定义清晰的“<strong>责任边界</strong>”和“<strong>业务规则</strong>”。这部分内容，将是我后续撰写PRD和设计流程的基础。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723135410717.png" alt="image-20250723135410717"></p><p>我的设计，将遵循以下的基本职责划分：</p><ul><li><p><strong>用户职责：发起者</strong><br><code>一切售后行为，由买家发起</code>。我的用户端产品设计，必须为用户，在订单详情页等位置，提供清晰、便捷的售后申请入口。</p></li><li><p><strong>商家职责：处理者</strong><br><code>商家收到售后申请进行响应</code>。商家是处理售后问题的第一责任人。我的商家后台设计，必须为商家提供处理这些申请的技术支持，但<strong>具体的处理结果（同意/拒绝），由商家自己决定</strong>。</p></li><li><p><strong>平台职责：保障者与仲裁者</strong><br><code>当用户与商家发生纠纷时，平台介入，保障双方权益</code>。我的平台运营后台设计，必须包含一套“<strong>纠纷仲裁</strong>”机制。在保障用户交易安全的前提下，对纠纷进行公平的判决。</p></li></ul><hr><h2 id="7-2-售后流程分析"><a href="#7-2-售后流程分析" class="headerlink" title="7.2 售后流程分析"></a>7.2 售后流程分析</h2><p>在我们构建了售后场景，明确了各方角色的职责之后，下一步，就是为每一个具体的场景，设计出<strong>清晰、严谨、可执行</strong>的业务流程。</p><p>我设计的售后流程，就像是一部“<strong>法律</strong>”。它需要清晰地定义出，在某种售后场景下，用户和商家，各自拥有什么<strong>权利</strong>，需要履行什么<strong>义务</strong>，以及系统应该如何根据他们的操作，来<strong>自动地流转订单的售后状态</strong>。</p><p>我们将逐一分析几种最高频的售后场景。</p><h3 id="1-场景一：待付款取消订单"><a href="#1-场景一：待付款取消订单" class="headerlink" title="1. 场景一：待付款取消订单"></a>1. 场景一：待付款取消订单</h3><ul><li><strong>核心逻辑</strong>：这是最简单的售后场景。<ul><li><strong>角色</strong>：完全由<strong>买家</strong>单方面发起和完成。</li><li><strong>流程</strong>：用户在“我的订单”中，找到“待付款”的订单，直接点击“取消订单”，订单状态即变为“已取消”。</li></ul></li><li><strong>我的设计思考</strong>：为什么这个流程，商家完全不参与？因为在用户付款之前，这笔订单，尚未进入商家的“待办事项（即待发货列表）”中，没有对商家产生任何实质性的履约成本。因此，我设计的流程，允许用户在这个阶段，自由地、无条件地取消订单。</li></ul><h3 id="2-场景二：待发货取消订单"><a href="#2-场景二：待发货取消订单" class="headerlink" title="2. 场景二：待发货取消订单"></a>2. 场景二：待发货取消订单</h3><ul><li><strong>核心逻辑</strong>：这个场景，开始涉及到买卖双方的交互。<ul><li><strong>角色</strong>：由<strong>买家</strong>发起申请，但需要<strong>商家</strong>进行审核。</li><li><strong>流程</strong>：买家在“待发货”订单中，发起“取消申请” -&gt; 商家在后台收到申请，进行审核 -&gt; 若商家同意，则订单取消，系统自动退款；若商家拒绝，则订单继续保持“待发货”状态。</li></ul></li><li><strong>我的设计思考</strong>：为什么商家需要审核？因为用户付款后，订单就已经进入了商家的履约流程。商家可能已经在“拣货”、“打包”，甚至已经交给了快递员但还未揽件。这个“审核”的环节，就是我留给商家的一个“<strong>拦截窗口</strong>”，让他去确认，这笔订单，是否还能从他的发货流程中，被成功地拦截下来。</li></ul><h3 id="3-场景三：待收货-已完成-退款退货"><a href="#3-场景三：待收货-已完成-退款退货" class="headerlink" title="3. 场景三：待收货/已完成 - 退款退货"></a>3. 场景三：待收货/已完成 - 退款退货</h3><p>这是最复杂的售后场景，因为它涉及到“<strong>逆向物流</strong>”（即用户把商品寄回给商家）。我必须为这个场景，设计一套严谨的、多步骤的“<strong>售后状态机</strong>”。</p><p><strong>我的流程与状态设计</strong>：</p><table><thead><tr><th align="left">售后状态</th><th align="left">触发动作</th><th align="left">下一步状态</th></tr></thead><tbody><tr><td align="left"><strong>（初始）</strong></td><td align="left"><strong>买家</strong>在用户端，对“待收货”或“已完成”的订单，发起退款退货申请，并填写理由。</td><td align="left"><code>待商家审核</code></td></tr><tr><td align="left"><code>待商家审核</code></td><td align="left"><strong>商家</strong>在后台，审核通过买家的申请。</td><td align="left"><code>待买家发货</code></td></tr><tr><td align="left"><code>待买家发货</code></td><td align="left"><strong>买家</strong>在用户端，填写退货的快递单号，或上门与自行寄回</td><td align="left"><code>商家待收货</code></td></tr><tr><td align="left"><code>商家待收货</code></td><td align="left"><strong>商家</strong>在后台，确认已收到买家寄回的商品，且商品完好无损。</td><td align="left"><code>退款中 / 退款成功</code></td></tr></tbody></table><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141252995.png" alt="image-20250723141252995"></p><h3 id="4-场景四：待收货-已完成-申请换货"><a href="#4-场景四：待收货-已完成-申请换货" class="headerlink" title="4. 场景四：待收货/已完成 - 申请换货"></a>4. 场景四：待收货/已完成 - 申请换货</h3><p>换货流程，因为涉及到“<strong>双向物流</strong>”（买家寄回 -&gt; 商家寄出），所以它的状态机，比退货更复杂。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141408765.png" alt="image-20250723141408765"></p><p><strong>我的流程与状态设计</strong>：<br>换货流程的前4步，与退款退货完全一致（从<code>待商家审核</code>到<code>商家待收货</code>）。在商家确认收到退货后，流程继续：</p><table><thead><tr><th align="left">售后状态</th><th align="left">触发动作</th><th align="left">下一步状态</th></tr></thead><tbody><tr><td align="left"><code>商家待收货</code></td><td align="left"><strong>商家</strong>在后台，确认收到退货后，将新的商品，重新发货给买家，并填写新的快递单号。</td><td align="left"><code>待买家收货</code></td></tr><tr><td align="left"><code>待买家收货</code></td><td align="left"><strong>买家</strong>在用户端，确认收到商家换发的商品。</td><td align="left"><strong>（换货流程结束）</strong></td></tr></tbody></table><p>通过为每一个售后场景，都设计这样一套清晰的流程和状态机，我就可以确保，我们平台、用户、商家三方，在处理复杂的售后问题时，都有据可依，有路可循。</p><h3 id="5-商家端与平台端的售后管理页面"><a href="#5-商家端与平台端的售后管理页面" class="headerlink" title="5.商家端与平台端的售后管理页面"></a>5.商家端与平台端的售后管理页面</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141535312.png" alt="image-20250723141535312"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141546753.png" alt="image-20250723141546753"></p><hr><h2 id="7-3-交易纠纷处理"><a href="#7-3-交易纠纷处理" class="headerlink" title="7.3 交易纠纷处理"></a>7.3 交易纠纷处理</h2><p>在我们设计的售后流程中，大部分的退款、退货申请，都可以由用户和商家，通过协商来顺利解决。</p><p>但是，我们必须考虑到一种情况：<strong>当用户和商家，无法达成一致时，应该怎么办？</strong><br>比如，用户申请退货的理由是“商品有质量问题”，并上传了图片；而商家则反驳，认为是用户“人为损坏”，并拒绝了退款申请。</p><p>此时，双方就陷入了“<strong>交易纠纷</strong>”。如果平台不介入，这个矛盾将永远无法解决，并会极大地损害用户对平台的信任。因此，我必须设计一套<strong>公平、公正、透明</strong>的<strong>平台介入仲裁机制</strong>。</p><h3 id="1-平台介入的原则与时机"><a href="#1-平台介入的原则与时机" class="headerlink" title="1. 平台介入的原则与时机"></a>1. 平台介入的原则与时机</h3><p>我设计这套仲裁机制，会遵循以下核心原则：</p><ul><li><strong>保障交易安全</strong>：我设计的平台规则，会<strong>优先保障用户的合法权益</strong>。</li><li><strong>明确介入时机</strong>：平台介入的“<strong>触发器</strong>”非常明确——<strong>在售后流程中，任何一方的合理请求，被另一方“拒绝”时</strong>，系统就应该为被拒绝的一方，提供“<strong>申请平台介入</strong>”的入口。</li><li><strong>依赖双方举证</strong>：平台作为“法官”，<strong>绝不偏听偏信</strong>。我的判决，必须建立在双方提供的“<strong>证据</strong>”之上。</li></ul><h3 id="2-交易纠纷处理流程与功能设计"><a href="#2-交易纠纷处理流程与功能设计" class="headerlink" title="2. 交易纠纷处理流程与功能设计"></a>2. 交易纠纷处理流程与功能设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142127598.png" alt="image-20250723142127598"></p><p>整个交易纠纷的处理，我将它设计为一个严谨的、多方参与的线上流程。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142252925.png" alt="image-20250723142252925"></p><ul><li><p><strong>用户端功能</strong><br>当用户的售后申请被商家拒绝后，我会在他的订单详情页，提供一个“<strong>申请平台介入</strong>”的按钮。点击后，会进入“<strong>举证页面</strong>”，用户可以在这里，上传他认为能支持自己诉求的文字、图片、视频等证据。</p></li><li><p><strong>商家端功能</strong><br>当用户申请平台介入后，这笔售后订单，在商家后台的状态，就会变为“<strong>待商家举证</strong>”。商家同样需要在这个订单的详情页，进入“<strong>举证页面</strong>”，上传对他有利的证据（如：发货前的商品完好视频、与用户的聊天记录等）。</p></li><li><p><strong>平台端功能</strong><br>这是我们内部客服和仲裁团队的“<strong>法庭</strong>”。</p><ol><li><p><strong>维权列表</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142331985.png" alt="image-20250723142331985"></p><p>所有用户申请介入的纠纷单，都会进入到这个独立的“<strong>维权列表</strong>”工作队列中，等待我们的客服“法官”来处理。</p></li><li><p><strong>维权详情页</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142348594.png" alt="image-20250723142348594"></p><p>这是“法官”的判案工作台。我设计的这个页面，会<strong>聚合</strong>这笔纠纷的所有信息：</p><ul><li>原始的订单信息。</li><li>完整的售后申请记录和双方的沟通日志。</li><li><strong>买家提供的证据</strong>。</li><li><strong>卖家提供的证据</strong>。</li></ul><p>在页面的最下方，我会为“法官”，提供最终的“<strong>判决</strong>”功能按钮，比如“<strong>支持买家</strong>”或“<strong>支持卖家</strong>”。</p></li></ol></li></ul><h3 id="3-证据链条设计"><a href="#3-证据链条设计" class="headerlink" title="3. 证据链条设计"></a>3. 证据链条设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142456946.png" alt="image-20250723142456946"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142519553.png" alt="image-20250723142519553"></p><p>“<strong>证据</strong>”，是我们整个仲裁流程的核心。因此，我设计的“举证页面”（无论是用户端还是商家端），都必须支持上传多种形式的证据。</p><table><thead><tr><th align="left"><strong>证据类型</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><strong>文字描述</strong></td><td align="left">供双方清晰地、有条理地，陈述事情的经过和自己的诉求。</td></tr><tr><td align="left"><strong>图片/视频证据</strong></td><td align="left">这是最直观的证据。如：商品损坏部位的照片、开箱视频、证明商品货不对板的截图等。</td></tr><tr><td align="left"><strong>凭证类文件</strong></td><td align="left">包括但不限于：与对方的<strong>聊天记录</strong>、<strong>发货/退货的快递底单</strong>、甚至是物流公司出具的“<strong>红章证明</strong>”等。</td></tr></tbody></table><p>通过这套严谨的“<strong>申请介入 -&gt; 双方举证 -&gt; 平台判决</strong>”的流程，我为我们的电商平台，建立起了一道能化解交易矛盾、保障用户和商家双方合法权益的“安全网”。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/32684.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/32684.html&quot;)">产品经理进阶（七）：第七章：售后管理</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/32684.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理进阶（七）：第七章：售后管理&amp;url=https://prorise666.site/posts/32684.html&amp;pic=https://bu.dusays.com/2025/07/25/6882f31a48223.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/37507.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理进阶（六）：第六章：电商后台 - 运营与交易管理</div></div></a></div><div class="next-post pull-right"><a href="/posts/25511.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理进阶（八）：第八章：电商后台 - 种草管理</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理进阶（七）：第七章：售后管理",date:"2025-07-24 22:13:45",updated:"2025-07-25 11:05:48",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第七章：售后管理\n\n在我看来，用户下单付款，绝不意味着我们服务的结束，恰恰相反，它开启了我们与用户之间，一段更需要“信任”来维系的长期关系。\n\n如何处理好用户在购后遇到的种种问题，如何设计一套公平、清晰、高效的售后流程，直接决定了我们平台的口碑和用户的复购率。\n\n## 7.1 售后场景构建\n\n在设计任何具体的售后功能之前，我的第一步，是**构建出所有可能发生的售后场景**。我需要绘制一张完整的“售后地图”，确保我的设计，能够覆盖所有可能的用户求助路径。\n\n### 1. 不同角色的关注点\n\n“售后”这件事，从来不是一个单方的行为，它至少牵动着**用户、商家、平台**这三方的心。我构建场景，会先从理解这三方的不同“痛点”和“关注点”开始。\n\n| **角色** | **核心关注点** |\n| :--- | :--- |\n| **用户**| “我买的东西不想要了/有问题，怎么才能快速、方便地退/换？” |\n| **商家**| “用户退回来的货有没有问题？退换货的运费成本谁来承担？差评会不会影响我的店铺？” |\n| **平台**| “我们的售后规则是否公平？如何才能在保障用户体验和控制商家风险之间，找到平衡？处理这些纠纷需要多少客服人力？” |\n\n### 2. 核心售后场景提炼\n\n基于用户的核心关注点，我就可以提炼出，用户在订单生命周期的不同阶段，会发起的几大类核心售后场景。\n\n这些场景，就是我们后续设计流程的“**需求来源**”。\n\n| **订单状态** | **用户可能发起的售后场景** |\n| :--- | :--- |\n| **订单未付款** | `取消订单` |\n| **订单已付款、待发货** | `取消订单（申请仅退款）` |\n| **待收货 / 已完成** | `申请退款退货`、 `申请换货`、`申请仅退款`（如：商品漏发） |\n| **任意环节** | `交易纠纷，申请平台介入` |\n\n### 3. 售后职责与需求提取\n\n![image-20250723135351237](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723135351237.png)\n\n场景明确后，我需要为这些场景，定义清晰的“**责任边界**”和“**业务规则**”。这部分内容，将是我后续撰写PRD和设计流程的基础。\n\n![image-20250723135410717](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723135410717.png)\n\n我的设计，将遵循以下的基本职责划分：\n* **用户职责：发起者**\n    `一切售后行为，由买家发起`。我的用户端产品设计，必须为用户，在订单详情页等位置，提供清晰、便捷的售后申请入口。\n\n* **商家职责：处理者**\n    `商家收到售后申请进行响应`。商家是处理售后问题的第一责任人。我的商家后台设计，必须为商家提供处理这些申请的技术支持，但**具体的处理结果（同意/拒绝），由商家自己决定**。\n\n* **平台职责：保障者与仲裁者**\n    `当用户与商家发生纠纷时，平台介入，保障双方权益`。我的平台运营后台设计，必须包含一套“**纠纷仲裁**”机制。在保障用户交易安全的前提下，对纠纷进行公平的判决。\n\n\n\n---\n## 7.2 售后流程分析\n\n在我们构建了售后场景，明确了各方角色的职责之后，下一步，就是为每一个具体的场景，设计出**清晰、严谨、可执行**的业务流程。\n\n我设计的售后流程，就像是一部“**法律**”。它需要清晰地定义出，在某种售后场景下，用户和商家，各自拥有什么**权利**，需要履行什么**义务**，以及系统应该如何根据他们的操作，来**自动地流转订单的售后状态**。\n\n我们将逐一分析几种最高频的售后场景。\n\n### 1. 场景一：待付款取消订单\n\n* **核心逻辑**：这是最简单的售后场景。\n    * **角色**：完全由**买家**单方面发起和完成。\n    * **流程**：用户在“我的订单”中，找到“待付款”的订单，直接点击“取消订单”，订单状态即变为“已取消”。\n* **我的设计思考**：为什么这个流程，商家完全不参与？因为在用户付款之前，这笔订单，尚未进入商家的“待办事项（即待发货列表）”中，没有对商家产生任何实质性的履约成本。因此，我设计的流程，允许用户在这个阶段，自由地、无条件地取消订单。\n\n### 2. 场景二：待发货取消订单\n\n* **核心逻辑**：这个场景，开始涉及到买卖双方的交互。\n    * **角色**：由**买家**发起申请，但需要**商家**进行审核。\n    * **流程**：买家在“待发货”订单中，发起“取消申请” -> 商家在后台收到申请，进行审核 -> 若商家同意，则订单取消，系统自动退款；若商家拒绝，则订单继续保持“待发货”状态。\n* **我的设计思考**：为什么商家需要审核？因为用户付款后，订单就已经进入了商家的履约流程。商家可能已经在“拣货”、“打包”，甚至已经交给了快递员但还未揽件。这个“审核”的环节，就是我留给商家的一个“**拦截窗口**”，让他去确认，这笔订单，是否还能从他的发货流程中，被成功地拦截下来。\n\n\n\n### 3. 场景三：待收货/已完成 - 退款退货\n\n这是最复杂的售后场景，因为它涉及到“**逆向物流**”（即用户把商品寄回给商家）。我必须为这个场景，设计一套严谨的、多步骤的“**售后状态机**”。\n\n**我的流程与状态设计**：\n\n| 售后状态 | 触发动作 | 下一步状态 |\n| :--- | :--- | :--- |\n| **（初始）** | **买家**在用户端，对“待收货”或“已完成”的订单，发起退款退货申请，并填写理由。 | `待商家审核` |\n| `待商家审核` | **商家**在后台，审核通过买家的申请。 | `待买家发货` |\n| `待买家发货`| **买家**在用户端，填写退货的快递单号，或上门与自行寄回 | `商家待收货` |\n| `商家待收货`| **商家**在后台，确认已收到买家寄回的商品，且商品完好无损。 | `退款中 / 退款成功`|\n\n![image-20250723141252995](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141252995.png)\n\n### 4. 场景四：待收货/已完成 - 申请换货\n\n换货流程，因为涉及到“**双向物流**”（买家寄回 -> 商家寄出），所以它的状态机，比退货更复杂。\n\n![image-20250723141408765](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141408765.png)\n\n**我的流程与状态设计**：\n换货流程的前4步，与退款退货完全一致（从`待商家审核`到`商家待收货`）。在商家确认收到退货后，流程继续：\n\n| 售后状态 | 触发动作 | 下一步状态 |\n| :--- | :--- | :--- |\n| `商家待收货`| **商家**在后台，确认收到退货后，将新的商品，重新发货给买家，并填写新的快递单号。 | `待买家收货` |\n| `待买家收货`| **买家**在用户端，确认收到商家换发的商品。 | **（换货流程结束）** |\n\n通过为每一个售后场景，都设计这样一套清晰的流程和状态机，我就可以确保，我们平台、用户、商家三方，在处理复杂的售后问题时，都有据可依，有路可循。\n\n### 5.商家端与平台端的售后管理页面\n\n![image-20250723141535312](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141535312.png)![image-20250723141546753](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723141546753.png)\n\n\n-----\n\n## 7.3 交易纠纷处理\n\n在我们设计的售后流程中，大部分的退款、退货申请，都可以由用户和商家，通过协商来顺利解决。\n\n但是，我们必须考虑到一种情况：**当用户和商家，无法达成一致时，应该怎么办？**\n比如，用户申请退货的理由是“商品有质量问题”，并上传了图片；而商家则反驳，认为是用户“人为损坏”，并拒绝了退款申请。\n\n此时，双方就陷入了“**交易纠纷**”。如果平台不介入，这个矛盾将永远无法解决，并会极大地损害用户对平台的信任。因此，我必须设计一套**公平、公正、透明**的**平台介入仲裁机制**。\n\n### 1\\. 平台介入的原则与时机\n\n我设计这套仲裁机制，会遵循以下核心原则：\n\n  * **保障交易安全**：我设计的平台规则，会**优先保障用户的合法权益**。\n  * **明确介入时机**：平台介入的“**触发器**”非常明确——**在售后流程中，任何一方的合理请求，被另一方“拒绝”时**，系统就应该为被拒绝的一方，提供“**申请平台介入**”的入口。\n  * **依赖双方举证**：平台作为“法官”，**绝不偏听偏信**。我的判决，必须建立在双方提供的“**证据**”之上。\n\n### 2\\. 交易纠纷处理流程与功能设计\n\n![image-20250723142127598](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142127598.png)\n\n整个交易纠纷的处理，我将它设计为一个严谨的、多方参与的线上流程。\n\n![image-20250723142252925](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142252925.png)\n\n  * **用户端功能**\n    当用户的售后申请被商家拒绝后，我会在他的订单详情页，提供一个“**申请平台介入**”的按钮。点击后，会进入“**举证页面**”，用户可以在这里，上传他认为能支持自己诉求的文字、图片、视频等证据。\n\n  * **商家端功能**\n    当用户申请平台介入后，这笔售后订单，在商家后台的状态，就会变为“**待商家举证**”。商家同样需要在这个订单的详情页，进入“**举证页面**”，上传对他有利的证据（如：发货前的商品完好视频、与用户的聊天记录等）。\n\n  * **平台端功能**\n    这是我们内部客服和仲裁团队的“**法庭**”。\n\n    1.  **维权列表**\n        ![image-20250723142331985](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142331985.png)\n\n        所有用户申请介入的纠纷单，都会进入到这个独立的“**维权列表**”工作队列中，等待我们的客服“法官”来处理。\n\n    2.  **维权详情页**\n        ![image-20250723142348594](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142348594.png)\n\n        这是“法官”的判案工作台。我设计的这个页面，会**聚合**这笔纠纷的所有信息：\n\n          * 原始的订单信息。\n          * 完整的售后申请记录和双方的沟通日志。\n          * **买家提供的证据**。\n          * **卖家提供的证据**。\n\n        在页面的最下方，我会为“法官”，提供最终的“**判决**”功能按钮，比如“**支持买家**”或“**支持卖家**”。\n\n### 3\\. 证据链条设计\n\n![image-20250723142456946](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142456946.png)\n\n![image-20250723142519553](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723142519553.png)\n\n“**证据**”，是我们整个仲裁流程的核心。因此，我设计的“举证页面”（无论是用户端还是商家端），都必须支持上传多种形式的证据。\n\n| **证据类型** | **我的设计说明** |\n| :--- | :--- |\n| **文字描述** | 供双方清晰地、有条理地，陈述事情的经过和自己的诉求。 |\n| **图片/视频证据**| 这是最直观的证据。如：商品损坏部位的照片、开箱视频、证明商品货不对板的截图等。 |\n| **凭证类文件** | 包括但不限于：与对方的**聊天记录**、**发货/退货的快递底单**、甚至是物流公司出具的“**红章证明**”等。 |\n\n通过这套严谨的“**申请介入 -\\> 双方举证 -\\> 平台判决**”的流程，我为我们的电商平台，建立起了一道能化解交易矛盾、保障用户和商家双方合法权益的“安全网”。\n\n\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E7%AB%A0%EF%BC%9A%E5%94%AE%E5%90%8E%E7%AE%A1%E7%90%86"><span class="toc-number">1.</span> <span class="toc-text">第七章：售后管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#7-1-%E5%94%AE%E5%90%8E%E5%9C%BA%E6%99%AF%E6%9E%84%E5%BB%BA"><span class="toc-number">1.1.</span> <span class="toc-text">7.1 售后场景构建</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%B8%8D%E5%90%8C%E8%A7%92%E8%89%B2%E7%9A%84%E5%85%B3%E6%B3%A8%E7%82%B9"><span class="toc-number">1.1.1.</span> <span class="toc-text">1. 不同角色的关注点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E6%A0%B8%E5%BF%83%E5%94%AE%E5%90%8E%E5%9C%BA%E6%99%AF%E6%8F%90%E7%82%BC"><span class="toc-number">1.1.2.</span> <span class="toc-text">2. 核心售后场景提炼</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%94%AE%E5%90%8E%E8%81%8C%E8%B4%A3%E4%B8%8E%E9%9C%80%E6%B1%82%E6%8F%90%E5%8F%96"><span class="toc-number">1.1.3.</span> <span class="toc-text">3. 售后职责与需求提取</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-2-%E5%94%AE%E5%90%8E%E6%B5%81%E7%A8%8B%E5%88%86%E6%9E%90"><span class="toc-number">1.2.</span> <span class="toc-text">7.2 售后流程分析</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E5%BE%85%E4%BB%98%E6%AC%BE%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95"><span class="toc-number">1.2.1.</span> <span class="toc-text">1. 场景一：待付款取消订单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E5%BE%85%E5%8F%91%E8%B4%A7%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95"><span class="toc-number">1.2.2.</span> <span class="toc-text">2. 场景二：待发货取消订单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9A%E5%BE%85%E6%94%B6%E8%B4%A7-%E5%B7%B2%E5%AE%8C%E6%88%90-%E9%80%80%E6%AC%BE%E9%80%80%E8%B4%A7"><span class="toc-number">1.2.3.</span> <span class="toc-text">3. 场景三：待收货/已完成 - 退款退货</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E5%9C%BA%E6%99%AF%E5%9B%9B%EF%BC%9A%E5%BE%85%E6%94%B6%E8%B4%A7-%E5%B7%B2%E5%AE%8C%E6%88%90-%E7%94%B3%E8%AF%B7%E6%8D%A2%E8%B4%A7"><span class="toc-number">1.2.4.</span> <span class="toc-text">4. 场景四：待收货/已完成 - 申请换货</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-%E5%95%86%E5%AE%B6%E7%AB%AF%E4%B8%8E%E5%B9%B3%E5%8F%B0%E7%AB%AF%E7%9A%84%E5%94%AE%E5%90%8E%E7%AE%A1%E7%90%86%E9%A1%B5%E9%9D%A2"><span class="toc-number">1.2.5.</span> <span class="toc-text">5.商家端与平台端的售后管理页面</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-3-%E4%BA%A4%E6%98%93%E7%BA%A0%E7%BA%B7%E5%A4%84%E7%90%86"><span class="toc-number">1.3.</span> <span class="toc-text">7.3 交易纠纷处理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%B9%B3%E5%8F%B0%E4%BB%8B%E5%85%A5%E7%9A%84%E5%8E%9F%E5%88%99%E4%B8%8E%E6%97%B6%E6%9C%BA"><span class="toc-number">1.3.1.</span> <span class="toc-text">1. 平台介入的原则与时机</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E4%BA%A4%E6%98%93%E7%BA%A0%E7%BA%B7%E5%A4%84%E7%90%86%E6%B5%81%E7%A8%8B%E4%B8%8E%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.2.</span> <span class="toc-text">2. 交易纠纷处理流程与功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E8%AF%81%E6%8D%AE%E9%93%BE%E6%9D%A1%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.3.</span> <span class="toc-text">3. 证据链条设计</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>