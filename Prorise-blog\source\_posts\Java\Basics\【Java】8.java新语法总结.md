---
title: Java（八）：8.0 Java新语法总结
categories:
  - 后端技术
  - Java
tags:
  - Java基础知识总汇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp'
comments: true
toc: true
ai: true
abbrlink: 14501
date: 2025-05-08 23:13:45
---

## 8.0 Java新语法总结

### 8.1 [核心] [Java 8+] Lambda 表达式与方法引用

> **本章导读**: Java 8 的发布是 Java 发展史上的一个里程碑，其最核心、最具代表性的特性无疑是 **Lambda 表达式**的引入。它将**函数式编程**思想正式带入了 Java 世界，从根本上改变了我们处理**行为参数化** 的方式。在本节中，我们将从 Lambda 的诞生背景出发，深入其核心语法、**函数式接口**，并最终掌握其语法糖——**方法引用**的使用技巧，为我们后续学习 Stream API 等高级特性打下坚实的基础。

![image-20250714145916792](https://cdn.smartcis.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250714145916792.png)



#### 8.1.1 背景：为什么要引入 Lambda

在 Java 8 之前，如果我们想将一个行为（一段代码逻辑）传递给另一个方法，通常需要依赖**匿名内部类** (`Anonymous Inner Class`)。这种写法不仅语法冗长、可读性差，而且会为每个实例生成一个独立的 `.class` 文件，显得非常笨重。

让我们通过一个简单的例子直观感受一下：对一个字符串列表按长度进行排序。

**背景**：假设我们需要对一个 `List<String>` 集合，按照字符串的长度进行升序排序。

####### **场景1：Java 8 之前的实现方式 - 使用匿名内部类**

```java
package com.example;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class Main {
    public static void main(String[] args) {
        List<String> names = Arrays.asList("peter", "anna", "mike", "xenia");
        // 按照字符串的长度进行升序排序。
        names.sort(new Comparator<String>() {
            @Override
            public int compare(String a, String b) {
                return a.length() - b.length();
            }
        });
        System.out.println("排序后的列表: " + names);

    }
}
// 输出： 
// 排序后的列表: [anna, mike, peter, xenia]
```

> **小结**: 我们可以看到，为了实现一个简单的比较逻辑，我们不得不编写一个完整的匿名内部类实例，其中真正核心的代码只有一行 `a.length() - b.length()`，其余都是模板化的语法噪音。这种写法显得“头重脚轻”，不够优雅。Lambda 表达式的诞生，正是为了解决这一痛点。

#### 8.1.2 核心：Lambda 语法与函数式接口

###### 1\. Lambda 表达式的三部分构成：`(参数) -> {方法体}` - 一种更紧凑的代码表示法

Lambda 表达式本质上是一个**匿名方法**，它提供了一种清晰、简洁的方式来表示一个函数式接口的实例。其构成如下：

  * **参数列表 `(parameters)`**：与接口中抽象方法的参数列表相对应。可以省略参数类型，编译器会自动推断。如果只有一个参数，可以省略括号 `()`。
  * **箭头符号 `->`**：将参数列表与方法体分开，读作 "goes to"。
  * **方法体 `body`**：包含了方法的实现逻辑。如果方法体只有一条语句，可以省略大括号 `{}` 和 `return` 关键字。

####### **场景1：使用 Lambda 表达式重写排序**

**背景**：同样是对字符串列表按长度排序，我们看看使用 Lambda 如何实现。

``` java
package com.example;

import java.util.Arrays;
import java.util.List;

public class Main {
    public static void main(String [] args) {
        List <String> names = Arrays.asList("peter", "anna", "mike", "xenia");

        // 1. 带有类型声明的完整语法
        // names.sort((String a, String b) -> {
        //     return a.length() - b.length();
        // });

        // 2. 省略类型声明，由编译器推断 (推荐)
        // names.sort((a, b) -> {
        //     return a.length() - b.length();
        // });

        // 3. 当方法体只有一行时，省略大括号和 return (最简洁，推荐)
        names.sort((a, b) -> a.length() - b.length());

        System.out.println("使用 Lambda 排序后的列表: " + names);
    }
}
// 输出:
// 使用 Lambda 排序后的列表: [anna, mike, peter, xenia]
```

> **小结**: 对比之前的匿名内部类，Lambda 表达式极大地简化了代码，使我们的意图一目了然：**传入两个字符串 `a` 和 `b`，返回它们的长度差**。

###### 2\. 函数式接口 (`@FunctionalInterface`) - Lambda 的类型基础

Lambda 表达式本身没有类型，它必须被赋值给一个**函数式接口**类型的变量。

> **定义**：函数式接口是**有且仅有一个抽象方法**的接口（可以包含多个默认方法或静态方法）。

为了让编译器强制检查一个接口是否为函数式接口，Java 8 引入了 `@FunctionalInterface` 注解。

> **最佳实践**: 我们强烈推荐为所有函数式接口添加 `@FunctionalInterface` 注解，以明确接口意图并利用编译时检查。

  * **常用函数式接口速查表**

`java.util.function` 包中预定义了大量常用的函数式接口，以下是四大核心接口：

| 接口名 | 抽象方法 | 功能描述 |
| :--- | :--- | :--- |
| `Predicate<T>` | `boolean test(T t)` | **（主力/推荐）接收一个参数并返回一个布尔值，常用于过滤**。 |
| `Function<T, R>` | `R apply(T t)` | **（主力/推荐）接收一个参数，返回一个结果，常用于映射/转换**。 |
| `Supplier<T>` | `T get()` | 不接收参数，返回一个结果，常用于对象的**工厂方法**。 |
| `Consumer<T>` | `void accept(T t)` | 接收一个参数，没有返回值，常用于对元素执行某种**消费操作**（如打印）。 |

####### **场景2：四大核心函数式接口实战**

**背景**：我们将通过一个简单的程序来演示这四个核心接口在实际代码中的应用。

``` java
package com.example;

import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;

public class Main {
    public static void main(String [] args) {
        // 1. Predicate <T>: 判断一个整数是否大于 10
        Predicate <Integer> isGreaterThan10 = x -> x > 10;
        System.out.println("15 是否大于 10? " + isGreaterThan10.test(15));

        // 2. Function <T, R>: 将字符串转换为其长度
        Function <String, Integer> stringLengthFunction = s -> s.length();
        System.out.println("'hello' 的长度是: " + stringLengthFunction.apply("hello"));

        // 3. Consumer <T>: 打印传入的字符串
        Consumer <String> printConsumer = s -> System.out.println("正在消费: " + s);
        printConsumer.accept("This is a message.");

        // 4. Supplier <T>: 生成一个随机数
        Supplier <Double> randomSupplier = () -> Math.random();
        System.out.println("生成的随机数: " + randomSupplier.get());
    }
}
// 输出:
// 15 是否大于 10? true
// 'hello' 的长度是: 5
// 正在消费: This is a message.
// 生成的随机数: 0.12345... (每次不同)
```

> 你可能已经注意到，上面的示例代码中我们直接使用了 Predicate、Function 等接口，但并没有看到 @FunctionalInterface 注解的影子。这是为什么呢？

**关键在于区分“接口的定义者”与“接口的使用者”：**

1. **定义者**：`@FunctionalInterface` 注解是给**接口的创建者**使用的。Java 官方在 设计 Predicate, Function 等接口时，**已经在它们的源码中添加了 `@FunctionalInterface` 注解**。这保证了 JDK 提供的这些接口绝对符合函数式接口的规范。
2. **使用者**：在我们的示例代码中，我们是这些接口的**使用者**。我们直接利用这些已经由 JDK 定义好并验证过的接口作为 Lambda 表达式的类型，而不需要（也不能）再去重复声明注解。

#### 8.1.3 进阶：方法引用的四种类型 (`::`)

**方法引用** (`Method Reference`) 是 Lambda 表达式的一种特殊语法糖，它允许我们通过方法的名字来直接引用一个已经存在的方法。当 Lambda 表达式的方法体已经有现成的方法可以实现时，使用方法引用会让代码更加简洁易读。

**用更具体的例子来说：**

假设我们要对一个字符串列表进行排序。

*   **不使用方法引用：**
  
    ``` java
    List <String> names = Arrays.asList("张三", "李四", "王五");
    Collections.sort(names, (s1, s2) -> s1.compareTo(s2)); // Lambda 表达式
    ```
这里，`(s1, s2) -> s1.compareTo(s2)` 就是你的 Lambda 表达式，你是在“教” `sort` 方法如何比较两个字符串。
    
* **使用方法引用（就像直接用现成的菜）：**

  ``` java
  List <String> names = Arrays.asList("张三", "李四", "王五");
  Collections.sort(names, String:: compareTo); // 方法引用
  ```
  `String::compareTo` 就是对已经存在的 `String` 类里的 `compareTo` 方法的引用。你不再需要写 `(s1, s2) -> s1.compareTo(s2)`，而是直接说：“用 `String` 类那个叫 `compareTo` 的方法来比吧！”

| 方法引用类型 | 示例语法（实际代码） | Lambda 等价形式（实际代码） | 核心意图 |
| :--- | :--- | :--- | :--- |
| **引用静态方法** | `类名::静态方法名` | `参数 -> 类名.静态方法名(参数)` | 调用类的静态方法 |
| **引用特定类型的任意对象的实例方法** | `类名::实例方法名` | `对象实例, 参数 -> 对象实例.实例方法名(参数)` | 调用对象身上的实例方法（对象是传入的） |
| **引用特定对象的实例方法** | `具体对象::实例方法名` | `参数 -> 具体对象.实例方法名(参数)` | 调用某个固定对象的实例方法 |
| **引用构造函数** | `类名::new` | `参数 -> new 类名(参数)` | 创建类的实例 |

**说明：**

*   这里的“参数”是根据实际的函数式接口定义来决定的，可能是一个或多个参数，也可能没有参数。
*   “对象实例”在“引用特定类型的任意对象的实例方法”中，是作为第一个隐式参数传入的。
*   “具体对象”在“引用特定对象的实例方法”中，是方法引用语法的一部分，已经固定了。
####### **方法引用实战场景详解**

**背景**：我们将通过代码示例逐一展示四种方法引用的应用场景。

``` java
package com.example;

import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

public class Main {

    public static void main(String [] args) {
        List <String> strList = Arrays.asList("apple", "banana", "cherry");
        // 场景 1: 引用静态方法
        // 将字符串转换为整数
        Function <String, Integer> parseIntFunc = s -> Integer.parseInt(s.replaceAll("\\D+", ""));
        Function <String, Integer> valueOfFunc = Integer:: valueOf;
        System.out.println("字符串 '123' 转为整数: " + valueOfFunc.apply("123"));

        // 场景 2: 引用特定类型的任意对象的实例方法
        // 将字符串列表全部转为大写
        strList.stream().map(String:: toUpperCase).forEach(s -> System.out.print(s + " "));

        // 场景 3: 引用特定对象的实例方法
        // 使用 System.out 对象的 println 方法
        Consumer <String> printer = System.out:: println;
        printer.accept("Hello, Method Reference!");

        // 场景 4: 引用构造函数
        // 创建一个 Person 对象
        Supplier <Person> personSupplier = Person:: new;
        Person person = personSupplier.get();
        System.out.println("创建了一个新 Person: " + person);
    }
}

class Person {
    @Override
    public String toString() {
        return "A Person instance";
    }
}

```

> **小结**: 当 Lambda 的逻辑仅仅是调用一个已存在的方法时，方法引用是最佳选择。它能消除冗余的参数声明，让代码的**“意图”**而非**“实现细节”**凸显出来。

#### 8.1.4 [高频面试题] Lambda 与匿名内部类的区别

> **Q: Lambda 表达式和匿名内部类有什么核心区别？**
>
> **A:** 尽管 Lambda 表达式在很多场景下可以替代匿名内部类，但它们在底层实现和行为上存在显著差异。
>
> 1.  **`this` 的指向不同 (Scope)**: 这是最核心的区别。
>* **匿名内部类**: `this` 关键字指向的是匿名内部类**自身的实例**。
>       * **Lambda 表达式**: `this` 关键字指向的是其**外层封装类**的实例。Lambda 表达式本身没有自己的 `this`，它在词法上是其外层作用域的一部分。
>       
>2.  **编译后产物不同**:
> * **匿名内部类**: 编译器会为每一个匿名内部类生成一个单独的 `.class` 文件，格式通常是 `EnclosingClass$1.class`。
>      * **Lambda 表达式**: 编译器不会为每个 Lambda 生成一个独立的 `.class` 文件。它会将 Lambda 的逻辑翻译成一个私有的静态方法，并在运行时通过 `invokedynamic` 指令来动态地创建实现函数式接口的对象。这种方式在性能和内存占用上通常更优。
>       
> 3.  **功能限制**:
>* **匿名内部类**: 功能更强大。它可以实现有多个抽象方法的接口，可以继承具体的类，也可以拥有自己的实例变量和方法。
>       * **Lambda 表达式**: 功能更专注。它只能用于实现**函数式接口**（即只有一个抽象方法的接口）。
>      
> 4.  **性能 :
> * 由于 `invokedynamic` 的机制，JVM 在运行时有更多的优化空间，比如可以对 Lambda 的调用进行内联或者延迟实例化，因此在很多情况下，Lambda 的性能会优于或等于匿名内部类。



-----

### 8.2 [数据处理革命] [Java 8+] Stream API

> **本章导读**: 如果说 Lambda 表达式是 Java 8 的“引擎”，那么 Stream API 就是搭载了这个强大引擎的“超级跑车”。它提供了一套声明式、可组合、可并行的 API，彻底改变了我们对集合数据的处理方式。告别繁琐的 `for` 循环和临时变量，Stream API 让我们能以一种更优雅、更符合人类思维的“流水线”方式来操作数据。本节，我们将全面探索 Stream 的世界。

#### 8.2.1 核心概念：流的创建与生命周期

一个典型的 Stream 操作流包含三个阶段：

1.  **创建 (Creation)**: 从一个数据源（如集合、数组）获取一个流。
2.  **中间操作 (Intermediate Operations)**: 对流进行一系列的转换或筛选操作。每个中间操作都会返回一个新的流，这使得操作可以像链条一样串联起来。这些操作是**惰性求值 (Lazy)** 的，也就是说，它们在终端操作被调用前不会真正执行。
3.  **终端操作 (Terminal Operations)**: 触发流的实际计算，并产生一个最终结果（如一个值、一个集合，或无返回值）。终端操作是**及早求值 (Eager)** 的，一旦执行，流就会被消耗且无法再次使用。

####### **场景1：流的多种创建方式**

**背景**：在开始处理数据前，我们首先需要知道如何从不同的数据源创建流。

``` java
package com.example;

import java.util.Arrays;
import java.util.List;
import java.util.stream.IntStream;
import java.util.stream.Stream;

public class Main {
    public static void main(String [] args) {
        // 1. 从集合创建流 (最常用)
        List <String> list = Arrays.asList("a", "b", "c");
        Stream <String> listStream = list.stream();
        System.out.print("从 List 创建的流: ");
        listStream.forEach(s -> System.out.print(s + " "));
        System.out.println();

        // 2. 从数组创建流
        String [] array = {"x", "y", "z"};
        Stream <String> arrayStream = Arrays.stream(array);
        System.out.print("从 Array 创建的流: ");
        arrayStream.forEach(s -> System.out.print(s + " "));
        System.out.println();

        // 3. 使用 Stream.of() 静态方法创建流
        Stream <Integer> ofStream = Stream.of(1, 2, 3, 4, 5);
        System.out.print("使用 Stream.of() 创建的流: ");
        ofStream.forEach(s -> System.out.print(s + " "));
        System.out.println();
        
        // 4. 使用 Stream.iterate() 创建无限流
        Stream <Integer> iterateStream = Stream.iterate(0, n -> n + 2).limit(5); // 必须用 limit 限制
        System.out.print("使用 Stream.iterate() 创建的无限流 (限制后): ");
        iterateStream.forEach(s -> System.out.print(s + " "));
        System.out.println();
    }
}
// 输出:
// 从 List 创建的流: a b c 
// 从 Array 创建的流: x y z 
// 使用 Stream.of() 创建的流: 1 2 3 4 5 
// 使用 Stream.iterate() 创建的无限流 (限制后): 0 2 4 6 8 
```

> **小结**: 创建流是所有操作的第一步。`collection.stream()` 是最常用的方式。对于无限流，务必使用 `limit()` 等中间操作将其变为有限流，否则可能会导致无限循环。

#### 8.2.2 中间操作详解

中间操作是 Stream API 的精髓所在，它们允许我们将复杂逻辑分解为一系列小而清晰的步骤。

###### 1\. 筛选与切片 - 从流中选取我们需要的元素

  * **常用筛选与切片方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `filter(Predicate<T>)` | **(主力/推荐)** 根据条件过滤流中元素，只保留满足条件的。 |
| `distinct()` | 去除流中重复的元素（基于元素的 `equals()` 方法）。 |
| `limit(long)` | 截断流，使其元素数量不超过给定值。 |
| `skip(long)` | 跳过前 N 个元素，返回一个扔掉了前 N 个元素的新流。 |

####### **场景2：筛选出前两名不重复的偶数**

**背景**：给定一个整数列表，我们需要找到其中不重复的偶数，并只取前两个。

``` java
package com.example;

import java.util.Arrays;
import java.util.List;

public class Main {
    public static void main(String [] args) {
        List <Integer> numbers = Arrays.asList(2, 4, 6, 4, 8, 2, 10, 12);

        System.out.print("处理后的结果: ");
        numbers.stream()
                .filter(n -> n % 2 == 0)   // 筛选偶数: [2, 4, 6, 4, 8, 2, 10, 12]
                .distinct()               // 去重: [2, 4, 6, 8, 10, 12]
                .skip(1)                  // 跳过第一个: [4, 6, 8, 10, 12]
                .limit(2)                 // 取前两个: [4, 6]
                .forEach(n -> System.out.print(n + " "));
        System.out.println();
    }
}
// 输出:
// 处理后的结果: 4 6 
```

###### 2\. 映射 - 将流中元素转换为其他形式或提取信息

  * **常用映射方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `map(Function<T,R>)` | **(主力/推荐)** 将流中每个元素 `T` 转换为另一个元素 `R` (一对一映射)。 |
| `flatMap(Function<T,Stream<R>>)` | **(进阶/常用)** 将每个元素转换为一个新流，然后将所有子流连接成一个流 (一对多扁平化)。 |

####### **场景3：从单词列表获取所有不同的字符**

**背景**：给定一个单词列表 `["Hello", "World"]`，我们希望得到其中所有字符列表 `[H, e, l, l, o, W, o, r, l, d]`。

``` java
package com.example;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class Main {
    public static void main(String [] args) {
        List <String> words = Arrays.asList("Hello", "World");
        List <String> list = words.stream()
                .map(word -> word.split("")) // 1. 得到 Stream <String[]>
                .flatMap(Arrays:: stream)   // 2. 扁平化为 Stream <String>
                .toList();// 3. 转为 List

        System.out.println(list);

    }
}
// 输出：
// [H, e, l, l, o, W, o, r, l, d]


```

> **小结**: 当你的转换逻辑会从**一个元素**产生**多个结果**时（例如，从一个单词产生多个字符），`flatMap` 就是你需要的。

###### 3\. 排序 - 对流中元素进行排序

  * **常用排序方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `sorted()` | 按自然顺序排序（元素需实现 `Comparable`）。 |
| `sorted(Comparator<T>)` | **(主力/推荐)** 根据自定义比较器进行排序，表达能力更强。 |

####### **场景4：对自定义对象进行排序**

**背景**：我们有一个 `Product` 列表，需要先按价格降序排序，如果价格相同，再按名称升序排序。

``` java
package com.example;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

public class Main {
    public static void main(String [] args) {
        List <Product> products = Arrays.asList(
                new Product("Laptop", 1200), new Product("Mouse", 50),
                new Product("Keyboard", 100), new Product("MousePad", 50),
                new Product("Keyboard", 300), new Product("MousePad", 50)
        );
        // 原生 Stream 流的实现方式
        products.stream()
                .sorted(Comparator.comparing(Product:: getPrice).reversed() // 按价格降序
                        .thenComparing(Product:: getName))       // 再按名称升序
                .forEach(System.out:: println);

    }
}

@Data
@AllArgsConstructor
class Product {
    private String name;
    private int price;
}


```

> **小结**: `Comparator` 接口在 Java 8 中也得到了极大的增强，`comparing()`, `thenComparing()`, `reversed()` 等静态和默认方法使得构建复杂的多级排序逻辑变得异常简单和直观。

#### 8.2.3 终端操作详解

终端操作是流处理的最后一步，它会触发所有懒加载的中间操作并生成最终结果。

###### 1\. 匹配与查找 - 检查流中元素是否满足特定条件

  * **常用匹配与查找方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `anyMatch(Predicate<T>)` | 检查流中是否**至少有一个**元素匹配条件。 |
| `allMatch(Predicate<T>)` | 检查流中是否**所有**元素都匹配条件。 |
| `noneMatch(Predicate<T>)`| 检查流中是否**没有**元素匹配条件。 |
| `findFirst()` | 返回流的第一个元素，用 `Optional` 包装。 |
| `findAny()` | 返回流中的任意一个元素，用 `Optional` 包装（并行流中更高效）。 |

####### **场景5：检查产品列表中是否存在高价商品**

``` java
package com.example;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;


@Data
@AllArgsConstructor
class Product {
    private String name;
    private int price;
}

public class Main {
    public static void main(String [] args) {
         List <Product> products = Arrays.asList(
            new Product("Laptop", 1200), new Product("Mouse", 50)
        );
        boolean hasExpensiveProduct = products.stream().anyMatch(p -> p.getPrice() > 1000);
        System.out.println("是否有价格 > 1000 的产品? " + hasExpensiveProduct);
    }
}
// 输出:
// 是否有价格 > 1000 的产品? true
```

###### 2\. 规约 (Reduction) - 将流中所有元素计算，得到一个值

  * **常用规约方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `reduce(T identity, BinaryOperator<T>)` | **(主力/推荐)** 从一个初始值 `identity` 开始，对流中所有元素进行规约操作。 |
| `reduce(BinaryOperator<T>)` | 无初始值的规约，因为流可能为空，所以返回一个 `Optional`。 |

####### **场景6：计算所有产品价格的总和**

``` java
package com.example;

import java.util.Arrays;
import java.util.List;

@Data
@AllArgsConstructor
class Product {
    private String name;
    private int price;
}
public class Main {
    public static void main(String [] args) {
        List <Product> products = Arrays.asList(
            new Product("Laptop", 1200), new Product("Mouse", 50)
        );
        int totalPrice = products.stream()
            .map(Product:: getPrice)
            .reduce(0, Integer:: sum); // 0 是初始值, Integer:: sum 是 (a, b) -> a + b
        System.out.println("所有产品的总价是: " + totalPrice);
    }
}
// 输出:
// 所有产品的总价是: 1250
```

###### 3\. 收集 (Collect) - 将流中元素转换成集合、Map或其他复杂对象

  * **常用收集器 (`Collectors`) 速查表**

| 收集器 (`Collector`) | 功能描述 |
| :--- | :--- |
| `toList()` / `toSet()` | **(最常用)** 将流中元素收集到 `List` 或 `Set`。 |
| `toMap(keyMapper, valueMapper)` | 将流中元素收集到 `Map`，需注意 key 重复问题。 |
| `groupingBy(classifier)` | **(主力/推荐)** 根据分类函数对元素进行分组，返回一个 `Map`。 |
| `joining(delimiter)` | 将流中元素（通常是字符串）连接成一个字符串。 |

####### **场景7：将产品列表按价格分组**

``` java
package com.example;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@Data
@AllArgsConstructor
class Product {
    private String name;
    private int price;
}
public class Main {
    public static void main(String [] args) {
        List <Product> products = Arrays.asList(
                new Product("Laptop", 1200), new Product("Mouse", 50),
                new Product("Keyboard", 100), new Product("MousePad", 50)
        );

        Map <Integer, List<Product> > productsByPrice = products.stream()
                .collect(Collectors.groupingBy(Product:: getPrice));

        System.out.println("按价格分组的结果:");
        productsByPrice.forEach((price, list) -> System.out.println("价格: " + price + " -> " + list));
    }
}
// 输出:
// 按价格分组的结果:
// 价格: 50 -> [Product{name ='Mouse', price = 50}, Product{name ='MousePad', price = 50}]
// 价格: 100 -> [Product{name ='Keyboard', price = 100}]
// 价格: 1200 -> [Product{name ='Laptop', price = 1200}]
```

> **小结**: `Collectors` 工具类提供了极其丰富和强大的收集器，是 `collect` 操作的“弹药库”。熟练掌握 `Collectors` 是高效使用 Stream API 的关键。



-----

### 8.3 [空指针终结者] [Java 8+] Optional 容器类

> **本章导读**: `NullPointerException` (NPE) 被其创造者称为“价值十亿美元的错误”，它长久以来都是 Java 开发者的噩梦。为了应对这一挑战，Java 8 引入了 `Optional<T>`。它是一个容器类，其核心设计思想并非简单地替代 `null`，而是通过**类型系统**来显式地表达一个值**可能缺失**的情况。这迫使我们作为调用者，必须正视并处理值不存在的可能性，从而将潜在的运行时 NPE 转换为编译时可见的设计考量，让代码更加健壮和优雅。

#### 8.3.1 设计哲学：为什么需要 Optional

在 `Optional` 出现之前，方法通常通过返回 `null` 来表示“没有找到结果”。这种做法存在一个致命缺陷：`null` 的含义是模糊的，并且完全依赖于调用者的“自觉性”去进行非空检查。

  * **返回 `null` 的问题**:
    1.  **意图不明**: `null` 可能代表“查询无结果”、“操作失败”或“值本身就是空”，容易引起混淆。
    2.  **契约不清**: 方法签名 `User findById(long id)` 无法告诉调用者它可能会返回 `null`，这是一种隐藏的、脆弱的契约。
    3.  **风险转嫁**: 将校验 `null` 的责任完全抛给了调用方，一旦忘记检查，`NullPointerException` 就会在运行时不期而至。

`Optional` 通过将“可能没有值”这一情况包装成一个对象，完美地解决了上述问题。一个返回 `Optional<User>` 的方法，其签名本身就在大声宣告：“我返回的可能是一个 `User`，也可能什么都没有，请你务必处理这两种情况！”

#### 8.3.2 核心方法分类使用

###### 1\. 创建 Optional 对象 - 包装你的值

  * **常用创建方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `of(T value)` | **(谨慎使用)** 为一个**你确定非 null** 的值创建一个 `Optional`。如果 `value` 为 `null`，会立即抛出 `NullPointerException`。 |
| `ofNullable(T value)` | **(主力/推荐)** 为一个**可能为 null** 的值创建一个 `Optional`。如果 `value` 为 `null`，则返回一个空的 `Optional` 对象。这是最安全、最常用的创建方式。 |
| `empty()` | 创建一个明确的、空的 `Optional` 实例。 |

####### **场景1：创建 Optional 实例**

**背景**：我们有一个方法，可能返回一个 `User` 对象，也可能返回 `null`。我们需要将这个结果安全地包装起来。

``` java
package com.example;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Optional;

public class Main {
    public static void main(String [] args) {
        User user = new User("John Doe");
        User nullUser = null;

        // 1. ofNullable: 最安全的方式
        Optional <User> optUser = Optional.ofNullable(user);
        Optional <User> optNullUser = Optional.ofNullable(nullUser);
        System.out.println("ofNullable (有值): " + optUser.isPresent());  // ofNullable (有值): true
        System.out.println("ofNullable (无值): " + optNullUser.isPresent()); // ofNullable (无值): false
        // 2. of: 当你确定值不为 null 时使用
        Optional <User> optUserOf = Optional.of(user);
        System.out.println("of (有值): " + optUserOf.isPresent()); // of (有值): true
        // 下面这行会立即抛出 NullPointerException
        // Optional.of(nullUser);

        // 3. empty: 创建一个空实例
        Optional <User> emptyOpt = Optional.empty();
        System.out.println("empty: " + emptyOpt.isPresent()); // empty: false

    }
}
@Data @AllArgsConstructor
class User {private String name;}
```

###### 2\. 获取值（带默认处理） - 安全地取出结果

> **[避坑指南]**: 应尽量避免使用 `get()` 方法。它在 `Optional` 为空时会抛出 `NoSuchElementException`，与我们期望避免运行时异常的初衷背道而驰。只有在万分确定 `Optional` 中有值时（例如，在 `isPresent()` 判断之后），才可使用。

  * **安全获取值方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `orElse(T other)` | **(常用)** 如果 `Optional` 中有值，则返回该值；否则返回指定的默认值 `other`。**注意**：无论 `Optional` 是否为空，`other` 参数都会被求值。|
| `orElseGet(Supplier<? extends T>)` | **(主力/推荐)** 如果 `Optional` 中有值，则返回该值；否则调用 `Supplier` 函数式接口来生成一个默认值。**优势**：`Supplier` 只在 `Optional` 为空时才会被调用，实现了懒加载，性能更优。|
| `orElseThrow(Supplier<? extends X>)` | 如果 `Optional` 中有值，则返回该值；否则抛出由 `Supplier` 生成的异常。|

####### **场景2：为可能不存在的用户提供默认名称**

**背景**：我们需要从一个 `Optional<User>` 中获取用户名，如果用户不存在，则返回 "Guest"。

``` java
package com.example;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Optional;

@Data @AllArgsConstructor
class User {private String name;}
public class Main {
    public static void main(String [] args) {
        Optional <User> userOpt = Optional.of(new User("Alice"));
        Optional <User> emptyOpt = Optional.empty();

        // orElse: 无论 Optional 是否为空，"new User(" Guest ")" 都会被执行
        String name1 = userOpt.orElse(new User("Guest")).getName();
        System.out.println("orElse (有值时): " + name1);

        String name2 = emptyOpt.orElse(new User("Guest")).getName();
        System.out.println("orElse (无值时): " + name2);

        // orElseGet: 只有在 Optional 为空时，lambda 表达式才会执行
        String name3 = emptyOpt.orElseGet(() -> new User("Default User")).getName();
        System.out.println("orElseGet (无值时): " + name3);
    }
}
// 输出:
// orElse (有值时): Alice
// orElse (无值时): Guest
// orElseGet (无值时): Default User
```

###### 3\. 值的转换与过滤 - 以函数式风格处理值

这些方法让我们可以像操作 Stream 一样，对 `Optional` 内部的值进行链式处理，而无需显式地进行非空判断。

  * **常用转换与过滤方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `map(Function<T, R>)` | **(主力/推荐)** 如果值存在，则对其应用 `Function` 映射，并返回一个包装了新值的 `Optional<R>`。如果值不存在，则直接返回一个空的 `Optional<R>`。|
| `flatMap(Function<T, Optional<R>>)` | **(进阶/常用)** 与 `map` 类似，但要求映射函数本身返回一个 `Optional`。`flatMap` 会将结果“扁平化”，避免出现 `Optional<Optional<T>>` 这样的嵌套结构。|
| `filter(Predicate<T>)` | 如果值存在且满足 `Predicate` 条件，则返回包含该值的 `Optional`；否则返回一个空的 `Optional`。|

####### **场景3：获取用户地址的邮政编码（多级可能为空）**

**背景**：`User` 可能没有 `Address`，`Address` 可能没有 `zipCode`。我们需要安全地获取邮编，如果中间任何一环为空，都应返回 "UNKNOWN"。

``` java
package com.example;

import java.util.Optional;

class Address {
    private String zipCode;
    public Address(String zipCode) { this.zipCode = zipCode; }
    public Optional <String> getZipCode() { return Optional.ofNullable(zipCode); }
}
class UserWithAddress {
    private Address address;
    public UserWithAddress(Address address) { this.address = address; }
    public Optional <Address> getAddress() { return Optional.ofNullable(address); }
}
public class Main {
    public static void main(String [] args) {
        // 一个拥有完整地址信息的用户
        Address address = new Address("100-0001");
        UserWithAddress user = new UserWithAddress(address);
        Optional <UserWithAddress> userOpt = Optional.of(user);

        // flatMap 用于处理返回 Optional 的 getter
        String zip = userOpt
                .flatMap(UserWithAddress:: getAddress)  // 返回 Optional <Address>
                .flatMap(Address:: getZipCode)         // 返回 Optional <String>
                .orElse("UNKNOWN");

        System.out.println("用户的邮政编码是: " + zip); // 用户的邮政编码是: 100-0001

        // 一个没有地址信息的用户
        UserWithAddress userWithoutAddress = new UserWithAddress(null);
        Optional <UserWithAddress> userOpt2 = Optional.of(userWithoutAddress);
        String zip2 = userOpt2
                .flatMap(UserWithAddress:: getAddress)
                .flatMap(Address:: getZipCode)
                .orElse("UNKNOWN");
        System.out.println("无地址用户的邮政编码是: " + zip2); // 无地址用户的邮政编码是: UNKNOWN
    }
}
// 输出:
// 用户的邮政编码是: 100-0001
// 无地址用户的邮政编码是: UNKNOWN
```

> **小结**: 当你的 `map` 操作返回的是一个 `Optional` 对象时，就应该使用 `flatMap` 来代替 `map`，以保持结构的扁平。

###### 4\. 判断与消费 - 在值存在时执行操作

  * **常用判断与消费方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `isPresent()` | **(不推荐)** 检查值是否存在。通常可以用函数式方法替代，以避免命令式的 `if` 语句。|
| `ifPresent(Consumer<T>)` | **(主力/推荐)** 如果值存在，则对该值执行 `Consumer` 操作。这是处理“有值”情况的最常用方式。|
| `ifPresentOrElse(Consumer<T>, Runnable)` | `[Java 9+]` 如果值存在，执行第一个 `Consumer`；否则，执行第二个 `Runnable` 任务。|

####### **场景4：如果用户存在，就打印其信息**

``` java
package com.example;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Optional;

@Data
@AllArgsConstructor
class User {private String name;}
public class Main {
    public static void main(String [] args) {
        Optional <User> userOpt = Optional.of(new User("Charles"));
        Optional <User> emptyOpt = Optional.empty();

        // 只有在 userOpt 有值时，才会执行 lambda
        userOpt.ifPresent(user -> System.out.println("用户信息: " + user.getName())); // 用户信息: Charles

        // emptyOpt 为空，lambda 不会被执行
        emptyOpt.ifPresent(user -> System.out.println("这段代码不会被执行"));

        // Java 9+ 的 ifPresentOrElse
         userOpt.ifPresentOrElse(
             user -> System.out.println("用户信息: " + user.getName()), // 用户信息: Charles
             () -> System.out.println("用户不存在") // 若用户不存在，则会执行此 lambda
         );
    }
}
// 输出:
// 用户信息: Charles
```



-----

### 8.4 [语言增强] [Java 8+] 语言及 API 增强

> **本章导读**: 除了像 Stream 和 Optional 这样的大型主题外，Java 8 及后续版本还带来了许多“小而美”的改进。本节将作为这些实用特性的一个合集，我们将学习接口如何通过默认方法和静态方法实现优雅演进，了解如何用一行代码创建不可变集合，并探索 String、Files 等日常核心 API 的便利新方法，以及可重复注解和 Base64 API 等实用工具。

#### 8.4.1 接口的演进 `[Java 8]`

在 Java 8 之前，接口是“纯粹”的，只能包含抽象方法和常量。这种设计的弊端在于，一旦接口发布，就很难再向其中添加新方法，因为这会强制所有已有的实现类都去实现这个新方法，造成大规模的代码破坏。Java 8 通过引入默认方法和静态方法，完美地解决了这个问题。

###### 1\. 默认方法 (`default`) - 在不破坏实现的前提下为接口添加新功能

**默认方法**允许我们在接口中提供一个方法的默认实现。实现该接口的类将自动继承这个默认方法，无需强制重写。

这对于API的向后兼容和优雅演进至关重要。

  * **常用方法速查表**

| 关键字 | 功能描述 |
| :--- | :--- |
| `default` | 在接口方法声明前使用，用于提供一个默认的方法体实现。 |

####### **场景1：为一个 `Logger` 接口添加新的日志级别方法**

**背景**：假设我们有一个已广泛使用的 `Logger` 接口，现在希望为它增加一个 `logWarning` 方法，但又不想让所有旧的实现类都报错。

``` java
package com.example;

interface Logger {
    void logInfo(String message);

    // 使用 default 关键字为接口添加一个新方法
    default void logWarning(String message) {
        System.out.println("【Default Warning】: " + message);
    }
}

// 一个老的实现类，它只实现了 logInfo
class SimpleFileLogger implements Logger {
    @Override
    public void logInfo(String message) {
        System.out.println("【File Info】: " + message);
    }
}

// 一个新的实现类，它选择重写默认方法
class ConsoleLogger implements Logger {
    @Override
    public void logInfo(String message) {
        System.out.println("【Console Info】: " + message);
    }

    @Override
    public void logWarning(String message) {
        System.err.println("【CONSOLE WARNING】: " + message);
    }
}

public class Main {
    public static void main(String [] args) {
        Logger fileLogger = new SimpleFileLogger();
        fileLogger.logInfo("Application started.");
        // 老的实现类可以直接调用新的默认方法，而无需修改自身代码
        fileLogger.logWarning("Configuration is missing.");

        System.out.println("--------------------");

        Logger consoleLogger = new ConsoleLogger();
        consoleLogger.logInfo("Processing data...");
        // 新的实现类调用的是它自己重写后的版本
        consoleLogger.logWarning("Disk space is low.");
    }
}
// 输出:
// 【File Info】: Application started.
// 【Default Warning】: Configuration is missing.
// --------------------
// 【Console Info】: Processing data...
// 【CONSOLE WARNING】: Disk space is low.
```

> **小结**: **默认方法**是 Java API 设计者向库中添加新功能而**不破坏向后兼容性**的强大工具。它使得接口的演进变得平滑和安全。

###### 2\. 静态方法 (`static`) - 在接口中定义工具方法

Java 8 还允许在接口中定义**静态方法**。这些方法不属于任何对象实例，而是直接属于接口本身。这使得我们可以将一些相关的工具方法直接组织在接口内部，而不是创建一个单独的 `xxxUtils` 工具类

  * **常用方法速查表**

| 关键字 | 功能描述 |
| :--- | :--- |
| `static` | 在接口方法声明前使用，定义一个属于接口本身的静态方法。 |

####### **场景2：在 `Logger` 接口中提供一个工厂方法**

**背景**：我们希望提供一个简单的方式来获取 `Logger` 的实例，可以直接在 `Logger` 接口中定义一个静态工厂方法。

``` java
package com.example;

interface LoggerWithFactory {
    void log(String message);

    // 在接口中定义一个静态工厂方法
    static LoggerWithFactory createConsoleLogger() {
        return new ConsoleLoggerImpl();
    }
}

class ConsoleLoggerImpl implements LoggerWithFactory {
    @Override
    public void log(String message) {
        System.out.println("LOG: " + message);
    }
}

public class Main {
    public static void main(String [] args) {
        // 直接通过接口名调用静态方法，就像调用普通工具类一样
        LoggerWithFactory logger = LoggerWithFactory.createConsoleLogger();
        logger.log("This is a test message.");
    }
}
// 输出:
// LOG: This is a test message.
```

> **小结**: **接口静态方法**进一步提升了接口作为**代码组织单元**的能力，让接口不仅能定义契约，还能提供相关的辅助工具。

#### 8.4.2 集合工厂方法 `[Java 9+]`

在 Java 9 之前，创建一个包含少量元素的集合通常需要好几行代码。Java 9 引入了一系列静态工厂方法 `of()`，极大地简化了**不可变集合**的创建。

  * **常用方法速查表**

| 方法名 | 功能描述 |
| :--- | :--- |
| `List.of(...)` | **(主力/推荐)** 创建一个**不可变**的 `List`。 |
| `Set.of(...)` | 创建一个**不可变**的 `Set`，不允许重复元素。 |
| `Map.of(k1, v1, ...)` | 创建一个**不可变**的 `Map`，不允许重复的键。 |

> **[避坑指南]**: 使用 `of()` 方法创建的集合是**不可变的** (`Immutable`)。任何尝试对其进行添加、删除等修改操作的行为，都会抛出 `UnsupportedOperationException`。此外，它们**不允许**存入 `null` 元素。

####### **场景3：快速创建只读的配置集合**

**背景**：我们需要创建一个包含默认配置项的 `Map`，这个配置在程序运行期间不应被修改。

``` java
package com.example;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class Main {
    public static void main(String [] args) {
        // 创建不可变 List
        List <String> names = List.of("Alice", "Bob", "Charlie");
        System.out.println("Immutable List: " + names);

        // 创建不可变 Set
        Set <Integer> numbers = Set.of(10, 20, 30);
        System.out.println("Immutable Set: " + numbers);

        // 创建不可变 Map
        Map <String, String> config = Map.of(
            "user", "admin",
            "password", "secret",
            "timeout", "3000"
        );
        System.out.println("Immutable Map: " + config);

        // 尝试修改会抛出异常
        try {
            names.add("David");
        } catch (UnsupportedOperationException e) {
            System.out.println("捕获到异常: " + e.getClass().getSimpleName());
        }
    }
}
// 输出:
// Immutable List: [Alice, Bob, Charlie]
// Immutable Set: [10, 20, 30]
// Immutable Map: {user = admin, password = secret, timeout = 3000}
// 捕获到异常: UnsupportedOperationException
```

#### 8.4.3 常用 API 增强 `[Java 11+]`

Java 在后续版本中，也持续对一些我们日常使用最频繁的类（如 `String`）进行功能增强。

  * **常用 String API 增强速查表**

| 方法名 | 引入版本 | 功能描述 |
| :--- | :--- | :--- |
| `isBlank()` | Java 11 | 判断字符串是否为空白（`isEmpty()` 或只包含空白字符）。 |
| `lines()` | Java 11 | 将字符串按行分隔符 `\n` 拆分为一个 `Stream<String>`。 |
| `repeat(int)` | Java 11 | 将字符串重复指定次数。 |
| `strip()` | Java 11 | 去除字符串首尾的空白字符（比 `trim()` 更能识别 Unicode 空白符）。 |

####### **场景4：处理多行文本并去除空白行**

**背景**：我们从外部系统获取了一段文本，其中包含多行内容和一些空白行，需要进行清洗。

``` java
package com.example;

import java.util.stream.Collectors;

public class Main {
    public static void main(String [] args) {
        String multilineText = "" "
            
            First line
            Second line
            
            Third line
            "" "; // 这是 Java 15 的文本块语法，此处用于方便地表示多行文本

        System.out.println("--- 清洗前的文本 ---");
        System.out.print(multilineText);
        System.out.println("--- 清洗后的文本 ---");

        String cleanedText = multilineText.lines()      // 1. 将文本分割成一个 Stream <String>
                .filter(line -> ! line.isBlank())        // 2. 使用 isBlank() 过滤掉空白行
                .map(String:: strip)                     // 3. 使用 strip() 去除每行首尾的空白
                .collect(Collectors.joining("\n")); // 4. 重新组合成一个字符串

        System.out.println(cleanedText);

        System.out.println("\n--- 其他 String API ---");
        System.out.println("重复三次 'Abc': " + "Abc".repeat(3));
    }
}
// 输出:
// --- 清洗前的文本 ---
// 
// First line
// Second line
// 
// Third line
// 
// --- 清洗后的文本 ---
// First line
// Second line
// Third line
// 
// --- 其他 String API ---
// 重复三次 'Abc': AbcAbcAbc
```


好的，我们已经完成了 `8.4 语言及 API 增强` 中多个版本的特性学习。

接下来，我们进入一个在 Java 10 中引入的、极大提升编码便利性的语法糖——**`8.5 局部变量类型推断 (var)`**。

-----

### 8.5 [语法现代化] [Java 10+] 局部变量类型推断 (var)

> **本章导读**: 在 Java 10 之前，我们声明变量时必须在左侧明确写出其类型，即使在右侧的初始化语句中类型已经非常清晰。这种冗余在处理复杂的泛型类型时尤为明显。为了解决这一问题，Java 10 引入了 `var` 关键字，它允许编译器根据变量的初始化表达式**自动推断**出其类型。这并非引入了动态类型，`var` 声明的变量仍然是**静态类型**的，它仅仅是为我们省去了手动声明类型的麻烦，让代码更简洁。

#### 8.5.1 `var` 的使用场景与优势

`var` 主要的优势在于能够简化代码，尤其是在处理嵌套泛型等复杂类型时，能极大地提升代码的可读性。

  * **`var` 常用场景速查表**

| 场景 | 示例 | 优势 |
| :--- | :--- | :--- |
| **简化复杂类型声明** | `var userMap = new HashMap<String, List<User>>();` | **(核心优势)** 大幅减少冗余的模板代码，让代码更聚焦于变量名和业务逻辑。 |
| **for-each 循环** | `for (var user : userList) { ... }` | 简化循环变量的声明。 |
| **try-with-resources** | `try (var reader = new BufferedReader(...)) { ... }` | 简化资源变量的声明，使代码更紧凑。 |

####### **场景1：简化复杂 Map 的声明**

**背景**：我们需要创建一个 `Map`，其键是字符串，值是一个存储了 `User` 对象的 `List`。我们来对比一下使用 `var` 前后的代码差异。

```java
package com.example;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

class User {
    private String name;
    public User(String name) { this.name = name; }
    @Override public String toString() { return "User{name='" + name + "'}"; }
}

public class Main {
    public static void main(String[] args) {
        // Java 10 之前的写法，类型声明非常冗长
        Map<String, List<User>> userMapBefore = new HashMap<>();
        userMapBefore.put("groupA", new ArrayList<>(List.of(new User("Alice"))));

        // 使用 var 的写法，代码瞬间清爽
        var userMapAfter = new HashMap<String, List<User>>();
        userMapAfter.put("groupB", new ArrayList<>(List.of(new User("Bob"))));

        // 编译器为 userMapAfter 推断出的类型仍然是 Map<String, List<User>>
        System.out.println("userMapAfter 的类型是 Map: " + (userMapAfter instanceof Map));
        System.out.println("userMapAfter 的内容: " + userMapAfter);
        
        System.out.println("--- for-each 循环中使用 var ---");
        var userList = List.of(new User("Charlie"), new User("David"));
        for (var user : userList) {
            // user 的类型被正确推断为 User
            System.out.println("循环中的用户: " + user);
        }
    }
}
// 输出:
// userMapAfter 的类型是 Map: true
// userMapAfter 的内容: {groupB=[User{name='Bob'}]}
// --- for-each 循环中使用 var ---
// 循环中的用户: User{name='Charlie'}
// 循环中的用户: User{name='David'}
```

> **小结**: `var` 让我们的代码不再被冗长的类型声明所淹没，尤其是在泛型和复杂的类名中。它鼓励我们为变量起一个更有意义的名字，因为类型的上下文已经由初始化表达式提供了。

#### 8.5.2 [避坑指南] `var` 的使用限制

`var` 虽然好用，但它不是“万金油”，只能在特定的上下文中使用。滥用或误用反而会降低代码的可读性。

> **核心原则**: **所有 `var` 声明的变量，编译器都必须能在编译时、仅通过其初始化表达式就明确地推断出它的唯一类型。**

以下是 `var` 的主要使用限制：

1.  **必须有初始化器**: 不能只声明不赋值。

      * 错误: `var name;`
      * 正确: `var name = "Alice";`

2.  **不能用 `null` 初始化**: `var` 无法从 `null` 推断出具体类型。

      * 错误: `var data = null;`

3.  **只能用于局部变量**: 这是最重要的一条规则。`var` **不能**用于：

      * 类的成员变量（字段）
      * 方法的参数
      * 方法的返回类型

4.  **Lambda 表达式和方法引用需要显式目标类型**:

      * 错误: `var runnable = () -> System.out.println("Hello");`
      * 原因: Lambda 表达式的类型依赖于其上下文的目标接口，仅凭 Lambda 自身无法推断。
      * 正确: `Runnable runnable = () -> System.out.println("Hello");`

5.  **小心泛型和菱形操作符 (`<>`)**:

      * `var list = new ArrayList<>();` 这行代码是合法的，但 `list` 的类型会被推断为 `ArrayList<Object>`，这可能不是我们想要的。
      * **最佳实践**: 如果想让 `var` 推断出正确的泛型类型，应在初始化表达式中明确指定它：`var stringList = new ArrayList<String>();`

####### **场景2：`var` 的非法使用示例**

**背景**：下面的代码展示了几种常见的 `var` 错误用法，帮助我们加深理解。

```java
package com.example;

import java.util.function.Consumer;

public class Main {
    // 错误1：不能用于成员变量
    // private var instanceVariable = "error";

    // 错误2：不能用于方法返回类型
    // public var getMessage() { return "error"; }

    // 错误3：不能用于方法参数
    // public void printMessage(var message) { System.out.println(message); }

    public static void main(String[] args) {
        // ------------------ 合法用法 ------------------
        var validMessage = "This is a valid local variable.";
        System.out.println(validMessage);

        // ------------------ 非法用法 ------------------
        // 错误4：没有初始化器
        // var name; 

        // 错误5：用 null 初始化
        // var data = null;

        // 错误6：Lambda 表达式需要显式目标类型
        // var printer = s -> System.out.println(s);
        
        // 正确的 Lambda 用法
        Consumer<String> printer = s -> System.out.println(s);
        printer.accept("Lambda with explicit type is fine.");
    }
}
// 输出:
// This is a valid local variable.
// Lambda with explicit type is fine.
```

> **小结**: `var` 是一个强大的便利工具，但绝不能滥用。在类型不明显或者会降低代码清晰度的场景下，我们仍应坚持使用明确的类型声明。**代码首先是写给人看的，其次才是给机器执行的。**



好的，我们已经学习了 Java 10 引入的 `var` 关键字。

接下来，我们将把目光投向 Java 16 和 17 中两个重要的“现代化 Java”基石，它们共同致力于增强 Java 的数据建模和领域建模能力。我们将把它们合并在 **`8.6 Record 类与 Sealed 类`** 这一节中进行讲解。

-----

### 8.6 [语法现代化] [Java 16/17+] Record 类与 Sealed 类

> **本章导读**: 随着应用日益复杂，如何清晰、准确地对业务领域进行建模变得至关重要。`Record` 类 (JEP 395, Java 16) 和 `Sealed` 类 (JEP 409, Java 17) 是 Java 在这方面给出的强力回应。`Record` 类旨在用最少的代码创建不可变的数据载体，彻底终结了传统 POJO/DTO 的冗长样板代码。而 `Sealed` 类则允许我们创建可控的、封闭的继承体系。这两者结合，特别是在后续的模式匹配中，能够构建出既安全又极具表达力的领域模型。

#### 8.6.1 Record 类：不可变数据载体 `[Java 16+]`

在 `Record` 出现之前，创建一个简单的数据类（如一个二维坐标点），需要我们手动编写构造函数、`getter`、以及 `equals()`、`hashCode()`、`toString()` 方法。这些都是高度重复且容易出错的样板代码。`Record` 类就是为了解决这个问题而生的。

> **核心思想**: **`Record` 是一种特殊的类，它专门用于充当不可变数据的“透明载体”。** 我们只需在声明时定义其“组件”，编译器就会自动为我们生成所有必要的成员。

  * **`record` 自动生成内容速查表**

| 当我们声明 `record Point(int x, int y) {}` 时，编译器自动生成: |
| :--- |
| `private final int x;` 和 `private final int y;` (私有 `final` 字段) |
| 一个包含所有组件的公共构造器 `public Point(int x, int y)` |
| 每个组件的公共“访问器”方法，如 `public int x()` 和 `public int y()` (注意：不是 `getX()`) |
| 一个完整的 `public boolean equals(Object o)` 实现 |
| 一个完整的 `public int hashCode()` 实现 |
| 一个完整的 `public String toString()` 实现 |

####### **场景1：使用 `record` 定义一个坐标点类**

**背景**：我们需要一个 `Point` 类来表示二维坐标。我们来对比一下传统方式和 `record` 方式的巨大差异。

```java
package com.example;

// 这是使用 record 的方式，仅需一行！
record Point(int x, int y) {
    // 我们还可以在 record 内部添加静态方法或自定义的实例方法
    public double distanceToOrigin() {
        return Math.sqrt(x * x + y * y);
    }
}

/*
// 如果不用 record，我们需要手写这么多代码：
class PointBeforeRecord {
    private final int x;
    private final int y;

    public PointBeforeRecord(int x, int y) { this.x = x; this.y = y; }
    public int getX() { return x; }
    public int getY() { return y; }
    // ... 还需要手动实现 equals(), hashCode(), toString() ...
}
*/

public class Main {
    public static void main(String[] args) {
        // 创建 record 实例
        Point p1 = new Point(3, 4);
        Point p2 = new Point(3, 4);
        Point p3 = new Point(5, 12);

        // 1. 访问器方法
        System.out.println("p1 的 x 坐标是: " + p1.x()); // 注意是 p1.x() 而不是 p1.getX()
        System.out.println("p1 的 y 坐标是: " + p1.y());

        // 2. toString()
        System.out.println("p1 的字符串表示: " + p1);

        // 3. equals()
        System.out.println("p1 和 p2 是否相等? " + p1.equals(p2));
        System.out.println("p1 和 p3 是否相等? " + p1.equals(p3));
        
        // 4. hashCode()
        System.out.println("p1 的哈希码: " + p1.hashCode());
        System.out.println("p2 的哈希码: " + p2.hashCode());

        // 5. 调用自定义方法
        System.out.printf("p3 到原点的距离是: %.2f\n", p3.distanceToOrigin());
    }
}
// 输出:
// p1 的 x 坐标是: 3
// p1 的 y 坐标是: 4
// p1 的字符串表示: Point[x=3, y=4]
// p1 和 p2 是否相等? true
// p1 和 p3 是否相等? false
// p1 的哈希码: 100
// p2 的哈希码: 100
// p3 到原点的距离是: 13.00
```

> **小结**: `Record` 类是创建**不可变数据聚合**的理想选择。它用最少的语法，提供了完整、正确的功能，极大地减少了样板代码，使我们的模型类定义更清晰、更可靠。

#### 8.6.2 Sealed 类/接口：精准的继承控制 `[Java 17+]`

在面向对象设计中，我们有时希望限制一个类或接口的继承体系，明确地指定“谁可以成为我的子类”。例如，一个 `Shape`（形状）接口，我们可能只希望它被 `Circle`（圆形）、`Square`（方形）和 `Triangle`（三角形）实现。

`Sealed` 类/接口正是为此而生，它允许我们创建一个**封闭的、可控的继承层次结构**。

> **核心语法**: 使用 `sealed` 关键字修饰类或接口，并使用 `permits` 关键字列出所有允许继承或实现的直接子类。

  * **子类的规则**

所有被 `permits` 关键字指定的子类，都必须遵循以下三条规则之一：

1.  必须声明为 `final`，表示继承关系到此为止，不能再被继承。
2.  必须声明为 `sealed`，表示它可以被继续继承，但同样需要用 `permits` 指定其子类。
3.  必须声明为 `non-sealed`，表示“打开”继承限制，任何类都可以继承它，回归到普通的继承模式。

####### **场景2：定义一个封闭的图形 (`Shape`) 继承体系**

**背景**：我们正在设计一个绘图程序，需要定义一个 `Shape` 接口，并确保系统中只存在 `Circle` 和 `Square` 这两种形状。

```java
package com.example;

// 1. 定义一个 sealed 接口，只允许 Circle 和 Square 实现它
sealed interface Shape permits Circle, Square {
    double area(); // 面积
}

// 2. Circle 是 Shape 的一个子类，我们将其声明为 final，表示它不能再被继承
final class Circle implements Shape {
    private final double radius;
    public Circle(double radius) { this.radius = radius; }
    
    @Override
    public double area() {
        return Math.PI * radius * radius;
    }
}

// 3. Square 是 Shape 的另一个子类，我们将其声明为 non-sealed，
//    表示任何其他类（如 SpecialSquare）都可以继承它。
non-sealed class Square implements Shape {
    private final double side;
    public Square(double side) { this.side = side; }
    
    @Override
    public double area() {
        return side * side;
    }
}

// 由于 Square 是 non-sealed, 我们可以自由地继承它
class SpecialSquare extends Square {
    public SpecialSquare(double side) { super(side); }
    public void doSomethingSpecial() {
        System.out.println("This is a special square!");
    }
}

public class Main {
    public static void main(String[] args) {
        Shape circle = new Circle(10.0);
        Shape square = new Square(10.0);

        System.out.printf("圆形的面积是: %.2f\n", circle.area());
        System.out.printf("方形的面积是: %.2f\n", square.area());

        SpecialSquare specialSquare = new SpecialSquare(5);
        specialSquare.doSomethingSpecial();
    }
}
// 输出:
// 圆形的面积是: 314.16
// 方形的面积是: 100.00
// This is a special square!
```

> **小结**: `Sealed` 类和接口为我们的领域建模提供了更强的控制力。它使得类的继承关系从“开放”变为“有意识的设计”，这在与后面要讲的**模式匹配**结合时，威力会得到最大程度的体现，因为编译器能够知晓所有可能的子类型，从而检查 `switch` 语句是否**穷尽了所有情况**。



-----

#### `[高频面试题]` `Record` VS Lombok

> **Q: `Record` 和 Lombok 是不是一样的？Lombok 的功能不是比 `Record` 更强吗？**
>
> **A:** 这是一个非常经典的问题。虽然 `Record` 和 Lombok 的 `@Data`/`@Value` 注解在**目标**上（即减少数据类的样板代码）有重叠，但它们在**本质、设计哲学和使用方式**上有着根本性的不同。
>
>   * **一句话总结**：Lombok 就是比Record牛逼
>





-----

### 8.7 [语法现代化] [Java 16-21+] 模式匹配

> **本章导读**: 在模式匹配出现之前，处理不同类型的对象通常需要遵循一个繁琐的固定流程：`instanceof` 类型检查 -\> 强制类型转换 -\> 使用转换后的变量。这个过程不仅冗长，而且容易出错（例如，忘记转换或转换错误）。**模式匹配**旨在彻底改变这一现状，它将类型测试、变量声明和条件提取合并为一步，让我们能以一种更具声明性的方式来表达“如果对象是这种模式，就这么做”。

#### 8.7.1 `instanceof` 的模式匹配 `[Java 16+]`

这是模式匹配最基础、最直接的应用，它首先对我们熟悉的 `instanceof` 运算符进行了增强。

> **核心思想**: 在 `instanceof` 类型检查成功后，直接声明一个该类型的变量，省去手动强制转换的步骤。

```java
// 假设 obj 是一个 Object 类型的变量，可能包含 String, Integer 等
Object obj = "hello world";

// --- 传统写法 (Java 16 之前) ---
if (obj instanceof String) {
    String s = (String) obj; // 类型检查后，需要显式向下转型
    System.out.println("传统方式打印大写: " + s.toUpperCase());
}

// --- instanceof 模式匹配写法 (Java 16+) ---
if (obj instanceof String s) { // 类型检查和变量声明/绑定一步完成
    System.out.println("模式匹配打印大写: " + s.toUpperCase()); // 直接使用 s，无需转型
}
```

> **小结**: `instanceof` 模式匹配虽然只是一个小改动，但它为更强大的 `switch` 模式匹配奠定了基础，并培养了我们用模式的思维去替代传统类型判断的习惯。

#### 8.7.2 `switch` 表达式与模式匹配 `[Java 21+]`

这是模式匹配的“完全体”，它极大地增强了 `switch` 语句（和表达式）的能力，使其可以对任意类型的对象进行匹配，并应用更复杂的逻辑。

  * **`switch` 模式匹配核心增强速查表**

| 新特性 | 功能描述 |
| :--- | :--- |
| **类型模式 ** | `case` 标签可以直接匹配对象的类型，如 `case String s`。 |
| **`case null`** | `switch` 现在可以直接处理 `null` 情况，无需在外部进行 `if (obj == null)` 判断。 |
| **守护模式** | 使用 `when` 关键字为 `case` 增加一个额外的布尔条件，如 `case String s when s.length() > 5`。 |
| **穷尽性检查** | **(安全保障)** 当对 `sealed` 类或枚举进行 `switch` 时，编译器会检查是否覆盖了所有可能的子类型，否则会报错。 |

####### **场景2：使用 `switch` 模式匹配和 `sealed` 接口重构图形面积计算**

**背景**：我们重用上一节定义的 `sealed interface Shape`，编写一个方法，使用 `switch` 模式匹配来计算不同形状的面积。

```java
package com.example;

// --- 复用上一节定义的 Shape 继承体系 ---
sealed interface Shape permits Circle, Square {}
final class Circle implements Shape {
    final double radius;
    public Circle(double radius) { this.radius = radius; }
}
non-sealed class Square implements Shape {
    final double side;
    public Square(double side) { this.side = side; }
}
// -----------------------------------------

public class Main {
    
    public static double getArea(Shape shape) {
        // 使用 switch 表达式进行模式匹配
        return switch (shape) {
            // 1. case null: 直接处理 null 输入
            case null -> 0.0;
            
            // 2. 类型模式: 匹配 Circle 类型，并创建变量 c
            case Circle c -> Math.PI * c.radius * c.radius;
            
            // 3. 守护模式: 匹配 Square 类型，但只有当边长大于0时才进入该分支
            case Square s when s.side > 0 -> s.side * s.side;
            
            // 4. default 分支: 覆盖所有其他情况（例如，一个边长小于等于0的 Square）
            // 如果不写 default，对于 sealed 接口，编译器会检查是否穷尽所有可能。
            // 在本例中，因为有 'when' 条件，所以必须有 default。
            default -> 0.0;
        };
    }

    public static void main(String[] args) {
        System.out.printf("圆形面积: %.2f\n", getArea(new Circle(10)));
        System.out.printf("方形面积: %.2f\n", getArea(new Square(5)));
        System.out.printf("null 图形面积: %.2f\n", getArea(null));
        System.out.printf("无效方形面积: %.2f\n", getArea(new Square(-5)));
    }
}
// 输出:
// 圆形面积: 314.16
// 方形面积: 25.00
// null 图形面积: 0.00
// 无效方形面积: 0.00
```

> **小结**: `switch` 模式匹配是 Java 语法现代化的一个巨大飞跃。它将类型判断、条件逻辑和安全性（通过穷尽性检查）完美结合，使得我们处理复杂、多态的业务逻辑时，代码能变得像查表一样清晰、直观和安全。