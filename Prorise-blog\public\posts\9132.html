<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>第二部分：Hexo 基础搭建与配置 | Prorise的小站</title><meta name="keywords" content="博客搭建教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="第二部分：Hexo 基础搭建与配置"><meta name="application-name" content="第二部分：Hexo 基础搭建与配置"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="第二部分：Hexo 基础搭建与配置"><meta property="og:url" content="https://prorise666.site/posts/9132.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第二部分：Hexo 基础搭建与配置在上一部分，我们已经完成了 Hexo 的环境准备和基本项目的初始化。现在，我将带您进一步了解 Hexo 的基础使用，包括核心命令以及对站点全局配置文件的初步认识。 1. Hexo 常用基础命令掌握 Hexo 的基础命令是高效管理博客的关键。以下是一些最常用的命令："><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp"><meta name="description" content="第二部分：Hexo 基础搭建与配置在上一部分，我们已经完成了 Hexo 的环境准备和基本项目的初始化。现在，我将带您进一步了解 Hexo 的基础使用，包括核心命令以及对站点全局配置文件的初步认识。 1. Hexo 常用基础命令掌握 Hexo 的基础命令是高效管理博客的关键。以下是一些最常用的命令："><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/9132.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"第二部分：Hexo 基础搭建与配置",postAI:"true",pageFillDescription:"第二部分：Hexo 基础搭建与配置, 1. Hexo 常用基础命令, 2. Hexo 根目录配置文件 (_config.yml), 3.页面配置（重点）, 1.Page Front-matter 用于页面配置, 2.Post Front-matter 用于文章页配置第二部分基础搭建与配置在上一部分我们已经完成了的环境准备和基本项目的初始化现在我将带您进一步了解的基础使用包括核心命令以及对站点全局配置文件的初步认识常用基础命令掌握的基础命令是高效管理博客的关键以下是一些最常用的命令如果您想在本地查看草稿的效果普通的命令是看不到的您必须使用一个特殊的命令或者简写命令简写功能描述常用场景备注初始化一个新博客项目到指定文件夹如果文件夹不存在会创建它首次创建博客项目通常在空目录下执行创建一篇新文件可以是文章页面或草稿默认是是文件名不含扩展名撰写新文章创建关于页面等会根据目录下的模板生成文件生成静态网站文件到目录内容更新后准备部署前本地预览前通常包含此步骤会处理目录下的文件主题和配置启动本地预览服务器在本地查看博客效果调试主题或内容默认地址支持热重载将生成的静态网站文件部署到远程服务器如将本地博客发布到线上需要先安装对应的部署插件并配置中的部分清理目录下的生成文件和缓存文件遇到生成错误或需要强制完全重新生成时有助于解决一些缓存问题或文件冲突显示和的版本信息检查环境是否符合要求排查版本兼容性问题常用命令组合撰写并预览我的第一篇文章编写内容浏览器预览更新并部署编写修改内容或者更简单的场景化解释当我写完一篇新文章或者对已有文章进行了修改我通常会先运行清理掉旧的生成文件和缓存然后运行重新生成最新的静态网站最后为了验证修改是否正确我会在本地运行进行预览当我确认本地预览无误准备发布到线上时我只需要运行命令如果配置了自动生成可以使用这个命令会将目录下的所有文件推送到我配置好的托管平台比如根目录配置文件文件位于项目的根目录下它是站点的全局配置文件大部分重要的全局设置如网站标题副标题作者语言文章链接格式主题等都在这里进行配置这个文件采用格式格式使用缩进表示层级关系键值对之间用冒号分隔注意冒号后面需要加两个小空格请务必注意缩进对的缩进非常敏感通常使用两个空格进行缩进默认的文件是英文的以下是文件中的中文翻译网站信息这部分定义了您博客的基础信息会显示在网站的各个位置网站主标题会显示在浏览器标签页和主题的显眼位置网站副标题通常显示在主标题下方网站描述主要用于告诉搜索引擎您的网站是关于什么内容的网站关键词用于多个关键词用英文逗号隔开您的名字或昵称网站语言对于中文博客强烈建议修改为网站时区建议设置为您所在的时区以确保文章发布时间的准确性例如中国或日本默认使用您电脑的时区但明确指定更好网址这部分配置与您网站的链接结构密切相关非常重要重要请务必修改为您的网站最终的访问网址例如如果您使用它可能是这个配置会影响网站所有资源的绝对路径如果错误可能导致图片加载失败文章的永久链接格式示例默认值例如例如非常简洁例如推荐使用插件生成短链接对友好且不会因修改标题而改变永久链接中各部分的默认值美化选项是否移除永久链接末尾的通常保持默认是否移除永久链接末尾的通常保持默认目录这部分定义了您项目中的各个核心文件夹的名称通常无需修改源文件夹您创作内容的地方文章图片等公共文件夹存放最终生成的静态网站文件最终部署到服务器上的内容标签页面的目录名例如归档页面的目录名例如分类页面的目录名例如代码下载目录如果您使用代码下载功能国际化语言文件的目录跳过渲染指定的文件或文件夹您可以在这里列出不希望被处理的文件路径写作这部分配置与您撰写文章时的行为相关新文章的文件名格式是文章标题新建文件的默认布局通常是文章或草稿是否将文章标题转换为首字母大写建议保持原文案外部链接设置是否在新标签页中打开外部链接建议以保留用户在您的网站上应用范围表示全站表示仅文章内容在这里列出的域名将不会被当作外部链接处理例如文件名大小写转换无变化小写大写是否渲染文件夹中的草稿表示默认不渲染是否启用文章资源文件夹如果设为当您用创建文章时会在目录下同时创建一个名为的文件夹方便您存放该文章专属的图片等资源是否将链接转换为与根目录的相对路径通常保持是否渲染发布日期在未来的文章表示会渲染代码高亮引擎可选值或等现代主题通常有自己的高亮方案可能会覆盖此设置的具体配置是否显示行号是否自动检测语言建议以获得更好的性能和准确性用什么字符替换是否用包裹代码块以实现复杂的行号显示是否启用内置的样式通常主题会有自己的样式所以设为的具体配置是否在预处理阶段进行语法高亮是否显示行号用什么字符替换主页设置这部分控制您博客首页的文章列表行为首页的路径空字符串表示网站根目录每页显示的文章数量表示禁用分页文章排序方式表示按日期降序最新的在最前表示升序分类与标签默认分类当文章没有指定分类时会使用此分类分类别名例如标签别名例如元数据是否在头部注入的标签有助于进行网站技术栈统计建议保留日期与时间格式使用库来处理时间格式格式定义日期显示格式时间显示格式文章更新时间的选项使用文件的最后修改时间作为更新时间推荐使用中的字段作为更新时间不使用更新时间分页归档页如分类页标签页的分页设置每页显示的文章数量表示禁用分页分页的目录例如包含与排除文件这些选项仅对文件夹生效默认会忽略隐藏文件和以或开头的文件文件夹如果您需要处理某些被忽略的文件可以在这里列出如果您希望忽略下的某些文件或文件夹可以在这里列出全局忽略规则扩展插件主题重要当前使用的主题名称请确保文件夹下有对应名称的主题文件夹例如要使用主题请修改为部署命令的配置部署类型例如部署到需要安装插件并将类型设为示例场景化解释修改会直接影响博客的全局信息比如在浏览器标签页搜索引擎结果以及主题的某些位置显示当我第一次搭建博客时会在这里填入我的博客名称简介作者信息等配置项至关重要它告诉您的博客最终部署到哪个网址如果此处配置错误可能导致生成的静态文件中资源路径错误比如加载失败使得网站只有文字而没有样式决定了您文章的形式我个人比较喜欢简洁的或格式方便记忆和分享在开始自定义主题之前我建议您根据自己的信息修改文件中的和等基本信息建议修改为以便更好地显示中文内容和使用中文主题至此我们已经完成了的基础搭建了解了核心命令并对站点配置文件有了初步认识在下一部分我们将正式安装并配置魔改的主题让您的博客焕然一新页面配置重点的基本认识是文件最上方以分隔的区域用于指定个别档案的变数其中又分为两种里用于页面配置必需字段默认通过可以生成页面的标题它会显示在浏览器标签页文章列表以及页面顶部友情链接默认通过可以生成页面的创建日期推荐格式为必需页面的类型或布局这个字段至关重要它会告诉主题该如何渲染这个页面您需要根据所用主题的文档来填写常见值有标签页分类页关于页友情链接页相册页朋友圈或即刻页可选字段内容与可选页面的最后更新日期如果忽略其值通常会默认等于创建日期可选页面的描述这段文字主要用于会显示在搜索引擎的结果摘要中强烈建议认真填写这里是我的个人博客的友情链接页面汇集了许多优秀博主的网站欢迎大家参观和交换友链可选页面的关键词多个关键词用英文逗号隔开同样主要用于帮助搜索引擎理解页面内容友情链接博客圈技术博客生活分享可选字段功能开关可选是否在此页面显示评论模块为显示默认为关闭可选是否为本页面单独开启数学公式渲染仅当主题全局配置中设置为时这两个选项才需要在此处单独开启可选是否显示侧边栏为显示默认为隐藏若想打造沉浸感强的页面可设为可选是否在本页面加载音乐播放器设置为后还需在页面正文中通过特定标签来配置播放列表可选是否折叠本页面的所有代码块为折叠为展开可用于覆盖主题的全局设置可选字段样式与外观可选页面的顶部大图路径是相对于博客目录的绝对路径可选为某些特殊页面的顶部模块设置背景图片这通常是平铺或覆盖的背景而非大图用于增强设计感这里是页面的正文内容用于文章页配置基本信息必需文章的标题深入解析核心技术与未来展望必需文章的创建日期格式为可选文章的最后更新日期如果您修改了旧文章强烈建议更新此时间有助于内容分类可选文章分类分类具有层级关系父子关系适合用于组织结构化的内容父分类子分类前端技术可选文章标签标签是扁平化的没有层级关系适合用于描述文章的关键词与摘要可选文章的关键词用英文逗号隔开主要用于博客可选文章的摘要或描述会显示在首页文章卡片和搜索引擎结果中是吸引点击的关键本文将从等核心组件出发深入探讨的工作原理并展望其在未来的应用场景可选文章摘要功能的开关具体作用需参考您所用主题的文档页面展示与样式可选文章顶部的横幅大图可选文章的缩略图封面填写图片地址则首页和文章页都用此图设为则不显示缩略图若不设置此项但设置了则通常会用作为封面可选文章的主色调必须是位的进制颜色值如不能缩写为首页推荐可选配置文章在首页轮播图中显示数字越小位置越靠前设置此项即可让文章进入轮播可选配置文章在首页右侧推荐卡片组中显示数字越小位置越靠前功能开关可选是否显示文章的评论模块默认为可选目录的相关设置用于覆盖主题的全局默认配置是否显示目录目录是否显示编号是否使用简洁样式的目录可选文章版权模块的详细设置您可以在此覆盖全局的版权信息为特定文章如转载设置不同的版权是否显示版权模块特邀作者张三自定义文章作者自定义作者链接自定义文章源链接本文为特邀作者原创转载请联系作者获得授权自定义版权声明文字可选高级功能开关通常用于在全局关闭某功能时为特定文章单独开启代表本页代码块默认折叠代表本页显示侧边栏",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-19 19:21:28",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E9%83%A8%E5%88%86%EF%BC%9AHexo-%E5%9F%BA%E7%A1%80%E6%90%AD%E5%BB%BA%E4%B8%8E%E9%85%8D%E7%BD%AE"><span class="toc-text">第二部分：Hexo 基础搭建与配置</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-Hexo-%E5%B8%B8%E7%94%A8%E5%9F%BA%E7%A1%80%E5%91%BD%E4%BB%A4"><span class="toc-text">1. Hexo 常用基础命令</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-Hexo-%E6%A0%B9%E7%9B%AE%E5%BD%95%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6-config-yml"><span class="toc-text">2. Hexo 根目录配置文件 (_config.yml)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE%EF%BC%88%E9%87%8D%E7%82%B9%EF%BC%89"><span class="toc-text">3.页面配置（重点）</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-Page-Front-matter-%E7%94%A8%E4%BA%8E%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE"><span class="toc-text">1.Page Front-matter 用于页面配置</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-Post-Front-matter-%E7%94%A8%E4%BA%8E%E6%96%87%E7%AB%A0%E9%A1%B5%E9%85%8D%E7%BD%AE"><span class="toc-text">2.Post Front-matter 用于文章页配置</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5f2a23">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#277340">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#c72008">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#11a7a2">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#276d10">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#6d6a95">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>博客搭建教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">第二部分：Hexo 基础搭建与配置</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-06-19T10:13:45.000Z" title="发表于 2025-06-19 18:13:45">2025-06-19</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-19T11:21:28.516Z" title="更新于 2025-07-19 19:21:28">2025-07-19</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">4.6k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>16分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="第二部分：Hexo 基础搭建与配置"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/9132.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/9132.html"><header><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">博客搭建教程</a><h1 id="CrawlerTitle" itemprop="name headline">第二部分：Hexo 基础搭建与配置</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-06-19T10:13:45.000Z" title="发表于 2025-06-19 18:13:45">2025-06-19</time><time itemprop="dateCreated datePublished" datetime="2025-07-19T11:21:28.516Z" title="更新于 2025-07-19 19:21:28">2025-07-19</time></header><div id="postchat_postcontent"><h2 id="第二部分：Hexo-基础搭建与配置"><a href="#第二部分：Hexo-基础搭建与配置" class="headerlink" title="第二部分：Hexo 基础搭建与配置"></a>第二部分：Hexo 基础搭建与配置</h2><p>在上一部分，我们已经完成了 Hexo 的环境准备和基本项目的初始化。现在，我将带您进一步了解 Hexo 的基础使用，包括核心命令以及对站点全局配置文件的初步认识。</p><h3 id="1-Hexo-常用基础命令"><a href="#1-Hexo-常用基础命令" class="headerlink" title="1. Hexo 常用基础命令"></a>1. Hexo 常用基础命令</h3><p>掌握 Hexo 的基础命令是高效管理博客的关键。以下是一些最常用的命令：</p><blockquote><p>如果您想在本地查看草稿的效果，普通的 <code>hexo server</code> 命令是看不到的，您必须使用一个特殊的命令：<code>hexo server --drafts</code> (或者简写 <code>hexo s --drafts</code>)，</p></blockquote><table><thead><tr><th align="left">命令</th><th align="left">简写</th><th align="left">功能描述</th><th align="left">常用场景</th><th align="left">备注</th></tr></thead><tbody><tr><td align="left"><code>hexo init [folder]</code></td><td align="left">-</td><td align="left">初始化一个新博客项目到指定文件夹。如果文件夹不存在，Hexo 会创建它。</td><td align="left">首次创建博客项目。</td><td align="left">通常在空目录下执行。</td></tr><tr><td align="left"><code>hexo new &lt;layout&gt; &lt;title&gt;</code></td><td align="left"><code>hexo n</code></td><td align="left">创建一篇新文件。<code>&lt;layout&gt;</code> 可以是 <code>post</code> (文章)、<code>page</code> (页面) 或 <code>draft</code> (草稿)，默认是 <code>post</code>。<code>&lt;title&gt;</code> 是文件名（不含扩展名）。</td><td align="left">撰写新文章、创建关于页面等。</td><td align="left">会根据 <code>scaffolds</code> 目录下的模板生成文件。</td></tr><tr><td align="left"><code>hexo generate</code></td><td align="left"><code>hexo g</code></td><td align="left">生成静态网站文件到 <code>public</code> 目录。</td><td align="left">内容更新后准备部署前；本地预览前（<code>hexo s</code> 通常包含此步骤）。</td><td align="left">会处理 <code>source</code> 目录下的 Markdown 文件、主题和配置。</td></tr><tr><td align="left"><code>hexo server</code></td><td align="left"><code>hexo s</code></td><td align="left">启动本地预览服务器。</td><td align="left">在本地查看博客效果、调试主题或内容。</td><td align="left">默认地址 <code>http://localhost:4000</code>，支持热重载。</td></tr><tr><td align="left"><code>hexo deploy</code></td><td align="left"><code>hexo d</code></td><td align="left">将生成的静态网站文件部署到远程服务器（如 GitHub Pages）。</td><td align="left">将本地博客发布到线上。</td><td align="left">需要先安装对应的部署插件并配置 <code>_config.yml</code> 中的 <code>deploy</code> 部分。</td></tr><tr><td align="left"><code>hexo clean</code></td><td align="left">-</td><td align="left">清理 <code>public</code> 目录下的生成文件和 <code>db.json</code> 缓存文件。</td><td align="left">遇到生成错误或需要强制完全重新生成时。</td><td align="left">有助于解决一些缓存问题或文件冲突。</td></tr><tr><td align="left"><code>hexo version</code></td><td align="left"><code>hexo v</code></td><td align="left">显示 Hexo、Node.js 和 npm 的版本信息。</td><td align="left">检查环境是否符合要求；排查版本兼容性问题。</td><td align="left"></td></tr></tbody></table><p><strong>常用命令组合：</strong></p><ul><li><strong>撰写并预览：</strong> <code>hexo new post "我的第一篇文章"</code> -&gt; 编写内容 -&gt; <code>hexo clean &amp;&amp; hexo server</code> -&gt; 浏览器预览。</li><li><strong>更新并部署：</strong> 编写/修改内容 -&gt; <code>hexo clean &amp;&amp; hexo generate &amp;&amp; hexo deploy</code> (或者更简单的 <code>hexo deploy -g</code>)。</li></ul><blockquote><p><strong>场景化解释：</strong></p><ul><li>当我写完一篇新文章或者对已有文章进行了修改，我通常会先运行 <code>hexo clean</code> 清理掉旧的生成文件和缓存，然后运行 <code>hexo generate</code> 重新生成最新的静态网站。最后，为了验证修改是否正确，我会在本地运行 <code>hexo server</code> 进行预览。</li><li>当我确认本地预览无误，准备发布到线上时，我只需要运行 <code>hexo deploy</code> 命令（如果配置了自动生成，可以使用 <code>hexo deploy -g</code>）。这个命令会将 <code>public</code> 目录下的所有文件推送到我配置好的托管平台（比如 GitHub Pages）。</li></ul></blockquote><h3 id="2-Hexo-根目录配置文件-config-yml"><a href="#2-Hexo-根目录配置文件-config-yml" class="headerlink" title="2. Hexo 根目录配置文件 (_config.yml)"></a>2. Hexo 根目录配置文件 (<code>_config.yml</code>)</h3><p><code>_config.yml</code> 文件位于 Hexo 项目的根目录下，它是站点的全局配置文件。大部分重要的全局设置，如网站标题、副标题、作者、语言、URL、文章链接格式、主题等，都在这里进行配置。</p><p>这个文件采用 YAML 格式。YAML 格式使用缩进表示层级关系，键值对之间用冒号 <code>:</code> 分隔<code>（注意冒号后面需要加两个小空格</code>）。请务必注意缩进，Hexo 对 YAML 的缩进非常敏感，通常使用两个空格进行缩进。</p><p>默认的文件是英文的以下是 <code>_config.yml</code> 文件中的中文翻译</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br><span class="line">197</span><br><span class="line">198</span><br><span class="line">199</span><br><span class="line">200</span><br><span class="line">201</span><br><span class="line">202</span><br><span class="line">203</span><br><span class="line">204</span><br><span class="line">205</span><br><span class="line">206</span><br><span class="line">207</span><br><span class="line">208</span><br><span class="line">209</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Hexo Configuration</span></span><br><span class="line"><span class="comment">## Docs: https://hexo.io/docs/configuration.html</span></span><br><span class="line"><span class="comment">## Source: https://github.com/hexojs/hexo/</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 网站信息 (Site) ---</span></span><br><span class="line"><span class="comment"># 这部分定义了您博客的基础信息，会显示在网站的各个位置。</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment"># 网站主标题，会显示在浏览器标签页和主题的显眼位置。</span></span><br><span class="line"><span class="attr">title:</span> <span class="string">Hexo</span> </span><br><span class="line"><span class="comment"># 网站副标题，通常显示在主标题下方。</span></span><br><span class="line"><span class="attr">subtitle:</span> <span class="string">''</span></span><br><span class="line"><span class="comment"># 网站描述，主要用于SEO，告诉搜索引擎您的网站是关于什么内容的。</span></span><br><span class="line"><span class="attr">description:</span> <span class="string">''</span></span><br><span class="line"><span class="comment"># 网站关键词，用于SEO，多个关键词用英文逗号隔开。</span></span><br><span class="line"><span class="attr">keywords:</span></span><br><span class="line"><span class="comment"># 您的名字或昵称。</span></span><br><span class="line"><span class="attr">author:</span> <span class="string">John</span> <span class="string">Doe</span></span><br><span class="line"><span class="comment"># 网站语言。对于中文博客，强烈建议修改为 'zh-CN'。</span></span><br><span class="line"><span class="attr">language:</span> <span class="string">en</span></span><br><span class="line"><span class="comment"># 网站时区。建议设置为您所在的时区，以确保文章发布时间的准确性。</span></span><br><span class="line"><span class="comment"># 例如：'Asia/Shanghai' (中国) 或 'Asia/Tokyo' (日本)。</span></span><br><span class="line"><span class="comment"># Hexo 默认使用您电脑的时区，但明确指定更好。</span></span><br><span class="line"><span class="attr">timezone:</span> <span class="string">''</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 网址 (URL) ---</span></span><br><span class="line"><span class="comment"># 这部分配置与您网站的链接结构（URL）密切相关，非常重要。</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment"># 【重要】请务必修改为您的网站最终的访问网址！</span></span><br><span class="line"><span class="comment"># 例如，如果您使用 GitHub Pages，它可能是 'https://yourname.github.io'。</span></span><br><span class="line"><span class="comment"># 这个配置会影响网站所有资源的绝对路径，如果错误，可能导致CSS、JS、图片加载失败。</span></span><br><span class="line"><span class="attr">url:</span> <span class="string">http://example.com</span></span><br><span class="line"><span class="comment"># 文章的永久链接格式。</span></span><br><span class="line"><span class="comment"># :year, :month, :day, :i_month, :i_day, :hour, :minute, :second, :title, :name, :post_title, :id, :category</span></span><br><span class="line"><span class="comment"># 示例:</span></span><br><span class="line"><span class="comment">#   :year/:month/:day/:title/  (默认值，例如 2025/06/08/hello-world/)</span></span><br><span class="line"><span class="comment">#   :title.html               (例如 hello-world.html，非常简洁)</span></span><br><span class="line"><span class="comment">#   :category/:title/          (例如 tech/hello-world/)</span></span><br><span class="line"><span class="comment"># 推荐使用 hexo-abbrlink 插件生成短链接，对SEO友好且不会因修改标题而改变： permalink: posts/:abbrlink.html</span></span><br><span class="line"><span class="attr">permalink:</span> <span class="string">:year/:month/:day/:title/</span></span><br><span class="line"><span class="comment"># 永久链接中各部分的默认值。</span></span><br><span class="line"><span class="attr">permalink_defaults:</span></span><br><span class="line"><span class="comment"># URL 美化选项。</span></span><br><span class="line"><span class="attr">pretty_urls:</span></span><br><span class="line">  <span class="comment"># 是否移除永久链接末尾的 'index.html'。通常保持默认。</span></span><br><span class="line">  <span class="attr">trailing_index:</span> <span class="literal">true</span> </span><br><span class="line">  <span class="comment"># 是否移除永久链接末尾的 '.html'。通常保持默认。</span></span><br><span class="line">  <span class="attr">trailing_html:</span> <span class="literal">true</span> </span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 目录 (Directory) ---</span></span><br><span class="line"><span class="comment"># 这部分定义了您项目中的各个核心文件夹的名称，通常无需修改。</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment"># 源文件夹，您创作内容的地方（文章、图片等）。</span></span><br><span class="line"><span class="attr">source_dir:</span> <span class="string">source</span></span><br><span class="line"><span class="comment"># 公共文件夹，存放最终生成的静态网站文件（最终部署到服务器上的内容）。</span></span><br><span class="line"><span class="attr">public_dir:</span> <span class="string">public</span></span><br><span class="line"><span class="comment"># 标签页面的目录名。例如: yoursite.com/tags/</span></span><br><span class="line"><span class="attr">tag_dir:</span> <span class="string">tags</span></span><br><span class="line"><span class="comment"># 归档页面的目录名。例如: yoursite.com/archives/</span></span><br><span class="line"><span class="attr">archive_dir:</span> <span class="string">archives</span></span><br><span class="line"><span class="comment"># 分类页面的目录名。例如: yoursite.com/categories/</span></span><br><span class="line"><span class="attr">category_dir:</span> <span class="string">categories</span></span><br><span class="line"><span class="comment"># 代码下载目录（如果您使用代码下载功能）。</span></span><br><span class="line"><span class="attr">code_dir:</span> <span class="string">downloads/code</span></span><br><span class="line"><span class="comment"># 国际化（i18n）语言文件的目录。</span></span><br><span class="line"><span class="attr">i18n_dir:</span> <span class="string">:lang</span></span><br><span class="line"><span class="comment"># 跳过渲染指定的文件或文件夹。您可以在这里列出不希望被Hexo处理的文件路径。</span></span><br><span class="line"><span class="attr">skip_render:</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 写作 (Writing) ---</span></span><br><span class="line"><span class="comment"># 这部分配置与您撰写文章时的行为相关。</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment"># 新文章的文件名格式。:title 是文章标题。</span></span><br><span class="line"><span class="attr">new_post_name:</span> <span class="string">:title.md</span> </span><br><span class="line"><span class="comment"># 新建文件的默认布局，通常是 'post' (文章) 或 'draft' (草稿)。</span></span><br><span class="line"><span class="attr">default_layout:</span> <span class="string">post</span></span><br><span class="line"><span class="comment"># 是否将文章标题转换为 "Title Case" (首字母大写)。建议 'false'，保持原文案。</span></span><br><span class="line"><span class="attr">titlecase:</span> <span class="literal">false</span> </span><br><span class="line"><span class="comment"># 外部链接设置。</span></span><br><span class="line"><span class="attr">external_link:</span></span><br><span class="line">  <span class="comment"># 是否在新标签页中打开外部链接。建议 'true'，以保留用户在您的网站上。</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span> </span><br><span class="line">  <span class="comment"># 应用范围。'site' 表示全站，'post' 表示仅文章内容。</span></span><br><span class="line">  <span class="attr">field:</span> <span class="string">site</span> </span><br><span class="line">  <span class="comment"># 在这里列出的域名将不会被当作外部链接处理。例如: 'exclude: yoursite.com'</span></span><br><span class="line">  <span class="attr">exclude:</span> <span class="string">''</span></span><br><span class="line"><span class="comment"># 文件名大小写转换。0: 无变化; 1: 小写; 2: 大写。</span></span><br><span class="line"><span class="attr">filename_case:</span> <span class="number">0</span></span><br><span class="line"><span class="comment"># 是否渲染 'source/_drafts' 文件夹中的草稿。'false' 表示默认不渲染。</span></span><br><span class="line"><span class="attr">render_drafts:</span> <span class="literal">false</span></span><br><span class="line"><span class="comment"># 是否启用文章资源文件夹。</span></span><br><span class="line"><span class="comment"># 如果设为 'true'，当您用 `hexo new post "xxx"` 创建文章时，</span></span><br><span class="line"><span class="comment"># 会在 `source/_posts` 目录下同时创建一个名为 "xxx" 的文件夹，方便您存放该文章专属的图片等资源。</span></span><br><span class="line"><span class="attr">post_asset_folder:</span> <span class="literal">false</span></span><br><span class="line"><span class="comment"># 是否将链接转换为与根目录的相对路径。通常保持 'false'。</span></span><br><span class="line"><span class="attr">relative_link:</span> <span class="literal">false</span></span><br><span class="line"><span class="comment"># 是否渲染发布日期在未来的文章。'true' 表示会渲染。</span></span><br><span class="line"><span class="attr">future:</span> <span class="literal">true</span></span><br><span class="line"><span class="comment"># 代码高亮引擎。可选值: 'highlight.js' 或 'prismjs'。</span></span><br><span class="line"><span class="comment"># Butterfly 等现代主题通常有自己的高亮方案，可能会覆盖此设置。</span></span><br><span class="line"><span class="attr">syntax_highlighter:</span> <span class="string">highlight.js</span></span><br><span class="line"><span class="comment"># highlight.js 的具体配置。</span></span><br><span class="line"><span class="attr">highlight:</span></span><br><span class="line">  <span class="comment"># 是否显示行号。</span></span><br><span class="line">  <span class="attr">line_number:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># 是否自动检测语言。建议 'false' 以获得更好的性能和准确性。</span></span><br><span class="line">  <span class="attr">auto_detect:</span> <span class="literal">false</span></span><br><span class="line">  <span class="comment"># 用什么字符替换 Tab。</span></span><br><span class="line">  <span class="attr">tab_replace:</span> <span class="string">''</span></span><br><span class="line">  <span class="comment"># 是否用 `&lt;table&gt;` 包裹代码块以实现复杂的行号显示。</span></span><br><span class="line">  <span class="attr">wrap:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># 是否启用 highlight.js 内置的样式。通常主题会有自己的样式，所以设为 'false'。</span></span><br><span class="line">  <span class="attr">hljs:</span> <span class="literal">false</span></span><br><span class="line"><span class="comment"># prismjs 的具体配置。</span></span><br><span class="line"><span class="attr">prismjs:</span></span><br><span class="line">  <span class="comment"># 是否在预处理阶段进行语法高亮。</span></span><br><span class="line">  <span class="attr">preprocess:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># 是否显示行号。</span></span><br><span class="line">  <span class="attr">line_number:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># 用什么字符替换 Tab。</span></span><br><span class="line">  <span class="attr">tab_replace:</span> <span class="string">''</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 主页设置 (Home page setting) ---</span></span><br><span class="line"><span class="comment"># 这部分控制您博客首页的文章列表行为。</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="attr">index_generator:</span></span><br><span class="line">  <span class="comment"># 首页的路径。空字符串 '' 表示网站根目录。</span></span><br><span class="line">  <span class="attr">path:</span> <span class="string">''</span></span><br><span class="line">  <span class="comment"># 每页显示的文章数量。0 表示禁用分页。</span></span><br><span class="line">  <span class="attr">per_page:</span> <span class="number">10</span></span><br><span class="line">  <span class="comment"># 文章排序方式。'-date' 表示按日期降序（最新的在最前），'date' 表示升序。</span></span><br><span class="line">  <span class="attr">order_by:</span> <span class="string">-date</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 分类与标签 (Category &amp; Tag) ---</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment"># 默认分类。当文章没有指定分类时，会使用此分类。</span></span><br><span class="line"><span class="attr">default_category:</span> <span class="string">uncategorized</span></span><br><span class="line"><span class="comment"># 分类别名。例如: 'cate_alias: my-cate'</span></span><br><span class="line"><span class="attr">category_map:</span></span><br><span class="line"><span class="comment"># 标签别名。例如: 'tag_alias: my-tag'</span></span><br><span class="line"><span class="attr">tag_map:</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 元数据 (Metadata elements) ---</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment"># 是否在HTML头部注入 Hexo 的 meta generator 标签。有助于进行网站技术栈统计，建议保留。</span></span><br><span class="line"><span class="attr">meta_generator:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 日期与时间格式 (Date / Time format) ---</span></span><br><span class="line"><span class="comment"># Hexo 使用 Moment.js 库来处理时间格式。</span></span><br><span class="line"><span class="comment"># 格式定义: http://momentjs.com/docs/#/displaying/format/</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment"># 日期显示格式。</span></span><br><span class="line"><span class="attr">date_format:</span> <span class="string">YYYY-MM-DD</span></span><br><span class="line"><span class="comment"># 时间显示格式。</span></span><br><span class="line"><span class="attr">time_format:</span> <span class="string">HH:mm:ss</span></span><br><span class="line"><span class="comment"># 文章更新时间的选项。</span></span><br><span class="line"><span class="comment"># 'mtime': 使用文件的最后修改时间作为更新时间 (推荐)。</span></span><br><span class="line"><span class="comment"># 'date': 使用 Front-matter 中的 'date' 字段作为更新时间。</span></span><br><span class="line"><span class="comment"># 'empty': 不使用更新时间。</span></span><br><span class="line"><span class="attr">updated_option:</span> <span class="string">'mtime'</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 分页 (Pagination) ---</span></span><br><span class="line"><span class="comment"># 归档页（如分类页、标签页）的分页设置。</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment"># 每页显示的文章数量。0 表示禁用分页。</span></span><br><span class="line"><span class="attr">per_page:</span> <span class="number">10</span></span><br><span class="line"><span class="comment"># 分页的目录。例如: yoursite.com/page/2/</span></span><br><span class="line"><span class="attr">pagination_dir:</span> <span class="string">page</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 包含与排除文件 (Include / Exclude file(s)) ---</span></span><br><span class="line"><span class="comment"># 这些选项仅对 'source/' 文件夹生效。</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment"># Hexo 默认会忽略隐藏文件和以 '_' 或 '#' 开头的文件/文件夹。</span></span><br><span class="line"><span class="comment"># include: [.well-known] # 如果您需要 Hexo 处理某些被忽略的文件，可以在这里列出。</span></span><br><span class="line"><span class="attr">include:</span></span><br><span class="line"><span class="comment"># exclude: [temp/] # 如果您希望 Hexo 忽略 'source/' 下的某些文件或文件夹，可以在这里列出。</span></span><br><span class="line"><span class="attr">exclude:</span></span><br><span class="line"><span class="comment"># ignore: [*.log] # 全局忽略规则。</span></span><br><span class="line"><span class="attr">ignore:</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 扩展 (Extensions) ---</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="comment">## 插件: https://hexo.io/plugins/</span></span><br><span class="line"><span class="comment">## 主题: https://hexo.io/themes/</span></span><br><span class="line"><span class="comment"># [重要] 当前使用的主题名称。请确保 'themes' 文件夹下有对应名称的主题文件夹。</span></span><br><span class="line"><span class="comment"># 例如，要使用 Butterfly 主题，请修改为: 'theme: butterfly'</span></span><br><span class="line"><span class="attr">theme:</span> <span class="string">landscape</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># --- 部署 (Deployment) ---</span></span><br><span class="line"><span class="comment"># `hexo deploy` 命令的配置。</span></span><br><span class="line"><span class="comment"># Docs: https://hexo.io/docs/one-command-deployment</span></span><br><span class="line"><span class="comment"># -----------------------------------------------------------</span></span><br><span class="line"><span class="attr">deploy:</span></span><br><span class="line">  <span class="comment"># 部署类型。例如，部署到 GitHub Pages，需要安装 `hexo-deployer-git` 插件，并将类型设为 'git'。</span></span><br><span class="line">  <span class="comment"># 示例:</span></span><br><span class="line">  <span class="comment">#   type: git</span></span><br><span class="line">  <span class="comment">#   repo: **************:yourname/yourname.github.io.git</span></span><br><span class="line">  <span class="comment">#   branch: main</span></span><br><span class="line">  <span class="attr">type:</span> <span class="string">''</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>场景化解释：</strong></p><ul><li>修改 <code>title</code>, <code>subtitle</code>, <code>description</code>, <code>author</code>, <code>language</code> 会直接影响博客的全局信息，比如在浏览器标签页、搜索引擎结果以及主题的某些位置显示。当我第一次搭建博客时，会在这里填入我的博客名称、简介、作者信息等。</li><li><code>url</code> 配置项至关重要，它告诉 Hexo 您的博客最终部署到哪个网址。如果此处配置错误，可能导致生成的静态文件中资源路径错误（比如 CSS/JS 加载失败），使得网站只有文字而没有样式。</li><li><code>permalink</code> 决定了您文章的 URL 形式。我个人比较喜欢简洁的 <code>:category/:title.html</code> 或 <code>:year/:month/:day/:title/</code> 格式，方便记忆和分享。</li></ul></blockquote><p>在开始自定义主题之前，我建议您根据自己的信息修改 <code>_config.yml</code> 文件中的 <code>title</code>、<code>author</code> 和 <code>url</code> 等基本信息。<code>language</code> 建议修改为 <code>zh-CN</code> 以便更好地显示中文内容和使用中文主题。</p><p>至此，我们已经完成了 Hexo 的基础搭建，了解了核心命令，并对站点配置文件有了初步认识。在下一部分，我们将正式安装并配置 魔改的Butterfly 主题，让您的博客焕然一新。</p><h3 id="3-页面配置（重点）"><a href="#3-页面配置（重点）" class="headerlink" title="3.页面配置（重点）"></a><code>3.页面配置（重点）</code></h3><p><strong>Front-matter 的基本认识</strong></p><p><code>Front-matter</code> 是 <code>markdown</code> 文件最上方以 <code>---</code> 分隔的区域，用于指定个别档案的变数。其中又分为两种 markdown 里</p><h5 id="1-Page-Front-matter-用于页面配置"><a href="#1-Page-Front-matter-用于页面配置" class="headerlink" title="1.Page Front-matter 用于页面配置"></a>1.Page Front-matter 用于页面配置</h5><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">---</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                          必需字段 (Required)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【默认通过hexo可以生成】页面的标题。</span></span><br><span class="line"><span class="comment"># 它会显示在浏览器标签页、文章列表以及页面顶部。</span></span><br><span class="line"><span class="attr">title:</span> <span class="string">友情链接</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【默认通过hexo可以生成】页面的创建日期。</span></span><br><span class="line"><span class="comment"># 推荐格式为：YYYY-MM-DD HH:mm:ss。</span></span><br><span class="line"><span class="attr">date:</span> <span class="number">2025-06-09 20:00:00</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【必需】页面的类型或布局。</span></span><br><span class="line"><span class="comment"># 这个字段至关重要，它会告诉主题该如何渲染这个页面。</span></span><br><span class="line"><span class="comment"># 您需要根据所用主题的文档来填写，常见值有：</span></span><br><span class="line"><span class="comment"># - tags: 标签页</span></span><br><span class="line"><span class="comment"># - categories: 分类页</span></span><br><span class="line"><span class="comment"># - about: 关于页</span></span><br><span class="line"><span class="comment"># - link: 友情链接页</span></span><br><span class="line"><span class="comment"># - photos: 相册页</span></span><br><span class="line"><span class="comment"># - moment / friends: 朋友圈或即刻页</span></span><br><span class="line"><span class="attr">type:</span> <span class="string">link</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                      可选字段 - 内容与SEO (Optional)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】页面的最后更新日期。</span></span><br><span class="line"><span class="comment"># 如果忽略，其值通常会默认等于创建日期 `date`。</span></span><br><span class="line"><span class="attr">updated:</span> <span class="number">2025-06-09 21:30:00</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】页面的描述。</span></span><br><span class="line"><span class="comment"># 这段文字主要用于 SEO，会显示在搜索引擎的结果摘要中，强烈建议认真填写。</span></span><br><span class="line"><span class="attr">description:</span> <span class="string">"这里是我的个人博客的友情链接页面，汇集了许多优秀博主的网站，欢迎大家参观和交换友链！"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】页面的关键词，多个关键词用英文逗号隔开。</span></span><br><span class="line"><span class="comment"># 同样主要用于 SEO，帮助搜索引擎理解页面内容。</span></span><br><span class="line"><span class="attr">keywords:</span> <span class="string">"友情链接, 博客圈, 技术博客, 生活分享, Hexo"</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                      可选字段 - 功能开关 (Optional)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】是否在此页面显示评论模块。</span></span><br><span class="line"><span class="comment"># true 为显示（默认），false 为关闭。</span></span><br><span class="line"><span class="attr">comments:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】是否为本页面单独开启数学公式渲染。</span></span><br><span class="line"><span class="comment"># 仅当主题全局配置中 per_page 设置为 false 时，这两个选项才需要在此处单独开启。</span></span><br><span class="line"><span class="attr">mathjax:</span> <span class="literal">false</span></span><br><span class="line"><span class="attr">katex:</span> <span class="literal">false</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】是否显示侧边栏。</span></span><br><span class="line"><span class="comment"># true 为显示（默认），false 为隐藏。若想打造沉浸感强的页面可设为 false。</span></span><br><span class="line"><span class="attr">aside:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】是否在本页面加载 APlayer 音乐播放器。</span></span><br><span class="line"><span class="comment"># 设置为 true 后，还需在页面正文中通过特定标签来配置播放列表。</span></span><br><span class="line"><span class="attr">aplayer:</span> <span class="literal">false</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】是否折叠本页面的所有代码块。</span></span><br><span class="line"><span class="comment"># true 为折叠，false 为展开。可用于覆盖主题的全局设置。</span></span><br><span class="line"><span class="attr">highlight_shrink:</span> <span class="literal">false</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                    可选字段 - 样式与外观 (Optional)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】页面的顶部大图（Banner）。</span></span><br><span class="line"><span class="comment"># 路径是相对于博客 `source` 目录的绝对路径。</span></span><br><span class="line"><span class="attr">top_img:</span> <span class="string">/img/banners/friends.jpg</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】为某些特殊页面的顶部模块设置背景图片。</span></span><br><span class="line"><span class="comment"># 这通常是平铺或覆盖的背景，而非 Banner 大图，用于增强设计感。</span></span><br><span class="line"><span class="attr">top_single_background:</span> <span class="string">/img/backgrounds/simple_sky.png</span></span><br><span class="line"></span><br><span class="line"><span class="meta">---</span></span><br><span class="line"><span class="meta"></span></span><br><span class="line"><span class="comment"># 这里是页面的正文 Markdown 内容...</span></span><br><span class="line"><span class="comment"># ...</span></span><br></pre></td></tr></tbody></table></figure><h5 id="2-Post-Front-matter-用于文章页配置"><a href="#2-Post-Front-matter-用于文章页配置" class="headerlink" title="2.Post Front-matter 用于文章页配置"></a>2.Post Front-matter 用于文章页配置</h5><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">---</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                      基本信息 (Required &amp; Core)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【必需】文章的标题。</span></span><br><span class="line"><span class="attr">title:</span> <span class="string">深入解析PWA核心技术与未来展望</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【必需】文章的创建日期。</span></span><br><span class="line"><span class="comment"># 格式为 YYYY-MM-DD HH:mm:ss。</span></span><br><span class="line"><span class="attr">date:</span> <span class="number">2025-06-09 18:30:00</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章的最后更新日期。</span></span><br><span class="line"><span class="comment"># 如果您修改了旧文章，强烈建议更新此时间，有助于SEO。</span></span><br><span class="line"><span class="attr">updated:</span> <span class="number">2025-06-09 20:15:00</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                      内容分类 (Categories &amp; Tags)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章分类。</span></span><br><span class="line"><span class="comment"># 分类具有层级关系（父子关系），适合用于组织结构化的内容。</span></span><br><span class="line"><span class="comment"># - 父分类</span></span><br><span class="line"><span class="comment">#   - 子分类</span></span><br><span class="line"><span class="attr">categories:</span></span><br><span class="line">  <span class="bullet">-</span> <span class="string">前端技术</span></span><br><span class="line">  <span class="bullet">-</span> <span class="string">Web</span> <span class="string">App</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章标签。</span></span><br><span class="line"><span class="comment"># 标签是扁平化的，没有层级关系，适合用于描述文章的关键词。</span></span><br><span class="line"><span class="attr">tags:</span></span><br><span class="line">  <span class="bullet">-</span> <span class="string">PWA</span></span><br><span class="line">  <span class="bullet">-</span> <span class="string">Hexo</span></span><br><span class="line">  <span class="bullet">-</span> <span class="string">Service</span> <span class="string">Worker</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                      SEO 与摘要 (SEO &amp; Description)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章的关键词，用英文逗号隔开。主要用于SEO。</span></span><br><span class="line"><span class="attr">keywords:</span> <span class="string">"PWA, Progressive Web App, Service Worker, Hexo博客"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章的摘要或描述。</span></span><br><span class="line"><span class="comment"># 会显示在首页文章卡片和搜索引擎结果中，是吸引点击的关键。</span></span><br><span class="line"><span class="attr">description:</span> <span class="string">"本文将从 Service Worker、Manifest 等核心组件出发，深入探讨PWA的工作原理，并展望其在未来的应用场景。"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章 AI 摘要功能的开关。</span></span><br><span class="line"><span class="comment"># 具体作用需参考您所用主题的文档。</span></span><br><span class="line"><span class="attr">ai:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                      页面展示与样式 (Display &amp; Style)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章顶部的横幅大图。</span></span><br><span class="line"><span class="attr">top_img:</span> <span class="string">/img/banners/pwa-banner.png</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章的缩略图（封面）。</span></span><br><span class="line"><span class="comment"># 1. 填写图片地址，则首页和文章页都用此图。</span></span><br><span class="line"><span class="comment"># 2. 设为 false，则不显示缩略图。</span></span><br><span class="line"><span class="comment"># 3. 若不设置此项，但设置了 `top_img`，则通常会用 `top_img` 作为封面。</span></span><br><span class="line"><span class="attr">cover:</span> <span class="string">/img/covers/pwa-cover.png</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章的主色调。</span></span><br><span class="line"><span class="comment"># 必须是6位的16进制颜色值，如 #123456，不能缩写为 #123。</span></span><br><span class="line"><span class="attr">main_color:</span> <span class="string">"#005af0"</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                首页推荐 (Homepage Features)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】配置文章在首页轮播图中显示。</span></span><br><span class="line"><span class="comment"># 数字越小，位置越靠前。设置此项即可让文章进入轮播。</span></span><br><span class="line"><span class="attr">swiper_index:</span> <span class="number">1</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】配置文章在首页右侧推荐卡片组中显示。</span></span><br><span class="line"><span class="comment"># 数字越小，位置越靠前。</span></span><br><span class="line"><span class="attr">top_group_index:</span> <span class="number">2</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"><span class="comment">#                     功能开关 (Feature Toggles)</span></span><br><span class="line"><span class="comment"># ===================================================================</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】是否显示文章的评论模块。默认为 true。</span></span><br><span class="line"><span class="attr">comments:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】TOC (Table of Contents) 目录的相关设置。</span></span><br><span class="line"><span class="comment"># 用于覆盖主题的全局默认配置。</span></span><br><span class="line"><span class="attr">toc:</span> <span class="literal">true</span> <span class="comment"># 是否显示目录</span></span><br><span class="line"><span class="attr">toc_number:</span> <span class="literal">false</span> <span class="comment"># 目录是否显示编号</span></span><br><span class="line"><span class="attr">toc_style_simple:</span> <span class="literal">true</span> <span class="comment"># 是否使用简洁样式的目录</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】文章版权模块的详细设置。</span></span><br><span class="line"><span class="comment"># 您可以在此覆盖全局的版权信息，为特定文章（如转载）设置不同的版权。</span></span><br><span class="line"><span class="attr">copyright:</span> <span class="literal">true</span> <span class="comment"># 是否显示版权模块</span></span><br><span class="line"><span class="attr">copyright_author:</span> <span class="string">"特邀作者 张三"</span> <span class="comment"># 自定义文章作者</span></span><br><span class="line"><span class="attr">copyright_author_href:</span> <span class="string">"https://example.com/zhangsan"</span> <span class="comment"># 自定义作者链接</span></span><br><span class="line"><span class="attr">copyright_url:</span> <span class="string">"https://example.com/original-post"</span> <span class="comment"># 自定义文章源链接</span></span><br><span class="line"><span class="attr">copyright_info:</span> <span class="string">"本文为特邀作者原创，转载请联系作者获得授权。"</span> <span class="comment"># 自定义版权声明文字</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 【可选】高级功能开关。</span></span><br><span class="line"><span class="comment"># 通常用于在全局关闭某功能时，为特定文章单独开启。</span></span><br><span class="line"><span class="attr">mathjax:</span> <span class="literal">false</span></span><br><span class="line"><span class="attr">katex:</span> <span class="literal">false</span></span><br><span class="line"><span class="attr">aplayer:</span> <span class="literal">false</span></span><br><span class="line"><span class="attr">highlight_shrink:</span> <span class="literal">true</span> <span class="comment"># true 代表本页代码块默认折叠</span></span><br><span class="line"><span class="attr">aside:</span> <span class="literal">true</span> <span class="comment"># true 代表本页显示侧边栏</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="meta">---</span></span><br></pre></td></tr></tbody></table></figure></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/9132.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/9132.html&quot;)">第二部分：Hexo 基础搭建与配置</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/9132.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=第二部分：Hexo 基础搭建与配置&amp;url=https://prorise666.site/posts/9132.html&amp;pic=https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>框架技术<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Hexo<span class="categoryesPageCount">31</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>博客搭建教程<span class="tagsPageCount">31</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/33216.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">第一部分：技术栈与环境准备</div></div></a></div><div class="next-post pull-right"><a href="/posts/58950.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">第三部分：集成与配置 Butterfly 主题——赋予博客华丽外观</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/65188.html" title="11.Twikoo 美化：添加自定义表情包"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">11.Twikoo 美化：添加自定义表情包</div></div></a></div><div><a href="/posts/24286.html" title="10.内容扩展：添加“安全跳转”中间页"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">10.内容扩展：添加“安全跳转”中间页</div></div></a></div><div><a href="/posts/20246.html" title="13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）</div></div></a></div><div><a href="/posts/43263.html" title="14.主题魔改：添加“背景切换”弹窗面板"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">14.主题魔改：添加“背景切换”弹窗面板</div></div></a></div><div><a href="/posts/57565.html" title="12.Twikoo 美化：自定义评论回复邮件模板"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">12.Twikoo 美化：自定义评论回复邮件模板</div></div></a></div><div><a href="/posts/10882.html" title="16.主题魔改：文章顶图根据封面图片自动配色"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">16.主题魔改：文章顶图根据封面图片自动配色</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"第二部分：Hexo 基础搭建与配置",date:"2025-06-19 18:13:45",updated:"2025-07-19 19:21:28",tags:["博客搭建教程"],categories:["框架技术","Hexo"],content:"\n## 第二部分：Hexo 基础搭建与配置\n\n在上一部分，我们已经完成了 Hexo 的环境准备和基本项目的初始化。现在，我将带您进一步了解 Hexo 的基础使用，包括核心命令以及对站点全局配置文件的初步认识。\n\n### 1. Hexo 常用基础命令\n\n掌握 Hexo 的基础命令是高效管理博客的关键。以下是一些最常用的命令：\n\n>如果您想在本地查看草稿的效果，普通的 `hexo server` 命令是看不到的，您必须使用一个特殊的命令：`hexo server --drafts` (或者简写 `hexo s --drafts`)，\n\n| 命令                | 简写 | 功能描述                                                     | 常用场景                                   | 备注                                                         |\n| :------------------ | :--- | :----------------------------------------------------------- | :----------------------------------------- | :----------------------------------------------------------- |\n| `hexo init [folder]`| -    | 初始化一个新博客项目到指定文件夹。如果文件夹不存在，Hexo 会创建它。 | 首次创建博客项目。                         | 通常在空目录下执行。                                         |\n| `hexo new <layout> <title>` | `hexo n` | 创建一篇新文件。`<layout>` 可以是 `post` (文章)、`page` (页面) 或 `draft` (草稿)，默认是 `post`。`<title>` 是文件名（不含扩展名）。 | 撰写新文章、创建关于页面等。               | 会根据 `scaffolds` 目录下的模板生成文件。                    |\n| `hexo generate`     | `hexo g` | 生成静态网站文件到 `public` 目录。                           | 内容更新后准备部署前；本地预览前（`hexo s` 通常包含此步骤）。 | 会处理 `source` 目录下的 Markdown 文件、主题和配置。         |\n| `hexo server`       | `hexo s` | 启动本地预览服务器。                                         | 在本地查看博客效果、调试主题或内容。       | 默认地址 `http://localhost:4000`，支持热重载。             |\n| `hexo deploy`       | `hexo d` | 将生成的静态网站文件部署到远程服务器（如 GitHub Pages）。    | 将本地博客发布到线上。                     | 需要先安装对应的部署插件并配置 `_config.yml` 中的 `deploy` 部分。 |\n| `hexo clean`        | -    | 清理 `public` 目录下的生成文件和 `db.json` 缓存文件。        | 遇到生成错误或需要强制完全重新生成时。     | 有助于解决一些缓存问题或文件冲突。                           |\n| `hexo version`      | `hexo v` | 显示 Hexo、Node.js 和 npm 的版本信息。                       | 检查环境是否符合要求；排查版本兼容性问题。 |                                                              |\n\n**常用命令组合：**\n\n*   **撰写并预览：** `hexo new post \"我的第一篇文章\"` -> 编写内容 -> `hexo clean && hexo server` -> 浏览器预览。\n*   **更新并部署：** 编写/修改内容 -> `hexo clean && hexo generate && hexo deploy` (或者更简单的 `hexo deploy -g`)。\n\n> **场景化解释：**\n> - 当我写完一篇新文章或者对已有文章进行了修改，我通常会先运行 `hexo clean` 清理掉旧的生成文件和缓存，然后运行 `hexo generate` 重新生成最新的静态网站。最后，为了验证修改是否正确，我会在本地运行 `hexo server` 进行预览。\n> - 当我确认本地预览无误，准备发布到线上时，我只需要运行 `hexo deploy` 命令（如果配置了自动生成，可以使用 `hexo deploy -g`）。这个命令会将 `public` 目录下的所有文件推送到我配置好的托管平台（比如 GitHub Pages）。\n\n### 2. Hexo 根目录配置文件 (`_config.yml`)\n\n`_config.yml` 文件位于 Hexo 项目的根目录下，它是站点的全局配置文件。大部分重要的全局设置，如网站标题、副标题、作者、语言、URL、文章链接格式、主题等，都在这里进行配置。\n\n这个文件采用 YAML 格式。YAML 格式使用缩进表示层级关系，键值对之间用冒号 `:` 分隔`（注意冒号后面需要加两个小空格`）。请务必注意缩进，Hexo 对 YAML 的缩进非常敏感，通常使用两个空格进行缩进。\n\n默认的文件是英文的以下是 `_config.yml` 文件中的中文翻译\n\n```yaml\n# Hexo Configuration\n## Docs: https://hexo.io/docs/configuration.html\n## Source: https://github.com/hexojs/hexo/\n\n# --- 网站信息 (Site) ---\n# 这部分定义了您博客的基础信息，会显示在网站的各个位置。\n# -----------------------------------------------------------\n# 网站主标题，会显示在浏览器标签页和主题的显眼位置。\ntitle: Hexo \n# 网站副标题，通常显示在主标题下方。\nsubtitle: ''\n# 网站描述，主要用于SEO，告诉搜索引擎您的网站是关于什么内容的。\ndescription: ''\n# 网站关键词，用于SEO，多个关键词用英文逗号隔开。\nkeywords:\n# 您的名字或昵称。\nauthor: John Doe\n# 网站语言。对于中文博客，强烈建议修改为 'zh-CN'。\nlanguage: en\n# 网站时区。建议设置为您所在的时区，以确保文章发布时间的准确性。\n# 例如：'Asia/Shanghai' (中国) 或 'Asia/Tokyo' (日本)。\n# Hexo 默认使用您电脑的时区，但明确指定更好。\ntimezone: ''\n\n\n# --- 网址 (URL) ---\n# 这部分配置与您网站的链接结构（URL）密切相关，非常重要。\n# -----------------------------------------------------------\n# 【重要】请务必修改为您的网站最终的访问网址！\n# 例如，如果您使用 GitHub Pages，它可能是 'https://yourname.github.io'。\n# 这个配置会影响网站所有资源的绝对路径，如果错误，可能导致CSS、JS、图片加载失败。\nurl: http://example.com\n# 文章的永久链接格式。\n# :year, :month, :day, :i_month, :i_day, :hour, :minute, :second, :title, :name, :post_title, :id, :category\n# 示例:\n#   :year/:month/:day/:title/  (默认值，例如 2025/06/08/hello-world/)\n#   :title.html               (例如 hello-world.html，非常简洁)\n#   :category/:title/          (例如 tech/hello-world/)\n# 推荐使用 hexo-abbrlink 插件生成短链接，对SEO友好且不会因修改标题而改变： permalink: posts/:abbrlink.html\npermalink: :year/:month/:day/:title/\n# 永久链接中各部分的默认值。\npermalink_defaults:\n# URL 美化选项。\npretty_urls:\n  # 是否移除永久链接末尾的 'index.html'。通常保持默认。\n  trailing_index: true \n  # 是否移除永久链接末尾的 '.html'。通常保持默认。\n  trailing_html: true \n\n\n# --- 目录 (Directory) ---\n# 这部分定义了您项目中的各个核心文件夹的名称，通常无需修改。\n# -----------------------------------------------------------\n# 源文件夹，您创作内容的地方（文章、图片等）。\nsource_dir: source\n# 公共文件夹，存放最终生成的静态网站文件（最终部署到服务器上的内容）。\npublic_dir: public\n# 标签页面的目录名。例如: yoursite.com/tags/\ntag_dir: tags\n# 归档页面的目录名。例如: yoursite.com/archives/\narchive_dir: archives\n# 分类页面的目录名。例如: yoursite.com/categories/\ncategory_dir: categories\n# 代码下载目录（如果您使用代码下载功能）。\ncode_dir: downloads/code\n# 国际化（i18n）语言文件的目录。\ni18n_dir: :lang\n# 跳过渲染指定的文件或文件夹。您可以在这里列出不希望被Hexo处理的文件路径。\nskip_render:\n\n\n# --- 写作 (Writing) ---\n# 这部分配置与您撰写文章时的行为相关。\n# -----------------------------------------------------------\n# 新文章的文件名格式。:title 是文章标题。\nnew_post_name: :title.md \n# 新建文件的默认布局，通常是 'post' (文章) 或 'draft' (草稿)。\ndefault_layout: post\n# 是否将文章标题转换为 \"Title Case\" (首字母大写)。建议 'false'，保持原文案。\ntitlecase: false \n# 外部链接设置。\nexternal_link:\n  # 是否在新标签页中打开外部链接。建议 'true'，以保留用户在您的网站上。\n  enable: true \n  # 应用范围。'site' 表示全站，'post' 表示仅文章内容。\n  field: site \n  # 在这里列出的域名将不会被当作外部链接处理。例如: 'exclude: yoursite.com'\n  exclude: ''\n# 文件名大小写转换。0: 无变化; 1: 小写; 2: 大写。\nfilename_case: 0\n# 是否渲染 'source/_drafts' 文件夹中的草稿。'false' 表示默认不渲染。\nrender_drafts: false\n# 是否启用文章资源文件夹。\n# 如果设为 'true'，当您用 `hexo new post \"xxx\"` 创建文章时，\n# 会在 `source/_posts` 目录下同时创建一个名为 \"xxx\" 的文件夹，方便您存放该文章专属的图片等资源。\npost_asset_folder: false\n# 是否将链接转换为与根目录的相对路径。通常保持 'false'。\nrelative_link: false\n# 是否渲染发布日期在未来的文章。'true' 表示会渲染。\nfuture: true\n# 代码高亮引擎。可选值: 'highlight.js' 或 'prismjs'。\n# Butterfly 等现代主题通常有自己的高亮方案，可能会覆盖此设置。\nsyntax_highlighter: highlight.js\n# highlight.js 的具体配置。\nhighlight:\n  # 是否显示行号。\n  line_number: true\n  # 是否自动检测语言。建议 'false' 以获得更好的性能和准确性。\n  auto_detect: false\n  # 用什么字符替换 Tab。\n  tab_replace: ''\n  # 是否用 `<table>` 包裹代码块以实现复杂的行号显示。\n  wrap: true\n  # 是否启用 highlight.js 内置的样式。通常主题会有自己的样式，所以设为 'false'。\n  hljs: false\n# prismjs 的具体配置。\nprismjs:\n  # 是否在预处理阶段进行语法高亮。\n  preprocess: true\n  # 是否显示行号。\n  line_number: true\n  # 用什么字符替换 Tab。\n  tab_replace: ''\n\n\n# --- 主页设置 (Home page setting) ---\n# 这部分控制您博客首页的文章列表行为。\n# -----------------------------------------------------------\nindex_generator:\n  # 首页的路径。空字符串 '' 表示网站根目录。\n  path: ''\n  # 每页显示的文章数量。0 表示禁用分页。\n  per_page: 10\n  # 文章排序方式。'-date' 表示按日期降序（最新的在最前），'date' 表示升序。\n  order_by: -date\n\n\n# --- 分类与标签 (Category & Tag) ---\n# -----------------------------------------------------------\n# 默认分类。当文章没有指定分类时，会使用此分类。\ndefault_category: uncategorized\n# 分类别名。例如: 'cate_alias: my-cate'\ncategory_map:\n# 标签别名。例如: 'tag_alias: my-tag'\ntag_map:\n\n\n# --- 元数据 (Metadata elements) ---\n# -----------------------------------------------------------\n# 是否在HTML头部注入 Hexo 的 meta generator 标签。有助于进行网站技术栈统计，建议保留。\nmeta_generator: true\n\n\n# --- 日期与时间格式 (Date / Time format) ---\n# Hexo 使用 Moment.js 库来处理时间格式。\n# 格式定义: http://momentjs.com/docs/#/displaying/format/\n# -----------------------------------------------------------\n# 日期显示格式。\ndate_format: YYYY-MM-DD\n# 时间显示格式。\ntime_format: HH:mm:ss\n# 文章更新时间的选项。\n# 'mtime': 使用文件的最后修改时间作为更新时间 (推荐)。\n# 'date': 使用 Front-matter 中的 'date' 字段作为更新时间。\n# 'empty': 不使用更新时间。\nupdated_option: 'mtime'\n\n\n# --- 分页 (Pagination) ---\n# 归档页（如分类页、标签页）的分页设置。\n# -----------------------------------------------------------\n# 每页显示的文章数量。0 表示禁用分页。\nper_page: 10\n# 分页的目录。例如: yoursite.com/page/2/\npagination_dir: page\n\n\n# --- 包含与排除文件 (Include / Exclude file(s)) ---\n# 这些选项仅对 'source/' 文件夹生效。\n# -----------------------------------------------------------\n# Hexo 默认会忽略隐藏文件和以 '_' 或 '#' 开头的文件/文件夹。\n# include: [.well-known] # 如果您需要 Hexo 处理某些被忽略的文件，可以在这里列出。\ninclude:\n# exclude: [temp/] # 如果您希望 Hexo 忽略 'source/' 下的某些文件或文件夹，可以在这里列出。\nexclude:\n# ignore: [*.log] # 全局忽略规则。\nignore:\n\n\n# --- 扩展 (Extensions) ---\n# -----------------------------------------------------------\n## 插件: https://hexo.io/plugins/\n## 主题: https://hexo.io/themes/\n# [重要] 当前使用的主题名称。请确保 'themes' 文件夹下有对应名称的主题文件夹。\n# 例如，要使用 Butterfly 主题，请修改为: 'theme: butterfly'\ntheme: landscape\n\n\n# --- 部署 (Deployment) ---\n# `hexo deploy` 命令的配置。\n# Docs: https://hexo.io/docs/one-command-deployment\n# -----------------------------------------------------------\ndeploy:\n  # 部署类型。例如，部署到 GitHub Pages，需要安装 `hexo-deployer-git` 插件，并将类型设为 'git'。\n  # 示例:\n  #   type: git\n  #   repo: **************:yourname/yourname.github.io.git\n  #   branch: main\n  type: ''\n```\n\n> **场景化解释：**\n> - 修改 `title`, `subtitle`, `description`, `author`, `language` 会直接影响博客的全局信息，比如在浏览器标签页、搜索引擎结果以及主题的某些位置显示。当我第一次搭建博客时，会在这里填入我的博客名称、简介、作者信息等。\n> - `url` 配置项至关重要，它告诉 Hexo 您的博客最终部署到哪个网址。如果此处配置错误，可能导致生成的静态文件中资源路径错误（比如 CSS/JS 加载失败），使得网站只有文字而没有样式。\n> - `permalink` 决定了您文章的 URL 形式。我个人比较喜欢简洁的 `:category/:title.html` 或 `:year/:month/:day/:title/` 格式，方便记忆和分享。\n\n在开始自定义主题之前，我建议您根据自己的信息修改 `_config.yml` 文件中的 `title`、`author` 和 `url` 等基本信息。`language` 建议修改为 `zh-CN` 以便更好地显示中文内容和使用中文主题。\n\n至此，我们已经完成了 Hexo 的基础搭建，了解了核心命令，并对站点配置文件有了初步认识。在下一部分，我们将正式安装并配置 魔改的Butterfly 主题，让您的博客焕然一新。\n\n\n\n### `3.页面配置（重点）`\n\n**Front-matter 的基本认识**\n\n`Front-matter` 是 `markdown` 文件最上方以 `---` 分隔的区域，用于指定个别档案的变数。其中又分为两种 markdown 里\n\n##### 1.Page Front-matter 用于页面配置\n\n```yaml\n---\n# ===================================================================\n#                          必需字段 (Required)\n# ===================================================================\n\n# 【默认通过hexo可以生成】页面的标题。\n# 它会显示在浏览器标签页、文章列表以及页面顶部。\ntitle: 友情链接\n\n# 【默认通过hexo可以生成】页面的创建日期。\n# 推荐格式为：YYYY-MM-DD HH:mm:ss。\ndate: 2025-06-09 20:00:00\n\n# 【必需】页面的类型或布局。\n# 这个字段至关重要，它会告诉主题该如何渲染这个页面。\n# 您需要根据所用主题的文档来填写，常见值有：\n# - tags: 标签页\n# - categories: 分类页\n# - about: 关于页\n# - link: 友情链接页\n# - photos: 相册页\n# - moment / friends: 朋友圈或即刻页\ntype: link\n\n\n# ===================================================================\n#                      可选字段 - 内容与SEO (Optional)\n# ===================================================================\n\n# 【可选】页面的最后更新日期。\n# 如果忽略，其值通常会默认等于创建日期 `date`。\nupdated: 2025-06-09 21:30:00\n\n# 【可选】页面的描述。\n# 这段文字主要用于 SEO，会显示在搜索引擎的结果摘要中，强烈建议认真填写。\ndescription: \"这里是我的个人博客的友情链接页面，汇集了许多优秀博主的网站，欢迎大家参观和交换友链！\"\n\n# 【可选】页面的关键词，多个关键词用英文逗号隔开。\n# 同样主要用于 SEO，帮助搜索引擎理解页面内容。\nkeywords: \"友情链接, 博客圈, 技术博客, 生活分享, Hexo\"\n\n\n# ===================================================================\n#                      可选字段 - 功能开关 (Optional)\n# ===================================================================\n\n# 【可选】是否在此页面显示评论模块。\n# true 为显示（默认），false 为关闭。\ncomments: true\n\n# 【可选】是否为本页面单独开启数学公式渲染。\n# 仅当主题全局配置中 per_page 设置为 false 时，这两个选项才需要在此处单独开启。\nmathjax: false\nkatex: false\n\n# 【可选】是否显示侧边栏。\n# true 为显示（默认），false 为隐藏。若想打造沉浸感强的页面可设为 false。\naside: true\n\n# 【可选】是否在本页面加载 APlayer 音乐播放器。\n# 设置为 true 后，还需在页面正文中通过特定标签来配置播放列表。\naplayer: false\n\n# 【可选】是否折叠本页面的所有代码块。\n# true 为折叠，false 为展开。可用于覆盖主题的全局设置。\nhighlight_shrink: false\n\n\n# ===================================================================\n#                    可选字段 - 样式与外观 (Optional)\n# ===================================================================\n\n# 【可选】页面的顶部大图（Banner）。\n# 路径是相对于博客 `source` 目录的绝对路径。\ntop_img: /img/banners/friends.jpg\n\n# 【可选】为某些特殊页面的顶部模块设置背景图片。\n# 这通常是平铺或覆盖的背景，而非 Banner 大图，用于增强设计感。\ntop_single_background: /img/backgrounds/simple_sky.png\n\n---\n\n# 这里是页面的正文 Markdown 内容...\n# ...\n```\n\n##### 2.Post Front-matter 用于文章页配置\n\n```yaml\n---\n# ===================================================================\n#                      基本信息 (Required & Core)\n# ===================================================================\n\n# 【必需】文章的标题。\ntitle: 深入解析PWA核心技术与未来展望\n\n# 【必需】文章的创建日期。\n# 格式为 YYYY-MM-DD HH:mm:ss。\ndate: 2025-06-09 18:30:00\n\n# 【可选】文章的最后更新日期。\n# 如果您修改了旧文章，强烈建议更新此时间，有助于SEO。\nupdated: 2025-06-09 20:15:00\n\n\n# ===================================================================\n#                      内容分类 (Categories & Tags)\n# ===================================================================\n\n# 【可选】文章分类。\n# 分类具有层级关系（父子关系），适合用于组织结构化的内容。\n# - 父分类\n#   - 子分类\ncategories:\n  - 前端技术\n  - Web App\n\n# 【可选】文章标签。\n# 标签是扁平化的，没有层级关系，适合用于描述文章的关键词。\ntags:\n  - PWA\n  - Hexo\n  - Service Worker\n\n\n# ===================================================================\n#                      SEO 与摘要 (SEO & Description)\n# ===================================================================\n\n# 【可选】文章的关键词，用英文逗号隔开。主要用于SEO。\nkeywords: \"PWA, Progressive Web App, Service Worker, Hexo博客\"\n\n# 【可选】文章的摘要或描述。\n# 会显示在首页文章卡片和搜索引擎结果中，是吸引点击的关键。\ndescription: \"本文将从 Service Worker、Manifest 等核心组件出发，深入探讨PWA的工作原理，并展望其在未来的应用场景。\"\n\n# 【可选】文章 AI 摘要功能的开关。\n# 具体作用需参考您所用主题的文档。\nai: true\n\n\n# ===================================================================\n#                      页面展示与样式 (Display & Style)\n# ===================================================================\n\n# 【可选】文章顶部的横幅大图。\ntop_img: /img/banners/pwa-banner.png\n\n# 【可选】文章的缩略图（封面）。\n# 1. 填写图片地址，则首页和文章页都用此图。\n# 2. 设为 false，则不显示缩略图。\n# 3. 若不设置此项，但设置了 `top_img`，则通常会用 `top_img` 作为封面。\ncover: /img/covers/pwa-cover.png\n\n# 【可选】文章的主色调。\n# 必须是6位的16进制颜色值，如 #123456，不能缩写为 #123。\nmain_color: \"#005af0\"\n\n\n# ===================================================================\n#                首页推荐 (Homepage Features)\n# ===================================================================\n\n# 【可选】配置文章在首页轮播图中显示。\n# 数字越小，位置越靠前。设置此项即可让文章进入轮播。\nswiper_index: 1\n\n# 【可选】配置文章在首页右侧推荐卡片组中显示。\n# 数字越小，位置越靠前。\ntop_group_index: 2\n\n\n# ===================================================================\n#                     功能开关 (Feature Toggles)\n# ===================================================================\n\n# 【可选】是否显示文章的评论模块。默认为 true。\ncomments: true\n\n# 【可选】TOC (Table of Contents) 目录的相关设置。\n# 用于覆盖主题的全局默认配置。\ntoc: true # 是否显示目录\ntoc_number: false # 目录是否显示编号\ntoc_style_simple: true # 是否使用简洁样式的目录\n\n# 【可选】文章版权模块的详细设置。\n# 您可以在此覆盖全局的版权信息，为特定文章（如转载）设置不同的版权。\ncopyright: true # 是否显示版权模块\ncopyright_author: \"特邀作者 张三\" # 自定义文章作者\ncopyright_author_href: \"https://example.com/zhangsan\" # 自定义作者链接\ncopyright_url: \"https://example.com/original-post\" # 自定义文章源链接\ncopyright_info: \"本文为特邀作者原创，转载请联系作者获得授权。\" # 自定义版权声明文字\n\n# 【可选】高级功能开关。\n# 通常用于在全局关闭某功能时，为特定文章单独开启。\nmathjax: false\nkatex: false\naplayer: false\nhighlight_shrink: true # true 代表本页代码块默认折叠\naside: true # true 代表本页显示侧边栏\n\n\n---\n```"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E9%83%A8%E5%88%86%EF%BC%9AHexo-%E5%9F%BA%E7%A1%80%E6%90%AD%E5%BB%BA%E4%B8%8E%E9%85%8D%E7%BD%AE"><span class="toc-number">1.</span> <span class="toc-text">第二部分：Hexo 基础搭建与配置</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-Hexo-%E5%B8%B8%E7%94%A8%E5%9F%BA%E7%A1%80%E5%91%BD%E4%BB%A4"><span class="toc-number">1.1.</span> <span class="toc-text">1. Hexo 常用基础命令</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-Hexo-%E6%A0%B9%E7%9B%AE%E5%BD%95%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6-config-yml"><span class="toc-number">1.2.</span> <span class="toc-text">2. Hexo 根目录配置文件 (_config.yml)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE%EF%BC%88%E9%87%8D%E7%82%B9%EF%BC%89"><span class="toc-number">1.3.</span> <span class="toc-text">3.页面配置（重点）</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-Page-Front-matter-%E7%94%A8%E4%BA%8E%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE"><span class="toc-number">1.3.0.1.</span> <span class="toc-text">1.Page Front-matter 用于页面配置</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-Post-Front-matter-%E7%94%A8%E4%BA%8E%E6%96%87%E7%AB%A0%E9%A1%B5%E9%85%8D%E7%BD%AE"><span class="toc-number">1.3.0.2.</span> <span class="toc-text">2.Post Front-matter 用于文章页配置</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>