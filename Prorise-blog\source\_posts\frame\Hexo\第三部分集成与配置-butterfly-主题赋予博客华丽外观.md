---
title: 第三部分：集成与配置 Butterfly 主题——赋予博客华丽外观
categories:
  - 框架技术
  - Hexo
tags:
  - 博客搭建教程
cover: 'https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp'
comments: true
toc: true
ai: true
abbrlink: 58950
date: 2025-06-19 19:13:45
---

## 第三部分：集成与配置 Butterfly 主题——赋予博客华丽外观

### Hexo Anzhiyu 主题

#### 快速起步

Butterfly 主题提供了丰富的内置配置项，让我们可以在不修改主题代码的情况下，实现背景、字体、配色等基础元素的个性化，但是从零打造一个全新样式的面板似乎有些太过困难了，我们采用在魔改Butterfly领域最出名的Anzhiyu主题来作为我们的基础起点

>温馨提示
>
>Anzhiyu主题拥有**Main**(稳定版)和**Dev**(测试版)两种版本, 我们推荐您安装使用**Github**(稳定版)以获得最佳的主题体验

下载 [最新 release 版本](https://github.com/anzhiyu-c/hexo-theme-anzhiyu/releases) 解压到 `themes` 目录，并将解压出的文件夹重命名为 `anzhiyu`（这一步非常重要）

**覆盖配置**

将`anzhiyu`文件夹的`_config.yml`内容重命名为`_config.anzhiyu.yml`放置在hexo文件夹根目录例如：`Prorise-blog/_config.anzhiyu.yml`

最后修改hexo的`_config.yml`

```yaml
# --- 扩展 (Extensions) ---
# -----------------------------------------------------------
## 插件: https://hexo.io/plugins/
## 主题: https://hexo.io/themes/
# [重要] 当前使用的主题名称。请确保 'themes' 文件夹下有对应名称的主题文件夹。
# 例如，要使用 Butterfly 主题，请修改为: 'theme: butterfly'
theme: anzhiyu
```

这样做能在更新主题时，不会将自己辛苦配置的所有配置都覆盖掉

我们也吧/themes/anzhiyu/source下的img文件复制一份，放置于Prorise-blog/source/img



#### 核心配置

默认拉取下来的主题还有一些 butterfly 官方的英文注释，所以我在这里给他全部都翻译成中文方便初始的覆盖（其中含有部分我的个人信息）

```yaml
# 菜单配置
menu:
  # 文章分类菜单项
  文章: # 菜单项名称
    隧道: /archives/ || anzhiyu-icon-box-archive # 子菜单项名称: 链接 || 图标类名
    分类: /categories/ || anzhiyu-icon-shapes
    标签: /tags/ || anzhiyu-icon-tags

  # 友链相关菜单项
  友链:
    友人帐: /link/ || anzhiyu-icon-link
    朋友圈: /fcircle/ || anzhiyu-icon-artstation
    留言板: /comments/ || anzhiyu-icon-envelope

  # 我的相关菜单项
  我的:
    音乐馆: /music/ || anzhiyu-icon-music
    追番页: /bangumis/ || anzhiyu-icon-bilibili
    相册集: /album/ || anzhiyu-icon-images
    小空调: /air-conditioner/ || anzhiyu-icon-fan

  # 关于相关菜单项
  关于:
    关于本人: /about/ || anzhiyu-icon-paper-plane
    闲言碎语: /essay/ || anzhiyu-icon-lightbulb
    随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1 # 使用js函数跳转到随机文章

# 导航栏相关配置 (顶部导航栏)
nav:
  enable: false # 是否启用顶部导航栏
  travelling: false # 是否显示前往“开往”按钮
  clock: false # 是否显示时钟
  menu: # 自定义导航菜单项
    - title: 网页 # 分组标题
      item: # 分组下的链接项
        - name: 博客 # 链接名称
          link: https://blog.anheyu.com/ # 链接地址
          icon: /img/favicon.ico # 链接图标 (图片url)
    - title: 项目
      item:
        - name: 安知鱼图床
          link: https://image.anheyu.com/
          icon: https://image.anheyu.com/favicon.ico

# mourn （哀悼日，指定日期网站简单变灰，不包括滚动条）
# 注意： 仅网站首页变灰，其他页面正常显示
mourn:
  enable: false # 是否启用哀悼日效果
  days: [4-5, 5-12, 7-7, 9-18, 12-13] # 指定哀悼日期，格式为 月-日

# Code Blocks (代码相关)
# --------------------------------------

highlight_theme: light # 代码块主题，可选值：darker / pale night / light / ocean / mac / mac light / false (关闭高亮)
highlight_copy: true # 是否显示代码块复制按钮
highlight_lang: true # 是否显示代码块语言名称
highlight_shrink: false # 是否启用代码块收缩功能。true: 收缩 / false: 展开 | none: 展开并隐藏收缩按钮
highlight_height_limit: 330 # 代码块收缩后的高度限制，单位：px
code_word_wrap: false # 是否启用代码块自动换行

# copy settings (复制设置)
# copyright: Add the copyright information after copied content (复制的内容后面加上版权信息)
# copy: enable 复制后弹窗提示版权信息
copy:
  enable: true # 复制内容后是否弹窗提示
  copyright:
    enable: false # 是否在复制的内容后面添加版权信息
    limit_count: 50 # 当复制字符数超过此限制时才添加版权信息

# social settings (社交图标设置)
# formal: # 格式说明
#   name: link || icon # 社交平台名称: 链接 || 图标类名
social:
  # Github: https://github.com/anzhiyu-c || anzhiyu-icon-github # Github图标示例
  # BiliBili: https://space.bilibili.com/372204786 || anzhiyu-icon-bilibili # Bilibili图标示例

# 作者卡片 状态 (侧边栏作者卡片上的个性签名/状态)
author_status:
  enable: false # 是否启用作者状态显示
  # 可以是任何图片，建议放表情包或者emoji图片，效果都很好，[表情包速查](https://emotion.xiaokang.me/)
  statusImg: "https://bu.dusays.com/2023/08/24/64e6ce9c507bb.png" # 状态图片url
  skills: # 技能/标签列表 (使用示例)
    # - 🤖️ 数码科技爱好者
    # - 🔍 分享与热心帮助
    # - 🏠 智能家居小能手
    # - 🔨 设计开发一条龙
    # - 🤝 专修交互与设计
    # - 🏃 脚踏实地行动派
    # - 🧱 团队小组发动机
    # - 💢 壮汉人狠话不多

# search (搜索)
# see https://blog.anheyu.com/posts/c27d.html#搜索系统
# --------------------------------------

# Algolia search (Algolia 搜索)
algolia_search:
  enable: false # 是否启用 Algolia 搜索
  hits:
    per_page: 6 # 每页显示搜索结果数
  tags: # 按标签过滤搜索结果 (使用示例)
    # - 前端
    # - Hexo

# Docsearch (Docsearch 搜索)
# Apply and Option Docs: see https://docsearch.algolia.com/
# Crawler Admin Console: see https://crawler.algolia.com/
# Settings: https://www.algolia.com/
docsearch:
  enable: false # 是否启用 Docsearch 搜索
  appId: # Algolia 应用 ID (参阅邮件获取)
  apiKey: # Algolia API Key (参阅邮件获取)
  indexName: # Algolia 索引名称 (参阅邮件获取)
  option: # Docsearch 其他配置项

# Local search (本地搜索)
local_search:
  enable: false # 是否启用本地搜索
  preload: true # 是否预加载搜索索引
  CDN: # 本地搜索所需的js文件CDN地址 (可选)

# Math (数学公式渲染)
# --------------------------------------
# About the per_page (关于 per_page 参数)
# if you set it to true, it will load mathjax/katex script in each page (true 表示每一页都加载js)
# if you set it to false, it will load mathjax/katex script according to your setting (add the 'mathjax: true' in page's front-matter)
# (false 需要时加载，须在使用的 Markdown Front-matter 加上 mathjax: true)

# MathJax
mathjax:
  enable: false # 是否启用 MathJax
  per_page: false # 是否在每一页都加载 MathJax 脚本

# KaTeX
katex:
  enable: false # 是否启用 KaTeX
  per_page: false # 是否在每一页都加载 KaTeX 脚本
  hide_scrollbar: true # 是否隐藏 KaTeX 渲染块的滚动条

# Image (图片设置)
# --------------------------------------

# Favicon（网站图标）
favicon: /favicon.ico # 网站 favicon 地址

# Avatar (头像)
avatar:
  img: /img/user/avatar.webp # 作者头像图片url
  effect: false # 头像是否启用悬停特效

# Disable all banner image (禁用所有顶部图片/横幅)
disable_top_img: false # 是否禁用所有页面的顶部图片

# The banner image of home page (首页顶部图片/横幅)
index_img: false # 首页顶部图片的背景样式，例如 "background: url() top / cover no-repeat"，设置为 false 则不显示

# If the banner of page not setting, it will show the top_img (如果页面未设置顶部图片，则显示此默认图片)
default_top_img: false # 默认顶部图片的url，设置为 false 则不显示默认图片

cover:
  # display the cover or not (是否显示文章封面)
  index_enable: true # 是否在首页文章列表中显示封面
  aside_enable: true # 是否在侧边栏显示封面 (如最新文章卡片)
  archives_enable: true # 是否在归档页显示封面
  # the position of cover in home page (首页文章封面显示的位置)
  # left/right/both
  position: left # 首页封面位置，可选 left/right/both (左/右/左右交替)
  # When cover is not set, the default cover is displayed (当没有设置cover时，默认的封面显示)
  default_cover: # 默认封面图片列表 (文章未设置封面时随机显示)
    # - /img/default_cover.jpg # 默认封面图片url示例

# Replace Broken Images (替换无法显示的图片)
error_img:
  flink: /img/friend_404.gif # 友链中图片加载失败时的替换图片
  post_page: /img/404.jpg # 文章内图片加载失败时的替换图片

# A simple 404 page (简单的404页面)
error_404:
  enable: true # 是否启用自定义404页面
  subtitle: "请尝试站内搜索寻找文章" # 404页面的副标题
  background: https://bu.dusays.com/2023/05/08/645907596997d.gif # 404页面的背景图片url

post_meta:
  page: # Home Page (主页文章列表的元信息显示设置)
    date_type: created # 日期类型，可选 created (创建日期) or updated (更新日期) or both (都显示)
    date_format: simple # 日期格式，可选 date (完整日期) / relative (相对日期，如“3天前”) / simple (简单日期，如 MM-DD)
    categories: true # 是否显示分类
    tags: true # 是否显示标签
    label: false # 是否显示元信息前面的描述性文字 (如“发布于”、“分类于”)
  post: # 文章详情页的元信息显示设置
    date_type: both # 日期类型，可选 created (创建日期) or updated (更新日期) or both (都显示)
    date_format: date # 日期格式，可选 date (完整日期) / relative (相对日期)
    categories: true # 是否显示分类
    tags: true # 是否显示标签
    label: true # 是否显示元信息前面的描述性文字
    unread: false # 是否启用文章未读功能 (显示阅读进度条)

# 主色调相关配置 (从图片中提取主色调并应用于页面元素)
mainTone:
  enable: false # 是否启用获取图片主色调功能
  mode: api # 获取主色调的模式，可选 cdn/api/both。cdn模式为图片url+imageAve参数获取主色调，api模式为请求API获取主色调，both模式会先请求cdn参数，无法获取的情况下将请求API获取。可以在文章内配置 main_color: '#3e5658'，使用十六进制颜色，则不会请求获取，而是直接使用配置的颜色。
  # 项目地址：https://github.com/anzhiyu-c/img2color-go
  api: https://img2color-go.vercel.app/api?img= # 当 mode 为 api 或 both 时，填写图片颜色提取API地址
  cover_change: true # 是否整篇文章跟随封面图片修改主色调

# wordcount (字数统计)
# see https://blog.anheyu.com/posts/c27d.html#字数统计
wordcount:
  enable: false # 是否启用字数统计和阅读时长功能
  post_wordcount: true # 是否在文章详情页显示字数统计
  min2read: true # 是否在文章详情页显示预计阅读时长
  total_wordcount: true # 是否在站点整体显示总字数统计

# Display the article introduction on homepage (在首页显示文章摘要)
# 1: description (优先显示文章 frontmatter 中的 description)
# 2: both (如果存在 description 则显示 description，否则显示自动截取的 auto_excerpt)
# 3: auto_excerpt (默认值，总是显示自动截取的摘要)
# false: do not show the article introduction (不在首页显示文章摘要)
index_post_content:
  method: 3 # 摘要显示方法
  length: 500 # 如果 method 设置为 2 或 3，需要配置自动截取的摘要长度 (单位：字符)

# anchor (锚点链接)
# when you scroll in post, the URL will update according to header id. (当你在文章中滚动时，URL 会根据标题的 ID 进行更新)
anchor: false # 是否启用滚动时更新URL的锚点功能

# Post (文章设置)
# --------------------------------------

# toc (目录)
toc:
  post: true # 是否在文章页显示目录
  page: false # 是否在普通页面显示目录
  number: true # 目录中是否显示标题编号
  expand: false # 是否默认展开所有目录项
  style_simple: false # 文章页目录是否使用简洁样式

post_copyright:
  enable: true # 是否启用文章版权信息
  decode: false # 是否解码博主姓名 (此处通常用于加密)
  author_href: # 博主名称链接 (留空则默认为站点首页)
  location: 长沙 # 文章发布地点
  license: CC BY-NC-SA 4.0 # 版权许可类型
  license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/ # 版权许可链接
  avatarSinks: false # 悬停时作者头像是否下沉
  copyright_author_img_back: # 版权信息区域作者头像背景图片
  copyright_author_img_front: # 版权信息区域作者头像前景图片
  copyright_author_link: / # 版权信息区域作者名称的链接

# Sponsor/reward (赞赏/打赏)
reward:
  enable: true # 是否启用赞赏功能
  QR_code: # 赞赏二维码列表
    - img: https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/qrcode-weichat.png # 二维码图片url
      link: # 二维码链接 (可选)
      text: 微信 # 二维码描述文本
    - img: https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/qrcode-alipay.png
      link:
      text: 支付宝

# Post edit (文章编辑链接)
# Easily browse and edit blog source code online. (方便在线浏览和编辑博客源代码)
post_edit: # 目前仅可选择一个平台在线编辑
  enable: false # 是否启用文章编辑链接
  # github: https://github.com/user-name/repo-name/edit/branch-name/subdirectory-name/ # Github 编辑链接格式
  # For example: https://github.com/jerryc127/butterfly.js.org/edit/main/source/ # Github 示例
  github: false # Github 编辑链接前缀

  # yuque: https://www.yuque.com/user-name/repo-name/ # 语雀编辑链接格式
  # 示例: https://www.yuque.com/yuque/yuque/
  # 你需要在语雀文章 Front Matter 添加参数 id 并确保其唯一性（例如 “id: yuque”, “id: 01”）
  yuque: false # 语雀编辑链接前缀

# Related Articles (相关文章)
related_post:
  enable: true # 是否启用相关文章功能
  limit: 6 # 显示相关文章的数量
  date_type: created # 相关文章的日期类型，可选 created (创建日期) or updated (更新日期)

# figcaption (图片描述文字)
photofigcaption: false # 是否显示图片的 figcaption 描述文字

# post_pagination (文章分页导航)
# value: 1 || 2 || 3 || 4 || false
# 1: The 'next post' will link to old post (下一篇文章链接到旧文章)
# 2: The 'next post' will link to new post (下一篇文章链接到新文章)
# 3: 只有下一篇，并且只在文章滚动到评论区时显示下一篇文章(旧文章)
# 4: 只有下一篇，并且只在文章滚动到评论区时显示下一篇文章(旧文章)，显示图片cover
# false: disable pagination (禁用文章分页导航)
post_pagination: 2 # 文章分页导航样式

# Displays outdated notice for a post (文章过期提醒)
noticeOutdate:
  enable: false # 是否启用文章过期提醒
  style: flat # 样式，可选 simple/flat
  limit_day: 365 # 文章发布或更新超过多少天后显示提醒
  position: top # 提醒显示位置，可选 top/bottom (顶部/底部)
  message_prev: It has been # 提醒信息前缀文本
  message_next: days since the last update, the content of the article may be outdated. # 提醒信息后缀文本

# Share System (分享功能)
# --------------------------------------

# Share.js (分享库)
# https://github.com/overtrue/share.js
sharejs:
  enable: true # 是否启用 Share.js 分享功能
  sites: facebook,twitter,wechat,weibo,qq # 要显示的分享平台列表 (用逗号分隔)

# AddToAny (分享库)
# https://www.addtoany.com/
addtoany:
  enable: false # 是否启用 AddToAny 分享功能
  item: facebook,twitter,wechat,sina_weibo,email,copy_link # 要显示的分享平台列表 (用逗号分隔)

# Comments System (评论系统)
# --------------------------------------

comments:
  # Up to two comments system, the first will be shown as default (最多可配置两个评论系统，第一个将作为默认显示)
  # Choose: Valine/Waline/Twikoo/Artalk (可选择的评论系统)
  use: # Twikoo/Waline # 配置使用的评论系统名称 (例如 ['Twikoo', 'Waline'])
  text: true # 是否在评论按钮旁边显示评论系统的名称
  # lazyload: The comment system will be load when comment element enters the browser's viewport. (评论系统在滚动到视口时加载)
  # If you set it to true, the comment count will be invalid (如果启用懒加载，评论计数可能不准确)
  lazyload: false # 是否启用评论系统懒加载
  count: false # 是否在文章顶部图片中显示评论计数
  card_post_count: false # 是否在首页文章列表卡片中显示评论计数

# valine (Valine 评论系统)
# https://valine.js.org
valine:
  appId: xxxxx # LeanCloud 应用 App ID
  appKey: xxxxx # LeanCloud 应用 App Key
  pageSize: 10 # 评论列表每页显示数量
  avatar: mp # Gravatar 头像样式，可选值参考 https://valine.js.org/#/avatar
  lang: zh-CN # 语言，可选 zh-CN/zh-TW/en/ja 等
  placeholder: 填写QQ邮箱就会使用QQ头像喔~. # 评论输入框的占位文本
  guest_info: nick,mail,link # 评论者信息填写项，可选 nick/mail/link (昵称/邮箱/网址)
  recordIP: false # 是否记录评论者 IP 地址
  serverURLs: # LeanCloud 国内自定义域名，海外版无需填写
  bg: /img/comment_bg.png # Valine 评论框背景图片
  emojiCDN: //i0.hdslb.com/bfs/emote/ # Emoji CDN 地址
  enableQQ: true # 是否启用 QQ 邮箱自动获取昵称和头像
  requiredFields: nick,mail # 必填字段，可选 nick/mail
  visitor: false # 是否启用文章阅读量统计 (基于 Valine 的 LeanCloud)
  master: # 博主邮箱 MD5 值列表 (用于标识博主身份)
    - xxxxx
  friends: # 朋友邮箱 MD5 值列表 (用于标识朋友身份)
    - xxxxxx
  tagMeta: "博主,小伙伴,访客" # 评论身份标签文本
  option: # 其他可选配置项

# waline - A simple comment system with backend support fork from Valine (Waline 评论系统)
# https://waline.js.org/
waline:
  serverURL: # Waline 后端服务地址 URL
  bg: # Waline 评论框背景图片
  pageview: false # 是否启用文章阅读量统计 (基于 Waline)
  meta_css: false # 是否引入 waline-meta.css, 以便显示 meta 图标
  imageUploader: true # 是否启用图片上传功能。配置为 > 换行后可自定义图片上传逻辑，示例: https://waline.js.org/cookbook/customize/upload-image.html#案例
  # 以下为可选配置，后续若有新增/修改配置参数可在此自行添加/修改
  option: # 其他可选配置项

# Twikoo (Twikoo 评论系统)
# https://github.com/imaegoo/twikoo
twikoo:
  envId: # Twikoo 环境 ID
  region: # Twikoo 环境地域 (例如 ap-shanghai, 可选)
  visitor: false # 是否启用文章阅读量统计 (基于 Twikoo)
  option: # 其他可选配置项

# Artalk (Artalk 评论系统)
# https://artalk.js.org/guide/frontend/config.html
artalk:
  server: # Artalk 后端服务地址
  site: # Artalk 站点名称
  visitor: false # 是否启用文章阅读量统计 (基于 Artalk)
  option: # 其他可选配置项

# giscus (giscus 评论系统)
# https://giscus.app/
giscus:
  repo: # GitHub 仓库名称 (格式: owner/repo)
  repo_id: # GitHub 仓库 ID
  category_id: # GitHub Discussions 分类 ID
  theme: # 主题样式
    light: light # Light mode 主题
    dark: dark # Dark mode 主题
  option: # 其他可选配置项
    data-lang: zh-CN # 语言
    data-mapping: # Discussions 和页面的映射关系
    data-category: # 分类名称 (如果设置了 data-category-id 则优先使用 ID)
    data-input-position: # 输入框位置

# Chat Services (聊天服务)
# --------------------------------------

# Chat Button [recommend] (聊天按钮 [推荐])
# It will create a button in the bottom right corner of website, and hide the origin button (这会在网站右下角创建一个聊天按钮，并隐藏原始的聊天窗口按钮)
chat_btn: false # 是否启用聊天按钮 (启用后原始聊天窗口会被隐藏)

# The origin chat button is displayed when scrolling up, and the button is hidden when scrolling down (原始聊天按钮在向上滚动时显示，向下滚动时隐藏)
chat_hide_show: false # 是否启用聊天按钮的滚动隐藏/显示效果

# chatra (Chatra 聊天服务)
# https://chatra.io/
chatra:
  enable: false # 是否启用 Chatra
  id: # Chatra 项目 ID

# tidio (Tidio 聊天服务)
# https://www.tidio.com/
tidio:
  enable: false # 是否启用 Tidio
  public_key: # Tidio 公钥

# daovoice (DaoVoice 聊天服务)
# http://daovoice.io/
daovoice:
  enable: false # 是否启用 DaoVoice
  app_id: # DaoVoice 应用 ID

# crisp (Crisp 聊天服务)
# https://crisp.chat/en/
crisp:
  enable: false # 是否启用 Crisp
  website_id: # Crisp 网站 ID

# Footer Settings (页脚设置)
# --------------------------------------
footer:
  owner: # 站点拥有者信息
    enable: true # 是否显示站点拥有者信息
    since: 2020 # 站点创建年份
  custom_text: # 自定义页脚文本
  runtime: # 网站运行时间
    enable: false # 是否显示网站运行时间
    launch_time: 04/01/2021 00:00:00 # 网站上线时间，格式为 月/日/年 时:分:秒
    work_img: https://npm.elemecdn.com/anzhiyu-blog@2.0.4/img/badge/安知鱼-上班摸鱼中.svg # 上班状态图片
    work_description: 距离月入25k也就还差一个大佬带我~ # 上班状态描述
    offduty_img: https://npm.elemecdn.com/anzhiyu-blog@2.0.4/img/badge/安知鱼-下班啦.svg # 下班状态图片
    offduty_description: 下班了就该开开心心的玩耍，嘿嘿~ # 下班状态描述
  # 徽标部分配置项 https://shields.io/
  # https://img.shields.io/badge/CDN-jsDelivr-orange?style=flat&logo=jsDelivr
  bdageitem: # 徽标列表
    enable: false # 是否启用徽标显示
    list: # 徽标项列表
      - link: https://hexo.io/ # 徽标指向网站链接
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg # 徽标图片API或URL
        message: 博客框架为Hexo_v5.4.0 # 鼠标悬停时显示的徽标提示语
      - link: https://blog.anheyu.com/
        shields: https://npm.elemecdn.com/anzhiyu-theme-static@1.0.9/img/Theme-AnZhiYu-2E67D3.svg
        message: 本站使用AnZhiYu主题
      # - link: https://www.dogecloud.com/ # 多吉云CDN示例
      #   shields: https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg
      #   message: 本站使用多吉云为静态资源提供CDN加速
      # - link: https://github.com/ # Github 托管示例
      #   shields: https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg
      #   message: 本站项目由Github托管
      # - link: http://creativecommons.org/licenses/by-nc-sa/4.0/ # CC 许可示例
      #   shields: https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg
      #   message: 本站采用知识共享署名-非商业性使用-相同方式共享4.0国际许可协议进行许可
  socialBar: # 社交链接栏 (在页脚显示社交图标)
    enable: false # 是否启用社交链接栏
    centerImg: # 中心图片url (可选)
    left: # 左侧社交链接列表 (使用示例)
      # - title: email # 链接标题
      #   link: mailto:<EMAIL> # 链接地址 (mailto: 发送邮件)
      #   icon: anzhiyu-icon-envelope # 图标类名
      # - title: 微博
      #   link: https://weibo.com/u/**********
      #   icon: anzhiyu-icon-weibo
      # - title: facebook
      #   link: https://www.facebook.com/profile.php?id=100092208016287&sk=about
      #   icon: anzhiyu-icon-facebook1
      # - title: RSS
      #   link: atom.xml
      #   icon: anzhiyu-icon-rss
    right: # 右侧社交链接列表 (使用示例)
      # - title: Github
      #   link: https://github.com/anzhiyu-c
      #   icon: anzhiyu-icon-github
      # - title: Bilibili
      #   link: https://space.bilibili.com/372204786
      #   icon: anzhiyu-icon-bilibili
      # - title: 抖音
      #   link: https://v.douyin.com/DwCpMEy/
      #   icon: anzhiyu-icon-tiktok
      # - title: CC
      #   link: /copyright
      #   icon: anzhiyu-icon-copyright-line
  list: # 页脚自定义链接列表 (多列显示)
    enable: false # 是否启用页脚自定义链接列表
    randomFriends: 3 # 随机显示友链数量 (如果启用友链页面)
    project: # 项目分组 (使用示例)
      # - title: 服务 # 分组标题
      #   links: # 链接列表
      #     - title: 51la统计 # 链接名称
      #       link: https://v6.51.la/ # 链接地址
      #     - title: 十年之约
      #       link: https://www.foreverblog.cn/
      #     - title: 开往
      #       link: https://github.com/travellings-link/travellings
      # - title: 主题 # 主题分组示例
      #   links:
      #     - title: 文档
      #       link: /docs/
      #     - title: 源码
      #       link: https://github.com/anzhiyu-c/hexo-theme-anzhiyu
      #     - title: 更新日志
      #       link: /update/
      # - title: 导航 # 导航分组示例
      #   links:
      #     - title: 即刻短文
      #       link: /essay/
      #     - title: 友链文章
      #       link: /fcircle/
      #     - title: 留言板
      #       link: /comments/
      # - title: 协议 # 协议分组示例
      #   links:
      #     - title: 隐私协议
      #       link: /privacy/
      #     - title: Cookies
      #       link: /cookies/
      #     - title: 版权协议
      #       link: /copyright/
  footerBar: # 页脚底部横栏
    enable: true # 是否启用页脚底部横栏
    authorLink: / # 作者名称的链接 (显示在底部横栏)
    cc: # CC 许可信息
      enable: false # 是否显示 CC 许可链接
      link: /copyright # CC 许可页面链接
    linkList: # 底部横栏自定义链接列表
      - link: https://github.com/anzhiyu-c/hexo-theme-anzhiyu # 链接地址
        text: 主题 # 链接文本
      # - link: https://image.anheyu.com # 图床链接示例
      #   text: 图床
      # - link: https://beian.miit.gov.cn/ # ICP 备案链接示例
      #   text: 湘ICP备-xxxxxxx号
    subTitle: # 底部横栏副标题
      enable: false # 是否启用底部横栏副标题
      # Typewriter Effect (打字效果)
      effect: true # 是否启用打字效果
      # Effect Speed Options (打字效果速度参数)
      startDelay: 300 # 打字开始前的延迟时间 (毫秒)
      typeSpeed: 150 # 打字速度 (毫秒/字符)
      backSpeed: 50 # 回退速度 (毫秒/字符)
      # loop (循环打字)
      loop: true # 是否循环打字
      # source 调用第三方服务 (副标题内容来源)
      # source: false 关闭调用
      # source: 1  调用一言网的一句话（简体） https://hitokoto.cn/
      # source: 2  调用一句网（简体） http://yijuzhan.com/
      # source: 3  调用今日诗词（简体） https://www.jinrishici.com/
      # subtitle 会先显示 source , 再显示 sub 的内容 (如果同时启用 source 和 sub，会先显示 source 的内容，再显示 sub 的内容)
      source: 1 # 副标题内容来源
      # 如果关闭打字效果，subtitle 只会显示 sub 的第一行文字 (如果禁用打字效果，只会显示 sub 列表中的第一项)
      sub: # 自定义副标题文本列表
        # - 生活明朗&#44; 万物可爱&#44; 人间值得&#44; 未来可期. # 文本内容，&#44; 是逗号的 HTML 实体编码

# Analysis (统计分析)
# --------------------------------------

# Baidu Analytics (百度统计)
# https://tongji.baidu.com/web/welcome/login
baidu_analytics: # 百度统计代码 ID (hm.baidu.com/hm.js? 后面的那串字符)

# Google Analytics (谷歌统计)
# https://analytics.google.com/analytics/web/
google_analytics: # 谷歌统计跟踪 ID (UA-XXXXX-Y 或 G-XXXXX)

# CNZZ Analytics (CNZZ 统计, 现友盟统计)
# https://www.umeng.com/
cnzz_analytics: # CNZZ 统计代码 ID

# Cloudflare Analytics (Cloudflare 统计)
# https://www.cloudflare.com/zh-tw/web-analytics/
cloudflare_analytics: # Cloudflare 统计 Token

# Microsoft Clarity (微软 Clarity)
# https://clarity.microsoft.com/
microsoft_clarity: # Microsoft Clarity 项目 ID

# Advertisement (广告)
# --------------------------------------

# Google Adsense (谷歌广告)
google_adsense:
  enable: false # 是否启用 Google Adsense
  auto_ads: true # 是否启用自动广告
  js: https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js # Adsense js 脚本地址 (通常无需修改)
  client: # Adsense Publisher ID (pub-XXXXXXXXXXXXXXX)
  enable_page_level_ads: true # 是否启用页面级广告 (已废弃，由 auto_ads 替代)

# Insert ads manually (手动插入广告)
# ad: # 广告插入位置配置
#   index: # 首页文章列表中的广告代码
#   aside: # 侧边栏广告代码
#   post: # 文章详情页中的广告代码

# Verification (站长验证)
# --------------------------------------

site_verification: # 站长验证列表
  - name: google-site-verification # 验证名称
    content: xxx # 验证代码
  - name: baidu-site-verification
    content: code-xxx
  - name: msvalidate.01
    content: xxx

# Beautify/Effect (美化/效果)
# --------------------------------------

# Theme color for customize (自定义主题颜色)
# Notice: color value must in double quotes like "#000" or may cause error! (注意：颜色值必须用双引号括起来，如 "#000"，否则可能出错!)

theme_color:
  enable: true # 是否启用自定义主题颜色
  main: "#425AEF" # 主题主色调 (light mode)
  dark_main: "#f2b94b" # 主题主色调 (dark mode)
  paginator: "#425AEF" # 分页器颜色
  #   button_hover: "#FF7242" # 按钮悬停颜色 (示例)
  text_selection: "#2128bd" # 文本选中颜色
  link_color: "var(--anzhiyu-fontcolor)" # 链接颜色 (使用CSS变量)
  meta_color: "var(--anzhiyu-fontcolor)" # 元信息颜色 (使用CSS变量)
  hr_color: "#4259ef23" # 水平分隔线颜色
  code_foreground: "#fff" # 代码块前景色
  code_background: "var(--anzhiyu-code-stress)" # 代码块背景色 (使用CSS变量)
  toc_color: "#425AEF" # 目录颜色
  #   blockquote_padding_color: "#425AEF" # 引用块 padding 颜色 (示例)
  #   blockquote_background_color: "#425AEF" # 引用块背景颜色 (示例)
  scrollbar_color: "var(--anzhiyu-scrollbar)" # 滚动条颜色 (使用CSS变量)
  meta_theme_color_light: "#f7f9fe" # 移动端浏览器顶部主题色 (light mode)
  meta_theme_color_dark: "#18171d" # 移动端浏览器顶部主题色 (dark mode)

# 移动端侧栏 (Mobile sidebar)
sidebar:
  site_data: # 站点数据卡片 (归档、标签、分类计数)
    archive: true # 是否显示归档计数
    tag: true # 是否显示标签计数
    category: true # 是否显示分类计数
  menus_items: true # 是否显示菜单项卡片
  tags_cloud: true # 是否显示标签云卡片
  display_mode: true # 是否显示显示模式切换 (亮色/暗色)
  nav_menu_project: true # 是否显示导航菜单项目卡片 (对应nav.menu配置)

# 文章h2添加分隔线
h2Divider: false # 是否在文章的 H2 标题下方添加分隔线

# 表格隔行变色
table_interlaced_discoloration: false # 是否启用表格隔行变色效果

# 首页双栏显示 (文章列表双列布局)
article_double_row: true # 是否在首页启用文章列表双列布局 (仅在大屏幕下生效)

# The top_img settings of home page (首页顶部图片/横幅设置)
# default: top img - full screen, site info - middle (默认：顶部图片全屏，站点信息居中)
# The position of site info, eg: 300px/300em/300rem/10% (主页标题距离顶部距离)
index_site_info_top: # 首页站点信息距离顶部的距离
# The height of top_img, eg: 300px/300em/300rem (主页top_img高度)
index_top_img_height: # 首页顶部图片的高度

# The user interface setting of category and tag page (category和tag页的UI设置)
# index - same as Homepage UI (index 值代表 UI将与首页的UI一样)
# default - same as archives UI 默认跟archives页面UI一样
category_ui: # 分类页面的 UI 样式，留空或 index
tag_ui: # 标签页面的 UI 样式，留空或 index

# Footer Background (页脚背景)
footer_bg: false # 页脚是否使用背景图片

# the position of bottom right button/default unit: px (右下角按钮距离底部的距离/默认单位为px)
rightside-bottom: 100px # 右下角功能按钮组距离浏览器底部的距离

# Background effects (背景特效)
# --------------------------------------

# canvas_ribbon (静止彩带背景)
# See: https://github.com/hustcc/ribbon.js
canvas_ribbon:
  enable: false # 是否启用静止彩带背景
  size: 150 # 彩带大小
  alpha: 0.6 # 彩带透明度 (0~1)
  zIndex: -1 # 彩带元素的 z-index 值 (通常设置为 -1 使其在背景层)
  click_to_change: false # 是否点击页面时改变彩带颜色
  mobile: false # 是否在移动端启用

# Fluttering Ribbon (动态彩带)
canvas_fluttering_ribbon:
  enable: true # 是否启用动态彩带背景
  mobile: false # 是否在移动端启用

# canvas_nest (动态线条背景)
# https://github.com/hustcc/canvas-nest.js
canvas_nest:
  enable: false # 是否启用动态线条背景
  color: "0,0,255" # 线条颜色，格式为 RGB 值 (R,G,B)。注意：使用逗号分隔
  opacity: 0.7 # 线条透明度 (0~1)
  zIndex: -1 # 背景元素的 z-index 值
  count: 99 # 线条数量
  mobile: false # 是否在移动端启用

# Typewriter Effect (打字效果)
# https://github.com/disjukr/activate-power-mode
activate_power_mode:
  enable: false # 是否启用打字时的 Power Mode 效果
  colorful: true # 是否启用粒子动画 (冒光特效)
  shake: false # 是否启用抖动特效
  mobile: false # 是否在移动端启用

# Mouse click effects: fireworks (鼠标点击效果: 烟火特效)
fireworks:
  enable: false # 是否启用鼠标点击烟火特效
  zIndex: 9999 # 烟火元素的 z-index 值 (-1 或 9999)
  mobile: false # 是否在移动端启用

# Mouse click effects: Heart symbol (鼠标点击效果: 爱心)
click_heart:
  enable: true # 是否启用鼠标点击爱心特效
  mobile: false # 是否在移动端启用

# Mouse click effects: words (鼠标点击效果: 文字)
ClickShowText:
  enable: false # 是否启用鼠标点击文字特效
  text: # 点击后显示的文本列表
    # - I # 文本内容示例
    # - LOVE
    # - YOU
  fontSize: 15px # 文字大小
  random: false # 是否随机显示文本列表中的内容
  mobile: false # 是否在移动端启用

# Default display mode (网站默认的显示模式)
# light (default) / dark (默认亮色模式 / 暗色模式)
display_mode: light # 网站默认显示模式

# Beautify (美化页面显示)
beautify:
  enable: true # 是否启用页面美化功能
  field: post # 应用范围，可选 site (全站) / post (仅文章)
  title-prefix-icon: '\f0c1' # 标题前缀图标的 Unicode 值 (Font Awesome 图标)
  title-prefix-icon-color: "#F47466" # 标题前缀图标颜色

# Global font settings (全局字体设置)
# Don't modify the following settings unless you know how they work (非必要不要修改)
font:
  global-font-size: 16px # 全局默认字体大小
  code-font-size: # 代码块字体大小 (留空则使用全局字体大小或浏览器默认大小)
  font-family: # 全局字体栈 (例如: 'Arial, sans-serif')
  code-font-family: consolas, Menlo, "PingFang SC", "Microsoft JhengHei", "Microsoft YaHei", sans-serif # 代码块字体栈

# Font settings for the site title and site subtitle (网站标题和副标题字体设置)
# 左上角网站名字 主页居中网站名字
blog_title_font:
  font_link: # 字体文件链接 (如来自 Google Fonts)
  font-family: PingFang SC, 'Hiragino Sans GB', 'Microsoft JhengHei', 'Microsoft YaHei', sans-serif # 字体栈

# The setting of divider icon (水平分隔线图标设置)
hr_icon:
  enable: true # 是否启用水平分隔线图标
  icon: \f0c4 # 图标的 Unicode 值 (Font Awesome 图标，如 '\f0c4' 表示链接图标)
  icon-top: # 图标距离顶部距离 (可选)

# the subtitle on homepage (主页subtitle)
subtitle:
  enable: false # 是否启用主页副标题
  # Typewriter Effect (打字效果)
  effect: true # 是否启用打字效果
  # Effect Speed Options (打字效果速度参数)
  startDelay: 300 # 打字开始前的延迟时间 (毫秒)
  typeSpeed: 150 # 打字速度 (毫秒/字符)
  backSpeed: 50 # 回退速度 (毫秒/字符)
  # loop (循环打字)
  loop: true # 是否循环打字
  # source 调用第三方服务 (副标题内容来源)
  # source: false 关闭调用
  # source: 1  调用一言网的一句话（简体） https://hitokoto.cn/
  # source: 2  调用一句网（简体） http://yijuzhan.com/
  # source: 3  调用今日诗词（简体） https://www.jinrishici.com/
  # subtitle 会先显示 source , 再显示 sub 的内容 (如果同时启用 source 和 sub，会先显示 source 的内容，再显示 sub 的内容)
  source: 1 # 副标题内容来源
  # 如果关闭打字效果，subtitle 只会显示 sub 的第一行文字 (如果禁用打字效果，只会显示 sub 列表中的第一项)
  sub: # 自定义副标题文本列表
    # - 生活明朗&#44;万物可爱&#44;人间值得&#44;未来可期. # 文本内容

# Loading Animation (加载动画)
preloader:
  enable: true # 是否启用页面加载动画
  # source (加载动画类型)
  # 1. fullpage-loading (全屏加载动画)
  # 2. pace (进度条)
  # else all (默认显示头像加载动画)
  source: 3 # 加载动画类型
  # pace theme (pace 进度条主题，参见 https://codebyzach.github.io/pace/)
  pace_css_url: # pace 主题 CSS 文件地址
  avatar: /img/user/avatar.png # 自定义加载动画头像图片url (当 source 非 1 或 2 时生效)

# aside (侧边栏)
# --------------------------------------

aside:
  enable: true # 是否启用侧边栏
  hide: false # 是否默认隐藏侧边栏 (需要配合 button: true 使用)
  button: true # 是否显示侧边栏显示/隐藏按钮
  mobile: true # 是否在移动端显示侧边栏
  position: right # 侧边栏位置，可选 left (左) or right (右)
  display: # 控制对应详情页面是否显示侧边栏
    archive: true # 归档页是否显示侧边栏
    tag: true # 标签页是否显示侧边栏
    category: true # 分类页是否显示侧边栏
  card_author: # 作者信息卡片
    enable: true # 是否启用作者信息卡片
    description: '<div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">这里是我的全栈开发笔记与思考，涵盖从 <b style="color:#fff">前端界面</b> 到 <b style="color:#fff">后端架构</b> 的全链路实践，以及 <b style="color:#fff">数据库设计</b>、<b style="color:#fff">自动化部署</b> 与 <b style="color:#fff">性能优化</b> 的相关探索。</div><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">无论你是初学者还是资深同行，相信都能在这里找到有价值的 <b style="color:#fff">实战教程</b> 与 <b style="color:#fff">深度解析</b>。欢迎一起交流，共同进步。</div>'
    name_link: / # 作者姓名的链接
  card_announcement: # 公告卡片
    enable: true # 是否启用公告卡片
    content: "这聒噪的世界,让沉默的人显得" # 公告内容
  card_weixin: # 微信二维码卡片
    enable: true # 是否启用微信二维码卡片
    face: https://bu.dusays.com/2023/01/13/63c02edf44033.png # 微信前置二维码图片url (如个人微信)
    backFace: https://bu.dusays.com/2023/05/13/645fa415e8694.png # 微信背景二维码图片url (如公众号)
  card_recent_post: # 最新文章卡片
    enable: true # 是否启用最新文章卡片
    limit: 5 # 显示最新文章数量 (设置为 0 显示所有)
    sort: date # 排序方式，可选 date (按创建日期) or updated (按更新日期)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  card_categories: # 分类卡片
    enable: false # 是否启用分类卡片
    limit: 8 # 显示分类数量 (设置为 0 显示所有)
    expand: none # 默认展开子分类，可选 none (不展开) / true (展开所有) / false (不展开)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  card_tags: # 标签卡片 (标签云)
    enable: true # 是否启用标签卡片
    limit: 40 # 显示标签数量 (设置为 0 显示所有)
    color: false # 标签是否使用随机颜色
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
    highlightTags: # 高亮显示的标签列表 (使用示例)
      # - Hexo
      # - 前端
  card_archives: # 归档卡片
    enable: true # 是否启用归档卡片
    type: monthly # 归档类型，可选 yearly (按年) or monthly (按月)
    format: MMMM YYYY # 归档格式，例如 MMMM YYYY (七月 2023)
    order: -1 # 排序顺序，1 为升序，-1 为降序
    limit: 8 # 显示归档数量 (设置为 0 显示所有)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  card_webinfo: # 站点信息卡片
    enable: true # 是否启用站点信息卡片
    post_count: true # 是否显示文章总数
    last_push_date: false # 是否显示站点最后更新日期
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)

# busuanzi count for PV / UV in site (不蒜子统计，用于站点访客数/访问量)
# 访问人数
busuanzi:
  site_uv: false # 是否显示站点总访客数 (UV)
  site_pv: false # 是否显示站点总访问量 (PV)
  page_pv: false # 是否显示文章页面访问量 (PV)

# Time difference between publish date and now (网页运行时间)
# Formal: Month/Day/Year Time or Year/Month/Day Time (格式：月/日/年 时间 或 年/月/日 时间)
runtimeshow:
  enable: true # 是否显示网站运行时间
  publish_date: 6/1/2025 00:00:00 # 网站上线时间，格式必须正确以便计算时长

# Console - Newest Comments (控制台 - 最新评论)
newest_comments:
  enable: true # 是否在控制台 (F12 打开开发者工具) 显示最新评论
  sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  limit: 6 # 显示最新评论数量
  storage: 10 # 数据存储到 localStorage 的时间 (单位：分钟)，避免频繁请求 API

# Bottom right button (右下角按钮)
# --------------------------------------

# Conversion between Traditional and Simplified Chinese (简繁转换)
translate:
  enable: true # 是否启用简繁转换按钮
  # The text of a button (按钮上显示的文本)
  default: 繁 # 默认按钮文本 (在简体模式下显示)
  # Right-click menu default text (右键菜单默认文本)
  rightMenuMsgDefault: "轉為繁體" # 右键菜单转换为繁体的文本
  # the language of website (1 - Traditional Chinese/ 2 - Simplified Chinese）(网站语言，1 - 繁体中文 / 2 - 简体中文)
  defaultEncoding: 2 # 网站默认编码 (简体)
  # Time delay (延迟时间)
  translateDelay: 0 # 翻译延迟时间 (毫秒)
  # The text of the button when the language is Simplified Chinese (在简体模式下按钮显示的文本)
  msgToTraditionalChinese: "繁" # 按钮文本：转换为繁体
  # The text of the button when the language is Traditional Chinese (在繁体模式下按钮显示的文本)
  msgToSimplifiedChinese: "简" # 按钮文本：转换为简体
  # Right-click the menu to traditional Chinese (右键菜单转换为繁体)
  rightMenuMsgToTraditionalChinese: "转为繁体" # 右键菜单文本：转为繁体
  # Right-click menu to simplified Chinese (右键菜单转换为简体)
  rightMenuMsgToSimplifiedChinese: "转为简体" # 右键菜单文本：转为简体

# Read Mode (阅读模式)
readmode: true # 是否启用阅读模式按钮

# 中控台 (Center Console)
centerConsole:
  enable: true # 是否启用中控台按钮 (通常包含快捷功能或信息)
  card_tags: # 中控台中的标签卡片
    enable: true # 是否启用中控台标签卡片
    limit: 40 # 显示标签数量 (0 为所有)
    color: false # 标签是否使用随机颜色
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
    highlightTags: # 高亮显示的标签列表 (使用示例)
      # - Hexo
      # - 前端
  card_archives: # 中控台中的归档卡片
    enable: true # 是否启用中控台归档卡片
    type: monthly # 归档类型，可选 yearly (按年) or monthly (按月)
    format: MMMM YYYY # 归档格式，例如 MMMM YYYY (七月 2023)
    order: -1 # 排序顺序，1 为升序，-1 为降序
    limit: 8 # 显示归档数量 (0 为所有)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)

# dark mode (暗色模式)
darkmode:
  enable: true # 是否启用暗色模式功能
  # Toggle Button to switch dark/light mode (切换亮色/暗色模式的按钮)
  button: true # 是否显示暗色模式切换按钮
  # Switch dark/light mode automatically (自动切换 dark mode 和 light mode)
  # autoChangeMode: 1  Following System Settings, if the system doesn't support dark mode, it will switch dark mode between 6 pm to 6 am (跟随系统设置，如果系统不支持暗色模式，则在晚上 6 点到早上 6 点之间切换到暗色模式)
  # autoChangeMode: 2  Switch dark mode between 6 pm to 6 am (在晚上 6 点到早上 6 点之间切换到暗色模式)
  # autoChangeMode: false (关闭自动切换)
  autoChangeMode: 1 # 自动切换模式
  start: # 自动切换到暗色模式的开始时间 (小时，例如 18)
  end: # 自动切换到暗色模式的结束时间 (小时，例如 6)

# Don't modify the following settings unless you know how they work (非必要请不要修改)
# Choose: readmode,translate,darkmode,hideAside,toc,chat,comment (可选的右下角按钮项)
# Don't repeat 不要重复
rightside_item_order: # 右下角按钮顺序和显示控制
  enable: false # 是否启用自定义右下角按钮顺序
  hide: # readmode,translate,darkmode,hideAside # 要隐藏的按钮列表
  show: # toc,chat,comment # 要显示的按钮列表 (如果启用自定义顺序，只列出的会显示)

# Lightbox (图片大图查看模式)
# --------------------------------------
# You can only choose one, or neither (只能选择一个 或者 两个都不选)

# medium-zoom (Medium Zoom 灯箱效果)
# https://github.com/francoischalifour/medium-zoom
medium_zoom: false # 是否启用 medium-zoom

# fancybox (Fancybox 3 灯箱效果)
# http://fancyapps.com/fancybox/3/
fancybox: true # 是否启用 fancybox

# Tag Plugins settings (标签外挂设置)
# --------------------------------------

# mermaid (Mermaid 图形渲染)
# see https://github.com/mermaid-js/mermaid
mermaid:
  enable: false # 是否启用 Mermaid 图形渲染
  # built-in themes: default/forest/dark/neutral (内置主题)
  theme: # 主题
    light: default # Light mode 主题
    dark: dark # Dark mode 主题

# Note (Bootstrap Callout / 提示框)
note:
  # Note tag style values: (提示框样式值)
  #  - simple    bs-callout old alert style. Default. (简洁风格)
  #  - modern    bs-callout new (v2-v3) alert style. (现代风格)
  #  - flat      flat callout style with background, like on Mozilla or StackOverflow. (扁平风格带背景)
  #  - disabled  disable all CSS styles import of note tag. (禁用所有样式导入)
  style: flat # 提示框样式
  icons: true # 是否显示图标
  border_radius: 3 # 边框圆角半径
  # Offset lighter of background in % for modern and flat styles (modern: -12 | 12; flat: -18 | 6). (现代和扁平风格背景颜色偏移百分比)
  # Offset also applied to label tag variables. This option can work with disabled note tag. (偏移也应用于标签变量。此选项在禁用 note 标签时也有效)
  light_bg_offset: 0 # 背景亮度偏移

icons:
  ali_iconfont_js: # 阿里图标 symbol 引用链接，主题会进行加载 symbol 引用
  fontawesome: false # 是否启用 fontawesome6 图标库
  fontawesome_animation_css: # fontawesome_animation 如果有就会加载，示例值：https://npm.elemecdn.com/hexo-butterfly-tag-plugins-plus@1.0.17/lib/assets/font-awesome-animation.min.css (fontawesome 动画 CSS 链接)

# other (其他设置)
# --------------------------------------

# Pjax (页面无刷新加载)
# It may contain bugs and unstable, give feedback when you find the bugs. (可能包含 bug 不稳定，发现 bug 时请反馈)
# https://github.com/MoOx/pjax
pjax:
  enable: true # 是否启用 Pjax 无刷新加载
  exclude: # 排除使用 Pjax 的页面路径列表 (使用示例)
    # - xxxx
    # - xxxx

# Inject the css and script (aplayer/meting) (注入 CSS 和脚本，如 Aplayer/Meting)
aplayerInject:
  enable: true # 是否启用 Aplayer/Meting 脚本注入
  per_page: true # 是否只在文章 frontmatter 中设置 aplayer: true / meting: true 的页面注入脚本

# Snackbar (Toast Notification 弹窗提示)
# https://github.com/polonel/SnackBar
# position 弹窗位置
# 可选 top-left / top-center / top-right / bottom-left / bottom-center / bottom-right
snackbar:
  enable: true # 是否启用 Snackbar 弹窗提示
  position: top-center # 弹窗位置
  bg_light: "#425AEF" # 亮色模式下弹窗背景颜色
  bg_dark: "#1f1f1f" # 暗色模式下弹窗背景颜色

# https://instant.page/ (链接预加载)
# prefetch (预加载)
instantpage: true # 是否启用 InstantClick/InstantPage 链接预加载

# https://github.com/vinta/pangu.js (盘古计划，中英文之间添加空格)
# Insert a space between Chinese character and English character (中英文之间添加空格)
pangu:
  enable: false # 是否启用盘古计划
  field: site # 应用范围，可选 site (全站) / post (仅文章)

# Lazyload (图片懒加载)
# https://github.com/verlok/vanilla-lazyload
lazyload:
  enable: true # 是否启用图片懒加载
  field: site # 应用范围，可选 site (全站) / post (仅文章)
  placeholder: # 图片加载前的占位符图片url (可选)
  blur: true # 图片加载前是否显示模糊效果
  progressive: true # 是否启用渐进式加载 (先加载模糊低质量图，再加载清晰图)

# PWA (渐进式 Web 应用)
# See https://github.com/JLHwung/hexo-offline
# ---------------
pwa:
  enable: false # 是否启用 PWA
  startup_image_enable: true # 是否启用启动画面
  manifest: /manifest.json # manifest 文件路径
  theme_color: var(--anzhiyu-main) # 主题颜色 (用于浏览器界面元素)
  mask_icon: /img/siteicon/apple-icon-180.png # Mask Icon 路径 (用于 Safari 固定标签页)
  apple_touch_icon: /img/siteicon/apple-icon-180.png # Apple Touch Icon 路径
  bookmark_icon: /img/siteicon/apple-icon-180.png # 书签图标路径
  favicon_32_32: /img/siteicon/32.png # 32x32 favicon
  favicon_16_16: /img/siteicon/16.png # 16x16 favicon

# Open graph meta tags (Open Graph 元标签)
# https://developers.facebook.com/docs/sharing/webmasters/
Open_Graph_meta: true # 是否启用 Open Graph meta 标签 (用于社交分享预览)

# Add the vendor prefixes to ensure compatibility (添加厂商前缀以确保兼容性)
css_prefix: true # 是否自动为 CSS 添加厂商前缀

# 首页顶部相关配置 (Homepage Top Section Settings)
home_top:
  enable: true # 开关，是否启用自定义首页顶部区域
  timemode: date # 日期显示模式，可选 date (创建日期) / updated (更新日期)
  title: 生活明朗 # 顶部区域主标题
  subTitle: 万物可爱。 # 顶部区域副标题
  siteText: anheyu.com # 顶部区域网站文本
  category: # 自定义分类链接列表
    - name: 前端 # 分类名称
      path: /categories/前端开发/ # 分类页面路径
      shadow: var(--anzhiyu-shadow-blue) # 阴影颜色 (使用CSS变量)
      class: blue # CSS 类名 (用于自定义样式)
      icon: anzhiyu-icon-dove # 图标类名
    - name: 大学
      path: /categories/大学生涯/
      shadow: var(--anzhiyu-shadow-red)
      class: red
      icon: anzhiyu-icon-fire
    - name: 生活
      path: /categories/生活日常/
      shadow: var(--anzhiyu-shadow-green)
      class: green
      icon: anzhiyu-icon-book
  default_descr: 再怎么看我也不知道怎么描述它的啦！ # 默认描述文本 (当文章没有 description 时显示)
  swiper: # Swiper 轮播图配置
    enable: false # 是否启用 Swiper 轮播图
    swiper_css: https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css # swiper css 依赖
    swiper_js: https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.js # swiper js 依赖
  banner: # 顶部静态 banner 配置
    tips: 新品主题 # 提示文本
    title: Theme-AnZhiYu # 主标题
    image: https://bu.dusays.com/2023/05/13/645fa3cf90d70.webp # 背景图片 url
    link: https://docs.anheyu.com/ # 点击链接地址

# 朋友圈配置 (友链朋友圈)
friends_vue:
  enable: false # 是否启用友链朋友圈功能
  vue_js: https://npm.elemecdn.com/anzhiyu-theme-static@1.1.1/friends/index.4f887d95.js # 友链朋友圈所需的 Vue JS 文件
  apiurl: # 友链朋友圈后端 API 地址
  top_tips: 使用 友链朋友圈 订阅友链最新文章 # 顶部提示文本
  top_background: # 顶部背景图片

# 深色模式粒子效果 canvas (Universe effect in dark mode)
universe:
  enable: true # 是否在暗色模式下启用宇宙粒子背景效果

# 页面卡片顶部气泡升起效果 (Bubble effect on card tops)
bubble:
  enable: false # 是否启用页面卡片顶部的气泡升起效果

#  控制台打印信息 (Console log messages)
console:
  enable: true # 是否在浏览器控制台打印主题相关信息

# 51a统计配置 (51.la Analytics)
LA:
  enable: false # 是否启用 51.la 统计
  ck: # 统计代码中的 ck 参数
  LingQueMonitorID: # 凌鹊监控 ID (可选)

# 标签卖萌 (Browser tab title change on visibility change)
diytitle:
  enable: true # 是否启用浏览器标签页标题变化效果
  leaveTitle: w(ﾟДﾟ)w 不要走！再看看嘛！ # 离开当前标签页时显示的标题文本
  backTitle: ♪(^∇^*)欢迎肥来！ # 返回当前标签页时显示的标题文本

# 留言弹幕配置 (Comment Barrage Configuration)
comment_barrage_config:
  enable: false # 是否启用评论弹幕功能
  # 同时最多显示弹幕数
  maxBarrage: 1 # 屏幕上同时显示的弹幕数量上限
  # 弹幕显示间隔时间ms
  barrageTime: 4000 # 两条弹幕之间的显示间隔时间 (毫秒)
  # token，在控制台中获取 (获取方式需参考主题或相关插件文档)
  accessToken: "" # 评论弹幕的访问 token
  # 博主邮箱md5值 (用于标识博主弹幕)
  mailMd5: "" # 博主邮箱的 MD5 值

# 左下角音乐配置项 (Bottom Left Music Player Configuration)
# https://github.com/metowolf/MetingJS
nav_music:
  enable: true # 是否启用左下角音乐播放器 (MetingJS)
  console_widescreen_music: false # 在宽屏状态下是否将音乐播放器显示在控制台内，而非左下角 (enable 为 true 时，控制台依然会显示)
  id: 8152976493 # 歌单或歌曲 ID (取决于 server 类型)
  server: netease # 音乐服务提供商，可选 netease (网易云音乐), tencent (QQ 音乐), kugou (酷狗音乐) 等
  volume: 0.7 # 默认音量 (0.0 ~ 1.0)
  all_playlist: https://y.qq.com/n/ryqq/playlist/8802438608 # 全部歌单链接 (点击播放器时跳转的链接)

# 路径为 /music 的音乐页面默认加载的歌单 1. nav_music 2. custom ( /music 页面的默认歌单来源)
music_page_default: nav_music # 可选 nav_music (使用 nav_music 配置的歌单) or custom (自定义歌单)

# 评论匿名邮箱 (Anonymous Email for Comments)
visitorMail:
  enable: true # 是否启用评论匿名邮箱功能 (评论时可选择使用匿名邮箱)
  mail: "" # 匿名邮箱地址 (可选)

# ptool 文章底部工具 (Post Bottom Tools)
ptool:
  enable: true # 是否启用文章底部工具栏
  share_mobile: true # 在移动端是否显示分享按钮
  share_weibo: true # 是否显示微博分享按钮
  share_copyurl: true # 是否显示复制链接按钮
  categories: false # 是否在底部工具栏显示分类信息
  mode: # 运营模式与责任说明 (不配置则不显示)

# 欢迎语配置 (Greeting Box Configuration)
greetingBox:
  enable: false # 开启后必须配置下面的list对应的时间段，不然会出现小白条 (开启后必须配置下面的 list 列表，否则可能显示异常)
  default: 晚上好👋 # 没有匹配到时间段时的默认欢迎语
  list: # 时间段欢迎语列表
    # - greeting: 晚安😴 # 欢迎语文本
    #   startTime: 0 # 开始时间 (小时，24小时制)
    #   endTime: 5 # 结束时间 (小时，24小时制)
    # - greeting: 早上好鸭👋, 祝你一天好心情！
    #   startTime: 6
    #   endTime: 9
    # - greeting: 上午好👋, 状态很好，鼓励一下～
    #   startTime: 10
    #   endTime: 10
    # - greeting: 11点多啦, 在坚持一下就吃饭啦～
    #   startTime: 11
    #   endTime: 11
    # - greeting: 午安👋, 宝贝
    #   startTime: 12
    #   endTime: 14
    # - greeting: 🌈充实的一天辛苦啦！
    #   startTime: 14
    #   endTime: 18
    # - greeting: 19点喽, 奖励一顿丰盛的大餐吧🍔。
    #   startTime: 19
    #   endTime: 19
    # - greeting: 晚上好👋, 在属于自己的时间好好放松😌~
    #   startTime: 20
    #   endTime: 24

# 文章顶部ai摘要 (Post Head AI Description)
post_head_ai_description:
  enable: true # 是否启用文章顶部 AI 摘要功能
  gptName: AnZhiYu # AI 的名称 (显示在摘要前)
  mode: local # 摘要模式，可选值: tianli (调用 Tianli API) / local (本地处理，需要配置 key)
  switchBtn: false # 是否显示切换模式按钮 (在 tianli 和 local 之间切换)
  btnLink: https://afdian.net/item/886a79d4db6711eda42a52540025c377 # 切换模式按钮的链接 (可选)
  randomNum: 3 # 按钮最大的随机次数，也就是一篇文章最大随机出来几种 (切换模式按钮随机显示的文本种类数量)
  basicWordCount: 1000 # 最低获取字符数, 最小1000, 最大1999 (用于生成摘要的文章内容最低字数要求)
  key: xxxx # API Key (当 mode 为 local 或 tianli 时可能需要，具体取决于实现)
  Referer: https://xx.xx/ # Referer 请求头 (可选)

# 快捷键配置 (Shortcut Key Configuration)
shortcutKey:
  enable: false # 是否启用快捷键功能
  delay: 100 # 所有键位延时触发而不是立即触发（包括shift，以解决和浏览器键位冲突问题）(所有快捷键触发的延迟时间，毫秒)
  shiftDelay: 200 # shift 按下延时多久开启 (shift 键按下后，等待多久开始检测其他快捷键组合)

# 无障碍优化（在首页按下「shift + ?」以查看效果）(Accessibility Optimization)
accesskey:
  enable: true # 是否启用无障碍优化功能 (按下 shift + ? 可查看说明)

# 友情链接顶部相关配置 (Friend Link Page Top Section Settings)
linkPageTop:
  enable: true # 是否启用友链页面顶部区域自定义内容
  title: 与数百名博主无限进步 # 友链页面顶部主标题
  # 添加博主友链的评论自定义格式 (在友链页面评论区显示的占位文本，引导评论者填写友链信息)
  addFriendPlaceholder: "昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"

# 缩略图后缀 archive/tag/category 页面单独开启后缀 (Thumbnail Suffix)
pageThumbnailSuffix: "" # 归档、标签、分类页面文章缩略图 URL 的后缀 (可选，用于 CDN 图片处理等)

# 隐私协议弹窗 (Privacy Agreement Popup)
agreementPopup:
  enable: false # 是否启用隐私协议弹窗
  url: /privacy # 隐私协议页面链接

# 右键菜单 (Custom Right Click Menu)
rightClickMenu:
  enable: false # 是否启用自定义右键菜单

# 首页随便逛逛people模式 而非技能点模式，关闭后为技能点模式需要配置creativity.yml (Homepage "People" Mode Background)
peoplecanvas:
  enable: true # 是否在首页启用人物背景模式 (取代技能点模式，需要配合 people.yml 配置)
  img: https://upload-bbs.miyoushe.com/upload/2024/07/27/125766904/ba62475f396df9de3316a08ed9e65d86_5680958632268053399..png # 人物背景图片url

# 动效 (Dynamic Effects)
dynamicEffect:
  postTopWave: true # 文章顶部是否启用波浪效果
  postTopRollZoomInfo: false # 文章顶部区域在滚动时是否缩放信息 (标题、日期等)
  pageCommentsRollZoom: false # 非文章页面 (如独立页) 的评论区域在滚动时是否缩放显示 (目前仅 Twikoo 生效)

# Inject (代码注入)
# Insert the code to head (before '</head>' tag) and the bottom (before '</body>' tag) (插入代码到头部 </head> 之前 和 底部 </body> 之前)
inject:
  head: # 注入到 </head> 标签之前的代码列表
    # 自定义css (示例)
    # - <link rel="stylesheet" href="/css/custom.css" media="defer" onload="this.media='all'"> # 外部 CSS 文件
  bottom: # 注入到 </body> 标签之前的代码列表
    # 自定义js (示例)
    # - <script src="/js/xxx"></script> # 外部 JS 文件

# CDN (CDN Settings)
# Don't modify the following settings unless you know how they work (非必要请不要修改)
# 非必要请不要修改
CDN:
  # The CDN provider of internal scripts (主题内部 JS 文件的 CDN 配置)
  # option: local/elemecdn/jsdelivr/unpkg/cdnjs/onmicrosoft/cbd/anheyu/custom (可选 CDN 提供商)
  # Dev version can only choose. ( dev版的主题只能设置为 local )
  internal_provider: local # 主题内部脚本的 CDN 提供商

  # The CDN provider of third party scripts (第三方 JS 库的 CDN 配置)
  # option: elemecdn/jsdelivr/unpkg/cdnjs/onmicrosoft/cbd/anheyu/custom (可选 CDN 提供商)
  third_party_provider: cbd # 第三方库的 CDN 提供商

  # Add version number to CDN, true or false (是否在 CDN 链接中包含版本号)
  version: true # 是否在 CDN 链接中包含版本号

  # Custom format (自定义 CDN 格式)
  # For example: https://cdn.staticfile.org/${cdnjs_name}/${version}/${min_cdnjs_file} # 示例格式
  custom_format: # https://npm.elemecdn.com/${name}@latest/${file} # 自定义 CDN 格式字符串

  option: # 特定资源的 CDN 地址覆盖 (非必要不要修改)
    # main_css: # 主题主 CSS
    # main: # 主题主 JS
    # utils: # 工具类 JS
    # translate: # 简繁转换 JS
    # random_friends_post_js: # 随机友链文章 JS
    # right_click_menu_js: # 右键菜单 JS
    # comment_barrage_js: # 评论弹幕 JS
    # ai_abstract_js: # AI 摘要 JS
    # people_js: # 人物背景 JS
    # local_search: # 本地搜索 JS
    # algolia_js: # Algolia JS v4
    # algolia_search: # Algolia search helper JS
    # instantsearch: # InstantSearch JS
    # docsearch_js: # Docsearch JS
    # docsearch_css: # Docsearch CSS
    # pjax: # Pjax JS
    # blueimp_md5: # MD5 JS
    # valine: # Valine JS
    # twikoo: # Twikoo JS
    # waline_js: # Waline JS
    # waline_css: # Waline CSS
    # sharejs: # Share.js JS
    # sharejs_css: # Share.js CSS
    # mathjax: # MathJax JS
    # katex: # KaTeX JS
    # katex_copytex: # KaTeX CopyTeX JS
    # mermaid: # Mermaid JS
    # canvas_ribbon: # Canvas Ribbon JS
    # canvas_fluttering_ribbon: # Canvas Fluttering Ribbon JS
    # canvas_nest: # Canvas Nest JS
    # lazyload: # Lazyload JS
    # instantpage: # InstantPage JS
    # typed: # Typed.js JS
    # pangu: # Pangu.js JS
    # fancybox_css: # Fancybox CSS
    # fancybox: # Fancybox JS
    # medium_zoom: # Medium Zoom JS
    # snackbar_css: # Snackbar CSS
    # snackbar: # Snackbar JS
    # activate_power_mode: # Activate Power Mode JS
    # fireworks: # Fireworks JS
    # click_heart: # Click Heart JS
    # ClickShowText: # Click Show Text JS
    # fontawesome: # Font Awesome JS
    # flickr_justified_gallery_js: # Flickr Justified Gallery JS
    # flickr_justified_gallery_css: # Flickr Justified Gallery CSS
    # aplayer_css: # Aplayer CSS
    # aplayer_js: # Aplayer JS
    # meting_js: # Meting JS
    # meting_api: # Meting API 地址
    # prismjs_js: # PrismJS JS
    # prismjs_lineNumber_js: # PrismJS 行号插件 JS
    # prismjs_autoloader: # PrismJS 自动加载器 JS
    # artalk_js: # Artalk JS
    # artalk_css: # Artalk CSS
    # pace_js: # Pace JS
    # pace_default_css: # Pace Default CSS
    # countup_js: # CountUp.js JS
    # gsap_js: # GSAP JS
    # busuanzi: # Busuanzi JS
    # rightmenu: # Custom Right Click Menu JS
    # waterfall: # Waterfall Layout JS (瀑布流)
    # ali_iconfont_css: # 阿里图标 CSS
    # accesskey_js: # Accesskey JS
```



---

### 第一章: 网站身份与核心导航

本章将引导您配置网站的基础身份信息、核心导航菜单以及其他关键入口，确保访问者能够识别您的网站并便捷地浏览内容。正确的配置不仅关乎网站的功能性，更是构建品牌形象的第一步。

---

#### 菜单配置 (`menu`)

主导航菜单是网站最重要的导航元素，通常位于页面顶部，引导用户访问核心页面和内容分类。

```yaml
# 菜单配置
menu:
  # 文章分类菜单项
  文章: # 菜单项名称
    隧道: /archives/ || anzhiyu-icon-box-archive # 子菜单项名称: 链接 || 图标类名
    分类: /categories/ || anzhiyu-icon-shapes
    标签: /tags/ || anzhiyu-icon-tags

  # 友链相关菜单项
  友链:
    友人帐: /link/ || anzhiyu-icon-link
    朋友圈: /fcircle/ || anzhiyu-icon-artstation
    留言板: /comments/ || anzhiyu-icon-envelope

  # 我的相关菜单项
  我的:
    音乐馆: /music/ || anzhiyu-icon-music
    追番页: /bangumis/ || anzhiyu-icon-bilibili
    相册集: /album/ || anzhiyu-icon-images
    小空调: /air-conditioner/ || anzhiyu-icon-fan

  # 关于相关菜单项
  关于:
    关于本人: /about/ || anzhiyu-icon-paper-plane
    闲言碎语: /essay/ || anzhiyu-icon-lightbulb
    随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1 # 使用js函数跳转到随机文章
```

##### 配置指导

-   `menu` 下的每个条目代表一个菜单项。
-   格式为 `菜单名称: 链接 || 图标类名`。
    -   `菜单名称`: 显示在菜单上的文本。
    -   `链接`: 菜单项指向的 URL 路径（相对于网站根目录）。
    -   `图标类名`: 可选，菜单项左侧显示的图标类名，使用 `||` 分隔。请使用主题提供的 `anzhiyu-icon-*` 类名，或直接使用[Font Awesome](https://fontawesome.com/search)的类名


##### 3.标签页配置

1.前往你的 Hexo 博客的根目录

2.在 Hexo 博客根目录 `[blog]`下打开终端，输入

```bash
hexo new page tags
```

你会找到 `source/tags/index.md` 这个文件

修改这个文件： 记得添加 `type: "tags"`

```yaml
---
# 【必需】页面的标题，会显示在浏览器标签页和页面顶部。
title: 标签墙

# 【必需】页面的创建日期。
date: 2025-06-09 18:36:00

# 【必需】页面类型。
# 必须设置为 'tags'，主题才会使用标签云的专属布局来渲染此页面。
type: tags

# 【可选】页面的顶部大图（Banner）。
# 如果不设置，则通常显示默认图片或无图片。
# top_img: /img/banners/tags-banner.jpg

# 【可选】是否在此页面显示评论模块。
# 对于标签页，通常建议关闭评论，设置为 false。
comments: false

# 【可选】标签的排序方式。
# - name: 按标签名称的字母顺序排序。
# - length: 按每个标签下文章数量的多少排序。（常用）
# - random: 随机排序。
orderby: length

# 【可选】标签的排序次序，与 orderby 配合使用。
# - 1 或 asc: 升序 (Ascending)。
# - -1 或 desc: 降序 (Descending)。
# 下面的配置代表“按文章数量从多到少排序”。
order: -1

---

# 标签云页面通常不需要在正文区域填写任何内容。
# 主题会自动获取所有的标签并根据您的排序设置来显示它们。
```



##### 4.分类页配置

1.前往你的 Hexo 博客的根目录

2.在 Hexo 博客根目录 `[blog]`下打开终端，输入

```bash
hexo new page categories
```

3.修改文件如下：

```yaml
---
title: 分类
date: 2025-06-09 17:38:08
aside: false
top_img: false
type: "categories"
---
```









#### 顶部导航栏 (`nav`)

除了主菜单，Anzhiyu 主题还提供顶部的辅助导航栏或其他顶部元素配置。

```yaml
# 导航栏相关配置 (顶部导航栏)
nav:
  enable: true # 是否启用顶部导航栏
  travelling: false # 是否显示前往“开往其他项目”按钮
  menu: # 自定义导航菜单项
    - title: 网页 # 分组标题
      item: # 分组下的链接项
        - name: 博客 # 链接名称
          link: / # 链接地址
          icon: /img/favicon.ico # 链接图标 (图片url)
    - title: 项目
      item:
        - name: 后续项目...
          link: /
          icon: https://image.anheyu.com/favicon.ico
```

##### 个性化方案推荐

-   **简洁明了:** 如果顶部导航栏用于显示网站标题或logo，确保其设计简洁，不干扰用户阅读内容。
-   **滚动行为:** 利用主题提供的滚动隐藏/显示或变色功能，优化移动端或长页面的用户体验。

#### 网站图标 (`favicon`)

网站图标（Favicon）是显示在浏览器标签页、书签栏和搜索结果中的小图标，是网站的重要标识。

```yaml
# Favicon（网站图标）
favicon: /img/user/icon-16x16.ico
```

#### 头像 (`avatar`)

头像通常代表网站作者或博主，在侧边栏或关于页面等位置显示。

```yaml
# Loading Animation (加载动画)
preloader:
  enable: true # 是否启用页面加载动画
  # source (加载动画类型)
  # 1. fullpage-loading (全屏加载动画)
  # 2. pace (进度条)
  # else all (默认显示头像加载动画)
  source: 3 # 加载动画类型
  # pace theme (pace 进度条主题，参见 https://codebyzach.github.io/pace/)
  pace_css_url: # pace 主题 CSS 文件地址
  avatar: /img/user/avatar.png # 自定义加载动画头像图片url (当 source 非 1 或 2 时生效)



# Avatar (头像)
avatar:
  img: /img/user/avatar.webp # 作者头像图片url
  effect: false # 头像是否启用悬停特效
```

##### 个性化方案推荐

-   **清晰照片:** 使用清晰、高质量的个人照片作为头像，增加网站的亲和力，可以采用webp式的木偶动画作为动态头像

#### 社交图标 (`social`)

社交图标通常显示在侧边栏、页脚或关于页面，用于链接到作者的社交媒体主页，有需要的话可以配置

```yaml
social:
  # Github: https://github.com/anzhiyu-c || anzhiyu-icon-github # Github图标示例
  # BiliBili: https://space.bilibili.com/372204786 || anzhiyu-icon-bilibili # Bilibili图标示例
```

##### 个性化方案推荐

-   **精选平台:** 只列出您活跃且希望分享的社交平台。
-   **常用在前:** 将您最常用的社交平台放在列表前面，它们通常会优先显示。

---

### 第二章: 基础配置系列

本章将深入探讨安知鱼 (Anzhiyu) 主题中的基础配置，特别是核心的侧边栏配置（aside）以及影响文章列表和详情页显示的内容元素配置

#### 2.1 代码块配置

```yaml
# Code Blocks (代码相关)
# --------------------------------------

highlight_theme: mac # 代码块主题，可选值：darker / pale night / light / ocean / mac / mac light / false (关闭高亮)
highlight_copy: true # 是否显示代码块复制按钮
highlight_lang: true # 是否显示代码块语言名称
highlight_shrink: false # 是否启用代码块收缩功能。true: 收缩 / false: 展开 | none: 展开并隐藏收缩按钮
highlight_height_limit: 330 # 代码块收缩后的高度限制，单位：px
code_word_wrap: false # 是否启用代码块自动换行
```

在默认情况下，Hexo 在编译的时候不会实现代码自动换行。如果你不希望在代码块的区域里有横向滚动条的话，那么你可以考虑开启这个功能。



#### 2.2 个人卡片

个人卡片hover后的显示描述，该描述请在侧边栏配置中的`aside.card_author.description`中修改，支持html显示。

```yaml
# 作者卡片 状态 (侧边栏作者卡片上的个性签名/状态)
author_status:
  enable: true # 是否启用作者状态显示
  # 可以是任何图片，建议放表情包或者emoji图片，效果都很好
  statusImg: /img/user/status.png # 状态图片url
  skills: # 技能/标签列表 (使用示例)
     - 🤖️ 数码科技爱好者
     - 🔍 分享与热心帮助
     - 🏠 智能家居小能手
     - 🔨 设计开发一条龙
     - 🤝 专修交互与设计
     - 🏃 脚踏实地行动派
     - 🧱 团队小组发动机
     - 💢 壮汉人狠话不多
```

#### 2.3 侧边栏配置

侧边栏是博客网站常用的一种布局元素，用于展示作者信息、公告、最新文章、分类、标签等辅助内容。安知鱼主题提供了丰富的侧边栏模块（即内容卡片）供您选择和配置。

```yaml
aside:
  enable: true # 是否启用侧边栏
  hide: false # 是否默认隐藏侧边栏 (需要配合 button: true 使用)
  button: true # 是否显示侧边栏显示/隐藏按钮
  mobile: true # 是否在移动端显示侧边栏
  position: right # 侧边栏位置，可选 left (左) or right (右)
  display: # 控制对应详情页面是否显示侧边栏
    archive: true # 归档页是否显示侧边栏
    tag: true # 标签页是否显示侧边栏
    category: true # 分类页是否显示侧边栏
  card_author: # 作者信息卡片
    enable: true # 是否启用作者信息卡片
    description: '<div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">这里是我的全栈开发笔记与思考，涵盖从 <b style="color:#fff">前端界面</b> 到 <b style="color:#fff">后端架构</b> 的全链路实践，以及 <b style="color:#fff">数据库设计</b>、<b style="color:#fff">自动化部署</b> 与 <b style="color:#fff">性能优化</b> 的相关探索。</div><div style="line-height:1.38;margin:0.6rem 0;text-align:justify;color:rgba(255, 255, 255, 0.8);">无论你是初学者还是资深同行，相信都能在这里找到有价值的 <b style="color:#fff">实战教程</b> 与 <b style="color:#fff">深度解析</b>。欢迎一起交流，共同进步。</div>'
    name_link: / # 作者姓名的链接
  card_announcement: # 公告卡片
    enable: false # 是否启用公告卡片
    content: "" # 公告内容
  card_weixin: # 微信二维码卡片
    enable: true # 是否启用微信二维码卡片
    face: https://bu.dusays.com/2023/01/13/63c02edf44033.png # 微信前置二维码图片url (如个人微信)
    backFace: https://bu.dusays.com/2023/05/13/645fa415e8694.png # 微信背景二维码图片url (如公众号)
  card_recent_post: # 最新文章卡片
    enable: true # 是否启用最新文章卡片
    limit: 5 # 显示最新文章数量 (设置为 0 显示所有)
    sort: date # 排序方式，可选 date (按创建日期) or updated (按更新日期)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  card_categories: # 分类卡片
    enable: true # 是否启用分类卡片
    limit: 8 # 显示分类数量 (设置为 0 显示所有)
    expand: none # 默认展开子分类，可选 none (不展开) / true (展开所有) / false (不展开)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  card_tags: # 标签卡片 (标签云)
    enable: true # 是否启用标签卡片
    limit: 40 # 显示标签数量 (设置为 0 显示所有)
    color: false # 标签是否使用随机颜色
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
    highlightTags: # 高亮显示的标签列表 (使用示例)
      # - Hexo
      # - 前端
  card_archives: # 归档卡片
    enable: true # 是否启用归档卡片
    type: monthly # 归档类型，可选 yearly (按年) or monthly (按月)
    format: MMMM YYYY # 归档格式，例如 MMMM YYYY (七月 2023)
    order: -1 # 排序顺序，1 为升序，-1 为降序
    limit: 8 # 显示归档数量 (设置为 0 显示所有)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  card_webinfo: # 站点信息卡片
    enable: true # 是否启用站点信息卡片
    post_count: true # 是否显示文章总数
    last_push_date: false # 是否显示站点最后更新日期
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
```

>注意，要配置个人的作者姓名需要在hexo的`_config.yml_`内配置，如下的信息

```yaml
# --- 网站信息 (Site) ---
# 这部分定义了您博客的基础信息，会显示在网站的各个位置。
# -----------------------------------------------------------

# 网站主标题，会显示在浏览器标签页和主题的显眼位置。
title: Prorise
# 网站副标题，通常显示在主标题下方。
subtitle: 'Prorise-blog'
# 网站描述，主要用于SEO，告诉搜索引擎您的网站是关于什么内容的。
# (优化建议) 建议写一句完整的话来描述您的博客。
description: 'Prorise的个人博客，一位超全栈工程师，在这里分享前后端开发、架构设计、运维部署等技术领域的学习笔记与实战经验。'
# 网站关键词，用于SEO，多个关键词用英文逗号隔开。
keywords: 全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享
# 您的名字或昵称。
author: Prorise
# 网站语言。对于中文博客，'zh-CN' 是正确的设置。
language: zh-CN
# 网站时区。建议设置为您所在的时区，以确保文章发布时间的准确性。
# 'Asia/Shanghai' 设置正确，代表中国时区。
timezone: 'Asia/Shanghai'
```

>**注意：**有关于名字的显示需要配置`social`配置项，若没有配置，则不会显示站点的名字！

```yaml
# social settings (社交图标设置)
# formal: # 格式说明
#   name: link || icon # 社交平台名称: 链接 || 图标类名
social:
   Github: https://github.com/Prorise-cool || anzhiyu-icon-github # Github图标示例
   BiliBili: https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0 || anzhiyu-icon-bilibili # Bilibili图标示例

```



#### 2.4 顶部图

如果不要显示顶部图，可直接配置 `disable_top_img: true`

当我们期望使用封面图时，可以对Markdown顶层做上如下的配置

```yaml
---
title: gsap详解
date: 2025-06-09 17:13:15
categories:
  - [前端技术, Web App]
tags:
  - PWA
  - Hexo
  - Service Worker
cover: /img/post/post_1.png
---
```

系统会按以下顺序寻找图片，找到第一个就使用：
1.  文章 `.md` 文件里的 `top_img`
2.  文章 `.md` 文件里的 `cover`
3.  主题 `_config.yml` 文件里的 `default_top_img`

****
| 配置             | 解释                                                         |
| :--------------- | :----------------------------------------------------------- |
| index_img        | 主页的 top_img，示例值: index_img: "background: url(https://img02.anheyu.com/xxx) top / cover no-repeat" |
| default_top_img  | 默认的 top_img，当页面的 top_img 没有配置时，会显示 default_top_img |
| archive_img      | 归档页面的 top_img                                           |
| tag_img          | tag 子页面 的 默认 top_img                                   |
| tag_per_img      | tag 子页面的 top_img，可配置每个 tag 的 top_img              |
| category_img     | category 子页面 的 默认 top_img                              |
| category_per_img | category 子页面的 top_img，可配置每个 category 的 top_img    |

其它页面 （tags/categories/自建页面）和 文章页 的 `top_img` ，请到对应的 md 页面设置 `front-matter` 中的 `top_img`

<span style="font-weight:bold; color:#FF3333;">注意：</span>并不推荐为每个 tag 和每个 category 都配置不同的顶部图，因为配置太多会拖慢生成速度



#### 2.5 文章置顶

【推荐】`hexo-generator-index` 从 2.0.0 开始，已经支持文章置顶功能。你可以直接在文章的 `front-matter` 区域里添加 `sticky: 1` 属性来把这篇文章置顶。数值越大，置顶的优先级越大。





#### 2.6 文章封面

文章的 markdown 文档上,在 `Front-matter` 添加 `cover` ,并填上要显示的图片地址。

如果不配置 `cover`,可以设置显示默认的 cover。

如果不想在首页显示 cover, 可以设置为 `false。`

文章封面的获取顺序 `Front-matter` 的 `cover` > `配置文件的 default_cover` > `false`

```yaml
---
title: gsap详解
date: 2025-06-09 17:13:15
cover: /img/post/post_1.png
---
```

在`_config`配置文件中设置：

```yaml
cover:
  # 是否显示文章封面
  index_enable: true # 是否在首页文章列表中显示封面
  aside_enable: true # 是否在侧边栏显示封面 (如最新文章卡片)
  archives_enable: true # 是否在归档页显示封面
  # 首页文章封面显示的位置
  # left/right/both
  position: left # 首页封面位置，可选 left/right/both (左/右/左右交替)
  # 当没有设置cover时，默认的封面显示
  default_cover: /img/default_cover.png # 默认封面图片url示例
```

当配置多张图片时,会随机选择一张作为 cover.此时写法应为

```yaml
default_cover:
  - https://file.crazywong.com/gh/jerryc127/CDN@latest/cover/default_bg.png
  - https://file.crazywong.com/gh/jerryc127/CDN@latest/cover/default_bg2.png
  - https://file.crazywong.com/gh/jerryc127/CDN@latest/cover/default_bg3.png
```

#### 2.7 文章 meta 显示

这个选项是用来显示文章的相关信息的。

```yaml
post_meta:
  page: # Home Page (主页文章列表的元信息显示设置)
    date_type: created # 日期类型，可选 created (创建日期) or updated (更新日期) or both (都显示)
    date_format: relative # 日期格式，可选 date (完整日期) / relative (相对日期，如“3天前”) / simple (简单日期，如 MM-DD)
    categories: true # 是否显示分类
    tags: true # 是否显示标签
    label: true # 是否显示元信息前面的描述性文字 (如“发布于”、“分类于”)
  post: # 文章详情页的元信息显示设置
    date_type: both # 日期类型，可选 created (创建日期) or updated (更新日期) or both (都显示)
    date_format: relative # 日期格式，可选 date (完整日期) / relative (相对日期)
    categories: true # 是否显示分类
    tags: true # 是否显示标签
    label: true # 是否显示元信息前面的描述性文字
    unread: true # 是否启用文章未读功能 (显示阅读进度条)
```



#### 2.8 文章版权

为你的博客文章展示文章版权和许可协议。

```yaml
post_copyright:
  enable: true # 是否启用文章版权信息
  decode: false # 是否解码博主姓名 (此处通常用于加密)
  author_href: # 博主名称链接 (留空则默认为站点首页)
  location: 广东 # 文章发布地点
  license: CC BY-NC-SA 4.0 # 版权许可类型
  license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/ # 版权许可链接
  avatarSinks: true # 悬停时作者头像是否下沉
  copyright_author_img_back: /img/user/avatar.webp # 版权信息区域作者头像背景图片
  copyright_author_img_front: /img/user/avatar.webp # 版权信息区域作者头像前景图片
  copyright_author_link: / # 版权信息区域作者名称的链接
```

由于 `Hexo 4.1` 开始，默认对网址进行解码，以至于如果是中文网址，会被解码，可设置 `decode: true` 来显示中文网址。

如果有文章（例如：转载文章）不需要显示版权，可以在文章 `Front-matter` 单独设置

```yaml
copyright: false
```

支持对单独文章设置版权信息，可以在文章 `Front-matter` 单独设置

```yaml
# 【可选】文章版权模块的详细设置。
# 您可以在此覆盖全局的版权信息，为特定文章（如转载）设置不同的版权。
copyright: true # 是否显示版权模块
copyright_author: "特邀作者 张三" # 自定义文章作者
copyright_author_href: "https://example.com/zhangsan" # 自定义作者链接
copyright_url: "https://example.com/original-post" # 自定义文章源链接
copyright_info: "本文为特邀作者原创，转载请联系作者获得授权。" # 自定义版权声明文字
```



#### 2.9 文章打赏

在你每篇文章的结尾，可以添加打赏按钮。相关二维码可以自行配置。

对于没有提供二维码的，可配置一张软件的 icon 图片，然后在 link 上添加相应的打赏链接。用户点击图片就会跳转到链接去。

link 可以不写，会默认为图片的链接。coinAudio 为投币的音频。

```yaml
# Sponsor/reward (赞赏/打赏)
reward:
  enable: true # 是否启用赞赏功能
  QR_code: # 赞赏二维码列表
    - img: https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png # 二维码图片url
      link: # 二维码链接 (可选)
      text: 微信 # 二维码描述文本
    - img: https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png
      link:
      text: 支付宝
```



#### 2.10 TOC

在文章页，会有一个目录，用于显示 TOC。修改 `主题配置文件`

```yaml
# toc (目录)
toc:
  post: true # 是否在文章页显示目录
  page: false # 是否在普通页面显示目录
  number: true # 目录中是否显示标题编号
  expand: false # 是否默认展开所有目录项
  style_simple: false # 文章页目录是否使用简洁样式
```

**为特定的文章配置**

在你的文章 md 文件的头部，加入 toc_number 和 toc，并配置 true 或者 false 即可。

主题会优先判断文章 Markdown 的 Front-matter 是否有配置，如有，则以 Front-matter 的配置为准。否则，以主题配置文件中的配置为准

```yaml
# 【可选】TOC (Table of Contents) 目录的相关设置。
# 用于覆盖主题的全局默认配置。
toc: true # 是否显示目录
toc_number: false # 目录是否显示编号
toc_style_simple: true # 是否使用简洁样式的目录
```



#### 2.11 相关文章

当文章封面设置为 `false` 时，或者没有获取到封面配置，相关文章背景将会显示主题色、相关文章推荐的原理是根据文章 tags 的比重来推荐

```yaml
# Related Articles (相关文章)
related_post:
  enable: true # 是否启用相关文章功能
  limit: 6 # 显示相关文章的数量
  date_type: created # 相关文章的日期类型，可选 created (创建日期) or updated (更新日期)
```



#### 2.12 文章过期提醒

可设置是否显示文章过期提醒，以更新时间为基准。

```yaml
# Displays outdated notice for a post (文章过期提醒)
noticeOutdate:
  enable: true # 是否启用文章过期提醒
  style: flat # 样式，可选 simple/flat
  limit_day: 3 # 文章发布或更新超过多少天后显示提醒
  position: top # 提醒显示位置，可选 top/bottom (顶部/底部)
  message_prev: 距离上次更新已经过了 # 提醒信息前缀文本
  message_next: 天，文章内容可能已经过时。 # 提醒信息后缀文本
```



![](https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/6Qwm/1268X288/PixPin-2025-06-09-22-52-11.png)

#### 2.13 文章编辑按钮

在文章标题旁边显示一个编辑按钮，点击会跳转到对应的链接去。

```yaml
# Post edit (文章编辑链接)
# Easily browse and edit blog source code online. (方便在线浏览和编辑博客源代码)
post_edit: # 目前仅可选择一个平台在线编辑
  enable: false # 是否启用文章编辑链接
  # github: https://github.com/user-name/repo-name/edit/branch-name/subdirectory-name/ # Github 编辑链接格式
  # For example: https://github.com/jerryc127/butterfly.js.org/edit/main/source/ # Github 示例
  github: false # Github 编辑链接前缀

  # yuque: https://www.yuque.com/user-name/repo-name/ # 语雀编辑链接格式
  # 示例: https://www.yuque.com/yuque/yuque/
  # 你需要在语雀文章 Front Matter 添加参数 id 并确保其唯一性（例如 “id: yuque”, “id: 01”）
  yuque: false # 语雀编辑链接前缀
```



#### 2.14 文章分页按钮

当文章封面设置为 `false` 时，或者没有获取到封面配置，分页背景将会显示主题色。

可设置分页的逻辑，也可以关闭分页显示

```yaml
# post_pagination (文章分页导航)
# value: 1 || 2 || 3 || 4 || false
# 1: 下一篇文章链接到旧文章
# 2: 下一篇文章链接到新文章
# 3: 只有下一篇，并且只在文章滚动到评论区时显示下一篇文章(旧文章)
# 4: 只有下一篇，并且只在文章滚动到评论区时显示下一篇文章(旧文章)，显示图片cover
# false: disable pagination (禁用文章分页导航)
post_pagination: 2 # 文章分页导航样式
```





### 第三章: 文章与页面深度配置

本章将深入探讨 Anzhiyu 主题中 页面的深度配置以及额外配置

#### 3.1 Footer 设置

```yaml
# Footer Settings (页脚设置)
# --------------------------------------
footer:
  # 站点拥有者信息
  owner:
    enable: true # ✅ 已启用
    since: 2025 # [请替换为您的站点起始年份]

  # 自定义页脚文本
  custom_text: 这是我的个人博客，分享技术与生活点滴。

  # 网站运行时间
  runtime:
    enable: true # ✅ 已启用
    launch_time: 06/10/2025 00:00:00 # [请替换为您的网站实际上线时间]
    # 上班状态图片
    work_img: # [请替换为您的“上班”状态图片链接,若有需要]
    work_description: 努力搬砖中，为了诗和远方~
    # 下班状态图片
    offduty_img: # [请替换为您的“下班”状态图片链接,若有需要]
    offduty_description: 下班了就该开开心心的玩耍，嘿嘿~

  # 徽标部分配置
  bdageitem:
    enable: true # ✅ 已启用
    list:
      - link: https://hexo.io/
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg
        message: 博客框架为Hexo7.0
      - link: https://www.dogecloud.com/
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg
        message: 本站使用多吉云为静态资源提供CDN加速
      - link: https://github.com/
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg
        message: 本站项目由Github托管
      - link: http://creativecommons.org/licenses/by-nc-sa/4.0/
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg
        message: 本站采用CC BY-NC-SA 4.0协议

  # 社交链接栏 (在页脚显示社交图标)
  socialBar:
    enable: true # ✅ 已启用
    centerImg: /img/user/smile.png # [请替换为您的中心图片链接，例如头像]
    # 左侧社交链接列表
    left:
      - title: email
        link: mailto:<EMAIL> # [请替换为您的邮箱地址]
        icon: anzhiyu-icon-envelope
      - title: 微博
        link: https://weibo.com/u/**********?tabtype=home # [请替换为您的微博链接]
        icon: anzhiyu-icon-weibo
      - title: RSS
        link: /atom.xml # [您的RSS地址，通常无需修改]
        icon: anzhiyu-icon-rss
    # 右侧社交链接列表
    right:
      - title: Github
        link: https://github.com/Prorise-cool # [请替换为您的Github链接]
        icon: anzhiyu-icon-github
      - title: Bilibili
        link: https://space.bilibili.com/361040115?spm_id_from=333.788.0.0 # [请替换为您的B站链接]
        icon: anzhiyu-icon-bilibili
      - title: CC
        link: /copyright # [您的版权页面链接]
        icon: anzhiyu-icon-copyright-line

  # 页脚自定义链接列表 (多列显示)
  list:
    enable: true # ✅ 已启用
    randomFriends: 3
    project:
      - title: 服务
        links:
          - title: 51la统计
            link: https://v6.51.la/
          - title: 十年之约
            link: https://www.foreverblog.cn/
      - title: 主题
        links:
          - title: 文档
            link: /docs/ # [链接到您的文档页面]
          - title: 源码
            link: https://github.com/anzhiyu-c/hexo-theme-anzhiyu
      - title: 导航
        links:
          - title: 即刻短文
            link: /essay/ # [链接到您的即刻短文页面]
          - title: 留言板
            link: /comments/ # [链接到您的留言板页面]
      - title: 协议
        links:
          - title: 隐私协议
            link: /privacy/ # [链接到您的隐私协议页面]
          - title: 版权协议
            link: /copyright/ # [链接到您的版权协议页面]

  # 页脚底部横栏
  footerBar:
    enable: true # ✅ 已启用
    authorLink: /about/ # [作者名称的链接，通常是“关于”页面]
    # CC 许可信息
    cc:
      enable: true # ✅ 已启用
      link: /copyright # [CC许可页面链接]
    # 底部横栏自定义链接列表
    linkList:
      - link: https://github.com/Prorise-cool?tab=repositories # [请替换为您的博客仓库链接]
        text: 网站源码
      - link: https://beian.miit.gov.cn/ # [请替换为您的ICP备案链接]
        text: 湘ICP备-xxxxxxx号 # [请替换为您的备案号]
    # 底部横栏副标题
    subTitle:
      enable: true # ✅ 已启用
      effect: true # 启用打字效果
      startDelay: 300
      typeSpeed: 150
      backSpeed: 50
      loop: true
      source: 1 # 调用一言网
      # 自定义副标题文本列表
      sub:
        - 生活明朗&#44; 万物可爱&#44; 人间值得&#44; 未来可期.
        - 愿你眼里的星星，永远亮晶晶。
        - 愿我们每个人都能被世界温柔以待。
```



#### 3.2 访问人数

访问 busuanzi 的[官方网站](http://busuanzi.ibruce.info/)查看更多的介绍

```yaml
# busuanzi count for PV / UV in site (不蒜子统计，用于站点访客数/访问量)
# 访问人数
busuanzi:
  site_uv: true # 是否显示站点总访客数 (UV)
  site_pv: true # 是否显示站点总访问量 (PV)
  page_pv: true # 是否显示文章页面访问量 (PV)
```

如果需要修改 busuanzi 的 CDN 链接，可通过 主题配置文件 的 CDN 中的 option 进行修改

```yaml
CDN:
  option:
  	busuanzi: xxxxxxxxx
```

![](https://img02.anheyu.com/adminuploads/1/2023/04/20/6441312192cba.png)

![](https://img02.anheyu.com/adminuploads/1/2023/04/20/6441312d5e8d0.webp!blogimg)



#### 3.3 右下角按钮

##### 简体繁体互换

```yaml
# 右下角按钮
# --------------------------------------

# 简繁转换
translate:
  enable: true # 是否启用简繁转换按钮
  # 按钮上显示的文本
  default: 繁 # 默认按钮文本 (在简体模式下显示)
  # 右键菜单默认文本
  rightMenuMsgDefault: "轉為繁體" # 右键菜单转换为繁体的文本
  # 网站语言，1 - 繁体中文 / 2 - 简体中文
  defaultEncoding: 2 # 网站默认编码 (简体)
  # 延迟时间
  translateDelay: 0 # 翻译延迟时间 (毫秒)
  # 在简体模式下按钮显示的文本
  msgToTraditionalChinese: "繁" # 按钮文本：转换为繁体
  # 在繁体模式下按钮显示的文本
  msgToSimplifiedChinese: "简" # 按钮文本：转换为简体
  # 右键菜单转换为繁体
  rightMenuMsgToTraditionalChinese: "转为繁体" # 右键菜单文本：转为繁体
  # 右键菜单转换为简体
  rightMenuMsgToSimplifiedChinese: "转为简体" # 右键菜单文本：转为简体
```

##### 阅读模式

阅读模式下会去掉除文章外的内容，避免干扰阅读。

只会出现在文章页面，右下角会有阅读模式按钮。

```yaml
# Read Mode (阅读模式)
readmode: true # 是否启用阅读模式按钮
```

##### 夜间模式

右下角会有夜间模式按钮

```yaml
# dark mode (暗色模式)
darkmode:
  enable: true # 是否启用暗色模式功能
  # 切换亮色/暗色模式的按钮
  button: true # 是否显示暗色模式切换按钮
  # 自动切换 dark mode 和 light mode
  # 跟随系统设置，如果系统不支持暗色模式，则在晚上 6 点到早上 6 点之间切换到暗色模式
  # autoChangeMode: 1  
  # 在晚上 6 点到早上 6 点之间切换到暗色模式
  # autoChangeMode: 2  
  # 关闭自动切换
  # autoChangeMode: false 
  autoChangeMode: 1 # 自动切换模式
  start: # 自动切换到暗色模式的开始时间 (小时，例如 18)
  end: # 自动切换到暗色模式的结束时间 (小时，例如 6)
```





#### 3.4 页面加载动画

当进入网页时，因为加载速度的问题，可能会导致 top_img 图片出现断层显示，或者网页加载不全而出现等待时间，开启preloader后，会显示加载动画，等页面加载完，加载动画会消失。

主题支持 pace.js 的加载动画，具体可查看 [pace.js](https://codebyzach.github.io/pace/)

```yaml

# Loading Animation (加载动画)
preloader:
  enable: true # 是否启用页面加载动画
  # source (加载动画类型)
  # 1. fullpage-loading (全屏加载动画)
  # 2. pace (进度条)
  # else all (默认显示头像加载动画)
  source: 2 # 加载动画类型
  # pace theme (pace 进度条主题，参见 https://codebyzach.github.io/pace/)
  pace_css_url: # pace 主题 CSS 文件地址
  avatar: /img/user/avatar.png # 自定义加载动画头像图片url (当 source 非 1 或 2 时生效)
```



#### 3.5 Inject（重点）

---
您可以把 `inject` 特性想象成一个**“官方指定的外挂接口”**。

它的核心作用是：**允许您在不修改主题核心模板文件的前提下，安全、干净地向网站的所有页面中“注入”您自己的代码**（通常是 `<link>` 标签来引入CSS，或 `<script>` 标签来引入JS）。

* **`inject.head`**: 将代码注入到每个HTML页面的 `</head>` 标签之前。最适合放自定义CSS、第三方字体、网站验证的 `meta` 标签等。
* **`inject.bottom`**: 将代码注入到每个HTML页面的 `</body>` 标签之前。最适合放需要最后加载的JS脚本，比如网站统计、特效JS等。

```yaml
inject:
  head:
  	- <link rel="stylesheet" href="/self.css">
  bottom:
  	- <script src="xxxx"></script>
```

**使用它的最大好处是**：当您的主题更新时，您不必担心自己修改过的模板文件被覆盖，因为您的所有自定义代码都集中在 `_config.yml` 这个配置文件里，升级主题时更加轻松，在后面我们可能会用到这个配置项，我们先简单了解一下

---

### 第四章: 插件与第三方服务集成

本章将详细指导您如何在安知鱼 (Anzhiyu) Hexo 主题中集成各种常用的第三方服务和插件，包括搜索、评论、网站统计、内容分享以及在线聊天功能。通过合理的配置，您可以极大地增强博客的互动性、可发现性和用户体验。所有相关配置都位于您的主题配置文件 `_config.anzhiyu.yml` 中。

#### 搜索系统 

为您的博客添加搜索功能是提升用户体验的关键。访问者可以通过搜索快速找到他们感兴趣的内容。Anzhiyu 主题支持多种搜索方案。
用最直接的方式来对比这三者。

| 特性 | 本地搜索 (Local Search) | Algolia (标准版) | DocSearch |
| :--- | :--- | :--- | :--- |
| **工作地点** | 在访客的浏览器里 | 在云端服务器上 | 在云端服务器上 |
| **性能体验** | 文章多会卡顿，功能基础 | 速度极快，功能强大、智能 | 速度极快，功能强大、智能 |
| **设置难度** | 简单，装个插件就行 | 复杂，需注册和配置API | 中等，需要提交申请并等待审核 |
| **费用** | 完全免费 | 有免费额度，超出后付费 | 对符合条件的网站完全免费 |
| **最适合谁** | 个人博客，中小型网站 | 对搜索要求高的大型、商业网站 | 开源项目文档，技术教程博客 |

---


* **想省事、刚起步** → 用 **本地搜索**。
* **追求极致体验、不差钱或流量很大** → 用 **Algolia**。
* **是公开的技术/开源文档网站** → 申请 **DocSearch** (如果能通过，这是最佳选择)。


```yaml
# search (搜索)
# see https://blog.anheyu.com/posts/c27d.html#搜索系统
# --------------------------------------

# Algolia search (Algolia 搜索)
algolia_search:
  enable: false # 是否启用 Algolia 搜索
  hits:
    per_page: 6 # 每页显示搜索结果数
  tags: # 按标签过滤搜索结果 (使用示例)
    # - 前端
    # - Hexo

# Docsearch (Docsearch 搜索)
# Apply and Option Docs: see https://docsearch.algolia.com/
# Crawler Admin Console: see https://crawler.algolia.com/
# Settings: https://www.algolia.com/
docsearch:
  enable: false # 是否启用 Docsearch 搜索
  appId: # Algolia 应用 ID (参阅邮件获取)
  apiKey: # Algolia API Key (参阅邮件获取)
  indexName: # Algolia 索引名称 (参阅邮件获取)
  option: # Docsearch 其他配置项

# Local search (本地搜索)
local_search:
  enable: false # 是否启用本地搜索
  preload: true # 是否预加载搜索索引
  CDN: # 本地搜索所需的js文件CDN地址 (可选)
```

##### Hexo 配置 Algolia 搜索完整流程

我们以配置Algolia 为例子，他是最通用且最强大的搜索插件

这个流程主要分为四个阶段：

1. **准备 Algolia 账号**：获取必要的“钥匙”（API Keys）。
2. **配置 Hexo 插件**：在您的本地博客项目中安装并设置“同步工具”。
3. **上传文章数据**：将您的博客内容“同步”到 Algolia 的云端。
4. **配置 Hexo 主题**：让您网站前端的搜索框“连接”到 Algolia。

[1] 注册 Algolia 账号

前往 [Algolia 官网](https://www.algolia.com/) 并注册一个账号。它提供一个永久免费的 `Build` 套餐，对于绝大多数个人博客来说完全够用。

点击官网Product / Serach，创建一个新索引

![](https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250610/etkR/1575X709/557227fa-6439-4a63-a519-e78b4b3675f5.png)

跳过所有选项，获取API Keys

- **Application ID**: 您的应用唯一ID。

- **Search-Only API Key**: **只读搜索密钥**。这个是**公开**的，会用在您网站的前端，只能用来搜索，不能修改数据。
- **Write  API Key**: **管理员密钥**。这个是**私密**的，**绝对不能泄露**！它拥有最高权限，我们用它来向 Algolia 上传和更新文章数据。



[2] 在本地 Hexo 项目中安装和配置插件

```bash
npm install hexo-algoliasearch --save
```

打开您博客**根目录**下的 `_config.yml` 文件，在文件的**最底部**添加以下配置：

```yaml
# --- Algolia Search (Final Configuration for AnZhiYu Theme) ---
algolia:
  appId: ""
  # 前端搜索用的“只读搜索Key”
  apiKey: "" # <--- 这里放 Search API Key
  # 运行 hexo algolia 命令用的“管理员Key”
  adminApiKey: "" # <--- 这里放 Write API Key

  indexName: "prorise_blog"
  chunkSize: 5000

  # fields 字段必须存在，并且至少包含一些基本项
  fields:
    - content:strip:truncate,0,500
    - excerpt:strip
    - permalink
    - tags
    - title
```

**再次警告**：这里的 `apiKey` 使用的是 Write API Key**，这个配置文件不应该**上传到公开的 GitHub 仓库，否则会泄露您的密钥。

[3] 上传索引（将文章同步到 Algolia）

```bash
hexo algolia
```

这个命令会启动 `hexo-algoliasearch` 插件，它会读取您所有的文章，然后使用您在第二阶段配置的 WriteKey 将它们推送到 Algolia 云端的指定 Index 中。

[4] 配置 主题文件 `_config.anzhiyu.yml`

```yaml
# Algolia search (Algolia 搜索)
algolia_search:
  enable: true # 确保这里是 true 来启用 Algolia 搜索

  # --- 关键API信息 ---
  # (请将下面三项替换为您自己的信息)
  appId: "你的 Application ID"      # 粘贴您的 Application ID
  apiKey: "你的 Search API Key"    # 注意！这里必须是只读的 Search Key
  indexName: "你创建的 Index 名称"   # 例如 'prorise_blog'

  # --- 搜索设置 ---
  hits:
    per_page: 6 # 每页显示搜索结果数，您可以根据喜好修改

  # --- 按标签过滤 (可选功能) ---
  # 如果您想让用户可以筛选特定标签下的搜索结果，可以在这里配置
  # 初期可以保持注释或留空
  tags:
     # - 前端
     # - Hexo
```

>注意：我们需要配置**博客根目录 `_config.yml` 文件中 `url` 字段**的默认值，否则Algolia的默认跳转连接是hexo默认定义的url

```yaml
# --- 网址 (URL) ---
# 这部分配置与您网站的链接结构（URL）密切相关，非常重要。
# -----------------------------------------------------------
# 【重要】请务必修改为您的网站最终的访问网址！
# 例如，如果您使用 GitHub Pages，它可能是 'https://yourname.github.io'。
# 这个配置会影响网站所有资源的绝对路径，如果错误，可能导致CSS、JS、图片加载失败。
url: http://localhost:4000 # 暂时使用测试地址
# 文章的永久链接格式。
# :year, :month, :day, :i_month, :i_day, :hour, :minute, :second, :title, :name, :post_title, :id, :category
# 示例:
#   :year/:month/:day/:title/  (默认值，例如 2025/06/08/hello-world/)
#   :title.html               (例如 hello-world.html，非常简洁)
#   :category/:title/          (例如 tech/hello-world/)
# 推荐使用 hexo-abbrlink 插件生成短链接，对SEO友好且不会因修改标题而改变： permalink: posts/:abbrlink.html
permalink: :year/:month/:day/:title/
# 永久链接中各部分的默认值。
permalink_defaults:
# URL 美化选项。
pretty_urls:
  # 是否移除永久链接末尾的 'index.html'。通常保持默认。
  trailing_index: true 
  # 是否移除永久链接末尾的 '.html'。通常保持默认。
  trailing_html: true 
```

[5] 日常使用

1. **最终验证**
   - 在终端中运行 `hexo clean && hexo s` 重启您的本地服务。
   - 打开 `localhost:4000`，点击网站的搜索按钮。
   - 此时弹出的应该就是 Algolia 的搜索框（通常会有 Algolia 的小图标）。随便输入您文章中的一个词，结果应该会瞬间出现。
2. **日常更新流程**
   - 当您写了一篇新文章或修改了旧文章后，您的工作流会比以前多一步：
     1. 正常写文章。
     2. `hexo g -d` (生成并部署您的网站)
     3. `hexo algolia` (**运行此命令来更新 Algolia 的搜索索引**)

恭喜您！至此，您的博客已经拥有了世界一流的搜索引擎。



---
#### 评论系统

评论系统是博客互动性的重要组成部分，Anzhiyu 主题集成了多种流行的评论服务。

**三种评论系统对比**

| **特性** | **Waline** | **Twikoo** | **Valine (旧版)** |
| :--- | :--- | :--- | :--- |
| **管理后台** | ✅ **有** (功能强大的独立管理界面) | ✅ **有** (功能强大的独立管理界面) | ❌ **无** (核心痛点) |
| **后端依赖** | Vercel / Netlify / Railway 等云平台 | 腾讯云开发 (Tencent CloudBase) | LeanCloud |
| **功能丰富度**| 非常高 (评论/阅读数/通知/表情等) | 非常高 (功能与Waline基本一致) | 基础 (只有基础评论和阅读数) |
| **项目维护状态**| **活跃** (持续更新) | **活跃** (持续更新) | **基本停滞** (不推荐) |
| **部署难度** | 中等 (需部署后端，但有“一键部署”) | 中等 (需配置云开发环境) | 简单 (但平台限制增多) |
| **推荐指数** | ⭐⭐⭐⭐⭐ (强烈推荐) | ⭐⭐⭐⭐ (优秀备选) | ⭐ (不推荐) |

```yaml
# Comments System (评论系统)
# --------------------------------------
comments:
  use: # Twikoo/Waline # 在此配置要使用的评论系统，例如：use: Waline
  text: true
  lazyload: false
  count: true
  card_post_count: true
```

**关于双评论功能**

* **含义**：这是主题提供的高级功能，允许在一个页面上配置并提供两个不同的评论系统（例如 Waline 和 Twikoo）供访客切换。
* **用途**：主要用于照顾不同地区访客的访问速度（例如 Vercel 在海外快，腾讯云在国内快），或满足不同用户的登录偏好。
* **配置方法**：在 `use` 字段中填写两个系统的名字即可，第一个为默认显示。例如：`use: Waline, Twikoo`
* **建议**：初期无需配置，先专注于一个系统即可。

##### 部署Waline
----
###### 核心流程概览
部署 Waline 需要一个独立的后端服务来存储和管理评论。我们将分为四个阶段完成：
1.  **准备数据库**：创建一个云数据库用于存储评论信息（以LeanCloud为例）。
2.  **部署后端服务**：在 Vercel 平台上一键部署 Waline 后端。
3.  **绑定自定义域名 (推荐)**：解决网络访问问题。
4.  **配置Hexo主题**：让您的博客与后端服务连接起来。

###### 创建云数据库 (以 LeanCloud 为例)
1.  登录或注册 LeanCloud 国际版并进入控制台: `https://console.leancloud.app/login?from=%2Fapps`
2.  点击左上角“创建应用”，选择免费的开发版并为应用命名。

![word/media/image2.png](https://i-blog.csdnimg.cn/img_convert/5506af6f0d2b6b29ec51d66eb58da322.png)

3.进入应用后，在左侧菜单选择 **设置 > 应用 Key**。您会看到 `APP ID`, `APP Key` 和 `Master Key`。**请将这三个值复制并保存好**，下一步会立刻用到。

![word/media/image3.png](https://i-blog.csdnimg.cn/img_convert/1817b06a648d79870a36c3aa76b93371.png)



###### 第一阶段：在 Vercel 上一键部署 Waline 后端
1.  **前提准备**：一个 GitHub 账号和一个 Vercel 账号。

2.  **点击官方部署模板链接**：
    ➡️ [部署链接](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwalinejs%2Fwaline%2Ftree%2Fmain%2Fexample)
    
3.  **在 Vercel 中创建项目**：点击链接后，Vercel 会引导您创建一个新的 Git 仓库。给仓库起一个您喜欢的名字（例如 `my-waline-server`），然后点击 **Create**。
  
    ![img](https://i-blog.csdnimg.cn/img_convert/90f100c1a3fb6b7fc067ed882f5578d2.png)
    
4.  **配置环境变量 (Environment Variables)**：在接下来的页面中，根据**第一步**中从 LeanCloud 获取到的信息，依次添加以下三个环境变量：

    * `LEAN_ID` (值为您的 APP ID)
    * `LEAN_KEY` (值为您的 APP Key)
    * `LEAN_MASTER_KEY` (值为您的 Master Key)

    ![img](https://i-blog.csdnimg.cn/img_convert/20b24c8a86ebac8143ca58b9159f6bcc.png)

5.  **部署**：配置好环境变量后，点击 **Deploy** 按钮，等待部署成功，部署成功后他会需求重启，照做即可

###### 第二阶段：绑定自定义域名 (强烈推荐)

您可能会发现，刚刚部署好的 `.vercel.app` 地址在某些网络环境下无法直接访问（需要“梯子”）。这是因为该域名受到了网络干扰。**最佳解决方案是为您的 Waline 服务绑定一个您自己的域名**。

1.  **在 Vercel 获取配置信息**：
    * 进入您刚部署好的 Vercel 项目，点击 **Settings -> Domains**。
    * 输入您想用的域名（例如 `waline.prorise666.site` 或根域名 `prorise666.site（不建议）`），点击 **Add**。
    * Vercel 会提示您需要添加的DNS记录，通常是 `A` 记录（对应一个IP地址）或 `CNAME` 记录（对应另一个地址）。
    
2.  **在您的域名服务商（如Spaceship）处添加记录**：
    * 登录您的域名服务商后台，找到DNS管理页面。
    * 按照 Vercel 的提示，添加对应的 `A` 或 `CNAME` 记录。
        * **类型(Type)**: `A` 或 `CNAME`
        * **主机(Host/Name)**: 如果是根域名，填 `@`；如果是子域名（如`waline`），则填 `waline`。
        * **值(Value)**: 粘贴从 Vercel 获取的 IP 地址或地址。
    * 保存记录。

    ![](https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/********/5z2Z/1794X889/63c77a1a-6412-4c3b-97ff-f88c91df73b0.png)
    
3.  **等待并验证**：
    * DNS 生效需要几分钟到几小时。您可以稍后回到 Vercel 的 Domains 页面，点击 **Refresh Status**。当红色提示消失，变为绿色成功状态时，即代表绑定成功。
    * **绑定成功后的这个自定义域名，才是我们最终要用的 `serverURL`。**

###### 第三阶段：首次配置 Waline 后端

1.  **访问管理界面**：在浏览器中打开 `https://<您的自定义域名>/ui/register`。
2.  **注册管理员**：设置您的用户名和密码并完成注册。这个账号用于管理所有评论。

###### 第四阶段：配置您的 Hexo 主题

1.  **打开主题配置文件** (`themes/anzhiyu/_config.yml`)。
2.  **填写 `comments` 和 `waline` 配置**：
    ```yaml
    comments:
      # 最多可配置两个评论系统，第一个将作为默认显示
      # Choose: Valine/Waline/Twikoo/Artalk (可选择的评论系统)
      use: Waline # Twikoo/Waline # 配置使用的评论系统名称 (例如 ['Twikoo', 'Waline'])
      text: true # 是否在评论按钮旁边显示评论系统的名称
      # 如果启用懒加载，评论计数可能不准确
      lazyload: false # 是否启用评论系统懒加载
      count: true # 是否在文章顶部图片中显示评论计数
      card_post_count: true # 是否在首页文章列表卡片中显示评论计数
    
    
    # waline - A simple comment system with backend support fork from Valine (Waline 评论系统)
    # https://waline.js.org/
    waline:
      serverURL: https://waline.prorise666.site # Waline 后端服务地址 URL
      bg: # Waline 评论框背景图片
      pageview: true # 是否启用文章阅读量统计 (基于 Waline)
      meta_css: true # 是否引入 waline-meta.css, 以便显示 meta 图标
      imageUploader: true # 是否启用图片上传功能。配置为 > 换行后可自定义图片上传逻辑，示例: https://waline.js.org/cookbook/customize/upload-image.html#案例
      # 以下为可选配置，后续若有新增/修改配置参数可在此自行添加/修改
      option: # 其他可选配置项
    
    ```
3.  **本地预览与最终验证**：
    * 在终端运行 `hexo clean && hexo s`。
    * 打开文章页，检查评论框是否正常显示并可以成功提交评论。



##### 部署 Twikoo 系统。

这篇文章主要介绍一下免费的方案 —— 使用 Vercel 部署 Twikoo 系统。
如果您希望部署 Twikoo 系统以使用评论弹幕等高级功能，但不想再注册新的数据库服务，而是希望利用现有的 LeanCloud 账户。

这里需要澄清一个关键点：Twikoo 官方提供给 Vercel 的“一键部署”方案是**为 MongoDB 数据库定制的**，它通过读取一个名为 `MONGODB_URI` 的环境变量来连接数据库。它无法直接识别 LeanCloud 的 `APP_ID` 和 `APP_KEY`。

因此，最稳定、最推荐的免费部署方案仍然是 **Vercel + MongoDB Atlas**。MongoDB Atlas 同样提供永久免费的数据库额度，足够个人博客使用。注册流程也非常快速。

此流程将指导您使用 Vercel (提供免费后端服务) 和 MongoDB Atlas (提供免费数据库) 来部署 Twikoo 评论系统。这是目前最稳定且官方推荐的免费部署方案。

###### 核心流程概览

1.  **准备数据库 (MongoDB Atlas)**：创建一个免费的云数据库用于存储 Twikoo 评论。
2.  **部署后端服务 (Vercel)**：在 Vercel 平台上一键部署 Twikoo 后端服务。
3.  **配置 Hexo 主题**：让您的博客与 Twikoo 后端服务连接起来。

###### 第一阶段：准备数据库 (MongoDB Atlas)

尽管您已有 LeanCloud 账户，但 Twikoo 的 Vercel 部署方案需要使用 MongoDB。请放心，注册和创建免费数据库的过程非常快捷。

1.  **注册并创建免费数据库**

      * 访问 [MongoDB Atlas 官网](https://www.mongodb.com/cloud/atlas/register) 并注册一个新账号。
      * 登录后，系统会引导您创建项目。然后点击 “**Build a Database**” 按钮。
      * 选择免费的 **M0 FREE** 套餐，选择一个云服务商和离您较近的地区（例如 AWS 的 `ap-northeast-1` 东京或 `ap-east-1` 香港），然后点击 “**Create**”。

      ![img](https://pic.yemengstar.com/picgo/mongodb-02.png)

2.  **创建数据库用户**

      * 在数据库的安全设置（Security）页面，找到 “**Database Access**” 选项卡。
      * 点击 “**Add New Database User**”，设置一个**用户名**和**密码**。请务必记下这两个值，稍后会用到。
      * 点击 “**Create Database User**” 完成创建。

      ![img](https://pic.yemengstar.com/picgo/mongodb-03.png)

3.  **配置网络访问权限**

      * 在安全设置（Security）页面，找到 “**Network Access**” 选项卡。
      * 点击 “**Add IP Address**”。
      * 在弹出的窗口中，点击 “**ALLOW ACCESS FROM ANYWHERE**” (允许任何 IP 地址访问)。这会自动填入 `0.0.0.0/0`。
      * 点击 “**Confirm**”。由于 Vercel 的服务器 IP 是动态的，这样设置可以确保 Twikoo 服务能正常连接到数据库。

      ![img](https://pic.yemengstar.com/picgo/mongodb-06.png)

4.  **获取数据库连接字符串 (Connection String)**

      * 回到 “**Database**” 概览页面，找到您创建的集群，点击 “**Connect**” 按钮。
      * 在连接方式中，选择 “**Drivers**”。

      ![img](https://pic.yemengstar.com/picgo/mongodb-08.png)

      * 页面上会显示一个连接字符串，格式如下：
        `mongodb+srv://<username>:<password>@cluster0.xxxx.mongodb.net/?retryWrites=true&w=majority`
      * **请将 `<username>` 和 `<password>` 替换为您在第2步中创建的真实用户名和密码**。

      ![img](https://pic.yemengstar.com/picgo/mongodb-09.png)

      * **将这个完整的字符串复制并妥善保存**，这是连接您数据库的唯一凭证，下一步将立即用到，连接字符串包含了连接到 MongoDB 数据库的所有信息，**一旦泄露会导致评论被任何人添加、修改、删除，并有可能获取你的 SMTP、图床 token 等信息。请妥善记录这一字符串**，之后需要填入到 Twikoo 的部署平台里。

###### 第二阶段：在 Vercel 上一键部署 Twikoo 后端

1.  **点击官方部署模板链接**：
    ➡️ [Twikoo 部署链接](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Ftwikoojs%2Ftwikoo%2Ftree%2Fmain%2Fsrc%2Fserver%2Fvercel-min&teamSlug=prorise-cools-projects)

2.  **在 Vercel 中创建项目**

      * 点击链接后，Vercel 会引导您基于此模板创建一个新的 Git 仓库。给仓库起一个您喜欢的名字 (例如 `my-twikoo-service`)，然后点击 **Create**。

3.  **配置环境变量 (关键步骤)**

      * 在接下来的 "Configure Project" 页面中，展开 **Environment Variables** (环境变量) 部分。
      * 添加一个名为 `MONGODB_URI` 的环境变量。
      * 它的值 (Value) 就是您在 **第一阶段第4步** 中获取并保存好的**数据库连接字符串**。

4.  **部署与验证**

      * 配置好环境变量后，点击 **Deploy** 按钮，并耐心等待部署完成。
      * 部署成功后，进入您项目的 Vercel 控制台，点击顶部菜单的 **Deployments**。
      * 找到刚刚完成的部署项，点击右侧的“三个点”菜单，选择 **Redeploy** (重新部署)，并确认。这一步是为了确保环境变量能被正确应用。
      * 等待重新部署完成后，回到项目概览 (Overview) 页面。在 **Domains** 下方会有一个 `.vercel.app` 结尾的链接。
      * 点击这个链接，如果页面显示 “**Twikoo 云函数运行正常**” 的提示，则代表您的 Twikoo 后端已成功部署！
      * **这个完整的 Vercel 链接 (例如 `https://twikoo-service.vercel.app`) 就是我们最终需要的 `envId` (环境ID)。**

###### 第三阶段：配置您的 Hexo 主题

1.  **打开主题配置文件** (`themes/anzhiyu/_config.yml`)。

2.  **启用 Twikoo 评论系统**：

      * 找到 `comments` 配置项，在 `use` 字段中填入 `Twikoo`。如果您想保留 Waline 作为备用选项，可以配置为 `use: Twikoo, Waline`，将默认想用的系统放在第一位。

    ```yaml
comments:
      # 在此配置要使用的评论系统，第一个为默认
      use: Twikoo
      text: true
      lazyload: false
      count: true
      card_post_count: true
    ```
    
3.  **填写 Twikoo 配置**：

      * 找到 `twikoo` 配置项，将 `envId` 填写为您在 **第二阶段第4步** 获得的 Vercel 域名。

    <!-- end list -->

    ```yaml
    # Twikoo - A simple, safe, free comment system.
    # https://twikoo.js.org/
    twikoo:
      envId: 'https://my-twikoo-service.vercel.app' # 必填，您的 Vercel 部署地址
      region: # Vercel 部署无需填写
      # visitor: true # 开启访客统计，如需使用请设置为 true
      # ... 其他Twikoo配置项
    ```

4.  **本地预览与最终验证**：

      * 在终端运行 `hexo clean && hexo s`。
      * 打开任意一篇文章页，您应该能看到 Twikoo 评论框已正常加载。
      * 点击评论框右下角的小齿轮图标，即可进入管理后台，首次进入需要设置管理员密码。
      * 现在，您已经可以根据之前的笔记，继续配置评论弹幕等功能了。






##### 配置最新评论

最新评论只会在刷新时才会去读取，并不会实时变化。由于 API 有访问次数限制，为了避免调用太多，主题默认存取期限为 10 分钟。也就是説，调用后资料会存在 localStorage 里，10 分钟内刷新网站只会去 localStorage 读取资料。 10 分钟期限一过，刷新页面时才会去调取 API 读取新的数据。（通过配置 `storage`，可自行配置缓存时间）

```yaml
# Console - Newest Comments (控制台 - 最新评论)
newest_comments:
  enable: true # 是否在控制台 (F12 打开开发者工具) 显示最新评论
  sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  limit: 6 # 显示最新评论数量
  storage: 10 # 数据存储到 localStorage 的时间 (单位：分钟)，避免频繁请求 API
```









#### 分享功能 (Sharing)

在文章页面添加分享按钮，可以让读者方便地将您的内容分享到社交媒体或通过其他渠道传播，增加文章的曝光度。Anzhiyu 主题支持集成不同的分享服务。


**两种分享服务对比总结**

| 特性 | Share.js | AddToAny |
| :--- | :--- | :--- |
| **核心特点** | 一个轻量化的JS分享插件 | 一个“全能型”的分享服务 |
| **主要面向地区** | **中国大陆**常用平台 (QQ/微博等) | **全球/国际**平台 (Facebook/Twitter等) |
| **按钮自定义** | **灵活**，可自由增删列表中的平台 | 相对固定，主要通过“+”号扩展 |
| **优点** | 轻量，对国内平台支持非常直接 | 支持的服务极多，覆盖面非常广 |
| **缺点** | 国际平台支持相对较少 | 国内常用平台通常被折叠，不够方便 |

---
###### **选择建议**

选择哪个，完全取决于您**博客的目标读者**在哪里。

**鉴于您是中文博客，目标读者很可能主要在国内，因此我强烈推荐您使用【Share.js】。**

它的默认列表就是为国内用户量身定做的，包含了微信、QQ、微博等最常用的平台，非常方便。而 AddToAny 更适合面向国际读者的英文博客。

---
###### **Share.js 配置流程**

您不需要去任何网站注册，只需要在主题配置文件中进行设置即可。

1.  **打开您的主题配置文件** (例如 `themes/anzhiyu/_config.yml`)。

2.  **找到 `share:` 部分**，并按照下面的示例进行修改。

    ```yaml
    # Sharing Services (分享功能)
    # --------------------------------------
    share:
      # 这是分享功能的总开关
      enable: true

      # --- Share.js 分享服务 ---
      # 我们选择启用这个
      sharejs:
        enable: true
        
        # 在这里配置您想要显示的分享平台列表
        # 您可以根据自己的喜好，从下面的列表中删除您不想要的平台，或者调整它们的顺序。
        platforms:
          - wechat  # 微信
          - qq      # QQ好友
          - qzone   # QQ空间
          - weibo   # 新浪微博
          - douban  # 豆瓣
          - email   # 通过邮件分享
          # --- 以下为国际平台，如果您的读者有海外用户，可以取消注释来启用 ---
          # - facebook
          # - twitter
          # - linkedin
          # - google

      # --- AddToAny 分享服务 ---
      # 确保这个是禁用的，避免冲突
      addtoany:
        enable: false
    ```

3.  **保存文件**。

4.  **重启本地服务**：在终端运行 `hexo clean && hexo s`。

完成以上步骤后，在您的每篇文章下方，应该就能看到一排分享按钮了，例如“分享到微信”、“分享到QQ”等。

```yaml
### Sharing
share:
  enable: true # 是否启用分享功能模块
##  # 如果主题支持多种分享服务，可能在此处选择或在下方单独配置

##  # Share.js 分享服务
  sharejs:
    enable: true # 是否启用 Share.js
    platforms: # 选择要显示的分享平台列表
      - qzone
      - qq
      - wechat # 微信分享通常依赖扫码或复制链接，前端展示形式可能特殊
      - weibo
      - douban
      - facebook
      - twitter
      - google # Google+ 已停止服务，此项可能不再有效或指向 Google Search
      - linkedin
      - email # 通过邮件分享
##      # 添加或删除列表中的平台名称

##  # AddToAny 分享服务
  addtoany:
    enable: false # 是否启用 AddToAny
##    # AddToAny 通常只需要启用，主题会嵌入其通用代码
##    # custom_code: '' # 如果需要高级定制，可以在此填写 AddToAny 提供的自定义代码片段
```



#### 聊天服务

在线聊天或客服功能可以方便访问者与您进行实时交流或留言。Anzhiyu 主题支持集成一些第三方聊天服务。

###### **各类在线聊天服务快速对比总结**

| 服务名称 | 核心特点 | 主要面向的用户 | 推荐指数 |
| :--- | :--- | :--- | :--- |
| **Tidio** | 界面现代，功能全面，免费套餐慷慨 | 全球/通用，追求良好UI和体验的用户 | ⭐⭐⭐⭐⭐ |
| **Crisp** | 设计简洁优雅，体验流畅 | 全球/通用，Tidio的优秀替代品 | ⭐⭐⭐⭐ |
| **DaoVoice (客服宝)** | **针对国内市场优化**，访问速度快 | **访客主要来自中国大陆**的博主 | ⭐⭐⭐⭐ |
| **Chatra** | 功能扎实，老牌聊天服务商 | 全球/通用 | ⭐⭐⭐ |

###### **选择建议**

选择哪个，主要取决于您的访客群体和个人偏好。

  * **如果您的访客遍布全球，或者您特别喜欢现代化的界面和强大的免费功能**：
* **首选推荐：Tidio**。它的免费版足以满足个人博客的一切需求，并且后台和App都非常好用。


**在开始之前，有一个极其重要的前提：**

您**不能同时启用**多个聊天服务，一次只能选择**一个**来激活。

下面的流程会教您如何配置每一个，但请记住，当您启用一个时（例如 `tidio.enable: true`），必须确保其他所有服务的 `enable` 都设置为 `false`，以免造成页面冲突和加载错误，我们还是以一个最强大的为例子，但他可能会与其他的部署流程更复杂一些，由于anzhiyu主题已经两年未更新，但Tidio的引入方式进行了大改，所以我们需要手动集成

-----

###### **部署流程：Tidio**

1.  **服务简介**：功能全面，界面现代化，免费套餐非常慷慨，是个人博客的绝佳选择。

2.  **部署流程**：
      * **第一步**：前往 **[Tidio 官网](https://www.tidio.com/)** 注册一个免费账号。
      * **第二步**：根据设置向导，输入您的网站地址、配置聊天窗口的颜色和语言（支持中文）等。
      * **第三步**：完成向导后，进入 Tidio 管理后台。点击左下角的**设置图标 (⚙️ Settings)**。
      * **第四步**：在菜单中选择，并如图中所示操作，将整个代码片段复制下来：

      ![](https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/********/j9Ne/1671X889/QQ********-201130.png)

3.  **主题配置**：

```yaml
# tidio (Tidio 聊天服务)
# https://www.tidio.com/
tidio:
  # 启用 Tidio
  enable: true
  # 在这里输入官网的Script中的其中以tk开头的内容，即为public_key
  public_key: 'tk.....'
```

-----

###### **部署流程：Crisp**

1.  **服务简介**：设计简洁优雅，体验流畅，是 Tidio 的一个强力竞争者，同样提供不错的免费套餐。
2.  **部署流程**：
      * **第一步**：前往 **[Crisp 官网](https://crisp.chat/)** 注册账号。
      * **第二步**：登录后，进入后台。点击左侧菜单的**设置图标 (⚙️)**，然后选择 **Website Settings**。
      * **第三步**：在网站列表中，点击您网站旁边的 **Settings** 按钮。
      * **第四步**：选择 **Setup Instructions** 或类似选项，您会看到一段JS代码。
      * **第五步**：从代码 `CRISP_WEBSITE_ID = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx";` 中，**复制**引号里的那串 **Website ID**。
3.  **主题配置**：
    ```yaml
    crisp:
      enable: true # <-- 启用 Crisp
      website_id: '您的 Crisp Website ID' # <--- 粘贴到这里

    # --- 确保其他聊天服务为 false ---
    # ...
    ```

-----

###### **部署流程：DaoVoice (客服宝)**

1.  **服务简介**：专注于国内市场的在线客服工具，如果您的访客主要来自中国大陆，它的连接速度和稳定性可能更有优势。
2.  **部署流程**：
      * **第一步**：前往 **[DaoVoice 官网](https://www.google.com/search?q=http://daovoice.io/)** 并注册账号。
      * **第二步**：登录后台后，根据系统的引导提示安装应用或添加网站。
      * **第三步**：在安装步骤或应用设置中，您会找到一串专属的 **App ID**。请复制它。
3.  **主题配置**：
    ```yaml
    daovoice:
      enable: true # <-- 启用 DaoVoice
      app_id: '您的 DaoVoice App ID' # <--- 粘贴到这里

    # --- 确保其他聊天服务为 false ---
    # ...
    ```

-----

###### **部署流程：Chatra**

1.  **服务简介**：一款功能扎实、成熟可靠的聊天服务。
2.  **部署流程**：
      * **第一步**：前往 **[Chatra 官网](https://chatra.io/)** 注册账号。
      * **第二步**：登录后，在左侧菜单进入 **Settings \> Chat Widget**。
      * **第三步**：页面上会有一段JS安装代码。
      * **第四步**：从代码中找到您的 **Public key** 或 **ID** (通常是一串10位左右的字母数字组合)。
3.  **主题配置**：
    ```yaml
    chatra:
      enable: true # <-- 启用 Chatra
      id: '您的 Chatra Public key' # <--- 粘贴到这里

    # --- 确保其他聊天服务为 false ---
    # ...
    ```

---

#### **AI 摘要功能**

该功能可以在文章顶部生成一段精炼的摘要，帮助读者快速了解文章核心内容。它提供两种模式：`tianli` (调用云端AI自动生成) 和 `local` (在文章中手动撰写)。

**两种模式对比**

| 模式 | 工作方式 | 优点 | 缺点 |
| :--- | :--- | :--- | :--- |
| **`tianli`** | 调用云端AI服务，自动分析文章并生成摘要 | **方便快捷，摘要质量高，无需动脑** | **付费服务**，有请求字数限制 |
| **`local`** | 在文章的 Front-matter 中手动撰写多条摘要 | **完全免费，内容可控，可精准表达** | **需要为每篇文章手动编写**，较为繁琐 |

下面，我将为您分别提供这两种模式的详细集成流程。

-----

###### **方案一：集成 Tianli GPT (自动摘要模式)**

此方案适合追求效率，希望自动为所有文章生成高质量摘要的用户，但是需要一点小钱

```bash
npm install hexo-plugin-postchat --save
```

编辑根目录下的`_config.yaml`，在`plugins`中添加`hexo-plugin-postchat`：

```yaml
plugins:
  - hexo-plugin-postchat
```



**1. 第一步：获取 Key 与绑定域名**

  * 访问 [前端管理面板](https://summary.tianli0.top/)，将您的 Key 与您博客的域名（即`Referer`）进行绑定，以防被盗用，并需要购买token以及新建项目，获取key

**2. 第二步：配置主题文件**

  * 打开您**主题的配置文件** (`themes/anzhiyu/_config.yml`)。
  * 找到 `post_head_ai_description:` 部分，并修改如下：
    ```yaml
    post_head_ai_description:
      enable: true
      mode: tianli # <--- 必须设为 tianli
      key: '您购买的Key' # <--- 粘贴您的Key
      Referer: 'https://您的博客域名.com/' # <--- 填写您绑定的博客地址
      gptName: AnZhiYu # AI的名字，可自定义
      switchBtn: false # 是否显示切换模式的按钮，推荐false
      randomNum: 3
      basicWordCount: 1000
    ```

**3. 第三步：在文章中启用**

  * （此步骤将在文末统一说明）

-----

###### **方案二：使用 Local 模式 (手动摘要模式)**

此方案完全免费，适合希望完全掌控摘要内容，或者只是想为少数几篇文章添加摘要的用户。

**1. 第一步：配置主题文件**

  * 打开您**主题的配置文件** (`themes/anzhiyu/_config.yml`)。
  * 找到 `post_head_ai_description:` 部分，并修改如下：
    ```yaml
    post_head_ai_description:
      enable: true
      mode: local # <--- 必须设为 local
      gptName: Prorise # AI的名字，可自定义
      # 以下三项在local模式下无需填写
      key:
      Referer:
      btnLink:
    ```

**2. 第二步：在文章中撰写摘要**

  * 打开您想添加摘要的**文章 .md 文件**。

  * 在文章最顶部的 **Front-matter** 区域，添加一个 `ai:` 字段，并以列表的形式提供一或多条您自己撰写的摘要。

  * 主题会从您提供的列表中**随机选择一条**进行显示。

    **示例：**

    ```yaml
    ---
    title: AnZhiYu主题快速开始
    date: 2025-06-12 01:00:00
    ai:
      - 本教程介绍了如何在博客中安装安知鱼主题，并提供了安装、应用主题等详细步骤。
      - 如果你是Hexo新手，本文将一步步带你从零开始配置安知鱼主题。
      - 本文的核心是帮助用户快速上手AnZhiYu主题，避免常见的配置错误。
    ---

    文章正文从这里开始...
    ```

-----

###### **最后一步：在文章中启用AI摘要**

无论您选择哪种模式，默认情况下摘要功能并不会对所有文章生效。您需要在使用它的文章的 **Front-matter** 中明确开启它。

  * **对于 `tianli` 模式**:

      * 在文章的 Front-matter 中添加 `ai: true`。

    <!-- end list -->

    ```yaml
    ---
    title: 我的文章标题
    date: 2025-06-12 02:00:00
    ai: true # <--- 像这样开启 Tianli 自动摘要
    ---
    ```

  * **对于 `local` 模式**:

      * 如上文所述，直接在 Front-matter 中提供 `ai:` 的摘要列表即可，无需额外的 `ai: true`。

配置完成后，`hexo clean && hexo s` 重启服务，找到您开启了此功能的文章，即可在文章顶部看到效果。





TODO:谷歌广告、站长验证、友链朋友圈、图片主色调API



#### 网站统计

anzhiyu提供了许多网站统计的集成，我们都可以通过快速搭建服务去配置他们，以下的服务我们其实不着急集成，由于网页还未正式部署上线

**首先，这是五种常用统计服务的快速对比总结表：**

| 服务名称 | 核心功能 | 一句话建议 | 设置难度 |
| :--- | :--- | :--- | :--- |
| **不蒜子 (Busuanzi)** | 简单的访客/浏览量计数器 | **推荐所有新博主**，提供即时反馈，增强动力。 | ⭐ (极简单) |
| **Google Analytics**| 专业、全面的网站数据分析 | **强烈推荐**，了解访客来源和内容表现的核心工具。 | ⭐⭐⭐ (中等) |
| **Microsoft Clarity**| 用户行为可视化（热力图、录屏） | **推荐进阶使用**，用于优化网站布局和用户体验。 | ⭐⭐⭐ (中等) |
| **百度统计 (Baidu)**| 面向中国大陆市场的专业分析 | **仅当访客主要来自中国大陆时**有必要使用。 | ⭐⭐⭐ (中等) |
| **CNZZ (友盟)** | 同百度统计，面向国内市场 | 基本同上，与百度统计二选一即可。 | ⭐⭐⭐ (中等) |

**我的最终建议组合：**

  * **基础必备**：**不蒜子** + **Google Analytics**
  * **进阶优化**：**不蒜子** + **Google Analytics** + **Microsoft Clarity**

下面，我们来逐一介绍每个服务的部署流程。

-----

##### 不蒜子 (Busuanzi) 统计

###### 它是什么？

一个极其**简单、轻量**的计数器，只显示网站总访客数（UV）和总浏览量（PV），给您最直观的反馈。

###### 我应该用它吗？

**强烈推荐**。设置极其简单，能立刻看到成果，可以和任何其他分析工具并存。

###### 部署流程

1.  **无需注册**：这是它最大的优点。
2.  **打开主题开关**：在您主题的配置文件中，找到 `busuanzi:` 部分，将 `enable` 设置为 `true` 即可。
  
    ```yaml
    busuanzi:
      enable: true
    ```
3.  **完成**：主题会自动处理脚本的引入和数据显示。

-----

##### Google Analytics (谷歌分析)

###### 它是什么？

Google出品的、全球最专业、最强大的**免费网站数据分析工具**，是行业标准。能详细分析访客来源、热门文章、停留时间等一切您想知道的数据。

###### 我应该用它吗？

**强烈推荐**。如果您想认真运营博客，这是必不可少的核心工具。

###### 部署流程

1.  **准备账号**：您需要一个 Google 账号。
2.  **访问官网**：前往 [Google Analytics 官网]([Google Analytics](https://analytics.google.com/analytics/web/provision/?hl=zh-cn#/provision))。
3.  **创建账号与媒体资源**：
      * 首次使用会引导您创建新“账号 (Account)”。
      * 接着创建“媒体资源 (Property)”，输入博客名，选择地区（Japan）和货币（JPY）。
4.  **创建数据流 (Data Stream)**：
      * 在设置向导中，选择平台 **“网站” (Web)**。
      * 输入您博客的**最终线上网址**（例如 `https://yourname.github.io`）。
      
      ![](https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/********/cwqZ/1531X935/fdbe9779-82ca-4dfa-9a7d-917b726d2ef3.png)
5.  **获取衡量ID (Measurement ID)**：

      * 创建成功后，在数据流详情页的右上角，找到并**复制**一个格式为 **`G-XXXXXXXXXX`** 的“衡量ID”。
6.  **配置您的主题文件**：
      * 打开主题配置文件，找到 `google_analytics:` 部分。
      * 将 `enable` 设为 `true`，并将您的ID粘贴到 `tracking_id`。
      ```yaml
      google_analytics:
        enable: true
        tracking_id: 'G-XXXXXXXXXX' # <--- 粘贴到这里
      ```
7.  **完成**：保存并重新部署博客。数据通常需要24-48小时才会开始在GA后台显示。

-----

##### Microsoft Clarity

###### 它是什么？

微软出品的**免费用户行为分析工具**，是GA的完美补充。它不侧重于数据，而是侧重于**可视化**，核心功能是**热力图**（看用户常点哪里）和**会话录制**（像看电影一样看用户的操作录像）。

###### 我应该用它吗？

**推荐**。当您想知道“为什么访客没有点击这个按钮”或想优化文章排版时，它非常有用。

###### 部署流程

1.  **准备账号**：一个微软账号（或用Google账号登录）。
2.  **访问官网**：前往 [Microsoft Clarity 官网](https://clarity.microsoft.com/)。
3.  **创建新项目**：点击“New project”，输入您博客的名字和网址。
4.  **获取项目ID (Project ID)**：创建后，在安装步骤中选择“手动安装 (Install manually)”，您会看到一段代码，从中**复制**出您的 Project ID（一长串字母数字）。
5.  **配置您的主题文件**：
    ```yaml
    microsoft_clarity:
      enable: true
      project_id: '您的Clarity Project ID' # <--- 粘贴到这里
    ```

-----

##### 百度统计 / CNZZ 统计

###### 它们是什么？

分别由百度和阿里巴巴（友盟+）推出的网站统计分析工具，可以看作是中国大陆地区的“Google Analytics”。

###### 我应该用它吗？

**仅当您的核心读者群体来自中国大陆时，才有必要配置**。如果您想优化在百度搜索引擎中的表现，百度统计会更有帮助。

###### 部署流程

流程与Google Analytics类似：前往各自官网注册 -\> 添加网站 -\> 在后台生成的跟踪代码中找到 **Site ID** -\> 填入主题配置中对应的 `tracking_id` 或 `site_id` 字段并启用。

网站统计帮助您了解网站的访问量、访客来源、受访页面等信息，是优化网站内容和推广策略的重要依据。Anzhiyu 主题支持集成多种统计服务。

```yaml
### Analytics Services (这些配置可能分散在 _config.anzhiyu.yml 中不同的区域)

### 百度统计
baidu_analytics:
  enable: true # 是否启用百度统计
  tracking_id: '您的百度统计 Site ID' # 在百度统计获取的 Site ID

### Google Analytics
google_analytics:
  enable: false # 是否启用 Google Analytics
  tracking_id: '您的 Google Analytics Tracking ID' # GA4 (G-XXXXXXX) 或 Universal Analytics (UA-XXXXX-Y) 的 ID

### CNZZ 统计 (友盟)
cnzz_analytics:
  enable: false # 是否启用 CNZZ 统计
  site_id: '您的 CNZZ Site ID' # 在 CNZZ 获取的 Site ID

### Cloudflare Web Analytics
### Cloudflare Analytics 通常在 Cloudflare 后台开启，如果未使用 CF Proxy，可能需要手动嵌入代码
cloudflare_analytics:
  enable: false # 主题是否嵌入 Cloudflare Analytics 代码
##  # token: '您的 Cloudflare Web Analytics Token' # 如果主题支持，填写您的 token

### Microsoft Clarity
microsoft_clarity:
  enable: false # 是否启用 Microsoft Clarity
  project_id: '您的 Clarity Project ID' # 在 Microsoft Clarity 获取的 Project ID

### 不蒜子 (Busuanzi) 统计
busuanzi:
  enable: true # 是否启用不蒜子统计
##  # 不蒜子通常只需启用即可，主题会引入其脚本并处理页面和站点访问量显示
##  # 具体显示位置和样式可能由主题其他配置控制 (如侧边栏卡片或文章元信息)
```



### 第五章: 外观、多页面集成

本章将深入探讨安知鱼 (Anzhiyu) 主题在网站外观、视觉特效及性能优化方面的各项配置。通过调整这些设置，您可以自定义网站的色彩风格、添加动态效果，并采取措施提升网站的加载速度和用户体验。所有相关配置均位于您的主题配置文件 `_config.anzhiyu.yml` 中。

#### 主题颜色与美化

本节介绍如何自定义网站的主题色调、文本选择颜色、代码块颜色等基础视觉元素，以及其他通用美化设置。

```yaml
# Theme color for customize (自定义主题颜色)
# Notice: color value must in double quotes like "#000" or may cause error! (注意：颜色值必须用双引号括起来，如 "#000"，否则可能出错!)

theme_color:
  enable: true # 是否启用自定义主题颜色
  main: "#52616b" # 主题主色调 (light mode)
  dark_main: "#f2b94b" # 主题主色调 (dark mode)
  paginator: "#52616b" # 分页器颜色
  #   button_hover: "#FF7242" # 按钮悬停颜色 (示例)
  text_selection: "#c9d6df" # 文本选中颜色
  link_color: "var(--anzhiyu-fontcolor)" # 链接颜色 (使用CSS变量)
  meta_color: "var(--anzhiyu-fontcolor)" # 元信息颜色 (使用CSS变量)
  hr_color: "#c9d6df33" # 水平分隔线颜色
  code_foreground: "#1e2022" # 代码块前景色
  code_background: "var(--anzhiyu-code-stress)" # 代码块背景色 (使用CSS变量)
  toc_color: "#52616b" # 目录颜色
  #   blockquote_padding_color: "#52616b" # 引用块 padding 颜色 (示例)
  #   blockquote_background_color: "#52616b" # 引用块背景颜色 (示例)
  scrollbar_color: "var(--anzhiyu-scrollbar)" # 滚动条颜色 (使用CSS变量)
  meta_theme_color_light: "#f0f5f9" # 移动端浏览器顶部主题色 (light mode)
  meta_theme_color_dark: "#18171d" # 移动端浏览器顶部主题色 (dark mode)


beautify:
  enable: true # 是否启用通用美化效果 (例如阴影、圆角等)
  shadow: true # 是否启用元素阴影效果
  rounded_corner: true # 是否启用元素圆角效果
##  # ... 其他可能的通用美化配置项

hr_icon:
  enable: true # 是否为分割线 (hr) 添加图标
  icon: "anzhiyu-icon-fire" # 分割线图标类名，使用主题内置图标
```

##### 个性化方案推荐

-   **品牌色一致:** 将 `main_color` 设置为您博客 Logo 或品牌色调一致的颜色，增强品牌识别度。
-   **阅读舒适度:** `text_selection` 选择一个对比度适中但不刺眼的颜色，例如柔和的黄色或浅蓝色，提升阅读体验。
-   **风格选择:** 根据您的网站风格决定是否启用 `beautify` 中的阴影和圆角效果。阴影可以增加元素的立体感，圆角使界面更柔和。偏向扁平化设计可以禁用这些效果。
-   **有趣的分割线:** 为分割线添加一个有趣或与文章内容相关的图标 (`hr_icon`)，可以为页面增添一丝活力。例如，技术文章用代码图标，随笔用笔尖图标。





#### 背景与特效

安知鱼主题提供了多种动态背景和鼠标特效，可以为您的网站增添活力和趣味性。**请注意，启用特效可能会增加页面的加载时间和资源消耗，影响性能。建议按需选择，避免同时开启过多特效。**

```yaml
# Background effects (背景特效)
# --------------------------------------

# canvas_ribbon (静止彩带背景)
# See: https://github.com/hustcc/ribbon.js
canvas_ribbon:
  enable: false # 是否启用静止彩带背景
  size: 150 # 彩带大小
  alpha: 0.6 # 彩带透明度 (0~1)
  zIndex: -1 # 彩带元素的 z-index 值 (通常设置为 -1 使其在背景层)
  click_to_change: false # 是否点击页面时改变彩带颜色
  mobile: false # 是否在移动端启用

# Fluttering Ribbon (动态彩带)
canvas_fluttering_ribbon:
  enable: true # 是否启用动态彩带背景
  mobile: false # 是否在移动端启用

# canvas_nest (动态线条背景)
# https://github.com/hustcc/canvas-nest.js
canvas_nest:
  enable: true # 是否启用动态线条背景
  color: "0,0,255" # 线条颜色，格式为 RGB 值 (R,G,B)。注意：使用逗号分隔
  opacity: 0.7 # 线条透明度 (0~1)
  zIndex: -1 # 背景元素的 z-index 值
  count: 99 # 线条数量
  mobile: false # 是否在移动端启用

# Typewriter Effect (打字效果)
# https://github.com/disjukr/activate-power-mode
activate_power_mode:
  enable: true # 是否启用打字时的 Power Mode 效果
  colorful: true # 是否启用粒子动画 (冒光特效)
  shake: false # 是否启用抖动特效
  mobile: false # 是否在移动端启用

# Mouse click effects: fireworks (鼠标点击效果: 烟火特效)
fireworks:
  enable: false # 是否启用鼠标点击烟火特效
  zIndex: 9999 # 烟火元素的 z-index 值 (-1 或 9999)
  mobile: false # 是否在移动端启用

# Mouse click effects: Heart symbol (鼠标点击效果: 爱心)
click_heart:
  enable: true # 是否启用鼠标点击爱心特效
  mobile: false # 是否在移动端启用

# Mouse click effects: words (鼠标点击效果: 文字)
ClickShowText:
  enable: false # 是否启用鼠标点击文字特效
  text: # 点击后显示的文本列表
    # - I # 文本内容示例
    # - LOVE
    # - YOU
  fontSize: 15px # 文字大小
  random: false # 是否随机显示文本列表中的内容
  mobile: false # 是否在移动端启用

# Default display mode (网站默认的显示模式)
# light (default) / dark (默认亮色模式 / 暗色模式)
display_mode: light # 网站默认显示模式

# Beautify (美化页面显示)
beautify:
  enable: true # 是否启用页面美化功能
  field: post # 应用范围，可选 site (全站) / post (仅文章)
  title-prefix-icon: '\f0c1' # 标题前缀图标的 Unicode 值 (Font Awesome 图标)
  title-prefix-icon-color: "#F47466" # 标题前缀图标颜色

# Global font settings (全局字体设置)
# Don't modify the following settings unless you know how they work (非必要不要修改)
font:
  global-font-size: 16px # 全局默认字体大小
  code-font-size: # 代码块字体大小 (留空则使用全局字体大小或浏览器默认大小)
  font-family: # 全局字体栈 (例如: 'Arial, sans-serif')
  code-font-family: consolas, Menlo, "PingFang SC", "Microsoft JhengHei", "Microsoft YaHei", sans-serif # 代码块字体栈

# Font settings for the site title and site subtitle (网站标题和副标题字体设置)
# 左上角网站名字 主页居中网站名字
blog_title_font:
  font_link: # 字体文件链接 (如来自 Google Fonts)
  font-family: PingFang SC, 'Hiragino Sans GB', 'Microsoft JhengHei', 'Microsoft YaHei', sans-serif # 字体栈

# The setting of divider icon (水平分隔线图标设置)
hr_icon:
  enable: true # 是否启用水平分隔线图标
  icon: \f0c4 # 图标的 Unicode 值 (Font Awesome 图标，如 '\f0c4' 表示链接图标)
  icon-top: # 图标距离顶部距离 (可选)

# the subtitle on homepage (主页subtitle)
subtitle:
  enable: false # 是否启用主页副标题
  # Typewriter Effect (打字效果)
  effect: true # 是否启用打字效果
  # Effect Speed Options (打字效果速度参数)
  startDelay: 300 # 打字开始前的延迟时间 (毫秒)
  typeSpeed: 150 # 打字速度 (毫秒/字符)
  backSpeed: 50 # 回退速度 (毫秒/字符)
  # loop (循环打字)
  loop: true # 是否循环打字
  # source 调用第三方服务 (副标题内容来源)
  # source: false 关闭调用
  # source: 1  调用一言网的一句话（简体） https://hitokoto.cn/
  # source: 2  调用一句网（简体） http://yijuzhan.com/
  # source: 3  调用今日诗词（简体） https://www.jinrishici.com/
  # subtitle 会先显示 source , 再显示 sub 的内容 (如果同时启用 source 和 sub，会先显示 source 的内容，再显示 sub 的内容)
  source: 1 # 副标题内容来源
  # 如果关闭打字效果，subtitle 只会显示 sub 的第一行文字 (如果禁用打字效果，只会显示 sub 列表中的第一项)
  sub: # 自定义副标题文本列表
    # - 生活明朗&#44;万物可爱&#44;人间值得&#44;未来可期. # 文本内容

# Loading Animation (加载动画)
preloader:
  enable: true # 是否启用页面加载动画
  # source (加载动画类型)
  # 1. fullpage-loading (全屏加载动画)
  # 2. pace (进度条)
  # else all (默认显示头像加载动画)
  source: 3 # 加载动画类型
  # pace theme (pace 进度条主题，参见 https://codebyzach.github.io/pace/)
  pace_css_url: # pace 主题 CSS 文件地址
  avatar: /img/user/avatar.png # 自定义加载动画头像图片url (当 source 非 1 或 2 时生效)

```



#### 友情链接配置
本指南将引导您为 AnZhiYu 主题创建一个专属的、具有丰富样式的友情链接页面。该页面通常被称为“友人帐”。

###### **核心流程概览**

1.  **创建页面文件**：通过Hexo命令生成友情链接页面的基础文件。
2.  **添加友链数据**：在指定的数据文件中，以 `yml` 格式管理您的朋友列表。
3.  **配置菜单入口**：在网站的导航菜单中，添加入口以便访客访问。
4.  **（可选）配置页面顶部**：自定义友情链接页面顶部的欢迎信息和评论区格式。

---
###### **第一步：创建友情链接页面文件**

1.  **执行创建页面命令**
  
    * 在您Hexo博客的根目录下，打开终端，并输入以下命令：
    ```bash
    hexo new page link
    ```
* 这条命令会在您的 `source` 目录下创建一个名为 `link` 的文件夹，其中包含一个 `index.md` 文件。
  
2.  **修改页面 Front-matter**
    * 打开刚刚生成的 `source/link/index.md` 文件。
    * **确保**文件顶部的 Front-matter 区域包含 `type: "link"` 这一项。这是告诉主题使用特殊“友人帐”布局的关键。

    **请将文件内容修改为：**
    ```yaml
    ---
    title: 友人帐
    date: 2025-06-12 11:30:00
    type: "link"
    ---
    ```

###### **第二步：添加您的友链数据**

友情链接的数据是独立管理的，这样更便于维护。

1.  **创建数据文件**
    * 在 `source` 目录下，检查是否存在一个名为 `_data` 的文件夹。如果**没有**，请手动新建一个。
    * 在 `source/_data/` 文件夹内，新建一个名为 `link.yml` 的文件。

2.  **编辑 `link.yml` 文件**
    * 将您的朋友信息按照下面的格式，添加到 `link.yml` 文件中。您可以创建多个分类。

    **格式示例：**
    ```yaml
    # 这是一个友链列表，以'-'开头代表一个独立的分类组
    -
      # class_name: 【必填】友链的分类名称，会作为标题显示
      class_name: 框架
      
      # flink_style: 【必填】这个分类下所有友链的展示样式
      # 可选值: flexcard (带截图的卡片), telescopic (伸缩卡片), anzhiyu (主题特色列表)
      flink_style: flexcard
      
      # hundredSuffix: 【可选】为头像URL添加后缀，通常用于CDN图片处理，例如添加 '!w120' 来请求压缩过的头像
    hundredSuffix: ""
      
      # link_list: 【必填】该分类下的友链列表
      link_list:
        - 
          name: Hexo       # 【必填】友链名称
          link: https://hexo.io/zh-tw/ # 【必填】友链链接
          avatar: https://d33wubrfki0l68.cloudfront.net/6657ba50e702d84afb32fe846bed54fba1a77add/827ae/logo.svg # 【必填】友链头像
          descr: 快速、简单且强大的网站框架 # 【必填】友链描述
          
        - 
          name: anzhiyu主题
          link: https://blog.anheyu.com/
          avatar: https://npm.elemecdn.com/anzhiyu-blog-static@1.0.4/img/avatar.jpg
          descr: 生活明朗，万物可爱
      # siteshot: 【可选】网站截图，当上面的 flink_style 为 flexcard 或 telescopic 时生效
          siteshot: https://npm.elemecdn.com/anzhiyu-theme-static@1.1.6/img/blog.anheyu.com.jpg
    
    -
      class_name: 推荐博客
      flink_style: telescopic
      hundredSuffix: ""
      link_list:
        - 
          name: 安知鱼
          link: https://blog.anheyu.com/
          avatar: https://npm.elemecdn.com/anzhiyu-blog-static@1.0.4/img/avatar.jpg
      descr: 生活明朗，万物可爱
          siteshot: https://npm.elemecdn.com/anzhiyu-theme-static@1.1.6/img/blog.anheyu.com.jpg
          # color: 【可选】为左上角的标签设置背景颜色，vip是主题预设的颜色之一
          color: vip
          # tag: 【可选】在友链卡片的左上角显示一个自定义标签
          tag: 技术
    
    -
      class_name: 小伙伴
      
      # class_desc: 【可选】这个分类的额外描述，会显示在分类标题下方
      class_desc: 那些人，那些事
      
      flink_style: anzhiyu
      hundredSuffix: ""
      link_list:
        - 
          name: 安知鱼
          link: https://blog.anheyu.com/
          avatar: https://npm.elemecdn.com/anzhiyu-blog-static@1.0.4/img/avatar.jpg
          descr: 生活明朗，万物可爱
          # recommend: 【可选】设为 true，会快捷地在左上角添加一个“荐”字标签
          recommend: true
    ```
    

---
###### **第三步：在菜单中显示“友人帐”**

1.  打开您**主题的配置文件** (`themes/anzhiyu/_config.yml`)。
2.  找到 `menu:` 部分。
3.  确保“友人帐”这一项是**开启状态**（即行首没有 `#` 注释符）。

    ```yaml
    menu:
      # ... 其他菜单项 ...
      友链:
        友人帐: /link/ || anzhiyu-icon-link # <--- 确保这一行是开启的
        朋友圈: /fcircle/ || anzhiyu-icon-artstation # (可选功能，如需也请开启)
        留言板: /comments/ || anzhiyu-icon-envelope
      # ... 其他菜单项 ...
    ```

---
###### **（可选）第四步：配置页面顶部信息**

您可以自定义友人帐页面顶部的标题，以及评论区申请友链的默认格式。

1.  同样在**主题配置文件**中，找到 `linkPageTop:` 部分。
2.  根据您的喜好进行修改。
    ```yaml
    # 友情链接顶部相关配置
    linkPageTop:
      enable: true
      title: 与数百名博主无限进步 # <--- 可修改为您的自定义标题
      # 下方是访客在评论区申请友链时，输入框里默认显示的格式引导
      addFriendPlaceholder: "昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"
    ```
    * **提示**：`addFriendPlaceholder` 的内容会显示在友人帐页面的评论框中，引导他人以正确的格式向您提交友链申请，非常方便。





---
#### **信笺样式留言板**

这是一个通过插件 `hexo-butterfly-envelope` 实现的特殊页面，它会将您的留言板渲染成一个从信封中抽出的信纸的动态效果，非常别致。

###### **核心流程概览**
1.  **安装插件**：为您的 Hexo 博客添加这个新功能。
2.  **配置插件**：在**站点配置文件**中，设置信纸的图片、文字等内容。
3.  **配置菜单入口**：在您的网站导航菜单中，添加“留言板”的链接。

---
###### **第一步：安装 `hexo-butterfly-envelope` 插件**

* 在您 Hexo 博客的根目录下，打开终端，并运行以下命令来安装插件：
    ```bash
    npm install hexo-butterfly-envelope --save
    ```

---
###### **第二步：配置插件 (根目录 `_config.yml`)**

* 打开您博客**根目录**下的 `_config.yml` 文件（**注意**：不是主题的配置文件）。
* 将下面这段 `envelope_comment:` 配置块，完整地粘贴到文件末尾。

    ```yaml
    # 信笺样式留言板 (Envelope Comment Page)
    # 插件主页: https://akilar.top/posts/e2d3c450/
    envelope_comment:
      # --- 功能总开关 ---
      enable: true
      
      # --- 自定义图片资源 ---
      # 您可以将这些图片下载后放到您自己的图床，然后替换链接
      custom_pic:
        cover: https://npm.elemecdn.com/hexo-butterfly-envelope/lib/violet.jpg # 信纸顶部的图片
        line: https://npm.elemecdn.com/hexo-butterfly-envelope/lib/line.png # 信纸底部的装饰线
        beforeimg: https://npm.elemecdn.com/hexo-butterfly-envelope/lib/before.png # 信封的上半部分
        afterimg: https://npm.elemecdn.com/hexo-butterfly-envelope/lib/after.png # 信封的下半部分
        
      # --- 信纸上的引导性问题 ---
      # 插件会从下面的列表中随机选择一条显示在信纸上
      message:
        - 有什么想对我说的吗？
        - 对我的博客有什么建议或想法？
        - 留下你的足迹，我们交个朋友吧！
        - 哪怕是有什么想吐槽的，都可以告诉我哦~
        
      # --- 信纸底部的署名 ---
      bottom: Prorise 竭诚为您服务！ # 仅支持单行文本，已为您替换
      
      # --- （可选）信纸划出的高度 ---
      # 如果您的 message 内容很长，可以适当增加这个值
      height: # 1024px
      
      # --- （可选）页面的访问路径 ---
      # 默认为 /comments/，生成的页面地址就是 yoursite.com/comments/
      path: comments
      
      # --- （可选）为这个自动生成的页面设置 Front-matter ---
      # 这是最关键的部分，它定义了留言板页面的属性
      front_matter:
        title: 留言板      # 页面标题
        comments: true   # 允许评论（必须为true）
        top_img: false   # 不显示页面顶部的横幅大图
        type: envelope   # 页面类型，主题会根据这个来应用特殊样式
    ```

---
###### **第三步：在菜单中添加入口**

现在，我们需要让访客能够从导航菜单中找到这个新创建的页面。

1.  **打开您主题的配置文件** (`themes/anzhiyu/_config.yml`)。
2.  找到 `menu:` 部分。
3.  确保“留言板”这一项是**开启状态**（即行首没有 `#` 注释符）。

    ```yaml
    menu:
      # ... 其他菜单项 ...
      友链:
        友人帐: /link/ || anzhiyu-icon-link
        朋友圈: /fcircle/ || anzhiyu-icon-artstation
        留言板: /comments/ || anzhiyu-icon-envelope # <--- 确保这一行是开启的
      # ... 其他菜单项 ...
    ```
    * **重要提示**：请确保这里的路径 `/comments/` 与您在 `envelope_comment.path` 中设置的路径保持一致。

---

#### **“关于我”页面**

此页面并非一个简单的Markdown页面，而是一个由数据驱动、用于全面展示您个人信息的独立展示页，包含了技能、生涯、爱好等多个模块。

###### **核心流程概览**
1.  **创建页面文件**：通过Hexo命令生成“关于”页面的基础文件。
2.  **创建并编辑数据文件**：在 `_data` 目录中，通过 `about.yml` 文件来管理和定义页面上显示的所有内容。
3.  **配置菜单入口**：在网站的导航菜单中，添加“关于本人”的链接。

---
###### **第一步：创建“关于”页面文件**

1.  **执行创建页面命令**
    * 在您Hexo博客的根目录下，打开终端，并输入以下命令：
    ```bash
    hexo new page about
    ```
    * 这会在您的 `source` 目录下创建一个 `about` 文件夹及 `index.md` 文件。

2.  **修改页面 Front-matter**
    * 打开 `source/about/index.md` 文件。
    * 确保文件顶部的 Front-matter 区域包含 `type: "about"`，并建议关闭侧边栏和评论区以获得最佳展示效果。

    **请将文件内容修改为：**
    ```yaml
    ---
    title: 关于我
    date: 2025-06-13 16:13:06
    aside: false
    top_img: false
    comments: true
    type: "about"
    ---
    ```

---
###### **第二步：创建并编辑您的个人信息数据 (`about.yml`)**

这是最关键的一步，页面的所有内容都由这个文件定义。

1.  **创建数据文件**
  
* 在 `source/_data/` 文件夹内，新建一个名为 `about.yml` 的文件。
  
2.  **编辑 `about.yml` 文件**
    * 将下面的**模板**完整复制到 `about.yml` 文件中，然后**将里面的信息替换成您自己的**。我已经为您预先填写和注释了一些内容。

    ```yaml
    # 这是一个列表，但通常我们只配置一个项目
    
    - 
      # --- 顶部核心信息 ---
      class_name: 关于我
      subtitle: 代码构建世界，思想驱动未来✨ # 副标题
      avatarImg: /img/user/avatar.webp # 您的头像
      name: Prorise # 您的名字
      description: 是一名 超全栈工程师、独立开发者、博主 # 您的身份描述
      
      # --- 头像旁的技能标签 ---
      avatarSkills:
        left:
          - 😉 全栈开发工程师
          - 🤝 分享与热心帮助
          - 🏠 前端架构设计师
          - 🔨 技术栈之多面手
        right:
          - 专修交互与设计🤝
          - 脚踏实地行动派🏃
          - 团队小组发动机🧱
          - 执行力属性爆棚💢
          
      # --- 关于站点 ---
      aboutsiteTips:
        tips: 目标
        title1: 源于
        title2: 热爱而去 感受
        word:
          - 学习
          - 生活
          - 程序
          - 体验
      helloAbout: Prorise
      
      # --- 技能模块 ---
      skillsTips:
        tips: 技能
        title: 开启创造力
      # --- 生涯模块 ---
      careers:
        tips: 生涯
        title: 无限进步
        list: # 您的教育或工作经历
          - desc: "广东计算机科学与技术"
            color: "#357ef5"
          - desc: "超全栈开发工程师"
            color: "#357ef5"
        img: https://bu.dusays.com/2023/04/21/644287166329b.png # 背景图
    
      # --- 文章统计模块 ---
      statistic:
        link: /archives/
        text: 文章隧道
        cover: https://bu.dusays.com/2023/05/01/644f4b037b930.jpg
    
      # --- 地图模块 ---
      map:
        title: 我现在住在
        StrengthenTitle: 广东省潮州市 # 您所在的国家或城市
        background: https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/TerO/1474X484/8d3acb37-2cb0-4b18-a6c4-a28e2894aba1.png
        backgroundDark: https://bu.dusays.com/2023/07/05/64a4c63495ac5.jpg
      
      # --- 更多个人信息 ---
      selfInfo:
        selfInfoTips1: 生于
        selfInfoContentYear: 2005 # 您的出生年份
        selfInfoTips2: 就业于
        selfInfoContent2: 网络茫茫大海
        selfInfoTips3: 现在职业
        selfInfoContent3: 全栈工程师
      
      # --- 16Personalities 性格测试模块 (可选) ---
      personalities:
        author_name: INFJ-T
        personality_type: "提倡者"
        photo_url: "https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/sdwO/DM-20250613173929-001.svg"
        personality_img: "https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/m2uo/DM-20250613174238-001.svg"
        name_url: "https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/sdwO/DM-20250613173929-001.svg"
      
      # --- 座右铭模块 ---
      maxim:
        maxim_tips: 座右铭
        maxim_top: 保持饥饿，
        maxim_bottom: 保持愚蠢。
      
      # --- 特长模块 ---
      buff:
        buff_tips: 特长
        buff_top: 不会聊天的 技术宅
        buff_bottom: 学习指数 MAX
      
      # --- 游戏/番剧/音乐/关注偏好模块 ---
      # 根据您的实际情况填写或删除
      game:
        game_tips: 爱好游戏
        game_title: 英雄联盟
        game_uid: "游戏ID: Prorise"
        game_bg: https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/oYRd/1192X703/f836eb3c-f02b-42be-9eda-e7854e8de265.png
      
      comic:
        comic_tips: 爱好番剧
        comic_title: 追番
        comic_list:
          - name: 约定的梦幻岛
            href: https://img02.anheyu.com/adminuploads/1/2022/12/13/63988658aa1b1.webp
            cover: https://img02.anheyu.com/adminuploads/1/2022/12/13/63988658aa1b1.webp
          - name: 咒术回战
            href: https://www.bilibili.com/bangumi/media/md28229899/?spm_id_from=666.25.b_6d656469615f6d6f64756c65.1
            cover: https://img02.anheyu.com/adminuploads/1/2022/12/13/6398864e572ed.webp
          - name: 紫罗兰永恒花园
            href: https://www.bilibili.com/bangumi/media/md8892/?spm_id_from=666.25.b_6d656469615f6d6f64756c65.1
            cover: https://img02.anheyu.com/adminuploads/1/2022/12/13/639886315d658.webp
          - name: 未闻花名
            href: https://www.bilibili.com/bangumi/media/md22718131/?spm_id_from=666.25.b_6d656469615f6d6f64756c65.1
            cover: https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/LXVm/474X663/226eae35-d054-4419-9cac-02a822b19913.png
          - name: 星游记
            href: https://www.bilibili.com/bangumi/media/md135652/?spm_id_from=666.25.b_6d656469615f6d6f64756c65.1
            cover: https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/XVRo/770X1080/bd2b1fe4-4a01-40c3-99cd-34a566b6fec9.png
          
          
      like:
        like_tips: 关注偏好
        like_title: 新硬件 & 开源项目
        like_bg: https://bu.dusays.com/2022/12/06/638f5f05ce1f7.jpg
        like_bottom: 手机、电脑软硬件
      
      music:
        music_tips: 音乐偏好
        music_title: 纯音、轻音乐、美音
        music_bg: https://p2.music.126.net/Mrg1i7DwcwjWBvQPIMt_Mg==/79164837213438.jpg
        music_link: /music/
    
      # --- 打赏列表 (可选) ---
      # 如果您不需要展示打赏记录，可以直接删除整个 reward_list 模块
      reward_list:
        - name: 感谢A
          amount: 8.8
          datatime: 2025-06-01
        - name: 感谢B
          amount: 66.6
          datatime: 2025-05-20
    ```



在`_data`新建`creativity.yml_data`填入以下信息用于确认个人技术栈

```yaml
- category_name: "前端开发"
  creativity_list:
  - name: "HTML5/CSS3"
    color: "#e44d26"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg"
  - name: "JavaScript"
    color: "#f7df1e"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg"
  - name: "TypeScript"
    color: "#3178c6"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg"
  - name: "Vue.js"
    color: "#4fc08d"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/vuejs/vuejs-original.svg"
  - name: "React"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/react/react-original-wordmark.svg"
  - name: "小程序开发"
    color: "#09b83e"
    icon: "https://res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png"
  - name: "Sass"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/sass/sass-original.svg"
  - name: "uniapp"
    color: "#FEFEF7"
    icon: "https://ts2.tc.mm.bing.net/th/id/ODLS.e63c536b-f08d-4550-b4cd-f6ca9d84b67e?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "Webpack"
    color: "#8dd6f9"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/webpack/webpack-original.svg"
  - name: "Vite"
    color: "#646cff"
    icon: "https://vitejs.dev/logo.svg"
  - name: "Canvas动画"
    color: "#FFFFFF"
    icon: "https://ts4.tc.mm.bing.net/th/id/ODLS.97013f0b-ae3b-41b4-8c13-ae3ac47fd06d?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "GSAP动画库"
    color: "#FFFFFF"
    icon: "https://ts4.tc.mm.bing.net/th/id/ODLS.827513d4-9077-440d-92b0-1f3d73888d03?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2"
  - name: "Electron"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/electron/electron-original.svg"
  - name: "TailwindCSS"
    color: "#FFFFFF"
    icon: "https://ts3.tc.mm.bing.net/th/id/ODLS.095f9b22-a70b-47ed-bdb1-070466f08dc4?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "Bootstrap"
    color: "#7952b3"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/bootstrap/bootstrap-original.svg"
  - name: "Element Plus"
    color: "#409eff"
    icon: "https://ts3.tc.mm.bing.net/th/id/ODLS.57304398-66ea-459d-8d9c-4647aea8751b?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2" 
  - name: "DaisyUI"
    color: "#FFFFFF"
    icon: "https://ts2.tc.mm.bing.net/th/id/ODLS.abfe53b0-a009-4cb9-9eb6-0e088c68c907?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2"

- category_name: "后端编程语言"
  creativity_list:
  - name: "Java"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg"
  - name: "Python"
    color: "#3776ab"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg"
  - name: "PHP"
    color: "#777bb4"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/php/php-original.svg"
  - name: "Node.js"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg"
  - name: "C/C++"
    color: "#00599c"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/cplusplus/cplusplus-original.svg"

- category_name: "Spring全家桶"
  creativity_list:
  - name: "Spring Boot"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg"
  - name: "Spring Framework"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg"
  - name: "Spring MVC"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg"
  - name: "Spring Security"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg"

- category_name: "后端框架与库"
  creativity_list:
  - name: "MyBatis"
    color: "#FFFFFF"
    icon: "https://ts4.tc.mm.bing.net/th/id/ODLS.513057f8-5234-45b9-92f8-da60ba13023d?w=32&h=32&qlt=93&pcl=fffffa&o=6&pid=1.2"
  - name: "MyBatis-Plus"
    color: "#FFFFFF"
    icon: "https://ts1.tc.mm.bing.net/th/id/ODLS.b5a2813e-eac8-4f6d-8c4a-3955777e3300?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2"
  - name: "Django"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/django/django-plain.svg"
  - name: "Flask"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/flask/flask-original.svg"
  - name: "FastAPI"
    color: "#FFFFFF"
    icon: "https://ts2.tc.mm.bing.net/th/id/ODLS.f4c3d716-9b6f-42a3-9099-8af0c6828d50?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "Scrapy"
    color: "#60a839"
    icon: "https://ts1.tc.mm.bing.net/th/id/ODLS.7c59a4c2-1c1a-47a1-b5c0-a9a7a345bbfb?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"
  - name: "ThinkPHP"
    color: "#FFFFFF"
    icon: "https://ts3.tc.mm.bing.net/th/id/ODLS.06dc16eb-e885-4f1a-890e-25dc17a3483b?w=32&h=32&qlt=90&pcl=fffffc&o=6&pid=1.2"

- category_name: "数据库与运维"
  creativity_list:
  - name: "MySQL"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg"
  - name: "MongoDB"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg"
  - name: "Redis"
    color: "#dc382d"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/redis/redis-original.svg"
  - name: "Linux"
    color: "#fcc624"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/linux/linux-original.svg"
  - name: "Docker"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg"
  - name: "Nginx"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nginx/nginx-original.svg"
  - name: "Git"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg"

- category_name: "AI与设计"
  creativity_list:
  - name: "提示词工程"
    color: "#FFFFFF"
    icon: "https://upload.wikimedia.org/wikipedia/commons/0/04/ChatGPT_logo.svg"
  - name: "UI设计"
    color: "#FFFFFF"
    icon: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/figma/figma-original.svg"
  - name: "UE设计"
    color: "#9c27b0"
    icon: "https://upload.wikimedia.org/wikipedia/commons/c/c2/Adobe_XD_CC_icon.svg"



- category_name: "开发框架"
  creativity_list:
  - name: "RuoYi框架"
    color: "#FFFFFF"
    icon: "https://ts2.tc.mm.bing.net/th/id/ODLS.5715a21c-7c5f-4781-ae97-2867ef553257?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2g"
  - name: "GoEazy网络框架"
    color: "#FFFFFF"
    icon: "https://ts3.tc.mm.bing.net/th/id/ODLS.d7f53ebd-3c0c-415f-943b-197fa248b289?w=32&h=32&qlt=90&pcl=fffffa&o=6&pid=1.2"
```

---
###### **第三步：在菜单中显示“关于本人”**

1.  打开您**主题的配置文件** (`themes/anzhiyu/_config.yml`)。
2.  找到 `menu:` 部分，确保“关于本人”这一项是**开启状态**（行首没有 `#` 注释符）。

    ```yaml
    menu:
      # ... 其他菜单项 ...
      关于:
        关于本人: /about/ || anzhiyu-icon-paper-plane # <--- 确保这一行是开启的
        闲言碎语: /essay/ || anzhiyu-icon-lightbulb
      # ... 其他菜单项 ...
    ```



---
#### **相册集**

##### **这是什么？**
这是一个两层结构的相册系统。您可以创建一个“相册大厅”页面，用于展示您所有不同主题的相册封面。点击任意一个封面后，会进入该相册的“展厅”页面，浏览其中的所有照片。

##### **核心流程概览**
1.  **创建“相册大厅”页面**：这是所有相册的入口。
2.  **创建并编辑相册数据 (`album.yml`)**：在这里管理您所有的相册信息和照片列表。
3.  **为每个相册创建独立的“展厅”页面 (关键步骤)**：`album.yml` 中的每个相册都需要一个对应的页面。
4.  **在菜单中添加入口**：让访客能找到您的“相册大厅”。

---
###### **第一步：创建“相册大厅”页面 (`/album/`)**

1.  **执行创建页面命令**
    * 在您Hexo博客的根目录下，打开终端，输入：
    ```bash
    hexo new page album
    ```
2.  **修改页面 Front-matter**
    * 打开新生成的 `source/album/index.md` 文件。
    * 确保其 `type` 为 `"album"`。
    ```yaml
    ---
    title: 相册集
    date: 2025-06-13 22:20:00
    aside: false
    top_img: false
    type: "album"
    ---
    ```

---
###### **第二步：创建并编辑您的相册数据 (`album.yml`)**

1.  **创建数据文件**
  
* 在 `source/_data/` 文件夹内，新建一个名为 `album.yml` 的文件。
  
2.  **编辑 `album.yml` 文件**
    * 这个文件是您所有相册的“总数据库”。它是一个列表，列表中的每一项（以 `-` 开头）都代表一个独立的相册集。
    * **请将下面的模板复制到文件中，并根据注释修改为您自己的内容。**

    ```yaml
    # 这是一个相册列表，每个 '-' 开头的块代表一个独立的相册集
    -
      class_name: "我的摄影作品"                     # 【必填】相册集的名称
      path_name: /my-photos/                        # 【必填】这个相册的专属访问路径。必须与您在第三步中创建的页面文件夹名完全一致
      type: 2                                     # 【必填】相册页面样式类型 (1 或 2)
      description: "记录生活中的光影瞬间"            # 【必填】相册的描述
      cover: https://picsum.photos/800/600?random=1  # 【必填】这个相册在"相册大厅"里显示的封面图
      rowHeight: 220                              # 【可选】仅当 type 为 2 时有效，照片墙的行高
      limit: 10                                   # 【可选】仅当 type 为 2 时有效，每次懒加载的数量
      lazyload: true                              # 【可选】仅当 type 为 2 时有效，是否开启懒加载
      btnLazyload: false                            # 【可选】将懒加载模式从"滚动到底部加载"变为"点击按钮加载"
      
      # 这个相册集里的照片列表
      album_list:
        - 
          date: 2025-06-10                      # 照片日期
          content: "城市夜景，灯火辉煌的街道，记录下这个不眠的夜晚。" # 照片描述
          address: "日本, 东京"                    # 拍摄地点
          from: Prorise                         # 拍摄者
          image:                                # 照片URL列表，可以放多张
            - https://picsum.photos/600/800?random=2
            - https://picsum.photos/800/600?random=3
            - https://picsum.photos/600/900?random=4
            - https://picsum.photos/900/600?random=5
        -
          date: 2025-06-11
          content: "古典建筑与现代都市的完美融合，传统与现代的对话。"
          address: "日本, 京都"
          from: Prorise
      image:
            - https://picsum.photos/800/600?random=6
            - https://picsum.photos/600/800?random=7
            - https://picsum.photos/700/500?random=8
            - https://picsum.photos/500/700?random=9
            - https://picsum.photos/800/800?random=10
        -
          date: 2025-06-12
          content: "自然风光，山川湖海的壮美，感受大自然的力量。"
          address: "瑞士, 阿尔卑斯山"
          from: Prorise
          image:
            - https://picsum.photos/900/600?random=11
            - https://picsum.photos/600/900?random=12
            - https://picsum.photos/800/500?random=13
            - https://picsum.photos/500/800?random=14
            - https://picsum.photos/700/700?random=15
            - https://picsum.photos/600/600?random=16
    
    -
      class_name: "我的日常生活"
      path_name: /my-daily-life/
      type: 1
      description: "一些沙雕日常和有趣的事情。"
      cover: https://picsum.photos/800/600?random=17
      album_list:
        - 
          date: 2025-06-01
          content: "周末宅家时光，享受慵懒的午后阳光。"
          address: "家里"
          from: Prorise
          image:
            - https://picsum.photos/600/800?random=18
            - https://picsum.photos/800/600?random=19
            - https://picsum.photos/500/700?random=20
        -
          date: 2025-06-02
          content: "和朋友们的聚餐时光，美食配美景。"
          address: "咖啡厅"
          from: Prorise
          image:
            - https://picsum.photos/700/500?random=21
            - https://picsum.photos/600/600?random=22
            - https://picsum.photos/800/500?random=23
            - https://picsum.photos/500/800?random=24
            - https://picsum.photos/900/600?random=25
        -
          date: 2025-06-03
          content: "公园散步，发现生活中的小美好。"
          address: "中央公园"
          from: Prorise
          image:
            - https://picsum.photos/600/900?random=26
            - https://picsum.photos/800/600?random=27
            - https://picsum.photos/700/700?random=28
    ```

---
###### **第三步：为每个相册创建独立的“展厅”页面 (关键步骤)**

这是此功能最关键也最容易被忽略的一步。您在 `album.yml` 中定义的**每一个**相册集，都必须有一个与之对应的、真实存在的页面。

1.  **根据 `path_name` 创建页面**
    * 例如，在上面的 `album.yml` 示例中，我们定义了两个相册集，它们的 `path_name` 分别是 `/my-photos/` 和 `/my-daily-life/`。
    * 那么，您就必须在终端**执行两条命令**来创建对应的页面：
    ```bash
    hexo new page my-photos
    hexo new page my-daily-life
    ```

2.  **修改详情页的 Front-matter**
    * **分别打开**新生成的 `source/my-photos/index.md` 和 `source/my-daily-life/index.md` 文件。
    * 将它们各自的 `type` 都设置为 `"album_detail"`。

    **`my-photos/index.md` 示例：**
    ```yaml
    ---
    title: 我的摄影作品
    date: 2025-06-13 22:30:00
    aside: false
    top_img: false
    type: "album_detail" # <--- 关键！类型必须是 "album_detail"
    ---
    ```
    **`my-daily-life/index.md` 示例：**
    ```yaml
    ---
    title: 我的日常生活
    date: 2025-06-13 22:31:00
    aside: false
    top_img: false
    type: "album_detail" # <--- 关键！类型必须是 "album_detail"
    ---
    ```
    > 总结：如果您在 `album.yml` 中定义了三个相册集，那么您就需要重复此步骤三次，创建三个对应的页面文件夹，并修改三个对应的 `.md` 文件。

---
###### **第四步：在菜单中显示“相册集”**

1.  打开您**主题的配置文件** (`themes/anzhiyu/_config.yml`)。
2.  找到 `menu:` 部分，确保“相册集”这一项是**开启状态**。
    ```yaml
    menu:
      # ...
      我的:
        # ...
        相册集: /album/ || anzhiyu-icon-images # <--- 确保这一行是开启的
        # ...
      # ...
    ```


---
#### **音乐馆**

##### **这是什么？**
本功能通过强大的 `APlayer` 播放器和 `MetingJS` 库，为您的博客创建一个专属的音乐播放页面。它不仅可以播放您自己收藏的音乐，还能直接拉取主流音乐平台（如网易云、QQ音乐）的歌单。

##### **核心流程概览**
1.  **安装并配置 APlayer 插件**：这是所有音乐功能的基础。
2.  **创建“音乐馆”页面文件**：生成 `/music/` 这个页面。
3.  **配置菜单入口与默认歌单**：在导航菜单中添加链接，并指定音乐馆页面默认播放哪个歌单。
4.  **（可选）配置自定义歌单**：创建一个由您自己指定的歌曲组成的“切换歌单”。

---
###### **第一步：安装并配置 APlayer 插件 (根目录)**

1.  **安装插件**
    * 在您Hexo博客的根目录下，打开终端，运行以下命令来安装 `hexo-tag-aplayer` 插件（它集成了APlayer和MetingJS）：
    ```bash
    npm install hexo-tag-aplayer --save
    ```

2.  **配置根目录 `_config.yml`**
  
    * 打开您博客**根目录**下的 `_config.yml` 文件。
    * 在文件末尾添加以下配置，以开启 MetingJS 功能。
    ```yaml
    # APlayer 播放器配置
    aplayer:
      meting: true # 必须开启，以支持从音乐平台获取歌单
      asset_inject: false # 主题会自行处理资源注入，这里建议设为false
    ```

---
###### **第二步：创建“音乐馆”页面**

1.  **执行创建页面命令**
  
    * 在终端中运行：
    ```bash
    hexo new page music
    ```
```
    
2.  **修改页面 Front-matter**
    * 打开新生成的 `source/music/index.md` 文件。
    * **确保**其 `type` 为 `"music"`，并且 `aplayer` 设置为 `true`，以在该页面加载播放器资源。
    ```yaml
    ---
    title: 音乐馆
    date: 2025-06-13 22:50:00
    type: music
    aplayer: true
    top_img: false
    comments: false
    aside: false
    ---
```

---
###### **第三步：配置菜单入口与默认歌单 (关键步骤)**

1.  **打开主题配置文件** (`themes/anzhiyu/_config.yml`)。
2.  **找到 `menu:` 部分**，确保“音乐馆”这一项是**开启状态**。
3.  **修改链接以指定默认歌单**：音乐馆页面**默认显示哪个歌单**，是在**菜单配置的链接中**决定的。您需要修改 `音乐馆:` 这一行的链接，为其加上 `server` 和 `id` 参数。

    ```yaml
    menu:
      # ...
      我的:
        # 格式: /music/?server=平台名称&id=歌单ID
        音乐馆: /music/?server=netease&id=8152976493 || anzhiyu-icon-music # <--- 示例：已修改为网易云音乐歌单
        # 追番页: /bangumis/ || anzhiyu-icon-bilibili
        # 相册集: /album/ || icon-images
      # ...
    ```
    * **参数解释**：
        * `server`: 音乐平台名称。常用值有 `netease`(网易云), `tencent`(QQ音乐), `kugou`(酷狗), `kuwo`(酷我)。
        * `id`: 您想播放的歌单或专辑的ID。您可以去对应音乐平台的网页版，打开一个歌单，其网址中的一串数字通常就是ID。

---
###### **第四步：（可选）配置自定义的“切换歌单”**

主题还允许您创建一个由**自己托管**的音乐文件组成的“备用歌单”。页面上会出现一个按钮，让您可以从默认歌单切换到这个自定义歌单。

1.  **创建数据文件**
    * 在 `source/` 目录下新建一个 `json` 文件夹。
    * 在 `source/json/` 文件夹内，新建一个名为 `music.json` 的文件。

2.  **编辑 `music.json` 文件**
    * 将您自己的歌曲信息按照下面的格式添加进去。**注意**：`url`, `cover`, `lrc` 都必须是可公开访问的链接地址。
    ```json
    [
      {
        "name": "歌曲名",
        "artist": "歌手名",
        "url": "https://example.com/music/song1.mp3",
        "cover": "https://example.com/images/cover1.jpg",
        "lrc": "https://example.com/lyrics/song1.lrc"
      },
      {
        "name": "另一首歌",
        "artist": "另一位歌手",
        "url": "https://example.com/music/song2.mp3",
        "cover": "https://example.com/images/cover2.jpg",
        "lrc": "https://example.com/lyrics/song2.lrc"
      }
    ]
    ```

---

#### **即刻短文**

##### **这是什么？**
一个独立的页面，以时间线的形式展示一系列简短的、图文并茂的“说说”或“微型博客”。所有内容都通过一个数据文件进行管理，无需为每一条短文都创建 `.md` 文件，非常方便。

##### **核心流程概览**
1.  **创建“即刻短文”页面文件**：通过Hexo命令生成 `/essay/` 页面的基础文件。
2.  **创建并编辑短文数据 (`essay.yml`)**：这是所有短文内容的“数据库”。
3.  **在菜单中添加入口**：让访客能找到这个页面。

---
###### **第一步：创建“即刻短文”页面**

1.  **执行创建页面命令**
    * 在您Hexo博客的根目录下，打开终端，输入：
    ```bash
    hexo new page essay
    ```

2.  **修改页面 Front-matter**
    * 打开新生成的 `source/essay/index.md` 文件。
    * **确保**其 `type` 为 `"essay"`，并根据需要设置其他属性，通常建议关闭侧边栏。
    ```yaml
    ---
    title: 闲言碎语
    date: 2025-06-13 22:42:00
    comments: true
    aside: false
    top_img: false
    type: "essay"
    ---
    ```

---
###### **第二步：创建并编辑您的短文数据 (`essay.yml`)**

1.  **创建数据文件**
  
* 在 `source/_data/` 文件夹内，新建一个名为 `essay.yml` 的文件。
  
2.  **编辑 `essay.yml` 文件**
    * 这个文件的结构很特别，第一部分是**对整个页面的配置**，第二部分 `essay_list` 则是**具体的短文列表**。
    * **请将下面的模板复制到文件中，并根据注释修改为您自己的内容。**

    ```yaml
    # 这是一个列表，但通常我们只配置第一项作为整个页面的设置
    - 
      # --- 页面整体配置 ---
      title: "即刻短文"                           # 【必填】页面大标题
      subTitle: "记录日常的灵感与碎碎念"       # 【必填】副标题
      tips: "随时随地，分享生活"                # 【必填】提示性文字
      buttonText: "关于我"                     # 【可选】页面顶部按钮的文字
      buttonLink: /about/                      # 【可选】页面顶部按钮的链接
      limit: 10                                # 【可选】首次加载显示的短文数量
      home_essay: true                         # 【可选】是否在首页也显示一个“即刻短文”的模块
      top_background: https://example.com/bg.jpg # 【可选】页面顶部的背景图

      # --- 短文列表 ---
      essay_list:
        # 示例1：纯文字
        - content: "今天天气真好，适合写代码！"
          date: 2025-06-13 22:45:00

        # 示例2：带图片和链接
        - content: "发现一个很棒的开源项目，推荐给大家。"
          date: 2025-06-12
          image:
            - https://example.com/project-screenshot.png
          link: https://github.com/example/project # 点击这条短文会跳转到这个链接

        # 示例3：内嵌B站视频
        # 注意：链接必须是 player.bilibili.com 的格式
        - content: "最近在看这个教程，讲得非常清楚。"
          date: 2025-06-11
          video:
            - https://player.bilibili.com/player.html?bvid=BV1Ch41137tR&autoplay=0

        # 示例4：内嵌音乐播放器
        - content: "分享一首最近单曲循环的歌。"
          date: 2025-06-10
          aplayer:
            server: netease      # 音乐平台: netease, tencent, kugou, kuwo
            id: '12345678'       # 歌曲或歌单ID
            
        # 示例5：带地点和出处
        - content: "今天在秋叶原淘到了好东西！"
          date: 2025-06-09
          address: 日本, 东京 # 地点
          from: Prorise      # 来源/作者
          image:
            - https://example.com/akihabara.jpg
    ```

---
###### **第三步：在菜单中显示“闲言碎语”**

1.  打开您**主题的配置文件** (`themes/anzhiyu/_config.yml`)。
2.  找到 `menu:` 部分，确保“闲言碎语”这一项是**开启状态**。
    ```yaml
    menu:
      # ...
      关于:
        关于本人: /about/ || anzhiyu-icon-paper-plane
        闲言碎语: /essay/ || anzhiyu-icon-lightbulb # <--- 确保这一行是开启的
        # ...
      # ...
    ```

---










#### **友情链接朋友圈（部署后实现）**

##### **这是什么？**

这是一个非常强大的功能，它能自动抓取您所有友情链接博客的最新文章，并以“朋友圈”或“时间线”的形式聚合在一个独立页面上，极大地增强了博客社区的互动性，但在项目初期还没有这么多的友链，所以我们滞后在完善这个页面

##### **核心架构：三个关键部**

要理解这个功能的配置，首先要明白它由三部分组成：
1.  **您的博客页面 (`/fcircle/`)**：这是最终展示给访客看的页面。
2.  **朋友圈的前端应用 (Vue.js App)**：一个JS文件，负责从后端获取数据并在页面上渲染出朋友圈的样式。
3.  **朋友圈的后端服务 (API Server)**：**这是最关键、最需要您动手的部分**。这是一个独立的、需要您自己部署的服务，它会像一个机器人，定期去您所有朋友的博客上“看”一下有没有新文章，然后把新文章的列表整理好，等待前端应用来获取。

---
###### **第一步：部署朋友圈后端服务 (最关键、最复杂)**

1.  **理解其作用**：您需要先拥有一个能工作的后端API服务。这个服务通常是基于一个名为 `hexo-circle-of-friends` 的开源项目。
2.  **如何部署**：您需要将这个项目部署到您自己的服务器或云平台（例如 Vercel, Railway, Zeabur 等免费平台）上。
    * **项目地址**：`https://github.com/Rock-Candy-Tea/hexo-circle-of-friends`
    * **操作指南**：请您**仔细阅读该项目的官方文档**来进行部署。这个过程通常涉及到数据库配置、爬虫规则设置等，是整个流程中最具技术挑战性的部分。
    * **重要提示 (`TIP`)**：根据您提供的文档，在配置后端时，请注意爬取规则可能需要使用 `common2` 格式，以确保能正确抓取到您朋友的数据。
3.  **获取成果**：当您成功部署后端服务后，您会得到一个您自己的、可以公开访问的API地址，例如 `https://my-friends-api.vercel.app`。这个就是我们后续配置需要的 `apiurl`。

---
###### **第二步：（可选）定制并托管前端JS文件**

官方提供的 `vue_js` 文件（`.../index.f9a2b8d2.js`）默认是去请求原作者的后端API。如果您在第一步中部署了自己的后端，就需要让这个JS文件去请求您自己的API地址。

* **方法A（修改JS文件）**：根据文档，您可以下载官方的JS文件，用文本编辑器打开，搜索并替换里面的 `friends.anheyu.com` 为您自己的后端API域名，然后将修改后的JS文件上传到您自己的CDN或服务器，并在后续配置中使用您自己的JS文件链接。
* **方法B（自行构建）**：更可靠的方法是，根据前端项目 `hexo-circle-of-friends-front` 的说明，在您本地修改源代码中的API地址，然后自己运行 `npm run build` 来构建出全新的JS文件，最后再上传使用。

> **初期建议**：为了简化流程，您可以**暂时跳过这一步**，先使用官方默认的JS和后端API来查看效果，等您自己的后端部署成功并运行稳定后，再回来进行替换。

---
###### **第三步：创建 Hexo 页面**

1.  在您博客的根目录下，打开终端，运行命令：
    ```bash
    hexo new page fcircle
    ```

2.  打开新生成的 `source/fcircle/index.md` 文件，确保其 `front-matter` 如下，**`type: "fcircle"` 是关键**。
    ```yaml
    ---
    title: 朋友圈
    date: 2025-06-12 18:00:00
    comments: false
    aside: false
    top_img: false
    type: "fcircle"
    ---
    ```

---
###### **第四步：配置您的 Hexo 主题**

1.  **配置菜单入口**
    * 打开您**主题的配置文件** (`themes/anzhiyu/_config.yml`)。
    * 找到 `menu:` 部分，确保“朋友圈”这一项是开启的（行首没有`#`）。
    ```yaml
    menu:
      # ...
      友链:
        # 友人帐: /link/ || anzhiyu-icon-link
        朋友圈: /fcircle/ || anzhiyu-icon-artstation # <--- 确保这一行是开启的
        # 留言板: /comments/ || anzhiyu-icon-envelope
      # ...
    ```

2.  **配置 `friends_vue`**
    * 在同一个主题配置文件中，找到 `friends_vue:` 部分。
    * 将 `enable` 设为 `true`，并填入您在第一步中部署好的**后端服务API地址**。

    ```yaml
    # 朋友圈配置
    friends_vue:
      enable: true
      # 初期可使用官方JS，后续替换为您自己在第二步中生成的JS地址
      vue_js: https://npm.elemecdn.com/anzhiyu-theme-static@1.1.2/friends/index.f9a2b8d2.js
      # 【必填】这里填写您在第一步中部署好的后端服务API地址
      apiurl: 'https://friends.anheyu.com/' # <--- 这是一个示例，请替换为您自己的API地址
      # 【可选】朋友圈页面顶部的背景图
      top_background: 'https://example.com/your-bg.jpg'
    ```

---
**总结与建议**

友情链接朋友圈是一个非常酷的功能，但它的配置流程是目前我们遇到的所有功能里最复杂的，因为它要求您独立部署和维护一个后端服务。

**我的建议是**：您可以将这个功能作为您博客的**长期目标**。在初期，可以先专注于写文章和配置其他更简单的功能。当您对整个建站流程更熟悉，并且有精力去研究后端部署时，再来挑战这个功能，会更有成就感。



---