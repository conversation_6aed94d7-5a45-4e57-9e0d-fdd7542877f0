- const { effect,loop,source,sub,startDelay,typeSpeed,backSpeed } = theme.footer.footerBar.subTitle
- let subContent = sub || new Array()

case source
  when 1
    script.
      function subtitleType () {
        fetch('https://v1.hitokoto.cn')
          .then(response => response.json())
          .then(data => {
            if (!{effect}) {
              const from = '出自 ' + data.from
              const sub = !{JSON.stringify(subContent)}
              sub.unshift(data.hitokoto, from)
              window.typed = new Typed('#footer-type-tips', {
                strings: sub,
                startDelay: !{startDelay},
                typeSpeed: !{typeSpeed},
                loop: !{loop},
                backSpeed: !{backSpeed},
              })
            } else {
              document.getElementById('footer-type-tips').innerHTML = data.hitokoto
            }
          })
      }

      if (!{effect}) {
        if (typeof Typed === 'function') {
          subtitleType()
        } else {
          getScript('!{url_for(theme.asset.typed)}').then(subtitleType)
        }
      } else {
        subtitleType()
      }

  when 2
    script.
      function subtitleType () {
        getScript('https://yijuzhan.com/api/word.php?m=js').then(() => {
          const con = str[0]
          if (!{effect}) {
            const from = '出自 ' + str[1]
            const sub = !{JSON.stringify(subContent)}
            sub.unshift(con, from)
            window.typed = new Typed('#footer-type-tips', {
              strings: sub,
              startDelay: !{startDelay},
              typeSpeed: !{typeSpeed},
              loop: !{loop},
              backSpeed: !{backSpeed},
            })
          } else {
            document.getElementById('footer-type-tips').innerHTML = con
          }
        })
      }

      if (!{effect}) {
        if (typeof Typed === 'function') {
          subtitleType()
        } else {
          getScript('!{url_for(theme.asset.typed)}').then(subtitleType)
        }
      } else {
        subtitleType()
      }

  when 3
    script.
      function subtitleType () {
        getScript('https://sdk.jinrishici.com/v2/browser/jinrishici.js').then(() => {
          jinrishici.load(result =>{
            if (!{effect}) {
              const sub = !{JSON.stringify(subContent)}
              const content = result.data.content
              sub.unshift(content)
              window.typed = new Typed('#footer-type-tips', {
                strings: sub,
                startDelay: !{startDelay},
                typeSpeed: !{typeSpeed},
                loop: !{loop},
                backSpeed: !{backSpeed},
              })
            } else {
              document.getElementById('footer-type-tips').innerHTML = result.data.content
            }
          })
        })
      }

      if (!{effect}) {
        if (typeof Typed === 'function') {
          subtitleType()
        } else {
          getScript('!{url_for(theme.asset.typed)}').then(subtitleType)
        }
      } else {
        subtitleType()
      }

  default
    - subContent = subContent.length ? subContent : new Array(config.subtitle)
    script.
      function subtitleType () {
        if (!{effect}) { 
          window.typed = new Typed("#footer-type-tips", {
            strings: !{JSON.stringify(subContent)},
            startDelay: !{startDelay},
            typeSpeed: !{typeSpeed},
            loop: !{loop},
            backSpeed: !{backSpeed}
          })
        } else {
          document.getElementById("footer-type-tips").innerHTML = '!{subContent[0]}'
        }
      }

      if (!{effect}) {
        if (typeof Typed === 'function') {
          subtitleType()
        } else {
          getScript('!{url_for(theme.asset.typed)}').then(subtitleType)
        }
      } else {
        subtitleType()
      }
