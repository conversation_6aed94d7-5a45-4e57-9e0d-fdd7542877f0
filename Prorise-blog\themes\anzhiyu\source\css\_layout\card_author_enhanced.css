/* Solitude风格作者卡片样式 */
.card-author-solitude.card-info {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  /* 移除固定高度，让内容自然撑开 */
}

/* 上半部分：渐变背景区域 */
.card-author-solitude.card-info .card-top-section {
  position: relative;
  height: 140px;
  background: linear-gradient(-25deg, var(--anzhiyu-main), var(--anzhiyu-main-op-deep));
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--anzhiyu-white);
  z-index: 1;
}

.card-author-solitude.card-info .card-top-section .sayhi {
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: fit-content;
  font-size: 14px; /* 增大字体 */
  white-space: nowrap; /* 新增：强制不换行 */
  background: var(--anzhiyu-white-op);
  border-radius: 15px;
  cursor: pointer;
  min-width: 80px;
  padding: 3px 10px;
  color: var(--anzhiyu-white);
  transition: all 0.3s;
  text-align: center;
}

.card-author-solitude.card-info .card-top-section .sayhi:hover {
  background: var(--anzhiyu-white);
  color: var(--anzhiyu-main);
  transform: translateX(-50%) scale(1.05);
}

/* 头像区域 - 在card-top-section内部，绝对定位到分界线上 */
.card-author-solitude.card-info .card-top-section .avatar {
  position: absolute;
  top: 90px; /* 让头像下半部分跨越到白色区域 */
  left: 50%;
  transform: translateX(-50%);
  width: 100px; /* 增大头像尺寸 */
  height: 100px;
  z-index: 100; /* 提高z-index确保在所有内容之上 */
  transition: all 0.3s;
}

.card-author-solitude.card-info .card-top-section .avatar img {
  border-radius: 50%;
  width: 100%;
  height: 100%;
  border: 4px solid var(--anzhiyu-white); /* 增加边框厚度 */
  overflow: hidden;
  object-fit: cover;
}

.card-author-solitude.card-info .card-top-section .avatar .sticker {
  position: absolute;
  bottom: 5px; /* 向上移动一点 */
  right: 5px; /* 向左移动一点 */
  width: 32px; /* 增大容器尺寸 */
  height: 32px; /* 增大容器尺寸 */
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--anzhiyu-white);
  border-radius: 50%;
}

.card-author-solitude.card-info .card-top-section .avatar .sticker .sticker-img {
  width: 24px; /* 增大图片尺寸 */
  height: 24px; /* 增大图片尺寸 */
  border-radius: 50%;
}

/* 下半部分：白色背景区域 */
.card-author-solitude.card-info .card-bottom-section {
  background: var(--anzhiyu-card-bg);
  padding: 60px 15px 15px; /* 增加顶部padding为头像留出空间 */
  color: var(--anzhiyu-fontcolor);
  /* 移除固定高度，让内容自然撑开 */
  position: relative;
}

.card-author-solitude.card-info .card-bottom-section .author-info {
  text-align: center;
  margin-bottom: 15px;
}

.card-author-solitude.card-info .card-bottom-section .author-info .name {
  font-size: 22px;
  font-weight: 700;
  color: var(--anzhiyu-fontcolor);
  margin-bottom: 5px;
}

.card-author-solitude.card-info .card-bottom-section .author-info .desc {
  font-size: 12px;
  color: var(--anzhiyu-fontcolor);
  opacity: 0.7;
  line-height: 1.3;
}

/* 站点统计样式 */
.card-author-solitude.card-info .card-bottom-section .site-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 12px;
}

.card-author-solitude.card-info .card-bottom-section .site-stats .stat-item {
  text-align: center;
  flex: 1;
}

.card-author-solitude.card-info .card-bottom-section .site-stats .stat-item a {
  display: block;
  color: var(--anzhiyu-fontcolor);
  text-decoration: none;
  transition: all 0.3s;
  padding: 5px;
  border-radius: 6px;
}

.card-author-solitude.card-info .card-bottom-section .site-stats .stat-item a:hover {
  background: var(--anzhiyu-secondbg);
  transform: translateY(-1px);
}

.card-author-solitude.card-info .card-bottom-section .site-stats .stat-item .stat-number {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 2px;
  color: var(--anzhiyu-fontcolor);
}

.card-author-solitude.card-info .card-bottom-section .site-stats .stat-item .stat-label {
  font-size: 11px;
  color: var(--anzhiyu-fontcolor);
  opacity: 0.7;
}

/* 社交图标样式 */
.card-author-solitude.card-info .card-bottom-section .social-icons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 10px;
}

.card-author-solitude.card-info .card-bottom-section .social-icons .social-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--anzhiyu-secondbg);
  border-radius: 50%;
  color: var(--anzhiyu-fontcolor);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card-author-solitude.card-info .card-bottom-section .social-icons .social-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.card-author-solitude.card-info .card-bottom-section .social-icons .social-icon:hover {
  background: var(--anzhiyu-main);
  color: var(--anzhiyu-white);
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-author-solitude.card-info .card-bottom-section .social-icons .social-icon:hover::before {
  transform: translateX(100%);
}

.card-author-solitude.card-info .card-bottom-section .social-icons .social-icon:active {
  transform: translateY(-1px) scale(1.05);
}

.card-author-solitude.card-info .card-bottom-section .social-icons .social-icon i {
  font-size: 18px;
  z-index: 1;
}

/* 移动端不显示作者卡片，所以移除响应式适配 */
