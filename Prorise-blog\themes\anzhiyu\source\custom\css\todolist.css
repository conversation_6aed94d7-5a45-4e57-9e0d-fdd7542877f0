/* Todolist 现代化设计 - 3.0版本 - 网格布局 */

body[data-type="todolist"] #web_bg {
  background: var(--anzhiyu-background);
}

body[data-type="todolist"] #page {
  border: 0;
  box-shadow: none !important;
  padding: 0 !important;
  background: transparent !important;
}

body[data-type="todolist"] #page .page-title {
  display: none;
}

/* 整体容器 */
#todolist-box {
  margin: 0 auto;
  max-width: 1200px;
  padding: 0 15px;
}

/* 顶部内容区 */
.author-content.todolist {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 20px;
}

.author-content .tips {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--anzhiyu-fontcolor);
  opacity: 0.75;
}

.author-content .tips i {
  color: var(--anzhiyu-theme);
  font-size: 0.8rem;
}

/* 筛选器样式 */
#todolist-filter {
  margin: 20px 0;
  padding: 15px;
  background: var(--anzhiyu-card-bg);
  border-radius: 12px;
  box-shadow: var(--anzhiyu-shadow-border);
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 12px;
  color: var(--anzhiyu-fontcolor);
  font-weight: 600;
}

.filter-title i {
  color: var(--anzhiyu-theme);
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-btn {
  background: var(--anzhiyu-card-bg);
  border: 1px solid var(--anzhiyu-theme-op);
  color: var(--anzhiyu-fontcolor);
  border-radius: 20px;
  padding: 5px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.filter-btn:hover {
  transform: translateY(-2px);
  border-color: var(--anzhiyu-theme);
}

.filter-btn.active {
  background: var(--anzhiyu-theme);
  color: white;
}

/* 主体布局 - 网格 */
#todolist-main {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

/* 卡片容器 */
.todolist-item {
  position: relative;
  background: var(--anzhiyu-card-bg);
  border-radius: 12px;
  padding: 1.2rem;
  box-shadow: var(--anzhiyu-shadow-border);
  transition: all 0.3s ease;
  overflow: hidden;
  border-left: 4px solid var(--anzhiyu-main);
  height: 100%;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.5s ease forwards;
}

/* 卡片hover效果 */
.todolist-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--anzhiyu-shadow-main);
}

/* 标题样式 */
h3.todolist-title {
  position: relative;
  margin: 0 0 1rem 0 !important;
  padding-bottom: 0.8rem;
  font-size: 1.25rem;
  color: var(--anzhiyu-fontcolor);
  border-bottom: 2px dashed var(--anzhiyu-theme-op);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

h3.todolist-title i {
  color: var(--anzhiyu-theme);
  font-size: 1.1rem;
  margin-right: 0.5rem;
}

h3.todolist-title span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 任务计数器 */
.task-count {
  background: var(--anzhiyu-theme-op);
  color: var(--anzhiyu-theme);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* 列表项基础样式 */
.todolist-ul {
  margin: 0;
  padding: 0;
  position: relative;
  flex: 1;
}

.todolist-ul li {
  list-style: none;
  padding: 0.75rem 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  background-color: transparent;
}

.todolist-ul li:last-child {
  margin-bottom: 0;
}

/* 列表项hover效果 */
.todolist-ul li:hover {
  background: linear-gradient(
    90deg,
    var(--anzhiyu-theme-op-deep) 0%,
    transparent 100%
  );
  padding-left: 1rem;
}

/* 未完成任务 */
li.todolist-li {
  color: var(--anzhiyu-fontcolor);
}

li.todolist-li i {
  color: var(--anzhiyu-theme);
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1.2em;
}

/* 图标hover效果 */
li.todolist-li:hover i {
  transform: scale(1.2);
  filter: drop-shadow(0 0 2px var(--anzhiyu-theme-op));
}

/* 已完成任务 */
li.todolist-li-done {
  color: var(--anzhiyu-secondtext);
}

li.todolist-li-done span {
  text-decoration: line-through;
  opacity: 0.8;
}

li.todolist-li-done i {
  color: var(--anzhiyu-green);
  font-size: 1.1rem;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1.2em;
  animation: checkmark 0.5s ease-out;
}

li.todolist-li-done:hover i {
  transform: scale(1.1) rotate(5deg);
}

/* 任务内容样式 */
.todolist-li span,
.todolist-li-done span {
  flex: 1;
  position: relative;
  line-height: 1.5;
}

/* 进度条样式 */
.progress-bar {
  height: 4px;
  background-color: var(--anzhiyu-theme-op);
  border-radius: 2px;
  margin-top: 1rem;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, var(--anzhiyu-theme) 0%, var(--anzhiyu-main) 100%);
  border-radius: 2px;
  transition: width 0.5s ease;
}

/* 分页样式 */
#todolist-pagination {
  display: flex;
  justify-content: center;
  margin: 30px 0;
}

.pagination-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.page-btn, 
.page-number {
  min-width: 36px;
  height: 36px;
  border: 1px solid var(--anzhiyu-card-border);
  background: var(--anzhiyu-card-bg);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--anzhiyu-fontcolor);
}

.page-btn:hover:not(:disabled),
.page-number:hover:not(.active) {
  border-color: var(--anzhiyu-theme);
  color: var(--anzhiyu-theme);
}

.page-number.active {
  background: var(--anzhiyu-theme);
  color: white;
  border-color: var(--anzhiyu-theme);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

#page-numbers {
  display: flex;
  gap: 5px;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  #todolist-box {
    margin: 0;
    padding: 0 10px;
  }

  .todolist-item {
    padding: 1rem;
  }
  
  h3.todolist-title {
    font-size: 1.1rem;
  }
  
  .filter-buttons {
    overflow-x: auto;
    padding-bottom: 5px;
    flex-wrap: nowrap;
  }
}

/* 暗黑模式特殊调整 */
[data-theme="dark"] .todolist-item {
  background: var(--anzhiyu-card-bg);
  border-left-color: var(--anzhiyu-main);
}

[data-theme="dark"] .todolist-ul li:hover {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.08) 0%,
    transparent 100%
  );
}

[data-theme="dark"] .filter-btn {
  background: var(--anzhiyu-card-bg);
  border-color: var(--anzhiyu-theme-op);
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.todolist-grid .todolist-item:nth-child(3n+1) {
  animation-delay: 0.1s;
}

.todolist-grid .todolist-item:nth-child(3n+2) {
  animation-delay: 0.2s;
}

.todolist-grid .todolist-item:nth-child(3n+3) {
  animation-delay: 0.3s;
}

/* 交互动画 */
@keyframes checkmark {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}