if theme.aside.card_author.enable
  .card-widget.card-info.card-author-solitude
    // 上半部分：渐变背景区域
    .card-top-section
      .sayhi#author-info__sayhi(onclick="anzhiyu.changeWittyWord()")
      .avatar
        img(alt="avatar", src=url_for(theme.avatar.img) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'`)
        if theme.author_status.enable && theme.author_status.statusImg
          .sticker
            img.sticker-img(src=url_for(theme.author_status.statusImg), alt="status")

    // 下半部分：白色背景区域
    .card-bottom-section
      .author-info
        .name= config.author
        .desc= theme.aside.card_author.content || config.description

      // 站点统计信息
      .site-stats
        .stat-item
          a(href=url_for('/archives/'))
            .stat-number= site.posts.length
            .stat-label 文章
        .stat-item
          a(href=url_for('/tags/'))
            .stat-number= site.tags.length
            .stat-label 标签
        .stat-item
          a(href=url_for('/categories/'))
            .stat-number= site.categories.length
            .stat-label 分类

      // 社交图标
      .social-icons
        if theme.social
          each value, label in theme.social
            - var array = value.split('||')
            a.social-icon(href=url_for(trim(array[0])), title=label, target='_blank')
              i.fab(class=trim(array[1]))
