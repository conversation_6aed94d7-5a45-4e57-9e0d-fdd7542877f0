- var $data = page.data
#equipment
    if $data
        each i in site.data[$data]
            .equipment-item
                h2.equipment-item-title= i.class_name
                .equipment-item-description= i.description
                .equipment-item-content
                    each item in i.equipment_list
                        .equipment-item-content-item
                            .equipment-item-content-item-cover
                                img.equipment-item-content-item-image(src=item.image, alt=item.name)
                            .equipment-item-content-item-info
                                .equipment-item-content-item-name(onclick='utils.copy("' + item.name + '")')= item.name
                                .equipment-item-content-item-specification= item.specification
                                .equipment-item-content-item-description= item.description
                                .equipment-item-content-item-toolbar
                                    if item.link
                                        if item.link.includes('https://') || item.link.includes('http://')
                                            a.equipment-item-content-item-link(href=item.link, target="_blank") 详情
                                        else
                                            a.equipment-item-content-item-link(href=item.link) 查看文章
                                    if theme.comment.use
                                        a.bber-reply(onclick=`sco.toTalk('${item.description}')`)
                                            i.solitude.fas.fa-comment(style="font-size: 1rem;")
