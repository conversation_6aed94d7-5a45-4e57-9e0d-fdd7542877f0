- let year
each post in page.posts.find({ parent: { $exists: false } }).data
    if(year !== post.date.year())
        - year = post.date.year()
        .article-sort-item.year #{year}
    .article-sort-item
        a.article-sort-item-img(href=url_for(post.path) title=post.title)
            img(src=url_for(post.cover) alt=post.title)
        .article-sort-item-info
            a.article-sort-item-title(href=url_for(post.path) title=post.title onclick="window.event.cancelBubble=true;") #{post.title}
            .article-sort-item-tags
                each tags in post.tags.data
                    a.article-meta__tags(href=url_for(tags.path) onclick="window.event.cancelBubble=true;")
                        span.tags-punctuation
                            i.solitude.fas.fa-hashtag
                            =tags.name