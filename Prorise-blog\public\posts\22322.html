<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试” | Prorise的小站</title><meta name="keywords" content="Java微服务篇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><meta name="application-name" content="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><meta property="og:url" content="https://prorise666.site/posts/22322.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="9. RAG 检索增强生成：AI 的“开卷考试”在人工智能的发展历程中，大语言模型（LLM）已经展现了惊人的“博闻强记”能力——它们能写诗、编码、解答常识问题，甚至模拟人类对话。然而，当面对专业领域知识或实时更新的信息时，这些模型往往会暴露其局限性：要么“一本正经地胡说八道”（产生幻觉），要么无奈承"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta name="description" content="9. RAG 检索增强生成：AI 的“开卷考试”在人工智能的发展历程中，大语言模型（LLM）已经展现了惊人的“博闻强记”能力——它们能写诗、编码、解答常识问题，甚至模拟人类对话。然而，当面对专业领域知识或实时更新的信息时，这些模型往往会暴露其局限性：要么“一本正经地胡说八道”（产生幻觉），要么无奈承"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/22322.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”",postAI:"true",pageFillDescription:"9. RAG 检索增强生成：AI 的开卷考试, 9.1 RAG：为何需要它它又是什么？, 9.1.1 LLM 的三大核心痛点, 9.1.2 RAG 的优雅解决方案, 9.1.3 文档 (Document)：知识的原子化表示, 9.2 Spring AI 实现 RAG 的核心, 9.2.1 RAG 的模块化构建图, 9.2.2 基础组件：QuestionAnswerAdvisor, 工作流程：, 核心配置项（实战已使用）：, 9.2.3 进阶控制：自定义 PromptTemplate, 9.2.4 自动化方案：RetrievalAugmentationAdvisor(重点内容), 适用场景, RetrievalAugmentationAdvisor 核心处理流程, 核心组件接口与实现详解, 1. 查询对象 (Query), 2. 检索前处理 (Pre-Retrieval), 3. 检索 (Retrieval), 4. 检索后处理 (Post-Retrieval), 5. 生成 (Generation), 完整配置与模块化代码示例, 1. pom.xml 核心依赖, 2. application.yml 最小配置, 3. 模块化 RAG 控制器示例 (RagModuleController), 与 QuestionAnswerAdvisor 对比简表, 9.3 实战：构建基础 RAG 问答应用, 9.3.1 环境与配置, 9.3.2 数据播种：手动向 VectorStore 添加知识, 9.3.3 RAG 服务与 API 实现, 9.3.4 接口测试检索增强生成的开卷考试在人工智能的发展历程中大语言模型已经展现了惊人的博闻强记能力它们能写诗编码解答常识问题甚至模拟人类对话然而当面对专业领域知识或实时更新的信息时这些模型往往会暴露其局限性要么一本正经地胡说八道产生幻觉要么无奈承认我不知道此时就要闪亮登场了它并非一种新的大模型而是一种更聪明的将大模型与外部知识库相结合的技术架构范式它旨在让从一个博闻强记的通才进化成一个能够引经据典言之有物的博学多才的领域专家为何需要它它又是什么的三大核心痛点要理解的价值我们必须先直面当前大语言模型在企业级应用中难以回避的三大局限性核心痛点具体描述对我们项目的影响知识盲区的知识被冻结在它训练完成的那一刻它对训练日期之后发生的任何事件发布的任何新技术都一无所知如果我们想让解答关于公司最新发布的版本框架的新特性一个在时代训练的将完全无法回答事实幻觉当面对其知识范围之外或不确定的问题时倾向于创造听起来非常流利和自信但事实基础却是错误的答案如果用户询问一个的用法幻觉出一个不存在的参数这可能会导致生产事故是绝对不可接受的数据隐私与领域特定性公开的无法访问我们企业内部的私有数据如内部技术文档项目资料客户支持记录财务报告等无法成为一个真正懂我们公司业务能解答内部问题的专家其应用场景将大受限制的优雅解决方案检索增强生成正是解决上述所有问题的优雅方案它的工作流程可以完美地比喻为让从闭卷考试升级为指定参考资料的开卷考试它包含两个核心阶段检索目标大海捞针精准定位流程当用户提出问题时例如如何配置的事务系统并不直接将问题发送给而是首先将用户问题作为查询指令在我们自己构建和维护的可信的外部知识库即在第八章中学习的中进行相似性搜索找到与问题语义最相关的若干信息片段例如它可能会找到公司内部最佳实践的第三章第五节增强生成目标据理力争言之有物流程系统将上一步检索到的相关信息片段作为上下文与用户的原始问题一同喂给并下达一个明确的指令请严格基于我提供的这些参考资料来回答这个问题通过这种方式考生不再仅凭其固有的可能过时的记忆来作答而是有了我们为它精心准备的权威且实时的参考资料这使得它能够生成更准确更具时效性且能理解我们私有知识的答案同时极大地抑制了事实幻觉的产生文档知识的原子化表示在的世界里是知识的原子单位它不仅仅是一段纯文本而是一个结构化的对象核心属性解释示例文档的唯一标识符核心文档的主要文本内容是一个关键工具关键一个存储任意键值对的用于描述文档的元信息对于系统至关重要它使得我们可以在检索时进行精确过滤例如只在这本书里查找并且在返回结果时能够告诉用户答案的来源实现可追溯性实现的核心在实际开发中仅靠向量检索并不能直接构建出稳健的问答系统提供了多个模块化组件帮助我们快速灵活地实现可控可调可插拔的流程本节将由浅入深梳理中与强相关的组件并结合实战代码说明其意义的模块化构建图的实现将复杂流程拆解为如下关键组件用户输入的问题对查询进行优化重写翻译压缩等提高检索效果从向量数据库中检索相关文档对检索结果进行后处理去重排序截断等将用户问题和文档上下文组合为提示词作为模型输入大语言模型基于上下文生成回答最终返回的答案结合了文档与问题其中每一步都可以独立扩展或替换大大增强了系统的可维护性和灵活性基础组件提供的是的核心实现之一适用于标准问答类型的任务适用于基于向量数据库进行知识问答用户问题结构清晰无上下文且需要较高检索与回答精准度工作流程使用用户问题向执行语义检索将检索到的文档拼接成上下文注入模板中引导给出答案核心配置项实战已使用相似度阈值返回前条在我们的实战中中注册了控制检索门槛与返回数量配合实现了优雅的问答流程进阶控制自定义默认拼接的上下文格式虽然通用但在高阶项目中往往需要更清晰的控制逻辑如避免幻觉提升逻辑性允许我们通过方法注入自定义以下是上下文资料请严格根据以上资料作答如果无法回答请说我不知道避免使用根据上下文等表述自动化方案重点内容是提供的一个高度模块化构建器适用于需要丰富控制和策略组合的场景而非简单快速上线的替代方案适用场景业务复杂流程可定制需要在检索前后注入查询转换文档处理等逻辑需处理多语言长对话召回提升支持等增强模块要求防幻觉策略清晰内置的空上下文拒答逻辑和自定义提示模板确保结果可控核心处理流程方法请求处理前创建原始查询从用户输入的文本参数和对话历史中构建一个对象查询转换依次通过列表中的每个转换器对查询进行修改如规范化重写等查询扩展若配置了则将转换后的查询扩展为一个或多个查询如同义词扩展以提升召回率异步检索文档对每个扩展后的查询异步调用检索相关文档文档合并使用将所有检索到的文档合并成一个列表文档后处理依次通过对合并后的文档进行进一步处理如去重排序摘要等查询增强用将原始查询和最终的文档结合生成带有上下文的增强查询更新请求用增强后的查询内容更新请求用于后续流程方法响应处理后将过程中检索到的文档添加到最终响应的元数据中便于溯源和调试核心组件接口与实现详解查询对象用于在流程中承载查询信息的标准类查询的文本内容当前查询相关的对话历史记录查询的上下文信息用于存储与查询相关的额外数据检索前处理查询转换此接口用于对原始查询进行变换以应对查询结构不佳术语歧义语言不同等问题通过优化查询使其更简洁精确以便在目标系统如向量数据库中获得更好的结果适用于用户查询冗长模糊的场景将对话历史和后续问题合并为一个独立的包含完整上下文的查询适用于多轮对话场景将用户查询翻译为目标语言适用于嵌入模型仅支持特定语言而用户查询语言多样的场景查询扩展此接口用于将单个查询扩展为多个语义上多样化的查询变体以提升召回率使用将单个查询生成多个语义相近但角度不同的查询从不同方面覆盖原始查询意图增加检索到相关结果的机会类型功能描述适用场景示例重写用户问题使之更利于搜索复杂长问题我最近在学机器学习什么是将上下文对话压缩成独立查询长对话追问它的第二大城市是哪将用户问题翻译为嵌入支持语言多语言用户输入扩展成多个语义变体提高召回率召回率优先如何运行项目查询扩展与转换通过优化查询使其更简洁精确以便在目标系统如向量数据库中获得更好的结果将对话历史和后续问题合并为一个独立的包含完整上下文的查询适用于多轮对话场景将用户查询翻译为目标语言适用于嵌入模型仅支持特定语言而用户查询语言多样的场景使用将单个查询生成多个语义相近但角度不同的查询从不同方面覆盖原始查询意图增加检索到相关结果的机会检索文档检索器此接口定义了从数据源检索文档的标准方法核心实现之一用于从中检索与输入查询语义相似的文档它支持相似度阈值返回数量和元数据过滤向量检索与结果合并用于从中检索与输入查询语义相似的文档它支持相似度阈值返回数量和元数据过滤返回相近的个文档如果一个文档里面出现了一些复杂查询比如查询年版本最新的公司文档那么这里的表达式可以写为文档合并器此接口用于将多个来源如多个扩展查询的结果的文档集合并为一个列表默认实现它将所有文档列表连接起来并通过文档进行去重最后根据文档分数降序排列检索后处理文档后处理器在文档检索完成后注入到最终提示词之前对此接口的实现可以对文档列表进行排序过滤重排压缩等操作以优化提供给大模型的上下文质量一般来他适合单独被抽离出去作为一个单独控制的代码方便管理这里以最简单的挑选第一个文档的排序方式来做演示在这一层如果需要定义话复杂需求即可操作传入进来的对象或是对象进行复杂的重排过滤文档选择器只选择第一个文档实现了接口用于在流水线中对检索到的文档进行后处理该实现只返回检索结果中的第一个文档用于限制上下文长度或选择最相关的文档处理文档列表只返回第一个文档查询对象检索到的文档列表只包含第一个文档的列表只返回第一个文档通常是相似度最高的文档生成查询增强器此接口负责将检索并处理后的文档上下文与原始用户查询结合生成最终发送给大模型的提示默认实现它使用一个提示模板将文档内容和用户问题组装起来空上下文策略通过控制如果为默认且检索结果为空它会使用一个特定的来生成一个无法回答的响应有效防止模型在没有信息时产生幻觉如果为则直接返回原始查询上下文增强完整配置与模块化代码示例核心依赖最小配置使用为应用指定的端口从环境变量读取其他配置模块化控制器示例这个示例清晰地展示了如何独立构建并组装的各个模块化组件新建一个基于内存的向量数据库作为测试新增实现初始化数据你的姓名是是一个全栈工程师年硕士毕业你的姓名是专业领域包含的数学前后端设计自然语言处理你姓名是爱好是发呆思考运动与顾问开始聊天查询扩展与转换通过优化查询使其更简洁精确以便在目标系统如向量数据库中获得更好的结果将对话历史和后续问题合并为一个独立的包含完整上下文的查询适用于多轮对话场景将用户查询翻译为目标语言适用于嵌入模型仅支持特定语言而用户查询语言多样的场景使用将单个查询生成多个语义相近但角度不同的查询从不同方面覆盖原始查询意图增加检索到相关结果的机会向量检索与结果合并用于从中检索与输入查询语义相似的文档它支持相似度阈值返回数量和元数据过滤返回相近的个文档如果一个文档里面出现了一些复杂查询比如查询年版本最新的公司文档那么这里的表达式可以写为自定义文档后处理假设实现为只取第一个文档构建组装优化查询多轮对话查询翻译查询多语义查询向量检索合并文档自定义文档后处理上下文增强单独检索源文档用于前端展示构建源文档信息最后我们创建一个与对比简表特性模块复杂度简单复杂可插拔多个策略检索配置支持支持且额外支持自定义支持支持通过查询增强不支持支持重写压缩翻译扩展检索后处理不支持支持排序去重摘要空上下文处理策略由模板控制默认拒答可配置推荐场景快速上线简单业务复杂业务高定制实战构建基础问答应用本节我们将聚焦后端搭建一个功能完备的问答服务我们将采用手动编排的方式来实现流程这种方式虽然代码稍多但能让我们最清晰地看到的每一步是如何运作的并且能灵活地构造返回给前端的数据结构包含引用来源注意本章我们暂时不深入讲解复杂文件的过程而是通过一个数据播种服务在应用启动时向中手动存入几条种子知识以模拟一个已存在的知识库完整的能处理等文件的管道我们将在第十章详细构建环境与配置首先确保我们的项目具备所有必要的零件第一步检查核心依赖请确保您的中已包含以下核心依赖第二步确认应用配置此配置继承自我们之前的章节确保连接指向端口并正确配置了智谱的模型信息使用为应用指定的端口从环境变量读取其他配置第三步配置核心配置项专用配置类提供用于优雅的实现用于功能向量存储实例配置好的数据播种手动向添加知识我们创建一个在应用启动时自动向中添加几条简单的作为我们的初始知识的实现可移植性知识库中已有数据跳过播种知识库为空开始进行数据播种旨在将生态系统的设计原则如可移植性模块化应用于工程领域是一个关键工具它能根据数量将长文本智能地分割成小块同时通过保证语义连续性在应用中必须为设计一个好的模板明确指示它只能根据提供的上下文回答以避免信息幻觉数据播种完成服务与实现现在我们来构建的问答服务和对应的接口定义提问请求回答响应为了前端能展示引用来源我们让它返回答案和源文档列表我们采用最优雅的注入形式符合官网推荐的代码编辑模式优雅的实现使用模式用户问题响应包含答案和源文档流程使用模式处理问题使用实现优雅的流程回答生成完成单独检索源文档用于前端展示构建源文档信息流程找到个相关源文档一个潜在的陷阱请注意在这里如果我们我们直接注入并使用了全局的当问答与需要长期记忆的普通聊天在同一个会话中混合时这可能会导致问题历史聊天记录被错误地注入解决此问题的最佳实践创建隔离的实例正如代码中演示的一致接口测试启动应用等待控制台输出数据播种完成后使用或测试我们的介绍一下你已知的知识你将收到一个包含精准答案和引用来源的响应证明系统正在基于我们播种的知识进行回答成功是一个开源的应用程序框架它被设计用来简化企业级应用程序的开发和维护以下是框架的一些核心概念和特性这些信息是基于您提供的参考资料旨在将生态系统的设计原则如可移植性模块化应用于工程领域旨在将生态系统的设计原则如可移植性模块化应用于工程领域旨在将生态系统的设计原则如可移植性模块化应用于工程领域",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-08 13:53:46",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#9-RAG-%E6%A3%80%E7%B4%A2%E5%A2%9E%E5%BC%BA%E7%94%9F%E6%88%90%EF%BC%9AAI-%E7%9A%84%E2%80%9C%E5%BC%80%E5%8D%B7%E8%80%83%E8%AF%95%E2%80%9D"><span class="toc-text">9. RAG 检索增强生成：AI 的“开卷考试”</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-RAG%EF%BC%9A%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81%E5%AE%83%EF%BC%8C%E5%AE%83%E5%8F%88%E6%98%AF%E4%BB%80%E4%B9%88%EF%BC%9F"><span class="toc-text">9.1 RAG：为何需要它，它又是什么？</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#9-1-1-LLM-%E7%9A%84%E4%B8%89%E5%A4%A7%E6%A0%B8%E5%BF%83%E2%80%9C%E7%97%9B%E7%82%B9%E2%80%9D"><span class="toc-text">9.1.1 LLM 的三大核心“痛点”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-1-2-RAG-%E7%9A%84%E4%BC%98%E9%9B%85%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88"><span class="toc-text">9.1.2 RAG 的优雅解决方案</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-1-3-%E6%96%87%E6%A1%A3-Document-%EF%BC%9A%E7%9F%A5%E8%AF%86%E7%9A%84%E5%8E%9F%E5%AD%90%E5%8C%96%E8%A1%A8%E7%A4%BA"><span class="toc-text">9.1.3 文档 (Document)：知识的原子化表示</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-Spring-AI-%E5%AE%9E%E7%8E%B0-RAG-%E7%9A%84%E6%A0%B8%E5%BF%83"><span class="toc-text">9.2 Spring AI 实现 RAG 的核心</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#9-2-1-RAG-%E7%9A%84%E6%A8%A1%E5%9D%97%E5%8C%96%E6%9E%84%E5%BB%BA%E5%9B%BE"><span class="toc-text">9.2.1 RAG 的模块化构建图</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-2-2-%E5%9F%BA%E7%A1%80%E7%BB%84%E4%BB%B6%EF%BC%9AQuestionAnswerAdvisor"><span class="toc-text">9.2.2 基础组件：QuestionAnswerAdvisor</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B7%A5%E4%BD%9C%E6%B5%81%E7%A8%8B%EF%BC%9A"><span class="toc-text">工作流程：</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E9%85%8D%E7%BD%AE%E9%A1%B9%EF%BC%88%E5%AE%9E%E6%88%98%E5%B7%B2%E4%BD%BF%E7%94%A8%EF%BC%89%EF%BC%9A"><span class="toc-text">核心配置项（实战已使用）：</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-2-3-%E8%BF%9B%E9%98%B6%E6%8E%A7%E5%88%B6%EF%BC%9A%E8%87%AA%E5%AE%9A%E4%B9%89-PromptTemplate"><span class="toc-text">9.2.3 进阶控制：自定义 PromptTemplate</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-2-4-%E8%87%AA%E5%8A%A8%E5%8C%96%E6%96%B9%E6%A1%88%EF%BC%9ARetrievalAugmentationAdvisor-%E9%87%8D%E7%82%B9%E5%86%85%E5%AE%B9"><span class="toc-text">9.2.4 自动化方案：RetrievalAugmentationAdvisor(重点内容)</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%80%82%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">适用场景</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#RetrievalAugmentationAdvisor-%E6%A0%B8%E5%BF%83%E5%A4%84%E7%90%86%E6%B5%81%E7%A8%8B"><span class="toc-text">RetrievalAugmentationAdvisor 核心处理流程</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%BB%84%E4%BB%B6%E6%8E%A5%E5%8F%A3%E4%B8%8E%E5%AE%9E%E7%8E%B0%E8%AF%A6%E8%A7%A3"><span class="toc-text">核心组件接口与实现详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E6%9F%A5%E8%AF%A2%E5%AF%B9%E8%B1%A1-Query"><span class="toc-text">1. 查询对象 (Query)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E6%A3%80%E7%B4%A2%E5%89%8D%E5%A4%84%E7%90%86-Pre-Retrieval"><span class="toc-text">2. 检索前处理 (Pre-Retrieval)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E6%A3%80%E7%B4%A2-Retrieval"><span class="toc-text">3. 检索 (Retrieval)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#4-%E6%A3%80%E7%B4%A2%E5%90%8E%E5%A4%84%E7%90%86-Post-Retrieval"><span class="toc-text">4. 检索后处理 (Post-Retrieval)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#5-%E7%94%9F%E6%88%90-Generation"><span class="toc-text">5. 生成 (Generation)</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%AE%8C%E6%95%B4%E9%85%8D%E7%BD%AE%E4%B8%8E%E6%A8%A1%E5%9D%97%E5%8C%96%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B"><span class="toc-text">完整配置与模块化代码示例</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-pom-xml-%E6%A0%B8%E5%BF%83%E4%BE%9D%E8%B5%96"><span class="toc-text">1. pom.xml 核心依赖</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-application-yml-%E6%9C%80%E5%B0%8F%E9%85%8D%E7%BD%AE"><span class="toc-text">2. application.yml 最小配置</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E6%A8%A1%E5%9D%97%E5%8C%96-RAG-%E6%8E%A7%E5%88%B6%E5%99%A8%E7%A4%BA%E4%BE%8B-RagModuleController"><span class="toc-text">3. 模块化 RAG 控制器示例 (RagModuleController)</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%8E-QuestionAnswerAdvisor-%E5%AF%B9%E6%AF%94%E7%AE%80%E8%A1%A8"><span class="toc-text">与 QuestionAnswerAdvisor 对比简表</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-%E5%AE%9E%E6%88%98%EF%BC%9A%E6%9E%84%E5%BB%BA%E5%9F%BA%E7%A1%80-RAG-%E9%97%AE%E7%AD%94%E5%BA%94%E7%94%A8"><span class="toc-text">9.3 实战：构建基础 RAG 问答应用</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#9-3-1-%E7%8E%AF%E5%A2%83%E4%B8%8E%E9%85%8D%E7%BD%AE"><span class="toc-text">9.3.1 环境与配置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-3-2-%E6%95%B0%E6%8D%AE%E6%92%AD%E7%A7%8D%EF%BC%9A%E6%89%8B%E5%8A%A8%E5%90%91-VectorStore-%E6%B7%BB%E5%8A%A0%E7%9F%A5%E8%AF%86"><span class="toc-text">9.3.2 数据播种：手动向 VectorStore 添加知识</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-3-3-RAG-%E6%9C%8D%E5%8A%A1%E4%B8%8E-API-%E5%AE%9E%E7%8E%B0"><span class="toc-text">9.3.3 RAG 服务与 API 实现</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-3-4-%E6%8E%A5%E5%8F%A3%E6%B5%8B%E8%AF%95"><span class="toc-text">9.3.4 接口测试</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5f2a23">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#277340">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#c72008">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#11a7a2">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#276d10">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#6d6a95">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java微服务篇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-03-21T08:13:45.000Z" title="发表于 2025-03-21 16:13:45">2025-03-21</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:46.283Z" title="更新于 2025-07-08 13:53:46">2025-07-08</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">7.3k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>29分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/22322.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/22322.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url">Java微服务篇</a><h1 id="CrawlerTitle" itemprop="name headline">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-03-21T08:13:45.000Z" title="发表于 2025-03-21 16:13:45">2025-03-21</time><time itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:46.283Z" title="更新于 2025-07-08 13:53:46">2025-07-08</time></header><div id="postchat_postcontent"><h2 id="9-RAG-检索增强生成：AI-的“开卷考试”"><a href="#9-RAG-检索增强生成：AI-的“开卷考试”" class="headerlink" title="9. RAG 检索增强生成：AI 的“开卷考试”"></a>9. RAG 检索增强生成：AI 的“开卷考试”</h2><p>在人工智能的发展历程中，大语言模型（LLM）已经展现了惊人的“博闻强记”能力——它们能写诗、编码、解答常识问题，甚至模拟人类对话。然而，当面对专业领域知识或实时更新的信息时，这些模型往往会暴露其局限性：要么“一本正经地胡说八道”（产生幻觉），要么无奈承认“我不知道”。</p><p>此时，<strong>RAG (Retrieval-Augmented Generation)</strong> 就要闪亮登场了。它并非一种新的大模型，而是一种<strong>更聪明的、将大模型与外部知识库相结合的技术架构范式</strong>。它旨在让 AI 从一个“博闻强记”的通才，进化成一个能够引经据典、言之有物的“博学多才”的领域专家。</p><h3 id="9-1-RAG：为何需要它，它又是什么？"><a href="#9-1-RAG：为何需要它，它又是什么？" class="headerlink" title="9.1 RAG：为何需要它，它又是什么？"></a>9.1 RAG：为何需要它，它又是什么？</h3><h4 id="9-1-1-LLM-的三大核心“痛点”"><a href="#9-1-1-LLM-的三大核心“痛点”" class="headerlink" title="9.1.1 LLM 的三大核心“痛点”"></a>9.1.1 LLM 的三大核心“痛点”</h4><p>要理解 RAG 的价值，我们必须先直面当前大语言模型（LLM）在企业级应用中难以回避的三大局限性：</p><table><thead><tr><th align="left">LLM 核心痛点</th><th align="left">具体描述</th><th align="left">对我们 AI-Copilot 项目的影响</th></tr></thead><tbody><tr><td align="left"><strong>知识盲区</strong></td><td align="left">LLM 的知识被“冻结”在它训练完成的那一刻。它对训练日期之后发生的任何事件、发布的任何新技术都一无所知。</td><td align="left">如果我们想让 AI-Copilot 解答关于公司最新发布的 <code>v3.5</code> 版本框架的新特性，一个在 <code>v3.0</code> 时代训练的 LLM 将完全无法回答。</td></tr><tr><td align="left">**事实幻觉 **</td><td align="left">当面对其知识范围之外或不确定的问题时，LLM 倾向于“创造”听起来非常流利和自信，但事实基础却是错误的答案。</td><td align="left">如果用户询问一个 API 的用法，AI 幻觉出一个不存在的参数，这可能会导致生产事故，是绝对不可接受的。</td></tr><tr><td align="left"><strong>数据隐私与领域特定性</strong></td><td align="left">公开的 LLM 无法访问我们企业内部的私有数据，如内部技术文档、项目资料、客户支持记录、财务报告等。</td><td align="left">AI-Copilot 无法成为一个真正懂我们公司业务、能解答内部问题的“专家”，其应用场景将大受限制。</td></tr></tbody></table><h4 id="9-1-2-RAG-的优雅解决方案"><a href="#9-1-2-RAG-的优雅解决方案" class="headerlink" title="9.1.2 RAG 的优雅解决方案"></a>9.1.2 RAG 的优雅解决方案</h4><p><strong>检索增强生成 (RAG)</strong> 正是解决上述所有问题的优雅方案。</p><p>它的工作流程可以完美地比喻为让 AI 从“<strong>闭卷考试</strong>”升级为“<strong>指定参考资料的开卷考试</strong>”。</p><p>它包含两个核心阶段：</p><ul><li><p><strong>1. 检索 (Retrieve):</strong></p><ul><li><strong>目标</strong>：大海捞针，精准定位。</li><li><strong>流程</strong>：当用户提出问题时（例如，“如何配置 Spring Batch 的事务？”），系统并<strong>不直接</strong>将问题发送给 LLM。而是首先将用户问题作为<strong>查询指令</strong>，在我们自己构建和维护的、可信的外部知识库（即在第八章中学习的 <strong><code>VectorStore</code></strong>）中进行<strong>相似性搜索</strong>，找到与问题语义最相关的若干信息片段（<code>Relevant Documents</code>）。例如，它可能会找到公司内部《Spring Batch 最佳实践.pdf》的第三章第五节。</li></ul></li><li><p><strong>2. 增强生成 (Augmented Generate):</strong></p><ul><li><strong>目标</strong>：据理力争，言之有物。</li><li><strong>流程</strong>：系统将上一步检索到的相关信息片段作为“<strong>上下文 (Context)</strong>”，与用户的原始问题<strong>一同</strong>“喂”给 LLM，并下达一个明确的指令：“<strong>请严格基于我提供的这些参考资料来回答这个问题</strong>”。</li></ul></li></ul><p>通过这种方式，LLM（考生）不再仅凭其固有的、可能过时的记忆来作答，而是有了我们为它精心准备的、权威且实时的参考资料。这使得它能够生成更准确、更具时效性、且能理解我们私有知识的答案，同时极大地抑制了“事实幻觉”的产生。</p><h4 id="9-1-3-文档-Document-：知识的原子化表示"><a href="#9-1-3-文档-Document-：知识的原子化表示" class="headerlink" title="9.1.3 文档 (Document)：知识的原子化表示"></a>9.1.3 文档 (Document)：知识的原子化表示</h4><p>在 Spring AI 的世界里，<code>Document</code> 是知识的原子单位。它不仅仅是一段纯文本，而是一个结构化的对象。</p><table><thead><tr><th align="left"><code>Document</code> 核心属性</th><th align="left">解释</th><th align="left">示例</th></tr></thead><tbody><tr><td align="left"><strong>id</strong></td><td align="left">文档的唯一标识符。</td><td align="left"><code>“doc_chunk_123”</code></td></tr><tr><td align="left"><strong>text</strong></td><td align="left"><strong>(核心)</strong> 文档的主要文本内容。</td><td align="left"><code>"TokenTextSplitter 是一个关键工具..."</code></td></tr><tr><td align="left"><strong>metadata</strong></td><td align="left"><strong>(关键)</strong> 一个存储任意键值对的 Map，用于描述文档的元信息。</td><td align="left"><code>{"source": "manual.pdf", "page": 5, "author": "Sam"}</code></td></tr></tbody></table><p><code>metadata</code> 对于 RAG 系统至关重要，它使得我们可以在检索时进行精确过滤（例如，“只在 <code>manual.pdf</code> 这本书里查找”），并且在返回结果时能够告诉用户答案的来源，实现<strong>可追溯性</strong>。</p><hr><h3 id="9-2-Spring-AI-实现-RAG-的核心"><a href="#9-2-Spring-AI-实现-RAG-的核心" class="headerlink" title="9.2 Spring AI 实现 RAG 的核心"></a>9.2 Spring AI 实现 RAG 的核心</h3><p>在实际开发中，仅靠向量检索并不能直接构建出稳健的问答系统。Spring AI 提供了多个模块化组件，帮助我们快速、灵活地实现<strong>可控、可调、可插拔的 RAG 流程</strong>。本节将由浅入深梳理 Spring AI 中与 RAG 强相关的组件，并结合实战代码说明其意义。</p><h4 id="9-2-1-RAG-的模块化构建图"><a href="#9-2-1-RAG-的模块化构建图" class="headerlink" title="9.2.1 RAG 的模块化构建图"></a>9.2.1 RAG 的模块化构建图</h4><p>Spring AI 的 RAG 实现将复杂流程拆解为如下关键组件：</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line">Query (用户输入的问题)</span><br><span class="line">  ↓</span><br><span class="line">QueryTransformer (对查询进行优化：重写、翻译、压缩等，提高检索效果)</span><br><span class="line">  ↓</span><br><span class="line">DocumentRetriever (从向量数据库中检索相关文档)</span><br><span class="line">  ↓</span><br><span class="line">DocumentPostProcessor (对检索结果进行后处理：去重、排序、截断等)</span><br><span class="line">  ↓</span><br><span class="line">PromptTemplate (将用户问题和文档上下文组合为提示词，作为模型输入)</span><br><span class="line">  ↓</span><br><span class="line">LLM (大语言模型基于上下文生成回答)</span><br><span class="line">  ↓</span><br><span class="line">Response (最终返回的答案，结合了文档与问题)</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><p>其中每一步都可以独立扩展或替换，大大增强了系统的可维护性和灵活性。</p><hr><h4 id="9-2-2-基础组件：QuestionAnswerAdvisor"><a href="#9-2-2-基础组件：QuestionAnswerAdvisor" class="headerlink" title="9.2.2 基础组件：QuestionAnswerAdvisor"></a>9.2.2 基础组件：QuestionAnswerAdvisor</h4><p>Spring AI 提供的 <code>QuestionAnswerAdvisor</code> 是 RAG 的核心实现之一，适用于「标准问答」类型的 RAG 任务。</p><p>适用于基于向量数据库进行知识问答，用户问题结构清晰（无上下文）且需要较高检索与回答精准度</p><h5 id="工作流程："><a href="#工作流程：" class="headerlink" title="工作流程："></a>工作流程：</h5><ol><li>使用用户问题向 <code>VectorStore</code> 执行语义检索；</li><li>将检索到的文档拼接成上下文；</li><li>注入 Prompt 模板中，引导 LLM 给出答案。</li></ol><h5 id="核心配置项（实战已使用）："><a href="#核心配置项（实战已使用）：" class="headerlink" title="核心配置项（实战已使用）："></a>核心配置项（实战已使用）：</h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">QuestionAnswerAdvisor.builder(vectorStore)</span><br><span class="line">    .searchRequest(SearchRequest.builder()</span><br><span class="line">        .similarityThreshold(<span class="number">0.8</span>)  <span class="comment">// 相似度阈值</span></span><br><span class="line">        .topK(<span class="number">4</span>)                   <span class="comment">// 返回前 K 条</span></span><br><span class="line">        .build())</span><br><span class="line">    .build();</span><br></pre></td></tr></tbody></table></figure><p>在我们的实战中 <code>RagConfig.java</code> 中注册了 QuestionAnswerAdvisor Bean，控制检索门槛与返回数量，配合 <code>ChatClient</code> 实现了优雅的 RAG 问答流程。</p><hr><h4 id="9-2-3-进阶控制：自定义-PromptTemplate"><a href="#9-2-3-进阶控制：自定义-PromptTemplate" class="headerlink" title="9.2.3 进阶控制：自定义 PromptTemplate"></a>9.2.3 进阶控制：自定义 PromptTemplate</h4><p><code>QuestionAnswerAdvisor</code> 默认拼接的上下文格式虽然通用，但在高阶项目中，往往需要更清晰的控制逻辑（如避免幻觉、提升逻辑性）。</p><p>Spring AI 允许我们通过 <code>.promptTemplate()</code> 方法注入自定义 Prompt：</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="type">PromptTemplate</span> <span class="variable">customPromptTemplate</span> <span class="operator">=</span> PromptTemplate.builder()</span><br><span class="line">    .renderer(StTemplateRenderer.builder().startDelimiterToken(<span class="string">'&lt;'</span>).endDelimiterToken(<span class="string">'&gt;'</span>).build())</span><br><span class="line">    .template(<span class="string">"""</span></span><br><span class="line"><span class="string">        &lt;query&gt;</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">        以下是上下文资料：</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">        ---------------------</span></span><br><span class="line"><span class="string">        &lt;question_answer_context&gt;</span></span><br><span class="line"><span class="string">        ---------------------</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">        请严格根据以上资料作答：</span></span><br><span class="line"><span class="string">        1. 如果无法回答，请说“我不知道”；</span></span><br><span class="line"><span class="string">        2. 避免使用“根据上下文...”等表述。</span></span><br><span class="line"><span class="string">        """</span>)</span><br><span class="line">    .build();</span><br></pre></td></tr></tbody></table></figure><hr><h4 id="9-2-4-自动化方案：RetrievalAugmentationAdvisor-重点内容"><a href="#9-2-4-自动化方案：RetrievalAugmentationAdvisor-重点内容" class="headerlink" title="9.2.4 自动化方案：RetrievalAugmentationAdvisor(重点内容)"></a>9.2.4 自动化方案：RetrievalAugmentationAdvisor(重点内容)</h4><p><code>RetrievalAugmentationAdvisor</code> 是 Spring AI 提供的一个高度<strong>模块化 RAG 构建器</strong>，适用于需要丰富控制和策略组合的场景，而非简单快速上线的替代方案。</p><h5 id="适用场景"><a href="#适用场景" class="headerlink" title="适用场景"></a>适用场景</h5><ul><li><strong>业务复杂、流程可定制</strong>：需要在检索前/后注入查询转换、文档处理等逻辑</li><li><strong>需处理多语言、长对话、召回提升</strong>：支持 <code>QueryTransformer</code>、<code>MultiQueryExpander</code> 等增强模块</li><li><strong>要求防幻觉、策略清晰</strong>：内置的空上下文拒答逻辑和自定义提示模板确保结果可控</li></ul><hr><h6 id="RetrievalAugmentationAdvisor-核心处理流程"><a href="#RetrievalAugmentationAdvisor-核心处理流程" class="headerlink" title="RetrievalAugmentationAdvisor 核心处理流程"></a>RetrievalAugmentationAdvisor 核心处理流程</h6><p><strong><code>before</code> 方法（请求处理前）:</strong></p><ol><li><strong>创建原始查询</strong>：从用户输入的文本、参数和对话历史中构建一个 <code>Query</code> 对象。</li><li><strong>查询转换</strong>：依次通过 <code>queryTransformers</code> 列表中的每个转换器对查询进行修改（如规范化、重写等）。</li><li><strong>查询扩展</strong>：若配置了 <code>queryExpander</code>，则将转换后的查询扩展为一个或多个查询（如同义词扩展），以提升召回率。</li><li><strong>异步检索文档</strong>：对每个扩展后的查询，异步调用 <code>documentRetriever</code> 检索相关文档。</li><li><strong>文档合并</strong>：使用 <code>documentJoiner</code> 将所有检索到的文档合并成一个列表。</li><li><strong>文档后处理</strong>：依次通过 <code>documentPostProcessors</code> 对合并后的文档进行进一步处理（如去重、排序、摘要等）。</li><li><strong>查询增强</strong>：用 <code>queryAugmenter</code> 将原始查询和最终的文档结合，生成带有上下文的增强查询。</li><li><strong>更新请求</strong>：用增强后的查询内容更新请求，用于后续流程。</li></ol><p><strong><code>after</code> 方法（响应处理后）:</strong></p><ol><li>将 RAG 过程中检索到的文档（<code>rag_document_context</code>）添加到最终响应的元数据中，便于溯源和调试。</li></ol><hr><h5 id="核心组件接口与实现详解"><a href="#核心组件接口与实现详解" class="headerlink" title="核心组件接口与实现详解"></a>核心组件接口与实现详解</h5><h6 id="1-查询对象-Query"><a href="#1-查询对象-Query" class="headerlink" title="1. 查询对象 (Query)"></a>1. 查询对象 (Query)</h6><p>用于在 RAG 流程中承载查询信息的标准类。</p><ul><li><code>String text</code>：查询的文本内容。</li><li><code>List&lt;Message&gt; history</code>：当前查询相关的对话历史记录。</li><li><code>Map&lt;String, Object&gt; context</code>：查询的上下文信息，用于存储与查询相关的额外数据。</li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> org.springframework.ai.rag;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.messages.Message;</span><br><span class="line"><span class="keyword">import</span> org.springframework.util.Assert;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">Query</span><span class="params">(String text, List&lt;Message&gt; history, Map&lt;String, Object&gt; context)</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> Query {</span><br><span class="line">        Assert.hasText(text, <span class="string">"text cannot be null or empty"</span>);</span><br><span class="line">        Assert.notNull(history, <span class="string">"history cannot be null"</span>);</span><br><span class="line">        Assert.noNullElements(history, <span class="string">"history elements cannot be null"</span>);</span><br><span class="line">        Assert.notNull(context, <span class="string">"context cannot be null"</span>);</span><br><span class="line">        Assert.noNullElements(context.keySet(), <span class="string">"context keys cannot be null"</span>);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">Query</span><span class="params">(String text)</span> {</span><br><span class="line">        <span class="built_in">this</span>(text, List.of(), Map.of());</span><br><span class="line">    }</span><br><span class="line">    <span class="comment">// ... Builder and other methods</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="2-检索前处理-Pre-Retrieval"><a href="#2-检索前处理-Pre-Retrieval" class="headerlink" title="2. 检索前处理 (Pre-Retrieval)"></a>2. 检索前处理 (Pre-Retrieval)</h6><p><strong>a. 查询转换 (QueryTransformer)</strong></p><p>此接口用于对原始查询进行变换，以应对查询结构不佳、术语歧义、语言不同等问题。</p><ul><li><strong><code>RewriteQueryTransformer</code></strong>: 通过 LLM 优化查询，使其更简洁、精确，以便在目标系统（如向量数据库）中获得更好的结果。适用于用户查询冗长、模糊的场景。</li><li><strong><code>CompressionQueryTransformer</code></strong>: 将对话历史和后续问题合并为一个独立的、包含完整上下文的查询。适用于多轮对话场景。</li><li><strong><code>TranslationQueryTransformer</code></strong>: 将用户查询翻译为目标语言。适用于嵌入模型仅支持特定语言，而用户查询语言多样的场景。</li></ul><p><strong>b. 查询扩展 (QueryExpander)</strong></p><p>此接口用于将单个查询扩展为多个语义上多样化的查询变体，以提升召回率。</p><ul><li><strong><code>MultiQueryExpander</code></strong>: 使用 LLM 将单个查询生成多个语义相近但角度不同的查询，从不同方面覆盖原始查询意图，增加检索到相关结果的机会。</li></ul><table><thead><tr><th>Transformer 类型</th><th>功能描述</th><th>适用场景</th><th>示例</th></tr></thead><tbody><tr><td><code>RewriteQueryTransformer</code></td><td>重写用户问题，使之更利于搜索</td><td>复杂长问题</td><td>“我最近在学机器学习，什么是 LLM？”</td></tr><tr><td><code>CompressionQueryTransformer</code></td><td>将上下文对话压缩成独立查询</td><td>长对话、追问</td><td>“它的第二大城市是哪？”</td></tr><tr><td><code>TranslationQueryTransformer</code></td><td>将用户问题翻译为嵌入支持语言</td><td>多语言用户输入</td><td>“Hvad er Danmarks hovedstad?”</td></tr><tr><td><code>MultiQueryExpander</code></td><td>扩展成多个语义变体提高召回率</td><td>召回率优先</td><td>“如何运行 Spring Boot 项目？”</td></tr></tbody></table><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 1. Pre-Retrieval: 查询扩展与转换</span></span><br><span class="line"><span class="comment">// 通过 LLM 优化查询，使其更简洁、精确，以便在目标系统（如向量数据库）中获得更好的结果</span></span><br><span class="line"><span class="type">RewriteQueryTransformer</span> <span class="variable">rewriteQueryTransformer</span> <span class="operator">=</span> RewriteQueryTransformer.builder()</span><br><span class="line">        .chatClientBuilder(<span class="built_in">this</span>.chatClientBuilder).build();</span><br><span class="line"></span><br><span class="line"><span class="comment">// 将对话历史和后续问题合并为一个独立的、包含完整上下文的查询。适用于多轮对话场景</span></span><br><span class="line"><span class="type">CompressionQueryTransformer</span> <span class="variable">compressionQueryTransformer</span> <span class="operator">=</span> CompressionQueryTransformer.builder()</span><br><span class="line">        .chatClientBuilder(<span class="built_in">this</span>.chatClientBuilder).build();</span><br><span class="line"></span><br><span class="line"><span class="comment">// 将用户查询翻译为目标语言。适用于嵌入模型仅支持特定语言，而用户查询语言多样的场景</span></span><br><span class="line"><span class="type">TranslationQueryTransformer</span> <span class="variable">translationQueryTransformer</span> <span class="operator">=</span> TranslationQueryTransformer.builder()</span><br><span class="line">        .chatClientBuilder(<span class="built_in">this</span>.chatClientBuilder).targetLanguage(<span class="string">"English"</span>).build();</span><br><span class="line"></span><br><span class="line"><span class="comment">// 使用 LLM 将单个查询生成多个语义相近但角度不同的查询，从不同方面覆盖原始查询意图，增加检索到相关结果的机会。</span></span><br><span class="line"><span class="type">MultiQueryExpander</span> <span class="variable">multiQueryExpander</span> <span class="operator">=</span> MultiQueryExpander.builder()</span><br><span class="line">        .chatClientBuilder(<span class="built_in">this</span>.chatClientBuilder).build();</span><br></pre></td></tr></tbody></table></figure><h6 id="3-检索-Retrieval"><a href="#3-检索-Retrieval" class="headerlink" title="3. 检索 (Retrieval)"></a>3. 检索 (Retrieval)</h6><p><strong>a. 文档检索器 (DocumentRetriever)</strong></p><p>此接口定义了从数据源检索文档的标准方法。</p><ul><li><strong><code>VectorStoreDocumentRetriever</code></strong>: 核心实现之一，用于从 <code>VectorStore</code> 中检索与输入查询语义相似的文档。它支持相似度阈值 (<code>similarityThreshold</code>)、返回数量 (<code>topK</code>) 和元数据过滤 (<code>filterExpression</code>)。</li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 2. Retrieval: 向量检索与结果合并</span></span><br><span class="line"><span class="comment">// 用于从 VectorStore 中检索与输入查询语义相似的文档。</span></span><br><span class="line"><span class="comment">// 它支持相似度阈值</span></span><br><span class="line"><span class="comment">// 返回数量 (`topK`) 和元数据过滤 (`filterExpression`)。</span></span><br><span class="line"><span class="type">VectorStoreDocumentRetriever</span> <span class="variable">vectorStoreDocumentRetriever</span> <span class="operator">=</span> VectorStoreDocumentRetriever.builder()</span><br><span class="line">        .vectorStore(simpleVectorStore)</span><br><span class="line">        .topK(<span class="number">4</span>) <span class="comment">// 返回相近的4个文档</span></span><br><span class="line">        <span class="comment">// 如果一个文档里面出现了一些复杂查询，比如</span></span><br><span class="line">        <span class="comment">// 查询2025年版本最新的XX公司文档,那么这里的表达式可以写为：</span></span><br><span class="line">        <span class="comment">// .filterExpression("metadata.year &gt;= 2025")</span></span><br><span class="line">        .build();</span><br></pre></td></tr></tbody></table></figure><p><strong>b. 文档合并器 (DocumentJoiner)</strong></p><p>此接口用于将多个来源（如多个扩展查询的结果）的文档集合并为一个列表。</p><ul><li><strong><code>ConcatenationDocumentJoiner</code></strong>: 默认实现，它将所有文档列表连接起来，并通过文档ID进行去重，最后根据文档分数（<code>score</code>）降序排列。</li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="type">ConcatenationDocumentJoiner</span> <span class="variable">concatenationDocumentJoiner</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">ConcatenationDocumentJoiner</span>();</span><br></pre></td></tr></tbody></table></figure><h6 id="4-检索后处理-Post-Retrieval"><a href="#4-检索后处理-Post-Retrieval" class="headerlink" title="4. 检索后处理 (Post-Retrieval)"></a>4. 检索后处理 (Post-Retrieval)</h6><p><strong>a. 文档后处理器 (DocumentPostProcessor)</strong></p><p>在文档检索完成后、注入到最终提示词之前，对此接口的实现可以对文档列表进行排序、过滤、重排（Re-ranking）、压缩等操作，以优化提供给大模型的上下文质量，一般来他适合单独被抽离出去作为一个单独控制的代码，方便管理</p><blockquote><p>这里以最简单的，挑选第一个文档的排序方式来做演示，在这一层，如果需要定义话复杂需求，即可操作传入进来的Document对象或是query对象进行复杂的重排/过滤</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.document.Document;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.Query;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.postretrieval.document.DocumentPostProcessor;</span><br><span class="line"><span class="keyword">import</span> java.util.Collections;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 文档选择器 - 只选择第一个文档</span></span><br><span class="line"><span class="comment"> * </span></span><br><span class="line"><span class="comment"> * 实现了 DocumentPostProcessor 接口，用于在 RAG 流水线中对检索到的文档进行后处理。</span></span><br><span class="line"><span class="comment"> * 该实现只返回检索结果中的第一个文档，用于限制上下文长度或选择最相关的文档。</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">DocumentSelectFirst</span> <span class="keyword">implements</span> <span class="title class_">DocumentPostProcessor</span> {</span><br><span class="line">    </span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 处理文档列表，只返回第一个文档</span></span><br><span class="line"><span class="comment">     * </span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> query 查询对象</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> documents 检索到的文档列表</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 只包含第一个文档的列表</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> List&lt;Document&gt; <span class="title function_">process</span><span class="params">(Query query, List&lt;Document&gt; documents)</span> {</span><br><span class="line">        <span class="comment">// 只返回第一个文档，通常是相似度最高的文档</span></span><br><span class="line">        <span class="keyword">return</span> Collections.singletonList(documents.get(<span class="number">0</span>));</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="5-生成-Generation"><a href="#5-生成-Generation" class="headerlink" title="5. 生成 (Generation)"></a>5. 生成 (Generation)</h6><p><strong>a. 查询增强器 (QueryAugmenter)</strong></p><p>此接口负责将检索并处理后的文档（上下文）与原始用户查询结合，生成最终发送给大模型的提示（Prompt）。</p><ul><li><p><strong><code>ContextualQueryAugmenter</code></strong>: 默认实现，它使用一个提示模板将文档内容和用户问题组装起来。</p><ul><li><strong>空上下文策略</strong>：通过 <code>allowEmptyContext(boolean)</code> 控制。如果为 <code>false</code> (默认) 且检索结果为空，它会使用一个特定的 <code>emptyContextPromptTemplate</code> 来生成一个“无法回答”的响应，有效防止模型在没有信息时产生幻觉。如果为 <code>true</code>，则直接返回原始查询。</li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 4. Generation: 上下文增强</span></span><br><span class="line"><span class="type">ContextualQueryAugmenter</span> <span class="variable">contextualQueryAugmenter</span> <span class="operator">=</span> ContextualQueryAugmenter.builder()</span><br><span class="line">        .allowEmptyContext(<span class="literal">true</span>).build();</span><br></pre></td></tr></tbody></table></figure></li></ul><hr><h5 id="完整配置与模块化代码示例"><a href="#完整配置与模块化代码示例" class="headerlink" title="完整配置与模块化代码示例"></a>完整配置与模块化代码示例</h5><h6 id="1-pom-xml-核心依赖"><a href="#1-pom-xml-核心依赖" class="headerlink" title="1. pom.xml 核心依赖"></a>1. pom.xml 核心依赖</h6><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-starter-model-zhipuai<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line"></span><br><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-starter-vector-store-redis<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line"></span><br><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.boot<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-boot-starter-web<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-autoconfigure-model-openai<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-autoconfigure-model-chat-client<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-rag<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure><h6 id="2-application-yml-最小配置"><a href="#2-application-yml-最小配置" class="headerlink" title="2. application.yml 最小配置"></a>2. application.yml 最小配置</h6><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">spring:</span></span><br><span class="line">  <span class="attr">data:</span></span><br><span class="line">    <span class="attr">redis:</span></span><br><span class="line">      <span class="attr">host:</span> <span class="string">localhost</span></span><br><span class="line">      <span class="attr">port:</span> <span class="number">6380</span> <span class="comment"># 使用为 AI 应用指定的 6380 端口</span></span><br><span class="line">  <span class="attr">ai:</span></span><br><span class="line">    <span class="attr">zhipuai:</span></span><br><span class="line">      <span class="attr">api-key:</span> <span class="string">${ZHIPU_API_KEY}</span> <span class="comment"># 从环境变量读取</span></span><br><span class="line">      <span class="attr">chat:</span></span><br><span class="line">        <span class="attr">options:</span></span><br><span class="line">          <span class="attr">model:</span> <span class="string">glm-4-air</span></span><br><span class="line">      <span class="attr">embedding:</span></span><br><span class="line">        <span class="attr">options:</span></span><br><span class="line">          <span class="attr">model:</span> <span class="string">embedding-3</span></span><br><span class="line">    <span class="attr">vector-store:</span></span><br><span class="line">      <span class="attr">redis:</span></span><br><span class="line">        <span class="attr">index-name:</span> <span class="string">ai-copilot-rag-index</span></span><br><span class="line">        <span class="attr">initialize-schema:</span> <span class="literal">true</span></span><br><span class="line"><span class="comment"># ... 其他配置</span></span><br></pre></td></tr></tbody></table></figure><h6 id="3-模块化-RAG-控制器示例-RagModuleController"><a href="#3-模块化-RAG-控制器示例-RagModuleController" class="headerlink" title="3. 模块化 RAG 控制器示例 (RagModuleController)"></a>3. 模块化 RAG 控制器示例 (RagModuleController)</h6><p>这个示例清晰地展示了如何独立构建并组装 <code>RetrievalAugmentationAdvisor</code> 的各个模块化组件。</p><p>新建一个基于内存的向量数据库作为测试：</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.config;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.embedding.EmbeddingModel;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.SimpleVectorStore;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.VectorStore;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Bean;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Configuration;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Configuration</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">VectorStoreConfig</span> {</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> SimpleVectorStore <span class="title function_">simpleVectorStore</span><span class="params">(EmbeddingModel embeddingModel)</span> {</span><br><span class="line">        <span class="keyword">return</span> SimpleVectorStore.builder(embeddingModel).build();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">}</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><p>新增<code>DocumentSelectFirst</code></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.document.Document;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.Query;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.postretrieval.document.DocumentPostProcessor;</span><br><span class="line"><span class="keyword">import</span> java.util.Collections;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">DocumentSelectFirst</span> <span class="keyword">implements</span> <span class="title class_">DocumentPostProcessor</span> {</span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> List&lt;Document&gt; <span class="title function_">process</span><span class="params">(Query query, List&lt;Document&gt; documents)</span> {</span><br><span class="line">        <span class="keyword">return</span> Collections.singletonList(documents.get(<span class="number">0</span>));</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p>实现<code>RagAdvancedService</code></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.response.RagResponse;</span><br><span class="line"><span class="keyword">import</span> lombok.extern.slf4j.Slf4j;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.ChatClient;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.prompt.PromptTemplate;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.document.Document;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.embedding.EmbeddingModel;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.advisor.RetrievalAugmentationAdvisor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.generation.augmentation.ContextualQueryAugmenter;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.preretrieval.query.expansion.MultiQueryExpander;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.preretrieval.query.transformation.CompressionQueryTransformer;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.preretrieval.query.transformation.RewriteQueryTransformer;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.preretrieval.query.transformation.TranslationQueryTransformer;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.retrieval.join.ConcatenationDocumentJoiner;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.rag.retrieval.search.VectorStoreDocumentRetriever;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.SearchRequest;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.SimpleVectorStore;</span><br><span class="line"><span class="keyword">import</span> org.springframework.stereotype.Service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.HashMap;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Collectors;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="meta">@Slf4j</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">RagAdvancedService</span> {</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> SimpleVectorStore simpleVectorStore;</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> ChatClient.Builder chatClientBuilder;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">RagAdvancedService</span><span class="params">(EmbeddingModel embeddingModel, ChatClient.Builder builder)</span> {</span><br><span class="line">        <span class="built_in">this</span>.simpleVectorStore = SimpleVectorStore.builder(embeddingModel).build();</span><br><span class="line">        <span class="comment">// 初始化数据...</span></span><br><span class="line">        List&lt;Document&gt; documents = List.of(</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Document</span>(<span class="string">"你的姓名是Prorise，是一个全栈工程师，25年硕士毕业"</span>),</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Document</span>(<span class="string">"你的姓名是Prorise，专业领域包含的数学、前后端、设计、自然语言处理..."</span>),</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Document</span>(<span class="string">"你姓名是Prorise，爱好是发呆、思考、运动..."</span>)</span><br><span class="line">        );</span><br><span class="line">        simpleVectorStore.add(documents);</span><br><span class="line">        <span class="built_in">this</span>.chatClientBuilder = builder;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> RagResponse <span class="title function_">ask</span><span class="params">(String question)</span> {</span><br><span class="line">        log.info(<span class="string">"与 RAG 顾问开始聊天"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 1. Pre-Retrieval: 查询扩展与转换</span></span><br><span class="line">        <span class="comment">// 通过 LLM 优化查询，使其更简洁、精确，以便在目标系统（如向量数据库）中获得更好的结果</span></span><br><span class="line">        <span class="type">RewriteQueryTransformer</span> <span class="variable">rewriteQueryTransformer</span> <span class="operator">=</span> RewriteQueryTransformer.builder()</span><br><span class="line">                .chatClientBuilder(<span class="built_in">this</span>.chatClientBuilder).build();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 将对话历史和后续问题合并为一个独立的、包含完整上下文的查询。适用于多轮对话场景</span></span><br><span class="line">        <span class="type">CompressionQueryTransformer</span> <span class="variable">compressionQueryTransformer</span> <span class="operator">=</span> CompressionQueryTransformer.builder()</span><br><span class="line">                .chatClientBuilder(<span class="built_in">this</span>.chatClientBuilder).build();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 将用户查询翻译为目标语言。适用于嵌入模型仅支持特定语言，而用户查询语言多样的场景</span></span><br><span class="line">        <span class="type">TranslationQueryTransformer</span> <span class="variable">translationQueryTransformer</span> <span class="operator">=</span> TranslationQueryTransformer.builder()</span><br><span class="line">                .chatClientBuilder(<span class="built_in">this</span>.chatClientBuilder).targetLanguage(<span class="string">"English"</span>).build();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 使用 LLM 将单个查询生成多个语义相近但角度不同的查询，从不同方面覆盖原始查询意图，增加检索到相关结果的机会。</span></span><br><span class="line">        <span class="type">MultiQueryExpander</span> <span class="variable">multiQueryExpander</span> <span class="operator">=</span> MultiQueryExpander.builder()</span><br><span class="line">                .chatClientBuilder(<span class="built_in">this</span>.chatClientBuilder).build();</span><br><span class="line"></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. Retrieval: 向量检索与结果合并</span></span><br><span class="line">        <span class="comment">// 用于从 VectorStore 中检索与输入查询语义相似的文档。</span></span><br><span class="line">        <span class="comment">// 它支持相似度阈值</span></span><br><span class="line">        <span class="comment">// 返回数量 (`topK`) 和元数据过滤 (`filterExpression`)。</span></span><br><span class="line">        <span class="type">VectorStoreDocumentRetriever</span> <span class="variable">vectorStoreDocumentRetriever</span> <span class="operator">=</span> VectorStoreDocumentRetriever.builder()</span><br><span class="line">                .vectorStore(simpleVectorStore)</span><br><span class="line">                <span class="comment">// .topK(4) // 返回相近的4个文档</span></span><br><span class="line">                <span class="comment">// 如果一个文档里面出现了一些复杂查询，比如</span></span><br><span class="line">                <span class="comment">// 查询2025年版本最新的XX公司文档,那么这里的表达式可以写为：</span></span><br><span class="line">                <span class="comment">// .filterExpression("metadata.year &gt;= 2025")</span></span><br><span class="line">                .build();</span><br><span class="line"></span><br><span class="line"></span><br><span class="line">        <span class="type">ConcatenationDocumentJoiner</span> <span class="variable">concatenationDocumentJoiner</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">ConcatenationDocumentJoiner</span>();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. Post-Retrieval: 自定义文档后处理</span></span><br><span class="line">        <span class="type">DocumentSelectFirst</span> <span class="variable">documentSelectFirst</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">DocumentSelectFirst</span>(); <span class="comment">// 假设实现为只取第一个文档</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 构建 ContextualQueryAugmenter</span></span><br><span class="line">        <span class="type">ContextualQueryAugmenter</span> <span class="variable">contextualQueryAugmenter</span> <span class="operator">=</span> ContextualQueryAugmenter.builder()</span><br><span class="line">                .allowEmptyContext(<span class="literal">false</span>)</span><br><span class="line">                .build();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 组装 Advisor</span></span><br><span class="line">        <span class="type">RetrievalAugmentationAdvisor</span> <span class="variable">retrievalAugmentationAdvisor</span> <span class="operator">=</span> RetrievalAugmentationAdvisor.builder()</span><br><span class="line">                .queryTransformers(rewriteQueryTransformer) <span class="comment">// 优化查询</span></span><br><span class="line">                .queryTransformers(compressionQueryTransformer) <span class="comment">// 多轮对话查询</span></span><br><span class="line">                .queryTransformers(translationQueryTransformer) <span class="comment">// 翻译查询</span></span><br><span class="line">                .queryExpander(multiQueryExpander) <span class="comment">// 多语义查询</span></span><br><span class="line">                .documentRetriever(vectorStoreDocumentRetriever) <span class="comment">// 向量检索</span></span><br><span class="line">                .documentJoiner(concatenationDocumentJoiner) <span class="comment">// 合并文档</span></span><br><span class="line">                .documentPostProcessors(documentSelectFirst) <span class="comment">// 自定义文档后处理</span></span><br><span class="line">                .queryAugmenter(contextualQueryAugmenter) <span class="comment">// 上下文增强</span></span><br><span class="line">                .build();</span><br><span class="line">        <span class="type">String</span> <span class="variable">answer</span> <span class="operator">=</span> <span class="built_in">this</span>.chatClientBuilder.build().prompt(question)</span><br><span class="line">                .advisors(retrievalAugmentationAdvisor)</span><br><span class="line">                .call().content();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 单独检索源文档，用于前端展示</span></span><br><span class="line">        List&lt;Document&gt; similarDocuments = simpleVectorStore.similaritySearch(</span><br><span class="line">                SearchRequest.builder()</span><br><span class="line">                        .query(question)</span><br><span class="line">                        .topK(<span class="number">4</span>)</span><br><span class="line">                        .similarityThreshold(<span class="number">0.3</span>)</span><br><span class="line">                        .build());</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 构建源文档信息</span></span><br><span class="line">        List&lt;Map&lt;String, Object&gt;&gt; sources = similarDocuments.stream()</span><br><span class="line">                .map(doc -&gt; {</span><br><span class="line">                    Map&lt;String, Object&gt; sourceMap = <span class="keyword">new</span> <span class="title class_">HashMap</span>&lt;&gt;(doc.getMetadata());</span><br><span class="line">                    sourceMap.put(<span class="string">"content"</span>, doc.getText());</span><br><span class="line">                    <span class="keyword">return</span> sourceMap;</span><br><span class="line">                })</span><br><span class="line">                .collect(Collectors.toList());</span><br><span class="line"></span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">RagResponse</span>(answer, sources);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p>最后我们创建一个Controller</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.controller;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.request.RagRequest;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.response.ApiResponse;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.response.RagResponse;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.service.RagAdvancedService;</span><br><span class="line"><span class="keyword">import</span> lombok.RequiredArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.*;</span><br><span class="line"></span><br><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="meta">@RequestMapping("/api/v1/rag-advanced")</span></span><br><span class="line"><span class="meta">@RequiredArgsConstructor</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">RagAdvancedController</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> RagAdvancedService ragAdvancedService;</span><br><span class="line"></span><br><span class="line">    <span class="meta">@PostMapping("/ask")</span></span><br><span class="line">    <span class="keyword">public</span> ApiResponse&lt;RagResponse&gt; <span class="title function_">askQuestion</span><span class="params">(<span class="meta">@RequestBody</span> RagRequest request)</span> {</span><br><span class="line">        <span class="type">RagResponse</span> <span class="variable">response</span> <span class="operator">=</span> ragAdvancedService.ask(request.question());</span><br><span class="line">        <span class="keyword">return</span> ApiResponse.success(response);</span><br><span class="line">    }</span><br><span class="line">} </span><br></pre></td></tr></tbody></table></figure><h5 id="与-QuestionAnswerAdvisor-对比简表"><a href="#与-QuestionAnswerAdvisor-对比简表" class="headerlink" title="与 QuestionAnswerAdvisor 对比简表"></a>与 QuestionAnswerAdvisor 对比简表</h5><table><thead><tr><th>特性</th><th>QuestionAnswerAdvisor</th><th>RetrievalAugmentationAdvisor</th></tr></thead><tbody><tr><td>模块复杂度</td><td>✅ 简单</td><td>🧩 复杂，可插拔多个策略</td></tr><tr><td>检索配置</td><td>支持 similarity/topK</td><td>支持且额外支持 filterExpression</td></tr><tr><td>自定义 PromptTemplate</td><td>✅ 支持</td><td>✅ 支持 (通过 QueryAugmenter)</td></tr><tr><td>查询增强</td><td>❌ 不支持</td><td>✅ 支持：重写、压缩、翻译、扩展</td></tr><tr><td>检索后处理</td><td>❌ 不支持</td><td>✅ 支持：排序、去重、摘要</td></tr><tr><td>空上下文处理策略</td><td>由模板控制</td><td>✅ 默认拒答 可配置 fallback</td></tr><tr><td>推荐场景</td><td>快速上线、简单业务</td><td>复杂业务、高定制</td></tr></tbody></table><hr><h3 id="9-3-实战：构建基础-RAG-问答应用"><a href="#9-3-实战：构建基础-RAG-问答应用" class="headerlink" title="9.3 实战：构建基础 RAG 问答应用"></a>9.3 实战：构建基础 RAG 问答应用</h3><p>本节，我们将聚焦后端，搭建一个功能完备的 RAG 问答服务。我们将采用<strong>手动编排</strong>的方式来实现 RAG 流程，这种方式虽然代码稍多，但能让我们最清晰地看到 RAG 的每一步是如何运作的，并且能灵活地构造返回给前端的数据结构（包含引用来源）。</p><blockquote><p><strong>注意</strong>：本章我们暂时不深入讲解复杂文件的ETL过程，而是通过一个“数据播种”服务，在应用启动时向 <code>VectorStore</code> 中手动存入几条“种子知识”，以模拟一个已存在的知识库。完整的、能处理 PDF 等文件的 ETL 管道，我们将在第十章详细构建。</p></blockquote><h4 id="9-3-1-环境与配置"><a href="#9-3-1-环境与配置" class="headerlink" title="9.3.1 环境与配置"></a>9.3.1 环境与配置</h4><p>首先，确保我们的项目具备所有必要的“零件”。</p><ul><li><p><strong>第一步：检查核心依赖 (<code>pom.xml</code>)</strong><br>请确保您的 <code>pom.xml</code> 中已包含以下核心依赖：</p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-starter-model-zhipuai<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line"></span><br><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-starter-vector-store-redis<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line">    </span><br><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.boot<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-boot-starter-web<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-autoconfigure-model-openai<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-autoconfigure-model-chat-client<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">        <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-rag<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>第二步：确认应用配置 (<code>application.yml</code>)</strong><br>此配置继承自我们之前的章节，确保 Redis 连接指向 <code>6380</code> 端口，并正确配置了智谱 AI 的模型信息。</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">spring:</span></span><br><span class="line">  <span class="attr">data:</span></span><br><span class="line">    <span class="attr">redis:</span></span><br><span class="line">      <span class="attr">host:</span> <span class="string">localhost</span></span><br><span class="line">      <span class="attr">port:</span> <span class="number">6380</span> <span class="comment"># 使用为 AI 应用指定的 6380 端口</span></span><br><span class="line">  <span class="attr">ai:</span></span><br><span class="line">    <span class="attr">zhipuai:</span></span><br><span class="line">      <span class="attr">api-key:</span> <span class="string">${ZHIPU_API_KEY}</span> <span class="comment"># 从环境变量读取</span></span><br><span class="line">      <span class="attr">chat:</span></span><br><span class="line">        <span class="attr">options:</span></span><br><span class="line">          <span class="attr">model:</span> <span class="string">glm-4-air</span></span><br><span class="line">      <span class="attr">embedding:</span></span><br><span class="line">        <span class="attr">options:</span></span><br><span class="line">          <span class="attr">model:</span> <span class="string">embedding-3</span></span><br><span class="line">    <span class="attr">vector-store:</span></span><br><span class="line">      <span class="attr">redis:</span></span><br><span class="line">        <span class="attr">index-name:</span> <span class="string">ai-copilot-rag-index</span></span><br><span class="line">        <span class="attr">initialize-schema:</span> <span class="literal">true</span></span><br><span class="line"><span class="comment"># ... 其他配置</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>第三步：配置核心配置项(<code>RagConfig</code>)</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.config;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.SearchRequest;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.VectorStore;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Bean;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Configuration;</span><br><span class="line"></span><br><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * RAG专用配置类</span></span><br><span class="line"><span class="comment"> * 提供 QuestionAnswerAdvisor Bean 用于优雅的 RAG 实现</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"><span class="meta">@Configuration</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">RagConfig</span> {</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * QuestionAnswerAdvisor Bean - 用于 RAG 功能</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> vectorStore 向量存储实例</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 配置好的 QuestionAnswerAdvisor</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> QuestionAnswerAdvisor <span class="title function_">questionAnswerAdvisor</span><span class="params">(VectorStore vectorStore)</span> {</span><br><span class="line">        <span class="keyword">return</span> QuestionAnswerAdvisor.builder(vectorStore)</span><br><span class="line">                .searchRequest(SearchRequest.builder()</span><br><span class="line">                        .similarityThreshold(<span class="number">0.8</span>)</span><br><span class="line">                        .topK(<span class="number">4</span>)</span><br><span class="line">                        .build())</span><br><span class="line">                .build();</span><br><span class="line">    }</span><br><span class="line">} </span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="9-3-2-数据播种：手动向-VectorStore-添加知识"><a href="#9-3-2-数据播种：手动向-VectorStore-添加知识" class="headerlink" title="9.3.2 数据播种：手动向 VectorStore 添加知识"></a>9.3.2 数据播种：手动向 VectorStore 添加知识</h4><p>我们创建一个 <code>DataSeedingService</code>，在应用启动时，自动向 <code>VectorStore</code> 中添加几条简单的 <code>Document</code> 作为我们的初始知识。</p><ul><li><p><strong><code>DataSeedingService.java</code> 的实现</strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> jakarta.annotation.PostConstruct;</span><br><span class="line"><span class="keyword">import</span> lombok.RequiredArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> lombok.extern.slf4j.Slf4j;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.document.Document;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.SearchRequest;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.VectorStore;</span><br><span class="line"><span class="keyword">import</span> org.springframework.stereotype.Service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Slf4j</span></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="meta">@RequiredArgsConstructor</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">DataSeedingService</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> VectorStore vectorStore;</span><br><span class="line"></span><br><span class="line">    <span class="meta">@PostConstruct</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">seedData</span><span class="params">()</span> {</span><br><span class="line">        List&lt;Document&gt; checkResult = vectorStore.similaritySearch(</span><br><span class="line">                SearchRequest.builder().query(<span class="string">"Spring AI"</span>).topK(<span class="number">1</span>).build()</span><br><span class="line">        );</span><br><span class="line"></span><br><span class="line">    <span class="keyword">if</span> (checkResult.stream().anyMatch(doc -&gt; doc.getText().contains(<span class="string">"可移植性"</span>))) {</span><br><span class="line">            log.info(<span class="string">"知识库中已有数据，跳过播种。"</span>);</span><br><span class="line">            <span class="keyword">return</span>;</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">    log.info(<span class="string">"知识库为空，开始进行数据播种..."</span>);</span><br><span class="line"></span><br><span class="line">    List&lt;Document&gt; documents = List.of(</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Document</span>(<span class="string">"Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"</span>, Map.of(<span class="string">"source"</span>, <span class="string">"spring-ai-manual.pdf"</span>, <span class="string">"chapter"</span>, <span class="number">1</span>)),</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Document</span>(<span class="string">"TokenTextSplitter 是一个关键工具，它能根据 Token 数量将长文本智能地分割成小块，同时通过 chunkOverlap 保证语义连续性。"</span>, Map.of(<span class="string">"source"</span>, <span class="string">"spring-ai-manual.pdf"</span>, <span class="string">"chapter"</span>, <span class="number">5</span>)),</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Document</span>(<span class="string">"在 RAG 应用中，必须为 LLM 设计一个好的 Prompt 模板，明确指示它只能根据提供的上下文回答，以避免信息幻觉。"</span>, Map.of(<span class="string">"source"</span>, <span class="string">"rag-best-practices.docx"</span>, <span class="string">"chapter"</span>, <span class="number">2</span>))</span><br><span class="line">        );</span><br><span class="line"></span><br><span class="line">        vectorStore.add(documents);</span><br><span class="line">    log.info(<span class="string">"数据播种完成！"</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="9-3-3-RAG-服务与-API-实现"><a href="#9-3-3-RAG-服务与-API-实现" class="headerlink" title="9.3.3 RAG 服务与 API 实现"></a>9.3.3 RAG 服务与 API 实现</h4><p>现在，我们来构建 RAG 的问答服务和对应的 API 接口。</p><ul><li><p><strong>DTO 定义</strong> (<code>dto/request/RagRequest.java</code>, <code>dto/response/RagResponse.java</code>):</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.dto.request;</span><br><span class="line"><span class="comment">// 提问请求 DTO</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">RagRequest</span><span class="params">(String question)</span> {}</span><br></pre></td></tr></tbody></table></figure><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.dto.response;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="comment">// 回答响应 DTO</span></span><br><span class="line"><span class="comment">// 为了前端能展示引用来源，我们让它返回答案和源文档列表</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">RagResponse</span><span class="params">(String answer, List&lt;Map&lt;String, Object&gt;&gt; sources)</span> {}</span><br></pre></td></tr></tbody></table></figure></li><li><p>**<code>RagService.java</code> **:<br>我们采用最优雅的AOP注入形式，符合官网推荐的代码编辑模式</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br></pre></td><td class="code"><pre><span class="line">    <span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">import</span> com.copilot.aicopilotbackend.dto.response.RagResponse;</span><br><span class="line">    <span class="keyword">import</span> lombok.RequiredArgsConstructor;</span><br><span class="line">    <span class="keyword">import</span> lombok.extern.slf4j.Slf4j;</span><br><span class="line">    <span class="keyword">import</span> org.springframework.ai.chat.client.ChatClient;</span><br><span class="line">    <span class="keyword">import</span> org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;</span><br><span class="line">    <span class="keyword">import</span> org.springframework.ai.document.Document;</span><br><span class="line">    <span class="keyword">import</span> org.springframework.ai.vectorstore.SearchRequest;</span><br><span class="line">    <span class="keyword">import</span> org.springframework.ai.vectorstore.VectorStore;</span><br><span class="line">    <span class="keyword">import</span> org.springframework.stereotype.Service;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">import</span> java.util.HashMap;</span><br><span class="line">    <span class="keyword">import</span> java.util.List;</span><br><span class="line">    <span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Collectors;</span><br><span class="line">    </span><br><span class="line">    <span class="meta">@Slf4j</span></span><br><span class="line">    <span class="meta">@Service</span></span><br><span class="line">    <span class="meta">@RequiredArgsConstructor</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">RagService</span> {</span><br><span class="line">    </span><br><span class="line">        <span class="keyword">private</span> <span class="keyword">final</span> ChatClient.Builder chatClientBuilder;</span><br><span class="line">        <span class="keyword">private</span> <span class="keyword">final</span> VectorStore vectorStore;</span><br><span class="line">        <span class="keyword">private</span> <span class="keyword">final</span> QuestionAnswerAdvisor questionAnswerAdvisor;</span><br><span class="line">    </span><br><span class="line">        <span class="comment">/**</span></span><br><span class="line"><span class="comment">         * 优雅的 RAG 实现，使用 Spring AI Advisor 模式</span></span><br><span class="line"><span class="comment">         * <span class="doctag">@param</span> question 用户问题</span></span><br><span class="line"><span class="comment">         * <span class="doctag">@return</span> RAG 响应，包含答案和源文档</span></span><br><span class="line"><span class="comment">         */</span></span><br><span class="line">        <span class="keyword">public</span> RagResponse <span class="title function_">ask</span><span class="params">(String question)</span> {</span><br><span class="line">            log.info(<span class="string">"RAG 流程：使用 Spring AI Advisor 模式处理问题: {}"</span>, question);</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 使用 QuestionAnswerAdvisor 实现优雅的 RAG</span></span><br><span class="line">            <span class="type">String</span> <span class="variable">answer</span> <span class="operator">=</span> chatClientBuilder.build().prompt()</span><br><span class="line">                    .advisors(questionAnswerAdvisor)</span><br><span class="line">                    .user(question)</span><br><span class="line">                    .call()</span><br><span class="line">                    .content();</span><br><span class="line">            </span><br><span class="line">            log.info(<span class="string">"RAG 流程：AI 回答生成完成"</span>);</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 单独检索源文档，用于前端展示</span></span><br><span class="line">            List&lt;Document&gt; similarDocuments = vectorStore.similaritySearch(</span><br><span class="line">                    SearchRequest.builder()</span><br><span class="line">                            .query(question)</span><br><span class="line">                            .topK(<span class="number">4</span>)</span><br><span class="line">                            .similarityThreshold(<span class="number">0.8</span>)</span><br><span class="line">                            .build());</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 构建源文档信息</span></span><br><span class="line">            List&lt;Map&lt;String, Object&gt;&gt; sources = similarDocuments.stream()</span><br><span class="line">                    .map(doc -&gt; {</span><br><span class="line">                        Map&lt;String, Object&gt; sourceMap = <span class="keyword">new</span> <span class="title class_">HashMap</span>&lt;&gt;(doc.getMetadata());</span><br><span class="line">                        sourceMap.put(<span class="string">"content"</span>, doc.getText());</span><br><span class="line">                        <span class="keyword">return</span> sourceMap;</span><br><span class="line">                    })</span><br><span class="line">                    .collect(Collectors.toList());</span><br><span class="line">            </span><br><span class="line">            log.info(<span class="string">"RAG 流程：找到 {} 个相关源文档"</span>, sources.size());</span><br><span class="line">            </span><br><span class="line">            <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">RagResponse</span>(answer, sources);</span><br><span class="line">        }</span><br><span class="line">    }</span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>一个潜在的陷阱</strong>：请注意，在这里如果我们我们直接注入并使用了全局的 <code>ChatClient</code> Bean。当 RAG 问答与需要长期记忆的普通聊天在同一个会话中混合时，这可能会导致问题（历史聊天记录被错误地注入 RAG Prompt）。解决此问题的最佳实践（创建隔离的 <code>ChatClient</code> 实例），正如代码中演示的一致</p></blockquote></li><li><p><strong><code>RagController.java</code></strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.controller;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.request.RagRequest;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.response.ApiResponse;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.response.RagResponse;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.service.RagService;</span><br><span class="line"><span class="keyword">import</span> lombok.RequiredArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.PostMapping;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RequestBody;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RequestMapping;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RestController;</span><br><span class="line"></span><br><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="meta">@RequestMapping("/api/v1/rag")</span></span><br><span class="line"><span class="meta">@RequiredArgsConstructor</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">RagController</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> RagService ragService;</span><br><span class="line"></span><br><span class="line">    <span class="meta">@PostMapping("/ask")</span></span><br><span class="line">    <span class="keyword">public</span> ApiResponse&lt;RagResponse&gt; <span class="title function_">askQuestion</span><span class="params">(<span class="meta">@RequestBody</span> RagRequest request)</span> {</span><br><span class="line">        <span class="type">RagResponse</span> <span class="variable">response</span> <span class="operator">=</span> ragService.ask(request.question());</span><br><span class="line">        <span class="keyword">return</span> ApiResponse.success(response);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="9-3-4-接口测试"><a href="#9-3-4-接口测试" class="headerlink" title="9.3.4 接口测试"></a>9.3.4 接口测试</h4><p>启动应用，等待控制台输出 <code>数据播种完成！</code> 后，使用 Postman 或 curl 测试我们的 RAG API。</p><ul><li><p><strong>Request</strong>:</p><ul><li>Method: <code>POST</code></li><li>URL: <code>http://localhost:8080/api/v1/rag/ask</code></li><li>Headers: <code>Content-Type: application/json</code></li><li>Body:<figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">  <span class="attr">"question"</span><span class="punctuation">:</span> <span class="string">"介绍一下你已知的SPring知识"</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li></ul></li><li><p><strong>Expected Response</strong>:<br>你将收到一个包含精准答案和引用来源的 JSON 响应，证明 RAG 系统正在基于我们播种的知识进行回答。</p><figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">    <span class="attr">"code"</span><span class="punctuation">:</span> <span class="string">"00000"</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"message"</span><span class="punctuation">:</span> <span class="string">"成功"</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"data"</span><span class="punctuation">:</span> <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"answer"</span><span class="punctuation">:</span> <span class="string">"Spring 是一个开源的 Java 应用程序框架，它被设计用来简化企业级应用程序的开发和维护。以下是 Spring 框架的一些核心概念和特性，这些信息是基于您提供的参考资料：...."</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"sources"</span><span class="punctuation">:</span> <span class="punctuation">[</span></span><br><span class="line">            <span class="punctuation">{</span></span><br><span class="line">                <span class="attr">"distance"</span><span class="punctuation">:</span> <span class="number">0.30841315</span><span class="punctuation">,</span></span><br><span class="line">                <span class="attr">"vector_score"</span><span class="punctuation">:</span> <span class="number">0.30841315</span><span class="punctuation">,</span></span><br><span class="line">                <span class="attr">"content"</span><span class="punctuation">:</span> <span class="string">"Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"</span></span><br><span class="line">            <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">            <span class="punctuation">{</span></span><br><span class="line">                <span class="attr">"distance"</span><span class="punctuation">:</span> <span class="number">0.30841315</span><span class="punctuation">,</span></span><br><span class="line">                <span class="attr">"vector_score"</span><span class="punctuation">:</span> <span class="number">0.30841315</span><span class="punctuation">,</span></span><br><span class="line">                <span class="attr">"content"</span><span class="punctuation">:</span> <span class="string">"Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"</span></span><br><span class="line">            <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">            <span class="punctuation">{</span></span><br><span class="line">                <span class="attr">"distance"</span><span class="punctuation">:</span> <span class="number">0.30841315</span><span class="punctuation">,</span></span><br><span class="line">                <span class="attr">"vector_score"</span><span class="punctuation">:</span> <span class="number">0.30841315</span><span class="punctuation">,</span></span><br><span class="line">                <span class="attr">"content"</span><span class="punctuation">:</span> <span class="string">"Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"</span></span><br><span class="line">            <span class="punctuation">}</span></span><br><span class="line">        <span class="punctuation">]</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"timestamp"</span><span class="punctuation">:</span> <span class="string">"2025-06-29T21:36:23.5084643"</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"success"</span><span class="punctuation">:</span> <span class="literal"><span class="keyword">true</span></span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li></ul><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/22322.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/22322.html&quot;)">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/22322.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”&amp;url=https://prorise666.site/posts/22322.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java微服务篇<span class="tagsPageCount">11</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/47912.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</div></div></a></div><div class="next-post pull-right"><a href="/posts/64777.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/59358.html" title="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</div></div></a></div><div><a href="/posts/5770.html" title="SpringAI（七）：7. Embedding Models：万物皆可向量化"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（七）：7. Embedding Models：万物皆可向量化</div></div></a></div><div><a href="/posts/60609.html" title="SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南</div></div></a></div><div><a href="/posts/52289.html" title="SpringAI（三）：3. 会话核心 API 深度解析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（三）：3. 会话核心 API 深度解析</div></div></a></div><div><a href="/posts/18714.html" title="SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用</div></div></a></div><div><a href="/posts/47912.html" title="SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”",date:"2025-03-21 16:13:45",updated:"2025-07-08 13:53:46",tags:["Java微服务篇"],categories:["后端技术","Java"],content:'\n## 9. RAG 检索增强生成：AI 的“开卷考试”\n\n在人工智能的发展历程中，大语言模型（LLM）已经展现了惊人的“博闻强记”能力——它们能写诗、编码、解答常识问题，甚至模拟人类对话。然而，当面对专业领域知识或实时更新的信息时，这些模型往往会暴露其局限性：要么“一本正经地胡说八道”（产生幻觉），要么无奈承认“我不知道”。\n\n此时，**RAG (Retrieval-Augmented Generation)** 就要闪亮登场了。它并非一种新的大模型，而是一种**更聪明的、将大模型与外部知识库相结合的技术架构范式**。它旨在让 AI 从一个“博闻强记”的通才，进化成一个能够引经据典、言之有物的“博学多才”的领域专家。\n\n### 9.1 RAG：为何需要它，它又是什么？\n\n#### 9.1.1 LLM 的三大核心“痛点”\n\n要理解 RAG 的价值，我们必须先直面当前大语言模型（LLM）在企业级应用中难以回避的三大局限性：\n\n| LLM 核心痛点 | 具体描述 | 对我们 AI-Copilot 项目的影响 |\n| :--- | :--- | :--- |\n| **知识盲区** | LLM 的知识被“冻结”在它训练完成的那一刻。它对训练日期之后发生的任何事件、发布的任何新技术都一无所知。 | 如果我们想让 AI-Copilot 解答关于公司最新发布的 `v3.5` 版本框架的新特性，一个在 `v3.0` 时代训练的 LLM 将完全无法回答。 |\n| **事实幻觉 ** | 当面对其知识范围之外或不确定的问题时，LLM 倾向于“创造”听起来非常流利和自信，但事实基础却是错误的答案。 | 如果用户询问一个 API 的用法，AI 幻觉出一个不存在的参数，这可能会导致生产事故，是绝对不可接受的。 |\n| **数据隐私与领域特定性** | 公开的 LLM 无法访问我们企业内部的私有数据，如内部技术文档、项目资料、客户支持记录、财务报告等。 | AI-Copilot 无法成为一个真正懂我们公司业务、能解答内部问题的“专家”，其应用场景将大受限制。 |\n\n#### 9.1.2 RAG 的优雅解决方案\n\n**检索增强生成 (RAG)** 正是解决上述所有问题的优雅方案。\n\n它的工作流程可以完美地比喻为让 AI 从“**闭卷考试**”升级为“**指定参考资料的开卷考试**”。\n\n它包含两个核心阶段：\n\n* **1. 检索 (Retrieve):**\n    * **目标**：大海捞针，精准定位。\n    * **流程**：当用户提出问题时（例如，“如何配置 Spring Batch 的事务？”），系统并**不直接**将问题发送给 LLM。而是首先将用户问题作为**查询指令**，在我们自己构建和维护的、可信的外部知识库（即在第八章中学习的 **`VectorStore`**）中进行**相似性搜索**，找到与问题语义最相关的若干信息片段（`Relevant Documents`）。例如，它可能会找到公司内部《Spring Batch 最佳实践.pdf》的第三章第五节。\n\n* **2. 增强生成 (Augmented Generate):**\n    * **目标**：据理力争，言之有物。\n    * **流程**：系统将上一步检索到的相关信息片段作为“**上下文 (Context)**”，与用户的原始问题**一同**“喂”给 LLM，并下达一个明确的指令：“**请严格基于我提供的这些参考资料来回答这个问题**”。\n\n通过这种方式，LLM（考生）不再仅凭其固有的、可能过时的记忆来作答，而是有了我们为它精心准备的、权威且实时的参考资料。这使得它能够生成更准确、更具时效性、且能理解我们私有知识的答案，同时极大地抑制了“事实幻觉”的产生。\n\n#### 9.1.3 文档 (Document)：知识的原子化表示\n\n在 Spring AI 的世界里，`Document` 是知识的原子单位。它不仅仅是一段纯文本，而是一个结构化的对象。\n\n| `Document` 核心属性 | 解释 | 示例 |\n| :--- | :--- | :--- |\n| **id** | 文档的唯一标识符。 | `“doc_chunk_123”` |\n| **text** | **(核心)** 文档的主要文本内容。 | `"TokenTextSplitter 是一个关键工具..."` |\n| **metadata** | **(关键)** 一个存储任意键值对的 Map，用于描述文档的元信息。 | `{"source": "manual.pdf", "page": 5, "author": "Sam"}` |\n\n`metadata` 对于 RAG 系统至关重要，它使得我们可以在检索时进行精确过滤（例如，“只在 `manual.pdf` 这本书里查找”），并且在返回结果时能够告诉用户答案的来源，实现**可追溯性**。\n\n\n---\n\n### 9.2 Spring AI 实现 RAG 的核心\n\n在实际开发中，仅靠向量检索并不能直接构建出稳健的问答系统。Spring AI 提供了多个模块化组件，帮助我们快速、灵活地实现**可控、可调、可插拔的 RAG 流程**。本节将由浅入深梳理 Spring AI 中与 RAG 强相关的组件，并结合实战代码说明其意义。\n\n#### 9.2.1 RAG 的模块化构建图\n\nSpring AI 的 RAG 实现将复杂流程拆解为如下关键组件：\n\n```\nQuery (用户输入的问题)\n  ↓\nQueryTransformer (对查询进行优化：重写、翻译、压缩等，提高检索效果)\n  ↓\nDocumentRetriever (从向量数据库中检索相关文档)\n  ↓\nDocumentPostProcessor (对检索结果进行后处理：去重、排序、截断等)\n  ↓\nPromptTemplate (将用户问题和文档上下文组合为提示词，作为模型输入)\n  ↓\nLLM (大语言模型基于上下文生成回答)\n  ↓\nResponse (最终返回的答案，结合了文档与问题)\n\n```\n\n其中每一步都可以独立扩展或替换，大大增强了系统的可维护性和灵活性。\n\n---\n\n#### 9.2.2 基础组件：QuestionAnswerAdvisor\n\nSpring AI 提供的 `QuestionAnswerAdvisor` 是 RAG 的核心实现之一，适用于「标准问答」类型的 RAG 任务。\n\n适用于基于向量数据库进行知识问答，用户问题结构清晰（无上下文）且需要较高检索与回答精准度\n\n##### 工作流程：\n\n1. 使用用户问题向 `VectorStore` 执行语义检索；\n2. 将检索到的文档拼接成上下文；\n3. 注入 Prompt 模板中，引导 LLM 给出答案。\n\n##### 核心配置项（实战已使用）：\n\n```java\nQuestionAnswerAdvisor.builder(vectorStore)\n    .searchRequest(SearchRequest.builder()\n        .similarityThreshold(0.8)  // 相似度阈值\n        .topK(4)                   // 返回前 K 条\n        .build())\n    .build();\n```\n\n在我们的实战中 `RagConfig.java` 中注册了 QuestionAnswerAdvisor Bean，控制检索门槛与返回数量，配合 `ChatClient` 实现了优雅的 RAG 问答流程。\n\n---\n\n#### 9.2.3 进阶控制：自定义 PromptTemplate\n\n`QuestionAnswerAdvisor` 默认拼接的上下文格式虽然通用，但在高阶项目中，往往需要更清晰的控制逻辑（如避免幻觉、提升逻辑性）。\n\nSpring AI 允许我们通过 `.promptTemplate()` 方法注入自定义 Prompt：\n\n```java\nPromptTemplate customPromptTemplate = PromptTemplate.builder()\n    .renderer(StTemplateRenderer.builder().startDelimiterToken(\'<\').endDelimiterToken(\'>\').build())\n    .template("""\n        <query>\n\n        以下是上下文资料：\n\n        ---------------------\n        <question_answer_context>\n        ---------------------\n\n        请严格根据以上资料作答：\n        1. 如果无法回答，请说“我不知道”；\n        2. 避免使用“根据上下文...”等表述。\n        """)\n    .build();\n```\n\n\n-----\n\n#### 9.2.4 自动化方案：RetrievalAugmentationAdvisor(重点内容)\n\n`RetrievalAugmentationAdvisor` 是 Spring AI 提供的一个高度**模块化 RAG 构建器**，适用于需要丰富控制和策略组合的场景，而非简单快速上线的替代方案。\n\n##### 适用场景\n\n  * **业务复杂、流程可定制**：需要在检索前/后注入查询转换、文档处理等逻辑\n  * **需处理多语言、长对话、召回提升**：支持 `QueryTransformer`、`MultiQueryExpander` 等增强模块\n  * **要求防幻觉、策略清晰**：内置的空上下文拒答逻辑和自定义提示模板确保结果可控\n\n-----\n\n###### RetrievalAugmentationAdvisor 核心处理流程\n\n**`before` 方法（请求处理前）:**\n\n1.  **创建原始查询**：从用户输入的文本、参数和对话历史中构建一个 `Query` 对象。\n2.  **查询转换**：依次通过 `queryTransformers` 列表中的每个转换器对查询进行修改（如规范化、重写等）。\n3.  **查询扩展**：若配置了 `queryExpander`，则将转换后的查询扩展为一个或多个查询（如同义词扩展），以提升召回率。\n4.  **异步检索文档**：对每个扩展后的查询，异步调用 `documentRetriever` 检索相关文档。\n5.  **文档合并**：使用 `documentJoiner` 将所有检索到的文档合并成一个列表。\n6.  **文档后处理**：依次通过 `documentPostProcessors` 对合并后的文档进行进一步处理（如去重、排序、摘要等）。\n7.  **查询增强**：用 `queryAugmenter` 将原始查询和最终的文档结合，生成带有上下文的增强查询。\n8.  **更新请求**：用增强后的查询内容更新请求，用于后续流程。\n\n**`after` 方法（响应处理后）:**\n\n1.  将 RAG 过程中检索到的文档（`rag_document_context`）添加到最终响应的元数据中，便于溯源和调试。\n\n-----\n\n##### 核心组件接口与实现详解\n\n###### 1\\. 查询对象 (Query)\n\n用于在 RAG 流程中承载查询信息的标准类。\n\n  * `String text`：查询的文本内容。\n  * `List<Message> history`：当前查询相关的对话历史记录。\n  * `Map<String, Object> context`：查询的上下文信息，用于存储与查询相关的额外数据。\n\n```java\npackage org.springframework.ai.rag;\n\nimport java.util.List;\nimport java.util.Map;\nimport org.springframework.ai.chat.messages.Message;\nimport org.springframework.util.Assert;\n\npublic record Query(String text, List<Message> history, Map<String, Object> context) {\n\n    public Query {\n        Assert.hasText(text, "text cannot be null or empty");\n        Assert.notNull(history, "history cannot be null");\n        Assert.noNullElements(history, "history elements cannot be null");\n        Assert.notNull(context, "context cannot be null");\n        Assert.noNullElements(context.keySet(), "context keys cannot be null");\n    }\n\n    public Query(String text) {\n        this(text, List.of(), Map.of());\n    }\n    // ... Builder and other methods\n}\n```\n\n###### 2\\. 检索前处理 (Pre-Retrieval)\n\n**a. 查询转换 (QueryTransformer)**\n\n此接口用于对原始查询进行变换，以应对查询结构不佳、术语歧义、语言不同等问题。\n\n  * **`RewriteQueryTransformer`**: 通过 LLM 优化查询，使其更简洁、精确，以便在目标系统（如向量数据库）中获得更好的结果。适用于用户查询冗长、模糊的场景。\n  * **`CompressionQueryTransformer`**: 将对话历史和后续问题合并为一个独立的、包含完整上下文的查询。适用于多轮对话场景。\n  * **`TranslationQueryTransformer`**: 将用户查询翻译为目标语言。适用于嵌入模型仅支持特定语言，而用户查询语言多样的场景。\n\n**b. 查询扩展 (QueryExpander)**\n\n此接口用于将单个查询扩展为多个语义上多样化的查询变体，以提升召回率。\n\n  * **`MultiQueryExpander`**: 使用 LLM 将单个查询生成多个语义相近但角度不同的查询，从不同方面覆盖原始查询意图，增加检索到相关结果的机会。\n\n| Transformer 类型              | 功能描述                     | 适用场景       | 示例                               |\n| ----------------------------- | ---------------------------- | -------------- | ---------------------------------- |\n| `RewriteQueryTransformer`     | 重写用户问题，使之更利于搜索 | 复杂长问题     | “我最近在学机器学习，什么是 LLM？” |\n| `CompressionQueryTransformer` | 将上下文对话压缩成独立查询   | 长对话、追问   | “它的第二大城市是哪？”             |\n| `TranslationQueryTransformer` | 将用户问题翻译为嵌入支持语言 | 多语言用户输入 | “Hvad er Danmarks hovedstad?”      |\n| `MultiQueryExpander`          | 扩展成多个语义变体提高召回率 | 召回率优先     | “如何运行 Spring Boot 项目？”      |\n\n```java\n// 1. Pre-Retrieval: 查询扩展与转换\n// 通过 LLM 优化查询，使其更简洁、精确，以便在目标系统（如向量数据库）中获得更好的结果\nRewriteQueryTransformer rewriteQueryTransformer = RewriteQueryTransformer.builder()\n        .chatClientBuilder(this.chatClientBuilder).build();\n\n// 将对话历史和后续问题合并为一个独立的、包含完整上下文的查询。适用于多轮对话场景\nCompressionQueryTransformer compressionQueryTransformer = CompressionQueryTransformer.builder()\n        .chatClientBuilder(this.chatClientBuilder).build();\n\n// 将用户查询翻译为目标语言。适用于嵌入模型仅支持特定语言，而用户查询语言多样的场景\nTranslationQueryTransformer translationQueryTransformer = TranslationQueryTransformer.builder()\n        .chatClientBuilder(this.chatClientBuilder).targetLanguage("English").build();\n\n// 使用 LLM 将单个查询生成多个语义相近但角度不同的查询，从不同方面覆盖原始查询意图，增加检索到相关结果的机会。\nMultiQueryExpander multiQueryExpander = MultiQueryExpander.builder()\n        .chatClientBuilder(this.chatClientBuilder).build();\n```\n\n###### 3\\. 检索 (Retrieval)\n\n**a. 文档检索器 (DocumentRetriever)**\n\n此接口定义了从数据源检索文档的标准方法。\n\n  * **`VectorStoreDocumentRetriever`**: 核心实现之一，用于从 `VectorStore` 中检索与输入查询语义相似的文档。它支持相似度阈值 (`similarityThreshold`)、返回数量 (`topK`) 和元数据过滤 (`filterExpression`)。\n\n```java\n// 2. Retrieval: 向量检索与结果合并\n// 用于从 VectorStore 中检索与输入查询语义相似的文档。\n// 它支持相似度阈值\n// 返回数量 (`topK`) 和元数据过滤 (`filterExpression`)。\nVectorStoreDocumentRetriever vectorStoreDocumentRetriever = VectorStoreDocumentRetriever.builder()\n        .vectorStore(simpleVectorStore)\n        .topK(4) // 返回相近的4个文档\n        // 如果一个文档里面出现了一些复杂查询，比如\n        // 查询2025年版本最新的XX公司文档,那么这里的表达式可以写为：\n        // .filterExpression("metadata.year >= 2025")\n        .build();\n```\n\n**b. 文档合并器 (DocumentJoiner)**\n\n此接口用于将多个来源（如多个扩展查询的结果）的文档集合并为一个列表。\n\n  * **`ConcatenationDocumentJoiner`**: 默认实现，它将所有文档列表连接起来，并通过文档ID进行去重，最后根据文档分数（`score`）降序排列。\n\n```java\nConcatenationDocumentJoiner concatenationDocumentJoiner = new ConcatenationDocumentJoiner();\n```\n\n###### 4\\. 检索后处理 (Post-Retrieval)\n\n**a. 文档后处理器 (DocumentPostProcessor)**\n\n在文档检索完成后、注入到最终提示词之前，对此接口的实现可以对文档列表进行排序、过滤、重排（Re-ranking）、压缩等操作，以优化提供给大模型的上下文质量，一般来他适合单独被抽离出去作为一个单独控制的代码，方便管理\n\n>这里以最简单的，挑选第一个文档的排序方式来做演示，在这一层，如果需要定义话复杂需求，即可操作传入进来的Document对象或是query对象进行复杂的重排/过滤\n\n```java\npackage com.copilot.aicopilotbackend.service;\n\nimport org.springframework.ai.document.Document;\nimport org.springframework.ai.rag.Query;\nimport org.springframework.ai.rag.postretrieval.document.DocumentPostProcessor;\nimport java.util.Collections;\nimport java.util.List;\n\n/**\n * 文档选择器 - 只选择第一个文档\n * \n * 实现了 DocumentPostProcessor 接口，用于在 RAG 流水线中对检索到的文档进行后处理。\n * 该实现只返回检索结果中的第一个文档，用于限制上下文长度或选择最相关的文档。\n */\npublic class DocumentSelectFirst implements DocumentPostProcessor {\n    \n    /**\n     * 处理文档列表，只返回第一个文档\n     * \n     * @param query 查询对象\n     * @param documents 检索到的文档列表\n     * @return 只包含第一个文档的列表\n     */\n    @Override\n    public List<Document> process(Query query, List<Document> documents) {\n        // 只返回第一个文档，通常是相似度最高的文档\n        return Collections.singletonList(documents.get(0));\n    }\n}\n```\n\n###### 5\\. 生成 (Generation)\n\n**a. 查询增强器 (QueryAugmenter)**\n\n此接口负责将检索并处理后的文档（上下文）与原始用户查询结合，生成最终发送给大模型的提示（Prompt）。\n\n  * **`ContextualQueryAugmenter`**: 默认实现，它使用一个提示模板将文档内容和用户问题组装起来。\n    \n      * **空上下文策略**：通过 `allowEmptyContext(boolean)` 控制。如果为 `false` (默认) 且检索结果为空，它会使用一个特定的 `emptyContextPromptTemplate` 来生成一个“无法回答”的响应，有效防止模型在没有信息时产生幻觉。如果为 `true`，则直接返回原始查询。\n      \n      ```java\n      // 4. Generation: 上下文增强\n      ContextualQueryAugmenter contextualQueryAugmenter = ContextualQueryAugmenter.builder()\n              .allowEmptyContext(true).build();\n      ```\n\n-----\n\n##### 完整配置与模块化代码示例\n\n###### 1\\. pom.xml 核心依赖\n\n```xml\n        <dependency>\n            <groupId>org.springframework.ai</groupId>\n            <artifactId>spring-ai-starter-model-zhipuai</artifactId>\n        </dependency>\n\n        <dependency>\n            <groupId>org.springframework.ai</groupId>\n            <artifactId>spring-ai-starter-vector-store-redis</artifactId>\n        </dependency>\n\n    <dependency>\n            <groupId>org.springframework.boot</groupId>\n            <artifactId>spring-boot-starter-web</artifactId>\n        </dependency>\n        <dependency>\n            <groupId>org.springframework.ai</groupId>\n            <artifactId>spring-ai-autoconfigure-model-openai</artifactId>\n        </dependency>\n        <dependency>\n            <groupId>org.springframework.ai</groupId>\n            <artifactId>spring-ai-autoconfigure-model-chat-client</artifactId>\n        </dependency>\n        <dependency>\n            <groupId>org.springframework.ai</groupId>\n            <artifactId>spring-ai-rag</artifactId>\n        </dependency>\n```\n\n###### 2\\. application.yml 最小配置\n\n```yaml\nspring:\n  data:\n    redis:\n      host: localhost\n      port: 6380 # 使用为 AI 应用指定的 6380 端口\n  ai:\n    zhipuai:\n      api-key: ${ZHIPU_API_KEY} # 从环境变量读取\n      chat:\n        options:\n          model: glm-4-air\n      embedding:\n        options:\n          model: embedding-3\n    vector-store:\n      redis:\n        index-name: ai-copilot-rag-index\n        initialize-schema: true\n# ... 其他配置\n```\n\n###### 3\\. 模块化 RAG 控制器示例 (RagModuleController)\n\n这个示例清晰地展示了如何独立构建并组装 `RetrievalAugmentationAdvisor` 的各个模块化组件。\n\n新建一个基于内存的向量数据库作为测试：\n\n```java\npackage com.copilot.aicopilotbackend.config;\n\nimport org.springframework.ai.embedding.EmbeddingModel;\nimport org.springframework.ai.vectorstore.SimpleVectorStore;\nimport org.springframework.ai.vectorstore.VectorStore;\nimport org.springframework.context.annotation.Bean;\nimport org.springframework.context.annotation.Configuration;\n\n@Configuration\npublic class VectorStoreConfig {\n\n    @Bean\n    public SimpleVectorStore simpleVectorStore(EmbeddingModel embeddingModel) {\n        return SimpleVectorStore.builder(embeddingModel).build();\n    }\n\n}\n\n```\n\n新增`DocumentSelectFirst`\n\n```java\npackage com.copilot.aicopilotbackend.service;\n\nimport org.springframework.ai.document.Document;\nimport org.springframework.ai.rag.Query;\nimport org.springframework.ai.rag.postretrieval.document.DocumentPostProcessor;\nimport java.util.Collections;\nimport java.util.List;\n\npublic class DocumentSelectFirst implements DocumentPostProcessor {\n    @Override\n    public List<Document> process(Query query, List<Document> documents) {\n        return Collections.singletonList(documents.get(0));\n    }\n}\n```\n\n实现`RagAdvancedService`\n\n```java\npackage com.copilot.aicopilotbackend.service;\n\nimport com.copilot.aicopilotbackend.dto.response.RagResponse;\nimport lombok.extern.slf4j.Slf4j;\nimport org.springframework.ai.chat.client.ChatClient;\nimport org.springframework.ai.chat.prompt.PromptTemplate;\nimport org.springframework.ai.document.Document;\nimport org.springframework.ai.embedding.EmbeddingModel;\nimport org.springframework.ai.rag.advisor.RetrievalAugmentationAdvisor;\nimport org.springframework.ai.rag.generation.augmentation.ContextualQueryAugmenter;\nimport org.springframework.ai.rag.preretrieval.query.expansion.MultiQueryExpander;\nimport org.springframework.ai.rag.preretrieval.query.transformation.CompressionQueryTransformer;\nimport org.springframework.ai.rag.preretrieval.query.transformation.RewriteQueryTransformer;\nimport org.springframework.ai.rag.preretrieval.query.transformation.TranslationQueryTransformer;\nimport org.springframework.ai.rag.retrieval.join.ConcatenationDocumentJoiner;\nimport org.springframework.ai.rag.retrieval.search.VectorStoreDocumentRetriever;\nimport org.springframework.ai.vectorstore.SearchRequest;\nimport org.springframework.ai.vectorstore.SimpleVectorStore;\nimport org.springframework.stereotype.Service;\n\nimport java.util.HashMap;\nimport java.util.List;\nimport java.util.Map;\nimport java.util.stream.Collectors;\n\n@Service\n@Slf4j\npublic class RagAdvancedService {\n    private final SimpleVectorStore simpleVectorStore;\n    private final ChatClient.Builder chatClientBuilder;\n\n    public RagAdvancedService(EmbeddingModel embeddingModel, ChatClient.Builder builder) {\n        this.simpleVectorStore = SimpleVectorStore.builder(embeddingModel).build();\n        // 初始化数据...\n        List<Document> documents = List.of(\n                new Document("你的姓名是Prorise，是一个全栈工程师，25年硕士毕业"),\n                new Document("你的姓名是Prorise，专业领域包含的数学、前后端、设计、自然语言处理..."),\n                new Document("你姓名是Prorise，爱好是发呆、思考、运动...")\n        );\n        simpleVectorStore.add(documents);\n        this.chatClientBuilder = builder;\n    }\n\n    public RagResponse ask(String question) {\n        log.info("与 RAG 顾问开始聊天");\n\n        // 1. Pre-Retrieval: 查询扩展与转换\n        // 通过 LLM 优化查询，使其更简洁、精确，以便在目标系统（如向量数据库）中获得更好的结果\n        RewriteQueryTransformer rewriteQueryTransformer = RewriteQueryTransformer.builder()\n                .chatClientBuilder(this.chatClientBuilder).build();\n\n        // 将对话历史和后续问题合并为一个独立的、包含完整上下文的查询。适用于多轮对话场景\n        CompressionQueryTransformer compressionQueryTransformer = CompressionQueryTransformer.builder()\n                .chatClientBuilder(this.chatClientBuilder).build();\n\n        // 将用户查询翻译为目标语言。适用于嵌入模型仅支持特定语言，而用户查询语言多样的场景\n        TranslationQueryTransformer translationQueryTransformer = TranslationQueryTransformer.builder()\n                .chatClientBuilder(this.chatClientBuilder).targetLanguage("English").build();\n\n        // 使用 LLM 将单个查询生成多个语义相近但角度不同的查询，从不同方面覆盖原始查询意图，增加检索到相关结果的机会。\n        MultiQueryExpander multiQueryExpander = MultiQueryExpander.builder()\n                .chatClientBuilder(this.chatClientBuilder).build();\n\n\n        // 2. Retrieval: 向量检索与结果合并\n        // 用于从 VectorStore 中检索与输入查询语义相似的文档。\n        // 它支持相似度阈值\n        // 返回数量 (`topK`) 和元数据过滤 (`filterExpression`)。\n        VectorStoreDocumentRetriever vectorStoreDocumentRetriever = VectorStoreDocumentRetriever.builder()\n                .vectorStore(simpleVectorStore)\n                // .topK(4) // 返回相近的4个文档\n                // 如果一个文档里面出现了一些复杂查询，比如\n                // 查询2025年版本最新的XX公司文档,那么这里的表达式可以写为：\n                // .filterExpression("metadata.year >= 2025")\n                .build();\n\n\n        ConcatenationDocumentJoiner concatenationDocumentJoiner = new ConcatenationDocumentJoiner();\n\n        // 3. Post-Retrieval: 自定义文档后处理\n        DocumentSelectFirst documentSelectFirst = new DocumentSelectFirst(); // 假设实现为只取第一个文档\n\n\n        // 构建 ContextualQueryAugmenter\n        ContextualQueryAugmenter contextualQueryAugmenter = ContextualQueryAugmenter.builder()\n                .allowEmptyContext(false)\n                .build();\n\n        // 组装 Advisor\n        RetrievalAugmentationAdvisor retrievalAugmentationAdvisor = RetrievalAugmentationAdvisor.builder()\n                .queryTransformers(rewriteQueryTransformer) // 优化查询\n                .queryTransformers(compressionQueryTransformer) // 多轮对话查询\n                .queryTransformers(translationQueryTransformer) // 翻译查询\n                .queryExpander(multiQueryExpander) // 多语义查询\n                .documentRetriever(vectorStoreDocumentRetriever) // 向量检索\n                .documentJoiner(concatenationDocumentJoiner) // 合并文档\n                .documentPostProcessors(documentSelectFirst) // 自定义文档后处理\n                .queryAugmenter(contextualQueryAugmenter) // 上下文增强\n                .build();\n        String answer = this.chatClientBuilder.build().prompt(question)\n                .advisors(retrievalAugmentationAdvisor)\n                .call().content();\n\n        // 单独检索源文档，用于前端展示\n        List<Document> similarDocuments = simpleVectorStore.similaritySearch(\n                SearchRequest.builder()\n                        .query(question)\n                        .topK(4)\n                        .similarityThreshold(0.3)\n                        .build());\n\n        // 构建源文档信息\n        List<Map<String, Object>> sources = similarDocuments.stream()\n                .map(doc -> {\n                    Map<String, Object> sourceMap = new HashMap<>(doc.getMetadata());\n                    sourceMap.put("content", doc.getText());\n                    return sourceMap;\n                })\n                .collect(Collectors.toList());\n\n        return new RagResponse(answer, sources);\n    }\n\n}\n```\n最后我们创建一个Controller\n\n```java\npackage com.copilot.aicopilotbackend.controller;\n\nimport com.copilot.aicopilotbackend.dto.request.RagRequest;\nimport com.copilot.aicopilotbackend.dto.response.ApiResponse;\nimport com.copilot.aicopilotbackend.dto.response.RagResponse;\nimport com.copilot.aicopilotbackend.service.RagAdvancedService;\nimport lombok.RequiredArgsConstructor;\nimport org.springframework.web.bind.annotation.*;\n\n@RestController\n@RequestMapping("/api/v1/rag-advanced")\n@RequiredArgsConstructor\npublic class RagAdvancedController {\n\n    private final RagAdvancedService ragAdvancedService;\n\n    @PostMapping("/ask")\n    public ApiResponse<RagResponse> askQuestion(@RequestBody RagRequest request) {\n        RagResponse response = ragAdvancedService.ask(request.question());\n        return ApiResponse.success(response);\n    }\n} \n```\n\n##### 与 QuestionAnswerAdvisor 对比简表\n\n| 特性                  | QuestionAnswerAdvisor | RetrievalAugmentationAdvisor    |\n| --------------------- | --------------------- | ------------------------------- |\n| 模块复杂度            | ✅ 简单                | 🧩 复杂，可插拔多个策略          |\n| 检索配置              | 支持 similarity/topK  | 支持且额外支持 filterExpression |\n| 自定义 PromptTemplate | ✅ 支持                | ✅ 支持 (通过 QueryAugmenter)    |\n| 查询增强              | ❌ 不支持              | ✅ 支持：重写、压缩、翻译、扩展  |\n| 检索后处理            | ❌ 不支持              | ✅ 支持：排序、去重、摘要        |\n| 空上下文处理策略      | 由模板控制            | ✅ 默认拒答 可配置 fallback      |\n| 推荐场景              | 快速上线、简单业务    | 复杂业务、高定制                |\n\n---\n\n\n\n### 9.3 实战：构建基础 RAG 问答应用\n\n本节，我们将聚焦后端，搭建一个功能完备的 RAG 问答服务。我们将采用**手动编排**的方式来实现 RAG 流程，这种方式虽然代码稍多，但能让我们最清晰地看到 RAG 的每一步是如何运作的，并且能灵活地构造返回给前端的数据结构（包含引用来源）。\n\n> **注意**：本章我们暂时不深入讲解复杂文件的ETL过程，而是通过一个“数据播种”服务，在应用启动时向 `VectorStore` 中手动存入几条“种子知识”，以模拟一个已存在的知识库。完整的、能处理 PDF 等文件的 ETL 管道，我们将在第十章详细构建。\n\n#### 9.3.1 环境与配置\n\n首先，确保我们的项目具备所有必要的“零件”。\n\n  * **第一步：检查核心依赖 (`pom.xml`)**\n    请确保您的 `pom.xml` 中已包含以下核心依赖：\n\n    ```xml\n            <dependency>\n                <groupId>org.springframework.ai</groupId>\n                <artifactId>spring-ai-starter-model-zhipuai</artifactId>\n            </dependency>\n\n            <dependency>\n                <groupId>org.springframework.ai</groupId>\n                <artifactId>spring-ai-starter-vector-store-redis</artifactId>\n            </dependency>\n    \n        <dependency>\n                <groupId>org.springframework.boot</groupId>\n                <artifactId>spring-boot-starter-web</artifactId>\n            </dependency>\n            <dependency>\n                <groupId>org.springframework.ai</groupId>\n                <artifactId>spring-ai-autoconfigure-model-openai</artifactId>\n            </dependency>\n            <dependency>\n                <groupId>org.springframework.ai</groupId>\n                <artifactId>spring-ai-autoconfigure-model-chat-client</artifactId>\n            </dependency>\n            <dependency>\n                <groupId>org.springframework.ai</groupId>\n                <artifactId>spring-ai-rag</artifactId>\n            </dependency>\n    ```\n    \n  * **第二步：确认应用配置 (`application.yml`)**\n    此配置继承自我们之前的章节，确保 Redis 连接指向 `6380` 端口，并正确配置了智谱 AI 的模型信息。\n\n    ```yaml\n    spring:\n      data:\n        redis:\n          host: localhost\n          port: 6380 # 使用为 AI 应用指定的 6380 端口\n      ai:\n        zhipuai:\n          api-key: ${ZHIPU_API_KEY} # 从环境变量读取\n          chat:\n            options:\n              model: glm-4-air\n          embedding:\n            options:\n              model: embedding-3\n        vector-store:\n          redis:\n            index-name: ai-copilot-rag-index\n            initialize-schema: true\n    # ... 其他配置\n    ```\n    \n* **第三步：配置核心配置项(`RagConfig`)**\n\n  ```java\n  package com.copilot.aicopilotbackend.config;\n  import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;\n  import org.springframework.ai.vectorstore.SearchRequest;\n  import org.springframework.ai.vectorstore.VectorStore;\n  import org.springframework.context.annotation.Bean;\n  import org.springframework.context.annotation.Configuration;\n  \n  /**\n   * RAG专用配置类\n   * 提供 QuestionAnswerAdvisor Bean 用于优雅的 RAG 实现\n   */\n  @Configuration\n  public class RagConfig {\n  \n      /**\n       * QuestionAnswerAdvisor Bean - 用于 RAG 功能\n       * @param vectorStore 向量存储实例\n       * @return 配置好的 QuestionAnswerAdvisor\n       */\n      @Bean\n      public QuestionAnswerAdvisor questionAnswerAdvisor(VectorStore vectorStore) {\n          return QuestionAnswerAdvisor.builder(vectorStore)\n                  .searchRequest(SearchRequest.builder()\n                          .similarityThreshold(0.8)\n                          .topK(4)\n                          .build())\n                  .build();\n      }\n  } \n  ```\n\n  \n\n#### 9.3.2 数据播种：手动向 VectorStore 添加知识\n\n我们创建一个 `DataSeedingService`，在应用启动时，自动向 `VectorStore` 中添加几条简单的 `Document` 作为我们的初始知识。\n\n  * **`DataSeedingService.java` 的实现**:\n\n    ```java\n    package com.copilot.aicopilotbackend.service;\n\n    import jakarta.annotation.PostConstruct;\n    import lombok.RequiredArgsConstructor;\n    import lombok.extern.slf4j.Slf4j;\n    import org.springframework.ai.document.Document;\n    import org.springframework.ai.vectorstore.SearchRequest;\n    import org.springframework.ai.vectorstore.VectorStore;\n    import org.springframework.stereotype.Service;\n\n    import java.util.List;\n    import java.util.Map;\n\n    @Slf4j\n    @Service\n    @RequiredArgsConstructor\n    public class DataSeedingService {\n\n        private final VectorStore vectorStore;\n\n        @PostConstruct\n        public void seedData() {\n            List<Document> checkResult = vectorStore.similaritySearch(\n                    SearchRequest.builder().query("Spring AI").topK(1).build()\n            );\n    \n        if (checkResult.stream().anyMatch(doc -> doc.getText().contains("可移植性"))) {\n                log.info("知识库中已有数据，跳过播种。");\n                return;\n            }\n    \n        log.info("知识库为空，开始进行数据播种...");\n    \n        List<Document> documents = List.of(\n                    new Document("Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。", Map.of("source", "spring-ai-manual.pdf", "chapter", 1)),\n                    new Document("TokenTextSplitter 是一个关键工具，它能根据 Token 数量将长文本智能地分割成小块，同时通过 chunkOverlap 保证语义连续性。", Map.of("source", "spring-ai-manual.pdf", "chapter", 5)),\n                    new Document("在 RAG 应用中，必须为 LLM 设计一个好的 Prompt 模板，明确指示它只能根据提供的上下文回答，以避免信息幻觉。", Map.of("source", "rag-best-practices.docx", "chapter", 2))\n            );\n    \n            vectorStore.add(documents);\n        log.info("数据播种完成！");\n        }\n    }\n    ```\n\n#### 9.3.3 RAG 服务与 API 实现\n\n现在，我们来构建 RAG 的问答服务和对应的 API 接口。\n\n  * **DTO 定义** (`dto/request/RagRequest.java`, `dto/response/RagResponse.java`):\n\n    ```java\n    package com.copilot.aicopilotbackend.dto.request;\n    // 提问请求 DTO\n    public record RagRequest(String question) {}\n    ```\n\n    ```java\n    package com.copilot.aicopilotbackend.dto.response;\n    import java.util.List;\n    import java.util.Map;\n    // 回答响应 DTO\n    // 为了前端能展示引用来源，我们让它返回答案和源文档列表\n    public record RagResponse(String answer, List<Map<String, Object>> sources) {}\n    ```\n\n  * **`RagService.java` **:\n    我们采用最优雅的AOP注入形式，符合官网推荐的代码编辑模式\n\n    ```java\n    package com.copilot.aicopilotbackend.service;\n\n    import com.copilot.aicopilotbackend.dto.response.RagResponse;\n    import lombok.RequiredArgsConstructor;\n    import lombok.extern.slf4j.Slf4j;\n    import org.springframework.ai.chat.client.ChatClient;\n    import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;\n    import org.springframework.ai.document.Document;\n    import org.springframework.ai.vectorstore.SearchRequest;\n    import org.springframework.ai.vectorstore.VectorStore;\n    import org.springframework.stereotype.Service;\n\n    import java.util.HashMap;\n    import java.util.List;\n    import java.util.Map;\nimport java.util.stream.Collectors;\n    \n    @Slf4j\n    @Service\n    @RequiredArgsConstructor\n    public class RagService {\n    \n        private final ChatClient.Builder chatClientBuilder;\n        private final VectorStore vectorStore;\n        private final QuestionAnswerAdvisor questionAnswerAdvisor;\n    \n        /**\n         * 优雅的 RAG 实现，使用 Spring AI Advisor 模式\n         * @param question 用户问题\n         * @return RAG 响应，包含答案和源文档\n         */\n        public RagResponse ask(String question) {\n            log.info("RAG 流程：使用 Spring AI Advisor 模式处理问题: {}", question);\n            \n            // 使用 QuestionAnswerAdvisor 实现优雅的 RAG\n            String answer = chatClientBuilder.build().prompt()\n                    .advisors(questionAnswerAdvisor)\n                    .user(question)\n                    .call()\n                    .content();\n            \n            log.info("RAG 流程：AI 回答生成完成");\n            \n            // 单独检索源文档，用于前端展示\n            List<Document> similarDocuments = vectorStore.similaritySearch(\n                    SearchRequest.builder()\n                            .query(question)\n                            .topK(4)\n                            .similarityThreshold(0.8)\n                            .build());\n            \n            // 构建源文档信息\n            List<Map<String, Object>> sources = similarDocuments.stream()\n                    .map(doc -> {\n                        Map<String, Object> sourceMap = new HashMap<>(doc.getMetadata());\n                        sourceMap.put("content", doc.getText());\n                        return sourceMap;\n                    })\n                    .collect(Collectors.toList());\n            \n            log.info("RAG 流程：找到 {} 个相关源文档", sources.size());\n            \n            return new RagResponse(answer, sources);\n        }\n    }\n    ```\n    \n    > **一个潜在的陷阱**：请注意，在这里如果我们我们直接注入并使用了全局的 `ChatClient` Bean。当 RAG 问答与需要长期记忆的普通聊天在同一个会话中混合时，这可能会导致问题（历史聊天记录被错误地注入 RAG Prompt）。解决此问题的最佳实践（创建隔离的 `ChatClient` 实例），正如代码中演示的一致\n    \n  * **`RagController.java`**:\n\n    ```java\n    package com.copilot.aicopilotbackend.controller;\n\n    import com.copilot.aicopilotbackend.dto.request.RagRequest;\n    import com.copilot.aicopilotbackend.dto.response.ApiResponse;\n    import com.copilot.aicopilotbackend.dto.response.RagResponse;\n    import com.copilot.aicopilotbackend.service.RagService;\n    import lombok.RequiredArgsConstructor;\n    import org.springframework.web.bind.annotation.PostMapping;\n    import org.springframework.web.bind.annotation.RequestBody;\n    import org.springframework.web.bind.annotation.RequestMapping;\n    import org.springframework.web.bind.annotation.RestController;\n\n    @RestController\n    @RequestMapping("/api/v1/rag")\n    @RequiredArgsConstructor\n    public class RagController {\n\n        private final RagService ragService;\n\n        @PostMapping("/ask")\n        public ApiResponse<RagResponse> askQuestion(@RequestBody RagRequest request) {\n            RagResponse response = ragService.ask(request.question());\n            return ApiResponse.success(response);\n        }\n    }\n    ```\n\n#### 9.3.4 接口测试\n\n启动应用，等待控制台输出 `数据播种完成！` 后，使用 Postman 或 curl 测试我们的 RAG API。\n\n  * **Request**:\n\n      * Method: `POST`\n      * URL: `http://localhost:8080/api/v1/rag/ask`\n      * Headers: `Content-Type: application/json`\n      * Body:\n        ```json\n        {\n          "question": "介绍一下你已知的SPring知识"\n        }\n        ```\n\n  * **Expected Response**:\n    你将收到一个包含精准答案和引用来源的 JSON 响应，证明 RAG 系统正在基于我们播种的知识进行回答。\n\n    ```json\n    {\n        "code": "00000",\n        "message": "成功",\n        "data": {\n            "answer": "Spring 是一个开源的 Java 应用程序框架，它被设计用来简化企业级应用程序的开发和维护。以下是 Spring 框架的一些核心概念和特性，这些信息是基于您提供的参考资料：....",\n            "sources": [\n                {\n                    "distance": 0.30841315,\n                    "vector_score": 0.30841315,\n                    "content": "Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"\n                },\n                {\n                    "distance": 0.30841315,\n                    "vector_score": 0.30841315,\n                    "content": "Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"\n                },\n                {\n                    "distance": 0.30841315,\n                    "vector_score": 0.30841315,\n                    "content": "Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"\n                }\n            ]\n        },\n        "timestamp": "2025-06-29T21:36:23.5084643",\n        "success": true\n    }\n    ```\n\n\n\n-----'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#9-RAG-%E6%A3%80%E7%B4%A2%E5%A2%9E%E5%BC%BA%E7%94%9F%E6%88%90%EF%BC%9AAI-%E7%9A%84%E2%80%9C%E5%BC%80%E5%8D%B7%E8%80%83%E8%AF%95%E2%80%9D"><span class="toc-number">1.</span> <span class="toc-text">9. RAG 检索增强生成：AI 的“开卷考试”</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-RAG%EF%BC%9A%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81%E5%AE%83%EF%BC%8C%E5%AE%83%E5%8F%88%E6%98%AF%E4%BB%80%E4%B9%88%EF%BC%9F"><span class="toc-number">1.1.</span> <span class="toc-text">9.1 RAG：为何需要它，它又是什么？</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#9-1-1-LLM-%E7%9A%84%E4%B8%89%E5%A4%A7%E6%A0%B8%E5%BF%83%E2%80%9C%E7%97%9B%E7%82%B9%E2%80%9D"><span class="toc-number">1.1.1.</span> <span class="toc-text">9.1.1 LLM 的三大核心“痛点”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-1-2-RAG-%E7%9A%84%E4%BC%98%E9%9B%85%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88"><span class="toc-number">1.1.2.</span> <span class="toc-text">9.1.2 RAG 的优雅解决方案</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-1-3-%E6%96%87%E6%A1%A3-Document-%EF%BC%9A%E7%9F%A5%E8%AF%86%E7%9A%84%E5%8E%9F%E5%AD%90%E5%8C%96%E8%A1%A8%E7%A4%BA"><span class="toc-number">1.1.3.</span> <span class="toc-text">9.1.3 文档 (Document)：知识的原子化表示</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-Spring-AI-%E5%AE%9E%E7%8E%B0-RAG-%E7%9A%84%E6%A0%B8%E5%BF%83"><span class="toc-number">1.2.</span> <span class="toc-text">9.2 Spring AI 实现 RAG 的核心</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#9-2-1-RAG-%E7%9A%84%E6%A8%A1%E5%9D%97%E5%8C%96%E6%9E%84%E5%BB%BA%E5%9B%BE"><span class="toc-number">1.2.1.</span> <span class="toc-text">9.2.1 RAG 的模块化构建图</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-2-2-%E5%9F%BA%E7%A1%80%E7%BB%84%E4%BB%B6%EF%BC%9AQuestionAnswerAdvisor"><span class="toc-number">1.2.2.</span> <span class="toc-text">9.2.2 基础组件：QuestionAnswerAdvisor</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B7%A5%E4%BD%9C%E6%B5%81%E7%A8%8B%EF%BC%9A"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">工作流程：</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E9%85%8D%E7%BD%AE%E9%A1%B9%EF%BC%88%E5%AE%9E%E6%88%98%E5%B7%B2%E4%BD%BF%E7%94%A8%EF%BC%89%EF%BC%9A"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">核心配置项（实战已使用）：</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-2-3-%E8%BF%9B%E9%98%B6%E6%8E%A7%E5%88%B6%EF%BC%9A%E8%87%AA%E5%AE%9A%E4%B9%89-PromptTemplate"><span class="toc-number">1.2.3.</span> <span class="toc-text">9.2.3 进阶控制：自定义 PromptTemplate</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-2-4-%E8%87%AA%E5%8A%A8%E5%8C%96%E6%96%B9%E6%A1%88%EF%BC%9ARetrievalAugmentationAdvisor-%E9%87%8D%E7%82%B9%E5%86%85%E5%AE%B9"><span class="toc-number">1.2.4.</span> <span class="toc-text">9.2.4 自动化方案：RetrievalAugmentationAdvisor(重点内容)</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%80%82%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.2.4.1.</span> <span class="toc-text">适用场景</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#RetrievalAugmentationAdvisor-%E6%A0%B8%E5%BF%83%E5%A4%84%E7%90%86%E6%B5%81%E7%A8%8B"><span class="toc-number">1.2.4.1.1.</span> <span class="toc-text">RetrievalAugmentationAdvisor 核心处理流程</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%BB%84%E4%BB%B6%E6%8E%A5%E5%8F%A3%E4%B8%8E%E5%AE%9E%E7%8E%B0%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.2.4.2.</span> <span class="toc-text">核心组件接口与实现详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E6%9F%A5%E8%AF%A2%E5%AF%B9%E8%B1%A1-Query"><span class="toc-number">1.2.4.2.1.</span> <span class="toc-text">1. 查询对象 (Query)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E6%A3%80%E7%B4%A2%E5%89%8D%E5%A4%84%E7%90%86-Pre-Retrieval"><span class="toc-number">1.2.4.2.2.</span> <span class="toc-text">2. 检索前处理 (Pre-Retrieval)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E6%A3%80%E7%B4%A2-Retrieval"><span class="toc-number">1.2.4.2.3.</span> <span class="toc-text">3. 检索 (Retrieval)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#4-%E6%A3%80%E7%B4%A2%E5%90%8E%E5%A4%84%E7%90%86-Post-Retrieval"><span class="toc-number">1.2.4.2.4.</span> <span class="toc-text">4. 检索后处理 (Post-Retrieval)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#5-%E7%94%9F%E6%88%90-Generation"><span class="toc-number">1.2.4.2.5.</span> <span class="toc-text">5. 生成 (Generation)</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%AE%8C%E6%95%B4%E9%85%8D%E7%BD%AE%E4%B8%8E%E6%A8%A1%E5%9D%97%E5%8C%96%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B"><span class="toc-number">1.2.4.3.</span> <span class="toc-text">完整配置与模块化代码示例</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-pom-xml-%E6%A0%B8%E5%BF%83%E4%BE%9D%E8%B5%96"><span class="toc-number">1.2.4.3.1.</span> <span class="toc-text">1. pom.xml 核心依赖</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-application-yml-%E6%9C%80%E5%B0%8F%E9%85%8D%E7%BD%AE"><span class="toc-number">1.2.4.3.2.</span> <span class="toc-text">2. application.yml 最小配置</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E6%A8%A1%E5%9D%97%E5%8C%96-RAG-%E6%8E%A7%E5%88%B6%E5%99%A8%E7%A4%BA%E4%BE%8B-RagModuleController"><span class="toc-number">1.2.4.3.3.</span> <span class="toc-text">3. 模块化 RAG 控制器示例 (RagModuleController)</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%8E-QuestionAnswerAdvisor-%E5%AF%B9%E6%AF%94%E7%AE%80%E8%A1%A8"><span class="toc-number">1.2.4.4.</span> <span class="toc-text">与 QuestionAnswerAdvisor 对比简表</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-%E5%AE%9E%E6%88%98%EF%BC%9A%E6%9E%84%E5%BB%BA%E5%9F%BA%E7%A1%80-RAG-%E9%97%AE%E7%AD%94%E5%BA%94%E7%94%A8"><span class="toc-number">1.3.</span> <span class="toc-text">9.3 实战：构建基础 RAG 问答应用</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#9-3-1-%E7%8E%AF%E5%A2%83%E4%B8%8E%E9%85%8D%E7%BD%AE"><span class="toc-number">1.3.1.</span> <span class="toc-text">9.3.1 环境与配置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-3-2-%E6%95%B0%E6%8D%AE%E6%92%AD%E7%A7%8D%EF%BC%9A%E6%89%8B%E5%8A%A8%E5%90%91-VectorStore-%E6%B7%BB%E5%8A%A0%E7%9F%A5%E8%AF%86"><span class="toc-number">1.3.2.</span> <span class="toc-text">9.3.2 数据播种：手动向 VectorStore 添加知识</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-3-3-RAG-%E6%9C%8D%E5%8A%A1%E4%B8%8E-API-%E5%AE%9E%E7%8E%B0"><span class="toc-number">1.3.3.</span> <span class="toc-text">9.3.3 RAG 服务与 API 实现</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#9-3-4-%E6%8E%A5%E5%8F%A3%E6%B5%8B%E8%AF%95"><span class="toc-number">1.3.4.</span> <span class="toc-text">9.3.4 接口测试</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>