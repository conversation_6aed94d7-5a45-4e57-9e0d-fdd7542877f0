---
title: SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 47912
date: 2025-03-21 13:13:45
---

## 8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)

如果我们把上一章的 Embedding 比作将书本（数据）的内容提炼成的知识卡片（向量），那么**向量数据库（Vector Store）** 就是存放这些卡片的、拥有超凡检索能力的巨大图书馆。它的核心价值在于，能基于向量间的“空间距离”进行高效的**相似性搜索**。这种能力是构建任何高级 AI 知识库或**检索增强生成（RAG）** 应用不可或缺的“记忆体”。

本章，我们将以官方文档为蓝本，深入剖析 `VectorStore` 的 API 设计，并专注于将我们的知识库服务无缝对接到一个业界领先的高性能内存数据库——**Redis**上，并通过完整的代码实战和数据验证来检验我们的成果。

### 8.1 核心 API 深度解析

在开始编码前，我们必须先深入理解 Spring AI 提供的 `VectorStore` 接口及其相关组件的最新设计。

#### 8.1.1 `VectorStore`：统一的向量操作门面

`VectorStore` 是 Spring AI 设计的、用于屏蔽底层不同向量数据库实现差异的统一接口。无论我们后端使用的是 Redis、PGVector 还是 Chroma，上层业务代码的调用方式都保持一致。

| `VectorStore` 核心方法 | 简要描述 |
| :--- | :--- |
| `void add(List<Document> documents)` | **数据入库**：将文档列表批量存入数据库。 |
| `List<Document> similaritySearch(SearchRequest request)` | **数据检索**：根据搜索请求执行相似度搜索。 |
| `void delete(List<String> idList)` | **按 ID 删除**：根据文档 ID 列表精确删除。 |
| `void delete(Filter.Expression filterExpression)` | **按条件删除**：根据元数据过滤表达式批量删除。 |

  * **`add` 方法**: 这是数据写入的唯一入口。一个关键特性是，此方法会自动调用我们项目配置好的 `EmbeddingModel`，在后台将 `Document` 的文本内容转换为向量，然后连同元数据一起存入数据库。开发者无需手动进行向量化。
  * **`similaritySearch` 方法**: 这是数据检索的核心。它接收一个 `SearchRequest` 对象，该对象封装了所有的查询条件，包括查询文本、期望返回的数量（topK）、相似度阈值以及元数据过滤器。
  * **`delete` 方法**: 提供了两种删除方式。按 ID 删除非常高效，适用于精确操作；按条件（Filter）删除则更为灵活，是实现数据管理、版本控制等复杂逻辑的基础。

#### 8.1.2 `SearchRequest`：构建你的精确检索意图

`SearchRequest` 是一个功能强大的请求构建器 (Builder)，它允许我们精细地控制搜索行为。根据最新官方 API，其 Builder 方法**没有 `with` 前缀**。

| Builder 方法 | 作用 |
| :--- | :--- |
| `query(String query)` | **(必需)** 设置用于语义搜索的查询文本。 |
| `topK(int topK)` | 设置返回最相似结果的最大数量。 |
| `similarityThreshold(double threshold)`| 设置相似度得分阈值 (0.0 到 1.0)。 |
| `filterExpression(String expression)` | **(关键)** 应用元数据过滤表达式。 |

  * **`query(String)`**: 这是搜索的“灵魂”，通常是用户的原始问题或一段用于匹配的文本。
  * **`topK(int)`**: 控制返回结果的数量。在 RAG 应用中，这个值的设定需要权衡——太小可能导致上下文不足，太大则可能引入不相关噪声并超出模型的上下文窗口限制。通常从一个较小的值 (如 3 或 5) 开始实验。
  * **`similarityThreshold(double)`**: 这是一个过滤器，只有相似度得分高于此阈值的文档才会被返回。在对结果相关性要求极高的场景下，设置一个较高的值 (如 0.75 以上) 能有效提升结果质量。
  * **`filterExpression(String)`**: 这是实现“混合搜索”的关键，它允许我们在进行语义搜索的同时，对文档的结构化元数据进行 SQL `WHERE` 子句式的精确过滤，极大地提升了搜索的精准度。

#### 8.1.3 元数据过滤：大海捞针的利器

**元数据过滤**允许我们在语义搜索的同时，对文档的结构化元数据（如类别、年份、标签等）进行精确的条件过滤。其表达式语法非常直观。

| 类别 | 支持的操作符 |
| :--- | :--- |
| **比较操作符** | `==` (等于), `!=` (不等于), `>` (大于), `>=` (大于等于), `<` (小于), `<=` (小于等于) |
| **集合操作符** | `IN` (包含于), `NIN` (不包含于) |
| **逻辑操作符** | `AND` (或 `&&`), `OR` (或 `||`) |

  * **场景应用：文档版本管理**
    官方文档提供了一个极佳的用例：管理文档版本。当我们需要发布新版文档时，一个安全的操作是先删除旧版，再添加新版。我们可以通过元数据轻松实现这一点。

    ```java
    // 这是一个在 Service 层中演示版本管理的示例
    public void updateDocument(Document newVersionDocument) {
        // 假设新旧文档通过元数据中的 'docId' 关联，并通过 'version' 区分
        String docId = (String) newVersionDocument.getMetadata().get("docId");
        String oldVersion = "1.0"; 

        // 1. 构造一个精确的过滤器表达式，定位到旧版本的文档
        String filterExpression = String.format("docId == '%s' AND version == '%s'", docId, oldVersion);
        
        // 2. 使用该表达式删除旧版本
        vectorStore.delete(filterExpression);

        // 3. 添加新版本的文档
        vectorStore.add(List.of(newVersionDocument));
    }
    ```

### 8.2 Redis 向量存储实战

Redis，作为家喻户晓的高性能内存数据库，通过其 **RediSearch** 模块提供了强大的向量搜索能力。它非常适合需要将缓存、消息队列和向量搜索等功能整合在同一个技术栈中，并追求极致读写性能的场景。

#### 8.2.1 环境准备：启动 Redis Stack

要解锁 Redis 的向量搜索功能，必须使用包含 `RediSearch` 模块的 **Redis Stack** 版本，而非普通的 Redis。

  * **规避端口冲突**
    一个常见的开发陷阱是，很多开发者本地已经运行了一个标准的 Redis 服务，占用了默认的 `6379` 端口。为了避免冲突，我们将特意为我们的 AI 应用在 `6380` 端口上启动一个新的 Redis Stack 实例。

  * **使用 Docker 启动 (推荐)**
    在项目根目录下创建 `docker-compose.yml` 文件。

    ```yaml
    # docker-compose.yml
    version: '3.8'
    services:
      redis-stack-for-ai:
        image: redis/redis-stack:latest
        container_name: redis-stack-for-ai
        ports:
          # 将容器的 6379 端口映射到主机的 6380 端口
          - "6380:6379"
          # RedisInsight 管理后台的端口保持不变
          - "8001:8001" 
    ```

    确保你的Docker桌面版（Windows）或Docker服务已经启动，然后在 `docker-compose.yml` 文件所在的目录运行 `docker-compose up -d` 命令即可在后台启动服务。

#### 8.2.2 Spring Boot 自动配置集成

  * **第一步：添加 Maven 依赖**
    在 `pom.xml` 中加入 Spring AI 为 Redis 提供的 Starter。请确保您也已经添加了 Embedding Model 的 Starter，例如我们项目中使用的智谱 AI。

    ```xml
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-vector-store-redis</artifactId>
    </dependency>

    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-starter-model-zhipuai</artifactId>
    </dependency>
    ```

  * **第二步：`application.yml` 配置**
    在配置文件中，我们提供 Redis 的连接信息，并对 `VectorStore` 进行参数配置，**注意将端口号修改为 `6380`**。

    ```yaml
    # src/main/resources/application.yml

    spring:
      # 配置 Redis 连接信息
      data:
        redis:
          host: localhost
          # 关键：连接到我们为AI应用指定的 6380 端口
          port: 6380
      
      ai:
        # VectorStore 的专属配置区
        vector-store:
          redis:
            # 指定在 Redis 中创建的索引名称，便于管理
            index-name: ai-copilot-kb-index
            # 关键：在开发时设为true，让Spring AI在应用启动时自动检查并创建向量索引。
            # 在生产环境中，推荐设为 false，并手动管理索引以获得更好的控制。
            initialize-schema: true
    ```

#### 8.2.3 升级现有服务：将向量存入Redis

当您调用 `vectorStore.add()` 时，Spring AI 在其内部为您执行了以下一系列操作：

1. **接收 `Document` 对象**：`add` 方法接收到您传入的 `Document` 列表。
2. **提取文本内容**：它会遍历列表中的每一个 `Document` 对象，并取出其 `content` 属性。
3. **调用内部 `EmbeddingModel`**：`RedisVectorStore` 在被 Spring 自动创建时，已经将您配置好的 `EmbeddingModel`（在我们的例子中是 `ZhiPuAiEmbeddingModel`）**注入**到了它自己的内部。此时，它会调用这个内部持有的 `embeddingModel` 实例，将提取出的所有文本内容进行批量向量化。
4. **关联向量与文档**：将生成的一批向量与原始的 `Document` 对象一一对应起来。
5. **写入 Redis**：最后，它连接到 Redis，为每一个 `Document` 创建一个 Hash，并将文档的 `content`、`metadata` 以及刚刚生成的**向量 `embedding`** 作为不同的字段，一并写入到这个 Hash 中。`

现在，我们对上一章的 `DocumentEmbeddingService` 进行一次关键的升级。它的职责不再仅仅是向量化，而是**将向量化后的文档持久化存储到 Redis 中**。

  * **目标**：修改 `DocumentEmbeddingService`，注入 `VectorStore`，并在处理文件后调用 `vectorStore.add()` 方法。

  * **代码实现** (`service/DocumentEmbeddingService.java`):

    ```java
    // src/main/java/com/copilot/aicopilotbackend/service/DocumentEmbeddingService.java
    package com.copilot.aicopilotbackend.service;
    
    import com.copilot.aicopilotbackend.validation.FileValidator;
    import lombok.RequiredArgsConstructor;
    import lombok.extern.slf4j.Slf4j;
    import org.springframework.ai.document.Document;
    import org.springframework.ai.vectorstore.VectorStore; // 1. 导入 VectorStore
    import org.springframework.stereotype.Service;
    import org.springframework.web.multipart.MultipartFile;
    
    import java.io.IOException;
    import java.nio.charset.StandardCharsets;
    import java.util.List;
    import java.util.Map;
    
    @Slf4j
    @Service
    @RequiredArgsConstructor
    public class DocumentEmbeddingService {
    
        // 2. 注入 VectorStore，Spring AI 会根据配置自动创建 RedisVectorStore 实例
        private final VectorStore vectorStore;
    
        /**
         * [升级版] 将上传的文件内容向量化并存入 Redis VectorStore。
         *
         * @param file 用户上传的文件
         * @return 包含文档ID和成功信息的字符串
         */
        public String embedDocument(MultipartFile file) {
            FileValidator.validateFile(file);
    
            try {
                String content = new String(file.getBytes(), StandardCharsets.UTF_8);
                
                // 3. 创建一个 Document 对象，这是 VectorStore 操作的基本单位
                // 我们可以将文件名等信息作为元数据一并存入
                Document document = new Document(content, Map.of(
                    "filename", file.getOriginalFilename(),
                    "size", file.getSize()
                ));
    
                log.info("准备将文档 '{}' 添加到 VectorStore...", file.getOriginalFilename());
    
                // 4. 调用 vectorStore.add() 方法，完成向量化和入库的全部操作
                vectorStore.add(List.of(document));
    
                log.info("文档 '{}' 已成功存入 Redis，ID: {}", file.getOriginalFilename(), document.getId());
    
                // 5. 返回更详细的成功信息
                return String.format("文件 '%s' 已成功存入知识库！文档ID为: %s", 
                                     file.getOriginalFilename(), document.getId());
    
            } catch (IOException e) {
                // ... 异常处理保持不变 ...
            }
        }
    }
    ```


#### 8.2.4 效果检验：在 Navicat 中查看数据

现在，我们服务的核心能力已经升级。虽然前端界面和上一章一样，但每次上传文件后，数据都会被永久地存入 Redis。让我们来亲眼验证一下。

1.  **重新上传文件**：启动应用，像上一章一样，通过前端界面或 Postman 上传一个 `.txt` 文件。

2.  **连接 Navicat for Redis**：

      * 打开 Navicat，点击“连接” -\> “Redis...”。
      * 在连接设置中，**主机**填写 `localhost`，**端口**填写我们指定的 `6380`。
      * 点击“连接测试”，成功后保存连接。

3.  **查找并检视数据**：

      * 双击打开连接，Navicat 会列出 Redis 中的所有 key。

      * Spring AI 默认会为存入的每个 `Document` 创建一个 Redis `Hash` 类型的 key。这个 key 的命名格式通常是 `prefix:id`。根据我们的配置 (`index-name: ai-copilot-kb-index`)，前缀通常是 `doc:ai-copilot-kb-index`。所以你应该能找到类似 `doc:ai-copilot-kb-index:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx` 的 key。

      * 双击打开这个 key，你将看到一个清晰的哈希表结构：

        * **`content`**: 字段存储了你上传文件的完整原始内容。
        * **`embedding`**: 字段存储了二进制格式的向量数据（在 Navicat 中可能显示为乱码或十六进制，这是正常的）。
        * **`metadata_filename`**: 我们自定义的元数据 `filename`。
        * **`metadata_size`**: 我们自定义的元数据 `size`。

        ```json
        {
            "filename": "附录.md",
            "size": 3496,
            "embedding": [ - 0.009663644, 0.0067672133,  - 0.026408637, 0.0010488915],
            "content": "---\ntitle: 附录\nc...."
        }
        ```




---