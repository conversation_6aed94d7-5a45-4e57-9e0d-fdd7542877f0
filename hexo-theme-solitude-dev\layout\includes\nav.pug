nav#nav.show
    #nav-group
        #blog_name
            if theme.nav.group
                .back-home-button(tabindex="-1")
                    i.back-home-button-icon.solitude.fas.fa-bars-progress
                    include ./widgets/nav/group
            a#site-name(href=url_for("/"), title=_p('nav.site_name_title'))
                if theme.site.name.class === 'i_class'
                    i.solitude(class=theme.site.name.custom, style="font-size: 16px;")
                else if theme.site.name.class === 'img'
                    img(src=theme.site.name.custom, alt=config.title)
                else if theme.site.name.class === 'text'
                    span.title= theme.site.name.custom
                i.solitude.fas.fa-home
        #page-name-mask
            #page-name
                a#page-name-text(onclick="sco.toTop()")= title || config.title
        #menus
            include ./widgets/nav/menu.pug
        #nav-left
        #nav-right
            include ./widgets/nav/right.pug
