#article-container
  .btn-center
    margin: 0 0 20px
    text-align: center

  .btn-anzhiyu
    display: inline-block
    margin: 0 4px 6px
    padding: 0 15px
    background-color: var(--btn-anzhiyu-color, $btn-default-color)
    color: $btn-color
    line-height: 2
    border-radius: 8px

    for $type in $color-types
      &.{$type}
        --btn-anzhiyu-color: lookup('$tagsP-' + $type + '-color')

    &:hover
      background-color: var(--btn-hover-color)

    i + span
      margin-left: 6px

    &:not(.block) + .btn-anzhiyu:not(.block)
      margin: 0 4px 20px

    &.block
      display: block
      margin: 0 0 20px
      width: fit-content
      width: -moz-fit-content

      &.center
        margin: 0 auto 20px

      &.right
        margin: 0 0 20px auto

    &.larger
      padding: 6px 15px

    &:hover
      text-decoration: none

    &.outline
      border: 1px solid transparent
      border-color: var(--btn-anzhiyu-color, $btn-default-color)
      background-color: transparent
      color: var(--btn-anzhiyu-color, $btn-default-color)

      &:hover
        background-color: var(--btn-anzhiyu-color, $btn-default-color)

      &:hover
        color: white !important
