#aside-content .card-widget#card-wechat {
  background: #57bd6a;
  display: flex;
  justify-content: center;
  align-content: center;
  padding: 0;
  cursor: pointer;
  border: none;
  height: 110px;
}
[data-theme="dark"] #aside-content .card-widget#card-wechat {
  background-color: #121121;
  border: var(--style-border);
}

#flip-wrapper {
  -webkit-perspective: 1000;
  perspective: 1000;
}

#flip-wrapper {
  position: relative;
  width: 235px;
  height: 110px;
  z-index: 1;
}
#flip-content {
  width: 100%;
  height: 100%;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  transition: cubic-bezier(0, 0, 0, 1.29) 0.3s;
}
#aside-content #flip-wrapper #flip-content .face {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  background-size: 100%;
}

#aside-content #flip-wrapper #flip-content .back.face {
  display: block;
  -webkit-transform: rotateY(180deg);
  transform: rotateY(180deg);
  box-sizing: border-box;
  background-size: 100%;
}
#flip-wrapper:hover #flip-content {
  -webkit-transform: rotateY(180deg);
  transform: rotateY(180deg);
}
