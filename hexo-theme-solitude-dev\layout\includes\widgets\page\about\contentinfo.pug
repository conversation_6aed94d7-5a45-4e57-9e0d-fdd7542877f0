- const contentinfo = site.data.about.contentinfo

if contentinfo
    .author-content
        .author-content-item.myInfoAndWittyWord
            .title1= contentinfo.sup
            .title2
                span.inline-word= contentinfo.name
            .title1= contentinfo.title
        .aboutsiteTips.author-content-item
            .author-content-item-tips= contentinfo.tip
            h2
                span.inline-word!= contentinfo.slogan
                .mask
                    each item,index in contentinfo.mask
                        if index === 0
                            span.first-tips(data-show='')= item
                        else if index === contentinfo.mask.length - 1
                            span(data-up='')= item
                        else
                            span= item

    script.
        var pursuitInterval = null;
        pursuitInterval = setInterval(function () {
            const show = document.querySelector('span[data-show]')
            const next = show.nextElementSibling || document.querySelector('.first-tips')
            const up = document.querySelector('span[data-up]')
            if (up) {
                up.removeAttribute('data-up')
            }
            show.removeAttribute('data-show')
            show.setAttribute('data-up', '')
            next.setAttribute('data-show', '')
        }, 2000)

        document.addEventListener('pjax:send', function () {
            clearInterval(pursuitInterval);
        });