---
title: 第七部分：多样化部署方案
categories:
  - 框架技术
  - Hexo
tags:
  - 博客搭建教程
cover: 'https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp'
comments: true
toc: true
ai: true
abbrlink: 64413
date: 2025-07-02 18:13:45
---

## 第七部分：多样化部署方案

搭建并配置好我们的Hexo博客后，下一步就是将其发布到互联网上，让全世界的读者都能访问。部署静态博客有多种方式，从简单的手动部署到自动化的持续集成/持续部署（CI/CD）。本部分，我们将介绍几种主流的免费部署方案，并重点讲解如何配置和使用它们。

### 1. 部署准备：安装 Hexo Git 部署插件

Hexo 默认支持多种部署方式，最常用的一种是通过 Git 将生成的静态文件推送到远程仓库，然后由托管平台（如 GitHub Pages, Coding Pages 等）负责提供访问服务。我们需要安装 `hexo-deployer-git` 插件来实现这一功能。

在 Hexo 项目的根目录下，打开命令行工具，执行以下命令：

```bash
# 安装 Hexo Git 部署插件
npm install hexo-deployer-git --save
```

安装完成后，我们需要在 Hexo 的配置文件 `_config.yml` 中配置 `deploy` 部分。

**配置 `_config.yml` 的 `deploy` 部分：**

找到 Hexo 项目根目录下的 `_config.yml` 文件，滑动到文件末尾，通常会有一个 `deploy` 部分。如果不存在，我们可以手动添加。其基本配置格式如下：

```yaml
# Deployment
## Docs: https://hexo.io/docs/one-command-deployment.html
deploy:
  type: git # 部署类型，这里设置为 git
  repo: <repository url> # 你的 Git 仓库 URL。例如：**************:username/username.github.io.git (SSH 方式) 或 https://github.com/username/username.github.io.git (HTTPS 方式)
  branch: <branch name> # 部署分支。GitHub Pages 默认通常是 main/master 或 gh-pages。Vercel/Netlify 通常是构建触发的分支（如 main）
  message: "feat: deploy blog via Hexo" # 可选：自定义部署提交信息
```

这是一个 `_config.yml` 文件中 `deploy` 部分的截图示例：

在这个配置中：
- `type: git` 指明了我们使用 Git 进行部署。
- `repo` 是你存储生成文件的 Git 仓库地址。对于 GitHub Pages，它通常是 `**************:YourGitHubName/YourGitHubName.github.io.git` (推荐使用 SSH 方式，需要配置 SSH Key) 或者 `https://github.com/YourGitHubName/YourGitHubName.github.io.git`。
- `branch` 是你希望将生成的静态文件推送到仓库的哪个分支。对于 GitHub Pages 用户网站（`username.github.io` 格式仓库），静态文件需要推送到 `main` 或 `master` 分支（取决于你仓库的默认分支以及 GitHub Pages 的配置）。对于项目网站，通常是 `gh-pages` 分支。对于 CI/CD 方式部署到 Vercel/Netlify，通常是将 Hexo 源文件推送到 `main` 分支，由 CI/CD 流程生成并部署。

配置完成后，保存 `_config.yml` 文件。下次运行 `hexo deploy` 命令时，Hexo 就会自动执行生成静态文件 (`hexo generate`) 并将 `public` 目录下的内容推送到你指定的 Git 仓库和分支。

### 2. 主流免费部署平台对比与实践

静态博客的部署选择多样，特别是对于个人博客，有许多提供免费额度且功能强大的托管平台。下面我们对比并介绍几个主流的免费部署平台。

| 平台           | CI/CD 支持 | 免费额度               | CDN 情况         | 国内访问速度 | 优缺点                                                                                                | 推荐场景                                 |
| :------------- | :--------- | :--------------------- | :--------------- | :----------- | :------------------------------------------------------------------------------------------------------ | :--------------------------------------- |
| **GitHub Pages** | 有限 (需 Git Push) / 良好 (配合 GitHub Actions) | 无限空间，每月流量限制 | GitHub 自带 CDN    | 较慢         | **优点:** 与 GitHub 工作流结合紧密，简单易用，适合托管静态网站。 **缺点:** 直接 Git Push 部署不够自动化，国内访问速度慢。 | GitHub 用户，对自动化要求不高或愿意配置 Actions |
| **Vercel**     | 优秀       | 构建时间/流量/函数调用 | Vercel 全球 CDN  | 较快         | **优点:** Git 自动部署，界面友好，功能强大（Serverless Functions, Edge Functions），全球 CDN 节点丰富。 **缺点:** 免费额度有一定限制，复杂配置需付费。 | 对部署自动化和访问速度有较高要求，乐于尝试新特性 |
| **Netlify**    | 优秀       | 构建时间/流量/函数调用 | Netlify 全球 CDN | 较快         | **优点:** Git 自动部署，功能丰富（Forms, Identity, Functions），免费额度慷慨，社区活跃。 **缺点:** 免费额度相对 Vercel 更慷慨，但复杂配置需付费。 | 对部署自动化和访问速度有较高要求，需要一些附加功能 |
| **Coding Pages** | 良好       | 无限空间，流量限制未知 | 腾讯云 CDN       | 较快         | **优点:** 国内平台，访问速度快，支持 Git 仓库。 **缺点:** 界面和文档不如 GitHub Pages/Vercel/Netlify 友好，自动化能力相对较弱。 | 主要面向国内用户，希望提高国内访问速度       |
| **Gitee Pages**  | 良好 (需手动更新或 webhook) | 无限空间，流量限制未知 | Gitee 自带 CDN   | 较快         | **优点:** 国内平台，访问速度快。 **缺点:** 免费版需要手动更新或配置 webhook，自动化程度最低。             | 主要面向国内用户，对自动化要求不高           |

#### 2.1 GitHub Pages：基于 Git Push 的传统部署

GitHub Pages 是 GitHub 提供的免费静态网站托管服务，特别适合托管个人博客。

部署步骤（使用 `hexo-deployer-git`）：

1.  **创建一个新的 GitHub 仓库**：
    仓库名称必须是 `<你的GitHub用户名>.github.io`（例如，我的 GitHub 用户名是 `exampleuser`，那么仓库名称就是 `exampleuser.github.io`）。这是一个特殊命名的仓库，GitHub 会自动将其主分支的静态内容发布到 `https://exampleuser.github.io` 域名下。请确保这是一个**公共**仓库。
2.  **配置 SSH Key (可选但推荐)**：
    为了使用 `**************:...` 这种 SSH 方式提交代码，你需要在本地生成 SSH Key 并添加到你的 GitHub 账户设置中。这样在 `hexo deploy` 时就无需反复输入密码。具体步骤可以参考 GitHub 官方文档。
3.  **配置 Hexo 的 `_config.yml`**：
    修改 Hexo 项目根目录下的 `_config.yml` 文件，配置 `deploy` 部分。将 `repo` 设置为你刚刚创建的仓库的 SSH 或 HTTPS 地址，将 `branch` 设置为你的仓库的默认分支（通常是 `main` 或 `master`）。

    ```yaml
    # Deployment
    deploy:
      type: git
      repo: **************:YourGitHubName/YourGitHubName.github.io.git # 你的 GitHub Pages 仓库地址 (SSH 方式)
      # 或使用 HTTPS 方式: repo: https://github.com/YourGitHubName/YourGitHubName.github.io.git
      branch: main # 或者 master，取决于你的仓库设置
      message: "Update blog via Hexo deploy"
    ```
    请将 `YourGitHubName` 替换为你的实际 GitHub 用户名。
4.  **生成并部署**：
    在 Hexo 项目根目录打开命令行，依次执行以下命令：

    ```bash
    # 清除缓存和已生成的静态文件 (重要，防止旧文件残留)
    hexo clean

    # 生成静态文件，生成结果在 public 目录下
    hexo generate

    # 部署到 GitHub Pages
    hexo deploy
    ```
    `hexo deploy` 命令实际上是先执行 `hexo generate`，然后将 `public` 目录下的内容使用 Git 推送到 `_config.yml` 中指定的仓库和分支。

5.  **GitHub Pages 设置确认**：
    部署成功后，访问你的 GitHub Pages 仓库页面，进入 `Settings` -> `Pages`。确认 `Source` 设置为 `Deploy from a branch`，并且 `Branch` 设置为你部署的分支（例如 `main` 或 `master`）以及 `/root` 目录。首次部署可能需要几分钟到十几分钟才能生效。

    ![GitHub Pages Settings 截图，展示 Source 和 Branch 配置](https://theme-next.js.org/images/github-pages.png)
    *图：GitHub Pages 仓库设置页面截图，确认从哪个分支和目录部署静态文件。*

    **注意:** 随着 GitHub Actions 的普及，GitHub Pages 现在推荐使用 GitHub Actions 来构建和部署静态网站，而不是直接将生成好的静态文件推送到特定分支。如果我们使用 GitHub Actions (详见第 3 部分)，则 Pages 设置中的 `Source` 需要改为 `GitHub Actions`。

#### 2.2 Vercel / Netlify：基于 Git 的自动化部署 (CI/CD)


CI/CD 能将我们从繁琐的部署命令中解放出来，实现“一次推送，自动上线”。我们只需将**博客源文件**推送到 GitHub，云端服务器就会自动为我们完成构建和部署。

| 平台 | CI/CD 支持 | 免费额度 | CDN 情况 | 国内访问速度 | 优缺点 | 推荐场景 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **Vercel** | **优秀** | 构建时间/流量 | Vercel 全球 CDN | **较慢 (官方) / 很快 (使用社区CDN)** | **优点:** Git 自动部署，界面极友好，功能强大。 **缺点:** 官方节点国内访问慢，需额外配置加速。 | **强烈推荐**，对自动化和访问速度有高要求，愿意进行简单优化配置。 |
| **Netlify** | 优秀 | 构建时间/流量 | Netlify 全球 CDN | 较慢 | **优点:** 功能丰富，免费额度慷慨。 **缺点:** 国内访问速度同样不理想。 | Vercel 的优秀替代品，功能需求多样化。 |
| **GitHub Pages** | 良好 (配合 Actions) | 无限空间 | GitHub 自带 CDN | 很慢 | **优点:** 与 GitHub 结合紧密。 **缺点:** 国内访问速度是主要瓶颈。 | 对速度要求不高，或作为代码备份。 |
| **Coding Pages** | 良好 | 无限空间 | 腾讯云 CDN | **很快** | **优点:** 国内平台，访问速度顶尖。 **缺点:** CI/CD 配置比 Vercel 稍复杂。 | 主要面向国内用户，追求极致国内速度。 |
| **Gitee Pages** | 有限 | 无限空间 | Gitee 自带 CDN | 很快 | **优点:** 国内平台，速度快。 **缺点:** 免费版需手动更新，自动化程度最低。 | 对自动化要求不高，主要面向国内。 |

**结论**：对于追求**自动化体验**和**全球化部署**的用户，**Vercel 是首选**。虽然其官方节点在国内访问慢，但通过简单的社区方案即可完美解决，实现“鱼和熊掌兼得”。

### 3. 实战核心：Vercel 自动化部署与深度优化

我们将以 Vercel 为例，走完从部署、踩坑到优化的完整流程。

#### 3.1 基础部署：从 Git 仓库到上线

1.  **准备源文件仓库**：确保你的 GitHub 仓库中存放的是 **Hexo 的完整源文件**（包含 `source/`, `themes/`, `_config.yml`, `package.json`等），而不是 `hexo g` 生成的 `public` 目录。
2.  **在 Vercel 中导入项目**：访问 Vercel 官网并用 GitHub 账号登录，点击 `Add New...` -> `Project`，选择并导入你的 Hexo 源文件仓库。
3.  **配置并部署**：Vercel 会自动识别出是 Hexo 项目。
    *   **Framework Preset**: 应为 `Hexo`。
    *   **Root Directory**: 保持 `./`。
    *   点击 `Deploy`。

首次部署你可能会遇到一个经典错误。

#### 3.2 解决常见错误与配置构建命令

**错误：`sh: line 1: hexo: command not found`**

这个报错意味着 Vercel 的服务器不认识 `hexo` 命令。原因是它只执行了 `hexo generate`，却没有安装 Hexo 本身。

**解决方案**：我们需要修改构建命令，让 Vercel 在构建前先安装依赖。

1.  在 Vercel 项目后台，进入 **Settings** -> **General**。
2.  找到 **Build & Development Settings**，覆盖 **Build Command** (构建命令) 为：
    ```bash
    npm install && hexo generate
    ```
    这条命令告诉 Vercel：先用 `npm install` 安装 `package.json` 中定义的所有依赖（包括 Hexo），成功后再执行 `hexo generate` 生成网站。
3.  保存后，回到 **Deployments** 页面，对失败的部署选择 **Redeploy**。

部署成功后，你的新工作流诞生了：**本地写文章 -> `git push` -> Vercel 自动上线**。你再也不需要在本地执行 `hexo g -d` 了！

#### 3.3 集成第三方服务 (以 Algolia 搜索为例)

自动化流程中，像 `hexo algolia` 这种需要私密 API Key 的命令怎么办？答案是使用 Vercel 的**环境变量**。

1.  **在 Vercel 添加环境变量**：
    *   进入 **Settings** -> **Environment Variables**。
    *   添加你的 Algolia 密钥，例如：
        *   `ALGOLIA_APP_ID` = 你的 Application ID
        *   `ALGOLIA_API_KEY` = 你的 **Admin API Key** (用于写入)
        *   `ALGOLIA_INDEX_NAME` = 你的索引名

2.  **修改 `_config.yml`**：让 Hexo 从环境变量中读取密钥，而不是明文存储。
    ```yaml
    # _config.yml
    algolia:
      appId: process.env.ALGOLIA_APP_ID
      apiKey: process.env.ALGOLIA_API_KEY
      indexName: process.env.ALGOLIA_INDEX_NAME
      chunkSize: 5000
    ```

3.  **更新构建命令**：在生成网站后，执行索引命令。同时，我们做一个优化：只在正式发布到主域名时才更新索引。
    *   将 **Build Command** 修改为：
    ```bash
    npm install && hexo generate && if [ "$VERCEL_ENV" = "production" ]; then hexo algolia; else echo "Not in production, skipping Algolia indexing."; fi
    ```
    这条命令利用 Vercel 的系统变量 `VERCEL_ENV` 判断，只有在生产环境 (`production`) 部署时，才执行 `hexo algolia`。

#### 3.4 绑定并加速自定义域名

1.  **在 Vercel 绑定域名**：在项目 **Settings** -> **Domains** 中，添加你的自定义域名，例如 `prorise666.site`。
2.  **国内访问速度优化**：此时，你会发现用自定义域名访问非常慢。这是因为 Vercel 官方节点在国内的通病。幸运的是，社区提供了优秀的解决方案。
3.  **配置 DNS (关键一步)**：
    *   登录你的域名注册商（如 Spaceship, GoDaddy）。
    *   找到你域名的 DNS 管理界面。
    *   **删除**所有指向 Vercel 官方 IP 的 `A` 记录。
    *   **创建一条 CNAME 记录**，配置如下：
        *   **类型 (Type):** `CNAME`
        *   **主机/名称 (Host/Name):** `@` (代表根域名)
        *   **值/指向 (Value/Points to):** `vercel.cdn.yt-blog.top`
    *   保存设置。

4.  **重要：忽略 Vercel 警告**
    配置 CNAME 后，Vercel 后台的域名状态会显示红色的 **"Invalid Configuration"** 警告。
    
> **请放心忽略此警告！** 这是因为我们的设置（社区 CNAME）与 Vercel 官方推荐（A 记录）不符。判断成功的唯一标准是：你的网站能通过域名正常访问，且速度变快了。
    
5.  **最终优化：添加 Vercel 缓存配置**
    为了让网站快上加快，我们可以配置 Vercel 的 CDN 缓存策略。
    *   在你的 Hexo 源文件仓库**根目录**下，创建一个 `vercel.json` 文件，内容如下：
        ```json
        {
          "headers": [
            {
              "source": "/(.*)",
              "headers": [
                {
                  "key": "Cache-Control",
                  "value": "public, s-maxage=86400"
                }, {
                  "key": "Vercel-CDN-Cache-Control",
                  "value": "max-age=3600"
                }
              ]
            }
          ]
        }
        ```
    *   将 `vercel.json` 文件 `git push` 到仓库，Vercel 会自动应用新的缓存规则。

至此，你已经拥有了一个全自动、高可用、在国内访问速度飞快的现代化博客！

CI/CD 的自动化极大地提高了部署效率，减少了手动操作可能带来的错误，让我们能更专注于内容创作本身。