<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Python（二十一）：第二十章：Python 语法新特性总结 | Prorise的小站</title><meta name="keywords" content="Python基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Python（二十一）：第二十章：Python 语法新特性总结"><meta name="application-name" content="Python（二十一）：第二十章：Python 语法新特性总结"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="Python（二十一）：第二十章：Python 语法新特性总结"><meta property="og:url" content="https://prorise666.site/posts/55902.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第二十章：Python 语法新特性总结 (3.8 ~ 3.13)Python 语言在不断发展，每个新版本都会带来一些有用的新特性、性能改进和语法糖，使得编写代码更加高效和愉悦。本章将简要回顾从 Python 3.8 到 3.13 版本引入的一些值得关注的语法和核心库特性。 Python 3.8 主要"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta name="description" content="第二十章：Python 语法新特性总结 (3.8 ~ 3.13)Python 语言在不断发展，每个新版本都会带来一些有用的新特性、性能改进和语法糖，使得编写代码更加高效和愉悦。本章将简要回顾从 Python 3.8 到 3.13 版本引入的一些值得关注的语法和核心库特性。 Python 3.8 主要"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/55902.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"Python（二十一）：第二十章：Python 语法新特性总结",postAI:"true",pageFillDescription:"第二十章：Python 语法新特性总结 (3.8 ~ 3.13), Python 3.8 主要特性, 1. 赋值表达式 (海象操作符) =, 2. 仅位置参数 (/), 3. f-string 增强 (=) 用于调试, Python 3.9 主要特性, 1. 字典合并与更新操作符 (| 和 |=), 2. 类型注解的内置集合类型 (泛型), 3. 字符串方法：removeprefix() 和 removesuffix(), Python 3.10 主要特性, 1. 结构化模式匹配 (matchx2Fcase), 2. 联合类型操作符 (|), 3. 上下文管理器改进：括号内多个 with 表达式, Python 3.11 主要特性, 1. 异常组 (ExceptionGroup) 和异常注释 (add_note()), 2. Self 类型, 3. LiteralString 类型, Python 3.12 主要特性, 1. 泛型类和函数的简化语法 (PEP 695), 2. 类型别名与类型变量语法改进 (type 语句) (PEP 695), 3. f-string 语法进一步改进 (PEP 701), Python 3.13 主要特性, 1. @typing.override 装饰器 (PEP 698)第二十章语法新特性总结语言在不断发展每个新版本都会带来一些有用的新特性性能改进和语法糖使得编写代码更加高效和愉悦本章将简要回顾从到版本引入的一些值得关注的语法和核心库特性主要特性带来了赋值表达式海象操作符仅位置参数调试支持等重要更新赋值表达式海象操作符海象操作符允许你在表达式内部为变量赋值这可以在某些情况下简化代码尤其是在语句或循环中当你需要先计算一个值然后判断这个值并且后续还想使用这个值时海象操作符演示假设可用演示海象操作符的使用海象操作符示例在条件中赋值并使用列表长度为大于前三个元素列表长度为不大于特性演示仅位置参数函数定义中可以使用来指明其前面的参数只能通过位置传递不能作为关键字参数传递这有助于库作者设计更清晰更不易出错的仅位置参数演示演示仅位置参数的使用仅位置参数创建用户信息字典和只能通过位置传递和可以是位置参数或关键字参数正确位置参数正确位置参数和关键字参数正确作为位置参数也是可以的演示真正的错误情况错误之前的参数不能用关键字方式传递在尝试执行操作时发生错误此处不重复假设按特性运行增强用于调试现在支持说明符可以在输出中包含表达式文本及其计算结果非常便于调试调试增强演示演示中的调试功能增强用于调试使用直接打印变量名和值调试信息输出示例调试信息也可以用于更复杂的表达式计算结果注意在表达式之后输出示例计算结果主要特性引入了新的字典合并操作符类型注解可以使用内置集合类型并为字符串添加了移除前缀后缀的方法字典合并与更新操作符和新的操作符用于合并两个字典用于原地更新字典字典合并与更新操作符演示兼容旧版类型提示但在中即可演示字典的和操作符字典合并与更新操作符键与重复合并操作如果键重复则右边字典的值优先合并后输出原地更新操作原始被修改原地更新后输出特性演示类型注解的内置集合类型泛型现在可以直接使用内置的集合类型如作为泛型类型进行注解而无需从模块导入大写版本如内置集合类型注解演示在中不再必需但为了兼容性或清晰性仍可使用演示使用内置集合类型进行类型注解参数字符串列表键为字符串值为整数的字典返回包含整数和浮点数的元组类型注解的内置集合类型函数定义使用了注解处理的名字处理的分数返回结果没有分数字符串方法和这两个新的字符串方法用于移除字符串的前缀或后缀如果存在的话字符串移除前缀后缀演示演示和字符串方法和移除后缀后缀不匹配未改变移除前缀前缀不匹配未改变主要特性带来了备受期待的结构化模式匹配更简洁的联合类型表示法以及上下文管理器语法的改进结构化模式匹配这是一种新的控制流语句允许你根据数据的结构和值来匹配模式并执行相应的代码块类似于其他语言中的或模式匹配结构化模式匹配演示演示结构化模式匹配处理不同数据结构结构化模式匹配输入匹配字典结构并捕获值用户数据姓名嵌套匹配和事件其他匹配列表包含守卫条件两个相等的整数点捕获列表剩余部分到操作在上的结果匹配字符串并用绑定收到退出指令通配符匹配任何其他情况未知或不匹配的数据包格式特性演示处理包结果联合类型操作符现在可以使用操作符来表示联合类型使得类型注解更简洁联合类型操作符演示在中和不再必需显式导入来使用使用代替演示使用作为联合类型操作符联合类型操作符接收到的值类型数值处理字符串处理使用代替演示使用代替未提供数据处理可选数据为上下文管理器改进括号内多个表达式现在可以在语句后的括号内编写多个上下文表达式而无需嵌套语句使代码更扁平上下文管理器改进演示用于文件操作演示在语句中使用括号包含多个上下文表达式上下文管理器改进括号内多个表达式创建临时输入文件第一行数据第二行数据已创建临时输入文件的写法同时打开读和写示例操作转为大写后写入内容已从输入文件读取处理后写入输出文件验证输出文件输出文件内容处理文件时发生错误清理临时文件临时文件已清理主要特性带来了显著的性能提升计划以及异常组异常注释类型和等新特性异常组和异常注释允许同时处理多个不相关的异常方法可以向异常对象添加上下文注释异常组和异常注释演示在中是内置的演示和异常组和异常注释演示演示输入的值无法转换为整数添加注释请确保输入的是有效的数字如果要重新引发带注释的异常捕获到带注释要查看注释你需要捕获异常并访问其属性或让它传播到顶层由解释器打印异常的默认异常回溯会显示注释重新引发以观察其默认打印效果重新引发的异常其回溯应包含注释异常包含的注释演示概念性演示概念性假设我们有多个并发任务每个都可能失败模拟任务任务模拟成功任务除法运算失败任务捕获模拟任务任务模拟成功任务字典键查找失败任务捕获如果有多个异常可以用包装它们在中是内置的对于旧版本可能需要此库在执行多个任务时发生错误发生了异常组处理异常组的语法捕获到异常组中的捕获到异常组中的在实际代码中会使用来处理所有模拟任务均成功完成特性演示类型或仅如果提供了一种更简洁更准确的方式来注解那些返回类自身实例的方法例如构造函数工厂方法或返回修改后自身的链式调用方法类型演示或演示类型的使用类型已创建返回类型是设置一个配置选项并返回自身以便链式调用选项设置为返回当前类的实例类方法返回创建一个带有默认设置的实例调用类方法指向或其子类构建配置使用示例链式调用演示在子类中的行为返回的仍然是类型的类型是应为类型用于注解那些值必须是字面量字符串即直接在代码中写出的字符串而不是通过变量拼接或函数调用生成的字符串的参数这有助于静态分析工具检测潜在的安全风险例如注入类型演示演示类型类型一个模拟的安全数据库查询函数它只接受字面量字符串作为查询语句以防止注入尝试执行安全查询参数在实际应用中这里会与数据库交互并可能使用参数化查询模拟执行查询参数化正确的用法传递字符串字面量错误的用法传递通过变量或拼接生成的字符串静态类型检查器如会对以下调用发出警告会警告请输入要查询的列名例如模拟用户输入会警告注意主要用于静态类型检查如果上面的注释行被取消且使用了它会标记出潜在的类型错误在运行时本身不强制取消注释以在运行时测试部分可选主要特性继续改进类型系统带来了更简洁的泛型类和函数语法以及的进一步增强泛型类和函数的简化语法现在可以使用更简洁的方括号语法直接在类或函数定义中声明类型参数而无需从导入简化泛型语法演示在中可以通过新语法隐式定义演示中简化的泛型类和函数语法泛型类和函数的简化语法简化泛型函数定义旧方法直接在函数名后用声明类型参数获取列表的第一个元素如果列表为空则返回调用简化泛型类定义旧方法直接在类名后用声明类型参数可以显式指定类型也可以让类型检查器推断特性演示类型别名与类型变量语法改进语句引入了语句作为创建类型别名和类型变量的更清晰更正式的方式语句演示仍可用于兼容性或复杂场景的语句定义类型别名定义类型变量现在是一个可以绑定到或定义逆变类型变量上界为定义协变类型变量上界为演示中使用语句定义类型别名和类型变量类型别名与类型变量语法改进语句处理点类型是泛型变量字符串泛型变量整数会报错类型不匹配整数列表的和会报错因为只能是或语法进一步改进中的更加强大和灵活主要体现在引号重用现在可以在表达式内部重用与包围自身所用类型相同的引号无需转义多行表达式和注释表达式部分现在可以跨越多行并且可以包含注释反斜杠表达式部分可以使用反斜杠语法改进演示演示中的语法改进语法进一步改进引号重用在之前需要不同类型的引号或转义或者可以重用引号引号重用外层单引号内部仍可方便使用双引号引号重用单引号多行表达式和注释注意在多行表达式中注释必须在自己的行上这是一个表达式内部的注释多行表达式方法调用带多行表达式和注释反斜杠虽然不常见但现在允许原本就这样可以现在更灵活中使用反斜杠主要特性带来了包括新的装饰器编译器的初步实验可能不直接体现在日常语法中以及对现有特性的一些改进装饰器一个新的装饰器来自模块或的中已有用于明确指示一个方法意图覆盖其父类中的同名方法这有助于静态类型检查器和开发者捕捉由于意外拼写错误或签名不匹配导致的覆盖失败装饰器演示或演示装饰器装饰器明确表示此方法覆盖父类方法明确表示此方法覆盖父类方法如果写成拼写错误这类类型检查器会报错因为它没有覆盖任何父类方法尝试覆盖一个不存在的基类方法会警告帮助在静态检查时发现覆盖错误部分特性演示",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-14 16:45:54",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E5%8D%81%E7%AB%A0%EF%BC%9APython-%E8%AF%AD%E6%B3%95%E6%96%B0%E7%89%B9%E6%80%A7%E6%80%BB%E7%BB%93-3-8-3-13"><span class="toc-text">第二十章：Python 语法新特性总结 (3.8 ~ 3.13)</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-8-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-text">Python 3.8 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%B5%8B%E5%80%BC%E8%A1%A8%E8%BE%BE%E5%BC%8F-%E6%B5%B7%E8%B1%A1%E6%93%8D%E4%BD%9C%E7%AC%A6"><span class="toc-text">1. 赋值表达式 (海象操作符) :=</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BB%85%E4%BD%8D%E7%BD%AE%E5%8F%82%E6%95%B0"><span class="toc-text">2. 仅位置参数 (/)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-f-string-%E5%A2%9E%E5%BC%BA-%E7%94%A8%E4%BA%8E%E8%B0%83%E8%AF%95"><span class="toc-text">3. f-string 增强 (=) 用于调试</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-9-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-text">Python 3.9 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%AD%97%E5%85%B8%E5%90%88%E5%B9%B6%E4%B8%8E%E6%9B%B4%E6%96%B0%E6%93%8D%E4%BD%9C%E7%AC%A6-%E5%92%8C"><span class="toc-text">1. 字典合并与更新操作符 (| 和 |=)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%B1%BB%E5%9E%8B%E6%B3%A8%E8%A7%A3%E7%9A%84%E5%86%85%E7%BD%AE%E9%9B%86%E5%90%88%E7%B1%BB%E5%9E%8B-%E6%B3%9B%E5%9E%8B"><span class="toc-text">2. 类型注解的内置集合类型 (泛型)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%AD%97%E7%AC%A6%E4%B8%B2%E6%96%B9%E6%B3%95%EF%BC%9Aremoveprefix-%E5%92%8C-removesuffix"><span class="toc-text">3. 字符串方法：removeprefix() 和 removesuffix()</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-10-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-text">Python 3.10 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%BB%93%E6%9E%84%E5%8C%96%E6%A8%A1%E5%BC%8F%E5%8C%B9%E9%85%8D-match-case"><span class="toc-text">1. 结构化模式匹配 (match/case)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%81%94%E5%90%88%E7%B1%BB%E5%9E%8B%E6%93%8D%E4%BD%9C%E7%AC%A6"><span class="toc-text">2. 联合类型操作符 (|)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8%E6%94%B9%E8%BF%9B%EF%BC%9A%E6%8B%AC%E5%8F%B7%E5%86%85%E5%A4%9A%E4%B8%AA-with-%E8%A1%A8%E8%BE%BE%E5%BC%8F"><span class="toc-text">3. 上下文管理器改进：括号内多个 with 表达式</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-11-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-text">Python 3.11 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BC%82%E5%B8%B8%E7%BB%84-ExceptionGroup-%E5%92%8C%E5%BC%82%E5%B8%B8%E6%B3%A8%E9%87%8A-add-note"><span class="toc-text">1. 异常组 (ExceptionGroup) 和异常注释 (add_note())</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-Self-%E7%B1%BB%E5%9E%8B"><span class="toc-text">2. Self 类型</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-LiteralString-%E7%B1%BB%E5%9E%8B"><span class="toc-text">3. LiteralString 类型</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-12-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-text">Python 3.12 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%B3%9B%E5%9E%8B%E7%B1%BB%E5%92%8C%E5%87%BD%E6%95%B0%E7%9A%84%E7%AE%80%E5%8C%96%E8%AF%AD%E6%B3%95-PEP-695"><span class="toc-text">1. 泛型类和函数的简化语法 (PEP 695)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%B1%BB%E5%9E%8B%E5%88%AB%E5%90%8D%E4%B8%8E%E7%B1%BB%E5%9E%8B%E5%8F%98%E9%87%8F%E8%AF%AD%E6%B3%95%E6%94%B9%E8%BF%9B-type-%E8%AF%AD%E5%8F%A5-PEP-695"><span class="toc-text">2. 类型别名与类型变量语法改进 (type 语句) (PEP 695)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-f-string-%E8%AF%AD%E6%B3%95%E8%BF%9B%E4%B8%80%E6%AD%A5%E6%94%B9%E8%BF%9B-PEP-701"><span class="toc-text">3. f-string 语法进一步改进 (PEP 701)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-13-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-text">Python 3.13 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-typing-override-%E8%A3%85%E9%A5%B0%E5%99%A8-PEP-698"><span class="toc-text">1. @typing.override 装饰器 (PEP 698)</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5f2a23">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#277340">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#c72008">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#11a7a2">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#276d10">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#6d6a95">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Python基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Python（二十一）：第二十章：Python 语法新特性总结</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-19T04:13:45.000Z" title="发表于 2025-04-19 12:13:45">2025-04-19</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-14T08:45:54.554Z" title="更新于 2025-07-14 16:45:54">2025-07-14</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">6.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>29分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Python（二十一）：第二十章：Python 语法新特性总结"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/55902.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/55902.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Python基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Python（二十一）：第二十章：Python 语法新特性总结</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-04-19T04:13:45.000Z" title="发表于 2025-04-19 12:13:45">2025-04-19</time><time itemprop="dateCreated datePublished" datetime="2025-07-14T08:45:54.554Z" title="更新于 2025-07-14 16:45:54">2025-07-14</time></header><div id="postchat_postcontent"><h2 id="第二十章：Python-语法新特性总结-3-8-3-13"><a href="#第二十章：Python-语法新特性总结-3-8-3-13" class="headerlink" title="第二十章：Python 语法新特性总结 (3.8 ~ 3.13)"></a>第二十章：Python 语法新特性总结 (3.8 ~ 3.13)</h2><p>Python 语言在不断发展，每个新版本都会带来一些有用的新特性、性能改进和语法糖，使得编写代码更加高效和愉悦。本章将简要回顾从 Python 3.8 到 3.13 版本引入的一些值得关注的语法和核心库特性。</p><h3 id="Python-3-8-主要特性"><a href="#Python-3-8-主要特性" class="headerlink" title="Python 3.8 主要特性"></a>Python 3.8 主要特性</h3><p>Python 3.8 带来了赋值表达式（海象操作符）、仅位置参数、f-string 调试支持等重要更新。</p><h4 id="1-赋值表达式-海象操作符"><a href="#1-赋值表达式-海象操作符" class="headerlink" title="1. 赋值表达式 (海象操作符) :="></a>1. 赋值表达式 (海象操作符) <code>:=</code></h4><p>海象操作符 <code>:=</code> 允许你在表达式内部为变量赋值。这可以在某些情况下简化代码，尤其是在 <code>if</code> 语句或 <code>while</code> 循环中，当你需要先计算一个值，然后判断这个值，并且后续还想使用这个值时。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 海象操作符演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *  <span class="comment"># 假设 print_utils.py 可用</span></span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> <span class="type">List</span>, <span class="type">Any</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_walrus_operator</span>(<span class="params">data: <span class="type">List</span>[<span class="type">Any</span>]</span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示海象操作符 (:=) 的使用。"""</span></span><br><span class="line">    print_subheader(<span class="string">"1. 海象操作符 (Assignment Expressions) `:=`"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 示例1: 在 if 条件中赋值并使用</span></span><br><span class="line">    <span class="keyword">if</span> (n := <span class="built_in">len</span>(data)) &gt; <span class="number">3</span>:</span><br><span class="line">        print_success(<span class="string">f"列表长度为 <span class="subst">{n}</span>，大于3。前三个元素: <span class="subst">{data[:<span class="number">3</span>]}</span>"</span>)</span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        print_info(<span class="string">f"列表长度为 <span class="subst">{n}</span>，不大于3。"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    print_header(<span class="string">"Python 3.8 特性演示"</span>)</span><br><span class="line">    sample_data_list: <span class="type">List</span>[<span class="type">Any</span>] = [<span class="number">1</span>, <span class="string">"abc"</span>, [<span class="number">1</span>, <span class="number">2</span>], <span class="number">3</span>]</span><br><span class="line">    demo_walrus_operator(sample_data_list)</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h4 id="2-仅位置参数"><a href="#2-仅位置参数" class="headerlink" title="2. 仅位置参数 (/)"></a>2. 仅位置参数 (<code>/</code>)</h4><p>函数定义中可以使用 <code>/</code> 来指明其前面的参数只能通过位置传递，不能作为关键字参数传递。这有助于库作者设计更清晰、更不易出错的 API。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 仅位置参数演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_positional_only_args</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示仅位置参数的使用。"""</span></span><br><span class="line">    print_subheader(<span class="string">"2. 仅位置参数 (`/`)"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">create_user</span>(<span class="params">user_id: <span class="built_in">int</span>, name: <span class="built_in">str</span>, /, age: <span class="built_in">int</span> = <span class="number">30</span>, city: <span class="built_in">str</span> = <span class="string">"Unknown"</span></span>) -&gt; <span class="built_in">dict</span>:</span><br><span class="line">        <span class="string">"""</span></span><br><span class="line"><span class="string">        创建用户信息字典。</span></span><br><span class="line"><span class="string">        user_id 和 name 只能通过位置传递。</span></span><br><span class="line"><span class="string">        age 和 city 可以是位置参数或关键字参数。</span></span><br><span class="line"><span class="string">        """</span></span><br><span class="line">        <span class="keyword">return</span> {<span class="string">"id"</span>: user_id, <span class="string">"name"</span>: name, <span class="string">"age"</span>: age, <span class="string">"city"</span>: city}</span><br><span class="line">    </span><br><span class="line">    print_success(<span class="built_in">str</span>(create_user(<span class="number">1</span>, <span class="string">"Alice"</span>)))  <span class="comment"># 正确：位置参数</span></span><br><span class="line">    print_success(<span class="built_in">str</span>(create_user(<span class="number">1</span>, <span class="string">"Alice"</span>, age=<span class="number">25</span>)))  <span class="comment"># 正确：位置参数和关键字参数</span></span><br><span class="line">    print_success(<span class="built_in">str</span>(create_user(<span class="number">1</span>, <span class="string">"Alice"</span>, <span class="number">25</span>)))  <span class="comment"># 正确：age 作为位置参数也是可以的</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 演示真正的错误情况</span></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        create_user(<span class="built_in">id</span>=<span class="number">1</span>, name=<span class="string">"Alice"</span>)  <span class="comment"># 错误：/ 之前的参数不能用关键字方式传递</span></span><br><span class="line">    <span class="keyword">except</span> TypeError <span class="keyword">as</span> e:</span><br><span class="line">        print_error(<span class="string">f"在尝试执行操作时发生错误: <span class="subst">{e}</span>"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    <span class="comment"># 此处不重复 print_header，假设按特性运行</span></span><br><span class="line">    demo_positional_only_args()</span><br></pre></td></tr></tbody></table></figure><h4 id="3-f-string-增强-用于调试"><a href="#3-f-string-增强-用于调试" class="headerlink" title="3. f-string 增强 (=) 用于调试"></a>3. f-string 增强 (<code>=</code>) 用于调试</h4><p>f-string 现在支持 <code>=</code> 说明符，可以在输出中包含表达式文本及其计算结果，非常便于调试。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === f-string 调试增强演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_fstring_debug</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示 f-string 中 `=` 的调试功能。"""</span></span><br><span class="line">    print_subheader(<span class="string">"3. f-string 增强 (`=`) 用于调试"</span>)</span><br><span class="line">    </span><br><span class="line">    app_name: <span class="built_in">str</span> = <span class="string">"MyApplication"</span></span><br><span class="line">    app_version: <span class="built_in">float</span> = <span class="number">2.1</span></span><br><span class="line">    user_count: <span class="built_in">int</span> = <span class="number">1050</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 使用 = 直接打印变量名和值</span></span><br><span class="line">    print_info(<span class="string">f"调试信息: <span class="subst">{app_name=}</span>, <span class="subst">{app_version=}</span>, <span class="subst">{user_count=}</span>"</span>)</span><br><span class="line">    <span class="comment"># 输出示例: 调试信息: app_name='MyApplication', app_version=2.1, user_count=1050</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 也可以用于更复杂的表达式</span></span><br><span class="line">    result: <span class="built_in">int</span> = user_count * <span class="number">2</span></span><br><span class="line">    print_info(<span class="string">f"计算结果: <span class="subst">{user_count * <span class="number">2</span> = }</span>"</span>) <span class="comment"># 注意 `=` 在表达式之后</span></span><br><span class="line">    <span class="comment"># 输出示例: 计算结果: user_count * 2 = 2100</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    demo_fstring_debug()</span><br></pre></td></tr></tbody></table></figure><h3 id="Python-3-9-主要特性"><a href="#Python-3-9-主要特性" class="headerlink" title="Python 3.9 主要特性"></a>Python 3.9 主要特性</h3><p>Python 3.9 引入了新的字典合并操作符，类型注解可以使用内置集合类型，并为字符串添加了移除前缀/后缀的方法。</p><h4 id="1-字典合并与更新操作符-和"><a href="#1-字典合并与更新操作符-和" class="headerlink" title="1. 字典合并与更新操作符 (| 和 |=)"></a>1. 字典合并与更新操作符 (<code>|</code> 和 <code>|=</code>)</h4><p>新的操作符 <code>|</code> 用于合并两个字典，<code>|=</code> 用于原地更新字典。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 字典合并与更新操作符演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> <span class="type">Dict</span>, <span class="type">Any</span>  <span class="comment"># 兼容旧版类型提示，但在3.9+中 dict 即可</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_dict_merge_operators</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示字典的 | 和 |= 操作符。"""</span></span><br><span class="line">    print_subheader(<span class="string">"1. 字典合并 (`|`) 与更新 (`|=`) 操作符"</span>)</span><br><span class="line"></span><br><span class="line">    dict_a: <span class="type">Dict</span>[<span class="built_in">str</span>, <span class="built_in">int</span>] = {<span class="string">"name_id"</span>: <span class="number">1</span>, <span class="string">"value"</span>: <span class="number">10</span>}</span><br><span class="line">    dict_b: <span class="type">Dict</span>[<span class="built_in">str</span>, <span class="built_in">int</span>] = {<span class="string">"value"</span>: <span class="number">20</span>, <span class="string">"status"</span>: <span class="number">0</span>}  <span class="comment"># "value"键与dict_a重复</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 合并操作 (|)，如果键重复，则右边字典的值优先</span></span><br><span class="line">    merged_dict: <span class="type">Dict</span>[<span class="built_in">str</span>, <span class="built_in">int</span>] = dict_a | dict_b</span><br><span class="line">    print_info(<span class="string">f"dict_a: <span class="subst">{dict_a}</span>"</span>)</span><br><span class="line">    print_info(<span class="string">f"dict_b: <span class="subst">{dict_b}</span>"</span>)</span><br><span class="line">    print_success(<span class="string">f"合并后 (merged_dict = dict_a | dict_b): <span class="subst">{merged_dict}</span>"</span>)</span><br><span class="line">    <span class="comment"># 输出: {'name_id': 1, 'value': 20, 'status': 0}</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 原地更新操作 (|=)</span></span><br><span class="line">    dict_c: <span class="type">Dict</span>[<span class="built_in">str</span>, <span class="built_in">int</span>] = {<span class="string">"item_code"</span>: <span class="number">101</span>, <span class="string">"quantity"</span>: <span class="number">5</span>}</span><br><span class="line">    dict_d: <span class="type">Dict</span>[<span class="built_in">str</span>, <span class="type">Any</span>] = {<span class="string">"quantity"</span>: <span class="number">15</span>, <span class="string">"description"</span>: <span class="string">"Gadget"</span>}</span><br><span class="line">    print_info(<span class="string">f"\n原始 dict_c: <span class="subst">{dict_c}</span>"</span>)</span><br><span class="line">    dict_c |= dict_d  <span class="comment"># dict_c 被修改</span></span><br><span class="line">    print_success(<span class="string">f"原地更新后 (dict_c |= dict_d): <span class="subst">{dict_c}</span>"</span>)</span><br><span class="line">    <span class="comment"># 输出: {'item_code': 101, 'quantity': 15, 'description': 'Gadget'}</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    print_header(<span class="string">"Python 3.9 特性演示"</span>)</span><br><span class="line">    demo_dict_merge_operators()</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h4 id="2-类型注解的内置集合类型-泛型"><a href="#2-类型注解的内置集合类型-泛型" class="headerlink" title="2. 类型注解的内置集合类型 (泛型)"></a>2. 类型注解的内置集合类型 (泛型)</h4><p>现在可以直接使用内置的集合类型 (如 <code>list</code>, <code>dict</code>, <code>tuple</code>, <code>set</code>) 作为泛型类型进行注解，而无需从 <code>typing</code> 模块导入大写版本 (如 <code>List</code>, <code>Dict</code>)。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 内置集合类型注解演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="comment"># from typing import List, Dict # 在 Python 3.9+ 中不再必需，但为了兼容性或清晰性仍可使用</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_builtin_generic_types</span>(<span class="params">names: <span class="built_in">list</span>[<span class="built_in">str</span>], scores: <span class="built_in">dict</span>[<span class="built_in">str</span>, <span class="built_in">int</span>]</span>) -&gt; <span class="built_in">tuple</span>[<span class="built_in">int</span>, <span class="built_in">float</span>]:</span><br><span class="line">    <span class="string">"""</span></span><br><span class="line"><span class="string">    演示使用内置集合类型进行类型注解。</span></span><br><span class="line"><span class="string">    参数:</span></span><br><span class="line"><span class="string">        names (list[str]): 字符串列表。</span></span><br><span class="line"><span class="string">        scores (dict[str, int]): 键为字符串，值为整数的字典。</span></span><br><span class="line"><span class="string">    返回:</span></span><br><span class="line"><span class="string">        tuple[int, float]: 包含整数和浮点数的元组。</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    print_subheader(<span class="string">"2. 类型注解的内置集合类型"</span>)</span><br><span class="line">    print_info(<span class="string">"函数定义使用了 list[str], dict[str, int], tuple[int, float] 注解。"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> names:</span><br><span class="line">        <span class="keyword">return</span> (<span class="number">0</span>, <span class="number">0.0</span>)</span><br><span class="line">        </span><br><span class="line">    total_score: <span class="built_in">int</span> = <span class="built_in">sum</span>(scores.get(name, <span class="number">0</span>) <span class="keyword">for</span> name <span class="keyword">in</span> names)</span><br><span class="line">    average_score: <span class="built_in">float</span> = total_score / <span class="built_in">len</span>(names) <span class="keyword">if</span> names <span class="keyword">else</span> <span class="number">0.0</span></span><br><span class="line">    </span><br><span class="line">    print_success(<span class="string">f"处理的名字: <span class="subst">{names}</span>"</span>)</span><br><span class="line">    print_success(<span class="string">f"处理的分数: <span class="subst">{scores}</span>"</span>)</span><br><span class="line">    print_success(<span class="string">f"返回结果: (<span class="subst">{<span class="built_in">len</span>(names)}</span>, <span class="subst">{average_score:<span class="number">.2</span>f}</span>)"</span>)</span><br><span class="line">    <span class="keyword">return</span> (<span class="built_in">len</span>(names), average_score)</span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    sample_names: <span class="built_in">list</span>[<span class="built_in">str</span>] = [<span class="string">"Alice"</span>, <span class="string">"Bob"</span>, <span class="string">"Charlie"</span>]</span><br><span class="line">    sample_scores: <span class="built_in">dict</span>[<span class="built_in">str</span>, <span class="built_in">int</span>] = {<span class="string">"Alice"</span>: <span class="number">90</span>, <span class="string">"Bob"</span>: <span class="number">85</span>, <span class="string">"David"</span>: <span class="number">70</span>} <span class="comment"># Charlie 没有分数</span></span><br><span class="line">    demo_builtin_generic_types(sample_names, sample_scores)</span><br></pre></td></tr></tbody></table></figure><h4 id="3-字符串方法：removeprefix-和-removesuffix"><a href="#3-字符串方法：removeprefix-和-removesuffix" class="headerlink" title="3. 字符串方法：removeprefix() 和 removesuffix()"></a>3. 字符串方法：<code>removeprefix()</code> 和 <code>removesuffix()</code></h4><p>这两个新的字符串方法用于移除字符串的前缀或后缀，如果存在的话。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 字符串移除前缀/后缀演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_string_prefix_suffix_removal</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示 str.removeprefix() 和 str.removesuffix()。"""</span></span><br><span class="line">    print_subheader(<span class="string">"3. 字符串方法 `removeprefix()` 和 `removesuffix()`"</span>)</span><br><span class="line"></span><br><span class="line">    filename: <span class="built_in">str</span> = <span class="string">"document_final_v2.pdf"</span></span><br><span class="line">    url: <span class="built_in">str</span> = <span class="string">"https://www.example.com/path/to/resource"</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 移除后缀</span></span><br><span class="line">    name_without_suffix: <span class="built_in">str</span> = filename.removesuffix(<span class="string">".pdf"</span>)</span><br><span class="line">    print_info(<span class="string">f"'<span class="subst">{filename}</span>'.removesuffix('.pdf') -&gt; '<span class="subst">{name_without_suffix}</span>'"</span>) <span class="comment"># 'document_final_v2'</span></span><br><span class="line">    </span><br><span class="line">    non_matching_suffix: <span class="built_in">str</span> = filename.removesuffix(<span class="string">".txt"</span>) <span class="comment"># 后缀不匹配</span></span><br><span class="line">    print_info(<span class="string">f"'<span class="subst">{filename}</span>'.removesuffix('.txt') -&gt; '<span class="subst">{non_matching_suffix}</span>' (未改变)"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 移除前缀</span></span><br><span class="line">    path_without_prefix: <span class="built_in">str</span> = url.removeprefix(<span class="string">"https://"</span>)</span><br><span class="line">    print_info(<span class="string">f"'<span class="subst">{url}</span>'.removeprefix('https://') -&gt; '<span class="subst">{path_without_prefix}</span>'"</span>) <span class="comment"># 'www.example.com/path/to/resource'</span></span><br><span class="line"></span><br><span class="line">    non_matching_prefix: <span class="built_in">str</span> = url.removeprefix(<span class="string">"http://"</span>) <span class="comment"># 前缀不匹配</span></span><br><span class="line">    print_info(<span class="string">f"'<span class="subst">{url}</span>'.removeprefix('http://') -&gt; '<span class="subst">{non_matching_prefix}</span>' (未改变)"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    demo_string_prefix_suffix_removal()</span><br></pre></td></tr></tbody></table></figure><h3 id="Python-3-10-主要特性"><a href="#Python-3-10-主要特性" class="headerlink" title="Python 3.10 主要特性"></a>Python 3.10 主要特性</h3><p>Python 3.10 带来了备受期待的结构化模式匹配 (match/case)、更简洁的联合类型表示法以及上下文管理器语法的改进。</p><h4 id="1-结构化模式匹配-match-case"><a href="#1-结构化模式匹配-match-case" class="headerlink" title="1. 结构化模式匹配 (match/case)"></a>1. 结构化模式匹配 (<code>match</code>/<code>case</code>)</h4><p>这是一种新的控制流语句，允许你根据数据的结构和值来匹配模式，并执行相应的代码块。类似于其他语言中的 <code>switch</code> 或模式匹配。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 结构化模式匹配演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> <span class="type">Any</span>, <span class="type">List</span>, <span class="type">Dict</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_structural_pattern_matching</span>(<span class="params">data_packet: <span class="type">Any</span></span>) -&gt; <span class="built_in">str</span>:</span><br><span class="line">    <span class="string">"""演示结构化模式匹配 (match/case) 处理不同数据结构。"""</span></span><br><span class="line">    print_subheader(<span class="string">f"1. 结构化模式匹配 (`match`/`case`) - 输入: <span class="subst">{data_packet}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">match</span> data_packet:</span><br><span class="line">        <span class="keyword">case</span> {<span class="string">"type"</span>: <span class="string">"user"</span>, <span class="string">"id"</span>: user_id, <span class="string">"name"</span>: <span class="built_in">str</span>(name_val)}: <span class="comment"># 匹配字典结构，并捕获值</span></span><br><span class="line">            <span class="keyword">return</span> <span class="string">f"用户数据: ID=<span class="subst">{user_id}</span>, 姓名='<span class="subst">{name_val}</span>'"</span></span><br><span class="line">        </span><br><span class="line">        <span class="keyword">case</span> {<span class="string">"type"</span>: <span class="string">"event"</span>, <span class="string">"name"</span>: event_name, <span class="string">"details"</span>: {<span class="string">"timestamp"</span>: ts, **other_details}}: <span class="comment"># 嵌套匹配和 **other_details</span></span><br><span class="line">            <span class="keyword">return</span> <span class="string">f"事件: '<span class="subst">{event_name}</span>' @ <span class="subst">{ts}</span>, 其他: <span class="subst">{other_details}</span>"</span></span><br><span class="line">            </span><br><span class="line">        <span class="keyword">case</span> [<span class="built_in">int</span>(x), <span class="built_in">int</span>(y)] <span class="keyword">if</span> x == y: <span class="comment"># 匹配列表，包含守卫条件 (guard)</span></span><br><span class="line">            <span class="keyword">return</span> <span class="string">f"两个相等的整数点: (<span class="subst">{x}</span>, <span class="subst">{y}</span>)"</span></span><br><span class="line">            </span><br><span class="line">        <span class="keyword">case</span> [<span class="built_in">str</span>(op), *numbers] <span class="keyword">if</span> op <span class="keyword">in</span> [<span class="string">"SUM"</span>, <span class="string">"AVG"</span>]: <span class="comment"># 捕获列表剩余部分到 *numbers</span></span><br><span class="line">            result: <span class="built_in">float</span> = <span class="built_in">sum</span>(numbers)</span><br><span class="line">            <span class="keyword">if</span> op == <span class="string">"AVG"</span> <span class="keyword">and</span> numbers:</span><br><span class="line">                result /= <span class="built_in">len</span>(numbers)</span><br><span class="line">            <span class="keyword">return</span> <span class="string">f"操作 '<span class="subst">{op}</span>' 在 <span class="subst">{numbers}</span> 上的结果: <span class="subst">{result:<span class="number">.2</span>f}</span>"</span></span><br><span class="line">            </span><br><span class="line">        <span class="keyword">case</span> <span class="built_in">str</span>() <span class="keyword">as</span> command <span class="keyword">if</span> command.lower() == <span class="string">"quit"</span>: <span class="comment"># 匹配字符串并用 as 绑定</span></span><br><span class="line">            <span class="keyword">return</span> <span class="string">"收到退出指令。"</span></span><br><span class="line">            </span><br><span class="line">        <span class="keyword">case</span> _: <span class="comment"># 通配符，匹配任何其他情况</span></span><br><span class="line">            <span class="keyword">return</span> <span class="string">"未知或不匹配的数据包格式。"</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    print_header(<span class="string">"Python 3.10 特性演示"</span>)</span><br><span class="line">    packets_to_test: <span class="type">List</span>[<span class="type">Any</span>] = [</span><br><span class="line">        {<span class="string">"type"</span>: <span class="string">"user"</span>, <span class="string">"id"</span>: <span class="number">101</span>, <span class="string">"name"</span>: <span class="string">"Alice"</span>},</span><br><span class="line">        {<span class="string">"type"</span>: <span class="string">"event"</span>, <span class="string">"name"</span>: <span class="string">"login"</span>, <span class="string">"details"</span>: {<span class="string">"timestamp"</span>: <span class="string">"2025-05-18T10:00:00Z"</span>, <span class="string">"source_ip"</span>: <span class="string">"***********"</span>}},</span><br><span class="line">        [<span class="number">10</span>, <span class="number">10</span>],</span><br><span class="line">        [<span class="number">5</span>, <span class="number">7</span>],</span><br><span class="line">        [<span class="string">"SUM"</span>, <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>],</span><br><span class="line">        [<span class="string">"AVG"</span>, <span class="number">10</span>, <span class="number">20</span>, <span class="number">30</span>],</span><br><span class="line">        <span class="string">"quit"</span>,</span><br><span class="line">        <span class="number">42</span></span><br><span class="line">    ]</span><br><span class="line">    <span class="keyword">for</span> i, packet <span class="keyword">in</span> <span class="built_in">enumerate</span>(packets_to_test):</span><br><span class="line">        print_info(<span class="string">f"处理包 <span class="subst">{i+<span class="number">1</span>}</span>:"</span>)</span><br><span class="line">        print_success(<span class="string">f"  结果: <span class="subst">{demo_structural_pattern_matching(packet)}</span>"</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="2-联合类型操作符"><a href="#2-联合类型操作符" class="headerlink" title="2. 联合类型操作符 (|)"></a>2. 联合类型操作符 (<code>|</code>)</h4><p>现在可以使用 <code>|</code> 操作符来表示联合类型 (Union)，使得类型注解更简洁。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 联合类型操作符演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="comment"># from typing import Union, Optional # 在 Python 3.10+ 中 Optional 和 Union 不再必需显式导入来使用 |</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_union_operator</span>(<span class="params">value: <span class="built_in">int</span> | <span class="built_in">str</span> | <span class="built_in">float</span></span>) -&gt; <span class="literal">None</span>: <span class="comment"># 使用 | 代替 Union[int, str, float]</span></span><br><span class="line">    <span class="string">"""演示使用 | 作为联合类型操作符。"""</span></span><br><span class="line">    print_subheader(<span class="string">"2. 联合类型操作符 (`|`)"</span>)</span><br><span class="line">    </span><br><span class="line">    print_info(<span class="string">f"接收到的值: <span class="subst">{value}</span> (类型: <span class="subst">{<span class="built_in">type</span>(value).__name__}</span>)"</span>)</span><br><span class="line">    <span class="keyword">if</span> <span class="built_in">isinstance</span>(value, (<span class="built_in">int</span>, <span class="built_in">float</span>)):</span><br><span class="line">        print_success(<span class="string">f"  数值处理: <span class="subst">{value * <span class="number">2</span>}</span>"</span>)</span><br><span class="line">    <span class="keyword">elif</span> <span class="built_in">isinstance</span>(value, <span class="built_in">str</span>):</span><br><span class="line">        print_success(<span class="string">f"  字符串处理: '<span class="subst">{value.upper()}</span>'"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">process_optional_data</span>(<span class="params">data: <span class="built_in">str</span> | <span class="literal">None</span> = <span class="literal">None</span></span>) -&gt; <span class="literal">None</span>: <span class="comment"># 使用 | None 代替 Optional[str]</span></span><br><span class="line">    <span class="string">"""演示使用 | None 代替 Optional。"""</span></span><br><span class="line">    <span class="keyword">if</span> data <span class="keyword">is</span> <span class="literal">None</span>:</span><br><span class="line">        print_warning(<span class="string">"  未提供数据。"</span>)</span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        print_success(<span class="string">f"  处理可选数据: '<span class="subst">{data}</span>'"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    demo_union_operator(<span class="number">100</span>)</span><br><span class="line">    demo_union_operator(<span class="string">"hello python 3.10"</span>)</span><br><span class="line">    demo_union_operator(<span class="number">3.14</span>)</span><br><span class="line">    process_optional_data(<span class="string">"Some data"</span>)</span><br><span class="line">    process_optional_data() <span class="comment"># data 为 None</span></span><br></pre></td></tr></tbody></table></figure><h4 id="3-上下文管理器改进：括号内多个-with-表达式"><a href="#3-上下文管理器改进：括号内多个-with-表达式" class="headerlink" title="3. 上下文管理器改进：括号内多个 with 表达式"></a>3. 上下文管理器改进：括号内多个 <code>with</code> 表达式</h4><p>现在可以在 <code>with</code> 语句后的括号内编写多个上下文表达式，而无需嵌套 <code>with</code> 语句，使代码更扁平。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 上下文管理器改进演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="keyword">import</span> os <span class="comment"># 用于文件操作</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_multiple_context_expressions</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示在 with 语句中使用括号包含多个上下文表达式。"""</span></span><br><span class="line">    print_subheader(<span class="string">"3. 上下文管理器改进 (括号内多个表达式)"</span>)</span><br><span class="line">    </span><br><span class="line">    input_filename: <span class="built_in">str</span> = <span class="string">"temp_input_for_with.txt"</span></span><br><span class="line">    output_filename: <span class="built_in">str</span> = <span class="string">"temp_output_for_with.txt"</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 创建临时输入文件</span></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(input_filename, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f_in_setup:</span><br><span class="line">        f_in_setup.write(<span class="string">"第一行数据。\n第二行数据。\n"</span>)</span><br><span class="line">    print_info(<span class="string">f"已创建临时输入文件: <span class="subst">{input_filename}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># Python 3.10+ 的写法</span></span><br><span class="line">        <span class="keyword">with</span> (</span><br><span class="line">            <span class="built_in">open</span>(input_filename, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> infile,</span><br><span class="line">            <span class="built_in">open</span>(output_filename, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> outfile</span><br><span class="line">        ):</span><br><span class="line">            print_info(<span class="string">f"  同时打开 '<span class="subst">{infile.name}</span>' (读) 和 '<span class="subst">{outfile.name}</span>' (写)。"</span>)</span><br><span class="line">            content: <span class="built_in">str</span> = infile.read()</span><br><span class="line">            outfile.write(content.upper()) <span class="comment"># 示例操作：转为大写后写入</span></span><br><span class="line">            print_success(<span class="string">f"  内容已从输入文件读取，处理后写入输出文件。"</span>)</span><br><span class="line"></span><br><span class="line">        <span class="comment"># 验证输出文件</span></span><br><span class="line">        <span class="keyword">if</span> os.path.exists(output_filename):</span><br><span class="line">            <span class="keyword">with</span> <span class="built_in">open</span>(output_filename, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f_check:</span><br><span class="line">                print_info(<span class="string">f"  输出文件 '<span class="subst">{output_filename}</span>' 内容:\n'''\n<span class="subst">{f_check.read().strip()}</span>\n'''"</span>)</span><br><span class="line">        </span><br><span class="line">    <span class="keyword">except</span> Exception <span class="keyword">as</span> e:</span><br><span class="line">        print_error(<span class="string">f"  处理文件时发生错误: <span class="subst">{e}</span>"</span>)</span><br><span class="line">    <span class="keyword">finally</span>:</span><br><span class="line">        <span class="comment"># 清理临时文件</span></span><br><span class="line">        <span class="keyword">if</span> os.path.exists(input_filename): os.remove(input_filename)</span><br><span class="line">        <span class="keyword">if</span> os.path.exists(output_filename): os.remove(output_filename)</span><br><span class="line">        print_info(<span class="string">"临时文件已清理。"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    demo_multiple_context_expressions()</span><br></pre></td></tr></tbody></table></figure><h3 id="Python-3-11-主要特性"><a href="#Python-3-11-主要特性" class="headerlink" title="Python 3.11 主要特性"></a>Python 3.11 主要特性</h3><p>Python 3.11 带来了显著的性能提升 (CPython Faster计划)，以及异常组、异常注释、<code>Self</code> 类型和 <code>LiteralString</code> 等新特性。</p><h4 id="1-异常组-ExceptionGroup-和异常注释-add-note"><a href="#1-异常组-ExceptionGroup-和异常注释-add-note" class="headerlink" title="1. 异常组 (ExceptionGroup) 和异常注释 (add_note())"></a>1. 异常组 (<code>ExceptionGroup</code>) 和异常注释 (<code>add_note()</code>)</h4><p><code>ExceptionGroup</code> 允许同时处理多个不相关的异常。<code>add_note()</code> 方法可以向异常对象添加上下文注释。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 异常组和异常注释演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="comment"># from exceptiongroup import ExceptionGroup # 在 Python 3.11+ 中，ExceptionGroup 是内置的</span></span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> <span class="type">List</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_exception_groups_and_notes</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示 ExceptionGroup 和 e.add_note()。"""</span></span><br><span class="line">    print_subheader(<span class="string">"1. 异常组 (`ExceptionGroup`) 和异常注释 (`add_note()`)"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 演示 add_note()</span></span><br><span class="line">    print_info(<span class="string">"演示 e.add_note():"</span>)</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        user_input: <span class="built_in">str</span> = <span class="string">"non_numeric_value"</span></span><br><span class="line">        value: <span class="built_in">int</span> = <span class="built_in">int</span>(user_input)</span><br><span class="line">    <span class="keyword">except</span> ValueError <span class="keyword">as</span> e:</span><br><span class="line">        e.add_note(<span class="string">f"输入的值 '<span class="subst">{user_input}</span>' 无法转换为整数。"</span>) <span class="comment"># 添加注释</span></span><br><span class="line">        e.add_note(<span class="string">"请确保输入的是有效的数字。"</span>)</span><br><span class="line">        <span class="comment"># raise # 如果要重新引发带注释的异常</span></span><br><span class="line">        print_warning(<span class="string">f"捕获到 ValueError (带注释):"</span>)</span><br><span class="line">        <span class="comment"># 要查看注释，你需要捕获异常并访问其 __notes__ 属性，或让它传播到顶层由解释器打印</span></span><br><span class="line">        <span class="comment"># print(f"  异常: {e}")</span></span><br><span class="line">        <span class="comment"># if hasattr(e, "__notes__"):</span></span><br><span class="line">        <span class="comment">#     for note in e.__notes__:</span></span><br><span class="line">        <span class="comment">#         print(f"    Note: {note}")</span></span><br><span class="line">        <span class="comment"># Python 3.11 的默认异常回溯会显示注释</span></span><br><span class="line">        <span class="keyword">try</span>:</span><br><span class="line">            <span class="keyword">raise</span> e <span class="comment"># 重新引发以观察其默认打印效果</span></span><br><span class="line">        <span class="keyword">except</span> ValueError <span class="keyword">as</span> e_raised:</span><br><span class="line">            print_error(<span class="string">f"  重新引发的异常 (其回溯应包含注释):\n<span class="subst">{e_raised}</span>"</span>)</span><br><span class="line">            <span class="keyword">if</span> <span class="built_in">hasattr</span>(e_raised, <span class="string">"__notes__"</span>): <span class="comment"># Python 3.11+</span></span><br><span class="line">                 print_info(<span class="string">"  异常包含的注释 (__notes__):"</span>)</span><br><span class="line">                 <span class="keyword">for</span> note_item <span class="keyword">in</span> e_raised.__notes__:</span><br><span class="line">                     <span class="built_in">print</span>(<span class="string">f"    - <span class="subst">{note_item}</span>"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 演示 ExceptionGroup (概念性)</span></span><br><span class="line">    print_info(<span class="string">"\n演示 ExceptionGroup (概念性):"</span>)</span><br><span class="line">    <span class="comment"># 假设我们有多个并发任务，每个都可能失败</span></span><br><span class="line">    exceptions_list: <span class="type">List</span>[Exception] = []</span><br><span class="line">    task1_result, task2_result = <span class="literal">None</span>, <span class="literal">None</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 模拟任务1</span></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># task1_result = 10 / 0</span></span><br><span class="line">        print_info(<span class="string">"  任务1: 模拟成功。"</span>)</span><br><span class="line">        task1_result = <span class="string">"Success"</span></span><br><span class="line">    <span class="keyword">except</span> ZeroDivisionError <span class="keyword">as</span> e1:</span><br><span class="line">        e1.add_note(<span class="string">"任务1 (除法运算) 失败。"</span>)</span><br><span class="line">        exceptions_list.append(e1)</span><br><span class="line">        print_warning(<span class="string">"  任务1: 捕获 ZeroDivisionError。"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 模拟任务2</span></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># data = {}</span></span><br><span class="line">        <span class="comment"># value = data["missing_key"]</span></span><br><span class="line">        print_info(<span class="string">"  任务2: 模拟成功。"</span>)</span><br><span class="line">        task2_result = <span class="string">"OK"</span></span><br><span class="line">    <span class="keyword">except</span> KeyError <span class="keyword">as</span> e2:</span><br><span class="line">        e2.add_note(<span class="string">"任务2 (字典键查找) 失败。"</span>)</span><br><span class="line">        exceptions_list.append(e2)</span><br><span class="line">        print_warning(<span class="string">"  任务2: 捕获 KeyError。"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">if</span> exceptions_list:</span><br><span class="line">        <span class="comment"># 如果有多个异常，可以用 ExceptionGroup 包装它们</span></span><br><span class="line">        <span class="comment"># 在 Python 3.11+ 中，ExceptionGroup 是内置的</span></span><br><span class="line">        <span class="comment"># from exceptiongroup import ExceptionGroup # 对于旧版本，可能需要此库</span></span><br><span class="line">        error_group = ExceptionGroup(<span class="string">"在执行多个任务时发生错误"</span>, exceptions_list)</span><br><span class="line">        print_error(<span class="string">f"  发生了异常组: <span class="subst">{error_group}</span>"</span>)</span><br><span class="line">        <span class="comment"># 处理异常组:</span></span><br><span class="line">        <span class="comment"># try:</span></span><br><span class="line">        <span class="comment">#     raise error_group</span></span><br><span class="line">        <span class="comment"># except* ValueError as eg_vg: # Python 3.11+ 的 except* 语法</span></span><br><span class="line">        <span class="comment">#     print_warning(f"  捕获到异常组中的 ValueError: {eg_vg.exceptions}")</span></span><br><span class="line">        <span class="comment"># except* TypeError as eg_tg:</span></span><br><span class="line">        <span class="comment">#     print_warning(f"  捕获到异常组中的 TypeError: {eg_tg.exceptions}")</span></span><br><span class="line">        print_info(<span class="string">"  (在实际代码中，会使用 try...except* 来处理 ExceptionGroup)"</span>)</span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        print_success(<span class="string">"  所有模拟任务均成功完成。"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    print_header(<span class="string">"Python 3.11 特性演示"</span>)</span><br><span class="line">    demo_exception_groups_and_notes()</span><br></pre></td></tr></tbody></table></figure><h4 id="2-Self-类型"><a href="#2-Self-类型" class="headerlink" title="2. Self 类型"></a>2. <code>Self</code> 类型</h4><p><code>typing.Self</code> (或仅 <code>Self</code>，如果 <code>from typing import Self</code>) 提供了一种更简洁、更准确的方式来注解那些返回类自身实例的方法 (例如构造函数、工厂方法、或返回修改后自身的链式调用方法)。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === Self 类型演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> Self <span class="comment"># Python 3.11+ (或 typing_extensions.Self for older)</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_self_type</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示 typing.Self 类型的使用。"""</span></span><br><span class="line">    print_subheader(<span class="string">"2. `Self` 类型"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">class</span> <span class="title class_">ConfigBuilder</span>:</span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, name: <span class="built_in">str</span></span>):</span><br><span class="line">            <span class="variable language_">self</span>.name: <span class="built_in">str</span> = name</span><br><span class="line">            <span class="variable language_">self</span>.settings: <span class="built_in">dict</span> = {}</span><br><span class="line">            print_info(<span class="string">f"  ConfigBuilder '<span class="subst">{name}</span>' 已创建。"</span>)</span><br><span class="line"></span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">set_option</span>(<span class="params">self, key: <span class="built_in">str</span>, value: <span class="type">Any</span></span>) -&gt; Self: <span class="comment"># 返回类型是 Self</span></span><br><span class="line">            <span class="string">"""设置一个配置选项并返回自身，以便链式调用。"""</span></span><br><span class="line">            <span class="variable language_">self</span>.settings[key] = value</span><br><span class="line">            print_info(<span class="string">f"    选项 '<span class="subst">{key}</span>' 设置为 '<span class="subst">{value}</span>'"</span>)</span><br><span class="line">            <span class="keyword">return</span> <span class="variable language_">self</span> <span class="comment"># 返回当前类的实例</span></span><br><span class="line"></span><br><span class="line"><span class="meta">        @classmethod</span></span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">create_default</span>(<span class="params">cls, name: <span class="built_in">str</span></span>) -&gt; Self: <span class="comment"># 类方法返回 Self</span></span><br><span class="line">            <span class="string">"""创建一个带有默认设置的实例。"""</span></span><br><span class="line">            print_info(<span class="string">f"  调用类方法 create_default for '<span class="subst">{name}</span>'"</span>)</span><br><span class="line">            instance = cls(name) <span class="comment"># cls 指向 ConfigBuilder (或其子类)</span></span><br><span class="line">            instance.set_option(<span class="string">"default_timeout"</span>, <span class="number">30</span>)</span><br><span class="line">            <span class="keyword">return</span> instance</span><br><span class="line">            </span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">build</span>(<span class="params">self</span>) -&gt; <span class="built_in">dict</span>:</span><br><span class="line">            print_info(<span class="string">f"  构建配置 '<span class="subst">{self.name}</span>'..."</span>)</span><br><span class="line">            <span class="keyword">return</span> {<span class="string">"name"</span>: <span class="variable language_">self</span>.name, <span class="string">"settings"</span>: <span class="variable language_">self</span>.settings}</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 使用示例</span></span><br><span class="line">    builder1: ConfigBuilder = ConfigBuilder(<span class="string">"App1"</span>)</span><br><span class="line">    builder1.set_option(<span class="string">"port"</span>, <span class="number">8080</span>).set_option(<span class="string">"retries"</span>, <span class="number">3</span>) <span class="comment"># 链式调用</span></span><br><span class="line">    config1: <span class="built_in">dict</span> = builder1.build()</span><br><span class="line">    print_success(<span class="string">f"  Config1: <span class="subst">{config1}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    builder2: ConfigBuilder = ConfigBuilder.create_default(<span class="string">"App2_Default"</span>)</span><br><span class="line">    builder2.set_option(<span class="string">"theme"</span>, <span class="string">"dark"</span>)</span><br><span class="line">    config2: <span class="built_in">dict</span> = builder2.build()</span><br><span class="line">    print_success(<span class="string">f"  Config2 (from default): <span class="subst">{config2}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 演示 Self 在子类中的行为</span></span><br><span class="line">    <span class="keyword">class</span> <span class="title class_">AdvancedConfigBuilder</span>(<span class="title class_ inherited__">ConfigBuilder</span>):</span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">enable_feature_x</span>(<span class="params">self</span>) -&gt; Self: <span class="comment"># 返回的仍然是 AdvancedConfigBuilder 类型</span></span><br><span class="line">            <span class="variable language_">self</span>.set_option(<span class="string">"feature_x"</span>, <span class="literal">True</span>)</span><br><span class="line">            <span class="keyword">return</span> <span class="variable language_">self</span></span><br><span class="line">            </span><br><span class="line">    adv_builder = AdvancedConfigBuilder(<span class="string">"AdvApp"</span>).enable_feature_x().set_option(<span class="string">"mode"</span>, <span class="string">"expert"</span>)</span><br><span class="line">    adv_config = adv_builder.build()</span><br><span class="line">    print_success(<span class="string">f"  AdvancedConfig: <span class="subst">{adv_config}</span>"</span>)</span><br><span class="line">    print_info(<span class="string">f"  adv_builder 的类型是: <span class="subst">{<span class="built_in">type</span>(adv_builder).__name__}</span> (应为 AdvancedConfigBuilder)"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    demo_self_type()</span><br></pre></td></tr></tbody></table></figure><h4 id="3-LiteralString-类型"><a href="#3-LiteralString-类型" class="headerlink" title="3. LiteralString 类型"></a>3. <code>LiteralString</code> 类型</h4><p><code>typing.LiteralString</code> 用于注解那些值必须是字面量字符串（即直接在代码中写出的字符串，而不是通过变量拼接或函数调用生成的字符串）的参数。这有助于静态分析工具检测潜在的安全风险，例如 SQL 注入。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === LiteralString 类型演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> LiteralString <span class="comment"># Python 3.11+</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_literal_string_type</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示 typing.LiteralString 类型。"""</span></span><br><span class="line">    print_subheader(<span class="string">"3. `LiteralString` 类型"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">execute_safe_query</span>(<span class="params">query: LiteralString, params: <span class="built_in">tuple</span> = (<span class="params"></span>)</span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">        <span class="string">"""</span></span><br><span class="line"><span class="string">        一个模拟的安全数据库查询函数。</span></span><br><span class="line"><span class="string">        它只接受字面量字符串作为查询语句，以防止SQL注入。</span></span><br><span class="line"><span class="string">        """</span></span><br><span class="line">        print_info(<span class="string">f"  尝试执行安全查询: '<span class="subst">{query}</span>' (参数: <span class="subst">{params}</span>)"</span>)</span><br><span class="line">        <span class="comment"># 在实际应用中，这里会与数据库交互，并可能使用参数化查询</span></span><br><span class="line">        print_success(<span class="string">f"    模拟执行查询: <span class="subst">{query}</span> (参数化: <span class="subst">{params}</span>)"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 正确的用法：传递字符串字面量</span></span><br><span class="line">    execute_safe_query(<span class="string">"SELECT * FROM users WHERE status = 'active'"</span>)</span><br><span class="line">    execute_safe_query(<span class="string">"SELECT name, email FROM customers WHERE id = %s"</span>, params=(<span class="number">101</span>,))</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 错误的用法：传递通过变量或拼接生成的字符串</span></span><br><span class="line">    <span class="comment"># 静态类型检查器 (如 MyPy) 会对以下调用发出警告</span></span><br><span class="line">    table_name_var: <span class="built_in">str</span> = <span class="string">"users"</span> </span><br><span class="line">    <span class="comment"># execute_safe_query(f"SELECT * FROM {table_name_var}") # MyPy 会警告: Expected LiteralString</span></span><br><span class="line">    </span><br><span class="line">    user_input_column: <span class="built_in">str</span> = <span class="built_in">input</span>(<span class="string">"请输入要查询的列名 (例如 'username'): "</span>) <span class="keyword">if</span> os.getenv(<span class="string">"ASK_INPUT"</span>) <span class="keyword">else</span> <span class="string">"username"</span> <span class="comment"># 模拟用户输入</span></span><br><span class="line">    <span class="comment"># execute_safe_query(f"SELECT {user_input_column} FROM data") # MyPy 会警告</span></span><br><span class="line"></span><br><span class="line">    print_warning(<span class="string">"  注意: LiteralString 主要用于静态类型检查。"</span>)</span><br><span class="line">    print_info(<span class="string">"  如果上面的注释行被取消且使用了 MyPy，它会标记出潜在的类型错误。"</span>)</span><br><span class="line">    print_info(<span class="string">"  在运行时，Python 本身不强制 LiteralString。"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    <span class="comment"># os.environ["ASK_INPUT"] = "1" # 取消注释以在运行时测试 input() 部分 (可选)</span></span><br><span class="line">    demo_literal_string_type()</span><br><span class="line">    <span class="comment"># if "ASK_INPUT" in os.environ: del os.environ["ASK_INPUT"]</span></span><br></pre></td></tr></tbody></table></figure><h3 id="Python-3-12-主要特性"><a href="#Python-3-12-主要特性" class="headerlink" title="Python 3.12 主要特性"></a>Python 3.12 主要特性</h3><p>Python 3.12 继续改进类型系统，带来了更简洁的泛型类和函数语法，以及 f-string 的进一步增强。</p><h4 id="1-泛型类和函数的简化语法-PEP-695"><a href="#1-泛型类和函数的简化语法-PEP-695" class="headerlink" title="1. 泛型类和函数的简化语法 (PEP 695)"></a>1. 泛型类和函数的简化语法 (PEP 695)</h4><p>现在可以使用更简洁的方括号语法直接在类或函数定义中声明类型参数，而无需从 <code>typing</code> 导入 <code>TypeVar</code>。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 简化泛型语法演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="comment"># from typing import TypeVar # 在 Python 3.12+ 中 TypeVar 可以通过新语法隐式定义</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_simplified_generics_syntax</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示 Python 3.12+ 中简化的泛型类和函数语法。"""</span></span><br><span class="line">    print_subheader(<span class="string">"1. 泛型类和函数的简化语法 (PEP 695)"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 简化泛型函数定义</span></span><br><span class="line">    <span class="comment"># 旧方法: T = TypeVar('T'); def old_first(items: list[T]) -&gt; T | None: ...</span></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">new_first</span>[T](items: <span class="built_in">list</span>[T]) -&gt; T | <span class="literal">None</span>: <span class="comment"># 直接在函数名后用 [] 声明类型参数 T</span></span><br><span class="line">        <span class="string">"""获取列表的第一个元素，如果列表为空则返回 None。"""</span></span><br><span class="line">        print_info(<span class="string">f"    new_first 调用，items: <span class="subst">{items}</span>"</span>)</span><br><span class="line">        <span class="keyword">return</span> items[<span class="number">0</span>] <span class="keyword">if</span> items <span class="keyword">else</span> <span class="literal">None</span></span><br><span class="line"></span><br><span class="line">    print_success(<span class="string">f"  new_first([10, 20]): <span class="subst">{new_first([<span class="number">10</span>, <span class="number">20</span>])}</span>"</span>)</span><br><span class="line">    print_success(<span class="string">f"  new_first(['a', 'b']): <span class="subst">{new_first([<span class="string">'a'</span>, <span class="string">'b'</span>])}</span>"</span>)</span><br><span class="line">    print_success(<span class="string">f"  new_first([]): <span class="subst">{new_first([])}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 简化泛型类定义</span></span><br><span class="line">    <span class="comment"># 旧方法: U = TypeVar('U'); class OldBox(Generic[U]): ...</span></span><br><span class="line">    <span class="keyword">class</span> <span class="title class_">NewBox</span>[U]: <span class="comment"># 直接在类名后用 [] 声明类型参数 U</span></span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, content: U</span>):</span><br><span class="line">            <span class="variable language_">self</span>.content: U = content</span><br><span class="line">        </span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">get_content</span>(<span class="params">self</span>) -&gt; U:</span><br><span class="line">            <span class="keyword">return</span> <span class="variable language_">self</span>.content</span><br><span class="line">        </span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">__repr__</span>(<span class="params">self</span>) -&gt; <span class="built_in">str</span>:</span><br><span class="line">            <span class="keyword">return</span> <span class="string">f"NewBox[<span class="subst">{<span class="built_in">type</span>(self.content).__name__}</span>](content=<span class="subst">{self.content!r}</span>)"</span></span><br><span class="line"></span><br><span class="line">    int_box = NewBox[<span class="built_in">int</span>](<span class="number">123</span>) <span class="comment"># 可以显式指定类型</span></span><br><span class="line">    str_box = NewBox(<span class="string">"Python 3.12"</span>) <span class="comment"># 也可以让类型检查器推断</span></span><br><span class="line"></span><br><span class="line">    print_success(<span class="string">f"  <span class="subst">{int_box}</span> -&gt; content: <span class="subst">{int_box.get_content()}</span>"</span>)</span><br><span class="line">    print_success(<span class="string">f"  <span class="subst">{str_box}</span> -&gt; content: <span class="subst">{str_box.get_content()}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    print_header(<span class="string">"Python 3.12 特性演示"</span>)</span><br><span class="line">    demo_simplified_generics_syntax()</span><br></pre></td></tr></tbody></table></figure><h4 id="2-类型别名与类型变量语法改进-type-语句-PEP-695"><a href="#2-类型别名与类型变量语法改进-type-语句-PEP-695" class="headerlink" title="2. 类型别名与类型变量语法改进 (type 语句) (PEP 695)"></a>2. 类型别名与类型变量语法改进 (<code>type</code> 语句) (PEP 695)</h4><p>Python 3.12 引入了 <code>type</code> 语句作为创建类型别名和类型变量的更清晰、更正式的方式。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === type 语句演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> <span class="type">List</span> <span class="comment"># List 仍可用于兼容性或复杂场景</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Python 3.12+ 的 type 语句</span></span><br><span class="line"><span class="comment"># 定义类型别名</span></span><br><span class="line"><span class="built_in">type</span> Point = <span class="built_in">tuple</span>[<span class="built_in">float</span>, <span class="built_in">float</span>]</span><br><span class="line"><span class="built_in">type</span> IntList = <span class="built_in">list</span>[<span class="built_in">int</span>]</span><br><span class="line"><span class="comment"># 定义类型变量 (TypeVar)</span></span><br><span class="line"><span class="built_in">type</span> T = <span class="built_in">str</span> | <span class="built_in">int</span> <span class="comment"># T 现在是一个 TypeVar，可以绑定到 str 或 int</span></span><br><span class="line"><span class="built_in">type</span> K_contra = contravariant <span class="built_in">str</span> <span class="comment"># 定义逆变类型变量 K，上界为 str</span></span><br><span class="line"><span class="built_in">type</span> V_co = covariant <span class="built_in">int</span> <span class="comment"># 定义协变类型变量 V，上界为 int</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_type_statement</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示 Python 3.12+ 中使用 type 语句定义类型别名和类型变量。"""</span></span><br><span class="line">    print_subheader(<span class="string">"2. 类型别名与类型变量语法改进 (`type` 语句)"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">process_point</span>(<span class="params">p: Point</span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">        print_info(<span class="string">f"    处理点: <span class="subst">{p}</span>, 类型: <span class="subst">{<span class="built_in">type</span>(p)}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">sum_int_list</span>(<span class="params">numbers: IntList</span>) -&gt; <span class="built_in">int</span>:</span><br><span class="line">        <span class="keyword">return</span> <span class="built_in">sum</span>(numbers)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">process_generic_var</span>(<span class="params">item: T</span>) -&gt; <span class="literal">None</span>: <span class="comment"># T 是 TypeVar (str | int)</span></span><br><span class="line">        <span class="keyword">if</span> <span class="built_in">isinstance</span>(item, <span class="built_in">str</span>):</span><br><span class="line">            print_info(<span class="string">f"    泛型变量 T (字符串): <span class="subst">{item.upper()}</span>"</span>)</span><br><span class="line">        <span class="keyword">elif</span> <span class="built_in">isinstance</span>(item, <span class="built_in">int</span>):</span><br><span class="line">            print_info(<span class="string">f"    泛型变量 T (整数): <span class="subst">{item * <span class="number">100</span>}</span>"</span>)</span><br><span class="line">            </span><br><span class="line">    my_point: Point = (<span class="number">1.0</span>, <span class="number">2.5</span>)</span><br><span class="line">    process_point(my_point)</span><br><span class="line">    <span class="comment"># process_point([1.0, 2.5]) # MyPy 会报错，类型不匹配</span></span><br><span class="line">    </span><br><span class="line">    my_numbers: IntList = [<span class="number">10</span>, <span class="number">20</span>, <span class="number">30</span>]</span><br><span class="line">    print_success(<span class="string">f"  整数列表 <span class="subst">{my_numbers}</span> 的和: <span class="subst">{sum_int_list(my_numbers)}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    process_generic_var(<span class="string">"hello"</span>)</span><br><span class="line">    process_generic_var(<span class="number">5</span>)</span><br><span class="line">    <span class="comment"># process_generic_var(3.14) # MyPy 会报错，因为 T 只能是 str 或 int</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    demo_type_statement()</span><br></pre></td></tr></tbody></table></figure><h4 id="3-f-string-语法进一步改进-PEP-701"><a href="#3-f-string-语法进一步改进-PEP-701" class="headerlink" title="3. f-string 语法进一步改进 (PEP 701)"></a>3. f-string 语法进一步改进 (PEP 701)</h4><p>Python 3.12 中的 f-string 更加强大和灵活，主要体现在：</p><ul><li><strong>引号重用</strong>：现在可以在 f-string 表达式内部重用与包围 f-string 自身所用类型相同的引号，无需转义。</li><li><strong>多行表达式和注释</strong>：f-string 表达式部分现在可以跨越多行，并且可以包含 <code>#</code> 注释。</li><li><strong>反斜杠</strong>：表达式部分可以使用反斜杠。</li></ul><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === f-string 语法改进演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_fstring_enhancements</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示 Python 3.12+ 中 f-string 的语法改进。"""</span></span><br><span class="line">    print_subheader(<span class="string">"3. f-string 语法进一步改进 (PEP 701)"</span>)</span><br><span class="line"></span><br><span class="line">    user_data: <span class="built_in">dict</span> = {<span class="string">"name"</span>: <span class="string">"Alice"</span>, <span class="string">"city"</span>: <span class="string">"Wonderland"</span>}</span><br><span class="line">    user_id: <span class="built_in">int</span> = <span class="number">101</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 引号重用</span></span><br><span class="line">    <span class="comment"># 在 3.12 之前: f"User data: {user_data['name']}" (需要不同类型的引号或转义)</span></span><br><span class="line">    <span class="comment"># 或者 f'User data: {user_data["name"]}'</span></span><br><span class="line">    message_quotes: <span class="built_in">str</span> = <span class="string">f"User: <span class="subst">{user_data[<span class="string">'name'</span>]}</span>"</span> <span class="comment"># 3.12+: 可以重用引号</span></span><br><span class="line">    print_success(<span class="string">f"  引号重用: <span class="subst">{message_quotes}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    message_quotes_single: <span class="built_in">str</span> = <span class="string">f'ID: <span class="subst">{user_data.get(<span class="string">"name"</span>, <span class="string">"N/A"</span>)!r}</span>'</span> <span class="comment"># 外层单引号，内部仍可方便使用双引号</span></span><br><span class="line">    print_success(<span class="string">f"  引号重用 (单引号): <span class="subst">{message_quotes_single}</span>"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 多行表达式和注释</span></span><br><span class="line">    <span class="comment"># (注意：在多行 f-string 表达式中，注释必须在自己的行上)</span></span><br><span class="line">    items_list: <span class="built_in">list</span>[<span class="built_in">int</span>] = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>, <span class="number">10</span>]</span><br><span class="line">    formatted_multiline: <span class="built_in">str</span> = (</span><br><span class="line">        <span class="string">f"Data summary for user <span class="subst">{user_id}</span>:\n"</span></span><br><span class="line">        <span class="string">f"  - Items count: <span class="subst">{<span class="built_in">len</span>(items_list)}</span>\n"</span></span><br><span class="line">        <span class="string">f"  - Even items sum: <span class="subst">{<span class="built_in">sum</span>(</span></span></span><br><span class="line"><span class="subst"><span class="string">            x </span></span></span><br><span class="line"><span class="subst"><span class="string">            <span class="keyword">for</span> x <span class="keyword">in</span> items_list </span></span></span><br><span class="line"><span class="subst"><span class="string">            <span class="keyword">if</span> x % <span class="number">2</span> == <span class="number">0</span>  # 这是一个 f-string 表达式内部的注释</span></span></span><br><span class="line"><span class="subst"><span class="string">        )}</span>\n"</span> <span class="comment"># 多行表达式</span></span><br><span class="line">        <span class="string">f"  - User info: <span class="subst">{user_data[<span class="string">'name'</span>].upper() # .upper() 方法调用</span></span></span><br><span class="line"><span class="subst"><span class="string">                          + <span class="string">' from '</span> + user_data[<span class="string">'city'</span>]}</span>"</span> </span><br><span class="line">    )</span><br><span class="line">    print_info(<span class="string">"  f-string 带多行表达式和注释:"</span>)</span><br><span class="line">    <span class="built_in">print</span>(formatted_multiline)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 反斜杠 (虽然不常见，但现在允许)</span></span><br><span class="line">    <span class="comment"># path_example = f"Path: {'C:\\Users\\<USER>\\nb'.split('\\n')}" # 现在更灵活</span></span><br><span class="line">    <span class="comment"># print_success(f"  f-string 中使用反斜杠: {fstring_backslash}")</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    demo_fstring_enhancements()</span><br></pre></td></tr></tbody></table></figure><h3 id="Python-3-13-主要特性"><a href="#Python-3-13-主要特性" class="headerlink" title="Python 3.13 主要特性"></a>Python 3.13 主要特性</h3><p>Python 3.13 带来了包括新的 <code>@override</code> 装饰器、JIT 编译器的初步实验（可能不直接体现在日常语法中）、以及对现有特性的一些改进。</p><h4 id="1-typing-override-装饰器-PEP-698"><a href="#1-typing-override-装饰器-PEP-698" class="headerlink" title="1. @typing.override 装饰器 (PEP 698)"></a>1. <code>@typing.override</code> 装饰器 (PEP 698)</h4><p>一个新的装饰器 <code>@override</code> (来自 <code>typing</code> 模块，或 Python 3.12+ 的 <code>typing_extensions</code> 中已有) 用于明确指示一个方法意图覆盖其父类中的同名方法。这有助于静态类型检查器和开发者捕捉由于意外拼写错误或签名不匹配导致的覆盖失败。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === @override 装饰器演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> *</span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> override <span class="comment"># Python 3.13+ (或 from typing_extensions import override for 3.12)</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demo_override_decorator</span>() -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示 @typing.override 装饰器。"""</span></span><br><span class="line">    print_subheader(<span class="string">"1. `@typing.override` 装饰器 (PEP 698)"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">class</span> <span class="title class_">BaseDocument</span>:</span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">get_title</span>(<span class="params">self</span>) -&gt; <span class="built_in">str</span>:</span><br><span class="line">            <span class="keyword">return</span> <span class="string">"Generic Document"</span></span><br><span class="line"></span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">render_content</span>(<span class="params">self</span>) -&gt; <span class="built_in">str</span>:</span><br><span class="line">            <span class="keyword">raise</span> NotImplementedError</span><br><span class="line"></span><br><span class="line">    <span class="keyword">class</span> <span class="title class_">Article</span>(<span class="title class_ inherited__">BaseDocument</span>):</span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, title: <span class="built_in">str</span>, body: <span class="built_in">str</span></span>):</span><br><span class="line">            <span class="variable language_">self</span>._title = title</span><br><span class="line">            <span class="variable language_">self</span>._body = body</span><br><span class="line">            </span><br><span class="line"><span class="meta">        @override </span><span class="comment"># 明确表示此方法覆盖父类方法</span></span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">get_title</span>(<span class="params">self</span>) -&gt; <span class="built_in">str</span>:</span><br><span class="line">            <span class="keyword">return</span> <span class="string">f"Article: <span class="subst">{self._title}</span>"</span></span><br><span class="line"></span><br><span class="line"><span class="meta">        @override </span><span class="comment"># 明确表示此方法覆盖父类方法</span></span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">render_content</span>(<span class="params">self</span>) -&gt; <span class="built_in">str</span>:</span><br><span class="line">            <span class="keyword">return</span> <span class="string">f"&lt;h1&gt;<span class="subst">{self.get_title()}</span>&lt;/h1&gt;&lt;p&gt;<span class="subst">{self._body}</span>&lt;/p&gt;"</span></span><br><span class="line">            </span><br><span class="line">        <span class="comment"># 如果写成 @override def get_titel(self) -&gt; str: ... (拼写错误)</span></span><br><span class="line">        <span class="comment"># MyPy 这类类型检查器会报错，因为它没有覆盖任何父类方法。</span></span><br><span class="line"></span><br><span class="line">    my_article = Article(<span class="string">"Python 3.13 News"</span>, <span class="string">"Override decorator is here!"</span>)</span><br><span class="line">    print_success(<span class="string">f"  Article Title: <span class="subst">{my_article.get_title()}</span>"</span>)</span><br><span class="line">    print_info(<span class="string">f"  Rendered Article (simulated):\n<span class="subst">{my_article.render_content()}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 尝试覆盖一个不存在的基类方法 (MyPy会警告)</span></span><br><span class="line">    <span class="comment"># class BadSubclass(BaseDocument):</span></span><br><span class="line">    <span class="comment">#     @override</span></span><br><span class="line">    <span class="comment">#     def non_existent_method_in_base(self) -&gt; None:</span></span><br><span class="line">    <span class="comment">#         pass</span></span><br><span class="line"></span><br><span class="line">    print_info(<span class="string">"  @override 帮助在静态检查时发现覆盖错误。"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    print_header(<span class="string">"Python 3.13 (部分) 特性演示"</span>)</span><br><span class="line">    demo_override_decorator()</span><br></pre></td></tr></tbody></table></figure></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/55902.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/55902.html&quot;)">Python（二十一）：第二十章：Python 语法新特性总结</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/55902.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=Python（二十一）：第二十章：Python 语法新特性总结&amp;url=https://prorise666.site/posts/55902.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Python<span class="categoryesPageCount">22</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Python基础知识总汇<span class="tagsPageCount">22</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/27024.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Python（二十）：第十九章 标准内置库详解</div></div></a></div><div class="next-post pull-right"><a href="/posts/43091.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Python（二十二）：第二十一章：项目结构规范与最佳实践</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/17730.html" title="Python（一）：Python 语言特性"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（一）：Python 语言特性</div></div></a></div><div><a href="/posts/45310.html" title="Python（七）：第六章：条件循环分支"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（七）：第六章：条件循环分支</div></div></a></div><div><a href="/posts/8019.html" title="Python（三）：第二章：转义字符"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（三）：第二章：转义字符</div></div></a></div><div><a href="/posts/43091.html" title="Python（二十二）：第二十一章：项目结构规范与最佳实践"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十二）：第二十一章：项目结构规范与最佳实践</div></div></a></div><div><a href="/posts/56572.html" title="Python（九）：第八章： 函数知识总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（九）：第八章： 函数知识总结</div></div></a></div><div><a href="/posts/2501.html" title="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Python（二十一）：第二十章：Python 语法新特性总结",date:"2025-04-19 12:13:45",updated:"2025-07-14 16:45:54",tags:["Python基础知识总汇"],categories:["后端技术","Python"],content:'\n## 第二十章：Python 语法新特性总结 (3.8 \\~ 3.13)\n\nPython 语言在不断发展，每个新版本都会带来一些有用的新特性、性能改进和语法糖，使得编写代码更加高效和愉悦。本章将简要回顾从 Python 3.8 到 3.13 版本引入的一些值得关注的语法和核心库特性。\n\n### Python 3.8 主要特性\n\nPython 3.8 带来了赋值表达式（海象操作符）、仅位置参数、f-string 调试支持等重要更新。\n\n#### 1\\. 赋值表达式 (海象操作符) `:=`\n\n海象操作符 `:=` 允许你在表达式内部为变量赋值。这可以在某些情况下简化代码，尤其是在 `if` 语句或 `while` 循环中，当你需要先计算一个值，然后判断这个值，并且后续还想使用这个值时。\n\n```python\n# === 海象操作符演示 ===\nfrom print_utils import *  # 假设 print_utils.py 可用\nfrom typing import List, Any\n\n\ndef demo_walrus_operator(data: List[Any]) -> None:\n    """演示海象操作符 (:=) 的使用。"""\n    print_subheader("1. 海象操作符 (Assignment Expressions) `:=`")\n\n    # 示例1: 在 if 条件中赋值并使用\n    if (n := len(data)) > 3:\n        print_success(f"列表长度为 {n}，大于3。前三个元素: {data[:3]}")\n    else:\n        print_info(f"列表长度为 {n}，不大于3。")\n\n\nif __name__ == \'__main__\':\n    print_header("Python 3.8 特性演示")\n    sample_data_list: List[Any] = [1, "abc", [1, 2], 3]\n    demo_walrus_operator(sample_data_list)\n\n```\n\n#### 2\\. 仅位置参数 (`/`)\n\n函数定义中可以使用 `/` 来指明其前面的参数只能通过位置传递，不能作为关键字参数传递。这有助于库作者设计更清晰、更不易出错的 API。\n\n```python\n# === 仅位置参数演示 ===\nfrom print_utils import *\n\ndef demo_positional_only_args() -> None:\n    """演示仅位置参数的使用。"""\n    print_subheader("2. 仅位置参数 (`/`)")\n\n    def create_user(user_id: int, name: str, /, age: int = 30, city: str = "Unknown") -> dict:\n        """\n        创建用户信息字典。\n        user_id 和 name 只能通过位置传递。\n        age 和 city 可以是位置参数或关键字参数。\n        """\n        return {"id": user_id, "name": name, "age": age, "city": city}\n    \n    print_success(str(create_user(1, "Alice")))  # 正确：位置参数\n    print_success(str(create_user(1, "Alice", age=25)))  # 正确：位置参数和关键字参数\n    print_success(str(create_user(1, "Alice", 25)))  # 正确：age 作为位置参数也是可以的\n    \n    # 演示真正的错误情况\n    try:\n        create_user(id=1, name="Alice")  # 错误：/ 之前的参数不能用关键字方式传递\n    except TypeError as e:\n        print_error(f"在尝试执行操作时发生错误: {e}")\n\n\n\nif __name__ == \'__main__\':\n    # 此处不重复 print_header，假设按特性运行\n    demo_positional_only_args()\n```\n\n#### 3\\. f-string 增强 (`=`) 用于调试\n\nf-string 现在支持 `=` 说明符，可以在输出中包含表达式文本及其计算结果，非常便于调试。\n\n```python\n# === f-string 调试增强演示 ===\nfrom print_utils import *\n\ndef demo_fstring_debug() -> None:\n    """演示 f-string 中 `=` 的调试功能。"""\n    print_subheader("3. f-string 增强 (`=`) 用于调试")\n    \n    app_name: str = "MyApplication"\n    app_version: float = 2.1\n    user_count: int = 1050\n\n    # 使用 = 直接打印变量名和值\n    print_info(f"调试信息: {app_name=}, {app_version=}, {user_count=}")\n    # 输出示例: 调试信息: app_name=\'MyApplication\', app_version=2.1, user_count=1050\n    \n    # 也可以用于更复杂的表达式\n    result: int = user_count * 2\n    print_info(f"计算结果: {user_count * 2 = }") # 注意 `=` 在表达式之后\n    # 输出示例: 计算结果: user_count * 2 = 2100\n\nif __name__ == \'__main__\':\n    demo_fstring_debug()\n```\n\n### Python 3.9 主要特性\n\nPython 3.9 引入了新的字典合并操作符，类型注解可以使用内置集合类型，并为字符串添加了移除前缀/后缀的方法。\n\n#### 1\\. 字典合并与更新操作符 (`|` 和 `|=`)\n\n新的操作符 `|` 用于合并两个字典，`|=` 用于原地更新字典。\n\n```python\n# === 字典合并与更新操作符演示 ===\nfrom print_utils import *\nfrom typing import Dict, Any  # 兼容旧版类型提示，但在3.9+中 dict 即可\n\n\ndef demo_dict_merge_operators() -> None:\n    """演示字典的 | 和 |= 操作符。"""\n    print_subheader("1. 字典合并 (`|`) 与更新 (`|=`) 操作符")\n\n    dict_a: Dict[str, int] = {"name_id": 1, "value": 10}\n    dict_b: Dict[str, int] = {"value": 20, "status": 0}  # "value"键与dict_a重复\n\n    # 合并操作 (|)，如果键重复，则右边字典的值优先\n    merged_dict: Dict[str, int] = dict_a | dict_b\n    print_info(f"dict_a: {dict_a}")\n    print_info(f"dict_b: {dict_b}")\n    print_success(f"合并后 (merged_dict = dict_a | dict_b): {merged_dict}")\n    # 输出: {\'name_id\': 1, \'value\': 20, \'status\': 0}\n\n    # 原地更新操作 (|=)\n    dict_c: Dict[str, int] = {"item_code": 101, "quantity": 5}\n    dict_d: Dict[str, Any] = {"quantity": 15, "description": "Gadget"}\n    print_info(f"\\n原始 dict_c: {dict_c}")\n    dict_c |= dict_d  # dict_c 被修改\n    print_success(f"原地更新后 (dict_c |= dict_d): {dict_c}")\n    # 输出: {\'item_code\': 101, \'quantity\': 15, \'description\': \'Gadget\'}\n\n\nif __name__ == \'__main__\':\n    print_header("Python 3.9 特性演示")\n    demo_dict_merge_operators()\n\n```\n\n#### 2\\. 类型注解的内置集合类型 (泛型)\n\n现在可以直接使用内置的集合类型 (如 `list`, `dict`, `tuple`, `set`) 作为泛型类型进行注解，而无需从 `typing` 模块导入大写版本 (如 `List`, `Dict`)。\n\n```python\n# === 内置集合类型注解演示 ===\nfrom print_utils import *\n# from typing import List, Dict # 在 Python 3.9+ 中不再必需，但为了兼容性或清晰性仍可使用\n\ndef demo_builtin_generic_types(names: list[str], scores: dict[str, int]) -> tuple[int, float]:\n    """\n    演示使用内置集合类型进行类型注解。\n    参数:\n        names (list[str]): 字符串列表。\n        scores (dict[str, int]): 键为字符串，值为整数的字典。\n    返回:\n        tuple[int, float]: 包含整数和浮点数的元组。\n    """\n    print_subheader("2. 类型注解的内置集合类型")\n    print_info("函数定义使用了 list[str], dict[str, int], tuple[int, float] 注解。")\n    \n    if not names:\n        return (0, 0.0)\n        \n    total_score: int = sum(scores.get(name, 0) for name in names)\n    average_score: float = total_score / len(names) if names else 0.0\n    \n    print_success(f"处理的名字: {names}")\n    print_success(f"处理的分数: {scores}")\n    print_success(f"返回结果: ({len(names)}, {average_score:.2f})")\n    return (len(names), average_score)\n\nif __name__ == \'__main__\':\n    sample_names: list[str] = ["Alice", "Bob", "Charlie"]\n    sample_scores: dict[str, int] = {"Alice": 90, "Bob": 85, "David": 70} # Charlie 没有分数\n    demo_builtin_generic_types(sample_names, sample_scores)\n```\n\n#### 3\\. 字符串方法：`removeprefix()` 和 `removesuffix()`\n\n这两个新的字符串方法用于移除字符串的前缀或后缀，如果存在的话。\n\n```python\n# === 字符串移除前缀/后缀演示 ===\nfrom print_utils import *\n\ndef demo_string_prefix_suffix_removal() -> None:\n    """演示 str.removeprefix() 和 str.removesuffix()。"""\n    print_subheader("3. 字符串方法 `removeprefix()` 和 `removesuffix()`")\n\n    filename: str = "document_final_v2.pdf"\n    url: str = "https://www.example.com/path/to/resource"\n\n    # 移除后缀\n    name_without_suffix: str = filename.removesuffix(".pdf")\n    print_info(f"\'{filename}\'.removesuffix(\'.pdf\') -> \'{name_without_suffix}\'") # \'document_final_v2\'\n    \n    non_matching_suffix: str = filename.removesuffix(".txt") # 后缀不匹配\n    print_info(f"\'{filename}\'.removesuffix(\'.txt\') -> \'{non_matching_suffix}\' (未改变)")\n\n    # 移除前缀\n    path_without_prefix: str = url.removeprefix("https://")\n    print_info(f"\'{url}\'.removeprefix(\'https://\') -> \'{path_without_prefix}\'") # \'www.example.com/path/to/resource\'\n\n    non_matching_prefix: str = url.removeprefix("http://") # 前缀不匹配\n    print_info(f"\'{url}\'.removeprefix(\'http://\') -> \'{non_matching_prefix}\' (未改变)")\n\nif __name__ == \'__main__\':\n    demo_string_prefix_suffix_removal()\n```\n\n### Python 3.10 主要特性\n\nPython 3.10 带来了备受期待的结构化模式匹配 (match/case)、更简洁的联合类型表示法以及上下文管理器语法的改进。\n\n#### 1\\. 结构化模式匹配 (`match`/`case`)\n\n这是一种新的控制流语句，允许你根据数据的结构和值来匹配模式，并执行相应的代码块。类似于其他语言中的 `switch` 或模式匹配。\n\n```python\n# === 结构化模式匹配演示 ===\nfrom print_utils import *\nfrom typing import Any, List, Dict\n\ndef demo_structural_pattern_matching(data_packet: Any) -> str:\n    """演示结构化模式匹配 (match/case) 处理不同数据结构。"""\n    print_subheader(f"1. 结构化模式匹配 (`match`/`case`) - 输入: {data_packet}")\n    \n    match data_packet:\n        case {"type": "user", "id": user_id, "name": str(name_val)}: # 匹配字典结构，并捕获值\n            return f"用户数据: ID={user_id}, 姓名=\'{name_val}\'"\n        \n        case {"type": "event", "name": event_name, "details": {"timestamp": ts, **other_details}}: # 嵌套匹配和 **other_details\n            return f"事件: \'{event_name}\' @ {ts}, 其他: {other_details}"\n            \n        case [int(x), int(y)] if x == y: # 匹配列表，包含守卫条件 (guard)\n            return f"两个相等的整数点: ({x}, {y})"\n            \n        case [str(op), *numbers] if op in ["SUM", "AVG"]: # 捕获列表剩余部分到 *numbers\n            result: float = sum(numbers)\n            if op == "AVG" and numbers:\n                result /= len(numbers)\n            return f"操作 \'{op}\' 在 {numbers} 上的结果: {result:.2f}"\n            \n        case str() as command if command.lower() == "quit": # 匹配字符串并用 as 绑定\n            return "收到退出指令。"\n            \n        case _: # 通配符，匹配任何其他情况\n            return "未知或不匹配的数据包格式。"\n\nif __name__ == \'__main__\':\n    print_header("Python 3.10 特性演示")\n    packets_to_test: List[Any] = [\n        {"type": "user", "id": 101, "name": "Alice"},\n        {"type": "event", "name": "login", "details": {"timestamp": "2025-05-18T10:00:00Z", "source_ip": "***********"}},\n        [10, 10],\n        [5, 7],\n        ["SUM", 1, 2, 3, 4, 5],\n        ["AVG", 10, 20, 30],\n        "quit",\n        42\n    ]\n    for i, packet in enumerate(packets_to_test):\n        print_info(f"处理包 {i+1}:")\n        print_success(f"  结果: {demo_structural_pattern_matching(packet)}")\n```\n\n#### 2\\. 联合类型操作符 (`|`)\n\n现在可以使用 `|` 操作符来表示联合类型 (Union)，使得类型注解更简洁。\n\n```python\n# === 联合类型操作符演示 ===\nfrom print_utils import *\n# from typing import Union, Optional # 在 Python 3.10+ 中 Optional 和 Union 不再必需显式导入来使用 |\n\ndef demo_union_operator(value: int | str | float) -> None: # 使用 | 代替 Union[int, str, float]\n    """演示使用 | 作为联合类型操作符。"""\n    print_subheader("2. 联合类型操作符 (`|`)")\n    \n    print_info(f"接收到的值: {value} (类型: {type(value).__name__})")\n    if isinstance(value, (int, float)):\n        print_success(f"  数值处理: {value * 2}")\n    elif isinstance(value, str):\n        print_success(f"  字符串处理: \'{value.upper()}\'")\n\ndef process_optional_data(data: str | None = None) -> None: # 使用 | None 代替 Optional[str]\n    """演示使用 | None 代替 Optional。"""\n    if data is None:\n        print_warning("  未提供数据。")\n    else:\n        print_success(f"  处理可选数据: \'{data}\'")\n\nif __name__ == \'__main__\':\n    demo_union_operator(100)\n    demo_union_operator("hello python 3.10")\n    demo_union_operator(3.14)\n    process_optional_data("Some data")\n    process_optional_data() # data 为 None\n```\n\n#### 3\\. 上下文管理器改进：括号内多个 `with` 表达式\n\n现在可以在 `with` 语句后的括号内编写多个上下文表达式，而无需嵌套 `with` 语句，使代码更扁平。\n\n```python\n# === 上下文管理器改进演示 ===\nfrom print_utils import *\nimport os # 用于文件操作\n\ndef demo_multiple_context_expressions() -> None:\n    """演示在 with 语句中使用括号包含多个上下文表达式。"""\n    print_subheader("3. 上下文管理器改进 (括号内多个表达式)")\n    \n    input_filename: str = "temp_input_for_with.txt"\n    output_filename: str = "temp_output_for_with.txt"\n\n    # 创建临时输入文件\n    with open(input_filename, "w", encoding="utf-8") as f_in_setup:\n        f_in_setup.write("第一行数据。\\n第二行数据。\\n")\n    print_info(f"已创建临时输入文件: {input_filename}")\n\n    try:\n        # Python 3.10+ 的写法\n        with (\n            open(input_filename, "r", encoding="utf-8") as infile,\n            open(output_filename, "w", encoding="utf-8") as outfile\n        ):\n            print_info(f"  同时打开 \'{infile.name}\' (读) 和 \'{outfile.name}\' (写)。")\n            content: str = infile.read()\n            outfile.write(content.upper()) # 示例操作：转为大写后写入\n            print_success(f"  内容已从输入文件读取，处理后写入输出文件。")\n\n        # 验证输出文件\n        if os.path.exists(output_filename):\n            with open(output_filename, "r", encoding="utf-8") as f_check:\n                print_info(f"  输出文件 \'{output_filename}\' 内容:\\n\'\'\'\\n{f_check.read().strip()}\\n\'\'\'")\n        \n    except Exception as e:\n        print_error(f"  处理文件时发生错误: {e}")\n    finally:\n        # 清理临时文件\n        if os.path.exists(input_filename): os.remove(input_filename)\n        if os.path.exists(output_filename): os.remove(output_filename)\n        print_info("临时文件已清理。")\n\nif __name__ == \'__main__\':\n    demo_multiple_context_expressions()\n```\n\n### Python 3.11 主要特性\n\nPython 3.11 带来了显著的性能提升 (CPython Faster计划)，以及异常组、异常注释、`Self` 类型和 `LiteralString` 等新特性。\n\n#### 1\\. 异常组 (`ExceptionGroup`) 和异常注释 (`add_note()`)\n\n`ExceptionGroup` 允许同时处理多个不相关的异常。`add_note()` 方法可以向异常对象添加上下文注释。\n\n```python\n# === 异常组和异常注释演示 ===\nfrom print_utils import *\n# from exceptiongroup import ExceptionGroup # 在 Python 3.11+ 中，ExceptionGroup 是内置的\nfrom typing import List\n\ndef demo_exception_groups_and_notes() -> None:\n    """演示 ExceptionGroup 和 e.add_note()。"""\n    print_subheader("1. 异常组 (`ExceptionGroup`) 和异常注释 (`add_note()`)")\n\n    # 演示 add_note()\n    print_info("演示 e.add_note():")\n    try:\n        user_input: str = "non_numeric_value"\n        value: int = int(user_input)\n    except ValueError as e:\n        e.add_note(f"输入的值 \'{user_input}\' 无法转换为整数。") # 添加注释\n        e.add_note("请确保输入的是有效的数字。")\n        # raise # 如果要重新引发带注释的异常\n        print_warning(f"捕获到 ValueError (带注释):")\n        # 要查看注释，你需要捕获异常并访问其 __notes__ 属性，或让它传播到顶层由解释器打印\n        # print(f"  异常: {e}")\n        # if hasattr(e, "__notes__"):\n        #     for note in e.__notes__:\n        #         print(f"    Note: {note}")\n        # Python 3.11 的默认异常回溯会显示注释\n        try:\n            raise e # 重新引发以观察其默认打印效果\n        except ValueError as e_raised:\n            print_error(f"  重新引发的异常 (其回溯应包含注释):\\n{e_raised}")\n            if hasattr(e_raised, "__notes__"): # Python 3.11+\n                 print_info("  异常包含的注释 (__notes__):")\n                 for note_item in e_raised.__notes__:\n                     print(f"    - {note_item}")\n\n\n    # 演示 ExceptionGroup (概念性)\n    print_info("\\n演示 ExceptionGroup (概念性):")\n    # 假设我们有多个并发任务，每个都可能失败\n    exceptions_list: List[Exception] = []\n    task1_result, task2_result = None, None\n    \n    # 模拟任务1\n    try:\n        # task1_result = 10 / 0\n        print_info("  任务1: 模拟成功。")\n        task1_result = "Success"\n    except ZeroDivisionError as e1:\n        e1.add_note("任务1 (除法运算) 失败。")\n        exceptions_list.append(e1)\n        print_warning("  任务1: 捕获 ZeroDivisionError。")\n\n\n    # 模拟任务2\n    try:\n        # data = {}\n        # value = data["missing_key"]\n        print_info("  任务2: 模拟成功。")\n        task2_result = "OK"\n    except KeyError as e2:\n        e2.add_note("任务2 (字典键查找) 失败。")\n        exceptions_list.append(e2)\n        print_warning("  任务2: 捕获 KeyError。")\n\n    if exceptions_list:\n        # 如果有多个异常，可以用 ExceptionGroup 包装它们\n        # 在 Python 3.11+ 中，ExceptionGroup 是内置的\n        # from exceptiongroup import ExceptionGroup # 对于旧版本，可能需要此库\n        error_group = ExceptionGroup("在执行多个任务时发生错误", exceptions_list)\n        print_error(f"  发生了异常组: {error_group}")\n        # 处理异常组:\n        # try:\n        #     raise error_group\n        # except* ValueError as eg_vg: # Python 3.11+ 的 except* 语法\n        #     print_warning(f"  捕获到异常组中的 ValueError: {eg_vg.exceptions}")\n        # except* TypeError as eg_tg:\n        #     print_warning(f"  捕获到异常组中的 TypeError: {eg_tg.exceptions}")\n        print_info("  (在实际代码中，会使用 try...except* 来处理 ExceptionGroup)")\n    else:\n        print_success("  所有模拟任务均成功完成。")\n\n\nif __name__ == \'__main__\':\n    print_header("Python 3.11 特性演示")\n    demo_exception_groups_and_notes()\n```\n\n#### 2\\. `Self` 类型\n\n`typing.Self` (或仅 `Self`，如果 `from typing import Self`) 提供了一种更简洁、更准确的方式来注解那些返回类自身实例的方法 (例如构造函数、工厂方法、或返回修改后自身的链式调用方法)。\n\n```python\n# === Self 类型演示 ===\nfrom print_utils import *\nfrom typing import Self # Python 3.11+ (或 typing_extensions.Self for older)\n\ndef demo_self_type() -> None:\n    """演示 typing.Self 类型的使用。"""\n    print_subheader("2. `Self` 类型")\n\n    class ConfigBuilder:\n        def __init__(self, name: str):\n            self.name: str = name\n            self.settings: dict = {}\n            print_info(f"  ConfigBuilder \'{name}\' 已创建。")\n\n        def set_option(self, key: str, value: Any) -> Self: # 返回类型是 Self\n            """设置一个配置选项并返回自身，以便链式调用。"""\n            self.settings[key] = value\n            print_info(f"    选项 \'{key}\' 设置为 \'{value}\'")\n            return self # 返回当前类的实例\n\n        @classmethod\n        def create_default(cls, name: str) -> Self: # 类方法返回 Self\n            """创建一个带有默认设置的实例。"""\n            print_info(f"  调用类方法 create_default for \'{name}\'")\n            instance = cls(name) # cls 指向 ConfigBuilder (或其子类)\n            instance.set_option("default_timeout", 30)\n            return instance\n            \n        def build(self) -> dict:\n            print_info(f"  构建配置 \'{self.name}\'...")\n            return {"name": self.name, "settings": self.settings}\n\n    # 使用示例\n    builder1: ConfigBuilder = ConfigBuilder("App1")\n    builder1.set_option("port", 8080).set_option("retries", 3) # 链式调用\n    config1: dict = builder1.build()\n    print_success(f"  Config1: {config1}")\n\n    builder2: ConfigBuilder = ConfigBuilder.create_default("App2_Default")\n    builder2.set_option("theme", "dark")\n    config2: dict = builder2.build()\n    print_success(f"  Config2 (from default): {config2}")\n    \n    # 演示 Self 在子类中的行为\n    class AdvancedConfigBuilder(ConfigBuilder):\n        def enable_feature_x(self) -> Self: # 返回的仍然是 AdvancedConfigBuilder 类型\n            self.set_option("feature_x", True)\n            return self\n            \n    adv_builder = AdvancedConfigBuilder("AdvApp").enable_feature_x().set_option("mode", "expert")\n    adv_config = adv_builder.build()\n    print_success(f"  AdvancedConfig: {adv_config}")\n    print_info(f"  adv_builder 的类型是: {type(adv_builder).__name__} (应为 AdvancedConfigBuilder)")\n\n\nif __name__ == \'__main__\':\n    demo_self_type()\n```\n\n#### 3\\. `LiteralString` 类型\n\n`typing.LiteralString` 用于注解那些值必须是字面量字符串（即直接在代码中写出的字符串，而不是通过变量拼接或函数调用生成的字符串）的参数。这有助于静态分析工具检测潜在的安全风险，例如 SQL 注入。\n\n```python\n# === LiteralString 类型演示 ===\nfrom print_utils import *\nfrom typing import LiteralString # Python 3.11+\n\ndef demo_literal_string_type() -> None:\n    """演示 typing.LiteralString 类型。"""\n    print_subheader("3. `LiteralString` 类型")\n\n    def execute_safe_query(query: LiteralString, params: tuple = ()) -> None:\n        """\n        一个模拟的安全数据库查询函数。\n        它只接受字面量字符串作为查询语句，以防止SQL注入。\n        """\n        print_info(f"  尝试执行安全查询: \'{query}\' (参数: {params})")\n        # 在实际应用中，这里会与数据库交互，并可能使用参数化查询\n        print_success(f"    模拟执行查询: {query} (参数化: {params})")\n\n    # 正确的用法：传递字符串字面量\n    execute_safe_query("SELECT * FROM users WHERE status = \'active\'")\n    execute_safe_query("SELECT name, email FROM customers WHERE id = %s", params=(101,))\n\n    # 错误的用法：传递通过变量或拼接生成的字符串\n    # 静态类型检查器 (如 MyPy) 会对以下调用发出警告\n    table_name_var: str = "users" \n    # execute_safe_query(f"SELECT * FROM {table_name_var}") # MyPy 会警告: Expected LiteralString\n    \n    user_input_column: str = input("请输入要查询的列名 (例如 \'username\'): ") if os.getenv("ASK_INPUT") else "username" # 模拟用户输入\n    # execute_safe_query(f"SELECT {user_input_column} FROM data") # MyPy 会警告\n\n    print_warning("  注意: LiteralString 主要用于静态类型检查。")\n    print_info("  如果上面的注释行被取消且使用了 MyPy，它会标记出潜在的类型错误。")\n    print_info("  在运行时，Python 本身不强制 LiteralString。")\n\n\nif __name__ == \'__main__\':\n    # os.environ["ASK_INPUT"] = "1" # 取消注释以在运行时测试 input() 部分 (可选)\n    demo_literal_string_type()\n    # if "ASK_INPUT" in os.environ: del os.environ["ASK_INPUT"]\n```\n\n### Python 3.12 主要特性\n\nPython 3.12 继续改进类型系统，带来了更简洁的泛型类和函数语法，以及 f-string 的进一步增强。\n\n#### 1\\. 泛型类和函数的简化语法 (PEP 695)\n\n现在可以使用更简洁的方括号语法直接在类或函数定义中声明类型参数，而无需从 `typing` 导入 `TypeVar`。\n\n```python\n# === 简化泛型语法演示 ===\nfrom print_utils import *\n# from typing import TypeVar # 在 Python 3.12+ 中 TypeVar 可以通过新语法隐式定义\n\ndef demo_simplified_generics_syntax() -> None:\n    """演示 Python 3.12+ 中简化的泛型类和函数语法。"""\n    print_subheader("1. 泛型类和函数的简化语法 (PEP 695)")\n\n    # 简化泛型函数定义\n    # 旧方法: T = TypeVar(\'T\'); def old_first(items: list[T]) -> T | None: ...\n    def new_first[T](items: list[T]) -> T | None: # 直接在函数名后用 [] 声明类型参数 T\n        """获取列表的第一个元素，如果列表为空则返回 None。"""\n        print_info(f"    new_first 调用，items: {items}")\n        return items[0] if items else None\n\n    print_success(f"  new_first([10, 20]): {new_first([10, 20])}")\n    print_success(f"  new_first([\'a\', \'b\']): {new_first([\'a\', \'b\'])}")\n    print_success(f"  new_first([]): {new_first([])}")\n\n    # 简化泛型类定义\n    # 旧方法: U = TypeVar(\'U\'); class OldBox(Generic[U]): ...\n    class NewBox[U]: # 直接在类名后用 [] 声明类型参数 U\n        def __init__(self, content: U):\n            self.content: U = content\n        \n        def get_content(self) -> U:\n            return self.content\n        \n        def __repr__(self) -> str:\n            return f"NewBox[{type(self.content).__name__}](content={self.content!r})"\n\n    int_box = NewBox[int](123) # 可以显式指定类型\n    str_box = NewBox("Python 3.12") # 也可以让类型检查器推断\n\n    print_success(f"  {int_box} -> content: {int_box.get_content()}")\n    print_success(f"  {str_box} -> content: {str_box.get_content()}")\n\nif __name__ == \'__main__\':\n    print_header("Python 3.12 特性演示")\n    demo_simplified_generics_syntax()\n```\n\n#### 2\\. 类型别名与类型变量语法改进 (`type` 语句) (PEP 695)\n\nPython 3.12 引入了 `type` 语句作为创建类型别名和类型变量的更清晰、更正式的方式。\n\n```python\n# === type 语句演示 ===\nfrom print_utils import *\nfrom typing import List # List 仍可用于兼容性或复杂场景\n\n# Python 3.12+ 的 type 语句\n# 定义类型别名\ntype Point = tuple[float, float]\ntype IntList = list[int]\n# 定义类型变量 (TypeVar)\ntype T = str | int # T 现在是一个 TypeVar，可以绑定到 str 或 int\ntype K_contra = contravariant str # 定义逆变类型变量 K，上界为 str\ntype V_co = covariant int # 定义协变类型变量 V，上界为 int\n\ndef demo_type_statement() -> None:\n    """演示 Python 3.12+ 中使用 type 语句定义类型别名和类型变量。"""\n    print_subheader("2. 类型别名与类型变量语法改进 (`type` 语句)")\n\n    def process_point(p: Point) -> None:\n        print_info(f"    处理点: {p}, 类型: {type(p)}")\n    \n    def sum_int_list(numbers: IntList) -> int:\n        return sum(numbers)\n\n    def process_generic_var(item: T) -> None: # T 是 TypeVar (str | int)\n        if isinstance(item, str):\n            print_info(f"    泛型变量 T (字符串): {item.upper()}")\n        elif isinstance(item, int):\n            print_info(f"    泛型变量 T (整数): {item * 100}")\n            \n    my_point: Point = (1.0, 2.5)\n    process_point(my_point)\n    # process_point([1.0, 2.5]) # MyPy 会报错，类型不匹配\n    \n    my_numbers: IntList = [10, 20, 30]\n    print_success(f"  整数列表 {my_numbers} 的和: {sum_int_list(my_numbers)}")\n    \n    process_generic_var("hello")\n    process_generic_var(5)\n    # process_generic_var(3.14) # MyPy 会报错，因为 T 只能是 str 或 int\n\nif __name__ == \'__main__\':\n    demo_type_statement()\n```\n\n#### 3\\. f-string 语法进一步改进 (PEP 701)\n\nPython 3.12 中的 f-string 更加强大和灵活，主要体现在：\n\n  * **引号重用**：现在可以在 f-string 表达式内部重用与包围 f-string 自身所用类型相同的引号，无需转义。\n  * **多行表达式和注释**：f-string 表达式部分现在可以跨越多行，并且可以包含 `#` 注释。\n  * **反斜杠**：表达式部分可以使用反斜杠。\n\n```python\n# === f-string 语法改进演示 ===\nfrom print_utils import *\n\ndef demo_fstring_enhancements() -> None:\n    """演示 Python 3.12+ 中 f-string 的语法改进。"""\n    print_subheader("3. f-string 语法进一步改进 (PEP 701)")\n\n    user_data: dict = {"name": "Alice", "city": "Wonderland"}\n    user_id: int = 101\n\n    # 引号重用\n    # 在 3.12 之前: f"User data: {user_data[\'name\']}" (需要不同类型的引号或转义)\n    # 或者 f\'User data: {user_data["name"]}\'\n    message_quotes: str = f"User: {user_data[\'name\']}" # 3.12+: 可以重用引号\n    print_success(f"  引号重用: {message_quotes}")\n    \n    message_quotes_single: str = f\'ID: {user_data.get("name", "N/A")!r}\' # 外层单引号，内部仍可方便使用双引号\n    print_success(f"  引号重用 (单引号): {message_quotes_single}")\n\n\n    # 多行表达式和注释\n    # (注意：在多行 f-string 表达式中，注释必须在自己的行上)\n    items_list: list[int] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]\n    formatted_multiline: str = (\n        f"Data summary for user {user_id}:\\n"\n        f"  - Items count: {len(items_list)}\\n"\n        f"  - Even items sum: {sum(\n            x \n            for x in items_list \n            if x % 2 == 0  # 这是一个 f-string 表达式内部的注释\n        )}\\n" # 多行表达式\n        f"  - User info: {user_data[\'name\'].upper() # .upper() 方法调用\n                          + \' from \' + user_data[\'city\']}" \n    )\n    print_info("  f-string 带多行表达式和注释:")\n    print(formatted_multiline)\n    \n    # 反斜杠 (虽然不常见，但现在允许)\n    # path_example = f"Path: {\'C:\\\\Users\\\\<USER>\'}" # 原本就这样可以\n    # fstring_backslash = f"{\'a\\\\nb\'.split(\'\\\\n\')}" # 现在更灵活\n    # print_success(f"  f-string 中使用反斜杠: {fstring_backslash}")\n\n\nif __name__ == \'__main__\':\n    demo_fstring_enhancements()\n```\n\n### Python 3.13 主要特性\n\nPython 3.13 带来了包括新的 `@override` 装饰器、JIT 编译器的初步实验（可能不直接体现在日常语法中）、以及对现有特性的一些改进。\n\n#### 1\\. `@typing.override` 装饰器 (PEP 698)\n\n一个新的装饰器 `@override` (来自 `typing` 模块，或 Python 3.12+ 的 `typing_extensions` 中已有) 用于明确指示一个方法意图覆盖其父类中的同名方法。这有助于静态类型检查器和开发者捕捉由于意外拼写错误或签名不匹配导致的覆盖失败。\n\n```python\n# === @override 装饰器演示 ===\nfrom print_utils import *\nfrom typing import override # Python 3.13+ (或 from typing_extensions import override for 3.12)\n\ndef demo_override_decorator() -> None:\n    """演示 @typing.override 装饰器。"""\n    print_subheader("1. `@typing.override` 装饰器 (PEP 698)")\n\n    class BaseDocument:\n        def get_title(self) -> str:\n            return "Generic Document"\n\n        def render_content(self) -> str:\n            raise NotImplementedError\n\n    class Article(BaseDocument):\n        def __init__(self, title: str, body: str):\n            self._title = title\n            self._body = body\n            \n        @override # 明确表示此方法覆盖父类方法\n        def get_title(self) -> str:\n            return f"Article: {self._title}"\n\n        @override # 明确表示此方法覆盖父类方法\n        def render_content(self) -> str:\n            return f"<h1>{self.get_title()}</h1><p>{self._body}</p>"\n            \n        # 如果写成 @override def get_titel(self) -> str: ... (拼写错误)\n        # MyPy 这类类型检查器会报错，因为它没有覆盖任何父类方法。\n\n    my_article = Article("Python 3.13 News", "Override decorator is here!")\n    print_success(f"  Article Title: {my_article.get_title()}")\n    print_info(f"  Rendered Article (simulated):\\n{my_article.render_content()}")\n    \n    # 尝试覆盖一个不存在的基类方法 (MyPy会警告)\n    # class BadSubclass(BaseDocument):\n    #     @override\n    #     def non_existent_method_in_base(self) -> None:\n    #         pass\n\n    print_info("  @override 帮助在静态检查时发现覆盖错误。")\n\nif __name__ == \'__main__\':\n    print_header("Python 3.13 (部分) 特性演示")\n    demo_override_decorator()\n```'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E5%8D%81%E7%AB%A0%EF%BC%9APython-%E8%AF%AD%E6%B3%95%E6%96%B0%E7%89%B9%E6%80%A7%E6%80%BB%E7%BB%93-3-8-3-13"><span class="toc-number">1.</span> <span class="toc-text">第二十章：Python 语法新特性总结 (3.8 ~ 3.13)</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-8-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-number">1.1.</span> <span class="toc-text">Python 3.8 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%B5%8B%E5%80%BC%E8%A1%A8%E8%BE%BE%E5%BC%8F-%E6%B5%B7%E8%B1%A1%E6%93%8D%E4%BD%9C%E7%AC%A6"><span class="toc-number">1.1.1.</span> <span class="toc-text">1. 赋值表达式 (海象操作符) :=</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BB%85%E4%BD%8D%E7%BD%AE%E5%8F%82%E6%95%B0"><span class="toc-number">1.1.2.</span> <span class="toc-text">2. 仅位置参数 (/)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-f-string-%E5%A2%9E%E5%BC%BA-%E7%94%A8%E4%BA%8E%E8%B0%83%E8%AF%95"><span class="toc-number">1.1.3.</span> <span class="toc-text">3. f-string 增强 (=) 用于调试</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-9-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-number">1.2.</span> <span class="toc-text">Python 3.9 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%AD%97%E5%85%B8%E5%90%88%E5%B9%B6%E4%B8%8E%E6%9B%B4%E6%96%B0%E6%93%8D%E4%BD%9C%E7%AC%A6-%E5%92%8C"><span class="toc-number">1.2.1.</span> <span class="toc-text">1. 字典合并与更新操作符 (| 和 |=)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%B1%BB%E5%9E%8B%E6%B3%A8%E8%A7%A3%E7%9A%84%E5%86%85%E7%BD%AE%E9%9B%86%E5%90%88%E7%B1%BB%E5%9E%8B-%E6%B3%9B%E5%9E%8B"><span class="toc-number">1.2.2.</span> <span class="toc-text">2. 类型注解的内置集合类型 (泛型)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%AD%97%E7%AC%A6%E4%B8%B2%E6%96%B9%E6%B3%95%EF%BC%9Aremoveprefix-%E5%92%8C-removesuffix"><span class="toc-number">1.2.3.</span> <span class="toc-text">3. 字符串方法：removeprefix() 和 removesuffix()</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-10-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-number">1.3.</span> <span class="toc-text">Python 3.10 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%BB%93%E6%9E%84%E5%8C%96%E6%A8%A1%E5%BC%8F%E5%8C%B9%E9%85%8D-match-case"><span class="toc-number">1.3.1.</span> <span class="toc-text">1. 结构化模式匹配 (match/case)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%81%94%E5%90%88%E7%B1%BB%E5%9E%8B%E6%93%8D%E4%BD%9C%E7%AC%A6"><span class="toc-number">1.3.2.</span> <span class="toc-text">2. 联合类型操作符 (|)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8%E6%94%B9%E8%BF%9B%EF%BC%9A%E6%8B%AC%E5%8F%B7%E5%86%85%E5%A4%9A%E4%B8%AA-with-%E8%A1%A8%E8%BE%BE%E5%BC%8F"><span class="toc-number">1.3.3.</span> <span class="toc-text">3. 上下文管理器改进：括号内多个 with 表达式</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-11-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-number">1.4.</span> <span class="toc-text">Python 3.11 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BC%82%E5%B8%B8%E7%BB%84-ExceptionGroup-%E5%92%8C%E5%BC%82%E5%B8%B8%E6%B3%A8%E9%87%8A-add-note"><span class="toc-number">1.4.1.</span> <span class="toc-text">1. 异常组 (ExceptionGroup) 和异常注释 (add_note())</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-Self-%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.4.2.</span> <span class="toc-text">2. Self 类型</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-LiteralString-%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.4.3.</span> <span class="toc-text">3. LiteralString 类型</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-12-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-number">1.5.</span> <span class="toc-text">Python 3.12 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%B3%9B%E5%9E%8B%E7%B1%BB%E5%92%8C%E5%87%BD%E6%95%B0%E7%9A%84%E7%AE%80%E5%8C%96%E8%AF%AD%E6%B3%95-PEP-695"><span class="toc-number">1.5.1.</span> <span class="toc-text">1. 泛型类和函数的简化语法 (PEP 695)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%B1%BB%E5%9E%8B%E5%88%AB%E5%90%8D%E4%B8%8E%E7%B1%BB%E5%9E%8B%E5%8F%98%E9%87%8F%E8%AF%AD%E6%B3%95%E6%94%B9%E8%BF%9B-type-%E8%AF%AD%E5%8F%A5-PEP-695"><span class="toc-number">1.5.2.</span> <span class="toc-text">2. 类型别名与类型变量语法改进 (type 语句) (PEP 695)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-f-string-%E8%AF%AD%E6%B3%95%E8%BF%9B%E4%B8%80%E6%AD%A5%E6%94%B9%E8%BF%9B-PEP-701"><span class="toc-number">1.5.3.</span> <span class="toc-text">3. f-string 语法进一步改进 (PEP 701)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Python-3-13-%E4%B8%BB%E8%A6%81%E7%89%B9%E6%80%A7"><span class="toc-number">1.6.</span> <span class="toc-text">Python 3.13 主要特性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-typing-override-%E8%A3%85%E9%A5%B0%E5%99%A8-PEP-698"><span class="toc-number">1.6.1.</span> <span class="toc-text">1. @typing.override 装饰器 (PEP 698)</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>