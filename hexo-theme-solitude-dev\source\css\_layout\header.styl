#page-header
  position relative
  width 100%
  transition all .5s ease 0s
  display flex
  justify-content center

  &.not-top-img
    height 60px
    background 0 center

    #nav
      transition .3s
      background var(--efu-card-bg)

      a
        color var(--efu-fontcolor)

        &:hover
          color var(--efu-card-bg)

  &:not(.nav-fixed)
    #nav
      background var(--efu-none)
      transition .3s

      +maxWidth768()
        transition 0s

      .post &

        #nav-right
          .nav-button
            .console_switchbutton
              label
                i
                  background var(--efu-white)

#page-header
  #site-title
    margin 0
    color var(--efu-white)
    font-size 1.85em

    +minWidth768()
      font-size 2.85em

  #nav
    z-index 100
    box-shadow none
    transition 0.3s
    display flex
    justify-content center
    user-select none

    +maxWidth768()
      transition 0s

    +maxWidth1400()
      padding 0

    a
      &:hover
        span
          &.solitude
            opacity 0

            +maxWidth768()
              opacity 1

    #nav-group
      max-width 1400px
      width 100%
      display flex
      position relative
      padding 0 1.5rem
      align-items center

      +maxWidth768()
        width 100%
        display flex
        position relative
        padding 0 12px

    #nav-right
      position absolute
      right 0
      z-index 102
      display flex
      flex-direction row
      height 100%
      align-items center
      margin-right 1.5rem
      overflow hidden

      +maxWidth768()
        margin-right 12px

      div
        padding 0
        margin-left .5rem

      .nav-button
        margin-left .5rem
        cursor pointer

        a
          height 35px
          width 35px
          display flex
          align-items center
          justify-content center
          border-radius 40px

          i
            font-size 20px

          &.totopbtn
            width 25px
            height 25px
            border-radius 40px
            background var(--efu-fontcolor)
            color var(--efu-card-bg)
            position absolute
            top 5px
            right 5px
            transition .3s

          &.console_switchbutton
            padding 10px 7.5px

            &:hover
              label
                i
                  background var(--efu-card-bg)

                .left
                  width .5rem
                  height .3rem

                .center
                  width .5rem
                  height .3rem

                .right
                  width .3rem
                  height 100%

            label
              display flex
              position relative
              width 100%
              height 100%
              cursor pointer

              i
                transition all .3s
                position absolute
                border-radius 12px
                background var(--efu-fontcolor)

                &.left
                  top 0
                  width 100%
                  height .3rem

                &.center
                  bottom 0
                  width .3rem
                  height .3rem

                &.right
                  right 0
                  bottom 0
                  width .5rem
                  height .3rem

        &.long
          a
            &.totopbtn
              width 70px

        &:hover
          a
            &.totopbtn
              background var(--efu-lighttext)

        &:not(.long):hover
          a
            &.totopbtn
              width 35px
              height 35px
              top 0
              right 0

  &.nav-fixed
    #nav
      position fixed
      top 0
      transition .3s
      box-shadow none
      background var(--efu-card-bg)
      outline 1px solid var(--efu-card-border)

      #menus
        +minWidth900()
          z-index 100

        & > div
          &.menus_items
            transition .3s
            height 40px
            margin auto 0

      #menus[style*="padding-right: 300px;"]
        .menus_items
          transform translateX(-150px)

      #page-name
        +minWidth900()
          z-index 101
          transition .3s
          top 10px

        &-text
          +minWidth900()
            display inline
            opacity 1
            transition .3s
            line-height 1
            width 70%
            overflow hidden
            text-overflow ellipsis
            white-space nowrap

          &:hover
            &:after
              +minWidth900()
                opacity 1
                line-height 36px

      div.menus_items
        +minWidth900()
          transition .15s
          position relative
          top -60px

    &.nav-visible
      #nav
        #page-name
          +minWidth900()
            z-index 100
            top 60px
            transition .3s

          &-text
            +minWidth900()
              display inline
              transition .15s

        div.menus_items
          +minWidth900()
            opacity 1
            transition .15s
            position relative
            top: 0

        #menus
          +minWidth900()
            z-index 101

    #travellings_button
      +maxWidth768()
        opacity 0

  &.no-top-img
    margin-bottom 0

  &:not(.nav-fixed)
    #percent
      transition .3s

    #nav-right #nav-totop
      width 0
      transform scale(0)
      margin-left 0
      overflow hidden
      transition .3s

#nav
  padding 0
  position absolute
  top 0
  z-index 10
  display flex
  flex-wrap wrap
  -webkit-box-align center
  align-items center
  width 100%
  height 60px
  font-size 1.3em
  opacity 0
  transition all .5s ease 0s
  outline 1px solid var(--efu-none)
  justify-content space-between

  +maxWidth768()
    padding 0

  &.show
    top 0
    transition .2s
    position fixed
    opacity 1
    filter none

  #toggle-menu
    display none
    padding .1rem 0 0 .3rem
    vertical-align top

    +maxWidth768()
      display inline-block

    &:hover
      color var(--efu-white)

  #site-name
    padding 0
    transition .3s
    display flex
    height 35px
    min-width 50px
    justify-content center
    align-items center
    border-radius 40px
    cursor pointer

    &:hover
      @media (hover: hover)
        i, span, img
          color var(--efu-card-bg)
          transition .3s

      +minWidth900()
        i, span, img
          opacity 0

      i.fas.fa-home
        +minWidth900()
          opacity: 1

    i.fas.fa-home
      opacity 0
      position absolute
      display flex
      z-index 1
      align-items center
      justify-content center
      font-size 22px
      line-height 1
      color var(--efu-card-bg)

    if hexo-config('site.name.class') == i_class
      i
        font-size 16px
        line-height 35px
        padding 0 12px

        +maxWidth768()
          transition .1s

        +minWidth900()
          opacity 1
          transition all .3s, color 0s, opacity .3s

    if hexo-config('site.name.class') == text
      span.title
        letter-spacing normal
        font-size 1rem
        font-weight 700
        padding 0 5px
        line-height 2rem
        +maxWidth768()
          padding-left 8px

    if hexo-config('site.name.class') == img
      img
        max-width 120px
        max-height 35px
        padding 4px 8px

  #blog_name
    flex-wrap nowrap
    height 60px
    display flex
    align-items center
    z-index 102
    transition .3s

  a
    transition .3s
    color var(--efu-white)
    padding .3rem .4rem 0
    border-radius 8px

    &:hover
      background var(--efu-white-op)
      transition .3s
      color var(--efu-white)

      +maxWidth768()
        background var(--efu-none)
        box-shadow none
        transition .3s
        color var(--efu-fontcolor)

  #menus
    display flex
    justify-content center
    width 100%
    position absolute
    height 60px
    left 0
    margin 0
    transform translateZ(0)

    +maxWidth768()
      display none

  .menus_items
    position relative
    width fit-content
    text-align center
    left 0
    right 0
    top 0
    display flex
    flex-direction row
    justify-content center

    .menus_item
      padding 0 .4rem
      display flex
      flex-direction column
      align-items center
      margin auto
      position relative

      .menus_item_child
        padding 6px 4px 8px 4px
        border-radius 100px
        background-color var(--efu-maskbgdeep)
        box-shadow var(--efu-shadow-black)
        border var(--style-border)
        transition .3s
        backdrop-filter blur(20px)
        top 35px
        position absolute
        margin-top 8px
        white-space nowrap
        opacity 0
        pointer-events none
        transform translateY(-10px) scale(.8)

        li
          display inline-flex
          list-style none
          border-radius 5px
          margin 0 4px

          #page-header & a
            letter-spacing 0
            display flex
            align-items center
            border-radius 100px
            padding .3rem .8rem
            width 100%
            color var(--efu-fontcolor)

            &:hover
              background var(--efu-lighttext)
              color var(--efu-card-bg)
              box-shadow var(--efu-shadow-main)
              margin 0 auto
              transform scale(1)
              padding .3rem 1rem

            i
              &:not(.fa-brands)
                font-size 16px
                margin-right 8px
                line-height 1

        &::before
          position absolute
          top -8px
          left 0
          width 100%
          height 20px
          content ""

        &:before
          top -10px

      &:hover
        .menus_item_child
          display block
          border var(--style-border-hover)
          box-shadow var(--efu-shadow-main)
          opacity 1
          pointer-events all
          transform translateY(0) scale(1)

        i
          &.expand
            transform rotate(180deg)

      i
        &.expand
          padding 4px
          transition transform .3s ease 0s

  #search-button
    display inline
    padding 0 .4rem

  .site-page
    position relative
    padding-bottom .3rem
    font-size .78em
    cursor pointer

  if hexo-config('nav.group')
    .back-home-button
      &:hover
        +minWidth900()
          box-shadow var(--efu-shadow-main)
          cursor pointer

  #page-name
    &-text
      +minWidth900()
        display inline
        font-weight 700
        padding 4px 8px
        opacity 0
        transition .1s
        text-overflow ellipsis
        overflow hidden
        white-space nowrap
        position relative
        text-align center
        cursor pointer
        top 0
        font-size .9rem
        animation-timing-function ease-in
        -webkit-animation-timing-function ease-in

      &:hover
        +minWidth900()
          color var(--efu-lighttext) !important
          background var(--efu-none)
          box-shadow none

    &-mask
      +maxWidth899()
        display none !important

#site-logo
  width 121px

#nav-totop
  position relative
  width 35px
  height 35px
  display flex
  border-radius 40px
  transition all .3s ease-in-out
  align-items center

  &.long
    width 80px

  .totopbtn
    padding-top 0

    i
      position absolute
      display flex
      font-size 22px
      opacity 0
      line-height 1

  &:hover
    .totopbtn
      i
        opacity 1
        color var(--efu-card-bg)
        transition .3s

    #percent
      opacity 0
      font-weight 700

  #percent
    font-size 12px
    border-radius 35px
    display flex
    justify-content center
    align-items center
    transition .3s
    white-space nowrap

#nav-totop #percent
  .nav-fixed &, .page &
    font-size 12px
    border-radius 35px
    display flex
    justify-content center
    align-items center
    transition .3s
    white-space nowrap

if hexo-config('nav.group')
  .back-home-button
    display flex
    width 35px
    height 35px
    padding 0 !important
    align-items center
    justify-content center
    margin-right 4px
    transition .3s
    border-radius 40px

    +maxWidth768()
      display: none

    i
      font-size 20px

    &:hover
      background var(--efu-main)
      color var(--efu-card-bg)

      .back-menu-list-groups
        display flex
        opacity 1
        top 55px
        pointer-events auto
        left 1.5rem
        transform scale(1)

        +maxWidth768()
          padding-bottom 8px
          box-shadow var(--efu-shadow-border)
          left: 0

    .back-menu-list-group
      display flex
      flex-direction column

      .back-menu-list-title
        margin 8px 0 0 16px
        transition .3s

      &:hover
        .back-menu-list-title
          color var(--efu-lighttext)

    .back-menu-list-groups
      position absolute
      top 45px
      transform scale(.8)
      transform-origin top left
      left 1.5rem
      background-color var(--efu-maskbgdeep)
      border-radius 12px
      border var(--style-border)
      flex-direction column
      font-size 12px
      color var(--efu-secondtext)
      box-shadow var(--efu-shadow-border)
      transition .3s
      opacity 0
      pointer-events none
      backdrop-filter blur(20px)
      -webkit-backdrop-filter blur(20px)

      +maxWidth768()
        left 0

      &:hover
        border var(--style-border-hover)

    .back-menu-list
      .back-menu-item
        width 150px

.nav-fixed
  #nav
    a
      color var(--efu-fontcolor)
      transition .3s

      &:hover
        background var(--efu-lighttext)
        color var(--efu-card-bg)
        transition .3s

    #site-name
      &:hover
        +minWidth900()
          background var(--efu-lighttext)

    if hexo-config('nav.group')
      .back-home-button
        color var(--efu-fontcolor)

        &:hover
          background var(--efu-lighttext)
          color var(--efu-card-bg)

    .menus_item
      &:hover
        & >
        a.site-page
          background-color var(--efu-lighttext)

          +minWidth768()
            background-color var(--efu-main)

.post
  .post-bg
    if hexo-config('nav.group')
      .back-home-button
        color var(--efu-white)

        &:hover
          background var(--efu-white-op)
          color var(--efu-white)
          cursor pointer

  #nav
    #site-name
      &:hover
        +minWidth900()
          align-items center

      i.fas.fa-home
        color var(--efu-white)

  .nav-fixed
    #nav
      #site-name
        i.fas.fa-home
          color var(--efu-card-bg)

  .not-top-img
    #nav
      a
        &:hover
          +minWidth900()
            background var(--efu-lighttext)

.back-menu-list
  display flex
  flex-direction row
  flex-wrap wrap
  width 340px
  justify-content space-between

  &::before
    position absolute
    top -15px
    left 0
    width 100%
    height 20px
    content ''

.back-menu-item
  display flex
  align-items center
  margin 4px 8px
  padding 4px 8px

  #nav &
    padding 4px 8px

  #nav &
    &:hover
      background var(--efu-lighttext)

  &:hover
    background var(--efu-lighttext)

    .back-menu-item-text
      color var(--efu-card-bg)

  .back-menu-item-icon
    width 24px
    height 24px
    border-radius 8px
    background var(--efu-none)

  .back-menu-item-text
    font-size 16px
    transition .3s
    margin-left .5rem
    color var(--efu-fontcolor)

    +maxWidth768()
      font-size 14px

.page
  #nav
    a
      &:hover
        color var(--efu-card-bg)
        background var(--efu-main)
        transition .3s
        box-shadow var(--efu-shadow-main)

    #site-name
      &:hover
        i
          color var(--efu-card-bg)

  .menus_item
    &:hover
      & > a.site-page
        +minWidth768()
          color var(--efu-card-bg) !important
          background var(--efu-main)
          box-shadow var(--efu-shadow-main)

.error
  #nav
    a
      &:hover
        background var(--efu-main)

    #site-name
      i
        +maxWidth768()
          color var(--efu-fontcolor)

#menus
  & > div
    &.menus_items
      & > div
        & > a
          letter-spacing .3rem
          padding-left .7rem
          font-weight 700
          padding-top 0
          padding-bottom 0
          height 35px
          line-height 35px
          border-radius 40px
          if $language == 'en-US'
            letter-spacing normal

#page-name
  +minWidth900()
    align-items center
    display flex
    border-radius 12px
    height 40px
    position absolute
    top 62px
    left 0
    right 0
    margin auto
    justify-content center
    animation-timing-function ease-out

#page-name-mask
  +minWidth900()
    width 100%
    height 100%
    position absolute
    overflow hidden
    left 0

if hexo-config('console.enable')
  #nav-console
    +maxWidth768()
      display none
