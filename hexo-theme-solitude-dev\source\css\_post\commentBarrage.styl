.comment-barrage
  position fixed
  bottom 0
  if hexo-config('rightside.enable')
    right 2.5rem
  else
    right 20px
  padding 0 0 20px 10px
  display none
  flex-direction column
  justify-content flex-end
  z-index 999
  transition all .3s ease 0s
  user-select none

  +maxWidth768()
    display none !important

  .comment-barrage-item
    min-width 300px
    max-width 300px
    width fit-content
    min-height 80px
    max-height 150px
    margin 4px
    padding 8px 14px
    background var(--efu-maskbgdeep)
    border-radius 8px
    color var(--efu-fontcolor)
    animation 0.6s cubic-bezier(0.42, 0, 0.3, 1.11) 0s 1 normal none running barrageIn;
    transition all 0.3s ease 0s
    display: flex
    flex-direction column
    border var(--style-border)
    backdrop-filter saturate(180%) blur(20px)
    box-shadow var(--efu-shadow-border)
    overflow hidden

    &:hover
      border var(--style-border-hover)
      box-shadow var(--efu-shadow-main)

    &.out
      opacity 0
      animation 0.6s cubic-bezier(0.42, 0, 0.3, 1.11) 0s 1 normal none running barrageOut

    &.hovered
      opacity 0

    .comment-barrage-close
      color var(--efu-secondtext)
      cursor pointer
      line-height 1
      margin 4px

      &:hover
        color var(--efu-main)

      .solitude
        color var(--efu-fontcolor)
        font-size 18px

    .barrageHead
      height 30px
      padding 0 0 6px
      line-height 30px
      font-size 12px
      border-bottom var(--style-border)
      display flex
      justify-content space-between
      align-items center
      font-weight 700

      .barrageTitle
        color var(--efu-card-bg)
        margin-right 8px
        background var(--efu-fontcolor)
        line-height 1
        padding 4px
        border-radius 4px
        white-space nowrap

        &:hover
          background var(--efu-main)
          color var(--efu-white)

    .barrageAvatar
      width 16px
      height 16px
      margin 0 8px 0 auto
      border-radius 50%
      background var(--efu-secondbg)

    .barrageContent
      height calc(100% - 30px)
      overflow hidden
      width fit-content
      max-height 48px
      font-size 14px !important
      font-weight 400 !important

      h1, h2, h3, h4
        font-size 14px !important
        font-weight 400 !important
        margin 8px 0 !important

      a
        pointer-events none
        font-size 14px !important

      p
        margin 8px 0
        line-height 1.3
        overflow hidden
        text-overflow ellipsis
        -webkit-line-clamp 2
        display -webkit-box
        -webkit-box-orient vertical
        font-size 14px
        max-width 270px

        img:not(.tk-owo-emotion)
          display none
        img.tk-owo-emotion
          width 16px
          padding 0
          margin 0
          transform translateY(2px)

      pre, li, blockquote, br
        display none

      &::-webkit-scrollbar
        height 0
        width 4px

      &::-webkit-scrollbar-button
        display none