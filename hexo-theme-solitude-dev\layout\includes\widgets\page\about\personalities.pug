- const personalities = site.data.about.personalities

if personalities
    .author-content
        .author-content-item.personalities
            .author-content-item-tips= personalities.tips
            span.author-content-item-title= personalities.title
            .title2(style=`color:${personalities.color}`)= personalities.type
            .image
                img.nolazyload(src=personalities.image)
            .post-tips
                = _p('about.personalities.tip1') + ' '
                a(href='https://www.16personalities.com/')= personalities.linkText + ' '
                = _p('about.personalities.tip2') + ' '
                a(href=personalities.typeLink)= personalities.typeName
        .author-content-item.myphoto
            img.author-content-img(src=personalities.myphoto, alt="my photo")