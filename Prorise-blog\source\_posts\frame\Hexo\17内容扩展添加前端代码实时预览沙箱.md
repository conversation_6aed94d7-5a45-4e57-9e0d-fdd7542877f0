---
title: 17.内容扩展：添加“前端代码实时预览”沙箱
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 11486
date: 2025-07-11 09:13:45
---

### **17.内容扩展：添加“前端代码实时预览”沙箱**

###### **前言：功能介绍**

本指南将引导您在文章中集成 **Mini Sandbox**。集成后，读者不仅能看到您的HTML, CSS, JS代码，还能**实时修改并立即看到运行效果**，对于前端教学和效果展示来说是绝佳的工具。

---
###### **第一步：添加夜间模式适配CSS（一次性配置）**

为了让沙箱的深色模式能与您主题的深色模式完美匹配，我们需要先添加一段全局的适配CSS。

1.  **打开您的自定义CSS文件** (`source/custom/css/sandbox_style.css`)。

2.  将下面这段**CSS代码**，完整地**追加**到文件末尾。

    ```css
    /* Mini Sandbox 夜间模式适配 */
    .mini-sandbox{
        box-shadow: none!important;
        border-radius: 7px;
        margin-bottom: 10px;
      }
      [data-theme=dark] .mini-sandbox{
        background-color: #151515!important;
      }
      [data-theme=dark] .cm-activeLine{
         background-color: #252525!important;
      }
      [data-theme=dark] .mini-sandbox .sandbox-head{
        background: #202020!important;
      }
      [data-theme=dark] .mini-sandbox .cm-gutters{
        background: #202020!important;
      }
      .mini-sandbox .sandbox-head .sandbox-tab .sandbox-tab-active{
        box-shadow: none!important;
      }
      [data-theme=dark] .mini-sandbox .sandbox-head .sandbox-tab .sandbox-tab-active{
        background: #363636!important;
      }
      [data-theme=dark] .cm-activeLineGutter{
        background: #363636!important;
      }
      [data-theme=dark] .sandbox-body .sandbox-gutter{
        background: #363636!important;
      }
      [data-theme=dark] .mini-sandbox .sandbox-gutter{
        border-left: 1px solid #404040!important;
        border-right: 1px solid #404040!important;
      }
      [data-theme=dark] .mini-sandbox .sandbox-head .sandbox-tab .sandbox-tab-active::after,
      [data-theme=dark] .mini-sandbox .sandbox-head .sandbox-tab .sandbox-tab-active::before{
        background: none!important;
      }
      [data-theme=dark] .mini-sandbox .sandbox-render{
        background: #E1E1E1!important;
      }
      /* 以下为代码颜色，可按需修改 */
      [data-theme=dark] .mini-sandbox .ͼd,
      [data-theme=dark] .mini-sandbox .ͼc { color: #c3e88d!important; }
      [data-theme=dark] .mini-sandbox .ͼb,
      [data-theme=dark] .mini-sandbox .ͼf { color: #1E90FF!important; }
      [data-theme=darks] .mini-sandbox .ͼh { color: #ff5370!important; }
      [data-theme=dark] .mini-sandbox .ͼa { color: #FF00FF!important; }
      [data-theme=dark] .mini-sandbox .ͼi { color:#5F9EA0!important; }
      [data-theme=dark] .mini-sandbox .ͼl { color:#969896!important; }
    ```

3.  **确认CSS已注入**：确保您这个自定义CSS文件已经被添加到了主题的 `inject` 配置中。

    ```yaml
    inject:
      head:
        # 引入代码沙盒样式文件
        - '<link rel="stylesheet" href="/custom/css/sandbox_style.css">'
    ```

---
###### **第二步：在文章中嵌入沙箱（每次使用时操作）**

当您想在某篇文章中插入一个代码沙箱时，请在该文章的 `.md` 文件中，按照以下两个步骤操作：

**1. 在文章正文放置HTML容器**
* 在您想显示沙箱的**具体位置**，插入一个 `<div>` 标签，并给它一个**唯一的ID**（如果一个页面有多个沙箱，ID不能重复）。
    ```html
    <div id="my-sandbox-demo"></div>
    ```

**2. 在文章末尾添加Pjax兼容脚本**
* 将下面这个**完整**的、**增强版**的脚本，复制并粘贴到您文章 `.md` 文件的**最末尾**。这个脚本已经整合了库的加载和初始化，您不再需要手动在顶部引入库文件了。

    ```javascript
    <script>
      // 1. 定义一个初始化沙箱的函数
      function initMySandbox() {
        // 检查页面上是否存在沙箱容器，以及 MiniSandbox 库是否已加载
        // 避免在没有沙箱的页面上，或者库加载失败时报错
        if (document.querySelector('#my-sandbox-demo') && typeof MiniSandbox !== 'undefined') {
          new MiniSandbox({
            el: '#my-sandbox-demo', // 确保ID与上面的div匹配
            files: {
              'index.html': {
                title: 'HTML',
                defaultValue: `<button class="my-btn">点我试试</button>`,
              },
              'index.css': {
                title: 'CSS',
                defaultValue: `button.my-btn { 
      background: #425AEF;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    button.my-btn:hover {
      background: #3a50d7;
      transform: translateY(-2px);
    }`,
              },
              'index.js': {
                title: 'JS',
                defaultValue: `const btn = document.querySelector('.my-btn')
    btn.addEventListener('click', () => {  
        alert('你点击了按钮！')
    })`,
              }
            },
            defaultConfig: {
              height: '350px'
            }
          });
          console.log('Mini Sandbox Initialized!'); // 这条日志可以帮助您在F12控制台确认脚本是否成功执行
        }
      }
    
      // 2. 定义一个加载库并初始化的函数
      const loadMiniSandbox = () => {
        // 检查库是否已加载，避免重复加载
        if (typeof MiniSandbox === 'undefined') {
          const script = document.createElement('script');
          script.src = 'https://unpkg.com/mini-sandbox@0.3.11';
          script.onload = () => {
            // 库加载完成后，执行初始化函数
            initMySandbox();
          };
          document.body.appendChild(script);
        } else {
          // 如果库已存在（例如在同一页面内Pjax刷新），直接执行初始化
          initMySandbox();
        }
      }
      
      // 3. 监听 Pjax 加载完成事件，在页面切换后再次执行
      document.addEventListener('pjax:success', function () {
        loadMiniSandbox();
      });
    
      // 4. 页面首次加载时也执行一次（应对非Pjax跳转或强制刷新）
      loadMiniSandbox();
    
    </script>
    ```