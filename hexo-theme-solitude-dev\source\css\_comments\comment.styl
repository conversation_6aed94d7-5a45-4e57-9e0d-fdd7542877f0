#post-comment
  .comment-head
    .comment-headline
      display inline-block
      vertical-align middle
      font-weight bold
      font-size 20px

    #comment-switch
      display inline-block
      float right
      margin .1rem auto 0
      padding .2rem .8rem
      width max-content
      border-radius 5px
      background #f6f8fa

      .first-comment
        color #307af6

      .second-comment
        color #ff7242

      .switch-btn
        position relative
        display inline-block
        margin -4px .4rem 0
        width 42px
        height 22px
        border-radius 34px
        background-color #307af6
        vertical-align middle
        cursor pointer
        transition all .4s ease

        &::before
          position absolute
          bottom 4px
          left 4px
          width 14px
          height 14px
          border-radius 50%
          background-color #fff
          content ""
          transition all .4s ease

        &.move
          background-color #ff7242

          &::before
            transform translateX(20px)

.post-comment
  background var(--efu-card-bg)

#post-comment
  .post &
    padding 0 2rem .5rem
    +maxWidth768()
      padding 0 .5rem .5rem
  > div.comment-head
    > div.comment-privacy
      display block
      justify-content space-between
      margin-left 8px
      font-size 13px

    > div.comment-privacy
      a:hover
        color var(--efu-main)

  blockquote
    background var(--efu-secondbg)
    border var(--style-border)
    box-shadow none
    margin 0 0 .5rem
    font-size .6rem
    color var(--efu-secondtext)
    border-radius 8px

  if hexo-config('comment.use')[1] || ',' in hexo-config('comment.use')
    &.move
      if hexo-config('comment.count')
        .comment-head
          .count span:first-child
            display none

          .count span:last-child
            display inline-block

      #switch-btn::before
        transform translateX(20px)

      .comment-wrap
        > div:first-child
          display none

        > div:last-child
          display block
          animation 0.5s ease 0s 1 normal none running tabshow

  .comment-head
    font-size .8em
    margin .5rem 0
    position relative

    if hexo-config('comment.use')[1] || ',' in hexo-config('comment.use')
      .count span:last-child
        display none

      .comment-switch
        display inline-block
        float right
        margin 2px auto 0
        padding 4px 16px
        width max-content
        user-select none

        #switch-btn
          position relative
          display inline-block
          margin 0 8px 0
          width 42px
          height 22px
          border-radius 34px
          background-color var(--efu-main)
          vertical-align middle
          cursor pointer
          transition .4s
          border var(--style-border-always)

          &::before
            position absolute
            bottom 3px
            left 4px
            width 14px
            height 14px
            border-radius 50%
            background-color #fff
            content ''
            transition .4s

  if hexo-config('comment.use')[1] || index(hexo-config('comment.use'), ',')
    .comment-wrap
      > div:last-child
        display none

      > div:first-child
        animation 0.5s ease 0s 1 normal none running tabshow

#owo-big
  position fixed
  align-items center
  background-color var(--efu-card-bg)
  border var(--style-border-always)
  border-radius 10px
  z-index 9999
  max-width 142px
  display none
  transform translate(0, -105%)
  overflow hidden
  animation owoIn .3s cubic-bezier(.42, 0, .3, 1.11)
  padding 1rem
  +maxWidth768()
    display none !important

if hexo-config('twikoo.style') && 'Twikoo' in hexo-config('comment.use')
  @import 'twikoo.styl'

if hexo-config('valine.style') && 'Valine' in hexo-config('comment.use')
  @import 'valine.styl'
