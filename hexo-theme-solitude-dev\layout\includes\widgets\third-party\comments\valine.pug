- const { lazyload, count, use,commentBarrage } = theme.comment
- const { appId, appKey, avatar, serverURLs, visitor, option } = theme.valine
- let emojiMaps = '""'

if site.data.valine
    - emojiMaps = JSON.stringify(site.data.valine)

script.
    (() => {
        const initValine = () => {
            const valine = new Valine({
                el: '#vcomment',
                appId: '#{appId}',
                appKey: '#{appKey}',
                avatar: '#{avatar}',
                serverURLs: '#{serverURLs}',
                emojiMaps: !{emojiMaps},
                path: window.location.pathname,
                visitor: #{visitor},
                ...!{JSON.stringify(option)}
            })
            GLOBAL_CONFIG.lightbox && utils.lightbox(document.querySelectorAll('#comment .vcontent img:not(.vemoji)'))
            sco.owoBig({body: '.vwrap', item: '.vemojis i'})
        }
        const loadValine = async () => {
            if (typeof Valine === 'function') setTimeout(initValine, 0)
            else {
                await utils.getScript('!{url_for(theme.cdn.valine)}').then(initValine)
            }
            !{commentBarrage} && barrageValine()
        }
        if ('!{use[0]}' === 'Valine' || !{lazyload}) {
            if (!{lazyload}) utils.loadComment(document.getElementById('vcomment'), loadValine)
            else loadValine()
        } else {
            window.loadTwoComment = loadValine
        }
    })()

if commentBarrage
    script.
        async function barrageValine() {
            const url = new URL('!{serverURLs}/1.1/classes/Comment')
            const params = {
                url: window.location.pathname,
                order: '-createdAt'
            }
            Object.entries(params).forEach(([key, value]) => url.searchParams.append(key, value))
            await fetch(url, {
                method: "GET",
                headers: {
                    "X-LC-Id": "#{appId}",
                    "X-LC-Key": "#{appKey}",
                    "Content-Type": "application/json"
                },
            }).then(async res => {
                if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`)
                const data = await res.json()
                const init = () =>
                    initializeCommentBarrage(data.results
                        .filter(item => item.url === window.location.pathname)
                        .map(item => ({
                            content: item.comment,
                            nick: item.nick,
                            mailMd5: md5(item.mail),
                            id: item.objectId
                        })))
                if (typeof md5 === "undefined") await utils.getScript('!{url_for(theme.cdn.blueimp_md5)}')
                if (typeof initializeCommentBarrage === "undefined") await utils.getScript('!{url_for(theme.cdn.commentBarrage)}').then(init)
                else init()
            }).catch(error => console.error("An error occurred while fetching comments: ", error))
        }