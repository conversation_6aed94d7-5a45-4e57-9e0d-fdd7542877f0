---
title: SpringAI（三）：3. 会话核心 API 深度解析
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 52289
date: 2025-03-20 22:13:45
---

## **3. 会话核心 API 深度解析**

在上一章，我们成功打通了前后端的任督二脉，实现了基础的聊天功能。但一个真正的智能应用，不能只有“一问一答”的瞬时反应，它还需要具备“反思”过去（可观测性）和“铭记”历史（对话记忆）的能力。本章，我们将深入 Spring AI 的两大核心机制，为我们的 AI-Copilot 赋予更深层次的智能。

### **3.1 揭开黑盒：`ChatClient` 的可观测性**

在进行任何复杂的系统开发时，可观测性（Observability）都是成功的基石。对于 AI 应用开发而言，这一点尤为重要。我们通过 `ChatClient` 的流畅API与大语言模型（LLM）交互，但这层优雅的抽象也可能成为一个“黑盒”。我们编写的 `.user("你好")`，在 Spring AI 框架内部，可能会被动态地与系统级指令、历史对话、甚至是函数调用（Function Calling）的定义组合在一起，形成一个远比我们想象中复杂的最终请求体。

当模型的响应与我们的预期出现偏差——例如，它没有遵循我们的系统指令，或者忘记了之前的对话内容——我们面临的第一个、也是最核心的难题便是：**我的应用最终到底向 AI 发送了什么内容？** 如果无法看清这个“黑盒”的内部，后续的 Prompt Engineering（提示词工程）、上下文管理优化、乃至错误排查都将无从谈起。本节将深入探讨 Spring AI 提供的核心调试工具，让我们能够点亮一盏灯，彻底照亮 `ChatClient` 的内部通信链路。

#### **3.1.1 核心利器: `Advisor` 与 `SimpleLoggerAdvisor`**

为了解决上述的可观测性问题，Spring AI 引入了名为 **`Advisor` (顾问)** 的优雅设计模式。在软件工程领域，这与面向切面编程（AOP）中的“通知”（Advice）或网络编程中的“拦截器”（Interceptor）思想一脉相承。`Advisor` 允许我们在不侵入 `ChatClient` 核心逻辑的前提下，在其请求发送前和响应返回后“织入”我们自定义的横切关注点，如日志记录、指标监控等。

在众多 `Advisor` 的内置实现中，`SimpleLoggerAdvisor` 是我们进行开发和调试时最不可或缺的利器。它的核心作用可以简洁地概括如下：

| 特性 | 描述 |
| :--- | :--- |
| **核心职责** | 打印完整的`ChatRequest`和`ChatResponse` |
| **工作模式** | 日志级别为`DEBUG`/`TRACE`时激活 |
| **适用场景** | 开发调试、Prompt调优、上下文问题排查 |
| **性质** | 只读、无侵入，不修改请求或响应 |

`Advisors` API 提供了一种灵活而强大的方法来拦截、修改和增强 Spring 应用程序中的 AI 驱动的交互。使用用户文本调用 AI 模型时，一种常见模式是使用上下文数据附加或增强提示。此上下文数据可以是不同的类型，常见的包括您自己的私有数据和对话历史记录。

`SimpleLoggerAdvisor` 会记录 `ChatClient` 的 request 和 response 数据，这对于调试和监控 AI 交互非常有用。

#### **3.1.2 实战：为 AI-Copilot 开启调试日志**

在 Spring Boot 的生态中，启用 `SimpleLoggerAdvisor` 的过程极其简单。在我们之前的做法中，通过使用 `@Slf4j` 这个注解进行精细化的日志控制，但这并不意味着它俩冲突。不过，仅在我们这一层，使用 `SimpleLoggerAdvisor` 的效果远比手动日志要来得好得多。

**1. 配置日志级别**

在 `src/main/resources/application.yml` 文件中添加 `logging.level` 配置，告诉日志系统我们关心 `advisor` 包下的 `DEBUG` 信息。

```yaml
# src/main/resources/application.yml
logging:
  level:
    # 关键：将 advisor 包的日志级别设置为 DEBUG。
    # 只有这样，SimpleLoggerAdvisor 内部的 isDebugEnabled() 判断才会为 true。
    org.springframework.ai.chat.client.advisor: DEBUG
    
    # 推荐：将我们自己应用的包也设为 DEBUG，方便观察完整调用链路。
    com.copilot.aicopilotbackend: DEBUG
```

**2. 在 `ChatClient` Bean 中应用顾问**

我们将 `SimpleLoggerAdvisor` 的应用，集中在 `config` 包中进行配置。

**创建 `AiConfig.java`:**

```java
// src/main/java/com/copilot/aicopilotbackend/config/AiConfig.java
package com.copilot.aicopilotbackend.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AiConfig {

    /**
     * 定义 ChatClient Bean，并为其配置默认的 Advisor。
     * @param chatModel Spring Boot 根据 application.yml 自动配置好的 ChatModel 实例
     * @return 一个配置了日志顾问的 ChatClient 实例
     */
    @Bean
    public ChatClient chatClient(ChatModel chatModel) {
        return ChatClient.builder(chatModel)
                .defaultAdvisors(new SimpleLoggerAdvisor())
                .build();
    }
}
```

**修改 `ChatService.java` 的构造函数:**
现在 `ChatService` 不再需要 `ChatClient.Builder`，而是直接注入我们配置好的 `ChatClient` Bean。

```java
// src/main/java/com/copilot/aicopilotbackend/service/ChatService.java
@Slf4j
@Service
public class ChatService {
    
    private final ChatClient chatClient;

    public ChatService(ChatClient chatClient) {
        this.chatClient = chatClient;
    }
    
    // ... 其他方法
}
```

#### **3.1.3 解读日志输出**

完成了以上配置并重启应用后，当我们调用任何一个聊天API接口时，控制台中将自动出现详尽的调试信息：

```log
2025-06-26 18:30:00.123 DEBUG 12345 --- [nio-8080-exec-1] o.s.a.c.c.a.SimpleLoggerAdvisor       :
--- Request:
[
  {
    "messageType": "USER",
    "content": "你好"
  }
]
--- Response:
{
  "messageType": "ASSISTANT",
  "content": "你好！我是AI-Copilot，很高兴能为您服务。"
}
```

这段日志是极其宝贵的调试信息，它清晰地展示了发送给大语言模型API的**最终请求体**和模型返回的**原始响应**，让我们能彻底洞察“黑盒”内部，为后续的 Prompt 调优和问题排查提供了坚实的基础。

#### **3.1.4 `ChatClient` 详解(重点内容) **

`ChatClient` 提供用于与 AI 模型通信的 Fluent API，支持同步和流式编程模型。

**1. 创建 `ChatClient`**

`ChatClient` 是使用 `ChatClient.Builder` 对象创建的。您可以获取自动配置的 `ChatClient.Builder` 实例，或者以编程方式创建一个。

在最简单的用例中， Spring AI 提供 Spring Boot 自动配置，创建一个原型 `ChatClient.Builder` bean，以便注入到你的类中。

```java
@RestController
class MyController {

    private final ChatClient chatClient;
    
    public MyController(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder.build();
    }
    
    @GetMapping("/ai")
    String generation(String userInput) {
        return this.chatClient.prompt()
            .user(userInput)
            .call()
            .content();
    }
}
```

> 注意：我们项目中采用的是将 `ChatClient` 本身注册为 Bean 的方式，如 `3.1.2` 节所示，这在管理默认配置时更为方便。

`ChatClient` 是 Spring AI 中进行大模型交互的核心，它提供了一个优雅的流式（Fluent）API。在我们的 AI-Copilot 项目中，我们没有在业务代码（如Controller或Service）中临时创建 `ChatClient` 实例，而是采用了一种更强大、更可维护的模式：**在 `AiConfig` 中将其配置为单例 Bean**。

这种方式的好处是显而易见的：

  * **集中配置**：所有默认行为，如模型选项（temperature、top\_p等）、系统提示、以及最重要的 `Advisors`（顾问），都在一个地方统一配置。
  * **简化业务**：业务代码只需要注入配置好的 `ChatClient` Bean，无需关心其复杂的构建过程，可以直接调用其功能。
  * **一致性**：确保整个应用的所有AI交互都遵循相同的基本配置和增强逻辑（如日志和对话记忆）。

下面，我们来详细解析在项目中是如何运用 `ChatClient` 的流式API的。

**1. 启动调用链 (`.prompt()`)**

所有交互都始于 `.prompt()` 方法。这是一个无参数的方法，它会返回一个调用链的起点，允许我们后续构建请求。

**2. 构建Prompt内容 (`.user()`, `.system()`)**

  * `.user(String message)`: 这是最常用的方法，用于设置用户的提问内容。在我们的 `ChatService` 中，我们正是用它来传递用户的输入。
  * `.system(String message)`: 用于设置系统级指令，引导AI的行为和角色。虽然我们在最终代码中是通过 `defaultAdvisors` 来管理上下文，但在需要临时改变AI角色的场景下，可以直接在调用链中使用此方法。

**3. 增强请求 (`.advisors()`)**

这是 `ChatClient` 最强大的功能之一。在我们的项目中，`Advisor` 是实现对话记忆的关键。

```java
// 来自于我们最终的 ChatService.java
.advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
```

这段代码的含义是：

  * `.advisors(...)` 方法接收一个 `Consumer<AdvisorSpec>` Lambda表达式，允许我们对本次请求的 `Advisors` 进行配置。
  * `a.param(key, value)` 用于向 `Advisor` 的执行上下文传递参数。
  * 在这里，我们将 `conversationId` 以 `ChatMemory.CONVERSATION_ID` 为键传入。我们之前在 `AiConfig` 中配置的 `MessageChatMemoryAdvisor` 会自动捕获这个参数，并根据它来查找对应的历史消息，然后将这些历史消息注入到最终发送给AI的 `Prompt` 中。

**4. 执行与响应处理 (`.stream().content()`)**

`ChatClient` 支持两种主要的执行模式：

  * **同步调用**: `.call()` 会阻塞并等待AI返回完整响应。它适用于不需要实时反馈的场景。
  * **流式调用**: `.stream()` 会立即返回一个 `Flux` (来自响应式编程库 Project Reactor)，AI的响应会以数据块的形式持续不断地推送过来。

为了提供最佳的用户体验，我们的项目**完全采用了流式调用**。

```java
// 来自于我们最终的 ChatService.java
.stream()  // 1. 声明我们想要一个流式响应
.content() // 2. 我们只关心响应流中的文本内容 (Flux<String>)
```

组合起来，`chatClient.prompt()...stream().content()` 就构成了一个完整的、具备对话记忆能力的流式聊天请求。

**5. 返回结构化数据 (`.entity()`)**

虽然我们的聊天功能主要处理文本流，但 `ChatClient` 还能将AI的响应直接转换为Java对象（POJO），这在需要AI生成特定JSON结构的场景下极为有用。

假设我们想让AI生成一个演员的电影列表，我们可以这样定义一个`record`：

```java
public record ActorFilms(String actor, List<String> movies) {}
```

然后，我们可以这样调用：

```java
ActorFilms actorFilms = chatClient.prompt()
    .user("请生成演员汤姆·汉克斯的电影作品列表，以JSON格式返回，包含 actor 和 movies 两个字段。")
    .call() // 注意：通常结构化数据需要等待完整响应，因此使用 .call()
    .entity(ActorFilms.class);
```

`ChatClient` 会自动提示AI按指定格式输出，并将返回的JSON字符串反序列化为 `ActorFilms` 对象。对于泛型，用法也同样直观：

```java
List<ActorFilms> actorFilms = chatClient.prompt()
    .user("请为汤姆·汉克斯和比尔·默里生成电影作品列表...")
    .call()
    .entity(new ParameterizedTypeReference<List<ActorFilms>>() {});
```

**6. 配置模型参数 (`.options()`)**

我们可以在请求级别覆盖默认的模型参数（如 `temperature`）。`OpenAiChatOptions` 的最新语法使用直接的 `.` 方法进行链式构建。

```java
// 这是一个示例，展示如何在单次请求中设置参数
String creativeResponse = chatClient.prompt()
    .user("写一首关于宇宙的诗")
    .options(OpenAiChatOptions.builder()
        .model("gpt-4o")
        .temperature(0.9f) // 设置更高的温度以获得更有创意的回答
        .build()
    )
    .call()
    .content();
```

当然，最佳实践依然是在 `AiConfig` 中通过 `.defaultOptions()` 设置全局默认值，只在需要时进行局部覆盖。

通过这样重构，本节内容现在完全与项目代码保持一致，清晰地解释了我们所使用的每一个API背后的原理和目的。
### **3.2 赋予AI记忆：`ChatMemory` 深度实践**

大语言模型（LLM）的API接口遵循HTTP协议，其核心特性之一就是**无状态（Stateless）**。每一次API调用都是一次全新的、独立的对话。这种“金鱼记忆”显然无法满足构建一个能持续对话的智能应用的需求。

为了解决这一核心痛点，Spring AI 提供了强大而灵活的`ChatMemory`功能。

#### **3.2.1 核心设计：策略与存储分离**

Spring AI在对话记忆功能上的核心设计思想，是软件工程中“关注点分离”原则的经典体现：**将记忆的策略（如何记住）与记忆的存储（记在哪里）相分离**。

这一思想通过两个核心接口得以实现：

1.  **`ChatMemory` (策略接口)**: 它定义了记忆的**行为和策略**。例如，它决定当对话历史过长时，应该保留哪些消息、遗忘哪些消息。
2.  **`ChatMemoryRepository` (存储接口)**: 它定义了记忆的**物理存储和检索**。它的职责非常纯粹，就是在后端（如内存、数据库、Redis）存取`Message`数据。

我们必须严格辨析两个极易混淆的概念：

| 概念 | 定义与范围 | 目的与用途 |
| :--- | :--- | :--- |
| **对话记忆 (Chat Memory)** | 用于构建下一次Prompt的、一个相关的、有限的对话历史**子集**。 | **为AI服务**，让AI理解上下文，进行连贯对话。 |
| **对话记录 (Chat History)** | 一次会话中**全部、完整的**消息交换历史。 | **为应用和用户服务**，用于审计、回溯、查看历史。 |

`ChatMemory` 抽象旨在管理*对话记忆*，而不是完整的*对话记录*。如果您需要维护所有交换消息的完整记录，应考虑使用不同的方法，比如我们稍后将介绍的基于 `MyBatis-Plus` 的方案。

#### **3.2.2 记忆策略与存储详解**

**1. 记忆策略 - `MessageWindowChatMemory`**

这是 Spring AI中最常用、也是默认的记忆策略。它实现了一种高效的“滑动窗口”机制，只保留最近的 N 条消息作为上下文，在成本、性能和相关性之间取得了最佳平衡。当消息数量超过此限制时，将驱逐较旧的消息，但会保留系统消息。

```java
MessageWindowChatMemory memory = MessageWindowChatMemory.builder()
	.maxMessages(20) // 设置窗口大小为 20 条消息
    .build();
```

**2. 记忆存储 - `ChatMemoryRepository`**

它负责将对话消息进行物理存储。Spring AI 提供了多种内置实现。

| 实现类 | 存储介质 | 优点 | 缺点 | 适用场景 |
| :--- | :--- | :--- | :--- | :--- |
| `InMemory...Repository` | JVM 内存 | 零配置, 极速 | 数据易失 | 开发, 测试, 原型 |
| `Jdbc...Repository` | 关系型数据库 | 可靠, 持久化 | 需配置 | **生产环境** |
| `Cassandra...Repository`| Cassandra | 高可用, 高扩展性 | 配置复杂 | 大规模分布式系统 |
| `Neo4j...Repository` | Neo4j图数据库 | 利用图关系 | 需图数据库知识 | 需要分析对话关系 |

在我们的项目中，我们将采用 `JdbcChatMemoryRepository` 实现生产级的持久化记忆。

#### **3.2.3 实战：为 AI-Copilot 实现多轮对话**

我们将使用 `JdbcChatMemoryRepository` 为我们的应用添加持久化的多轮对话能力。

**1. 添加数据库依赖 (`pom.xml`)**

```xml
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-model-chat-memory-repository-jdbc</artifactId>
</dependency>
```

**2. 配置数据库与 `application.yml`**

  * **执行 DDL**: 请在您的 MySQL 数据库中执行以下 SQL 来创建所需的表。

    ```sql
    CREATE TABLE IF NOT EXISTS `spring_ai_chat_memory` (
      `id` BIGINT NOT NULL AUTO_INCREMENT,
      `conversation_id` VARCHAR(255) NOT NULL,
      `content` TEXT NOT NULL,
      `type` VARCHAR(50) NOT NULL,
      `timestamp` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
      `media` JSON DEFAULT NULL,
      `metadata` JSON DEFAULT NULL,
      PRIMARY KEY (`id`),
      KEY `idx_conversation_id` (`conversation_id`)
    );
    ```

  * **修改 `application.yml`**:

    ```yaml
    spring:
      ai:
        chat:
          memory:
            repository:
              jdbc:
                initialize-schema: never
                platform: mysql
      datasource:
        url: **************************************************************************************************
        username: root
        password: root
        driver-class-name: com.mysql.cj.jdbc.Driver
    ```

**3. 在 `AiConfig` 中配置记忆功能**

我们将 `ChatMemory` 的创建和 `MessageChatMemoryAdvisor` 的应用，全部集中在 `AiConfig` 配置类中。

```java
package com.copilot.aicopilotbackend.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AiConfig {

    /**
     * 定义 ChatClient Bean，并为其配置默认的 Advisor。
     * @param chatModel Spring Boot 根据 application.yml 自动配置好的 ChatModel 实例
     * @param chatMemory 聊天内存实例，用于维护对话上下文
     * @return 一个配置了日志顾问和内存顾问的 ChatClient 实例
     */
    @Bean
    public ChatClient chatClient(ChatModel chatModel, ChatMemory chatMemory) {
        return ChatClient.builder(chatModel)
                .defaultAdvisors(
                        new SimpleLoggerAdvisor(),
                        MessageChatMemoryAdvisor.builder(chatMemory).build()
                )
                .build();
    }

    /**
     * Spring AI 会自动配置 ChatMemory Bean
     * 当检测到 JDBC 依赖时会自动使用 JDBC 存储库
     * 这里不需要手动配置，依赖自动配置机制
     */
}

```

> **自动配置说明**: Spring AI 会自动检测到 `spring-ai-starter-model-chat-memory-repository-jdbc` 依赖和 `application.yml` 中的配置，并自动创建一个 `JdbcChatMemoryRepository` 的 Bean。我们的 `chatMemory()` 方法会自动使用这个 Bean 作为 `MessageWindowChatMemory` 的后端存储。

> **顾问链顺序**: 将 advisor 添加到链中的顺序至关重要。`ChatClient.builder().advisors(advisor1, advisor2)` 中，`advisor1` 会先执行。

**4. 改造后端业务代码以支持会话**

- **改造`ChatRequest`**

  ```java
  package com.copilot.aicopilotbackend.dto.request;
  
  import com.fasterxml.jackson.annotation.JsonProperty;
  
  import java.util.UUID;
  
  /**
   * 聊天请求DTO
   * @param message 用户消息内容
   * @param conversationId 会话ID，可选参数，为空时自动生成
   */
  public record ChatRequest(
          @JsonProperty("message") String message,
          @JsonProperty("conversationId") String conversationId,
          @JsonProperty("isDeepThink") Boolean isDeepThink
  ) {
      /**
       * 获取有效的会话ID
       * @return 非空的会话ID
       */
      public String getEffectiveConversationId() {
          return conversationId != null ? conversationId : UUID.randomUUID().toString();
      }
  }
  
  ```

  

  * **改造 `ChatService.java`**:

    ```java
    // src/main/java/com/copilot/aicopilotbackend/service/ChatService.java
    package com.copilot.aicopilotbackend.service;
    
    import com.copilot.aicopilotbackend.exception.SystemException;
    import com.copilot.aicopilotbackend.exception.ErrorCode;
    import com.copilot.aicopilotbackend.validation.ChatMessageValidator;
    import org.springframework.ai.chat.client.ChatClient;
    import org.springframework.ai.chat.memory.ChatMemory;
    import org.springframework.stereotype.Service;
    import reactor.core.publisher.Flux;
    
    /**
     * 聊天服务
     * 提供AI流式聊天功能
     */
    @Service
    public class ChatService {
        
        private final ChatClient chatClient;
    
        public ChatService(ChatClient chatClient) {
            this.chatClient = chatClient;
        }
        
        /**
         * 获取流式聊天响应
         * @param message 用户消息
         * @param conversationId 会话ID
         * @return 流式响应
         */
        public Flux<String> getStreamingChat(String message, String conversationId) {
            // 验证消息
            ChatMessageValidator.validateMessage(message);
            
            return chatClient.prompt()
                    .user(message)
                	// 重点在这里，通过advisors注入会话记忆
                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                    .stream()
                    .content()
                    .onErrorMap(e -> new SystemException(ErrorCode.AI_SERVICE_UNAVAILABLE, 
                                                       "AI服务当前不可用，请稍后重试", e));
        }
    }
    ```

  * **改造 `ChatController.java`**:

    ```java
    package com.copilot.aicopilotbackend.controller;
    
    import com.copilot.aicopilotbackend.dto.request.ChatRequest;
    import com.copilot.aicopilotbackend.service.ChatService;
    import com.fasterxml.jackson.databind.ObjectMapper;
    import lombok.RequiredArgsConstructor;
    import org.springframework.http.MediaType;
    import org.springframework.http.codec.ServerSentEvent;
    import org.springframework.web.bind.annotation.*;
    import reactor.core.publisher.Flux;
    
    import java.time.Duration;
    import java.util.Map;
    
    @RestController
    @RequestMapping("/api/v1/chat")
    @RequiredArgsConstructor
    public class ChatController {
    
        private final ChatService chatService;
        private final ObjectMapper objectMapper = new ObjectMapper();
    
        @PostMapping(
                value = "/stream",
                consumes = MediaType.APPLICATION_JSON_VALUE,
                produces = MediaType.TEXT_EVENT_STREAM_VALUE
        )
        public Flux<ServerSentEvent<String>> stream(@RequestBody ChatRequest req) {
            return chatService.getStreamingChat(req.message(), req.getEffectiveConversationId())
                    .map(content -> {
                        try {
                            // 将AI内容包装成JSON格式：{"content": "实际内容"}
                            String jsonContent = objectMapper.writeValueAsString(
                                    Map.of("content", content)
                            );
                            return ServerSentEvent.<String>builder()
                                    .data(jsonContent)
                                    .build();
                        } catch (Exception e) {
                            // 如果JSON序列化失败，发送错误信息
                            return ServerSentEvent.<String>builder()
                                    .data("{\"content\": \"\"}")
                                    .build();
                        }
                    })
                    .concatWith(Flux.just(
                            // 发送结束信号
                            ServerSentEvent.<String>builder()
                                    .data("[DONE]")
                                    .build()
                    ));
        }
    }
    
    ```




**5. 改造前端以管理和传递 `conversationId`**

  * **安装 `uuid` 库**:

    ```bash
    pnpm add uuid
    ```

  * **改造 `src/apis/chatService.js`**:

    ```javascript
    // src/apis/chatService.js
    
    /**
     * 获取流式聊天回复的Response对象
     * @param {string} message - 用户消息
     * @param {string} conversationId - 会话ID
     * @returns {Promise<Response>} 返回Response对象供useXStream处理
     */
    export const getStreamingChatResponse = async (message, conversationId) => {
        try {
            // 使用原生fetch发送请求
            const response = await fetch('http://localhost:8080/api/v1/chat/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message,
                    conversationId
                })
            });
    
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
    
            return response;
        } catch (error) {
            console.error('流式聊天请求失败:', error);
            throw error;
        }
    };
    
    ```

  * **改造 `src/views/ChatView.vue`**:

    ```vue
    <script setup lang="ts">
    import { ref, nextTick, computed, watch } from 'vue';
    import { v4 as uuidv4 } from 'uuid';
    import { useXStream } from 'vue-element-plus-x';
    import { getStreamingChatResponse } from '@/apis/chatService';
import type { BubbleListItemProps } from 'vue-element-plus-x/types/BubbleList';
    import ConversationManager from '../components/ConversationManager.vue';
    import { useConversations } from '../composables/useConversations';
    
    // 使用 useXStream 钩子
    const { startStream, cancel, data, error, isLoading } = useXStream();
    
    // 使用会话管理
    const {
      currentConversationId,
      currentConversation,
      currentMessages,
      addMessage,
      clearCurrentMessages
    } = useConversations();
    
    // AI和用户头像
    const avatarUser = 'https://bu.dusays.com/2025/06/16/684f747174bc3.webp';
    const avatarAI = 'https://bu.dusays.com/2025/06/26/685cf1884034c.png';
    
    // 消息类型定义
    type MessageType = BubbleListItemProps & {
      key: number;
      role: 'user' | 'ai';
      messageId: string;
    };
    
    // 消息列表数据 - 符合BubbleList格式
    const messages = ref<MessageType[]>([
      {
        key: 1,
        role: 'ai',
        messageId: uuidv4(),
        placement: 'start',
        content: '您好！我是您的 AI-Copilot，已准备就绪。',
        avatar: avatarAI,
        avatarSize: '48px',
        isMarkdown: true,
        shape: 'corner',
        variant: 'filled'
      }
    ]);
    
    // 用户输入的消息内容
    const inputMessage = ref('');
    
    // 计算属性 - 合并流式数据（纯函数）
    const streamContent = computed(() => {
      if (!data.value.length) return '';
    
      let text = '';
      for (let index = 0; index < data.value.length; index++) {
        const chunk = data.value[index].data;
    
        // 跳过空数据或结束标识
        if (!chunk || chunk === '[DONE]' || chunk.trim() === '') {
          continue;
        }
    
        try {
          // 解析JSON格式：{"content": "实际内容"}
          const parsed = JSON.parse(chunk);
          const content = parsed.content;
          if (content !== undefined && content !== null && content !== '') {
            text += content;
          }
        } catch (error) {
          // 如果解析失败，作为纯文本处理（向后兼容）
          if (typeof chunk === 'string' && chunk.trim() !== '') {
            text += chunk;
          }
        }
      }
    
      return text;
    });
    
    // 监听流数据变化，实现真正的流式体验
    watch(streamContent, (newContent) => {
      if (newContent && messages.value.length > 0) {
        const lastMessage = messages.value[messages.value.length - 1];
        if (lastMessage.role === 'ai') {
          // 流数据一到达就立即切换到流式显示模式
          if (lastMessage.loading) {
            lastMessage.loading = false; // 立即关闭loading
            lastMessage.typing = true;   // 启用打字效果
          }
          lastMessage.content = newContent; // 实时更新内容
        }
      }
    }, { immediate: true });
    
    /**
     * 发送消息处理函数
     * @param {string} message - 用户输入的消息内容
     */
    const handleSend = async (message) => {
      // 验证消息有效性和是否正在加载中
      if (!message.trim() || isLoading.value) return;
    
      // 添加用户消息到对话列表
      const userMessage: MessageType = {
        key: messages.value.length + 1,
        role: 'user',
        messageId: uuidv4(),
        placement: 'end',
        content: message.trim(),
        avatar: avatarUser,
        avatarSize: '32px',
        shape: 'corner',
        variant: 'filled' // 使用填充样式，在黑色背景下更清晰
      };
      messages.value.push(userMessage);
    
      // 立即添加AI思考状态气泡
      const aiMessage: MessageType = {
        key: messages.value.length + 1,
        role: 'ai',
        messageId: uuidv4(),
        placement: 'start',
        content: '正在思考中...',
        avatar: avatarAI,
        avatarSize: '48px',
        isMarkdown: true,
        shape: 'corner',
        variant: 'filled',
        loading: true, // 设置加载状态
        typing: false
      };
      messages.value.push(aiMessage);
    
      // 清空输入框
      inputMessage.value = '';
    
      try {
        // 获取Response对象
        const response = await getStreamingChatResponse(message, conversationId.value);
        const readableStream = response.body!;
    
        // 使用useXStream处理SSE流数据（默认支持SSE协议）
        await startStream({ readableStream });
    
        // 流结束后，停止打字效果
        const lastAiMessage = messages.value[messages.value.length - 1];
        if (lastAiMessage.role === 'ai') {
          lastAiMessage.typing = false; // 停止打字效果
          // loading 已在 watch 中处理，content 已实时更新
        }
    
      } catch (err) {
        console.error('流式请求失败:', err);
    
        // 更新最后一个AI消息为错误消息
        const lastAiMessage = messages.value[messages.value.length - 1];
        if (lastAiMessage.role === 'ai') {
          lastAiMessage.content = '抱歉，服务暂时不可用，请稍后重试。';
          lastAiMessage.loading = false;
          lastAiMessage.typing = false;
        }
      }
    };
    
    
    /**
     * 表单提交处理函数
     * 当用户点击发送按钮或按下回车键时触发
     */
    const handleSubmit = () => {
      if (inputMessage.value.trim()) {
        handleSend(inputMessage.value);
        // 清理输入框
        inputMessage.value = '';
      }
    };
    
    /**
     * 取消请求处理函数
     * 用于中断当前AI请求（预留功能）
     */
    const handleCancel = () => {
      // TODO: 实现请求取消功能
      console.log('Cancel request');
    };
    
    /**
     * 录音状态变化处理函数
     * @param {Object} event - 录音状态事件对象
     */
    const handleRecordingChange = (event) => {
      console.log('Recording state changed:', event);
    };
    
    /**
     * 触发器事件处理函数
     * 处理特殊命令触发（如 /help、/clear 等）
     * @param {Object} event - 触发器事件对象
     */
    const handleTrigger = (event) => {
      console.log('Trigger event:', event);
    };
    </script>
    
    <template>
      <div class="flex flex-col h-full">
        <!-- 应用头部 -->
        <header class="bg-white shadow-md p-4 text-center">
          <h1 class="text-xl font-bold text-primary">AI-Copilot</h1>
        </header>
    
        <!-- 聊天主体区域 -->
        <main class="flex-1 overflow-hidden">
          <!-- 使用BubbleList组件 -->
          <BubbleList :list="messages" max-height="100%" :always-show-scrollbar="false" :show-back-button="true"
            :btn-loading="isLoading" btn-color="#409EFF" />
        </main>
    
        <!-- 输入区域 -->
        <footer class="p-4 bg-white">
          <!-- 消息发送组件 -->
          <Sender v-model="inputMessage" placeholder="请输入您的问题..." :auto-size="{ minRows: 1, maxRows: 6 }" :read-only="false"
            :disabled="false" :submitBtnDisabled="isLoading" :loading="isLoading" :clearable="true" :allowSpeech="true"
            submitType="enter" :headerAnimationTimer="300" inputWidth="100%" variant="default" :showUpdown="true"
            :inputStyle="{}" :triggerStrings="['/help', '/clear']" :triggerPopoverVisible="false"
            triggerPopoverWidth="fit-content" triggerPopoverLeft="0px" :triggerPopoverOffset="8"
            triggerPopoverPlacement="top-start" @submit="handleSubmit" @cancel="handleCancel"
            @recordingChange="handleRecordingChange" @trigger="handleTrigger" />
        </footer>
      </div>
    </template>
    ```

重启应用并刷新前端页面，现在您的 AI-Copilot 已经拥有了基于数据库的持久化“记忆”！

### **3.3 管理长期记忆：实现会话历史服务**

`ChatMemory` 解决了 AI 的上下文问题，但作为应用开发者，我们还需要为用户提供查看、管理他们完整对话记录（Chat History）的功能。这需要我们直接操作 `spring_ai_chat_memory` 这张表。

#### **3.3.1 后端服务搭建：基于 MyBatis-Plus**

我们将按照标准三层架构，构建一套完整的、面向业务的会话历史管理服务。

**1. 依赖与配置**

  * **`pom.xml`**: 添加 MyBatis-Plus 依赖。
    
    ```xml
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        <version>3.5.7</version> 
    </dependency>
    ```
  * **主启动类**: 添加 `@MapperScan` 注解。
    
    ```java
    @SpringBootApplication
    @MapperScan("com.copilot.aicopilotbackend.repository") // 指向 Mapper 接口包
    public class AiCopilotBackendApplication { /* ... */ }
    ```
  * **`application.yml`**: 添加 MyBatis-Plus 配置。
    ```yaml
    mybatis-plus:
      configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    ```

**2. 数据访问层 (DAO)**

  * **`entity/ChatMemoryEntity.java`**: 创建用于 MyBatis-Plus 操作的实体类。

    ```java
    package com.copilot.aicopilotbackend.entity;

    import com.baomidou.mybatisplus.annotation.*;
    import lombok.Data;
    import java.time.LocalDateTime;

    @Data
    @TableName("spring_ai_chat_memory")
    public class ChatMemoryEntity {
        @TableId(value = "id", type = IdType.AUTO)
        private Long id;
        @TableField("conversation_id")
        private String conversationId;
        @TableField("content")
        private String content;
        @TableField("type")
        private String type;
        @TableField("timestamp")
        private LocalDateTime timestamp;
    }
    ```

  * **`repository/ChatMemoryMapper.java`**: 创建继承 `BaseMapper` 的 Mapper 接口。

    ```java
    package com.copilot.aicopilotbackend.repository;

    import com.baomidou.mybatisplus.core.mapper.BaseMapper;
    import com.copilot.aicopilotbackend.entity.ChatMemoryEntity;
    import org.apache.ibatis.annotations.Mapper;

    @Mapper
    public interface ChatMemoryMapper extends BaseMapper<ChatMemoryEntity> {
    }
    ```

**3. 业务逻辑层 (Service)**

  * **`service/IChatHistoryService.java`**: 定义服务接口。

    ```java
    package com.copilot.aicopilotbackend.service;

    import com.baomidou.mybatisplus.extension.service.IService;
    import com.copilot.aicopilotbackend.entity.ChatMemoryEntity;
    import java.util.List;
    import java.util.Map;

    public interface IChatHistoryService extends IService<ChatMemoryEntity> {
        List<Map<String, Object>> getAllConversations();
        List<ChatMemoryEntity> getChatHistory(String conversationId);
        boolean deleteChatHistory(String conversationId);
    }
    ```

  * **`service/impl/ChatHistoryServiceImpl.java`**: 实现服务接口。

    ```java
    package com.copilot.aicopilotbackend.service.impl;

    import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
    import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    import com.copilot.aicopilotbackend.entity.ChatMemoryEntity;
    import com.copilot.aicopilotbackend.repository.ChatMemoryMapper;
    import com.copilot.aicopilotbackend.service.IChatHistoryService;
    import org.springframework.stereotype.Service;
    import java.time.LocalDateTime;
    import java.util.HashMap;
    import java.util.List;
    import java.util.Map;
    import java.util.stream.Collectors;

    @Service
    public class ChatHistoryServiceImpl extends ServiceImpl<ChatMemoryMapper, ChatMemoryEntity> implements IChatHistoryService {

        @Override
        public List<Map<String, Object>> getAllConversations() {
            QueryWrapper<ChatMemoryEntity> idQueryWrapper = new QueryWrapper<>();
            idQueryWrapper.select("conversation_id", "timestamp").orderByDesc("timestamp");
            List<ChatMemoryEntity> allRecords = this.list(idQueryWrapper);

            List<String> distinctConvIds = allRecords.stream()
                    .map(ChatMemoryEntity::getConversationId)
                    .distinct()
                    .collect(Collectors.toList());

            return distinctConvIds.stream().map(id -> {
                Map<String, Object> conversationInfo = new HashMap<>();
                conversationInfo.put("id", id);

                ChatMemoryEntity firstUserMessage = this.query()
                        .eq("conversation_id", id).eq("type", "USER")
                        .orderByAsc("timestamp").last("LIMIT 1").one();
                String title = (firstUserMessage != null && firstUserMessage.getContent() != null)
                        ? firstUserMessage.getContent().trim() : "新的会话";
                conversationInfo.put("label", title.length() > 20 ? title.substring(0, 20) + "..." : title);

                ChatMemoryEntity lastMessage = this.query()
                        .eq("conversation_id", id).orderByDesc("timestamp")
                        .last("LIMIT 1").one();
                conversationInfo.put("updatedAt", lastMessage != null ? lastMessage.getTimestamp() : LocalDateTime.now());

                return conversationInfo;
            }).collect(Collectors.toList());
        }

        @Override
        public List<ChatMemoryEntity> getChatHistory(String conversationId) {
            QueryWrapper<ChatMemoryEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("conversation_id", conversationId).orderByAsc("timestamp");
            return this.list(queryWrapper);
        }

        @Override
        public boolean deleteChatHistory(String conversationId) {
            QueryWrapper<ChatMemoryEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("conversation_id", conversationId);
            return this.remove(queryWrapper);
        }
    }
    ```

**4. API 接口层 (Controller)**

  * **`controller/ChatHistoryController.java`**: 创建新的 Controller，暴露三个 API 端点。
    
    ```java
package com.copilot.aicopilotbackend.controller;
    
    import com.copilot.aicopilotbackend.dto.response.ApiResponse;
    import com.copilot.aicopilotbackend.entity.ChatMemoryEntity;
    import com.copilot.aicopilotbackend.service.IChatHistoryService;
    import lombok.RequiredArgsConstructor;
    import org.springframework.web.bind.annotation.*;
    import java.util.List;
import java.util.Map;
    
    @RestController
    @RequestMapping("/api/v1/chat-history")
    @RequiredArgsConstructor
public class ChatHistoryController {
    
    private final IChatHistoryService chatHistoryService;
    
        @GetMapping("/conversations")
        public ApiResponse<List<Map<String, Object>>> getAllConversations() {
            return ApiResponse.success(chatHistoryService.getAllConversations());
    }
    
        @GetMapping("/conversation/{conversationId}")
        public ApiResponse<List<ChatMemoryEntity>> getChatHistory(@PathVariable String conversationId) {
            return ApiResponse.success(chatHistoryService.getChatHistory(conversationId));
    }
    
        @DeleteMapping("/conversation/{conversationId}")
        public ApiResponse<Map<String, Object>> deleteChatHistory(@PathVariable String conversationId) {
            boolean success = chatHistoryService.deleteChatHistory(conversationId);
            Map<String, Object> result = Map.of(
                "success", success,
                "message", success ? "删除成功" : "删除失败或会话不存在"
            );
            return ApiResponse.success(result);
        }
    }
    ```

#### **3.3.2 API 接口文档**

以下是为“会话历史管理”功能提供的后端RESTful API接口文档。

**基础URL**: `http://localhost:8080/api/v1`

-----

**1. 获取所有会话列表**

获取所有已存在的会话的概要信息列表，按最后更新时间倒序排列。

  * **请求**: `GET /chat-history/conversations`
  * **请求参数**: 无
  * **成功响应 (`200 OK`)**:
    ```json
    {
      "code": "00000",
      "message": "操作成功",
      "data": [
        {
          "id": "conv_1718985665_a1b2c3d4",
          "label": "你好，介绍一下你自己",
          "updatedAt": "2025-06-22T01:21:05.123456"
        },
        {
          "id": "conv_1718985601_e5f6g7h8",
          "label": "请用Java写一个快排...",
          "updatedAt": "2025-06-22T01:20:01.789012"
        }
      ],
      "timestamp": "2025-06-27T10:00:00.000000"
    }
    ```
  * **响应数据字段说明**:

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `String` | 会话的唯一标识符。 |
| `label`| `String` | 根据会话第一条用户消息生成的默认标题（最长20个字符）。 |
| `updatedAt`|`String` | 会话的最后更新时间（ISO 8601格式）。 |

-----

**2. 获取指定会话详情**

根据提供的`conversationId`，获取该会话的完整聊天记录，按消息时间升序排列。

  * **请求**: `GET /chat-history/conversation/{conversationId}`
  * **请求参数**:
      * **路径参数**:
        | 参数 | 类型 | 状态 | 描述 |
        | :--- | :--- | :--- | :--- |
        | `conversationId` | `String` | **必需** | 要查询的会话ID。 |
  * **成功响应 (`200 OK`)**:
    ```json
    {
      "code": "00000",
      "message": "操作成功",
      "data": [
        {
          "id": 101,
          "conversationId": "conv_1718985665_a1b2c3d4",
          "content": "你好，介绍一下你自己",
          "type": "USER",
          "timestamp": "2025-06-22T01:21:00.123456"
        },
        {
          "id": 102,
          "conversationId": "conv_1718985665_a1b2c3d4",
          "content": "你好！我是一个由Spring AI驱动的大语言模型...",
          "type": "ASSISTANT",
          "timestamp": "2025-06-22T01:21:05.123456"
        }
      ],
      "timestamp": "2025-06-27T10:01:00.000000"
    }
    ```
  * **响应数据字段说明**:

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `Long` | 消息的数据库主键ID。 |
| `conversationId`| `String` | 所属会话的ID。 |
| `content` | `String` | 消息的文本内容。 |
| `type` | `String` | 消息角色，值为 `USER` 或 `ASSISTANT`。 |
| `timestamp` | `String` | 消息的创建时间戳（ISO 8601格式）。 |

-----

**3. 删除指定会话**

根据提供的`conversationId`，删除该会话的所有相关聊天记录。

  * **请求**: `DELETE /chat-history/conversation/{conversationId}`
  * **请求参数**:
    
      * **路径参数**:
        | 参数 | 类型 | 状态 | 描述 |
        | :--- | :--- | :--- | :--- |
        | `conversationId` | `String` | **必需** | 要删除的会话ID。 |
  * **成功响应 (`200 OK`)**:
    ```json
    {
      "code": "00000",
      "message": "操作成功",
      "data": {
          "success": true,
          "message": "删除成功"
      },
      "timestamp": "2025-06-27T10:02:00.000000"
    }
    ```
  * **响应数据字段说明**:

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `success`| `Boolean` | 操作是否成功。`true`表示成功，`false`表示失败。 |
| `message`| `String` | 操作结果的文本描述信息。 |


-----

