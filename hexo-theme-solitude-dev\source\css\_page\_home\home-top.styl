#home_top
  .recent-top-post-group
    border-radius 12px
    overflow hidden
    width 100%
    margin-bottom 0
    user-select none

    +maxWidth768()
      border-radius 0

    .recent-post-top
      display flex
      flex-direction row
      flex-wrap nowrap
      width 100%
      gap .5rem

      +maxWidth1200()
        flex-direction column

      &::-webkit-scrollbar
        display none

      .top-post-group
        display flex
        flex-direction row
        justify-content start
        flex-wrap wrap
        align-content space-between
        width 100%
        gap .5rem

        +maxWidth1200()
          flex-wrap nowrap

      / #bannerGroup
        display flex
        +minWidth1201()
          flex 1 1 0
          height calc(328px + .5rem)
          display flex
          flex-direction column
          justify-content space-between

        .tags-group-wrapper
          margin-top 0
          display flex
          flex-wrap wrap
          animation rowup 60s linear infinite
          width 300px
          margin-left auto

        .tags-group-icon-pair
          display flex

          .tags-group-icon:nth-child(even)
            margin-left 4rem

  /.topGroup
    display flex
    gap .5rem
    +maxWidth1200()
      overflow-x auto

      &::-webkit-scrollbar
        display none

    +minWidth1201()
      display flex
      flex-direction row
      flex-wrap wrap
      flex 1 1 0
      justify-content flex-start
      height calc(328px + 0.5rem)
      align-content space-between
      position relative
      animation slide-in .6s .1s backwards

    .top-post-item
      display flex
      flex-direction column
      flex 1 1 30%
      align-items flex-start
      background var(--efu-card-bg)
      border-radius 12px
      overflow hidden
      height 164px
      max-height 164px
      border var(--style-border-always)
      transition .3s
      position relative
      box-shadow var(--efu-shadow-border)

      +maxWidth1200()
        width 200px
        min-width 200px

      &:hover .post_cover a .top-post-top-text
        left 0

      .post_cover
        width 100%

        a
          height 100px
          overflow hidden
          display flex

          img
            object-fit cover
            width 100%
            background var(--efu-secondbg)

          .top-post-top-text
            position absolute
            top 0
            left -50px
            display flex
            z-index 1
            background var(--efu-theme)
            color var(--efu-white)
            padding 2px 8px
            font-size .6rem
            border-radius 0 0 12px 0
            transition .3s
            cursor pointer
            if $language == 'en-US'
              left -90px

      /.top-post-info
        padding .3rem .5rem .3rem .5rem
        transition .3s

        .article-title
          -webkit-line-clamp 2
          overflow hidden
          display -webkit-box
          -webkit-box-orient vertical
          line-height 1.5
          justify-content center
          align-items flex-end
          align-content center
          font-weight 700
          font-size .8rem
          padding 0

          &:hover
            color var(--efu-fontcolor)

    .todayCard
      display none

      +minWidth1201()
        position absolute
        width 100%
        z-index 3
        height 100%
        top 0
        right 0
        background var(--efu-card-bg)
        border-radius 12px
        overflow hidden
        transition .3s
        display flex
        cursor pointer
        pointer-events all

        &::after
          position: absolute
          content: ''
          width: 100%
          height: 100%
          top: 0
          left: 0
          box-shadow 0 -109px 133px -9px $todayCardColor inset

        &.hide
          opacity: 0
          pointer-events: none

        .todayCard-info
          position: absolute
          bottom: 2rem
          left: 2rem
          z-index: 2
          color: var(--efu-white)
          max-width: 60%
          transition: .3s

          .todayCard-tips
            opacity: .8
            font-size: .6rem

          .todayCard-title
            font-size: 28px
            font-weight: 700
            line-height: 36px

        .todayCard-cover
          position: absolute
          min-width: 100%
          min-height: 100%
          top: 0
          left: 0
          background-size: cover
          z-index: -1
          transition: .3s

        .banner-button-group
          position: absolute
          right: 2rem
          bottom: 2rem
          display: flex
          transition: .3s

        .banner-button
          background: var(--efu-white-op)
          border-radius: 20px
          color: var(--efu-white)
          display: flex
          align-items: center
          z-index: 1
          transition: .3s
          cursor: pointer
          backdrop-filter: saturate(180%) blur(20px)
          transform: translateZ(0)
          height: 40px
          width: 118px
          justify-content: center

          i
            margin-right: 8px
            font-size: 22px

          &:hover
            background: var(--efu-theme)
            color: var(--efu-white)

        &.hide
          .todayCard-info
            bottom: 1rem
            opacity: 0

          .todayCard-cover
            transform: scale(1.2)

          .banner-button-group
            bottom: 1rem

.banners-links
  display flex
  position absolute
  bottom 20px
  left 40px
  flex-direction column
  justify-content space-between
  flex-wrap wrap
  height 120px

.banners-link-btn
  display flex
  flex-direction row
  align-items center
  padding 8px 18px 8px 14px
  border-radius 60px
  margin-bottom 12px
  color var(--efu-white)
  position relative
  background-size 200%
  margin-right 10px

  &:hover
    background-position 100% 0

.banners-link-title
  margin 0 .1rem 0 .5rem
