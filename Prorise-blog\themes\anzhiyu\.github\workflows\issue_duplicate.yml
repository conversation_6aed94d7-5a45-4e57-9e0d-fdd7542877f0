name: Issue Duplicate

on:
  issues:
    types: [labeled]

jobs:
  create-comment:
    runs-on: ubuntu-latest
    if: github.event.label.name == 'duplicate'
    steps:
      - name: Create comment
        uses: actions-cool/issues-helper@v2
        with:
          actions: "create-comment"
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            Hello @${{ github.event.issue.user.login }}, your issue is duplicated and will be closed.
            你好 @${{ github.event.issue.user.login }}，你的issue是重复的，将被关闭。
      - name: Close issue
        uses: actions-cool/issues-helper@v2
        with:
          actions: "close-issue"
          token: ${{ secrets.GITHUB_TOKEN }}
