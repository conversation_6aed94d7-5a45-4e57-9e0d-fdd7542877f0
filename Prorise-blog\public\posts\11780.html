<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理入门（八）：第八章：内容产品自媒体端设计 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理入门（八）：第八章：内容产品自媒体端设计"><meta name="application-name" content="产品经理入门（八）：第八章：内容产品自媒体端设计"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理入门（八）：第八章：内容产品自媒体端设计"><meta property="og:url" content="https://prorise666.site/posts/11780.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第八章：内容产品自媒体端设计在第七章，我们为“普通用户”设计了一套完整、流畅的消费体验。但是，一个内容平台的繁荣，离不开持续产出优质内容的创作者，也就是“自媒体”。 在这一章，我们将为这些创作者，设计一套专属的“创作工坊”——自媒体端。这是一个能让他们方便地发布内容、管理作品、与粉丝互动、并洞察数据"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta name="description" content="第八章：内容产品自媒体端设计在第七章，我们为“普通用户”设计了一套完整、流畅的消费体验。但是，一个内容平台的繁荣，离不开持续产出优质内容的创作者，也就是“自媒体”。 在这一章，我们将为这些创作者，设计一套专属的“创作工坊”——自媒体端。这是一个能让他们方便地发布内容、管理作品、与粉丝互动、并洞察数据"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/11780.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理入门（八）：第八章：内容产品自媒体端设计",postAI:"true",pageFillDescription:"第八章：内容产品自媒体端设计, 8.1 自媒体端设计思路, 8.1.1 学习目标, 8.1.2 自媒体端的核心价值, 8.1.3 设计原则与要点, 8.1.4 本节小结, 8.2 入驻与登录, 8.2.1 学习目标, 8.2.2 创作者入驻流程设计, 8.2.3 登录与身份切换, 8.2.4 本节小结, 8.3 内容管理, 8.3.1 学习目标, 8.3.2 内容列表 amp 数据概览, 1. 核心数据指标, 2. 列表设计要点, 8.3.3 内容发布与编辑, 8.3.4 内容状态管理, 8.3.5 本节小结, 8.4 评论管理, 8.4.1 学习目标, 8.4.2 评论列表设计, 8.4.3 评论处理功能（回复x2F置顶x2F删除）, 8.4.4 本节小结, 8.5 粉丝管理, 8.5.1 学习目标, 8.5.2 粉丝列表, 8.5.3 粉丝概况, 1. 核心数据指标, 2. 数据可视化设计, 8.5.4 粉丝画像, 8.5.5 本节小结, 8.6 本章总结, 8.6.1 课程内容回顾第八章内容产品自媒体端设计在第七章我们为普通用户设计了一套完整流畅的消费体验但是一个内容平台的繁荣离不开持续产出优质内容的创作者也就是自媒体在这一章我们将为这些创作者设计一套专属的创作工坊自媒体端这是一个能让他们方便地发布内容管理作品与粉丝互动并洞察数据最终帮助他们在我们平台获得成功的后台系统自媒体端设计思路在动手设计之前我们同样需要先建立一套清晰的设计思路我们的出发点依然是产品的顶层战略我们的生产模式是这就决定了我们必须服务好自媒体这个核心角色学习目标在本节中我的目标是带大家一起完成自媒体端设计的顶层战略思考我们将从创作者最原始的需求出发一步步深挖提炼出我们自媒体端产品的核心价值并最终推导出我们后台需要设计的核心功能模块自媒体端的核心价值我们首先来分析创作者的核心需求最原始的需求作为内容的提供者创作者最基本最直接的需求就是需要有一个渠道能够编辑发布和管理自己的内容但是我经常会反问自己和团队一个问题只提供一个发布和管理工具就足够了吗一个创作者他不仅仅是一个发布者他更像是在我们平台上经营着自己小事业的创业者当他的内容被用户消费订阅评论后他的内心一定会产生更深层次的渴望来自用户订阅的渴望有多少人关注了我他们都是谁这背后是对粉丝增长和社群归属的渴望来自用户浏览的渴望我的内容受欢迎吗哪篇文章的数据最好这背后是对内容表现和创作反馈的渴望来自用户评论的渴望我的读者们都在讨论什么我如何与他们互动这背后是对粉丝互动和舆论管理的渴望因此我为我们的自媒体端定义了它的核心价值它绝不仅仅是一个内容发布工具而是一个创作者成功平台我们的使命是为创作者提供一整套服务不仅帮助他们创作更要帮助他们洞察数据连接粉丝获得成长设计原则与要点基于上述的核心价值我确立了自媒体端的设计原则并推导出了它必须具备的三大核心功能模块设计原则赋能创作提供高效易用的内容发布与管理工具数据驱动提供清晰直观的数据反馈帮助创作者优化内容连接粉丝提供便捷的粉丝互动与管理工具帮助创作者建立社群核心功能模块入驻登录这是创作者进入我们平台的大门内容管理这是创作者的创作车间负责内容的生产编辑和数据监控粉丝管理这是创作者的社群负责粉丝的互动和分析这三大模块就构成了我们自媒体端产品的骨架在接下来的小节中我们将逐一进行详细的设计本节小结思考层次核心洞察最终产出基础需求创作者需要发布和管理内容核心功能内容管理深层需求创作者需要获得数据反馈与粉丝互动并实现个人成长核心功能粉丝管理数据分析核心价值我们要做的不是一个工具而是一个创作者成功平台设计原则赋能创作数据驱动连接粉丝入驻与登录这是我们为创作者开启的梦想之门与普通用户追求快速无感的登录不同创作者的入驻更像是一次签约合作因此我设计的流程不仅要考虑便捷更要体现出专业性仪式感和契约精神学习目标在本节中我的目标是带大家设计一个完整专业的创作者入驻流程我们将学习如何区分普通用户注册与创作者入驻并设计出包含资质审核协议签署等关键环节的新用户引导流程流程创作者入驻流程设计图中的流程登录注册实名认证是普通用户升级为创作者的一个高度简化版在我的实际设计中我会将这个流程细化为一套更完整的入驻流程它通常发生在用户已经拥有一个普通账户之后我设计的专业入驻流程如下基础账户注册登录用户首先需要有一个我们平台的普通账户通过手机号验证码等方式发起入驻申请在的某个位置比如个人中心我会放置一个醒目的成为创作者或创作者中心的入口用户点击后才正式开始入驻流程提交资质信息这是最关键的一步是平台对创作者质量进行初步筛选的环节我会要求用户提交实名认证这是政策要求也是建立信任的基础创作者基本信息设置公开的创作者昵称头像创作领域选择让创作者选择自己擅长的内容领域如科技美食旅行这便于我们后续的内容分发可选辅助材料对于要求较高的平台我还会增加提交其他平台代表作链接的步骤用于评估创作者的实力平台协议签署用户必须阅读并同意我们的内容创作协议和平台规范等等待平台审核提交申请后用户的状态会变为审核中我们会通过人工或对其资质进行审核入驻成功审核通过后用户才正式获得创作者身份可以开始使用自媒体端的各项功能登录与身份切换当一个用户同时拥有普通用户和创作者双重身份后他的登录体验依然是统一的使用同一个手机号或微信登录我需要设计的是登录后的身份切换机制通常我会在个人中心页面提供一个清晰的入口比如创作者中心已获得创作者身份的用户点击后即可进入我们接下来要设计的自媒体后台本节小结设计环节我的核心设计思考创作者入驻不能等同于普通注册它是一个包含资质审核和协议签署的更正式的流程目的是筛选高质量创作者并建立契约关系登录与切换登录使用统一账户但在产品内必须为创作者提供一个清晰便捷的入口以切换到自媒体后台内容管理这是自媒体端后台的心脏是创作者最核心的创作车间我设计的首要目标是让它功能强大同时体验简洁高效学习目标在本节中我的目标是带大家设计一个功能完善的内容管理模块我们将学习如何设计内容的数据概览和列表页以及如何设计一个体验良好的内容发布与编辑器内容列表数据概览当创作者进入后台他们第一眼最想看到的一定是自己作品的表现因此内容管理模块的首页我通常会设计成一个集数据概览和内容列表于一体的核心数据指标在页面的最上方我会放置一个数据概览模块实时展示创作者最关心的核心数据指标并支持按时间筛选今日本周本月等这些指标通常包括浏览数内容被用户看到的次数评论数用户对内容的互动情况点赞数用户对内容的认可度分享数内容被传播的情况列表设计要点在数据概览下方是内容列表我会用表格的形式展示创作者发布的所有内容为了方便管理这个列表必须具备关键信息展示清晰地展示文章标题封面图所属分类发表时间等筛选与搜索功能提供按标题关键词搜索按分类筛选按日期筛选的功能快捷操作入口每一行内容后面都需要有编辑删除查看下线等快捷操作按钮分页功能当内容数量过多时必须提供分页内容发布与编辑点击发布文章或编辑按钮就会进入我们的内容编辑器这是创作者挥洒才华的地方体验必须流畅我的设计会包含基础信息区清晰的文章标题作者署名等输入框富文本编辑器一个所见即所得的编辑器至少要支持加粗列表插入图片等基础的图文混排功能分类与标签提供文章分类的选择功能并允许作者为文章打上标签这既方便用户理解也便于我们的算法进行分发操作区提供预览存草稿和发布等核心操作按钮内容状态管理在内容列表中我还需要一个状态字段来清晰地标识每一篇内容的生命周期至少应包含以下几种状态草稿已保存但还未提交发布审核中已提交正在等待平台审核已发布审核通过所有用户可见审核驳回审核不通过作者需要修改后重新提交已下线由作者本人或平台主动下架用户不可见本节小结模块我的核心设计思考数据概览让数据说话第一时间向创作者展示最核心的内容表现数据给予他们最直接的反馈和激励内容列表高效管理提供强大的筛选搜索和批量操作功能帮助高产的作者轻松管理自己的百宝箱内容发布沉浸创作提供一个稳定易用的编辑器让作者可以专注于创作本身不受工具的干扰评论管理在我看来评论区是创作者与粉丝之间最重要的连接器一个活跃健康的评论区是内容生命力的延续因此为创作者提供一套高效便捷的评论管理工具是自媒体后台设计的重中之重我的设计主要围绕过滤查看回复这三个核心动作展开学习目标在本节中我的目标是带大家设计一个功能完善的评论管理系统我们将学习如何设计一个两层结构的评论列表并为创作者提供必要的评论处理功能评论列表设计为了让创作者能高效地管理海量评论我通常会设计一个两级结构的列表第一级文章评论概览后台的入口首先是一个以文章为维度的评论列表这张列表清晰地展示了内容标题是哪篇文章收到了评论评论总数和待回复评论数让创作者快速了解整体情况和待办事项操作提供一个查看评论的入口点击后进入第二级列表第二级单篇评论详情点击查看评论后创作者会进入针对某一篇文章的详细评论列表在这里可以清晰地看到每一条评论的评论内容用户昵称用户头像等同时我还会提供按用户昵称或评论内容进行筛选和搜索的功能评论处理功能回复置顶删除在单篇评论详情列表的每一条评论后面我必须为创作者提供一组管理工具回复这是最重要的功能是创作者与粉丝直接对话的桥梁删除赋予创作者管理自己评论区环境的权力可以删除不友善或垃圾评论可选置顶这是一个非常好的精细化运营功能我可以通过置顶优质评论来引导整个评论区的讨论氛围本节小结一个好的评论管理系统能让创作者感受到自己对社群的掌控感并激励他们更积极地与粉丝互动从而形成一个正向的社区循环粉丝管理如果说内容是创作者的作品那么粉丝就是创作者最宝贵的资产一个优秀的自媒体后台必须为创作者提供一套轻量级的客户关系管理系统帮助他们了解自己的粉丝从而创作出更受欢迎的内容学习目标在本节中我的目标是带大家设计一个包含三视图列表概况画像的粉丝管理模块为创作者提供从宏观到微观的粉丝洞察能力粉丝列表这是粉丝管理的微观视图它是一个完整的可搜索可筛选的粉丝名录核心功能我会以列表的形式展示每一位粉丝的昵称头像性别地区等基础信息互动设计在每一位粉丝后面我会提供私信或关注回关等操作按钮为创作者提供主动触达粉丝的渠道粉丝概况这是粉丝管理的宏观数据视图它告诉创作者我的粉丝群体整体发展趋势如何核心数据指标在页面的最顶部我会用数据卡片的形式展示创作者最关心的几个核心比如粉丝累计总数昨日新增粉丝昨日取关数数据可视化设计为了让趋势更直观我会用折线图的形式来展示粉丝总数和每日净增的变化趋势在图表下方再附上详细的每日数据表格供创作者进行深度分析粉丝画像这是粉丝管理的宏观特征视图它回答了创作者最关心的问题我的粉丝到底是一群什么样的人我通过数据可视化的方式对创作者的所有粉丝进行群体的匿名的特征分析通常包括性别分布男女比例图年龄分布年龄段柱状图地域分布地图热力或省份排行这些画像信息对于创作者判断自己未来的内容方向具有极高的战略价值本节小结模块核心视图我的设计目标粉丝列表微观视图让创作者能看到每一个具体的粉丝并提供互动渠道粉丝概况宏观数据视图让创作者能看到粉丝总量的增长趋势和每日变化粉丝画像宏观特征视图让创作者能了解自己粉丝群体的人口统计学特征本章总结课程内容回顾在本章我们完整地设计了内容产品生态的另一端自媒体端我们首先确立了设计思路明确了我们的目标是打造一个创作者成功平台我们设计了专业的入驻登录流程为平台筛选优质创作者我们设计了核心的内容管理模块为创作者提供了集发布管理数据分析于一体的创作车间我们设计了评论管理功能赋予创作者与粉丝互动管理社区的能力最后我们设计了包含三视图的粉丝管理系统为创作者提供了宝贵的粉丝洞察",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-21 14:51:58",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%85%AB%E7%AB%A0%EF%BC%9A%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E8%87%AA%E5%AA%92%E4%BD%93%E7%AB%AF%E8%AE%BE%E8%AE%A1"><span class="toc-text">第八章：内容产品自媒体端设计</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#8-1-%E8%87%AA%E5%AA%92%E4%BD%93%E7%AB%AF%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-text">8.1 自媒体端设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">8.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-2-%E8%87%AA%E5%AA%92%E4%BD%93%E7%AB%AF%E7%9A%84%E6%A0%B8%E5%BF%83%E4%BB%B7%E5%80%BC"><span class="toc-text">8.1.2 自媒体端的核心价值</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-3-%E8%AE%BE%E8%AE%A1%E5%8E%9F%E5%88%99%E4%B8%8E%E8%A6%81%E7%82%B9"><span class="toc-text">8.1.3 设计原则与要点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-4-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">8.1.4 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-2-%E5%85%A5%E9%A9%BB%E4%B8%8E%E7%99%BB%E5%BD%95"><span class="toc-text">8.2 入驻与登录</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">8.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-2-%E5%88%9B%E4%BD%9C%E8%80%85%E5%85%A5%E9%A9%BB%E6%B5%81%E7%A8%8B%E8%AE%BE%E8%AE%A1"><span class="toc-text">8.2.2 创作者入驻流程设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-3-%E7%99%BB%E5%BD%95%E4%B8%8E%E8%BA%AB%E4%BB%BD%E5%88%87%E6%8D%A2"><span class="toc-text">8.2.3 登录与身份切换</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-4-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">8.2.4 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-3-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86"><span class="toc-text">8.3 内容管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">8.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-2-%E5%86%85%E5%AE%B9%E5%88%97%E8%A1%A8-%E6%95%B0%E6%8D%AE%E6%A6%82%E8%A7%88"><span class="toc-text">8.3.2 内容列表 &amp; 数据概览</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E6%95%B0%E6%8D%AE%E6%8C%87%E6%A0%87"><span class="toc-text">1. 核心数据指标</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%88%97%E8%A1%A8%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">2. 列表设计要点</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-3-%E5%86%85%E5%AE%B9%E5%8F%91%E5%B8%83%E4%B8%8E%E7%BC%96%E8%BE%91"><span class="toc-text">8.3.3 内容发布与编辑</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-4-%E5%86%85%E5%AE%B9%E7%8A%B6%E6%80%81%E7%AE%A1%E7%90%86"><span class="toc-text">8.3.4 内容状态管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">8.3.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-4-%E8%AF%84%E8%AE%BA%E7%AE%A1%E7%90%86"><span class="toc-text">8.4 评论管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">8.4.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-2-%E8%AF%84%E8%AE%BA%E5%88%97%E8%A1%A8%E8%AE%BE%E8%AE%A1"><span class="toc-text">8.4.2 评论列表设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-3-%E8%AF%84%E8%AE%BA%E5%A4%84%E7%90%86%E5%8A%9F%E8%83%BD%EF%BC%88%E5%9B%9E%E5%A4%8D-%E7%BD%AE%E9%A1%B6-%E5%88%A0%E9%99%A4%EF%BC%89"><span class="toc-text">8.4.3 评论处理功能（回复/置顶/删除）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-4-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">8.4.4 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-5-%E7%B2%89%E4%B8%9D%E7%AE%A1%E7%90%86"><span class="toc-text">8.5 粉丝管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">8.5.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-2-%E7%B2%89%E4%B8%9D%E5%88%97%E8%A1%A8"><span class="toc-text">8.5.2 粉丝列表</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-3-%E7%B2%89%E4%B8%9D%E6%A6%82%E5%86%B5"><span class="toc-text">8.5.3 粉丝概况</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E6%95%B0%E6%8D%AE%E6%8C%87%E6%A0%87-1"><span class="toc-text">1. 核心数据指标</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%95%B0%E6%8D%AE%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%BE%E8%AE%A1"><span class="toc-text">2. 数据可视化设计</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-4-%E7%B2%89%E4%B8%9D%E7%94%BB%E5%83%8F"><span class="toc-text">8.5.4 粉丝画像</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">8.5.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-6-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">8.6 本章总结</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-6-1-%E8%AF%BE%E7%A8%8B%E5%86%85%E5%AE%B9%E5%9B%9E%E9%A1%BE"><span class="toc-text">8.6.1 课程内容回顾</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理入门（八）：第八章：内容产品自媒体端设计</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-20T15:13:45.000Z" title="发表于 2025-07-20 23:13:45">2025-07-20</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-21T06:51:58.992Z" title="更新于 2025-07-21 14:51:58">2025-07-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">4.4k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>12分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理入门（八）：第八章：内容产品自媒体端设计"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/11780.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/11780.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理入门（八）：第八章：内容产品自媒体端设计</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-20T15:13:45.000Z" title="发表于 2025-07-20 23:13:45">2025-07-20</time><time itemprop="dateCreated datePublished" datetime="2025-07-21T06:51:58.992Z" title="更新于 2025-07-21 14:51:58">2025-07-21</time></header><div id="postchat_postcontent"><h1 id="第八章：内容产品自媒体端设计"><a href="#第八章：内容产品自媒体端设计" class="headerlink" title="第八章：内容产品自媒体端设计"></a>第八章：内容产品自媒体端设计</h1><p>在第七章，我们为“普通用户”设计了一套完整、流畅的消费体验。但是，一个内容平台的繁荣，离不开持续产出优质内容的创作者，也就是“<strong>自媒体</strong>”。</p><p>在这一章，我们将为这些创作者，设计一套专属的“创作工坊”——<strong>自媒体端</strong>。这是一个能让他们方便地发布内容、管理作品、与粉丝互动、并洞察数据，最终帮助他们在我们平台获得成功的后台系统。</p><h2 id="8-1-自媒体端设计思路"><a href="#8-1-自媒体端设计思路" class="headerlink" title="8.1 自媒体端设计思路"></a>8.1 自媒体端设计思路</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103341813.png" alt="image-20250721103341813"></p><p>在动手设计之前，我们同样需要先建立一套清晰的设计思路。我们的出发点，依然是产品的顶层战略。我们V1.0的“<strong>生产模式</strong>”是PGC+UGC，这就决定了我们必须服务好“<strong>自媒体</strong>”这个核心角色。</p><h3 id="8-1-1-学习目标"><a href="#8-1-1-学习目标" class="headerlink" title="8.1.1 学习目标"></a>8.1.1 学习目标</h3><p>在本节中，我的目标是带大家一起，完成自媒体端设计的顶层战略思考。我们将从创作者最原始的需求出发，一步步深挖，提炼出我们自媒体端产品的核心价值，并最终推导出我们后台需要设计的核心功能模块。</p><h3 id="8-1-2-自媒体端的核心价值"><a href="#8-1-2-自媒体端的核心价值" class="headerlink" title="8.1.2 自媒体端的核心价值"></a>8.1.2 自媒体端的核心价值</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103428666.png" alt="image-20250721103428666"></p><p>我们首先来分析创作者的核心需求。</p><ul><li><strong>最原始的需求</strong>：作为内容的提供者，创作者最基本、最直接的需求，就是<strong>需要有一个渠道，能够编辑、发布和管理自己的内容</strong>。</li></ul><p>但是，我经常会反问自己和团队一个问题：<strong>只提供一个发布和管理工具，就足够了吗？</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103507471.png" alt="image-20250721103507471"></p><p>一个创作者，他不仅仅是一个“发布者”，他更像是在我们平台上经营着自己“小事业”的创业者。当他的内容被用户消费、订阅、评论后，他的内心一定会产生更深层次的渴望：</p><ul><li><strong>来自“用户订阅”的渴望</strong>：“有多少人关注了我？他们都是谁？” → 这背后是对<strong>粉丝增长</strong>和<strong>社群归属</strong>的渴望。</li><li><strong>来自“用户浏览”的渴望</strong>：“我的内容受欢迎吗？哪篇文章的数据最好？” → 这背后是对<strong>内容表现</strong>和<strong>创作反馈</strong>的渴望。</li><li><strong>来自“用户评论”的渴望</strong>：“我的读者们都在讨论什么？我如何与他们互动？” → 这背后是对<strong>粉丝互动</strong>和<strong>舆论管理</strong>的渴望。</li></ul><p>因此，我为我们的自媒体端，定义了它的核心价值：它绝不仅仅是一个“<strong>内容发布工具</strong>”，而是一个“<strong>创作者成功平台</strong>”。我们的使命，是为创作者提供一整套服务，不仅帮助他们创作，更要帮助他们<strong>洞察数据、连接粉丝、获得成长</strong>。</p><h3 id="8-1-3-设计原则与要点"><a href="#8-1-3-设计原则与要点" class="headerlink" title="8.1.3 设计原则与要点"></a>8.1.3 设计原则与要点</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103535155.png" alt="image-20250721103535155"></p><p>基于上述的核心价值，我确立了自媒体端的设计原则，并推导出了它必须具备的三大核心功能模块。</p><ul><li><p><strong>设计原则</strong>：</p><ol><li><strong>赋能创作</strong>：提供高效、易用的内容发布与管理工具。</li><li><strong>数据驱动</strong>：提供清晰、直观的数据反馈，帮助创作者优化内容。</li><li><strong>连接粉丝</strong>：提供便捷的粉丝互动与管理工具，帮助创作者建立社群。</li></ol></li><li><p><strong>核心功能模块</strong>：</p><ol><li><strong>入驻登录</strong>：这是创作者进入我们平台的“大门”。</li><li><strong>内容管理</strong>：这是创作者的“创作车间”，负责内容的生产、编辑和数据监控。</li><li><strong>粉丝管理</strong>：这是创作者的“社群CRM”，负责粉丝的互动和分析。</li></ol></li></ul><p>这三大模块，就构成了我们自媒体端产品的“骨架”。在接下来的小节中，我们将逐一进行详细的设计。</p><h3 id="8-1-4-本节小结"><a href="#8-1-4-本节小结" class="headerlink" title="8.1.4 本节小结"></a>8.1.4 本节小结</h3><table><thead><tr><th align="left"><strong>思考层次</strong></th><th align="left"><strong>核心洞察</strong></th><th align="left"><strong>最终产出</strong></th></tr></thead><tbody><tr><td align="left"><strong>基础需求</strong></td><td align="left">创作者需要发布和管理内容。</td><td align="left"><strong>核心功能</strong>：内容管理</td></tr><tr><td align="left"><strong>深层需求</strong></td><td align="left">创作者需要获得数据反馈、与粉丝互动、并实现个人成长。</td><td align="left"><strong>核心功能</strong>：粉丝管理、数据分析</td></tr><tr><td align="left"><strong>核心价值</strong></td><td align="left">我们要做的不是一个“工具”，而是一个“<strong>创作者成功平台</strong>”。</td><td align="left"><strong>设计原则</strong>：赋能创作、数据驱动、连接粉丝</td></tr></tbody></table><hr><h2 id="8-2-入驻与登录"><a href="#8-2-入驻与登录" class="headerlink" title="8.2 入驻与登录"></a>8.2 入驻与登录</h2><p>这是我们为创作者开启的“梦想之门”。与普通用户追求“快速无感”的登录不同，创作者的入驻，更像是一次“签约合作”。因此，我设计的流程，不仅要考虑便捷，更要体现出<strong>专业性、仪式感和契约精神</strong>。</p><h3 id="8-2-1-学习目标"><a href="#8-2-1-学习目标" class="headerlink" title="8.2.1 学习目标"></a>8.2.1 学习目标</h3><p>在本节中，我的目标是带大家设计一个完整、专业的创作者入驻流程。我们将学习如何区分普通用户注册与创作者入驻，并设计出包含资质审核、协议签署等关键环节的 onboarding(新用户引导流程) 流程。</p><h3 id="8-2-2-创作者入驻流程设计"><a href="#8-2-2-创作者入驻流程设计" class="headerlink" title="8.2.2 创作者入驻流程设计"></a>8.2.2 创作者入驻流程设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E5%85%A5%E9%A9%BB%E6%B5%81%E7%A8%8B%E5%9B%BE.png" alt="入驻流程图"></p><p>图中的流程（<code>登录 → 注册 → 实名认证</code>）是普通用户升级为创作者的一个高度简化版。在我的实际设计中，我会将这个流程细化为一套更完整的“<strong>入驻（Onboarding）</strong>”流程，它通常发生在用户已经拥有一个普通账户之后。</p><p>我设计的专业入驻流程如下：</p><ol><li><strong>基础账户注册/登录</strong>：用户首先需要有一个我们平台的普通账户（通过手机号+验证码等方式）。</li><li><strong>发起入驻申请</strong>：在App的某个位置（比如个人中心），我会放置一个醒目的“<strong>成为创作者</strong>”或“<strong>创作者中心</strong>”的入口。用户点击后，才正式开始入驻流程。</li><li><strong>提交资质信息</strong>：这是最关键的一步，是平台对创作者质量进行初步筛选的环节。我会要求用户提交：<ul><li><strong>实名认证</strong>：这是政策要求，也是建立信任的基础。</li><li><strong>创作者基本信息</strong>：设置公开的创作者昵称、头像。</li><li><strong>创作领域选择</strong>：让创作者选择自己擅长的内容领域（如科技、美食、旅行），这便于我们后续的内容分发。</li><li><strong>（可选）辅助材料</strong>：对于要求较高的平台，我还会增加“提交其他平台代表作链接”的步骤，用于评估创作者的实力。</li></ul></li><li><strong>平台协议签署</strong>：用户必须阅读并同意我们的《内容创作协议》和《平台规范》等。</li><li><strong>等待平台审核</strong>：提交申请后，用户的状态会变为“审核中”，我们会通过人工或AI，对其资质进行审核。</li><li><strong>入驻成功</strong>：审核通过后，用户才正式获得“创作者”身份，可以开始使用自媒体端的各项功能。</li></ol><h3 id="8-2-3-登录与身份切换"><a href="#8-2-3-登录与身份切换" class="headerlink" title="8.2.3 登录与身份切换"></a>8.2.3 登录与身份切换</h3><p>当一个用户同时拥有“普通用户”和“创作者”双重身份后，他的登录体验依然是统一的（使用同一个手机号或微信登录）。</p><p>我需要设计的是登录后的<strong>身份切换机制</strong>。通常，我会在“个人中心”页面，提供一个清晰的入口，比如“<strong>创作者中心</strong>”，已获得创作者身份的用户，点击后即可进入我们接下来要设计的自媒体后台。</p><h3 id="8-2-4-本节小结"><a href="#8-2-4-本节小结" class="headerlink" title="8.2.4 本节小结"></a>8.2.4 本节小结</h3><table><thead><tr><th align="left"><strong>设计环节</strong></th><th align="left"><strong>我的核心设计思考</strong></th></tr></thead><tbody><tr><td align="left"><strong>创作者入驻</strong></td><td align="left">不能等同于普通注册。它是一个包含<strong>资质审核</strong>和<strong>协议签署</strong>的、更正式的<strong>Onboarding</strong>流程，目的是筛选高质量创作者，并建立契约关系。</td></tr><tr><td align="left"><strong>登录与切换</strong></td><td align="left">登录使用统一账户，但在产品内，必须为创作者提供一个<strong>清晰、便捷</strong>的入口，以切换到自媒体后台。</td></tr></tbody></table><hr><h2 id="8-3-内容管理"><a href="#8-3-内容管理" class="headerlink" title="8.3 内容管理"></a>8.3 内容管理</h2><p>这是自媒体端后台的“心脏”，是创作者最核心的“创作车间”。我设计的首要目标，是让它功能强大，同时体验简洁、高效。</p><h3 id="8-3-1-学习目标"><a href="#8-3-1-学习目标" class="headerlink" title="8.3.1 学习目标"></a>8.3.1 学习目标</h3><p>在本节中，我的目标是带大家设计一个功能完善的内容管理模块。我们将学习如何设计内容的数据概览和列表页，以及如何设计一个体验良好的内容发布与编辑器。</p><h3 id="8-3-2-内容列表-数据概览"><a href="#8-3-2-内容列表-数据概览" class="headerlink" title="8.3.2 内容列表 &amp; 数据概览"></a>8.3.2 内容列表 &amp; 数据概览</h3><p>当创作者进入后台，他们第一眼最想看到的，一定是自己作品的表现。因此，内容管理模块的首页，我通常会设计成一个集<strong>数据概览</strong>和<strong>内容列表</strong>于一体的Dashboard。</p><h4 id="1-核心数据指标"><a href="#1-核心数据指标" class="headerlink" title="1. 核心数据指标"></a>1. 核心数据指标</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721110551564.png" alt="image-20250721110551564"></p><p>在页面的最上方，我会放置一个“<strong>数据概览</strong>”模块，实时展示创作者最关心的核心数据指标，并支持按时间筛选（今日/本周/本月等）。这些指标通常包括：</p><ul><li><strong>浏览数</strong>：内容被用户看到的次数。</li><li><strong>评论数</strong>：用户对内容的互动情况。</li><li><strong>点赞数</strong>：用户对内容的认可度。</li><li><strong>分享数</strong>：内容被传播的情况。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721110609019.png" alt="image-20250721110609019"></p><h4 id="2-列表设计要点"><a href="#2-列表设计要点" class="headerlink" title="2. 列表设计要点"></a>2. 列表设计要点</h4><p>在数据概览下方，是“<strong>内容列表</strong>”，我会用表格的形式，展示创作者发布的所有内容。为了方便管理，这个列表必须具备：</p><ul><li><strong>关键信息展示</strong>：清晰地展示文章标题、封面图、所属分类、发表时间等。</li><li><strong>筛选与搜索功能</strong>：提供按标题关键词搜索、按分类筛选、按日期筛选的功能。</li><li><strong>快捷操作入口</strong>：每一行内容后面，都需要有“编辑”、“删除”、“查看”、“下线”等快捷操作按钮。</li><li><strong>分页功能</strong>：当内容数量过多时，必须提供分页。</li></ul><h3 id="8-3-3-内容发布与编辑"><a href="#8-3-3-内容发布与编辑" class="headerlink" title="8.3.3 内容发布与编辑"></a>8.3.3 内容发布与编辑</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721110832220.png" alt="image-20250721110832220"></p><p>点击“发布文章”或“编辑”按钮，就会进入我们的<strong>内容编辑器</strong>。这是创作者挥洒才华的地方，体验必须流畅。我的设计会包含：</p><ul><li><strong>基础信息区</strong>：清晰的<code>文章标题</code>、<code>作者署名</code>等输入框。</li><li><strong>富文本编辑器</strong>：一个所见即所得的编辑器，至少要支持加粗、列表、插入图片等基础的图文混排功能。</li><li><strong>分类与标签</strong>：提供<code>文章分类</code>的选择功能，并允许作者为文章打上<code>标签</code>，这既方便用户理解，也便于我们的算法进行分发。</li><li><strong>操作区</strong>：提供<code>预览</code>、<code>存草稿</code>和<code>发布</code>等核心操作按钮。</li></ul><h3 id="8-3-4-内容状态管理"><a href="#8-3-4-内容状态管理" class="headerlink" title="8.3.4 内容状态管理"></a>8.3.4 内容状态管理</h3><p>在内容列表中，我还需要一个“<strong>状态</strong>”字段，来清晰地标识每一篇内容的生命周期。至少应包含以下几种状态：</p><ul><li><strong>草稿</strong>：已保存，但还未提交发布。</li><li><strong>审核中</strong>：已提交，正在等待平台审核。</li><li><strong>已发布</strong>：审核通过，所有用户可见。</li><li><strong>审核驳回</strong>：审核不通过，作者需要修改后重新提交。</li><li><strong>已下线</strong>：由作者本人或平台主动下架，用户不可见。</li></ul><h3 id="8-3-5-本节小结"><a href="#8-3-5-本节小结" class="headerlink" title="8.3.5 本节小结"></a>8.3.5 本节小结</h3><table><thead><tr><th align="left"><strong>模块</strong></th><th align="left"><strong>我的核心设计思考</strong></th></tr></thead><tbody><tr><td align="left"><strong>数据概览</strong></td><td align="left"><strong>让数据说话</strong>。第一时间向创作者展示最核心的内容表现数据，给予他们最直接的反馈和激励。</td></tr><tr><td align="left"><strong>内容列表</strong></td><td align="left"><strong>高效管理</strong>。提供强大的筛选、搜索和批量操作功能，帮助高产的作者轻松管理自己的百宝箱。</td></tr><tr><td align="left"><strong>内容发布</strong></td><td align="left"><strong>沉浸创作</strong>。提供一个稳定、易用的编辑器，让作者可以专注于创作本身，不受工具的干扰。</td></tr></tbody></table><hr><h2 id="8-4-评论管理"><a href="#8-4-评论管理" class="headerlink" title="8.4 评论管理"></a>8.4 评论管理</h2><p>在我看来，评论区是创作者与粉丝之间最重要的“连接器”。一个活跃、健康的评论区，是内容生命力的延续。因此，为创作者提供一套高效、便捷的评论管理工具，是自媒体后台设计的重中之重。我的设计，主要围绕<strong>过滤、查看、回复</strong>这三个核心动作展开。</p><h3 id="8-4-1-学习目标"><a href="#8-4-1-学习目标" class="headerlink" title="8.4.1 学习目标"></a>8.4.1 学习目标</h3><p>在本节中，我的目标是带大家设计一个功能完善的评论管理系统。我们将学习如何设计一个两层结构的评论列表，并为创作者提供必要的评论处理功能。</p><h3 id="8-4-2-评论列表设计"><a href="#8-4-2-评论列表设计" class="headerlink" title="8.4.2 评论列表设计"></a>8.4.2 评论列表设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112220480.png" alt="image-20250721112220480"></p><p>为了让创作者能高效地管理海量评论，我通常会设计一个两级结构的列表。</p><ol><li><p><strong>第一级：文章评论概览</strong><br>后台的入口，首先是一个以“<strong>文章</strong>”为维度的评论列表。这张列表清晰地展示了：</p><ul><li><code>内容标题</code>：是哪篇文章收到了评论。</li><li><code>评论总数</code> 和 <code>待回复评论数</code>：让创作者快速了解整体情况和待办事项。</li></ul></li></ol><ul><li><code>操作</code>：提供一个“<strong>查看评论</strong>”的入口，点击后进入第二级列表。</li></ul><ol start="2"><li><p><strong>第二级：单篇评论详情</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112306067.png" alt="image-20250721112306067"></p><p>点击“查看评论”后，创作者会进入针对<strong>某一篇文章</strong>的详细评论列表。在这里，可以清晰地看到每一条评论的<code>评论内容</code>、<code>用户昵称</code>、<code>用户头像</code>等。同时，我还会提供按<code>用户昵称</code>或<code>评论内容</code>进行筛选和搜索的功能。</p></li></ol><h3 id="8-4-3-评论处理功能（回复-置顶-删除）"><a href="#8-4-3-评论处理功能（回复-置顶-删除）" class="headerlink" title="8.4.3 评论处理功能（回复/置顶/删除）"></a>8.4.3 评论处理功能（回复/置顶/删除）</h3><p>在单篇评论详情列表的每一条评论后面，我必须为创作者提供一组管理工具：</p><ul><li><strong>回复</strong>：这是最重要的功能，是创作者与粉丝直接对话的桥梁。</li><li><strong>删除</strong>：赋予创作者管理自己评论区环境的权力，可以删除不友善或垃圾评论。</li><li><strong>（可选）置顶</strong>：这是一个非常好的精细化运营功能。我可以通过置顶优质评论，来引导整个评论区的讨论氛围。</li></ul><h3 id="8-4-4-本节小结"><a href="#8-4-4-本节小结" class="headerlink" title="8.4.4 本节小结"></a>8.4.4 本节小结</h3><p>一个好的评论管理系统，能让创作者感受到自己对社群的“掌控感”，并激励他们更积极地与粉丝互动，从而形成一个正向的社区循环。</p><hr><h2 id="8-5-粉丝管理"><a href="#8-5-粉丝管理" class="headerlink" title="8.5 粉丝管理"></a>8.5 粉丝管理</h2><p>如果说内容是创作者的“作品”，那么粉丝就是创作者最宝贵的“资产”。一个优秀的自媒体后台，必须为创作者提供一套轻量级的CRM（客户关系管理）系统，帮助他们了解自己的粉丝，从而创作出更受欢迎的内容。</p><h3 id="8-5-1-学习目标"><a href="#8-5-1-学习目标" class="headerlink" title="8.5.1 学习目标"></a>8.5.1 学习目标</h3><p>在本节中，我的目标是带大家设计一个包含“三视图”（列表、概况、画像）的粉丝管理模块，为创作者提供从宏观到微观的粉丝洞察能力。</p><h3 id="8-5-2-粉丝列表"><a href="#8-5-2-粉丝列表" class="headerlink" title="8.5.2 粉丝列表"></a>8.5.2 粉丝列表</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112703081.png" alt="image-20250721112703081"></p><p>这是粉丝管理的“<strong>微观视图</strong>”。它是一个完整的、可搜索、可筛选的粉丝名录。</p><ul><li><strong>核心功能</strong>：我会以列表的形式，展示每一位粉丝的<code>昵称</code>、<code>头像</code>、<code>性别</code>、<code>地区</code>等基础信息。</li><li><strong>互动设计</strong>：在每一位粉丝后面，我会提供“<strong>私信</strong>”或“<strong>关注</strong>”（回关）等操作按钮，为创作者提供主动触达粉丝的渠道。</li></ul><h3 id="8-5-3-粉丝概况"><a href="#8-5-3-粉丝概况" class="headerlink" title="8.5.3 粉丝概况"></a>8.5.3 粉丝概况</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112739984.png" alt="image-20250721112739984"></p><p>这是粉丝管理的“<strong>宏观数据视图</strong>”，它告诉创作者“我的粉丝群体整体发展趋势如何？”。</p><h4 id="1-核心数据指标-1"><a href="#1-核心数据指标-1" class="headerlink" title="1. 核心数据指标"></a>1. 核心数据指标</h4><p>在页面的最顶部，我会用数据卡片的形式，展示创作者最关心的几个核心KPI，比如：</p><ul><li><strong>粉丝累计总数</strong></li><li><strong>昨日新增粉丝</strong></li><li><strong>昨日取关数</strong></li></ul><h4 id="2-数据可视化设计"><a href="#2-数据可视化设计" class="headerlink" title="2. 数据可视化设计"></a>2. 数据可视化设计</h4><p>为了让趋势更直观，我会用<strong>折线图</strong>的形式，来展示粉丝总数和每日净增的变化趋势。在图表下方，再附上详细的每日数据表格，供创作者进行深度分析。</p><h3 id="8-5-4-粉丝画像"><a href="#8-5-4-粉丝画像" class="headerlink" title="8.5.4 粉丝画像"></a>8.5.4 粉丝画像</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112824923.png" alt="image-20250721112824923"></p><p>这是粉丝管理的“<strong>宏观特征视图</strong>”，它回答了创作者最关心的问题：“<strong>我的粉丝，到底是一群什么样的人？</strong>”</p><p>我通过数据可视化的方式，对创作者的所有粉丝，进行群体的、匿名的特征分析，通常包括：</p><ul><li><strong>性别分布</strong>（男女比例图）</li><li><strong>年龄分布</strong>（年龄段柱状图）</li><li><strong>地域分布</strong>（地图热力或省份排行）</li></ul><p>这些画像信息，对于创作者判断自己未来的内容方向，具有极高的战略价值。</p><h3 id="8-5-5-本节小结"><a href="#8-5-5-本节小结" class="headerlink" title="8.5.5 本节小结"></a>8.5.5 本节小结</h3><table><thead><tr><th align="left"><strong>模块</strong></th><th align="left"><strong>核心视图</strong></th><th align="left"><strong>我的设计目标</strong></th></tr></thead><tbody><tr><td align="left"><strong>粉丝列表</strong></td><td align="left"><strong>微观视图</strong></td><td align="left">让创作者能看到<strong>每一个</strong>具体的粉丝，并提供互动渠道。</td></tr><tr><td align="left"><strong>粉丝概况</strong></td><td align="left"><strong>宏观数据视图</strong></td><td align="left">让创作者能看到粉丝总量的<strong>增长趋势</strong>和每日变化。</td></tr><tr><td align="left"><strong>粉丝画像</strong></td><td align="left"><strong>宏观特征视图</strong></td><td align="left">让创作者能了解自己粉丝群体的<strong>人口统计学特征</strong>。</td></tr></tbody></table><hr><h2 id="8-6-本章总结"><a href="#8-6-本章总结" class="headerlink" title="8.6 本章总结"></a>8.6 本章总结</h2><h3 id="8-6-1-课程内容回顾"><a href="#8-6-1-课程内容回顾" class="headerlink" title="8.6.1 课程内容回顾"></a>8.6.1 课程内容回顾</h3><p>在本章，我们完整地设计了内容产品生态的另一端——<strong>自媒体端</strong>。</p><ul><li>我们首先确立了<strong>设计思路</strong>，明确了我们的目标是打造一个“创作者成功平台”。</li><li>我们设计了专业的<strong>入驻登录</strong>流程，为平台筛选优质创作者。</li><li>我们设计了核心的<strong>内容管理</strong>模块，为创作者提供了集发布、管理、数据分析于一体的“创作车间”。</li><li>我们设计了<strong>评论管理</strong>功能，赋予创作者与粉丝互动、管理社区的能力。</li><li>最后，我们设计了包含“三视图”的<strong>粉丝管理</strong>系统，为创作者提供了宝贵的粉丝洞察。</li></ul><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/11780.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/11780.html&quot;)">产品经理入门（八）：第八章：内容产品自媒体端设计</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/11780.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理入门（八）：第八章：内容产品自媒体端设计&amp;url=https://prorise666.site/posts/11780.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/51587.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div class="next-post pull-right"><a href="/posts/38041.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理入门（八）：第八章：内容产品自媒体端设计",date:"2025-07-20 23:13:45",updated:"2025-07-21 14:51:58",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第八章：内容产品自媒体端设计\n\n在第七章，我们为“普通用户”设计了一套完整、流畅的消费体验。但是，一个内容平台的繁荣，离不开持续产出优质内容的创作者，也就是“**自媒体**”。\n\n在这一章，我们将为这些创作者，设计一套专属的“创作工坊”——**自媒体端**。这是一个能让他们方便地发布内容、管理作品、与粉丝互动、并洞察数据，最终帮助他们在我们平台获得成功的后台系统。\n\n## 8.1 自媒体端设计思路\n\n![image-20250721103341813](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103341813.png)\n\n在动手设计之前，我们同样需要先建立一套清晰的设计思路。我们的出发点，依然是产品的顶层战略。我们V1.0的“**生产模式**”是PGC+UGC，这就决定了我们必须服务好“**自媒体**”这个核心角色。\n\n### 8.1.1 学习目标\n\n在本节中，我的目标是带大家一起，完成自媒体端设计的顶层战略思考。我们将从创作者最原始的需求出发，一步步深挖，提炼出我们自媒体端产品的核心价值，并最终推导出我们后台需要设计的核心功能模块。\n\n### 8.1.2 自媒体端的核心价值\n\n![image-20250721103428666](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103428666.png)\n\n我们首先来分析创作者的核心需求。\n* **最原始的需求**：作为内容的提供者，创作者最基本、最直接的需求，就是**需要有一个渠道，能够编辑、发布和管理自己的内容**。\n\n但是，我经常会反问自己和团队一个问题：**只提供一个发布和管理工具，就足够了吗？**\n\n![image-20250721103507471](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103507471.png)\n\n一个创作者，他不仅仅是一个“发布者”，他更像是在我们平台上经营着自己“小事业”的创业者。当他的内容被用户消费、订阅、评论后，他的内心一定会产生更深层次的渴望：\n\n* **来自“用户订阅”的渴望**：“有多少人关注了我？他们都是谁？” → 这背后是对**粉丝增长**和**社群归属**的渴望。\n* **来自“用户浏览”的渴望**：“我的内容受欢迎吗？哪篇文章的数据最好？” → 这背后是对**内容表现**和**创作反馈**的渴望。\n* **来自“用户评论”的渴望**：“我的读者们都在讨论什么？我如何与他们互动？” → 这背后是对**粉丝互动**和**舆论管理**的渴望。\n\n因此，我为我们的自媒体端，定义了它的核心价值：它绝不仅仅是一个“**内容发布工具**”，而是一个“**创作者成功平台**”。我们的使命，是为创作者提供一整套服务，不仅帮助他们创作，更要帮助他们**洞察数据、连接粉丝、获得成长**。\n\n### 8.1.3 设计原则与要点\n\n![image-20250721103535155](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721103535155.png)\n\n基于上述的核心价值，我确立了自媒体端的设计原则，并推导出了它必须具备的三大核心功能模块。\n\n* **设计原则**：\n    1.  **赋能创作**：提供高效、易用的内容发布与管理工具。\n    2.  **数据驱动**：提供清晰、直观的数据反馈，帮助创作者优化内容。\n    3.  **连接粉丝**：提供便捷的粉丝互动与管理工具，帮助创作者建立社群。\n\n* **核心功能模块**：\n    1.  **入驻登录**：这是创作者进入我们平台的“大门”。\n    2.  **内容管理**：这是创作者的“创作车间”，负责内容的生产、编辑和数据监控。\n    3.  **粉丝管理**：这是创作者的“社群CRM”，负责粉丝的互动和分析。\n\n这三大模块，就构成了我们自媒体端产品的“骨架”。在接下来的小节中，我们将逐一进行详细的设计。\n\n### 8.1.4 本节小结\n\n| **思考层次** | **核心洞察** | **最终产出** |\n| :--- | :--- | :--- |\n| **基础需求** | 创作者需要发布和管理内容。 | **核心功能**：内容管理 |\n| **深层需求** | 创作者需要获得数据反馈、与粉丝互动、并实现个人成长。 | **核心功能**：粉丝管理、数据分析 |\n| **核心价值** | 我们要做的不是一个“工具”，而是一个“**创作者成功平台**”。 | **设计原则**：赋能创作、数据驱动、连接粉丝 |\n\n\n\n\n---\n\n## 8.2 入驻与登录\n\n这是我们为创作者开启的“梦想之门”。与普通用户追求“快速无感”的登录不同，创作者的入驻，更像是一次“签约合作”。因此，我设计的流程，不仅要考虑便捷，更要体现出**专业性、仪式感和契约精神**。\n\n### 8.2.1 学习目标\n\n在本节中，我的目标是带大家设计一个完整、专业的创作者入驻流程。我们将学习如何区分普通用户注册与创作者入驻，并设计出包含资质审核、协议签署等关键环节的 onboarding(新用户引导流程) 流程。\n\n### 8.2.2 创作者入驻流程设计\n\n![入驻流程图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E5%85%A5%E9%A9%BB%E6%B5%81%E7%A8%8B%E5%9B%BE.png)\n\n图中的流程（`登录 → 注册 → 实名认证`）是普通用户升级为创作者的一个高度简化版。在我的实际设计中，我会将这个流程细化为一套更完整的“**入驻（Onboarding）**”流程，它通常发生在用户已经拥有一个普通账户之后。\n\n我设计的专业入驻流程如下：\n1.  **基础账户注册/登录**：用户首先需要有一个我们平台的普通账户（通过手机号+验证码等方式）。\n2.  **发起入驻申请**：在App的某个位置（比如个人中心），我会放置一个醒目的“**成为创作者**”或“**创作者中心**”的入口。用户点击后，才正式开始入驻流程。\n3.  **提交资质信息**：这是最关键的一步，是平台对创作者质量进行初步筛选的环节。我会要求用户提交：\n    * **实名认证**：这是政策要求，也是建立信任的基础。\n    * **创作者基本信息**：设置公开的创作者昵称、头像。\n    * **创作领域选择**：让创作者选择自己擅长的内容领域（如科技、美食、旅行），这便于我们后续的内容分发。\n    * **（可选）辅助材料**：对于要求较高的平台，我还会增加“提交其他平台代表作链接”的步骤，用于评估创作者的实力。\n4.  **平台协议签署**：用户必须阅读并同意我们的《内容创作协议》和《平台规范》等。\n5.  **等待平台审核**：提交申请后，用户的状态会变为“审核中”，我们会通过人工或AI，对其资质进行审核。\n6.  **入驻成功**：审核通过后，用户才正式获得“创作者”身份，可以开始使用自媒体端的各项功能。\n\n### 8.2.3 登录与身份切换\n\n当一个用户同时拥有“普通用户”和“创作者”双重身份后，他的登录体验依然是统一的（使用同一个手机号或微信登录）。\n\n我需要设计的是登录后的**身份切换机制**。通常，我会在“个人中心”页面，提供一个清晰的入口，比如“**创作者中心**”，已获得创作者身份的用户，点击后即可进入我们接下来要设计的自媒体后台。\n\n### 8.2.4 本节小结\n\n| **设计环节** | **我的核心设计思考** |\n| :--- | :--- |\n| **创作者入驻** | 不能等同于普通注册。它是一个包含**资质审核**和**协议签署**的、更正式的**Onboarding**流程，目的是筛选高质量创作者，并建立契约关系。 |\n| **登录与切换** | 登录使用统一账户，但在产品内，必须为创作者提供一个**清晰、便捷**的入口，以切换到自媒体后台。 |\n\n---\n\n## 8.3 内容管理\n\n这是自媒体端后台的“心脏”，是创作者最核心的“创作车间”。我设计的首要目标，是让它功能强大，同时体验简洁、高效。\n\n### 8.3.1 学习目标\n\n在本节中，我的目标是带大家设计一个功能完善的内容管理模块。我们将学习如何设计内容的数据概览和列表页，以及如何设计一个体验良好的内容发布与编辑器。\n\n### 8.3.2 内容列表 & 数据概览\n\n\n\n当创作者进入后台，他们第一眼最想看到的，一定是自己作品的表现。因此，内容管理模块的首页，我通常会设计成一个集**数据概览**和**内容列表**于一体的Dashboard。\n\n#### 1. 核心数据指标\n\n![image-20250721110551564](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721110551564.png)\n\n在页面的最上方，我会放置一个“**数据概览**”模块，实时展示创作者最关心的核心数据指标，并支持按时间筛选（今日/本周/本月等）。这些指标通常包括：\n* **浏览数**：内容被用户看到的次数。\n* **评论数**：用户对内容的互动情况。\n* **点赞数**：用户对内容的认可度。\n* **分享数**：内容被传播的情况。\n\n![image-20250721110609019](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721110609019.png)\n\n#### 2. 列表设计要点\n\n在数据概览下方，是“**内容列表**”，我会用表格的形式，展示创作者发布的所有内容。为了方便管理，这个列表必须具备：\n* **关键信息展示**：清晰地展示文章标题、封面图、所属分类、发表时间等。\n* **筛选与搜索功能**：提供按标题关键词搜索、按分类筛选、按日期筛选的功能。\n* **快捷操作入口**：每一行内容后面，都需要有“编辑”、“删除”、“查看”、“下线”等快捷操作按钮。\n* **分页功能**：当内容数量过多时，必须提供分页。\n\n### 8.3.3 内容发布与编辑\n\n![image-20250721110832220](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721110832220.png)\n\n点击“发布文章”或“编辑”按钮，就会进入我们的**内容编辑器**。这是创作者挥洒才华的地方，体验必须流畅。我的设计会包含：\n* **基础信息区**：清晰的`文章标题`、`作者署名`等输入框。\n* **富文本编辑器**：一个所见即所得的编辑器，至少要支持加粗、列表、插入图片等基础的图文混排功能。\n* **分类与标签**：提供`文章分类`的选择功能，并允许作者为文章打上`标签`，这既方便用户理解，也便于我们的算法进行分发。\n* **操作区**：提供`预览`、`存草稿`和`发布`等核心操作按钮。\n\n### 8.3.4 内容状态管理\n\n在内容列表中，我还需要一个“**状态**”字段，来清晰地标识每一篇内容的生命周期。至少应包含以下几种状态：\n* **草稿**：已保存，但还未提交发布。\n* **审核中**：已提交，正在等待平台审核。\n* **已发布**：审核通过，所有用户可见。\n* **审核驳回**：审核不通过，作者需要修改后重新提交。\n* **已下线**：由作者本人或平台主动下架，用户不可见。\n\n### 8.3.5 本节小结\n\n| **模块** | **我的核心设计思考** |\n| :--- | :--- |\n| **数据概览** | **让数据说话**。第一时间向创作者展示最核心的内容表现数据，给予他们最直接的反馈和激励。 |\n| **内容列表** | **高效管理**。提供强大的筛选、搜索和批量操作功能，帮助高产的作者轻松管理自己的百宝箱。 |\n| **内容发布** | **沉浸创作**。提供一个稳定、易用的编辑器，让作者可以专注于创作本身，不受工具的干扰。 |\n\n\n\n\n\n---\n\n## 8.4 评论管理\n\n在我看来，评论区是创作者与粉丝之间最重要的“连接器”。一个活跃、健康的评论区，是内容生命力的延续。因此，为创作者提供一套高效、便捷的评论管理工具，是自媒体后台设计的重中之重。我的设计，主要围绕**过滤、查看、回复**这三个核心动作展开。\n\n### 8.4.1 学习目标\n\n在本节中，我的目标是带大家设计一个功能完善的评论管理系统。我们将学习如何设计一个两层结构的评论列表，并为创作者提供必要的评论处理功能。\n\n### 8.4.2 评论列表设计\n\n![image-20250721112220480](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112220480.png)\n\n为了让创作者能高效地管理海量评论，我通常会设计一个两级结构的列表。\n\n1.  **第一级：文章评论概览**\n    后台的入口，首先是一个以“**文章**”为维度的评论列表。这张列表清晰地展示了：\n    \n    * `内容标题`：是哪篇文章收到了评论。\n    * `评论总数` 和 `待回复评论数`：让创作者快速了解整体情况和待办事项。\n* `操作`：提供一个“**查看评论**”的入口，点击后进入第二级列表。\n  \n2. **第二级：单篇评论详情**\n\n    ![image-20250721112306067](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112306067.png)\n\n    点击“查看评论”后，创作者会进入针对**某一篇文章**的详细评论列表。在这里，可以清晰地看到每一条评论的`评论内容`、`用户昵称`、`用户头像`等。同时，我还会提供按`用户昵称`或`评论内容`进行筛选和搜索的功能。\n\n### 8.4.3 评论处理功能（回复/置顶/删除）\n\n在单篇评论详情列表的每一条评论后面，我必须为创作者提供一组管理工具：\n* **回复**：这是最重要的功能，是创作者与粉丝直接对话的桥梁。\n* **删除**：赋予创作者管理自己评论区环境的权力，可以删除不友善或垃圾评论。\n* **（可选）置顶**：这是一个非常好的精细化运营功能。我可以通过置顶优质评论，来引导整个评论区的讨论氛围。\n\n### 8.4.4 本节小结\n\n一个好的评论管理系统，能让创作者感受到自己对社群的“掌控感”，并激励他们更积极地与粉丝互动，从而形成一个正向的社区循环。\n\n---\n\n## 8.5 粉丝管理\n\n如果说内容是创作者的“作品”，那么粉丝就是创作者最宝贵的“资产”。一个优秀的自媒体后台，必须为创作者提供一套轻量级的CRM（客户关系管理）系统，帮助他们了解自己的粉丝，从而创作出更受欢迎的内容。\n\n### 8.5.1 学习目标\n\n在本节中，我的目标是带大家设计一个包含“三视图”（列表、概况、画像）的粉丝管理模块，为创作者提供从宏观到微观的粉丝洞察能力。\n\n### 8.5.2 粉丝列表\n\n![image-20250721112703081](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112703081.png)\n\n这是粉丝管理的“**微观视图**”。它是一个完整的、可搜索、可筛选的粉丝名录。\n* **核心功能**：我会以列表的形式，展示每一位粉丝的`昵称`、`头像`、`性别`、`地区`等基础信息。\n* **互动设计**：在每一位粉丝后面，我会提供“**私信**”或“**关注**”（回关）等操作按钮，为创作者提供主动触达粉丝的渠道。\n\n### 8.5.3 粉丝概况\n\n![image-20250721112739984](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112739984.png)\n\n这是粉丝管理的“**宏观数据视图**”，它告诉创作者“我的粉丝群体整体发展趋势如何？”。\n\n#### 1. 核心数据指标\n\n在页面的最顶部，我会用数据卡片的形式，展示创作者最关心的几个核心KPI，比如：\n* **粉丝累计总数**\n* **昨日新增粉丝**\n* **昨日取关数**\n\n#### 2. 数据可视化设计\n\n为了让趋势更直观，我会用**折线图**的形式，来展示粉丝总数和每日净增的变化趋势。在图表下方，再附上详细的每日数据表格，供创作者进行深度分析。\n\n### 8.5.4 粉丝画像\n\n![image-20250721112824923](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721112824923.png)\n\n这是粉丝管理的“**宏观特征视图**”，它回答了创作者最关心的问题：“**我的粉丝，到底是一群什么样的人？**”\n\n我通过数据可视化的方式，对创作者的所有粉丝，进行群体的、匿名的特征分析，通常包括：\n* **性别分布**（男女比例图）\n* **年龄分布**（年龄段柱状图）\n* **地域分布**（地图热力或省份排行）\n\n这些画像信息，对于创作者判断自己未来的内容方向，具有极高的战略价值。\n\n### 8.5.5 本节小结\n\n| **模块** | **核心视图** | **我的设计目标** |\n| :--- | :--- | :--- |\n| **粉丝列表** | **微观视图** | 让创作者能看到**每一个**具体的粉丝，并提供互动渠道。 |\n| **粉丝概况** | **宏观数据视图** | 让创作者能看到粉丝总量的**增长趋势**和每日变化。 |\n| **粉丝画像** | **宏观特征视图** | 让创作者能了解自己粉丝群体的**人口统计学特征**。 |\n\n---\n\n## 8.6 本章总结\n\n### 8.6.1 课程内容回顾\n\n在本章，我们完整地设计了内容产品生态的另一端——**自媒体端**。\n* 我们首先确立了**设计思路**，明确了我们的目标是打造一个“创作者成功平台”。\n* 我们设计了专业的**入驻登录**流程，为平台筛选优质创作者。\n* 我们设计了核心的**内容管理**模块，为创作者提供了集发布、管理、数据分析于一体的“创作车间”。\n* 我们设计了**评论管理**功能，赋予创作者与粉丝互动、管理社区的能力。\n* 最后，我们设计了包含“三视图”的**粉丝管理**系统，为创作者提供了宝贵的粉丝洞察。\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%85%AB%E7%AB%A0%EF%BC%9A%E5%86%85%E5%AE%B9%E4%BA%A7%E5%93%81%E8%87%AA%E5%AA%92%E4%BD%93%E7%AB%AF%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.</span> <span class="toc-text">第八章：内容产品自媒体端设计</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#8-1-%E8%87%AA%E5%AA%92%E4%BD%93%E7%AB%AF%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-number">1.1.</span> <span class="toc-text">8.1 自媒体端设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.1.</span> <span class="toc-text">8.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-2-%E8%87%AA%E5%AA%92%E4%BD%93%E7%AB%AF%E7%9A%84%E6%A0%B8%E5%BF%83%E4%BB%B7%E5%80%BC"><span class="toc-number">1.1.2.</span> <span class="toc-text">8.1.2 自媒体端的核心价值</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-3-%E8%AE%BE%E8%AE%A1%E5%8E%9F%E5%88%99%E4%B8%8E%E8%A6%81%E7%82%B9"><span class="toc-number">1.1.3.</span> <span class="toc-text">8.1.3 设计原则与要点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-4-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.1.4.</span> <span class="toc-text">8.1.4 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-2-%E5%85%A5%E9%A9%BB%E4%B8%8E%E7%99%BB%E5%BD%95"><span class="toc-number">1.2.</span> <span class="toc-text">8.2 入驻与登录</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.2.1.</span> <span class="toc-text">8.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-2-%E5%88%9B%E4%BD%9C%E8%80%85%E5%85%A5%E9%A9%BB%E6%B5%81%E7%A8%8B%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.2.2.</span> <span class="toc-text">8.2.2 创作者入驻流程设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-3-%E7%99%BB%E5%BD%95%E4%B8%8E%E8%BA%AB%E4%BB%BD%E5%88%87%E6%8D%A2"><span class="toc-number">1.2.3.</span> <span class="toc-text">8.2.3 登录与身份切换</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-4-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.2.4.</span> <span class="toc-text">8.2.4 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-3-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.</span> <span class="toc-text">8.3 内容管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.3.1.</span> <span class="toc-text">8.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-2-%E5%86%85%E5%AE%B9%E5%88%97%E8%A1%A8-%E6%95%B0%E6%8D%AE%E6%A6%82%E8%A7%88"><span class="toc-number">1.3.2.</span> <span class="toc-text">8.3.2 内容列表 &amp; 数据概览</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E6%95%B0%E6%8D%AE%E6%8C%87%E6%A0%87"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">1. 核心数据指标</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%88%97%E8%A1%A8%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.3.2.2.</span> <span class="toc-text">2. 列表设计要点</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-3-%E5%86%85%E5%AE%B9%E5%8F%91%E5%B8%83%E4%B8%8E%E7%BC%96%E8%BE%91"><span class="toc-number">1.3.3.</span> <span class="toc-text">8.3.3 内容发布与编辑</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-4-%E5%86%85%E5%AE%B9%E7%8A%B6%E6%80%81%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.4.</span> <span class="toc-text">8.3.4 内容状态管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.3.5.</span> <span class="toc-text">8.3.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-4-%E8%AF%84%E8%AE%BA%E7%AE%A1%E7%90%86"><span class="toc-number">1.4.</span> <span class="toc-text">8.4 评论管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.4.1.</span> <span class="toc-text">8.4.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-2-%E8%AF%84%E8%AE%BA%E5%88%97%E8%A1%A8%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.4.2.</span> <span class="toc-text">8.4.2 评论列表设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-3-%E8%AF%84%E8%AE%BA%E5%A4%84%E7%90%86%E5%8A%9F%E8%83%BD%EF%BC%88%E5%9B%9E%E5%A4%8D-%E7%BD%AE%E9%A1%B6-%E5%88%A0%E9%99%A4%EF%BC%89"><span class="toc-number">1.4.3.</span> <span class="toc-text">8.4.3 评论处理功能（回复/置顶/删除）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-4-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.4.4.</span> <span class="toc-text">8.4.4 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-5-%E7%B2%89%E4%B8%9D%E7%AE%A1%E7%90%86"><span class="toc-number">1.5.</span> <span class="toc-text">8.5 粉丝管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.5.1.</span> <span class="toc-text">8.5.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-2-%E7%B2%89%E4%B8%9D%E5%88%97%E8%A1%A8"><span class="toc-number">1.5.2.</span> <span class="toc-text">8.5.2 粉丝列表</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-3-%E7%B2%89%E4%B8%9D%E6%A6%82%E5%86%B5"><span class="toc-number">1.5.3.</span> <span class="toc-text">8.5.3 粉丝概况</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E6%95%B0%E6%8D%AE%E6%8C%87%E6%A0%87-1"><span class="toc-number">1.5.3.1.</span> <span class="toc-text">1. 核心数据指标</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%95%B0%E6%8D%AE%E5%8F%AF%E8%A7%86%E5%8C%96%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.5.3.2.</span> <span class="toc-text">2. 数据可视化设计</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-4-%E7%B2%89%E4%B8%9D%E7%94%BB%E5%83%8F"><span class="toc-number">1.5.4.</span> <span class="toc-text">8.5.4 粉丝画像</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.5.5.</span> <span class="toc-text">8.5.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#8-6-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.6.</span> <span class="toc-text">8.6 本章总结</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-6-1-%E8%AF%BE%E7%A8%8B%E5%86%85%E5%AE%B9%E5%9B%9E%E9%A1%BE"><span class="toc-number">1.6.1.</span> <span class="toc-text">8.6.1 课程内容回顾</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>