#about-page
  .author-content-item.like-technology
    background-size cover
    min-height 230px
    color var(--efu-white)
    &::after
      box-shadow 0 -69px 203px 11px #050b20 inset
      position absolute
      content ''
      width 100%
      height 100%
      top 0
      left 0

  .author-content-item.like-music
    background-size cover
    min-height 400px
    color var(--efu-white)
    overflow hidden
    &::after
      box-shadow 0 -69px 203px 11px #0e0e0e inset
      position absolute
      content ''
      width 100%
      height 100%
      top 0
      left 0

  .author-content-item.comic
    min-height 400px
    color var(--efu-white)
    position relative
    
    .comic-box
      width 110%
      height 100%
      display flex
      position absolute
      left 50%
      top 0
      transform translateX(-50%)
      &::after
        box-shadow 0 -69px 203px 11px #04120f inset
        position absolute
        content ''
        width 100%
        height 100%
        top 0
        left 0
        pointer-events: none;
    
    .author-content-item-tips,
    .author-content-item-title,
    .content-bottom
      z-index 3
      color var(--efu-white)
    
    .comic-item
      height 100%
      color var(--efu-white)
      width 20%
      transform skew(-10deg, 0deg)
      transition 0.8s
      position relative
      overflow hidden
      &:hover
        width 46%
        .comic-item-cover
          left 16%
          transform skew(10deg, 0deg) scale(1.6)

      .comic-item-cover
        position absolute
        top 0
        left -50%
        height 100%
        transform skew(10deg, 0deg)
        object-fit cover
        transition scale 0.2s, all 0.8s
        img
          height 100%
          max-width none
          transition 0.8s

  .author-content-item.comic
    .comic-box
      +maxWidth1400()
          width 120%
          height 100%      
      +maxWidth768()
          width 109%
          height 100%
