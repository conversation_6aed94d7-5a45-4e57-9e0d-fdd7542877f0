---
title: 产品经理进阶（三）：第三章：电商用户端产品设计
categories: 产品经理
tags:
  - 产品经理教程
cover: 'https://bu.dusays.com/2025/07/25/6882f31a48223.webp'
comments: true
toc: true
ai: true
abbrlink: 17683
date: 2025-07-24 18:13:45
---

# 第三章：电商用户端产品设计

欢迎来到第三章。在这一章，我们将真正地以“建筑师”的身份，从地基开始，一砖一瓦地搭建起我们电商产品的“用户端大楼”。

我们将系统性地学习，从用户首次进入产品的“大门”（产品形态），到在“商场”中闲逛（浏览商品）、挑选结账（下单支付）、寻求服务（售后），再到参与“广场”讨论（商品种草）和回到“私人房间”（个人中心）的全过程设计。

## 3.1 学习目标

在本节中，我的核心目标是，带大家掌握电商用户端设计的两大基石：**产品形态选择**和**核心设计思路**。

我们将学会对比不同产品形态（App/小程序等）的优劣，并能以微信小程序为例，掌握其独特的设计规范。



## 3.2 用户端产品形态选择

![image-20250721220851552](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721220851552.png)

在项目正式启动后，我面临的第一个重大的技术和战略决策就是：**我们主要应该为用户，打造一个什么样的“场”？**

是开发一个功能强大的独立**App**？还是一个便于在微信里传播的**小程序**？亦或是一个灵活轻便的**H5**网页？

正如我们看到的，像淘宝、京东这样成熟的平台，通常是“全都要”，在每一种形态上都有布局。但对于启动期的我们，必须做出取舍，选择最适合我们当前战略目标的形态。

### 3.2.1 常见产品形态对比 (App / 小程序 / H5 / Web端)

为了做出正确的决策，我通常会用下面这张对比表格，来系统地分析不同形态的优劣。

![image-20250721220811815](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721220811815.png)

| 产品形态 | 是否需安装 | 开发投入 | 用户体验 | 我的选择考量（适用场景） |
| :--- | :--- | :--- | :--- | :--- |
| **App** | **是** | **高** (需安卓/iOS分别开发) | **完善** | 当我的产品功能极其复杂、需要调用手机底层能力（如GPS、蓝牙）、且追求极致性能和体验时，我会选择App。它的推广和获客成本最高。 |
| **微信小程序**| **否** | **中** (前端开发) | **好** | 当我希望**借助微信的社交生态进行裂变传播和获客**时，小程序是我的不二之选。它体验好、开发快，特别适合电商、本地生活等需要社交分享的场景。 |
| **H5** | **否** | **低** (前端开发) | **中等** | 当我需要最大化的**灵活性和传播范围**时，我会选择H5。它不受任何平台限制，一个链接就可以走天下，特别适合制作营销活动页、内容文章页。 |
| **Web端**| **否** | **中** (前端开发) | **一般** | 当我的核心场景是**需要用户在电脑上进行复杂操作**时，我会选择Web端。比如，我们为商家设计的后台管理系统，就必须是Web端。 |

对于我们的“大P超级电商”项目，结合我们拥有海量C端用户的背景，**优先开发一个微信小程序**来承接和转化这部分流量，是一个非常明智的启动策略。

### 3.2.2 设计规范：以微信小程序为例

既然我们选择了小程序，那我就必须深入理解它的“游戏规则”。虽然小程序的设计在整体上与App非常类似，但因为它“寄生”于微信这个超级生态之上，所以也带来了一些独特的设计规范和特殊功能。

#### 1. 页面结构

* **官方小程序胶囊**：我必须时刻牢记，小程序页面的右上角，有一个官方的、包含“关闭/更多”等功能的“胶囊”按钮，这个区域是**不可设计的**，我的页面布局必须为它留出空间。

![image-20250721221058059](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221058059.png)

* **顶部导航栏**：小程序的顶部导航栏，由微信官方统一样式，我们只能定义中间的`标题区`文字和左侧`导航区`的返回逻辑。

![image-20250721221137650](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221137650.png)

* **标签栏 (Tab Bar)**：小程序底部的标签栏，有严格的数量限制：**最少2个，最多5个**。

![image-20250721221430247](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221430247.png)

* **尺寸规范**：在绘制原型时，我依然会采用和App一致的**375x667px**作为基准画板，并遵循`状态栏(22px)`、`导航栏(44px)`、`标签栏(49px)`的标准高度。

#### 2. 特殊功能与限制

小程序最大的魅力，在于它能调用微信生态的独有能力。假设我们需要实现“获取手机号”和“推送消息”，在小程序中的实现方式就与App完全不同。



![image-20250721221318459](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221318459.png)

* **获取微信手机号**：在App中，我需要用户手动输入手机号，再通过短信验证，流程繁琐。而在小程序里，我可以直接放置一个“**微信用户一键授权**”的按钮。用户点击后，会拉起微信的官方授权弹窗，用户只需点击“允许”，我们就能安全地获取到他绑定在微信上的手机号，**极大提升了注册/登录的转化率**。

![image-20250721221356124](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221356124.png)

* **订阅消息**：在App中，只要用户允许，我可以相对自由地向他推送消息。但在小程序中，则严格得多。我**不能主动向用户推送营销消息**。用户必须**主动“订阅”**某一个服务通知（比如“发货通知”、“降价提醒”），我才能向他发送一条对应的服务消息。这是一种对用户打扰更小的“**一次性授权**”模式，我在设计运营功能时必须充分考虑这个限制。


---
## 3.3 用户端产品设计思路

在我们选定了产品形态（如：微信小程序）之后，我不会立刻开始绘制具体的页面。我会先退一步，从更高维度，建立起整个用户端产品的“**设计思路和骨架**”。

虽然我们之前学习过内容产品的设计，但电商用户端，有其自身独特的业务核心和功能侧重。在这一节，我将带大家明确我们电商产品的核心业务，并掌握指导我们进行界面布局的两大基本设计原则。

### 3.3.1 核心业务与功能模块

![image-20250722091703261](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091703261.png)

我的第一步，是**将用户的完整购物旅程，拆解为几个核心的业务模块**。这能帮助我确保，我的产品设计，覆盖了用户从“认知”到“购后”的每一个关键环节。

对于一个典型的电商产品，我将核心业务拆解为以下六大模块：
1.  **注册登录**：这是用户“获取身份”的入口。
2.  **浏览商品**：这是用户“逛商场”的核心环节，包括有目的的搜索和无目的的闲逛。
3.  **下单支付**：这是电商的“收银台”，是完成交易的核心闭环。
4.  **订单与售后**：这是用户购后的“服务中心”，负责履约和处理问题。
5.  **商品种草**：这是我们产品特色的“内容社区”，负责吸引用户、建立信任。
6.  **个人中心**：这是用户在我们平台的“家”，负责汇总个人数据和产品全局设置。

![image-20250722091746923](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091746923.png)

这六大业务模块，就构成了我们用户端的“**核心功能架构**”。图中的“用户端核心架构”就是一个很好的示例，它将具体的、零散的功能点（如：搜索栏、金刚区），清晰地归类到了它所属的业务模块之下（如：首页）。这个架构，就是我们后续进行详细页面设计的“总纲”。

### 3.3.2 核心设计原则

有了功能架构的“骨架”，我接下来需要思考，如何为这个骨架“填充血肉”？也就是说，在具体的页面上，我应该如何组织和排布那些繁多的功能和信息，才能让用户觉得界面清晰、易于理解？

在这里，我会借助格式塔心理学（Gestalt Psychology）中，两个最基础、也最强大的视觉设计原则。

#### 1. 接近法则 (Law of Proximity)

![image-20250722091947975](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091947975.png)

* **法则定义**：我们的大脑，会本能地，将物理空间上**彼此靠近**的元素，视为一个**整体**。
* **我的设计应用**：在界面设计中，“**间距**”是我最有力的设计工具之一。
    * 我会把**相关**的元素（比如，一张商品图和它的商品标题）紧紧地放在一起，让它们在视觉上自然地成为一组。
    * 我会用**留白**，来拉开**不相关**的元素或组之间的距离，形成清晰的视觉区块。

#### 2. 相似法则 (Law of Similarity)

![image-20250722092013020](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092013201.png)

* **法则定义**：我们的大脑，会本能地，将那些在**视觉特征（如形状、颜色、大小）上相似**的元素，视为**同类**。

* **我的设计应用**：在界面设计中，“**一致性**”是建立用户认知的关键。
  
    * 我会确保所有**功能相同或相近**的元素，在视觉上保持**相似**。比如，所有“可点击”的按钮，都用同一种颜色和形状；所有“可输入”的文本框，都用同一种样式。
    
    ![image-20250722092053425](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092053425.png)
    
    * 正如“常用功能”案例所示，尽管每一个图标的图案都不同，但它们统一的尺寸、颜色和圆角风格，都在强烈地向用户暗示：“我们都属于一类，我们都是可以点击的功能入口”。

**总结**：在后续的页面设计中，我将综合运用“**接近法则**”来**组织页面布局、划分区块**，并用“**相似法则**”来**统一控件样式、建立操作认知**。这是让我们的设计变得“专业”和“易用”的秘诀。



-----

## 3.4 浏览商品

[此处放置“首页思考”的图片 (`image_2d6800.png`)]

我们电商产品的“商场”已经建好，现在，当用户走进这扇“大门”时，他们首先看到的，就是我们的“**商场大堂**”——**首页**。

首页，是用户对我们产品形成第一印象、也是我们引导用户走向各个“专柜”的最核心的枢纽。我设计首页时，始终围绕着一个核心问题：**它需要同时满足谁的需求？达成什么样的目的？**

### 3.4.1 首页设计

#### 1\. 首页的核心目的

我设计首页，必须同时扮演好“**服务员**”（满足用户需求）和“**商场经理**”（达成公司目标）的双重角色。

  * **从用户角度：满足多样化的“逛街”心态**
    ![image-20250722092825580](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092825580.png)

    我需要为进入首页、心态各不相同的用户，都提供最高效的解决方案。

| 用户心态 | 我的设计方案 |
| :--- | :--- |
| **“我明确知道要买什么”** | 在页面最顶部，提供一个**高效、精准的搜索栏**。 |
| **“我知道大概要买什么品类”** | 提供一套**清晰、易懂的商品分类入口**。 |
| **“我就是想随便逛逛，看有啥好东西”** | 提供一个无限下拉的、引人入胜的**个性化商品推荐列表**。 |
| **“我想看看有啥便宜可以占”** | 提供突出、有吸引力的**促销/活动专区**，如秒杀、百亿补贴等。 |

  * **从公司角度：实现平台的商业目标**
    ![image-20250722092907876](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092907876.png)

    同时，我也需要利用首页这个“寸土寸金”的场地，来达成我们的商业目的。

| 公司目标 | 我的设计方案 |
| :--- | :--- |
| **帮助商家促销引流** | 在首页的核心位置，为付费的商家提供**Banner广告位**和**活动入口**。 |
| **帮助自营店铺促销引流** | （若有自营业务）为自营的重点商品或活动，提供专属的曝光区域。 |
| **展现平台调性** | 整个首页的**视觉风格（UI）、文案、推荐内容**，都必须严格符合我们“内容驱动的潮流社区电商”的定位。 |

#### 2\. 常见首页模块解析

![image-20250722092959787](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092959787.png)

为了同时满足上述的用户和公司需求，经过多年的演化，电商首页已经形成了一套相对成熟的模块化布局。我会通过分析竞品，来借鉴和思考我们自己的设计。

| **核心模块** | **我的设计解读与应用** |
| :--- | :--- |
| **搜索栏** | **雷打不动的第一模块**，必须始终固定在页面最顶部，服务于目的性最强的用户。 |
| **金刚区** | ![image-20250722093051105](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722093051105.png)<br> 这是指搜索栏下方的一组**图标网格导航**。我把它看作是**核心业务的“一级入口”**。我会把我们最重要的商品分类（如“潮流服饰”、“数码新品”）和特色业务（如“达人直播”、“好物种草”）放在这里。 |
| **Banner / 促销区** | 这是首页最**黄金的广告和活动展示位**。我会用它来推广平台的重大活动，或将其作为重要的广告收入来源。 |
| **商品推荐列表** | 这是首页占据面积最大、也是留住“闲逛”用户的**核心内容区**。我会采用“**瀑布流**”的形式，通过个性化推荐算法，为每个用户呈现一个独一无二的、无限延伸的商品列表。 |

#### 3\. 我们“大P超级电商”的首页设计思路

最后，结合对竞品的分析和我们自身的定位，我为我们“大P超级电商”的首页，确立了以下设计思路：

1.  **UI风格**：界面要简洁、留白充分，营造出“呼吸感”，整体视觉风格要年轻、时尚，符合“90后”的审美。
2.  **金刚区设计**：必须体现我们“内容+电商”的特色。除了`服装`、`数码`等品类入口，必须包含`直播`、`种草`等内容社区的入口。

我们在设计首页时一定会遇到的经典决策：“金刚区”到底应该放几个图标？上面的文字应该怎么写？

| 图标 (示意) | 文字标签     | 我的设计思路                                                 |
| ----------- | ------------ | ------------------------------------------------------------ |
| 📱           | **手机数码** | “90后”核心关注的高价值品类，属于**品类入口**。               |
| 👚           | **潮流服饰** | 贴合我们“潮流”的平台调性，属于**品类入口**。                 |
| 💄           | **美妆个护** | 年轻用户，特别是女性用户的高频消费品类，属于**品类入口**。   |
| 📺           | **达人直播** | 我们的**核心差异化**业务，必须给予最强的曝光，属于**功能入口**。 |
| 💸           | **百亿补贴** | 电商平台“拉新促活”的标配，用明确的利益点吸引用户，属于**活动入口**。 |
| 🧾           | **领券中心** | 培养用户“先领券再购物”的习惯，提升转化率，属于**功能入口**。 |
| 📦           | **我的订单** | 用户最高频使用的查询功能之一，提供一个快捷入口，属于**功能入口**。 |
| ➡️           | **全部分类** | “渐进式呈现”原则的应用，收纳所有其他品类。                   |

3.**推荐算法**：商品推荐列表的算法，除了考虑用户的浏览和购买行为，还必须**高度重视用户的社交和内容偏好**。比如，优先推荐“用户关注的KOL正在推荐的商品”

最后我们产出的商品低保真原型原型，大致是这样的：

![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/c2f3be4f-3f73-4c54-a40b-d41760dc006b.png)


---
### 3.4.2 商品分类设计（无分类、一级、多级）

在设计好首页之后，我们需要为那些有大概购物方向的用户，提供一套清晰的“货架导引”系统，这个系统就是**商品分类**。它的核心目的，是满足用户高效缩小寻找范围的需求。

我给商品类目的定义是：按照商品的用途、特征等维度，并且根据一定的管理目的，把相似的商品归为一类的行为。并且在类别当中又会存在细分的类型。

我设计分类体系的复杂度，完全取决于我们平台商品的数量和丰富度。我通常会根据平台的体量，考虑三种不同的分类形式。

![image-20250722101139313](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101139313.png)

这三种形式，分别适用于不同的业务阶段和规模，我们可以通过上面的案例直观地感受它们的差异。对于我们“大P超级电商”这样的综合性平台，一个清晰的“多级分类”体系是必不可少的设计。

### 3.4.3 商品列表与详情页设计（图文、参数、评价、推荐）

当用户通过首页、搜索或分类，最终都会来到两个核心的页面：**商品列表页（PLP）**和**商品详情页（PDP）**。

我设计这两个页面时，脑海里始终装着三类典型用户：目的明确的“小风”、犹豫不决的“中风”、以及纯粹闲逛的“大风”。

![image-20250722101836348](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101836348.png)

**1. 商品列表页 (Product List Page - PLP)**

商品列表页是我们商场的“**货架**”。它的核心设计目标，是让用户能**高效地筛选和对比**。虽然列表的内容来源可能不同（如搜索结果、推荐、分类），但其页面结构和设计要点是共通的。

![image-20250722101931329](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101931329.png)

一个优秀的商品列表页，必须包含清晰的**商品卡片**（展示图、文、价等核心信息），以及强大的**筛选与排序**功能，来帮助用户快速从海量商品中，找到自己心仪的目标。

![image-20250722105209159](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722105209159.png)



**2. 商品详情页 (Product Detail Page - PDP)**

商品详情页是我们商场的“**金牌销售员**”，它的核心设计目标是**打消用户的所有疑虑，促成最终的购买**。我通常会将页面上半部分，用来满足理性用户的决策需求。

![image-20250722101959967](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101959967.png)

页面的首屏要点，在于简明扼要地表现出产品的核心信息，让用户直接判断出“这个产品是什么”。我会通过**商品展示区**（高清图/视频）、**商品属性区**（规格参数）和**用户评价区**（社群证明），来分别满足用户对“颜值”、“内涵”和“口碑”的确认需求。

页面的下半部分，我则用来服务那些还在犹豫、或者纯粹闲逛的用户，通过更丰富的内容，对他们进行深度“种草”。

![image-20250722102009141](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722102009141.png)

这部分通常包括图文并茂的**详情介绍**，用来吸引用户；以及**问答模块**，用来对评价功能进行进一步强化，打消用户的购买疑虑。

在详情页的底部，我一定会设计一个“**智能推荐**”模块。它的核心目的，是在用户对当前商品不满意、准备离开时，为他提供更多相关的选择，**形成一个流量的闭环**，增加用户在我们平台留存和成交的机会。

![image-20250722102020748](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722102020748.png)




---
## 3.5 下单支付

在用户完成了“逛”和“选”，将心仪的商品加入购物车之后，我们就进入了整个电商流程的“**核心交易环节**”。

我把这个环节的设计，看作是引导用户走过一条“**信任与效率的走廊**”。这条走廊上的任何一个障碍、一丝疑虑，都可能导致用户在最后关头放弃购买。因此，我的设计目标，必须是**极致的顺滑、清晰与安全**。

### 3.5.1 下单流程与页面设计（提交页、支付页）

我通常会将整个下单支付流程，拆解为两个核心的页面来进行设计：**订单提交页**和**支付页（收银台）**。

#### 1. 订单提交页 (Order Submission Page)

当用户在商品详情页点击“立即购买”，或在购物车点击“去结算”后，并不会直接进入付款环节，而是会先来到**订单提交页**。

![image-20250722110142381](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110142381.png)

这个页面的核心作用，我把它定义为用户的“**最后一次确认**”。在用户真正掏钱之前，我必须为他提供一个清晰的、所有交易信息的汇总页面，让他进行最后的检查和确认。

![image-20250722110204985](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110204985.png)

我设计的订单提交页，必须让用户能够清晰地完成三件事：
* **确认商品**：清晰地罗列出本次将要购买的所有商品信息（名称、SKU、数量、价格）。
* **确认地址**：提供默认收货地址，并允许用户方便地选择或新增其他地址。
* **确认价格**：清晰地展示商品总额、运费、优惠券抵扣、最终实付金额等所有价格明细。



#### 2. 支付页/收银台 (Payment Page / Cashier)

![image-20250722110039262](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110039262.png)

当用户在订单提交页，点击“提交订单”后，他才真正进入了“**支付页**”，我常称之为“**收银台**”。

![image-20250722105741343](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722105741343.png)

这个页面的设计，我追求的是**极致的简洁和安全感**。它的核心作用只有三个：
1.  **确认实付金额**：醒目地展示最终需要支付的金额。
2.  **选择支付方式**：提供用户选择支付渠道（如微信、支付宝）的入口。
3.  **完成支付**：一个清晰、唯一的“确认支付”按钮。

**我的拓展设计（支付异常处理）**：
![image-20250722110255711](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110255711.png)

设计支付页时，我必须考虑一个最常见的异常场景：**用户进入了收银台，但因为种种原因，没有完成支付就退出了**。
一个糟糕的设计，可能会让用户之前提交的订单直接消失。而一个优秀的设计，正如案例所示，应该**将这份订单，自动保存为一张“待支付”的订单**。用户可以在“我的订单”中随时找到它，并重新发起支付。这个小小的设计，能为我们挽回大量可能流失的销售额。

#### 3. 支付成功后的流程

![image-20250722110528929](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110528929.png)

当用户成功支付后，这笔“交易”在后台就正式生成了，并进入了它的生命周期。我会用一组“**订单状态**”，来清晰地标记它在流程中所处的节点。支付成功，就是订单状态从“**待支付**”流转到“**待发货**”的触发器。

![image-20250722110510755](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110510755.png)

此时，用户可以在“个人中心”的“**我的订单**”列表中，看到这笔订单，并查看到它“待发货”的状态。当商家发货后，用户最关心的“**物流信息**”就会在这里出现。

![image-20250722110805038](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110805038.png)

>`思考：“物流信息是从哪来的🤔”`

![image-20250722110821135](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110821135.png)

![image-20250722110923521](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110923521.png)

它并不是由我们的商家手动更新的。在我设计的后台，商家发货时，只需要选择**快递公司**并输入**快递单号**。随后，我们的**后端服务器**，就会通过API接口，**定时地向第三方快递查询平台（如“申通”）发起查询请求**，获取最新的物流轨迹，然后将这些信息，展示在用户端的订单详情页上。

![image-20250722111007040](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111007040.png)

我在撰写PRD时，必须将这个技术方案的逻辑，清晰地描述出来。




---
### 3.5.2 支付方式（微信、支付宝、银联、聚合支付）

在设计好“收银台”页面后，我需要做的最重要的决策，就是为这个收银台，配备哪些“**收款设备**”，也就是我们常说的**支付方式**。

在今天的中国市场，只提供一种支付方式是远远不够的。为了最大化地提升支付成功率，我至少需要为用户提供微信支付和支付宝这两种主流选择。

#### 1. 微信支付 & 支付宝 & 银联（独立渠道对接）

![image-20250722111349078](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111349078.png)



![image-20250722111359741](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111359741.png)

![image-20250722111408567](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111408567.png)

理论上，我们可以分别独立地去对接每一个支付渠道。

**我需要告诉开发的关键点（以微信支付为例）：**
要接入微信支付，不是一个纯粹的技术工作，它需要产品、运营和技术共同协作。我需要了解并推动以下几个步骤：

1.  **商户号申请（运营/商务负责）**：首先，我们需要由公司的运营或商务同事，前往“**微信支付商户平台**”，提交我们公司的营业执照等资质，申请一个“商户号（mch_id）”。这个过程，支付宝和银联也完全一样，都需要我们先拥有一个官方认证的“商家身份”。
2.  **获取开发凭证（研发负责人）**：当我们的商户号被批准后，技术负责人需要登录这个商户平台，去获取进行技术开发所必需的“**身份凭证**”。这通常包括`AppID`、`API证书`、`API密钥`等。我把它们理解为，我们公司服务器与微信支付服务器之间，进行加密通信的“账号和密码”。
3.  **技术对接（研发负责）**：拿到凭证后，研发同学才会真正开始写代码。
    * **后端开发**：需要按照微信/支付宝的官方开发文档，开发服务端接口。这些接口主要负责创建“预支付订单”、接收支付成功或失败的“异步通知”等。
    * **前端开发（App）**：需要集成微信/支付宝的官方SDK（软件开发工具包）。这个SDK的主要作用，是在我们的App里，能够“拉起”用户手机上已经安装的微信或支付宝App，来进行最终的密码/指纹输入。

#### 2. 聚合支付（我的推荐方案）

![image-20250722111626234](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111626234.png)

在我们理解了独立对接的流程后，两个核心痛点就浮现出来了：
* **接入成本高**：我要支持微信、支付宝、银联三种支付，我的研发团队就需要把上面的流程，**重复做三遍**。这需要耗费巨大的研发资源。
* **财务管理难**：每天，我的财务同事，需要分别登录三个不同的商户后台，去下载三份不同的对账单，再进行手动的汇总和核对，极其繁琐且容易出错。

![image-20250722111642616](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111642616.png)

为了解决这两个痛点，一个更聪明的、也是我强烈推荐的方案，就是使用“**聚合支付**”。

![image-20250722111748908](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111748908.png)

**聚合支付**服务商（如 **Ping++、Adapay**），就是支付领域的“**万能转换插头**”。它已经提前帮我们，把市面上所有的主流支付渠道（微信、支付宝、银联、各类银行卡等），都预先集成好了。

**我需要告诉开发的关键点（技术视角）：**
* **一次对接，全部拥有**：我们不再需要去对接微信、支付宝等多个上游渠道。我的研发团队，只需要按照聚合支付服务商提供的一份开发文档，**进行一次技术对接**即可。
* **统一的API和SDK**：聚合支付会为我们提供**一套统一的API和SDK**。当用户在我们的App里选择用微信支付时，我们的App调用的，是聚合支付的SDK；我们的服务器，也只请求聚合支付的API。后续由聚合支付的服务，来和微信的服务器进行通信。
* **统一的后台和对账单**：我的财务同事，只需要登录聚合支付这**一个后台**，就可以看到所有渠道的交易流水，并下载一份统一的对-账单。

**我的决策权衡：**
使用聚合支付的唯一“缺点”，是它会在每个渠道的原有费率基础上，再收取一点点的服务费。但考虑到它能为我们**节省巨大的研发成本和财务管理成本**，对于绝大多数公司（特别是初创和中型公司）而言，这笔服务费，都是一笔**极具性价比**的投资。

>`聚合支付是不是代表不用哪些营业证书之类的，直接通过付钱就能接入了🤔`
>
>答案是：**不是的，聚合支付并不能免除您提供公司资质（如营业执照）的义务。**
>
>您可以把聚合支付服务商，看作是一个“**超级代办员**”或“**技术外包服务商**”，而不是一个“**资质豁免机构**”。他们的核心价值在于**简化技术对接和财务管理**，而不是绕过金融监管。
>
>我为您梳理一下实际的流程：
>
>1. **您与聚合支付签约**：您首先需要选择一家聚合支付服务商（如Ping++），并在他们的平台上注册账户。
>2. **您向聚合支付提交资质**：在注册过程中，您**仍然需要**向聚合支付服务商，提交您自己公司的全套有效资质，包括但不限于：
>	- **营业执照**
>	- **法人身份证信息**
>	- **对公银行账户**
>	- **网站/App的ICP备案信息**（如果适用）
>3. **聚合支付为您代办申请**：聚合支付服务商在收到您的资质后，会作为您的“代办员”，拿着您的这些材料，去分别向微信支付、支付宝、银联等官方渠道，为您**集中申请**开通各个支付渠道的商户权限。
>4. **最终结果**：审批通过后，您最终获得的，依然是**您自己公司名下的、在微信和支付宝备案的合法商户号**。聚合支付只是为您提供了一个统一的技术接口和管理后台来操作它们。

| **支付方案** | **我的解读** | **优点** | **缺点** |
| :--- | :--- | :--- | :--- |
| **逐个渠道对接** | 我们分别与微信、支付宝等签约并进行技术开发。 | 费率可能略低，资金直接到账。 | 开发成本极高，财务对账繁琐。 |
| **使用聚合支付** | 我们只与一家聚合支付服务商签约和开发。 | **开发成本极低（只需一次对接）**，财务对账简单。 | 费率略高，资金需要经过聚合服务商中转。 |





---
### 3.5.3 库存管理与问题应对

我们都可能有过这样的经历：在一个平台下单后，不急着付款，过了一会儿想起来去支付，发现订单依然有效；

而在另一个平台，同样的操作，回来支付时却被告知“商品已售罄”。

**这是为什么呢？**
这背后，就反映了不同电商平台，对于“**库存扣减**”这个核心问题，采取了两种截然不同的产品策略。

#### 1. 库存扣减方式（拍下减 vs 付款减）

在我设计交易系统时，我必须与我的技术和业务负责人，共同做出一个关键决策：**我们的库存，到底应该在哪一个节点扣减？** 这个决策，没有绝对的好坏，只有不同选择下的利弊权衡。

| 扣减方式 | 核心逻辑 | 用户体验 | 主要风险 | 我的选择考量 |
| :--- | :--- | :--- | :--- | :--- |
| **拍下减** | 当用户点击“**提交订单**”的瞬间，无论是否付款，系统都会立即为他预留这份库存。 | **好**。用户会感觉“只要我下单了，这个货就是我的了”，体验非常安心。 | **恶拍** | 我通常会在普通商品的销售中，采用此方式，因为它能提供最佳的用户体验。 |
| **付款减** | 只有当用户**成功完成支付**的瞬间，系统才会去扣减实际的物理库存。 | **一般**。用户下单后，可能会因为犹豫了几分钟，回来支付时发现商品已被别人买走，导致体验不佳和用户流失。 | **超卖** | 我通常只在库存极少、瞬时流量极大的“秒杀”等营销活动中，才会谨慎采用此方式。 |

作为产品经理，我的工作，就是**选择一种方式，并设计一套完整的机制，来最大化地规避它的潜在风险**。

#### 2. 问题与应对（恶意拍单、超卖）

##### **应对“恶拍”**

![image-20250722112931696](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722112931696.png)

如果我选择了“**拍下减库存**”，那我的头号敌人，就是“**恶拍**”——即，竞争对手或黄牛，恶意地大量下单但不支付，以此来锁死我的库存，让真实用户无法购买。

为了应对它，我必须建立一套“**组合防御**”体系：
1.  **减少库存保留时间**：这是我最核心的武器。我会设计一个“**订单自动取消**”的规则。比如，**下单后15分钟内未支付**，系统将自动取消这笔订单，并将预留的库存，**重新释放**回公共库存池中，供其他用户购买。
2.  **限购**：对于一些热门或促销商品，我会在产品层面，增加“**限购**”规则。比如，规定“**单个ID限购1件**”，这能有效防止单一恶意用户锁死大量库存。
3.  **安全策略**：我还会和风控团队合作，建立监控机制。当发现某个用户ID，在短时间内，有大量“下单后又取消”的异常行为时，系统可以暂时限制他的下单权限。

##### **应对“超卖”**

![image-20250722113011849](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113011849.png)

如果我选择了“**付款减库存**”，那我最大的噩梦，就是“**超卖**”——即，我们实际卖出的商品数量，超过了我们的真实库存。这会引发严重的客诉，极大地损害平台信誉。

为了应对它，我同样需要一套“**组合防御**”体系：
1.  **技术角度解决**：这主要依赖于研发团队。我会要求我的技术负责人，必须在技术层面，通过**数据库锁**或**分布式队列**等技术，来处理高并发场景下的库存扣减请求，确保对最后一件库存的扣减操作，是“**原子性**”的（即，在同一瞬间，只能有一个请求能成功）。
2.  **提示用户**：在产品体验层面，为了管理用户预期，当某个商品的库存数量很少时（比如，少于10件），我会在商品详情页和购物车中，明确地展示“**库存紧张**”或“**仅剩X件**”的提示文案。
3.  **设置安全库存**：这是我最常用的一个运营策略。如果一个商品的物理库存有1000件，我会在电商后台的“可售卖库存”中，只填写**950**件。那剩下的50件，就成了我的“**安全库存**”。它就像一个缓冲垫，既能消化掉极端情况下，因技术原因产生的少量超卖，也能用来应对用户“退货换货”的需求。





-----

### 3.5.4 拆单逻辑（父子订单、半拆单）

当用户在我们的购物车里，同时选中了来自不同商家、或者满足某些特殊条件的多个商品，然后点击“去结算”时，一个复杂的问题就摆在了我面前：

**后台应该如何处理这张“大单”？是把它当作一个订单，还是多个订单？**

![image-20250722110016603](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110016603.png)

“如果从购物车（多个店铺多个商品）进入结算，需要考虑什么？” 在这种情况下，我的订单提交页，必须进行“**拆单**”展示。我会**按照不同的店铺，将商品进行分组**。每个店铺的商品，会形成一个独立的“包裹”，分别计算运费和优惠，最终汇总成一个总的支付金额。这种清晰的结构，是解决多商家同时结算场景的最佳实践。

#### 1\. 为什么要拆单？

我设计拆单逻辑，主要是为了应对以下五种常见的业务场景：

| **我的设计考量** | **拆单因素** |
| :--- | :--- |
| ![image-20250722113550199](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113550199.png) <br> 这是最常见的拆单原因。不同商家的商品，其**货权、发货地、财务结算主体**都不同，因此必须拆分为独立的订单，分别进行处理。 | **店铺 (Store)** |
| ![image-20250722113814579](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113814579.png)<br> 即便用户购买的是同一个自营商家的多件商品，这些商品也可能存放在**全国不同的仓库**。为了最高效地完成履约，系统需要按仓库，将订单拆分给不同的仓储中心进行打包发货。 | **仓库 (Warehouse)** |
| ![image-20250722113838115](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113838115.png) <br> 不同的快递公司，对单个包裹的**重量和体积**都有上限。当用户购买的商品总重量或总体积超过限制时，就需要拆分为多个包裹，对应生成多个订单。 | **物流 (Logistics)** |
| ![image-20250722113859980](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113859980.png)<br> 某些**特殊品类**的商品需要单独处理。比如，`易碎品`需要特殊包装，`超大件`（如轮胎）无法与普通商品合并打包，都需要独立成单。 | **品类 (Category)** |
| ![image-20250722113920499](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113920499.png) <br> 这主要应用于**跨境海淘**业务。根据国家政策，跨境零售进口商品的单次交易限值为5000元。当用户的单笔订单超过这个限值时，系统必须将其拆分为多个订单，以符合清关和税务要求。 | **商品价值 (Value)** |

#### 2\. 拆单的两种主流模式

![image-20250722113943020](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113943020.png)

明确了“为什么拆”，我们再来看“怎么拆”。行业内，主要有两种主流的拆单模式，它们最核心的区别，在于**拆单发生的时机**。

##### **父子订单模式 (Parent-Child Order Model)**

![image-20250722114113042](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722114113042.png)

  * **核心逻辑**：**先支付，后拆分**。
  * **我的解读**：在这种模式下，用户从下单到支付完成，始终面对的是一个统一的“**父订单**”。他只需要付一次总的款项。当支付成功后，我们的后端系统，才会根据拆单规则，将这个父订单，在后台默默地拆分为多个“**子订单**”，分别推送给不同的仓库或商家去履约。
  * **用户感知**：用户在“我的订单”列表中，会看到一个父订单，点进去之后，才能看到下面包含的多个子订单，每个子订单都有独立的物流和状态。
  * **典型代表**：**京东**。
  * **优点**：用户支付体验统一、流畅。能有效避免下面要讲的“优惠券漏洞”。
  * **缺点**：后端系统的处理逻辑相对更复杂。

##### **半拆单模式 (Semi-split Order Model)**

![image-20250722132926059](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722132926059.png)

  * **核心逻辑**：**先拆分，后支付**。

  * **我的解读**：在这种模式下，当用户从购物车点击“去结算”时，系统在进入“**订单提交页**”的那一刻，就已经**完成了拆分**。页面上会直接按照店铺等维度，将商品展示为**多个独立的订单**。用户需要对这些独立的订单，进行统一支付（或者也可以选择只支付其中一部分）。

  * **用户感知**：用户在支付前，就已经明确知道自己的购物车，被分成了几笔不同的订单。

  * **典型代表**：**淘宝**。

  * **优点**：业务逻辑相对简单清晰。

  * **缺点（及我的应对）**：
    ![image-20250722133014820](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133014820.png)

    这种模式存在一个著名的“**薅羊毛**”漏洞。比如，平台有一个“跨店满300减50”的活动。用户可以从A店选200元商品，B店选100元商品，凑成一单。在订单提交页，系统会把优惠按比例分摊到两个独立的订单上。此时，用户如果只支付A店的那个订单，就等于用不到200元的价格，享受到了满减优惠。

    我作为产品经理，**必须设计规则来规避这个漏洞**。比如，我会定义：
    
    “**对于参与跨店满减活动的组合订单，若用户在规定时间内未完成所有相关订单的支付，则所有订单将被自动取消，优惠券也将退回。**”

---
### 3.5.5 购物车功能设计（信息展示、库存监控、结算等）

在设计下单流程时，我首先要面临一个战略性的选择：**我们的电商产品，到底需不需要购物车？**

这并不是一个理所当然的问题。购物车的设计，必须服务于我们产品的核心交易模式。

#### 1. 购物车的作用与决策

![image-20250722133533377](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133533377.png)

![image-20250722133601731](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133601731.png)

![image-20250722133608676](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133608676.png)


* **什么时候我“需要”购物车？**
    对于像我们“大P超级电商”这样的**招商模式/混合模式**平台，购物车是**必须品**。因为用户会在不同的店铺之间“逛”，它的核心作用是：
    1.  **凑单与比价**：让用户可以把来自不同店铺的、感兴趣的商品，先放在一个地方，进行统一的比较和筛选。
    2.  **跨店促销**：是实现“跨店满减”等复杂促销活动的**技术基础**。

* **什么时候我“不需要”购物车？**
    1.  **C2C二手交易模式（如：闲鱼）**：二手商品大多是“孤品”（库存只有1件），交易前通常需要买卖双方进行沟通议价，流程复杂。购物车这种“先暂存再统一结算”的模式，会增加无效库存的锁定，并打断沟通流程，反而降低交易效率。
    2.  **拼团模式（如：拼多多）**：拼多多的核心是“低价爆款、冲动消费”。它希望用户看到一个商品，立刻就完成下单转化。购物车的存在，会让用户“冷静下来”、进行“反复比价”，这与它的核心商业模式是相悖的。

**结论**：对于我们的“大P超级电商”，购物车是用户完成多商品、跨店铺购买的核心功能，我们必须精心设计。

#### 2. 购物车核心功能设计

![image-20250722133729846](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133729846.png)

我设计购物车，会围绕“**进入**”、“**使用**”、“**离开**”这三个场景，来规划它的核心功能。

* **信息展示 (Information Display)**
    ![image-20250722133750028](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133750028.png)
    这是购物车最基础，也是最重要的部分。
    * **状态区分**：我需要设计两种状态。**未登录**时，购物车应为空，并展示商品推荐，引导用户去逛；**登录**后，则展示用户已添加的商品。
    * **分组与排序**：为了让信息清晰，我必须将商品按“**店铺**”进行分组。在店铺内部，商品会按照“**添加时间**”的倒序排列，最新添加的在最上方。
    * **营销信息**：我会清晰地展示每个商品适用的优惠信息（如“满减”、“优惠券”），以及店铺整体的促销活动，刺激用户凑单。

* **库存监控 (Inventory Monitoring)**
    ![image-20250722133856218](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133856218.png)
    我必须在购物车里，就向用户提供实时的库存状态，避免他到了提交订单的最后一步，才发现商品已售罄。我会设计三种库存状态的展示：
    1.  **有货**：正常显示。
    2.  **库存紧张**：当库存很少时（如＜5件），用红字等醒目的方式，提示用户“仅剩X件”，制造稀缺感，促进转化。
    3.  **无货**：当商品售罄时，商品必须被置灰，数量选择器变为不可用状态，并清晰地提示“已售罄”或“无货”。

* **编辑功能 (Editing)**
    ![image-20250722133941901](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133941901.png)
    我必须赋予用户对购物车的完全掌控权。这包括：
    * **修改商品数量**：提供简单易用的加、减数量选择器。
    * **修改商品规格**：允许用户直接在购物车，切换商品的SKU（如颜色、尺码）。
    * **删除商品**：提供删除单个商品，以及在“编辑”模式下，批量删除多个商品的功能。

* **结算功能 (Checkout)**
    ![image-20250722134009275](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134009275.png)
    这是购物车最终的“使命召唤”按钮。我会在页面底部，设计一个常驻的结算栏，它必须清晰地展示：
    * **已勾选商品的总计金额**
    * **优惠减免的金额明细**
    * **一个色彩鲜明、吸引点击的“去结算”按钮**，并标明已选商品的数量。




---
## 3.6 订单评价及售后

用户的购物旅程，在支付成功的那一刻，其实才刚刚过半。

**从“支付成功”到“满意使用”**，这“最后一公里”的体验，我称之为**购后体验**，它直接决定了用户是否会成为我们的回头客。

本节，我们就来设计购后体验中，最重要的两个环节：**订单评价**和**售后流程**。

### 3.6.1 订单评价维度（商品质量、服务态度等）

我始终认为，**订单评价**，是电商平台**信任体系的基石**。它既是后续用户的“购买决策参考”，也是平台用来“管理商家”的重要数据来源。

![image-20250722134457500](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134457500.png)

一个设计良好的评价体系，能极大地促进平台的健康循环。正如流程图所示，**查看商品评价**，是用户在“购买决策”前的关键一步，直接影响着平台的转化率。

![image-20250722134535203](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134535203.png)

我设计评价体系，核心是定义好“**评价维度**”，即，我希望用户从哪些方面，来对这次交易进行评价。

![image-20250722134626963](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134626963.png)

一个专业、全面的评价功能，至少应该包含以下两部分：

**1. 多维度评分**
为了得到可量化的、能用于商家考核的数据，我不会只让用户打一个“总分”，而是会将评分，拆解为几个核心的维度。

![image-20250722134658540](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134658540.png)

| **评价维度** | **我的设计说明** |
| :--- | :--- |
| **商品质量** | 核心维度，直接反映了“货”的品质。 |
| **发货速度**| 反映商家履约环节的“物流”效率。 |
| **服务态度**| 反映商家在售前、售中、售后环节的“服务”质量。 |

我会将这几个维度，都设计为“**1-5星**”的评分形式，这能让我非常直观地，计算出商家的综合服务评分。

**2. 图文评价**
除了量化的评分，我还需要提供一个让用户能自由表达、分享购物体验的“内容创作区”。

![image-20250722134741503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134741503.png)

这个功能的设计，我会包含：
* **文字评价**：一个开放的文本输入框，让用户可以详细描述购物心得。
* **图片/视频评价**：提供图片/视频的上传功能。“有图有真相”，带图的评价，是所有评价中，对其他用户参考价值最高、最可信的。

### 3.6.2 售后流程设计（取消、退款退货、换货等）

即便我们尽了最大努力，交易过程中也难免会出现各种问题。一套**清晰、合理、公正**的售后流程，是我们在用户遇到问题时，挽回他们信任的最后机会。我把这个流程，也称为“**逆向流程**”。

我设计售后流程，最核心的原则是：**在订单的不同生命周期（状态）下，为用户提供不同的、符合当前场景的售后操作**。

![image-20250722134857703](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134857703.png)


| **订单状态** | **可执行的售后操作** | **我的设计说明** |
| :--- | :--- | :--- |
| `待支付` | **取消订单** | 用户未付款，可无理由取消。 |
| `待发货` | **仅退款** | 用户已付款但未发货，可直接申请退款（无需退货）。 |
| `待收货` | **申请退款** | 用户可申请退款，触发包裹拦截。**退款成功需待拦截成功或用户拒收。** 建议收货后再发起换货。 |
| `交易成功`| **申请售后** | 在售后保障期内，可申请退款退货、换货或维修。 |




---
## 3.7 商品种草（社区化设计）

我们已经设计完了电商平台最核心的“**交易链路**”。现在，我们要开始为我们的产品，构建真正的“**护城河**”——**商品种草**，也就是**社区化设计**。

这部分的设计，将直接体现我们“**内容驱动的潮流社区电商**”的核心定位，是我们区别于传统货架式电商、吸引和留存年轻用户的关键。

### 3.7.1 种草定义与场景（内容推荐、发布与互动）

首先，我来定义一下“种草”。在我看来，它是一种**基于真实体验和信任关系的内容化商品推荐行为**。它包含两个方面：
* **被种草**：我通过看别人的分享，发现了一款好物，并产生了购买的欲望。
* **去种草**：我因为使用了一款好物，自发地去分享我的使用心得，推荐给别人。

![image-20250722154101263](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154101263.png)

在我们的平台上，用户的“种草”旅程，也分为“**看帖**”和“**发帖**”这两条核心路径。基于这两条路径，我提炼出了三大核心用户场景，以及支撑这些场景的必备功能。

![image-20250722154203318](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154203318.png)

1.  **发布购物心得**：这是“去种草”的场景，需要我们提供`发布心得`的功能。
2.  **查看他人购物心得**：这是“被种草”的场景，需要我们提供一个`种草社区`（信息流）。
3.  **针对购物心得互动**：这是社区活跃的保障，需要我们提供`点赞`、`收藏`、`分享`、`评论`等功能。

### 3.7.2 社区功能结构（搜索、发布、瀑布流、话题标签）

现在，我们来具体设计支撑上述场景的核心功能界面。

**1. 种草社区 (信息流)**

![image-20250722154335398](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154335398.png)

这是用户“被种草”的核心场所，我设计的要点如下：
* **瀑布流布局**：为了最大化地突出图片这种强视觉冲击力的内容，我会采用**瀑布流**的布局形式来呈现“种草”笔记列表。
* **关键词搜索**：在顶部，我必须提供一个强大的**搜索**功能，让用户可以根据关键词，精准地查找自己感兴趣的“种草”内容。
* **分类/话题查看**：提供按不同**分类**或**话题**，来筛选和浏览“种草”笔记的功能，满足用户宽泛的浏览需求。

**2. 发布种草 (内容发布页)**

![image-20250722155905759](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722155905759.png)

这是用户“去种草”的核心工具，我设计的要点如下：
* **图片/视频上传**：提供入口，让用户可以选择手机里的**单张或多张图片/视频**进行上传。
* **编写心得内容**：提供一个富文本编辑器，让用户可以**撰写**自己的使用心得和推荐理由。
* **关联商品**：**这是连接“内容”与“电商”最关键的一步**。我必须提供一个功能，让用户可以在发布笔记时，方便地**关联**到我们平台上正在售卖的**具体商品**。这就在“种草”和“拔草”之间，建立起了最短的转化路径。
* **选择话题标签**：允许用户为自己的笔记，选择或创建**话题标签**，这既能表达自己的内容核心，也便于被有相同兴趣的用户发现。





---
## 3.8 个人中心

当用户在我们的“商场”里完成了浏览、购买、评价等一系列行为后，他们需要一个地方，来存放他们的“战利品”（订单）、“会员卡”（个人信息）和“购物小票”（历史记录）。这个地方，就是**个人中心**。

在我看来，个人中心是**用户在我们平台上的“数字资产”管理中心**，是提升用户归属感、提供深度服务的核心枢纽。

### 3.8.1 核心功能版块设计（我的订单、设置、推荐、快捷入口等）

![image-20250722160633138](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722160633138.png)

我设计个人中心，不会简单地把所有功能堆砌在一起。我会像整理房间一样，将功能进行**逻辑分区**，让用户能快速找到自己想要的东西。根据淘宝这类成熟产品的经验，我通常会将个人中心，划分为以下四大版块：

**1. 用户数据与资产**
这是整个页面的“门面”，是用户最核心的个人信息和资产的展示区。
* **个人信息**：最顶部，清晰地展示用户的`头像`和`昵称`，并提供一个入口，可以跳转到更详细的“个人资料页”进行编辑。
* **业务数据**：将用户最关心的几个动态数据，进行可视化展示，比如`商品收藏`、`店铺收藏`、`浏览足迹`的数量。
* **核心资产入口**：提供用户最重要的“资产”的快捷入口。对于我们平台，最重要的就是`我的订单`和我们特色功能的`我的种草`。

**2. 快捷功能入口**
这是一个灵活的、网格布局的区域，我用它来聚合一些**使用频率相对较高**的功能或运营活动入口。比如`我的优惠券`、`客服中心`、`地址管理`、`每日签到`等。

**3. 应用全局设置**
这个版块，我通常会把它放在页面的下半部分，或者收纳到一个统一的“**设置**”入口里。它包含的是一些低频、但必要的全局性功能，比如`账号与安全`、`支付设置`、`关于我们`，以及最重要的`退出登录`按钮。

**4. 个性化推荐**
个人中心是一个高度个性化的页面，因此，它也是进行**精准商品推荐**的绝佳场所。在页面的底部，我会设计一个“**为你推荐**”的模块，根据用户的历史购买、收藏和浏览记录，为他推荐可能感兴趣的商品，以创造更多的交叉销售机会。

![image-20250722160650480](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722160650480.png)

我们为“大P超级电商”设计的这份个人中心线框图，就是上述设计思路的一个具体体现。它结构清晰、主次分明，将用户最关心的“我的订单”和“我的种草”放在了最核心的位置，确保了用户体验的便捷。

## 3.9 本章总结

至此，我们已经完整地设计出了一个电商产品用户端的所有核心模块。让我们最后回顾一下本章的整个设计旅程：

| **设计模块** | **核心产出与学习要点** |
| :--- | :--- |
| **产品形态选择**| 我们对比了**App/小程序/H5/Web**的优劣，并深入学习了**微信小程序**独特的设计规范与特殊功能。 |
| **用户端设计思路**| 我们确立了电商的**六大核心业务模块**，并掌握了指导界面布局的**接近法则**与**相似法则**。 |
| **浏览商品**| 我们设计了**首页、商品分类、商品列表页**和**商品详情页**，构建了用户“逛”和“选”的核心路径。 |
| **下单支付**| 我们设计了**订单提交页、支付页**和**购物车**，并深入探讨了**库存管理、拆单逻辑**等复杂的后端策略。 |
| **订单评价及售后**| 我们设计了购后体验的**评价体系**和基于订单状态的**售后流程**，以建立用户信任。 |
| **商品种草**| 我们设计了产品的差异化模块——**社区**，通过**信息流**和**发布**功能，打通内容与电商。 |
| **个人中心**| 我们为用户设计了一个清晰、有序的“家”，聚合了**用户资产、快捷入口**和**全局设置**。 |

通过这一章的实战，我们已经将电商用户端的理论，全部转化为了具体、可视的产品设计方案。我们已经拥有了一份足以交付给UI和开发团队的、完整的“建筑蓝图”。






---