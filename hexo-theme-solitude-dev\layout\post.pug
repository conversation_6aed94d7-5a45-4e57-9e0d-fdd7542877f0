extends includes/layout.pug

block content
    main.layout#content-inner
        #post
            if page.not_cover
                include includes/widgets/post/postInfo
            article.post-content.article-container
                if page.ai_text && theme.post.ai.enable
                    include includes/widgets/post/ai
                != page.content
            include includes/widgets/post/copyright
            .post-tools-right
                .tag_share
                    .post-meta__tag-list
                        each data in page.tags.data || []
                            a(href=url_for(data.path)).post-meta__tags
                                span.tags-punctuation
                                    i.solitude.fas.fa-hashtag
                                    =data.name
                                    span.tagsPageCount=data.length
            if theme.google_adsense.enable && !theme.google_adsense.auto_ads && theme.google_adsense.post_content
                div.google-ads-warp
                    ins.adsbygoogle(style="display:block; text-align:center; height:284px", data-ad-layout="in-article", data-ad-format="fluid", hide-unfilled="true", data-ad-client=theme.google_adsense.client, data-ad-slot=theme.google_adsense.slot)
                    script.
                        (adsbygoogle = window.adsbygoogle || []).push({});
            if site.posts.length > 1
                include includes/widgets/post/postNav
            if theme.related_post.enable
                != related_posts(page, site.posts)
            if page.comment
                - var comment_js = true
                include includes/widgets/third-party/comments/comment
        if page.aside
            include includes/widgets/aside/aside