// 我的装备
.goodthings-title
  margin: 1rem 0
  line-height: 1;
.equipment-item
  .equipment-item-content
    display: flex
    flex-direction: row
    flex-wrap: wrap
    margin: 0 -8px
    .equipment-item-content-item
      width: calc(25% - 12px)
      border-radius: 12px
      border: var(--style-border-always)
      overflow: hidden
      margin: 8px 6px
      background: var(--anzhiyu-card-bg)
      box-shadow: var(--anzhiyu-shadow-border)
      min-height: 400px
      position: relative
      +maxWidth1200()
        width: calc(50% - 12px)
      +maxWidth768()
        width: 100%
      
      .equipment-item-content-item-info
        padding: 8px 16px 16px 16px
        margin-top: 12px

      .equipment-item-content-item-name
        font-size: 18px
        font-weight: bold
        line-height: 1
        margin-bottom: 8px
        white-space: nowrap
        overflow: hidden
        text-overflow: ellipsis
        width: fit-content
        cursor pointer
        &:hover
          color: var(--anzhiyu-main)

      .equipment-item-content-item-specification
        font-size: 12px
        color: var(--anzhiyu-secondtext)
        line-height: 16px
        margin-bottom: 5px
        white-space: nowrap
        overflow: hidden
        text-overflow: ellipsis

      .equipment-item-content-item-description
        line-height: 20px
        color: var(--anzhiyu-secondtext)
        height: 60px
        display: -webkit-box
        overflow: hidden
        -webkit-line-clamp: 3
        -webkit-box-orient: vertical
        font-size: 14px

      a.equipment-item-content-item-link
        font-size: 12px
        background: var(--anzhiyu-gray-op)
        padding: 4px 8px
        border-radius: 8px
        cursor: pointer

        &:hover
          background: var(--anzhiyu-main)
          color: var(--anzhiyu-white)

      .equipment-item-content-item-cover
        width: 100%
        height: 200px
        background: var(--anzhiyu-secondbg)
        display: flex
        justify-content: center
        align-items: center;

      img.equipment-item-content-item-image
        object-fit: contain
        height: 80%
        width 260px

      .equipment-item-content-item-toolbar
        display: flex
        justify-content: space-between
        position: absolute
        bottom: 12px
        left: 0
        width: 100%
        padding: 0 16px

body[data-type="equipment"] #web_bg
  background: var(--anzhiyu-background);
body[data-type="equipment"] #page
  border: 0;
  box-shadow: none !important;
  padding: 0 !important;
  background: 0 0 !important;
body[data-type="equipment"] #page .page-title
  display: none;


