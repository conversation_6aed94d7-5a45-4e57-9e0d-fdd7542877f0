<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理进阶（六）：第六章：电商后台 - 运营与交易管理 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理进阶（六）：第六章：电商后台 - 运营与交易管理"><meta name="application-name" content="产品经理进阶（六）：第六章：电商后台 - 运营与交易管理"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理进阶（六）：第六章：电商后台 - 运营与交易管理"><meta property="og:url" content="https://prorise666.site/posts/37507.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第六章：电商后台 - 运营与交易管理6.1 学习目标在本章中，我的核心目标是，带大家掌握电商运营后台最核心的几个模块的设计。我们将学习如何设计营销位管理系统（如Banner）、支付与订单管理系统，以及评价管理系统，为我们的运营同事，打造一套强大、高效的“指挥中心”。 6.2 营销位管理我们已经在用户"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta name="description" content="第六章：电商后台 - 运营与交易管理6.1 学习目标在本章中，我的核心目标是，带大家掌握电商运营后台最核心的几个模块的设计。我们将学习如何设计营销位管理系统（如Banner）、支付与订单管理系统，以及评价管理系统，为我们的运营同事，打造一套强大、高效的“指挥中心”。 6.2 营销位管理我们已经在用户"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/37507.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理进阶（六）：第六章：电商后台 - 运营与交易管理",postAI:"true",pageFillDescription:"第六章：电商后台 - 运营与交易管理, 6.1 学习目标, 6.2 营销位管理, 6.2.1 首页Banner管理, 6.2.2 推荐位管理, 6.2.3 活动会场管理, 6.3 支付管理, 6.3.1 支付渠道管理, 1. 支付渠道列表, 2. 支付渠道配置, 6.3.2 交易流水与对账（拓展设计）, 6.4 订单管理, 6.4.1 商家端订单管理, 1. 强大的查询与筛选区, 2. 按状态分类的工作队列, 3. 结构化的订单信息列表, 4. 丰富的操作项, 5. 订单详情与状态流转, 6. 异常订单与售后订单管理, 6.4.2 平台端订单管理, 1. 核心设计差异, 2. 平台端订单列表, 3. 平台端订单详情, 6.5 订单统计, 6.5.1 核心统计维度, 1. 交易维度 - 我们生意做得怎么样？, 核心指标及解读：, 💡 支付转化率的两种计算方式：, ✅ 1. 订单转化率, ✅ 2. 支付成功率, 2. 商品维度 - 我们什么东西卖得好？, 核心指标及解读：, 3. 订单来源维度 - 我们的客人从哪里来？, 6.6 评价管理, 6.6.1 评价列表与审核, 6.6.2 评价申诉处理, 6.7 本章总结第六章电商后台运营与交易管理学习目标在本章中我的核心目标是带大家掌握电商运营后台最核心的几个模块的设计我们将学习如何设计营销位管理系统如支付与订单管理系统以及评价管理系统为我们的运营同事打造一套强大高效的指挥中心营销位管理我们已经在用户端首页为金刚区等营销模块预留了最好的广告位但这些广告位里具体要展示什么内容链接到哪里去什么时候上下线这些都不能由程序员写死在代码里而必须由我们的运营同事在后台进行灵活实时的配置营销位管理后台就是我们为运营同事设计的这个展位内容配置中心首页管理首页是我们平台最宝贵最核心的流量入口我必须为它设计一套功能完善的后台管理系统我设计这个后台会从前台的用户体验反推后台需要具备的功能前台需要图片能轮播有顺序能跳转运营需要能定期自动地更换推导出后台功能基于此我设计的后台就必须包含列表新增编辑审核和自动上下线等核心功能新增编辑的表单是这个模块的核心我的设计会包含以下配置项配置项我的设计说明名称这是给运营看的内部名称方便识别例如年双十一主会场封面图提供图片上传入口我会在中严格规定图片的尺寸大小和格式确保前端展示效果状态提供一个上线下线的开关运营可以手动控制该是否展示起止时间这是最重要的功能提供日期和时间选择器让运营可以预设的自动上线时间和自动下线时间实现无人值守的活动更新跳转页面即用户点击这张后会跳转到哪里显示顺序一个数字输入框用来控制这张在首页轮播图中的排序数字越小排得越前对于跳转页面这个配置项我设计的后台至少要支持两种链接类型链接运营可以填写一个任意的网址原生页面链接运营可以选择跳转到内部的某个特定页面如某个商品详情页某个分类列表页所有配置好的都会在一个列表中进行统一的管理和查看推荐位管理除了首页我们的中还有很多其他的推荐位比如首页金刚区商品详情页的为你推荐等如果我为每一个推荐位都设计一个独立的后台那整个系统将变得无比臃肿和混乱因此我的设计思路是建立一个统一的平台级的活动管理中心用一套通用的逻辑来管理所有不同位置的推荐内容我们看到的这张电子渠道运营管理平台的截图就是一个很好的例子它将首页消息推送等所有运营活动都放在一个列表中进行管理我设计的新建编辑推荐位内容的后台表单会包含以下这些核心的可复用的配置项配置项我的设计说明活动推荐名称给本次推荐内容起一个内部识的别名称运营位显示位置这是最重要的字段我会提供两个下拉框让运营同事可以精确地选择这条内容要投放到哪一个页面运营位的哪一个具体位置显示位置比如运营位首页显示位置金刚区第位权重排序值一个数字当同一个推荐位下有多条内容同时在线时系统会根据这个值的大小来决定它们的显示顺序内容元素提供图片上传标题和副标题输入框跳转链接配置用户点击这个推荐位后要跳转的目标页面或原生页面排期提供开始时间和结束时间选择器用于内容的自动上下线审核流程所有新增或修改的推荐内容都必须经过提交审核的操作由上级主管审批通过后才能正式发布上线确保安全活动会场管理活动会场是一个比单个推荐位要复杂得多的营销模块它通常是一个由多个不同模块如商品列表优惠券入口等聚合而成的专题页面要实现这种高度灵活的页面我需要为我的运营同事设计一个可视化的页面搭建工具也就是一个轻量级的内容管理系统我们看到的这张复杂业务管理的后台截图就是一个很好的范例我设计一个活动会场搭建后台会包含以下核心思路基础信息配置在表单的顶部让运营可以先设置好整个会场的活动名称开始结束时间分享标题并上传最顶部的头图模块化组件我会预先定义好一套标准组件库运营同事可以像搭积木一样自由地选择和组合这些组件来搭建自己的页面我的拓展设计常见组件这个组件库通常会包括商品列表组件运营可以选择一批商品并选择不同的展示样式如一行两个列表等优惠券组件运营可以关联几张优惠券让用户在页面上直接领取图片组件可以上传任意图片作为楼层分隔或视觉点缀文本组件可以添加富文本内容可视化编排一个更理想的设计是让运营可以通过拖拽的方式来自由地调整页面上各个组件的上下顺序统一的管理与审核所有搭建好的活动会场页面都会生成一个唯一的运营同事可以将这个配置到我们节设计的推荐位的跳转链接中去从而实现引流支付管理我们在节已经从技术实现的视角探讨了如何接入多种支付方式现在我们回到平台运营的视角来思考当我们的平台已经同时接入了微信支付支付宝银联等多种渠道后我就必须为我的运营和财务同事提供一个后台来管理这些支付渠道比如因为某个渠道的费率调整或系统维护我们需要暂时关闭它这个操作就必须能在后台一键完成支付渠道管理这是支付管理后台的核心我设计的这套功能能让运营同事像控制开关一样灵活地管理前端向用户展示的支付方式支付渠道列表这个模块的首页是一个支付渠道列表它清晰地列出了我们平台已经完成技术对接的所有支付渠道列表字段我的设计说明支付方式清晰地展示支付渠道的名称和官方图标状态这是最重要的运营开关运营同事可以通过一个启用禁用的开关来实时控制这个支付渠道是否在前台对用户可见排序一个数字输入框运营可以通过调整数字的大小来控制各个支付方式在前台收银台的显示顺序操作提供一个配置按钮点击后可以进入该渠道的参数配置页支付渠道配置点击配置后就会进入支付渠道配置页这个页面的核心作用是为我们的技术人员提供一个安全地结构化地存储和管理各个支付渠道凭证的地方正如我们在节提到的我们从微信支付宝等官方申请下来的商户资质会包含一系列用于通信的账号密码这个配置页就是用来填写和保存它们的我们应用在支付渠道的唯一标识商户号我们公司的商家身份编号商户证书密钥用于我们服务器与支付渠道服务器之间进行安全加密通信的密码通过这套列表控制开关配置填写参数的设计我就将复杂的技术对接过程与日常的运营开关过程完美地解耦了交易流水与对账拓展设计一个更完整的支付管理后台除了上述的渠道管理还应该包含以下两个核心模块交易流水查询我需要设计一个功能让我的财务和客服同事可以查询到通过我们平台的每一笔支付记录这个流水列表需要支持按订单号用户交易时间支付渠道支付状态等多个维度进行复杂的查询和筛选对账与结算管理这是一个更高级的财务功能我需要设计一个自动对账的系统它能每天自动地从微信支付宝等渠道拉取官方的结算账单然后与我们自己系统的订单记录进行逐条的自动化的比对并将差异项标记出来供财务人员处理这能极大地提升我们公司的财务管理效率订单管理我们已经设计了营销和支付当用户完成支付后一个订单数据就正式诞生了订单是连接用户商家平台三方的契约是整个电商交易流程中最核心最根本的信息载体订单管理模块就是我们为不同角色提供的查看和操作这份契约的后台功能它的设计必须同时服务于商家履约和用户查询商家端订单管理我们先设计商家端的订单管理后台它的核心目标是为商家内部的运营仓储打包等多个协同部门提供一套高效准确流程化的线上作业工具帮助他们顺利地完成从接收订单到发货的全过程强大的查询与筛选区为了应对商家在日常运营中需要从海量订单里寻找特定订单的复杂场景如处理客诉核对问题订单我必须在页面的最顶部设计一个功能极其强大的查询与筛选区订单列表是商家运营人员每天上班后第一个要打开的页面它是所有订单处理工作的总任务看板筛选字段我的设计说明订单编号支持按唯一的订单进行精准查询商品名称编码支持按商品维度查找所有包含该商品的订单下单时间提供日期范围选择器方便商家查询特定时间段内的订单客户姓名电话账号当出现客诉时方便客服人员快速定位到该用户的所有订单支付方式订单来源便于财务或运营人员进行渠道和业务来源的数据分析按状态分类的工作队列在筛选区的下方我会设计一组状态页它的作用是为商家预设好最高频使用的筛选器将订单列表直接划分为几个独立的工作队列待出库这是商家最重要的工作队列所有已付款等待打包发货的订单都会在这里出现未付款商家可以在这里看到那些已经拍下但还未付款的订单可以进行催付等运营操作已出库已完成已取消方便商家查询历史订单和问题订单结构化的订单信息列表列表的主体部分我会以一个订单包裹为一行进行结构化的信息展示确保商家能在一行内获取到这笔订单最核心的信息核心信息如截图所示必须包含商品信息缩略图标题单价数量货款金额下单账号订单状态等物流信息对于已发货的订单需要清晰地展示快递公司和快递单号丰富的操作项列表最右侧的操作列是商家进行订单处理的核心入口我需要根据订单的不同状态提供对应的高频操作订单详情这是所有状态下都必须有的入口出库这是待出库状态下最核心的操作点击后会进入发货操作流程如填写快递单号修改物流单修改地址这是为应对异常情况提供的必要补救功能延迟发货提醒当商家无法按时发货时可以通过这个功能主动向用户发送一条安抚性的提醒订单详情与状态流转点击订单列表中的任一订单即可进入订单详情页这是关于这笔交易的唯一事实凭证它必须完整清晰地展示所有相关信息订单的生命周期是由订单状态来驱动的在商家后台商家执行的每一个核心操作都会驱动订单状态向前流转例如当商家在后台点击发货并填写完快递单号后这笔订单的状态就会从待发货自动变更为待收货异常订单与售后订单管理异常订单处理我需要为商家设计处理异常情况的功能比如因库存不足而需要拒单或在发货前应用户要求修改订单特别是收货地址售后订单管理当用户发起退款退货申请时这些申请需要进入商家后台的一个独立售后订单工作队列中供商家进行同意退款拒绝申请等操作平台端订单管理对于平台而言它也有订单管理模块现在我们来设计我们自己内部运营和客服同事使用的平台端订单管理后台核心设计差异我设计平台端与设计商家端的核心思路差异在于视角不同商家端只能看到自己的订单而平台端必须能看到全平台所有商家的订单职责不同商家的核心职责是操作如发货而我们平台的核心职责更多的是监督和客服仲裁平台端订单列表基于上述差异我设计的平台端订单列表与商家端相比有以下不同增加店铺维度在列表的筛选器和表头中我必须增加店铺名称这一维度这能让我的客服同事在接到用户电话时可以快速定位到是哪个店铺的订单出了问题简化操作项在操作列平台端的操作会更精简主要以查看订单详情为主而不会包含发货这类应由商家执行的操作平台端订单详情平台端的订单详情页在信息展示上与商家端需要保持完整和一致我的设计我会额外在页面顶部增加一个店铺信息模块清晰地展示出这笔订单所属商家的店铺名称联系方式等当出现交易纠纷时这能方便我的客服同事快速地联系上对应的商家进行沟通和处理订单统计订单管理解决的是处理单笔交易的问题而订单统计解决的是洞察整体业务的问题我设计的订单统计模块其核心目标是为我们的运营市场和管理团队提供一个数据驱动的决策中心它不是简单的数据罗列而是要能通过数据清晰地回答三个核心的业务问题我们的生意做得怎么样交易维度我们什么商品卖得好商品维度我们的客人从哪里来订单来源维度核心统计维度在设计数据看板时我会围绕三个核心维度分别构建不同的数据分析模块交易维度我们生意做得怎么样这是最高层级的模块用于评估平台整体经营状况核心指标及解读核心指标我的解读订单销售额即特定时间段内用户下单的总金额是衡量平台体量的最核心指标订单量特定时间段内的总订单数量客单价总销售额总订单数反映用户的平均购买力下单支付用户数两者的对比可计算出支付转化率评估下单后支付流程是否顺畅订单金额分布将订单金额分为如元元等区间帮助分析用户的核心消费力区间新老客交易构成分析新客和老客各自贡献的销售额与订单量是衡量平台用户健康度的重要指标支付转化率的两种计算方式订单转化率定义衡量完成支付的独立访客占总访客的比例计算公式订单转化率完成支付的订单数访问网站的总用户数解释完成支付的订单数指成功付款的订单数量访问总用户数独立访客或会话数依据数据分析工具适用场景衡量从访问到购买的整体效率反映电商平台的直接销售能力支付成功率定义衡量成功支付交易占尝试支付交易的比例计算公式支付成功率成功完成支付的交易数发起支付的交易总数解释成功支付交易数用户付款成功的交易发起支付交易数用户点击支付后实际发起的请求无论成功与否适用场景用于评估支付接口或渠道的稳定性和用户体验二者对比如下订单转化率衡量用户意愿与行为支付成功率衡量支付能力实现与顺畅程度两个指标都很关键分别代表转化意愿和支付执行的两个环节商品维度我们什么东西卖得好这个模块帮助我与品类运营同事洞察商品表现优化选品与库存核心指标及解读核心指标我的解读商品浏览量商品详情页被浏览的次数反映商品的吸引力商品销量商品的实际销售数量反映市场接受度商品转化率商品支付订单数商品浏览量是衡量商品从吸引到成交的黄金指标加购物车数反映了用户对该商品的潜在兴趣我会基于这些数据设计如热销商品排行榜高转化率商品榜品类销售额占比等数据榜单订单来源维度我们的客人从哪里来这个模块帮助我与市场运营同事评估流量渠道与营销活动效果以优化预算投入核心指标我的解读用户渠道来源分析用户产生订单时其最初来自哪个渠道如商城端微信小程序等转化入口来源分析用户通过哪个营销位下单如首页活动入口等用于计算每个广告位的投入产出比评价管理我们都知道在电商购物中用户评价是影响购买决策的最重要的因素之一它是一种强大的社会认同机制因此为平台设计一套公平透明高效的评价管理体系是我工作的重中之重我设计评价管理后台需要同时服务于商家和平台这两个角色他们的核心需求是不同的商家更关注用户反馈希望管理店铺声誉平台更关注内容监管希望维护社区环境的健康因此我通常会为他们设计两套功能各有侧重的后台评价列表与审核首先我需要明确评价的入口时机在我的流程设计中只有当订单状态流转为待评价通常在用户确认收货后用户端才会出现评价按钮用户提交评价后订单状态则变为已完成形成闭环平台端内容审核与监管用户提交的所有评价都会首先进入我们平台端的评价管理后台核心职责平台运营的核心职责是内容审核他们需要快速地筛选和处理所有评价特别是检查其中是否包含敏感词汇违规图片等不良信息核心功能因此平台端的评价列表功能相对纯粹列表需要展示订单编号用户昵称评价内容等全局信息而最核心的操作就是删除对于违规的评价平台拥有最高权限可以将其直接删除商家端查看与互动当一条评价通过了平台的审核它才会出现在商家端的评价管理后台核心职责商家的核心职责是与用户互动维护自己的店铺声誉核心功能因此商家端的后台操作更丰富回复商家可以回复用户的评价这是最重要的客户关系维护功能置顶商家可以将优质的图文并茂的好评在自己商品详情页的评价区进行置顶作为买家秀的典范申诉当商家认为自己遭遇了恶意差评时可以向平台发起申诉评价申诉处理评价申诉是我为保障商家权益设计的平台仲裁流程商家发起申诉商家在后台点击某条差评旁的申诉按钮提交申诉理由我会设计一个弹窗让商家可以填写申诉的理由并上传相关的证据如与用户的聊天记录截图平台介入仲裁这条申诉会进入我们平台端后台的一个申诉处理队列中平台做出判决由我们的平台运营作为中立的第三方来对申诉进行判决最终的判决结果可能是维持原评价也可能是隐藏删除评价这个申诉流程是维护平台公平公正调解用户与商家矛盾的关键机制本章总结在本章我们完整地设计了电商后台中负责让生意转起来的几个核心运营与交易模块设计模块我的核心设计思考营销位管理为运营同事提供了可以灵活配置首页推荐位活动会场等营销资源的弹药库支付管理我们设计了后台的支付渠道开关并了解了独立对接与聚合支付在技术和商务上的区别订单管理我们分别为商家端侧重履约操作和平台端侧重全局监督设计了功能职责各有侧重的订单管理后台订单统计我们为平台设计了一个包含交易商品来源三大维度的数据驾驶舱用于业务的宏观洞察评价管理我们为平台设计了内容审核后台为商家设计了互动与申诉后台共同维护了一个健康的评价生态",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-25 11:05:48",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%85%AD%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0-%E8%BF%90%E8%90%A5%E4%B8%8E%E4%BA%A4%E6%98%93%E7%AE%A1%E7%90%86"><span class="toc-text">第六章：电商后台 - 运营与交易管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#6-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">6.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-2-%E8%90%A5%E9%94%80%E4%BD%8D%E7%AE%A1%E7%90%86"><span class="toc-text">6.2 营销位管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-2-1-%E9%A6%96%E9%A1%B5Banner%E7%AE%A1%E7%90%86"><span class="toc-text">6.2.1 首页Banner管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-2-2-%E6%8E%A8%E8%8D%90%E4%BD%8D%E7%AE%A1%E7%90%86"><span class="toc-text">6.2.2 推荐位管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-2-3-%E6%B4%BB%E5%8A%A8%E4%BC%9A%E5%9C%BA%E7%AE%A1%E7%90%86"><span class="toc-text">6.2.3 活动会场管理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-3-%E6%94%AF%E4%BB%98%E7%AE%A1%E7%90%86"><span class="toc-text">6.3 支付管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-3-1-%E6%94%AF%E4%BB%98%E6%B8%A0%E9%81%93%E7%AE%A1%E7%90%86"><span class="toc-text">6.3.1 支付渠道管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%94%AF%E4%BB%98%E6%B8%A0%E9%81%93%E5%88%97%E8%A1%A8"><span class="toc-text">1. 支付渠道列表</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%94%AF%E4%BB%98%E6%B8%A0%E9%81%93%E9%85%8D%E7%BD%AE"><span class="toc-text">2. 支付渠道配置</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-3-2-%E4%BA%A4%E6%98%93%E6%B5%81%E6%B0%B4%E4%B8%8E%E5%AF%B9%E8%B4%A6%EF%BC%88%E6%8B%93%E5%B1%95%E8%AE%BE%E8%AE%A1%EF%BC%89"><span class="toc-text">6.3.2 交易流水与对账（拓展设计）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-4-%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86"><span class="toc-text">6.4 订单管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-4-1-%E5%95%86%E5%AE%B6%E7%AB%AF%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86"><span class="toc-text">6.4.1 商家端订单管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BC%BA%E5%A4%A7%E7%9A%84%E6%9F%A5%E8%AF%A2%E4%B8%8E%E7%AD%9B%E9%80%89%E5%8C%BA"><span class="toc-text">1. 强大的查询与筛选区</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%8C%89%E7%8A%B6%E6%80%81%E5%88%86%E7%B1%BB%E7%9A%84%E5%B7%A5%E4%BD%9C%E9%98%9F%E5%88%97"><span class="toc-text">2. 按状态分类的工作队列</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%BB%93%E6%9E%84%E5%8C%96%E7%9A%84%E8%AE%A2%E5%8D%95%E4%BF%A1%E6%81%AF%E5%88%97%E8%A1%A8"><span class="toc-text">3. 结构化的订单信息列表</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E4%B8%B0%E5%AF%8C%E7%9A%84%E6%93%8D%E4%BD%9C%E9%A1%B9"><span class="toc-text">4. 丰富的操作项</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-%E8%AE%A2%E5%8D%95%E8%AF%A6%E6%83%85%E4%B8%8E%E7%8A%B6%E6%80%81%E6%B5%81%E8%BD%AC"><span class="toc-text">5. 订单详情与状态流转</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#6-%E5%BC%82%E5%B8%B8%E8%AE%A2%E5%8D%95%E4%B8%8E%E5%94%AE%E5%90%8E%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86"><span class="toc-text">6. 异常订单与售后订单管理</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-4-2-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86"><span class="toc-text">6.4.2 平台端订单管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1%E5%B7%AE%E5%BC%82"><span class="toc-text">1. 核心设计差异</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%A2%E5%8D%95%E5%88%97%E8%A1%A8"><span class="toc-text">2. 平台端订单列表</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%A2%E5%8D%95%E8%AF%A6%E6%83%85"><span class="toc-text">3. 平台端订单详情</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-5-%E8%AE%A2%E5%8D%95%E7%BB%9F%E8%AE%A1"><span class="toc-text">6.5 订单统计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-5-1-%E6%A0%B8%E5%BF%83%E7%BB%9F%E8%AE%A1%E7%BB%B4%E5%BA%A6"><span class="toc-text">6.5.1 核心统计维度</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%A4%E6%98%93%E7%BB%B4%E5%BA%A6-%E2%80%9C%E6%88%91%E4%BB%AC%E7%94%9F%E6%84%8F%E5%81%9A%E5%BE%97%E6%80%8E%E4%B9%88%E6%A0%B7%EF%BC%9F%E2%80%9D"><span class="toc-text">1. 交易维度 - “我们生意做得怎么样？”</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%8C%87%E6%A0%87%E5%8F%8A%E8%A7%A3%E8%AF%BB%EF%BC%9A"><span class="toc-text">核心指标及解读：</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%F0%9F%92%A1-%E6%94%AF%E4%BB%98%E8%BD%AC%E5%8C%96%E7%8E%87%E7%9A%84%E4%B8%A4%E7%A7%8D%E8%AE%A1%E7%AE%97%E6%96%B9%E5%BC%8F%EF%BC%9A"><span class="toc-text">💡 支付转化率的两种计算方式：</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E2%9C%85-1-%E8%AE%A2%E5%8D%95%E8%BD%AC%E5%8C%96%E7%8E%87"><span class="toc-text">✅ 1. 订单转化率</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E2%9C%85-2-%E6%94%AF%E4%BB%98%E6%88%90%E5%8A%9F%E7%8E%87"><span class="toc-text">✅ 2. 支付成功率</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%95%86%E5%93%81%E7%BB%B4%E5%BA%A6-%E2%80%9C%E6%88%91%E4%BB%AC%E4%BB%80%E4%B9%88%E4%B8%9C%E8%A5%BF%E5%8D%96%E5%BE%97%E5%A5%BD%EF%BC%9F%E2%80%9D"><span class="toc-text">2. 商品维度 - “我们什么东西卖得好？”</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%8C%87%E6%A0%87%E5%8F%8A%E8%A7%A3%E8%AF%BB%EF%BC%9A-1"><span class="toc-text">核心指标及解读：</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E8%AE%A2%E5%8D%95%E6%9D%A5%E6%BA%90%E7%BB%B4%E5%BA%A6-%E2%80%9C%E6%88%91%E4%BB%AC%E7%9A%84%E5%AE%A2%E4%BA%BA%E4%BB%8E%E5%93%AA%E9%87%8C%E6%9D%A5%EF%BC%9F%E2%80%9D"><span class="toc-text">3. 订单来源维度 - “我们的客人从哪里来？”</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-6-%E8%AF%84%E4%BB%B7%E7%AE%A1%E7%90%86"><span class="toc-text">6.6 评价管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-6-1-%E8%AF%84%E4%BB%B7%E5%88%97%E8%A1%A8%E4%B8%8E%E5%AE%A1%E6%A0%B8"><span class="toc-text">6.6.1 评价列表与审核</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-6-2-%E8%AF%84%E4%BB%B7%E7%94%B3%E8%AF%89%E5%A4%84%E7%90%86"><span class="toc-text">6.6.2 评价申诉处理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-7-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">6.7 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5f2a23">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#277340">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#c72008">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#11a7a2">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#276d10">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#6d6a95">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理进阶（六）：第六章：电商后台 - 运营与交易管理</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-24T13:13:45.000Z" title="发表于 2025-07-24 21:13:45">2025-07-24</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.638Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">6.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>19分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理进阶（六）：第六章：电商后台 - 运营与交易管理"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/37507.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/37507.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理进阶（六）：第六章：电商后台 - 运营与交易管理</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-24T13:13:45.000Z" title="发表于 2025-07-24 21:13:45">2025-07-24</time><time itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.638Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></header><div id="postchat_postcontent"><h1 id="第六章：电商后台-运营与交易管理"><a href="#第六章：电商后台-运营与交易管理" class="headerlink" title="第六章：电商后台 - 运营与交易管理"></a>第六章：电商后台 - 运营与交易管理</h1><h2 id="6-1-学习目标"><a href="#6-1-学习目标" class="headerlink" title="6.1 学习目标"></a>6.1 学习目标</h2><p>在本章中，我的核心目标是，带大家掌握电商运营后台最核心的几个模块的设计。我们将学习如何设计<strong>营销位管理</strong>系统（如Banner）、<strong>支付与订单</strong>管理系统，以及<strong>评价管理</strong>系统，为我们的运营同事，打造一套强大、高效的“指挥中心”。</p><h2 id="6-2-营销位管理"><a href="#6-2-营销位管理" class="headerlink" title="6.2 营销位管理"></a>6.2 营销位管理</h2><p>我们已经在用户端首页，为Banner、金刚区等营销模块，预留了最好的“广告位”。但这些广告位里，<strong>具体要展示什么内容？链接到哪里去？什么时候上下线？</strong> 这些都不能由程序员写死在代码里，而必须由我们的运营同事，在后台进行灵活、实时的配置。</p><p><strong>营销位管理</strong>后台，就是我们为运营同事，设计的这个“<strong>展位内容配置中心</strong>”。</p><h3 id="6-2-1-首页Banner管理"><a href="#6-2-1-首页Banner管理" class="headerlink" title="6.2.1 首页Banner管理"></a>6.2.1 首页Banner管理</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100716311.png" alt="image-20250723100716311"></p><p>首页Banner，是我们平台最宝贵、最核心的流量入口。我必须为它设计一套功能完善的后台管理系统。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100737050.png" alt="image-20250723100737050"></p><p>我设计这个后台，会从前台的用户体验，反推后台需要具备的功能。</p><ul><li><strong>前台需要</strong>：图片能轮播、有顺序、能跳转。</li><li><strong>运营需要</strong>：Banner能定期、自动地更换。</li><li><strong>推导出后台功能</strong>：基于此，我设计的后台，就必须包含<strong>Banner列表</strong>、<strong>Banner新增/编辑</strong>、<strong>审核</strong>和<strong>自动上下线</strong>等核心功能。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100808952.png" alt="image-20250723100808952"></p><p>“<strong>新增/编辑Banner</strong>”的表单，是这个模块的核心。我的设计会包含以下配置项：</p><table><thead><tr><th align="left"><strong>配置项</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><code>Banner名称</code></td><td align="left">这是给运营看的内部名称，方便识别。例如：“2025年双十一主会场Banner-1”。</td></tr><tr><td align="left"><code>封面图</code></td><td align="left">提供图片上传入口。我会在PRD中，严格规定<strong>图片的尺寸、大小和格式</strong>，确保前端展示效果。</td></tr><tr><td align="left"><code>Banner状态</code></td><td align="left">提供一个“<strong>上线/下线</strong>”的开关。运营可以手动控制该Banner是否展示。</td></tr><tr><td align="left"><code>起止时间</code></td><td align="left"><strong>这是最重要的功能</strong>。提供日期和时间选择器，让运营可以预设Banner的“<strong>自动上线时间</strong>”和“<strong>自动下线时间</strong>”，实现无人值守的活动更新。</td></tr><tr><td align="left"><code>跳转页面 (Link)</code></td><td align="left">即，用户点击这张Banner后，会跳转到哪里。</td></tr><tr><td align="left"><code>显示顺序</code></td><td align="left">一个数字输入框，用来控制这张Banner，在首页轮播图中的排序。数字越小，排得越前。</td></tr></tbody></table><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100835858.png" alt="image-20250723100835858"></p><p>对于“<strong>跳转页面</strong>”这个配置项，我设计的后台，至少要支持两种链接类型：</p><ol><li><strong>H5链接</strong>：运营可以填写一个任意的网址（URL）。</li><li><strong>原生页面链接</strong>：运营可以选择跳转到App内部的某个特定页面（如：某个商品详情页、某个分类列表页）。</li></ol><p>所有配置好的Banner，都会在一个列表中进行统一的管理和查看。</p><hr><h3 id="6-2-2-推荐位管理"><a href="#6-2-2-推荐位管理" class="headerlink" title="6.2.2 推荐位管理"></a>6.2.2 推荐位管理</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_004.png" alt="image_004"></p><p>除了首页Banner，我们的App中，还有很多其他的<strong>推荐位</strong>，比如首页金刚区、商品详情页的“为你推荐”等。</p><p>如果我为每一个推荐位，都设计一个独立的后台，那整个系统将变得无比臃肿和混乱。因此，我的设计思路是，建立一个<strong>统一的、平台级的“活动管理中心”</strong>，用一套通用的逻辑，来管理所有不同位置的推荐内容。</p><p>我们看到的这张“电子渠道运营管理平台”的截图，就是一个很好的例子。它将<code>首页</code>、<code>消息推送</code>、<code>banner</code>等所有运营活动，都放在一个列表中进行管理。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_006.png" alt="image_006"></p><p>我设计的“<strong>新建/编辑推荐位内容</strong>”的后台表单，会包含以下这些核心的、可复用的配置项：</p><table><thead><tr><th align="left"><strong>配置项</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><strong>活动/推荐名称</strong></td><td align="left">给本次推荐内容，起一个内部识的别名称。</td></tr><tr><td align="left"><strong>运营位 &amp; 显示位置</strong></td><td align="left"><strong>这是最重要的字段</strong>。我会提供两个下拉框，让运营同事，可以精确地选择，这条内容要投放到<strong>哪一个页面（运营位）<strong>的</strong>哪一个具体位置（显示位置）</strong>。比如：<code>运营位：首页</code>，<code>显示位置：金刚区-第2位</code>。</td></tr><tr><td align="left"><strong>权重/排序值</strong></td><td align="left">一个数字。当同一个推荐位下，有多条内容同时在线时，系统会根据这个值的大小，来决定它们的显示顺序。</td></tr><tr><td align="left"><strong>内容元素</strong></td><td align="left">提供<code>图片/ICON上传</code>、<code>标题</code>和<code>副标题</code>输入框。</td></tr><tr><td align="left"><strong>跳转链接</strong></td><td align="left">配置用户点击这个推荐位后，要跳转的目标页面（H5或App原生页面）。</td></tr><tr><td align="left"><strong>排期</strong></td><td align="left">提供“<strong>开始时间</strong>”和“<strong>结束时间</strong>”选择器，用于内容的自动上下线。</td></tr><tr><td align="left"><strong>审核流程</strong></td><td align="left">所有新增或修改的推荐内容，都必须经过“<strong>提交审核</strong>”的操作，由上级主管审批通过后，才能正式发布上线，确保安全。</td></tr></tbody></table><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_005.png" alt="image_005"></p><h3 id="6-2-3-活动会场管理"><a href="#6-2-3-活动会场管理" class="headerlink" title="6.2.3 活动会场管理"></a>6.2.3 活动会场管理</h3><p><strong>活动会场</strong>，是一个比单个“推荐位”，要复杂得多的营销模块。它通常是一个<strong>由多个不同模块（如Banner、商品列表、优惠券入口等）聚合而成的专题页面</strong>。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_008.png" alt="img"></p><p>要实现这种高度灵活的页面，我需要为我的运营同事，设计一个“<strong>可视化</strong>”的页面搭建工具，也就是一个轻量级的<strong>CMS（内容管理系统）</strong>。</p><p>我们看到的这张“复杂业务管理”的后台截图，就是一个很好的范例。我设计一个活动会场搭建后台，会包含以下核心思路：</p><ol><li><strong>基础信息配置</strong>：在表单的顶部，让运营可以先设置好整个会场的<code>活动名称</code>、<code>开始/结束时间</code>、<code>分享标题</code>，并上传最顶部的<code>头图</code>。</li><li><strong>模块化组件</strong>：我会预先定义好一套“<strong>标准组件库</strong>”，运营同事可以像“搭积木”一样，自由地选择和组合这些组件，来搭建自己的页面。<ul><li><strong>我的拓展设计（常见组件）</strong>：这个组件库通常会包括：<ul><li><code>商品列表组件</code>：运营可以选择一批商品，并选择不同的展示样式（如：一行两个、列表等）。</li><li><code>优惠券组件</code>：运营可以关联几张优惠券，让用户在页面上直接领取。</li><li><code>图片组件</code>：可以上传任意图片，作为楼层分隔或视觉点缀。</li><li><code>文本组件</code>：可以添加富文本内容。</li></ul></li></ul></li><li><strong>可视化编排</strong>：一个更理想的设计，是让运营可以通过“<strong>拖拽</strong>”的方式，来自由地调整页面上各个组件的上下顺序。</li><li><strong>统一的管理与审核</strong>：所有搭建好的活动会场页面，都会生成一个唯一的URL。运营同事可以将这个URL，配置到我们<code>6.2.2</code>节设计的“推荐位”的跳转链接中去，从而实现引流。</li></ol><hr><h2 id="6-3-支付管理"><a href="#6-3-支付管理" class="headerlink" title="6.3 支付管理"></a>6.3 支付管理</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723103028460.png" alt="image-20250723103028460"></p><p>我们在<code>3.5.2</code>节，已经从“技术实现”的视角，探讨了如何接入多种支付方式。现在，我们回到“<strong>平台运营</strong>”的视角来思考。</p><p>当我们的平台，已经同时接入了微信支付、支付宝、银联等多种渠道后，我就必须为我的运营和财务同事，提供一个后台，来<strong>管理这些支付渠道</strong>。比如，因为某个渠道的费率调整或系统维护，我们需要暂时关闭它，这个操作，就必须能在后台，一键完成。</p><h3 id="6-3-1-支付渠道管理"><a href="#6-3-1-支付渠道管理" class="headerlink" title="6.3.1 支付渠道管理"></a>6.3.1 支付渠道管理</h3><p>这是支付管理后台的核心。我设计的这套功能，能让运营同事，像控制“开关”一样，灵活地管理前端向用户展示的支付方式。</p><h4 id="1-支付渠道列表"><a href="#1-支付渠道列表" class="headerlink" title="1. 支付渠道列表"></a>1. 支付渠道列表</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723103136091.png" alt="image-20250723103136091">这个模块的首页，是一个“<strong>支付渠道列表</strong>”。它清晰地列出了我们平台已经完成技术对接的所有支付渠道。</p><table><thead><tr><th align="left"><strong>列表字段</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><code>支付方式</code></td><td align="left">清晰地展示支付渠道的名称和官方图标。</td></tr><tr><td align="left"><strong><code>状态</code></strong></td><td align="left"><strong>这是最重要的运营开关</strong>。运营同事可以通过一个“<strong>启用/禁用</strong>”的开关，来实时控制这个支付渠道，是否在前台对用户可见。</td></tr><tr><td align="left"><code>排序</code></td><td align="left">一个数字输入框。运营可以通过调整数字的大小，来控制各个支付方式，在前台收银台的<strong>显示顺序</strong>。</td></tr><tr><td align="left"><strong><code>操作</code></strong></td><td align="left">提供一个“<strong>配置</strong>”按钮，点击后，可以进入该渠道的参数配置页。</td></tr></tbody></table><h4 id="2-支付渠道配置"><a href="#2-支付渠道配置" class="headerlink" title="2. 支付渠道配置"></a>2. 支付渠道配置</h4><p>点击“配置”后，就会进入“<strong>支付渠道配置页</strong>”。这个页面的核心作用，是为我们的技术人员，提供一个<strong>安全地、结构化地，存储和管理各个支付渠道API凭证</strong>的地方。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723103306061.png" alt="image-20250723103306061"></p><p>正如我们在<code>3.5.2</code>节提到的，我们从微信、支付宝等官方申请下来的商户资质，会包含一系列用于API通信的“账号密码”。这个配置页，就是用来填写和保存它们的。</p><ul><li><strong>AppID</strong>：我们应用在支付渠道的唯一标识。</li><li><strong>mchid (商户号)</strong>：我们公司的商家身份编号。</li><li><strong>商户API证书/密钥</strong>：用于我们服务器与支付渠道服务器之间，进行安全加密通信的“密码”。</li></ul><p>通过这套“<strong>列表（控制开关）+配置（填写参数）</strong>”的设计，我就将复杂的“技术对接”过程，与日常的“运营开关”过程，完美地解耦了。</p><h3 id="6-3-2-交易流水与对账（拓展设计）"><a href="#6-3-2-交易流水与对账（拓展设计）" class="headerlink" title="6.3.2 交易流水与对账（拓展设计）"></a>6.3.2 交易流水与对账（拓展设计）</h3><p>一个更完整的支付管理后台，除了上述的“渠道管理”，还应该包含以下两个核心模块：</p><ol><li><strong>交易流水查询</strong>：我需要设计一个功能，让我的财务和客服同事，可以查询到通过我们平台的<strong>每一笔</strong>支付记录。这个流水列表，需要支持按订单号、用户ID、交易时间、支付渠道、支付状态等多个维度，进行复杂的查询和筛选。</li><li><strong>对账与结算管理</strong>：这是一个更高级的财务功能。我需要设计一个“自动对账”的系统。它能每天自动地，从微信、支付宝等渠道，拉取官方的结算账单，然后与我们自己系统的订单记录，进行逐条的、自动化的比对，并将差异项标记出来，供财务人员处理。这能极大地提升我们公司的财务管理效率。</li></ol><hr><h2 id="6-4-订单管理"><a href="#6-4-订单管理" class="headerlink" title="6.4 订单管理"></a>6.4 订单管理</h2><p>我们已经设计了营销和支付。当用户完成支付后，一个“<strong>订单</strong>”数据就正式诞生了。订单，是连接用户、商家、平台三方的“<strong>契约</strong>”，是整个电商交易流程中，最核心、最根本的<strong>信息载体</strong>。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723105200805.png" alt="image-20250723105200805"></p><p><strong>订单管理</strong>模块，就是我们为不同角色，提供的查看和操作这份“契约”的后台功能。它的设计，必须同时服务于<strong>商家</strong>（履约）和<strong>用户</strong>（查询）。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723104828982.png" alt="image-20250723104828982"></p><h3 id="6-4-1-商家端订单管理"><a href="#6-4-1-商家端订单管理" class="headerlink" title="6.4.1 商家端订单管理"></a>6.4.1 商家端订单管理</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723104848091.png" alt="image-20250723104848091"></p><p>我们先设计<strong>商家端</strong>的订单管理后台。它的核心目标，是为商家内部的<strong>运营、仓储、打包</strong>等多个协同部门，提供一套<strong>高效、准确、流程化</strong>的线上作业工具，帮助他们顺利地完成从“<strong>接收订单</strong>”到“<strong>发货</strong>”的全过程。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723105002093.png" alt="image-20250723105002093"></p><h4 id="1-强大的查询与筛选区"><a href="#1-强大的查询与筛选区" class="headerlink" title="1. 强大的查询与筛选区"></a>1. 强大的查询与筛选区</h4><p>为了应对商家在日常运营中，需要从海量订单里寻找特定订单的复杂场景（如：处理客诉、核对问题订单），我必须在页面的最顶部，设计一个功能极其强大的“<strong>查询与筛选区</strong>”。</p><p><strong>订单列表</strong>，是商家运营人员每天上班后，第一个要打开的页面。它是所有订单处理工作的“<strong>总任务看板</strong>”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723105252266.png" alt="image-20250723105252266"></p><table><thead><tr><th align="left">筛选字段</th><th align="left">我的设计说明</th></tr></thead><tbody><tr><td align="left"><code>订单编号</code></td><td align="left">支持按唯一的订单ID，进行精准查询。</td></tr><tr><td align="left"><code>商品名称</code> / <code>SKU编码</code></td><td align="left">支持按商品维度，查找所有包含该商品的订单。</td></tr><tr><td align="left"><code>下单时间</code></td><td align="left">提供日期范围选择器，方便商家查询特定时间段内的订单。</td></tr><tr><td align="left"><code>客户姓名</code> / <code>电话</code> / <code>账号</code></td><td align="left">当出现客诉时，方便客服人员快速定位到该用户的所有订单。</td></tr><tr><td align="left"><code>支付方式</code> / <code>订单来源</code></td><td align="left">便于财务或运营人员，进行渠道和业务来源的数据分析。</td></tr></tbody></table><h4 id="2-按状态分类的工作队列"><a href="#2-按状态分类的工作队列" class="headerlink" title="2. 按状态分类的工作队列"></a>2. 按状态分类的工作队列</h4><p>在筛选区的下方，我会设计一组“<strong>状态Tab页</strong>”。它的作用，是为商家预设好最高频使用的筛选器，将订单列表，直接划分为几个独立的“<strong>工作队列</strong>”。</p><ul><li><strong><code>待出库</code></strong>：这是商家最重要的工作队列，所有已付款、等待打包发货的订单，都会在这里出现。</li><li><strong><code>未付款</code></strong>：商家可以在这里，看到那些已经拍下但还未付款的订单，可以进行催付等运营操作。</li><li><strong><code>已出库</code>、<code>已完成</code>、<code>已取消</code></strong>：方便商家查询历史订单和问题订单。</li></ul><h4 id="3-结构化的订单信息列表"><a href="#3-结构化的订单信息列表" class="headerlink" title="3. 结构化的订单信息列表"></a>3. 结构化的订单信息列表</h4><p>列表的主体部分，我会以“<strong>一个订单包裹</strong>”为一行，进行结构化的信息展示，确保商家能在一行内，获取到这笔订单最核心的信息。</p><ul><li><strong>核心信息</strong>：如截图所示，必须包含<code>商品信息</code>（缩略图、标题）、<code>单价/数量</code>、<code>货款金额</code>、<code>下单账号</code>、<code>订单状态</code>等。</li><li><strong>物流信息</strong>：对于已发货的订单，需要清晰地展示<code>快递公司</code>和<code>快递单号</code>。</li></ul><h4 id="4-丰富的操作项"><a href="#4-丰富的操作项" class="headerlink" title="4. 丰富的操作项"></a>4. 丰富的操作项</h4><p>列表最右侧的“<strong>操作</strong>”列，是商家进行订单处理的核心入口。我需要根据订单的不同状态，提供对应的高频操作。</p><ul><li><strong><code>订单详情</code></strong>：这是所有状态下，都必须有的入口。</li><li><strong><code>出库</code></strong>：这是“待出库”状态下，最核心的操作。点击后，会进入发货操作流程（如：填写快递单号）。</li><li><strong><code>修改物流单</code> / <code>修改地址</code></strong>：这是为应对异常情况，提供的必要“补救”功能。</li><li><strong><code>延迟发货提醒</code></strong>：当商家无法按时发货时，可以通过这个功能，主动向用户发送一条安抚性的提醒。</li></ul><h4 id="5-订单详情与状态流转"><a href="#5-订单详情与状态流转" class="headerlink" title="5. 订单详情与状态流转"></a>5. 订单详情与状态流转</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723111453299.png" alt="image-20250723111453299"><br>点击订单列表中的任一订单，即可进入<strong>订单详情页</strong>。这是关于这笔交易的“<strong>唯一事实凭证</strong>”，它必须完整、清晰地展示所有相关信息。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723111508768.png" alt="image-20250723111508768"><br>订单的生命周期，是由“<strong>订单状态</strong>”来驱动的。在商家后台，商家执行的每一个核心操作，都会驱动订单状态向前流转。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723111555824.png" alt="image-20250723111555824"><br>例如，当商家在后台点击“<strong>发货</strong>”并填写完快递单号后，这笔订单的状态，就会从<code>待发货</code>自动变更为<code>待收货</code>。</p><h4 id="6-异常订单与售后订单管理"><a href="#6-异常订单与售后订单管理" class="headerlink" title="6. 异常订单与售后订单管理"></a>6. 异常订单与售后订单管理</h4><ul><li><strong>异常订单处理</strong>：我需要为商家，设计处理异常情况的功能，比如因库存不足而需要“<strong>拒单</strong>”，或在发货前应用户要求“<strong>修改订单</strong>”（特别是收货地址）。</li><li><strong>售后订单管理</strong>：当用户发起“退款/退货”申请时，这些申请需要进入商家后台的一个独立“<strong>售后订单</strong>”工作队列中，供商家进行“<strong>同意退款</strong>”、“<strong>拒绝申请</strong>”等操作。</li></ul><hr><h3 id="6-4-2-平台端订单管理"><a href="#6-4-2-平台端订单管理" class="headerlink" title="6.4.2 平台端订单管理"></a>6.4.2 平台端订单管理</h3><p>“对于平台而言，它也有订单管理模块…”</p><p>现在，我们来设计我们自己<strong>内部运营和客服同事</strong>使用的<strong>平台端订单管理</strong>后台。</p><h4 id="1-核心设计差异"><a href="#1-核心设计差异" class="headerlink" title="1. 核心设计差异"></a>1. 核心设计差异</h4><p>我设计平台端，与设计商家端的核心思路差异在于：</p><ul><li><strong>视角不同</strong>：商家端只能看到<strong>自己的</strong>订单；而平台端，必须能看到<strong>全平台所有商家</strong>的订单。</li><li><strong>职责不同</strong>：商家的核心职责是<strong>操作</strong>（如：发货）；而我们平台的核心职责，更多的是<strong>监督</strong>和<strong>客服仲裁</strong>。</li></ul><h4 id="2-平台端订单列表"><a href="#2-平台端订单列表" class="headerlink" title="2. 平台端订单列表"></a>2. 平台端订单列表</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723112351314.png" alt="image-20250723112351314"></p><p>基于上述差异，我设计的平台端订单列表，与商家端相比，有以下不同：</p><ul><li><strong>增加“店铺”维度</strong>：在列表的筛选器和表头中，我必须增加“<strong>店铺名称</strong>”这一维度。这能让我的客服同事，在接到用户电话时，可以快速定位到是哪个店铺的订单出了问题。</li><li><strong>简化操作项</strong>：在“操作”列，平台端的操作会更精简。主要以“<strong>查看订单详情</strong>”为主，而不会包含“发货”这类应由商家执行的操作。</li></ul><h4 id="3-平台端订单详情"><a href="#3-平台端订单详情" class="headerlink" title="3. 平台端订单详情"></a>3. 平台端订单详情</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723112452278.png" alt="image-20250723112452278"></p><p>平台端的订单详情页，在信息展示上，与商家端需要保持完整和一致。</p><ul><li><strong>我的设计</strong>：我会额外在页面顶部，增加一个“<strong>店铺信息</strong>”模块，清晰地展示出这笔订单所属商家的<code>店铺名称</code>、<code>联系方式</code>等。当出现交易纠纷时，这能方便我的客服同事，快速地联系上对应的商家，进行沟通和处理。</li></ul><hr><h2 id="6-5-订单统计"><a href="#6-5-订单统计" class="headerlink" title="6.5 订单统计"></a>6.5 订单统计</h2><p>订单管理，解决的是“<strong>处理单笔交易</strong>”的问题。而订单统计，解决的是“<strong>洞察整体业务</strong>”的问题。</p><p>我设计的订单统计模块，其核心目标，是为我们的运营、市场和管理团队，提供一个<strong>数据驱动的决策中心</strong>。它不是简单的数据罗列，而是要能通过数据，清晰地回答三个核心的业务问题：</p><ol><li><strong>我们的“生意”做得怎么样？（交易维度）</strong></li><li><strong>我们什么“商品”卖得好？（商品维度）</strong></li><li><strong>我们的“客人”从哪里来？（订单来源维度）</strong></li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723113907620.png" alt="image-20250723113907620"></p><hr><h3 id="6-5-1-核心统计维度"><a href="#6-5-1-核心统计维度" class="headerlink" title="6.5.1 核心统计维度"></a>6.5.1 核心统计维度</h3><p>在设计数据看板时，我会围绕<strong>三个核心维度</strong>，分别构建不同的数据分析模块：</p><h4 id="1-交易维度-“我们生意做得怎么样？”"><a href="#1-交易维度-“我们生意做得怎么样？”" class="headerlink" title="1. 交易维度 - “我们生意做得怎么样？”"></a>1. 交易维度 - “我们生意做得怎么样？”</h4><p>这是<strong>最高层级</strong>的模块，用于评估平台整体经营状况。</p><h5 id="核心指标及解读："><a href="#核心指标及解读：" class="headerlink" title="核心指标及解读："></a>核心指标及解读：</h5><table><thead><tr><th><strong>核心指标</strong></th><th><strong>我的解读</strong></th></tr></thead><tbody><tr><td><strong>订单销售额 (GMV)</strong></td><td>即“GMV”，特定时间段内用户下单的总金额，是衡量平台体量的最核心指标。</td></tr><tr><td><strong>订单量</strong></td><td>特定时间段内的总订单数量。</td></tr><tr><td><strong>客单价 (AOV)</strong></td><td>总销售额 ÷ 总订单数，反映用户的平均购买力。</td></tr><tr><td><strong>下单/支付用户数</strong></td><td><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114340925.png" alt="下单与支付用户数对比图"><br>两者的对比可计算出“<strong>支付转化率</strong>”，评估下单后支付流程是否顺畅。</td></tr><tr><td><strong>订单金额分布</strong></td><td><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114628770.png" alt="订单金额分布图"><br>将订单金额分为如“0-50元”、“51-100元”等区间，帮助分析用户的核心消费力区间。</td></tr><tr><td><strong>新老客交易构成</strong></td><td><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114404565.png" alt="新老客交易构成图"><br>分析新客和老客各自贡献的销售额与订单量，是衡量平台用户健康度的重要指标。</td></tr></tbody></table><hr><h5 id="💡-支付转化率的两种计算方式："><a href="#💡-支付转化率的两种计算方式：" class="headerlink" title="💡 支付转化率的两种计算方式："></a>💡 支付转化率的两种计算方式：</h5><h6 id="✅-1-订单转化率"><a href="#✅-1-订单转化率" class="headerlink" title="✅ 1. 订单转化率"></a>✅ 1. 订单转化率</h6><p><strong>定义：</strong> 衡量<strong>完成支付的独立访客</strong>占<strong>总访客</strong>的比例。</p><p><strong>计算公式：</strong></p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">订单转化率 = (完成支付的订单数 ÷ 访问网站/App的总用户数) × 100%</span><br></pre></td></tr></tbody></table></figure><p><strong>解释：</strong></p><ul><li><strong>完成支付的订单数</strong>：指成功付款的订单数量。</li><li><strong>访问总用户数</strong>：独立访客或会话数（依据数据分析工具）。</li></ul><p><strong>适用场景：</strong> 衡量从访问到购买的<strong>整体效率</strong>，反映电商平台的直接销售能力。</p><hr><h6 id="✅-2-支付成功率"><a href="#✅-2-支付成功率" class="headerlink" title="✅ 2. 支付成功率"></a>✅ 2. 支付成功率</h6><p><strong>定义：</strong> 衡量<strong>成功支付交易</strong>占<strong>尝试支付交易</strong>的比例。</p><p><strong>计算公式：</strong></p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">支付成功率 = (成功完成支付的交易数 ÷ 发起支付的交易总数) × 100%</span><br></pre></td></tr></tbody></table></figure><p><strong>解释：</strong></p><ul><li><strong>成功支付交易数</strong>：用户付款成功的交易。</li><li><strong>发起支付交易数</strong>：用户点击支付后实际发起的请求（无论成功与否）。</li></ul><p><strong>适用场景：</strong> 用于评估<strong>支付接口或渠道的稳定性</strong>和用户体验。</p><hr><blockquote><p><code>二者对比如下：</code></p></blockquote><ul><li><strong>订单转化率</strong> → 衡量<strong>用户意愿与行为</strong>。</li><li><strong>支付成功率</strong> → 衡量<strong>支付能力实现与顺畅程度</strong>。</li></ul><p>两个指标都很关键，分别代表“转化意愿”和“支付执行”的两个环节。</p><hr><h4 id="2-商品维度-“我们什么东西卖得好？”"><a href="#2-商品维度-“我们什么东西卖得好？”" class="headerlink" title="2. 商品维度 - “我们什么东西卖得好？”"></a>2. 商品维度 - “我们什么东西卖得好？”</h4><p>这个模块帮助我与品类运营同事洞察商品表现，优化选品与库存。</p><h5 id="核心指标及解读：-1"><a href="#核心指标及解读：-1" class="headerlink" title="核心指标及解读："></a>核心指标及解读：</h5><table><thead><tr><th><strong>核心指标</strong></th><th><strong>我的解读</strong></th></tr></thead><tbody><tr><td><strong>商品浏览量 (PV)</strong></td><td>商品详情页被浏览的次数，反映商品的<strong>吸引力</strong>。</td></tr><tr><td><strong>商品销量</strong></td><td>商品的实际销售数量，反映<strong>市场接受度</strong>。</td></tr><tr><td><strong>商品转化率</strong></td><td>商品支付订单数 ÷ 商品浏览量，是衡量商品从吸引到成交的“<strong>黄金指标</strong>”。</td></tr><tr><td><strong>加购物车数</strong></td><td>反映了用户对该商品的“<strong>潜在兴趣</strong>”。</td></tr></tbody></table><p>我会基于这些数据设计如：</p><ul><li>热销商品排行榜</li><li>高转化率商品榜</li><li>品类销售额占比等数据榜单</li></ul><hr><h4 id="3-订单来源维度-“我们的客人从哪里来？”"><a href="#3-订单来源维度-“我们的客人从哪里来？”" class="headerlink" title="3. 订单来源维度 - “我们的客人从哪里来？”"></a>3. 订单来源维度 - “我们的客人从哪里来？”</h4><p>这个模块帮助我与市场运营同事评估<strong>流量渠道</strong>与<strong>营销活动</strong>效果，以优化预算投入。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114753992.png" alt="订单来源分析图"></p><table><thead><tr><th><strong>核心指标</strong></th><th><strong>我的解读</strong></th></tr></thead><tbody><tr><td><strong>用户渠道来源</strong></td><td>分析用户产生订单时，其最初来自哪个渠道：如 App、H5 商城、PC 端、微信小程序等。</td></tr><tr><td><strong>转化入口来源</strong></td><td>分析用户通过哪个“<strong>营销位</strong>”下单（如首页 Banner、活动入口等），用于计算每个广告位的 <strong>ROI（投入产出比）</strong>。</td></tr></tbody></table><hr><h2 id="6-6-评价管理"><a href="#6-6-评价管理" class="headerlink" title="6.6 评价管理"></a>6.6 评价管理</h2><p>我们都知道，在电商购物中，<strong>用户评价</strong>是影响购买决策的最重要的因素之一。它是一种强大的“<strong>社会认同（Social Proof）</strong>”机制。因此，为平台设计一套<strong>公平、透明、高效</strong>的评价管理体系，是我工作的重中之重。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723133910689.png" alt="image-20250723133910689"></p><p>我设计评价管理后台，需要同时服务于<strong>商家</strong>和<strong>平台</strong>这两个角色，他们的核心需求是不同的：</p><ul><li><strong>商家</strong>：更关注<strong>用户反馈</strong>，希望管理店铺声誉。</li><li><strong>平台</strong>：更关注<strong>内容监管</strong>，希望维护社区环境的健康。</li></ul><p>因此，我通常会为他们，设计两套功能各有侧重的后台。</p><h3 id="6-6-1-评价列表与审核"><a href="#6-6-1-评价列表与审核" class="headerlink" title="6.6.1 评价列表与审核"></a>6.6.1 评价列表与审核</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723133952088.png" alt="image-20250723133952088"></p><p>首先，我需要明确评价的<strong>入口时机</strong>。在我的流程设计中，只有当订单状态流转为“<strong>待评价</strong>”（通常在用户“确认收货”后），用户端才会出现“评价”按钮。用户提交评价后，订单状态则变为“<strong>已完成</strong>”，形成闭环。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723134151545.png" alt="image-20250723134151545"></p><p><strong>1. 平台端：内容审核与监管</strong><br>用户提交的所有评价，都会首先进入我们<strong>平台端的评价管理后台</strong>。</p><ul><li><strong>核心职责</strong>：平台运营的核心职责，是<strong>内容审核</strong>。他们需要快速地筛选和处理所有评价，特别是检查其中是否包含<strong>敏感词汇、违规图片</strong>等不良信息。</li><li><strong>核心功能</strong>：因此，平台端的评价列表，功能相对纯粹。列表需要展示<code>订单编号</code>、<code>用户昵称</code>、<code>评价内容</code>等全局信息。而最核心的操作，就是“<strong>删除</strong>”。对于违规的评价，平台拥有最高权限，可以将其直接删除。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723134141388.png" alt="image-20250723134141388"></p><p><strong>2. 商家端：查看与互动</strong><br>当一条评价通过了平台的审核，它才会出现在<strong>商家端的评价管理后台</strong>。</p><ul><li><strong>核心职责</strong>：商家的核心职责，是<strong>与用户互动，维护自己的店铺声誉</strong>。</li><li><strong>核心功能</strong>：因此，商家端的后台，操作更丰富：<ul><li><strong><code>回复</code></strong>：商家可以回复用户的评价，这是最重要的客户关系维护功能。</li><li><strong><code>置顶</code></strong>：商家可以将优质的、图文并茂的好评，在自己商品详情页的评价区进行置顶，作为“买家秀”的典范。</li><li><strong><code>申诉</code></strong>：当商家认为自己遭遇了恶意差评时，可以向平台发起申诉。</li></ul></li></ul><h3 id="6-6-2-评价申诉处理"><a href="#6-6-2-评价申诉处理" class="headerlink" title="6.6.2 评价申诉处理"></a>6.6.2 评价申诉处理</h3><p>“<strong>评价申诉</strong>”，是我为保障商家权益，设计的平台仲裁流程。</p><ul><li><strong>商家发起申诉</strong>：商家在后台，点击某条差评旁的“申诉”按钮。</li><li><strong>提交申诉理由</strong>：我会设计一个弹窗，让商家可以填写申诉的理由，并上传相关的证据（如：与用户的聊天记录截图）。</li><li><strong>平台介入仲裁</strong>：这条申诉，会进入我们平台端后台的一个“<strong>申诉处理</strong>”队列中。</li><li><strong>平台做出判决</strong>：由我们的平台运营，作为<strong>中立的第三方</strong>，来对申诉进行判决。最终的判决结果，可能是“<strong>维持原评价</strong>”，也可能是“<strong>隐藏/删除评价</strong>”。</li></ul><p>这个申诉流程，是维护平台公平公正、调解用户与商家矛盾的关键机制。</p><h2 id="6-7-本章总结"><a href="#6-7-本章总结" class="headerlink" title="6.7 本章总结"></a>6.7 本章总结</h2><p>在本章，我们完整地设计了电商后台中，负责“<strong>让生意转起来</strong>”的几个核心运营与交易模块。</p><table><thead><tr><th align="left"><strong>设计模块</strong></th><th align="left"><strong>我的核心设计思考</strong></th></tr></thead><tbody><tr><td align="left"><strong>营销位管理</strong></td><td align="left">为运营同事，提供了可以<strong>灵活配置</strong>首页<code>Banner</code>、<code>推荐位</code>、<code>活动会场</code>等营销资源的“弹药库”。</td></tr><tr><td align="left"><strong>支付管理</strong></td><td align="left">我们设计了后台的“<strong>支付渠道开关</strong>”，并了解了独立对接与<strong>聚合支付</strong>在技术和商务上的区别。</td></tr><tr><td align="left"><strong>订单管理</strong></td><td align="left">我们分别为<strong>商家端</strong>（侧重<strong>履约操作</strong>）和<strong>平台端</strong>（侧重<strong>全局监督</strong>），设计了功能职责各有侧重的订单管理后台。</td></tr><tr><td align="left"><strong>订单统计</strong></td><td align="left">我们为平台，设计了一个包含<strong>交易、商品、来源</strong>三大维度的“数据驾驶舱”，用于业务的宏观洞察。</td></tr><tr><td align="left"><strong>评价管理</strong></td><td align="left">我们为平台设计了<strong>内容审核</strong>后台，为商家设计了<strong>互动与申诉</strong>后台，共同维护了一个健康的评价生态。</td></tr></tbody></table><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/37507.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/37507.html&quot;)">产品经理进阶（六）：第六章：电商后台 - 运营与交易管理</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/37507.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理进阶（六）：第六章：电商后台 - 运营与交易管理&amp;url=https://prorise666.site/posts/37507.html&amp;pic=https://bu.dusays.com/2025/07/25/6882f31a48223.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/4512.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理进阶（五）：第五章：电商后台 - 商品管理</div></div></a></div><div class="next-post pull-right"><a href="/posts/32684.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理进阶（七）：第七章：售后管理</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理进阶（六）：第六章：电商后台 - 运营与交易管理",date:"2025-07-24 21:13:45",updated:"2025-07-25 11:05:48",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第六章：电商后台 - 运营与交易管理\n\n## 6.1 学习目标\n\n在本章中，我的核心目标是，带大家掌握电商运营后台最核心的几个模块的设计。我们将学习如何设计**营销位管理**系统（如Banner）、**支付与订单**管理系统，以及**评价管理**系统，为我们的运营同事，打造一套强大、高效的“指挥中心”。\n\n## 6.2 营销位管理\n\n我们已经在用户端首页，为Banner、金刚区等营销模块，预留了最好的“广告位”。但这些广告位里，**具体要展示什么内容？链接到哪里去？什么时候上下线？** 这些都不能由程序员写死在代码里，而必须由我们的运营同事，在后台进行灵活、实时的配置。\n\n**营销位管理**后台，就是我们为运营同事，设计的这个“**展位内容配置中心**”。\n\n### 6.2.1 首页Banner管理\n\n![image-20250723100716311](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100716311.png)\n\n首页Banner，是我们平台最宝贵、最核心的流量入口。我必须为它设计一套功能完善的后台管理系统。\n\n![image-20250723100737050](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100737050.png)\n\n我设计这个后台，会从前台的用户体验，反推后台需要具备的功能。\n* **前台需要**：图片能轮播、有顺序、能跳转。\n* **运营需要**：Banner能定期、自动地更换。\n* **推导出后台功能**：基于此，我设计的后台，就必须包含**Banner列表**、**Banner新增/编辑**、**审核**和**自动上下线**等核心功能。\n\n![image-20250723100808952](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100808952.png)\n\n“**新增/编辑Banner**”的表单，是这个模块的核心。我的设计会包含以下配置项：\n\n| **配置项** | **我的设计说明** |\n| :--- | :--- |\n| `Banner名称` | 这是给运营看的内部名称，方便识别。例如：“2025年双十一主会场Banner-1”。 |\n| `封面图` | 提供图片上传入口。我会在PRD中，严格规定**图片的尺寸、大小和格式**，确保前端展示效果。 |\n| `Banner状态`| 提供一个“**上线/下线**”的开关。运营可以手动控制该Banner是否展示。 |\n| `起止时间` | **这是最重要的功能**。提供日期和时间选择器，让运营可以预设Banner的“**自动上线时间**”和“**自动下线时间**”，实现无人值守的活动更新。 |\n| `跳转页面 (Link)` | 即，用户点击这张Banner后，会跳转到哪里。 |\n| `显示顺序`| 一个数字输入框，用来控制这张Banner，在首页轮播图中的排序。数字越小，排得越前。 |\n\n![image-20250723100835858](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723100835858.png)\n\n对于“**跳转页面**”这个配置项，我设计的后台，至少要支持两种链接类型：\n1.  **H5链接**：运营可以填写一个任意的网址（URL）。\n2.  **原生页面链接**：运营可以选择跳转到App内部的某个特定页面（如：某个商品详情页、某个分类列表页）。\n\n所有配置好的Banner，都会在一个列表中进行统一的管理和查看。\n\n\n\n---\n### 6.2.2 推荐位管理\n\n![image_004](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_004.png)\n\n除了首页Banner，我们的App中，还有很多其他的**推荐位**，比如首页金刚区、商品详情页的“为你推荐”等。\n\n如果我为每一个推荐位，都设计一个独立的后台，那整个系统将变得无比臃肿和混乱。因此，我的设计思路是，建立一个**统一的、平台级的“活动管理中心”**，用一套通用的逻辑，来管理所有不同位置的推荐内容。\n\n我们看到的这张“电子渠道运营管理平台”的截图，就是一个很好的例子。它将`首页`、`消息推送`、`banner`等所有运营活动，都放在一个列表中进行管理。\n\n![image_006](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_006.png)\n\n我设计的“**新建/编辑推荐位内容**”的后台表单，会包含以下这些核心的、可复用的配置项：\n\n| **配置项** | **我的设计说明** |\n| :--- | :--- |\n| **活动/推荐名称** | 给本次推荐内容，起一个内部识的别名称。 |\n| **运营位 & 显示位置** | **这是最重要的字段**。我会提供两个下拉框，让运营同事，可以精确地选择，这条内容要投放到**哪一个页面（运营位）**的**哪一个具体位置（显示位置）**。比如：`运营位：首页`，`显示位置：金刚区-第2位`。 |\n| **权重/排序值** | 一个数字。当同一个推荐位下，有多条内容同时在线时，系统会根据这个值的大小，来决定它们的显示顺序。 |\n| **内容元素** | 提供`图片/ICON上传`、`标题`和`副标题`输入框。 |\n| **跳转链接** | 配置用户点击这个推荐位后，要跳转的目标页面（H5或App原生页面）。 |\n| **排期** | 提供“**开始时间**”和“**结束时间**”选择器，用于内容的自动上下线。 |\n| **审核流程** | 所有新增或修改的推荐内容，都必须经过“**提交审核**”的操作，由上级主管审批通过后，才能正式发布上线，确保安全。 |\n\n![image_005](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_005.png)\n\n### 6.2.3 活动会场管理\n\n**活动会场**，是一个比单个“推荐位”，要复杂得多的营销模块。它通常是一个**由多个不同模块（如Banner、商品列表、优惠券入口等）聚合而成的专题页面**。\n\n![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image_008.png)\n\n要实现这种高度灵活的页面，我需要为我的运营同事，设计一个“**可视化**”的页面搭建工具，也就是一个轻量级的**CMS（内容管理系统）**。\n\n我们看到的这张“复杂业务管理”的后台截图，就是一个很好的范例。我设计一个活动会场搭建后台，会包含以下核心思路：\n\n1.  **基础信息配置**：在表单的顶部，让运营可以先设置好整个会场的`活动名称`、`开始/结束时间`、`分享标题`，并上传最顶部的`头图`。\n2.  **模块化组件**：我会预先定义好一套“**标准组件库**”，运营同事可以像“搭积木”一样，自由地选择和组合这些组件，来搭建自己的页面。\n    * **我的拓展设计（常见组件）**：这个组件库通常会包括：\n        * `商品列表组件`：运营可以选择一批商品，并选择不同的展示样式（如：一行两个、列表等）。\n        * `优惠券组件`：运营可以关联几张优惠券，让用户在页面上直接领取。\n        * `图片组件`：可以上传任意图片，作为楼层分隔或视觉点缀。\n        * `文本组件`：可以添加富文本内容。\n3.  **可视化编排**：一个更理想的设计，是让运营可以通过“**拖拽**”的方式，来自由地调整页面上各个组件的上下顺序。\n4.  **统一的管理与审核**：所有搭建好的活动会场页面，都会生成一个唯一的URL。运营同事可以将这个URL，配置到我们`6.2.2`节设计的“推荐位”的跳转链接中去，从而实现引流。\n\n\n---\n## 6.3 支付管理\n\n![image-20250723103028460](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723103028460.png)\n\n我们在`3.5.2`节，已经从“技术实现”的视角，探讨了如何接入多种支付方式。现在，我们回到“**平台运营**”的视角来思考。\n\n当我们的平台，已经同时接入了微信支付、支付宝、银联等多种渠道后，我就必须为我的运营和财务同事，提供一个后台，来**管理这些支付渠道**。比如，因为某个渠道的费率调整或系统维护，我们需要暂时关闭它，这个操作，就必须能在后台，一键完成。\n\n### 6.3.1 支付渠道管理\n\n这是支付管理后台的核心。我设计的这套功能，能让运营同事，像控制“开关”一样，灵活地管理前端向用户展示的支付方式。\n\n#### 1. 支付渠道列表\n\n![image-20250723103136091](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723103136091.png)这个模块的首页，是一个“**支付渠道列表**”。它清晰地列出了我们平台已经完成技术对接的所有支付渠道。\n\n| **列表字段** | **我的设计说明** |\n| :--- | :--- |\n| `支付方式` | 清晰地展示支付渠道的名称和官方图标。 |\n| **`状态`**| **这是最重要的运营开关**。运营同事可以通过一个“**启用/禁用**”的开关，来实时控制这个支付渠道，是否在前台对用户可见。 |\n| `排序` | 一个数字输入框。运营可以通过调整数字的大小，来控制各个支付方式，在前台收银台的**显示顺序**。 |\n| **`操作`**| 提供一个“**配置**”按钮，点击后，可以进入该渠道的参数配置页。 |\n\n#### 2. 支付渠道配置\n\n点击“配置”后，就会进入“**支付渠道配置页**”。这个页面的核心作用，是为我们的技术人员，提供一个**安全地、结构化地，存储和管理各个支付渠道API凭证**的地方。\n\n![image-20250723103306061](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723103306061.png)\n\n正如我们在`3.5.2`节提到的，我们从微信、支付宝等官方申请下来的商户资质，会包含一系列用于API通信的“账号密码”。这个配置页，就是用来填写和保存它们的。\n* **AppID**：我们应用在支付渠道的唯一标识。\n* **mchid (商户号)**：我们公司的商家身份编号。\n* **商户API证书/密钥**：用于我们服务器与支付渠道服务器之间，进行安全加密通信的“密码”。\n\n通过这套“**列表（控制开关）+配置（填写参数）**”的设计，我就将复杂的“技术对接”过程，与日常的“运营开关”过程，完美地解耦了。\n\n### 6.3.2 交易流水与对账（拓展设计）\n\n一个更完整的支付管理后台，除了上述的“渠道管理”，还应该包含以下两个核心模块：\n1.  **交易流水查询**：我需要设计一个功能，让我的财务和客服同事，可以查询到通过我们平台的**每一笔**支付记录。这个流水列表，需要支持按订单号、用户ID、交易时间、支付渠道、支付状态等多个维度，进行复杂的查询和筛选。\n2.  **对账与结算管理**：这是一个更高级的财务功能。我需要设计一个“自动对账”的系统。它能每天自动地，从微信、支付宝等渠道，拉取官方的结算账单，然后与我们自己系统的订单记录，进行逐条的、自动化的比对，并将差异项标记出来，供财务人员处理。这能极大地提升我们公司的财务管理效率。\n\n\n\n---\n## 6.4 订单管理\n\n我们已经设计了营销和支付。当用户完成支付后，一个“**订单**”数据就正式诞生了。订单，是连接用户、商家、平台三方的“**契约**”，是整个电商交易流程中，最核心、最根本的**信息载体**。\n\n![image-20250723105200805](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723105200805.png)\n\n**订单管理**模块，就是我们为不同角色，提供的查看和操作这份“契约”的后台功能。它的设计，必须同时服务于**商家**（履约）和**用户**（查询）。\n\n![image-20250723104828982](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723104828982.png)\n\n### 6.4.1 商家端订单管理\n\n![image-20250723104848091](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723104848091.png)\n\n我们先设计**商家端**的订单管理后台。它的核心目标，是为商家内部的**运营、仓储、打包**等多个协同部门，提供一套**高效、准确、流程化**的线上作业工具，帮助他们顺利地完成从“**接收订单**”到“**发货**”的全过程。\n\n![image-20250723105002093](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723105002093.png)\n\n#### 1. 强大的查询与筛选区\n\n为了应对商家在日常运营中，需要从海量订单里寻找特定订单的复杂场景（如：处理客诉、核对问题订单），我必须在页面的最顶部，设计一个功能极其强大的“**查询与筛选区**”。\n\n**订单列表**，是商家运营人员每天上班后，第一个要打开的页面。它是所有订单处理工作的“**总任务看板**”。\n\n![image-20250723105252266](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723105252266.png)\n\n| 筛选字段 | 我的设计说明 |\n| :--- | :--- |\n| `订单编号` | 支持按唯一的订单ID，进行精准查询。 |\n| `商品名称` / `SKU编码` | 支持按商品维度，查找所有包含该商品的订单。 |\n| `下单时间` | 提供日期范围选择器，方便商家查询特定时间段内的订单。 |\n| `客户姓名` / `电话` / `账号` | 当出现客诉时，方便客服人员快速定位到该用户的所有订单。 |\n| `支付方式` / `订单来源` | 便于财务或运营人员，进行渠道和业务来源的数据分析。 |\n\n#### 2. 按状态分类的工作队列\n\n在筛选区的下方，我会设计一组“**状态Tab页**”。它的作用，是为商家预设好最高频使用的筛选器，将订单列表，直接划分为几个独立的“**工作队列**”。\n* **`待出库`**：这是商家最重要的工作队列，所有已付款、等待打包发货的订单，都会在这里出现。\n* **`未付款`**：商家可以在这里，看到那些已经拍下但还未付款的订单，可以进行催付等运营操作。\n* **`已出库`、`已完成`、`已取消`**：方便商家查询历史订单和问题订单。\n\n#### 3. 结构化的订单信息列表\n\n列表的主体部分，我会以“**一个订单包裹**”为一行，进行结构化的信息展示，确保商家能在一行内，获取到这笔订单最核心的信息。\n* **核心信息**：如截图所示，必须包含`商品信息`（缩略图、标题）、`单价/数量`、`货款金额`、`下单账号`、`订单状态`等。\n* **物流信息**：对于已发货的订单，需要清晰地展示`快递公司`和`快递单号`。\n\n#### 4. 丰富的操作项\n\n列表最右侧的“**操作**”列，是商家进行订单处理的核心入口。我需要根据订单的不同状态，提供对应的高频操作。\n* **`订单详情`**：这是所有状态下，都必须有的入口。\n* **`出库`**：这是“待出库”状态下，最核心的操作。点击后，会进入发货操作流程（如：填写快递单号）。\n* **`修改物流单` / `修改地址`**：这是为应对异常情况，提供的必要“补救”功能。\n* **`延迟发货提醒`**：当商家无法按时发货时，可以通过这个功能，主动向用户发送一条安抚性的提醒。\n\n\n#### 5. 订单详情与状态流转\n![image-20250723111453299](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723111453299.png)\n点击订单列表中的任一订单，即可进入**订单详情页**。这是关于这笔交易的“**唯一事实凭证**”，它必须完整、清晰地展示所有相关信息。\n\n![image-20250723111508768](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723111508768.png)\n订单的生命周期，是由“**订单状态**”来驱动的。在商家后台，商家执行的每一个核心操作，都会驱动订单状态向前流转。\n\n![image-20250723111555824](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723111555824.png)\n例如，当商家在后台点击“**发货**”并填写完快递单号后，这笔订单的状态，就会从`待发货`自动变更为`待收货`。\n\n#### 6. 异常订单与售后订单管理\n\n* **异常订单处理**：我需要为商家，设计处理异常情况的功能，比如因库存不足而需要“**拒单**”，或在发货前应用户要求“**修改订单**”（特别是收货地址）。\n* **售后订单管理**：当用户发起“退款/退货”申请时，这些申请需要进入商家后台的一个独立“**售后订单**”工作队列中，供商家进行“**同意退款**”、“**拒绝申请**”等操作。\n\n---\n### 6.4.2 平台端订单管理\n\n“对于平台而言，它也有订单管理模块...”\n\n现在，我们来设计我们自己**内部运营和客服同事**使用的**平台端订单管理**后台。\n\n#### 1. 核心设计差异\n\n我设计平台端，与设计商家端的核心思路差异在于：\n* **视角不同**：商家端只能看到**自己的**订单；而平台端，必须能看到**全平台所有商家**的订单。\n* **职责不同**：商家的核心职责是**操作**（如：发货）；而我们平台的核心职责，更多的是**监督**和**客服仲裁**。\n\n#### 2. 平台端订单列表\n\n![image-20250723112351314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723112351314.png)\n\n基于上述差异，我设计的平台端订单列表，与商家端相比，有以下不同：\n* **增加“店铺”维度**：在列表的筛选器和表头中，我必须增加“**店铺名称**”这一维度。这能让我的客服同事，在接到用户电话时，可以快速定位到是哪个店铺的订单出了问题。\n* **简化操作项**：在“操作”列，平台端的操作会更精简。主要以“**查看订单详情**”为主，而不会包含“发货”这类应由商家执行的操作。\n\n#### 3. 平台端订单详情\n\n![image-20250723112452278](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723112452278.png)\n\n平台端的订单详情页，在信息展示上，与商家端需要保持完整和一致。\n* **我的设计**：我会额外在页面顶部，增加一个“**店铺信息**”模块，清晰地展示出这笔订单所属商家的`店铺名称`、`联系方式`等。当出现交易纠纷时，这能方便我的客服同事，快速地联系上对应的商家，进行沟通和处理。\n\n\n\n\n-----\n\n## 6.5 订单统计\n\n订单管理，解决的是“**处理单笔交易**”的问题。而订单统计，解决的是“**洞察整体业务**”的问题。\n\n我设计的订单统计模块，其核心目标，是为我们的运营、市场和管理团队，提供一个**数据驱动的决策中心**。它不是简单的数据罗列，而是要能通过数据，清晰地回答三个核心的业务问题：\n\n1.  **我们的“生意”做得怎么样？（交易维度）**\n2.  **我们什么“商品”卖得好？（商品维度）**\n3.  **我们的“客人”从哪里来？（订单来源维度）**\n\n![image-20250723113907620](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723113907620.png)\n\n\n\n---\n\n### 6.5.1 核心统计维度\n\n在设计数据看板时，我会围绕**三个核心维度**，分别构建不同的数据分析模块：\n\n#### 1. 交易维度 - “我们生意做得怎么样？”\n\n这是**最高层级**的模块，用于评估平台整体经营状况。\n\n##### 核心指标及解读：\n\n| **核心指标**        | **我的解读**                                                                                                                                         |\n| --------------- | ------------------------------------------------------------------------------------------------------------------------------------------------ |\n| **订单销售额 (GMV)** | 即“GMV”，特定时间段内用户下单的总金额，是衡量平台体量的最核心指标。                                                                                                             |\n| **订单量**         | 特定时间段内的总订单数量。                                                                                                                                    |\n| **客单价 (AOV)**   | 总销售额 ÷ 总订单数，反映用户的平均购买力。                                                                                                                          |\n| **下单/支付用户数**    | ![下单与支付用户数对比图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114340925.png)<br>两者的对比可计算出“**支付转化率**”，评估下单后支付流程是否顺畅。      |\n| **订单金额分布**      | ![订单金额分布图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114628770.png)<br>将订单金额分为如“0-50元”、“51-100元”等区间，帮助分析用户的核心消费力区间。 |\n| **新老客交易构成**     | ![新老客交易构成图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114404565.png)<br>分析新客和老客各自贡献的销售额与订单量，是衡量平台用户健康度的重要指标。        |\n\n---\n\n##### 💡 支付转化率的两种计算方式：\n\n###### ✅ 1. 订单转化率\n\n**定义：** 衡量**完成支付的独立访客**占**总访客**的比例。\n\n**计算公式：**\n\n```\n订单转化率 = (完成支付的订单数 ÷ 访问网站/App的总用户数) × 100%\n```\n\n**解释：**\n\n* **完成支付的订单数**：指成功付款的订单数量。\n* **访问总用户数**：独立访客或会话数（依据数据分析工具）。\n\n**适用场景：** 衡量从访问到购买的**整体效率**，反映电商平台的直接销售能力。\n\n---\n\n###### ✅ 2. 支付成功率\n\n**定义：** 衡量**成功支付交易**占**尝试支付交易**的比例。\n\n**计算公式：**\n\n```\n支付成功率 = (成功完成支付的交易数 ÷ 发起支付的交易总数) × 100%\n```\n\n**解释：**\n\n* **成功支付交易数**：用户付款成功的交易。\n* **发起支付交易数**：用户点击支付后实际发起的请求（无论成功与否）。\n\n**适用场景：** 用于评估**支付接口或渠道的稳定性**和用户体验。\n\n----\n\n>`二者对比如下：`\n\n* **订单转化率** → 衡量**用户意愿与行为**。\n* **支付成功率** → 衡量**支付能力实现与顺畅程度**。\n\n两个指标都很关键，分别代表“转化意愿”和“支付执行”的两个环节。\n\n---\n\n#### 2. 商品维度 - “我们什么东西卖得好？”\n\n这个模块帮助我与品类运营同事洞察商品表现，优化选品与库存。\n\n##### 核心指标及解读：\n\n| **核心指标**       | **我的解读**                                |\n| -------------- | --------------------------------------- |\n| **商品浏览量 (PV)** | 商品详情页被浏览的次数，反映商品的**吸引力**。               |\n| **商品销量**       | 商品的实际销售数量，反映**市场接受度**。                  |\n| **商品转化率**      | 商品支付订单数 ÷ 商品浏览量，是衡量商品从吸引到成交的“**黄金指标**”。 |\n| **加购物车数**      | 反映了用户对该商品的“**潜在兴趣**”。                   |\n\n我会基于这些数据设计如：\n\n* 热销商品排行榜\n* 高转化率商品榜\n* 品类销售额占比等数据榜单\n\n---\n\n#### 3. 订单来源维度 - “我们的客人从哪里来？”\n\n这个模块帮助我与市场运营同事评估**流量渠道**与**营销活动**效果，以优化预算投入。\n\n![订单来源分析图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723114753992.png)\n\n\n| **核心指标**   | **我的解读**                                                         |\n| ---------- | ---------------------------------------------------------------- |\n| **用户渠道来源** | 分析用户产生订单时，其最初来自哪个渠道：如 App、H5 商城、PC 端、微信小程序等。                     |\n| **转化入口来源** | 分析用户通过哪个“**营销位**”下单（如首页 Banner、活动入口等），用于计算每个广告位的 **ROI（投入产出比）**。 |\n\n---\n\n## 6.6 评价管理\n\n我们都知道，在电商购物中，**用户评价**是影响购买决策的最重要的因素之一。它是一种强大的“**社会认同（Social Proof）**”机制。因此，为平台设计一套**公平、透明、高效**的评价管理体系，是我工作的重中之重。\n\n![image-20250723133910689](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723133910689.png)\n\n我设计评价管理后台，需要同时服务于**商家**和**平台**这两个角色，他们的核心需求是不同的：\n* **商家**：更关注**用户反馈**，希望管理店铺声誉。\n* **平台**：更关注**内容监管**，希望维护社区环境的健康。\n\n因此，我通常会为他们，设计两套功能各有侧重的后台。\n\n### 6.6.1 评价列表与审核\n\n![image-20250723133952088](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723133952088.png)\n\n首先，我需要明确评价的**入口时机**。在我的流程设计中，只有当订单状态流转为“**待评价**”（通常在用户“确认收货”后），用户端才会出现“评价”按钮。用户提交评价后，订单状态则变为“**已完成**”，形成闭环。\n\n![image-20250723134151545](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723134151545.png)\n\n**1. 平台端：内容审核与监管**\n用户提交的所有评价，都会首先进入我们**平台端的评价管理后台**。\n\n* **核心职责**：平台运营的核心职责，是**内容审核**。他们需要快速地筛选和处理所有评价，特别是检查其中是否包含**敏感词汇、违规图片**等不良信息。\n* **核心功能**：因此，平台端的评价列表，功能相对纯粹。列表需要展示`订单编号`、`用户昵称`、`评价内容`等全局信息。而最核心的操作，就是“**删除**”。对于违规的评价，平台拥有最高权限，可以将其直接删除。\n\n![image-20250723134141388](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723134141388.png)\n\n**2. 商家端：查看与互动**\n当一条评价通过了平台的审核，它才会出现在**商家端的评价管理后台**。\n\n* **核心职责**：商家的核心职责，是**与用户互动，维护自己的店铺声誉**。\n* **核心功能**：因此，商家端的后台，操作更丰富：\n    * **`回复`**：商家可以回复用户的评价，这是最重要的客户关系维护功能。\n    * **`置顶`**：商家可以将优质的、图文并茂的好评，在自己商品详情页的评价区进行置顶，作为“买家秀”的典范。\n    * **`申诉`**：当商家认为自己遭遇了恶意差评时，可以向平台发起申诉。\n\n### 6.6.2 评价申诉处理\n\n“**评价申诉**”，是我为保障商家权益，设计的平台仲裁流程。\n* **商家发起申诉**：商家在后台，点击某条差评旁的“申诉”按钮。\n* **提交申诉理由**：我会设计一个弹窗，让商家可以填写申诉的理由，并上传相关的证据（如：与用户的聊天记录截图）。\n* **平台介入仲裁**：这条申诉，会进入我们平台端后台的一个“**申诉处理**”队列中。\n* **平台做出判决**：由我们的平台运营，作为**中立的第三方**，来对申诉进行判决。最终的判决结果，可能是“**维持原评价**”，也可能是“**隐藏/删除评价**”。\n\n这个申诉流程，是维护平台公平公正、调解用户与商家矛盾的关键机制。\n\n## 6.7 本章总结\n\n在本章，我们完整地设计了电商后台中，负责“**让生意转起来**”的几个核心运营与交易模块。\n\n| **设计模块** | **我的核心设计思考** |\n| :--- | :--- |\n| **营销位管理**| 为运营同事，提供了可以**灵活配置**首页`Banner`、`推荐位`、`活动会场`等营销资源的“弹药库”。 |\n| **支付管理**| 我们设计了后台的“**支付渠道开关**”，并了解了独立对接与**聚合支付**在技术和商务上的区别。 |\n| **订单管理**| 我们分别为**商家端**（侧重**履约操作**）和**平台端**（侧重**全局监督**），设计了功能职责各有侧重的订单管理后台。 |\n| **订单统计**| 我们为平台，设计了一个包含**交易、商品、来源**三大维度的“数据驾驶舱”，用于业务的宏观洞察。 |\n| **评价管理**| 我们为平台设计了**内容审核**后台，为商家设计了**互动与申诉**后台，共同维护了一个健康的评价生态。 |\n\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%85%AD%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0-%E8%BF%90%E8%90%A5%E4%B8%8E%E4%BA%A4%E6%98%93%E7%AE%A1%E7%90%86"><span class="toc-number">1.</span> <span class="toc-text">第六章：电商后台 - 运营与交易管理</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#6-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.</span> <span class="toc-text">6.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-2-%E8%90%A5%E9%94%80%E4%BD%8D%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.</span> <span class="toc-text">6.2 营销位管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-2-1-%E9%A6%96%E9%A1%B5Banner%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.1.</span> <span class="toc-text">6.2.1 首页Banner管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-2-2-%E6%8E%A8%E8%8D%90%E4%BD%8D%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.2.</span> <span class="toc-text">6.2.2 推荐位管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-2-3-%E6%B4%BB%E5%8A%A8%E4%BC%9A%E5%9C%BA%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.3.</span> <span class="toc-text">6.2.3 活动会场管理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-3-%E6%94%AF%E4%BB%98%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.</span> <span class="toc-text">6.3 支付管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-3-1-%E6%94%AF%E4%BB%98%E6%B8%A0%E9%81%93%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.1.</span> <span class="toc-text">6.3.1 支付渠道管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%94%AF%E4%BB%98%E6%B8%A0%E9%81%93%E5%88%97%E8%A1%A8"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">1. 支付渠道列表</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%94%AF%E4%BB%98%E6%B8%A0%E9%81%93%E9%85%8D%E7%BD%AE"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">2. 支付渠道配置</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-3-2-%E4%BA%A4%E6%98%93%E6%B5%81%E6%B0%B4%E4%B8%8E%E5%AF%B9%E8%B4%A6%EF%BC%88%E6%8B%93%E5%B1%95%E8%AE%BE%E8%AE%A1%EF%BC%89"><span class="toc-number">1.3.2.</span> <span class="toc-text">6.3.2 交易流水与对账（拓展设计）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-4-%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86"><span class="toc-number">1.4.</span> <span class="toc-text">6.4 订单管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-4-1-%E5%95%86%E5%AE%B6%E7%AB%AF%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86"><span class="toc-number">1.4.1.</span> <span class="toc-text">6.4.1 商家端订单管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BC%BA%E5%A4%A7%E7%9A%84%E6%9F%A5%E8%AF%A2%E4%B8%8E%E7%AD%9B%E9%80%89%E5%8C%BA"><span class="toc-number">1.4.1.1.</span> <span class="toc-text">1. 强大的查询与筛选区</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%8C%89%E7%8A%B6%E6%80%81%E5%88%86%E7%B1%BB%E7%9A%84%E5%B7%A5%E4%BD%9C%E9%98%9F%E5%88%97"><span class="toc-number">1.4.1.2.</span> <span class="toc-text">2. 按状态分类的工作队列</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%BB%93%E6%9E%84%E5%8C%96%E7%9A%84%E8%AE%A2%E5%8D%95%E4%BF%A1%E6%81%AF%E5%88%97%E8%A1%A8"><span class="toc-number">1.4.1.3.</span> <span class="toc-text">3. 结构化的订单信息列表</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E4%B8%B0%E5%AF%8C%E7%9A%84%E6%93%8D%E4%BD%9C%E9%A1%B9"><span class="toc-number">1.4.1.4.</span> <span class="toc-text">4. 丰富的操作项</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-%E8%AE%A2%E5%8D%95%E8%AF%A6%E6%83%85%E4%B8%8E%E7%8A%B6%E6%80%81%E6%B5%81%E8%BD%AC"><span class="toc-number">1.4.1.5.</span> <span class="toc-text">5. 订单详情与状态流转</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#6-%E5%BC%82%E5%B8%B8%E8%AE%A2%E5%8D%95%E4%B8%8E%E5%94%AE%E5%90%8E%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86"><span class="toc-number">1.4.1.6.</span> <span class="toc-text">6. 异常订单与售后订单管理</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-4-2-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86"><span class="toc-number">1.4.2.</span> <span class="toc-text">6.4.2 平台端订单管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1%E5%B7%AE%E5%BC%82"><span class="toc-number">1.4.2.1.</span> <span class="toc-text">1. 核心设计差异</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%A2%E5%8D%95%E5%88%97%E8%A1%A8"><span class="toc-number">1.4.2.2.</span> <span class="toc-text">2. 平台端订单列表</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%A2%E5%8D%95%E8%AF%A6%E6%83%85"><span class="toc-number">1.4.2.3.</span> <span class="toc-text">3. 平台端订单详情</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-5-%E8%AE%A2%E5%8D%95%E7%BB%9F%E8%AE%A1"><span class="toc-number">1.5.</span> <span class="toc-text">6.5 订单统计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-5-1-%E6%A0%B8%E5%BF%83%E7%BB%9F%E8%AE%A1%E7%BB%B4%E5%BA%A6"><span class="toc-number">1.5.1.</span> <span class="toc-text">6.5.1 核心统计维度</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%A4%E6%98%93%E7%BB%B4%E5%BA%A6-%E2%80%9C%E6%88%91%E4%BB%AC%E7%94%9F%E6%84%8F%E5%81%9A%E5%BE%97%E6%80%8E%E4%B9%88%E6%A0%B7%EF%BC%9F%E2%80%9D"><span class="toc-number">1.5.1.1.</span> <span class="toc-text">1. 交易维度 - “我们生意做得怎么样？”</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%8C%87%E6%A0%87%E5%8F%8A%E8%A7%A3%E8%AF%BB%EF%BC%9A"><span class="toc-number">1.5.1.1.1.</span> <span class="toc-text">核心指标及解读：</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%F0%9F%92%A1-%E6%94%AF%E4%BB%98%E8%BD%AC%E5%8C%96%E7%8E%87%E7%9A%84%E4%B8%A4%E7%A7%8D%E8%AE%A1%E7%AE%97%E6%96%B9%E5%BC%8F%EF%BC%9A"><span class="toc-number">1.5.1.1.2.</span> <span class="toc-text">💡 支付转化率的两种计算方式：</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E2%9C%85-1-%E8%AE%A2%E5%8D%95%E8%BD%AC%E5%8C%96%E7%8E%87"><span class="toc-number">1.5.1.1.2.1.</span> <span class="toc-text">✅ 1. 订单转化率</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E2%9C%85-2-%E6%94%AF%E4%BB%98%E6%88%90%E5%8A%9F%E7%8E%87"><span class="toc-number">1.5.1.1.2.2.</span> <span class="toc-text">✅ 2. 支付成功率</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%95%86%E5%93%81%E7%BB%B4%E5%BA%A6-%E2%80%9C%E6%88%91%E4%BB%AC%E4%BB%80%E4%B9%88%E4%B8%9C%E8%A5%BF%E5%8D%96%E5%BE%97%E5%A5%BD%EF%BC%9F%E2%80%9D"><span class="toc-number">1.5.1.2.</span> <span class="toc-text">2. 商品维度 - “我们什么东西卖得好？”</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%8C%87%E6%A0%87%E5%8F%8A%E8%A7%A3%E8%AF%BB%EF%BC%9A-1"><span class="toc-number">1.5.1.2.1.</span> <span class="toc-text">核心指标及解读：</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E8%AE%A2%E5%8D%95%E6%9D%A5%E6%BA%90%E7%BB%B4%E5%BA%A6-%E2%80%9C%E6%88%91%E4%BB%AC%E7%9A%84%E5%AE%A2%E4%BA%BA%E4%BB%8E%E5%93%AA%E9%87%8C%E6%9D%A5%EF%BC%9F%E2%80%9D"><span class="toc-number">1.5.1.3.</span> <span class="toc-text">3. 订单来源维度 - “我们的客人从哪里来？”</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-6-%E8%AF%84%E4%BB%B7%E7%AE%A1%E7%90%86"><span class="toc-number">1.6.</span> <span class="toc-text">6.6 评价管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#6-6-1-%E8%AF%84%E4%BB%B7%E5%88%97%E8%A1%A8%E4%B8%8E%E5%AE%A1%E6%A0%B8"><span class="toc-number">1.6.1.</span> <span class="toc-text">6.6.1 评价列表与审核</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#6-6-2-%E8%AF%84%E4%BB%B7%E7%94%B3%E8%AF%89%E5%A4%84%E7%90%86"><span class="toc-number">1.6.2.</span> <span class="toc-text">6.6.2 评价申诉处理</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#6-7-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.7.</span> <span class="toc-text">6.7 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>