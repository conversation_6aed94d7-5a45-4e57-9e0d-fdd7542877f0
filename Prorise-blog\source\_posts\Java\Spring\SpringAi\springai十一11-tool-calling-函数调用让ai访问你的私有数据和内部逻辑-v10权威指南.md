---
title: SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 9442
date: 2025-03-21 22:13:45
---

## **11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 (v1.0+权威指南)**

在之前的章节里，我们已经让 AI 具备了强大的对话、理解和记忆能力。但它始终是一个“局外人”，被限制在自己的模型世界里，无法与我们应用程序的**内部状态**进行交互。它不知道当前用户的购物车里有什么，无法查询一个特定订单的物流状态，更不能帮用户执行一个取消订单的业务操作。

本章，我们将解锁一项革命性的功能——**工具调用（Tool Calling）**，它为 AI 安装了连接应用内部世界的“神经接口”，让它能够调用你编写的 Java 代码，从而查询**私有数据库**、执行复杂的业务逻辑。

### **11.1 Tool Calling 核心思想与流程**

> **什么是工具调用？**
> 工具调用是一种机制，允许大语言模型（LLM）在对话过程中，智能地判断出用户的意图需要通过**应用程序的内部功能**来完成，并生成一个结构化的 JSON 对象来请求调用这个功能。你的应用程序负责接收这个请求，执行相应的本地方法，然后将执行结果返回给 LLM，LLM 再根据这个结果，生成最终的自然语言答复。

**必须理解的核心安全原则**：模型永远不会、也永远无法直接执行您的代码。它只是一个**请求者**，您的应用程序是**执行者**。整个流程是安全可控的。

### **11.2 核心API与定义工具的方式**

Spring AI 1.0+ 提供了多种方式来定义一个工具，本章我们将深入讲解两种最重要、最常用的方式。

| 定义方式 | 优点 | 核心API/注解 |
| :--- | :--- | :--- |
| **1. 声明式方法 (`@Tool`)** | **最简单、最直观**，代码可读性高，适合将一个类中的多个相关方法打包成工具集。 | `@Tool`, `@ToolParam` |
| **2. 函数式Bean (`@Bean`)** | **耦合度低，更符合Spring DI思想**，易于测试和替换，适合定义单一、独立的工具。 | `@Bean`, `@Description` |

### **11.3 实战：构建一个数据库驱动的订单管理AI客服**

我们将构建一个统一的AI服务，它能够同时使用`@Tool`注解定义的**订单管理工具 (`OrderTools`)和通过`@Bean`定义的退款工具 (`refundProcessor`)**，并直接操作**MySQL数据库**。

##### **1. 第一步：环境准备 (MySQL & MyBatis-Plus)**

  * **Docker启动MySQL**:

    ```yaml
    # docker-compose.yml
    version: '3.8'
    services:
      mysql:
        image: mysql:8.0
        container_name: mysql-for-ai
        ports:
          - "3306:3306"
        environment:
          - MYSQL_ROOT_PASSWORD=root
          - MYSQL_DATABASE=spring_ai_db
    ```

    在项目根目录运行 `docker-compose up -d`。

  * **创建订单表**:

    ```sql
    CREATE TABLE `orders` (
      `id` varchar(255) NOT NULL,
      `status` varchar(50) DEFAULT NULL,
      `products_json` json DEFAULT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

    -- 插入一些测试数据
    INSERT INTO `orders` (id, status, products_json) VALUES
    ('ORD-001', 'SHIPPED', '["Spring AI实战指南", "Java编程思想"]'),
    ('ORD-002', 'PROCESSING', '["高性能MySQL"]'),
    ('ORD-003', 'DELIVERED', '["算法导论"]');
    ```

  * **添加Maven依赖**:

    ```xml
    <!-- MySQL Connector -->
    <dependency>
        <groupId>com.mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <scope>runtime</scope>
    </dependency>
    <!-- MyBatis-Plus Starter -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-spring-boot-starter</artifactId>
        <version>3.5.7</version>
    </dependency>
    ```

  * **`application.yml`配置**:

    ```yaml
    spring:
      datasource:
        url: *******************************************************************************
        username: root
        password: root
    ```

##### **2. 第二步：定义数据访问层 (Entity & Mapper)**

  * **`entity/Order.java`**:

    ```java
    package com.example.hellospringai.entity;

    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableField;
    import com.baomidou.mybatisplus.annotation.TableId;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
    import lombok.AllArgsConstructor;
    import lombok.Data;
    import lombok.NoArgsConstructor;
    import java.util.List;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @TableName(value = "orders", autoResultMap = true)
    public class Order {
        @TableId(type = IdType.INPUT)
        private String id;
        private String status;
        @TableField(value = "products_json", typeHandler = JacksonTypeHandler.class)
        private List<String> products;
    }
    ```

  * **`mapper/OrderMapper.java`**:

    ```java
    package com.example.hellospringai.mapper;
    import com.baomidou.mybatisplus.core.mapper.BaseMapper;
    import com.example.hellospringai.entity.Order;
    import org.apache.ibatis.annotations.Mapper;

    @Mapper
    public interface OrderMapper extends BaseMapper<Order> {}
    ```

##### **3. 第三步：定义所有工具**

  * **定义`@Tool`工具集 (`tool/OrderTools.java`)**:

    ```java
    package com.example.hellospringai.tool;

    import com.example.hellospringai.entity.Order;
    import com.example.hellospringai.mapper.OrderMapper;
    import org.springframework.ai.tool.annotation.Tool;
    import org.springframework.ai.tool.annotation.ToolParam;
    import org.springframework.context.annotation.Description;
    import org.springframework.stereotype.Component;
    import java.util.List;

    @Component
    @Description("用于查询和管理用户订单的数据库工具集")
    public class OrderTools {

        private final OrderMapper orderMapper;

        public OrderTools(OrderMapper orderMapper) {
            this.orderMapper = orderMapper;
        }

        public record CancelResponse(boolean success, String message) {}

        @Tool(description = "根据订单ID查询订单状态")
        public String getOrderStatus(@ToolParam(description = "要查询的订单ID") String orderId) {
            System.out.printf(">>> [Tool Executing] 从MySQL查询订单状态，ID: %s%n", orderId);
            Order order = orderMapper.selectById(orderId);
            return (order != null) ? order.getStatus() : "NOT_FOUND";
        }

        @Tool(description = "根据订单ID获取订单中的商品列表")
        public List<String> getOrderProducts(@ToolParam(description = "要查询的订单ID") String orderId) {
            System.out.printf(">>> [Tool Executing] 从MySQL查询订单商品，ID: %s%n", orderId);
            Order order = orderMapper.selectById(orderId);
            return (order != null) ? order.getProducts() : List.of();
        }

        @Tool(description = "取消一个用户的订单。只有处理中(PROCESSING)的订单可以被取消。")
        public CancelResponse cancelOrder(@ToolParam(description = "要取消的订单ID") String orderId) {
            System.out.printf(">>> [Tool Executing] 尝试从MySQL取消订单，ID: %s%n", orderId);
            Order order = orderMapper.selectById(orderId);

            if (order == null) {
                return new CancelResponse(false, "订单未找到。");
            }
            if ("PROCESSING".equals(order.getStatus())) {
                order.setStatus("CANCELLED");
                orderMapper.updateById(order);
                return new CancelResponse(true, "订单 " + orderId + " 已成功取消。");
            }
            return new CancelResponse(false, "订单无法取消，因为它当前的状态是: " + order.getStatus());
        }
    }
    ```

  * **定义`@Bean`函数式工具 (`config/ToolConfiguration.java`)**:

    ```java
    package com.example.hellospringai.config;

    import com.example.hellospringai.service.PaymentService;
    import org.springframework.context.annotation.Bean;
    import org.springframework.context.annotation.Configuration;
    import org.springframework.context.annotation.Description;

    import java.util.function.Function;

    @Configuration
    public class ToolConfiguration {

        @Bean("refundProcessor")
        @Description("为指定的订单ID和金额处理退款流程，返回退款交易号和状态") 
        public Function<PaymentService.RefundRequest, PaymentService.RefundResponse> refundProcessor() {
            return new PaymentService();
        }
    }
    ```

    (其中 `service/PaymentService.java` 的代码与您之前的版本一致，此处不再赘述)

##### **4. 第四步：构建统一的AI服务与API入口**

  * **定义DTO (`dto`包)**:

    ```java
    package com.example.hellospringai.dto;
    public record ToolRequest(String message) {}

    package com.example.hellospringai.dto;
    public record AiResponse(String message, Object data) {}
    ```

  * **创建统一的Service (`service/UnifiedAiService.java`)**:

    ```java
    package com.example.hellospringai.service;

    import com.example.hellospringai.tool.OrderTools;
    import org.springframework.ai.chat.client.ChatClient;
    import org.springframework.stereotype.Service;

    @Service
    public class UnifiedAiService {

        private final ChatClient chatClient;
        private final OrderTools orderTools;

        public UnifiedAiService(ChatClient.Builder chatClientBuilder, OrderTools orderTools) {
            this.orderTools = orderTools;
            this.chatClient = chatClientBuilder.build();
        }

        /**
         * 统一处理用户的自然语言请求，并智能地调用所有可用的工具。
         */
        public String processUserRequest(String message) {
            return chatClient.prompt()
                    .user(message)
                    .tools(orderTools)
                    .call()
                    .content();
        }
    }
    ```

  * **创建统一的Controller (`controller/UnifiedAiController.java`)**:

    ```java
    package com.example.hellospringai.controller;

    import com.example.hellospringai.dto.AiResponse;
    import com.example.hellospringai.dto.ToolRequest;
    import com.example.hellospringai.service.UnifiedAiService;
    import org.springframework.http.ResponseEntity;
    import org.springframework.web.bind.annotation.PostMapping;
    import org.springframework.web.bind.annotation.RequestBody;
    import org.springframework.web.bind.annotation.RequestMapping;
    import org.springframework.web.bind.annotation.RestController;

    @RestController
    @RequestMapping("/ai")
    public class UnifiedAiController {

        private final UnifiedAiService unifiedAiService;

        public UnifiedAiController(UnifiedAiService unifiedAiService) {
            this.unifiedAiService = unifiedAiService;
        }

        /**
         * 统一的AI请求入口
         */
        @PostMapping("/request")
        public ResponseEntity<AiResponse> handleRequest(@RequestBody ToolRequest request) {
            String content = unifiedAiService.processUserRequest(request.message());
            return ResponseEntity.ok(new AiResponse(content, null));
        }
    }
    ```

#### **11.5 效果检验：测试统一的智能Agent**

现在，我们所有的测试都将指向唯一的入口 `POST /ai/request`。

##### **测试场景一：AI智能选择`OrderTools`**

  * **请求**: `POST http://localhost:8080/ai/request`
  * **Body**: `{"message": "我的订单ORD-001里有什么东西？"}`
  * **后台日志**: `>>> [Tool Executing] 从MySQL查询订单商品，ID: ORD-001`
  * **API响应**: `"您的订单ORD-001中包含的商品有：[Spring AI实战指南, Java编程思想]。"`

##### **测试场景二：AI智能选择`refundProcessor`**

  * **请求**: `POST http://localhost:8080/ai/request`
  * **Body**: `{"message": "帮我给订单ORD-003退款50块钱"}`
  * **后台日志**: `>>> [Function Bean Executing] 正在为订单 ORD-003 处理退款，金额: 50.00`
  * **API响应**: `"好的，我已经为您处理了订单ORD-003的50.0元退款，相关的交易号是TXN-xxxxxxxxxxxxx。"`

##### **【关键】测试场景三：AI智能规划并连续调用多个工具**

  * **请求**: `POST http://localhost:8080/ai/request`
  * **Body**: `{"message": "我的订单ORD-002是啥状态？如果是处理中，就帮我取消它，然后给这个订单退款100元。"}`
  * **后台日志 (将按顺序打印)**:
    ```
    >>> [Tool Executing] 从MySQL查询订单状态，ID: ORD-002
    >>> [Tool Executing] 尝试从MySQL取消订单，ID: ORD-002
    >>> [Function Bean Executing] 正在为订单 ORD-002 处理退款，金额: 100.00
    ```
  * **API响应**: `"您好，订单ORD-002之前的状态是PROCESSING。我已经成功为您取消了该订单，并处理了一笔100.0元的退款，交易号是TXN-xxxxxxxxxxxxx。"`