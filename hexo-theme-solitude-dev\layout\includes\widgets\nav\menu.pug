- var menu = theme.nav.menu

if menu
    .menus_items
        each value, label in menu
            .menus_item
                if typeof value !== 'object'
                    a.site-page(href=url_for(value))
                        span= label
                else
                    a.site-page(href="javascript:void(0);")
                        span= label
                    ul.menus_item_child
                        each childValue, childLabel in value
                            li
                                - var array = childValue.split('||')
                                a.site-page.child(href=url_for(trim(array[0])))
                                    if array[1]
                                        i.solitude(class=array[1])
                                    span= childLabel