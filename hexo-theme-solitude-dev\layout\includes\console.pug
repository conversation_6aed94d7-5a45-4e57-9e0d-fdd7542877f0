- const { recentComment, card } = theme.console
- const cache = recentComment.storage
- const limit = 6
- const { use } = theme.comment

div#console
    div.close-btn(onclick="sco.hideConsole()")
        i.solitude.fas.fa-xmark
    div.console-card-group
        if use && recentComment.enable
            div.console-card-group-left
                div.console-card#card-newest-comments(onclick="sco.hideConsole()")
                    div.card-content
                        div.author-content-item-tips= _p('console.comment_tip')
                        div.author-content-item-title
                            | #{_p('console.comment_title')}
                            if theme.recent_comments.enable
                                a.recent-comment-more(href=url_for(theme.recent_comments.page) title=_p('console.recent_comment_more'))
                                    i.solitude.fas.fa-circle-chevron-right
                    .console_recentcomments
                        - var sel = '.console-card .console_recentcomments'
                        case use[0]
                            when 'Twikoo'
                                - var str_name = 'twikoo-recent-comments-console'
                                include ./widgets/page/recentcomment/twikoo
                            when 'Valine'
                                - var str_name = 'valine-recent-comments-console'
                                include ./widgets/page/recentcomment/valine
                            when 'Waline'
                                - var str_name = 'waline-recent-comments-console'
                                include ./widgets/page/recentcomment/waline
                            when 'Artalk'
                                - var str_name = 'artalk-recent-comments-console'
                                include ./widgets/page/recentcomment/artalk
        if card.tags || card.archive
            div.console-card-group-right
                if card.tags
                    div.console-card.tags(onclick="sco.hideConsole()")
                        div.card-content
                            div.author-content-item-tips= _p('console.tag_tip')
                            div.author-content-item-title= _p('console.tag_title')
                        div.card-tag-cloud
                            each tag in site.tags.find({ parent: { $exists: false } }).data
                                a(href=url_for(tag.path))= tag.name
                                    sup= tag.length
                if card.archive
                    div.console-card.history(onclick="sco.hideConsole()")
                        - var archives = getArchiveLength(card.archive ? card.archive : 'year')
                        ul.card-archive-list
                            each value, key in archives
                                li.item
                                    a(href=`/archives/${key}/`)
                                        span.date= key
                                        .count-group
                                            span.count= value
                                            span.unit= __('console.archive_unit')
                            li.item
                                a(href='/archives/')
                                    span.date= __('console.archive_all')
                                    .count-group
                                        span.count= site.posts.length
                                        span.unit= __('console.archive_unit')

    div.button-group
        div.console-btn-item
            span.darkmode_switchbutton(onclick="sco.switchDarkMode()", title=_p('console.switch_darkmode'))
                i.solitude.fas.fa-circle-half-stroke
        div.console-btn-item#consoleHideAside
            span.asideSwitch(onclick="sco.switchHideAside()", title=_p('console.switch_hideAside'))
                i.solitude.fas.fa-arrows-left-right-to-line
        if theme.keyboard.enable
            div.console-btn-item#consoleKeyboard(onclick="sco.switchKeyboard()")
                span.keyboardSwitch(title=_p('console.switch_keyboard'))
                    i.solitude.fas.fa-keyboard
        if theme.capsule.enable
            div.console-btn-item#consoleMusic(onclick="sco.musicToggle()")
                span.music-switch(title=_p('console.switch_music'))
                    i.solitude.fas.fa-compact-disc
        if theme.comment.use && theme.comment.commentBarrage
            div.console-btn-item.on#consoleCommentBarrage(onclick="sco.switchCommentBarrage()")
                span.commentBarrage
                    i.solitude.fas.fa-comment
    div.console-mask(onclick="sco.hideConsole()")
