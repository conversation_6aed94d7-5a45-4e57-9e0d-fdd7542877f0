#footer
  position: relative
  background: linear-gradient(180deg, var(--anzhiyu-card-bg-none) 0%, var(--anzhiyu-card-bg) 25%);
  background-attachment: scroll
  background-position: bottom
  background-size: cover
  +maxWidth768()
    z-index: 3
    margin-top: -1px;

  if hexo-config('footer_bg') != false
    &:before
      position: absolute
      width: 100%
      height: 100%
      background-color: alpha($dark-black, .5)
      content: ''

#footer-bar
  padding: 1rem;
  color: var(--anzhiyu-fontcolor);
  margin-top: 1rem;
  background: var(--anzhiyu-secondbg);
  display: flex;
  overflow: hidden;
  z-index: 1002;
  transition: 0.3s;
  a
    &:hover
      color: var(--anzhiyu-lighttext);
  .footer-bar-link
    margin-top: 8px;
    margin-bottom: 8px;
    margin-right: 1rem;
    color: var(--anzhiyu-fontcolor);
    font-size: 1rem;
    font-weight: bold;
    white-space: nowrap;
    &.cc i
      margin-right: 0.7rem;
      font-size: 18px;
  .footer-bar-links
    display: flex;
    justify-content: space-between;
    max-width: 1400px;
    width: 100%;
    margin: 0 auto;
    flex-wrap: wrap;
    align-items: center;
    line-height: 1;

    .footer-bar-left
      display: flex;
      flex-wrap: wrap;
      min-height: 32px;
      align-items: center;
    .footer-bar-right
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: center;

#footer-wrap
  position: relative
  if hexo-config('footer.footerBar') && hexo-config('footer.footerBar.enable')
    padding: 40px 20px 0
  else 
    padding: 40px 20px
  color: var(--light-grey)
  text-align: center
  +maxWidth768()
    padding: 0
  #footer_deal
    justify-content: center;
    display: flex;
    align-items: center;
    +maxWidth768()
      flex-wrap: wrap;
      flex-direction: row;
    .deal_link
      display: flex;
      margin: 1rem 27px;
      color: var(--anzhiyu-card-bg);
      border-radius: 3rem;
      width: 32px;
      height: 32px;
      background: var(--anzhiyu-fontcolor);
      justify-content: center;
      align-items: center;
      transition: 0.3s;
      &:hover
        color: var(--anzhiyu-white);
        background: var(--anzhiyu-main);

    .footer_mini_logo
      width: 50px;
      height: 50px;
      object-fit: cover;
      border-radius: 50%;
      margin: 0 1rem;
      cursor: pointer;
      transition: cubic-bezier(0, 0, 0, 1.29) 0.5s;
      -webkit-user-select: none;
      +maxWidth768()
        display: none;
  #anzhiyu-footer
    display: flex;
    flex-direction: row;
    width: 100%;
    max-width: 1200px;
    margin: 1rem auto;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 0 2rem;
    .footer-group
      min-width: 120px;
      .footer-title-group
        display: flex;
        align-items: center;
        a
          margin-left: 8px
          line-height: 32px;
        i
          line-height: 1;
          color: var(--anzhiyu-secondtext);
          transition: 0.3s;
          font-size: 16px;
          opacity: 0.6;
    .footer-title
      color: var(--anzhiyu-secondtext);
      font-size: 1rem;
      text-align: left;
      font-weight: 600;
      margin: 1rem 0;
    .footer-links
      display: flex;
      flex-direction: column;
    .footer-item
      font-size: 1rem;
      line-height: 1rem;
      margin: 8px 0;
      color: var(--anzhiyu-fontcolor);
      margin-right: auto;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 120px;
      cursor: pointer;
      padding: 2px 0;
      text-decoration: none
      &:hover
        color: var(--anzhiyu-theme);
  a
    color: var(--light-grey)
  i
    font-size: 1rem;
    line-height: 1rem;
    height: 1rem;

  .footer-separator
    margin: 0 4px

  .icp-icon
    padding: 0 4px
    max-height: 1.4em
    width: auto
    vertical-align: text-bottom
[data-theme=light] .post #footer
  +maxWidth768()
    background: linear-gradient(180deg, var(--anzhiyu-background) 0%, var(--anzhiyu-card-bg) 25%);