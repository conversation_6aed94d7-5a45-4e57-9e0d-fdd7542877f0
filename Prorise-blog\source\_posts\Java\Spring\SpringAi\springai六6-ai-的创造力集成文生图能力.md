---
title: SpringAI（六）：6. AI 的创造力：集成文生图能力
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 29776
date: 2025-03-21 07:13:45
---

## **6. AI 的创造力：集成文生图能力**

除了强大的语言对话能力，现代 AI 的多模态特性也日益重要。文生图（Text-to-Image）就是其中最引人注目的能力之一。它能将我们脑海中的文字想象，转化为生动的视觉画面。

本章，我们将深入 Spring AI 全新设计的 `ImageModel` API 体系，并以**智谱 AI 的 `CogView` 模型**为例，为我们的 AI-Copilot 项目实战集成强大的文生图功能。

### **6.1 Spring AI 图像生成 API 核心**

`ImageModel` 的 API 围绕以下几个关键类构建，理解它们的职责是掌握图像生成功能的基础。

#### **6.1.1 核心组件解析**

| 类/接口名 | 角色 | 核心内容/职责 |
| :--- | :--- | :--- |
| **`ImageModel`** | **核心模型接口** | 定义了 `call(ImagePrompt request)` 方法，是与所有图像生成模型交互的统一入口。 |
| **`ImagePrompt`** | **请求封装对象** | 封装了完整的图像生成请求，包含具体的文本指令（`ImageMessage`）和生成选项（`ImageOptions`）。 |
| **`ImageOptions`** | **可移植生成选项** | 定义了可跨模型移植的通用参数，如生成数量(`n`)、模型(`model`)、宽高(`width`/`height`)。 |
| **`ZhiPuAiImageOptions`** | **特定模型选项** | 继承自 `ImageOptions`，用于设置特定于智谱 `CogView` 模型的高级参数，如质量(`quality`)。 |
| **`ImageResponse`** | **响应封装对象** | 封装了模型的完整响应，包含一个或多个 `ImageGeneration` 实例。 |
| **`ImageGeneration`**| **单张图片结果** | 代表一张生成的图片及其元数据，可从中获取图片 **URL** 或 **Base64** 数据。 |

这个体系的设计，使得开发者可以用一套统一的逻辑来构建请求和解析响应，同时又能通过特定于模型的 `Options` 实现类来利用特定厂商的独有功能。

### **6.2 实战：为 AI-Copilot 集成 `CogView` 文生图**

现在，我们将一步步地为项目添加一个新功能：用户输入一段描述，AI-Copilot为其生成一张图片。

#### **6.2.1 后端实现：构建图像生成服务**

##### 1.  **依赖与配置**

  * **Maven**: 确保 `pom.xml` 中已有 `spring-ai-starter-model-zhipuai` 依赖。
  * **`application.yml`**:
    ```yaml
    spring:
      ai:
        zhipuai:
          api-key: "YOUR_ZHIPUAI_API_KEY"
          # 新增 image 部分的 options 配置
          image:
            options:
              # 设置默认使用的文生图模型为 CogView
              model: cogview-4-250304 
    ```

> **深度注释**：与 `chat.options` 类似，`image.options` 用于为所有图像生成请求设置全局默认值。我们在这里将 `cogview-3` 设置为默认模型。

##### 2.  **创建 DTO (`dto/request/ImageGenerationRequest.java`)**

为了接收前端更丰富的请求，我们创建一个 DTO。

```java
// src/main/java/com/copilot/aicopilotbackend/dto/request/ImageGenerationRequest.java
package com.copilot.aicopilotbackend.dto.request;

public record ImageGenerationRequest(String prompt) {}
```

##### 3.  **创建图像生成服务 (`service/ImageService.java`)**

这个服务将封装所有与 `ImageModel` 交互的逻辑。

```java
// src/main/java/com/copilot/aicopilotbackend/service/ImageService.java
package com.copilot.aicopilotbackend.service;

import com.copilot.aicopilotbackend.dto.request.ImageGenerationRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.image.ImagePrompt;
import org.springframework.ai.image.ImageResponse;
import org.springframework.ai.zhipuai.ZhiPuAiImageModel;
import org.springframework.ai.zhipuai.ZhiPuAiImageOptions;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ImageService {

    private final ZhiPuAiImageModel imageModel;

public String generateImage(ImageGenerationRequest request) {
        log.info("接收到图像生成请求: prompt='{}'", request.prompt());

    // 1. 构建 ZhiPuAiImageOptions，运行时指定使用最新的 cogview-3 模型
        // 注意：根据源码，我们只能设置 model 和 user。
        ZhiPuAiImageOptions options = ZhiPuAiImageOptions.builder()
                .withModel("cogview-3") 
                .build();

        // 2. 创建 ImagePrompt
        ImagePrompt imagePrompt = new ImagePrompt(request.prompt(), options);

        // 3. 调用 ImageModel 的 call 方法
        ImageResponse response = imageModel.call(imagePrompt);
    String imageUrl = response.getResult().getOutput().getUrl();
        log.info("成功生成图片, URL: {}", imageUrl);

        // 4. 返回图片的URL
    return imageUrl;
    }
}
```

##### 4.  **创建 API 端点 (`controller/ImageController.java`)**

```java
// src/main/java/com/copilot/aicopilotbackend/controller/ImageController.java
package com.copilot.aicopilotbackend.controller;

import com.copilot.aicopilotbackend.dto.request.ImageGenerationRequest;
import com.copilot.aicopilotbackend.dto.response.ApiResponse;
import com.copilot.aicopilotbackend.service.ImageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/images")
@RequiredArgsConstructor
public class ImageController {

    private final ImageService imageService;

    @PostMapping("/generations")
    public ApiResponse<Map<String, String>> generateImage(@RequestBody ImageGenerationRequest request) {
        String imageUrl = imageService.generateImage(request);
        // 将URL包装在Map中，再用ApiResponse包装，符合我们项目的标准格式
        return ApiResponse.success(Map.of("url", imageUrl));
    }
}
```



##### 5.接口文档测试
**`POST http://localhost:8080/api/v1/images/generations`**

```json
// 请求示例
{
  "prompt": "一座未来城市，赛博朋克风格，下雨"
}
```

```json
{
    "code": "00000",
    "message": "成功",
    "data": {
        "url": "https://aigc-files.bigmodel.cn/api/cogview/202506291135537d48f4311bb849f0_0.png"
    },
    "timestamp": "2025-06-29T11:36:00.3608927",
    "success": true
}
```

-----

