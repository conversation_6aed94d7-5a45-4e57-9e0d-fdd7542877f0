<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>第七部分：多样化部署方案 | Prorise的小站</title><meta name="keywords" content="博客搭建教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="第七部分：多样化部署方案"><meta name="application-name" content="第七部分：多样化部署方案"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="第七部分：多样化部署方案"><meta property="og:url" content="https://prorise666.site/posts/64413.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第七部分：多样化部署方案搭建并配置好我们的Hexo博客后，下一步就是将其发布到互联网上，让全世界的读者都能访问。部署静态博客有多种方式，从简单的手动部署到自动化的持续集成&amp;#x2F;持续部署（CI&amp;#x2F;CD）。本部分，我们将介绍几种主流的免费部署方案，并重点讲解如何配置和使用它们。 1. 部署"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp"><meta name="description" content="第七部分：多样化部署方案搭建并配置好我们的Hexo博客后，下一步就是将其发布到互联网上，让全世界的读者都能访问。部署静态博客有多种方式，从简单的手动部署到自动化的持续集成&amp;#x2F;持续部署（CI&amp;#x2F;CD）。本部分，我们将介绍几种主流的免费部署方案，并重点讲解如何配置和使用它们。 1. 部署"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/64413.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"第七部分：多样化部署方案",postAI:"true",pageFillDescription:"第七部分：多样化部署方案, 1. 部署准备：安装 Hexo Git 部署插件, 2. 主流免费部署平台对比与实践, 2.1 GitHub Pages：基于 Git Push 的传统部署, 2.2 Vercel x2F Netlify：基于 Git 的自动化部署 (CIx2FCD), 3. 实战核心：Vercel 自动化部署与深度优化, 3.1 基础部署：从 Git 仓库到上线, 3.2 解决常见错误与配置构建命令, 3.3 集成第三方服务 (以 Algolia 搜索为例), 3.4 绑定并加速自定义域名第七部分多样化部署方案搭建并配置好我们的博客后下一步就是将其发布到互联网上让全世界的读者都能访问部署静态博客有多种方式从简单的手动部署到自动化的持续集成持续部署本部分我们将介绍几种主流的免费部署方案并重点讲解如何配置和使用它们部署准备安装部署插件默认支持多种部署方式最常用的一种是通过将生成的静态文件推送到远程仓库然后由托管平台如等负责提供访问服务我们需要安装插件来实现这一功能在项目的根目录下打开命令行工具执行以下命令安装部署插件安装完成后我们需要在的配置文件中配置部分配置的部分找到项目根目录下的文件滑动到文件末尾通常会有一个部分如果不存在我们可以手动添加其基本配置格式如下部署类型这里设置为你的仓库例如方式或方式部署分支默认通常是或通常是构建触发的分支如可选自定义部署提交信息这是一个文件中部分的截图示例在这个配置中指明了我们使用进行部署是你存储生成文件的仓库地址对于它通常是推荐使用方式需要配置或者是你希望将生成的静态文件推送到仓库的哪个分支对于用户网站格式仓库静态文件需要推送到或分支取决于你仓库的默认分支以及的配置对于项目网站通常是分支对于方式部署到通常是将源文件推送到分支由流程生成并部署配置完成后保存文件下次运行命令时就会自动执行生成静态文件并将目录下的内容推送到你指定的仓库和分支主流免费部署平台对比与实践静态博客的部署选择多样特别是对于个人博客有许多提供免费额度且功能强大的托管平台下面我们对比并介绍几个主流的免费部署平台平台支持免费额度情况国内访问速度优缺点推荐场景有限需良好配合无限空间每月流量限制自带较慢优点与工作流结合紧密简单易用适合托管静态网站缺点直接部署不够自动化国内访问速度慢用户对自动化要求不高或愿意配置优秀构建时间流量函数调用全球较快优点自动部署界面友好功能强大全球节点丰富缺点免费额度有一定限制复杂配置需付费对部署自动化和访问速度有较高要求乐于尝试新特性优秀构建时间流量函数调用全球较快优点自动部署功能丰富免费额度慷慨社区活跃缺点免费额度相对更慷慨但复杂配置需付费对部署自动化和访问速度有较高要求需要一些附加功能良好无限空间流量限制未知腾讯云较快优点国内平台访问速度快支持仓库缺点界面和文档不如友好自动化能力相对较弱主要面向国内用户希望提高国内访问速度良好需手动更新或无限空间流量限制未知自带较快优点国内平台访问速度快缺点免费版需要手动更新或配置自动化程度最低主要面向国内用户对自动化要求不高基于的传统部署是提供的免费静态网站托管服务特别适合托管个人博客部署步骤使用创建一个新的仓库仓库名称必须是你的用户名例如我的用户名是那么仓库名称就是这是一个特殊命名的仓库会自动将其主分支的静态内容发布到域名下请确保这是一个公共仓库配置可选但推荐为了使用这种方式提交代码你需要在本地生成并添加到你的账户设置中这样在时就无需反复输入密码具体步骤可以参考官方文档配置的修改项目根目录下的文件配置部分将设置为你刚刚创建的仓库的或地址将设置为你的仓库的默认分支通常是或你的仓库地址方式或使用方式或者取决于你的仓库设置请将替换为你的实际用户名生成并部署在项目根目录打开命令行依次执行以下命令清除缓存和已生成的静态文件重要防止旧文件残留生成静态文件生成结果在目录下部署到命令实际上是先执行然后将目录下的内容使用推送到中指定的仓库和分支设置确认部署成功后访问你的仓库页面进入确认设置为并且设置为你部署的分支例如或以及目录首次部署可能需要几分钟到十几分钟才能生效图仓库设置页面截图确认从哪个分支和目录部署静态文件注意随着的普及现在推荐使用来构建和部署静态网站而不是直接将生成好的静态文件推送到特定分支如果我们使用详见第部分则设置中的需要改为基于的自动化部署能将我们从繁琐的部署命令中解放出来实现一次推送自动上线我们只需将博客源文件推送到云端服务器就会自动为我们完成构建和部署平台支持免费额度情况国内访问速度优缺点推荐场景优秀构建时间流量全球较慢官方很快使用社区优点自动部署界面极友好功能强大缺点官方节点国内访问慢需额外配置加速强烈推荐对自动化和访问速度有高要求愿意进行简单优化配置优秀构建时间流量全球较慢优点功能丰富免费额度慷慨缺点国内访问速度同样不理想的优秀替代品功能需求多样化良好配合无限空间自带很慢优点与结合紧密缺点国内访问速度是主要瓶颈对速度要求不高或作为代码备份良好无限空间腾讯云很快优点国内平台访问速度顶尖缺点配置比稍复杂主要面向国内用户追求极致国内速度有限无限空间自带很快优点国内平台速度快缺点免费版需手动更新自动化程度最低对自动化要求不高主要面向国内结论对于追求自动化体验和全球化部署的用户是首选虽然其官方节点在国内访问慢但通过简单的社区方案即可完美解决实现鱼和熊掌兼得实战核心自动化部署与深度优化我们将以为例走完从部署踩坑到优化的完整流程基础部署从仓库到上线准备源文件仓库确保你的仓库中存放的是的完整源文件包含等而不是生成的目录在中导入项目访问官网并用账号登录点击选择并导入你的源文件仓库配置并部署会自动识别出是项目应为保持点击首次部署你可能会遇到一个经典错误解决常见错误与配置构建命令错误这个报错意味着的服务器不认识命令原因是它只执行了却没有安装本身解决方案我们需要修改构建命令让在构建前先安装依赖在项目后台进入找到覆盖构建命令为这条命令告诉先用安装中定义的所有依赖包括成功后再执行生成网站保存后回到页面对失败的部署选择部署成功后你的新工作流诞生了本地写文章自动上线你再也不需要在本地执行了集成第三方服务以搜索为例自动化流程中像这种需要私密的命令怎么办答案是使用的环境变量在添加环境变量进入添加你的密钥例如你的你的用于写入你的索引名修改让从环境变量中读取密钥而不是明文存储更新构建命令在生成网站后执行索引命令同时我们做一个优化只在正式发布到主域名时才更新索引将修改为这条命令利用的系统变量判断只有在生产环境部署时才执行绑定并加速自定义域名在绑定域名在项目中添加你的自定义域名例如国内访问速度优化此时你会发现用自定义域名访问非常慢这是因为官方节点在国内的通病幸运的是社区提供了优秀的解决方案配置关键一步登录你的域名注册商如找到你域名的管理界面删除所有指向官方的记录创建一条记录配置如下类型主机名称代表根域名值指向保存设置重要忽略警告配置后后台的域名状态会显示红色的警告请放心忽略此警告这是因为我们的设置社区与官方推荐记录不符判断成功的唯一标准是你的网站能通过域名正常访问且速度变快了最终优化添加缓存配置为了让网站快上加快我们可以配置的缓存策略在你的源文件仓库根目录下创建一个文件内容如下将文件到仓库会自动应用新的缓存规则至此你已经拥有了一个全自动高可用在国内访问速度飞快的现代化博客的自动化极大地提高了部署效率减少了手动操作可能带来的错误让我们能更专注于内容创作本身",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-19 19:21:28",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E9%83%A8%E5%88%86%EF%BC%9A%E5%A4%9A%E6%A0%B7%E5%8C%96%E9%83%A8%E7%BD%B2%E6%96%B9%E6%A1%88"><span class="toc-text">第七部分：多样化部署方案</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E9%83%A8%E7%BD%B2%E5%87%86%E5%A4%87%EF%BC%9A%E5%AE%89%E8%A3%85-Hexo-Git-%E9%83%A8%E7%BD%B2%E6%8F%92%E4%BB%B6"><span class="toc-text">1. 部署准备：安装 Hexo Git 部署插件</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E4%B8%BB%E6%B5%81%E5%85%8D%E8%B4%B9%E9%83%A8%E7%BD%B2%E5%B9%B3%E5%8F%B0%E5%AF%B9%E6%AF%94%E4%B8%8E%E5%AE%9E%E8%B7%B5"><span class="toc-text">2. 主流免费部署平台对比与实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-GitHub-Pages%EF%BC%9A%E5%9F%BA%E4%BA%8E-Git-Push-%E7%9A%84%E4%BC%A0%E7%BB%9F%E9%83%A8%E7%BD%B2"><span class="toc-text">2.1 GitHub Pages：基于 Git Push 的传统部署</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-Vercel-Netlify%EF%BC%9A%E5%9F%BA%E4%BA%8E-Git-%E7%9A%84%E8%87%AA%E5%8A%A8%E5%8C%96%E9%83%A8%E7%BD%B2-CI-CD"><span class="toc-text">2.2 Vercel / Netlify：基于 Git 的自动化部署 (CI/CD)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%AE%9E%E6%88%98%E6%A0%B8%E5%BF%83%EF%BC%9AVercel-%E8%87%AA%E5%8A%A8%E5%8C%96%E9%83%A8%E7%BD%B2%E4%B8%8E%E6%B7%B1%E5%BA%A6%E4%BC%98%E5%8C%96"><span class="toc-text">3. 实战核心：Vercel 自动化部署与深度优化</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-%E5%9F%BA%E7%A1%80%E9%83%A8%E7%BD%B2%EF%BC%9A%E4%BB%8E-Git-%E4%BB%93%E5%BA%93%E5%88%B0%E4%B8%8A%E7%BA%BF"><span class="toc-text">3.1 基础部署：从 Git 仓库到上线</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-2-%E8%A7%A3%E5%86%B3%E5%B8%B8%E8%A7%81%E9%94%99%E8%AF%AF%E4%B8%8E%E9%85%8D%E7%BD%AE%E6%9E%84%E5%BB%BA%E5%91%BD%E4%BB%A4"><span class="toc-text">3.2 解决常见错误与配置构建命令</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-3-%E9%9B%86%E6%88%90%E7%AC%AC%E4%B8%89%E6%96%B9%E6%9C%8D%E5%8A%A1-%E4%BB%A5-Algolia-%E6%90%9C%E7%B4%A2%E4%B8%BA%E4%BE%8B"><span class="toc-text">3.3 集成第三方服务 (以 Algolia 搜索为例)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-4-%E7%BB%91%E5%AE%9A%E5%B9%B6%E5%8A%A0%E9%80%9F%E8%87%AA%E5%AE%9A%E4%B9%89%E5%9F%9F%E5%90%8D"><span class="toc-text">3.4 绑定并加速自定义域名</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>博客搭建教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">第七部分：多样化部署方案</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-02T10:13:45.000Z" title="发表于 2025-07-02 18:13:45">2025-07-02</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-19T11:21:28.510Z" title="更新于 2025-07-19 19:21:28">2025-07-19</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>12分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="第七部分：多样化部署方案"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/64413.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/64413.html"><header><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">博客搭建教程</a><h1 id="CrawlerTitle" itemprop="name headline">第七部分：多样化部署方案</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-02T10:13:45.000Z" title="发表于 2025-07-02 18:13:45">2025-07-02</time><time itemprop="dateCreated datePublished" datetime="2025-07-19T11:21:28.510Z" title="更新于 2025-07-19 19:21:28">2025-07-19</time></header><div id="postchat_postcontent"><h2 id="第七部分：多样化部署方案"><a href="#第七部分：多样化部署方案" class="headerlink" title="第七部分：多样化部署方案"></a>第七部分：多样化部署方案</h2><p>搭建并配置好我们的Hexo博客后，下一步就是将其发布到互联网上，让全世界的读者都能访问。部署静态博客有多种方式，从简单的手动部署到自动化的持续集成/持续部署（CI/CD）。本部分，我们将介绍几种主流的免费部署方案，并重点讲解如何配置和使用它们。</p><h3 id="1-部署准备：安装-Hexo-Git-部署插件"><a href="#1-部署准备：安装-Hexo-Git-部署插件" class="headerlink" title="1. 部署准备：安装 Hexo Git 部署插件"></a>1. 部署准备：安装 Hexo Git 部署插件</h3><p>Hexo 默认支持多种部署方式，最常用的一种是通过 Git 将生成的静态文件推送到远程仓库，然后由托管平台（如 GitHub Pages, Coding Pages 等）负责提供访问服务。我们需要安装 <code>hexo-deployer-git</code> 插件来实现这一功能。</p><p>在 Hexo 项目的根目录下，打开命令行工具，执行以下命令：</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 安装 Hexo Git 部署插件</span></span><br><span class="line">npm install hexo-deployer-git --save</span><br></pre></td></tr></tbody></table></figure><p>安装完成后，我们需要在 Hexo 的配置文件 <code>_config.yml</code> 中配置 <code>deploy</code> 部分。</p><p><strong>配置 <code>_config.yml</code> 的 <code>deploy</code> 部分：</strong></p><p>找到 Hexo 项目根目录下的 <code>_config.yml</code> 文件，滑动到文件末尾，通常会有一个 <code>deploy</code> 部分。如果不存在，我们可以手动添加。其基本配置格式如下：</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Deployment</span></span><br><span class="line"><span class="comment">## Docs: https://hexo.io/docs/one-command-deployment.html</span></span><br><span class="line"><span class="attr">deploy:</span></span><br><span class="line">  <span class="attr">type:</span> <span class="string">git</span> <span class="comment"># 部署类型，这里设置为 git</span></span><br><span class="line">  <span class="attr">repo:</span> <span class="string">&lt;repository</span> <span class="string">url&gt;</span> <span class="comment"># 你的 Git 仓库 URL。例如：**************:username/username.github.io.git (SSH 方式) 或 https://github.com/username/username.github.io.git (HTTPS 方式)</span></span><br><span class="line">  <span class="attr">branch:</span> <span class="string">&lt;branch</span> <span class="string">name&gt;</span> <span class="comment"># 部署分支。GitHub Pages 默认通常是 main/master 或 gh-pages。Vercel/Netlify 通常是构建触发的分支（如 main）</span></span><br><span class="line">  <span class="attr">message:</span> <span class="string">"feat: deploy blog via Hexo"</span> <span class="comment"># 可选：自定义部署提交信息</span></span><br></pre></td></tr></tbody></table></figure><p>这是一个 <code>_config.yml</code> 文件中 <code>deploy</code> 部分的截图示例：</p><p>在这个配置中：</p><ul><li><code>type: git</code> 指明了我们使用 Git 进行部署。</li><li><code>repo</code> 是你存储生成文件的 Git 仓库地址。对于 GitHub Pages，它通常是 <code>**************:YourGitHubName/YourGitHubName.github.io.git</code> (推荐使用 SSH 方式，需要配置 SSH Key) 或者 <code>https://github.com/YourGitHubName/YourGitHubName.github.io.git</code>。</li><li><code>branch</code> 是你希望将生成的静态文件推送到仓库的哪个分支。对于 GitHub Pages 用户网站（<code>username.github.io</code> 格式仓库），静态文件需要推送到 <code>main</code> 或 <code>master</code> 分支（取决于你仓库的默认分支以及 GitHub Pages 的配置）。对于项目网站，通常是 <code>gh-pages</code> 分支。对于 CI/CD 方式部署到 Vercel/Netlify，通常是将 Hexo 源文件推送到 <code>main</code> 分支，由 CI/CD 流程生成并部署。</li></ul><p>配置完成后，保存 <code>_config.yml</code> 文件。下次运行 <code>hexo deploy</code> 命令时，Hexo 就会自动执行生成静态文件 (<code>hexo generate</code>) 并将 <code>public</code> 目录下的内容推送到你指定的 Git 仓库和分支。</p><h3 id="2-主流免费部署平台对比与实践"><a href="#2-主流免费部署平台对比与实践" class="headerlink" title="2. 主流免费部署平台对比与实践"></a>2. 主流免费部署平台对比与实践</h3><p>静态博客的部署选择多样，特别是对于个人博客，有许多提供免费额度且功能强大的托管平台。下面我们对比并介绍几个主流的免费部署平台。</p><table><thead><tr><th align="left">平台</th><th align="left">CI/CD 支持</th><th align="left">免费额度</th><th align="left">CDN 情况</th><th align="left">国内访问速度</th><th align="left">优缺点</th><th align="left">推荐场景</th></tr></thead><tbody><tr><td align="left"><strong>GitHub Pages</strong></td><td align="left">有限 (需 Git Push) / 良好 (配合 GitHub Actions)</td><td align="left">无限空间，每月流量限制</td><td align="left">GitHub 自带 CDN</td><td align="left">较慢</td><td align="left"><strong>优点:</strong> 与 GitHub 工作流结合紧密，简单易用，适合托管静态网站。 <strong>缺点:</strong> 直接 Git Push 部署不够自动化，国内访问速度慢。</td><td align="left">GitHub 用户，对自动化要求不高或愿意配置 Actions</td></tr><tr><td align="left"><strong>Vercel</strong></td><td align="left">优秀</td><td align="left">构建时间/流量/函数调用</td><td align="left">Vercel 全球 CDN</td><td align="left">较快</td><td align="left"><strong>优点:</strong> Git 自动部署，界面友好，功能强大（Serverless Functions, Edge Functions），全球 CDN 节点丰富。 <strong>缺点:</strong> 免费额度有一定限制，复杂配置需付费。</td><td align="left">对部署自动化和访问速度有较高要求，乐于尝试新特性</td></tr><tr><td align="left"><strong>Netlify</strong></td><td align="left">优秀</td><td align="left">构建时间/流量/函数调用</td><td align="left">Netlify 全球 CDN</td><td align="left">较快</td><td align="left"><strong>优点:</strong> Git 自动部署，功能丰富（Forms, Identity, Functions），免费额度慷慨，社区活跃。 <strong>缺点:</strong> 免费额度相对 Vercel 更慷慨，但复杂配置需付费。</td><td align="left">对部署自动化和访问速度有较高要求，需要一些附加功能</td></tr><tr><td align="left"><strong>Coding Pages</strong></td><td align="left">良好</td><td align="left">无限空间，流量限制未知</td><td align="left">腾讯云 CDN</td><td align="left">较快</td><td align="left"><strong>优点:</strong> 国内平台，访问速度快，支持 Git 仓库。 <strong>缺点:</strong> 界面和文档不如 GitHub Pages/Vercel/Netlify 友好，自动化能力相对较弱。</td><td align="left">主要面向国内用户，希望提高国内访问速度</td></tr><tr><td align="left"><strong>Gitee Pages</strong></td><td align="left">良好 (需手动更新或 webhook)</td><td align="left">无限空间，流量限制未知</td><td align="left">Gitee 自带 CDN</td><td align="left">较快</td><td align="left"><strong>优点:</strong> 国内平台，访问速度快。 <strong>缺点:</strong> 免费版需要手动更新或配置 webhook，自动化程度最低。</td><td align="left">主要面向国内用户，对自动化要求不高</td></tr></tbody></table><h4 id="2-1-GitHub-Pages：基于-Git-Push-的传统部署"><a href="#2-1-GitHub-Pages：基于-Git-Push-的传统部署" class="headerlink" title="2.1 GitHub Pages：基于 Git Push 的传统部署"></a>2.1 GitHub Pages：基于 Git Push 的传统部署</h4><p>GitHub Pages 是 GitHub 提供的免费静态网站托管服务，特别适合托管个人博客。</p><p>部署步骤（使用 <code>hexo-deployer-git</code>）：</p><ol><li><p><strong>创建一个新的 GitHub 仓库</strong>：<br>仓库名称必须是 <code>&lt;你的GitHub用户名&gt;.github.io</code>（例如，我的 GitHub 用户名是 <code>exampleuser</code>，那么仓库名称就是 <code>exampleuser.github.io</code>）。这是一个特殊命名的仓库，GitHub 会自动将其主分支的静态内容发布到 <code>https://exampleuser.github.io</code> 域名下。请确保这是一个<strong>公共</strong>仓库。</p></li><li><p><strong>配置 SSH Key (可选但推荐)</strong>：<br>为了使用 <code>**************:...</code> 这种 SSH 方式提交代码，你需要在本地生成 SSH Key 并添加到你的 GitHub 账户设置中。这样在 <code>hexo deploy</code> 时就无需反复输入密码。具体步骤可以参考 GitHub 官方文档。</p></li><li><p><strong>配置 Hexo 的 <code>_config.yml</code></strong>：<br>修改 Hexo 项目根目录下的 <code>_config.yml</code> 文件，配置 <code>deploy</code> 部分。将 <code>repo</code> 设置为你刚刚创建的仓库的 SSH 或 HTTPS 地址，将 <code>branch</code> 设置为你的仓库的默认分支（通常是 <code>main</code> 或 <code>master</code>）。</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Deployment</span></span><br><span class="line"><span class="attr">deploy:</span></span><br><span class="line">  <span class="attr">type:</span> <span class="string">git</span></span><br><span class="line">  <span class="attr">repo:</span> <span class="string">**************:YourGitHubName/YourGitHubName.github.io.git</span> <span class="comment"># 你的 GitHub Pages 仓库地址 (SSH 方式)</span></span><br><span class="line">  <span class="comment"># 或使用 HTTPS 方式: repo: https://github.com/YourGitHubName/YourGitHubName.github.io.git</span></span><br><span class="line">  <span class="attr">branch:</span> <span class="string">main</span> <span class="comment"># 或者 master，取决于你的仓库设置</span></span><br><span class="line">  <span class="attr">message:</span> <span class="string">"Update blog via Hexo deploy"</span></span><br></pre></td></tr></tbody></table></figure><p>请将 <code>YourGitHubName</code> 替换为你的实际 GitHub 用户名。</p></li><li><p><strong>生成并部署</strong>：<br>在 Hexo 项目根目录打开命令行，依次执行以下命令：</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 清除缓存和已生成的静态文件 (重要，防止旧文件残留)</span></span><br><span class="line">hexo clean</span><br><span class="line"></span><br><span class="line"><span class="comment"># 生成静态文件，生成结果在 public 目录下</span></span><br><span class="line">hexo generate</span><br><span class="line"></span><br><span class="line"><span class="comment"># 部署到 GitHub Pages</span></span><br><span class="line">hexo deploy</span><br></pre></td></tr></tbody></table></figure><p><code>hexo deploy</code> 命令实际上是先执行 <code>hexo generate</code>，然后将 <code>public</code> 目录下的内容使用 Git 推送到 <code>_config.yml</code> 中指定的仓库和分支。</p></li><li><p><strong>GitHub Pages 设置确认</strong>：<br>部署成功后，访问你的 GitHub Pages 仓库页面，进入 <code>Settings</code> -&gt; <code>Pages</code>。确认 <code>Source</code> 设置为 <code>Deploy from a branch</code>，并且 <code>Branch</code> 设置为你部署的分支（例如 <code>main</code> 或 <code>master</code>）以及 <code>/root</code> 目录。首次部署可能需要几分钟到十几分钟才能生效。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://theme-next.js.org/images/github-pages.png" alt="GitHub Pages Settings 截图，展示 Source 和 Branch 配置"><br><em>图：GitHub Pages 仓库设置页面截图，确认从哪个分支和目录部署静态文件。</em></p><p><strong>注意:</strong> 随着 GitHub Actions 的普及，GitHub Pages 现在推荐使用 GitHub Actions 来构建和部署静态网站，而不是直接将生成好的静态文件推送到特定分支。如果我们使用 GitHub Actions (详见第 3 部分)，则 Pages 设置中的 <code>Source</code> 需要改为 <code>GitHub Actions</code>。</p></li></ol><h4 id="2-2-Vercel-Netlify：基于-Git-的自动化部署-CI-CD"><a href="#2-2-Vercel-Netlify：基于-Git-的自动化部署-CI-CD" class="headerlink" title="2.2 Vercel / Netlify：基于 Git 的自动化部署 (CI/CD)"></a>2.2 Vercel / Netlify：基于 Git 的自动化部署 (CI/CD)</h4><p>CI/CD 能将我们从繁琐的部署命令中解放出来，实现“一次推送，自动上线”。我们只需将<strong>博客源文件</strong>推送到 GitHub，云端服务器就会自动为我们完成构建和部署。</p><table><thead><tr><th align="left">平台</th><th align="left">CI/CD 支持</th><th align="left">免费额度</th><th align="left">CDN 情况</th><th align="left">国内访问速度</th><th align="left">优缺点</th><th align="left">推荐场景</th></tr></thead><tbody><tr><td align="left"><strong>Vercel</strong></td><td align="left"><strong>优秀</strong></td><td align="left">构建时间/流量</td><td align="left">Vercel 全球 CDN</td><td align="left"><strong>较慢 (官方) / 很快 (使用社区CDN)</strong></td><td align="left"><strong>优点:</strong> Git 自动部署，界面极友好，功能强大。 <strong>缺点:</strong> 官方节点国内访问慢，需额外配置加速。</td><td align="left"><strong>强烈推荐</strong>，对自动化和访问速度有高要求，愿意进行简单优化配置。</td></tr><tr><td align="left"><strong>Netlify</strong></td><td align="left">优秀</td><td align="left">构建时间/流量</td><td align="left">Netlify 全球 CDN</td><td align="left">较慢</td><td align="left"><strong>优点:</strong> 功能丰富，免费额度慷慨。 <strong>缺点:</strong> 国内访问速度同样不理想。</td><td align="left">Vercel 的优秀替代品，功能需求多样化。</td></tr><tr><td align="left"><strong>GitHub Pages</strong></td><td align="left">良好 (配合 Actions)</td><td align="left">无限空间</td><td align="left">GitHub 自带 CDN</td><td align="left">很慢</td><td align="left"><strong>优点:</strong> 与 GitHub 结合紧密。 <strong>缺点:</strong> 国内访问速度是主要瓶颈。</td><td align="left">对速度要求不高，或作为代码备份。</td></tr><tr><td align="left"><strong>Coding Pages</strong></td><td align="left">良好</td><td align="left">无限空间</td><td align="left">腾讯云 CDN</td><td align="left"><strong>很快</strong></td><td align="left"><strong>优点:</strong> 国内平台，访问速度顶尖。 <strong>缺点:</strong> CI/CD 配置比 Vercel 稍复杂。</td><td align="left">主要面向国内用户，追求极致国内速度。</td></tr><tr><td align="left"><strong>Gitee Pages</strong></td><td align="left">有限</td><td align="left">无限空间</td><td align="left">Gitee 自带 CDN</td><td align="left">很快</td><td align="left"><strong>优点:</strong> 国内平台，速度快。 <strong>缺点:</strong> 免费版需手动更新，自动化程度最低。</td><td align="left">对自动化要求不高，主要面向国内。</td></tr></tbody></table><p><strong>结论</strong>：对于追求<strong>自动化体验</strong>和<strong>全球化部署</strong>的用户，<strong>Vercel 是首选</strong>。虽然其官方节点在国内访问慢，但通过简单的社区方案即可完美解决，实现“鱼和熊掌兼得”。</p><h3 id="3-实战核心：Vercel-自动化部署与深度优化"><a href="#3-实战核心：Vercel-自动化部署与深度优化" class="headerlink" title="3. 实战核心：Vercel 自动化部署与深度优化"></a>3. 实战核心：Vercel 自动化部署与深度优化</h3><p>我们将以 Vercel 为例，走完从部署、踩坑到优化的完整流程。</p><h4 id="3-1-基础部署：从-Git-仓库到上线"><a href="#3-1-基础部署：从-Git-仓库到上线" class="headerlink" title="3.1 基础部署：从 Git 仓库到上线"></a>3.1 基础部署：从 Git 仓库到上线</h4><ol><li><strong>准备源文件仓库</strong>：确保你的 GitHub 仓库中存放的是 <strong>Hexo 的完整源文件</strong>（包含 <code>source/</code>, <code>themes/</code>, <code>_config.yml</code>, <code>package.json</code>等），而不是 <code>hexo g</code> 生成的 <code>public</code> 目录。</li><li><strong>在 Vercel 中导入项目</strong>：访问 Vercel 官网并用 GitHub 账号登录，点击 <code>Add New...</code> -&gt; <code>Project</code>，选择并导入你的 Hexo 源文件仓库。</li><li><strong>配置并部署</strong>：Vercel 会自动识别出是 Hexo 项目。<ul><li><strong>Framework Preset</strong>: 应为 <code>Hexo</code>。</li><li><strong>Root Directory</strong>: 保持 <code>./</code>。</li><li>点击 <code>Deploy</code>。</li></ul></li></ol><p>首次部署你可能会遇到一个经典错误。</p><h4 id="3-2-解决常见错误与配置构建命令"><a href="#3-2-解决常见错误与配置构建命令" class="headerlink" title="3.2 解决常见错误与配置构建命令"></a>3.2 解决常见错误与配置构建命令</h4><p><strong>错误：<code>sh: line 1: hexo: command not found</code></strong></p><p>这个报错意味着 Vercel 的服务器不认识 <code>hexo</code> 命令。原因是它只执行了 <code>hexo generate</code>，却没有安装 Hexo 本身。</p><p><strong>解决方案</strong>：我们需要修改构建命令，让 Vercel 在构建前先安装依赖。</p><ol><li>在 Vercel 项目后台，进入 <strong>Settings</strong> -&gt; <strong>General</strong>。</li><li>找到 <strong>Build &amp; Development Settings</strong>，覆盖 <strong>Build Command</strong> (构建命令) 为：<figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install &amp;&amp; hexo generate</span><br></pre></td></tr></tbody></table></figure>这条命令告诉 Vercel：先用 <code>npm install</code> 安装 <code>package.json</code> 中定义的所有依赖（包括 Hexo），成功后再执行 <code>hexo generate</code> 生成网站。</li><li>保存后，回到 <strong>Deployments</strong> 页面，对失败的部署选择 <strong>Redeploy</strong>。</li></ol><p>部署成功后，你的新工作流诞生了：<strong>本地写文章 -&gt; <code>git push</code> -&gt; Vercel 自动上线</strong>。你再也不需要在本地执行 <code>hexo g -d</code> 了！</p><h4 id="3-3-集成第三方服务-以-Algolia-搜索为例"><a href="#3-3-集成第三方服务-以-Algolia-搜索为例" class="headerlink" title="3.3 集成第三方服务 (以 Algolia 搜索为例)"></a>3.3 集成第三方服务 (以 Algolia 搜索为例)</h4><p>自动化流程中，像 <code>hexo algolia</code> 这种需要私密 API Key 的命令怎么办？答案是使用 Vercel 的<strong>环境变量</strong>。</p><ol><li><p><strong>在 Vercel 添加环境变量</strong>：</p><ul><li>进入 <strong>Settings</strong> -&gt; <strong>Environment Variables</strong>。</li><li>添加你的 Algolia 密钥，例如：<ul><li><code>ALGOLIA_APP_ID</code> = 你的 Application ID</li><li><code>ALGOLIA_API_KEY</code> = 你的 <strong>Admin API Key</strong> (用于写入)</li><li><code>ALGOLIA_INDEX_NAME</code> = 你的索引名</li></ul></li></ul></li><li><p><strong>修改 <code>_config.yml</code></strong>：让 Hexo 从环境变量中读取密钥，而不是明文存储。</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># _config.yml</span></span><br><span class="line"><span class="attr">algolia:</span></span><br><span class="line">  <span class="attr">appId:</span> <span class="string">process.env.ALGOLIA_APP_ID</span></span><br><span class="line">  <span class="attr">apiKey:</span> <span class="string">process.env.ALGOLIA_API_KEY</span></span><br><span class="line">  <span class="attr">indexName:</span> <span class="string">process.env.ALGOLIA_INDEX_NAME</span></span><br><span class="line">  <span class="attr">chunkSize:</span> <span class="number">5000</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>更新构建命令</strong>：在生成网站后，执行索引命令。同时，我们做一个优化：只在正式发布到主域名时才更新索引。</p><ul><li>将 <strong>Build Command</strong> 修改为：</li></ul><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install &amp;&amp; hexo generate &amp;&amp; <span class="keyword">if</span> [ <span class="string">"<span class="variable">$VERCEL_ENV</span>"</span> = <span class="string">"production"</span> ]; <span class="keyword">then</span> hexo algolia; <span class="keyword">else</span> <span class="built_in">echo</span> <span class="string">"Not in production, skipping Algolia indexing."</span>; <span class="keyword">fi</span></span><br></pre></td></tr></tbody></table></figure><p>这条命令利用 Vercel 的系统变量 <code>VERCEL_ENV</code> 判断，只有在生产环境 (<code>production</code>) 部署时，才执行 <code>hexo algolia</code>。</p></li></ol><h4 id="3-4-绑定并加速自定义域名"><a href="#3-4-绑定并加速自定义域名" class="headerlink" title="3.4 绑定并加速自定义域名"></a>3.4 绑定并加速自定义域名</h4><ol><li><p><strong>在 Vercel 绑定域名</strong>：在项目 <strong>Settings</strong> -&gt; <strong>Domains</strong> 中，添加你的自定义域名，例如 <code>prorise666.site</code>。</p></li><li><p><strong>国内访问速度优化</strong>：此时，你会发现用自定义域名访问非常慢。这是因为 Vercel 官方节点在国内的通病。幸运的是，社区提供了优秀的解决方案。</p></li><li><p><strong>配置 DNS (关键一步)</strong>：</p><ul><li>登录你的域名注册商（如 Spaceship, GoDaddy）。</li><li>找到你域名的 DNS 管理界面。</li><li><strong>删除</strong>所有指向 Vercel 官方 IP 的 <code>A</code> 记录。</li><li><strong>创建一条 CNAME 记录</strong>，配置如下：<ul><li><strong>类型 (Type):</strong> <code>CNAME</code></li><li><strong>主机/名称 (Host/Name):</strong> <code>@</code> (代表根域名)</li><li><strong>值/指向 (Value/Points to):</strong> <code>vercel.cdn.yt-blog.top</code></li></ul></li><li>保存设置。</li></ul></li><li><p><strong>重要：忽略 Vercel 警告</strong><br>配置 CNAME 后，Vercel 后台的域名状态会显示红色的 <strong>“Invalid Configuration”</strong> 警告。</p></li></ol><blockquote><p><strong>请放心忽略此警告！</strong> 这是因为我们的设置（社区 CNAME）与 Vercel 官方推荐（A 记录）不符。判断成功的唯一标准是：你的网站能通过域名正常访问，且速度变快了。</p></blockquote><ol start="5"><li><strong>最终优化：添加 Vercel 缓存配置</strong><br>为了让网站快上加快，我们可以配置 Vercel 的 CDN 缓存策略。<ul><li>在你的 Hexo 源文件仓库<strong>根目录</strong>下，创建一个 <code>vercel.json</code> 文件，内容如下：<figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">  <span class="attr">"headers"</span><span class="punctuation">:</span> <span class="punctuation">[</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">      <span class="attr">"source"</span><span class="punctuation">:</span> <span class="string">"/(.*)"</span><span class="punctuation">,</span></span><br><span class="line">      <span class="attr">"headers"</span><span class="punctuation">:</span> <span class="punctuation">[</span></span><br><span class="line">        <span class="punctuation">{</span></span><br><span class="line">          <span class="attr">"key"</span><span class="punctuation">:</span> <span class="string">"Cache-Control"</span><span class="punctuation">,</span></span><br><span class="line">          <span class="attr">"value"</span><span class="punctuation">:</span> <span class="string">"public, s-maxage=86400"</span></span><br><span class="line">        <span class="punctuation">}</span><span class="punctuation">,</span> <span class="punctuation">{</span></span><br><span class="line">          <span class="attr">"key"</span><span class="punctuation">:</span> <span class="string">"Vercel-CDN-Cache-Control"</span><span class="punctuation">,</span></span><br><span class="line">          <span class="attr">"value"</span><span class="punctuation">:</span> <span class="string">"max-age=3600"</span></span><br><span class="line">        <span class="punctuation">}</span></span><br><span class="line">      <span class="punctuation">]</span></span><br><span class="line">    <span class="punctuation">}</span></span><br><span class="line">  <span class="punctuation">]</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li><li>将 <code>vercel.json</code> 文件 <code>git push</code> 到仓库，Vercel 会自动应用新的缓存规则。</li></ul></li></ol><p>至此，你已经拥有了一个全自动、高可用、在国内访问速度飞快的现代化博客！</p><p>CI/CD 的自动化极大地提高了部署效率，减少了手动操作可能带来的错误，让我们能更专注于内容创作本身。</p></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/64413.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/64413.html&quot;)">第七部分：多样化部署方案</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/64413.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=第七部分：多样化部署方案&amp;url=https://prorise666.site/posts/64413.html&amp;pic=https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>框架技术<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Hexo<span class="categoryesPageCount">31</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>博客搭建教程<span class="tagsPageCount">31</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/5555.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">第六部分：SEO终极攻略与实践</div></div></a></div><div class="next-post pull-right"><a href="/posts/17934.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">第八部分：内容创作与长期维护</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/57565.html" title="12.Twikoo 美化：自定义评论回复邮件模板"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">12.Twikoo 美化：自定义评论回复邮件模板</div></div></a></div><div><a href="/posts/24286.html" title="10.内容扩展：添加“安全跳转”中间页"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">10.内容扩展：添加“安全跳转”中间页</div></div></a></div><div><a href="/posts/65188.html" title="11.Twikoo 美化：添加自定义表情包"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">11.Twikoo 美化：添加自定义表情包</div></div></a></div><div><a href="/posts/20246.html" title="13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）</div></div></a></div><div><a href="/posts/34091.html" title="15.主题魔改：自定义全站字体"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">15.主题魔改：自定义全站字体</div></div></a></div><div><a href="/posts/11486.html" title="17.内容扩展：添加“前端代码实时预览”沙箱"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">17.内容扩展：添加“前端代码实时预览”沙箱</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"第七部分：多样化部署方案",date:"2025-07-02 18:13:45",updated:"2025-07-19 19:21:28",tags:["博客搭建教程"],categories:["框架技术","Hexo"],content:'\n## 第七部分：多样化部署方案\n\n搭建并配置好我们的Hexo博客后，下一步就是将其发布到互联网上，让全世界的读者都能访问。部署静态博客有多种方式，从简单的手动部署到自动化的持续集成/持续部署（CI/CD）。本部分，我们将介绍几种主流的免费部署方案，并重点讲解如何配置和使用它们。\n\n### 1. 部署准备：安装 Hexo Git 部署插件\n\nHexo 默认支持多种部署方式，最常用的一种是通过 Git 将生成的静态文件推送到远程仓库，然后由托管平台（如 GitHub Pages, Coding Pages 等）负责提供访问服务。我们需要安装 `hexo-deployer-git` 插件来实现这一功能。\n\n在 Hexo 项目的根目录下，打开命令行工具，执行以下命令：\n\n```bash\n# 安装 Hexo Git 部署插件\nnpm install hexo-deployer-git --save\n```\n\n安装完成后，我们需要在 Hexo 的配置文件 `_config.yml` 中配置 `deploy` 部分。\n\n**配置 `_config.yml` 的 `deploy` 部分：**\n\n找到 Hexo 项目根目录下的 `_config.yml` 文件，滑动到文件末尾，通常会有一个 `deploy` 部分。如果不存在，我们可以手动添加。其基本配置格式如下：\n\n```yaml\n# Deployment\n## Docs: https://hexo.io/docs/one-command-deployment.html\ndeploy:\n  type: git # 部署类型，这里设置为 git\n  repo: <repository url> # 你的 Git 仓库 URL。例如：**************:username/username.github.io.git (SSH 方式) 或 https://github.com/username/username.github.io.git (HTTPS 方式)\n  branch: <branch name> # 部署分支。GitHub Pages 默认通常是 main/master 或 gh-pages。Vercel/Netlify 通常是构建触发的分支（如 main）\n  message: "feat: deploy blog via Hexo" # 可选：自定义部署提交信息\n```\n\n这是一个 `_config.yml` 文件中 `deploy` 部分的截图示例：\n\n在这个配置中：\n- `type: git` 指明了我们使用 Git 进行部署。\n- `repo` 是你存储生成文件的 Git 仓库地址。对于 GitHub Pages，它通常是 `**************:YourGitHubName/YourGitHubName.github.io.git` (推荐使用 SSH 方式，需要配置 SSH Key) 或者 `https://github.com/YourGitHubName/YourGitHubName.github.io.git`。\n- `branch` 是你希望将生成的静态文件推送到仓库的哪个分支。对于 GitHub Pages 用户网站（`username.github.io` 格式仓库），静态文件需要推送到 `main` 或 `master` 分支（取决于你仓库的默认分支以及 GitHub Pages 的配置）。对于项目网站，通常是 `gh-pages` 分支。对于 CI/CD 方式部署到 Vercel/Netlify，通常是将 Hexo 源文件推送到 `main` 分支，由 CI/CD 流程生成并部署。\n\n配置完成后，保存 `_config.yml` 文件。下次运行 `hexo deploy` 命令时，Hexo 就会自动执行生成静态文件 (`hexo generate`) 并将 `public` 目录下的内容推送到你指定的 Git 仓库和分支。\n\n### 2. 主流免费部署平台对比与实践\n\n静态博客的部署选择多样，特别是对于个人博客，有许多提供免费额度且功能强大的托管平台。下面我们对比并介绍几个主流的免费部署平台。\n\n| 平台           | CI/CD 支持 | 免费额度               | CDN 情况         | 国内访问速度 | 优缺点                                                                                                | 推荐场景                                 |\n| :------------- | :--------- | :--------------------- | :--------------- | :----------- | :------------------------------------------------------------------------------------------------------ | :--------------------------------------- |\n| **GitHub Pages** | 有限 (需 Git Push) / 良好 (配合 GitHub Actions) | 无限空间，每月流量限制 | GitHub 自带 CDN    | 较慢         | **优点:** 与 GitHub 工作流结合紧密，简单易用，适合托管静态网站。 **缺点:** 直接 Git Push 部署不够自动化，国内访问速度慢。 | GitHub 用户，对自动化要求不高或愿意配置 Actions |\n| **Vercel**     | 优秀       | 构建时间/流量/函数调用 | Vercel 全球 CDN  | 较快         | **优点:** Git 自动部署，界面友好，功能强大（Serverless Functions, Edge Functions），全球 CDN 节点丰富。 **缺点:** 免费额度有一定限制，复杂配置需付费。 | 对部署自动化和访问速度有较高要求，乐于尝试新特性 |\n| **Netlify**    | 优秀       | 构建时间/流量/函数调用 | Netlify 全球 CDN | 较快         | **优点:** Git 自动部署，功能丰富（Forms, Identity, Functions），免费额度慷慨，社区活跃。 **缺点:** 免费额度相对 Vercel 更慷慨，但复杂配置需付费。 | 对部署自动化和访问速度有较高要求，需要一些附加功能 |\n| **Coding Pages** | 良好       | 无限空间，流量限制未知 | 腾讯云 CDN       | 较快         | **优点:** 国内平台，访问速度快，支持 Git 仓库。 **缺点:** 界面和文档不如 GitHub Pages/Vercel/Netlify 友好，自动化能力相对较弱。 | 主要面向国内用户，希望提高国内访问速度       |\n| **Gitee Pages**  | 良好 (需手动更新或 webhook) | 无限空间，流量限制未知 | Gitee 自带 CDN   | 较快         | **优点:** 国内平台，访问速度快。 **缺点:** 免费版需要手动更新或配置 webhook，自动化程度最低。             | 主要面向国内用户，对自动化要求不高           |\n\n#### 2.1 GitHub Pages：基于 Git Push 的传统部署\n\nGitHub Pages 是 GitHub 提供的免费静态网站托管服务，特别适合托管个人博客。\n\n部署步骤（使用 `hexo-deployer-git`）：\n\n1.  **创建一个新的 GitHub 仓库**：\n    仓库名称必须是 `<你的GitHub用户名>.github.io`（例如，我的 GitHub 用户名是 `exampleuser`，那么仓库名称就是 `exampleuser.github.io`）。这是一个特殊命名的仓库，GitHub 会自动将其主分支的静态内容发布到 `https://exampleuser.github.io` 域名下。请确保这是一个**公共**仓库。\n2.  **配置 SSH Key (可选但推荐)**：\n    为了使用 `**************:...` 这种 SSH 方式提交代码，你需要在本地生成 SSH Key 并添加到你的 GitHub 账户设置中。这样在 `hexo deploy` 时就无需反复输入密码。具体步骤可以参考 GitHub 官方文档。\n3.  **配置 Hexo 的 `_config.yml`**：\n    修改 Hexo 项目根目录下的 `_config.yml` 文件，配置 `deploy` 部分。将 `repo` 设置为你刚刚创建的仓库的 SSH 或 HTTPS 地址，将 `branch` 设置为你的仓库的默认分支（通常是 `main` 或 `master`）。\n\n    ```yaml\n    # Deployment\n    deploy:\n      type: git\n      repo: **************:YourGitHubName/YourGitHubName.github.io.git # 你的 GitHub Pages 仓库地址 (SSH 方式)\n      # 或使用 HTTPS 方式: repo: https://github.com/YourGitHubName/YourGitHubName.github.io.git\n      branch: main # 或者 master，取决于你的仓库设置\n      message: "Update blog via Hexo deploy"\n    ```\n    请将 `YourGitHubName` 替换为你的实际 GitHub 用户名。\n4.  **生成并部署**：\n    在 Hexo 项目根目录打开命令行，依次执行以下命令：\n\n    ```bash\n    # 清除缓存和已生成的静态文件 (重要，防止旧文件残留)\n    hexo clean\n\n    # 生成静态文件，生成结果在 public 目录下\n    hexo generate\n\n    # 部署到 GitHub Pages\n    hexo deploy\n    ```\n    `hexo deploy` 命令实际上是先执行 `hexo generate`，然后将 `public` 目录下的内容使用 Git 推送到 `_config.yml` 中指定的仓库和分支。\n\n5.  **GitHub Pages 设置确认**：\n    部署成功后，访问你的 GitHub Pages 仓库页面，进入 `Settings` -> `Pages`。确认 `Source` 设置为 `Deploy from a branch`，并且 `Branch` 设置为你部署的分支（例如 `main` 或 `master`）以及 `/root` 目录。首次部署可能需要几分钟到十几分钟才能生效。\n\n    ![GitHub Pages Settings 截图，展示 Source 和 Branch 配置](https://theme-next.js.org/images/github-pages.png)\n    *图：GitHub Pages 仓库设置页面截图，确认从哪个分支和目录部署静态文件。*\n\n    **注意:** 随着 GitHub Actions 的普及，GitHub Pages 现在推荐使用 GitHub Actions 来构建和部署静态网站，而不是直接将生成好的静态文件推送到特定分支。如果我们使用 GitHub Actions (详见第 3 部分)，则 Pages 设置中的 `Source` 需要改为 `GitHub Actions`。\n\n#### 2.2 Vercel / Netlify：基于 Git 的自动化部署 (CI/CD)\n\n\nCI/CD 能将我们从繁琐的部署命令中解放出来，实现“一次推送，自动上线”。我们只需将**博客源文件**推送到 GitHub，云端服务器就会自动为我们完成构建和部署。\n\n| 平台 | CI/CD 支持 | 免费额度 | CDN 情况 | 国内访问速度 | 优缺点 | 推荐场景 |\n| :--- | :--- | :--- | :--- | :--- | :--- | :--- |\n| **Vercel** | **优秀** | 构建时间/流量 | Vercel 全球 CDN | **较慢 (官方) / 很快 (使用社区CDN)** | **优点:** Git 自动部署，界面极友好，功能强大。 **缺点:** 官方节点国内访问慢，需额外配置加速。 | **强烈推荐**，对自动化和访问速度有高要求，愿意进行简单优化配置。 |\n| **Netlify** | 优秀 | 构建时间/流量 | Netlify 全球 CDN | 较慢 | **优点:** 功能丰富，免费额度慷慨。 **缺点:** 国内访问速度同样不理想。 | Vercel 的优秀替代品，功能需求多样化。 |\n| **GitHub Pages** | 良好 (配合 Actions) | 无限空间 | GitHub 自带 CDN | 很慢 | **优点:** 与 GitHub 结合紧密。 **缺点:** 国内访问速度是主要瓶颈。 | 对速度要求不高，或作为代码备份。 |\n| **Coding Pages** | 良好 | 无限空间 | 腾讯云 CDN | **很快** | **优点:** 国内平台，访问速度顶尖。 **缺点:** CI/CD 配置比 Vercel 稍复杂。 | 主要面向国内用户，追求极致国内速度。 |\n| **Gitee Pages** | 有限 | 无限空间 | Gitee 自带 CDN | 很快 | **优点:** 国内平台，速度快。 **缺点:** 免费版需手动更新，自动化程度最低。 | 对自动化要求不高，主要面向国内。 |\n\n**结论**：对于追求**自动化体验**和**全球化部署**的用户，**Vercel 是首选**。虽然其官方节点在国内访问慢，但通过简单的社区方案即可完美解决，实现“鱼和熊掌兼得”。\n\n### 3. 实战核心：Vercel 自动化部署与深度优化\n\n我们将以 Vercel 为例，走完从部署、踩坑到优化的完整流程。\n\n#### 3.1 基础部署：从 Git 仓库到上线\n\n1.  **准备源文件仓库**：确保你的 GitHub 仓库中存放的是 **Hexo 的完整源文件**（包含 `source/`, `themes/`, `_config.yml`, `package.json`等），而不是 `hexo g` 生成的 `public` 目录。\n2.  **在 Vercel 中导入项目**：访问 Vercel 官网并用 GitHub 账号登录，点击 `Add New...` -> `Project`，选择并导入你的 Hexo 源文件仓库。\n3.  **配置并部署**：Vercel 会自动识别出是 Hexo 项目。\n    *   **Framework Preset**: 应为 `Hexo`。\n    *   **Root Directory**: 保持 `./`。\n    *   点击 `Deploy`。\n\n首次部署你可能会遇到一个经典错误。\n\n#### 3.2 解决常见错误与配置构建命令\n\n**错误：`sh: line 1: hexo: command not found`**\n\n这个报错意味着 Vercel 的服务器不认识 `hexo` 命令。原因是它只执行了 `hexo generate`，却没有安装 Hexo 本身。\n\n**解决方案**：我们需要修改构建命令，让 Vercel 在构建前先安装依赖。\n\n1.  在 Vercel 项目后台，进入 **Settings** -> **General**。\n2.  找到 **Build & Development Settings**，覆盖 **Build Command** (构建命令) 为：\n    ```bash\n    npm install && hexo generate\n    ```\n    这条命令告诉 Vercel：先用 `npm install` 安装 `package.json` 中定义的所有依赖（包括 Hexo），成功后再执行 `hexo generate` 生成网站。\n3.  保存后，回到 **Deployments** 页面，对失败的部署选择 **Redeploy**。\n\n部署成功后，你的新工作流诞生了：**本地写文章 -> `git push` -> Vercel 自动上线**。你再也不需要在本地执行 `hexo g -d` 了！\n\n#### 3.3 集成第三方服务 (以 Algolia 搜索为例)\n\n自动化流程中，像 `hexo algolia` 这种需要私密 API Key 的命令怎么办？答案是使用 Vercel 的**环境变量**。\n\n1.  **在 Vercel 添加环境变量**：\n    *   进入 **Settings** -> **Environment Variables**。\n    *   添加你的 Algolia 密钥，例如：\n        *   `ALGOLIA_APP_ID` = 你的 Application ID\n        *   `ALGOLIA_API_KEY` = 你的 **Admin API Key** (用于写入)\n        *   `ALGOLIA_INDEX_NAME` = 你的索引名\n\n2.  **修改 `_config.yml`**：让 Hexo 从环境变量中读取密钥，而不是明文存储。\n    ```yaml\n    # _config.yml\n    algolia:\n      appId: process.env.ALGOLIA_APP_ID\n      apiKey: process.env.ALGOLIA_API_KEY\n      indexName: process.env.ALGOLIA_INDEX_NAME\n      chunkSize: 5000\n    ```\n\n3.  **更新构建命令**：在生成网站后，执行索引命令。同时，我们做一个优化：只在正式发布到主域名时才更新索引。\n    *   将 **Build Command** 修改为：\n    ```bash\n    npm install && hexo generate && if [ "$VERCEL_ENV" = "production" ]; then hexo algolia; else echo "Not in production, skipping Algolia indexing."; fi\n    ```\n    这条命令利用 Vercel 的系统变量 `VERCEL_ENV` 判断，只有在生产环境 (`production`) 部署时，才执行 `hexo algolia`。\n\n#### 3.4 绑定并加速自定义域名\n\n1.  **在 Vercel 绑定域名**：在项目 **Settings** -> **Domains** 中，添加你的自定义域名，例如 `prorise666.site`。\n2.  **国内访问速度优化**：此时，你会发现用自定义域名访问非常慢。这是因为 Vercel 官方节点在国内的通病。幸运的是，社区提供了优秀的解决方案。\n3.  **配置 DNS (关键一步)**：\n    *   登录你的域名注册商（如 Spaceship, GoDaddy）。\n    *   找到你域名的 DNS 管理界面。\n    *   **删除**所有指向 Vercel 官方 IP 的 `A` 记录。\n    *   **创建一条 CNAME 记录**，配置如下：\n        *   **类型 (Type):** `CNAME`\n        *   **主机/名称 (Host/Name):** `@` (代表根域名)\n        *   **值/指向 (Value/Points to):** `vercel.cdn.yt-blog.top`\n    *   保存设置。\n\n4.  **重要：忽略 Vercel 警告**\n    配置 CNAME 后，Vercel 后台的域名状态会显示红色的 **"Invalid Configuration"** 警告。\n    \n> **请放心忽略此警告！** 这是因为我们的设置（社区 CNAME）与 Vercel 官方推荐（A 记录）不符。判断成功的唯一标准是：你的网站能通过域名正常访问，且速度变快了。\n    \n5.  **最终优化：添加 Vercel 缓存配置**\n    为了让网站快上加快，我们可以配置 Vercel 的 CDN 缓存策略。\n    *   在你的 Hexo 源文件仓库**根目录**下，创建一个 `vercel.json` 文件，内容如下：\n        ```json\n        {\n          "headers": [\n            {\n              "source": "/(.*)",\n              "headers": [\n                {\n                  "key": "Cache-Control",\n                  "value": "public, s-maxage=86400"\n                }, {\n                  "key": "Vercel-CDN-Cache-Control",\n                  "value": "max-age=3600"\n                }\n              ]\n            }\n          ]\n        }\n        ```\n    *   将 `vercel.json` 文件 `git push` 到仓库，Vercel 会自动应用新的缓存规则。\n\n至此，你已经拥有了一个全自动、高可用、在国内访问速度飞快的现代化博客！\n\nCI/CD 的自动化极大地提高了部署效率，减少了手动操作可能带来的错误，让我们能更专注于内容创作本身。'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E9%83%A8%E5%88%86%EF%BC%9A%E5%A4%9A%E6%A0%B7%E5%8C%96%E9%83%A8%E7%BD%B2%E6%96%B9%E6%A1%88"><span class="toc-number">1.</span> <span class="toc-text">第七部分：多样化部署方案</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E9%83%A8%E7%BD%B2%E5%87%86%E5%A4%87%EF%BC%9A%E5%AE%89%E8%A3%85-Hexo-Git-%E9%83%A8%E7%BD%B2%E6%8F%92%E4%BB%B6"><span class="toc-number">1.1.</span> <span class="toc-text">1. 部署准备：安装 Hexo Git 部署插件</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E4%B8%BB%E6%B5%81%E5%85%8D%E8%B4%B9%E9%83%A8%E7%BD%B2%E5%B9%B3%E5%8F%B0%E5%AF%B9%E6%AF%94%E4%B8%8E%E5%AE%9E%E8%B7%B5"><span class="toc-number">1.2.</span> <span class="toc-text">2. 主流免费部署平台对比与实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-GitHub-Pages%EF%BC%9A%E5%9F%BA%E4%BA%8E-Git-Push-%E7%9A%84%E4%BC%A0%E7%BB%9F%E9%83%A8%E7%BD%B2"><span class="toc-number">1.2.1.</span> <span class="toc-text">2.1 GitHub Pages：基于 Git Push 的传统部署</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-Vercel-Netlify%EF%BC%9A%E5%9F%BA%E4%BA%8E-Git-%E7%9A%84%E8%87%AA%E5%8A%A8%E5%8C%96%E9%83%A8%E7%BD%B2-CI-CD"><span class="toc-number">1.2.2.</span> <span class="toc-text">2.2 Vercel / Netlify：基于 Git 的自动化部署 (CI/CD)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%AE%9E%E6%88%98%E6%A0%B8%E5%BF%83%EF%BC%9AVercel-%E8%87%AA%E5%8A%A8%E5%8C%96%E9%83%A8%E7%BD%B2%E4%B8%8E%E6%B7%B1%E5%BA%A6%E4%BC%98%E5%8C%96"><span class="toc-number">1.3.</span> <span class="toc-text">3. 实战核心：Vercel 自动化部署与深度优化</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#3-1-%E5%9F%BA%E7%A1%80%E9%83%A8%E7%BD%B2%EF%BC%9A%E4%BB%8E-Git-%E4%BB%93%E5%BA%93%E5%88%B0%E4%B8%8A%E7%BA%BF"><span class="toc-number">1.3.1.</span> <span class="toc-text">3.1 基础部署：从 Git 仓库到上线</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-2-%E8%A7%A3%E5%86%B3%E5%B8%B8%E8%A7%81%E9%94%99%E8%AF%AF%E4%B8%8E%E9%85%8D%E7%BD%AE%E6%9E%84%E5%BB%BA%E5%91%BD%E4%BB%A4"><span class="toc-number">1.3.2.</span> <span class="toc-text">3.2 解决常见错误与配置构建命令</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-3-%E9%9B%86%E6%88%90%E7%AC%AC%E4%B8%89%E6%96%B9%E6%9C%8D%E5%8A%A1-%E4%BB%A5-Algolia-%E6%90%9C%E7%B4%A2%E4%B8%BA%E4%BE%8B"><span class="toc-number">1.3.3.</span> <span class="toc-text">3.3 集成第三方服务 (以 Algolia 搜索为例)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-4-%E7%BB%91%E5%AE%9A%E5%B9%B6%E5%8A%A0%E9%80%9F%E8%87%AA%E5%AE%9A%E4%B9%89%E5%9F%9F%E5%90%8D"><span class="toc-number">1.3.4.</span> <span class="toc-text">3.4 绑定并加速自定义域名</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>