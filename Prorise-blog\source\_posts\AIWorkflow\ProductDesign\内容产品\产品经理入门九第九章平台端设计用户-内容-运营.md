---
title: 产品经理入门（九）：第九章：平台端设计（用户-内容-运营）
categories:
  - 产品经理
tags:
  - 产品经理教程
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp'
comments: true
toc: true
ai: true
abbrlink: 38041
date: 2025-07-21 00:13:45
---

# 第九章：平台端设计（用户-内容-运营）

![image-20250721124904426](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721124904426.png)

在前面两个章节，我们已经为产品的“前台”——用户端和自媒体端，设计好了舞台和化妆间。现在，我们需要开始搭建整个剧院的“**后台**”——**平台端（Admin Backend）**。

这是我们作为平台运营和管理人员，进行日常工作的“驾驶舱”，是整个产品生态能够有序、健康运转的中枢神经。我们将从这个后台最基础，也是最重要的模块开始：**用户管理**。

## 9.1 用户管理

我设计用户管理模块的核心思路是“**分类与控制**”。正如思考题所提示的，管理一名普通的内容消费者，和管理一名专业的内容创作者，其关注点和所需要的工具是截然不同的。因此，我的后台设计，必须清晰地将他们分类，并提供差异化的管理能力。

### 9.1.1 学习目标

在本节中，我的目标是带大家掌握一套专业的后台用户管理系统的设计方法。我们将学习如何分别为 **普通用户** 和 **自媒体用户** 设计管理列表，并重点拆解 **自媒体的审核流程**，最后还会介绍一个平台运营中非常实用的高级功能——**马甲管理**。

### 9.1.2 普通用户管理

![image-20250721124938842](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721124938842.png)

这是我们用户管理的基础模块，用于管理平台上所有的内容消费者。

#### 1. 用户列表与查询

一个合格的用户列表，必须具备强大的查询和筛选能力。我需要为运营同事，提供多维度的查询字段，比如：

* **昵称**
* **手机号码**
* **用户状态**（如：正常、已封禁）
* **用户性别**

列表的表头，则需要清晰地展示用户的核心信息，如 `昵称`、`头像`、`性别`、`地区`、`手机号`、`用户状态` 等。

#### 2. 用户详情查看

在操作列，点击“查看”，运营同事可以进入用户的详情页，看到该用户更完整的资料、行为日志、消费记录等。

#### 3. 用户状态管理（封禁/解封）

这是最重要的管理权限。在操作列，我必须提供“**封禁**”功能。当一个用户出现违规行为时，运营同事可以将其账号封禁。当然，也必须提供对应的“**解封**”功能。

### 9.1.3 自媒体用户管理

![image-20250721125236680](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721125236680.png)

管理创作者，我们需要关注比普通用户更多的信息。

#### 1. 创作者列表与查询

创作者列表的设计，在普通用户列表的基础上，我会额外增加几个关键的展示字段和筛选条件：

* **认证类型**：清晰地标识出该创作者是“个人认证”还是“企业认证”。
* **认证时间**：记录其通过审核、正式成为创作者的时间。

#### 2. 自媒体审核

![image-20250721125408677](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721125408677.png)

这是自媒体用户管理中，一个独立且至关重要的工作流，我通常会把它设计成一个单独的页面。

* **待审核列表**：这个页面的默认视图，就是所有提交了入驻申请、正在等待审核的用户列表。
* **审核详情页（通过/驳回）**：运营同事点击“审核”后，可以查看该用户提交的所有资质信息。在这个页面，必须有两个明确的操作按钮：“**通过**”和“**驳回**”。如果选择“驳回”，还需要提供一个填写驳回理由的输入框。
* **审核历史记录**：系统需要记录所有的审核操作，便于未来追溯。



---

### 9.1.4 马甲管理

现在，我们来探讨一个平台运营中，非常实用甚至可以说是必不可少的高阶功能——**马甲管理**。

#### 1. 马甲管理的价值与“鲶鱼效应”

“马甲”，指的就是由我们平台内部运营人员，在后台创建和控制的“虚拟用户”。我设计这个功能，主要是为了在社区运营中，起到“**鲶鱼效应**”——即，通过引入一些活跃的“鲶鱼”（马甲），来激活整个“鱼塘”（真实用户生态）的活力。

它的核心价值主要体现在两方面：

1. **制造争议、热点话题，带节奏**：在社区冷启动或需要引导舆论时，我的运营同事可以使用马甲，发布一些具有话题性的内容，引发用户讨论，掌握社区的话题走向。
2. **灌水，活跃社区氛围**：在社区初期用户较少时，通过马甲发布一些日常内容、进行评论和互动，可以快速地让社区看起来“有人气”，打破“无人区”的尴尬，从而吸引真实用户加入和发言。

#### 2. 添加马甲

![image-20250721131112565](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131112565.png)

为了让运营同事能方便地创建这些虚拟用户，我需要设计一个“**新增马甲**”的功能，它通常是一个后台的弹窗表单。

* **核心字段**：表单中需要包含创建逼真用户所需的核心字段，比如 `马甲昵称`、`头像`、`性别`、`地区` 等。具体需要哪些字段，由我们产品的实际业务决定。
* **归属管理员**：这是我设计中非常关键的一环。为了分工明确、责任到人，**每一个马甲，都必须可以分配给一个指定的管理员**。这样，我们就能清晰地知道，哪个马甲由哪位运营同事负责使用和维护。

#### 3. 马甲列表

![image-20250721131135140](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131135140.png)

所有创建的马甲，都需要在一个单独的“**马甲管理**”列表中进行统一的查看和维护。这个列表，只有特定的、高权限的管理员才能看到。

* **列表设计要点**：
    1. **查询与筛选**：列表必须提供强大的查询功能，特别是要支持按“**归属管理员**”进行筛选，方便运营主管查看自己团队成员名下的马甲。
    2. **信息展示**：列表中需要清晰地展示 `马甲昵称`、`归属管理员`、`状态` 等核心信息。
    3. **技术实现**：一个设计要点是，马甲可以不需要像真实用户一样，拥有完整的账号密码体系。从技术实现上，它可以是一个仅有昵称、头像等信息的“虚拟身份”，能通过后台直接进行内容发布和评论即可。



---

## 9.2 内容管理

![image-20250721131945141](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131945141.png)

在第八章，我们为创作者（自媒体）设计了他们的“创作车间”。但这引出了一个问题：作为平台，我们难道只需要提供工具，然后对海量的内容放任不管吗？

答案显然是 **否定** 的。

一个健康的内容生态，平台绝不能只当一个被动的“房东”，而必须是一个积极的“**秩序维护者**”和“**价值发现者**”。**内容管理** 后台，就是我们履行这个职责的核心工具。

### 9.2.1 学习目标

在本节中，我的目标是带大家设计一个专业、高效的内容管理后台。我们将重点学习 **内容列表** 的设计，掌握如何实现强大的 **查询与筛选**、严谨的 **内容审核** 流程，以及精细化的 **内容推荐与加权** 等高级运营功能。

### 9.2.2 内容列表

![image-20250721132036484](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721132036484.png)

内容管理模块的核心，就是这张包罗万象的 **内容列表**。这是我们的运营和审核同学，日常工作中停留时间最长的页面。我设计的核心目标是 **效率** 和 **掌控力**。

#### 1. 内容查询与筛选

一个内容平台，文章动辄成千上万。如果不能让运营同学在 3 秒内找到他想要的内容，那这个后台就是失败的。因此，我必须设计一套强大的查询与筛选系统。

* **基础筛选字段**：根据图例，至少要包含：
  * `文章标题`
  * `作者昵称`
  * `发表时间`（支持按时间范围筛选）
  * `文章分类`

* **我的拓展设计**：为了让管理更精细，我还会额外增加几个关键的筛选条件：
  * `内容ID`：用于研发同学进行精准的问题定位。
  * `来源`：这是图例中提到的一个设计要点。我必须让运营可以筛选出 **“自媒体发布”** 或 **“普通用户发布”**（如果我们的产品支持）的内容，因为这两者的审核标准和权重可能不同。
  * `审核状态`：这是审核人员最高频使用的筛选条件。他们可以通过筛选“**待审核**”状态，快速进入自己的工作队列。

#### 2. 内容审核（通过/驳回/下架）

这是内容列表的“控制核心”，体现在“**操作**”这一列。一个严谨的后台，其可执行的操作，必须与内容的“**审核状态**”严格绑定。我会将它设计成一个状态机：

* **当内容状态为“待审核”时**：
    操作列应提供：`查看`、`通过`、`驳回`。
  * **设计要点**：点击“驳回”时，必须弹出一个输入框，让审核人员填写驳回理由，这个理由会反馈给创作者。

* **当内容状态为“已通过”（即已上线）时**：
    操作列应变为：`查看`、`下架`、`删除`。
  * **设计要点**：“下架”是一个软删除，内容仅对用户不可见，作者后台依然可见；而“删除”则是一个硬删除，会彻底清除内容。我必须为“删除”操作，设计一个“二次确认”的弹窗，防止误操作。

* **当内容状态为“已驳回”或“已下架”时**：
    操作列可以简化为：`查看`、`删除`。

#### 3. 内容推荐与加权

除了基础的审核，一个优秀的后台，还要能让运营同事对优质内容进行“助推”。我会增加几个高级运营功能：

* **置顶**：如图例所示，这是最常见的运营手段。提供一个“置顶”按钮，可以将优质内容在前端的某个列表（如分类列表、频道首页）顶部固定显示。我还会设计一个“取消置顶”的功能。
* **加权**：这是一个更精细化的运营工具。我会在后台为每篇文章增加一个“**权重值**”输入框（比如 1-100）。我们前端的算法推荐或热度排序公式，就可以把这个“人工权重”作为一个重要的计算因子。这样，我就实现了“**人工编辑意志**”与“**算法推荐**”的完美结合。
* **推送**：对于 S 级的顶级内容，我还会设计一个“**推送**”按钮。运营同事点击后，可以将这篇文章通过 Push（推送通知）的形式，直接触达我们的用户，实现最大化的曝光。


---

### 9.2.3 敏感词管理

在我们的内容列表中，审核人员需要对“待审核”的内容进行人工处理。但面对 UGC 平台海量的内容生产，如果完全依赖人工，审核团队会被瞬间淹没。

因此，我必须设计一套 **自动化的初审过滤系统**，来分担团队的压力，并将最明显的违规内容，拦截在“摇篮”里。这套系统的核心，就是 **敏感词管理**。

![image-20250721132535224](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721132535224.png)

#### 1. 敏感词库维护（增/删/改/查）

我需要为我的运营同事，提供一个简单、易用的后台界面，来持续地维护我们平台的“**敏感词词库**”。这个词库，就是我们自动化审核的“规则库”。

这个后台界面，必须具备以下核心功能：

* **添加敏感词**：提供一个“+ 添加敏感词”的入口，让运营可以随时将新发现的违规词语录入系统。
* **查询与筛选**：当词库变得庞大时，必须提供按“**敏感词**”本身进行搜索，或按“**状态**”进行筛选的功能。
* **编辑与状态管理**：这是我设计中的一个要点。除了编辑和删除，我必须为每个敏感词，增加一个“**上线/下线**”的状态。只有处于“**上线**”状态的词，才会被系统用于前端的匹配过滤。这个设计，能让运营同事在不删除历史词语的情况下，灵活地启用或禁用某些词的过滤规则，特别是在应对一些突发的热点事件时，非常有用。

#### 2. 敏感词分类与处理规则

仅仅有一个词库列表，在我看来，还是一个非常初级的设计。一个专业的敏感词系统，必须具备更“聪明”的、差异化的处理能力。为了实现这一点，我会在我的设计中，再增加两个维度：**敏感词分类** 和 **处理规则**。

* **第一步：为敏感词分类**
    在“添加敏感词”时，我会要求运营同事，必须为这个词选择一个预设的“**分类**”。我会将词库至少分为以下几类：

  * `涉政类`
  * `暴恐类`
  * `色情类`
  * `广告营销类`
* `辱骂攻击类`
  
* **第二步：设定差异化的处理规则**
    完成了分类，我就可以为 **不同类别** 的敏感词，设定 **不同的自动化处理规则**。这才是这个系统智能化的体现。

    | **敏感词分类** | **示例** | **我设定的处理规则** | **对用户的反馈** |
    | :--- | :--- | :--- | :--- |
    | **辱骂攻击类** | “傻 X”、“垃圾”等 | **替换（Masking）** | 系统自动将“傻 X”替换为“**”，内容依然可以发布。 |
    | **广告营销类** | “加 V 信”、“www. ... .com” | **拦截（Block）** | 系统直接阻止该内容的发布，并向用户提示“内容包含违规广告信息，请修改”。 |
    | **涉政/暴恐等高危类**| （一些高度敏感的词语） | **转人工审核（Manual Review）** | 内容发布后，用户自己可见，但其他用户不可见，同时该内容自动进入我们后台的“待审核”列表，由人工进行最终判定。 |

通过这套“**分类+规则**”的组合设计，我们的敏感词管理系统，就从一个简单的“过滤器”，升级为了一个具备初步智能的、能分级处理风险的“**自动化审核引擎**”。



---

### 9.2.4 分类管理

![image-20250721133028297](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133028297.png)

一个很核心的问题是：当创作者发布内容时，他可以选择的那些“分类”，是从哪里来的呢？答案就是由我们平台的运营人员，在这个“**分类管理**”后台中，进行统一的创建和维护。

在我看来，分类管理是为我们的内容产品，搭建一个清晰、有序的“**图书馆目录**”。它将直接决定我们产品前台的 **频道划分** 和 **用户浏览结构**。

我通常将这个“目录”体系，分为两个层级：宏观的“**频道/分类**”和微观的“**标签**”。

#### 1. 频道/分类管理

![image-20250721133051445](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133051445.png)

“频道/分类”是内容最高层级的划分，它通常直接对应着我们 App 首页的 **导航栏**，比如“科技”、“财经”、“体育”、“娱乐”等。

这个后台管理界面，我的设计要点如下：

* **分类的增删改查**：这是最基础的功能。运营人员必须可以方便地 `新增分类`，并对已有的分类进行 `编辑` 和 `删除`。
  * **我的拓展思考**：在设计“删除”功能时，我必须考虑一个边界情况：如果某个分类下已经存在大量内容，那么直接删除这个分类，会导致这些内容成为“孤儿”。因此，一个严谨的删除逻辑应该是：**当分类下存在内容时，禁止删除，并提示运营人员先将该分类下的内容，迁移至其他分类**。
* **状态管理**：和我们之前设计的其他模块一样，我必须为每个分类，提供“**上线/下线**”状态。这能让运营同事从容地去筹备一个新频道，在内容和运营活动都准备好之后，再一键“上线”，呈现给所有用户。
* **排序功能**：我认为 **极其重要** 的一个功能。运营同事必须可以 **手动调整分类的显示顺序**。这个后台的排序，将直接决定前端 App 导航栏的频道顺序。我通常会设计一个支持“上移/下移”或“拖拽排序”的功能。

#### 2. 标签管理

![image-20250721133343391](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133343391.png)

如果说“分类”是图书馆的“区域”（如：A 区-文学），那么“**标签**”就是贴在每一本书上，更精细的“**关键词**”（如：科幻、刘慈欣、三体）。我们为内容打上精准的标签，核心目的就是为了 **喂给我们的算法推荐引擎**，让它能实现更精准的“人-内容”匹配。

这个后台的设计，除了基础的增删改查和状态管理外，我还会重点考虑以下两点：

* **标签分类与层级**：当标签数量达到成千上万时，一个扁平的列表是无法管理的。因此，我必须设计一个 **支持层级** 的标签体系。
  * 如图例所示，一个“`产品经理`”的标签，它的上级分类可能是“`互联网`”。这种树状的层级结构，有两大好处：
        1. **便于管理**：运营同事可以像操作文件夹一样，高效地管理和查找标签。
        2. **便于算法**：能让我们的推荐算法更“聪明”。算法会知道，喜欢“`产品经理`”标签内容的用户，可能也会对“`互联网`”这个更大范畴下的其他内容感兴趣，从而扩大推荐的相关性。


![image-20250721133429415](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133429415.png)

* **标签的创建与合并**：
  * **创建权限**：我需要做一个重要的产品决策：`新标签是由谁来创建的？` 是只能由运营在后台创建？还是允许创作者在发布内容时，自由地创建新标签？前者能保证标签库的规范和整洁，后者则能让标签库的内容更丰富、更接地气。在产品初期，我通常会采用“**运营后台创建为主，创作者可申请为辅**”的策略。
  * **合并功能**：当标签库由多人维护或允许用户创建时，不可避免地会出现语义相同但文字不同的标签（如：“产品经理”、“PM”、“产品策划”）。因此，我必须设计一个“**标签合并**”功能，让运营可以将这几个重复的标签，合并为一个标准标签，并自动更新所有关联了这些标签的内容。



-----

## 9.3 运营管理

![image-20250721134507525](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134507525.png)

我们已经设计了用户和内容的管理后台，但这还不够。一个好的产品，不能只依赖用户“**主动**”地回来使用。在如今这个信息爆炸、App 泛滥的时代，“酒香也怕巷子深”。

我们必须建立一套属于自己的“**广播系统**”，能够主动地、在合适的时机，去触达我们的用户，提醒他们、吸引他们回来。这就是 **运营管理** 模块的核心价值。

### 9.3.1 学习目标

在本节中，我的目标是带大家设计一个专业的运营管理后台。我们将重点学习 **消息推送**、**后台账号与权限** 和 **日志管理** 这三大系统的设计。

### 9.3.2 消息推送

消息推送（Push Notification）是我作为平台运营方，唯一能“**免费**”且“**主动**”触达已安装 App 但未打开用户的渠道。它是拉动用户活跃、进行活动通知、实现用户召回的最强武器。

#### 1\. 推送任务列表与数据统计

![image-20250721134611755](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134611755.png)

这是我运营团队的“**发射控制中心**”。它是一个记录了所有已发送和待发送推送任务的列表。除了展示 `标题`、`目标用户`、`推送时间` 等基础信息外，一个专业后台的核心，在于提供推送效果的数据统计。

| 数据指标 | 说明 |
| :--- | :--- |
| **发送数 (Sent)** | 本次任务，我们总共向多少个设备发送了通知。 |
| **到达数 (Delivered)**| 其中，成功送达到用户设备的数量。（有些可能因网络或卸载失败） |
| **点击数 (Clicks/Opens)**| 最终，有多少用户被我们的文案吸引，点击了这条通知。 |
| **点击率 (CTR)**| **最重要的评估指标（CTR = 点击数 / 到达数）**，直接反映了推送的质量。 |

#### 2\. 新建推送配置

![image-20250721134721627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134721627.png)

这是我为运营同事设计的“**炮弹编辑器**”。这个“新建推送”的表单，必须严谨、清晰，我通常会把它设计为“**推送四要素**”的配置。

| 配置要素 | 核心选项与说明 |
| :--- | :--- |
| **推送对象 (Who)** | **全部用户**：用于平台级的重大公告。<br>**用户分群**：按用户标签（如地域、兴趣）、活跃度等进行精准推送。<br>**指定用户**：通过上传用户 ID 列表，进行点对点推送。 |
| **推送时间 (When)**| **立即推送**：用于突发新闻、热点事件。<br>**定时推送**：用于已规划好的运营活动，可以提前设置。 |
| **推送内容 (What)**| **通知标题**：吸引用户眼球的第一句话，至关重要。<br>**通知内容**：对标题的补充说明，可支持插入用户昵称等个性化变量。 |
| **目标页配置 (Where To)**| **打开应用首页**<br>**打开应用内指定页面**：如某篇文章、某个活动页。<br>**打开一个 H5 链接**：跳转到外部网页。 |

**我的拓展设计（技术实现浅谈）**：
这个功能的技术实现，我们通常不会自己从零搭建，而是会依赖专业的 **第三方推送服务**。对于 iOS 端，我们后台需要接入苹果官方的 **APNs**；对于国内的 Android 端，我通常会选择集成一个像 **极光推送（JPush）或个推** 这样的第三方聚合服务商。我的 PRD 需要明确技术方案，因为不同服务商的能力会影响我的产品设计。

### 9.3.3 账号与权限管理

这个模块，管理的不是我们产品的“用户”，而是我们公司内部使用这个后台系统的“**员工**”。其设计的核心，是确保后台系统的 **安全性** 和 **规范性**。

#### 1\. 核心思想：RBAC 模型

我设计后台权限，普遍采用的是 **RBAC（Role-Based Access Control，基于角色的访问控制）** 模型。

它的逻辑很简单：我不直接给“某个人”分配权限，而是先创建不同的“**角色**”（如：内容审核员、高级运营），为这些“角色”分配权限，最后再把“某个人”归属到某个“角色”里去。

这样做的好处是，当公司人员变动时，我只需要调整这个人的角色，而不需要重新为他配置一遍复杂的权限，管理效率极高。

#### 2\. 角色管理

![image-20250721135012847](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135012847.png)

* **角色列表**：一个展示所有已创建角色的列表，如“超级管理员”、“内容审核”、“数据分析师”等。
* **新建/编辑角色**：提供创建新角色的功能。
* **角色权限配置**：这是核心。我会以“功能菜单树”的形式，列出后台的所有功能点，让管理员可以通过勾选的方式，为每个角色分配它能看到和操作的菜单权限。

#### 3\. 账号管理（后台用户）

这是具体管理员工账号的地方。

![image-20250721135036854](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135036854.png)

* **后台账号列表**：展示所有后台用户的列表。
* **新增/编辑账号**：当我需要为一位新同事开通后台权限时，我会在这里为他新建账号。

| 新增账号字段 | 填写说明 |
| :--- | :--- |
| **登录账号** | 用于后台登录的唯一 ID，建议使用员工的企业邮箱。 |
| **员工姓名** | 账号使用者的真实姓名，用于日志记录和责任追溯。 |
| **所属角色** | 从我们创建的“角色列表”中，为该账号选择一个角色，从而赋予他对应的权限。 |
| **账号状态** | 正常/冻结。当员工离职时，我可以将其账号冻结，而不是直接删除。 |

### 9.3.4 日志管理

日志管理是后台的“**黑匣子**”和“**监控录像**”。它记录了所有管理员在后台的一举一动，是进行安全审计和问题追溯的最后一道防线。

#### 1\. 操作日志的价值

它的价值在于 **安全** 和 **可追溯**。当后台出现误操作或恶意操作时，我可以通过日志，精准地定位到是“谁”，在“什么时间”，对“什么东西”，做了“什么操作”。

#### 2\. 操作日志设计要点

![image-20250721135143985](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135143985.png)

一个合格的操作日志系统，必须清晰地记录以下关键信息，并提供强大的查询功能。

| 记录字段 | 说明 | 示例 |
| :--- | :--- | :--- |
| **操作人** | 执行该操作的后台账号。 | `yunying_xiaowang` |
| **时间戳** | 操作发生的精确时间。 | `2025-07-21 14:42:00` |
| **IP 地址** | 操作人当时使用的 IP 地址。 | `************` |
| **操作行为**| 具体执行了什么动作。 | `内容管理 - 下架文章` |
| **操作对象**| 该动作作用于哪个具体目标。 | `文章ID: 88012` |
| **操作结果**| 本次操作是成功还是失败。 | `成功` |



---