@font-face {
    font-family: 'JetBrains Mono';
    src: local('JetBrains Mono'),
    url('./JetBrainsMono-Regular.woff2') format('woff2');
    font-display: swap;
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'JetBrains Mono';
    src: local('JetBrains Mono'),
    url('./JetBrainsMono-Italic.woff2') format('woff2');
    font-display: swap;
    font-weight: normal;
    font-style: italic;
}
@font-face {
    font-family: 'JetBrains Mono';
    src: local('JetBrains Mono'),
    url('./JetBrainsMono-Bold.woff2') format('woff2');
    font-display: swap;
    font-weight: bold;
    font-style: normal;
}
@font-face {
    font-family: 'JetBrains Mono';
    src: local('JetBrains Mono'),
    url('./JetBrainsMono-BoldItalic.woff2') format('woff2');
    font-display: swap;
    font-weight: bold;
    font-style: italic;
}

/*快速自定义配置*/
:root {
    --monospace: "Iosev<PERSON> Curly", "JetBrains Mono", "Fira Code", "Cascadia Code", Menlo, "Ubuntu Mono", Consolas, HYZhengYuan; /*代码字体*/
    --text-font: var(--monospace); /*正文字体*/
    --title-font: var(--monospace); /*标题字体*/
    --latex-font: var(--monospace); /*LaTeX字体(不含英语)*/
    --text-line-height: 1.6; /*正文行间距*/
    --code-line-height: 1.6; /*代码块行间距*/
    --p-spacing: 0.8rem; /*段间距*/
    --file-tree-text-size: 1.1rem; /*文件树大小*/
    --toc-text-size: 1rem; /*大纲大小*/
    --text-size: 14px; /*字体大小 推荐配置: 应用设置-外观-字体大小*/
}