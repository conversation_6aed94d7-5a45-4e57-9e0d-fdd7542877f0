- const {use} = theme.comment

each name in use
    case name
        when 'Twikoo'
            !=partial('includes/widgets/third-party/comments/twikoo', {}, {cache: true})
        when 'Waline'
            !=partial('includes/widgets/third-party/comments/waline', {}, {cache: true})
        when 'Valine'
            !=partial('includes/widgets/third-party/comments/valine', {}, {cache: true})
        when 'Artalk'
            !=partial('includes/widgets/third-party/comments/artalk', {}, {cache: true})
        when 'Giscus'
            !=partial('includes/widgets/third-party/comments/giscus', {}, {cache: true})