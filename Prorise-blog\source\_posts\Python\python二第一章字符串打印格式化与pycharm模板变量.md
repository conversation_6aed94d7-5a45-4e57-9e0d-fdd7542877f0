---
title: Python（二）：第一章：字符串打印格式化与PyCharm模板变量
categories:
  - 后端技术
  - Python
tags:
  - Python基础知识总汇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp'
comments: true
toc: true
ai: true
abbrlink: 2501
date: 2025-04-18 17:13:45
---

## 第一章：字符串打印格式化与PyCharm模板变量

本章将分为两个主要部分：首先介绍如何在 Python 控制台中使用 ANSI 转义序列来实现文本的彩色和格式化输出，并提供一个实用的封装示例；其次，我们将探讨如何利用 IDE（特别是 PyCharm，但概念也适用于其他IDE）中的 Live Template (实时模板/代码片段) 功能，通过预设的模板变量和缩写来大幅提升 Python 的编码效率。

### 字符串打印格式化 (ANSI 转义序列)

Python 允许在控制台中输出彩色文本和特殊格式，这在创建命令行界面 (CLI) 或需要突出显示特定输出时非常有用，可以显著增强用户体验和信息的可读性。这种效果通常是通过 **ANSI 转义序列 (ANSI escape sequences)** 来实现的。

#### 基本语法

ANSI 转义序列的基本格式如下：

```text
\033[参数m内容\033[0m
```

或者在 Python 字符串中，通常写作：

```python
'\033[<参数>m<你的文本内容>\033[0m'
```

其中：

  * `\033` (或 `\x1b`)：这是 ESC 字符的八进制（或十六进制）表示，标志着转义序列的开始。
  * `[`：控制序列引导符 (Control Sequence Introducer, CSI)。
  * `<参数>`：一个或多个用分号 `;` 分隔的数字。这些数字代码控制着文本的显示方式、前景色（文字颜色）和背景色。
  * `m`：表示设置图形再现参数 (Select Graphic Rendition, SGR) 的结束标志。
  * `<你的文本内容>`：你希望应用这些格式的实际文本。
  * `\033[0m`：这是一个特殊的重置序列，它会清除之前设置的所有格式属性，使后续的文本恢复到终端的默认显示状态。**每次使用完特殊格式后，都强烈建议使用此序列来重置，以避免格式污染后续的输出。**

#### ANSI 转义码表

下表列出了一些常用的 ANSI SGR 参数代码：

| 显示方式   | 代码 | 前景色   | 代码 | 背景色   | 代码 |
| :--------- | :--- | :------- | :--- | :------- | :--- |
| 默认       | 0    | 黑色     | 30   | 黑色     | 40   |
| 高亮/粗体  | 1    | 红色     | 31   | 红色     | 41   |
| (通常不使用) | 2    | 绿色     | 32   | 绿色     | 42   |
| 下划线     | 4    | 黄色     | 33   | 黄色     | 43   |
| 闪烁       | 5    | 蓝色     | 34   | 蓝色     | 44   |
| 反白       | 7    | 紫红色   | 35   | 紫红色   | 45   |
| 不可见     | 8    | 青蓝色   | 36   | 青蓝色   | 46   |
|            |      | 白色     | 37   | 白色     | 47   |

**注意**：

  * 除了上述标准颜色 (30-37, 40-47)，现代终端通常还支持高强度颜色 (例如，高亮红色使用 `\033[1;31m` 或者单独的亮色代码 `\033[91m`)、256色模式以及 RGB 真彩色模式 (例如 `\033[38;2;r;g;bm` 设置前景色为 RGB(r,g,b))。但这些高级模式的兼容性可能因终端模拟器而异。
  * “闪烁”(代码5) 和 “不可见”(代码8) 的支持程度也取决于终端。

#### 实用示例

##### 基本颜色设置

```python
# 红色文字
print('\033[31m这是红色文字\033[0m')

# 绿色文字
print('\033[32m这是绿色文字\033[0m')

# 黄色文字 (通常与高亮/粗体结合使用效果更明显)
print('\033[1;33m这是高亮黄色文字\033[0m') # 1表示高亮/粗体，33表示黄色

# 蓝色文字
print('\033[34m这是蓝色文字\033[0m')
```

##### 组合使用

可以同时设置显示方式、前景色和背景色，用分号分隔参数。

```python
# 红色文字 + 黄色背景
print('\033[31;43m红字黄底\033[0m')

# 高亮 + 绿色文字
print('\033[1;32m高亮绿色文字\033[0m')

# 下划线 + 蓝色文字
print('\033[4;34m带下划线的蓝色文字\033[0m')

# 高亮 + 紫红色文字 + 白色背景
print('\033[1;35;47m高亮紫红色文字白色背景\033[0m')
```

#### 实际应用场景 (封装为 `print_utils.py` 工具模块)

为了在项目中更方便、更一致地使用彩色打印，通常我们会将这些 ANSI 转义序列封装成常量或函数。

```python
"""
打印工具模块，提供彩色和结构化的打印函数。
"""

# ======== 彩色打印工具 ========
class Colors:
    """存储 ANSI 颜色和样式代码的常量。"""
    HEADER = '\033[95m'    # 亮紫色 (常用于标题)
    BLUE = '\033[94m'      # 亮蓝色
    CYAN = '\033[96m'      # 亮青色
    GREEN = '\033[92m'     # 亮绿色
    WARNING = '\033[93m'   # 亮黄色
    FAIL = '\033[91m'      # 亮红色
    BOLD = '\033[1m'       # 粗体/高亮
    UNDERLINE = '\033[4m'  # 下划线
    END = '\033[0m'        # 重置所有格式


def print_header(text: str) -> None:
    """打印带特殊格式的标题。"""
    print(f"\n{Colors.HEADER}{Colors.BOLD}--- {text} ---{Colors.END}")

def print_subheader(text: str) -> None:
    """打印带下划线的青色子标题。"""
    print(f"\n{Colors.CYAN}{Colors.UNDERLINE}  {text}{Colors.END}")

def print_info(text: str) -> None:
    """打印普通信息 (默认颜色)。"""
    print(f" INFO: {text}")

def print_success(text: str) -> None:
    """打印成功信息 (绿色)。"""
    print(f"{Colors.GREEN}  ✔ {text}{Colors.END}")

def print_warning(text: str) -> None:
    """打印警告信息 (黄色)。"""
    print(f"{Colors.WARNING}  ⚠️ [Warning] {text}{Colors.END}")

def print_error(text: str) -> None:
    """打印错误信息 (红色)。"""
    print(f"{Colors.FAIL}  ❌ [Error] {text}{Colors.END}")

def print_sql(sql: str) -> None:
    """打印SQL语句 (蓝色)。"""
    print(f"{Colors.BLUE}    SQL: {sql.strip()}{Colors.END}")

def print_result_item(item: any, indent: int = 4) -> None:
    """以结构化方式打印结果项，特别是字典。"""
    prefix = " " * indent
    if isinstance(item, dict):
        details = ", ".join([
            f"{Colors.BOLD}{key}{Colors.END}: {repr(value)}" for key, value in item.items()
        ])
        print(f"{prefix}Row({details})")
    else:
        print(f"{prefix}{repr(item)}")

# ======== END 彩色打印工具 ========
```

**如何使用这个 `print_utils` 模块：**

1.  将上述代码保存为 `print_utils.py` 文件。
2.  在您的其他 Python 脚本中，通过 `from print_utils import *` 或 `import print_utils` 来使用这些函数。

```python
# 示例：在另一个脚本中使用 print_utils.py
# from print_utils import print_header, print_success, print_error # 假设已导入

if __name__ == "__main__":
    # print_header("应用程序任务") # 调用封装好的函数
    # print_success("任务A已成功完成！")
    # print_error("任务B执行失败，错误代码：1024")
    pass # 仅为结构示例
```

### PyCharm Live Templates 提升编码效率

Live Templates（实时模板或代码片段）是现代集成开发环境 (IDE) 如 PyCharm、VS Code 等提供的一项核心功能。它允许开发者定义常用的代码结构，并通过输入一个简短的**缩写 (Abbreviation)** 后按下特定按键（通常是 `Tab`）来快速插入这些**代码块 (Template text)**。这些模板通常还支持**占位符变量**，如 `$VAR$` 或 `$CURSOR$`，在模板展开后，IDE 会引导用户快速填充这些变量或将光标定位到预设位置。

使用 Live Templates 可以：

  * **显著减少重复的样板代码输入**。
  * **提高编码速度和效率**。
  * **帮助保持代码风格和结构的一致性**。
  * **减少因手动输入而出错的可能性**。

我们需要根据如下步骤去键入模板

![image-20250518133509693](https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250518133509693.png)

#### 1\. 基本循环

##### a. `for...in` 循环 (遍历序列)

  * **模板用途**: 快速生成一个遍历可迭代对象的 `for` 循环。
  * **建议缩写**: `fori` (或您截图中的 `iter`)
  * **描述**: `for item in iterable:` 循环
  * **模板文本**:
    ```python
    for $ITEM$ in $ITERABLE$:
        $CURSOR$
    ```
  * **主要占位符说明**:
      * `$ITEM$`: 循环中每次迭代的元素变量名。
      * `$ITERABLE$`: 要遍历的序列或可迭代对象。
      * `$CURSOR$`: 模板展开后光标的初始位置。

##### b. `for...in enumerate` 循环 (带索引遍历)

  * **模板用途**: 快速生成一个同时遍历索引和元素的 `for` 循环。
  * **建议缩写**: `forenum` (或您截图中的 `itere`)
  * **描述**: `for index, item in enumerate(iterable):` 循环
  * **模板文本**:
    ```python
    for $INDEX$, $ITEM$ in enumerate($ITERABLE$):
        $CURSOR$
    ```
  * **主要占位符说明**:
      * `$INDEX$`: 循环中每次迭代的索引变量名。
      * `$ITEM$`: 循环中每次迭代的元素变量名。
      * `$ITERABLE$`: 要遍历的序列或可迭代对象。

##### c. `for...in range` 循环 (按次数)

  * **模板用途**: 快速生成一个按指定次数执行的 `for` 循环。
  * **建议缩写**: `forr`
  * **描述**: `for i in range(count):` 循环
  * **模板文本**:
    ```python
    for $VAR$ in range($COUNT$):
        $CURSOR$
    ```
  * **主要占位符说明**:
      * `$VAR$`: 循环计数变量名 (通常是 `i`)。
      * `$COUNT$`: 循环的次数。

#### 2\. 条件判断

##### a. `if` 语句

  * **模板用途**: 快速生成一个基本的 `if` 条件判断语句。
  * **建议缩写**: `ifc`
  * **描述**: `if condition:` 语句
  * **模板文本**:
    ```python
    if $CONDITION$:
        $CURSOR$
    ```
  * **主要占位符说明**:
      * `$CONDITION$`: `if` 语句的条件表达式。
      * `$CURSOR$`: 光标位于 `if` 代码块内部。

##### b. `if-else` 语句

  * **模板用途**: 快速生成 `if-else` 条件判断结构。
  * **建议缩写**: `ifel`
  * **描述**: `if condition: ... else: ...` 语句
  * **模板文本**:
    ```python
    if $CONDITION$:
        $ACTION_IF_TRUE$
    else:
        $ACTION_IF_FALSE$
    $CURSOR$
    ```
  * **主要占位符说明**: `$CONDITION$`, `$ACTION_IF_TRUE$`, `$ACTION_IF_FALSE$`, `$CURSOR$`。

##### c. `if-elif-else` 语句

  * **模板用途**: 快速生成 `if-elif-else` 多条件判断结构。
  * **建议缩写**: `ifelifel`
  * **描述**: `if cond1: ... elif cond2: ... else: ...` 语句
  * **模板文本**:
    ```python
    if $CONDITION1$:
        $ACTION1$
    elif $CONDITION2$:
        $ACTION2$
    else:
        $ACTION_ELSE$
    $CURSOR$
    ```
  * **主要占位符说明**: `$CONDITION1$`, `$ACTION1$`, `$CONDITION2$`, `$ACTION2$`, `$ACTION_ELSE$`, `$CURSOR$`。

#### 3\. 打印与日志

##### a. `print(f"...")` (f-string 打印)

  * **模板用途**: 快速生成一个使用 f-string 格式化的 `print` 语句。
  * **建议缩写**: `prf`
  * **描述**: `print(f"...")` 语句
  * **模板文本**:
    ```python
    print(f"$CURSOR$")
    ```
  * **主要占位符说明**: `$CURSOR$`: 光标直接定位在 f-string 的引号内。

##### b. `logger.info` (快速日志记录 - 假设 `logger` 对象已配置)

  * **模板用途**: 快速插入一条 `logger.info` 日志记录。
  * **建议缩写**: `logi`
  * **描述**: `logger.info(f"...")`
  * **模板文本**:
    ```python
    logger.info(f"$MESSAGE$")
    $CURSOR$
    ```
  * **主要占位符说明**: `$MESSAGE$`, `$CURSOR$`。 (类似地，可以为 `debug`, `warning`, `error`, `exception` 创建模板)

#### 4\. Python 结构与定义

  * **模板用途**: 快速生成一个带类型注解和文档字符串的函数定义。
  * **建议缩写**: `defn`
  * **描述**: 带类型注解和文档字符串的函数定义
  * **模板文本**:
    ```python
    def $FUNCTION_NAME$($PARAMS$) -> $RETURN_TYPE$:
        """$DOCSTRING$"""
        $CURSOR$
        return $RETURN_VALUE$
    ```
  * **主要占位符说明**: `$FUNCTION_NAME$`, `$PARAMS$`, `$RETURN_TYPE$` (可默认为 `None`), `$DOCSTRING$`, `$CURSOR$`, `$RETURN_VALUE$` (可默认为 `None`)。

##### c. 类定义 (基本结构)

  * **模板用途**: 快速生成一个带 `__init__` 方法的类定义。
  * **建议缩写**: `cls`
  * **描述**: 基本类定义 (含 `__init__`)
  * **模板文本**:
    ```python
    class $ClassName$:
        """$CLASS_DOCSTRING$"""

        def __init__(self, $ARGS$):
            """初始化 $ClassName$ 对象。
            
            Args:
                $ARGS_DOC$
            """
            $INIT_BODY$
            $CURSOR$
    ```
  * **主要占位符说明**: `$ClassName$`, `$CLASS_DOCSTRING$`, `$ARGS$`, `$ARGS_DOC$`, `$INIT_BODY$`, `$CURSOR$`.

##### d. `@dataclass` 类定义

  * **模板用途**: 快速生成一个使用 `dataclasses` 模块定义的类。
  * **建议缩写**: `dtcls`
  * **描述**: `@dataclass` 类定义
  * **模板文本**:
    ```python
    from dataclasses import dataclass

    @dataclass
    class $ClassName$:
        """表示 $ENTITY_DESCRIPTION$ 的数据类。"""
        $FIELD_NAME$: $FIELD_TYPE$
        $CURSOR$
    ```
  * **主要占位符说明**: `$ClassName$`, `$ENTITY_DESCRIPTION$`, `$FIELD_NAME$`, `$FIELD_TYPE$`, `$CURSOR$`.

#### 5\. 异常处理

##### a. `try-except` 基本块

  * **模板用途**: 快速生成一个基本的 `try-except` 异常处理块。
  * **建议缩写**: `tryex`
  * **描述**: `try...except Exception as e:` 块
  * **模板文本**:
    ```python
    try:
        $TRY_BODY$
    except $EXCEPTION_TYPE$ as e:
        # logger.exception(f"An error occurred: {e}") # 如果使用日志
        print(f"An error occurred ($EXCEPTION_TYPE$): {e}") # 简单打印
        $CURSOR$
    ```
  * **主要占位符说明**: `$TRY_BODY$`, `$EXCEPTION_TYPE$` (可默认为 `Exception`), `$CURSOR$`.

##### b. `try-except-else-finally` 完整块

  * **模板用途**: 快速生成包含 `else` 和 `finally` 子句的 `try-except` 块。
  * **建议缩写**: `tryexelfi`
  * **描述**: `try...except...else...finally:` 块
  * **模板文本**:
    ```python
    try:
        $TRY_BODY$
    except $EXCEPTION_TYPE$ as e:
        print(f"An error occurred ($EXCEPTION_TYPE$): {e}")
        $EXCEPT_BODY$
    else:
        print("Operation successful, no exceptions.")
        $ELSE_BODY$
    finally:
        print("Executing finally block.")
        $FINALLY_BODY$
    $CURSOR$
    ```
  * **主要占位符说明**: `$TRY_BODY$`, `$EXCEPTION_TYPE$`, `$EXCEPT_BODY$`, `$ELSE_BODY$`, `$FINALLY_BODY$`, `$CURSOR$`.

#### 6\. 文件操作

##### a. `with open(...)` 上下文管理器

  * **模板用途**: 快速生成使用 `with` 语句安全打开和操作文件的代码。
  * **建议缩写**: `fwith`
  * **描述**: `with open(...) as f:` (安全文件操作)
  * **模板文本**:
    ```python
    try:
        with open("$FILEPATH$", mode="$MODE$", encoding="utf-8") as $VARNAME$:
            $CURSOR$
            # content = $VARNAME$.read()
    except FileNotFoundError:
        print(f"Error: File '$FILEPATH$' not found.")
    except IOError as e:
        print(f"Error reading/writing file '$FILEPATH$': {e}")
    ```
  * **主要占位符说明**: `$FILEPATH$`, `$MODE$` (可默认为 `'r'`), `$VARNAME$` (可默认为 `f`), `$CURSOR$`.

#### 7\. 推导式

##### a. 列表推导式 (List Comprehension)

  * **模板用途**: 快速生成列表推导式。
  * **建议缩写**: `lc` (或您截图中的 `compl` / `compli` for with if)
  * **描述**: `[expr for item in iterable if condition]`
  * **模板文本 (带if)**:
    ```python
    [$EXPRESSION$ for $ITEM$ in $ITERABLE$ if $CONDITION$]
    $CURSOR$
    ```
  * **模板文本 (不带if)**:
    
    ```python
    [$EXPRESSION$ for $ITEM$ in $ITERABLE$]
    $CURSOR$
    ```
  * **主要占位符说明**: `$EXPRESSION$`, `$ITEM$`, `$ITERABLE$`, `$CONDITION$` (可选)。

##### b. 字典推导式 (Dictionary Comprehension)

  * **模板用途**: 快速生成字典推导式。
  * **建议缩写**: `dc` (或您截图中的 `compd` / `compdi` for with if)
  * **描述**: `{key_expr: val_expr for item in iterable if condition}`
  * **模板文本 (带if)**:
    ```python
    {$KEY_EXPRESSION$: $VALUE_EXPRESSION$ for $ITEM$ in $ITERABLE$ if $CONDITION$}
    $CURSOR$
    ```
  * **模板文本 (不带if)**:
    ```python
    {$KEY_EXPRESSION$: $VALUE_EXPRESSION$ for $ITEM$ in $ITERABLE$}
    $CURSOR$
    ```
  * **主要占位符说明**: `$KEY_EXPRESSION$`, `$VALUE_EXPRESSION$`, `$ITEM$`, `$ITERABLE$`, `$CONDITION$` (可选)。



#### 8. 其他

##### a. `lambda` 匿名函数

  * **模板用途**: 快速创建一个简单的 `lambda` 函数。
  * **建议缩写**: `lam`
  * **描述**: `lambda arguments: expression`
  * **模板文本**:
    ```python
    $LAMBDA_VAR$ = lambda $ARGUMENTS$: $EXPRESSION$
    $CURSOR$
    ```
  * **主要占位符说明**: `$LAMBDA_VAR$`, `$ARGUMENTS$`, `$EXPRESSION$`.

##### b. 注释标记

  * **模板用途**: 快速插入标准的 TODO, FIXME, NOTE 注释。
  * **建议缩写**: `todo` / `fixme` / `note`
  * **描述**: `# TODO: ...` / `# FIXME: ...` / `# NOTE: ...`
  * **模板文本 (以 `todo` 为例)**:
    ```python
    # TODO ($USER$ @ $DATE$): $MESSAGE$
    $CURSOR$
    ```
  * **主要占位符说明**: `$USER$` (IDE或可配置), `$DATE$` (IDE或可配置), `$MESSAGE$`.

-----