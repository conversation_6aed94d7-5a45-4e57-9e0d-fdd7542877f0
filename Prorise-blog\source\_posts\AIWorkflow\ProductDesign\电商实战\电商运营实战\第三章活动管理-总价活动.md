---
title: 第三章：活动管理-总价活动
categories: 产品经理
tags:
  - 产品经理教程
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp'
comments: true
toc: true
ai: true
abbrlink: 27803
date: 2025-07-26 11:13:45
---

# 第三章：活动管理-总价活动

欢迎来到第三章。在上一章，我们的所有活动都聚焦于“**单个商品**”的降价。但作为运营，我还有一个更重要的目标：**如何让用户一次买得更多？** 这就引出了我们本章的主题——**总价活动**。这类活动不再关注单个商品的价格，而是着眼于用户的“**购物车总价**”，通过设置一个“满X元”的门槛，来激励用户为了凑单而购买更多商品。

## 3.1 营销活动

我们将要学习的第一个，也是最经典的总价活动，就是“满减”。

### 3.1.1 总价活动-满减活动管理

![image-20250725201823505](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725201823505.png)

我为什么要设计“满减”功能？其核心的业务诉求，正如上图所述，是为了**提升客单价**。

在日常运营中，我会遇到很多类似的场景，比如：
* **满减**：订单金额满100元，减免20元。
* **满赠**：订单金额满200元，就赠送一个小礼物。
* **满折**：订单满3件，就享受8折优惠。

这些玩法的本质，都是在用户下单的最后一步“临门一脚”，通过一个有吸引力的优惠，引导他们“再多买一件”。本节，我们就来从0到1地设计出“满减”这个功能。

**1. 角色与流程**

满减活动的设计思路，依然遵循我们熟悉的框架：`角色` -> `流程` -> `功能` -> `字段`。

* **核心角色**：依然是**商家**（活动的创建者）和**用户**（活动的参与者）。
* **整体流程**：商家在后台创建满减活动，用户在前台浏览商品并参与活动。

![image-20250725202251529](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202251529.png)

![image-20250725202329925](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202329925.png)

对于用户来说，参与的体验必须是无缝且直观的。我的设计要点是：

* **在商品详情页**：就要清晰地展示出这个商品正在参与的满减活动，比如上图案例中的“满200减20”标签，这会成为用户把它加入购物车的重要动力，而这又分为`直接满减`与`阶梯满减`，对于阶梯满减来说，用户购买的越多优惠越多
* **在结算页**：当用户选购的商品满足了满减门槛时，系统需要自动地计算优惠，并清晰地展示出减免的金额，给用户带来“占到便宜”的满足感。

**2. 商家创建满减活动**

![image-20250725202431819](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202431819.png)

对于商家，我设计的创建流程依然是清晰的四步走：

1.  **选择活动类型**：商家首先在活动中心选择创建“满减活动”。
2.  **填写基本信息**：设置活动的名称、时间、面向人群等。
3.  **设置满减规则**：这是满减活动的核心，也是设计的重点。
4.  **选择活动商品**：圈定参与本次满减活动的商品范围。

![](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202812573.png)

现在，我们把上述流程，细化为具体的字段。

* **基本信息**：`活动名称`、`活动时间`、`参与人群`等，这些都是我们之前设计中可复用的标准组件。
* **活动规则**：这是设计的关键。与单品活动不同，这里的规则是作用于总价的。我通常会为商家提供两种优惠类型：
    * **直接满减**：最简单的模式，例如“满100元，减10元”。
    * **阶梯满减**：更灵活的模式，例如“满100减10，满200减30，满300减50”，通过多个档位，进一步刺激用户提高客单价。
* **活动商品**：商家可以选择`全部商品`都参与，也可以`指定部分商品`参与。

![image-20250725202902097](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202902097.png)

最终，这些字段会构成我们商家后台的创建页面。请特别注意“**优惠类型**”的设计，我通过单选框（Radio Button）让商家可以在“直接满减”和“阶梯满减”中切换。当选择“阶梯满减”时，我还提供了一个“**+**”号按钮，让商家可以动态地增加优惠层级。这种设计，兼顾了功能的强大性和操作的灵活性。

**3. 满减活动列表**

![image-20250725202925437](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202925437.png)

当商家创建完活动后，他需要一个统一的列表来管理。我设计的这张列表，除了包含活动名称、时间、状态等常规字段外，还增加了一个非常重要的字段——“**优惠信息**”。

在这个字段里，我会清晰地展示出这个活动的核心规则，如“满300元，减30元”。这能让商家在不点进详情的情况下，快速地浏览和识别每一个活动，极大地提升了管理效率。同时，列表页也提供了`查看`、`编辑`、`结束`等必要的操作入口，其背后的状态机逻辑与我们之前设计的完全一致。


---
### 3.1.2 总价活动-满赠活动管理

在开始设计前，我们先来看一个我经常遇到的真实业务场景：**某个商品库存积压严重，直接打折降价怕影响品牌形象，怎么办？**

一个非常有效的策略，就是把它作为“**赠品**”，去搭配那些热销的商品。这就是“满赠”活动的核心价值之一：它不仅能像“满减”一样提升客单价，还能帮助我们优化库存结构，并给用户带来一种“意外之喜”的超值感。

![image-20250725203555950](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203555950.png)

我给**满赠**的定义是：**在活动时间段内，用户购买主商品，当订单金额或商品件数满足预设的门槛时，即可免费获得指定赠品的营销活动。**

和满减一样，我也把它分为两种常见形式：
1.  **直接满赠**：规则简单直接，例如“满100元赠USB风扇”。
2.  **阶梯满赠**：设置多个优惠档位，例如“满100元赠USB风扇，满200元赠小熊电饭煲”，以此激励用户冲击更高的消费档次。

**1. 商家创建满赠活动**

满赠活动的B端创建流程，与满减活动的主干完全一致，我依然采用了我们熟悉的“**创建四部曲**”。

`选择活动类型 -> 活动基本信息 -> 满赠规则设置 -> 活动商品选择`

这体现了我在设计后台系统时的一个重要原则：**保持操作逻辑的一致性，降低用户的学习成本**。

![image-20250725203652822](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203652822.png)

我们把创建流程拆解为具体的字段。这个功能的关键，在于“**满赠规则设置**”和“**活动商品选择**”这两步，有了全新的内涵。

![image-20250725203726873](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203726873.png)

现在，我们来看这张“创建满赠活动”的页面原型，这是本节功能设计的核心。

它的上半部分（活动信息、优惠类型）与“满减”的设计几乎可以完全复用。真正的区别在下半部分：

* **选择商品（主商品）**：这里的商品列表，指的是用户**必须购买**才能享受优惠的“**主商品**”。商家可以选择全店商品参与，也可以指定部分热销商品参与。
* **选择赠品**：这部分是“满赠”功能设计的灵魂。我需要在这里，为商家提供另一个商品选择器，让他可以从自己的商品库中，选择一个或多个商品，作为本次活动的“**赠品**”。

我的设计要点是，赠品的选择必须灵活。商家不仅可以指定“赠品A”，还可以设置“满200元，可在赠品B或赠品C中任选其一”，把选择权交给用户，从而提升活动的吸引力。

**2. 用户端体验**

![image-20250725203831933](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203831933.png)

一个后台功能设计得再好，如果用户在前端无感知，那也是失败的。所以，我必须在C端（用户端）清晰地把优惠信息传达出去。

* **商品详情页**：当一个商品参与了满赠活动，我会在其价格旁边，增加一个醒目的“**赠**”字标签。用户点击后，可以看到详细的活动规则，例如“满100元即可获赠XX商品一件”。
* **结算页**：当用户的订单满足满赠条件时，在结算页的商品清单中，我会**把赠品作为一个单独的行给展示出来，价格标注为0元**。这能给用户带来实实在在的“获得感”，让他清晰地感知到自己享受到的优惠。

**3. 满赠活动管理**

![image-20250725203937920](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203937920.png)

最后，商家创建的所有满赠活动，也会进入到我们统一的“**活动管理**”列表中。

这张列表的整体框架是完全复用的。我需要做的，仅仅是确保“**优惠信息**”这一列，能够准确地显示出满赠活动的核心规则，例如“满100元赠A商品”。通过这种方式，商家可以高效地对进行中或未开始的活动，进行统一的管理和后续操作


---
### 3.1.3 总价活动-满折活动管理

我们已经有了“满减”（针对订单金额的优惠）和“满赠”（针对订单价值的提升），现在，我需要一个能直接激励用户购买“**更多件数**”的工具。尤其是在服装、图书、日用品这类客单价不一定高，但用户常常会一次性购买多件的品类中，这个工具就显得尤为重要。于是，“**满折**”活动就应运而生了。

满折，即**当用户购买指定商品的件数，达到预设门槛时，即可享受整单相应折扣的优惠**。例如，“指定商品任选3件，即可享受8折优惠”。

**1. 商家创建满折活动**

在B端设计上，我依然沿用了标准化的“**创建四部曲**”流程，这能确保商家在使用我们后台时，有一种统一、连贯的操作体验，而不需要为每一种新活动都重新学习一遍。

![image-20250726092532568](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092532568.png)

我们把目光聚焦到这个功能的核心——“**填写活动规则**”。

![image-20250726092602653](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092602653.png)

我们直接来看“创建满折活动”的页面原型。它的核心设计，全部体现在“**活动规则**”这个模块里。

* **规则设置**：这里的规则不再是“满X元”，而是“**满X件**”，优惠方式也从“减Y元”变成了“**打Z折**”。
* **阶梯折扣**：和满减、满赠一样，我也为“满折”设计了阶梯模式。商家可以设置“满2件打9折，满3件打8折”，通过一个更优惠的折扣力度，来强力吸引用户“再多拿一件”，从而有效提升订单的商品件数（即购物深度）和销售总额。

**2. 用户端体验**

![image-20250726092913868](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092913868.png)

在C端，为了让用户能清晰地感知到“满折”优惠，我的设计思路和“满减”是完全一致的。

* **活动感知**：我会在参与活动的商品详情页上，用一个醒目的“**满折**”标签来吸引用户的注意力。
* **优惠计算**：在结算页，当用户购物车中参与活动的商品件数满足了规则后，系统会自动计算出折扣金额，并在“优惠金额”处明确展示，让用户直观地看到自己省了多少钱。

**3. 满折活动管理**

![image-20250726093347431](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726093347431.png)

在“**活动管理**”后台，商家也能清晰地看到所有已创建的满折活动。

在列表的“**优惠类型**”这一列，我会直观地显示出“满3件，8折”这样的核心规则，便于运营人员进行后续的查看、编辑或结束等管理操作。通过对列表、创建页等核心组件的复用，我能用最低的成本，最高效地扩展出新的营销玩法。

到此为止，我们已经系统性地掌握了总价活动中的“三剑客”：**满减、满赠、满折**。它们在B端的设计上有很多共通之处，但在C端的体感和核心运营策略上又各有侧重。

最后，我给你留一个思考题：如果我们要做一个更复杂的“**满返**”（比如，订单满100元，返还20元优惠券或2000积分）活动，它的B端和C端产品设计，与我们已经学过的这三种相比，又会有哪些共同点和差异点呢？带着这个问题，我们将在后续的课程中继续探索。

---
### 3.1.4 总价活动-套装活动

在之前的几种总价活动中，我们都是设定一个“规则门槛”，让用户**自由地选择商品**去凑单。但很多时候，我作为商家，希望能更“主动”地为用户规划好一组合集，并给出一个打包优惠价来提升整体销量。

比如，快餐店里“汉堡+薯条+可乐”的套餐，美妆领域“水、乳、精华”的护肤品套装，或者服饰店里“上衣+裤子”的搭配组合。这些，就是我们这节要设计的——**套装活动**。

**1. 套装活动设计思路**

套装活动的核心，是**将多个独立的商品，打包成一个新的销售单元进行促销**。它的设计思路依然遵循我们的标准框架，关键在于对“套装规则”的定义。

在B端设计上，我将“套装规则”进一步细分为了两种核心类型，以满足商家多样化的营销需求：
1.  **固定套装**：一个打包好的组合，用户必须完整购买，不可更改。
2.  **搭配套装**：提供一定的选择空间，例如“主商品A + 搭配商品B/C/D任选其一”。

**2. 设计详解：固定套装**

![image-20250726094401049](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094401049.png)

“固定套装”是最简单、最常见的模式。它的逻辑就是“**商品A + 商品B + 商品C = 一个固定的打包价**”。

我们来看它的创建页面：
* **套装类型**：商家首先选择“固定套装”。
* **套装价格**：商家需要为这个打包好的组合，设置一个全新的、有吸引力的“套装价”。
* **选择商品**：商家在下方的商品选择器中，勾选出所有要包含在这个固定套餐里的商品。

这种模式的优点是规则清晰，用户决策成本低。缺点是灵活性较差。

**3. 设计详解：搭配套装**

“搭配套装”则为商家和用户提供了更高的灵活性。它的逻辑更像是“**主商品区任选一件 + 搭配商品区任选一件 = 优惠组合价**”。

在创建页面上，它的设计也更为复杂：
* **套装类型**：商家需要选择“搭配套装”。
* **选择主商品**：商家首先要指定一批“主商品”。
* **选择搭配商品**：然后，再指定一批可供搭配的“副商品”。

![image-20250726094747956](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094747956.png)

这种模式非常适用于服饰、3C配件等品类。例如，我可以设置一个“买任意一款手机（主商品），即可半价换购任意一款手机壳（搭配商品）”的活动。这给了用户一定的自主选择权，体验更好，也更容易促成关联销售。



**4. 用户端体验**

![image-20250726094729297](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094729297.png)

当一个商品参与了套装活动时，我该如何在C端把它呈现给用户呢？

我的方案是，在该商品的详情页下方，专门开辟一个“**优惠套餐**”的区域。

如上图所示，这个区域会清晰地展示出套餐内的所有商品图片、名称，以及最具吸引力的“**套餐价**”，并提供一个“立即购买套餐”的按钮。通过这种直观的对比，用户能立刻感知到购买套餐的超值之处，从而被引导完成购买。

**5. 套装活动管理**

![image-20250726095246261](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095246261.png)

最后，在B端的活动管理后台，商家可以统一管理所有已创建的套装活动。

为了便于商家区分，我特意在活动列表中增加了一列“**套装类型**”。通过这一列，商家可以一目了然地分清，哪些是“固定套装”，哪些是“搭配套装”，从而进行更有针对性的管理和数据分析。


---
## 3.2 营销规则

我们已经为商家设计了品类丰富的单品活动和总价活动。但当这些“武器”可以被同时使用时，一个新的、也是更复杂的问题就摆在了我的面前。

![image-20250726095759645](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095759645.png)

为了解决这个“优惠爆炸”的问题，防止出现混乱和亏损，我作为产品经理，必须设计一套清晰、严谨的“**营销规则**”。它就像我们营销系统的“基本法”，规定了所有活动之间应该如何协同工作。

我将这套复杂的规则，拆解为四大核心模块。接下来，我们将逐一攻克。

### 3.2.1 规则一：叠加与互斥

我们先来看一个真实的运营场景：新款iPhone上市，运营同学恨不得把秒杀、拼团、满减、满赠所有优惠都给它加上，让它看起来“优惠到极致”。但，这样真的可以吗？

答案是否定的。如果没有任何限制，多个大力度的单品活动叠加，商品价格可能会变成负数。因此，我必须定义清楚

哪些活动之间是“**互斥**”的（不能同时享受），哪些又是可以“**叠加**”的（可以同时享受）。

![image-20250726095825205](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095825205.png)

要定义规则，我首先需要把平台内所有的“优惠”形式，按照性质进行归类。我将它们划分为四大类：
1.  **单品活动**：直接作用于**商品本身**的优惠，如秒杀、直降、拼团。
2.  **总价活动**：作用于**订单总价**的优惠，如满减、满赠、满折。
3.  **抵扣活动**：用户使用**虚拟资产**进行抵扣的活动，如优惠券、积分、礼品卡。
4.  **支付活动**：与**支付渠道**绑定的优惠，如信用卡支付立减。

![image-20250726104251355](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104251355.png)

理清了分类，我就可以制定出上面这张“**优惠叠加互斥规则表**”。这是我们整个营销系统的“交通法规”。

它的核心逻辑可以总结为：
* **同类互斥**：一个商品不能同时参与两个“单品活动”（例如，你不能让一个商品既是秒杀价，又是拼团价）。同理，一个订单也不能同时满足两个“总价活动”。
* **异类叠加**：不同类型的活动，原则上是可以叠加享受的。例如，一个商品可以先享受“秒杀”价（单品活动），达到门槛后可以再享受“满减”（总价活动），结算时还可以用“优惠券”（抵扣活动），最后用“信用卡支付”（支付活动）再减一点钱。

### 3.2.2 规则二：活动顺序

我们已经知道哪些活动可以一起用了。但新的问题又来了：**先算哪个，后算哪个？** 顺序不同，结果可能天差地别。

![image-20250726103851360](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726103851360.png)

我的设计原则是，**模拟用户真实的交易环节，定义一条雷打不动的计算链路**。

1.  **第一步：计算单品活动**。先算出商品经过秒杀、直降等活动后的价格。
2.  **第二步：计算总价活动**。用第一步得出的价格总和，去判断是否满足满减、满折的门槛。
3.  **第三步：计算抵扣活动**。用第二步得出的价格，去使用优惠券、积分等进行抵扣。
4.  **第四步：计算支付活动**。用第三步得出的最终应付金额，去享受支付渠道的优惠。

![image-20250726103917986](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726103917986.png)

但在这个大原则下，还有一个更细致的问题：当同一种类型的活动有多个时，又该怎么算？比如，一个订单同时满足“满200减20”和“满300减40”。

这里，我设计了两种模式供运营人员选择：
* **递进式**：先计算第一个门槛的优惠，用优惠后的金额，再去判断是否满足下一个门槛。这种模式对平台最有利，能严格控制成本，但计算逻辑复杂。
* **平行式**：所有满足门槛的优惠，都基于原始金额进行计算，然后全部生效。这种模式对用户最友好，计算速度快，但商家有亏损的风险（例如，用户买300元商品，同时享受了“满200减20”和“满300减40”，平行计算下总共优惠了60元）。

![image-20250726104208247](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104208247.png)

通过上面这个案例，你可以清晰地看到，一个999元的鼠标，在“递进式”和“平行式”两种不同规则下，最终的成交价是不同的。在后台为运营设计这个功能时，我必须把这两种模式的选择权交给他们，并讲清楚其中的利弊。


### 3.2.3 规则三：优惠分摊规则（深度解析）

我们必须认识到，这个规则的存在，是为了解决一个核心的财务问题：**当一笔享受了总价优惠的订单发生部分退款时，如何确保退款金额的计算是公平且准确的，以防止平台或商家产生亏损。**

#### 1. 基础场景：单一总价优惠的分摊

![image-20250726104708001](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104708001.png)

我们从一个最基础，也最常见的场景开始。如上图所示，一个60元的订单，通过“满60减20”的活动，用户实际支付了40元。现在，用户需要退掉其中10元的A商品。我们应该退给他10元吗？

绝对不行。如果退10元，就意味着用户用30元买到了价值50元的B和C商品，享受了“满50减20”的优惠，这与我们“满60减20”的活动规则相悖，平台或商家平白无故地亏损了。

要解决这个问题，就必须引入我们分摊规则的“**第一性原理**”：**任何一笔作用于订单整体的优惠，都必须按比例分摊到订单内的每一个商品上。**

我制定的核心分摊公式如下：
`商品优惠金额 = 总优惠金额 × (商品金额 / 参与活动商品的价格总和)`

现在，我们用这个公式来精确计算A商品的退款金额：
1.  **计算A商品分摊到的优惠金额**：
    `A商品优惠金额 = 20元 × (10元 / 60元) = 3.33元`
2.  **计算A商品应退款金额**：
    `A商品应退款 = A商品原价 - A商品分摊到的优惠金额 = 10元 - 3.33元 = 6.67元`

只有这样，我才能确保退款后，剩余的B、C两件商品，其合计支付金额（40-6.67=33.33元）与它们应该享受的优惠是匹配的。

#### 2. 进阶问题：计算精度与尾差处理

![image-20250726110938190](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110938190.png)

应用这个公式，我们可以继续计算出B和C的应退款金额。但是，在真实的计算机系统中，除法运算常常会导致无限循环小数（例如 `10/60 = 0.1666...`），这会带来精度问题。如果A、B、C的优惠金额分别是3.33, 6.67, 10.00，三者相加可能等于20.00，也可能等于19.99或20.01。这个微小的误差，在海量订单下，会累积成巨大的财务漏洞。

![image-20250726111003639](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111003639.png)

为了确保万无一失，我设计了一条“**尾差处理规则**”：**最后一个商品的优惠金额 = 总优惠金额 - 之前所有商品已分摊的优惠金额之和**。

![image-20250726111530614](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111530614.png)

同时，为了让计算过程更稳定，我还会制定一条工程上的最佳实践：
![image-20250726111541687](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111541687.png)
**按商品金额从小到大进行计算**，然后将所有的计算尾差，都归结到最后一个（即金额最大）的商品上。这能保证，无论如何计算，**一个订单内所有商品分摊的优惠总和，绝对等于这笔订单享受的优惠总额**，一分不多，一分不少。

#### 3. 终极挑战：多商品、多活动、多层级优惠的混合分摊

现在，我们来挑战一个最复杂的场景，它融合了我们前面学到的所有规则。

![image-20250726111624491](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111624491.png)

这个场景的复杂性在于，优惠不再是单一的“满减”，而是包含了**单品活动、总价活动、抵扣活动**的多层级优惠。

要解决这个问题，我必须严格遵循我们在上一节定义的“**活动顺序**”。

![image-20250726111749384](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111749384.png)

我们必须再次重申这条计算的生命线：**单品活动 > 总价活动 > 抵扣活动**。优惠的计算和分摊，必须严格按照这个优先级，层层递进。

![image-20250726111732314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111732314.png)

现在，我们对这个终极案例，进行庖丁解牛式的拆解：

* **第一步：计算单品活动。**
    * A商品参加“直降1000元”，其优惠后的价格变为 `3000 - 1000 = 2000` 元。
    * B商品不参与单品活动，价格仍为 `100` 元。
    * **此时，用于下一步计算的订单价格基础是 A=2000元, B=100元。**

* **第二步：计算总价活动。**
    * B商品参加“满100-50”的满减活动，其价格变为 `100 - 50 = 50` 元。
    * A商品不参与总价活动，价格仍为 `2000` 元。
    * **此时，用于下一步计算的订单价格基础是 A=2000元, B=50元。**

* **第三步：分摊抵扣活动（优惠券）。**
    * 现在，我们需要将这张1500元的优惠券，分摊到A和B两个商品上。
    * **用于分摊的商品价格总和为**：`2000元（A的折后价） + 50元（B的折后价） = 2050元`。
    * **B商品应分摊的优惠券金额** = `1500元 × (50元 / 2050元) ≈ 36.59元`。
    * **A商品应分摊的优惠券金额** = `1500元 - 36.59元 = 1463.41元` （应用尾差处理规则）。

* **第四步：得出结论。**
    * A商品总共优惠了：`1000元（直降） + 1463.41元（优惠券） = 2463.41元`。
    * B商品总共优惠了：`50元（满减） + 36.59元（优惠券） = 86.59元`。

通过以上严谨的、层层递进的规则设计，我才能确保，无论运营人员配置出多么复杂的优惠组合，我的系统都能准确、公平、安全地计算出最终价格和退款金额，守住平台和商家资金安全的生命线。这，就是“分摊规则”设计的严肃性和重要性所在。


### 3.2.4 规则四：风险与防范

![image-20250726110124008](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110124008.png)

作为产品经理，我不仅要设计功能，更要保护平台和商家的利益，防止他们因为误操作而造成亏损。

![image-20250726110131853](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110131853.png)

为此，我设计了一套“**风险防范组合拳**”：

1.  **低价预警**：当系统检测到商家设置的优惠力度过大，可能导致亏损时（例如，折后价低于成本价），自动弹出醒目的预警提示，让商家进行二次确认。
2.  **活动审核**：对于一些重要的、或者新手商家创建的活动，我可以设计一个“审核”流程。活动创建后不会立刻生效，而是进入“待审核”状态，需要由运营主管或平台管理员审核通过后，才能正式上线。
3.  **安全策略**：为了防止专业的“羊毛党”通过技术手段刷单，我还需要设计一些基础的“反作弊”策略，例如限制同一个IP地址、同一个设备、同一个收货地址的参与次数等。

![image-20250726110202924](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110202924.png)

最后我们总结一下，以上就是我设计的营销规则体系。它就像一张无形的、精密的大网，确保了我们整个营销活动系统，能够在复杂多变的场景下，依然能够公平、稳定、安全地运行。



## 3.3 营销工具
在第三章的后续部分，我们将进入一个更有趣、更具互动性的领域——**营销工具**。它不再是简单的让利，而是通过游戏化的玩法，来提升用户的参与度和粘性，实现“品效合一”的营销目标。


### 3.3.1 抽奖工具

抽奖，是一种低成本、高回报的互动营销玩法。它通过设置有吸引力的奖品，来驱动用户完成我们期望的特定行为，如每日访问、分享拉新等。

#### 1. 抽奖工具的需求分析

在我动手设计具体的产品功能前，我必须首先回归原点，搞清楚我们“为什么”要做这个功能。

![image-20250726112559235](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112559235.png)

我之所以要在系统中，增加一个看似和“卖货”没有直接关系的“抽奖”功能，其核心驱动力，来自于商家提升用户活跃度与忠诚度的真实诉求。

通过上图的需求背景，我提炼出抽奖工具需要满足的两大核心业务目标：
1.  **提升老用户粘性**：通过每日免费抽奖等形式，为老用户提供一个持续访问我们App或店铺的理由，提升DAU（日活跃用户）。
2.  **促进新用户增长**：将“分享”与“增加抽奖次数”进行绑定，激励老用户主动去进行社交分享，从而为店铺带来低成本的新流量。

![image-20250726112647407](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112647407.png)

明确了业务目标后，我就需要从最宏观的视角，来构思这个工具的完整生态。我首先要定义其中的“**核心角色**”和“**整体流程**”。

* **核心角色**：抽奖工具的生态中，主要有三方参与者：
    * **平台方**：我作为平台的产品经理，负责设计和提供稳定、通用的抽奖工具。
    * **商家**：是抽奖活动的发起者和成本承担者，他们使用我提供的工具，来配置活动规则和奖品。
    * **用户**：是抽奖活动的最终参与者。
* **整体流程**：整个业务的生命周期，是一个清晰的闭环。如上图所示，`平台提供工具` -> `商家配置活动` -> `用户参与抽奖` -> `商家发放奖品` -> `用户查看奖品`。我的产品设计，必须确保这个链条上的每一个环节都顺畅无误。

在宏观流程中，“**用户参与抽奖活动**”是整个玩法能否成功的关键。那么，用户的体验旅程应该是怎样的呢？

![image-20250726112736166](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112736166.png)

为了让用户体验顺畅、且富有激励性，我为C端用户设计了上面这条完整的“**参与流程**”闭环。

我们来一步步拆解这个流程：
1.  **触发抽奖**：用户进入活动页面，点击“立即抽奖”按钮。
2.  **前置判断**：系统首先判断用户“是否还有抽奖次数”。
3.  **次数用完**：如果次数已用完，系统会弹出提示，并引导用户去“**通过分享获得更多抽奖次数**”。这正是我们实现拉新裂变的关键设计。
4.  **执行抽奖**：如果次数未用完，系统则根据后台配置的算法，来判断本次抽奖“是否中奖”。
5.  **结果反馈**：
    * 如果**中奖**，则弹出“恭喜中奖”的强提示，并引导用户去“查看奖品信息”。
    * 如果**未中奖**，则弹出“谢谢参与”的安慰性提示。

![image-20250726124606407](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124606407.png)

通过对业务目标、宏观流程、用户旅程的完整分析，我就为接下来进行具体的“产品设计”，打下了坚实的基础。

---

#### 2. 抽奖工具的产品设计

根据我们之前的分析，抽奖工具的设计，必须同时兼顾**商家端（B端）**的易用性和灵活性，以及**用户端（C端）**的趣味性和流畅体验。我将为你分别进行拆解。

##### **一、 商家端（B端）产品设计**

我们首先来看商家后台的设计。我需要为商家提供一个足够强大，但操作又不能过于复杂的活动创建流程。

![image-20250726124513436](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124513436.png)

我设计的商家创建流程，依然遵循我们熟悉的“四部曲”，确保了后台操作的一致性。接下来，我们详细看一下每一步的具体设计。

**1. 设置基本信息**

![image-20250726124625992](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124625992.png)

这是活动创建的第一步，商家需要在这里设置活动的“身份信息”，包括`活动名称`、`活动时间`、`活动平台`（是在App内还是H5页面生效）以及`活动说明`（即活动规则的文字描述）。

**2. 填写抽奖规则**

![image-20250726124651499](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124651499.png)

这是抽奖功能设计的灵魂，它决定了整个活动的核心玩法。

![image-20250726124725381](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124725381.png)

在设计这部分功能时，我主要思考并解决了上面这两个核心问题。

* **抽奖类型**：我为商家提供了两种模式，来回答第一个问题。
    * `即时抽奖`：用户抽完立刻知道结果。这是最常见的模式，能提供即时反馈和刺激。
    * `非即时抽奖`：用户参与后，需要等待统一的开奖时间（例如，每周五开奖）。这种模式适用于需要营造悬念和持续关注度的活动。
* **抽奖条件**：我允许商家设置参与门槛，例如用户必须`使用XX积分`、达到`XX会员等级`，或者`完成订单后`才能获得抽奖资格。
* **参与次数**：商家可以灵活控制用户参与的频率，是`每人每天可抽N次`，还是在整个活动周期内`每人一共可抽N次`。
* **分享设置**：这是实现裂变增长的关键。我需要让商家可以配置“**用户分享活动后，可以额外增加N次抽奖机会**”的规则。
* **提示文案**：为了让体验更友好，我允许商家自定义各种场景下的提示文案，如`中奖提示`、`未中奖提示`、`活动未开始提示`等。

**3. 选择抽奖奖品**

![image-20250726124857807](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124857807.png)

在这一步，商家需要设置本次活动的“奖池”。为了回答“如何控制奖品数量”这个问题，我要求商家在设置每一个奖品时，都必须明确两项核心信息：**`奖品数量`**和**`中奖概率`**。系统会根据这两项配置，通过抽奖算法来精确控制奖品的发放。

为了丰富奖品的类型，我设计的奖池支持多种奖品形态：

* **实物商品**

    ![image-20250726125038417](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125038417.png)

* **优惠券**
    ![image-20250726125104812](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125104812.png)
    
* **积分**
    ![image-20250726125113561](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125113561.png)



![image-20250726125326357](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125326357.png)

![image-20250726125341599](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125341599.png)

通过这种模块化的设计，商家就可以非常灵活地配置出具有吸引力的奖品组合。

##### **二、 用户端（C端）产品设计**

当商家在后台配置好活动后，C端用户看到和体验到的，必须是一个有趣、流畅的界面。

**1. 抽奖主页面**

![image-20250726125205586](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125205586.png)

![image-20250726125128157](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125128157.png)

我采用了最经典的“**九宫格**”抽奖样式。这个页面的核心元素包括：
* **抽奖区域**：九个格子中，分布着不同的奖品和“谢谢参与”的选项。
* **抽奖按钮**：用户点击“立即抽奖”，转盘开始转动。当用户次数用尽，按钮会变为“明天再来”或“分享获取次数”等不可用状态。
* **中奖名单**：页面下方会实时滚动最新的中奖信息，营造一种热闹、很多人中奖的氛围，来激励其他用户参与。

**2. 抽奖结果反馈**

对于“即时抽奖”来说，及时的结果反馈至关重要。
* **中奖**：立刻弹出强提示的“恭喜中奖”弹窗，告知用户获得了什么奖品。
* **未中奖**：弹出安慰性的“祝您下次中奖”弹窗，并引导用户“下次再来”。

**3. 我的奖品列表**


所有用户中奖的记录，都会沉淀在“**我的中奖记录**”这个页面。用户可以在这里，清晰地看到自己获得的所有奖品，以及每一个奖品的当前状态，是“**待兑换**”还是“**已兑换**”，方便进行后续的核销与使用。

---
### 3.3.2 优惠券工具

如果说“抽奖”是提升趣味性和互动性的利器，那么“**优惠券**”则是我工具箱中，用途最广泛、玩法最灵活、最能实现精细化运营的“万能钥匙”。它几乎可以和任何营销场景进行组合，是我们刺激用户行为、提升转化和复购的核心手段。

![image-20250726130639644](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130639644.png)

#### 1.优惠券工具的需求分析

![image-20250726130159412](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130159412.png)

我为什么要设计一套独立的、复杂的优惠券系统？我们来看一个商家最常见的困惑：**用户在一次大促中尽兴消费后，就“消失”了，如何能有效地把他们“拉回来”，产生第二次、第三次消费呢？**

“满减”、“满折”这类活动，解决的是“当下”的转化问题。而优惠券，则是我用来连接“**当下**”与“**未来**”的桥梁。我将优惠券的核心业务价值，总结为两点：

1.  **提升复购率**：通过在用户完成交易后、或在日常的互动中，向其发放一张带有有效期的优惠券，我就为他创造了一个“必须在未来某个时间点回来消费”的强烈理由。
2.  **精准控制成本**：与全场打折不同，优惠券可以“**指哪打哪**”。我可以控制它的发放数量、发放人群、使用门槛和适用商品，从而将营销预算，精准地花在最有价值的用户和商品上。

**1. 优惠券的构成要素**

在设计功能前，我首先要像解剖麻雀一样，拆解“优惠券”这个事物的核心构成要素。

一张小小的优惠券，看似简单，实则包含了丰富的信息和规则。我作为产品经理，在设计时必须考虑到以下所有要素：

| **要素分类** | **核心字段** | **我的解读** |
| :--- | :--- | :--- |
| **券面价值** | `面值` / `折扣` | 这是优惠券最核心的价值。例如，10元代金券，或8折折扣券。 |
| **使用门槛** | `使用条件` | 用户需要满足什么条件才能使用这张券。例如，“满100元可用”。无门槛券则没有此项。|
| **适用范围** | `使用范围` / `使用平台` | 这张券可以用在哪些地方。是“全场通用”，还是仅限“购买A商品”可用？是仅限App内，还是小程序也可用？ |
| **有效期限** | `使用时间` | 这是刺激用户在未来消费的关键。是“领取后7天内有效”，还是只能在“固定的10月1日到10月7日”之间使用？ |
| **发放与领取**| `发放数量` / `领取人` | 这张券总共准备发多少张？是所有人都可以公开领取，还是只发给“VIP用户”的专属福利？ |

只有将这些要素全部定义清楚，我才能设计出一套足够灵活、能满足各种运营场景的优惠券系统。

**2. 优惠券的生命周期与用户旅程**

![image-20250726130240979](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130240979.png)

定义了优惠券的核心要素后，我们再从宏观视角，看一下优惠券从诞生到消亡的“一生”，也就是它的生命周期流程。

* **核心角色**：**商家**（创建者和发放者）与**用户**（领取者和使用者）。
* **生命周期**：`商家创建/发放` -> `用户领取` -> `用户使用` -> `商家统计`。我的产品设计，必须支撑起这个完整的闭环。

对于用户来说，他们与优惠券的互动，主要发生在两个核心环节：“**领券**”和“**用券**”。

* **“领券”环节的场景设计**

![image-20250726130516515](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130516515.png)

我必须在用户消费决策的关键路径上，为他们提供清晰、便捷的领券入口。例如，在商品详情页，我会明确地告诉用户“**本店现有以下优惠券可供领取**”，用户点击后，即可在弹窗中一键领取。

* **“用券”环节的场景设计**

![image-20250726130544241](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130544241.png)当用户选好了商品，来到订单结算页时，这是优惠券发挥作用的最后、也是最关键的环节。在这个页面，我会设计一个“优惠券”的选择栏，用户点击后：

1.  系统会自动判断用户当前订单满足了哪些优惠券的使用门槛，将“**可用优惠券**”高亮地展示在最前方。
2.  对于那些用户已领取、但当前订单“**不可用**”的优惠券，我也会展示出来，并清晰地告知用户“不可用”的原因（例如，“未达到满减金额”）。这是一种反向的激励，可能会促使用户返回去，再多买一件商品来凑单。

通过对业务目标、核心要素、生命周期和用户旅程的完整分析，我们就为接下来进行具体的B端“优惠券创建”功能设计，铺平了道路。

好的，我们已经清晰地定义了优惠券工具的需求。接下来，我将带你进入产品设计的核心环节，看看我是如何将这些需求，转化为一个强大、灵活且易于商家使用的后台功能。



---

#### 2.优惠券工具的产品设计

我的设计哲学是，**把复杂留给自己，把简单交给用户**。对于优惠券这种玩法极其丰富的工具，B端（商家端）的设计尤其考验产品经理的抽象和归纳能力。我需要将万千种运营场景，收敛到一套结构化、标准化的创建流程中。

![image-20250726131021810](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726131021810.png)

我设计的优惠券创建过程，主要分为三大步骤：**设置基本信息 -> 填写领取规则 -> 填写使用规则**。

##### 一、设置基本信息与领取规则

![image-20250726131936090](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726131936090.png)

在创建优惠券的第一步，我将“基本信息”和“领取规则”放在了一起，因为它们共同定义了这张优惠券的“**身份**”和“**发放方式**”。

* **基本信息**：
    * `优惠券名称`：方便商家在后台进行识别和管理。
    * `优惠券数量`：即“库存”，控制了这张券的总发放量，是控制成本的第一道闸门。
    * `使用平台`：定义这张券是在App、H5还是小程序中生效。
* **领取规则**：这是实现“**精细化运营**”的关键。
    * `领取用户`：我为商家提供了多种用户圈定方式。可以是`全部用户`可领的普惠券；也可以是针对`用户等级`（如：钻石会员专享）或`用户标签`（如：高潜流失用户）的精准券；甚至支持`上传文件`，针对特定的用户ID列表进行一对一发放。
    * `领取张数`：可以限制`每人限领N张`，防止被“羊毛党”恶意刷取。
    * `领取时间`：定义这张优惠券可以被领取的起止时间。
    * `公开设置`：这是一个非常重要的开关。如果勾选了“**公开领取**”，这张券就会出现在商品详情页等C端入口，供用户主动领取。如果不勾选，它就是一张“**私有券**”，不会对外展示，只能由运营人员通过后台手动发放给指定用户，常用于客服补偿等场景。

##### 二、填写使用规则——玩法的核心

这是优惠券设计的灵魂所在。一张券到底“怎么用”，决定了它的营销价值。我设计了多种优惠券类型，来满足不同的业务场景。

**2.1 满减券的设计**

![image-20250726132042176](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132042176.png)

这是最常见的一种优惠券。它的核心规则包括：

* **优惠券类型**：首先，我定义了券的适用范围。是仅限购买某些商品的`商品券`，还是全场通用的`通用券`，或者是只能抵扣运费的`运费券`。
* **使用门槛**：即“满X元可用”。
* **优惠券面额**：即“减Y元”。
* **有效期**：这是刺激用户复购的关键。我设计了两种模式：
    * `固定时间`：例如，国庆节专用券，只能在10月1日到10月7日之间使用。
    * `相对时效`：这种模式更为灵活，例如`自领取之日起N天内可用`，或者`自领取次日起N天内可用`。这能确保每个领到券的用户，都有一个完整的有效期。
* **适用范围**：这里可以更精细地控制券能用于哪些商品，是`全部商品`，还是`指定商品`、`指定类目`或`指定品牌`。

![image-20250726132157048](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132157048.png)

当商家选择“指定商品”时，我会提供一个与我们之前设计完全一致的、可复用的商品选择器组件，让他可以方便地进行勾选。

**2.2 折扣券的设计**

![image-20250726132136682](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132136682.png)

折扣券的设计，与满减券大部分相同，核心区别在于“优惠方式”的定义。商家不再是输入一个固定的“面额”，而是输入一个“**折扣率**”，例如“打8折”。

**2.3 立减券的设计**

![image-20250726132143949](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132143949.png)

立减券，又称“现金券”，是优惠力度最大的一种。它的特点是“**无使用门槛**”。在设计上，我只需要让商家输入一个“**立减金额**”即可。这种券通常用于新用户注册礼包、或高价值用户的回归召回等关键场景。

##### 三、 优惠券的管理

![image-20250726132209133](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132209133.png)

当商家创建完所有优惠券后，他可以在这张“**优惠券管理列表**”中，对所有券进行统一的查看和操作。

我为这张列表设计了清晰的信息维度：
* **核心信息**：优惠券的`名称`、`类型`（满减/折扣）、`发放数量`、`有效期`等一目了然。
* **优惠券状态**：我通过`未开始`、`领取中`、`已结束`、`已失效`这几种状态，让商家可以清晰地了解每一张券当前的生命周期阶段。
* **快捷操作**：商家可以对不同状态的券，进行`查看`、`编辑`、`结束活动`或查看`数据`等操作。

通过以上这套B端产品设计，我就为商家提供了一个功能强大、配置灵活、管理方便的“优惠券弹药库”，让他们可以根据不同的营销战役，自由地组合和使用这些“弹药”。

---
#### 3. 优惠券的逻辑规则

当一个订单中，存在多个商品、多张可用优惠券时，系统必须有一套清晰的规则，来决定最终如何计算优惠。我将它总结为三大核心规则。

##### **规则一：叠加与互斥**

![image-20250726132817387](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132817387.png)

这个问题的答案，取决于优惠券的类型。我制定的核心原则非常简单：**同一类型的优惠券，一个订单只能使用一张；不同类型的优惠券，在不冲突的情况下，可以叠加使用。**

例如，用户可以同时使用一张“店铺满减券”、一张“平台品类券”和一张“运费券”，但不能同时使用两张“店铺满减券”。

![image-20250726132847661](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132847661.png)

上图就是一个非常典型的真实案例，一个订单同时叠加了多种不同类型的优惠，最终形成了一个极具吸引力的价格。我的系统设计，就必须能够支持这种复杂的叠加计算。

![image-20250726133202001](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726133202001.png)

我们来看上面这个非常经典的、模拟真实购物场景的案例。要判断这4张优惠券能否同时使用，我设计的系统，会遵循以下逻辑进行严谨的校验。

**第一步：识别每张优惠券的“类型”与“范围”**

我首先会将这4张券进行归类：

| **优惠券**  | **券类型** | **作用范围**        | **使用门槛**      |
| ----------- | ---------- | ------------------- | ----------------- |
| **优惠券1** | `商品券`   | 仅限“鼠标”这个商品  | 无门槛（立减）    |
| **优惠券2** | `店铺券`   | 仅限“A店铺”内的商品 | 订单金额满100元   |
| **优惠券3** | `平台券`   | 跨店铺所有商品      | 订单总金额满400元 |
| **优惠券4** | `运费券`   | 仅限“A店铺”的运费   | 无门槛（免邮）    |

**第二步：逐一校验每张券的“门槛”与“叠加规则”**

1. **校验【优惠券1】**：它是一张`商品券`，直接作用于鼠标，无使用门槛。**可用**。
2. **校验【优惠券2】**：它是一张`店铺券`。计算A店铺的商品总价为 `99元(鼠标) + 30元(鼠标垫) = 129元`。这个价格满足了“满100元”的使用门槛。由于它和优惠券1的类型（`店铺券` vs `商品券`）不同，因此**可叠加使用**。
3. **校验【优惠券3】**：它是一张`平台券`。计算跨店订单的总价为 `129元(A店铺) + 398元(B店铺) = 527元`。这个价格满足了“满400元”的使用门槛。由于它和前两张券的类型（`平台券` vs `店铺券`/`商品券`）都不同，因此**可叠加使用**。
4. **校验【优惠券4】**：它是一张`运费券`，属于特殊类型，用于抵扣A店铺的运费，通常可以和所有其他类型的优惠券叠加。**可用**。

**第三步：得出最终结论**

- **这4张优惠券可以同时使用吗？**
	- **可以。** 因为这四张券分别属于**商品券、店铺券、平台券、运费券**，类型各不相同，且订单情况满足了它们各自的使用门槛，因此它们可以完美地叠加使用。
- **系统应该推荐使用哪张优惠券呢？**
	- **全部推荐使用。** 在这个场景下，由于所有券都可以叠加，并且都能带来优惠，系统的最优策略就是默认将这4张券**全部勾选并应用**，从而为用户计算出最终的、优惠力度最大的订单价格。

在电商后台，我定义优惠券的“类型”，其核心依据，并不是它“长什么样”，而是它的“**作用范围**”和“**成本由谁承担**”。只有基于这两个维度，我才能建立起一套严谨、无歧义的叠加互斥规则。

我将优惠券，严格划分为以下几个**层级完全不同**的类型：

| **优惠券类型** | **定义与作用范围** | **成本承担方** | **核心目的** |
| :--- | :--- | :--- | :--- |
| **单品券** | **层级最低**。仅对指定的某一个商品（SKU/SPU）生效。 | 商家 | 推广单一爆款或清仓。 |
| **店铺券** | **层级居中**。对**指定店铺内**的所有或部分商品生效。 | **商家** | 提升**本店的客单价**和转化率。 |
| **平台券** | **层级最高**。可**跨店使用**，对平台上所有或部分店铺的商品生效。| **平台** | 提升**整个平台的GMV**和用户活跃度。 |
| **运费券** | **类型特殊**。仅用于抵扣运费。 | 商家 或 平台 | 降低用户的购买决策门槛。 |

**核心规则**：只有**同一个层级**的优惠券，才存在“互斥”关系。**不同层级**的优惠券，因为其作用范围和成本方完全不同，所以**可以叠加**。

-----

##### **规则二：推荐原则**

当一个订单同时满足多张优惠券的使用门槛时，系统应该如何帮助用户做出最优决策，商家或许并不想让用户同时使用多张卷，所以在我们上一小结的设计中，三个劵同时归类为了`商品券`，这时候我们的优先计算原则就是优惠最大的金额

![image-20250726133421088](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726133421088.png)

我的设计原则是——**永远帮助用户做出“最省钱”的选择**。系统后台会自动计算所有可能的、可叠加的优惠券组合方式，并默认选中那个“**优惠总金额最大**”的最佳组合。

我们来看一个复杂的实战案例：

![image-20250726134305240](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134305240.png)

面对这个复杂的场景，我的系统后台会进行如下的智能计算：

![image-20250726134246505](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134246505.png)

如上图所示，系统会：
1.  **匹配**：首先判断每个商品分别适用哪些优惠券。
2.  **组合**：然后尝试所有可行的叠加组合。
3.  **择优**：最后计算出“水杯类满减券 + A店铺满减券 + 运费券”这个组合，可以优惠131元，是所有组合中优惠力度最大的，因此系统会向用户默认推荐这个方案。

##### **规则三：分摊规则**

我们再次遇到了这个至关重要的财务规则。

![image-20250726134517071](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134517071.png)

当一个订单使用了多张、作用范围不同的优惠券后，发生部分退款时，分摊计算就变得更加复杂。我将这个计算过程，用一张表格为您清晰地呈现：

![image-20250726134534335](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134534335.png)

**最终结论**：当用户想要退货A商品（鼠标，原价99元）时，我设计的系统会从其原价中，扣除掉它所分摊到的`23.35`元优惠，最终应退款 `99 - 23.35 = 75.65` 元。只有这样，才能保证财务的绝对严谨。

#### 4. 优惠券工具的数据

作为一名专业的产品或运营，我绝不能只满足于“把功能做出来”。我必须知道我策划的每一次活动，效果如何，成本怎样。因此，为优惠券工具设计一套完善的“**数据详情**”，是必不可少的一步。

![image-20250726134849709](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134849709.png)

我将优惠券的数据监控，分为了四大维度，共计11个核心指标：

通过对这11个核心数据指标的持续监控和分析，我作为运营，就能够精准地洞察每一次优惠券活动的成败得失，并为下一次的优化，提供可靠的数据支撑。

---