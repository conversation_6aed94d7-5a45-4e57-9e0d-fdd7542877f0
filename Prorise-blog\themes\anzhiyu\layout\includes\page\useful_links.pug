#article-container
  // 添加搜索功能区域
  #useful-links-search-container
    .search-header
      .search-box
        input#useful-links-search(type="text" placeholder="搜索实用网站..." autocomplete="off")
        i.anzhiyufont.anzhiyu-icon-search.search-icon
      .category-filters
        .category-filter.active(data-category="all") 全部
        if site.data.useful_links
          each i in site.data.useful_links
            if i.class_name
              .category-filter(data-category=`${i.class_name}`)= i.class_name
    .search-stats
      span#search-results-count

  .flink
    if site.data.useful_links
      each i in site.data.useful_links
        if i.class_name
          h2(data-category=`${i.class_name}`)!= i.class_name + "(" + i.link_list.length + ")"
        if i.class_desc
          .flink-desc!=i.class_desc
        if i.flink_style === 'anzhiyu'
          // <<< 修改点在这里：在 div.anzhiyu-flink-list 上添加 data-category 属性
          div(class=i.lost_contact ? 'anzhiyu-flink-list cf-friends-lost-contact' : 'anzhiyu-flink-list' data-category=`${i.class_name}`)
            if i.link_list
              each item in i.link_list
                - let color = item.color || ""
                - let tag = item.tag || ""

                .flink-list-item
                  if color == "vip" && tag
                    span.site-card-tag.vip #[=tag]
                      i.light
                  else if color == "speed" && tag
                    span.site-card-tag.speed #[=tag]
                  else if tag
                    span.site-card-tag(style=`background-color: ${color}`) #[=tag]
                  else if item.recommend
                    span.site-card-tag 荐
                  if i.lost_contact
                    a.cf-friends-link(href=url_for(item.link) title=item.name target="_blank")
                      if theme.lazyload.enable
                        img.no-lightbox(data-lazy-src=url_for(item.avatar) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )
                      else
                        img.cf-friends-avatar.no-lightbox(src=url_for(item.avatar) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )
                      .flink-item-info
                        .flink-item-name.cf-friends-name-lost-contact= item.name
                  else
                    a.cf-friends-link(href=url_for(item.link) cf-href=url_for(item.link) title=item.name target="_blank")
                      if theme.lazyload.enable
                        img.cf-friends-avatar.no-lightbox(data-lazy-src=url_for(item.avatar), cf-src=url_for(item.avatar), onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )
                      else
                        img.cf-friends-avatar.no-lightbox(src=url_for(item.avatar) cf-src=url_for(item.avatar) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )
                      .flink-item-info
                        .flink-item-name.cf-friends-name= item.name
                        .flink-item-desc(title=item.descr)= item.descr
    else
      .flink-null 暂无实用网站数据
    != page.content