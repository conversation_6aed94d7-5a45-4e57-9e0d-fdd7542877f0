---
title: 19.内容拓展：增加隐藏文章功能
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 26102
date: 2025-07-11 11:13:45
---

### **19.内容拓展：增加隐藏文章功能**

###### **前言：功能介绍与用途**

本指南将引导您使用 `hexo-hide-posts` 插件，为您的博客添加“隐藏文章”功能。

启用后，被标记为“隐藏”的文章将**不会**出现在您的**首页、归档页、分类页、标签页或RSS订阅**中，但它依然会被生成为一个独立的页面，可以通过**直接访问它的永久链接**来查看。

这非常适合发布一些您不想公开展示，但又希望特定人群能看到的内容，例如：

* 私密的想法或日日志。
* 尚未完成、仅供朋友预览的草稿文章。
* 为特定活动或人群准备的专属页面。

---
###### **第一步：安装 `hexo-hide-posts` 插件**

在您Hexo博客的根目录下，打开终端，并运行以下命令来安装插件：
```bash
npm install hexo-hide-posts --save
```

---
###### **第二步：配置插件 (根目录 `_config.yml`)**

打开您博客**根目录**下的 `_config.yml` 文件，在文件末尾添加以下配置：

```yaml
# 隐藏文章插件配置 (hexo-hide-posts)
hide_posts:
  # 功能总开关
  enable: true

  # front-matter 中用于隐藏文章的“暗号”，可以自定义，默认为 'hidden'
  filter: hidden

  # 【推荐开启】为隐藏的文章添加 noindex 标签，阻止搜索引擎收录
  noindex: true

  # 高级选项，通常无需修改。用于控制哪些生成器可以“看到”隐藏文章。
  # allowlist_generators: []
  # blocklist_generators: ['*'] # ['*'] 表示默认对所有列表隐藏
```
* **配置说明**：这里的 `filter: hidden` 意味着，您之后只需要在文章的 Front-matter 中添加 `hidden: true` 就可以隐藏它。如果您喜欢，也可以把 `hidden` 改成 `private` 或其他任何您喜欢的词。

---
###### **第三步：在文章中应用隐藏标记**

1.  打开任何一篇您想要隐藏的文章的 `.md` 文件。
2.  在文件最顶部的 Front-matter 区域，添加您在第二步中设置的那个“暗号”（`filter`）。

    **示例：**
    ```yaml
    ---
    title: 这是一篇我想隐藏的文章
    date: 2025-06-14 23:55:00
    hidden: true # <--- 添加这一行，因为我们的 filter 设置的是 'hidden'
    ---

    这是文章的正文内容，它不会显示在首页...
    ```

---