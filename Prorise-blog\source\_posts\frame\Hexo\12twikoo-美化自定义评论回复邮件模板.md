---
title: 12.Twiko<PERSON> 美化：自定义评论回复邮件模板
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 57565
date: 2025-07-11 04:13:45
---

### **12.Twikoo 美化：自定义评论回复邮件模板**

###### **前言：功能介绍**

本指南将引导您将 Twikoo 默认的纯文本评论回复邮件，替换为一个图文并茂、样式精美的“亚克力粉”风格 HTML 模板。当有访客的评论被回复时，他们收到的不再是简单的文字，而是一封精心设计的通知邮件。

###### **核心流程概览**
1.  **获取邮件模板HTML代码**：复制教程作者提供的、包含所有样式的HTML代码。
2.  **在Twikoo后台配置邮件模板**：将代码粘贴到Twikoo管理后台的指定位置。
3.  **个性化修改模板**：将模板中的默认图标替换为您自己的网站图标。

---
###### **第一步：复制模板HTML代码**

首先，请**完整地复制**下面这个代码框中的全部 HTML 代码。这就是邮件模板的本体。

```html
<div class="page flex-col">
  <div class="box_3 flex-col" style="display: flex; position: relative; width: 100%; height: 206px; background: rgba(82, 97, 107, 0.1); top: 0; left: 0; justify-content: center;">
    <div class="section_1 flex-col" style="background-image: url(&quot;【请替换为您的网站图标URL】&quot;); position: absolute; width: 152px; height: 152px; display: flex; top: 130px; background-size: cover;"></div>
  </div>
  <div class="box_4 flex-col" style="margin-top: 92px; display: flex; flex-direction: column; align-items: center;">
    <div class="text-group_5 flex-col justify-between" style="display: flex; flex-direction: column; align-items: center; margin: 0 20px;">
      <span class="text_1" style="font-size: 26px; font-family: PingFang-SC-Bold, PingFang-SC; font-weight: bold; color: #1e2022; line-height: 37px; text-align: center;">嘿！您在&nbsp;${SITE_NAME}&nbsp;博客中收到一条新回复。</span>
      <span class="text_2" style="font-size: 16px; font-family: PingFang-SC-Bold, PingFang-SC; font-weight: bold; color: rgba(30, 32, 34, 0.5); line-height: 22px; margin-top: 21px; text-align: center;">您之前的评论&nbsp;在&nbsp;${SITE_NAME} 博客中收到来自&nbsp;${NICK}&nbsp;的回复</span>
    </div>
    <div class="box_2 flex-row" style="margin: 0 20px; min-height: 128px; background: #f0f5f9; border-radius: 12px; margin-top: 34px; display: flex; flex-direction: column; align-items: flex-start; padding: 32px 16px; width: calc(100% - 40px);">
      <div class="text-wrapper_4 flex-col justify-between" style="display: flex; flex-direction: column; margin-left: 30px; margin-bottom: 16px;">
        <span class="text_3" style="height: 22px; font-size: 16px; font-family: PingFang-SC-Bold, PingFang-SC; font-weight: bold; color: #52616b; line-height: 22px;">${PARENT_NICK}</span>
        <span class="text_4" style="margin-top: 6px; margin-right: 22px; font-size: 16px; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #1e2022; line-height: 22px;">${PARENT_COMMENT}</span>
      </div>
      <hr style="display: flex; position: relative; border: 1px dashed #c9d6df; box-sizing: content-box; height: 0px; overflow: visible; width: 100%;">
      <div class="text-wrapper_4 flex-col justify-between" style="display: flex; flex-direction: column; margin-left: 30px;">
        <hr>
        <span class="text_3" style="height: 22px; font-size: 16px; font-family: PingFang-SC-Bold, PingFang-SC; font-weight: bold; color: #52616b; line-height: 22px;">${NICK}</span>
        <span class="text_4" style="margin-top: 6px; margin-right: 22px; font-size: 16px; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #1e2022; line-height: 22px;">${COMMENT}</span>
      </div>
      <a class="text-wrapper_2 flex-col" style="min-width: 106px; height: 38px; background: rgba(82, 97, 107, 0.15); border-radius: 32px; display: flex; align-items: center; justify-content: center; text-decoration: none; margin: auto; margin-top: 32px;" href="${POST_URL}">
        <span class="text_5" style="color: #52616b;">查看详情</span>
      </a>
    </div>
    <div class="text-group_6 flex-col justify-between" style="display: flex; flex-direction: column; align-items: center; margin-top: 34px;">
      <span class="text_6" style="height: 17px; font-size: 12px; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: rgba(30, 32, 34, 0.6); line-height: 17px;">此邮件由评论服务自动发出，直接回复无效。</span>
      <a class="text_7" style="height: 17px; font-size: 12px; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #52616b; line-height: 17px; margin-top: 6px; text-decoration: none;" href="${SITE_URL}">前往博客</a>
    </div>
  </div>
</div>
```

---
###### **第二步：在Twikoo后台粘贴并修改模板**

1.  **登录您的Twikoo管理后台**。
2.  输入管理员密码进入管理面板。
3.  在后台管理界面中，找到 **“邮件通知”** 选项卡。
4.  找到名为 **`MAIL_TEMPLATE`** 或 **“邮件模板”** 的输入框。
5.  将您在第一步中复制的**全部HTML代码**粘贴进去。
6.  **【关键】** 在您粘贴的代码中，找到这行文字：`这里更改为你的网站图标`。
7.  请将它替换为您自己网站图标的**完整、可公开访问的图片链接**。例如：`https://prorise.com/img/favicon.ico`。
8.  点击“保存”。

---
###### **第三步：（可选）了解并进阶魔改**

如果您想进一步定制邮件内容，可以利用 Twikoo 提供的动态变量。这些变量在发送邮件时会自动被替换为真实数据。

| 变量 (Variable) | 含义 (Meaning) |
| :--- | :--- |
| `${SITE_URL}` | 网站链接 |
| `${SITE_NAME}` | 网站名字 |
| `${PARENT_NICK}` | 被回复人昵称 |
| `${PARENT_COMMENT}`| 被回复人的评论内容 |
| `${NICK}` | 回复人昵称 |
| `${COMMENT}` | 回复人评论内容 |
| `${POST_URL}` | 评论所在的文章链接 |
| `${IMG}` | 回复人头像 |
| `${PARENT_IMG}` | 被回复人头像 |
| `${MAIL}` | 回复人邮箱 |
| `${IP}` | 回复人 IP 地址 |

例如，您可以将邮件标题 `嘿！你在 ${SITE_NAME} 博客中收到一条新回复。` 修改为您更喜欢的任何文字。



对此我们可以新建一个博主收到回复的通知，用于放置到`MAIL_TEMPLATE_ADMIN`

```html
<div style="background-color: #f0f5f9; padding: 20px; font-family: Arial, 'Microsoft YaHei', sans-serif; line-height: 1.6; color: #1e2022;">
  <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border: 1px solid #c9d6df; border-radius: 8px; overflow: hidden;">
    
    <div style="background-color: #52616b; color: #ffffff; padding: 20px; text-align: center;">
      <h1 style="margin: 0; font-size: 24px;">新评论抵达通知</h1>
      <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.8;">来自您的博客 [${SITE_NAME}]</p>
    </div>
    
    <div style="padding: 20px 30px 30px 30px;">
      
      <p style="margin: 0; font-size: 16px;">
        一位名为 <strong style="color: #52616b;">${NICK}</strong> 的访客在您的文章中发表了新评论：
      </p>

      <div style="background-color: #f0f5f9; border-left: 3px solid #52616b; padding: 15px; margin: 20px 0; font-style: italic;">
        ${COMMENT}
      </div>

      <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; border-top: 1px solid #c9d6df; padding-top: 20px; margin-top: 20px;">
        <tr>
          <td style="padding: 5px 0; color: #52616b; width: 80px;"><strong>评论者:</strong></td>
          <td style="padding: 5px 0;">${NICK}</td>
        </tr>
        <tr>
          <td style="padding: 5px 0; color: #52616b;"><strong>邮 箱:</strong></td>
          <td style="padding: 5px 0;"><a href="mailto:${MAIL}" style="color: #52616b; text-decoration: none;">${MAIL}</a></td>
        </tr>
        <tr>
          <td style="padding: 5px 0; color: #52616b;"><strong>IP 地址:</strong></td>
          <td style="padding: 5px 0; font-family: 'Courier New', Courier, monospace;">${IP}</td>
        </tr>
        <tr>
          <td style="padding: 5px 0; color: #52616b;"><strong>来 源:</strong></td>
          <td style="padding: 5px 0;"><a href="${POST_URL}" target="_blank" style="color: #52616b; text-decoration: none;">点击查看原文</a></td>
        </tr>
      </table>

      <div style="padding-top: 30px; text-align: center; border-top: 1px solid #c9d6df; margin-top: 20px;">
        <a href="${POST_URL}" target="_blank" style="display: inline-block; padding: 12px 28px; background-color: #52616b; color: #ffffff; text-decoration: none; border-radius: 5px; margin: 0 5px;">快速回复</a>
        <a href="https://twikoo.prorise666.site/ui" target="_blank" style="display: inline-block; padding: 12px 24px; background-color: #e2e8f0; color: #1e2022; text-decoration: none; border-radius: 5px; margin: 0 5px;">管理后台</a>
      </div>

    </div>
    
    <div style="background-color: #f0f5f9; color: #52616b; padding: 15px; text-align: center; font-size: 12px;">
      <p style="margin: 0;">Prorise's Blog Automated System // Do not reply directly.</p>
    </div>
  </div>
</div>
```

#### 收不到提醒邮件？

如果是 Vercel 部署的云函数，请配置国外邮件服务商，避免被邮件服务商判定为垃圾邮件行为。如果是其他原因，请前往 Twikoo 管理面板，找到邮件测试功能，输入个人邮箱，根据测试结果排查原因。

如果是 Vercel 部署的云函数，邮件测试正常，但实际评论收不到任何即时消息通知 / 邮件通知，请打开 Vercel 云函数管理页面，进入 Settings - Deployment Protection，设置 Vercel Authentication 为 Disabled，并 Save。

[![img](https://twikoo.js.org/assets/vercel-1.Czytea8u.png)](https://twikoo.js.org/assets/vercel-1.Czytea8u.png)

为了避免频繁检查邮箱带来的性能问题，邮件配置有 10 分钟左右的缓存，如果确定配置没有问题，但测试失败，可以等待 10 分钟后再测试。

由于博主发表评论时，不会通知博主，如果您想实际测试通知功能，请注销管理面板后用非博主邮箱发表或回复评论。

---