script.
    (() => {
        const changeContent = (content) => {
            if (!content) return '';
            const replacements = [
                {regex: /<img.*?src="(.*?)"?[^\>]+>/ig, replacement: '[!{_p("console.newest_comment.image")}]'},
                {
                    regex: /<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi,
                    replacement: '[!{_p("console.newest_comment.link")}]'
                },
                {regex: /<pre><code>.*?<\/pre>/gi, replacement: '[!{_p("console.newest_comment.code")}]'},
                {regex: /<[^>]+>/g, replacement: ''}
            ]
            content = replacements.reduce((str, {regex, replacement}) => str.replace(regex, replacement), content)
            return content.length > 150 ? `${content.substring(0, 100)}...` : content
        }
        const generateHtml = (array) => {
            const html = array.map(item => `
                <div class='aside-list-item'>
                    <div class='thumbnail'>
                        <img src='${item.avatar}' alt='${item.nick}'>
                    </div>
                    <div class='content'>
                        <div class='comment' onclick='pjax.loadUrl("${item.url}")'>${item.content}</div>
                        <time class="datetime" datetime="${item.date}"></time>
                    </div>
                </div>
            `).join('')
            document.querySelector('.card-recent-comment .aside-list').innerHTML = array.length ? html : "!{_p('newest_comment.zero')}"
            window.lazyLoadInstance?.update()
            window.pjax?.refresh()
            sco?.changeTimeFormat(document.querySelectorAll('.aside-list-item time'))
        }
        const getComment = async () => {
            const runTwikoo = () => {
                twikoo.getRecentComments({
                    envId: '!{theme.twikoo.envId}',
                    region: '',
                    pageSize: !{limit},
                    includeReply: true
                }).then(res => {
                    const twikooArray = res.map(e => ({
                        content: changeContent(e.comment),
                        avatar: e.avatar,
                        nick: e.nick,
                        url: `${e.url}#${e.id}`,
                        date: new Date(e.created).toString()
                    })).slice(0, 6)
                    utils.saveToLocal.set('twikoo-newest-comment', twikooArray, !{theme.comment.newest_comment.storage})
                    generateHtml(twikooArray)
                }).catch((err) => {
                    console.error(err);
                    document.querySelector('.card-recent-comment .aside-list').textContent = "!{_p('newest_comment.error')}"
                })
            }
            if (typeof twikoo === 'object') {
                runTwikoo()
            } else {
                utils.getScript('!{url_for(theme.cdn.twikoo)}').then(runTwikoo)
            }
        }
        const newestCommentInit = () => {
            if (!document.querySelector('.card-recent-comment')) return
            const data = utils.saveToLocal.get('twikoo-newest-comment')
            data ? generateHtml(data) : getComment()
        }
        window.addEventListener('DOMContentLoaded', newestCommentInit, false)
        window.addEventListener('pjax:complete', newestCommentInit, false);
    })()