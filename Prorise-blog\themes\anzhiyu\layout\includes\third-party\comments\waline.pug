- const { serverURL, option, pageview, meta_css, imageUploader } = theme.waline
- const { lazyload, count, use } = theme.comments

script.
  (() => {
    let initFn = window.walineFn || null

    const initWaline = (Fn) => {
      const waline = Fn(Object.assign({
        el: '#waline-wrap',
        serverURL: '!{serverURL}',
        pageview: !{lazyload ? false : pageview},
        dark: 'html[data-theme="dark"]',
        path: window.location.pathname,
        comment: !{lazyload ? false : count},
        imageUploader: !{imageUploader},
      }, !{JSON.stringify(option)}))

      const destroyWaline = () => {
        waline.destroy()
      }

      anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
    }

    const loadWaline = async () => {
      if (initFn) initWaline(initFn)
      else {
        await getCSS('!{url_for(theme.asset.waline_css)}')
        if (!{meta_css}) await getCSS('!{url_for(theme.asset.waline_meta_css)}')
        const { init } = await import('!{url_for(theme.asset.waline_js)}')
        initFn = init || Waline.init
        initWaline(initFn)
        window.walineFn = initFn
      }
    }

    if ('!{use[0]}' === 'Waline' || !!{lazyload}) {
      if (!{lazyload}) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
      else setTimeout(loadWaline, 0)
    } else {
      window.loadOtherComment = loadWaline
    }
  })()
