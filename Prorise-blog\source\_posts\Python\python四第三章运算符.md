---
title: Python（四）：第三章：运算符
categories:
  - 后端技术
  - Python
tags:
  - Python基础知识总汇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp'
comments: true
toc: true
ai: true
abbrlink: 51603
date: 2025-04-18 19:13:45
---

## 第三章：运算符

### 运算符优先级
加减乘除 > 比较 > not > and > or

### 运算符表

| 类型       | 运算符                        | 描述                                           |
| ---------- | ----------------------------- | ---------------------------------------------- |
| 算术运算符 | +, -, *, /, //, %, **         | 加、减、乘、除、整除、取模、幂                 |
| 比较运算符 | ==, !=, >, <, > =, <=         | 等于、不等于、大于、小于、大于等于、小于等于   |
| 逻辑运算符 | and, or, not                  | 逻辑与、逻辑或、逻辑非                         |
| 位运算符   | &, \|, ^, ~, <<, > >          | 按位与、按位或、按位异或、按位取反、左移、右移 |
| 赋值运算符 | =, +=, -=, *=, /=, //=, %= 等 | 简单赋值、复合赋值                             |
| 身份运算符 | is, is not                    | 判断对象标识                                   |
| 成员运算符 | in, not in                    | 判断成员关系                                   |

### 赋值技巧
```python
# 不换行输出
print(1, end=' ')
print(2, end=' ')
print(3, end=' ')

# 增量赋值
n += 1  # 等价于 n = n + 1

# 链式赋值（同值多变量）
x = y = z = 10

# 交叉赋值（交换变量值）
m, n = 10, 20
m, n = n, m  # 交换值
```