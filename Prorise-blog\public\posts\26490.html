<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理进阶（四）：第四章：电商后台产品设计 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理进阶（四）：第四章：电商后台产品设计"><meta name="application-name" content="产品经理进阶（四）：第四章：电商后台产品设计"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理进阶（四）：第四章：电商后台产品设计"><meta property="og:url" content="https://prorise666.site/posts/26490.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第四章：电商后台产品设计欢迎来到第四章。在上一章，我们已经为用户，设计出了一套完整、华丽的“前台”购物体验。但支撑这一切前台体验能够顺利运转的，是一个我们普通用户永远看不到的、强大而复杂的“后台”系统。 后台，就是我们电商平台的“中央厨房”与“指挥中心”。本章，我们就将学习如何设计这个核心系统。 4"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta name="description" content="第四章：电商后台产品设计欢迎来到第四章。在上一章，我们已经为用户，设计出了一套完整、华丽的“前台”购物体验。但支撑这一切前台体验能够顺利运转的，是一个我们普通用户永远看不到的、强大而复杂的“后台”系统。 后台，就是我们电商平台的“中央厨房”与“指挥中心”。本章，我们就将学习如何设计这个核心系统。 4"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/26490.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理进阶（四）：第四章：电商后台产品设计",postAI:"true",pageFillDescription:"第四章：电商后台产品设计, 4.1 学习目标, 4.2 电商后台核心作用及架构, 4.2.1 后台对前台业务的支撑作用, 4.2.2 商家业务 vs 平台运营业务划分, 4.2.3 整体业务模块架构, 4.3 平台运营后台核心设计, 4.3.1 C端用户管理（用户状态与权限操作）, 1. 用户列表与查询, 2. 用户状态与权限操作, 4.3.2 商家入驻审核与店铺管理, 1. 商家入驻审核, 2. 店铺管理, 4.4 商家后台核心设计, 4.4.1 商家入驻流程与类型（京东、淘宝x2F天猫、自营x2F非自营）, 4.4.2 商家子账号设置与权限划分, 4.5 本章总结第四章电商后台产品设计欢迎来到第四章在上一章我们已经为用户设计出了一套完整华丽的前台购物体验但支撑这一切前台体验能够顺利运转的是一个我们普通用户永远看不到的强大而复杂的后台系统后台就是我们电商平台的中央厨房与指挥中心本章我们就将学习如何设计这个核心系统学习目标在本节中我的核心目标是带大家建立起对电商后台的宏观架构认知我们将学习后台的核心作用并重点理解在招商模式下如何清晰地划分商家业务与平台业务并最终绘制出我们整个电商生态的业务模块架构图电商后台核心作用及架构后台对前台业务的支撑作用我理解的电商后台其最核心最根本的作用就是为前台的所有业务提供支撑前台用户能看到的每一个商品都是由后台的商品中心来管理的用户下的每一笔订单都是由后台的订单中心和支付中心来处理的订单能被顺利配送则依赖于后台的物流中心和仓储管理系统前台是光鲜亮丽的餐厅而后台就是保证餐厅能正常运转的厨房仓库和收银系统的总和商家业务平台运营业务划分对于我们大超级电商这样的招商模式平台我需要特别澄清一点我们的后台其实并不是一个单一的系统而是两个既相互独立又紧密关联的系统商家后台这是我设计给入驻商家使用的后台它是商家在我们平台上开店经营的专属工作台其核心的业务方向是服务于商家的日常经营包括入驻平台管理店铺发布管理商品订单跟进售后处理平台运营后台这是我设计给我们自己公司内部员工运营审核客服等使用的后台它是我们管理整个平台的上帝视角控制台其核心的业务方向是管理和监督包括审核商家入驻审核商家发布的商品订单跟进仲裁管理平台所有的商家和用户清晰地划分这两套后台的业务边界是我进行后台架构设计的第一步整体业务模块架构最后我会将我们整个电商生态的所有功能模块汇总成一张整体业务模块架构图这张图就是我们整个项目的总蓝图如案例图所示这张架构图清晰地划分出了我们项目的三大组成部分用户端即我们在第三章设计的面向端消费者的前台商家端即我们即将设计的面向端商家的后台管理系统平台端即我们即将设计的面向我们自己内部员工的后台管理系统在每一个端的下面我会罗列出它所包含的核心功能模块比如平台端就包含了商品管理订单管理系统管理店铺管理等模块这张架构图不仅能让所有项目成员对我们产品的全貌一目了然更能清晰地揭示出前台的每一个核心业务是由后台的哪一个模块来提供支撑的它是我们后续进行详细设计和技术架构设计的最高纲领平台运营后台核心设计现在我们开始设计我们自己公司内部员工使用的上帝视角后台平台运营后台这个系统的设计我最看重的三个原则是安全高效可追溯因为在这里的每一个操作都可能会对我们平台上的用户商家乃至整个平台的声誉产生直接的影响本节我们重点设计其中两个最高频也最重要的模块端用户管理和商家管理端用户管理用户状态与权限操作这个模块赋予了我们的运营和客服同事管理平台上所有端消费者买家的权力用户列表与查询正如案例图所示这个界面的核心是一个完整的用户列表为了让我的运营同事能从数以万计的用户中精准地找到目标我必须为他们设计一套强大的查询和筛选功能筛选字段我的设计说明用户信息提供一个统一的输入框支持按用户昵称手机号甚至用户进行模糊或精确搜索注册时间提供一个日期范围选择器方便运营分析特定时期内如某次活动期间的新增用户用户状态这是最重要的筛选条件提供一个下拉菜单让客服或风控团队能快速筛选出所有正常已禁用的用户进行集中的管理用户状态与权限操作操作列是这个模块权力的体现一个严谨的设计是根据用户当前的状态来提供不同的操作权限当用户状态为正常时我提供的核心操作是查看编辑禁用我的拓展设计点击禁用时我不会让它立刻生效我会设计一个弹窗要求操作员必须选择一个禁用的时长如天天永久并填写禁用的原因这个原因一部分会展示给用户另一部分则会作为内部日志记录在案以备查验当用户状态为禁用时禁用按钮则会变为解禁按钮操作员点击后二次确认即可将该用户恢复为正常状态这种基于状态的权限设计能极大地避免运营人员的误操作让后台管理更规范更安全商家入驻审核与店铺管理管理商家卖家比管理用户要复杂得多因为它直接关系到我们平台的商品供给服务质量和核心收入商家入驻审核这是我们平台的第一道门卫所有想在我们平台开店的商家都必须经过这一环节的资质审核我会把它设计成一个独立的工作台待审核列表页面的默认视图是一个待审核队列展示了所有提交了入驻申请但尚未被处理的商家审核详情页运营点击审核后会进入一个详情页能看到该商家提交的所有信息包括营业执照法人信息品牌授权书等在这个页面只有两个核心的操作按钮通过和驳回我的拓展设计点击驳回时我同样会要求运营必须从一个预设的驳回原因列表如资质不全信息不符中选择一项或手动输入驳回原因这个原因将通过短信或站内信清晰地告知申请者提升平台的专业度店铺管理当商家审核通过拥有了自己的店铺后他们就会进入到店铺管理列表中核心信息这个列表除了展示商家的基本信息外更需要展示店铺名称主营类目入驻时间店铺状态等运营信息核心操作针对店铺这个主体我设计的操作权限会比针对用户的权限更谨慎层级更高查看店铺可以一键跳转到该店铺的前台页面冻结店铺当商家出现较严重的违规行为如售假时运营可以暂时冻结其店铺冻结期间店铺所有商品都会被下架商家也无法登录后台关闭店铺这是最严厉的处罚意味着与该商家终止合作将其清退出平台这个操作我通常会设计为需要运营主管及以上级别的角色才能执行商家后台核心设计在上一节我们设计了我们自己内部团队使用的平台运营后台现在我们要来设计一个同样重要但使用者完全不同的系统商家后台这是我们为所有入驻我们大超级电商平台的第三方商家提供的专属线上店铺办公室这个后台的体验好坏直接决定了我们能否吸引和留住优质的商家是保障我们平台商品丰富度和供给侧质量的生命线商家入驻流程与类型京东淘宝天猫自营非自营在我设计商家后台的第一个功能商家入驻时我首先要思考一个战略问题我们希望吸引什么样的商家像淘宝一样追求让天下没有难做的生意允许个人和企业免费开店降低门槛最大化地丰富商品供给还是像天猫一样只面向具备一定品质和资质的企业抬高门槛保障平台的品牌调性这个决策决定了我们入驻流程的复杂度和审核的严格度对于我们的招商模式我们将主要设计一个非自营商家的入驻流程一个专业的商家入驻流程远比用户注册要复杂我设计的流程通常包含以下几个核心阶段入驻前准备在申请入口我会清晰地告知商家需要提前准备好哪些资质文件在线申请引导商家填写一系列的申请信息平台审核商家提交后申请单会进入我们上一节设计的平台运营后台商家审核模块由我们的运营同事进行审核开店任务审核通过后我会引导商家完成一系列的开店准备工作如缴纳费用签署在线合同等在在线申请这个环节我设计的表单会清晰地区分个人类型和企业类型的商家因为他们需要提交的个人信息和公司信息如营业执照是完全不同的在开店任务环节我设计的流程必须清晰地向商家展示需要缴纳的费用这通常包括平台使用费年费按年收取的技术服务费保证金一笔押金用于在商家出现违规行为损害消费者利益时对消费者进行赔付商家子账号设置与权限划分当一个商家的店铺成功开张生意越做越大一个新需求就出现了店主主账号一个人忙不过来了他需要让手下的员工如客服运营库管来一起管理店铺但是他又不能把最高权限的主账号密码告诉所有人为了解决这个痛点我必须为商家后台设计一个子账号管理功能这本质上是为每一个商家提供了一套迷你的店铺内部的基于角色的访问控制系统我的设计主要包含两个核心功能角色管理我会允许主账号在后台创建不同的角色如客服专员运营专员并为这些角色勾选分配不同的操作权限如客服专员只能查看订单和回复咨询但无权修改商品价格员工管理主账号可以在这里为他的每一位员工创建一个子账号并为这个子账号指定一个我们上面创建好的角色这个功能是区分一个玩具级和一个企业级商家后台的重要标志本章总结在本章我们深入到了电商冰山的水下部分系统地学习了后台的设计核心作用与架构我们明确了后台是为前台服务的指挥中心并学会了如何划分商家业务和平台业务搭建起用户端商家端平台端三位一体的宏观产品架构平台运营后台我们设计了平台自己使用的核心模块包括如何进行端用户管理特别是状态控制以及如何建立一套严谨的商家入驻审核与店铺管理流程商家后台我们设计了服务于商家的核心模块包括如何根据不同平台定位设计差异化的商家入驻流程以及如何通过子账号功能来满足商家的团队协作与权限管理需求",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-25 11:05:48",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%9B%9B%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">第四章：电商后台产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">4.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-2-%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0%E6%A0%B8%E5%BF%83%E4%BD%9C%E7%94%A8%E5%8F%8A%E6%9E%B6%E6%9E%84"><span class="toc-text">4.2 电商后台核心作用及架构</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-1-%E5%90%8E%E5%8F%B0%E5%AF%B9%E5%89%8D%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%9A%84%E6%94%AF%E6%92%91%E4%BD%9C%E7%94%A8"><span class="toc-text">4.2.1 后台对前台业务的支撑作用</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-2-%E5%95%86%E5%AE%B6%E4%B8%9A%E5%8A%A1-vs-%E5%B9%B3%E5%8F%B0%E8%BF%90%E8%90%A5%E4%B8%9A%E5%8A%A1%E5%88%92%E5%88%86"><span class="toc-text">4.2.2 商家业务 vs 平台运营业务划分</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-3-%E6%95%B4%E4%BD%93%E4%B8%9A%E5%8A%A1%E6%A8%A1%E5%9D%97%E6%9E%B6%E6%9E%84"><span class="toc-text">4.2.3 整体业务模块架构</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-3-%E5%B9%B3%E5%8F%B0%E8%BF%90%E8%90%A5%E5%90%8E%E5%8F%B0%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1"><span class="toc-text">4.3 平台运营后台核心设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-1-C%E7%AB%AF%E7%94%A8%E6%88%B7%E7%AE%A1%E7%90%86%EF%BC%88%E7%94%A8%E6%88%B7%E7%8A%B6%E6%80%81%E4%B8%8E%E6%9D%83%E9%99%90%E6%93%8D%E4%BD%9C%EF%BC%89"><span class="toc-text">4.3.1 C端用户管理（用户状态与权限操作）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%94%A8%E6%88%B7%E5%88%97%E8%A1%A8%E4%B8%8E%E6%9F%A5%E8%AF%A2"><span class="toc-text">1. 用户列表与查询</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%94%A8%E6%88%B7%E7%8A%B6%E6%80%81%E4%B8%8E%E6%9D%83%E9%99%90%E6%93%8D%E4%BD%9C"><span class="toc-text">2. 用户状态与权限操作</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-2-%E5%95%86%E5%AE%B6%E5%85%A5%E9%A9%BB%E5%AE%A1%E6%A0%B8%E4%B8%8E%E5%BA%97%E9%93%BA%E7%AE%A1%E7%90%86"><span class="toc-text">4.3.2 商家入驻审核与店铺管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%95%86%E5%AE%B6%E5%85%A5%E9%A9%BB%E5%AE%A1%E6%A0%B8"><span class="toc-text">1. 商家入驻审核</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%BA%97%E9%93%BA%E7%AE%A1%E7%90%86"><span class="toc-text">2. 店铺管理</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-4-%E5%95%86%E5%AE%B6%E5%90%8E%E5%8F%B0%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1"><span class="toc-text">4.4 商家后台核心设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-1-%E5%95%86%E5%AE%B6%E5%85%A5%E9%A9%BB%E6%B5%81%E7%A8%8B%E4%B8%8E%E7%B1%BB%E5%9E%8B%EF%BC%88%E4%BA%AC%E4%B8%9C%E3%80%81%E6%B7%98%E5%AE%9D-%E5%A4%A9%E7%8C%AB%E3%80%81%E8%87%AA%E8%90%A5-%E9%9D%9E%E8%87%AA%E8%90%A5%EF%BC%89"><span class="toc-text">4.4.1 商家入驻流程与类型（京东、淘宝/天猫、自营/非自营）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-2-%E5%95%86%E5%AE%B6%E5%AD%90%E8%B4%A6%E5%8F%B7%E8%AE%BE%E7%BD%AE%E4%B8%8E%E6%9D%83%E9%99%90%E5%88%92%E5%88%86"><span class="toc-text">4.4.2 商家子账号设置与权限划分</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-5-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">4.5 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理进阶（四）：第四章：电商后台产品设计</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-24T11:13:45.000Z" title="发表于 2025-07-24 19:13:45">2025-07-24</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.651Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>10分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理进阶（四）：第四章：电商后台产品设计"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/26490.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/26490.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理进阶（四）：第四章：电商后台产品设计</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-24T11:13:45.000Z" title="发表于 2025-07-24 19:13:45">2025-07-24</time><time itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.651Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></header><div id="postchat_postcontent"><h1 id="第四章：电商后台产品设计"><a href="#第四章：电商后台产品设计" class="headerlink" title="第四章：电商后台产品设计"></a>第四章：电商后台产品设计</h1><p>欢迎来到第四章。在上一章，我们已经为用户，设计出了一套完整、华丽的“<strong>前台</strong>”购物体验。但支撑这一切前台体验能够顺利运转的，是一个我们普通用户永远看不到的、强大而复杂的“<strong>后台</strong>”系统。</p><p>后台，就是我们电商平台的“<strong>中央厨房</strong>”与“<strong>指挥中心</strong>”。本章，我们就将学习如何设计这个核心系统。</p><h2 id="4-1-学习目标"><a href="#4-1-学习目标" class="headerlink" title="4.1 学习目标"></a>4.1 学习目标</h2><p>在本节中，我的核心目标是，带大家建立起对电商后台的<strong>宏观架构认知</strong>。我们将学习后台的核心作用，并重点理解，在招商模式下，如何清晰地划分“<strong>商家业务</strong>”与“<strong>平台业务</strong>”，并最终绘制出我们整个电商生态的“<strong>业务模块架构图</strong>”。</p><h2 id="4-2-电商后台核心作用及架构"><a href="#4-2-电商后台核心作用及架构" class="headerlink" title="4.2 电商后台核心作用及架构"></a>4.2 电商后台核心作用及架构</h2><h3 id="4-2-1-后台对前台业务的支撑作用"><a href="#4-2-1-后台对前台业务的支撑作用" class="headerlink" title="4.2.1 后台对前台业务的支撑作用"></a>4.2.1 后台对前台业务的支撑作用</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722161730158.png" alt="image-20250722161730158"></p><p>我理解的电商后台，其最核心、最根本的作用，就是<strong>为前台的所有业务，提供支撑</strong>。</p><ul><li>前台用户能看到的每一个<strong>商品</strong>，都是由后台的“<strong>商品中心</strong>”来管理的。</li><li>用户下的每一笔<strong>订单</strong>，都是由后台的“<strong>订单中心</strong>”和“<strong>支付中心</strong>”来处理的。</li><li>订单能被顺利<strong>配送</strong>，则依赖于后台的“<strong>物流中心</strong>”和“<strong>WMS（仓储管理系统）</strong>”。</li></ul><p>前台是光鲜亮丽的“餐厅”，而后台，就是保证餐厅能正常运转的“厨房”、“仓库”和“收银系统”的总和。</p><h3 id="4-2-2-商家业务-vs-平台运营业务划分"><a href="#4-2-2-商家业务-vs-平台运营业务划分" class="headerlink" title="4.2.2 商家业务 vs 平台运营业务划分"></a>4.2.2 商家业务 vs 平台运营业务划分</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722161835579.png" alt="image-20250722161835579"></p><p>对于我们“大P超级电商”这样的<strong>招商模式</strong>平台，我需要特别澄清一点：我们的“后台”，其实并不是一个单一的系统，而是<strong>两个既相互独立、又紧密关联的系统</strong>：</p><p><strong>1. 商家后台</strong><br>这是我设计给<strong>入驻商家</strong>使用的后台。它是商家在我们平台上“开店经营”的专属工作台。其核心的业务方向，是服务于商家的日常经营，包括：</p><ul><li><code>入驻平台/管理店铺</code></li><li><code>发布/管理商品</code></li><li><code>订单跟进</code></li><li><code>售后处理</code></li></ul><p><strong>2. 平台运营后台</strong><br>这是我设计给我们<strong>自己公司内部员工</strong>（运营、审核、客服等）使用的后台。它是我们管理整个平台的“上帝视角”控制台。其核心的业务方向，是<strong>管理和监督</strong>，包括：</p><ul><li><code>审核商家入驻</code></li><li><code>审核商家发布的商品</code></li><li><code>订单跟进（仲裁）</code></li><li><code>管理平台所有的商家和用户</code></li></ul><p>清晰地划分这两套后台的业务边界，是我进行后台架构设计的第一步。</p><h3 id="4-2-3-整体业务模块架构"><a href="#4-2-3-整体业务模块架构" class="headerlink" title="4.2.3 整体业务模块架构"></a>4.2.3 整体业务模块架构</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722162014848.png" alt="image-20250722162014848"></p><p>最后，我会将我们整个电商生态的所有功能模块，汇总成一张“<strong>整体业务模块架构图</strong>”。这张图，就是我们整个项目的“总蓝图”。</p><p>如案例图所示，这张架构图，清晰地划分出了我们项目的三大组成部分：</p><ul><li><strong>用户端</strong>：即我们在第三章设计的，面向C端消费者的前台App。</li><li><strong>商家端</strong>：即我们即将设计的，面向B端商家的后台管理系统。</li><li><strong>平台端</strong>：即我们即将设计的，面向我们自己内部员工的后台管理系统。</li></ul><p>在每一个“端”的下面，我会罗列出它所包含的核心功能模块。比如，<code>平台端</code>就包含了<code>商品管理</code>、<code>订单管理</code>、<code>系统管理</code>、<code>店铺管理</code>等模块。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722162110994.png" alt="image-20250722162110994"></p><p>这张架构图，不仅能让所有项目成员，对我们产品的全貌一目了然，更能清晰地揭示出，前台的每一个核心业务，是由后台的哪一个模块来提供支撑的。它是我们后续进行详细设计和技术架构设计的“最高纲领”。</p><hr><h2 id="4-3-平台运营后台核心设计"><a href="#4-3-平台运营后台核心设计" class="headerlink" title="4.3 平台运营后台核心设计"></a>4.3 平台运营后台核心设计</h2><p>现在，我们开始设计我们自己公司内部员工使用的“<strong>上帝视角</strong>”后台——<strong>平台运营后台</strong>。</p><p>这个系统的设计，我最看重的三个原则是：<strong>安全、高效、可追溯</strong>。因为在这里的每一个操作，都可能会对我们平台上的用户、商家，乃至整个平台的声誉，产生直接的影响。</p><p>本节，我们重点设计其中两个最高频、也最重要的模块：<strong>C端用户管理</strong>和<strong>商家管理</strong>。</p><h3 id="4-3-1-C端用户管理（用户状态与权限操作）"><a href="#4-3-1-C端用户管理（用户状态与权限操作）" class="headerlink" title="4.3.1 C端用户管理（用户状态与权限操作）"></a>4.3.1 C端用户管理（用户状态与权限操作）</h3><p>这个模块，赋予了我们的运营和客服同事，管理平台上所有C端消费者（买家）的权力。</p><h4 id="1-用户列表与查询"><a href="#1-用户列表与查询" class="headerlink" title="1. 用户列表与查询"></a>1. 用户列表与查询</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722170937934.png" alt="image-20250722170937934"></p><p>正如案例图所示，这个界面的核心，是一个完整的用户列表。为了让我的运营同事，能从数以万计的用户中，精准地找到目标，我必须为他们设计一套强大的查询和筛选功能。</p><table><thead><tr><th align="left">筛选字段</th><th align="left">我的设计说明</th></tr></thead><tbody><tr><td align="left"><strong>用户信息</strong></td><td align="left">提供一个统一的输入框，支持按<code>用户昵称</code>、<code>手机号</code>、甚至<code>用户ID</code>进行模糊或精确搜索。</td></tr><tr><td align="left"><strong>注册时间</strong></td><td align="left">提供一个日期范围选择器，方便运营分析特定时期内（如：某次活动期间）的新增用户。</td></tr><tr><td align="left"><strong>用户状态</strong></td><td align="left"><strong>这是最重要的筛选条件</strong>。提供一个下拉菜单，让客服或风控团队能快速筛选出所有<code>正常</code>、<code>已禁用</code>的用户，进行集中的管理。</td></tr></tbody></table><h4 id="2-用户状态与权限操作"><a href="#2-用户状态与权限操作" class="headerlink" title="2. 用户状态与权限操作"></a>2. 用户状态与权限操作</h4><p>“<strong>操作</strong>”列，是这个模块“权力”的体现。一个严谨的设计，是<strong>根据用户当前的状态，来提供不同的操作权限</strong>。</p><ul><li><p><strong>当用户状态为“正常”时</strong>：<br>我提供的核心操作是 <code>查看</code>、<code>编辑</code>、<code>禁用</code>。</p><ul><li><strong>我的拓展设计</strong>：点击“<strong>禁用</strong>”时，我不会让它立刻生效。我会设计一个弹窗，要求操作员必须<strong>选择一个禁用的时长</strong>（如：7天、30天、永久）并<strong>填写禁用的原因</strong>。这个原因，一部分会展示给用户，另一部分则会作为内部日志，记录在案，以备查验。</li></ul></li><li><p><strong>当用户状态为“禁用”时</strong>：<br>“禁用”按钮，则会变为“<strong>解禁</strong>”按钮。操作员点击后，二次确认，即可将该用户恢复为正常状态。</p></li></ul><p>这种“<strong>基于状态的权限设计</strong>”，能极大地避免运营人员的误操作，让后台管理更规范、更安全。</p><h3 id="4-3-2-商家入驻审核与店铺管理"><a href="#4-3-2-商家入驻审核与店铺管理" class="headerlink" title="4.3.2 商家入驻审核与店铺管理"></a>4.3.2 商家入驻审核与店铺管理</h3><p>管理商家（卖家），比管理用户要复杂得多，因为它直接关系到我们平台的商品供给、服务质量和核心收入。</p><h4 id="1-商家入驻审核"><a href="#1-商家入驻审核" class="headerlink" title="1. 商家入驻审核"></a>1. 商家入驻审核</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171315184.png" alt="image-20250722171315184"></p><p>这是我们平台的“<strong>第一道门卫</strong>”。所有想在我们平台开店的商家，都必须经过这一环节的资质审核。我会把它设计成一个独立的工作台。</p><ul><li><strong>待审核列表</strong>：页面的默认视图，是一个“<strong>待审核</strong>”队列，展示了所有提交了入驻申请，但尚未被处理的商家。</li><li><strong>审核详情页</strong>：运营点击“审核”后，会进入一个详情页，能看到该商家提交的所有信息，包括<code>营业执照</code>、<code>法人信息</code>、<code>品牌授权书</code>等。在这个页面，只有两个核心的操作按钮：“<strong>通过</strong>”和“<strong>驳回</strong>”。</li><li><strong>我的拓展设计</strong>：点击“<strong>驳回</strong>”时，我同样会要求运营，必须从一个预设的“驳回原因列表”（如：资质不全、信息不符）中选择一项，或手动输入驳回原因。这个原因，将通过短信或站内信，清晰地告知申请者，提升平台的专业度。</li></ul><h4 id="2-店铺管理"><a href="#2-店铺管理" class="headerlink" title="2. 店铺管理"></a>2. 店铺管理</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171327591.png" alt="image-20250722171327591"></p><p>当商家审核通过，拥有了自己的店铺后，他们就会进入到“<strong>店铺管理</strong>”列表中。</p><ul><li><strong>核心信息</strong>：这个列表，除了展示商家的基本信息外，更需要展示<code>店铺名称</code>、<code>主营类目</code>、<code>入驻时间</code>、<code>店铺状态</code>等运营信息。</li><li><strong>核心操作</strong>：针对“店铺”这个主体，我设计的操作权限，会比针对“用户”的权限，更谨慎、层级更高。<ul><li><code>查看店铺</code>：可以一键跳转到该店铺的前台页面。</li><li><code>冻结店铺</code>：当商家出现较严重的违规行为（如售假）时，运营可以暂时“<strong>冻结</strong>”其店铺。冻结期间，店铺所有商品都会被下架，商家也无法登录后台。</li><li><code>关闭店铺</code>：这是最严厉的处罚。意味着与该商家终止合作，将其清退出平台。这个操作，我通常会设计为，需要“<strong>运营主管</strong>”及以上级别的角色，才能执行。</li></ul></li></ul><hr><h2 id="4-4-商家后台核心设计"><a href="#4-4-商家后台核心设计" class="headerlink" title="4.4 商家后台核心设计"></a>4.4 商家后台核心设计</h2><p>在上一节，我们设计了我们自己内部团队使用的“平台运营后台”。现在，我们要来设计一个同样重要，但使用者完全不同的系统——<strong>商家后台</strong>。</p><p>这是我们为所有入驻我们“大P超级电商”平台的第三方商家，提供的专属“<strong>线上店铺办公室</strong>”。这个后台的体验好坏，直接决定了我们能否吸引和留住优质的商家，是保障我们平台商品丰富度和供给侧质量的生命线。</p><h3 id="4-4-1-商家入驻流程与类型（京东、淘宝-天猫、自营-非自营）"><a href="#4-4-1-商家入驻流程与类型（京东、淘宝-天猫、自营-非自营）" class="headerlink" title="4.4.1 商家入驻流程与类型（京东、淘宝/天猫、自营/非自营）"></a>4.4.1 商家入驻流程与类型（京东、淘宝/天猫、自营/非自营）</h3><p>在我设计商家后台的第一个功能“<strong>商家入驻</strong>”时，我首先要思考一个战略问题：<strong>我们希望吸引什么样的商家？</strong></p><ul><li>像<strong>淘宝</strong>一样，追求“让天下没有难做的生意”，允许个人和企业“<strong>免费开店</strong>”，降低门槛，最大化地丰富商品供给？</li><li>还是像<strong>天猫</strong>一样，只面向具备一定<strong>品质和资质的企业</strong>，抬高门槛，保障平台的品牌调性？</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171019089.png" alt="image-20250722171019089"></p><p>这个决策，决定了我们入驻流程的复杂度和审核的严格度。对于我们 V1.0 的招商模式，我们将主要设计一个<strong>非自营（POP）商家的入驻流程</strong>。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171035591.png" alt="image-20250722171035591"></p><p>一个专业的商家入驻流程，远比用户注册要复杂。我设计的流程，通常包含以下几个核心阶段：</p><ol><li><strong>入驻前准备</strong>：在申请入口，我会清晰地告知商家，需要提前准备好哪些资质文件。</li><li><strong>在线申请</strong>：引导商家填写一系列的申请信息。</li><li><strong>平台审核</strong>：商家提交后，申请单会进入我们上一节设计的“平台运营后台-商家审核”模块，由我们的运营同事进行审核。</li><li><strong>开店任务</strong>：审核通过后，我会引导商家完成一系列的开店准备工作，如<strong>缴纳费用、签署在线合同</strong>等。</li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171136628.png" alt="image-20250722171136628"></p><p>在“在线申请”这个环节，我设计的表单，会清晰地区分“<strong>个人类型</strong>”和“<strong>企业类型</strong>”的商家，因为他们需要提交的<code>个人信息</code>和<code>公司信息</code>（如营业执照）是完全不同的。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171206667.png" alt="image-20250722171206667"></p><p>在“开店任务”环节，我设计的流程，必须清晰地向商家展示需要缴纳的费用。这通常包括：</p><ul><li><strong>平台使用费/年费</strong>：按年收取的技术服务费。</li><li><strong>保证金</strong>：一笔押金，用于在商家出现违规行为、损害消费者利益时，对消费者进行赔付。</li></ul><h3 id="4-4-2-商家子账号设置与权限划分"><a href="#4-4-2-商家子账号设置与权限划分" class="headerlink" title="4.4.2 商家子账号设置与权限划分"></a>4.4.2 商家子账号设置与权限划分</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171544019.png" alt="image-20250722171544019"></p><p>当一个商家的店铺成功开张，生意越做越大，一个新需求就出现了：<strong>店主（主账号）一个人忙不过来了</strong>，他需要让手下的员工（如：客服、运营、库管）来一起管理店铺。但是，他又不能把最高权限的“主账号”密码，告诉所有人。</p><p>为了解决这个痛点，我必须为商家后台，设计一个“<strong>子账号</strong>”管理功能。这本质上，是为每一个商家，提供了一套<strong>迷你的、店铺内部的RBAC（基于角色的访问控制）系统</strong>。</p><p>我的设计，主要包含两个核心功能：</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171852869.png" alt="image-20250722171852869"></p><ol><li><strong>角色管理</strong>：我会允许“<strong>主账号</strong>”，在后台<strong>创建不同的角色</strong>（如：“客服专员”、“运营专员”），并为这些角色，<strong>勾选分配不同的操作权限</strong>（如：“客服专员”只能查看订单和回复咨询，但无权修改商品价格）。</li><li><strong>员工管理</strong>：主账号可以在这里，为他的每一位员工，<strong>创建一个子账号</strong>，并为这个子账号，<strong>指定一个我们上面创建好的角色</strong>。</li></ol><p>这个功能，是区分一个“玩具级”和一个“企业级”商家后台的重要标志。</p><h2 id="4-5-本章总结"><a href="#4-5-本章总结" class="headerlink" title="4.5 本章总结"></a>4.5 本章总结</h2><p>在本章，我们深入到了电商“冰山”的水下部分，系统地学习了后台的设计。</p><ul><li><strong>核心作用与架构</strong>：我们明确了后台是为前台服务的“指挥中心”，并学会了如何划分<strong>商家业务</strong>和<strong>平台业务</strong>，搭建起<strong>用户端、商家端、平台端</strong>三位一体的宏观产品架构。</li><li><strong>平台运营后台</strong>：我们设计了平台自己使用的核心模块，包括如何进行<strong>C端用户管理</strong>（特别是状态控制），以及如何建立一套严谨的<strong>商家入驻审核与店铺管理</strong>流程。</li><li><strong>商家后台</strong>：我们设计了服务于商家的核心模块，包括如何根据不同平台定位，设计差异化的<strong>商家入驻流程</strong>，以及如何通过<strong>子账号</strong>功能，来满足商家的团队协作与权限管理需求。</li></ul><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/26490.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/26490.html&quot;)">产品经理进阶（四）：第四章：电商后台产品设计</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/26490.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理进阶（四）：第四章：电商后台产品设计&amp;url=https://prorise666.site/posts/26490.html&amp;pic=https://bu.dusays.com/2025/07/25/6882f31a48223.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/17683.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理进阶（三）：第三章：电商用户端产品设计</div></div></a></div><div class="next-post pull-right"><a href="/posts/4512.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理进阶（五）：第五章：电商后台 - 商品管理</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理进阶（四）：第四章：电商后台产品设计",date:"2025-07-24 19:13:45",updated:"2025-07-25 11:05:48",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第四章：电商后台产品设计\n\n欢迎来到第四章。在上一章，我们已经为用户，设计出了一套完整、华丽的“**前台**”购物体验。但支撑这一切前台体验能够顺利运转的，是一个我们普通用户永远看不到的、强大而复杂的“**后台**”系统。\n\n后台，就是我们电商平台的“**中央厨房**”与“**指挥中心**”。本章，我们就将学习如何设计这个核心系统。\n\n## 4.1 学习目标\n\n在本节中，我的核心目标是，带大家建立起对电商后台的**宏观架构认知**。我们将学习后台的核心作用，并重点理解，在招商模式下，如何清晰地划分“**商家业务**”与“**平台业务**”，并最终绘制出我们整个电商生态的“**业务模块架构图**”。\n\n## 4.2 电商后台核心作用及架构\n\n### 4.2.1 后台对前台业务的支撑作用\n\n![image-20250722161730158](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722161730158.png)\n\n我理解的电商后台，其最核心、最根本的作用，就是**为前台的所有业务，提供支撑**。\n* 前台用户能看到的每一个**商品**，都是由后台的“**商品中心**”来管理的。\n* 用户下的每一笔**订单**，都是由后台的“**订单中心**”和“**支付中心**”来处理的。\n* 订单能被顺利**配送**，则依赖于后台的“**物流中心**”和“**WMS（仓储管理系统）**”。\n\n前台是光鲜亮丽的“餐厅”，而后台，就是保证餐厅能正常运转的“厨房”、“仓库”和“收银系统”的总和。\n\n### 4.2.2 商家业务 vs 平台运营业务划分\n\n![image-20250722161835579](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722161835579.png)\n\n对于我们“大P超级电商”这样的**招商模式**平台，我需要特别澄清一点：我们的“后台”，其实并不是一个单一的系统，而是**两个既相互独立、又紧密关联的系统**：\n\n**1. 商家后台**\n这是我设计给**入驻商家**使用的后台。它是商家在我们平台上“开店经营”的专属工作台。其核心的业务方向，是服务于商家的日常经营，包括：\n\n* `入驻平台/管理店铺`\n* `发布/管理商品`\n* `订单跟进`\n* `售后处理`\n\n**2. 平台运营后台**\n这是我设计给我们**自己公司内部员工**（运营、审核、客服等）使用的后台。它是我们管理整个平台的“上帝视角”控制台。其核心的业务方向，是**管理和监督**，包括：\n\n* `审核商家入驻`\n* `审核商家发布的商品`\n* `订单跟进（仲裁）`\n* `管理平台所有的商家和用户`\n\n清晰地划分这两套后台的业务边界，是我进行后台架构设计的第一步。\n\n### 4.2.3 整体业务模块架构\n\n![image-20250722162014848](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722162014848.png)\n\n最后，我会将我们整个电商生态的所有功能模块，汇总成一张“**整体业务模块架构图**”。这张图，就是我们整个项目的“总蓝图”。\n\n如案例图所示，这张架构图，清晰地划分出了我们项目的三大组成部分：\n\n* **用户端**：即我们在第三章设计的，面向C端消费者的前台App。\n* **商家端**：即我们即将设计的，面向B端商家的后台管理系统。\n* **平台端**：即我们即将设计的，面向我们自己内部员工的后台管理系统。\n\n在每一个“端”的下面，我会罗列出它所包含的核心功能模块。比如，`平台端`就包含了`商品管理`、`订单管理`、`系统管理`、`店铺管理`等模块。\n\n![image-20250722162110994](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722162110994.png)\n\n这张架构图，不仅能让所有项目成员，对我们产品的全貌一目了然，更能清晰地揭示出，前台的每一个核心业务，是由后台的哪一个模块来提供支撑的。它是我们后续进行详细设计和技术架构设计的“最高纲领”。\n\n\n\n\n---\n## 4.3 平台运营后台核心设计\n\n现在，我们开始设计我们自己公司内部员工使用的“**上帝视角**”后台——**平台运营后台**。\n\n这个系统的设计，我最看重的三个原则是：**安全、高效、可追溯**。因为在这里的每一个操作，都可能会对我们平台上的用户、商家，乃至整个平台的声誉，产生直接的影响。\n\n本节，我们重点设计其中两个最高频、也最重要的模块：**C端用户管理**和**商家管理**。\n\n### 4.3.1 C端用户管理（用户状态与权限操作）\n\n这个模块，赋予了我们的运营和客服同事，管理平台上所有C端消费者（买家）的权力。\n\n#### 1. 用户列表与查询\n\n![image-20250722170937934](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722170937934.png)\n\n正如案例图所示，这个界面的核心，是一个完整的用户列表。为了让我的运营同事，能从数以万计的用户中，精准地找到目标，我必须为他们设计一套强大的查询和筛选功能。\n\n| 筛选字段 | 我的设计说明 |\n| :--- | :--- |\n| **用户信息** | 提供一个统一的输入框，支持按`用户昵称`、`手机号`、甚至`用户ID`进行模糊或精确搜索。 |\n| **注册时间** | 提供一个日期范围选择器，方便运营分析特定时期内（如：某次活动期间）的新增用户。 |\n| **用户状态**| **这是最重要的筛选条件**。提供一个下拉菜单，让客服或风控团队能快速筛选出所有`正常`、`已禁用`的用户，进行集中的管理。 |\n\n#### 2. 用户状态与权限操作\n\n“**操作**”列，是这个模块“权力”的体现。一个严谨的设计，是**根据用户当前的状态，来提供不同的操作权限**。\n\n* **当用户状态为“正常”时**：\n    我提供的核心操作是 `查看`、`编辑`、`禁用`。\n    * **我的拓展设计**：点击“**禁用**”时，我不会让它立刻生效。我会设计一个弹窗，要求操作员必须**选择一个禁用的时长**（如：7天、30天、永久）并**填写禁用的原因**。这个原因，一部分会展示给用户，另一部分则会作为内部日志，记录在案，以备查验。\n\n* **当用户状态为“禁用”时**：\n    “禁用”按钮，则会变为“**解禁**”按钮。操作员点击后，二次确认，即可将该用户恢复为正常状态。\n\n这种“**基于状态的权限设计**”，能极大地避免运营人员的误操作，让后台管理更规范、更安全。\n\n### 4.3.2 商家入驻审核与店铺管理\n\n管理商家（卖家），比管理用户要复杂得多，因为它直接关系到我们平台的商品供给、服务质量和核心收入。\n\n#### 1. 商家入驻审核\n\n![image-20250722171315184](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171315184.png)\n\n这是我们平台的“**第一道门卫**”。所有想在我们平台开店的商家，都必须经过这一环节的资质审核。我会把它设计成一个独立的工作台。\n* **待审核列表**：页面的默认视图，是一个“**待审核**”队列，展示了所有提交了入驻申请，但尚未被处理的商家。\n* **审核详情页**：运营点击“审核”后，会进入一个详情页，能看到该商家提交的所有信息，包括`营业执照`、`法人信息`、`品牌授权书`等。在这个页面，只有两个核心的操作按钮：“**通过**”和“**驳回**”。\n* **我的拓展设计**：点击“**驳回**”时，我同样会要求运营，必须从一个预设的“驳回原因列表”（如：资质不全、信息不符）中选择一项，或手动输入驳回原因。这个原因，将通过短信或站内信，清晰地告知申请者，提升平台的专业度。\n\n#### 2. 店铺管理\n\n![image-20250722171327591](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171327591.png)\n\n当商家审核通过，拥有了自己的店铺后，他们就会进入到“**店铺管理**”列表中。\n* **核心信息**：这个列表，除了展示商家的基本信息外，更需要展示`店铺名称`、`主营类目`、`入驻时间`、`店铺状态`等运营信息。\n* **核心操作**：针对“店铺”这个主体，我设计的操作权限，会比针对“用户”的权限，更谨慎、层级更高。\n    * `查看店铺`：可以一键跳转到该店铺的前台页面。\n    * `冻结店铺`：当商家出现较严重的违规行为（如售假）时，运营可以暂时“**冻结**”其店铺。冻结期间，店铺所有商品都会被下架，商家也无法登录后台。\n    * `关闭店铺`：这是最严厉的处罚。意味着与该商家终止合作，将其清退出平台。这个操作，我通常会设计为，需要“**运营主管**”及以上级别的角色，才能执行。\n\n\n\n---\n## 4.4 商家后台核心设计\n\n在上一节，我们设计了我们自己内部团队使用的“平台运营后台”。现在，我们要来设计一个同样重要，但使用者完全不同的系统——**商家后台**。\n\n这是我们为所有入驻我们“大P超级电商”平台的第三方商家，提供的专属“**线上店铺办公室**”。这个后台的体验好坏，直接决定了我们能否吸引和留住优质的商家，是保障我们平台商品丰富度和供给侧质量的生命线。\n\n### 4.4.1 商家入驻流程与类型（京东、淘宝/天猫、自营/非自营）\n\n在我设计商家后台的第一个功能“**商家入驻**”时，我首先要思考一个战略问题：**我们希望吸引什么样的商家？**\n* 像**淘宝**一样，追求“让天下没有难做的生意”，允许个人和企业“**免费开店**”，降低门槛，最大化地丰富商品供给？\n* 还是像**天猫**一样，只面向具备一定**品质和资质的企业**，抬高门槛，保障平台的品牌调性？\n\n![image-20250722171019089](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171019089.png)\n\n这个决策，决定了我们入驻流程的复杂度和审核的严格度。对于我们 V1.0 的招商模式，我们将主要设计一个**非自营（POP）商家的入驻流程**。\n\n![image-20250722171035591](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171035591.png)\n\n一个专业的商家入驻流程，远比用户注册要复杂。我设计的流程，通常包含以下几个核心阶段：\n1.  **入驻前准备**：在申请入口，我会清晰地告知商家，需要提前准备好哪些资质文件。\n2.  **在线申请**：引导商家填写一系列的申请信息。\n3.  **平台审核**：商家提交后，申请单会进入我们上一节设计的“平台运营后台-商家审核”模块，由我们的运营同事进行审核。\n4.  **开店任务**：审核通过后，我会引导商家完成一系列的开店准备工作，如**缴纳费用、签署在线合同**等。\n\n![image-20250722171136628](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171136628.png)\n\n在“在线申请”这个环节，我设计的表单，会清晰地区分“**个人类型**”和“**企业类型**”的商家，因为他们需要提交的`个人信息`和`公司信息`（如营业执照）是完全不同的。\n\n![image-20250722171206667](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171206667.png)\n\n在“开店任务”环节，我设计的流程，必须清晰地向商家展示需要缴纳的费用。这通常包括：\n* **平台使用费/年费**：按年收取的技术服务费。\n* **保证金**：一笔押金，用于在商家出现违规行为、损害消费者利益时，对消费者进行赔付。\n\n### 4.4.2 商家子账号设置与权限划分\n\n![image-20250722171544019](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171544019.png)\n\n当一个商家的店铺成功开张，生意越做越大，一个新需求就出现了：**店主（主账号）一个人忙不过来了**，他需要让手下的员工（如：客服、运营、库管）来一起管理店铺。但是，他又不能把最高权限的“主账号”密码，告诉所有人。\n\n为了解决这个痛点，我必须为商家后台，设计一个“**子账号**”管理功能。这本质上，是为每一个商家，提供了一套**迷你的、店铺内部的RBAC（基于角色的访问控制）系统**。\n\n我的设计，主要包含两个核心功能：\n\n![image-20250722171852869](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171852869.png)\n\n1.  **角色管理**：我会允许“**主账号**”，在后台**创建不同的角色**（如：“客服专员”、“运营专员”），并为这些角色，**勾选分配不同的操作权限**（如：“客服专员”只能查看订单和回复咨询，但无权修改商品价格）。\n2.  **员工管理**：主账号可以在这里，为他的每一位员工，**创建一个子账号**，并为这个子账号，**指定一个我们上面创建好的角色**。\n\n这个功能，是区分一个“玩具级”和一个“企业级”商家后台的重要标志。\n\n## 4.5 本章总结\n\n在本章，我们深入到了电商“冰山”的水下部分，系统地学习了后台的设计。\n* **核心作用与架构**：我们明确了后台是为前台服务的“指挥中心”，并学会了如何划分**商家业务**和**平台业务**，搭建起**用户端、商家端、平台端**三位一体的宏观产品架构。\n* **平台运营后台**：我们设计了平台自己使用的核心模块，包括如何进行**C端用户管理**（特别是状态控制），以及如何建立一套严谨的**商家入驻审核与店铺管理**流程。\n* **商家后台**：我们设计了服务于商家的核心模块，包括如何根据不同平台定位，设计差异化的**商家入驻流程**，以及如何通过**子账号**功能，来满足商家的团队协作与权限管理需求。\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%9B%9B%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.</span> <span class="toc-text">第四章：电商后台产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.</span> <span class="toc-text">4.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-2-%E7%94%B5%E5%95%86%E5%90%8E%E5%8F%B0%E6%A0%B8%E5%BF%83%E4%BD%9C%E7%94%A8%E5%8F%8A%E6%9E%B6%E6%9E%84"><span class="toc-number">1.2.</span> <span class="toc-text">4.2 电商后台核心作用及架构</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-1-%E5%90%8E%E5%8F%B0%E5%AF%B9%E5%89%8D%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%9A%84%E6%94%AF%E6%92%91%E4%BD%9C%E7%94%A8"><span class="toc-number">1.2.1.</span> <span class="toc-text">4.2.1 后台对前台业务的支撑作用</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-2-%E5%95%86%E5%AE%B6%E4%B8%9A%E5%8A%A1-vs-%E5%B9%B3%E5%8F%B0%E8%BF%90%E8%90%A5%E4%B8%9A%E5%8A%A1%E5%88%92%E5%88%86"><span class="toc-number">1.2.2.</span> <span class="toc-text">4.2.2 商家业务 vs 平台运营业务划分</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-3-%E6%95%B4%E4%BD%93%E4%B8%9A%E5%8A%A1%E6%A8%A1%E5%9D%97%E6%9E%B6%E6%9E%84"><span class="toc-number">1.2.3.</span> <span class="toc-text">4.2.3 整体业务模块架构</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-3-%E5%B9%B3%E5%8F%B0%E8%BF%90%E8%90%A5%E5%90%8E%E5%8F%B0%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.</span> <span class="toc-text">4.3 平台运营后台核心设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-1-C%E7%AB%AF%E7%94%A8%E6%88%B7%E7%AE%A1%E7%90%86%EF%BC%88%E7%94%A8%E6%88%B7%E7%8A%B6%E6%80%81%E4%B8%8E%E6%9D%83%E9%99%90%E6%93%8D%E4%BD%9C%EF%BC%89"><span class="toc-number">1.3.1.</span> <span class="toc-text">4.3.1 C端用户管理（用户状态与权限操作）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%94%A8%E6%88%B7%E5%88%97%E8%A1%A8%E4%B8%8E%E6%9F%A5%E8%AF%A2"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">1. 用户列表与查询</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%94%A8%E6%88%B7%E7%8A%B6%E6%80%81%E4%B8%8E%E6%9D%83%E9%99%90%E6%93%8D%E4%BD%9C"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">2. 用户状态与权限操作</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-2-%E5%95%86%E5%AE%B6%E5%85%A5%E9%A9%BB%E5%AE%A1%E6%A0%B8%E4%B8%8E%E5%BA%97%E9%93%BA%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.2.</span> <span class="toc-text">4.3.2 商家入驻审核与店铺管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%95%86%E5%AE%B6%E5%85%A5%E9%A9%BB%E5%AE%A1%E6%A0%B8"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">1. 商家入驻审核</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%BA%97%E9%93%BA%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.2.2.</span> <span class="toc-text">2. 店铺管理</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-4-%E5%95%86%E5%AE%B6%E5%90%8E%E5%8F%B0%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.4.</span> <span class="toc-text">4.4 商家后台核心设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-1-%E5%95%86%E5%AE%B6%E5%85%A5%E9%A9%BB%E6%B5%81%E7%A8%8B%E4%B8%8E%E7%B1%BB%E5%9E%8B%EF%BC%88%E4%BA%AC%E4%B8%9C%E3%80%81%E6%B7%98%E5%AE%9D-%E5%A4%A9%E7%8C%AB%E3%80%81%E8%87%AA%E8%90%A5-%E9%9D%9E%E8%87%AA%E8%90%A5%EF%BC%89"><span class="toc-number">1.4.1.</span> <span class="toc-text">4.4.1 商家入驻流程与类型（京东、淘宝/天猫、自营/非自营）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-2-%E5%95%86%E5%AE%B6%E5%AD%90%E8%B4%A6%E5%8F%B7%E8%AE%BE%E7%BD%AE%E4%B8%8E%E6%9D%83%E9%99%90%E5%88%92%E5%88%86"><span class="toc-number">1.4.2.</span> <span class="toc-text">4.4.2 商家子账号设置与权限划分</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-5-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.5.</span> <span class="toc-text">4.5 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>