case item
    when 'about'
        include ./asideInfoCard.pug
    when 'newestPost'
        include ./asideNewestPost.pug
    when 'allInfo'
        include ./asideAllInfo.pug
    when 'ads'
        include ./asideAdsense.pug
    when 'newest_comment'
        include ./asideNewstComments.pug
    default
        - const custom = site.data?.aside?.find((i) => i.name === item)
        if custom
            include ./asideCustom.pug
