- page.type = is_home() ? 'solitude' : page.type
doctype html
html(lang=config.language, data-theme="light")
    head
        include ./head.pug
    body#body(data-type=page.type)
        // universe
        if theme.display_mode.universe
            canvas#universe

        // background img
        if theme.background.enable
            #global_bg

        // loading
        if theme.loading.fullpage
            include ./loading.pug

        // console
        if theme.console.enable
            include ./console.pug

        // sidebar
        include ./sidebar.pug

        // keyboard
        if theme.keyboard.enable
            include ./keyboard.pug
        
        // righhtside
        if theme.rightside.enable
            include rightside

        #body-wrap(class = is_post() ? 'post' : 'page')
            include ./header.pug

            if(page.type !== '404')
                block content

                footer#footer
                    include ./footer.pug
            else
                .error#body-wrap
                    block content

        // right_menu
        if theme.right_menu.enable
            include rightmenu

        // inject body
        include ./inject/body.pug

        // search
        include ./widgets/third-party/search/index.pug

        // music
        if theme.capsule.enable
            include ./widgets/third-party/music.pug
