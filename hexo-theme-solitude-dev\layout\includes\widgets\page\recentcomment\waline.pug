script(pjax).
    (async () => {
        document.querySelector('!{sel}').textContent = `#{__("loading")}`
        const emojiReg = /<img [^>]+ class="wl-emoji">/g
        let cache = utils.saveToLocal.get('!{str_name}')
        if (cache) {
            setHtml(document.querySelector('!{sel}'), cache)
            return
        }
        let ls = []
        await fetch('!{theme.waline.envId}/api/comment?type=recent&count=!{limit}', {method: 'GET'}).then(async res => res.json())
            .then(async data => {
                for (const i of data.data) {
                    let title = ''
                    if (i.url) {
                        await fetch(i.url).then(res => res.text()).then(html => {
                            const parser = new DOMParser()
                            const doc = parser.parseFromString(html, 'text/html')
                            title = doc.querySelector('title').innerText
                        }).catch(() => {
                            title = i.url
                        })
                    }
                    if (title.indexOf('|') > 0) {
                        title = title.split('|')[0]
                    }
                    ls.push({
                        title: title,
                        url: i.url,
                        nick: i.nick,
                        avatar: i.avatar,
                        time: new Date(i.time),
                        content: formatContent(i.comment)
                    })
                }
                setHtml(document.querySelector('!{sel}'), ls)
                utils.saveToLocal.set('!{str_name}', ls, !{cache})
            })

        function setHtml(el, data) {
            el.innerHTML = data.length !== 0 ? data.map(i => `
                                <div class="comment-card" title="${i.title}" onclick="pjax.loadUrl('${i.url}')">
                                    <div class="comment-info">
                                        <img src="${i.avatar}" class="nolazyload" alt="${i.nick}">
                                        <div>
                                            <span class="comment-user">${i.nick}</span>
                                        </div>
                                        <time class="comment-time" datetime="${i.time}"></time>
                                    </div>
                                    <div class="comment-content">${i.content}</div>
                                    <div class="comment-title">
                                    <i class="solitude fas fa-comment"></i>
                                    ${i.title}</div>
                                </div>
                            `).join('') : `#{__("console.newest_comment.empty")}`
            if (typeof utils !== 'undefined') utils.diffDateFormat(document.querySelectorAll('.comment-time'))
            else {
                document.addEventListener('pjax:complete', () => utils.diffDateFormat(document.querySelectorAll('.comment-time')))
                document.addEventListener('DOMContentLoaded', () => utils.diffDateFormat(document.querySelectorAll('.comment-time')))
            }
        }

        function formatContent(content) {
            content = content.replace(emojiReg, '[!{__("console.newest_comment.emoji")}]')
            content = content.replace(/<img.*?>/g, '[!{__("console.newest_comment.image")}]');
            content = content.replace(/<a.*?>.*?<\/a>/g, '[!{__("console.newest_comment.link")}]');
            content = content.replace(/<pre.*?>.*?<\/pre>/g, '[!{__("console.newest_comment.code")}]');
            content = content.replace(/<.*?>/g, '');
            return content
        }
    })()