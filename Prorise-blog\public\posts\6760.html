<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Java（四）：4.0 [核心] Java I/O 流体系与实战 | Prorise的小站</title><meta name="keywords" content="Java基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Java（四）：4.0 [核心] Java I/O 流体系与实战"><meta name="application-name" content="Java（四）：4.0 [核心] Java I/O 流体系与实战"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="Java（四）：4.0 [核心] Java I/O 流体系与实战"><meta property="og:url" content="https://prorise666.site/posts/6760.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="4.0 [核心] Java I&amp;#x2F;O 流体系与实战本章将深入Java的I&amp;#x2F;O（输入&amp;#x2F;输出）世界。I&amp;#x2F;O是程序与外部世界（如文件、网络、控制台）沟通的桥梁。我们将从I&amp;#x2F;O的“四大家族”和装饰器设计模式入手，理解其核心设计思想，然后深入文件操作的现代实践（"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"><meta name="description" content="4.0 [核心] Java I&amp;#x2F;O 流体系与实战本章将深入Java的I&amp;#x2F;O（输入&amp;#x2F;输出）世界。I&amp;#x2F;O是程序与外部世界（如文件、网络、控制台）沟通的桥梁。我们将从I&amp;#x2F;O的“四大家族”和装饰器设计模式入手，理解其核心设计思想，然后深入文件操作的现代实践（"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/6760.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"Java（四）：4.0 [核心] Java I/O 流体系与实战",postAI:"true",pageFillDescription:"4.0 [核心] Java Ix2FO 流体系与实战, 4.1 Ix2FO 核心概念与设计模式, 4.1.1 面试题引入, 4.1.2 流的四大家族与核心区别, 字节流 vs. 字符流, 代码示例：乱码问题的产生与解决, 场景二：使用处理流包装节点流（高级、推荐的方式）, 两种方式对比总结, 4.1.4 [设计模式] java.io 的灵魂：装饰器模式, 代码示例：装饰器的层层嵌套, 4.2 文件操作：从 传统File 到 现代NIO.2, 4.2.1 传统方式：java.io.File 类, 核心用途与场景, 常用方法速查表, 设计缺陷与痛点, 4.2.2 [现代实践] java.nio.file 包 (NIO.2), 代码实战：Files 工具类的强大功能, 场景一：文件的创建与读写, 场景二：文件与目录的复制, 4.2.3 java.nio.file.Files 核心方法速查表, 1. 文件和目录检查, 2. 文件和目录创建, 3. 文件和目录删除, 4. 文件读x2F写操作（小文件）, 5. 文件读x2F写操作（大文件x2F流式处理）, 6. 复制与移动, 7. 目录遍历 (Stream API), 4.3 核心Ix2FO处理流搭配使用, 4.3.1 缓冲流 (BufferedInputStream x2F BufferedReader) 性能优化的基石, 面试题引入, 核心原理解析, 代码实战：文件复制性能对比, 4.3.2 转换流 (InputStreamReader x2F OutputStreamWriter) 字节与字符的桥梁, 核心用途, 应用场景, 代码示例：读取GBK文件并转存为UTF-8, 4.3.3 对象流 (ObjectInputStream x2F ObjectOutputStream) 对象的序列化与反序列化, 面试题引入, 核心概念, 关键知识点, 代码实战：将User对象持久化到文件, 4.4 [实战进阶] 简化Ix2FO操作的第三方库, 4.4.1 面试题引入与核心思想, 面试题引入, 核心思想：为什么需要第三方库？, 4.4.2 主流库介绍：Apache Commons IO, 4.4.3 代码实战：JDK原生写法 vs. Commons IO 一行代码, 场景一：读取整个文件到字符串, 场景二：复制文件, 场景三：递归删除目录, 4.5 Apache Commons IO 核心方法速查表, FileUtils 类：面向文件和目录的操作, 1. 读操作 (Read Operations), 2. 写操作 (Write Operations), 3. 复制与移动 (Copy amp Move Operations), 4. 删除与清空 (Delete amp Clean Operations), 5. 状态检查与其它, IOUtils 类：面向流的操作, 1. 读x2F转换操作 (Read x2F Convert Operations), 2. 写操作 (Write Operations), 3. 复制操作 (Copy Operations), 4. 关闭操作 (Close Operations)核心流体系与实战本章将深入的输入输出世界是程序与外部世界如文件网络控制台沟通的桥梁我们将从的四大家族和装饰器设计模式入手理解其核心设计思想然后深入文件操作的现代实践并最终探讨如何在真实项目中利用强大的第三方库来告别繁琐的样板代码核心概念与设计模式面试题引入请谈谈你对的理解字节流和字符流有什么区别节点流和处理流呢流的四大家族与核心区别的本质是程序与外部数据源之间的数据传输通道通过流这一抽象概念来表示这个通道并根据数据传输单位和方向的不同提供了四大家族作为所有操作的基石分类维度方向字节流处理一切数据如图片视频文本字符流专为处理文本数据优化输入读数据源程序抽象基类抽象基类输出写程序数据源抽象基类抽象基类字节流字符流这是体系中最根本的区别也是面试中的高频考点字节流以字节为单位进行读写它是最原始最通用的流可以处理任何类型的二进制数据如图片音频视频文件等但它的缺点在于在处理文本时它不关心字符编码字符流以字符为单位进行读写它是在字节流的基础上构建的专门用于处理文本数据其内部封装了字节到字符的解码和字符到字节的编码过程因此字符流能够正确地处理包含各种语言如中文的文本有效避免乱码问题代码示例乱码问题的产生与解决场景我们有一个编码的文本文件内容为你好错误演示使用字节流读取文本使用字节流逐字节读取结果出现了乱码原因分析在编码中一个中文字符通常由个字节表示上述代码一次只读一个字节并试图将其强转为字符自然无法正确还原你和好这两个字导致乱码正确方式使用字符流读取文本使用字符流逐字符读取结果乱码没了节点流处理流这是从功能层面对流的另一种划分方式节点流也被称为低级流它们是直接与数据源如文件网络套接字内存数组相连接的管道负责实际的数据传输例如处理流也被称为高级流它们不直接连接数据源而是套在已存在的流节点流或其他处理流之上像一个过滤器或增强器为原始的流增加额外的功能例如增加缓冲功能增加对象反序列化功能缓冲功能通过内部缓存机制一次性读取较多数据减少与底层数据源如文件网络的交互次数从而提高读取性能反序列化功能可以将之前通过写入的对象恢复为内存中的对象实现对象的持久化和传输为了最清晰地展示两者的区别与关系我们设定一个共同的非常常见的开发任务读取一个文本文件的内容并将其逐行打印到控制台假设我们有一个名为的文件内容如下床前明月光疑是地上霜举头望明月低头思故乡场景一仅使用节点流低级繁琐的方式如果我们只使用节点流如意味着我们需要亲自处理最原始的字节数据并手动管理缓冲和字符转换使用确保流被关闭创建一个节点流直接连接到数据源文件仅使用节点流读取我们必须自己创建一个字节数组作为缓冲区手动循环读取字节块手动将读取到的字节块按指定编码转换为字符串这里的操作非常底层且没有方便的按行读取功能分析这种方式的痛点操作底层我们直接和原始的打交道功能有限本身不提供按行读取这样的便捷功能编码繁琐需要手动处理字节到字符串的转换场景二使用处理流包装节点流高级推荐的方式现在我们引入处理流来装饰或增强节点流节点流仍然是直接连接到文件的管道它负责基础的字符读取处理流套在节点流之上为其增加强大的功能使用处理流节点流读取直接使用处理流提供的便捷的方法在内部帮我们处理好了缓冲和按行读取的所有细节两种方式对比总结对比维度仅使用节点流处理流节点流职责负责连接数据源进行最基础的字节读写节点流负责连接数据源处理流负责功能增强易用性差需要手动处理缓冲编码按行读取等高提供了等便捷性能低每次都可能是一次物理磁盘高内部缓冲机制大大减少了物理次数代码量繁琐样板代码多简洁代码意图清晰通过这个对比我们可以清晰地看到处理流的价值它将开发者从复杂的底层细节中解放出来让我们能更专注于业务逻辑本身同时还能获得更好的性能在实际开发中我们几乎总是使用处理流包装节点流的方式来进行操作这正是装饰器模式在中应用的精髓设计模式的灵魂装饰器模式理解体系的关键在于理解其背后优美的装饰器设计模式该模式允许我们向一个现有对象动态地添加新的功能同时又不改变其结构在中等节点流是我们的基础组件而等处理流则是装饰器我们可以像搭积木一样自由地将这些装饰器套在基础组件上按需组合出强大的功能代码示例装饰器的层层嵌套场景从一个文件中以高效缓冲的方式读取一个被序列化后的对象假设我们有一个名为的文件存储了一个对象这是一个典型的装饰器模式应用最内层节点流直接连接数据源文件中间层处理流为文件流增加缓冲功能提升性能最外层处理流为缓冲流增加对象反序列化功能最终我们通过功能最强大的最外层流进行操作流已成功按装饰器模式构建操作顺序文件语句会自动按相反的顺序关闭所有流无需手动操作这种设计使得体系既灵活又可扩展当需要新功能时只需创建一个新的处理流装饰器即可而无需修改现有的任何流类文件操作从传统到现代在理解了流的分类和装饰器设计模式后我们将聚焦于操作的一个核心应用文件操作这包括如何创建删除重命名文件和目录以及如何读取它们的属性为此提供了两代我们将对比学习并重点掌握现代化的解决方案传统方式类类是早期用于表示文件系统中的一个文件或目录路径的抽象它可以用于执行创建删除重命名等操作但其设计存在一些固有缺陷因此在现代开发中已不被推荐作为首选核心用途与场景在维护旧项目或使用一些尚未升级到的老旧第三方库时我们仍然会遇到类常用方法速查表方法签名功能描述检查文件或目录是否存在创建一个新文件创建单级多级目录删除文件或空目录重命名或移动文件获取名称绝对路径获取文件大小字节判断是目录还是文件列出目录下的文件和子目录设计缺陷与痛点错误处理不友好许多关键操作如在失败时仅返回而不会抛出异常这使得我们无法得知失败的具体原因是权限不足文件被占用还是其他问题给可靠的错误处理带来了巨大困难功能有限原生不支持符号链接等现代文件系统特性也无法方便地访问和修改文件元数据如权限所有者等无力处理非空目录方法只能删除文件或空目录要删除整个目录树需要自己编写复杂的递归逻辑现代实践包自起的引入为文件系统操作带来了革命性的变化它以接口为核心通过和两个强大的工具类提供了功能更全面设计更优良错误处理更明确的现代文件操作核心优势明确的异常处理所有操作在失败时都会抛出具体的让问题无处遁形强大的功能原生支持符号链接文件属性视图文件系统监视等高级功能高效的提供了大量便捷高效的静态方法可以用一行代码完成过去需要一个方法块才能实现的功能代码实战工具类的强大功能下面通过几个核心场景对比展示相比于传统类的巨大优势场景一文件的创建与读写需求创建一个文本文件向其中写入内容然后再读取出来使用创建对象这是现代文件路径的表示方式写入文件如果文件不存在则创建如果存在则追加内容是的方法极其方便这是用写入的第一行以只读方式打开文件以写入方式打开文件如果文件已存在则将数据追加到文件末尾而不是覆盖如果文件已存在则将其长度截断为即清空文件内容如果文件不存在则创建新文件如果文件已存在则抛出异常否则创建新文件在关闭文件时尝试删除该文件提示系统创建一个稀疏文件仅在支持稀疏文件的文件系统上有效每次更新文件内容或元数据时都同步写入磁盘每次更新文件内容时都同步写入磁盘但不包括元数据这是追加的第二行文件写入完成读取文件所有行到中读取文件内容的错误处理非常明确文件操作失败场景二文件与目录的复制需求将一个文件复制到另一个位置并将一个完整的目录包含子目录和文件复制到新位置复制单个文件准备源文件和目标目录一些源数据使用如果目标文件已存在则替换单个文件复制完成递归复制整个目录准备源目录使用和来递归复制无法复制整个目录已成功复制结论在所有新的项目中都应优先并坚持使用包进行文件和目录操作它更安全功能更强大代码也更现代化核心方法速查表好的明白了为了让速查表更加简洁一目了然我将移除方法签名列中的返回类型和修饰符只保留核心的方法名和参数示意文件和目录检查方法名功能描述注意事项最佳实践检查文件或目录是否存在最常用的检查方法检查文件或目录是否不存在的一个更具可读性的替代方案判断路径是否为目录判断路径是否为普通文件判断文件是否可读判断文件是否可写判断文件是否可执行判断两个是否指向同一个文件比更可靠因为它会处理符号链接等情况文件和目录创建方法名功能描述注意事项最佳实践创建一个新文件如果文件已存在会抛出创建一个新目录只能创建单级目录如果父目录不存在会抛出异常强烈推荐创建多级目录如果父目录不存在会自动一并创建非常方便在默认或指定位置创建一个临时文件常用于需要临时存储数据的场景创建一个临时目录文件和目录删除方法名功能描述注意事项最佳实践删除一个文件或空目录如果路径不存在抛出如果目录非空抛出如果文件或目录存在则删除它推荐使用比更安全因为它不会在文件不存在时抛出异常只会返回文件读写操作小文件这些方法会将文件的全部内容一次性读入内存非常便捷但只适用于小文件方法名功能描述注意事项最佳实践将文件的所有内容读取为一个字节数组注意内存溢出风险不适用于大文件将文件的所有行读取到一个字符串列表中注意内存溢出风险默认使用编码将一个字节数组或字符串集合写入文件默认会覆盖已有文件可使用指定追加创建等将字符串写入文件写入文本最简单的方式读取文件内容为字符串读取小文本文件最简单的方式文件读写操作大文件流式处理当处理大文件时应使用流式避免一次性将所有内容加载到内存方法名功能描述注意事项最佳实践打开一个文件返回一个用于读取的处理大文件的标准方式获取流之后需配合使用打开或创建一个文件返回一个用于写入的同上打开一个文件返回一个用于读取文本的提供了高效的方法是读取大文本文件的首选打开或创建一个文件返回一个写入大文本文件的首选返回一个由文件所有行组成的懒加载非常适合用函数式编程风格处理大文本文件内存占用极小复制与移动方法名功能描述注意事项最佳实践复制一个文件或目录默认情况下如果目标文件已存在会失败需使用来覆盖复制目录时只复制目录本身不复制其内容移动或重命名一个文件可以指定来保证操作的原子性目录遍历方法名功能描述注意事项最佳实践返回一个表示目录下所有条目不含子目录的非递归只遍历当前层级返回一个从指定路径开始递归遍历所有文件和目录的功能强大可以配合等操作轻松实现文件查找等功能功能类似但可以额外传入一个匹配器来筛选路径比后接更高效核心处理流搭配使用在掌握了文件和目录的表示方法后我们回到流本身聚焦于如何通过组合不同的处理流来高效灵活地读写文件内容缓冲流性能优化的基石面试题引入为什么我们在进行文件时总是推荐使用缓冲流它的原理是什么核心原理解析计算机中对磁盘或网络的操作系统调用相比于内存操作是极其缓慢的如果我们直接使用的方法逐字节读取文件那么每读取一个字节就可能触发一次昂贵的物理磁盘访问缓冲流正是为解决这一性能瓶颈而生它的原理是在内部维护一个内存缓冲区一个字节或字符数组默认大小通常为读取时会一次性从磁盘读取一大块数据例如填充到内部缓冲区之后你再调用方法时它会直接从高速的内存缓冲区中返回数据直到缓冲区耗尽才会再次触发下一次对磁盘的大块读取写入时会先将你写入的数据存放在缓冲区直到缓冲区满了或者你手动调用方法它才会将整个缓冲区的数据一次性写入磁盘结论缓冲流通过化零为整的策略用一次大的操作替代了无数次小的操作极大地减少了与底层物理设备的交互次数从而实现性能的飞跃代码实战文件复制性能对比场景复制一个文件对比使用缓冲流和不使用缓冲流的耗时为了测试先创建一个较大的文件方案一不使用缓冲流逐字节复制不使用缓冲流耗时方案二使用缓冲流使用缓冲流耗时运行结果示例不使用缓冲流耗时使用缓冲流耗时可以看到性能差异是数量级的因此在进行任何文件时使用缓冲流包装节点流都应成为一种编程习惯转换流字节与字符的桥梁核心用途转换流的核心作用是适配器它能将字节流转换为字符流并在转换过程中处理字符编码应用场景当你需要读写的文本文件编码与当前操作系统的默认编码不一致时必须使用转换流来显式指定正确的编码否则就会产生乱码读取例如在的服务器上读取一个由记事本默认编码生成的中文文件写入例如无论程序运行在什么系统上都希望统一生成编码的配置文件代码示例读取文件并转存为假设是一个编码的文件内容为你好世界我们可以先手动创建一个这样的文件用于测试创建一个输入字节流连接到源文件使用转换流指定用编码来解码字节流为了效率再套上一个创建一个输出字节流连接到目标文件使用转换流指定用编码来编码字符流同样套上编码转换完成对象流对象的序列化与反序列化面试题引入什么是的序列化关键字和有什么作用核心概念序列化将一个对象的状态转换为字节序列的过程反序列化从字节序列中恢复并重建对象的过程用途实现对象的持久化保存到文件或数据库和网络传输关键知识点接口一个类必须实现这个标记接口没有任何方法其对象才能被序列化它像一个通行证告诉这个类的对象可以被扁平化为字节关键字用于修饰字段被修饰的字段将被排除在序列化过程之外不会被写入字节流反序列化后该字段的值会是其类型的默认值如对象为为常用于密码安全令牌缓存数据等敏感或无需持久化的字段面试核心这是一个用于标识可序列化类版本的型常量作用在反序列化时会比较字节流中的和当前加载的类中的如果两者不一致会抛出以防止因类版本不兼容导致的数据错乱最佳实践强烈建议所有可序列化类都显式声明一个如果不声明编译器会自动生成一个但这个自动生成的值对类的结构非常敏感如增删字段稍有改动就会变化导致旧的序列化数据无法被新版类反序列化代码实战将对象持久化到文件类必须实现接口强烈建议显式声明使用关键字密码将不会被序列化序列化过程原始对象对象已成功序列化到文件反序列化过程从文件反序列化出的对象注意字段因为是所以变成了实战进阶简化操作的第三方库面试题引入与核心思想面试题引入在实际项目中你还会频繁地手动编写和循环来复制一个文件吗有没有更便捷更专业的处理方式核心思想为什么需要第三方库虽然我们必须深入理解原生流的体系因为它是所有操作的基础也是排查底层问题的关键但在真实的快节奏的商业项目开发中对于那些反复出现的通用任务如读写文件复制目录等如果每次都手动编写原始的流处理代码会存在几个明显的问题代码冗长完成一个简单的任务需要创建多个流对象编写循环处理异常代码显得非常臃肿容易出错手动管理资源和编写逻辑稍有不慎就可能导致资源未关闭缓冲区处理不当等难以察觉的效率低下重复编写同样功能的代码是对开发时间的浪费因此在专业开发领域我们遵循不重复造轮子的原则对于操作社区已经为我们提供了极其优秀经过数万个项目验证的轮子第三方工具库主流库介绍是生态中处理的事实上的行业标准它是一个稳定可靠功能极其丰富的工具包几乎是所有项目的必备依赖之一它的核心价值在于将那些繁琐的样板代码封装成了简单强大的一行代码如何引入在项目中只需在中添加以下依赖即可核心工具类面向文件对象和目录的操作面向流等的操作代码实战原生写法一行代码下面我们将通过几个鲜明的之前之后的对比来感受的威力场景一读取整个文件到字符串原生写法方法调用写法一行代码搞定内部已处理好所有流的打开和关闭方法调用场景二复制文件原生写法方法调用写法同样是一行代码方法调用场景三递归删除目录原生写法的无法删除非空目录要实现此功能必须自己编写一个递归方法先删除目录下的所有文件和子目录最后再删除该目录本身过程复杂且容易出错写法无论目录是否为空一行代码安全删除方法调用核心方法速查表类面向文件和目录的操作读操作方法名功能描述最常用将整个文件内容读取为一个字符串将文件的每一行读取到一个字符串列表中将整个文件内容读取为一个字节数组写操作方法名功能描述最常用将一个字符串写入文件会覆盖或追加将一个字符串集合如逐行写入文件将一个字节数组写入文件复制与移动方法名功能描述常用复制一个文件到新位置强大递归复制整个目录及其所有内容将一个文件复制到指定的目录下移动一个文件本质上是复制后删除移动整个目录将文件或目录移动到指定的目录下删除与清空方法名功能描述强大递归删除整个目录无论其是否为空清空一个目录下的所有内容但不删除目录本身强制删除一个文件或目录如果删除失败会尝试多次状态检查与其它方法名功能描述获取一个文件的大小常用递归计算整个目录的大小判断一个文件是否比另一个文件或指定时间戳更新类面向流的操作读转换操作方法名功能描述最常用将一个或的内容读取为一个字符串常用将一个或的内容读取为一个字节数组将一个如转换为一个将一个或的内容按行读取到一个中写操作方法名功能描述将一个或的内容写入到一个或中复制操作方法名功能描述最常用将一个的内容复制到一个中或将复制到返回复制的字节字符数用于复制大于的超大流关闭操作方法名功能描述历史著名安静地关闭一个如流忽略所有异常注意在及之后官方推荐使用语句来自动管理资源此方法的必要性已大大降低被视为一种过时的模式",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-14 16:51:40",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-0-%E6%A0%B8%E5%BF%83-Java-I-O-%E6%B5%81%E4%BD%93%E7%B3%BB%E4%B8%8E%E5%AE%9E%E6%88%98"><span class="toc-text">4.0 [核心] Java I/O 流体系与实战</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-I-O-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%E4%B8%8E%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8F"><span class="toc-text">4.1 I/O 核心概念与设计模式</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-1-1-%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5"><span class="toc-text">4.1.1 面试题引入</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-1-2-%E6%B5%81%E7%9A%84%E2%80%9C%E5%9B%9B%E5%A4%A7%E5%AE%B6%E6%97%8F%E2%80%9D%E4%B8%8E%E6%A0%B8%E5%BF%83%E5%8C%BA%E5%88%AB"><span class="toc-text">4.1.2 流的“四大家族”与核心区别</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%AD%97%E8%8A%82%E6%B5%81-vs-%E5%AD%97%E7%AC%A6%E6%B5%81"><span class="toc-text">字节流 vs. 字符流</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E4%B9%B1%E7%A0%81%E9%97%AE%E9%A2%98%E7%9A%84%E4%BA%A7%E7%94%9F%E4%B8%8E%E8%A7%A3%E5%86%B3"><span class="toc-text">代码示例：乱码问题的产生与解决</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E4%BD%BF%E7%94%A8%E5%A4%84%E7%90%86%E6%B5%81%E5%8C%85%E8%A3%85%E8%8A%82%E7%82%B9%E6%B5%81%EF%BC%88%E9%AB%98%E7%BA%A7%E3%80%81%E6%8E%A8%E8%8D%90%E7%9A%84%E6%96%B9%E5%BC%8F%EF%BC%89"><span class="toc-text">场景二：使用处理流包装节点流（高级、推荐的方式）</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%A4%E7%A7%8D%E6%96%B9%E5%BC%8F%E5%AF%B9%E6%AF%94%E6%80%BB%E7%BB%93"><span class="toc-text">两种方式对比总结</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-1-4-%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8F-java-io-%E7%9A%84%E7%81%B5%E9%AD%82%EF%BC%9A%E8%A3%85%E9%A5%B0%E5%99%A8%E6%A8%A1%E5%BC%8F"><span class="toc-text">4.1.4 [设计模式] java.io 的灵魂：装饰器模式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%A3%85%E9%A5%B0%E5%99%A8%E7%9A%84%E5%B1%82%E5%B1%82%E5%B5%8C%E5%A5%97"><span class="toc-text">代码示例：装饰器的层层嵌套</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C%EF%BC%9A%E4%BB%8E-%E4%BC%A0%E7%BB%9FFile-%E5%88%B0-%E7%8E%B0%E4%BB%A3NIO-2"><span class="toc-text">4.2 文件操作：从 传统File 到 现代NIO.2</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-1-%E4%BC%A0%E7%BB%9F%E6%96%B9%E5%BC%8F%EF%BC%9Ajava-io-File-%E7%B1%BB"><span class="toc-text">4.2.1 传统方式：java.io.File 类</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF"><span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-text">常用方法速查表</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%AE%BE%E8%AE%A1%E7%BC%BA%E9%99%B7%E4%B8%8E%E7%97%9B%E7%82%B9"><span class="toc-text">设计缺陷与痛点</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-2-%E7%8E%B0%E4%BB%A3%E5%AE%9E%E8%B7%B5-java-nio-file-%E5%8C%85-%E2%80%9CNIO-2%E2%80%9D"><span class="toc-text">4.2.2 [现代实践] java.nio.file 包 (“NIO.2”)</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E5%AE%9E%E6%88%98%EF%BC%9AFiles-%E5%B7%A5%E5%85%B7%E7%B1%BB%E7%9A%84%E5%BC%BA%E5%A4%A7%E5%8A%9F%E8%83%BD"><span class="toc-text">代码实战：Files 工具类的强大功能</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E6%96%87%E4%BB%B6%E7%9A%84%E5%88%9B%E5%BB%BA%E4%B8%8E%E8%AF%BB%E5%86%99"><span class="toc-text">场景一：文件的创建与读写</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E6%96%87%E4%BB%B6%E4%B8%8E%E7%9B%AE%E5%BD%95%E7%9A%84%E5%A4%8D%E5%88%B6"><span class="toc-text">场景二：文件与目录的复制</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-3-java-nio-file-Files-%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-text">4.2.3 java.nio.file.Files 核心方法速查表</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E6%96%87%E4%BB%B6%E5%92%8C%E7%9B%AE%E5%BD%95%E6%A3%80%E6%9F%A5"><span class="toc-text">1. 文件和目录检查</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E6%96%87%E4%BB%B6%E5%92%8C%E7%9B%AE%E5%BD%95%E5%88%9B%E5%BB%BA"><span class="toc-text">2. 文件和目录创建</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E6%96%87%E4%BB%B6%E5%92%8C%E7%9B%AE%E5%BD%95%E5%88%A0%E9%99%A4"><span class="toc-text">3. 文件和目录删除</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-%E6%96%87%E4%BB%B6%E8%AF%BB-%E5%86%99%E6%93%8D%E4%BD%9C%EF%BC%88%E5%B0%8F%E6%96%87%E4%BB%B6%EF%BC%89"><span class="toc-text">4. 文件读/写操作（小文件）</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#5-%E6%96%87%E4%BB%B6%E8%AF%BB-%E5%86%99%E6%93%8D%E4%BD%9C%EF%BC%88%E5%A4%A7%E6%96%87%E4%BB%B6-%E6%B5%81%E5%BC%8F%E5%A4%84%E7%90%86%EF%BC%89"><span class="toc-text">5. 文件读/写操作（大文件/流式处理）</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#6-%E5%A4%8D%E5%88%B6%E4%B8%8E%E7%A7%BB%E5%8A%A8"><span class="toc-text">6. 复制与移动</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#7-%E7%9B%AE%E5%BD%95%E9%81%8D%E5%8E%86-Stream-API"><span class="toc-text">7. 目录遍历 (Stream API)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-%E6%A0%B8%E5%BF%83I-O%E5%A4%84%E7%90%86%E6%B5%81%E6%90%AD%E9%85%8D%E4%BD%BF%E7%94%A8"><span class="toc-text">4.3 核心I/O处理流搭配使用</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-1-%E7%BC%93%E5%86%B2%E6%B5%81-BufferedInputStream-BufferedReader-%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96%E7%9A%84%E5%9F%BA%E7%9F%B3"><span class="toc-text">4.3.1 缓冲流 (BufferedInputStream / BufferedReader): 性能优化的基石</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5"><span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E5%8E%9F%E7%90%86%E8%A7%A3%E6%9E%90"><span class="toc-text">核心原理解析</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E5%AE%9E%E6%88%98%EF%BC%9A%E6%96%87%E4%BB%B6%E5%A4%8D%E5%88%B6%E6%80%A7%E8%83%BD%E5%AF%B9%E6%AF%94"><span class="toc-text">代码实战：文件复制性能对比</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-2-%E8%BD%AC%E6%8D%A2%E6%B5%81-InputStreamReader-OutputStreamWriter-%E5%AD%97%E8%8A%82%E4%B8%8E%E5%AD%97%E7%AC%A6%E7%9A%84%E6%A1%A5%E6%A2%81"><span class="toc-text">4.3.2 转换流 (InputStreamReader / OutputStreamWriter): 字节与字符的桥梁</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94"><span class="toc-text">核心用途</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">应用场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%AF%BB%E5%8F%96GBK%E6%96%87%E4%BB%B6%E5%B9%B6%E8%BD%AC%E5%AD%98%E4%B8%BAUTF-8"><span class="toc-text">代码示例：读取GBK文件并转存为UTF-8</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-3-%E5%AF%B9%E8%B1%A1%E6%B5%81-ObjectInputStream-ObjectOutputStream-%E5%AF%B9%E8%B1%A1%E7%9A%84%E5%BA%8F%E5%88%97%E5%8C%96%E4%B8%8E%E5%8F%8D%E5%BA%8F%E5%88%97%E5%8C%96"><span class="toc-text">4.3.3 对象流 (ObjectInputStream / ObjectOutputStream): 对象的序列化与反序列化</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-1"><span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5"><span class="toc-text">核心概念</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%85%B3%E9%94%AE%E7%9F%A5%E8%AF%86%E7%82%B9"><span class="toc-text">关键知识点</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E5%AE%9E%E6%88%98%EF%BC%9A%E5%B0%86User%E5%AF%B9%E8%B1%A1%E6%8C%81%E4%B9%85%E5%8C%96%E5%88%B0%E6%96%87%E4%BB%B6"><span class="toc-text">代码实战：将User对象持久化到文件</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-%E5%AE%9E%E6%88%98%E8%BF%9B%E9%98%B6-%E7%AE%80%E5%8C%96I-O%E6%93%8D%E4%BD%9C%E7%9A%84%E7%AC%AC%E4%B8%89%E6%96%B9%E5%BA%93"><span class="toc-text">4.4 [实战进阶] 简化I/O操作的第三方库</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-1-%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5%E4%B8%8E%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3"><span class="toc-text">4.4.1 面试题引入与核心思想</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-2"><span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3%EF%BC%9A%E4%B8%BA%E4%BB%80%E4%B9%88%E9%9C%80%E8%A6%81%E7%AC%AC%E4%B8%89%E6%96%B9%E5%BA%93%EF%BC%9F"><span class="toc-text">核心思想：为什么需要第三方库？</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-2-%E4%B8%BB%E6%B5%81%E5%BA%93%E4%BB%8B%E7%BB%8D%EF%BC%9AApache-Commons-IO"><span class="toc-text">4.4.2 主流库介绍：Apache Commons IO</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-3-%E4%BB%A3%E7%A0%81%E5%AE%9E%E6%88%98%EF%BC%9AJDK%E5%8E%9F%E7%94%9F%E5%86%99%E6%B3%95-vs-Commons-IO-%E4%B8%80%E8%A1%8C%E4%BB%A3%E7%A0%81"><span class="toc-text">4.4.3 代码实战：JDK原生写法 vs. Commons IO 一行代码</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E8%AF%BB%E5%8F%96%E6%95%B4%E4%B8%AA%E6%96%87%E4%BB%B6%E5%88%B0%E5%AD%97%E7%AC%A6%E4%B8%B2"><span class="toc-text">场景一：读取整个文件到字符串</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E5%A4%8D%E5%88%B6%E6%96%87%E4%BB%B6"><span class="toc-text">场景二：复制文件</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9A%E9%80%92%E5%BD%92%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95"><span class="toc-text">场景三：递归删除目录</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-5-Apache-Commons-IO-%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-text">4.5 Apache Commons IO 核心方法速查表</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#FileUtils-%E7%B1%BB%EF%BC%9A%E9%9D%A2%E5%90%91%E6%96%87%E4%BB%B6%E5%92%8C%E7%9B%AE%E5%BD%95%E7%9A%84%E6%93%8D%E4%BD%9C"><span class="toc-text">FileUtils 类：面向文件和目录的操作</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E8%AF%BB%E6%93%8D%E4%BD%9C-Read-Operations"><span class="toc-text">1. 读操作 (Read Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E5%86%99%E6%93%8D%E4%BD%9C-Write-Operations"><span class="toc-text">2. 写操作 (Write Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E5%A4%8D%E5%88%B6%E4%B8%8E%E7%A7%BB%E5%8A%A8-Copy-Move-Operations"><span class="toc-text">3. 复制与移动 (Copy &amp; Move Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-%E5%88%A0%E9%99%A4%E4%B8%8E%E6%B8%85%E7%A9%BA-Delete-Clean-Operations"><span class="toc-text">4. 删除与清空 (Delete &amp; Clean Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#5-%E7%8A%B6%E6%80%81%E6%A3%80%E6%9F%A5%E4%B8%8E%E5%85%B6%E5%AE%83"><span class="toc-text">5. 状态检查与其它</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#IOUtils-%E7%B1%BB%EF%BC%9A%E9%9D%A2%E5%90%91%E6%B5%81%E7%9A%84%E6%93%8D%E4%BD%9C"><span class="toc-text">IOUtils 类：面向流的操作</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E8%AF%BB-%E8%BD%AC%E6%8D%A2%E6%93%8D%E4%BD%9C-Read-Convert-Operations"><span class="toc-text">1. 读/转换操作 (Read / Convert Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E5%86%99%E6%93%8D%E4%BD%9C-Write-Operations-1"><span class="toc-text">2. 写操作 (Write Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E5%A4%8D%E5%88%B6%E6%93%8D%E4%BD%9C-Copy-Operations"><span class="toc-text">3. 复制操作 (Copy Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-%E5%85%B3%E9%97%AD%E6%93%8D%E4%BD%9C-Close-Operations"><span class="toc-text">4. 关闭操作 (Close Operations)</span></a></li></ol></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Java（四）：4.0 [核心] Java I/O 流体系与实战</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-05-08T11:13:45.000Z" title="发表于 2025-05-08 19:13:45">2025-05-08</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-14T08:51:40.660Z" title="更新于 2025-07-14 16:51:40">2025-07-14</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">8.9k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>32分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Java（四）：4.0 [核心] Java I/O 流体系与实战"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/6760.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/6760.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Java基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Java（四）：4.0 [核心] Java I/O 流体系与实战</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-05-08T11:13:45.000Z" title="发表于 2025-05-08 19:13:45">2025-05-08</time><time itemprop="dateCreated datePublished" datetime="2025-07-14T08:51:40.660Z" title="更新于 2025-07-14 16:51:40">2025-07-14</time></header><div id="postchat_postcontent"><h2 id="4-0-核心-Java-I-O-流体系与实战"><a href="#4-0-核心-Java-I-O-流体系与实战" class="headerlink" title="4.0 [核心] Java I/O 流体系与实战"></a><strong>4.0 [核心] Java I/O 流体系与实战</strong></h2><p>本章将深入Java的I/O（输入/输出）世界。I/O是程序与外部世界（如文件、网络、控制台）沟通的桥梁。我们将从I/O的“四大家族”和装饰器设计模式入手，理解其核心设计思想，然后深入文件操作的现代实践（NIO.2），并最终探讨如何在真实项目中，利用强大的第三方库来告别繁琐的I/O样板代码。</p><h3 id="4-1-I-O-核心概念与设计模式"><a href="#4-1-I-O-核心概念与设计模式" class="headerlink" title="4.1 I/O 核心概念与设计模式"></a><strong>4.1 I/O 核心概念与设计模式</strong></h3><h4 id="4-1-1-面试题引入"><a href="#4-1-1-面试题引入" class="headerlink" title="4.1.1 面试题引入"></a><strong>4.1.1 面试题引入</strong></h4><blockquote><p>“请谈谈你对Java I/O的理解。字节流和字符流有什么区别？节点流和处理流呢？”</p></blockquote><h4 id="4-1-2-流的“四大家族”与核心区别"><a href="#4-1-2-流的“四大家族”与核心区别" class="headerlink" title="4.1.2 流的“四大家族”与核心区别"></a><strong>4.1.2 流的“四大家族”与核心区别</strong></h4><p>I/O的本质是程序与外部数据源之间的数据传输通道。Java通过<strong>流（Stream）这一抽象概念来表示这个通道，并根据数据传输单位</strong>和<strong>方向</strong>的不同，提供了“四大家族”作为所有I/O操作的基石：</p><table><thead><tr><th align="left">分类维度</th><th align="left">方向</th><th align="left">字节流 (处理一切数据，如图片、视频、文本)</th><th align="left">字符流 (专为处理文本数据优化)</th></tr></thead><tbody><tr><td align="left"><strong>输入(读)</strong></td><td align="left"><code>数据源 -&gt; 程序</code></td><td align="left"><code>InputStream</code> (抽象基类)</td><td align="left"><code>Reader</code> (抽象基类)</td></tr><tr><td align="left"><strong>输出(写)</strong></td><td align="left"><code>程序 -&gt; 数据源</code></td><td align="left"><code>OutputStream</code> (抽象基类)</td><td align="left"><code>Writer</code> (抽象基类)</td></tr></tbody></table><h5 id="字节流-vs-字符流"><a href="#字节流-vs-字符流" class="headerlink" title="字节流 vs. 字符流"></a><strong>字节流 vs. 字符流</strong></h5><p>这是I/O体系中最根本的区别，也是面试中的高频考点。</p><ul><li><p><strong>字节流 (<code>InputStream</code>/<code>OutputStream</code>)</strong> 以<strong>字节（byte,<br>8-bit）为单位进行读写。它是最原始、最通用的流，可以处理任何类型的二进制数据，如图片、音频、视频文件等。但它的缺点在于，在处理文本时，它不关心字符编码</strong>。</p></li><li><p><strong>字符流 (<code>Reader</code>/<code>Writer</code>)</strong> 以<strong>字符（char,<br>16-bit）为单位进行读写。它是在字节流的基础上构建的，专门用于处理文本数据。其内部封装了字节到字符的解码</strong>和字符到字节的<strong>编码</strong>过程。因此，字符流能够正确地处理包含各种语言（如中文）的文本，有效避免<strong>乱码</strong>问题。</p></li></ul><h5 id="代码示例：乱码问题的产生与解决"><a href="#代码示例：乱码问题的产生与解决" class="headerlink" title="代码示例：乱码问题的产生与解决"></a><strong>代码示例：乱码问题的产生与解决</strong></h5><blockquote><p><strong>场景</strong>：我们有一个UTF-8编码的文本文件<code>test.txt</code>，内容为“你好Java”。</p></blockquote><ul><li><p><strong>错误演示：使用字节流读取文本</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.FileInputStream;</span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> <span class="keyword">throws</span> IOException {</span><br><span class="line">        <span class="type">FileInputStream</span> <span class="variable">fis</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">FileInputStream</span>(<span class="string">"test.txt"</span>);</span><br><span class="line">        System.out.println(<span class="string">"--- 使用字节流逐字节读取 ---"</span>);</span><br><span class="line">        <span class="type">int</span> byteData;</span><br><span class="line">        <span class="keyword">while</span> ((byteData = fis.read()) != -<span class="number">1</span>) {</span><br><span class="line">            System.out.print((<span class="type">char</span>) byteData);</span><br><span class="line">        }</span><br><span class="line">        System.out.println(<span class="string">"\n结果：出现了乱码。"</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>原因分析</strong>：在UTF-8编码中，一个中文字符通常由3个字节表示。上述代码一次只读一个字节，并试图将其强转为字符，自然无法正确还原“你”和“好”这两个字，导致乱码。</p></li><li><p><strong>正确方式：使用字符流读取文本</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.FileInputStream;</span><br><span class="line"><span class="keyword">import</span> java.io.FileReader;</span><br></pre></td></tr></tbody></table></figure></li></ul><p>import java.io.IOException;</p><pre><code>public class Main {
    public static void main(String[] args) throws IOException {
        FileReader reader = new FileReader("test.txt");
        System.out.println("--- 使用字符流逐字符读取 ---");
        int byteData;
        while ((byteData = reader.read()) != -1) {
            System.out.print((char) byteData);
        }
        System.out.println("\n结果：乱码没了");
    }
}
<figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"></span><br><span class="line">#### **4.1.3 节点流 vs. 处理流**</span><br><span class="line"></span><br><span class="line">这是从功能层面对流的另一种划分方式。</span><br><span class="line"></span><br><span class="line">- **节点流 (Node</span><br><span class="line">  Stream)**：也被称为“低级流”。它们是直接与数据源（如文件、网络套接字、内存数组）相连接的“管道”，负责实际的数据传输。例如</span><br><span class="line">  `FileInputStream`、`ByteArrayInputStream`。</span><br><span class="line">- **处理流 (Processing</span><br><span class="line">  Stream)**：也被称为“高级流”。它们**不直接连接数据源**，而是“套”在已存在的流（节点流或其他处理流）之上，像一个“过滤器”或“增强器”，为原始的流增加额外的功能。例如</span><br><span class="line">  `BufferedInputStream`（增加缓冲功能）、`ObjectInputStream`（增加对象反序列化功能）。</span><br><span class="line"></span><br><span class="line">* **缓冲功能**：`BufferedInputStream`</span><br><span class="line">  通过内部缓存机制，一次性读取较多数据，减少与底层数据源（如文件、网络）的交互次数，从而提高读取性能。</span><br><span class="line">* **反序列化功能**：`ObjectInputStream` 可以将之前通过 `ObjectOutputStream`</span><br><span class="line">  写入的 Java 对象恢复为内存中的对象，实现对象的持久化和传输。</span><br><span class="line"></span><br><span class="line">为了最清晰地展示两者的区别与关系，我们设定一个共同的、非常常见的开发任务：**读取一个文本文件的内容，并将其逐行打印到控制台**。</span><br><span class="line"></span><br><span class="line">假设我们有一个名为 `poem.txt` 的文件，内容如下：</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure>
</code></pre><p>床前明月光，<br>疑是地上霜。<br>举头望明月，<br>低头思故乡。</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br></pre></td><td class="code"><pre><span class="line"></span><br><span class="line">##### **场景一：仅使用节点流（低级、繁琐的方式）**</span><br><span class="line"></span><br><span class="line">如果我们只使用节点流（如`FileInputStream`），意味着我们需要亲自处理最原始的字节数据，并手动管理缓冲和字符转换。</span><br><span class="line"></span><br><span class="line">```java</span><br><span class="line">package com.example;</span><br><span class="line"></span><br><span class="line">import java.io.FileInputStream;</span><br><span class="line">import java.io.IOException;</span><br><span class="line"></span><br><span class="line">public class Main {</span><br><span class="line">    public static void main(String[] args) {</span><br><span class="line">        // 使用 try-with-resources 确保流被关闭</span><br><span class="line">        try (</span><br><span class="line">            // 1. 创建一个节点流，直接连接到数据源（文件）</span><br><span class="line">            FileInputStream fis = new FileInputStream("poem.txt")</span><br><span class="line">        ) {</span><br><span class="line">            System.out.println("--- 仅使用节点流 FileInputStream 读取 ---");</span><br><span class="line">            </span><br><span class="line">            // 2. 我们必须自己创建一个字节数组作为“缓冲区”</span><br><span class="line">            byte[] buffer = new byte[1024]; </span><br><span class="line">            int bytesRead;</span><br><span class="line"></span><br><span class="line">            // 3. 手动循环读取字节块</span><br><span class="line">            while ((bytesRead = fis.read(buffer)) != -1) {</span><br><span class="line">                // 4. 手动将读取到的字节块，按指定编码转换为字符串</span><br><span class="line">                // 这里的操作非常底层，且没有方便的按行读取功能</span><br><span class="line">                String chunk = new String(buffer, 0, bytesRead, "UTF-8");</span><br><span class="line">                System.out.print(chunk);</span><br><span class="line">            }</span><br><span class="line">        } catch (IOException e) {</span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>分析这种方式的痛点：</strong></p><ul><li><strong>操作底层</strong>：我们直接和原始的 <code>byte[]</code> 打交道。</li><li><strong>功能有限</strong>：<code>FileInputStream</code> 本身不提供按行读取 (<code>readLine</code>)<br>这样的便捷功能。</li><li><strong>编码繁琐</strong>：需要手动处理字节到字符串的转换。</li></ul><h5 id="场景二：使用处理流包装节点流（高级、推荐的方式）"><a href="#场景二：使用处理流包装节点流（高级、推荐的方式）" class="headerlink" title="场景二：使用处理流包装节点流（高级、推荐的方式）"></a><strong>场景二：使用处理流包装节点流（高级、推荐的方式）</strong></h5><p>现在，我们引入处理流 <code>BufferedReader</code> 来“装饰”或“增强”节点流 <code>FileReader</code>。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.BufferedReader;</span><br><span class="line"><span class="keyword">import</span> java.io.FileReader;</span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="keyword">try</span> (</span><br><span class="line">            <span class="comment">// 1. 节点流 FileReader: 仍然是直接连接到文件的管道，它负责基础的字符读取</span></span><br><span class="line">            <span class="type">FileReader</span> <span class="variable">fileReader</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">FileReader</span>(<span class="string">"poem.txt"</span>);</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 2. 处理流 BufferedReader: “套”在节点流之上，为其增加强大的功能</span></span><br><span class="line">            <span class="type">BufferedReader</span> <span class="variable">bufferedReader</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedReader</span>(fileReader)</span><br><span class="line">        ) {</span><br><span class="line">            System.out.println(<span class="string">"--- 使用处理流 BufferedReader + 节点流 FileReader 读取 ---"</span>);</span><br><span class="line">            String line;</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 3. 直接使用处理流提供的、便捷的 readLine() 方法</span></span><br><span class="line">            <span class="comment">// BufferedReader 在内部帮我们处理好了缓冲和按行读取的所有细节</span></span><br><span class="line">            <span class="keyword">while</span> ((line = bufferedReader.readLine()) != <span class="literal">null</span>) {</span><br><span class="line">                System.out.println(line);</span><br><span class="line">            }</span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="两种方式对比总结"><a href="#两种方式对比总结" class="headerlink" title="两种方式对比总结"></a><strong>两种方式对比总结</strong></h5><table><thead><tr><th align="left">对比维度</th><th align="left">仅使用节点流 (<code>FileInputStream</code>)</th><th align="left">处理流 + 节点流 (<code>BufferedReader</code> + <code>FileReader</code>)</th></tr></thead><tbody><tr><td align="left"><strong>职责</strong></td><td align="left">负责连接数据源，进行最基础的字节读写。</td><td align="left"><strong>节点流</strong>负责连接数据源，<strong>处理流</strong>负责功能增强。</td></tr><tr><td align="left"><strong>易用性</strong></td><td align="left">差，需要手动处理缓冲、编码、按行读取等。</td><td align="left"><strong>高</strong>，提供了<code>readLine()</code>等便捷API。</td></tr><tr><td align="left"><strong>性能</strong></td><td align="left">低，每次<code>read()</code>都可能是一次物理磁盘I/O。</td><td align="left"><strong>高</strong>，内部缓冲机制大大减少了物理I/O次数。</td></tr><tr><td align="left"><strong>代码量</strong></td><td align="left">繁琐，样板代码多。</td><td align="left">简洁，代码意图清晰。</td></tr></tbody></table><p>通过这个对比，我们可以清晰地看到<strong>处理流</strong>的价值：它将开发者从复杂的底层I/O细节中解放出来，让我们能更专注于业务逻辑本身，同时还能获得更好的性能。</p><p>在实际开发中，我们几乎总是使用<strong>处理流包装节点流</strong>的方式来进行I/O操作，这正是装饰器模式在Java<br>I/O中应用的精髓。</p><h4 id="4-1-4-设计模式-java-io-的灵魂：装饰器模式"><a href="#4-1-4-设计模式-java-io-的灵魂：装饰器模式" class="headerlink" title="4.1.4 [设计模式] java.io 的灵魂：装饰器模式"></a><strong>4.1.4 [设计模式] <code>java.io</code> 的灵魂：装饰器模式</strong></h4><p>理解Java I/O体系的关键，在于理解其背后优美的<strong>装饰器设计模式（Decorator<br>Pattern）</strong>。该模式允许我们向一个现有对象动态地添加新的功能，同时又不改变其结构。</p><p>在I/O中，<code>FileInputStream</code>等节点流是我们的<strong>基础组件（ConcreteComponent）</strong>，而<code>BufferedInputStream</code>等处理流则是<strong>装饰器（Decorator）</strong>。我们可以像搭积木一样，自由地将这些装饰器“套”在基础组件上，按需组合出强大的功能。</p><h5 id="代码示例：装饰器的层层嵌套"><a href="#代码示例：装饰器的层层嵌套" class="headerlink" title="代码示例：装饰器的层层嵌套"></a><strong>代码示例：装饰器的层层嵌套</strong></h5><blockquote><p><strong>场景</strong>：从一个文件中，以高效缓冲的方式，读取一个被序列化后的Java对象。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.*;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 假设我们有一个名为 "user.ser" 的文件，存储了一个User对象</span></span><br><span class="line">        </span><br><span class="line">        <span class="comment">// 这是一个典型的装饰器模式应用</span></span><br><span class="line">        <span class="keyword">try</span> (</span><br><span class="line">            <span class="comment">// 1. 最内层：节点流，直接连接数据源文件</span></span><br><span class="line">            <span class="type">FileInputStream</span> <span class="variable">fileIn</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">FileInputStream</span>(<span class="string">"user.ser"</span>);</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 2. 中间层：处理流，为文件流增加“缓冲”功能，提升性能</span></span><br><span class="line">            <span class="type">BufferedInputStream</span> <span class="variable">bufferedIn</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedInputStream</span>(fileIn);</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 3. 最外层：处理流，为缓冲流增加“对象反序列化”功能</span></span><br><span class="line">            <span class="type">ObjectInputStream</span> <span class="variable">objectIn</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">ObjectInputStream</span>(bufferedIn)</span><br><span class="line">        ) {</span><br><span class="line">            <span class="comment">// 最终，我们通过功能最强大的最外层流进行操作</span></span><br><span class="line">            <span class="comment">// User user = (User) objectIn.readObject();</span></span><br><span class="line">            System.out.println(<span class="string">"I/O流已成功按装饰器模式构建。"</span>);</span><br><span class="line">            System.out.println(<span class="string">"操作顺序: ObjectInputStream -&gt; BufferedInputStream -&gt; FileInputStream -&gt; 文件"</span>);</span><br><span class="line">            </span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            <span class="comment">// try-with-resources 语句会自动按相反的顺序关闭所有流，无需手动操作</span></span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p>这种设计使得Java<br>I/O体系既灵活又可扩展。当需要新功能时，只需创建一个新的处理流（装饰器）即可，而无需修改现有的任何流类。</p><hr><h3 id="4-2-文件操作：从-传统File-到-现代NIO-2"><a href="#4-2-文件操作：从-传统File-到-现代NIO-2" class="headerlink" title="4.2 文件操作：从 传统File 到 现代NIO.2"></a><strong>4.2 文件操作：从 传统<code>File</code> 到 现代<code>NIO.2</code></strong></h3><p>在理解了I/O流的分类和装饰器设计模式后，我们将聚焦于I/O操作的一个核心应用——<strong>文件操作</strong>。这包括如何创建、删除、重命名文件和目录，以及如何读取它们的属性。Java为此提供了两代API，我们将对比学习，并重点掌握现代化的解决方案。</p><h4 id="4-2-1-传统方式：java-io-File-类"><a href="#4-2-1-传统方式：java-io-File-类" class="headerlink" title="4.2.1 传统方式：java.io.File 类"></a><strong>4.2.1 传统方式：<code>java.io.File</code> 类</strong></h4><p><code>File</code>类是Java早期用于表示文件系统中的一个文件或目录路径的抽象。它可以用于执行创建、删除、重命名等操作，但其设计存在一些固有缺陷，因此在现代Java开发中已不被推荐作为首选。</p><h5 id="核心用途与场景"><a href="#核心用途与场景" class="headerlink" title="核心用途与场景"></a><strong>核心用途与场景</strong></h5><p>在维护旧项目或使用一些尚未升级到NIO.2的老旧第三方库时，我们仍然会遇到<code>File</code>类。</p><h5 id="常用方法速查表"><a href="#常用方法速查表" class="headerlink" title="常用方法速查表"></a><strong>常用方法速查表</strong></h5><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>boolean exists()</code></td><td align="left">检查文件或目录是否存在。</td></tr><tr><td align="left"><code>boolean createNewFile()</code></td><td align="left">创建一个新文件。</td></tr><tr><td align="left"><code>boolean mkdir()</code> / <code>mkdirs()</code></td><td align="left">创建单级/多级目录。</td></tr><tr><td align="left"><code>boolean delete()</code></td><td align="left">删除文件或<strong>空</strong>目录。</td></tr><tr><td align="left"><code>boolean renameTo(File dest)</code></td><td align="left">重命名或移动文件。</td></tr><tr><td align="left"><code>String getName()</code> / <code>getAbsolutePath()</code></td><td align="left">获取名称/绝对路径。</td></tr><tr><td align="left"><code>long length()</code></td><td align="left">获取文件大小（字节）。</td></tr><tr><td align="left"><code>boolean isDirectory()</code> / <code>isFile()</code></td><td align="left">判断是目录还是文件。</td></tr><tr><td align="left"><code>File[] listFiles()</code></td><td align="left">列出目录下的文件和子目录。</td></tr></tbody></table><h5 id="设计缺陷与痛点"><a href="#设计缺陷与痛点" class="headerlink" title="设计缺陷与痛点"></a><strong>设计缺陷与痛点</strong></h5><ol><li><strong>错误处理不友好</strong>：许多关键操作（如<code>delete()</code>,<br><code>renameTo()</code>）在失败时仅返回<code>false</code>，而<strong>不会抛出异常</strong>。这使得我们无法得知失败的具体原因（是权限不足？文件被占用？还是其他问题？），给可靠的错误处理带来了巨大困难。</li><li><strong>功能有限</strong>：原生不支持符号链接等现代文件系统特性，也无法方便地访问和修改文件元数据（如权限、所有者等）。</li><li><strong>无力处理非空目录</strong>：<code>delete()</code>方法只能删除文件或空目录，要删除整个目录树需要自己编写复杂的递归逻辑。</li></ol><h4 id="4-2-2-现代实践-java-nio-file-包-“NIO-2”"><a href="#4-2-2-现代实践-java-nio-file-包-“NIO-2”" class="headerlink" title="4.2.2 [现代实践] java.nio.file 包 (“NIO.2”)"></a><strong>4.2.2 [现代实践] <code>java.nio.file</code> 包 (“NIO.2”)</strong></h4><p>自Java<br>7起，NIO.2的引入为文件系统操作带来了革命性的变化。它以<code>Path</code>接口为核心，通过<code>Paths</code>和<code>Files</code>两个强大的工具类，提供了功能更全面、设计更优良、错误处理更明确的现代文件操作API。</p><ul><li><strong>核心优势</strong>：<ul><li><strong>明确的异常处理</strong>：所有操作在失败时都会抛出具体的<code>IOException</code>，让问题无处遁形。</li><li><strong>强大的功能</strong>：原生支持符号链接、文件属性视图、文件系统监视等高级功能。</li><li><strong>高效的API</strong>：提供了大量便捷、高效的静态方法，可以用一行代码完成过去需要一个方法块才能实现的功能。</li></ul></li></ul><h5 id="代码实战：Files-工具类的强大功能"><a href="#代码实战：Files-工具类的强大功能" class="headerlink" title="代码实战：Files 工具类的强大功能"></a><strong>代码实战：<code>Files</code> 工具类的强大功能</strong></h5><p>下面通过几个核心场景，对比展示NIO.2相比于传统<code>File</code>类的巨大优势。</p><h6 id="场景一：文件的创建与读写"><a href="#场景一：文件的创建与读写" class="headerlink" title="场景一：文件的创建与读写"></a><strong>场景一：文件的创建与读写</strong></h6><blockquote><p><strong>需求</strong>：创建一个文本文件，向其中写入内容，然后再读取出来。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"><span class="keyword">import</span> java.nio.file.Files;</span><br><span class="line"><span class="keyword">import</span> java.nio.file.Path;</span><br><span class="line"><span class="keyword">import</span> java.nio.file.Paths;</span><br><span class="line"><span class="keyword">import</span> java.nio.file.StandardOpenOption;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">Path</span> <span class="variable">filepath</span> <span class="operator">=</span> Paths.get(<span class="string">"poem.txt"</span>);</span><br><span class="line">        <span class="keyword">try</span> {</span><br><span class="line">            <span class="comment">// 1. 使用Paths.get()创建Path对象，这是现代文件路径的表示方式</span></span><br><span class="line">            <span class="type">Path</span> <span class="variable">filePath</span> <span class="operator">=</span> Paths.get(<span class="string">"poem.txt"</span>);</span><br><span class="line">            <span class="comment">// 2. 写入文件（如果文件不存在则创建，如果存在则追加内容）</span></span><br><span class="line">            <span class="comment">// Files.writeString() 是Java 11+的方法，极其方便</span></span><br><span class="line">            <span class="type">String</span> <span class="variable">contentToWrite</span> <span class="operator">=</span> <span class="string">"这是用NIO.2写入的第一行。\n"</span>;</span><br><span class="line">            <span class="comment">// 1.READ：以只读方式打开文件。</span></span><br><span class="line">            <span class="comment">// 2.WRITE：以写入方式打开文件。</span></span><br><span class="line">            <span class="comment">// 3.APPEND：如果文件已存在，则将数据追加到文件末尾（而不是覆盖）。</span></span><br><span class="line">            <span class="comment">// 4.TRUNCATE_EXISTING：如果文件已存在，则将其长度截断为 0（即清空文件内容）。</span></span><br><span class="line">            <span class="comment">// 5.CREATE：如果文件不存在，则创建新文件。</span></span><br><span class="line">            <span class="comment">// 6.CREATE_NEW：如果文件已存在，则抛出异常；否则创建新文件。</span></span><br><span class="line">            <span class="comment">// 7.DELETE_ON_CLOSE：在关闭文件时尝试删除该文件。</span></span><br><span class="line">            <span class="comment">// 8.SPARSE：提示系统创建一个稀疏文件（仅在支持稀疏文件的文件系统上有效）。</span></span><br><span class="line">            <span class="comment">// 9.SYNC：每次更新文件内容或元数据时都同步写入磁盘。</span></span><br><span class="line">            <span class="comment">// 10.DSYNC：每次更新文件内容时都同步写入磁盘，但不包括元数据。</span></span><br><span class="line">            Files.writeString(filePath, contentToWrite, StandardOpenOption.CREATE, StandardOpenOption.APPEND);</span><br><span class="line">            <span class="type">String</span> <span class="variable">secondLine</span> <span class="operator">=</span> <span class="string">"这是追加的第二行。\n"</span>;</span><br><span class="line">            Files.writeString(filePath, secondLine, StandardOpenOption.APPEND);</span><br><span class="line">            System.out.println(<span class="string">"文件 '"</span> + filePath.getFileName() + <span class="string">"' 写入完成。"</span>);</span><br><span class="line">            <span class="comment">// 3. 读取文件所有行到List中</span></span><br><span class="line">            System.out.println(<span class="string">"\n--- 读取文件内容 ---"</span>);</span><br><span class="line">            List&lt;String&gt; lines = Files.readAllLines(filePath);</span><br><span class="line">            lines.forEach(System.out::println);</span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            <span class="comment">// NIO.2的错误处理非常明确</span></span><br><span class="line">            System.err.println(<span class="string">"文件操作失败: "</span> + e.getMessage());</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="场景二：文件与目录的复制"><a href="#场景二：文件与目录的复制" class="headerlink" title="场景二：文件与目录的复制"></a><strong>场景二：文件与目录的复制</strong></h6><blockquote><p><strong>需求</strong>：将一个文件复制到另一个位置，并将一个完整的目录（包含子目录和文件）复制到新位置。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"><span class="keyword">import</span> java.nio.file.*;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Stream;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> <span class="keyword">throws</span> IOException {</span><br><span class="line">        <span class="comment">// --- 1. 复制单个文件 ---</span></span><br><span class="line">        <span class="type">Path</span> <span class="variable">sourceFile</span> <span class="operator">=</span> Paths.get(<span class="string">"source.txt"</span>);</span><br><span class="line">        <span class="type">Path</span> <span class="variable">destFile</span> <span class="operator">=</span> Paths.get(<span class="string">"dest_folder/source_copy.txt"</span>);</span><br><span class="line">        </span><br><span class="line">        <span class="comment">// 准备源文件和目标目录</span></span><br><span class="line">        Files.createDirectories(destFile.getParent());</span><br><span class="line">        Files.writeString(sourceFile, <span class="string">"一些源数据"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 使用Files.copy，如果目标文件已存在则替换</span></span><br><span class="line">        Files.copy(sourceFile, destFile, StandardCopyOption.REPLACE_EXISTING);</span><br><span class="line">        System.out.println(<span class="string">"单个文件复制完成！"</span>);</span><br><span class="line"></span><br><span class="line"></span><br><span class="line">        <span class="comment">// --- 2. 递归复制整个目录 ---</span></span><br><span class="line">        <span class="type">Path</span> <span class="variable">sourceDir</span> <span class="operator">=</span> Paths.get(<span class="string">"my_app_v1"</span>);</span><br><span class="line">        <span class="type">Path</span> <span class="variable">destDir</span> <span class="operator">=</span> Paths.get(<span class="string">"my_app_v2"</span>);</span><br><span class="line">        </span><br><span class="line">        <span class="comment">// 准备源目录</span></span><br><span class="line">        Files.createDirectories(sourceDir.resolve(<span class="string">"config"</span>));</span><br><span class="line">        Files.writeString(sourceDir.resolve(<span class="string">"main.conf"</span>), <span class="string">"data"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 使用Stream API和Files.walk来递归复制</span></span><br><span class="line">        <span class="keyword">try</span> (Stream&lt;Path&gt; stream = Files.walk(sourceDir)) {</span><br><span class="line">            stream.forEach(sourcePath -&gt; {</span><br><span class="line">                <span class="keyword">try</span> {</span><br><span class="line">                    <span class="type">Path</span> <span class="variable">targetPath</span> <span class="operator">=</span> destDir.resolve(sourceDir.relativize(sourcePath));</span><br><span class="line">                    Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);</span><br><span class="line">                } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">                    System.err.println(<span class="string">"无法复制: "</span> + sourcePath);</span><br><span class="line">                }</span><br><span class="line">            });</span><br><span class="line">        }</span><br><span class="line">        System.out.println(<span class="string">"整个目录已成功复制！"</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>结论</strong>：在所有新的Java项目中，都应<strong>优先并坚持使用<code>java.nio.file</code>包</strong>进行文件和目录操作。它更安全、功能更强大、代码也更现代化。</p><hr><h4 id="4-2-3-java-nio-file-Files-核心方法速查表"><a href="#4-2-3-java-nio-file-Files-核心方法速查表" class="headerlink" title="4.2.3 java.nio.file.Files 核心方法速查表"></a><strong>4.2.3 <code>java.nio.file.Files</code> 核心方法速查表</strong></h4><p>好的，明白了。为了让速查表更加简洁、一目了然，我将移除“方法签名”列中的返回类型和修饰符，只保留核心的方法名和参数示意。</p><h5 id="1-文件和目录检查"><a href="#1-文件和目录检查" class="headerlink" title="1. 文件和目录检查"></a><strong>1. 文件和目录检查</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>exists(...)</code></td><td align="left">检查文件或目录是否存在。</td><td align="left">最常用的检查方法。</td></tr><tr><td align="left"><code>notExists(...)</code></td><td align="left">检查文件或目录是否<strong>不</strong>存在。</td><td align="left"><code>!Files.exists(path)</code> 的一个更具可读性的替代方案。</td></tr><tr><td align="left"><code>isDirectory(...)</code></td><td align="left">判断路径是否为目录。</td><td align="left"></td></tr><tr><td align="left"><code>isRegularFile(...)</code></td><td align="left">判断路径是否为普通文件。</td><td align="left"></td></tr><tr><td align="left"><code>isReadable(...)</code></td><td align="left">判断文件是否可读。</td><td align="left"></td></tr><tr><td align="left"><code>isWritable(...)</code></td><td align="left">判断文件是否可写。</td><td align="left"></td></tr><tr><td align="left"><code>isExecutable(...)</code></td><td align="left">判断文件是否可执行。</td><td align="left"></td></tr><tr><td align="left"><code>isSameFile(...)</code></td><td align="left">判断两个<code>Path</code>是否指向同一个文件。</td><td align="left">比<code>p1.equals(p2)</code>更可靠，因为它会处理符号链接等情况。</td></tr></tbody></table><h5 id="2-文件和目录创建"><a href="#2-文件和目录创建" class="headerlink" title="2. 文件和目录创建"></a><strong>2. 文件和目录创建</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>createFile(...)</code></td><td align="left">创建一个新文件。</td><td align="left">如果文件已存在，会抛出<code>FileAlreadyExistsException</code>。</td></tr><tr><td align="left"><code>createDirectory(...)</code></td><td align="left">创建一个新目录。</td><td align="left">只能创建单级目录，如果父目录不存在会抛出异常。</td></tr><tr><td align="left"><code>createDirectories(...)</code></td><td align="left"><strong>强烈推荐</strong>。创建多级目录。</td><td align="left">如果父目录不存在，会自动一并创建，非常方便。</td></tr><tr><td align="left"><code>createTempFile(...)</code></td><td align="left">在默认或指定位置创建一个临时文件。</td><td align="left">常用于需要临时存储数据的场景。</td></tr><tr><td align="left"><code>createTempDirectory(...)</code></td><td align="left">创建一个临时目录。</td><td align="left"></td></tr></tbody></table><h5 id="3-文件和目录删除"><a href="#3-文件和目录删除" class="headerlink" title="3. 文件和目录删除"></a><strong>3. 文件和目录删除</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>delete(...)</code></td><td align="left">删除一个文件或<strong>空</strong>目录。</td><td align="left">如果路径不存在，抛出<code>NoSuchFileException</code>。如果目录非空，抛出<code>DirectoryNotEmptyException</code>。</td></tr><tr><td align="left"><code>deleteIfExists(...)</code></td><td align="left">如果文件或目录存在，则删除它。</td><td align="left"><strong>推荐使用</strong>。比<code>delete()</code>更安全，因为它不会在文件不存在时抛出异常，只会返回<code>false</code>。</td></tr></tbody></table><h5 id="4-文件读-写操作（小文件）"><a href="#4-文件读-写操作（小文件）" class="headerlink" title="4. 文件读/写操作（小文件）"></a><strong>4. 文件读/写操作（小文件）</strong></h5><p>这些方法会将文件的全部内容一次性读入内存，非常便捷，但只适用于小文件。</p><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>readAllBytes(...)</code></td><td align="left">将文件的所有内容读取为一个字节数组。</td><td align="left"><strong>注意内存溢出（OOM）风险</strong>，不适用于大文件。</td></tr><tr><td align="left"><code>readAllLines(...)</code></td><td align="left">将文件的所有行读取到一个字符串列表中。</td><td align="left"><strong>注意内存溢出风险</strong>。默认使用UTF-8编码。</td></tr><tr><td align="left"><code>write(...)</code></td><td align="left">将一个字节数组或字符串集合写入文件。</td><td align="left">默认会覆盖已有文件。可使用<code>StandardOpenOption</code>指定追加、创建等。</td></tr><tr><td align="left"><code>writeString(...)</code></td><td align="left"><strong>[Java 11+]</strong> 将字符串写入文件。</td><td align="left">写入文本最简单的方式。</td></tr><tr><td align="left"><code>readString(...)</code></td><td align="left"><strong>[Java 11+]</strong> 读取文件内容为字符串。</td><td align="left">读取小文本文件最简单的方式。</td></tr></tbody></table><h5 id="5-文件读-写操作（大文件-流式处理）"><a href="#5-文件读-写操作（大文件-流式处理）" class="headerlink" title="5. 文件读/写操作（大文件/流式处理）"></a><strong>5. 文件读/写操作（大文件/流式处理）</strong></h5><p>当处理大文件时，应使用流式API，避免一次性将所有内容加载到内存。</p><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>newInputStream(...)</code></td><td align="left">打开一个文件，返回一个用于读取的<code>InputStream</code>。</td><td align="left"><strong>处理大文件的标准方式</strong>。获取流之后，需配合<code>try-with-resources</code>使用。</td></tr><tr><td align="left"><code>newOutputStream(...)</code></td><td align="left">打开或创建一个文件，返回一个用于写入的<code>OutputStream</code>。</td><td align="left">同上。</td></tr><tr><td align="left"><code>newBufferedReader(...)</code></td><td align="left">打开一个文件，返回一个用于读取文本的<code>BufferedReader</code>。</td><td align="left">提供了高效的<code>readLine()</code>方法，是<strong>读取大文本文件</strong>的首选。</td></tr><tr><td align="left"><code>newBufferedWriter(...)</code></td><td align="left">打开或创建一个文件，返回一个<code>BufferedWriter</code>。</td><td align="left"><strong>写入大文本文件</strong>的首选。</td></tr><tr><td align="left"><code>lines(...)</code></td><td align="left"><strong>[Java 8+]</strong> 返回一个由文件所有行组成的<code>Stream</code>。</td><td align="left"><strong>懒加载</strong>，非常适合用函数式编程风格处理大文本文件，内存占用极小。</td></tr></tbody></table><h5 id="6-复制与移动"><a href="#6-复制与移动" class="headerlink" title="6. 复制与移动"></a><strong>6. 复制与移动</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>copy(...)</code></td><td align="left">复制一个文件或目录。</td><td align="left">默认情况下，如果目标文件已存在会失败。需使用<code>StandardCopyOption.REPLACE_EXISTING</code>来覆盖。复制目录时，只复制目录本身，不复制其内容。</td></tr><tr><td align="left"><code>move(...)</code></td><td align="left">移动或重命名一个文件。</td><td align="left">可以指定<code>StandardCopyOption.ATOMIC_MOVE</code>来保证操作的原子性。</td></tr></tbody></table><h5 id="7-目录遍历-Stream-API"><a href="#7-目录遍历-Stream-API" class="headerlink" title="7. 目录遍历 (Stream API)"></a><strong>7. 目录遍历 (Stream API)</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>list(...)</code></td><td align="left"><strong>[Java 8+]</strong> 返回一个表示目录下所有条目（不含子目录）的<code>Stream</code>。</td><td align="left"><strong>非递归</strong>，只遍历当前层级。</td></tr><tr><td align="left"><code>walk(...)</code></td><td align="left"><strong>[Java 8+]</strong> 返回一个从指定路径开始、<strong>递归遍历</strong>所有文件和目录的<code>Stream</code>。</td><td align="left">功能强大，可以配合<code>filter</code>等操作轻松实现文件查找等功能。</td></tr><tr><td align="left"><code>find(...)</code></td><td align="left"><strong>[Java 8+]</strong> 功能类似<code>walk</code>，但可以额外传入一个匹配器来筛选路径。</td><td align="left">比<code>walk</code>后接<code>filter</code>更高效。</td></tr></tbody></table><hr><h3 id="4-3-核心I-O处理流搭配使用"><a href="#4-3-核心I-O处理流搭配使用" class="headerlink" title="4.3 核心I/O处理流搭配使用"></a><strong>4.3 核心I/O处理流搭配使用</strong></h3><p>在掌握了文件和目录的表示方法后，我们回到流本身，聚焦于如何通过组合不同的处理流，来高效、灵活地读写文件内容。</p><h4 id="4-3-1-缓冲流-BufferedInputStream-BufferedReader-性能优化的基石"><a href="#4-3-1-缓冲流-BufferedInputStream-BufferedReader-性能优化的基石" class="headerlink" title="4.3.1 缓冲流 (BufferedInputStream / BufferedReader): 性能优化的基石"></a><strong>4.3.1 缓冲流 (<code>BufferedInputStream</code> / <code>BufferedReader</code>): 性能优化的基石</strong></h4><h5 id="面试题引入"><a href="#面试题引入" class="headerlink" title="面试题引入"></a><strong>面试题引入</strong></h5><blockquote><p>“为什么我们在进行文件IO时，总是推荐使用缓冲流？它的原理是什么？”</p></blockquote><h5 id="核心原理解析"><a href="#核心原理解析" class="headerlink" title="核心原理解析"></a><strong>核心原理解析</strong></h5><p>计算机中，对磁盘或网络的I/O操作（系统调用）相比于内存操作，是极其缓慢的。如果我们直接使用<code>FileInputStream</code>的<code>read()</code>方法逐字节读取文件，那么每读取一个字节，就可能触发一次昂贵的物理磁盘访问。</p><p><strong>缓冲流（Buffered<br>Stream）正是为解决这一性能瓶颈而生。它的原理是在内部维护一个内存缓冲区</strong>（一个字节或字符数组，默认大小通常为8192）。</p><ul><li><strong>读取时</strong>：<code>BufferedInputStream</code>会一次性从磁盘读取一大块数据（例如8KB）填充到内部缓冲区。之后你再调用<code>read()</code>方法时，它会直接从高速的内存缓冲区中返回数据，直到缓冲区耗尽，才会再次触发下一次对磁盘的大块读取。</li><li><strong>写入时</strong>：<code>BufferedOutputStream</code>会先将你写入的数据存放在缓冲区，直到缓冲区满了，或者你手动调用<code>flush()</code>方法，它才会将整个缓冲区的数据一次性写入磁盘。</li></ul><p><strong>结论</strong>：缓冲流通过**“化零为整”**的策略，用一次大的I/O操作替代了无数次小的I/O操作，<strong>极大地减少了与底层物理设备的交互次数</strong>，从而实现性能的飞跃。</p><h5 id="代码实战：文件复制性能对比"><a href="#代码实战：文件复制性能对比" class="headerlink" title="代码实战：文件复制性能对比"></a><strong>代码实战：文件复制性能对比</strong></h5><blockquote><p><strong>场景</strong>：复制一个文件，对比使用缓冲流和不使用缓冲流的耗时。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.*;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 为了测试，先创建一个较大的文件</span></span><br><span class="line">    <span class="keyword">static</span> {</span><br><span class="line">        <span class="keyword">try</span> (<span class="type">FileWriter</span> <span class="variable">writer</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">FileWriter</span>(<span class="string">"source_file.txt"</span>)) {</span><br><span class="line">            <span class="keyword">for</span> (<span class="type">int</span> <span class="variable">i</span> <span class="operator">=</span> <span class="number">0</span>; i &lt; <span class="number">100000</span>; i++) {</span><br><span class="line">                writer.write(<span class="string">"abcdefghij\n"</span>);</span><br><span class="line">            }</span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 方案一：不使用缓冲流，逐字节复制</span></span><br><span class="line">        <span class="type">long</span> <span class="variable">start1</span> <span class="operator">=</span> System.currentTimeMillis();</span><br><span class="line">        copyFileWithoutBuffer();</span><br><span class="line">        <span class="type">long</span> <span class="variable">end1</span> <span class="operator">=</span> System.currentTimeMillis();</span><br><span class="line">        System.out.println(<span class="string">"不使用缓冲流耗时: "</span> + (end1 - start1) + <span class="string">" ms"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 方案二：使用缓冲流</span></span><br><span class="line">        <span class="type">long</span> <span class="variable">start2</span> <span class="operator">=</span> System.currentTimeMillis();</span><br><span class="line">        copyFileWithBuffer();</span><br><span class="line">        <span class="type">long</span> <span class="variable">end2</span> <span class="operator">=</span> System.currentTimeMillis();</span><br><span class="line">        System.out.println(<span class="string">"使用缓冲流耗时: "</span> + (end2 - start2) + <span class="string">" ms"</span>);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">copyFileWithoutBuffer</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">try</span> (<span class="type">FileInputStream</span> <span class="variable">fis</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">FileInputStream</span>(<span class="string">"source_file.txt"</span>);</span><br><span class="line">             <span class="type">FileOutputStream</span> <span class="variable">fos</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">FileOutputStream</span>(<span class="string">"copy_no_buffer.txt"</span>)) {</span><br><span class="line">            <span class="type">int</span> byteData;</span><br><span class="line">            <span class="keyword">while</span> ((byteData = fis.read()) != -<span class="number">1</span>) {</span><br><span class="line">                fos.write(byteData);</span><br><span class="line">            }</span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">copyFileWithBuffer</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">try</span> (<span class="type">BufferedInputStream</span> <span class="variable">bis</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedInputStream</span>(<span class="keyword">new</span> <span class="title class_">FileInputStream</span>(<span class="string">"source_file.txt"</span>));</span><br><span class="line">             <span class="type">BufferedOutputStream</span> <span class="variable">bos</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedOutputStream</span>(<span class="keyword">new</span> <span class="title class_">FileOutputStream</span>(<span class="string">"copy_with_buffer.txt"</span>))) {</span><br><span class="line">            <span class="type">int</span> byteData;</span><br><span class="line">            <span class="keyword">while</span> ((byteData = bis.read()) != -<span class="number">1</span>) {</span><br><span class="line">                bos.write(byteData);</span><br><span class="line">            }</span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>运行结果（示例）</strong>：</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">不使用缓冲流耗时: 12629 ms</span><br><span class="line">使用缓冲流耗时: 60 ms</span><br></pre></td></tr></tbody></table></figure><p>可以看到，性能差异是数量级的。因此，在进行任何文件I/O时，<strong>使用缓冲流包装节点流都应成为一种编程习惯</strong>。</p><h4 id="4-3-2-转换流-InputStreamReader-OutputStreamWriter-字节与字符的桥梁"><a href="#4-3-2-转换流-InputStreamReader-OutputStreamWriter-字节与字符的桥梁" class="headerlink" title="4.3.2 转换流 (InputStreamReader / OutputStreamWriter): 字节与字符的桥梁"></a><strong>4.3.2 转换流 (<code>InputStreamReader</code> / <code>OutputStreamWriter</code>): 字节与字符的桥梁</strong></h4><h5 id="核心用途"><a href="#核心用途" class="headerlink" title="核心用途"></a><strong>核心用途</strong></h5><p>转换流的核心作用是<strong>适配器</strong>，它能将<strong>字节流</strong>转换为<strong>字符流</strong>，并在转换过程中处理<strong>字符编码</strong>。</p><h5 id="应用场景"><a href="#应用场景" class="headerlink" title="应用场景"></a><strong>应用场景</strong></h5><p>当你需要读写的文本文件编码与当前操作系统的默认编码不一致时，必须使用转换流来显式指定正确的编码，否则就会产生乱码。</p><ul><li><strong>读取</strong>：例如，在UTF-8的服务器上读取一个由Windows记事本（默认GBK编码）生成的中文文件。</li><li><strong>写入</strong>：例如，无论程序运行在什么系统上，都希望统一生成UTF-8编码的配置文件。</li></ul><h5 id="代码示例：读取GBK文件并转存为UTF-8"><a href="#代码示例：读取GBK文件并转存为UTF-8" class="headerlink" title="代码示例：读取GBK文件并转存为UTF-8"></a><strong>代码示例：读取GBK文件并转存为UTF-8</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.*;</span><br><span class="line"><span class="keyword">import</span> java.nio.charset.StandardCharsets;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 假设 gbk_file.txt 是一个GBK编码的文件，内容为“你好，世界”</span></span><br><span class="line">        <span class="comment">// 我们可以先手动创建一个这样的文件用于测试</span></span><br><span class="line">        </span><br><span class="line">        <span class="keyword">try</span> (</span><br><span class="line">            <span class="comment">// 1. 创建一个输入字节流连接到源文件</span></span><br><span class="line">            <span class="type">FileInputStream</span> <span class="variable">fis</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">FileInputStream</span>(<span class="string">"gbk_file.txt"</span>);</span><br><span class="line">            <span class="comment">// 2. 使用转换流InputStreamReader，指定用GBK编码来解码字节流</span></span><br><span class="line">            <span class="type">InputStreamReader</span> <span class="variable">isr</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">InputStreamReader</span>(fis, <span class="string">"GBK"</span>);</span><br><span class="line">            <span class="comment">// 3. 为了效率，再套上一个BufferedReader</span></span><br><span class="line">            <span class="type">BufferedReader</span> <span class="variable">br</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedReader</span>(isr);</span><br><span class="line"></span><br><span class="line">            <span class="comment">// 4. 创建一个输出字节流连接到目标文件</span></span><br><span class="line">            <span class="type">FileOutputStream</span> <span class="variable">fos</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">FileOutputStream</span>(<span class="string">"utf8_file.txt"</span>);</span><br><span class="line">            <span class="comment">// 5. 使用转换流OutputStreamWriter，指定用UTF-8编码来编码字符流</span></span><br><span class="line">            <span class="type">OutputStreamWriter</span> <span class="variable">osw</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">OutputStreamWriter</span>(fos, StandardCharsets.UTF_8);</span><br><span class="line">            <span class="comment">// 6. 同样，套上BufferedWriter</span></span><br><span class="line">            <span class="type">BufferedWriter</span> <span class="variable">bw</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedWriter</span>(osw)</span><br><span class="line">        ) {</span><br><span class="line">            String line;</span><br><span class="line">            <span class="keyword">while</span> ((line = br.readLine()) != <span class="literal">null</span>) {</span><br><span class="line">                bw.write(line);</span><br><span class="line">                bw.newLine();</span><br><span class="line">            }</span><br><span class="line">            System.out.println(<span class="string">"编码转换完成！"</span>);</span><br><span class="line"></span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="4-3-3-对象流-ObjectInputStream-ObjectOutputStream-对象的序列化与反序列化"><a href="#4-3-3-对象流-ObjectInputStream-ObjectOutputStream-对象的序列化与反序列化" class="headerlink" title="4.3.3 对象流 (ObjectInputStream / ObjectOutputStream): 对象的序列化与反序列化"></a><strong>4.3.3 对象流 (<code>ObjectInputStream</code> / <code>ObjectOutputStream</code>): 对象的序列化与反序列化</strong></h4><h5 id="面试题引入-1"><a href="#面试题引入-1" class="headerlink" title="面试题引入"></a><strong>面试题引入</strong></h5><blockquote><p>“什么是Java的序列化？<code>transient</code>关键字和<code>serialVersionUID</code>有什么作用？”</p></blockquote><h5 id="核心概念"><a href="#核心概念" class="headerlink" title="核心概念"></a><strong>核心概念</strong></h5><ul><li><strong>序列化 (Serialization)</strong>：将一个Java<strong>对象</strong>的状态转换为<strong>字节序列</strong>的过程。</li><li><strong>反序列化 (Deserialization)</strong>：从字节序列中恢复并重建Java对象的过程。</li><li><strong>用途</strong>：实现对象的<strong>持久化</strong>（保存到文件或数据库）和<strong>网络传输</strong>。</li></ul><h5 id="关键知识点"><a href="#关键知识点" class="headerlink" title="关键知识点"></a><strong>关键知识点</strong></h5><ol><li><strong><code>Serializable</code><br>接口</strong>：一个类必须实现这个<strong>标记接口</strong>（没有任何方法），其对象才能被序列化。它像一个“通行证”，告诉JVM这个类的对象可以被“扁平化”为字节。</li><li><strong><code>transient</code><br>关键字</strong>：用于修饰字段。被<code>transient</code>修饰的字段将被<strong>排除</strong>在序列化过程之外，不会被写入字节流。反序列化后，该字段的值会是其类型的默认值（如对象为<code>null</code>，<code>int</code>为0）。常用于密码、安全令牌、缓存数据等敏感或无需持久化的字段。</li><li><strong><code>serialVersionUID</code><br>(面试核心)</strong>：这是一个用于标识可序列化类版本的<code>long</code>型常量。<ul><li><strong>作用</strong>：在反序列化时，JVM会比较字节流中的<code>serialVersionUID</code>和当前加载的类中的<code>serialVersionUID</code>。如果两者不一致，会抛出<code>InvalidClassException</code>，以防止因类版本不兼容导致的数据错乱。</li><li><strong>最佳实践</strong>：<strong>强烈建议</strong>所有可序列化类都<strong>显式声明</strong>一个<code>private static final long serialVersionUID</code>。如果不声明，编译器会自动生成一个，但这个自动生成的值对类的结构非常敏感（如增删字段），稍有改动就会变化，导致旧的序列化数据无法被新版类反序列化。</li></ul></li></ol><h5 id="代码实战：将User对象持久化到文件"><a href="#代码实战：将User对象持久化到文件" class="headerlink" title="代码实战：将User对象持久化到文件"></a><strong>代码实战：将User对象持久化到文件</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.*;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 1. User类必须实现Serializable接口</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">User</span> <span class="keyword">implements</span> <span class="title class_">Serializable</span> {</span><br><span class="line">    <span class="comment">// 2. 强烈建议显式声明serialVersionUID</span></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> <span class="type">long</span> <span class="variable">serialVersionUID</span> <span class="operator">=</span> <span class="number">1L</span>;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> String name;</span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> age;</span><br><span class="line">    <span class="comment">// 3. 使用transient关键字，密码将不会被序列化</span></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">transient</span> String password; </span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">User</span><span class="params">(String name, <span class="type">int</span> age, String password)</span> {</span><br><span class="line">        <span class="built_in">this</span>.name = name;</span><br><span class="line">        <span class="built_in">this</span>.age = age;</span><br><span class="line">        <span class="built_in">this</span>.password = password;</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">toString</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> <span class="string">"User{name='"</span> + name + <span class="string">"', age="</span> + age + <span class="string">", password='"</span> + password + <span class="string">"'}"</span>;</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">File</span> <span class="variable">userFile</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">File</span>(<span class="string">"user.ser"</span>);</span><br><span class="line">        <span class="type">User</span> <span class="variable">userToWrite</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"Alice"</span>, <span class="number">25</span>, <span class="string">"mySecret123"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// --- 序列化过程 ---</span></span><br><span class="line">        <span class="keyword">try</span> (<span class="type">ObjectOutputStream</span> <span class="variable">oos</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">ObjectOutputStream</span>(<span class="keyword">new</span> <span class="title class_">FileOutputStream</span>(userFile))) {</span><br><span class="line">            oos.writeObject(userToWrite);</span><br><span class="line">            System.out.println(<span class="string">"原始对象: "</span> + userToWrite);</span><br><span class="line">            System.out.println(<span class="string">"对象已成功序列化到文件 "</span> + userFile.getName());</span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"\n--- 反序列化过程 ---"</span>);</span><br><span class="line">        <span class="keyword">try</span> (<span class="type">ObjectInputStream</span> <span class="variable">ois</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">ObjectInputStream</span>(<span class="keyword">new</span> <span class="title class_">FileInputStream</span>(userFile))) {</span><br><span class="line">            <span class="type">User</span> <span class="variable">userToRead</span> <span class="operator">=</span> (User) ois.readObject();</span><br><span class="line">            System.out.println(<span class="string">"从文件反序列化出的对象: "</span> + userToRead);</span><br><span class="line">            <span class="comment">// 注意：password字段因为是transient，所以变成了null</span></span><br><span class="line">        } <span class="keyword">catch</span> (IOException | ClassNotFoundException e) {</span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><hr><h3 id="4-4-实战进阶-简化I-O操作的第三方库"><a href="#4-4-实战进阶-简化I-O操作的第三方库" class="headerlink" title="4.4 [实战进阶] 简化I/O操作的第三方库"></a><strong>4.4 [实战进阶] 简化I/O操作的第三方库</strong></h3><h4 id="4-4-1-面试题引入与核心思想"><a href="#4-4-1-面试题引入与核心思想" class="headerlink" title="4.4.1 面试题引入与核心思想"></a><strong>4.4.1 面试题引入与核心思想</strong></h4><h5 id="面试题引入-2"><a href="#面试题引入-2" class="headerlink" title="面试题引入"></a><strong>面试题引入</strong></h5><blockquote><p>“在实际项目中，你还会频繁地手动编写<code>try-with-resources</code>和循环来复制一个文件吗？有没有更便捷、更专业的处理方式？”</p></blockquote><h5 id="核心思想：为什么需要第三方库？"><a href="#核心思想：为什么需要第三方库？" class="headerlink" title="核心思想：为什么需要第三方库？"></a><strong>核心思想：为什么需要第三方库？</strong></h5><p>虽然我们必须深入理解JDK原生I/O流的体系，因为它是所有I/O操作的基础，也是排查底层问题的关键。但在真实的、快节奏的商业项目开发中，对于那些反复出现的通用I/O任务（如读写文件、复制目录等），如果每次都手动编写原始的流处理代码，会存在几个明显的问题：</p><ol><li><strong>代码冗长</strong>：完成一个简单的任务需要创建多个流对象、编写循环、处理异常，代码显得非常臃肿。</li><li><strong>容易出错</strong>：手动管理资源和编写逻辑，稍有不慎就可能导致资源未关闭、缓冲区处理不当等难以察觉的BUG。</li><li><strong>效率低下</strong>：重复编写同样功能的代码，是对开发时间的浪费。</li></ol><p>因此，在专业开发领域，我们遵循“<strong>不重复造轮子 (Don’t Reinvent the<br>Wheel)</strong>”的原则。对于I/O操作，社区已经为我们提供了极其优秀、经过数万个项目验证的“轮子”——<strong>第三方工具库</strong>。</p><h4 id="4-4-2-主流库介绍：Apache-Commons-IO"><a href="#4-4-2-主流库介绍：Apache-Commons-IO" class="headerlink" title="4.4.2 主流库介绍：Apache Commons IO"></a><strong>4.4.2 主流库介绍：<code>Apache Commons IO</code></strong></h4><p><code>Apache Commons IO</code><br>是Java生态中处理I/O的<strong>事实上的行业标准</strong>。它是一个稳定、可靠、功能极其丰富的工具包，几乎是所有Java项目的必备依赖之一。它的核心价值在于，将那些繁琐的I/O样板代码封装成了简单、强大的一行代码。</p><ul><li><strong>如何引入</strong>： 在Maven项目中，只需在<code>pom.xml</code>中添加以下依赖即可：<figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>commons-io<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>commons-io<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">version</span>&gt;</span>2.14.0<span class="tag">&lt;/<span class="name">version</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure></li><li><strong>核心工具类</strong>：<ul><li><strong><code>FileUtils</code></strong>：面向文件（<code>File</code>对象）和目录的操作。</li><li><strong><code>IOUtils</code></strong>：面向流（<code>InputStream</code>, <code>OutputStream</code>等）的操作。</li></ul></li></ul><h4 id="4-4-3-代码实战：JDK原生写法-vs-Commons-IO-一行代码"><a href="#4-4-3-代码实战：JDK原生写法-vs-Commons-IO-一行代码" class="headerlink" title="4.4.3 代码实战：JDK原生写法 vs. Commons IO 一行代码"></a><strong>4.4.3 代码实战：JDK原生写法 vs. <code>Commons IO</code> 一行代码</strong></h4><p>下面，我们将通过几个鲜明的“<strong>之前 vs. 之后</strong>”的对比，来感受<code>Commons IO</code>的威力。</p><h5 id="场景一：读取整个文件到字符串"><a href="#场景一：读取整个文件到字符串" class="headerlink" title="场景一：读取整个文件到字符串"></a><strong>场景一：读取整个文件到字符串</strong></h5><ul><li><strong>JDK 原生写法</strong><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.BufferedReader;</span><br><span class="line"><span class="keyword">import</span> java.io.FileReader;</span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> String <span class="title function_">readFileWithJDK</span><span class="params">(String filePath)</span> <span class="keyword">throws</span> IOException {</span><br><span class="line">        <span class="type">StringBuilder</span> <span class="variable">content</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">StringBuilder</span>();</span><br><span class="line">        <span class="keyword">try</span> (<span class="type">BufferedReader</span> <span class="variable">reader</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedReader</span>(<span class="keyword">new</span> <span class="title class_">FileReader</span>(filePath))) {</span><br><span class="line">            String line;</span><br><span class="line">            <span class="keyword">while</span> ((line = reader.readLine()) != <span class="literal">null</span>) {</span><br><span class="line">                content.append(line).append(System.lineSeparator());</span><br><span class="line">            }</span><br><span class="line">        }</span><br><span class="line">        <span class="keyword">return</span> content.toString();</span><br><span class="line">    }</span><br><span class="line">    <span class="comment">// ... main方法调用 ...</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><strong>Commons IO 写法</strong><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> org.apache.commons.io.FileUtils;</span><br><span class="line"><span class="keyword">import</span> java.io.File;</span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"><span class="keyword">import</span> java.nio.charset.StandardCharsets;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> String <span class="title function_">readFileWithCommonsIO</span><span class="params">(String filePath)</span> <span class="keyword">throws</span> IOException {</span><br><span class="line">        <span class="comment">// 一行代码，搞定！内部已处理好所有流的打开和关闭。</span></span><br><span class="line">        <span class="keyword">return</span> FileUtils.readFileToString(<span class="keyword">new</span> <span class="title class_">File</span>(filePath), StandardCharsets.UTF_8);</span><br><span class="line">    }</span><br><span class="line">    <span class="comment">// ... main方法调用 ...</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="场景二：复制文件"><a href="#场景二：复制文件" class="headerlink" title="场景二：复制文件"></a><strong>场景二：复制文件</strong></h5><ul><li><strong>JDK 原生写法</strong><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.*;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">copyFileWithJDK</span><span class="params">(String src, String dest)</span> <span class="keyword">throws</span> IOException {</span><br><span class="line">        <span class="keyword">try</span> (<span class="type">InputStream</span> <span class="variable">in</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedInputStream</span>(<span class="keyword">new</span> <span class="title class_">FileInputStream</span>(src));</span><br><span class="line">             <span class="type">OutputStream</span> <span class="variable">out</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedOutputStream</span>(<span class="keyword">new</span> <span class="title class_">FileOutputStream</span>(dest))) {</span><br><span class="line">            <span class="type">byte</span>[] buffer = <span class="keyword">new</span> <span class="title class_">byte</span>[<span class="number">1024</span>];</span><br><span class="line">            <span class="type">int</span> length;</span><br><span class="line">            <span class="keyword">while</span> ((length = in.read(buffer)) &gt; <span class="number">0</span>) {</span><br><span class="line">                out.write(buffer, <span class="number">0</span>, length);</span><br><span class="line">            }</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">    <span class="comment">// ... main方法调用 ...</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><strong>Commons IO 写法</strong><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> org.apache.commons.io.FileUtils;</span><br><span class="line"><span class="keyword">import</span> java.io.File;</span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">copyFileWithCommonsIO</span><span class="params">(String src, String dest)</span> <span class="keyword">throws</span> IOException {</span><br><span class="line">        <span class="comment">// 同样是一行代码</span></span><br><span class="line">        FileUtils.copyFile(<span class="keyword">new</span> <span class="title class_">File</span>(src), <span class="keyword">new</span> <span class="title class_">File</span>(dest));</span><br><span class="line">    }</span><br><span class="line">    <span class="comment">// ... main方法调用 ...</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="场景三：递归删除目录"><a href="#场景三：递归删除目录" class="headerlink" title="场景三：递归删除目录"></a><strong>场景三：递归删除目录</strong></h5><ul><li><p><strong>JDK 原生写法</strong><br>JDK的<code>File.delete()</code>无法删除非空目录。要实现此功能，必须自己编写一个递归方法，先删除目录下的所有文件和子目录，最后再删除该目录本身，过程复杂且容易出错。</p></li><li><p><strong>Commons IO 写法</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line">  ```</span><br><span class="line"></span><br><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">import</span> org.apache.commons.io.FileUtils;</span><br><span class="line">    <span class="keyword">import</span> java.io.File;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">        <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">deleteDirWithCommonsIO</span><span class="params">(String dirPath)</span> <span class="keyword">throws</span> IOException {</span><br><span class="line">            <span class="comment">// 无论目录是否为空，一行代码安全删除</span></span><br><span class="line">            FileUtils.deleteDirectory(<span class="keyword">new</span> <span class="title class_">File</span>(dirPath));</span><br><span class="line">        }</span><br><span class="line">        <span class="comment">// ... main方法调用 ...</span></span><br><span class="line">    }</span><br></pre></td></tr></tbody></table></figure></li></ul><hr><h3 id="4-5-Apache-Commons-IO-核心方法速查表"><a href="#4-5-Apache-Commons-IO-核心方法速查表" class="headerlink" title="4.5 Apache Commons IO 核心方法速查表"></a><strong>4.5 <code>Apache Commons IO</code> 核心方法速查表</strong></h3><h4 id="FileUtils-类：面向文件和目录的操作"><a href="#FileUtils-类：面向文件和目录的操作" class="headerlink" title="FileUtils 类：面向文件和目录的操作"></a><strong><code>FileUtils</code> 类：面向文件和目录的操作</strong></h4><h5 id="1-读操作-Read-Operations"><a href="#1-读操作-Read-Operations" class="headerlink" title="1. 读操作 (Read Operations)"></a><strong>1. 读操作 (Read Operations)</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>String readFileToString(File file, Charset cs)</code></td><td align="left">**（最常用）**将整个文件内容读取为一个字符串。</td></tr><tr><td align="left"><code>List&lt;String&gt; readLines(File file, Charset cs)</code></td><td align="left">将文件的每一行读取到一个字符串列表 (<code>List&lt;String&gt;</code>) 中。</td></tr><tr><td align="left"><code>byte[] readFileToByteArray(File file)</code></td><td align="left">将整个文件内容读取为一个字节数组 (<code>byte[]</code>)。</td></tr></tbody></table><h5 id="2-写操作-Write-Operations"><a href="#2-写操作-Write-Operations" class="headerlink" title="2. 写操作 (Write Operations)"></a><strong>2. 写操作 (Write Operations)</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>void writeStringToFile(File file, String data, ...)</code></td><td align="left">**（最常用）**将一个字符串写入文件（会覆盖或追加）。</td></tr><tr><td align="left"><code>void writeLines(File file, Collection&lt;?&gt; lines, ...)</code></td><td align="left">将一个字符串集合（如<code>List</code>）逐行写入文件。</td></tr><tr><td align="left"><code>void writeByteArrayToFile(File file, byte[] data)</code></td><td align="left">将一个字节数组写入文件。</td></tr></tbody></table><h5 id="3-复制与移动-Copy-Move-Operations"><a href="#3-复制与移动-Copy-Move-Operations" class="headerlink" title="3. 复制与移动 (Copy &amp; Move Operations)"></a><strong>3. 复制与移动 (Copy &amp; Move Operations)</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>void copyFile(File src, File dest)</code></td><td align="left">**（常用）**复制一个文件到新位置。</td></tr><tr><td align="left"><code>void copyDirectory(File src, File dest)</code></td><td align="left">**（强大）**递归复制整个目录及其所有内容。</td></tr><tr><td align="left"><code>void copyFileToDirectory(File src, File destDir)</code></td><td align="left">将一个文件复制到指定的目录下。</td></tr><tr><td align="left"><code>void moveFile(File src, File dest)</code></td><td align="left">移动一个文件（本质上是“复制后删除”）。</td></tr><tr><td align="left"><code>void moveDirectory(File src, File dest)</code></td><td align="left">移动整个目录。</td></tr><tr><td align="left"><code>void moveToDirectory(File src, File destDir, ...)</code></td><td align="left">将文件或目录移动到指定的目录下。</td></tr></tbody></table><h5 id="4-删除与清空-Delete-Clean-Operations"><a href="#4-删除与清空-Delete-Clean-Operations" class="headerlink" title="4. 删除与清空 (Delete &amp; Clean Operations)"></a><strong>4. 删除与清空 (Delete &amp; Clean Operations)</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>void deleteDirectory(File dir)</code></td><td align="left">**（强大）**递归删除整个目录，无论其是否为空。</td></tr><tr><td align="left"><code>void cleanDirectory(File dir)</code></td><td align="left">清空一个目录下的所有内容，但<strong>不删除</strong>目录本身。</td></tr><tr><td align="left"><code>boolean forceDelete(File file)</code></td><td align="left">强制删除一个文件或目录。如果删除失败，会尝试多次。</td></tr></tbody></table><h5 id="5-状态检查与其它"><a href="#5-状态检查与其它" class="headerlink" title="5. 状态检查与其它"></a><strong>5. 状态检查与其它</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>long sizeOf(File file)</code></td><td align="left">获取一个文件的大小。</td></tr><tr><td align="left"><code>long sizeOfDirectory(File dir)</code></td><td align="left">**（常用）**递归计算整个目录的大小。</td></tr><tr><td align="left"><code>boolean isFileNewer(File file, ...)</code></td><td align="left">判断一个文件是否比另一个文件或指定时间戳更新。</td></tr></tbody></table><h4 id="IOUtils-类：面向流的操作"><a href="#IOUtils-类：面向流的操作" class="headerlink" title="IOUtils 类：面向流的操作"></a><strong><code>IOUtils</code> 类：面向流的操作</strong></h4><h5 id="1-读-转换操作-Read-Convert-Operations"><a href="#1-读-转换操作-Read-Convert-Operations" class="headerlink" title="1. 读/转换操作 (Read / Convert Operations)"></a><strong>1. 读/转换操作 (Read / Convert Operations)</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>String toString(InputStream in, Charset cs)</code></td><td align="left">**（最常用）**将一个<code>InputStream</code>或<code>Reader</code>的内容读取为一个字符串。</td></tr><tr><td align="left"><code>byte[] toByteArray(InputStream in)</code></td><td align="left">**（常用）**将一个<code>InputStream</code>或<code>Reader</code>的内容读取为一个字节数组。</td></tr><tr><td align="left"><code>InputStream toInputStream(String input, Charset cs)</code></td><td align="left">将一个<code>CharSequence</code>（如<code>String</code>）转换为一个<code>InputStream</code>。</td></tr><tr><td align="left"><code>List&lt;String&gt; readLines(InputStream in, Charset cs)</code></td><td align="left">将一个<code>InputStream</code>或<code>Reader</code>的内容按行读取到一个<code>List&lt;String&gt;</code>中。</td></tr></tbody></table><h5 id="2-写操作-Write-Operations-1"><a href="#2-写操作-Write-Operations-1" class="headerlink" title="2. 写操作 (Write Operations)"></a><strong>2. 写操作 (Write Operations)</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>void write(String data, OutputStream out, ...)</code></td><td align="left">将一个<code>String</code>或<code>byte[]</code>的内容写入到一个<code>OutputStream</code>或<code>Writer</code>中。</td></tr></tbody></table><h5 id="3-复制操作-Copy-Operations"><a href="#3-复制操作-Copy-Operations" class="headerlink" title="3. 复制操作 (Copy Operations)"></a><strong>3. 复制操作 (Copy Operations)</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>int copy(InputStream in, OutputStream out)</code></td><td align="left">**（最常用）**将一个<code>InputStream</code>的内容复制到一个<code>OutputStream</code>中，或将<code>Reader</code>复制到<code>Writer</code>。返回复制的字节/字符数。</td></tr><tr><td align="left"><code>long copyLarge(InputStream in, OutputStream out)</code></td><td align="left">用于复制大于2GB的超大流。</td></tr></tbody></table><h5 id="4-关闭操作-Close-Operations"><a href="#4-关闭操作-Close-Operations" class="headerlink" title="4. 关闭操作 (Close Operations)"></a><strong>4. 关闭操作 (Close Operations)</strong></h5><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>void closeQuietly(Closeable c)</code></td><td align="left"><strong>（历史著名）<strong>安静地关闭一个<code>Closeable</code>（如流），忽略所有异常。<strong>注意</strong>：在Java 7及之后，官方</strong>推荐使用<code>try-with-resources</code>语句</strong>来自动管理资源，此方法的必要性已大大降低，被视为一种过时的模式。</td></tr></tbody></table><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/6760.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/6760.html&quot;)">Java（四）：4.0 [核心] Java I/O 流体系与实战</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/6760.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=Java（四）：4.0 [核心] Java I/O 流体系与实战&amp;url=https://prorise666.site/posts/6760.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java基础知识总汇<span class="tagsPageCount">9</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/42235.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Java（三）：3.0 [核心] 面向对象编程</div></div></a></div><div class="next-post pull-right"><a href="/posts/62133.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Java（五）：5.0 [元编程] 反射、注解</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/30645.html" title="Java（一）：1.0 Java语言概述与核心生态"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（一）：1.0 Java语言概述与核心生态</div></div></a></div><div><a href="/posts/43523.html" title="Java（9）：9.0 JavaWeb核心知识点速查"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/814899.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-09</div><div class="title">Java（9）：9.0 JavaWeb核心知识点速查</div></div></a></div><div><a href="/posts/42235.html" title="Java（三）：3.0 [核心] 面向对象编程"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（三）：3.0 [核心] 面向对象编程</div></div></a></div><div><a href="/posts/35626.html" title="Java（二）：2.0 Java基础"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（二）：2.0 Java基础</div></div></a></div><div><a href="/posts/64051.html" title="Java（七）：7.0 Java并发编程"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（七）：7.0 Java并发编程</div></div></a></div><div><a href="/posts/14501.html" title="Java（八）：8.0 Java新语法总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（八）：8.0 Java新语法总结</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Java（四）：4.0 [核心] Java I/O 流体系与实战",date:"2025-05-08 19:13:45",updated:"2025-07-14 16:51:40",tags:["Java基础知识总汇"],categories:["后端技术","Java"],content:'\n## **4.0 [核心] Java I/O 流体系与实战**\n\n本章将深入Java的I/O（输入/输出）世界。I/O是程序与外部世界（如文件、网络、控制台）沟通的桥梁。我们将从I/O的“四大家族”和装饰器设计模式入手，理解其核心设计思想，然后深入文件操作的现代实践（NIO.2），并最终探讨如何在真实项目中，利用强大的第三方库来告别繁琐的I/O样板代码。\n\n### **4.1 I/O 核心概念与设计模式**\n\n#### **4.1.1 面试题引入**\n\n> “请谈谈你对Java I/O的理解。字节流和字符流有什么区别？节点流和处理流呢？”\n\n#### **4.1.2 流的“四大家族”与核心区别**\n\nI/O的本质是程序与外部数据源之间的数据传输通道。Java通过**流（Stream）这一抽象概念来表示这个通道，并根据数据传输单位**和**方向**的不同，提供了“四大家族”作为所有I/O操作的基石：\n\n| 分类维度     | 方向             | 字节流 (处理一切数据，如图片、视频、文本) | 字符流 (专为处理文本数据优化) |\n| :----------- | :--------------- | :---------------------------------------- | :---------------------------- |\n| **输入(读)** | `数据源 -> 程序` | `InputStream` (抽象基类)                  | `Reader` (抽象基类)           |\n| **输出(写)** | `程序 -> 数据源` | `OutputStream` (抽象基类)                 | `Writer` (抽象基类)           |\n\n##### **字节流 vs. 字符流**\n\n这是I/O体系中最根本的区别，也是面试中的高频考点。\n\n- **字节流 (`InputStream`/`OutputStream`)** 以**字节（byte,\n  8-bit）为单位进行读写。它是最原始、最通用的流，可以处理任何类型的二进制数据，如图片、音频、视频文件等。但它的缺点在于，在处理文本时，它不关心字符编码**。\n\n- **字符流 (`Reader`/`Writer`)** 以**字符（char,\n  16-bit）为单位进行读写。它是在字节流的基础上构建的，专门用于处理文本数据。其内部封装了字节到字符的解码**和字符到字节的**编码**过程。因此，字符流能够正确地处理包含各种语言（如中文）的文本，有效避免**乱码**问题。\n\n##### **代码示例：乱码问题的产生与解决**\n\n> **场景**：我们有一个UTF-8编码的文本文件`test.txt`，内容为“你好Java”。\n\n- **错误演示：使用字节流读取文本**\n\n  ```java\n  package com.example;\n\n  import java.io.FileInputStream;\n  import java.io.IOException;\n\n  public class Main {\n      public static void main(String[] args) throws IOException {\n          FileInputStream fis = new FileInputStream("test.txt");\n          System.out.println("--- 使用字节流逐字节读取 ---");\n          int byteData;\n          while ((byteData = fis.read()) != -1) {\n              System.out.print((char) byteData);\n          }\n          System.out.println("\\n结果：出现了乱码。");\n      }\n  }\n  ```\n\n  **原因分析**：在UTF-8编码中，一个中文字符通常由3个字节表示。上述代码一次只读一个字节，并试图将其强转为字符，自然无法正确还原“你”和“好”这两个字，导致乱码。\n\n- **正确方式：使用字符流读取文本**\n\n  ```java\n  package com.example;\n\n  import java.io.FileInputStream;\n  import java.io.FileReader;\n  ```\n\nimport java.io.IOException;\n\n    public class Main {\n        public static void main(String[] args) throws IOException {\n            FileReader reader = new FileReader("test.txt");\n            System.out.println("--- 使用字符流逐字符读取 ---");\n            int byteData;\n            while ((byteData = reader.read()) != -1) {\n                System.out.print((char) byteData);\n            }\n            System.out.println("\\n结果：乱码没了");\n        }\n    }\n    ```\n\n#### **4.1.3 节点流 vs. 处理流**\n\n这是从功能层面对流的另一种划分方式。\n\n- **节点流 (Node\n  Stream)**：也被称为“低级流”。它们是直接与数据源（如文件、网络套接字、内存数组）相连接的“管道”，负责实际的数据传输。例如\n  `FileInputStream`、`ByteArrayInputStream`。\n- **处理流 (Processing\n  Stream)**：也被称为“高级流”。它们**不直接连接数据源**，而是“套”在已存在的流（节点流或其他处理流）之上，像一个“过滤器”或“增强器”，为原始的流增加额外的功能。例如\n  `BufferedInputStream`（增加缓冲功能）、`ObjectInputStream`（增加对象反序列化功能）。\n\n* **缓冲功能**：`BufferedInputStream`\n  通过内部缓存机制，一次性读取较多数据，减少与底层数据源（如文件、网络）的交互次数，从而提高读取性能。\n* **反序列化功能**：`ObjectInputStream` 可以将之前通过 `ObjectOutputStream`\n  写入的 Java 对象恢复为内存中的对象，实现对象的持久化和传输。\n\n为了最清晰地展示两者的区别与关系，我们设定一个共同的、非常常见的开发任务：**读取一个文本文件的内容，并将其逐行打印到控制台**。\n\n假设我们有一个名为 `poem.txt` 的文件，内容如下：\n\n```\n床前明月光，\n疑是地上霜。\n举头望明月，\n低头思故乡。\n```\n\n##### **场景一：仅使用节点流（低级、繁琐的方式）**\n\n如果我们只使用节点流（如`FileInputStream`），意味着我们需要亲自处理最原始的字节数据，并手动管理缓冲和字符转换。\n\n```java\npackage com.example;\n\nimport java.io.FileInputStream;\nimport java.io.IOException;\n\npublic class Main {\n    public static void main(String[] args) {\n        // 使用 try-with-resources 确保流被关闭\n        try (\n            // 1. 创建一个节点流，直接连接到数据源（文件）\n            FileInputStream fis = new FileInputStream("poem.txt")\n        ) {\n            System.out.println("--- 仅使用节点流 FileInputStream 读取 ---");\n            \n            // 2. 我们必须自己创建一个字节数组作为“缓冲区”\n            byte[] buffer = new byte[1024]; \n            int bytesRead;\n\n            // 3. 手动循环读取字节块\n            while ((bytesRead = fis.read(buffer)) != -1) {\n                // 4. 手动将读取到的字节块，按指定编码转换为字符串\n                // 这里的操作非常底层，且没有方便的按行读取功能\n                String chunk = new String(buffer, 0, bytesRead, "UTF-8");\n                System.out.print(chunk);\n            }\n        } catch (IOException e) {\n            e.printStackTrace();\n        }\n    }\n}\n```\n\n**分析这种方式的痛点：**\n\n- **操作底层**：我们直接和原始的 `byte[]` 打交道。\n- **功能有限**：`FileInputStream` 本身不提供按行读取 (`readLine`)\n  这样的便捷功能。\n- **编码繁琐**：需要手动处理字节到字符串的转换。\n\n##### **场景二：使用处理流包装节点流（高级、推荐的方式）**\n\n现在，我们引入处理流 `BufferedReader` 来“装饰”或“增强”节点流 `FileReader`。\n\n```java\npackage com.example;\n\nimport java.io.BufferedReader;\nimport java.io.FileReader;\nimport java.io.IOException;\n\npublic class Main {\n    public static void main(String[] args) {\n        try (\n            // 1. 节点流 FileReader: 仍然是直接连接到文件的管道，它负责基础的字符读取\n            FileReader fileReader = new FileReader("poem.txt");\n            \n            // 2. 处理流 BufferedReader: “套”在节点流之上，为其增加强大的功能\n            BufferedReader bufferedReader = new BufferedReader(fileReader)\n        ) {\n            System.out.println("--- 使用处理流 BufferedReader + 节点流 FileReader 读取 ---");\n            String line;\n            \n            // 3. 直接使用处理流提供的、便捷的 readLine() 方法\n            // BufferedReader 在内部帮我们处理好了缓冲和按行读取的所有细节\n            while ((line = bufferedReader.readLine()) != null) {\n                System.out.println(line);\n            }\n        } catch (IOException e) {\n            e.printStackTrace();\n        }\n    }\n}\n```\n\n##### **两种方式对比总结**\n\n| 对比维度   | 仅使用节点流 (`FileInputStream`)          | 处理流 + 节点流 (`BufferedReader` + `FileReader`)  |\n| :--------- | :---------------------------------------- | :------------------------------------------------- |\n| **职责**   | 负责连接数据源，进行最基础的字节读写。    | **节点流**负责连接数据源，**处理流**负责功能增强。 |\n| **易用性** | 差，需要手动处理缓冲、编码、按行读取等。  | **高**，提供了`readLine()`等便捷API。              |\n| **性能**   | 低，每次`read()`都可能是一次物理磁盘I/O。 | **高**，内部缓冲机制大大减少了物理I/O次数。        |\n| **代码量** | 繁琐，样板代码多。                        | 简洁，代码意图清晰。                               |\n\n通过这个对比，我们可以清晰地看到**处理流**的价值：它将开发者从复杂的底层I/O细节中解放出来，让我们能更专注于业务逻辑本身，同时还能获得更好的性能。\n\n在实际开发中，我们几乎总是使用**处理流包装节点流**的方式来进行I/O操作，这正是装饰器模式在Java\nI/O中应用的精髓。\n\n#### **4.1.4 [设计模式] `java.io` 的灵魂：装饰器模式**\n\n理解Java I/O体系的关键，在于理解其背后优美的**装饰器设计模式（Decorator\nPattern）**。该模式允许我们向一个现有对象动态地添加新的功能，同时又不改变其结构。\n\n在I/O中，`FileInputStream`等节点流是我们的**基础组件（ConcreteComponent）**，而`BufferedInputStream`等处理流则是**装饰器（Decorator）**。我们可以像搭积木一样，自由地将这些装饰器“套”在基础组件上，按需组合出强大的功能。\n\n##### **代码示例：装饰器的层层嵌套**\n\n> **场景**：从一个文件中，以高效缓冲的方式，读取一个被序列化后的Java对象。\n\n```java\npackage com.example;\n\nimport java.io.*;\n\npublic class Main {\n    public static void main(String[] args) {\n        // 假设我们有一个名为 "user.ser" 的文件，存储了一个User对象\n        \n        // 这是一个典型的装饰器模式应用\n        try (\n            // 1. 最内层：节点流，直接连接数据源文件\n            FileInputStream fileIn = new FileInputStream("user.ser");\n            \n            // 2. 中间层：处理流，为文件流增加“缓冲”功能，提升性能\n            BufferedInputStream bufferedIn = new BufferedInputStream(fileIn);\n            \n            // 3. 最外层：处理流，为缓冲流增加“对象反序列化”功能\n            ObjectInputStream objectIn = new ObjectInputStream(bufferedIn)\n        ) {\n            // 最终，我们通过功能最强大的最外层流进行操作\n            // User user = (User) objectIn.readObject();\n            System.out.println("I/O流已成功按装饰器模式构建。");\n            System.out.println("操作顺序: ObjectInputStream -> BufferedInputStream -> FileInputStream -> 文件");\n            \n        } catch (IOException e) {\n            // try-with-resources 语句会自动按相反的顺序关闭所有流，无需手动操作\n            e.printStackTrace();\n        }\n    }\n}\n```\n\n这种设计使得Java\nI/O体系既灵活又可扩展。当需要新功能时，只需创建一个新的处理流（装饰器）即可，而无需修改现有的任何流类。\n\n---\n\n### **4.2 文件操作：从 传统`File` 到 现代`NIO.2`**\n\n在理解了I/O流的分类和装饰器设计模式后，我们将聚焦于I/O操作的一个核心应用——**文件操作**。这包括如何创建、删除、重命名文件和目录，以及如何读取它们的属性。Java为此提供了两代API，我们将对比学习，并重点掌握现代化的解决方案。\n\n#### **4.2.1 传统方式：`java.io.File` 类**\n\n`File`类是Java早期用于表示文件系统中的一个文件或目录路径的抽象。它可以用于执行创建、删除、重命名等操作，但其设计存在一些固有缺陷，因此在现代Java开发中已不被推荐作为首选。\n\n##### **核心用途与场景**\n\n在维护旧项目或使用一些尚未升级到NIO.2的老旧第三方库时，我们仍然会遇到`File`类。\n\n##### **常用方法速查表**\n\n| 方法签名                                 | 功能描述                   |\n| :--------------------------------------- | :------------------------- |\n| `boolean exists()`                       | 检查文件或目录是否存在。   |\n| `boolean createNewFile()`                | 创建一个新文件。           |\n| `boolean mkdir()` / `mkdirs()`           | 创建单级/多级目录。        |\n| `boolean delete()`                       | 删除文件或**空**目录。     |\n| `boolean renameTo(File dest)`            | 重命名或移动文件。         |\n| `String getName()` / `getAbsolutePath()` | 获取名称/绝对路径。        |\n| `long length()`                          | 获取文件大小（字节）。     |\n| `boolean isDirectory()` / `isFile()`     | 判断是目录还是文件。       |\n| `File[] listFiles()`                     | 列出目录下的文件和子目录。 |\n\n##### **设计缺陷与痛点**\n\n1. **错误处理不友好**：许多关键操作（如`delete()`,\n   `renameTo()`）在失败时仅返回`false`，而**不会抛出异常**。这使得我们无法得知失败的具体原因（是权限不足？文件被占用？还是其他问题？），给可靠的错误处理带来了巨大困难。\n2. **功能有限**：原生不支持符号链接等现代文件系统特性，也无法方便地访问和修改文件元数据（如权限、所有者等）。\n3. **无力处理非空目录**：`delete()`方法只能删除文件或空目录，要删除整个目录树需要自己编写复杂的递归逻辑。\n\n#### **4.2.2 [现代实践] `java.nio.file` 包 (“NIO.2”)**\n\n自Java\n7起，NIO.2的引入为文件系统操作带来了革命性的变化。它以`Path`接口为核心，通过`Paths`和`Files`两个强大的工具类，提供了功能更全面、设计更优良、错误处理更明确的现代文件操作API。\n\n- **核心优势**：\n  - **明确的异常处理**：所有操作在失败时都会抛出具体的`IOException`，让问题无处遁形。\n  - **强大的功能**：原生支持符号链接、文件属性视图、文件系统监视等高级功能。\n  - **高效的API**：提供了大量便捷、高效的静态方法，可以用一行代码完成过去需要一个方法块才能实现的功能。\n\n##### **代码实战：`Files` 工具类的强大功能**\n\n下面通过几个核心场景，对比展示NIO.2相比于传统`File`类的巨大优势。\n\n###### **场景一：文件的创建与读写**\n\n> **需求**：创建一个文本文件，向其中写入内容，然后再读取出来。\n\n```java\npackage com.example;\n\nimport java.io.IOException;\nimport java.nio.file.Files;\nimport java.nio.file.Path;\nimport java.nio.file.Paths;\nimport java.nio.file.StandardOpenOption;\nimport java.util.List;\n\npublic class Main {\n    public static void main(String[] args) {\n        Path filepath = Paths.get("poem.txt");\n        try {\n            // 1. 使用Paths.get()创建Path对象，这是现代文件路径的表示方式\n            Path filePath = Paths.get("poem.txt");\n            // 2. 写入文件（如果文件不存在则创建，如果存在则追加内容）\n            // Files.writeString() 是Java 11+的方法，极其方便\n            String contentToWrite = "这是用NIO.2写入的第一行。\\n";\n            // 1.READ：以只读方式打开文件。\n            // 2.WRITE：以写入方式打开文件。\n            // 3.APPEND：如果文件已存在，则将数据追加到文件末尾（而不是覆盖）。\n            // 4.TRUNCATE_EXISTING：如果文件已存在，则将其长度截断为 0（即清空文件内容）。\n            // 5.CREATE：如果文件不存在，则创建新文件。\n            // 6.CREATE_NEW：如果文件已存在，则抛出异常；否则创建新文件。\n            // 7.DELETE_ON_CLOSE：在关闭文件时尝试删除该文件。\n            // 8.SPARSE：提示系统创建一个稀疏文件（仅在支持稀疏文件的文件系统上有效）。\n            // 9.SYNC：每次更新文件内容或元数据时都同步写入磁盘。\n            // 10.DSYNC：每次更新文件内容时都同步写入磁盘，但不包括元数据。\n            Files.writeString(filePath, contentToWrite, StandardOpenOption.CREATE, StandardOpenOption.APPEND);\n            String secondLine = "这是追加的第二行。\\n";\n            Files.writeString(filePath, secondLine, StandardOpenOption.APPEND);\n            System.out.println("文件 \'" + filePath.getFileName() + "\' 写入完成。");\n            // 3. 读取文件所有行到List中\n            System.out.println("\\n--- 读取文件内容 ---");\n            List<String> lines = Files.readAllLines(filePath);\n            lines.forEach(System.out::println);\n        } catch (IOException e) {\n            // NIO.2的错误处理非常明确\n            System.err.println("文件操作失败: " + e.getMessage());\n        }\n    }\n}\n```\n\n###### **场景二：文件与目录的复制**\n\n> **需求**：将一个文件复制到另一个位置，并将一个完整的目录（包含子目录和文件）复制到新位置。\n\n```java\npackage com.example;\n\nimport java.io.IOException;\nimport java.nio.file.*;\nimport java.util.stream.Stream;\n\npublic class Main {\n    public static void main(String[] args) throws IOException {\n        // --- 1. 复制单个文件 ---\n        Path sourceFile = Paths.get("source.txt");\n        Path destFile = Paths.get("dest_folder/source_copy.txt");\n        \n        // 准备源文件和目标目录\n        Files.createDirectories(destFile.getParent());\n        Files.writeString(sourceFile, "一些源数据");\n\n        // 使用Files.copy，如果目标文件已存在则替换\n        Files.copy(sourceFile, destFile, StandardCopyOption.REPLACE_EXISTING);\n        System.out.println("单个文件复制完成！");\n\n\n        // --- 2. 递归复制整个目录 ---\n        Path sourceDir = Paths.get("my_app_v1");\n        Path destDir = Paths.get("my_app_v2");\n        \n        // 准备源目录\n        Files.createDirectories(sourceDir.resolve("config"));\n        Files.writeString(sourceDir.resolve("main.conf"), "data");\n\n        // 使用Stream API和Files.walk来递归复制\n        try (Stream<Path> stream = Files.walk(sourceDir)) {\n            stream.forEach(sourcePath -> {\n                try {\n                    Path targetPath = destDir.resolve(sourceDir.relativize(sourcePath));\n                    Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);\n                } catch (IOException e) {\n                    System.err.println("无法复制: " + sourcePath);\n                }\n            });\n        }\n        System.out.println("整个目录已成功复制！");\n    }\n}\n```\n\n**结论**：在所有新的Java项目中，都应**优先并坚持使用`java.nio.file`包**进行文件和目录操作。它更安全、功能更强大、代码也更现代化。\n\n---\n\n#### **4.2.3 `java.nio.file.Files` 核心方法速查表**\n\n好的，明白了。为了让速查表更加简洁、一目了然，我将移除“方法签名”列中的返回类型和修饰符，只保留核心的方法名和参数示意。\n\n##### **1. 文件和目录检查**\n\n| 方法名               | 功能描述                           | 注意事项 / 最佳实践                                   |\n| :------------------- | :--------------------------------- | :---------------------------------------------------- |\n| `exists(...)`        | 检查文件或目录是否存在。           | 最常用的检查方法。                                    |\n| `notExists(...)`     | 检查文件或目录是否**不**存在。     | `!Files.exists(path)` 的一个更具可读性的替代方案。    |\n| `isDirectory(...)`   | 判断路径是否为目录。               |                                                       |\n| `isRegularFile(...)` | 判断路径是否为普通文件。           |                                                       |\n| `isReadable(...)`    | 判断文件是否可读。                 |                                                       |\n| `isWritable(...)`    | 判断文件是否可写。                 |                                                       |\n| `isExecutable(...)`  | 判断文件是否可执行。               |                                                       |\n| `isSameFile(...)`    | 判断两个`Path`是否指向同一个文件。 | 比`p1.equals(p2)`更可靠，因为它会处理符号链接等情况。 |\n\n##### **2. 文件和目录创建**\n\n| 方法名                     | 功能描述                           | 注意事项 / 最佳实践                                  |\n| :------------------------- | :--------------------------------- | :--------------------------------------------------- |\n| `createFile(...)`          | 创建一个新文件。                   | 如果文件已存在，会抛出`FileAlreadyExistsException`。 |\n| `createDirectory(...)`     | 创建一个新目录。                   | 只能创建单级目录，如果父目录不存在会抛出异常。       |\n| `createDirectories(...)`   | **强烈推荐**。创建多级目录。       | 如果父目录不存在，会自动一并创建，非常方便。         |\n| `createTempFile(...)`      | 在默认或指定位置创建一个临时文件。 | 常用于需要临时存储数据的场景。                       |\n| `createTempDirectory(...)` | 创建一个临时目录。                 |                                                      |\n\n##### **3. 文件和目录删除**\n\n| 方法名                | 功能描述                       | 注意事项 / 最佳实践                                                                         |\n| :-------------------- | :----------------------------- | :------------------------------------------------------------------------------------------ |\n| `delete(...)`         | 删除一个文件或**空**目录。     | 如果路径不存在，抛出`NoSuchFileException`。如果目录非空，抛出`DirectoryNotEmptyException`。 |\n| `deleteIfExists(...)` | 如果文件或目录存在，则删除它。 | **推荐使用**。比`delete()`更安全，因为它不会在文件不存在时抛出异常，只会返回`false`。       |\n\n##### **4. 文件读/写操作（小文件）**\n\n这些方法会将文件的全部内容一次性读入内存，非常便捷，但只适用于小文件。\n\n| 方法名              | 功能描述                               | 注意事项 / 最佳实践                                              |\n| :------------------ | :------------------------------------- | :--------------------------------------------------------------- |\n| `readAllBytes(...)` | 将文件的所有内容读取为一个字节数组。   | **注意内存溢出（OOM）风险**，不适用于大文件。                    |\n| `readAllLines(...)` | 将文件的所有行读取到一个字符串列表中。 | **注意内存溢出风险**。默认使用UTF-8编码。                        |\n| `write(...)`        | 将一个字节数组或字符串集合写入文件。   | 默认会覆盖已有文件。可使用`StandardOpenOption`指定追加、创建等。 |\n| `writeString(...)`  | **[Java 11+]** 将字符串写入文件。      | 写入文本最简单的方式。                                           |\n| `readString(...)`   | **[Java 11+]** 读取文件内容为字符串。  | 读取小文本文件最简单的方式。                                     |\n\n##### **5. 文件读/写操作（大文件/流式处理）**\n\n当处理大文件时，应使用流式API，避免一次性将所有内容加载到内存。\n\n| 方法名                   | 功能描述                                               | 注意事项 / 最佳实践                                                    |\n| :----------------------- | :----------------------------------------------------- | :--------------------------------------------------------------------- |\n| `newInputStream(...)`    | 打开一个文件，返回一个用于读取的`InputStream`。        | **处理大文件的标准方式**。获取流之后，需配合`try-with-resources`使用。 |\n| `newOutputStream(...)`   | 打开或创建一个文件，返回一个用于写入的`OutputStream`。 | 同上。                                                                 |\n| `newBufferedReader(...)` | 打开一个文件，返回一个用于读取文本的`BufferedReader`。 | 提供了高效的`readLine()`方法，是**读取大文本文件**的首选。             |\n| `newBufferedWriter(...)` | 打开或创建一个文件，返回一个`BufferedWriter`。         | **写入大文本文件**的首选。                                             |\n| `lines(...)`             | **[Java 8+]** 返回一个由文件所有行组成的`Stream`。     | **懒加载**，非常适合用函数式编程风格处理大文本文件，内存占用极小。     |\n\n##### **6. 复制与移动**\n\n| 方法名      | 功能描述               | 注意事项 / 最佳实践                                                                                                                 |\n| :---------- | :--------------------- | :---------------------------------------------------------------------------------------------------------------------------------- |\n| `copy(...)` | 复制一个文件或目录。   | 默认情况下，如果目标文件已存在会失败。需使用`StandardCopyOption.REPLACE_EXISTING`来覆盖。复制目录时，只复制目录本身，不复制其内容。 |\n| `move(...)` | 移动或重命名一个文件。 | 可以指定`StandardCopyOption.ATOMIC_MOVE`来保证操作的原子性。                                                                        |\n\n##### **7. 目录遍历 (Stream API)**\n\n| 方法名      | 功能描述                                                                     | 注意事项 / 最佳实践                                      |\n| :---------- | :--------------------------------------------------------------------------- | :------------------------------------------------------- |\n| `list(...)` | **[Java 8+]** 返回一个表示目录下所有条目（不含子目录）的`Stream`。           | **非递归**，只遍历当前层级。                             |\n| `walk(...)` | **[Java 8+]** 返回一个从指定路径开始、**递归遍历**所有文件和目录的`Stream`。 | 功能强大，可以配合`filter`等操作轻松实现文件查找等功能。 |\n| `find(...)` | **[Java 8+]** 功能类似`walk`，但可以额外传入一个匹配器来筛选路径。           | 比`walk`后接`filter`更高效。                             |\n\n---\n\n### **4.3 核心I/O处理流搭配使用**\n\n在掌握了文件和目录的表示方法后，我们回到流本身，聚焦于如何通过组合不同的处理流，来高效、灵活地读写文件内容。\n\n#### **4.3.1 缓冲流 (`BufferedInputStream` / `BufferedReader`): 性能优化的基石**\n\n##### **面试题引入**\n\n> “为什么我们在进行文件IO时，总是推荐使用缓冲流？它的原理是什么？”\n\n##### **核心原理解析**\n\n计算机中，对磁盘或网络的I/O操作（系统调用）相比于内存操作，是极其缓慢的。如果我们直接使用`FileInputStream`的`read()`方法逐字节读取文件，那么每读取一个字节，就可能触发一次昂贵的物理磁盘访问。\n\n**缓冲流（Buffered\nStream）正是为解决这一性能瓶颈而生。它的原理是在内部维护一个内存缓冲区**（一个字节或字符数组，默认大小通常为8192）。\n\n- **读取时**：`BufferedInputStream`会一次性从磁盘读取一大块数据（例如8KB）填充到内部缓冲区。之后你再调用`read()`方法时，它会直接从高速的内存缓冲区中返回数据，直到缓冲区耗尽，才会再次触发下一次对磁盘的大块读取。\n- **写入时**：`BufferedOutputStream`会先将你写入的数据存放在缓冲区，直到缓冲区满了，或者你手动调用`flush()`方法，它才会将整个缓冲区的数据一次性写入磁盘。\n\n**结论**：缓冲流通过**“化零为整”**的策略，用一次大的I/O操作替代了无数次小的I/O操作，**极大地减少了与底层物理设备的交互次数**，从而实现性能的飞跃。\n\n##### **代码实战：文件复制性能对比**\n\n> **场景**：复制一个文件，对比使用缓冲流和不使用缓冲流的耗时。\n\n```java\npackage com.example;\n\nimport java.io.*;\n\npublic class Main {\n\n    // 为了测试，先创建一个较大的文件\n    static {\n        try (FileWriter writer = new FileWriter("source_file.txt")) {\n            for (int i = 0; i < 100000; i++) {\n                writer.write("abcdefghij\\n");\n            }\n        } catch (IOException e) {\n            e.printStackTrace();\n        }\n    }\n\n    public static void main(String[] args) {\n        // 方案一：不使用缓冲流，逐字节复制\n        long start1 = System.currentTimeMillis();\n        copyFileWithoutBuffer();\n        long end1 = System.currentTimeMillis();\n        System.out.println("不使用缓冲流耗时: " + (end1 - start1) + " ms");\n\n        // 方案二：使用缓冲流\n        long start2 = System.currentTimeMillis();\n        copyFileWithBuffer();\n        long end2 = System.currentTimeMillis();\n        System.out.println("使用缓冲流耗时: " + (end2 - start2) + " ms");\n    }\n\n    public static void copyFileWithoutBuffer() {\n        try (FileInputStream fis = new FileInputStream("source_file.txt");\n             FileOutputStream fos = new FileOutputStream("copy_no_buffer.txt")) {\n            int byteData;\n            while ((byteData = fis.read()) != -1) {\n                fos.write(byteData);\n            }\n        } catch (IOException e) {\n            e.printStackTrace();\n        }\n    }\n\n    public static void copyFileWithBuffer() {\n        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream("source_file.txt"));\n             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream("copy_with_buffer.txt"))) {\n            int byteData;\n            while ((byteData = bis.read()) != -1) {\n                bos.write(byteData);\n            }\n        } catch (IOException e) {\n            e.printStackTrace();\n        }\n    }\n}\n```\n\n**运行结果（示例）**：\n\n```\n不使用缓冲流耗时: 12629 ms\n使用缓冲流耗时: 60 ms\n```\n\n可以看到，性能差异是数量级的。因此，在进行任何文件I/O时，**使用缓冲流包装节点流都应成为一种编程习惯**。\n\n#### **4.3.2 转换流 (`InputStreamReader` / `OutputStreamWriter`): 字节与字符的桥梁**\n\n##### **核心用途**\n\n转换流的核心作用是**适配器**，它能将**字节流**转换为**字符流**，并在转换过程中处理**字符编码**。\n\n##### **应用场景**\n\n当你需要读写的文本文件编码与当前操作系统的默认编码不一致时，必须使用转换流来显式指定正确的编码，否则就会产生乱码。\n\n- **读取**：例如，在UTF-8的服务器上读取一个由Windows记事本（默认GBK编码）生成的中文文件。\n- **写入**：例如，无论程序运行在什么系统上，都希望统一生成UTF-8编码的配置文件。\n\n##### **代码示例：读取GBK文件并转存为UTF-8**\n\n```java\npackage com.example;\n\nimport java.io.*;\nimport java.nio.charset.StandardCharsets;\n\npublic class Main {\n    public static void main(String[] args) {\n        // 假设 gbk_file.txt 是一个GBK编码的文件，内容为“你好，世界”\n        // 我们可以先手动创建一个这样的文件用于测试\n        \n        try (\n            // 1. 创建一个输入字节流连接到源文件\n            FileInputStream fis = new FileInputStream("gbk_file.txt");\n            // 2. 使用转换流InputStreamReader，指定用GBK编码来解码字节流\n            InputStreamReader isr = new InputStreamReader(fis, "GBK");\n            // 3. 为了效率，再套上一个BufferedReader\n            BufferedReader br = new BufferedReader(isr);\n\n            // 4. 创建一个输出字节流连接到目标文件\n            FileOutputStream fos = new FileOutputStream("utf8_file.txt");\n            // 5. 使用转换流OutputStreamWriter，指定用UTF-8编码来编码字符流\n            OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);\n            // 6. 同样，套上BufferedWriter\n            BufferedWriter bw = new BufferedWriter(osw)\n        ) {\n            String line;\n            while ((line = br.readLine()) != null) {\n                bw.write(line);\n                bw.newLine();\n            }\n            System.out.println("编码转换完成！");\n\n        } catch (IOException e) {\n            e.printStackTrace();\n        }\n    }\n}\n```\n\n#### **4.3.3 对象流 (`ObjectInputStream` / `ObjectOutputStream`): 对象的序列化与反序列化**\n\n##### **面试题引入**\n\n> “什么是Java的序列化？`transient`关键字和`serialVersionUID`有什么作用？”\n\n##### **核心概念**\n\n- **序列化 (Serialization)**：将一个Java**对象**的状态转换为**字节序列**的过程。\n- **反序列化 (Deserialization)**：从字节序列中恢复并重建Java对象的过程。\n- **用途**：实现对象的**持久化**（保存到文件或数据库）和**网络传输**。\n\n##### **关键知识点**\n\n1. **`Serializable`\n   接口**：一个类必须实现这个**标记接口**（没有任何方法），其对象才能被序列化。它像一个“通行证”，告诉JVM这个类的对象可以被“扁平化”为字节。\n2. **`transient`\n   关键字**：用于修饰字段。被`transient`修饰的字段将被**排除**在序列化过程之外，不会被写入字节流。反序列化后，该字段的值会是其类型的默认值（如对象为`null`，`int`为0）。常用于密码、安全令牌、缓存数据等敏感或无需持久化的字段。\n3. **`serialVersionUID`\n   (面试核心)**：这是一个用于标识可序列化类版本的`long`型常量。\n   - **作用**：在反序列化时，JVM会比较字节流中的`serialVersionUID`和当前加载的类中的`serialVersionUID`。如果两者不一致，会抛出`InvalidClassException`，以防止因类版本不兼容导致的数据错乱。\n   - **最佳实践**：**强烈建议**所有可序列化类都**显式声明**一个`private static final long serialVersionUID`。如果不声明，编译器会自动生成一个，但这个自动生成的值对类的结构非常敏感（如增删字段），稍有改动就会变化，导致旧的序列化数据无法被新版类反序列化。\n\n##### **代码实战：将User对象持久化到文件**\n\n```java\npackage com.example;\n\nimport java.io.*;\n\n// 1. User类必须实现Serializable接口\nclass User implements Serializable {\n    // 2. 强烈建议显式声明serialVersionUID\n    private static final long serialVersionUID = 1L;\n\n    private String name;\n    private int age;\n    // 3. 使用transient关键字，密码将不会被序列化\n    private transient String password; \n\n    public User(String name, int age, String password) {\n        this.name = name;\n        this.age = age;\n        this.password = password;\n    }\n    \n    @Override\n    public String toString() {\n        return "User{name=\'" + name + "\', age=" + age + ", password=\'" + password + "\'}";\n    }\n}\n\npublic class Main {\n    public static void main(String[] args) {\n        File userFile = new File("user.ser");\n        User userToWrite = new User("Alice", 25, "mySecret123");\n\n        // --- 序列化过程 ---\n        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(userFile))) {\n            oos.writeObject(userToWrite);\n            System.out.println("原始对象: " + userToWrite);\n            System.out.println("对象已成功序列化到文件 " + userFile.getName());\n        } catch (IOException e) {\n            e.printStackTrace();\n        }\n\n        System.out.println("\\n--- 反序列化过程 ---");\n        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(userFile))) {\n            User userToRead = (User) ois.readObject();\n            System.out.println("从文件反序列化出的对象: " + userToRead);\n            // 注意：password字段因为是transient，所以变成了null\n        } catch (IOException | ClassNotFoundException e) {\n            e.printStackTrace();\n        }\n    }\n}\n```\n\n---\n\n### **4.4 [实战进阶] 简化I/O操作的第三方库**\n\n#### **4.4.1 面试题引入与核心思想**\n\n##### **面试题引入**\n\n> “在实际项目中，你还会频繁地手动编写`try-with-resources`和循环来复制一个文件吗？有没有更便捷、更专业的处理方式？”\n\n##### **核心思想：为什么需要第三方库？**\n\n虽然我们必须深入理解JDK原生I/O流的体系，因为它是所有I/O操作的基础，也是排查底层问题的关键。但在真实的、快节奏的商业项目开发中，对于那些反复出现的通用I/O任务（如读写文件、复制目录等），如果每次都手动编写原始的流处理代码，会存在几个明显的问题：\n\n1. **代码冗长**：完成一个简单的任务需要创建多个流对象、编写循环、处理异常，代码显得非常臃肿。\n2. **容易出错**：手动管理资源和编写逻辑，稍有不慎就可能导致资源未关闭、缓冲区处理不当等难以察觉的BUG。\n3. **效率低下**：重复编写同样功能的代码，是对开发时间的浪费。\n\n因此，在专业开发领域，我们遵循“**不重复造轮子 (Don\'t Reinvent the\nWheel)**”的原则。对于I/O操作，社区已经为我们提供了极其优秀、经过数万个项目验证的“轮子”——**第三方工具库**。\n\n#### **4.4.2 主流库介绍：`Apache Commons IO`**\n\n`Apache Commons IO`\n是Java生态中处理I/O的**事实上的行业标准**。它是一个稳定、可靠、功能极其丰富的工具包，几乎是所有Java项目的必备依赖之一。它的核心价值在于，将那些繁琐的I/O样板代码封装成了简单、强大的一行代码。\n\n- **如何引入**： 在Maven项目中，只需在`pom.xml`中添加以下依赖即可：\n  ```xml\n  <dependency>\n      <groupId>commons-io</groupId>\n      <artifactId>commons-io</artifactId>\n      <version>2.14.0</version>\n  </dependency>\n  ```\n- **核心工具类**：\n  - **`FileUtils`**：面向文件（`File`对象）和目录的操作。\n  - **`IOUtils`**：面向流（`InputStream`, `OutputStream`等）的操作。\n\n#### **4.4.3 代码实战：JDK原生写法 vs. `Commons IO` 一行代码**\n\n下面，我们将通过几个鲜明的“**之前 vs. 之后**”的对比，来感受`Commons IO`的威力。\n\n##### **场景一：读取整个文件到字符串**\n\n- **JDK 原生写法**\n  ```java\n  package com.example;\n\n  import java.io.BufferedReader;\n  import java.io.FileReader;\n  import java.io.IOException;\n\n  public class Main {\n      public static String readFileWithJDK(String filePath) throws IOException {\n          StringBuilder content = new StringBuilder();\n          try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {\n              String line;\n              while ((line = reader.readLine()) != null) {\n                  content.append(line).append(System.lineSeparator());\n              }\n          }\n          return content.toString();\n      }\n      // ... main方法调用 ...\n  }\n  ```\n- **Commons IO 写法**\n  ```java\n  package com.example;\n\n  import org.apache.commons.io.FileUtils;\n  import java.io.File;\n  import java.io.IOException;\n  import java.nio.charset.StandardCharsets;\n\n  public class Main {\n      public static String readFileWithCommonsIO(String filePath) throws IOException {\n          // 一行代码，搞定！内部已处理好所有流的打开和关闭。\n          return FileUtils.readFileToString(new File(filePath), StandardCharsets.UTF_8);\n      }\n      // ... main方法调用 ...\n  }\n  ```\n\n##### **场景二：复制文件**\n\n- **JDK 原生写法**\n  ```java\n  package com.example;\n\n  import java.io.*;\n\n  public class Main {\n      public static void copyFileWithJDK(String src, String dest) throws IOException {\n          try (InputStream in = new BufferedInputStream(new FileInputStream(src));\n               OutputStream out = new BufferedOutputStream(new FileOutputStream(dest))) {\n              byte[] buffer = new byte[1024];\n              int length;\n              while ((length = in.read(buffer)) > 0) {\n                  out.write(buffer, 0, length);\n              }\n          }\n      }\n      // ... main方法调用 ...\n  }\n  ```\n- **Commons IO 写法**\n  ```java\n  package com.example;\n\n  import org.apache.commons.io.FileUtils;\n  import java.io.File;\n  import java.io.IOException;\n\n  public class Main {\n      public static void copyFileWithCommonsIO(String src, String dest) throws IOException {\n          // 同样是一行代码\n          FileUtils.copyFile(new File(src), new File(dest));\n      }\n      // ... main方法调用 ...\n  }\n  ```\n\n##### **场景三：递归删除目录**\n\n- **JDK 原生写法**\n  JDK的`File.delete()`无法删除非空目录。要实现此功能，必须自己编写一个递归方法，先删除目录下的所有文件和子目录，最后再删除该目录本身，过程复杂且容易出错。\n- **Commons IO 写法**\n\n  ```java\n  ```\n\npackage com.example;\n\n    import org.apache.commons.io.FileUtils;\n    import java.io.File;\n\nimport java.io.IOException;\n\n    public class Main {\n        public static void deleteDirWithCommonsIO(String dirPath) throws IOException {\n            // 无论目录是否为空，一行代码安全删除\n            FileUtils.deleteDirectory(new File(dirPath));\n        }\n        // ... main方法调用 ...\n    }\n    ```\n\n---\n\n### **4.5 `Apache Commons IO` 核心方法速查表**\n\n#### **`FileUtils` 类：面向文件和目录的操作**\n\n##### **1. 读操作 (Read Operations)**\n\n| 方法名                                           | 功能描述                                                 |\n| :----------------------------------------------- | :------------------------------------------------------- |\n| `String readFileToString(File file, Charset cs)` | **（最常用）**将整个文件内容读取为一个字符串。           |\n| `List<String> readLines(File file, Charset cs)`  | 将文件的每一行读取到一个字符串列表 (`List<String>`) 中。 |\n| `byte[] readFileToByteArray(File file)`          | 将整个文件内容读取为一个字节数组 (`byte[]`)。            |\n\n##### **2. 写操作 (Write Operations)**\n\n| 方法名                                                 | 功能描述                                             |\n| :----------------------------------------------------- | :--------------------------------------------------- |\n| `void writeStringToFile(File file, String data, ...)`  | **（最常用）**将一个字符串写入文件（会覆盖或追加）。 |\n| `void writeLines(File file, Collection<?> lines, ...)` | 将一个字符串集合（如`List`）逐行写入文件。           |\n| `void writeByteArrayToFile(File file, byte[] data)`    | 将一个字节数组写入文件。                             |\n\n##### **3. 复制与移动 (Copy & Move Operations)**\n\n| 方法名                                              | 功能描述                                   |\n| :-------------------------------------------------- | :----------------------------------------- |\n| `void copyFile(File src, File dest)`                | **（常用）**复制一个文件到新位置。         |\n| `void copyDirectory(File src, File dest)`           | **（强大）**递归复制整个目录及其所有内容。 |\n| `void copyFileToDirectory(File src, File destDir)`  | 将一个文件复制到指定的目录下。             |\n| `void moveFile(File src, File dest)`                | 移动一个文件（本质上是“复制后删除”）。     |\n| `void moveDirectory(File src, File dest)`           | 移动整个目录。                             |\n| `void moveToDirectory(File src, File destDir, ...)` | 将文件或目录移动到指定的目录下。           |\n\n##### **4. 删除与清空 (Delete & Clean Operations)**\n\n| 方法名                           | 功能描述                                           |\n| :------------------------------- | :------------------------------------------------- |\n| `void deleteDirectory(File dir)` | **（强大）**递归删除整个目录，无论其是否为空。     |\n| `void cleanDirectory(File dir)`  | 清空一个目录下的所有内容，但**不删除**目录本身。   |\n| `boolean forceDelete(File file)` | 强制删除一个文件或目录。如果删除失败，会尝试多次。 |\n\n##### **5. 状态检查与其它**\n\n| 方法名                                | 功能描述                                       |\n| :------------------------------------ | :--------------------------------------------- |\n| `long sizeOf(File file)`              | 获取一个文件的大小。                           |\n| `long sizeOfDirectory(File dir)`      | **（常用）**递归计算整个目录的大小。           |\n| `boolean isFileNewer(File file, ...)` | 判断一个文件是否比另一个文件或指定时间戳更新。 |\n\n#### **`IOUtils` 类：面向流的操作**\n\n##### **1. 读/转换操作 (Read / Convert Operations)**\n\n| 方法名                                                | 功能描述                                                            |\n| :---------------------------------------------------- | :------------------------------------------------------------------ |\n| `String toString(InputStream in, Charset cs)`         | **（最常用）**将一个`InputStream`或`Reader`的内容读取为一个字符串。 |\n| `byte[] toByteArray(InputStream in)`                  | **（常用）**将一个`InputStream`或`Reader`的内容读取为一个字节数组。 |\n| `InputStream toInputStream(String input, Charset cs)` | 将一个`CharSequence`（如`String`）转换为一个`InputStream`。         |\n| `List<String> readLines(InputStream in, Charset cs)`  | 将一个`InputStream`或`Reader`的内容按行读取到一个`List<String>`中。 |\n\n##### **2. 写操作 (Write Operations)**\n\n| 方法名                                           | 功能描述                                                             |\n| :----------------------------------------------- | :------------------------------------------------------------------- |\n| `void write(String data, OutputStream out, ...)` | 将一个`String`或`byte[]`的内容写入到一个`OutputStream`或`Writer`中。 |\n\n##### **3. 复制操作 (Copy Operations)**\n\n| 方法名                                             | 功能描述                                                                                                               |\n| :------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------- |\n| `int copy(InputStream in, OutputStream out)`       | **（最常用）**将一个`InputStream`的内容复制到一个`OutputStream`中，或将`Reader`复制到`Writer`。返回复制的字节/字符数。 |\n| `long copyLarge(InputStream in, OutputStream out)` | 用于复制大于2GB的超大流。                                                                                              |\n\n##### **4. 关闭操作 (Close Operations)**\n\n| 方法名                           | 功能描述                                                                                                                                                                                            |\n| :------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\n| `void closeQuietly(Closeable c)` | **（历史著名）**安静地关闭一个`Closeable`（如流），忽略所有异常。**注意**：在Java 7及之后，官方**推荐使用`try-with-resources`语句**来自动管理资源，此方法的必要性已大大降低，被视为一种过时的模式。 |\n\n---\n'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-0-%E6%A0%B8%E5%BF%83-Java-I-O-%E6%B5%81%E4%BD%93%E7%B3%BB%E4%B8%8E%E5%AE%9E%E6%88%98"><span class="toc-number">1.</span> <span class="toc-text">4.0 [核心] Java I/O 流体系与实战</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-I-O-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%E4%B8%8E%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.1.</span> <span class="toc-text">4.1 I/O 核心概念与设计模式</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-1-1-%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5"><span class="toc-number">1.1.1.</span> <span class="toc-text">4.1.1 面试题引入</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-1-2-%E6%B5%81%E7%9A%84%E2%80%9C%E5%9B%9B%E5%A4%A7%E5%AE%B6%E6%97%8F%E2%80%9D%E4%B8%8E%E6%A0%B8%E5%BF%83%E5%8C%BA%E5%88%AB"><span class="toc-number">1.1.2.</span> <span class="toc-text">4.1.2 流的“四大家族”与核心区别</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%AD%97%E8%8A%82%E6%B5%81-vs-%E5%AD%97%E7%AC%A6%E6%B5%81"><span class="toc-number">1.1.2.1.</span> <span class="toc-text">字节流 vs. 字符流</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E4%B9%B1%E7%A0%81%E9%97%AE%E9%A2%98%E7%9A%84%E4%BA%A7%E7%94%9F%E4%B8%8E%E8%A7%A3%E5%86%B3"><span class="toc-number">1.1.2.2.</span> <span class="toc-text">代码示例：乱码问题的产生与解决</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E4%BD%BF%E7%94%A8%E5%A4%84%E7%90%86%E6%B5%81%E5%8C%85%E8%A3%85%E8%8A%82%E7%82%B9%E6%B5%81%EF%BC%88%E9%AB%98%E7%BA%A7%E3%80%81%E6%8E%A8%E8%8D%90%E7%9A%84%E6%96%B9%E5%BC%8F%EF%BC%89"><span class="toc-number">1.1.2.3.</span> <span class="toc-text">场景二：使用处理流包装节点流（高级、推荐的方式）</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%A4%E7%A7%8D%E6%96%B9%E5%BC%8F%E5%AF%B9%E6%AF%94%E6%80%BB%E7%BB%93"><span class="toc-number">1.1.2.4.</span> <span class="toc-text">两种方式对比总结</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-1-4-%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8F-java-io-%E7%9A%84%E7%81%B5%E9%AD%82%EF%BC%9A%E8%A3%85%E9%A5%B0%E5%99%A8%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.1.3.</span> <span class="toc-text">4.1.4 [设计模式] java.io 的灵魂：装饰器模式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%A3%85%E9%A5%B0%E5%99%A8%E7%9A%84%E5%B1%82%E5%B1%82%E5%B5%8C%E5%A5%97"><span class="toc-number">1.1.3.1.</span> <span class="toc-text">代码示例：装饰器的层层嵌套</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C%EF%BC%9A%E4%BB%8E-%E4%BC%A0%E7%BB%9FFile-%E5%88%B0-%E7%8E%B0%E4%BB%A3NIO-2"><span class="toc-number">1.2.</span> <span class="toc-text">4.2 文件操作：从 传统File 到 现代NIO.2</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-1-%E4%BC%A0%E7%BB%9F%E6%96%B9%E5%BC%8F%EF%BC%9Ajava-io-File-%E7%B1%BB"><span class="toc-number">1.2.1.</span> <span class="toc-text">4.2.1 传统方式：java.io.File 类</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF"><span class="toc-number">1.2.1.1.</span> <span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-number">1.2.1.2.</span> <span class="toc-text">常用方法速查表</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%AE%BE%E8%AE%A1%E7%BC%BA%E9%99%B7%E4%B8%8E%E7%97%9B%E7%82%B9"><span class="toc-number">1.2.1.3.</span> <span class="toc-text">设计缺陷与痛点</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-2-%E7%8E%B0%E4%BB%A3%E5%AE%9E%E8%B7%B5-java-nio-file-%E5%8C%85-%E2%80%9CNIO-2%E2%80%9D"><span class="toc-number">1.2.2.</span> <span class="toc-text">4.2.2 [现代实践] java.nio.file 包 (“NIO.2”)</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E5%AE%9E%E6%88%98%EF%BC%9AFiles-%E5%B7%A5%E5%85%B7%E7%B1%BB%E7%9A%84%E5%BC%BA%E5%A4%A7%E5%8A%9F%E8%83%BD"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">代码实战：Files 工具类的强大功能</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E6%96%87%E4%BB%B6%E7%9A%84%E5%88%9B%E5%BB%BA%E4%B8%8E%E8%AF%BB%E5%86%99"><span class="toc-number">1.2.2.1.1.</span> <span class="toc-text">场景一：文件的创建与读写</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E6%96%87%E4%BB%B6%E4%B8%8E%E7%9B%AE%E5%BD%95%E7%9A%84%E5%A4%8D%E5%88%B6"><span class="toc-number">1.2.2.1.2.</span> <span class="toc-text">场景二：文件与目录的复制</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-3-java-nio-file-Files-%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-number">1.2.3.</span> <span class="toc-text">4.2.3 java.nio.file.Files 核心方法速查表</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E6%96%87%E4%BB%B6%E5%92%8C%E7%9B%AE%E5%BD%95%E6%A3%80%E6%9F%A5"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">1. 文件和目录检查</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E6%96%87%E4%BB%B6%E5%92%8C%E7%9B%AE%E5%BD%95%E5%88%9B%E5%BB%BA"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">2. 文件和目录创建</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E6%96%87%E4%BB%B6%E5%92%8C%E7%9B%AE%E5%BD%95%E5%88%A0%E9%99%A4"><span class="toc-number">1.2.3.3.</span> <span class="toc-text">3. 文件和目录删除</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-%E6%96%87%E4%BB%B6%E8%AF%BB-%E5%86%99%E6%93%8D%E4%BD%9C%EF%BC%88%E5%B0%8F%E6%96%87%E4%BB%B6%EF%BC%89"><span class="toc-number">1.2.3.4.</span> <span class="toc-text">4. 文件读/写操作（小文件）</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#5-%E6%96%87%E4%BB%B6%E8%AF%BB-%E5%86%99%E6%93%8D%E4%BD%9C%EF%BC%88%E5%A4%A7%E6%96%87%E4%BB%B6-%E6%B5%81%E5%BC%8F%E5%A4%84%E7%90%86%EF%BC%89"><span class="toc-number">1.2.3.5.</span> <span class="toc-text">5. 文件读/写操作（大文件/流式处理）</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#6-%E5%A4%8D%E5%88%B6%E4%B8%8E%E7%A7%BB%E5%8A%A8"><span class="toc-number">1.2.3.6.</span> <span class="toc-text">6. 复制与移动</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#7-%E7%9B%AE%E5%BD%95%E9%81%8D%E5%8E%86-Stream-API"><span class="toc-number">1.2.3.7.</span> <span class="toc-text">7. 目录遍历 (Stream API)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-%E6%A0%B8%E5%BF%83I-O%E5%A4%84%E7%90%86%E6%B5%81%E6%90%AD%E9%85%8D%E4%BD%BF%E7%94%A8"><span class="toc-number">1.3.</span> <span class="toc-text">4.3 核心I/O处理流搭配使用</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-1-%E7%BC%93%E5%86%B2%E6%B5%81-BufferedInputStream-BufferedReader-%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96%E7%9A%84%E5%9F%BA%E7%9F%B3"><span class="toc-number">1.3.1.</span> <span class="toc-text">4.3.1 缓冲流 (BufferedInputStream / BufferedReader): 性能优化的基石</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E5%8E%9F%E7%90%86%E8%A7%A3%E6%9E%90"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">核心原理解析</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E5%AE%9E%E6%88%98%EF%BC%9A%E6%96%87%E4%BB%B6%E5%A4%8D%E5%88%B6%E6%80%A7%E8%83%BD%E5%AF%B9%E6%AF%94"><span class="toc-number">1.3.1.3.</span> <span class="toc-text">代码实战：文件复制性能对比</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-2-%E8%BD%AC%E6%8D%A2%E6%B5%81-InputStreamReader-OutputStreamWriter-%E5%AD%97%E8%8A%82%E4%B8%8E%E5%AD%97%E7%AC%A6%E7%9A%84%E6%A1%A5%E6%A2%81"><span class="toc-number">1.3.2.</span> <span class="toc-text">4.3.2 转换流 (InputStreamReader / OutputStreamWriter): 字节与字符的桥梁</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">核心用途</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.3.2.2.</span> <span class="toc-text">应用场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%AF%BB%E5%8F%96GBK%E6%96%87%E4%BB%B6%E5%B9%B6%E8%BD%AC%E5%AD%98%E4%B8%BAUTF-8"><span class="toc-number">1.3.2.3.</span> <span class="toc-text">代码示例：读取GBK文件并转存为UTF-8</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-3-%E5%AF%B9%E8%B1%A1%E6%B5%81-ObjectInputStream-ObjectOutputStream-%E5%AF%B9%E8%B1%A1%E7%9A%84%E5%BA%8F%E5%88%97%E5%8C%96%E4%B8%8E%E5%8F%8D%E5%BA%8F%E5%88%97%E5%8C%96"><span class="toc-number">1.3.3.</span> <span class="toc-text">4.3.3 对象流 (ObjectInputStream / ObjectOutputStream): 对象的序列化与反序列化</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-1"><span class="toc-number">1.3.3.1.</span> <span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5"><span class="toc-number">1.3.3.2.</span> <span class="toc-text">核心概念</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%85%B3%E9%94%AE%E7%9F%A5%E8%AF%86%E7%82%B9"><span class="toc-number">1.3.3.3.</span> <span class="toc-text">关键知识点</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E5%AE%9E%E6%88%98%EF%BC%9A%E5%B0%86User%E5%AF%B9%E8%B1%A1%E6%8C%81%E4%B9%85%E5%8C%96%E5%88%B0%E6%96%87%E4%BB%B6"><span class="toc-number">1.3.3.4.</span> <span class="toc-text">代码实战：将User对象持久化到文件</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-%E5%AE%9E%E6%88%98%E8%BF%9B%E9%98%B6-%E7%AE%80%E5%8C%96I-O%E6%93%8D%E4%BD%9C%E7%9A%84%E7%AC%AC%E4%B8%89%E6%96%B9%E5%BA%93"><span class="toc-number">1.4.</span> <span class="toc-text">4.4 [实战进阶] 简化I/O操作的第三方库</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-1-%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5%E4%B8%8E%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3"><span class="toc-number">1.4.1.</span> <span class="toc-text">4.4.1 面试题引入与核心思想</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5-2"><span class="toc-number">1.4.1.1.</span> <span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3%EF%BC%9A%E4%B8%BA%E4%BB%80%E4%B9%88%E9%9C%80%E8%A6%81%E7%AC%AC%E4%B8%89%E6%96%B9%E5%BA%93%EF%BC%9F"><span class="toc-number">1.4.1.2.</span> <span class="toc-text">核心思想：为什么需要第三方库？</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-2-%E4%B8%BB%E6%B5%81%E5%BA%93%E4%BB%8B%E7%BB%8D%EF%BC%9AApache-Commons-IO"><span class="toc-number">1.4.2.</span> <span class="toc-text">4.4.2 主流库介绍：Apache Commons IO</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-3-%E4%BB%A3%E7%A0%81%E5%AE%9E%E6%88%98%EF%BC%9AJDK%E5%8E%9F%E7%94%9F%E5%86%99%E6%B3%95-vs-Commons-IO-%E4%B8%80%E8%A1%8C%E4%BB%A3%E7%A0%81"><span class="toc-number">1.4.3.</span> <span class="toc-text">4.4.3 代码实战：JDK原生写法 vs. Commons IO 一行代码</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E8%AF%BB%E5%8F%96%E6%95%B4%E4%B8%AA%E6%96%87%E4%BB%B6%E5%88%B0%E5%AD%97%E7%AC%A6%E4%B8%B2"><span class="toc-number">1.4.3.1.</span> <span class="toc-text">场景一：读取整个文件到字符串</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E5%A4%8D%E5%88%B6%E6%96%87%E4%BB%B6"><span class="toc-number">1.4.3.2.</span> <span class="toc-text">场景二：复制文件</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9A%E9%80%92%E5%BD%92%E5%88%A0%E9%99%A4%E7%9B%AE%E5%BD%95"><span class="toc-number">1.4.3.3.</span> <span class="toc-text">场景三：递归删除目录</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-5-Apache-Commons-IO-%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-number">1.5.</span> <span class="toc-text">4.5 Apache Commons IO 核心方法速查表</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#FileUtils-%E7%B1%BB%EF%BC%9A%E9%9D%A2%E5%90%91%E6%96%87%E4%BB%B6%E5%92%8C%E7%9B%AE%E5%BD%95%E7%9A%84%E6%93%8D%E4%BD%9C"><span class="toc-number">1.5.1.</span> <span class="toc-text">FileUtils 类：面向文件和目录的操作</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E8%AF%BB%E6%93%8D%E4%BD%9C-Read-Operations"><span class="toc-number">1.5.1.1.</span> <span class="toc-text">1. 读操作 (Read Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E5%86%99%E6%93%8D%E4%BD%9C-Write-Operations"><span class="toc-number">1.5.1.2.</span> <span class="toc-text">2. 写操作 (Write Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E5%A4%8D%E5%88%B6%E4%B8%8E%E7%A7%BB%E5%8A%A8-Copy-Move-Operations"><span class="toc-number">1.5.1.3.</span> <span class="toc-text">3. 复制与移动 (Copy &amp; Move Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-%E5%88%A0%E9%99%A4%E4%B8%8E%E6%B8%85%E7%A9%BA-Delete-Clean-Operations"><span class="toc-number">1.5.1.4.</span> <span class="toc-text">4. 删除与清空 (Delete &amp; Clean Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#5-%E7%8A%B6%E6%80%81%E6%A3%80%E6%9F%A5%E4%B8%8E%E5%85%B6%E5%AE%83"><span class="toc-number">1.5.1.5.</span> <span class="toc-text">5. 状态检查与其它</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#IOUtils-%E7%B1%BB%EF%BC%9A%E9%9D%A2%E5%90%91%E6%B5%81%E7%9A%84%E6%93%8D%E4%BD%9C"><span class="toc-number">1.5.2.</span> <span class="toc-text">IOUtils 类：面向流的操作</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E8%AF%BB-%E8%BD%AC%E6%8D%A2%E6%93%8D%E4%BD%9C-Read-Convert-Operations"><span class="toc-number">1.5.2.1.</span> <span class="toc-text">1. 读/转换操作 (Read / Convert Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E5%86%99%E6%93%8D%E4%BD%9C-Write-Operations-1"><span class="toc-number">1.5.2.2.</span> <span class="toc-text">2. 写操作 (Write Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E5%A4%8D%E5%88%B6%E6%93%8D%E4%BD%9C-Copy-Operations"><span class="toc-number">1.5.2.3.</span> <span class="toc-text">3. 复制操作 (Copy Operations)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-%E5%85%B3%E9%97%AD%E6%93%8D%E4%BD%9C-Close-Operations"><span class="toc-number">1.5.2.4.</span> <span class="toc-text">4. 关闭操作 (Close Operations)</span></a></li></ol></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>