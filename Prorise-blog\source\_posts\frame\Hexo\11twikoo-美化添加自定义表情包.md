---
title: 11.Twiko<PERSON> 美化：添加自定义表情包
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 65188
date: 2025-07-11 03:13:45
---

### **11.Twikoo 美化：添加自定义表情包**

###### **前言：功能介绍**

本指南将引导您如何为博客的 Twikoo 评论系统添加自定义的表情包（例如阿鲁、泡泡等），替换或扩充其默认的表情列表。

###### **核心流程概览**

1.  **获取并编辑表情包数据**：从网络资源站获取表情包的 `JSON` 数据，并根据您的需求将其合并、修改。
2.  **托管您的表情包JSON文件**：将制作好的 `JSON` 文件放到一个可以通过链接公开访问的地方。
3.  **在Twikoo后台进行配置**：告诉 Twikoo 去哪里加载您的自定义表情包。

-----

###### **第一步：获取并编辑表情包数据 (JSON)**

1.  **寻找表情包源数据**

      * 首先，我们需要找到预先制作好的表情包JSON数据。这里我们推荐一个非常好的资源：
          * **[小康的表情速查](https://emotion.xiaokang.me/)**
      * 从这个网站，您可以直接复制单个表情包的JSON代码。
      
2.  **理解并合并JSON代码 (关键步骤)**

      * 如果您只想使用**一个**表情包，可以直接复制完整的代码。例如，“阿鲁”表情包的代码结构如下：

    <!-- end list -->

    ```json
    {
      "aru": {
        "type": "image",
        "container": [
          { "text": "aru-1", "icon": "<img src='...'>" },
          { "text": "aru-99", "icon": "<img src='...'>" }
        ]
      }
    }
    ```

      * 如果您想添加**多个**表情包，您需要手动将它们合并成一个大的JSON对象。**这是最容易出错的步骤，请仔细操作**：
          * **规则1**: 确保所有内容都在一个最外层的大括号 `{}` 中。
          * **规则2**: 每个表情包的 `}` 后面，需要用一个英文逗号 `,` 隔开。
          * **规则3**: **最后**一个表情包的 `}` 后面**不能**有逗号。

    **合并示例（阿鲁 + blob）：**

    ```json
    {
      "aru": {
        "type": "image",
        "container": [
          { "text": "aru-1", "icon": "<img src='https://7.dusays.com/2021/01/15/3c76dffbc08a5.png'>" },
          { "text": "aru-99", "icon": "<img src='https://7.dusays.com/2021/01/15/a5cf180e2f22a.png'>" }
        ]
      },
      "blob": {
        "type": "image",
        "container": [
          { "text": "blob-RedTick", "icon": "<img src='https://7.dusays.com/2021/01/15/c8407d638ca85.png'>" },
          { "text": "blob-wumpusblob", "icon": "<img src='https://7.dusays.com/2021/01/15/309dc42e77869.png'>" }
        ]
      }
    }
    ```

3.  **验证JSON格式 (必做)**

      * 在保存之前，**强烈建议**您将合并后的全部内容，粘贴到“**[在线JSON格式化验证工具](https://www.json.cn/)**”中进行检查，确保没有语法错误。

-----

###### **（可选）第二步：自定义表情包标签页图标**

默认情况下，表情包的切换标签显示的是文字（如 "aru"）。如果您希望它显示为一个图标，只需将 `JSON` 中的名字（`"aru"`）替换为一个 `<img>` 标签即可。

  * **文字版**: `"aru": { ... }`
  * **图片版**: `"<img src='https://example.com/aru_icon.png' style='width:30px'>": { ... }`

-----

###### **第三步：托管您的JSON文件**

您需要将最终制作好的JSON文件，放到一个可以通过公开链接访问到的地方。最简单的方法就是放在您的博客源文件中。

1.  在您博客的 `source` 目录下，新建一个文件夹，例如 `data` **注意这里没有下划线**
2.  将您编辑好的表情包内容，保存为一个 `.json` 文件，例如 `my_emotions.json`，并放入刚刚创建的文件夹中。
      * **最终文件路径示例**：`source/data/my_emotions.json`
3.  完成这一步后，当您的博客部署后，这个文件的**公开访问链接**就是：
    `https://您的域名/data/my_emotions.json`
    请记住这个链接。

-----

###### **第四步：在Twikoo后台进行配置**

1.  **登录您的Twikoo管理后台**（访问您的云函数地址）。
2.  在后台管理界面中，找到 **“插件 (Plugins)”** 或 **“通用设置 (General)”** 选项卡。
3.  找到名为 **`EMOTION_CDN`** 或 **“自定义表情 CDN”** 的输入框。
4.  将您在第三步中得到的那个JSON文件的公开链接（例如 `https://您的域名/data/my_emotions.json`）**完整地粘贴进去**。
5.  保存设置。

-----