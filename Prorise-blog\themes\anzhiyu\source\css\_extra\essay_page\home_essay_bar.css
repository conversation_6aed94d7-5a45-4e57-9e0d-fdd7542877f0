#bbTimeList {
  background: var(--anzhiyu-white);
  color: var(--anzhiyu-fontcolor);
  padding: 0.5rem 1rem;
  border-radius: 12px;
  box-shadow: var(--anzhiyu-shadow-lightblack);
  display: flex;
  transition: all 0.3s ease 0s;
  margin: 1rem auto 0;
  border: var(--style-border);
  align-items: center;
  height: 50px;
  width: 100%;
  max-width: calc(1400px - 3rem);
  animation: slide-in .6s 0s backwards;
}
@media screen and (max-width: 1400px) {
  #bbTimeList {
    max-width: calc(100% - 3rem);
    animation: slide-in 0.6s 0s backwards;
  }
}
@media screen and (max-width: 768px) {
  div#bbTimeList {
    margin: 0 20px 15px;
  }
}
[data-theme="dark"] #bbTimeList {
  background: #000 !important;
}
#bbtalk {
  width: 100%;
}
#bber-talk {
  width: 100%;
  height: 30px;
  line-height: 30px;
  display: flex;
  flex-direction: column;
}
i.bber-logo {
  font-size: 2rem;
  line-height: 22px;
  margin-right: 1rem;
  transition: all 0.3s ease 0s;
  cursor: pointer !important;
}
i.bber-logo:hover,
i.bber-gotobb:hover {
  color: var(--anzhiyu-main);
}

i.bber-gotobb {
  line-height: 30px;
  margin-left: 1rem;
  transition: all 0.3s ease 0s;
  cursor: pointer;
}

#bber-talk .li-style {
  width: 100%;
  max-width: 100%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: 0.3s;
  font-weight: 700;
  margin: auto;
  cursor: pointer;
  white-space: nowrap;
}
#bber-talk .li-style:hover {
  color: var(--anzhiyu-main);
}

#bbTimeList:hover {
  border: var(--style-border-hover);
  box-shadow: var(--anzhiyu-shadow-main);
}

/* 文章页H1-H6图标样式效果 */
@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(-1turn);
    transform: rotate(-1turn);
  }
}
@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(-1turn);
    transform: rotate(-1turn);
  }
}
