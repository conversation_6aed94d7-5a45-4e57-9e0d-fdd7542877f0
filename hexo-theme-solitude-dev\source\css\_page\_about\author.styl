#about-page
  .author-info
    display flex
    align-items center
    margin 0 0 16px 0

    .author-tag-left
      display flex
      flex-direction column
      align-items flex-end

      .author-tag
        &:first-child,
        &:last-child
          margin-right -16px

    .author-tag-right
      display flex
      flex-direction column
      align-items flex-start

      .author-tag
        &:first-child,
        &:last-child
          margin-left -16px

    +maxWidth768()
      .author-tag-left, .author-tag-right
        display none

    .author-tag
      transform translate(0, -4px)
      padding 1px 8px
      background var(--efu-card-bg)
      border var(--style-border-always)
      border-radius 40px
      margin-top 6px
      font-size 14px
      font-weight 700
      box-shadow var(--efu-shadow-lightblack)
      animation 6s ease-in-out 0s infinite normal none running floating

      &:nth-child(1)
        animation-delay 0s

      &:nth-child(2)
        animation-delay .6s

      &:nth-child(3)
        animation-delay 1.2s

      &:nth-child(4)
        animation-delay 1.8s

    .author-img
      margin 0 30px
      border-radius 50%
      width 180px
      height 180px
      position relative
      background var(--efu-secondbg)
      user-select none
      transition .3s

      &:hover
        transform scale(1.1)

      &::before
        content ''
        transition 1s
        width 30px
        height 30px
        background var(--efu-green)
        position absolute
        border-radius 50%
        border 5px solid var(--efu-background)
        bottom 5px
        right 10px
        z-index 2

      +maxWidth768()
        width 120px
        height 120px

        &::before
          bottom -2px
          right -2px
          width 20px
          height 20px

      img
        border-radius 200px
        width 100%
        height 100%
        object-fit cover