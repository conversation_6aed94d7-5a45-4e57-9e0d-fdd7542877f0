- var limit = theme.comment.newest_comment.limit ? theme.comment.newest_comment.limit : 5
if theme.comment.use
    case theme.comment.use[0]
        when 'Twikoo'
            !=partial('includes/widgets/third-party/news-comment/twikoo', {limit}, {cache: true})
        when 'Waline'
            !=partial('includes/widgets/third-party/news-comment/waline', {limit}, {cache: true})
        when 'Valine'
            !=partial('includes/widgets/third-party/news-comment/valine', {limit}, {cache: true})
        when 'Artalk'
            !=partial('includes/widgets/third-party/news-comment/artalk', {limit}, {cache: true})