<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道 | Prorise的小站</title><meta name="keywords" content="Java微服务篇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道"><meta name="application-name" content="SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道"><meta property="og:url" content="https://prorise666.site/posts/64777.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="10. ETL 框架与 Spring Batch：构建工业级数据管道在第九章，我们通过“手动播种”数据，成功搭建了一个 RAG 问答原型，验证了其核心流程。然而，一个生产级的 RAG 应用，其成败往往取决于知识库的质量和数据更新的效率。所谓“垃圾进，垃圾出”，只有通过一个健壮、高效、可扩展的数据处理"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta name="description" content="10. ETL 框架与 Spring Batch：构建工业级数据管道在第九章，我们通过“手动播种”数据，成功搭建了一个 RAG 问答原型，验证了其核心流程。然而，一个生产级的 RAG 应用，其成败往往取决于知识库的质量和数据更新的效率。所谓“垃圾进，垃圾出”，只有通过一个健壮、高效、可扩展的数据处理"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/64777.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道",postAI:"false",pageFillDescription:"10. ETL 框架与 Spring Batch：构建工业级数据管道, 10.1 从一次性脚本到企业级作业：为何选择 Spring Batch？, 10.2 Spring AI ETL 框架深度解析, 10.2.1 API 概述与核心接口, 10.2.2 DocumentReader 详解 (数据提取), 1. JsonReader, 2. PagePdfDocumentReader (常用), 3. TikaDocumentReader (通用), 10.2.3 DocumentTransformer 详解 (数据转换), 1. TokenTextSplitter (核心), 2. SummaryMetadataEnricher (增强), 10.3 Spring Batch 核心概念入门, 10.4 实战：使用 Spring Batch 构建生产级 ETL 作业, 10.4.1 第一步：添加依赖与启用 Batch, 10.4.2 第二步：定义作业配置 (RagEtlBatchConfig.java), 10.4.3 第三步：创建 API 接口触发作业, 10.4.4 测试与验证框架与构建工业级数据管道在第九章我们通过手动播种数据成功搭建了一个问答原型验证了其核心流程然而一个生产级的应用其成败往往取决于知识库的质量和数据更新的效率所谓垃圾进垃圾出只有通过一个健壮高效可扩展的数据处理流水线我们才能为提供源源不断的高质量养料本章我们将深入探讨如何使用生态中强大的批处理框架结合的工具来构建一个真正企业级的数据摄取流水线彻底替换掉第九章中仅适用于演示的从一次性脚本到企业级作业为何选择你可能会问我们之前使用在应用启动时加载数据不是挺好的吗为什么需要引入这么重的框架对于简单的一次性的数据加载任务确实足够但当我们面对生产环境的复杂需求时它的局限性就暴露无遗了特性简单脚本方式企业级作业框架可重启性不支持如果加载个文件在第个失败则必须手动清理已入库的数据然后从头开始核心特性自动记录每个文件的处理状态任务失败后可从失败的那个文件断点续传无需重复处理已成功的部分事务管理粗糙需要手动管理大事务一旦失败回滚成本高数据一致性难以保证精细化提供基于块的事务管理可以做到处理一个文件提交一次事务确保了数据处理的原子性扩展性差默认单线程执行面对海量数据时处理速度成为瓶颈难以水平或垂直扩展极强原生支持多线程步骤和并行处理可轻松扩展以处理海量数据可监控性无执行过程像一个黑盒无法得知进度耗时错误详情等全面提供了一套完整的元数据表等详细记录每次作业执行的详情状态读写数量耗时等调度与管理原始需要自己结合等实现简单的定时任务专业可轻松与或企业级调度工具如集成并能通过进行启停查询等管理操作简而言之当我们的数据摄取任务需要可靠性可扩展性和可管理性时就是不二之选它将我们的过程从一个临时的脚本提升为了一个受管理的生产级的企业作业框架深度解析在用编排任务之前我们必须先深入了解流水线上的每一个工具即提供的组件概述与核心接口管道负责创建转换和存储实例整个流程由三个核心接口定义接口实现的函数式接口核心职责提取作为数据源提供原始的列表转换接收一批文档进行处理后返回新的一批文档加载消费一批文档并将其写入最终目的地这三个接口的设计使得我们可以用非常优雅的链式调用的方式来构建一个简单的数据处理流例如一个典型的处理流程可以这样表示这是一个演示性的函数式调用链提取转换加载接下来我们将详细探索框架为我们提供的各种和实现详解数据提取用于处理文件能将数组中的每个对象或整个文件转换为示例数据一款适合入门级越野骑行的高性能山地车为竞赛爱好者打造的空气动力学公路自行车代码示例我们可以指定只使用和字段来构成的文本内容演示只使用和字段作为文本内容读取到文档预期输出读取到文档一款适合入门级越野骑行的高性能山地车读取到文档为竞赛爱好者打造的空气动力学公路自行车常用处理文档的核心工具基于库它会将的每一页解析成一个独立的对象并自动附加页码等元数据依赖代码示例演示从中读取到页内容会自动添加页码等元数据第一页的元数据通用当需要处理等多种文档格式时是不二之选它基于强大的库能从上百种文件格式中提取纯文本内容依赖代码示例演示从中成功提取内容提取到的文本片段详解数据转换这是整个流程中技术含量最高对最终效果影响最大的一步核心其核心任务是文档分割将长文档切分为符合上下文窗口限制同时又保持语义完整的文本块核心参数作用与解释最佳实践建议块大小每个文本块的目标数量从开始实验较小值如使上下文更聚焦较大值如保留更完整上下文最小块字符数防止产生过小的无意义的碎片化文本块保持默认或根据文本特性微调块重叠相邻两个文本块之间重叠的数量至关重要设为的如它能确保一个完整的句子不会在块边界被切断代码示例假设是从读取到的列表创建一个实例每个块的目标大小为块的最小字符数相邻块之间重叠一个文档最多被分割成的块数保留分隔符如换行符应用分割器它会智能地将长文档分割并自动将元数据复制到所有分块中增强这是一个非常有用的元数据增强器它能利用为每个文档块自动生成摘要并将摘要作为新的元数据如添加回去这可以用于构建更复杂的检索策略代码示例将配置为一个在分割之后加载之前对文档块进行摘要增强与之类似可以自动提取关键词作为元数据核心概念入门在了解了的工具后我们来学习如何使用这个工厂生产线来编排它们一个典型的作业由一个或多个步骤组成最常见的步骤类型是面向块的处理它像一条精密的工厂流水线完美地契合了我们的流程读取器流水线的起点它的职责是从数据源如文件系统中读取一个数据项在我们的场景中一个就是一个待处理的文件资源处理器流水线的加工站它接收传来的单个数据项对其进行任意复杂的处理和转换如读取分割然后输出处理后的结果写入器流水线的终点它接收输出的一块处理结果并将它们写入目标系统如会以块为单位驱动数据从流动并在每个块处理完成后提交事务极大地提升了效率和健壮性实战使用构建生产级作业现在让我们动手将第十章的逻辑重构为一个结构清晰功能强大的作业第一步添加依赖与启用启用处理在主启动类上添加注解启用功能第二步定义作业配置我们将创建一个专门的配置类来定义和组装我们的作业使用默认配置第三步创建接口触发作业我们创建一个端点来按需可控地触发它会根据的名称自动注入接收到作业启动请求作业已成功异步启动启动作业失败作业启动失败测试与验证清空数据建议先通过或命令清空中的旧数据启动应用并准备在目录下放入一些文件触发作业通过向发送一个请求观察日志你会在控制台看到的详细执行日志验证数据与问答作业完成后通过我们第九章的问答接口提出一个只有你上传的中才有的问题验证是否能正确回答",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-08 13:53:38",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#10-ETL-%E6%A1%86%E6%9E%B6%E4%B8%8E-Spring-Batch%EF%BC%9A%E6%9E%84%E5%BB%BA%E5%B7%A5%E4%B8%9A%E7%BA%A7%E6%95%B0%E6%8D%AE%E7%AE%A1%E9%81%93"><span class="toc-text">10. ETL 框架与 Spring Batch：构建工业级数据管道</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-%E4%BB%8E%E2%80%9C%E4%B8%80%E6%AC%A1%E6%80%A7%E8%84%9A%E6%9C%AC%E2%80%9D%E5%88%B0%E2%80%9C%E4%BC%81%E4%B8%9A%E7%BA%A7%E4%BD%9C%E4%B8%9A%E2%80%9D%EF%BC%9A%E4%B8%BA%E4%BD%95%E9%80%89%E6%8B%A9-Spring-Batch%EF%BC%9F"><span class="toc-text">10.1 从“一次性脚本”到“企业级作业”：为何选择 Spring Batch？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-Spring-AI-ETL-%E6%A1%86%E6%9E%B6%E6%B7%B1%E5%BA%A6%E8%A7%A3%E6%9E%90"><span class="toc-text">10.2 Spring AI ETL 框架深度解析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-1-API-%E6%A6%82%E8%BF%B0%E4%B8%8E%E6%A0%B8%E5%BF%83%E6%8E%A5%E5%8F%A3"><span class="toc-text">10.2.1 API 概述与核心接口</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-2-DocumentReader-%E8%AF%A6%E8%A7%A3-%E6%95%B0%E6%8D%AE%E6%8F%90%E5%8F%96"><span class="toc-text">10.2.2 DocumentReader 详解 (数据提取)</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-JsonReader"><span class="toc-text">1. JsonReader</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-PagePdfDocumentReader-%E5%B8%B8%E7%94%A8"><span class="toc-text">2. PagePdfDocumentReader (常用)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-TikaDocumentReader-%E9%80%9A%E7%94%A8"><span class="toc-text">3. TikaDocumentReader (通用)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-3-DocumentTransformer-%E8%AF%A6%E8%A7%A3-%E6%95%B0%E6%8D%AE%E8%BD%AC%E6%8D%A2"><span class="toc-text">10.2.3 DocumentTransformer 详解 (数据转换)</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-TokenTextSplitter-%E6%A0%B8%E5%BF%83"><span class="toc-text">1. TokenTextSplitter (核心)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-SummaryMetadataEnricher-%E5%A2%9E%E5%BC%BA"><span class="toc-text">2. SummaryMetadataEnricher (增强)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-Spring-Batch-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%E5%85%A5%E9%97%A8"><span class="toc-text">10.3 Spring Batch 核心概念入门</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-%E5%AE%9E%E6%88%98%EF%BC%9A%E4%BD%BF%E7%94%A8-Spring-Batch-%E6%9E%84%E5%BB%BA%E7%94%9F%E4%BA%A7%E7%BA%A7-ETL-%E4%BD%9C%E4%B8%9A"><span class="toc-text">10.4 实战：使用 Spring Batch 构建生产级 ETL 作业</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-1-%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E6%B7%BB%E5%8A%A0%E4%BE%9D%E8%B5%96%E4%B8%8E%E5%90%AF%E7%94%A8-Batch"><span class="toc-text">10.4.1 第一步：添加依赖与启用 Batch</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-2-%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E5%AE%9A%E4%B9%89%E4%BD%9C%E4%B8%9A%E9%85%8D%E7%BD%AE-RagEtlBatchConfig-java"><span class="toc-text">10.4.2 第二步：定义作业配置 (RagEtlBatchConfig.java)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-3-%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA-API-%E6%8E%A5%E5%8F%A3%E8%A7%A6%E5%8F%91%E4%BD%9C%E4%B8%9A"><span class="toc-text">10.4.3 第三步：创建 API 接口触发作业</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-4-%E6%B5%8B%E8%AF%95%E4%B8%8E%E9%AA%8C%E8%AF%81"><span class="toc-text">10.4.4 测试与验证</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java微服务篇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-03-21T11:13:45.000Z" title="发表于 2025-03-21 19:13:45">2025-03-21</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:38.152Z" title="更新于 2025-07-08 13:53:38">2025-07-08</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.3k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>13分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/64777.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/64777.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url">Java微服务篇</a><h1 id="CrawlerTitle" itemprop="name headline">SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-03-21T11:13:45.000Z" title="发表于 2025-03-21 19:13:45">2025-03-21</time><time itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:38.152Z" title="更新于 2025-07-08 13:53:38">2025-07-08</time></header><div id="postchat_postcontent"><h2 id="10-ETL-框架与-Spring-Batch：构建工业级数据管道"><a href="#10-ETL-框架与-Spring-Batch：构建工业级数据管道" class="headerlink" title="10. ETL 框架与 Spring Batch：构建工业级数据管道"></a>10. ETL 框架与 Spring Batch：构建工业级数据管道</h2><p>在第九章，我们通过“手动播种”数据，成功搭建了一个 RAG 问答原型，验证了其核心流程。然而，一个生产级的 RAG 应用，其成败往往取决于知识库的质量和数据更新的效率。所谓“垃圾进，垃圾出”，只有通过一个健壮、高效、可扩展的数据处理流水线，我们才能为 AI 提供源源不断的高质量“养料”。</p><p>本章，我们将深入探讨如何使用 Spring 生态中强大的批处理框架——<strong>Spring Batch</strong>，结合 Spring AI 的 ETL 工具，来构建一个真正企业级的数据摄取（Ingestion）流水线，彻底替换掉第九章中仅适用于演示的 <code>DataSeedingService</code>。</p><h3 id="10-1-从“一次性脚本”到“企业级作业”：为何选择-Spring-Batch？"><a href="#10-1-从“一次性脚本”到“企业级作业”：为何选择-Spring-Batch？" class="headerlink" title="10.1 从“一次性脚本”到“企业级作业”：为何选择 Spring Batch？"></a>10.1 从“一次性脚本”到“企业级作业”：为何选择 Spring Batch？</h3><p>你可能会问，我们之前使用 <code>@PostConstruct</code> 在应用启动时加载数据不是挺好的吗？为什么需要引入 Spring Batch 这么“重”的框架？</p><p>对于简单的、一次性的数据加载任务，<code>@PostConstruct</code> 确实足够。但当我们面对生产环境的复杂需求时，它的局限性就暴露无遗了。</p><table><thead><tr><th align="left">特性</th><th align="left"><code>@PostConstruct</code> (简单脚本方式)</th><th align="left">Spring Batch (企业级作业框架)</th></tr></thead><tbody><tr><td align="left"><strong>可重启性</strong></td><td align="left"><strong>不支持</strong>。如果加载 1000 个文件，在第 500 个失败，则必须手动清理已入库的数据，然后从头开始。</td><td align="left"><strong>核心特性</strong>。自动记录每个文件的处理状态。任务失败后，可从失败的那个文件<strong>断点续传</strong>，无需重复处理已成功的部分。</td></tr><tr><td align="left"><strong>事务管理</strong></td><td align="left"><strong>粗糙</strong>。需要手动管理大事务，一旦失败，回滚成本高，数据一致性难以保证。</td><td align="left"><strong>精细化</strong>。提供基于块（Chunk）的事务管理，可以做到“处理一个文件，提交一次事务”，确保了数据处理的原子性。</td></tr><tr><td align="left"><strong>扩展性</strong></td><td align="left"><strong>差</strong>。默认单线程执行，面对海量数据时，处理速度成为瓶颈，难以水平或垂直扩展。</td><td align="left"><strong>极强</strong>。原生支持多线程步骤（<code>Multi-threaded Step</code>）和并行处理（<code>Parallel Steps</code>），可轻松扩展以处理海量数据。</td></tr><tr><td align="left"><strong>可监控性</strong></td><td align="left"><strong>无</strong>。执行过程像一个黑盒，无法得知进度、耗时、错误详情等。</td><td align="left"><strong>全面</strong>。提供了一套完整的元数据表（<code>BATCH_JOB_INSTANCE</code>, <code>BATCH_STEP_EXECUTION</code> 等），详细记录每次作业执行的详情、状态、读写数量、耗时等。</td></tr><tr><td align="left"><strong>调度与管理</strong></td><td align="left"><strong>原始</strong>。需要自己结合 <code>@Scheduled</code> 等实现简单的定时任务。</td><td align="left"><strong>专业</strong>。可轻松与 Spring Scheduler 或企业级调度工具（如 Quartz, Control-M）集成，并能通过 API 进行启停、查询等管理操作。</td></tr></tbody></table><p>简而言之，当我们的数据摄取任务需要<strong>可靠性、可扩展性和可管理性</strong>时，Spring Batch 就是不二之选。它将我们的 ETL 过程从一个临时的脚本，提升为了一个受管理的、生产级的企业作业。</p><h3 id="10-2-Spring-AI-ETL-框架深度解析"><a href="#10-2-Spring-AI-ETL-框架深度解析" class="headerlink" title="10.2 Spring AI ETL 框架深度解析"></a>10.2 Spring AI ETL 框架深度解析</h3><p>在用 Spring Batch 编排任务之前，我们必须先深入了解流水线上的每一个“工具”——即 Spring AI 提供的 ETL 组件。</p><h4 id="10-2-1-API-概述与核心接口"><a href="#10-2-1-API-概述与核心接口" class="headerlink" title="10.2.1 API 概述与核心接口"></a>10.2.1 API 概述与核心接口</h4><p>ETL 管道负责创建、转换和存储 <code>Document</code> 实例。整个流程由三个核心接口定义：</p><table><thead><tr><th align="left">接口</th><th align="left">实现的函数式接口</th><th align="left">核心职责</th></tr></thead><tbody><tr><td align="left"><code>DocumentReader</code></td><td align="left"><code>Supplier&lt;List&lt;Document&gt;&gt;</code></td><td align="left"><strong>提取 (Extract)</strong>：作为数据源，提供原始的 <code>Document</code> 列表。</td></tr><tr><td align="left"><code>DocumentTransformer</code></td><td align="left"><code>Function&lt;List&lt;Document&gt;, List&lt;Document&gt;&gt;</code></td><td align="left"><strong>转换 (Transform)</strong>：接收一批文档，进行处理后，返回新的一批文档。</td></tr><tr><td align="left"><code>DocumentWriter</code></td><td align="left"><code>Consumer&lt;List&lt;Document&gt;&gt;</code></td><td align="left"><strong>加载 (Load)</strong>：消费一批文档，并将其写入最终目的地。</td></tr></tbody></table><p>这三个接口的设计，使得我们可以用非常优雅的、链式调用的方式来构建一个简单的数据处理流。例如，一个典型的 PDF 处理流程可以这样表示：</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 这是一个演示性的函数式调用链</span></span><br><span class="line"><span class="comment">// 1. pdfReader.get() -&gt; 提取</span></span><br><span class="line"><span class="comment">// 2. tokenTextSplitter.apply(...) -&gt; 转换</span></span><br><span class="line"><span class="comment">// 3. vectorStore.accept(...) -&gt; 加载</span></span><br><span class="line">vectorStore.accept(tokenTextSplitter.apply(pdfReader.get()));</span><br></pre></td></tr></tbody></table></figure><p>接下来，我们将详细探索框架为我们提供的各种 <code>DocumentReader</code> 和 <code>DocumentTransformer</code> 实现。</p><h4 id="10-2-2-DocumentReader-详解-数据提取"><a href="#10-2-2-DocumentReader-详解-数据提取" class="headerlink" title="10.2.2 DocumentReader 详解 (数据提取)"></a>10.2.2 <code>DocumentReader</code> 详解 (数据提取)</h4><h5 id="1-JsonReader"><a href="#1-JsonReader" class="headerlink" title="1. JsonReader"></a><strong>1. <code>JsonReader</code></strong></h5><p>用于处理 JSON 文件，能将 JSON 数组中的每个对象或整个 JSON 文件转换为 <code>Document</code>。</p><ul><li><p><strong>示例数据 (<code>docs/bikes.json</code>)</strong>:</p><figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">[</span></span><br><span class="line">  <span class="punctuation">{</span> <span class="attr">"id"</span><span class="punctuation">:</span> <span class="number">1</span><span class="punctuation">,</span> <span class="attr">"brand"</span><span class="punctuation">:</span> <span class="string">"Trek"</span><span class="punctuation">,</span> <span class="attr">"model"</span><span class="punctuation">:</span> <span class="string">"Marlin 5"</span><span class="punctuation">,</span> <span class="attr">"description"</span><span class="punctuation">:</span> <span class="string">"一款适合入门级越野骑行的高性能山地车。"</span> <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">  <span class="punctuation">{</span> <span class="attr">"id"</span><span class="punctuation">:</span> <span class="number">2</span><span class="punctuation">,</span> <span class="attr">"brand"</span><span class="punctuation">:</span> <span class="string">"Giant"</span><span class="punctuation">,</span> <span class="attr">"model"</span><span class="punctuation">:</span> <span class="string">"TCR Advanced"</span><span class="punctuation">,</span> <span class="attr">"description"</span><span class="punctuation">:</span> <span class="string">"为竞赛爱好者打造的空气动力学公路自行车。"</span> <span class="punctuation">}</span></span><br><span class="line"><span class="punctuation">]</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>代码示例</strong>:<br>我们可以指定只使用 <code>description</code> 和 <code>brand</code> 字段来构成 <code>Document</code> 的文本内容。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">@Slf4j</span></span><br><span class="line"><span class="meta">@Component</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">JsonReaderDemo</span> {</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Value("classpath:docs/bikes.json")</span></span><br><span class="line">    <span class="keyword">private</span> Resource jsonData;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// @PostConstruct</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">run</span><span class="params">()</span> {</span><br><span class="line">        log.info(<span class="string">"--- 演示 JsonReader ---"</span>);</span><br><span class="line">        <span class="comment">// 只使用 "description" 和 "brand" 字段作为文本内容</span></span><br><span class="line">        <span class="type">JsonReader</span> <span class="variable">jsonReader</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">JsonReader</span>(jsonData, <span class="string">"description"</span>, <span class="string">"brand"</span>);</span><br><span class="line">        List&lt;Document&gt; documents = jsonReader.get();</span><br><span class="line">        documents.forEach(doc -&gt; log.info(<span class="string">"读取到JSON文档: {}"</span>, doc.getText()));</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>预期输出</strong>:</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">读取到JSON文档: {description=一款适合入门级越野骑行的高性能山地车。, brand=Trek}</span><br><span class="line">读取到JSON文档: {description=为竞赛爱好者打造的空气动力学公路自行车。, brand=Giant}</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="2-PagePdfDocumentReader-常用"><a href="#2-PagePdfDocumentReader-常用" class="headerlink" title="2. PagePdfDocumentReader (常用)"></a><strong>2. <code>PagePdfDocumentReader</code> (常用)</strong></h5><p>处理 PDF 文档的核心工具，基于 Apache PDFBox 库，它会将 PDF 的<strong>每一页</strong>解析成一个独立的 <code>Document</code> 对象，并自动附加页码等元数据。</p><ul><li><p><strong>依赖 (<code>pom.xml</code>)</strong>:</p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-pdf-document-reader<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>代码示例</strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">@Slf4j</span></span><br><span class="line"><span class="meta">@Component</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">PdfReaderDemo</span> {</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Value("classpath:docs/spring-ai-reference.pdf")</span></span><br><span class="line">    <span class="keyword">private</span> Resource pdfResource;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// @PostConstruct</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">run</span><span class="params">()</span> {</span><br><span class="line">        log.info(<span class="string">"--- 演示 PagePdfDocumentReader ---"</span>);</span><br><span class="line">        <span class="type">PagePdfDocumentReader</span> <span class="variable">pdfReader</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">PagePdfDocumentReader</span>(pdfResource);</span><br><span class="line">        List&lt;Document&gt; documents = pdfReader.get();</span><br><span class="line">        log.info(<span class="string">"从 {} 中读取到 {} 页内容。"</span>, pdfResource.getFilename(), documents.size());</span><br><span class="line">        <span class="keyword">if</span> (!documents.isEmpty()) {</span><br><span class="line">            <span class="comment">// PDF Reader 会自动添加页码等元数据</span></span><br><span class="line">            log.info(<span class="string">"第一页的元数据: {}"</span>, documents.get(<span class="number">0</span>).getMetadata());</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="3-TikaDocumentReader-通用"><a href="#3-TikaDocumentReader-通用" class="headerlink" title="3. TikaDocumentReader (通用)"></a><strong>3. <code>TikaDocumentReader</code> (通用)</strong></h5><p>当需要处理 Word (<code>.docx</code>)、PPT (<code>.pptx</code>) 等多种 Office 文档格式时，<code>TikaDocumentReader</code> 是不二之选。它基于强大的 Apache Tika 库，能从上百种文件格式中提取纯文本内容。</p><ul><li><p><strong>依赖 (<code>pom.xml</code>)</strong>:</p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-tika-document-reader<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>代码示例</strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">@Slf4j</span></span><br><span class="line"><span class="meta">@Component</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">TikaReaderDemo</span> {</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Value("classpath:docs/rag-best-practices.docx")</span></span><br><span class="line">    <span class="keyword">private</span> Resource docxResource;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// @PostConstruct</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">run</span><span class="params">()</span> {</span><br><span class="line">        log.info(<span class="string">"--- 演示 TikaDocumentReader ---"</span>);</span><br><span class="line">        <span class="type">TikaDocumentReader</span> <span class="variable">tikaReader</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">TikaDocumentReader</span>(docxResource);</span><br><span class="line">        List&lt;Document&gt; documents = tikaReader.get();</span><br><span class="line">        log.info(<span class="string">"从 {} 中成功提取内容。"</span>, docxResource.getFilename());</span><br><span class="line">        documents.forEach(doc -&gt; log.info(<span class="string">"提取到的文本片段: {}..."</span>, doc.getText().substring(<span class="number">0</span>, <span class="number">50</span>)));</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="10-2-3-DocumentTransformer-详解-数据转换"><a href="#10-2-3-DocumentTransformer-详解-数据转换" class="headerlink" title="10.2.3 DocumentTransformer 详解 (数据转换)"></a>10.2.3 <code>DocumentTransformer</code> 详解 (数据转换)</h4><p>这是整个 ETL 流程中技术含量最高、对最终 RAG 效果影响最大的一步。</p><h5 id="1-TokenTextSplitter-核心"><a href="#1-TokenTextSplitter-核心" class="headerlink" title="1. TokenTextSplitter (核心)"></a><strong>1. <code>TokenTextSplitter</code> (核心)</strong></h5><p>其核心任务是<strong>文档分割 (Splitting/Chunking)</strong>，将长文档切分为符合 LLM 上下文窗口限制、同时又保持语义完整的文本块。</p><table><thead><tr><th align="left">核心参数</th><th align="left">作用与解释</th><th align="left">最佳实践建议</th></tr></thead><tbody><tr><td align="left"><code>defaultChunkSize</code></td><td align="left"><strong>块大小</strong>：每个文本块的目标 Token 数量。</td><td align="left">从 <code>512</code> 开始实验。较小值（如256）使上下文更聚焦；较大值（如1024）保留更完整上下文。</td></tr><tr><td align="left"><code>minChunkSizeChars</code></td><td align="left"><strong>最小块字符数</strong>：防止产生过小的、无意义的碎片化文本块。</td><td align="left">保持默认或根据文本特性微调。</td></tr><tr><td align="left"><code>chunkOverlap</code></td><td align="left"><strong>块重叠</strong>：相邻两个文本块之间重叠的 Token 数量。</td><td align="left"><strong>至关重要</strong>。设为 <code>chunkSize</code> 的 10%-20%（如 <code>chunkSize=512</code>, <code>chunkOverlap=64</code>）。它能确保一个完整的句子不会在块边界被切断。</td></tr></tbody></table><ul><li><strong>代码示例</strong>:<figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 假设 documents 是从 DocumentReader 读取到的列表</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 1. 创建一个 TokenTextSplitter 实例</span></span><br><span class="line"><span class="type">TokenTextSplitter</span> <span class="variable">textSplitter</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">TokenTextSplitter</span>(</span><br><span class="line">    <span class="number">512</span>,  <span class="comment">// 每个块的目标大小为 512 token</span></span><br><span class="line">    <span class="number">100</span>,  <span class="comment">// 块的最小字符数</span></span><br><span class="line">    <span class="number">64</span>,   <span class="comment">// 相邻块之间重叠 64 token</span></span><br><span class="line">    <span class="number">10000</span>,<span class="comment">// 一个文档最多被分割成的块数</span></span><br><span class="line">    <span class="literal">true</span>  <span class="comment">// 保留分隔符（如换行符）</span></span><br><span class="line">);</span><br><span class="line"></span><br><span class="line"><span class="comment">// 2. 应用分割器，它会智能地将长文档分割，并自动将元数据复制到所有分块中</span></span><br><span class="line">List&lt;Document&gt; splitDocuments = textSplitter.apply(documents);</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="2-SummaryMetadataEnricher-增强"><a href="#2-SummaryMetadataEnricher-增强" class="headerlink" title="2. SummaryMetadataEnricher (增强)"></a><strong>2. <code>SummaryMetadataEnricher</code> (增强)</strong></h5><p>这是一个非常有用的“元数据增强器”。它能利用 <code>ChatModel</code> 为每个文档块<strong>自动生成摘要</strong>，并将摘要作为新的元数据（如 <code>section_summary</code>）添加回去。这可以用于构建更复杂的检索策略。</p><ul><li><strong>代码示例</strong>:<figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">@Configuration</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">EnricherConfig</span> {</span><br><span class="line">    <span class="comment">// 将 Enricher 配置为一个 Bean</span></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> SummaryMetadataEnricher <span class="title function_">summaryEnricher</span><span class="params">(ChatModel chatModel)</span> {</span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">SummaryMetadataEnricher</span>(chatModel, List.of(SummaryMetadataEnricher.SummaryType.CURRENT));</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="meta">@Component</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">MyTransformerDemo</span> {</span><br><span class="line">    <span class="meta">@Autowired</span></span><br><span class="line">    <span class="keyword">private</span> SummaryMetadataEnricher summaryEnricher;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">run</span><span class="params">(List&lt;Document&gt; documents)</span> {</span><br><span class="line">        <span class="comment">// 在分割之后，加载之前，对文档块进行摘要增强</span></span><br><span class="line">        List&lt;Document&gt; enrichedDocuments = <span class="built_in">this</span>.summaryEnricher.apply(documents);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><em><code>KeywordMetadataEnricher</code></em> 与之类似，可以自动提取关键词作为元数据。</li></ul><h3 id="10-3-Spring-Batch-核心概念入门"><a href="#10-3-Spring-Batch-核心概念入门" class="headerlink" title="10.3 Spring Batch 核心概念入门"></a>10.3 Spring Batch 核心概念入门</h3><p>在了解了 ETL 的“工具”后，我们来学习如何使用 Spring Batch 这个“工厂生产线”来编排它们。</p><p>一个典型的 Spring Batch 作业（Job）由一个或多个步骤（Step）组成。最常见的步骤类型是**面向块（Chunk-Oriented）**的处理，它像一条精密的工厂流水线，完美地契合了我们的 ETL 流程：</p><ol><li><strong><code>ItemReader</code> (读取器)</strong>: 流水线的<strong>起点</strong>。它的职责是从数据源（如文件系统）中<strong>读取</strong>一个数据项（Item）。在我们的场景中，一个 Item 就是一个待处理的 PDF 文件资源 (<code>Resource</code>)。</li><li><strong><code>ItemProcessor</code> (处理器)</strong>: 流水线的<strong>加工站</strong>。它接收 <code>ItemReader</code> 传来的单个数据项，对其进行任意复杂的<strong>处理</strong>和<strong>转换</strong>（如读取+分割），然后输出处理后的结果。</li><li><strong><code>ItemWriter</code> (写入器)</strong>: 流水线的<strong>终点</strong>。它接收 <code>ItemProcessor</code> 输出的一“块”（Chunk）处理结果，并将它们<strong>写入</strong>目标系统（如 <code>VectorStore</code>）。</li></ol><p>Spring Batch 会以“块”为单位，驱动数据从 Reader -&gt; Processor -&gt; Writer 流动，并在每个块处理完成后提交事务，极大地提升了效率和健壮性。</p><h3 id="10-4-实战：使用-Spring-Batch-构建生产级-ETL-作业"><a href="#10-4-实战：使用-Spring-Batch-构建生产级-ETL-作业" class="headerlink" title="10.4 实战：使用 Spring Batch 构建生产级 ETL 作业"></a>10.4 实战：使用 Spring Batch 构建生产级 ETL 作业</h3><p>现在，让我们动手将第十章的 ETL 逻辑，重构为一个结构清晰、功能强大的 Spring Batch 作业。</p><h4 id="10-4-1-第一步：添加依赖与启用-Batch"><a href="#10-4-1-第一步：添加依赖与启用-Batch" class="headerlink" title="10.4.1 第一步：添加依赖与启用 Batch"></a>10.4.1 第一步：添加依赖与启用 Batch</h4><ul><li><p><strong><code>pom.xml</code></strong>:</p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.boot<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-boot-starter-batch<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>启用 Batch 处理</strong>:<br>在主启动类上，添加 <code>@EnableBatchProcessing</code> 注解。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">@SpringBootApplication</span></span><br><span class="line"><span class="meta">@EnableBatchProcessing</span> <span class="comment">// 启用 Spring Batch 功能</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">AiCopilotBackendApplication</span> {</span><br><span class="line">    <span class="comment">// ...</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="10-4-2-第二步：定义作业配置-RagEtlBatchConfig-java"><a href="#10-4-2-第二步：定义作业配置-RagEtlBatchConfig-java" class="headerlink" title="10.4.2 第二步：定义作业配置 (RagEtlBatchConfig.java)"></a>10.4.2 第二步：定义作业配置 (<code>RagEtlBatchConfig.java</code>)</h4><p>我们将创建一个专门的配置类，来定义和组装我们的作业。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.config.batch;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.document.Document;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.reader.pdf.PagePdfDocumentReader;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.transformer.splitter.TokenTextSplitter;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.vectorstore.VectorStore;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.core.Job;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.core.Step;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.core.job.builder.JobBuilder;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.core.launch.support.RunIdIncrementer;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.core.repository.JobRepository;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.core.step.builder.StepBuilder;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.item.ItemProcessor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.item.ItemReader;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.item.ItemWriter;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.item.support.ListItemReader;</span><br><span class="line"><span class="keyword">import</span> org.springframework.beans.factory.annotation.Value;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Bean;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Configuration;</span><br><span class="line"><span class="keyword">import</span> org.springframework.core.io.Resource;</span><br><span class="line"><span class="keyword">import</span> org.springframework.transaction.PlatformTransactionManager;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Configuration</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">RagEtlBatchConfig</span> {</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Value("classpath:/docs/*.pdf")</span></span><br><span class="line">    <span class="keyword">private</span> Resource[] pdfResources;</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> ItemReader&lt;Resource&gt; <span class="title function_">pdfResourceReader</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">ListItemReader</span>&lt;&gt;(Arrays.asList(pdfResources));</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> ItemProcessor&lt;Resource, List&lt;Document&gt;&gt; <span class="title function_">ragDocumentProcessor</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> resource -&gt; {</span><br><span class="line">            <span class="type">PagePdfDocumentReader</span> <span class="variable">pdfReader</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">PagePdfDocumentReader</span>(resource);</span><br><span class="line">            List&lt;Document&gt; documents = pdfReader.get();</span><br><span class="line">            <span class="type">TokenTextSplitter</span> <span class="variable">textSplitter</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">TokenTextSplitter</span>(); <span class="comment">// 使用默认配置</span></span><br><span class="line">            <span class="keyword">return</span> textSplitter.apply(documents);</span><br><span class="line">        };</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> ItemWriter&lt;List&lt;Document&gt;&gt; <span class="title function_">vectorStoreWriter</span><span class="params">(VectorStore vectorStore)</span> {</span><br><span class="line">        <span class="keyword">return</span> chunk -&gt; chunk.getItems().forEach(vectorStore::add);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> Step <span class="title function_">ragEtlStep</span><span class="params">(JobRepository jobRepository, PlatformTransactionManager transactionManager,</span></span><br><span class="line"><span class="params">                           ItemReader&lt;Resource&gt; pdfResourceReader,</span></span><br><span class="line"><span class="params">                           ItemProcessor&lt;Resource, List&lt;Document&gt;&gt; ragDocumentProcessor,</span></span><br><span class="line"><span class="params">                           ItemWriter&lt;List&lt;Document&gt;&gt; vectorStoreWriter)</span> {</span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">StepBuilder</span>(<span class="string">"documentProcessingStep"</span>, jobRepository)</span><br><span class="line">                .&lt;Resource, List&lt;Document&gt;&gt;chunk(<span class="number">1</span>, transactionManager)</span><br><span class="line">                .reader(pdfResourceReader)</span><br><span class="line">                .processor(ragDocumentProcessor)</span><br><span class="line">                .writer(vectorStoreWriter)</span><br><span class="line">                .build();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Bean</span></span><br><span class="line">    <span class="keyword">public</span> Job <span class="title function_">ragEtlJob</span><span class="params">(JobRepository jobRepository, Step ragEtlStep)</span> {</span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">JobBuilder</span>(<span class="string">"ragEtlJob"</span>, jobRepository)</span><br><span class="line">                .incrementer(<span class="keyword">new</span> <span class="title class_">RunIdIncrementer</span>())</span><br><span class="line">                .flow(ragEtlStep)</span><br><span class="line">                .end()</span><br><span class="line">                .build();</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="10-4-3-第三步：创建-API-接口触发作业"><a href="#10-4-3-第三步：创建-API-接口触发作业" class="headerlink" title="10.4.3 第三步：创建 API 接口触发作业"></a>10.4.3 第三步：创建 API 接口触发作业</h4><p>我们创建一个 REST API 端点来按需、可控地触发它。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.controller;</span><br><span class="line"></span><br><span class="line"><span class="comment">// ... imports ...</span></span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.core.Job;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.core.JobParametersBuilder;</span><br><span class="line"><span class="keyword">import</span> org.springframework.batch.core.launch.JobLauncher;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Slf4j</span></span><br><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="meta">@RequestMapping("/api/v1/etl")</span></span><br><span class="line"><span class="meta">@RequiredArgsConstructor</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">EtlJobController</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> JobLauncher jobLauncher;</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> Job ragEtlJob; <span class="comment">// Spring 会根据 @Bean 的名称自动注入</span></span><br><span class="line"></span><br><span class="line">    <span class="meta">@PostMapping("/run")</span></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">runRagEtlJob</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">try</span> {</span><br><span class="line">            log.info(<span class="string">"接收到 ETL 作业启动请求..."</span>);</span><br><span class="line">            jobLauncher.run(ragEtlJob, <span class="keyword">new</span> <span class="title class_">JobParametersBuilder</span>()</span><br><span class="line">                    .addDate(<span class="string">"timestamp"</span>, <span class="keyword">new</span> <span class="title class_">Date</span>())</span><br><span class="line">                    .toJobParameters());</span><br><span class="line">            <span class="keyword">return</span> <span class="string">"RAG ETL 作业已成功异步启动。"</span>;</span><br><span class="line">        } <span class="keyword">catch</span> (Exception e) {</span><br><span class="line">            log.error(<span class="string">"启动 RAG ETL 作业失败"</span>, e);</span><br><span class="line">            <span class="keyword">return</span> <span class="string">"作业启动失败: "</span> + e.getMessage();</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="10-4-4-测试与验证"><a href="#10-4-4-测试与验证" class="headerlink" title="10.4.4 测试与验证"></a>10.4.4 测试与验证</h4><ol><li><strong>清空数据</strong>: 建议先通过 Navicat 或 <code>redis-cli</code> (<code>FLUSHDB</code> 命令) 清空 Redis 中的旧数据。</li><li><strong>启动应用</strong>并<strong>准备PDF</strong>：在 <code>src/main/resources/docs</code> 目录下放入一些PDF文件。</li><li><strong>触发作业</strong>：通过 Postman 向 <code>POST http://localhost:8080/api/v1/etl/run</code> 发送一个请求。</li><li><strong>观察日志</strong>: 你会在控制台看到 Spring Batch 的详细执行日志。</li><li><strong>验证数据与问答</strong>: 作业完成后，通过我们第九章的 RAG 问答接口，提出一个只有你上传的 PDF 中才有的问题，验证 AI-Copilot 是否能正确回答。</li></ol><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/64777.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/64777.html&quot;)">SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/64777.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道&amp;url=https://prorise666.site/posts/64777.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java微服务篇<span class="tagsPageCount">11</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/22322.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</div></div></a></div><div class="next-post pull-right"><a href="/posts/9442.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/5770.html" title="SpringAI（七）：7. Embedding Models：万物皆可向量化"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（七）：7. Embedding Models：万物皆可向量化</div></div></a></div><div><a href="/posts/52289.html" title="SpringAI（三）：3. 会话核心 API 深度解析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（三）：3. 会话核心 API 深度解析</div></div></a></div><div><a href="/posts/22322.html" title="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</div></div></a></div><div><a href="/posts/60609.html" title="SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南</div></div></a></div><div><a href="/posts/18714.html" title="SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用</div></div></a></div><div><a href="/posts/59358.html" title="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道",date:"2025-03-21 19:13:45",updated:"2025-07-08 13:53:38",tags:["Java微服务篇"],categories:["后端技术","Java"],content:'\n## 10\\. ETL 框架与 Spring Batch：构建工业级数据管道\n\n在第九章，我们通过“手动播种”数据，成功搭建了一个 RAG 问答原型，验证了其核心流程。然而，一个生产级的 RAG 应用，其成败往往取决于知识库的质量和数据更新的效率。所谓“垃圾进，垃圾出”，只有通过一个健壮、高效、可扩展的数据处理流水线，我们才能为 AI 提供源源不断的高质量“养料”。\n\n本章，我们将深入探讨如何使用 Spring 生态中强大的批处理框架——**Spring Batch**，结合 Spring AI 的 ETL 工具，来构建一个真正企业级的数据摄取（Ingestion）流水线，彻底替换掉第九章中仅适用于演示的 `DataSeedingService`。\n\n### 10.1 从“一次性脚本”到“企业级作业”：为何选择 Spring Batch？\n\n你可能会问，我们之前使用 `@PostConstruct` 在应用启动时加载数据不是挺好的吗？为什么需要引入 Spring Batch 这么“重”的框架？\n\n对于简单的、一次性的数据加载任务，`@PostConstruct` 确实足够。但当我们面对生产环境的复杂需求时，它的局限性就暴露无遗了。\n\n| 特性 | `@PostConstruct` (简单脚本方式) | Spring Batch (企业级作业框架) |\n| :--- | :--- | :--- |\n| **可重启性** | **不支持**。如果加载 1000 个文件，在第 500 个失败，则必须手动清理已入库的数据，然后从头开始。 | **核心特性**。自动记录每个文件的处理状态。任务失败后，可从失败的那个文件**断点续传**，无需重复处理已成功的部分。 |\n| **事务管理** | **粗糙**。需要手动管理大事务，一旦失败，回滚成本高，数据一致性难以保证。 | **精细化**。提供基于块（Chunk）的事务管理，可以做到“处理一个文件，提交一次事务”，确保了数据处理的原子性。 |\n| **扩展性** | **差**。默认单线程执行，面对海量数据时，处理速度成为瓶颈，难以水平或垂直扩展。 | **极强**。原生支持多线程步骤（`Multi-threaded Step`）和并行处理（`Parallel Steps`），可轻松扩展以处理海量数据。 |\n| **可监控性** | **无**。执行过程像一个黑盒，无法得知进度、耗时、错误详情等。 | **全面**。提供了一套完整的元数据表（`BATCH_JOB_INSTANCE`, `BATCH_STEP_EXECUTION` 等），详细记录每次作业执行的详情、状态、读写数量、耗时等。 |\n| **调度与管理** | **原始**。需要自己结合 `@Scheduled` 等实现简单的定时任务。 | **专业**。可轻松与 Spring Scheduler 或企业级调度工具（如 Quartz, Control-M）集成，并能通过 API 进行启停、查询等管理操作。 |\n\n简而言之，当我们的数据摄取任务需要**可靠性、可扩展性和可管理性**时，Spring Batch 就是不二之选。它将我们的 ETL 过程从一个临时的脚本，提升为了一个受管理的、生产级的企业作业。\n\n### 10.2 Spring AI ETL 框架深度解析\n\n在用 Spring Batch 编排任务之前，我们必须先深入了解流水线上的每一个“工具”——即 Spring AI 提供的 ETL 组件。\n\n#### 10.2.1 API 概述与核心接口\n\nETL 管道负责创建、转换和存储 `Document` 实例。整个流程由三个核心接口定义：\n\n| 接口 | 实现的函数式接口 | 核心职责 |\n| :--- | :--- | :--- |\n| `DocumentReader` | `Supplier<List<Document>>` | **提取 (Extract)**：作为数据源，提供原始的 `Document` 列表。 |\n| `DocumentTransformer`| `Function<List<Document>, List<Document>>` | **转换 (Transform)**：接收一批文档，进行处理后，返回新的一批文档。 |\n| `DocumentWriter` | `Consumer<List<Document>>` | **加载 (Load)**：消费一批文档，并将其写入最终目的地。 |\n\n这三个接口的设计，使得我们可以用非常优雅的、链式调用的方式来构建一个简单的数据处理流。例如，一个典型的 PDF 处理流程可以这样表示：\n\n```java\n// 这是一个演示性的函数式调用链\n// 1. pdfReader.get() -> 提取\n// 2. tokenTextSplitter.apply(...) -> 转换\n// 3. vectorStore.accept(...) -> 加载\nvectorStore.accept(tokenTextSplitter.apply(pdfReader.get()));\n```\n\n接下来，我们将详细探索框架为我们提供的各种 `DocumentReader` 和 `DocumentTransformer` 实现。\n\n#### 10.2.2 `DocumentReader` 详解 (数据提取)\n\n##### **1. `JsonReader`**\n\n用于处理 JSON 文件，能将 JSON 数组中的每个对象或整个 JSON 文件转换为 `Document`。\n\n  * **示例数据 (`docs/bikes.json`)**:\n\n    ```json\n    [\n      { "id": 1, "brand": "Trek", "model": "Marlin 5", "description": "一款适合入门级越野骑行的高性能山地车。" },\n      { "id": 2, "brand": "Giant", "model": "TCR Advanced", "description": "为竞赛爱好者打造的空气动力学公路自行车。" }\n    ]\n    ```\n\n  * **代码示例**:\n    我们可以指定只使用 `description` 和 `brand` 字段来构成 `Document` 的文本内容。\n\n    ```java\n    @Slf4j\n    @Component\n    public class JsonReaderDemo {\n\n        @Value("classpath:docs/bikes.json")\n        private Resource jsonData;\n\n        // @PostConstruct\n        public void run() {\n            log.info("--- 演示 JsonReader ---");\n            // 只使用 "description" 和 "brand" 字段作为文本内容\n            JsonReader jsonReader = new JsonReader(jsonData, "description", "brand");\n            List<Document> documents = jsonReader.get();\n            documents.forEach(doc -> log.info("读取到JSON文档: {}", doc.getText()));\n        }\n    }\n    ```\n\n  * **预期输出**:\n\n    ```\n    读取到JSON文档: {description=一款适合入门级越野骑行的高性能山地车。, brand=Trek}\n    读取到JSON文档: {description=为竞赛爱好者打造的空气动力学公路自行车。, brand=Giant}\n    ```\n\n##### **2. `PagePdfDocumentReader` (常用)**\n\n处理 PDF 文档的核心工具，基于 Apache PDFBox 库，它会将 PDF 的**每一页**解析成一个独立的 `Document` 对象，并自动附加页码等元数据。\n\n  * **依赖 (`pom.xml`)**:\n\n    ```xml\n    <dependency>\n        <groupId>org.springframework.ai</groupId>\n        <artifactId>spring-ai-pdf-document-reader</artifactId>\n    </dependency>\n    ```\n\n  * **代码示例**:\n\n    ```java\n    @Slf4j\n    @Component\n    public class PdfReaderDemo {\n\n        @Value("classpath:docs/spring-ai-reference.pdf")\n        private Resource pdfResource;\n\n        // @PostConstruct\n        public void run() {\n            log.info("--- 演示 PagePdfDocumentReader ---");\n            PagePdfDocumentReader pdfReader = new PagePdfDocumentReader(pdfResource);\n            List<Document> documents = pdfReader.get();\n            log.info("从 {} 中读取到 {} 页内容。", pdfResource.getFilename(), documents.size());\n            if (!documents.isEmpty()) {\n                // PDF Reader 会自动添加页码等元数据\n                log.info("第一页的元数据: {}", documents.get(0).getMetadata());\n            }\n        }\n    }\n    ```\n\n##### **3. `TikaDocumentReader` (通用)**\n\n当需要处理 Word (`.docx`)、PPT (`.pptx`) 等多种 Office 文档格式时，`TikaDocumentReader` 是不二之选。它基于强大的 Apache Tika 库，能从上百种文件格式中提取纯文本内容。\n\n  * **依赖 (`pom.xml`)**:\n\n    ```xml\n    <dependency>\n        <groupId>org.springframework.ai</groupId>\n        <artifactId>spring-ai-tika-document-reader</artifactId>\n    </dependency>\n    ```\n\n  * **代码示例**:\n\n    ```java\n    @Slf4j\n    @Component\n    public class TikaReaderDemo {\n\n        @Value("classpath:docs/rag-best-practices.docx")\n        private Resource docxResource;\n\n        // @PostConstruct\n        public void run() {\n            log.info("--- 演示 TikaDocumentReader ---");\n            TikaDocumentReader tikaReader = new TikaDocumentReader(docxResource);\n            List<Document> documents = tikaReader.get();\n            log.info("从 {} 中成功提取内容。", docxResource.getFilename());\n            documents.forEach(doc -> log.info("提取到的文本片段: {}...", doc.getText().substring(0, 50)));\n        }\n    }\n    ```\n\n#### 10.2.3 `DocumentTransformer` 详解 (数据转换)\n\n这是整个 ETL 流程中技术含量最高、对最终 RAG 效果影响最大的一步。\n\n##### **1. `TokenTextSplitter` (核心)**\n\n其核心任务是**文档分割 (Splitting/Chunking)**，将长文档切分为符合 LLM 上下文窗口限制、同时又保持语义完整的文本块。\n\n| 核心参数 | 作用与解释 | 最佳实践建议 |\n| :--- | :--- | :--- |\n| `defaultChunkSize` | **块大小**：每个文本块的目标 Token 数量。 | 从 `512` 开始实验。较小值（如256）使上下文更聚焦；较大值（如1024）保留更完整上下文。 |\n| `minChunkSizeChars`| **最小块字符数**：防止产生过小的、无意义的碎片化文本块。 | 保持默认或根据文本特性微调。 |\n| `chunkOverlap` | **块重叠**：相邻两个文本块之间重叠的 Token 数量。 | **至关重要**。设为 `chunkSize` 的 10%-20%（如 `chunkSize=512`, `chunkOverlap=64`）。它能确保一个完整的句子不会在块边界被切断。 |\n\n  * **代码示例**:\n    ```java\n    // 假设 documents 是从 DocumentReader 读取到的列表\n\n    // 1. 创建一个 TokenTextSplitter 实例\n    TokenTextSplitter textSplitter = new TokenTextSplitter(\n        512,  // 每个块的目标大小为 512 token\n        100,  // 块的最小字符数\n        64,   // 相邻块之间重叠 64 token\n        10000,// 一个文档最多被分割成的块数\n        true  // 保留分隔符（如换行符）\n    );\n\n    // 2. 应用分割器，它会智能地将长文档分割，并自动将元数据复制到所有分块中\n    List<Document> splitDocuments = textSplitter.apply(documents);\n    ```\n\n##### **2. `SummaryMetadataEnricher` (增强)**\n\n这是一个非常有用的“元数据增强器”。它能利用 `ChatModel` 为每个文档块**自动生成摘要**，并将摘要作为新的元数据（如 `section_summary`）添加回去。这可以用于构建更复杂的检索策略。\n\n  * **代码示例**:\n    ```java\n    @Configuration\n    class EnricherConfig {\n        // 将 Enricher 配置为一个 Bean\n        @Bean\n        public SummaryMetadataEnricher summaryEnricher(ChatModel chatModel) {\n            return new SummaryMetadataEnricher(chatModel, List.of(SummaryMetadataEnricher.SummaryType.CURRENT));\n        }\n    }\n\n    @Component\n    class MyTransformerDemo {\n        @Autowired\n        private SummaryMetadataEnricher summaryEnricher;\n\n        public void run(List<Document> documents) {\n            // 在分割之后，加载之前，对文档块进行摘要增强\n            List<Document> enrichedDocuments = this.summaryEnricher.apply(documents);\n        }\n    }\n    ```\n    *`KeywordMetadataEnricher`* 与之类似，可以自动提取关键词作为元数据。\n\n### 10.3 Spring Batch 核心概念入门\n\n在了解了 ETL 的“工具”后，我们来学习如何使用 Spring Batch 这个“工厂生产线”来编排它们。\n\n一个典型的 Spring Batch 作业（Job）由一个或多个步骤（Step）组成。最常见的步骤类型是\\*\\*面向块（Chunk-Oriented）\\*\\*的处理，它像一条精密的工厂流水线，完美地契合了我们的 ETL 流程：\n\n1.  **`ItemReader` (读取器)**: 流水线的**起点**。它的职责是从数据源（如文件系统）中**读取**一个数据项（Item）。在我们的场景中，一个 Item 就是一个待处理的 PDF 文件资源 (`Resource`)。\n2.  **`ItemProcessor` (处理器)**: 流水线的**加工站**。它接收 `ItemReader` 传来的单个数据项，对其进行任意复杂的**处理**和**转换**（如读取+分割），然后输出处理后的结果。\n3.  **`ItemWriter` (写入器)**: 流水线的**终点**。它接收 `ItemProcessor` 输出的一“块”（Chunk）处理结果，并将它们**写入**目标系统（如 `VectorStore`）。\n\nSpring Batch 会以“块”为单位，驱动数据从 Reader -\\> Processor -\\> Writer 流动，并在每个块处理完成后提交事务，极大地提升了效率和健壮性。\n\n### 10.4 实战：使用 Spring Batch 构建生产级 ETL 作业\n\n现在，让我们动手将第十章的 ETL 逻辑，重构为一个结构清晰、功能强大的 Spring Batch 作业。\n\n#### 10.4.1 第一步：添加依赖与启用 Batch\n\n  * **`pom.xml`**:\n\n    ```xml\n    <dependency>\n        <groupId>org.springframework.boot</groupId>\n        <artifactId>spring-boot-starter-batch</artifactId>\n    </dependency>\n    ```\n\n  * **启用 Batch 处理**:\n    在主启动类上，添加 `@EnableBatchProcessing` 注解。\n\n    ```java\n    @SpringBootApplication\n    @EnableBatchProcessing // 启用 Spring Batch 功能\n    public class AiCopilotBackendApplication {\n        // ...\n    }\n    ```\n\n#### 10.4.2 第二步：定义作业配置 (`RagEtlBatchConfig.java`)\n\n我们将创建一个专门的配置类，来定义和组装我们的作业。\n\n```java\npackage com.copilot.aicopilotbackend.config.batch;\n\nimport org.springframework.ai.document.Document;\nimport org.springframework.ai.reader.pdf.PagePdfDocumentReader;\nimport org.springframework.ai.transformer.splitter.TokenTextSplitter;\nimport org.springframework.ai.vectorstore.VectorStore;\nimport org.springframework.batch.core.Job;\nimport org.springframework.batch.core.Step;\nimport org.springframework.batch.core.job.builder.JobBuilder;\nimport org.springframework.batch.core.launch.support.RunIdIncrementer;\nimport org.springframework.batch.core.repository.JobRepository;\nimport org.springframework.batch.core.step.builder.StepBuilder;\nimport org.springframework.batch.item.ItemProcessor;\nimport org.springframework.batch.item.ItemReader;\nimport org.springframework.batch.item.ItemWriter;\nimport org.springframework.batch.item.support.ListItemReader;\nimport org.springframework.beans.factory.annotation.Value;\nimport org.springframework.context.annotation.Bean;\nimport org.springframework.context.annotation.Configuration;\nimport org.springframework.core.io.Resource;\nimport org.springframework.transaction.PlatformTransactionManager;\n\nimport java.util.Arrays;\nimport java.util.List;\n\n@Configuration\npublic class RagEtlBatchConfig {\n\n    @Value("classpath:/docs/*.pdf")\n    private Resource[] pdfResources;\n\n    @Bean\n    public ItemReader<Resource> pdfResourceReader() {\n        return new ListItemReader<>(Arrays.asList(pdfResources));\n    }\n\n    @Bean\n    public ItemProcessor<Resource, List<Document>> ragDocumentProcessor() {\n        return resource -> {\n            PagePdfDocumentReader pdfReader = new PagePdfDocumentReader(resource);\n            List<Document> documents = pdfReader.get();\n            TokenTextSplitter textSplitter = new TokenTextSplitter(); // 使用默认配置\n            return textSplitter.apply(documents);\n        };\n    }\n\n    @Bean\n    public ItemWriter<List<Document>> vectorStoreWriter(VectorStore vectorStore) {\n        return chunk -> chunk.getItems().forEach(vectorStore::add);\n    }\n\n    @Bean\n    public Step ragEtlStep(JobRepository jobRepository, PlatformTransactionManager transactionManager,\n                           ItemReader<Resource> pdfResourceReader,\n                           ItemProcessor<Resource, List<Document>> ragDocumentProcessor,\n                           ItemWriter<List<Document>> vectorStoreWriter) {\n        return new StepBuilder("documentProcessingStep", jobRepository)\n                .<Resource, List<Document>>chunk(1, transactionManager)\n                .reader(pdfResourceReader)\n                .processor(ragDocumentProcessor)\n                .writer(vectorStoreWriter)\n                .build();\n    }\n\n    @Bean\n    public Job ragEtlJob(JobRepository jobRepository, Step ragEtlStep) {\n        return new JobBuilder("ragEtlJob", jobRepository)\n                .incrementer(new RunIdIncrementer())\n                .flow(ragEtlStep)\n                .end()\n                .build();\n    }\n}\n```\n\n#### 10.4.3 第三步：创建 API 接口触发作业\n\n我们创建一个 REST API 端点来按需、可控地触发它。\n\n```java\npackage com.copilot.aicopilotbackend.controller;\n\n// ... imports ...\nimport org.springframework.batch.core.Job;\nimport org.springframework.batch.core.JobParametersBuilder;\nimport org.springframework.batch.core.launch.JobLauncher;\n\n@Slf4j\n@RestController\n@RequestMapping("/api/v1/etl")\n@RequiredArgsConstructor\npublic class EtlJobController {\n\n    private final JobLauncher jobLauncher;\n    private final Job ragEtlJob; // Spring 会根据 @Bean 的名称自动注入\n\n    @PostMapping("/run")\n    public String runRagEtlJob() {\n        try {\n            log.info("接收到 ETL 作业启动请求...");\n            jobLauncher.run(ragEtlJob, new JobParametersBuilder()\n                    .addDate("timestamp", new Date())\n                    .toJobParameters());\n            return "RAG ETL 作业已成功异步启动。";\n        } catch (Exception e) {\n            log.error("启动 RAG ETL 作业失败", e);\n            return "作业启动失败: " + e.getMessage();\n        }\n    }\n}\n```\n\n#### 10.4.4 测试与验证\n\n1.  **清空数据**: 建议先通过 Navicat 或 `redis-cli` (`FLUSHDB` 命令) 清空 Redis 中的旧数据。\n2.  **启动应用**并**准备PDF**：在 `src/main/resources/docs` 目录下放入一些PDF文件。\n3.  **触发作业**：通过 Postman 向 `POST http://localhost:8080/api/v1/etl/run` 发送一个请求。\n4.  **观察日志**: 你会在控制台看到 Spring Batch 的详细执行日志。\n5.  **验证数据与问答**: 作业完成后，通过我们第九章的 RAG 问答接口，提出一个只有你上传的 PDF 中才有的问题，验证 AI-Copilot 是否能正确回答。\n\n\n\n\n\n-----'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#10-ETL-%E6%A1%86%E6%9E%B6%E4%B8%8E-Spring-Batch%EF%BC%9A%E6%9E%84%E5%BB%BA%E5%B7%A5%E4%B8%9A%E7%BA%A7%E6%95%B0%E6%8D%AE%E7%AE%A1%E9%81%93"><span class="toc-number">1.</span> <span class="toc-text">10. ETL 框架与 Spring Batch：构建工业级数据管道</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-%E4%BB%8E%E2%80%9C%E4%B8%80%E6%AC%A1%E6%80%A7%E8%84%9A%E6%9C%AC%E2%80%9D%E5%88%B0%E2%80%9C%E4%BC%81%E4%B8%9A%E7%BA%A7%E4%BD%9C%E4%B8%9A%E2%80%9D%EF%BC%9A%E4%B8%BA%E4%BD%95%E9%80%89%E6%8B%A9-Spring-Batch%EF%BC%9F"><span class="toc-number">1.1.</span> <span class="toc-text">10.1 从“一次性脚本”到“企业级作业”：为何选择 Spring Batch？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-Spring-AI-ETL-%E6%A1%86%E6%9E%B6%E6%B7%B1%E5%BA%A6%E8%A7%A3%E6%9E%90"><span class="toc-number">1.2.</span> <span class="toc-text">10.2 Spring AI ETL 框架深度解析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-1-API-%E6%A6%82%E8%BF%B0%E4%B8%8E%E6%A0%B8%E5%BF%83%E6%8E%A5%E5%8F%A3"><span class="toc-number">1.2.1.</span> <span class="toc-text">10.2.1 API 概述与核心接口</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-2-DocumentReader-%E8%AF%A6%E8%A7%A3-%E6%95%B0%E6%8D%AE%E6%8F%90%E5%8F%96"><span class="toc-number">1.2.2.</span> <span class="toc-text">10.2.2 DocumentReader 详解 (数据提取)</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-JsonReader"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">1. JsonReader</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-PagePdfDocumentReader-%E5%B8%B8%E7%94%A8"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">2. PagePdfDocumentReader (常用)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-TikaDocumentReader-%E9%80%9A%E7%94%A8"><span class="toc-number">1.2.2.3.</span> <span class="toc-text">3. TikaDocumentReader (通用)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-3-DocumentTransformer-%E8%AF%A6%E8%A7%A3-%E6%95%B0%E6%8D%AE%E8%BD%AC%E6%8D%A2"><span class="toc-number">1.2.3.</span> <span class="toc-text">10.2.3 DocumentTransformer 详解 (数据转换)</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-TokenTextSplitter-%E6%A0%B8%E5%BF%83"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">1. TokenTextSplitter (核心)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-SummaryMetadataEnricher-%E5%A2%9E%E5%BC%BA"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">2. SummaryMetadataEnricher (增强)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-Spring-Batch-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%E5%85%A5%E9%97%A8"><span class="toc-number">1.3.</span> <span class="toc-text">10.3 Spring Batch 核心概念入门</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-%E5%AE%9E%E6%88%98%EF%BC%9A%E4%BD%BF%E7%94%A8-Spring-Batch-%E6%9E%84%E5%BB%BA%E7%94%9F%E4%BA%A7%E7%BA%A7-ETL-%E4%BD%9C%E4%B8%9A"><span class="toc-number">1.4.</span> <span class="toc-text">10.4 实战：使用 Spring Batch 构建生产级 ETL 作业</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-1-%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E6%B7%BB%E5%8A%A0%E4%BE%9D%E8%B5%96%E4%B8%8E%E5%90%AF%E7%94%A8-Batch"><span class="toc-number">1.4.1.</span> <span class="toc-text">10.4.1 第一步：添加依赖与启用 Batch</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-2-%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E5%AE%9A%E4%B9%89%E4%BD%9C%E4%B8%9A%E9%85%8D%E7%BD%AE-RagEtlBatchConfig-java"><span class="toc-number">1.4.2.</span> <span class="toc-text">10.4.2 第二步：定义作业配置 (RagEtlBatchConfig.java)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-3-%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA-API-%E6%8E%A5%E5%8F%A3%E8%A7%A6%E5%8F%91%E4%BD%9C%E4%B8%9A"><span class="toc-number">1.4.3.</span> <span class="toc-text">10.4.3 第三步：创建 API 接口触发作业</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-4-%E6%B5%8B%E8%AF%95%E4%B8%8E%E9%AA%8C%E8%AF%81"><span class="toc-number">1.4.4.</span> <span class="toc-text">10.4.4 测试与验证</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>