#category
  #category-bar
    padding .4rem .6rem
    border none
    height auto
    border var(--style-border)
    box-shadow none

    +maxWidth768()
      border-radius 0
      background var(--efu-background)
      position -webkit-sticky
      position sticky
      z-index 1
      padding 0
      height 50px
      margin-top 0
      align-items center

#page
  .category-lists
    padding 1rem 0 1.5rem

    +maxWidth768()
      padding 0

    ul
      display flex
      flex-wrap wrap
      flex-direction row
      margin-top .4rem
      padding 0 0 0 1rem
      list-style none
      counter-reset li 0

      ul
        padding-left .2rem

      li
        position relative
        margin .3rem 0
        padding 4px 16px
        width 200px
        border-radius 12px
        background var(--efu-card-bg)
        border var(--style-border-always)

        &::before
          display none
          position absolute
          left 0
          cursor pointer
          transition all .3s ease-out 0s
          top .7em
          width .43em
          height .43em
          border .215em solid #307af6
          border-radius .43em
          background 0 0
          content ""

        &:hover::before
          border-color #ff7242

    .category-title
      font-size 2.57em

      +maxWidth768()
        font-size 2em

    .tag-cloud-list
      gap 1rem
      a
        display flex
        width fit-content
        color var(--efu-fontcolor)
        font-size 1.4em
        padding 0.2em 0.5em
        background var(--efu-card-bg)
        border-radius 12px
        border var(--style-border-always)
        box-shadow var(--efu-shadow-border)
        align-items center
        transition all .3s ease 0s

        +maxWidth768()
          zoom .85

        span.tags-punctuation
          transition none

        &:hover
          background var(--efu-main) !important
          box-shadow var(--efu-shadow-blue)
          color var(--efu-white) !important
          border var(--style-border-hover)

          span.tagsPageCount
            color var(--efu-lighttext)

          span.tags-punctuation
            color inherit

span.tagsPageCount
  background var(--efu-secondbg)
  padding 4px 4px
  border-radius 8px
  color var(--efu-secondtext)
  line-height 1
  text-align center
  min-width 35px
  display inline-block
  font-size 1rem
  margin-left 4px