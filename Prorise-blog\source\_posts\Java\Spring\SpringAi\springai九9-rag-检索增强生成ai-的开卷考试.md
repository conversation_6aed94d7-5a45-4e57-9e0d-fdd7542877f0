---
title: SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: true
abbrlink: 22322
date: 2025-03-21 16:13:45
---

## 9. RAG 检索增强生成：AI 的“开卷考试”

在人工智能的发展历程中，大语言模型（LLM）已经展现了惊人的“博闻强记”能力——它们能写诗、编码、解答常识问题，甚至模拟人类对话。然而，当面对专业领域知识或实时更新的信息时，这些模型往往会暴露其局限性：要么“一本正经地胡说八道”（产生幻觉），要么无奈承认“我不知道”。

此时，**RAG (Retrieval-Augmented Generation)** 就要闪亮登场了。它并非一种新的大模型，而是一种**更聪明的、将大模型与外部知识库相结合的技术架构范式**。它旨在让 AI 从一个“博闻强记”的通才，进化成一个能够引经据典、言之有物的“博学多才”的领域专家。

### 9.1 RAG：为何需要它，它又是什么？

#### 9.1.1 LLM 的三大核心“痛点”

要理解 RAG 的价值，我们必须先直面当前大语言模型（LLM）在企业级应用中难以回避的三大局限性：

| LLM 核心痛点 | 具体描述 | 对我们 AI-Copilot 项目的影响 |
| :--- | :--- | :--- |
| **知识盲区** | LLM 的知识被“冻结”在它训练完成的那一刻。它对训练日期之后发生的任何事件、发布的任何新技术都一无所知。 | 如果我们想让 AI-Copilot 解答关于公司最新发布的 `v3.5` 版本框架的新特性，一个在 `v3.0` 时代训练的 LLM 将完全无法回答。 |
| **事实幻觉 ** | 当面对其知识范围之外或不确定的问题时，LLM 倾向于“创造”听起来非常流利和自信，但事实基础却是错误的答案。 | 如果用户询问一个 API 的用法，AI 幻觉出一个不存在的参数，这可能会导致生产事故，是绝对不可接受的。 |
| **数据隐私与领域特定性** | 公开的 LLM 无法访问我们企业内部的私有数据，如内部技术文档、项目资料、客户支持记录、财务报告等。 | AI-Copilot 无法成为一个真正懂我们公司业务、能解答内部问题的“专家”，其应用场景将大受限制。 |

#### 9.1.2 RAG 的优雅解决方案

**检索增强生成 (RAG)** 正是解决上述所有问题的优雅方案。

它的工作流程可以完美地比喻为让 AI 从“**闭卷考试**”升级为“**指定参考资料的开卷考试**”。

它包含两个核心阶段：

* **1. 检索 (Retrieve):**
    * **目标**：大海捞针，精准定位。
    * **流程**：当用户提出问题时（例如，“如何配置 Spring Batch 的事务？”），系统并**不直接**将问题发送给 LLM。而是首先将用户问题作为**查询指令**，在我们自己构建和维护的、可信的外部知识库（即在第八章中学习的 **`VectorStore`**）中进行**相似性搜索**，找到与问题语义最相关的若干信息片段（`Relevant Documents`）。例如，它可能会找到公司内部《Spring Batch 最佳实践.pdf》的第三章第五节。

* **2. 增强生成 (Augmented Generate):**
    * **目标**：据理力争，言之有物。
    * **流程**：系统将上一步检索到的相关信息片段作为“**上下文 (Context)**”，与用户的原始问题**一同**“喂”给 LLM，并下达一个明确的指令：“**请严格基于我提供的这些参考资料来回答这个问题**”。

通过这种方式，LLM（考生）不再仅凭其固有的、可能过时的记忆来作答，而是有了我们为它精心准备的、权威且实时的参考资料。这使得它能够生成更准确、更具时效性、且能理解我们私有知识的答案，同时极大地抑制了“事实幻觉”的产生。

#### 9.1.3 文档 (Document)：知识的原子化表示

在 Spring AI 的世界里，`Document` 是知识的原子单位。它不仅仅是一段纯文本，而是一个结构化的对象。

| `Document` 核心属性 | 解释 | 示例 |
| :--- | :--- | :--- |
| **id** | 文档的唯一标识符。 | `“doc_chunk_123”` |
| **text** | **(核心)** 文档的主要文本内容。 | `"TokenTextSplitter 是一个关键工具..."` |
| **metadata** | **(关键)** 一个存储任意键值对的 Map，用于描述文档的元信息。 | `{"source": "manual.pdf", "page": 5, "author": "Sam"}` |

`metadata` 对于 RAG 系统至关重要，它使得我们可以在检索时进行精确过滤（例如，“只在 `manual.pdf` 这本书里查找”），并且在返回结果时能够告诉用户答案的来源，实现**可追溯性**。


---

### 9.2 Spring AI 实现 RAG 的核心

在实际开发中，仅靠向量检索并不能直接构建出稳健的问答系统。Spring AI 提供了多个模块化组件，帮助我们快速、灵活地实现**可控、可调、可插拔的 RAG 流程**。本节将由浅入深梳理 Spring AI 中与 RAG 强相关的组件，并结合实战代码说明其意义。

#### 9.2.1 RAG 的模块化构建图

Spring AI 的 RAG 实现将复杂流程拆解为如下关键组件：

```
Query (用户输入的问题)
  ↓
QueryTransformer (对查询进行优化：重写、翻译、压缩等，提高检索效果)
  ↓
DocumentRetriever (从向量数据库中检索相关文档)
  ↓
DocumentPostProcessor (对检索结果进行后处理：去重、排序、截断等)
  ↓
PromptTemplate (将用户问题和文档上下文组合为提示词，作为模型输入)
  ↓
LLM (大语言模型基于上下文生成回答)
  ↓
Response (最终返回的答案，结合了文档与问题)

```

其中每一步都可以独立扩展或替换，大大增强了系统的可维护性和灵活性。

---

#### 9.2.2 基础组件：QuestionAnswerAdvisor

Spring AI 提供的 `QuestionAnswerAdvisor` 是 RAG 的核心实现之一，适用于「标准问答」类型的 RAG 任务。

适用于基于向量数据库进行知识问答，用户问题结构清晰（无上下文）且需要较高检索与回答精准度

##### 工作流程：

1. 使用用户问题向 `VectorStore` 执行语义检索；
2. 将检索到的文档拼接成上下文；
3. 注入 Prompt 模板中，引导 LLM 给出答案。

##### 核心配置项（实战已使用）：

```java
QuestionAnswerAdvisor.builder(vectorStore)
    .searchRequest(SearchRequest.builder()
        .similarityThreshold(0.8)  // 相似度阈值
        .topK(4)                   // 返回前 K 条
        .build())
    .build();
```

在我们的实战中 `RagConfig.java` 中注册了 QuestionAnswerAdvisor Bean，控制检索门槛与返回数量，配合 `ChatClient` 实现了优雅的 RAG 问答流程。

---

#### 9.2.3 进阶控制：自定义 PromptTemplate

`QuestionAnswerAdvisor` 默认拼接的上下文格式虽然通用，但在高阶项目中，往往需要更清晰的控制逻辑（如避免幻觉、提升逻辑性）。

Spring AI 允许我们通过 `.promptTemplate()` 方法注入自定义 Prompt：

```java
PromptTemplate customPromptTemplate = PromptTemplate.builder()
    .renderer(StTemplateRenderer.builder().startDelimiterToken('<').endDelimiterToken('>').build())
    .template("""
        <query>

        以下是上下文资料：

        ---------------------
        <question_answer_context>
        ---------------------

        请严格根据以上资料作答：
        1. 如果无法回答，请说“我不知道”；
        2. 避免使用“根据上下文...”等表述。
        """)
    .build();
```


-----

#### 9.2.4 自动化方案：RetrievalAugmentationAdvisor(重点内容)

`RetrievalAugmentationAdvisor` 是 Spring AI 提供的一个高度**模块化 RAG 构建器**，适用于需要丰富控制和策略组合的场景，而非简单快速上线的替代方案。

##### 适用场景

  * **业务复杂、流程可定制**：需要在检索前/后注入查询转换、文档处理等逻辑
  * **需处理多语言、长对话、召回提升**：支持 `QueryTransformer`、`MultiQueryExpander` 等增强模块
  * **要求防幻觉、策略清晰**：内置的空上下文拒答逻辑和自定义提示模板确保结果可控

-----

###### RetrievalAugmentationAdvisor 核心处理流程

**`before` 方法（请求处理前）:**

1.  **创建原始查询**：从用户输入的文本、参数和对话历史中构建一个 `Query` 对象。
2.  **查询转换**：依次通过 `queryTransformers` 列表中的每个转换器对查询进行修改（如规范化、重写等）。
3.  **查询扩展**：若配置了 `queryExpander`，则将转换后的查询扩展为一个或多个查询（如同义词扩展），以提升召回率。
4.  **异步检索文档**：对每个扩展后的查询，异步调用 `documentRetriever` 检索相关文档。
5.  **文档合并**：使用 `documentJoiner` 将所有检索到的文档合并成一个列表。
6.  **文档后处理**：依次通过 `documentPostProcessors` 对合并后的文档进行进一步处理（如去重、排序、摘要等）。
7.  **查询增强**：用 `queryAugmenter` 将原始查询和最终的文档结合，生成带有上下文的增强查询。
8.  **更新请求**：用增强后的查询内容更新请求，用于后续流程。

**`after` 方法（响应处理后）:**

1.  将 RAG 过程中检索到的文档（`rag_document_context`）添加到最终响应的元数据中，便于溯源和调试。

-----

##### 核心组件接口与实现详解

###### 1\. 查询对象 (Query)

用于在 RAG 流程中承载查询信息的标准类。

  * `String text`：查询的文本内容。
  * `List<Message> history`：当前查询相关的对话历史记录。
  * `Map<String, Object> context`：查询的上下文信息，用于存储与查询相关的额外数据。

```java
package org.springframework.ai.rag;

import java.util.List;
import java.util.Map;
import org.springframework.ai.chat.messages.Message;
import org.springframework.util.Assert;

public record Query(String text, List<Message> history, Map<String, Object> context) {

    public Query {
        Assert.hasText(text, "text cannot be null or empty");
        Assert.notNull(history, "history cannot be null");
        Assert.noNullElements(history, "history elements cannot be null");
        Assert.notNull(context, "context cannot be null");
        Assert.noNullElements(context.keySet(), "context keys cannot be null");
    }

    public Query(String text) {
        this(text, List.of(), Map.of());
    }
    // ... Builder and other methods
}
```

###### 2\. 检索前处理 (Pre-Retrieval)

**a. 查询转换 (QueryTransformer)**

此接口用于对原始查询进行变换，以应对查询结构不佳、术语歧义、语言不同等问题。

  * **`RewriteQueryTransformer`**: 通过 LLM 优化查询，使其更简洁、精确，以便在目标系统（如向量数据库）中获得更好的结果。适用于用户查询冗长、模糊的场景。
  * **`CompressionQueryTransformer`**: 将对话历史和后续问题合并为一个独立的、包含完整上下文的查询。适用于多轮对话场景。
  * **`TranslationQueryTransformer`**: 将用户查询翻译为目标语言。适用于嵌入模型仅支持特定语言，而用户查询语言多样的场景。

**b. 查询扩展 (QueryExpander)**

此接口用于将单个查询扩展为多个语义上多样化的查询变体，以提升召回率。

  * **`MultiQueryExpander`**: 使用 LLM 将单个查询生成多个语义相近但角度不同的查询，从不同方面覆盖原始查询意图，增加检索到相关结果的机会。

| Transformer 类型              | 功能描述                     | 适用场景       | 示例                               |
| ----------------------------- | ---------------------------- | -------------- | ---------------------------------- |
| `RewriteQueryTransformer`     | 重写用户问题，使之更利于搜索 | 复杂长问题     | “我最近在学机器学习，什么是 LLM？” |
| `CompressionQueryTransformer` | 将上下文对话压缩成独立查询   | 长对话、追问   | “它的第二大城市是哪？”             |
| `TranslationQueryTransformer` | 将用户问题翻译为嵌入支持语言 | 多语言用户输入 | “Hvad er Danmarks hovedstad?”      |
| `MultiQueryExpander`          | 扩展成多个语义变体提高召回率 | 召回率优先     | “如何运行 Spring Boot 项目？”      |

```java
// 1. Pre-Retrieval: 查询扩展与转换
// 通过 LLM 优化查询，使其更简洁、精确，以便在目标系统（如向量数据库）中获得更好的结果
RewriteQueryTransformer rewriteQueryTransformer = RewriteQueryTransformer.builder()
        .chatClientBuilder(this.chatClientBuilder).build();

// 将对话历史和后续问题合并为一个独立的、包含完整上下文的查询。适用于多轮对话场景
CompressionQueryTransformer compressionQueryTransformer = CompressionQueryTransformer.builder()
        .chatClientBuilder(this.chatClientBuilder).build();

// 将用户查询翻译为目标语言。适用于嵌入模型仅支持特定语言，而用户查询语言多样的场景
TranslationQueryTransformer translationQueryTransformer = TranslationQueryTransformer.builder()
        .chatClientBuilder(this.chatClientBuilder).targetLanguage("English").build();

// 使用 LLM 将单个查询生成多个语义相近但角度不同的查询，从不同方面覆盖原始查询意图，增加检索到相关结果的机会。
MultiQueryExpander multiQueryExpander = MultiQueryExpander.builder()
        .chatClientBuilder(this.chatClientBuilder).build();
```

###### 3\. 检索 (Retrieval)

**a. 文档检索器 (DocumentRetriever)**

此接口定义了从数据源检索文档的标准方法。

  * **`VectorStoreDocumentRetriever`**: 核心实现之一，用于从 `VectorStore` 中检索与输入查询语义相似的文档。它支持相似度阈值 (`similarityThreshold`)、返回数量 (`topK`) 和元数据过滤 (`filterExpression`)。

```java
// 2. Retrieval: 向量检索与结果合并
// 用于从 VectorStore 中检索与输入查询语义相似的文档。
// 它支持相似度阈值
// 返回数量 (`topK`) 和元数据过滤 (`filterExpression`)。
VectorStoreDocumentRetriever vectorStoreDocumentRetriever = VectorStoreDocumentRetriever.builder()
        .vectorStore(simpleVectorStore)
        .topK(4) // 返回相近的4个文档
        // 如果一个文档里面出现了一些复杂查询，比如
        // 查询2025年版本最新的XX公司文档,那么这里的表达式可以写为：
        // .filterExpression("metadata.year >= 2025")
        .build();
```

**b. 文档合并器 (DocumentJoiner)**

此接口用于将多个来源（如多个扩展查询的结果）的文档集合并为一个列表。

  * **`ConcatenationDocumentJoiner`**: 默认实现，它将所有文档列表连接起来，并通过文档ID进行去重，最后根据文档分数（`score`）降序排列。

```java
ConcatenationDocumentJoiner concatenationDocumentJoiner = new ConcatenationDocumentJoiner();
```

###### 4\. 检索后处理 (Post-Retrieval)

**a. 文档后处理器 (DocumentPostProcessor)**

在文档检索完成后、注入到最终提示词之前，对此接口的实现可以对文档列表进行排序、过滤、重排（Re-ranking）、压缩等操作，以优化提供给大模型的上下文质量，一般来他适合单独被抽离出去作为一个单独控制的代码，方便管理

>这里以最简单的，挑选第一个文档的排序方式来做演示，在这一层，如果需要定义话复杂需求，即可操作传入进来的Document对象或是query对象进行复杂的重排/过滤

```java
package com.copilot.aicopilotbackend.service;

import org.springframework.ai.document.Document;
import org.springframework.ai.rag.Query;
import org.springframework.ai.rag.postretrieval.document.DocumentPostProcessor;
import java.util.Collections;
import java.util.List;

/**
 * 文档选择器 - 只选择第一个文档
 * 
 * 实现了 DocumentPostProcessor 接口，用于在 RAG 流水线中对检索到的文档进行后处理。
 * 该实现只返回检索结果中的第一个文档，用于限制上下文长度或选择最相关的文档。
 */
public class DocumentSelectFirst implements DocumentPostProcessor {
    
    /**
     * 处理文档列表，只返回第一个文档
     * 
     * @param query 查询对象
     * @param documents 检索到的文档列表
     * @return 只包含第一个文档的列表
     */
    @Override
    public List<Document> process(Query query, List<Document> documents) {
        // 只返回第一个文档，通常是相似度最高的文档
        return Collections.singletonList(documents.get(0));
    }
}
```

###### 5\. 生成 (Generation)

**a. 查询增强器 (QueryAugmenter)**

此接口负责将检索并处理后的文档（上下文）与原始用户查询结合，生成最终发送给大模型的提示（Prompt）。

  * **`ContextualQueryAugmenter`**: 默认实现，它使用一个提示模板将文档内容和用户问题组装起来。
    
      * **空上下文策略**：通过 `allowEmptyContext(boolean)` 控制。如果为 `false` (默认) 且检索结果为空，它会使用一个特定的 `emptyContextPromptTemplate` 来生成一个“无法回答”的响应，有效防止模型在没有信息时产生幻觉。如果为 `true`，则直接返回原始查询。
      
      ```java
      // 4. Generation: 上下文增强
      ContextualQueryAugmenter contextualQueryAugmenter = ContextualQueryAugmenter.builder()
              .allowEmptyContext(true).build();
      ```

-----

##### 完整配置与模块化代码示例

###### 1\. pom.xml 核心依赖

```xml
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-zhipuai</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-vector-store-redis</artifactId>
        </dependency>

    <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-autoconfigure-model-openai</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-autoconfigure-model-chat-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-rag</artifactId>
        </dependency>
```

###### 2\. application.yml 最小配置

```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6380 # 使用为 AI 应用指定的 6380 端口
  ai:
    zhipuai:
      api-key: ${ZHIPU_API_KEY} # 从环境变量读取
      chat:
        options:
          model: glm-4-air
      embedding:
        options:
          model: embedding-3
    vector-store:
      redis:
        index-name: ai-copilot-rag-index
        initialize-schema: true
# ... 其他配置
```

###### 3\. 模块化 RAG 控制器示例 (RagModuleController)

这个示例清晰地展示了如何独立构建并组装 `RetrievalAugmentationAdvisor` 的各个模块化组件。

新建一个基于内存的向量数据库作为测试：

```java
package com.copilot.aicopilotbackend.config;

import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class VectorStoreConfig {

    @Bean
    public SimpleVectorStore simpleVectorStore(EmbeddingModel embeddingModel) {
        return SimpleVectorStore.builder(embeddingModel).build();
    }

}

```

新增`DocumentSelectFirst`

```java
package com.copilot.aicopilotbackend.service;

import org.springframework.ai.document.Document;
import org.springframework.ai.rag.Query;
import org.springframework.ai.rag.postretrieval.document.DocumentPostProcessor;
import java.util.Collections;
import java.util.List;

public class DocumentSelectFirst implements DocumentPostProcessor {
    @Override
    public List<Document> process(Query query, List<Document> documents) {
        return Collections.singletonList(documents.get(0));
    }
}
```

实现`RagAdvancedService`

```java
package com.copilot.aicopilotbackend.service;

import com.copilot.aicopilotbackend.dto.response.RagResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.rag.advisor.RetrievalAugmentationAdvisor;
import org.springframework.ai.rag.generation.augmentation.ContextualQueryAugmenter;
import org.springframework.ai.rag.preretrieval.query.expansion.MultiQueryExpander;
import org.springframework.ai.rag.preretrieval.query.transformation.CompressionQueryTransformer;
import org.springframework.ai.rag.preretrieval.query.transformation.RewriteQueryTransformer;
import org.springframework.ai.rag.preretrieval.query.transformation.TranslationQueryTransformer;
import org.springframework.ai.rag.retrieval.join.ConcatenationDocumentJoiner;
import org.springframework.ai.rag.retrieval.search.VectorStoreDocumentRetriever;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RagAdvancedService {
    private final SimpleVectorStore simpleVectorStore;
    private final ChatClient.Builder chatClientBuilder;

    public RagAdvancedService(EmbeddingModel embeddingModel, ChatClient.Builder builder) {
        this.simpleVectorStore = SimpleVectorStore.builder(embeddingModel).build();
        // 初始化数据...
        List<Document> documents = List.of(
                new Document("你的姓名是Prorise，是一个全栈工程师，25年硕士毕业"),
                new Document("你的姓名是Prorise，专业领域包含的数学、前后端、设计、自然语言处理..."),
                new Document("你姓名是Prorise，爱好是发呆、思考、运动...")
        );
        simpleVectorStore.add(documents);
        this.chatClientBuilder = builder;
    }

    public RagResponse ask(String question) {
        log.info("与 RAG 顾问开始聊天");

        // 1. Pre-Retrieval: 查询扩展与转换
        // 通过 LLM 优化查询，使其更简洁、精确，以便在目标系统（如向量数据库）中获得更好的结果
        RewriteQueryTransformer rewriteQueryTransformer = RewriteQueryTransformer.builder()
                .chatClientBuilder(this.chatClientBuilder).build();

        // 将对话历史和后续问题合并为一个独立的、包含完整上下文的查询。适用于多轮对话场景
        CompressionQueryTransformer compressionQueryTransformer = CompressionQueryTransformer.builder()
                .chatClientBuilder(this.chatClientBuilder).build();

        // 将用户查询翻译为目标语言。适用于嵌入模型仅支持特定语言，而用户查询语言多样的场景
        TranslationQueryTransformer translationQueryTransformer = TranslationQueryTransformer.builder()
                .chatClientBuilder(this.chatClientBuilder).targetLanguage("English").build();

        // 使用 LLM 将单个查询生成多个语义相近但角度不同的查询，从不同方面覆盖原始查询意图，增加检索到相关结果的机会。
        MultiQueryExpander multiQueryExpander = MultiQueryExpander.builder()
                .chatClientBuilder(this.chatClientBuilder).build();


        // 2. Retrieval: 向量检索与结果合并
        // 用于从 VectorStore 中检索与输入查询语义相似的文档。
        // 它支持相似度阈值
        // 返回数量 (`topK`) 和元数据过滤 (`filterExpression`)。
        VectorStoreDocumentRetriever vectorStoreDocumentRetriever = VectorStoreDocumentRetriever.builder()
                .vectorStore(simpleVectorStore)
                // .topK(4) // 返回相近的4个文档
                // 如果一个文档里面出现了一些复杂查询，比如
                // 查询2025年版本最新的XX公司文档,那么这里的表达式可以写为：
                // .filterExpression("metadata.year >= 2025")
                .build();


        ConcatenationDocumentJoiner concatenationDocumentJoiner = new ConcatenationDocumentJoiner();

        // 3. Post-Retrieval: 自定义文档后处理
        DocumentSelectFirst documentSelectFirst = new DocumentSelectFirst(); // 假设实现为只取第一个文档


        // 构建 ContextualQueryAugmenter
        ContextualQueryAugmenter contextualQueryAugmenter = ContextualQueryAugmenter.builder()
                .allowEmptyContext(false)
                .build();

        // 组装 Advisor
        RetrievalAugmentationAdvisor retrievalAugmentationAdvisor = RetrievalAugmentationAdvisor.builder()
                .queryTransformers(rewriteQueryTransformer) // 优化查询
                .queryTransformers(compressionQueryTransformer) // 多轮对话查询
                .queryTransformers(translationQueryTransformer) // 翻译查询
                .queryExpander(multiQueryExpander) // 多语义查询
                .documentRetriever(vectorStoreDocumentRetriever) // 向量检索
                .documentJoiner(concatenationDocumentJoiner) // 合并文档
                .documentPostProcessors(documentSelectFirst) // 自定义文档后处理
                .queryAugmenter(contextualQueryAugmenter) // 上下文增强
                .build();
        String answer = this.chatClientBuilder.build().prompt(question)
                .advisors(retrievalAugmentationAdvisor)
                .call().content();

        // 单独检索源文档，用于前端展示
        List<Document> similarDocuments = simpleVectorStore.similaritySearch(
                SearchRequest.builder()
                        .query(question)
                        .topK(4)
                        .similarityThreshold(0.3)
                        .build());

        // 构建源文档信息
        List<Map<String, Object>> sources = similarDocuments.stream()
                .map(doc -> {
                    Map<String, Object> sourceMap = new HashMap<>(doc.getMetadata());
                    sourceMap.put("content", doc.getText());
                    return sourceMap;
                })
                .collect(Collectors.toList());

        return new RagResponse(answer, sources);
    }

}
```
最后我们创建一个Controller

```java
package com.copilot.aicopilotbackend.controller;

import com.copilot.aicopilotbackend.dto.request.RagRequest;
import com.copilot.aicopilotbackend.dto.response.ApiResponse;
import com.copilot.aicopilotbackend.dto.response.RagResponse;
import com.copilot.aicopilotbackend.service.RagAdvancedService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/rag-advanced")
@RequiredArgsConstructor
public class RagAdvancedController {

    private final RagAdvancedService ragAdvancedService;

    @PostMapping("/ask")
    public ApiResponse<RagResponse> askQuestion(@RequestBody RagRequest request) {
        RagResponse response = ragAdvancedService.ask(request.question());
        return ApiResponse.success(response);
    }
} 
```

##### 与 QuestionAnswerAdvisor 对比简表

| 特性                  | QuestionAnswerAdvisor | RetrievalAugmentationAdvisor    |
| --------------------- | --------------------- | ------------------------------- |
| 模块复杂度            | ✅ 简单                | 🧩 复杂，可插拔多个策略          |
| 检索配置              | 支持 similarity/topK  | 支持且额外支持 filterExpression |
| 自定义 PromptTemplate | ✅ 支持                | ✅ 支持 (通过 QueryAugmenter)    |
| 查询增强              | ❌ 不支持              | ✅ 支持：重写、压缩、翻译、扩展  |
| 检索后处理            | ❌ 不支持              | ✅ 支持：排序、去重、摘要        |
| 空上下文处理策略      | 由模板控制            | ✅ 默认拒答 可配置 fallback      |
| 推荐场景              | 快速上线、简单业务    | 复杂业务、高定制                |

---



### 9.3 实战：构建基础 RAG 问答应用

本节，我们将聚焦后端，搭建一个功能完备的 RAG 问答服务。我们将采用**手动编排**的方式来实现 RAG 流程，这种方式虽然代码稍多，但能让我们最清晰地看到 RAG 的每一步是如何运作的，并且能灵活地构造返回给前端的数据结构（包含引用来源）。

> **注意**：本章我们暂时不深入讲解复杂文件的ETL过程，而是通过一个“数据播种”服务，在应用启动时向 `VectorStore` 中手动存入几条“种子知识”，以模拟一个已存在的知识库。完整的、能处理 PDF 等文件的 ETL 管道，我们将在第十章详细构建。

#### 9.3.1 环境与配置

首先，确保我们的项目具备所有必要的“零件”。

  * **第一步：检查核心依赖 (`pom.xml`)**
    请确保您的 `pom.xml` 中已包含以下核心依赖：

    ```xml
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-starter-model-zhipuai</artifactId>
            </dependency>

            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-starter-vector-store-redis</artifactId>
            </dependency>
    
        <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-autoconfigure-model-openai</artifactId>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-autoconfigure-model-chat-client</artifactId>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-rag</artifactId>
            </dependency>
    ```
    
  * **第二步：确认应用配置 (`application.yml`)**
    此配置继承自我们之前的章节，确保 Redis 连接指向 `6380` 端口，并正确配置了智谱 AI 的模型信息。

    ```yaml
    spring:
      data:
        redis:
          host: localhost
          port: 6380 # 使用为 AI 应用指定的 6380 端口
      ai:
        zhipuai:
          api-key: ${ZHIPU_API_KEY} # 从环境变量读取
          chat:
            options:
              model: glm-4-air
          embedding:
            options:
              model: embedding-3
        vector-store:
          redis:
            index-name: ai-copilot-rag-index
            initialize-schema: true
    # ... 其他配置
    ```
    
* **第三步：配置核心配置项(`RagConfig`)**

  ```java
  package com.copilot.aicopilotbackend.config;
  import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;
  import org.springframework.ai.vectorstore.SearchRequest;
  import org.springframework.ai.vectorstore.VectorStore;
  import org.springframework.context.annotation.Bean;
  import org.springframework.context.annotation.Configuration;
  
  /**
   * RAG专用配置类
   * 提供 QuestionAnswerAdvisor Bean 用于优雅的 RAG 实现
   */
  @Configuration
  public class RagConfig {
  
      /**
       * QuestionAnswerAdvisor Bean - 用于 RAG 功能
       * @param vectorStore 向量存储实例
       * @return 配置好的 QuestionAnswerAdvisor
       */
      @Bean
      public QuestionAnswerAdvisor questionAnswerAdvisor(VectorStore vectorStore) {
          return QuestionAnswerAdvisor.builder(vectorStore)
                  .searchRequest(SearchRequest.builder()
                          .similarityThreshold(0.8)
                          .topK(4)
                          .build())
                  .build();
      }
  } 
  ```

  

#### 9.3.2 数据播种：手动向 VectorStore 添加知识

我们创建一个 `DataSeedingService`，在应用启动时，自动向 `VectorStore` 中添加几条简单的 `Document` 作为我们的初始知识。

  * **`DataSeedingService.java` 的实现**:

    ```java
    package com.copilot.aicopilotbackend.service;

    import jakarta.annotation.PostConstruct;
    import lombok.RequiredArgsConstructor;
    import lombok.extern.slf4j.Slf4j;
    import org.springframework.ai.document.Document;
    import org.springframework.ai.vectorstore.SearchRequest;
    import org.springframework.ai.vectorstore.VectorStore;
    import org.springframework.stereotype.Service;

    import java.util.List;
    import java.util.Map;

    @Slf4j
    @Service
    @RequiredArgsConstructor
    public class DataSeedingService {

        private final VectorStore vectorStore;

        @PostConstruct
        public void seedData() {
            List<Document> checkResult = vectorStore.similaritySearch(
                    SearchRequest.builder().query("Spring AI").topK(1).build()
            );
    
        if (checkResult.stream().anyMatch(doc -> doc.getText().contains("可移植性"))) {
                log.info("知识库中已有数据，跳过播种。");
                return;
            }
    
        log.info("知识库为空，开始进行数据播种...");
    
        List<Document> documents = List.of(
                    new Document("Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。", Map.of("source", "spring-ai-manual.pdf", "chapter", 1)),
                    new Document("TokenTextSplitter 是一个关键工具，它能根据 Token 数量将长文本智能地分割成小块，同时通过 chunkOverlap 保证语义连续性。", Map.of("source", "spring-ai-manual.pdf", "chapter", 5)),
                    new Document("在 RAG 应用中，必须为 LLM 设计一个好的 Prompt 模板，明确指示它只能根据提供的上下文回答，以避免信息幻觉。", Map.of("source", "rag-best-practices.docx", "chapter", 2))
            );
    
            vectorStore.add(documents);
        log.info("数据播种完成！");
        }
    }
    ```

#### 9.3.3 RAG 服务与 API 实现

现在，我们来构建 RAG 的问答服务和对应的 API 接口。

  * **DTO 定义** (`dto/request/RagRequest.java`, `dto/response/RagResponse.java`):

    ```java
    package com.copilot.aicopilotbackend.dto.request;
    // 提问请求 DTO
    public record RagRequest(String question) {}
    ```

    ```java
    package com.copilot.aicopilotbackend.dto.response;
    import java.util.List;
    import java.util.Map;
    // 回答响应 DTO
    // 为了前端能展示引用来源，我们让它返回答案和源文档列表
    public record RagResponse(String answer, List<Map<String, Object>> sources) {}
    ```

  * **`RagService.java` **:
    我们采用最优雅的AOP注入形式，符合官网推荐的代码编辑模式

    ```java
    package com.copilot.aicopilotbackend.service;

    import com.copilot.aicopilotbackend.dto.response.RagResponse;
    import lombok.RequiredArgsConstructor;
    import lombok.extern.slf4j.Slf4j;
    import org.springframework.ai.chat.client.ChatClient;
    import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;
    import org.springframework.ai.document.Document;
    import org.springframework.ai.vectorstore.SearchRequest;
    import org.springframework.ai.vectorstore.VectorStore;
    import org.springframework.stereotype.Service;

    import java.util.HashMap;
    import java.util.List;
    import java.util.Map;
import java.util.stream.Collectors;
    
    @Slf4j
    @Service
    @RequiredArgsConstructor
    public class RagService {
    
        private final ChatClient.Builder chatClientBuilder;
        private final VectorStore vectorStore;
        private final QuestionAnswerAdvisor questionAnswerAdvisor;
    
        /**
         * 优雅的 RAG 实现，使用 Spring AI Advisor 模式
         * @param question 用户问题
         * @return RAG 响应，包含答案和源文档
         */
        public RagResponse ask(String question) {
            log.info("RAG 流程：使用 Spring AI Advisor 模式处理问题: {}", question);
            
            // 使用 QuestionAnswerAdvisor 实现优雅的 RAG
            String answer = chatClientBuilder.build().prompt()
                    .advisors(questionAnswerAdvisor)
                    .user(question)
                    .call()
                    .content();
            
            log.info("RAG 流程：AI 回答生成完成");
            
            // 单独检索源文档，用于前端展示
            List<Document> similarDocuments = vectorStore.similaritySearch(
                    SearchRequest.builder()
                            .query(question)
                            .topK(4)
                            .similarityThreshold(0.8)
                            .build());
            
            // 构建源文档信息
            List<Map<String, Object>> sources = similarDocuments.stream()
                    .map(doc -> {
                        Map<String, Object> sourceMap = new HashMap<>(doc.getMetadata());
                        sourceMap.put("content", doc.getText());
                        return sourceMap;
                    })
                    .collect(Collectors.toList());
            
            log.info("RAG 流程：找到 {} 个相关源文档", sources.size());
            
            return new RagResponse(answer, sources);
        }
    }
    ```
    
    > **一个潜在的陷阱**：请注意，在这里如果我们我们直接注入并使用了全局的 `ChatClient` Bean。当 RAG 问答与需要长期记忆的普通聊天在同一个会话中混合时，这可能会导致问题（历史聊天记录被错误地注入 RAG Prompt）。解决此问题的最佳实践（创建隔离的 `ChatClient` 实例），正如代码中演示的一致
    
  * **`RagController.java`**:

    ```java
    package com.copilot.aicopilotbackend.controller;

    import com.copilot.aicopilotbackend.dto.request.RagRequest;
    import com.copilot.aicopilotbackend.dto.response.ApiResponse;
    import com.copilot.aicopilotbackend.dto.response.RagResponse;
    import com.copilot.aicopilotbackend.service.RagService;
    import lombok.RequiredArgsConstructor;
    import org.springframework.web.bind.annotation.PostMapping;
    import org.springframework.web.bind.annotation.RequestBody;
    import org.springframework.web.bind.annotation.RequestMapping;
    import org.springframework.web.bind.annotation.RestController;

    @RestController
    @RequestMapping("/api/v1/rag")
    @RequiredArgsConstructor
    public class RagController {

        private final RagService ragService;

        @PostMapping("/ask")
        public ApiResponse<RagResponse> askQuestion(@RequestBody RagRequest request) {
            RagResponse response = ragService.ask(request.question());
            return ApiResponse.success(response);
        }
    }
    ```

#### 9.3.4 接口测试

启动应用，等待控制台输出 `数据播种完成！` 后，使用 Postman 或 curl 测试我们的 RAG API。

  * **Request**:

      * Method: `POST`
      * URL: `http://localhost:8080/api/v1/rag/ask`
      * Headers: `Content-Type: application/json`
      * Body:
        ```json
        {
          "question": "介绍一下你已知的SPring知识"
        }
        ```

  * **Expected Response**:
    你将收到一个包含精准答案和引用来源的 JSON 响应，证明 RAG 系统正在基于我们播种的知识进行回答。

    ```json
    {
        "code": "00000",
        "message": "成功",
        "data": {
            "answer": "Spring 是一个开源的 Java 应用程序框架，它被设计用来简化企业级应用程序的开发和维护。以下是 Spring 框架的一些核心概念和特性，这些信息是基于您提供的参考资料：....",
            "sources": [
                {
                    "distance": 0.30841315,
                    "vector_score": 0.30841315,
                    "content": "Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"
                },
                {
                    "distance": 0.30841315,
                    "vector_score": 0.30841315,
                    "content": "Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"
                },
                {
                    "distance": 0.30841315,
                    "vector_score": 0.30841315,
                    "content": "Spring AI 旨在将 Spring 生态系统的设计原则（如可移植性、模块化）应用于 AI 工程领域。"
                }
            ]
        },
        "timestamp": "2025-06-29T21:36:23.5084643",
        "success": true
    }
    ```



-----