.comment-barrage {
  position: fixed;
  bottom: 0;
  right: 60px;
  padding: 0 0 20px 10px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: flex-end;
  z-index: 999;
  transition: 0.3s;
}
@media screen and (max-width: 768px) {
  .comment-barrage {
    display: none !important;
  }
}
.comment-barrage-item {
  min-width: 300px;
  max-width: 300px;
  width: fit-content;
  min-height: 80px;
  max-height: 150px;
  margin: 4px;
  padding: 8px 14px;
  background: rgba(240, 245, 249, 0.95);
  border-radius: 12px;
  color: rgb(30, 32, 34);
  animation: barrageIn 0.6s cubic-bezier(0.42, 0, 0.3, 1.11);
  transition: 0.3s;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(201, 214, 223, 0.6);
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transform: translateZ(0);
  position: fixed;
  box-shadow: 0 4px 12px rgba(82, 97, 107, 0.15);
}
.comment-barrage-item:hover {
  border: 1px solid rgb(82, 97, 107);
  box-shadow: 0 6px 20px rgba(82, 97, 107, 0.25);
  transform: translateY(-2px);
}
.comment-barrage-item.out {
  opacity: 0;
  animation: barrageOut 0.6s cubic-bezier(0.42, 0, 0.3, 1.11);
}
.comment-barrage-item a.barrageContent:hover {
  color: rgb(82, 97, 107);
}
.comment-barrage-item.hovered {
  opacity: 0;
}
.comment-barrage-item .comment-barrage-close {
  color: rgb(82, 97, 107);
  cursor: pointer;
  line-height: 1;
  padding: 4px;
  transition: color 0.2s ease;
}
.comment-barrage-item .comment-barrage-close:hover {
  color: rgb(30, 32, 34);
}
.comment-barrage-item pre {
  display: none;
}
.comment-barrage-item p img:not(.tk-owo-emotion) {
  display: none;
}

.comment-barrage-item .barrageHead {
  height: 30px;
  padding: 0;
  line-height: 30px;
  font-size: 12px;
  border-bottom: 1px solid rgba(201, 214, 223, 0.4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 700;
  padding-bottom: 6px;
}
.comment-barrage-item .barrageHead .barrageTitle {
  color: rgb(240, 245, 249);
  margin-right: 8px;
  background: rgb(82, 97, 107);
  line-height: 1;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  transition: all 0.2s ease;
}
.comment-barrage-item .barrageHead .barrageTitle.barrageBloggerTitle {
  background: rgb(30, 32, 34);
  box-shadow: 0 2px 6px rgba(30, 32, 34, 0.2);
}
.comment-barrage-item .barrageAvatar {
  width: 18px;
  height: 18px;
  margin: 0;
  margin-left: auto;
  margin-right: 8px;
  border-radius: 50%;
  background: rgb(201, 214, 223);
  border: 1px solid rgba(82, 97, 107, 0.2);
}
.comment-barrage-item .barrageContent {
  font-size: 14px !important;
  font-weight: 400 !important;
  height: calc(100% - 30px);
  overflow: scroll;
  cursor: pointer;
  line-height: 1.4;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar {
  height: 0;
  width: 4px;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar-track {
  background: rgba(201, 214, 223, 0.3);
  border-radius: 2px;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar-thumb {
  background: rgba(82, 97, 107, 0.5);
  border-radius: 2px;
}
.comment-barrage-item .barrageContent::-webkit-scrollbar-thumb:hover {
  background: rgb(82, 97, 107);
}
.comment-barrage-item .barrageContent::-webkit-scrollbar-button {
  display: none;
}
.comment-barrage-item .barrageContent p {
  margin: 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.comment-barrage-item .barrageContent blockquote p {
  margin: 0;
}
.comment-barrage-item .barrageContent h1,
.comment-barrage-item .barrageContent h2,
.comment-barrage-item .barrageContent h3,
.comment-barrage-item .barrageContent h4 {
  font-size: 14px !important;
  font-weight: 600 !important;
  margin: 8px 0 !important;
  color: rgb(30, 32, 34);
}

@media (prefers-color-scheme: dark) {
  .comment-barrage-item {
    background: rgba(30, 32, 34, 0.95);
    color: rgb(240, 245, 249);
    border: 1px solid rgba(82, 97, 107, 0.6);
  }
  
  .comment-barrage-item:hover {
    border: 1px solid rgb(201, 214, 223);
    box-shadow: 0 6px 20px rgba(201, 214, 223, 0.15);
  }
  
  .comment-barrage-item .barrageHead {
    border-bottom: 1px solid rgba(82, 97, 107, 0.4);
  }
  
  .comment-barrage-item .barrageHead .barrageTitle {
    background: rgb(201, 214, 223);
    color: rgb(30, 32, 34);
  }
  
  .comment-barrage-item .barrageHead .barrageTitle.barrageBloggerTitle {
    background: rgb(240, 245, 249);
    color: rgb(30, 32, 34);
  }
  
  .comment-barrage-item .barrageAvatar {
    background: rgb(82, 97, 107);
    border: 1px solid rgba(201, 214, 223, 0.2);
  }
  
  .comment-barrage-item .barrageContent::-webkit-scrollbar-track {
    background: rgba(82, 97, 107, 0.3);
  }
  
  .comment-barrage-item .barrageContent::-webkit-scrollbar-thumb {
    background: rgba(201, 214, 223, 0.5);
  }
}
