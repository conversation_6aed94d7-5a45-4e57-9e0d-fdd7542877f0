- var skills = site.data.about.skills
- var careers = site.data.about.careers

if skills || careers
    .author-content
        if skills
            .author-content-item.skills
                .card-content
                    .author-content-item-tips= skills.title
                    span.author-content-item-title= skills.subtitle
                    .skills-style-group
                        .tags-group-all
                            .tags-group-wrapper
                                - var pair = []
                                each i in [0,1]
                                    each tag, index in skills.tags
                                        - pair.push(tag)
                                        if pair.length === 2
                                            .tags-group-icon-pair
                                                each item in pair
                                                    .tags-group-icon(style=`background:${item.color}`)
                                                        if item.img
                                                            img(src=item.img, title=item.title)
                                                        else if item.icon
                                                            i(class=item.icon, title=item.title, style=`color: ${item.icon_color ? item.icon_color : ''}`)
                                            - pair = []
                        .skills-list
                            each tag in skills.tags
                                .skill-info
                                    .skill-icon(style=`background:${tag.color}`)
                                        if tag.img
                                            img(src=tag.img, title=tag.title)
                                        else if tag.icon
                                            i(class=tag.icon, title=tag.title, style=`color: ${tag.icon_color ? tag.icon_color : ''}`)

                                    .skill-name
                                        span= tag.title
                            .etc ...
        if careers
            .author-content-item.careers
                .card-content
                    .author-content-item-tips= careers.title
                    span.author-content-item-title= careers.subtitle
                    .careers-group
                        each item in careers.items
                            .careers-item
                                .circle(style=`background:${item.color}`)
                                .name= `${item.school}, ${item.major}`
                    img.author-content-img(src=careers.image, alt=careers.title)