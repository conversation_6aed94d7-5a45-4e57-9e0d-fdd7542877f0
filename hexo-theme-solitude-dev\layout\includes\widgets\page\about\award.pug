- award = site.data.about.award

- var sum = 0
if site.data.about.rewardList && site.data.about.award.enable
    .author-content
        .author-content-item.single.reward
            .author-content-item-tips= _p('award.thanks')
            span.author-content-item-title= _p('award.title')
            .author-content-item-description
                != award.description
            .reward-list-all
                each reward in site.data.about.rewardList.sort((a, b) => b.time - a.time) || []
                    - sum += reward.money
                    .reward-list-item
                        .reward-list-item-name= reward.name
                        .reward-list-bottom-group
                            .reward-list-item-money(style="background-color:" + reward.color)
                                if reward.icon
                                    i.solitude(class=reward.icon)
                                | ¥ #{reward.money}
                            time.datetime.reward-list-item-time(datetime=moment(reward.time).format())
                if theme.post.award.enable
                    .post-reward
                        .post-reward(onclick="AddRewardMask()")
                        .reward-button(title=_p('award.tipping'))
                            i.solitude.fas.fa-heart
                            = _p('award.tipping')
                        .reward-main
                            ul.reward-all
                                span.reward-title= theme.post.award.title
                                ul.reward-group
                                    - var rewards = theme.post.award.list
                                    each reward in rewards
                                        li.reward-item
                                            a(href=url_for(reward.url))
                                                img.post-qr-code-img(alt=reward.name, src=reward.qcode, style="border-color:" + reward.color)
                                            .post-qr-code-desc= reward.name
            .reward-list-tips
                p= award.tips.replace('{sum}', sum.toFixed(2))
