<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Java（二）：2.0 Java基础 | Prorise的小站</title><meta name="keywords" content="Java基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Java（二）：2.0 Java基础"><meta name="application-name" content="Java（二）：2.0 Java基础"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="Java（二）：2.0 Java基础"><meta property="og:url" content="https://prorise666.site/posts/35626.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="2.0 Java基础本章将深入Java语言的根基——构成程序万物的基础语法。我们将从“数据类型”这个核心筒开始，对Java世界中所有用于承载数据的基本单元和复杂结构，进行一次全面、详尽的探险。这趟旅程不仅涵盖它们的用法，更会深入其设计原理与实战中的避坑技巧，为后续所有高阶概念的学习打下最坚实的地基。"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"><meta name="description" content="2.0 Java基础本章将深入Java语言的根基——构成程序万物的基础语法。我们将从“数据类型”这个核心筒开始，对Java世界中所有用于承载数据的基本单元和复杂结构，进行一次全面、详尽的探险。这趟旅程不仅涵盖它们的用法，更会深入其设计原理与实战中的避坑技巧，为后续所有高阶概念的学习打下最坚实的地基。"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/35626.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"Java（二）：2.0 Java基础",postAI:"true",pageFillDescription:"2.0 Java基础, 2.1 数据类型全解, 2.1.1 引言：Java数据世界的两大基石, 基本数据类型, 引用数据类型, 2.1.2 基本数据类型, 整数家族 (byte short int long), 浮点数家族 (float double), char (字符类型) 与 boolean (布尔类型), 2.1.3 包装类, 核心用途, Integer 深度剖析, 2.1.4 字符串：String, 核心用途, 类型介绍与核心面试题, 常用方法速查表, 代码示例详解, 关联类型：StringBuilder与StringBuffer, 核心用途与场景, 类型介绍与原理, 常用方法速查表, 代码示例详解, [面试题] 何时使用StringBuilder和StringBuffer？, 2.1.5 数组 (Arrays) 与其工具类, 核心用途与场景, 类型介绍与初始化, java.util.Arrays 核心工具方法详解, 代码示例详解, 排序, 查找, 比较, 复制, 填充, 转换, 流处理, [面试题] 数组 (Array) vs. 列表 (ArrayList), 2.1.6 集合框架：List, 核心用途, ArrayList 详解, LinkedList 详解, [面试题] ArrayList vs LinkedList 对比, 2.1.7 集合框架：Set, HashSet 详解, 常用方法速查表, 代码示例详解, LinkedHashSet 详解, 核心用途与场景, 类型介绍与底层原理, TreeSet 详解, 核心用途与场景, 类型介绍与排序原理, 常用方法速查表, 2.1.8 集合框架：Map（重点）, Map 接口核心特性, HashMap 核心方法速查表, 1. 核心操作, 2. 视图操作, 3. 状态查询, 4. Java 8+ 增强方法, **5. 批量操作 **, 核心用途与场景, 场景一：实现内存缓存, 场景二：存储配置信息, 场景三：将列表数据转换为索引, 场景四：计数统计, TreeMap 简介, 核心用途与场景, 类型介绍与排序原理, 代码示例详解, [面试题] HashMap vs Hashtable vs ConcurrentHashMap, 2.2 运算符详解, 2.2.0 计算机基础：进制与编码, 常见进制介绍, 进制转换核心方法, 十进制转二进制（除2取余法）, 二进制转十进制（按权展开法）, 2.2.3 [计算机基础] 原码、反码、补码, 为何需要补码？, 正数与负数的编码表示, 补码的计算过程, 2.2.1 [面试高频] 位运算符深度剖析, 核心对比：gtgt (算术右移) vs. gtgtgt (逻辑右移), 代码示例：对比 gtgt 和 gtgtgt, 实战场景与代码详解, 场景一：高效运算, 2.2.2 [避坑指南] 逻辑与自增x2F自减运算符陷阱, 短路逻辑 (ampamp 和 ||), i++ vs. ++i, 经典面试题：i = i++, 2.2.3 运算符优先级与核心建议, 核心开发建议：不要依赖隐式优先级, 2.3 [深度] 循环与异常处理进阶, 2.3.1 [深度] for-each循环与Iterator迭代器原理, for-each的底层真相, 代码示例：for-each的编译后等价代码, [面试高频] Iterator 与 Fail-Fast 机制, 代码示例：触发ConcurrentModificationException与正确删除, 2.3.2 [进阶] 带标签的 break 和 continue, 代码示例：在二维数组中查找并跳出所有循环, 2.3.3 [核心] Java异常处理机制, 面试题引入, [底层] Throwable 家族：异常体系结构, [面试高频] try-catch-finally 的执行内幕, 2.3.4 [Java 7+] try-with-resources 最佳实践, 核心用途与场景, 代码示例：优雅地关闭资源基础本章将深入语言的根基构成程序万物的基础语法我们将从数据类型这个核心筒开始对世界中所有用于承载数据的基本单元和复杂结构进行一次全面详尽的探险这趟旅程不仅涵盖它们的用法更会深入其设计原理与实战中的避坑技巧为后续所有高阶概念的学习打下最坚实的地基数据类型全解引言数据世界的两大基石在开始学习具体的数据类型之前我们必须先建立一个宏观的认知中的所有数据类型归根结底分为两大阵营理解它们的本质区别是掌握内存管理和程序性能的关键基本数据类型定义这是语言内置的最基础的种数据类型它们并非对象特点变量本身直接存储数据的值通常存储在栈内存中特指方法内的局部变量这使得它们的存取速度非常快引用数据类型定义除了种基本数据类型之外的所有类型包括我们自定义的类接口数组枚举以及后面要讲的和集合等都属于引用类型特点变量存储的是一个内存地址这个地址指向了真正的数据即对象实例变量的引用地址存储在栈上而对象实例则存储在堆内存中基本数据类型这部分类型是构成程序运算的最小单位它们不具备对象的方法但性能极高整数家族核心用途主要用于文件操作作为字节流进行数据传输或在内存敏感的场景下节省空间使用场景较少通常在一些底层或兼容性代码中可见最常用的整数类型用于计数索引等绝大多数整数运算场景用于表示需要超出范围的整数如时间戳毫秒数大型文件大小大型计数等类型介绍与面试题类型大小位取值范围默认值面试题结果是什么答结果是这是因为类型最大值为二进制加后发生溢出二进制变为这在计算机补码表示中恰好是代码示例这行会编译错误因为的结果已经是类型正确的做法类型运算结果已提升为浮点数家族核心用途用于需要小数的计算如科学计算图形学等双精度比单精度更常用因为它精度更高类型介绍与避坑指南位数值后需加或后缀位是默认的小数类型面试必考避坑指南为何金融计算禁用答因为和采用二进制浮点数表示法无法精确表示所有十进制小数例如这会导致舍入误差在要求高精度的金融或商业计算中是致命的最佳实践是使用类代码示例输出通常不是精确的使用进行精确计算注意使用字符串构造以保证精度精确计算字符类型与布尔类型核心用途表示单个字符如字母数字或符号用于逻辑判断只有和两个值类型介绍与面试题在中占位字节采用编码因此可以表示世界上绝大多数语言的字符面试题类型能否存储一个中文汉字答可以因为的类型使用编码其范围覆盖了绝大多数汉字包装类核心用途包装类的存在是为了解决基本数据类型无法像对象一样被操作的问题核心用途包括在集合框架中使用如因为泛型参数必须是对象允许值为用于表示缺失或未定义的状态包含了许多实用的静态方法如类型转换进制转换等深度剖析类型介绍与面试题自动装箱编译器自动转换为自动拆箱编译器自动转换为自动装箱约等于这一行自动拆箱约等于这一行输出面试必考缓存池为了提高性能方法对到之间的整数进行了缓存当通过自动装箱或创建这个范围内的对象时会直接返回缓存中的同一个对象超出这个范围则会一个新的对象因此使用比较时若两个对象不是同一个实例就会得到从而引发问题建议使用方法进行值比较缓存池演示因为在缓存池内超出缓存范围创建了新对象常用方法速查表方法签名功能描述将字符串解析为基本类型将字符串或转换为对象推荐使用会利用缓存将对象转换为基本类型比较两个对象的大小比较两个对象的值是否相等将字符串解析为基本类型输出将字符串或转换为对象推荐使用可利用缓存将对象转为基本类型输出比较两个对象的大小返回或输出因为比较两个对象的值是否相等返回因为字符串核心用途用于表示和操作一切文本信息是中使用最频繁的类之一类型介绍与核心面试题面试必考的不可变性对象一旦被创建其内容就不能被修改任何对的修改操作如拼接替换都会返回一个新的对象好处线程安全利于缓存字符串常量池作为的键时可保证不变面试字符串常量池位于堆内存中当使用字面量如创建字符串时会先检查池中是否存在如果存在则直接返回其引用否则创建新的并放入池中创建字符串并赋值检查两个字符串是否引用相同结果为因为和指向同一个对象创建一个新的字符串对象检查和是否引用相同结果为因为是新生成的对象面试题创建了几个对象答可能是一个也可能是两个如果常量池中已有则只在堆中创建一个新的对象如果常量池中没有则会在池中创建一个同时在堆中也创建一个共两个对象常用方法速查表分类方法签名功能描述获取判断获取长度判空获取字符判断包含查找查找子串首次末次出现的位置比较内容比较区分不区分大小写截取分割截取子串按正则表达式分割替换字符替换正则替换转换大小写转换去首尾空格转数组代码示例详解获取长度判空获取字符判断包含查找子串首次出现的位置查找子串末次出现的位置内容比较区分大小写内容比较不区分大小写截取子串按正则表达式分割字符替换正则替换小写转换大写转换去首尾空格转字符数组转字节数组字节形式的字符串关联类型与核心用途与场景当我们需要频繁地修改或拼接字符串时使用不可变的会因创建大量临时对象而导致性能低下和正是为解决这一问题而生的可变字符串序列适用于单线程环境下的字符串拼接或修改是绝大多数场景下的首选因为它性能最高适用于多线程环境下需要保证共享字符串数据线程安全的场景类型介绍与原理和本质上都是一个可变的字符数组容器与每次操作都返回新对象不同它们的大部分操作如都是在内部的字符数组上直接进行的只有在数组容量不足时才会进行扩容从而避免了不必要的对象创建可变性它们的内部数组不是的并且长度可以动态改变线程安全机制面试必考它的所有公开方法如都被关键字修饰这意味着在同一时刻只有一个线程能访问这些方法从而保证了线程安全但加锁也带来了额外的性能开销它在中被引入可以看作是的一个非线程安全版本去掉了关键字因此在单线程环境下性能更优常用方法速查表以下方法对和均适用方法签名功能描述在序列末尾追加内容此方法被重载以接受所有基本类型等在指定索引位置插入内容删除指定范围内的字符删除指定位置的单个字符用指定字符串替换范围内的内容将序列反转返回当前序列的长度返回当前内部数组的容量将当前的可变序列转换为一个不可变的对象代码示例详解场景一循环中的高效拼接这是最核心最经典的应用场景低效的方法拼接结果高效的方法使用最后需要时再转换为拼接结果场景二链式调用构建复杂字符串等方法返回对象本身使得链式编程成为可能代码更简洁构建的查询场景三字符串反转本身没有提供反转方法使用可以轻松实现的反转是面试题何时使用和答当需要进行大量或循环内的字符串拼接时应使用它们来避免创建大量临时的对象从而提高性能在选择时单线程环境优先且总是使用因为它没有同步开销性能更好多线程环境如果一个字符串对象需要被多个线程共享和修改必须使用来保证线程安全数组与其工具类核心用途与场景数组是中最基础最高效的数据结构之一其核心用途是在内存中存储固定大小同一类型的元素序列核心场景当元素数量固定且对性能有较高要求时如算法题底层数据缓冲作为更复杂数据结构如的内部实现表示和操作矩阵或表格使用多维数组方法的参数或返回值尤其是类型介绍与初始化数组即对象在中数组是一个引用类型数组变量存储在栈中它指向堆内存中一块连续开辟的空间这也解释了为什么数组的长度一旦创建就不可改变因为其内存空间是连续且固定的属性数组拥有一个公共的属性来获取其长度注意它是一个属性而非方法区别于的方法初始化方式静态初始化在创建时直接指定内容动态初始化指定数组长度由系统分配默认值所有元素默认为所有元素默认为所有元素默认为进阶多维数组中的多维数组本质上是数组的数组例如一个二维数组实际上是一个类型的数组它的每个元素都是一个数组因为是数组的数组所以支持不规则数组即二维数组的每一行可以有不同的长度第一行长度为第二行长度为第三行长度为核心工具方法详解是一个专门用于操作数组的工具类提供了大量高效的静态方法分类方法签名功能描述与注意事项排序对数组进行升序排序底层算法为对象使用为基本类型使用优化的快速排序可提供自定义比较器位于新语法会详讲查找必须在已排序数组上使用如果找到返回索引否则返回比较比较一维数组内容用于递归比较多维数组复制复制整个数组到新长度复制指定范围是实现数组扩容缩容的常用手段填充用同一个值填充数组的所有元素常用于初始化转换用于优雅地打印一维数组用于打印多维数组转换高频避坑返回一个固定大小的视图不支持操作对列表的修改会直接反映到原数组上反之亦然转换将数组转换为一个便于使用函数式编程进行链式操作极大增强了数组的处理能力代码示例详解排序升序排序升序排序结果降序排序查找索引索引未找到时返回未找到时返回比较因为和是不同的对象在双重数组中方法比较的是数组的引用而不是数组的内容这时候可以采用方法比较的是数组的内容复制复制前个元素从索引到不包含填充转换注意返回的是固定大小的列表不能添加或删除元素抛出流处理这在后续的语法中是至关重要的一个方法开启一个流并将每一个元素作为一个流来处理输出面试题数组列表对比维度数组大小固定创建时必须指定不可改变动态可根据需要自动扩容元素类型可存储基本数据类型和对象引用只能存储对象引用基本类型需自动装箱性能访问极快增删慢需手动实现访问快增删尤其在中间相对较慢泛型支持不支持泛型支持泛型提供编译时类型安全检查与功能功能有限需依赖工具类功能强大提供了丰富的增删改查方法核心选择依据数量固定且追求极致性能时选数组数量不固定需要灵活增删和丰富时选集合框架核心用途存储有序可重复的元素集合长度可动态改变是日常开发中最常用的集合类型之一详解核心用途最常用的实现适用于高频的随机访问查改场景类型介绍与底层原理底层基于动态数组实现当添加元素导致容量不足时会触发扩容机制通常是创建一个倍于原容量的新数组并将旧数据复制过去常用方法速查表方法签名功能描述在列表末尾添加元素在指定索引处插入元素获取指定索引处的元素替换指定索引处的元素移除指定索引处的元素返回列表中的元素数量代码示例详解在索引处插入第一个水果替换所有水果详解核心用途适用于高频的头尾增删操作场景它还实现了接口可作为队列或栈使用类型介绍与底层原理底层基于双向链表实现每个节点都存储着数据以及前后节点的引用常用方法速查表方法签名功能描述接口来源在列表头部添加元素在列表尾部添加元素获取并移除列表头部元素获取并移除列表尾部元素查看列表头部元素不移除代码示例作为队列使用入队处理任务出队下一个任务面试题对比特性底层结构动态数组双向链表随机访问快慢增删末尾快中间慢需移动元素头尾极快中间慢需遍历定位内存占用较少内存连续较大需额外空间存节点引用适用场景读多写少随机访问多写多读少头尾操作多集合框架接口继承自接口它代表一个不包含重复元素的集合这是与最本质的区别的主要设计目标就是确保其中每个元素的唯一性并提供快速的成员资格检查核心特性不重复中不允许出现重复的元素尝试添加一个已经存在的元素将会失败且不会抛出异常通常无序大部分的实现如不保证元素的存储和迭代顺序但也有例外如会保持插入顺序会保持排序顺序详解核心用途与场景是接口最常用性能最高的实现类其核心价值在于高效的元素去重与查找他是无序的在去重一个列表中会将元素打乱顺序不一定按照顺序最佳场景对一个数据集如进行快速去重需要快速判断某个元素是否存在于一个庞大的集合中存储一组唯一的或标识符类型介绍与去重原理面试去重流程保证元素唯一的两大基石是和方法当调用方法时其内部会执行参数是一个常量通常用于表示键已存在但不需要存储额外的值它常用于或的实现中作为占位符值以区分键是否被插入过的流程如下首先计算的值通过哈希算法定位到内部数组的某个桶索引如果这个桶是空的元素直接存入如果桶中已经有其他元素即发生哈希冲突则会遍历这个桶中的所有元素逐个用方法与新元素进行比较只要有一次返回就认定元素已存在添加失败如果所有比较结果都为则将新元素添加到这个桶中通常是链表或红黑树的末端结论若想让自定义的类如对象能在中被正确去重必须同时正确地重写和方法常用方法速查表方法签名功能描述添加元素如果元素已存在则返回集合不变移除指定元素如果成功移除返回判断是否包含指定元素这是的核心优势之一返回集合中的元素数量清空集合中的所有元素获取用于遍历集合的迭代器代码示例详解场景一基本数据类型去重使用为去重原始列表输出去重后集合输出顺序不保证场景二自定义对象的正确去重相同被认为是重复对象无法添加用户集合大小输出输出两个对象构造方法通常用唯一标识如来计算哈希详解核心用途与场景当你在需要的去重特性的同时还希望保持元素的插入顺序时是最佳选择最佳场景记录用户操作序列并去除重复操作需要去重但后续的展示或处理需要按照添加的先后顺序类型介绍与底层原理继承自它的实现方式与类似但其内部使用的是一个实例在的基础上额外维护了一个贯穿所有元素的双向链表正是这个链表保证了迭代的顺序与元素插入的顺序一致创建一个来记录用户操作模拟用户操作登录查看个人信息退出登录重复操作打印用户操作序列登录查看个人信息退出详解核心用途与场景当你需要一个时刻保持排序状态的且元素唯一的集合时是唯一的选择最佳场景排行榜的实时更新与展示需要从一个集合中快速获取最大或最小元素存储需要按特定规则排序的唯一数据创建一个实例自动按升序排序重复元素不会被添加输出中的元素自动排序中的元素获取最小值和最大值最小值最大值检查是否包含某个元素是否包含删除元素删除后的类型介绍与排序原理底层数据结构的底层是基于红黑树实现的这是一种自平衡的二叉搜索树元素在被添加时会根据其排序规则被放置在树的正确位置从而保证了集合始终处于有序状态实际上内部使用的是一个面试必考排序规则判断元素大小和唯一性的依据是元素的比较结果而非和它有两种排序方式自然排序存入的元素所属的类必须实现接口并重写方法中许多核心类如都已实现此接口定制排序如果在创建时通过构造函数传入一个的实现类那么将使用这个比较器来对元素进行排序这种方式更灵活也更常用常用方法速查表除了接口的通用方法外还提供了一系列强大的导航方法方法签名功能描述返回集合中的第一个最小元素返回集合中的最后一个最大元素返回小于给定元素的最大元素返回大于给定元素的最小元素返回小于等于给定元素的最大元素返回大于等于给定元素的最小元素移除并返回第一个最小元素移除并返回最后一个最大元素集合框架重点接口核心特性接口是集合框架的另一大分支它专门用于存储键值对数据中的每一个元素都包含一个唯一的键和一个与之关联的值核心特性键的唯一性中不允许存在重复的键如果尝试用一个已存在的键新值新值会覆盖旧值键的唯一性判断依赖于其和方法值可重复不同的键可以关联相同的值快速查找的核心价值在于能通过键来快速定位到值核心方法速查表核心操作这是日常使用中最频繁的增删改查操作方法签名功能描述注意事项最佳实践将指定的键值对存入如果键已存在则覆盖旧值返回值返回与关联的旧值如果是新的则返回根据键获取其对应的值如果不存在返回因此返回不一定代表不存在也可能对应的值本身就是根据键移除对应的键值对返回值返回被移除的所对应的如果不存在则返回判断中是否包含指定的键视图操作提供了三种视图用于以不同的角度审视中的数据这些视图与本身是联动的方法签名功能描述注意事项最佳实践返回中所有键组成的一个集合返回的是一个视图不是副本对这个进行移除操作会同步影响到原但不支持添加操作返回中所有值组成的一个同样是视图可以包含重复元素对这个集合的修改同样会影响原返回中所有键值对节点组成的集合最高效的遍历方式对象提供了和方法状态查询方法签名功能描述注意事项最佳实践返回中键值对的数量时间复杂度为判断是否为空即是否为比更具可读性清空中所有的键值对调用后将变为增强方法引入了一系列函数式方法极大地简化了代码方法签名功能描述注意事项最佳实践强烈推荐获取值若不存在则返回一个指定的优雅地解决了可能返回的问题避免了的样板代码仅当不存在或其值为时才存入该键值对可用于实现缓存单例初始化等原子性操作避免覆盖已有值使用表达式遍历的每个键值对是目前最简洁最推荐的遍历方式之一对指定的值进行计算和更新功能强大且原子适用于需要先再计算最后的复杂更新场景合并值如果不存在存入如果存在则用旧值和新执行函数并将结果存入非常适合实现计数统计等聚合操作比更强大仅当存在时才用新替换旧值批量操作方法签名功能描述注意事项最佳实践将另一个中所有的键值对都复制到当前中如果键冲突会用新中的值覆盖当前中的值总结与建议在日常开发中应熟练掌握核心操作和视图操作同时强烈建议多利用提供的新方法如等它们能让您的代码变得更简洁更安全更具表现力核心用途与场景是接口最通用的实现是日常开发中使用频率最高的集合之一它适用于任何需要通过一个唯一标识来存取管理一系列数据的场景典型场景实现内存缓存快速存取热点数据减轻数据库压力存储配置信息加载应用的配置项键为配置名值为配置值数据索引将中的数据按某个字段如用户转为实现快速查找计数统计统计文本中的词频或集合中各元素的出现次数传递灵活参数在方法间传递一组不固定的参数类似一个动态对象场景一实现内存缓存目的将耗时操作如数据库查询网络请求的结果存储起来当再次需要相同数据时直接从内存中快速获取避免重复执行耗时操作从而提升系统性能一个简单的内存缓存服务使用作为缓存容器根据获取数据如果缓存中有则直接返回如果没有则模拟一次耗时操作后存入缓存再返回数据的唯一标识获取到的数据先从缓存中查找成功从缓存中获取数据缓存中没有执行耗时操作例如查询数据库缓存未命中开始执行数据库查询将结果存入缓存数据已存入缓存模拟一个耗时的数据库查询模拟秒的延迟第一次请求会执行慢速操作第一次请求结果第二次请求应直接从缓存返回速度很快第二次请求结果输出缓存未命中开始执行数据库查询数据已存入缓存第一次请求结果成功从缓存中获取数据第二次请求结果场景二存储配置信息目的在程序启动时将配置文件如或中的键值对加载到中便于在程序运行期间随时快速地获取配置项模拟一个应用配置管理器在构造时模拟加载配置正在加载应用配置配置加载完成获取类型配置应用名称服务器端口输出正在加载应用配置配置加载完成应用名称服务器端口场景三将列表数据转换为索引目的将一个对象列表转换为以对象的某个唯一标识如为键的从而将原先的遍历查找操作优化为的直接访问操作使用将高效地转换为现在可以通过直接获取产品对象无需遍历通过快速查找到的产品强制生成无参构造函数假设是必需的但没有默认值也是必需的但没有默认值场景四计数统计目的统计一个集合或文本中每个独立元素出现的次数是实现该功能的完美数据结构遍历数组进行词频统计是的优雅写法避免了繁琐的判断如果已存在获取其当前计数值如果不存在返回默认值然后将计数值再存入各单词出现频率次输出各单词出现频率次次次次简介核心用途与场景当你需要一个键时刻保持排序状态的时是你的不二之选最佳场景需要按键的自然顺序或自定义顺序遍历需要对的键进行范围查找如查找所有在到之间的用户类型介绍与排序原理底层基于红黑树实现排序规则与完全相同依赖于键的接口自然排序或在构造时传入的定制排序代码示例详解创建时传入使字符串按长度排序遍历时输出会按的长度排序面试题这是其余的两个与最常用的作为对比特性已不推荐使用推荐线程安全非线程安全线程安全对整个表加锁线程安全分段锁性能远超支持允许和为不允许和为会抛不允许和为性能最高单线程最低锁竞争激烈高并发环境推荐用法单线程环境下的首选不推荐使用是过时的历史遗留类并发环境下的首选运算符详解本节将直接深入运算符的核心与难点剔除基础部分我们将聚焦于位运算符的强大能力逻辑与自增自减运算的常见陷阱优先级的避坑指南以及新版本带来的语法糖但在这之前要深入理解位运算符我们首先需要回归计算机的母语二进制并掌握不同进制间的转换以及计算机内部表示数字尤其是负数的精妙方式补码计算机基础进制与编码要深入理解位运算符我们首先需要回归计算机的母语二进制并掌握不同进制间的转换以及计算机内部表示数字尤其是负数的精妙方式补码常见进制介绍在日常生活中我们使用十进制但在计算机科学中我们必须熟悉以下几种进制二进制基数为由和组成是计算机物理层面的通用语言在中以或开头如八进制基数为由组成在中以开头如十进制基数为由组成我们最熟悉的进制十六进制基数为由和代表组成常用于表示内存地址颜色值等在中以或开头如进制转换核心方法十进制转二进制除取余法手算方法将十进制数连续除以直到商为然后将每步得到的余数逆序排列手算示例将十进制转换为二进制余余余余余余结果从下往上倒序取余数得到二进制转十进制按权展开法手算方法权重法法则从右到左写出每位的权重取出二进制中为的权重累加即可手算示例将转换为十进制权重计算机基础原码反码补码为何需要补码计算机硬件层面只有加法器为了简化电路设计希望将减法运算也统一为加法运算例如希望能变成原码无法满足这个要求而补码巧妙地解决了这个问题并统一了和的表示正数与负数的编码表示正数原码反码补码都相同负数原码最高位为符号位代表负其余位是其绝对值的二进制例如我们之前计算的的源码就为不足八位则不足若是则为反码在原码的基础上若为正数则反码不变若为负数则符号位不变其他全部取反变例如的反码就是补码在反码的基础上末位加例如的补码就是计算机内存中所有整数都以补码的形式存储补码的计算过程示例计算在一个位中的补码先求的原码求的原码符号位变求的反码符号位不变其余取反求的补码反码加所以在内存中存储的就是面试高频位运算符深度剖析位运算符直接在整数的二进制位上进行操作不关心其十进制值它们之所以在面试和高性能场景中备受青睐主要源于两大优势极致的运行效率因为更接近硬件操作和高效的空间利用例如用一个存储个开关状态现代的即时编译器已经非常智能可能会自动将这样的代码优化为但在一些对性能要求极为苛刻的场景或者在阅读一些经典框架如的源码时你会发现它们的身影源码片段这里的就是核心对比算术右移逻辑右移这个区别是面试中的经典考点它仅在处理负数时有所不同带符号右移算术右移进行右移操作时空出的高位会用原始数字的符号位来填充如果原数是正数符号位为则高位补如果原数是负数符号位为则高位补这样做的目的是保持数字的正负性质不变无符号右移逻辑右移进行右移操作时无论原始数字是正数还是负数空出的高位一律用填充这意味着对一个负数进行无符号右移后其结果会变成一个非常大的正数代码示例对比和位二进制补码调用方法格式化二进制字符串负数的二进制带符号右移高位补保持负数性质负数结果得到的位二进制补码无符号右移高位补负数结果得到将二进制字符串按每位分割并用空格连接原始二进制字符串格式化后的二进制字符串从字符串末尾开始每位添加一个空格第一步确定的位二进制补码表示计算机不会直接处理这个符号而是处理它的二进制补码求的原码在一个位的中的二进制表示非常简单求的反码在原码的基础上符号位最高位变为其余位按位取反变变求的补码将反码加就得到了在内存中实际存储的形式这就是我们操作的起始状态第二步执行带符号右移操作意味着将的补码向右移动两位原始补码向右移动两位所有的位都向右平移个位置最右边的两位被丢弃左边空出了两个位置填充高位因为是带符号右移所以空出的高位会用原始的符号位来填充的符号位是所以用来填充现在我们就得到了右移操作后的二进制结果第三步将结果转换回十进制我们需要将这个新的补码转换回我们能理解的十进制数以验证它就是观察符号位最高位是说明这是一个负数求其反码补码减求其原码符号位不变其余位取反读取数值这个原码表示的数值就是实战场景与代码详解场景一高效运算判断奇偶数比效率更高因为任何整数的二进制表示中最低位是则为奇数是则为偶数用于检查的二进制最低位是否为若不为则返回偶数是偶数代替乘除的幂运算相当于相当于输出避坑指南逻辑与自增自减运算符陷阱短路逻辑和与和或具有短路特性这是面试和日常编码中必须注意的细节短路与如果第一个操作数为则不会再执行第二个操作数直接判定整个表达式为短路或如果第一个操作数为则不会再执行第二个操作数直接判定整个表达式为和也可以用作逻辑运算符但它们不具备短路特性会执行所有操作数前自增先自增后取值表达式返回的是加之后的值后自增先取值后自增表达式返回的是加之前的原始值经典面试题的最终值是结果与原理解析输出结果是底层执行步骤将的当前值加载到一个临时变量区我们称之为自身的值加此时变量变为这个表达式返回的是加前的原始值即的值执行赋值操作将表达式的返回值赋给最终的值被重新覆盖为了运算符优先级与核心建议完全记住运算符优先级表是困难且不切实际的我们仅需关注几个易错点并养成一个好习惯易错点位运算符的优先级低于关系运算符如括号必不可少易错点的优先级高于如等价于核心开发建议不要依赖隐式优先级代码首先是写给人看的其次才是给机器执行的在任何可能产生歧义的复杂表达式中请毫不犹豫地使用圆括号来明确指定运算顺序这不仅能避免由优先级问题导致的难以察觉的更能极大地提升代码的可读性和可维护性深度循环与异常处理进阶本章将绕开基础的循环和判断的语法直接深入探讨程序流程控制的内功循环的底层机制现代化的语法演进以及构建健壮可靠程序的基石的异常处理框架深度循环与迭代器原理循环或称增强型循环是引入的语法糖它极大地简化了对数组和集合的遍历但要真正掌握它必须理解其背后的机制的底层真相循环并非一种新的循环结构而是编译器为我们提供的便利编译器在处理循环时会将其转换为不同的遍历方式对于数组它会被转换为一个传统的带索引的循环对于集合它会被转换为使用迭代器的循环这是理解所有相关问题的关键代码示例的编译后等价代码我们写的循环循环编译器实际生成的代码等价形式编译器生成的等价代码面试高频与机制接口提供了统一的遍历集合的方式其核心方法为检查是否有下一个元素获取下一个元素并后移指针和从集合中删除方法最后返回的那个元素快速失败机制这是等非并发集合的一个重要特性在集合内部有一个名为的变量记录着集合结构被修改如的次数当创建时迭代器会记下当时的值在迭代过程中每次调用时都会检查迭代器的记录值与集合当前的是否一致如果不一致说明在迭代期间集合被外部非迭代器自身修改了迭代器会立刻抛出以避免在数据不一致的状态下继续操作这就是快速失败代码示例触发与正确删除在任何情况下都绝对不要在循环中直接调用集合的或方法这是非常危险且不可靠的编码方式唯一正确且安全的方式是使用的方法错误的删除方式在循环中直接修改集合这会改变的导致异常错误演示触发了正确的删除方式使用迭代器自身的方法这是唯一安全的方式正确删除后的列表输出进阶带标签的和这是一个不常用但非常有用的语法它解决了如何从内层循环直接跳出外层循环的问题代码示例在二维数组中查找并跳出所有循环就是一个标签找到了目标在位置使用带标签的直接跳出名为的外层循环未找到目标核心异常处理机制面试题引入谈谈你对异常体系的理解和有什么区别和呢底层家族异常体系结构中所有可抛出的东西都继承自类它有两个重要的子类和代表了本身无法恢复的严重内部错误如栈溢出内存耗尽应用程序不应该也无法捕获或处理这类错误代表了应用程序层面可以处理的异常情况它又分为两大类受检异常继承自但非的异常编译器会强制开发者处理它们必须使用捕获或在方法签名上用声明它们通常代表了可预见的可恢复的外部问题如非受检异常即及其所有子类编译器不强制处理它们它们通常是由程序自身的逻辑错误引起的如面试高频的执行内幕面试题块一定会执行吗答绝大多数情况下是的块的设立目的就是为了保证无论是否发生异常某些清理代码如关闭资源都能得到执行只有两种极端情况不会执行在或块中调用了崩溃或线程被强制杀死面试题如果块中有语句块会执行吗答会执行执行流程是先执行块中的代码当遇到时会先将要返回的值保存起来然后去执行块块执行完毕后方法再带着之前保存的值返回最佳实践核心用途与场景用于自动管理和关闭实现了或接口的资源如文件流数据库连接等以防止资源泄漏代码示例优雅地关闭资源之前的写法需要在中手动关闭繁琐且容易出错读取文件的写法简洁且安全括号内声明的资源在块结束时会自动关闭直接读取文件无需关心关闭只需处理读取过程中的异常文件读取失败结论在处理任何可关闭的资源时永远优先使用这是现代开发的标准实践",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-14 16:38:56",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#2-0-Java%E5%9F%BA%E7%A1%80"><span class="toc-text">2.0 Java基础</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B%E5%85%A8%E8%A7%A3"><span class="toc-text">2.1 数据类型全解</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-1-%E5%BC%95%E8%A8%80%EF%BC%9AJava%E6%95%B0%E6%8D%AE%E4%B8%96%E7%95%8C%E7%9A%84%E4%B8%A4%E5%A4%A7%E5%9F%BA%E7%9F%B3"><span class="toc-text">2.1.1 引言：Java数据世界的两大基石</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9F%BA%E6%9C%AC%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B"><span class="toc-text">基本数据类型</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BC%95%E7%94%A8%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B"><span class="toc-text">引用数据类型</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-2-%E5%9F%BA%E6%9C%AC%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B"><span class="toc-text">2.1.2 基本数据类型</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%95%B4%E6%95%B0%E5%AE%B6%E6%97%8F-byte-short-int-long"><span class="toc-text">整数家族 (byte, short, int, long)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B5%AE%E7%82%B9%E6%95%B0%E5%AE%B6%E6%97%8F-float-double"><span class="toc-text">浮点数家族 (float, double)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#char-%E5%AD%97%E7%AC%A6%E7%B1%BB%E5%9E%8B-%E4%B8%8E-boolean-%E5%B8%83%E5%B0%94%E7%B1%BB%E5%9E%8B"><span class="toc-text">char (字符类型) 与 boolean (布尔类型)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-3-%E5%8C%85%E8%A3%85%E7%B1%BB"><span class="toc-text">2.1.3 包装类</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94"><span class="toc-text">核心用途</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#Integer-%E6%B7%B1%E5%BA%A6%E5%89%96%E6%9E%90"><span class="toc-text">Integer 深度剖析</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-4-%E5%AD%97%E7%AC%A6%E4%B8%B2%EF%BC%9AString"><span class="toc-text">2.1.4 字符串：String</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94-1"><span class="toc-text">核心用途</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E6%A0%B8%E5%BF%83%E9%9D%A2%E8%AF%95%E9%A2%98"><span class="toc-text">类型介绍与核心面试题</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-text">常用方法速查表</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3"><span class="toc-text">代码示例详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%85%B3%E8%81%94%E7%B1%BB%E5%9E%8B%EF%BC%9AStringBuilder%E4%B8%8EStringBuffer"><span class="toc-text">关联类型：StringBuilder与StringBuffer</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF"><span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E5%8E%9F%E7%90%86"><span class="toc-text">类型介绍与原理</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8-1"><span class="toc-text">常用方法速查表</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3-1"><span class="toc-text">代码示例详解</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98-%E4%BD%95%E6%97%B6%E4%BD%BF%E7%94%A8StringBuilder%E5%92%8CStringBuffer%EF%BC%9F"><span class="toc-text">[面试题] 何时使用StringBuilder和StringBuffer？</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-5-%E6%95%B0%E7%BB%84-Arrays-%E4%B8%8E%E5%85%B6%E5%B7%A5%E5%85%B7%E7%B1%BB"><span class="toc-text">2.1.5 数组 (Arrays) 与其工具类</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-1"><span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E5%88%9D%E5%A7%8B%E5%8C%96"><span class="toc-text">类型介绍与初始化</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#java-util-Arrays-%E6%A0%B8%E5%BF%83%E5%B7%A5%E5%85%B7%E6%96%B9%E6%B3%95%E8%AF%A6%E8%A7%A3"><span class="toc-text">java.util.Arrays 核心工具方法详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3-2"><span class="toc-text">代码示例详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%8E%92%E5%BA%8F"><span class="toc-text">排序</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%9F%A5%E6%89%BE"><span class="toc-text">查找</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%AF%94%E8%BE%83"><span class="toc-text">比较</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%A4%8D%E5%88%B6"><span class="toc-text">复制</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%A1%AB%E5%85%85"><span class="toc-text">填充</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E8%BD%AC%E6%8D%A2"><span class="toc-text">转换</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%B5%81%E5%A4%84%E7%90%86"><span class="toc-text">流处理</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98-%E6%95%B0%E7%BB%84-Array-vs-%E5%88%97%E8%A1%A8-ArrayList"><span class="toc-text">[面试题] 数组 (Array) vs. 列表 (ArrayList)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-6-%E9%9B%86%E5%90%88%E6%A1%86%E6%9E%B6%EF%BC%9AList"><span class="toc-text">2.1.6 集合框架：List</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94-2"><span class="toc-text">核心用途</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#ArrayList-%E8%AF%A6%E8%A7%A3"><span class="toc-text">ArrayList 详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#LinkedList-%E8%AF%A6%E8%A7%A3"><span class="toc-text">LinkedList 详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98-ArrayList-vs-LinkedList-%E5%AF%B9%E6%AF%94"><span class="toc-text">[面试题] ArrayList vs LinkedList 对比</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-7-%E9%9B%86%E5%90%88%E6%A1%86%E6%9E%B6%EF%BC%9ASet"><span class="toc-text">2.1.7 集合框架：Set</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#HashSet-%E8%AF%A6%E8%A7%A3"><span class="toc-text">HashSet 详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8-2"><span class="toc-text">常用方法速查表</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3-3"><span class="toc-text">代码示例详解</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#LinkedHashSet-%E8%AF%A6%E8%A7%A3"><span class="toc-text">LinkedHashSet 详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-2"><span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E5%BA%95%E5%B1%82%E5%8E%9F%E7%90%86"><span class="toc-text">类型介绍与底层原理</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#TreeSet-%E8%AF%A6%E8%A7%A3"><span class="toc-text">TreeSet 详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-3"><span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E6%8E%92%E5%BA%8F%E5%8E%9F%E7%90%86"><span class="toc-text">类型介绍与排序原理</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8-3"><span class="toc-text">常用方法速查表</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-8-%E9%9B%86%E5%90%88%E6%A1%86%E6%9E%B6%EF%BC%9AMap%EF%BC%88%E9%87%8D%E7%82%B9%EF%BC%89"><span class="toc-text">2.1.8 集合框架：Map（重点）</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#Map-%E6%8E%A5%E5%8F%A3%E6%A0%B8%E5%BF%83%E7%89%B9%E6%80%A7"><span class="toc-text">Map 接口核心特性</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#HashMap-%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-text">HashMap 核心方法速查表</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E6%93%8D%E4%BD%9C"><span class="toc-text">1. 核心操作</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E8%A7%86%E5%9B%BE%E6%93%8D%E4%BD%9C"><span class="toc-text">2. 视图操作</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E7%8A%B6%E6%80%81%E6%9F%A5%E8%AF%A2"><span class="toc-text">3. 状态查询</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#4-Java-8-%E5%A2%9E%E5%BC%BA%E6%96%B9%E6%B3%95"><span class="toc-text">4. Java 8+ 增强方法</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#5-%E6%89%B9%E9%87%8F%E6%93%8D%E4%BD%9C"><span class="toc-text">**5. 批量操作 **</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-4"><span class="toc-text">核心用途与场景</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E5%AE%9E%E7%8E%B0%E5%86%85%E5%AD%98%E7%BC%93%E5%AD%98"><span class="toc-text">场景一：实现内存缓存</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E5%AD%98%E5%82%A8%E9%85%8D%E7%BD%AE%E4%BF%A1%E6%81%AF"><span class="toc-text">场景二：存储配置信息</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9A%E5%B0%86%E5%88%97%E8%A1%A8%E6%95%B0%E6%8D%AE%E8%BD%AC%E6%8D%A2%E4%B8%BA%E7%B4%A2%E5%BC%95"><span class="toc-text">场景三：将列表数据转换为索引</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E5%9B%9B%EF%BC%9A%E8%AE%A1%E6%95%B0%E7%BB%9F%E8%AE%A1"><span class="toc-text">场景四：计数统计</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#TreeMap-%E7%AE%80%E4%BB%8B"><span class="toc-text">TreeMap 简介</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-5"><span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E6%8E%92%E5%BA%8F%E5%8E%9F%E7%90%86-1"><span class="toc-text">类型介绍与排序原理</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3-4"><span class="toc-text">代码示例详解</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98-HashMap-vs-Hashtable-vs-ConcurrentHashMap"><span class="toc-text">[面试题] HashMap vs Hashtable vs ConcurrentHashMap</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-%E8%BF%90%E7%AE%97%E7%AC%A6%E8%AF%A6%E8%A7%A3"><span class="toc-text">2.2 运算符详解</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-0-%E8%AE%A1%E7%AE%97%E6%9C%BA%E5%9F%BA%E7%A1%80%EF%BC%9A%E8%BF%9B%E5%88%B6%E4%B8%8E%E7%BC%96%E7%A0%81"><span class="toc-text">2.2.0 计算机基础：进制与编码</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B8%B8%E8%A7%81%E8%BF%9B%E5%88%B6%E4%BB%8B%E7%BB%8D"><span class="toc-text">常见进制介绍</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%BF%9B%E5%88%B6%E8%BD%AC%E6%8D%A2%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95"><span class="toc-text">进制转换核心方法</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%8D%81%E8%BF%9B%E5%88%B6%E8%BD%AC%E4%BA%8C%E8%BF%9B%E5%88%B6%EF%BC%88%E9%99%A42%E5%8F%96%E4%BD%99%E6%B3%95%EF%BC%89"><span class="toc-text">十进制转二进制（除2取余法）</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BA%8C%E8%BF%9B%E5%88%B6%E8%BD%AC%E5%8D%81%E8%BF%9B%E5%88%B6%EF%BC%88%E6%8C%89%E6%9D%83%E5%B1%95%E5%BC%80%E6%B3%95%EF%BC%89"><span class="toc-text">二进制转十进制（按权展开法）</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-3-%E8%AE%A1%E7%AE%97%E6%9C%BA%E5%9F%BA%E7%A1%80-%E5%8E%9F%E7%A0%81%E3%80%81%E5%8F%8D%E7%A0%81%E3%80%81%E8%A1%A5%E7%A0%81"><span class="toc-text">2.2.3 [计算机基础] 原码、反码、补码</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81%E8%A1%A5%E7%A0%81%EF%BC%9F"><span class="toc-text">为何需要补码？</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%AD%A3%E6%95%B0%E4%B8%8E%E8%B4%9F%E6%95%B0%E7%9A%84%E7%BC%96%E7%A0%81%E8%A1%A8%E7%A4%BA"><span class="toc-text">正数与负数的编码表示</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%A1%A5%E7%A0%81%E7%9A%84%E8%AE%A1%E7%AE%97%E8%BF%87%E7%A8%8B"><span class="toc-text">补码的计算过程</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-1-%E9%9D%A2%E8%AF%95%E9%AB%98%E9%A2%91-%E4%BD%8D%E8%BF%90%E7%AE%97%E7%AC%A6%E6%B7%B1%E5%BA%A6%E5%89%96%E6%9E%90"><span class="toc-text">2.2.1 [面试高频] 位运算符深度剖析</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E5%AF%B9%E6%AF%94%EF%BC%9A-%E7%AE%97%E6%9C%AF%E5%8F%B3%E7%A7%BB-vs-%E9%80%BB%E8%BE%91%E5%8F%B3%E7%A7%BB"><span class="toc-text">核心对比：&gt;&gt; (算术右移) vs. &gt;&gt;&gt; (逻辑右移)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E5%AF%B9%E6%AF%94-%E5%92%8C"><span class="toc-text">代码示例：对比 &gt;&gt; 和 &gt;&gt;&gt;</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%AE%9E%E6%88%98%E5%9C%BA%E6%99%AF%E4%B8%8E%E4%BB%A3%E7%A0%81%E8%AF%A6%E8%A7%A3"><span class="toc-text">实战场景与代码详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E9%AB%98%E6%95%88%E8%BF%90%E7%AE%97"><span class="toc-text">场景一：高效运算</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-2-%E9%81%BF%E5%9D%91%E6%8C%87%E5%8D%97-%E9%80%BB%E8%BE%91%E4%B8%8E%E8%87%AA%E5%A2%9E-%E8%87%AA%E5%87%8F%E8%BF%90%E7%AE%97%E7%AC%A6%E9%99%B7%E9%98%B1"><span class="toc-text">2.2.2 [避坑指南] 逻辑与自增/自减运算符陷阱</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%9F%AD%E8%B7%AF%E9%80%BB%E8%BE%91-%E5%92%8C"><span class="toc-text">短路逻辑 (&amp;&amp; 和 ||)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#i-vs-i"><span class="toc-text">i++ vs. ++i</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%BB%8F%E5%85%B8%E9%9D%A2%E8%AF%95%E9%A2%98%EF%BC%9Ai-i"><span class="toc-text">经典面试题：i = i++</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-3-%E8%BF%90%E7%AE%97%E7%AC%A6%E4%BC%98%E5%85%88%E7%BA%A7%E4%B8%8E%E6%A0%B8%E5%BF%83%E5%BB%BA%E8%AE%AE"><span class="toc-text">2.2.3 运算符优先级与核心建议</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E5%BC%80%E5%8F%91%E5%BB%BA%E8%AE%AE%EF%BC%9A%E4%B8%8D%E8%A6%81%E4%BE%9D%E8%B5%96%E9%9A%90%E5%BC%8F%E4%BC%98%E5%85%88%E7%BA%A7"><span class="toc-text">核心开发建议：不要依赖隐式优先级</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-%E6%B7%B1%E5%BA%A6-%E5%BE%AA%E7%8E%AF%E4%B8%8E%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E8%BF%9B%E9%98%B6"><span class="toc-text">2.3 [深度] 循环与异常处理进阶</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#2-3-1-%E6%B7%B1%E5%BA%A6-for-each%E5%BE%AA%E7%8E%AF%E4%B8%8EIterator%E8%BF%AD%E4%BB%A3%E5%99%A8%E5%8E%9F%E7%90%86"><span class="toc-text">2.3.1 [深度] for-each循环与Iterator迭代器原理</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#for-each%E7%9A%84%E5%BA%95%E5%B1%82%E7%9C%9F%E7%9B%B8"><span class="toc-text">for-each的底层真相</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9Afor-each%E7%9A%84%E7%BC%96%E8%AF%91%E5%90%8E%E7%AD%89%E4%BB%B7%E4%BB%A3%E7%A0%81"><span class="toc-text">代码示例：for-each的编译后等价代码</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%AB%98%E9%A2%91-Iterator-%E4%B8%8E-Fail-Fast-%E6%9C%BA%E5%88%B6"><span class="toc-text">[面试高频] Iterator 与 Fail-Fast 机制</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%A7%A6%E5%8F%91ConcurrentModificationException%E4%B8%8E%E6%AD%A3%E7%A1%AE%E5%88%A0%E9%99%A4"><span class="toc-text">代码示例：触发ConcurrentModificationException与正确删除</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-3-2-%E8%BF%9B%E9%98%B6-%E5%B8%A6%E6%A0%87%E7%AD%BE%E7%9A%84-break-%E5%92%8C-continue"><span class="toc-text">2.3.2 [进阶] 带标签的 break 和 continue</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E5%9C%A8%E4%BA%8C%E7%BB%B4%E6%95%B0%E7%BB%84%E4%B8%AD%E6%9F%A5%E6%89%BE%E5%B9%B6%E8%B7%B3%E5%87%BA%E6%89%80%E6%9C%89%E5%BE%AA%E7%8E%AF"><span class="toc-text">代码示例：在二维数组中查找并跳出所有循环</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-3-3-%E6%A0%B8%E5%BF%83-Java%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E6%9C%BA%E5%88%B6"><span class="toc-text">2.3.3 [核心] Java异常处理机制</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5"><span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%95%E5%B1%82-Throwable-%E5%AE%B6%E6%97%8F%EF%BC%9A%E5%BC%82%E5%B8%B8%E4%BD%93%E7%B3%BB%E7%BB%93%E6%9E%84"><span class="toc-text">[底层] Throwable 家族：异常体系结构</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%AB%98%E9%A2%91-try-catch-finally-%E7%9A%84%E6%89%A7%E8%A1%8C%E5%86%85%E5%B9%95"><span class="toc-text">[面试高频] try-catch-finally 的执行内幕</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-3-4-Java-7-try-with-resources-%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5"><span class="toc-text">2.3.4 [Java 7+] try-with-resources 最佳实践</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-6"><span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E4%BC%98%E9%9B%85%E5%9C%B0%E5%85%B3%E9%97%AD%E8%B5%84%E6%BA%90"><span class="toc-text">代码示例：优雅地关闭资源</span></a></li></ol></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Java（二）：2.0 Java基础</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-05-08T09:13:45.000Z" title="发表于 2025-05-08 17:13:45">2025-05-08</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-14T08:38:56.887Z" title="更新于 2025-07-14 16:38:56">2025-07-14</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">15.8k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>60分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Java（二）：2.0 Java基础"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/35626.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/35626.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Java基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Java（二）：2.0 Java基础</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-05-08T09:13:45.000Z" title="发表于 2025-05-08 17:13:45">2025-05-08</time><time itemprop="dateCreated datePublished" datetime="2025-07-14T08:38:56.887Z" title="更新于 2025-07-14 16:38:56">2025-07-14</time></header><div id="postchat_postcontent"><h2 id="2-0-Java基础"><a href="#2-0-Java基础" class="headerlink" title="2.0 Java基础"></a><strong>2.0 Java基础</strong></h2><p>本章将深入Java语言的根基——构成程序万物的基础语法。我们将从“数据类型”这个核心筒开始，对Java世界中所有用于承载数据的基本单元和复杂结构，进行一次全面、详尽的探险。这趟旅程不仅涵盖它们的用法，更会深入其设计原理与实战中的避坑技巧，为后续所有高阶概念的学习打下最坚实的地基。</p><h3 id="2-1-数据类型全解"><a href="#2-1-数据类型全解" class="headerlink" title="2.1 数据类型全解"></a><strong>2.1 数据类型全解</strong></h3><h4 id="2-1-1-引言：Java数据世界的两大基石"><a href="#2-1-1-引言：Java数据世界的两大基石" class="headerlink" title="2.1.1 引言：Java数据世界的两大基石"></a><strong>2.1.1 引言：Java数据世界的两大基石</strong></h4><p>在开始学习具体的数据类型之前，我们必须先建立一个宏观的认知：Java中的所有数据类型归根结底分为两大阵营。理解它们的本质区别，是掌握Java内存管理和程序性能的关键。</p><h5 id="基本数据类型"><a href="#基本数据类型" class="headerlink" title="基本数据类型"></a>基本数据类型</h5><ul><li><strong>定义</strong>：这是Java语言内置的、最基础的8种数据类型。它们并非对象。</li><li><strong>特点</strong>：变量本身直接存储<strong>数据的值</strong>。通常存储在<strong>栈（Stack）内存</strong>中（特指方法内的局部变量），这使得它们的存取速度非常快。</li></ul><h5 id="引用数据类型"><a href="#引用数据类型" class="headerlink" title="引用数据类型"></a><strong>引用数据类型</strong></h5><ul><li><strong>定义</strong>：除了8种基本数据类型之外的所有类型，包括我们自定义的类（Class）、接口（Interface）、数组（Array）、枚举（Enum）以及后面要讲的<code>String</code>和集合等，都属于引用类型。</li><li><strong>特点</strong>：变量存储的是一个<strong>内存地址</strong>，这个地址指向了真正的数据（即对象实例）。变量的引用地址存储在<strong>栈</strong>上，而对象实例则存储在<strong>堆（Heap）内存</strong>中。</li></ul><h4 id="2-1-2-基本数据类型"><a href="#2-1-2-基本数据类型" class="headerlink" title="2.1.2 基本数据类型"></a>2.1.2 基本数据类型</h4><p>这部分类型是构成程序运算的最小单位，它们不具备对象的方法，但性能极高。</p><h5 id="整数家族-byte-short-int-long"><a href="#整数家族-byte-short-int-long" class="headerlink" title="整数家族 (byte, short, int, long)"></a><strong>整数家族 (<code>byte</code>, <code>short</code>, <code>int</code>, <code>long</code>)</strong></h5><ul><li><p><strong>核心用途</strong></p><ul><li><code>byte</code>: 主要用于文件I/O操作，作为字节流进行数据传输，或在内存敏感的场景下节省空间。</li><li><code>short</code>: 使用场景较少，通常在一些底层或兼容性代码中可见。</li><li><code>int</code>: <strong>最常用</strong>的整数类型，用于计数、索引、ID等绝大多数整数运算场景。</li><li><code>long</code>: 用于表示需要超出<code>int</code>范围的整数，如时间戳（毫秒数）、大型文件大小、大型计数等2。</li></ul></li><li><p><strong>类型介绍与面试题</strong></p><table><thead><tr><th align="left">类型</th><th align="left">大小(位)</th><th align="left">取值范围</th><th align="left">默认值</th></tr></thead><tbody><tr><td align="left"><code>byte</code></td><td align="left">8</td><td align="left">-128 ~ 127</td><td align="left"><code>0</code></td></tr><tr><td align="left"><code>short</code></td><td align="left">16</td><td align="left">-32,768 ~ 32,767</td><td align="left"><code>0</code></td></tr><tr><td align="left"><code>int</code></td><td align="left">32</td><td align="left">-2³¹ ~ 2³¹-1</td><td align="left"><code>0</code></td></tr><tr><td align="left"><code>long</code></td><td align="left">64</td><td align="left">-2⁶³ ~ 2⁶³-1</td><td align="left"><code>0L</code></td></tr></tbody></table><ul><li><strong>[面试题] <code>byte b = 127; b++;</code> 结果是什么？</strong></li></ul><blockquote><p>答：结果是 <code>-128</code>。这是因为<code>byte</code>类型最大值为127（二进制 <code>01111111</code>），加1后发生<strong>溢出</strong>，二进制变为<code>10000000</code>，这在计算机补码表示中恰好是-128。</p></blockquote></li><li><p><strong>代码示例</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line">    <span class="keyword">package</span> com.example;</span><br><span class="line">    </span><br><span class="line">    <span class="comment">//TIP To &lt;b&gt;Run&lt;/b&gt; code, press &lt;shortcut actionId="Run"/&gt; or</span></span><br><span class="line"><span class="comment">// click the &lt;icon src="AllIcons.Actions.Execute"/&gt; icon in the gutter.</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">        <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">            <span class="type">long</span> <span class="variable">timestamp</span> <span class="operator">=</span> <span class="number">1672531200000L</span>;</span><br><span class="line">            <span class="type">byte</span> <span class="variable">b1</span> <span class="operator">=</span> <span class="number">10</span>;</span><br><span class="line">            <span class="type">byte</span> <span class="variable">b2</span> <span class="operator">=</span> <span class="number">20</span>;</span><br><span class="line">            <span class="comment">// byte b3 = b1 + b2; // 这行会编译错误，因为b1+b2的结果已经是int类型</span></span><br><span class="line">            <span class="type">int</span> <span class="variable">result</span> <span class="operator">=</span> b1 + b2; <span class="comment">// 正确的做法</span></span><br><span class="line">            System.out.println(<span class="string">"byte类型运算结果（已提升为int）: "</span> + result);</span><br><span class="line">        }</span><br><span class="line">    }</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="浮点数家族-float-double"><a href="#浮点数家族-float-double" class="headerlink" title="浮点数家族 (float, double)"></a><strong>浮点数家族 (<code>float</code>, <code>double</code>)</strong></h5><ul><li><p><strong>核心用途</strong>：用于需要小数的计算，如科学计算、图形学等。<code>double</code>（双精度）比<code>float</code>（单精度）更常用，因为它精度更高。</p></li><li><p><strong>类型介绍与避坑指南</strong></p><ul><li><p><code>float</code>: 32位，数值后需加<code>F</code>或<code>f</code>后缀。</p></li><li><p><code>double</code>: 64位，是默认的小数类型。</p></li><li><p><strong>[面试必考][避坑指南] 为何金融计算禁用<code>float</code>/<code>double</code>？</strong></p><blockquote><p>答：因为<code>float</code>和<code>double</code>采用二进制浮点数表示法，无法精确表示所有十进制小数（例如0.1）。这会导致舍入误差，在要求高精度的金融或商业计算中是致命的。<strong>最佳实践是使用<code>java.math.BigDecimal</code>类</strong>。</p></blockquote></li></ul></li><li><p><strong>代码示例</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.math.BigDecimal;</span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">        <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">            <span class="type">double</span> <span class="variable">d1</span> <span class="operator">=</span> <span class="number">0.2</span>;</span><br><span class="line">            <span class="type">double</span> <span class="variable">d2</span> <span class="operator">=</span> <span class="number">0.3</span>;</span><br><span class="line">            System.out.println(<span class="string">"0.1 + 0.2 = "</span> + (d1 + d2)); <span class="comment">// 输出通常不是精确的0.3</span></span><br><span class="line">            <span class="comment">// 使用BigDecimal进行精确计算</span></span><br><span class="line">            <span class="type">var</span> <span class="variable">bd1</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BigDecimal</span>(<span class="string">"0.1"</span>); <span class="comment">// 注意：使用字符串构造以保证精度</span></span><br><span class="line">            <span class="type">var</span> <span class="variable">bd2</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BigDecimal</span>(<span class="string">"0.2"</span>);</span><br><span class="line">            System.out.println(<span class="string">"BigDecimal 精确计算: "</span> + bd1.add(bd2));</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="char-字符类型-与-boolean-布尔类型"><a href="#char-字符类型-与-boolean-布尔类型" class="headerlink" title="char (字符类型) 与 boolean (布尔类型)"></a><strong><code>char</code> (字符类型) 与 <code>boolean</code> (布尔类型)</strong></h5><ul><li><strong>核心用途</strong><ul><li><code>char</code>: 表示单个字符，如字母、数字或符号。</li><li><code>boolean</code>: 用于逻辑判断，只有<code>true</code>和<code>false</code>两个值。</li></ul></li><li><strong>类型介绍与面试题</strong><ul><li><p><code>char</code>在Java中占16位（2字节），采用Unicode编码，因此可以表示世界上绝大多数语言的字符。</p></li><li><p><strong>[面试题] <code>char</code>类型能否存储一个中文汉字？</strong></p><blockquote><p>答：可以。因为Java的<code>char</code>类型使用Unicode编码，其范围覆盖了绝大多数汉字。</p></blockquote></li></ul></li></ul><h4 id="2-1-3-包装类"><a href="#2-1-3-包装类" class="headerlink" title="2.1.3 包装类"></a>2.1.3 包装类</h4><h5 id="核心用途"><a href="#核心用途" class="headerlink" title="核心用途"></a><strong>核心用途</strong></h5><p>包装类的存在是为了解决基本数据类型无法像对象一样被操作的问题。核心用途包括：</p><ol><li>在集合框架中使用，如 <code>List&lt;Integer&gt;</code>，因为泛型参数必须是对象。</li><li>允许值为<code>null</code>，用于表示缺失或未定义的状态。</li><li>包含了许多实用的静态方法，如类型转换、进制转换等。</li></ol><h5 id="Integer-深度剖析"><a href="#Integer-深度剖析" class="headerlink" title="Integer 深度剖析"></a><strong><code>Integer</code> 深度剖析</strong></h5><ul><li><p><strong>类型介绍与面试题</strong></p><ul><li><strong>自动装箱 (Autoboxing)</strong>: <code>Integer i = 100;</code> 编译器自动转换为 <code>Integer i = Integer.valueOf(100);</code>。</li><li><strong>自动拆箱 (Unboxing)</strong>: <code>int n = i;</code> 编译器自动转换为 <code>int n = i.intValue();</code>。</li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">Integer</span> <span class="variable">i</span> <span class="operator">=</span> <span class="number">100</span>; <span class="comment">// 自动装箱</span></span><br><span class="line">        <span class="comment">// 约等于这一行 -&gt; Integer i = Integer.valueOf(100);</span></span><br><span class="line">        <span class="type">int</span> <span class="variable">n</span> <span class="operator">=</span> i; <span class="comment">// 自动拆箱</span></span><br><span class="line">        <span class="comment">// 约等于这一行 -&gt; int n = i.intValue();</span></span><br><span class="line">        System.out.println(<span class="string">"n = "</span> + n); <span class="comment">// 输出 100</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>[面试必考] <code>Integer</code>缓存池</strong></p><blockquote><p>为了提高性能，<code>Integer.valueOf()</code>方法对 <strong>-128到127</strong> 之间的整数进行了缓存。当通过自动装箱或<code>valueOf()</code>创建这个范围内的<code>Integer</code>对象时，会直接返回缓存中的同一个对象。超出这个范围，则会<code>new</code>一个新的对象。因此，使用 <code>==</code> 比较时，若两个对象不是同一个实例，就会得到 <code>false</code>，从而引发问题。建议使用 <code>.equals()</code> 方法进行值比较。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// Integer 缓存池演示</span></span><br><span class="line">        <span class="type">Integer</span> <span class="variable">a</span> <span class="operator">=</span> <span class="number">100</span>;</span><br><span class="line">        <span class="type">Integer</span> <span class="variable">b</span> <span class="operator">=</span> <span class="number">100</span>;</span><br><span class="line">        System.out.println(<span class="string">"a == b (100): "</span> + (a == b)); <span class="comment">// true, 因为在缓存池内</span></span><br><span class="line"></span><br><span class="line">        <span class="type">Integer</span> <span class="variable">c</span> <span class="operator">=</span> <span class="number">200</span>;</span><br><span class="line">        <span class="type">Integer</span> <span class="variable">d</span> <span class="operator">=</span> <span class="number">200</span>;</span><br><span class="line">        System.out.println(<span class="string">"c == d (200): "</span> + (c == d)); <span class="comment">// false, 超出缓存范围，创建了新对象</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>常用方法速查表</strong></p><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>parseInt(String s)</code></td><td align="left">将字符串解析为<code>int</code>基本类型。</td></tr><tr><td align="left"><code>valueOf(String s / int i)</code></td><td align="left">将字符串或<code>int</code>转换为<code>Integer</code>对象。（推荐使用，会利用缓存）</td></tr><tr><td align="left"><code>intValue()</code></td><td align="left">将<code>Integer</code>对象转换为<code>int</code>基本类型。</td></tr><tr><td align="left">int <code>compareTo(Integer anotherInteger)</code></td><td align="left">比较两个<code>Integer</code>对象的大小。</td></tr><tr><td align="left"><code>boolean equals(Object obj)</code></td><td align="left">比较两个<code>Integer</code>对象的值是否相等。</td></tr></tbody></table></li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// parseInt：将字符串解析为int基本类型</span></span><br><span class="line">        <span class="type">int</span> <span class="variable">i1</span> <span class="operator">=</span> Integer.parseInt(<span class="string">"123"</span>);</span><br><span class="line">        System.out.println(i1); <span class="comment">// 输出：Int(123)</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// valueOf：将字符串或int转换为Integer对象（推荐使用，可利用缓存）</span></span><br><span class="line">        <span class="type">Integer</span> <span class="variable">i2</span> <span class="operator">=</span> Integer.valueOf(<span class="string">"456"</span>);</span><br><span class="line">        <span class="type">Integer</span> <span class="variable">i3</span> <span class="operator">=</span> Integer.valueOf(<span class="number">789</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// intValue：将Integer对象转为int基本类型</span></span><br><span class="line">        <span class="type">int</span> <span class="variable">i4</span> <span class="operator">=</span> i3.intValue();</span><br><span class="line">        System.out.println(i4); <span class="comment">// 输出：789</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// compareTo：比较两个Integer对象的大小</span></span><br><span class="line">        <span class="type">int</span> <span class="variable">result</span> <span class="operator">=</span> i2.compareTo(i3); <span class="comment">// 返回 -1、0 或 1</span></span><br><span class="line">        System.out.println(result); <span class="comment">// 输出：-1，因为i2 &lt; i3</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// equals：比较两个Integer对象的值是否相等</span></span><br><span class="line">        <span class="type">boolean</span> <span class="variable">isEqual</span> <span class="operator">=</span> i2.equals(i3); <span class="comment">// 返回 false，因为i2=456，i3=789</span></span><br><span class="line">        System.out.println(isEqual);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="2-1-4-字符串：String"><a href="#2-1-4-字符串：String" class="headerlink" title="2.1.4 字符串：String"></a><strong>2.1.4 字符串：<code>String</code></strong></h4><h5 id="核心用途-1"><a href="#核心用途-1" class="headerlink" title="核心用途"></a><strong>核心用途</strong></h5><p>用于表示和操作一切文本信息，是Java中使用最频繁的类之一。</p><h5 id="类型介绍与核心面试题"><a href="#类型介绍与核心面试题" class="headerlink" title="类型介绍与核心面试题"></a><strong>类型介绍与核心面试题</strong></h5><ul><li><p><strong>[面试必考] <code>String</code>的不可变性</strong>：<code>String</code>对象一旦被创建，其内容就不能被修改。任何对<code>String</code>的修改操作（如拼接、替换）都会返回一个<strong>新的</strong><code>String</code>对象。<strong>好处</strong>：</p></li><li><ol><li><p><strong>线程安全</strong>；</p></li><li><p><strong>利于缓存</strong>（字符串常量池）；</p></li></ol><p>3.作为<code>HashMap</code>的键时，可保证<code>hashCode</code>不变。</p></li><li><p><strong>[面试] 字符串常量池(String Pool)</strong>：位于堆内存中。当使用字面量（如 <code>String s = "Java";</code>）创建字符串时，JVM会先检查池中是否存在”Java”，如果存在则直接返回其引用，否则创建新的并放入池中。</p></li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 创建字符串并赋值</span></span><br><span class="line">        <span class="type">String</span> <span class="variable">str1</span> <span class="operator">=</span> <span class="string">"Hello"</span>;</span><br><span class="line">        <span class="type">String</span> <span class="variable">str2</span> <span class="operator">=</span> <span class="string">"Hello"</span>;</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检查两个字符串是否引用相同</span></span><br><span class="line">        <span class="type">boolean</span> <span class="variable">areSame</span> <span class="operator">=</span> (str1 == str2); <span class="comment">// 结果为true，因为str1和str2指向同一个对象</span></span><br><span class="line">        System.out.println(areSame);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 创建一个新的字符串对象</span></span><br><span class="line">        <span class="type">String</span> <span class="variable">str3</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">String</span>(<span class="string">"Hello"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检查str1和str3是否引用相同</span></span><br><span class="line">        <span class="type">boolean</span> <span class="variable">areSame2</span> <span class="operator">=</span> (str1 == str3); <span class="comment">// 结果为false，因为str3是新生成的对象</span></span><br><span class="line">        System.out.println(areSame2);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><ul><li><p><strong>[面试题] <code>new String("abc")</code> 创建了几个对象？</strong></p><blockquote><p>答：可能是一个，也可能是两个。如果常量池中已有”abc”，则只在堆中创建一个新的<code>String</code>对象。如果常量池中没有，则会在池中创建一个，同时在堆中也创建一个，共两个对象。</p></blockquote></li></ul><h5 id="常用方法速查表"><a href="#常用方法速查表" class="headerlink" title="常用方法速查表"></a><strong>常用方法速查表</strong></h5><table><thead><tr><th align="left">分类</th><th align="left">方法签名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><strong>获取/判断</strong></td><td align="left"><code>length()</code>, <code>isEmpty()</code>, <code>charAt(int index)</code>, <code>contains(CharSequence s)</code></td><td align="left">获取长度、判空、获取字符、判断包含</td></tr><tr><td align="left"><strong>查找</strong></td><td align="left"><code>indexOf(String str)</code>, <code>lastIndexOf(String str)</code></td><td align="left">查找子串首次/末次出现的位置</td></tr><tr><td align="left"><strong>比较</strong></td><td align="left"><code>equals(Object anObject)</code>, <code>equalsIgnoreCase(String anotherString)</code></td><td align="left">内容比较（区分/不区分大小写）</td></tr><tr><td align="left"><strong>截取/分割</strong></td><td align="left"><code>substring(int beginIndex, int endIndex)</code>, <code>split(String regex)</code></td><td align="left">截取子串，按正则表达式分割</td></tr><tr><td align="left"><strong>替换</strong></td><td align="left"><code>replace(char oldChar, char newChar)</code>, <code>replaceAll(String regex, String replacement)</code></td><td align="left">字符替换，正则替换</td></tr><tr><td align="left"><strong>转换</strong></td><td align="left"><code>toLowerCase()</code>, <code>toUpperCase()</code>, <code>trim()</code>, <code>toCharArray()</code>, <code>getBytes()</code></td><td align="left">大小写转换、去首尾空格、转数组</td></tr></tbody></table><h5 id="代码示例详解"><a href="#代码示例详解" class="headerlink" title="代码示例详解"></a><strong>代码示例详解</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br></pre></td><td class="code"><pre><span class="line"><span class="type">String</span> <span class="variable">str</span> <span class="operator">=</span> <span class="string">"Hello, World!"</span>;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 获取长度</span></span><br><span class="line"><span class="type">int</span> <span class="variable">length</span> <span class="operator">=</span> str.length(); <span class="comment">// 13</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 判空</span></span><br><span class="line"><span class="type">boolean</span> <span class="variable">isEmpty</span> <span class="operator">=</span> str.isEmpty(); <span class="comment">// false</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 获取字符</span></span><br><span class="line"><span class="type">char</span> <span class="variable">c</span> <span class="operator">=</span> str.charAt(<span class="number">0</span>); <span class="comment">// 'H'</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 判断包含</span></span><br><span class="line"><span class="type">boolean</span> <span class="variable">contains</span> <span class="operator">=</span> str.contains(<span class="string">"World"</span>); <span class="comment">// true</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 查找子串首次出现的位置</span></span><br><span class="line"><span class="type">int</span> <span class="variable">indexOf</span> <span class="operator">=</span> str.indexOf(<span class="string">"World"</span>); <span class="comment">// 7</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 查找子串末次出现的位置</span></span><br><span class="line"><span class="type">int</span> <span class="variable">lastIndexOf</span> <span class="operator">=</span> str.lastIndexOf(<span class="string">"o"</span>); <span class="comment">// 8</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 内容比较（区分大小写）</span></span><br><span class="line"><span class="type">boolean</span> <span class="variable">equals</span> <span class="operator">=</span> str.equals(<span class="string">"Hello, World!"</span>); <span class="comment">// true</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 内容比较（不区分大小写）</span></span><br><span class="line"><span class="type">boolean</span> <span class="variable">equalsIgnoreCase</span> <span class="operator">=</span> str.equalsIgnoreCase(<span class="string">"hello, world!"</span>); <span class="comment">// true</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 截取子串</span></span><br><span class="line"><span class="type">String</span> <span class="variable">substring</span> <span class="operator">=</span> str.substring(<span class="number">0</span>, <span class="number">5</span>); <span class="comment">// "Hello"</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 按正则表达式分割</span></span><br><span class="line">String[] split = str.split(<span class="string">", "</span>); <span class="comment">// ["Hello", "World!"]</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 字符替换</span></span><br><span class="line"><span class="type">String</span> <span class="variable">replace</span> <span class="operator">=</span> str.replace(<span class="string">'o'</span>, <span class="string">'a'</span>); <span class="comment">// "Hella, Warld!"</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 正则替换</span></span><br><span class="line"><span class="type">String</span> <span class="variable">replaceAll</span> <span class="operator">=</span> str.replaceAll(<span class="string">"[a-zA-Z]"</span>, <span class="string">"*"</span>); <span class="comment">// "*****, *****!"</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 小写转换</span></span><br><span class="line"><span class="type">String</span> <span class="variable">toLowerCase</span> <span class="operator">=</span> str.toLowerCase(); <span class="comment">// "hello, world!"</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 大写转换</span></span><br><span class="line"><span class="type">String</span> <span class="variable">toUpperCase</span> <span class="operator">=</span> str.toUpperCase(); <span class="comment">// "HELLO, WORLD!"</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 去首尾空格</span></span><br><span class="line"><span class="type">String</span> <span class="variable">trim</span> <span class="operator">=</span> <span class="string">"   Hello   "</span>.trim(); <span class="comment">// "Hello"</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 转字符数组</span></span><br><span class="line"><span class="type">char</span>[] toCharArray = str.toCharArray(); <span class="comment">// ['H','e','l','l','o',',',' ','W','o','r','l','d','!']</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 转字节数组</span></span><br><span class="line"><span class="type">byte</span>[] getBytes = str.getBytes(); <span class="comment">// 字节形式的字符串</span></span><br></pre></td></tr></tbody></table></figure><h5 id="关联类型：StringBuilder与StringBuffer"><a href="#关联类型：StringBuilder与StringBuffer" class="headerlink" title="关联类型：StringBuilder与StringBuffer"></a>关联类型：<code>StringBuilder</code>与<code>StringBuffer</code></h5><h6 id="核心用途与场景"><a href="#核心用途与场景" class="headerlink" title="核心用途与场景"></a><strong>核心用途与场景</strong></h6><p>当我们需要频繁地修改或拼接字符串时，使用不可变的 <code>String</code> 会因创建大量临时对象而导致性能低下。<code>StringBuilder</code> 和 <code>StringBuffer</code> 正是为解决这一问题而生的可变字符串序列。</p><ul><li><strong><code>StringBuilder</code></strong>: 适用于<strong>单线程</strong>环境下的字符串拼接或修改。是绝大多数场景下的首选，因为它性能最高。</li><li><strong><code>StringBuffer</code></strong>: 适用于<strong>多线程</strong>环境下，需要保证共享字符串数据线程安全的场景。</li></ul><h6 id="类型介绍与原理"><a href="#类型介绍与原理" class="headerlink" title="类型介绍与原理"></a><strong>类型介绍与原理</strong></h6><p><code>StringBuilder</code> 和 <code>StringBuffer</code> 本质上都是一个<strong>可变的字符数组容器</strong>。与 <code>String</code> 每次操作都返回新对象不同，它们的大部分操作（如<code>append</code>）都是在内部的字符数组上直接进行的，只有在数组容量不足时才会进行扩容，从而避免了不必要的对象创建。</p><ul><li><strong>可变性 (Mutability)</strong>：它们的内部 <code>char[]</code> 数组不是 <code>final</code> 的，并且长度可以动态改变。</li><li><strong>线程安全机制 (面试必考)</strong>：<ul><li><code>StringBuffer</code>：它的所有公开方法（如 <code>append</code>, <code>insert</code>）都被 <code>synchronized</code> 关键字修饰，这意味着在同一时刻，只有一个线程能访问这些方法，从而保证了线程安全。但加锁也带来了额外的性能开销。</li><li><code>StringBuilder</code>：它在Java 5中被引入，可以看作是 <code>StringBuffer</code> 的一个非线程安全版本，去掉了 <code>synchronized</code> 关键字，因此在单线程环境下性能更优。</li></ul></li></ul><h6 id="常用方法速查表-1"><a href="#常用方法速查表-1" class="headerlink" title="常用方法速查表"></a><strong>常用方法速查表</strong></h6><p>（以下方法对 <code>StringBuilder</code> 和 <code>StringBuffer</code> 均适用）</p><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>append(...)</code></td><td align="left">在序列末尾追加内容。此方法被重载以接受所有基本类型、<code>String</code>等。</td></tr><tr><td align="left"><code>insert(int offset, ...)</code></td><td align="left">在指定索引位置插入内容。</td></tr><tr><td align="left"><code>delete(int start, int end)</code></td><td align="left">删除指定范围内的字符。</td></tr><tr><td align="left"><code>deleteCharAt(int index)</code></td><td align="left">删除指定位置的单个字符。</td></tr><tr><td align="left"><code>replace(int start, int end, String str)</code></td><td align="left">用指定字符串替换范围内的内容。</td></tr><tr><td align="left"><code>reverse()</code></td><td align="left">将序列反转。</td></tr><tr><td align="left"><code>length()</code></td><td align="left">返回当前序列的长度。</td></tr><tr><td align="left"><code>capacity()</code></td><td align="left">返回当前内部数组的容量。</td></tr><tr><td align="left"><code>toString()</code></td><td align="left">将当前的可变序列转换为一个不可变的<code>String</code>对象。</td></tr></tbody></table><h6 id="代码示例详解-1"><a href="#代码示例详解-1" class="headerlink" title="代码示例详解"></a><strong>代码示例详解</strong></h6><ul><li><p><strong>场景一：循环中的高效拼接</strong></p><blockquote><p>这是 <code>StringBuilder</code> 最核心、最经典的应用场景。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 低效的方法</span></span><br><span class="line">        <span class="type">String</span> <span class="variable">str</span> <span class="operator">=</span> <span class="string">""</span>;</span><br><span class="line">        <span class="keyword">for</span> (<span class="type">int</span> <span class="variable">i</span> <span class="operator">=</span> <span class="number">0</span>; i &lt; <span class="number">10</span>; i++) {</span><br><span class="line">            str += i;</span><br><span class="line">        }</span><br><span class="line">        System.out.println(<span class="string">"String '+' 拼接结果: "</span> + str);</span><br><span class="line"></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 高效的方法：使用StringBuilder</span></span><br><span class="line">        <span class="type">StringBuilder</span> <span class="variable">sb</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">StringBuilder</span>();</span><br><span class="line">        <span class="keyword">for</span> (<span class="type">int</span> <span class="variable">i</span> <span class="operator">=</span> <span class="number">0</span>; i &lt; <span class="number">10</span>; i++) {</span><br><span class="line">            sb.append(i);</span><br><span class="line">        }</span><br><span class="line">        <span class="type">String</span> <span class="variable">result</span> <span class="operator">=</span> sb.toString(); <span class="comment">// 最后需要时再转换为String</span></span><br><span class="line">        System.out.println(<span class="string">"StringBuilder 拼接结果: "</span> + result);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>场景二：链式调用构建复杂字符串</strong></p><blockquote><p><code>append</code>等方法返回对象本身，使得链式编程成为可能，代码更简洁。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">StringBuilder</span> <span class="variable">queryBuilder</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">StringBuilder</span>();</span><br><span class="line">        queryBuilder.append(<span class="string">"SELECT "</span>)</span><br><span class="line">                .append(<span class="string">"id, name, email "</span>)</span><br><span class="line">                .append(<span class="string">"FROM users "</span>)</span><br><span class="line">                .append(<span class="string">"WHERE age &gt; ?"</span>);</span><br><span class="line">        <span class="type">String</span> <span class="variable">sqlQuery</span> <span class="operator">=</span> queryBuilder.toString();</span><br><span class="line">        System.out.println(<span class="string">"构建的SQL查询: "</span> + sqlQuery);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>场景三：字符串反转</strong></p><blockquote><p><code>String</code>本身没有提供反转方法，使用<code>StringBuilder</code>可以轻松实现。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">String</span> <span class="variable">original</span> <span class="operator">=</span> <span class="string">"level"</span>;</span><br><span class="line">        <span class="type">StringBuilder</span> <span class="variable">reverseBuilder</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">StringBuilder</span>(original);</span><br><span class="line">        <span class="type">String</span> <span class="variable">reversed</span> <span class="operator">=</span> reverseBuilder.reverse().toString();</span><br><span class="line">        System.out.println(<span class="string">"'"</span> + original + <span class="string">"' 的反转是 '"</span> + reversed + <span class="string">"'"</span>); <span class="comment">// 🤭</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h6 id="面试题-何时使用StringBuilder和StringBuffer？"><a href="#面试题-何时使用StringBuilder和StringBuffer？" class="headerlink" title="[面试题] 何时使用StringBuilder和StringBuffer？"></a><strong>[面试题] 何时使用<code>StringBuilder</code>和<code>StringBuffer</code>？</strong></h6><p>答：当需要进行<strong>大量或循环内的字符串拼接</strong>时，应使用它们来避免创建大量临时的<code>String</code>对象，从而提高性能。在选择时：</p><ul><li><strong>单线程环境</strong>：优先且总是使用 <code>StringBuilder</code>，因为它没有同步开销，性能更好。</li><li><strong>多线程环境</strong>：如果一个字符串对象需要被多个线程共享和修改，必须使用 <code>StringBuffer</code> 来保证线程安全。</li></ul><hr><h4 id="2-1-5-数组-Arrays-与其工具类"><a href="#2-1-5-数组-Arrays-与其工具类" class="headerlink" title="2.1.5 数组 (Arrays) 与其工具类"></a><strong>2.1.5 数组 (Arrays) 与其工具类</strong></h4><h5 id="核心用途与场景-1"><a href="#核心用途与场景-1" class="headerlink" title="核心用途与场景"></a><strong>核心用途与场景</strong></h5><p>数组是Java中最基础、最高效的数据结构之一，其核心用途是在内存中存储<strong>固定大小</strong>、<strong>同一类型</strong>的元素序列。</p><ul><li><strong>核心场景</strong>:<ul><li>当元素数量固定，且对性能有较高要求时（如算法题、底层数据缓冲）。</li><li>作为更复杂数据结构（如<code>ArrayList</code>, <code>HashMap</code>）的内部实现。</li><li>表示和操作矩阵或表格（使用多维数组）。</li><li>方法的参数或返回值，尤其是<code>main(String[] args)</code>。</li></ul></li></ul><h5 id="类型介绍与初始化"><a href="#类型介绍与初始化" class="headerlink" title="类型介绍与初始化"></a><strong>类型介绍与初始化</strong></h5><ul><li><p><strong>数组即对象</strong>：在Java中，数组是一个<strong>引用类型</strong>。数组变量存储在栈中，它指向堆内存中一块<strong>连续开辟</strong>的空间。这也解释了为什么数组的长度一旦创建就不可改变，因为其内存空间是连续且固定的。</p></li><li><p><strong><code>length</code> 属性</strong>：数组拥有一个公共的<code>final</code>属性<code>length</code>来获取其长度，注意它是一个<strong>属性</strong>，而非方法（区别于<code>List</code>的<code>size()</code>方法）。</p></li><li><p><strong>初始化方式</strong>：</p><ol><li><strong>静态初始化</strong>：在创建时直接指定内容。<figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="type">int</span>[] staticArray = {<span class="number">10</span>, <span class="number">20</span>, <span class="number">30</span>};</span><br><span class="line">String[] names = <span class="keyword">new</span> <span class="title class_">String</span>[]{<span class="string">"Java"</span>, <span class="string">"Python"</span>};</span><br></pre></td></tr></tbody></table></figure></li><li><strong>动态初始化</strong>：指定数组长度，由系统分配默认值。<figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="type">int</span>[] dynamicArray = <span class="keyword">new</span> <span class="title class_">int</span>[<span class="number">5</span>]; <span class="comment">// 所有元素默认为 0</span></span><br><span class="line"><span class="type">boolean</span>[] flags = <span class="keyword">new</span> <span class="title class_">boolean</span>[<span class="number">3</span>]; <span class="comment">// 所有元素默认为 false</span></span><br><span class="line">String[] strings = <span class="keyword">new</span> <span class="title class_">String</span>[<span class="number">4</span>]; <span class="comment">// 所有元素默认为 null</span></span><br></pre></td></tr></tbody></table></figure></li></ol></li><li><p><strong>[进阶] 多维数组</strong></p><ul><li>Java中的多维数组本质上是“<strong>数组的数组</strong>”。例如，一个二维数组 <code>int[][]</code> 实际上是一个<code>int[]</code>类型的数组，它的每个元素都是一个<code>int[]</code>数组。</li><li>因为是“数组的数组”，所以Java支持<strong>不规则数组</strong>（Ragged Array），即二维数组的每一行可以有不同的长度。</li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">int</span>[][] arr = <span class="keyword">new</span> <span class="title class_">int</span>[<span class="number">3</span>][];</span><br><span class="line">        arr[<span class="number">0</span>] = <span class="keyword">new</span> <span class="title class_">int</span>[<span class="number">2</span>];  <span class="comment">// 第一行长度为 2</span></span><br><span class="line">        arr[<span class="number">1</span>] = <span class="keyword">new</span> <span class="title class_">int</span>[<span class="number">3</span>];  <span class="comment">// 第二行长度为 3</span></span><br><span class="line">        arr[<span class="number">2</span>] = <span class="keyword">new</span> <span class="title class_">int</span>[<span class="number">5</span>];  <span class="comment">// 第三行长度为 5</span></span><br><span class="line">        </span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="java-util-Arrays-核心工具方法详解"><a href="#java-util-Arrays-核心工具方法详解" class="headerlink" title="java.util.Arrays 核心工具方法详解"></a><strong><code>java.util.Arrays</code> 核心工具方法详解</strong></h5><p><code>java.util.Arrays</code> 是一个专门用于操作数组的工具类，提供了大量高效的静态方法。</p><table><thead><tr><th align="left">分类</th><th align="left">方法签名</th><th align="left">功能描述与注意事项</th></tr></thead><tbody><tr><td align="left"><strong>排序</strong></td><td align="left"><code>sort(T[] a)</code> / <code>sort(T[] a, Comparator c)</code></td><td align="left">对数组进行升序排序。<strong>底层算法</strong>：为对象使用TimSort，为基本类型使用优化的快速排序。可提供自定义比较器（位于Java8新语法会详讲<code>Comparator</code>）</td></tr><tr><td align="left"><strong>查找</strong></td><td align="left"><code>binarySearch(T[] a, T key)</code></td><td align="left"><strong>必须在已排序数组上使用</strong>。如果找到，返回索引；否则返回 <code>-(insertion point) - 1</code>。</td></tr><tr><td align="left"><strong>比较</strong></td><td align="left"><code>equals(T[] a, T[] a2)</code> / <code>deepEquals(Object[] a1, Object[] a2)</code></td><td align="left"><code>equals</code> 比较一维数组内容。<code>deepEquals</code> 用于递归比较<strong>多维数组</strong>。</td></tr><tr><td align="left"><strong>复制</strong></td><td align="left"><code>copyOf(T[] original, int len)</code> / <code>copyOfRange(T[] o, int f, int t)</code></td><td align="left"><code>copyOf</code> 复制整个数组到新长度。<code>copyOfRange</code> 复制指定范围。是实现数组<strong>扩容/缩容</strong>的常用手段。</td></tr><tr><td align="left"><strong>填充</strong></td><td align="left"><code>fill(T[] a, T val)</code></td><td align="left">用同一个值填充数组的所有元素，常用于初始化。</td></tr><tr><td align="left"><strong>转换</strong></td><td align="left"><code>toString(T[] a)</code> / <code>deepToString(Object[] a)</code></td><td align="left"><code>toString</code> 用于优雅地打印一维数组。<code>deepToString</code> 用于打印<strong>多维数组</strong>。</td></tr><tr><td align="left"><strong>转换</strong></td><td align="left"><code>asList(T... a)</code></td><td align="left"><strong>[高频避坑]</strong> 返回一个<strong>固定大小</strong>的<code>List</code>视图，<strong>不支持add/remove操作</strong>。对列表的修改会直接反映到原数组上，反之亦然。</td></tr><tr><td align="left"><strong>转换</strong></td><td align="left"><code>stream(T[] array)</code></td><td align="left"><strong>[Java 8+]</strong> 将数组转换为一个<code>Stream</code>，便于使用函数式编程进行链式操作，极大增强了数组的处理能力。</td></tr></tbody></table><h5 id="代码示例详解-2"><a href="#代码示例详解-2" class="headerlink" title="代码示例详解"></a><strong>代码示例详解</strong></h5><h6 id="排序"><a href="#排序" class="headerlink" title="排序"></a>排序</h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.Comparator;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        Integer[] arr = {<span class="number">5</span>, <span class="number">2</span>, <span class="number">9</span>, <span class="number">1</span>, <span class="number">8</span>, <span class="number">6</span>};</span><br><span class="line">        Arrays.sort(arr); <span class="comment">// 升序排序</span></span><br><span class="line">        System.out.println(<span class="string">"升序排序结果："</span> + Arrays.toString(arr)); <span class="comment">// [1, 2, 5, 6, 8, 9]</span></span><br><span class="line"></span><br><span class="line">        Arrays.sort(arr, Comparator.reverseOrder()); <span class="comment">// 降序排序</span></span><br><span class="line">        System.out.println(Arrays.toString(arr)); <span class="comment">// [9, 8, 6, 5, 2, 1]</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="查找"><a href="#查找" class="headerlink" title="查找"></a>查找</h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">int</span>[] arr = {<span class="number">10</span>, <span class="number">20</span>, <span class="number">30</span>, <span class="number">40</span>, <span class="number">50</span>};</span><br><span class="line">        <span class="type">int</span> <span class="variable">index</span> <span class="operator">=</span> Arrays.binarySearch(arr, <span class="number">30</span>);</span><br><span class="line">        System.out.println(<span class="string">"索引: "</span> + index); <span class="comment">// 索引: 2</span></span><br><span class="line"></span><br><span class="line">        index = Arrays.binarySearch(arr, <span class="number">25</span>);</span><br><span class="line">        System.out.println(<span class="string">"未找到时返回: "</span> + index); <span class="comment">// 未找到时返回: -3</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="比较"><a href="#比较" class="headerlink" title="比较"></a>比较</h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">int</span>[] a = {<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>};</span><br><span class="line">        <span class="type">int</span>[] b = {<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>};</span><br><span class="line">        <span class="type">int</span>[] c = {<span class="number">1</span>, <span class="number">2</span>, <span class="number">4</span>};</span><br><span class="line"></span><br><span class="line">        System.out.println(Arrays.equals(a, b)); <span class="comment">// true</span></span><br><span class="line">        System.out.println(Arrays.equals(a, c)); <span class="comment">// false</span></span><br><span class="line"></span><br><span class="line">        <span class="type">int</span>[][] d = {{<span class="number">1</span>, <span class="number">2</span>}, {<span class="number">3</span>, <span class="number">4</span>}};</span><br><span class="line">        <span class="type">int</span>[][] e = {{<span class="number">1</span>, <span class="number">2</span>}, {<span class="number">3</span>, <span class="number">4</span>}};</span><br><span class="line">        <span class="type">int</span>[][] f = {{<span class="number">1</span>, <span class="number">2</span>}, {<span class="number">3</span>, <span class="number">5</span>}};</span><br><span class="line">        <span class="comment">// false 因为d和e是不同的对象,在双重数组中,equals方法比较的是数组的引用,而不是数组的内容</span></span><br><span class="line">        System.out.println(Arrays.equals(d,e));</span><br><span class="line">        <span class="comment">// 这时候可以采用deepEquals方法,比较的是数组的内容</span></span><br><span class="line">        System.out.println(Arrays.deepEquals(d, e)); <span class="comment">// true</span></span><br><span class="line"></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="复制"><a href="#复制" class="headerlink" title="复制"></a>复制</h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">int</span>[] original = {<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>};</span><br><span class="line">        <span class="type">int</span>[] copy = Arrays.copyOf(original, <span class="number">3</span>); <span class="comment">// 复制前3个元素</span></span><br><span class="line">        System.out.println(Arrays.toString(copy)); <span class="comment">// [1, 2, 3]</span></span><br><span class="line"></span><br><span class="line">        <span class="type">int</span>[] rangeCopy = Arrays.copyOfRange(original, <span class="number">1</span>, <span class="number">4</span>); <span class="comment">// 从索引1到3（不包含4）</span></span><br><span class="line">        System.out.println(Arrays.toString(rangeCopy)); <span class="comment">// [2, 3, 4]</span></span><br><span class="line"></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="填充"><a href="#填充" class="headerlink" title="填充"></a>填充</h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">int</span>[] arr = <span class="keyword">new</span> <span class="title class_">int</span>[<span class="number">5</span>];</span><br><span class="line">        Arrays.fill(arr, <span class="number">10</span>);</span><br><span class="line">        System.out.println(Arrays.toString(arr)); <span class="comment">// [10, 10, 10, 10, 10]</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="转换"><a href="#转换" class="headerlink" title="转换"></a>转换</h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        String[] arr = {<span class="string">"a"</span>, <span class="string">"b"</span>, <span class="string">"c"</span>};</span><br><span class="line">        System.out.println(Arrays.toString(arr)); <span class="comment">// [a, b, c]</span></span><br><span class="line"></span><br><span class="line">        List&lt;String&gt; list = Arrays.asList(arr);</span><br><span class="line">        System.out.println(list); <span class="comment">// [a, b, c]</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 注意：asList 返回的是固定大小的列表，不能添加或删除元素</span></span><br><span class="line">        <span class="comment">// list.add("d"); // 抛出 UnsupportedOperationException</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="流处理"><a href="#流处理" class="headerlink" title="流处理"></a>流处理</h6><p>这在后续的Java8语法中是至关重要的一个方法，开启一个流，并将每一个元素作为一个流来处理</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Stream;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        String[] arr = {<span class="string">"apple"</span>, <span class="string">"banana"</span>, <span class="string">"cherry"</span>};</span><br><span class="line">        Stream&lt;String&gt; stream = Arrays.stream(arr);</span><br><span class="line">        stream.forEach(System.out::println);</span><br><span class="line">        <span class="comment">// 输出:</span></span><br><span class="line">        <span class="comment">// apple</span></span><br><span class="line">        <span class="comment">// banana</span></span><br><span class="line">        <span class="comment">// cherry</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="面试题-数组-Array-vs-列表-ArrayList"><a href="#面试题-数组-Array-vs-列表-ArrayList" class="headerlink" title="[面试题] 数组 (Array) vs. 列表 (ArrayList)"></a><strong>[面试题] 数组 (Array) vs. 列表 (<code>ArrayList</code>)</strong></h5><table><thead><tr><th align="left">对比维度</th><th align="left">数组 (Array)</th><th align="left"><code>ArrayList</code></th></tr></thead><tbody><tr><td align="left"><strong>大小</strong></td><td align="left"><strong>固定</strong>，创建时必须指定，不可改变。</td><td align="left"><strong>动态</strong>，可根据需要自动扩容。</td></tr><tr><td align="left"><strong>元素类型</strong></td><td align="left">可存储<strong>基本数据类型</strong>和对象引用。</td><td align="left"><strong>只能存储对象引用</strong>（基本类型需自动装箱）。</td></tr><tr><td align="left"><strong>性能</strong></td><td align="left">访问（get/set）极快 ，增删慢（需手动实现）。</td><td align="left">访问快，增删（尤其在中间）相对较慢。</td></tr><tr><td align="left"><strong>泛型支持</strong></td><td align="left">不支持泛型。</td><td align="left">支持泛型，提供编译时类型安全检查。</td></tr><tr><td align="left"><strong>API与功能</strong></td><td align="left">功能有限，需依赖<code>Arrays</code>工具类。</td><td align="left">功能强大，提供了丰富的增删改查方法。</td></tr><tr><td align="left"><strong>核心选择依据</strong></td><td align="left">数量固定且追求极致性能时选<strong>数组</strong>。</td><td align="left">数量不固定，需要灵活增删和丰富API时选<code>ArrayList</code>。</td></tr></tbody></table><h4 id="2-1-6-集合框架：List"><a href="#2-1-6-集合框架：List" class="headerlink" title="2.1.6 集合框架：List"></a><strong>2.1.6 集合框架：<code>List</code></strong></h4><h5 id="核心用途-2"><a href="#核心用途-2" class="headerlink" title="核心用途"></a><strong>核心用途</strong></h5><p>存储<strong>有序、可重复</strong>的元素集合，长度可动态改变。是日常开发中最常用的集合类型之一。</p><h5 id="ArrayList-详解"><a href="#ArrayList-详解" class="headerlink" title="ArrayList 详解"></a><strong><code>ArrayList</code> 详解</strong></h5><ul><li><strong>核心用途</strong>：最常用的<code>List</code>实现，适用于**高频的随机访问（查、改）**场景。</li><li><strong>类型介绍与底层原理</strong>：底层基于<strong>动态数组</strong>实现。当添加元素导致容量不足时，会触发<strong>扩容</strong>机制，通常是创建一个1.5倍于原容量的新数组，并将旧数据复制过去。</li><li><strong>常用方法速查表</strong><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>boolean add(E e)</code></td><td align="left">在列表末尾添加元素。</td></tr><tr><td align="left"><code>void add(int index, E element)</code></td><td align="left">在指定索引处插入元素。</td></tr><tr><td align="left"><code>E get(int index)</code></td><td align="left">获取指定索引处的元素。</td></tr><tr><td align="left"><code>E set(int index, E element)</code></td><td align="left">替换指定索引处的元素。</td></tr><tr><td align="left"><code>E remove(int index)</code></td><td align="left">移除指定索引处的元素。</td></tr><tr><td align="left"><code>int size()</code></td><td align="left">返回列表中的元素数量。</td></tr></tbody></table></li><li><strong>代码示例详解</strong><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line">    <span class="keyword">package</span> com.example;</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">import</span> java.util.ArrayList;</span><br><span class="line">    <span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line">    <span class="keyword">import</span> java.util.stream.Stream;</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">        <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">            ArrayList&lt;String&gt; fruits = <span class="keyword">new</span> <span class="title class_">ArrayList</span>&lt;&gt;();</span><br><span class="line">            fruits.add(<span class="string">"Apple"</span>);</span><br><span class="line">            fruits.add(<span class="string">"Banana"</span>);</span><br><span class="line">            fruits.add(<span class="number">0</span>, <span class="string">"Orange"</span>); <span class="comment">// 在索引0处插入</span></span><br><span class="line">    </span><br><span class="line">            System.out.println(<span class="string">"第一个水果: "</span> + fruits.get(<span class="number">0</span>)); <span class="comment">// Orange</span></span><br><span class="line">            fruits.set(<span class="number">1</span>, <span class="string">"Grape"</span>); <span class="comment">// 替换</span></span><br><span class="line">            System.out.println(<span class="string">"所有水果: "</span> + fruits); <span class="comment">// [Orange, Grape, Banana]</span></span><br><span class="line">    </span><br><span class="line">        }</span><br><span class="line">    }</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="LinkedList-详解"><a href="#LinkedList-详解" class="headerlink" title="LinkedList 详解"></a><strong><code>LinkedList</code> 详解</strong></h5><ul><li><p><strong>核心用途</strong>：适用于<strong>高频的头尾增删</strong>操作场景。它还实现了<code>Deque</code>接口，可作为<strong>队列</strong>或<strong>栈</strong>使用。</p></li><li><p><strong>类型介绍与底层原理</strong>：底层基于<strong>双向链表</strong>实现。每个节点都存储着数据以及前后节点的引用。</p></li><li><p><strong>常用方法速查表</strong></p><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th><th align="left">接口来源</th></tr></thead><tbody><tr><td align="left"><code>void addFirst(E e)</code></td><td align="left">在列表头部添加元素。</td><td align="left"><code>Deque</code></td></tr><tr><td align="left"><code>void addLast(E e)</code></td><td align="left">在列表尾部添加元素。</td><td align="left"><code>Deque</code></td></tr><tr><td align="left"><code>E poll()</code> / <code>E pollFirst()</code></td><td align="left">获取并移除列表头部元素。</td><td align="left"><code>Queue</code>/<code>Deque</code></td></tr><tr><td align="left"><code>E pollLast()</code></td><td align="left">获取并移除列表尾部元素。</td><td align="left"><code>Deque</code></td></tr><tr><td align="left"><code>E peek()</code> / <code>E peekFirst()</code></td><td align="left">查看列表头部元素（不移除）。</td><td align="left"><code>Queue</code>/<code>Deque</code></td></tr></tbody></table></li><li><p><strong>代码示例</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.ArrayList;</span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.LinkedList;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Stream;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        LinkedList&lt;String&gt; taskQueue = <span class="keyword">new</span> <span class="title class_">LinkedList</span>&lt;&gt;();</span><br><span class="line">        <span class="comment">// 作为队列使用</span></span><br><span class="line">        taskQueue.offer(<span class="string">"Task 1"</span>); <span class="comment">// 入队</span></span><br><span class="line">        taskQueue.offer(<span class="string">"Task 2"</span>);</span><br><span class="line">        System.out.println(<span class="string">"处理任务: "</span> + taskQueue.poll()); <span class="comment">// 出队</span></span><br><span class="line">        System.out.println(<span class="string">"下一个任务: "</span> + taskQueue.peek());</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="面试题-ArrayList-vs-LinkedList-对比"><a href="#面试题-ArrayList-vs-LinkedList-对比" class="headerlink" title="[面试题] ArrayList vs LinkedList 对比"></a><strong>[面试题] <code>ArrayList</code> vs <code>LinkedList</code> 对比</strong></h5><table><thead><tr><th align="left">特性</th><th align="left"><code>ArrayList</code></th><th align="left"><code>LinkedList</code></th></tr></thead><tbody><tr><td align="left"><strong>底层结构</strong></td><td align="left">动态数组</td><td align="left">双向链表</td></tr><tr><td align="left"><strong>随机访问(get)</strong></td><td align="left">快</td><td align="left">慢</td></tr><tr><td align="left"><strong>增/删(add/remove)</strong></td><td align="left">末尾快，中间慢（需移动元素）</td><td align="left">头尾极快，中间慢（需遍历定位）</td></tr><tr><td align="left"><strong>内存占用</strong></td><td align="left">较少，内存连续</td><td align="left">较大，需额外空间存节点引用</td></tr><tr><td align="left"><strong>适用场景</strong></td><td align="left">读多写少，随机访问多</td><td align="left">写多读少，头尾操作多</td></tr></tbody></table><hr><h4 id="2-1-7-集合框架：Set"><a href="#2-1-7-集合框架：Set" class="headerlink" title="2.1.7 集合框架：Set"></a><strong>2.1.7 集合框架：<code>Set</code></strong></h4><p><code>Set</code>接口继承自<code>Collection</code>接口，它代表一个不包含重复元素的集合。这是<code>Set</code>与<code>List</code>最本质的区别。<code>Set</code>的主要设计目标就是确保其中每个元素的唯一性，并提供快速的成员资格检查。</p><ul><li><strong>核心特性</strong>:<ol><li><strong>不重复 (No Duplicates)</strong>：<code>Set</code>中不允许出现重复的元素。尝试添加一个已经存在的元素将会失败，且不会抛出异常。</li><li><strong>通常无序 (Generally Unordered)</strong>：大部分<code>Set</code>的实现（如<code>HashSet</code>）不保证元素的存储和迭代顺序。但也有例外，如<code>LinkedHashSet</code>会保持插入顺序，<code>TreeSet</code>会保持排序顺序。</li></ol></li></ul><h5 id="HashSet-详解"><a href="#HashSet-详解" class="headerlink" title="HashSet 详解"></a><strong><code>HashSet</code> 详解</strong></h5><p><strong>核心用途与场景</strong></p><p><code>HashSet</code>是<code>Set</code>接口最常用、性能最高的实现类，其核心价值在于<strong>高效的元素去重与查找</strong>，他是无序的，在去重一个列表中会将元素打乱，顺序不一定按照顺序</p><ul><li><strong>最佳场景</strong>：<ul><li>对一个数据集（如<code>List</code>）进行快速去重。</li><li>需要快速判断某个元素是否存在于一个庞大的集合中。</li><li>存储一组唯一的ID或标识符。</li></ul></li></ul><p><strong>类型介绍与去重原理</strong></p><ul><li><p><strong>[面试] 去重流程</strong>：<br><code>HashSet</code>保证元素唯一的两大基石是<code>hashCode()</code>和<code>equals()</code>方法。当调用<code>add(element)</code>方法时，其内部会执行<code>map.put(element, PRESENT)</code>,参数 <code>PRESENT</code> 是一个常量，通常用于表示键已存在，但不需要存储额外的值。它常用于 <code>HashSet</code> 或 <code>HashMap</code> 的实现中，作为占位符值，以区分键是否被插入过。</p></li><li><p><code>HashMap</code>的<code>put</code>流程如下：<br>首先，计算<code>element</code>的<code>hashCode()</code>值，通过哈希算法定位到内部数组的某个“桶”（bucket）索引。如果这个桶是空的，元素直接存入。如果桶中已经有其他元素（即发生哈希冲突），则会遍历这个桶中的所有元素，逐个用<code>equals()</code>方法与新元素进行比较。只要有一次<code>equals()</code>返回<code>true</code>，就认定元素已存在，添加失败；如果所有比较结果都为<code>false</code>，则将新元素添加到这个桶中（通常是链表或红黑树的末端）。</p></li></ul><blockquote><p><strong>结论</strong>：若想让自定义的类（如<code>User</code>）对象能在<code>HashSet</code>中被正确去重，<strong>必须同时、正确地重写<code>hashCode()</code>和<code>equals()</code>方法</strong>。</p></blockquote><h6 id="常用方法速查表-2"><a href="#常用方法速查表-2" class="headerlink" title="常用方法速查表"></a><strong>常用方法速查表</strong></h6><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>boolean add(E e)</code></td><td align="left">添加元素。如果元素已存在，则返回<code>false</code>，集合不变。</td></tr><tr><td align="left"><code>boolean remove(Object o)</code></td><td align="left">移除指定元素。如果成功移除，返回<code>true</code>。</td></tr><tr><td align="left"><code>boolean contains(Object o)</code></td><td align="left">判断是否包含指定元素。这是<code>Set</code>的核心优势之一。</td></tr><tr><td align="left"><code>int size()</code></td><td align="left">返回集合中的元素数量。</td></tr><tr><td align="left"><code>void clear()</code></td><td align="left">清空集合中的所有元素。</td></tr><tr><td align="left"><code>Iterator&lt;E&gt; iterator()</code></td><td align="left">获取用于遍历集合的迭代器。</td></tr></tbody></table><h6 id="代码示例详解-3"><a href="#代码示例详解-3" class="headerlink" title="代码示例详解"></a><strong>代码示例详解</strong></h6><ul><li><p><strong>场景一：基本数据类型去重</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.*;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Stream;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 使用HashSet为List去重</span></span><br><span class="line">        List&lt;String&gt; nameList = Arrays.asList(<span class="string">"Alice"</span>, <span class="string">"Bob"</span>, <span class="string">"Alice"</span>, <span class="string">"Charlie"</span>);</span><br><span class="line">        Set&lt;String&gt; uniqueNames = <span class="keyword">new</span> <span class="title class_">HashSet</span>&lt;&gt;(nameList);</span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"原始列表: "</span> + nameList);      <span class="comment">// 输出: [Alice, Bob, Alice, Charlie]</span></span><br><span class="line">        System.out.println(<span class="string">"去重后集合: "</span> + uniqueNames); <span class="comment">// 输出: [Alice, Bob, Charlie] (顺序不保证)</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>场景二：自定义对象的正确去重</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.*;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        Set&lt;User&gt; users = <span class="keyword">new</span> <span class="title class_">HashSet</span>&lt;&gt;();</span><br><span class="line">        users.add(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"U001"</span>, <span class="string">"Alice"</span>));</span><br><span class="line">        users.add(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"U002"</span>, <span class="string">"Bob"</span>));</span><br><span class="line">        users.add(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"U001"</span>, <span class="string">"Alice V2"</span>)); <span class="comment">// id相同，被认为是重复对象，无法添加</span></span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"用户集合大小: "</span> + users.size()); <span class="comment">// 输出: 2</span></span><br><span class="line">        System.out.println(users); <span class="comment">// 输出两个User对象</span></span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">User</span> {</span><br><span class="line">    String id;</span><br><span class="line">    String name;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 构造方法</span></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">User</span><span class="params">(String id, String name)</span> {</span><br><span class="line">        <span class="built_in">this</span>.id = id;</span><br><span class="line">        <span class="built_in">this</span>.name = name;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> <span class="type">int</span> <span class="title function_">hashCode</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> java.util.Objects.hash(id); <span class="comment">// 通常用唯一标识（如ID）来计算哈希</span></span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> <span class="type">boolean</span> <span class="title function_">equals</span><span class="params">(Object obj)</span> {</span><br><span class="line">        <span class="keyword">if</span> (<span class="built_in">this</span> == obj) <span class="keyword">return</span> <span class="literal">true</span>;</span><br><span class="line">        <span class="keyword">if</span> (obj == <span class="literal">null</span> || getClass() != obj.getClass()) <span class="keyword">return</span> <span class="literal">false</span>;</span><br><span class="line">        <span class="type">User</span> <span class="variable">user</span> <span class="operator">=</span> (User) obj;</span><br><span class="line">        <span class="keyword">return</span> java.util.Objects.equals(id, user.id);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line">    </span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="LinkedHashSet-详解"><a href="#LinkedHashSet-详解" class="headerlink" title="LinkedHashSet 详解"></a><strong><code>LinkedHashSet</code> 详解</strong></h5><h6 id="核心用途与场景-2"><a href="#核心用途与场景-2" class="headerlink" title="核心用途与场景"></a><strong>核心用途与场景</strong></h6><p>当你在需要<code>Set</code>的去重特性的同时，还希望<strong>保持元素的插入顺序</strong>时，<code>LinkedHashSet</code>是最佳选择。</p><ul><li><strong>最佳场景</strong>：<ul><li>记录用户操作序列，并去除重复操作。</li><li>需要去重，但后续的展示或处理需要按照添加的先后顺序。</li></ul></li></ul><h6 id="类型介绍与底层原理"><a href="#类型介绍与底层原理" class="headerlink" title="类型介绍与底层原理"></a><strong>类型介绍与底层原理</strong></h6><p><code>LinkedHashSet</code>继承自<code>HashSet</code>。它的实现方式与<code>HashSet</code>类似，但其内部使用的是一个<code>LinkedHashMap</code>实例。<code>LinkedHashMap</code>在<code>HashMap</code>的基础上，额外维护了一个贯穿所有元素的双向链表，正是这个链表保证了迭代的顺序与元素插入的顺序一致。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.LinkedHashSet;</span><br><span class="line"><span class="keyword">import</span> java.util.Set;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 创建一个LinkedHashSet来记录用户操作</span></span><br><span class="line">        Set&lt;String&gt; userActions = <span class="keyword">new</span> <span class="title class_">LinkedHashSet</span>&lt;&gt;();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 模拟用户操作</span></span><br><span class="line">        userActions.add(<span class="string">"登录"</span>);</span><br><span class="line">        userActions.add(<span class="string">"查看个人信息"</span>);</span><br><span class="line">        userActions.add(<span class="string">"退出"</span>);</span><br><span class="line">        userActions.add(<span class="string">"登录"</span>); <span class="comment">// 重复操作</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 打印用户操作序列</span></span><br><span class="line">        System.out.println(<span class="string">"User Actions: "</span> + userActions); <span class="comment">// [登录, 查看个人信息, 退出]</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="TreeSet-详解"><a href="#TreeSet-详解" class="headerlink" title="TreeSet 详解"></a><strong><code>TreeSet</code> 详解</strong></h5><h5 id="核心用途与场景-3"><a href="#核心用途与场景-3" class="headerlink" title="核心用途与场景"></a><strong>核心用途与场景</strong></h5><p>当你需要一个时刻保持<strong>排序状态</strong>的、且元素唯一的集合时，<code>TreeSet</code>是唯一的选择。</p><ul><li><strong>最佳场景</strong>：<ul><li>排行榜的实时更新与展示。</li><li>需要从一个集合中快速获取最大或最小元素。</li><li>存储需要按特定规则排序的唯一数据。</li></ul></li></ul><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.TreeSet;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 创建一个 TreeSet 实例，自动按升序排序</span></span><br><span class="line">        TreeSet&lt;Integer&gt; treeSet = <span class="keyword">new</span> <span class="title class_">TreeSet</span>&lt;&gt;();</span><br><span class="line">        treeSet.add(<span class="number">10</span>);</span><br><span class="line">        treeSet.add(<span class="number">30</span>);</span><br><span class="line">        treeSet.add(<span class="number">20</span>);</span><br><span class="line">        treeSet.add(<span class="number">10</span>); <span class="comment">// 重复元素不会被添加</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 输出 TreeSet 中的元素（自动排序）</span></span><br><span class="line">        System.out.println(<span class="string">"TreeSet 中的元素: "</span> + treeSet);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 获取最小值和最大值</span></span><br><span class="line">        System.out.println(<span class="string">"最小值: "</span> + treeSet.first());</span><br><span class="line">        System.out.println(<span class="string">"最大值: "</span> + treeSet.last());</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检查是否包含某个元素</span></span><br><span class="line">        System.out.println(<span class="string">"是否包含 20: "</span> + treeSet.contains(<span class="number">20</span>));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 删除元素</span></span><br><span class="line">        treeSet.remove(<span class="number">20</span>);</span><br><span class="line">        System.out.println(<span class="string">"删除 20 后的 TreeSet: "</span> + treeSet);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="类型介绍与排序原理"><a href="#类型介绍与排序原理" class="headerlink" title="类型介绍与排序原理"></a><strong>类型介绍与排序原理</strong></h5><ul><li><p><strong>[底层] 数据结构</strong>：<code>TreeSet</code>的底层是基于**红黑树（Red-Black Tree）**实现的，这是一种自平衡的二叉搜索树。元素在被添加时，会根据其排序规则被放置在树的正确位置，从而保证了集合始终处于有序状态。实际上，<code>TreeSet</code>内部使用的是一个<code>TreeMap</code>。</p></li><li><p><strong>[面试必考] 排序规则</strong>:<br><code>TreeSet</code>判断元素大小和唯一性的依据是元素的<strong>比较</strong>结果，而非<code>hashCode()</code>和<code>equals()</code>。它有两种排序方式：</p><ol><li><strong>自然排序</strong>：存入<code>TreeSet</code>的元素所属的类必须实现<code>java.lang.Comparable</code>接口，并重写<code>compareTo(T o)</code>方法。Java中许多核心类如<code>Integer</code>、<code>String</code>都已实现此接口。</li><li><strong>定制排序</strong>：如果在创建<code>TreeSet</code>时，通过构造函数传入一个<code>java.util.Comparator</code>的实现类，那么<code>TreeSet</code>将使用这个比较器来对元素进行排序。这种方式更灵活，也更常用。</li></ol></li></ul><h5 id="常用方法速查表-3"><a href="#常用方法速查表-3" class="headerlink" title="常用方法速查表"></a><strong>常用方法速查表</strong></h5><p>除了<code>Set</code>接口的通用方法外，<code>TreeSet</code>还提供了一系列强大的导航方法。</p><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>E first()</code></td><td align="left">返回集合中的第一个（最小）元素。</td></tr><tr><td align="left"><code>E last()</code></td><td align="left">返回集合中的最后一个（最大）元素。</td></tr><tr><td align="left"><code>E lower(E e)</code></td><td align="left">返回小于给定元素e的最大元素。</td></tr><tr><td align="left"><code>E higher(E e)</code></td><td align="left">返回大于给定元素e的最小元素。</td></tr><tr><td align="left"><code>E floor(E e)</code></td><td align="left">返回小于等于给定元素e的最大元素。</td></tr><tr><td align="left"><code>E ceiling(E e)</code></td><td align="left">返回大于等于给定元素e的最小元素。</td></tr><tr><td align="left"><code>E pollFirst()</code></td><td align="left">移除并返回第一个（最小）元素。</td></tr><tr><td align="left"><code>E pollLast()</code></td><td align="left">移除并返回最后一个（最大）元素。</td></tr></tbody></table><hr><h4 id="2-1-8-集合框架：Map（重点）"><a href="#2-1-8-集合框架：Map（重点）" class="headerlink" title="2.1.8 集合框架：Map（重点）"></a>2.1.8 集合框架：Map（重点）</h4><h5 id="Map-接口核心特性"><a href="#Map-接口核心特性" class="headerlink" title="Map 接口核心特性"></a><strong><code>Map</code> 接口核心特性</strong></h5><p><code>Map</code>接口是Java集合框架的另一大分支，它专门用于存储**键值对（Key-Value）**数据。<code>Map</code>中的每一个元素都包含一个唯一的键（Key）和一个与之关联的值（Value）。</p><ul><li><strong>核心特性</strong>:<ol><li><strong>键的唯一性 (Unique Keys)</strong>：<code>Map</code>中不允许存在重复的键。如果尝试用一个已存在的键<code>put</code>新值，新值会覆盖旧值。键的唯一性判断依赖于其<code>hashCode()</code>和<code>equals()</code>方法。</li><li><strong>值可重复</strong>：不同的键可以关联相同的值。</li><li><strong>快速查找</strong>：<code>Map</code>的核心价值在于能通过键来快速定位到值</li></ol></li></ul><hr><h5 id="HashMap-核心方法速查表"><a href="#HashMap-核心方法速查表" class="headerlink" title="HashMap 核心方法速查表"></a><strong><code>HashMap</code> 核心方法速查表</strong></h5><h6 id="1-核心操作"><a href="#1-核心操作" class="headerlink" title="1. 核心操作"></a><strong>1. 核心操作</strong></h6><p>这是日常使用中最频繁的增、删、改、查操作。</p><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>V put(K key, V value)</code></td><td align="left">将指定的键值对存入<code>Map</code>。如果键已存在，则<strong>覆盖</strong>旧值。</td><td align="left"><strong>返回值</strong>：返回与<code>key</code>关联的<strong>旧值</strong>；如果<code>key</code>是新的，则返回<code>null</code>。</td></tr><tr><td align="left"><code>V get(Object key)</code></td><td align="left">根据键获取其对应的值。</td><td align="left">如果<code>key</code>不存在，返回<code>null</code>。因此，<code>get()</code>返回<code>null</code>不一定代表<code>key</code>不存在，也可能<code>key</code>对应的值本身就是<code>null</code>。</td></tr><tr><td align="left"><code>V remove(Object key)</code></td><td align="left">根据键移除对应的键值对。</td><td align="left"><strong>返回值</strong>：返回被移除的<code>key</code>所对应的<code>value</code>；如果<code>key</code>不存在，则返回<code>null</code>。</td></tr><tr><td align="left"><code>boolean containsKey(Object key)</code></td><td align="left">判断<code>Map</code>中是否包含指定的键。</td><td align="left"></td></tr></tbody></table><h6 id="2-视图操作"><a href="#2-视图操作" class="headerlink" title="2. 视图操作"></a><strong>2. 视图操作</strong></h6><p><code>HashMap</code>提供了三种视图，用于以不同的角度审视<code>Map</code>中的数据。这些视图与<code>Map</code>本身是联动的。</p><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>Set&lt;K&gt; keySet()</code></td><td align="left">返回<code>Map</code>中所有**键（Key）**组成的一个<code>Set</code>集合。</td><td align="left">返回的是一个<strong>视图</strong>，不是副本。对这个<code>Set</code>进行移除操作会同步影响到原<code>Map</code>，但<strong>不支持添加操作</strong>。</td></tr><tr><td align="left"><code>Collection&lt;V&gt; values()</code></td><td align="left">返回<code>Map</code>中所有**值（Value）**组成的一个<code>Collection</code>。</td><td align="left">同样是视图。可以包含重复元素。对这个集合的修改同样会影响原<code>Map</code>。</td></tr><tr><td align="left"><code>Set&lt;Map.Entry&lt;K, V&gt;&gt; entrySet()</code></td><td align="left">返回<code>Map</code>中所有**键值对节点（<code>Map.Entry</code>）**组成的<code>Set</code>集合。</td><td align="left"><strong>最高效的遍历方式</strong>。<code>Map.Entry</code>对象提供了<code>getKey()</code>和<code>getValue()</code>方法。</td></tr></tbody></table><h6 id="3-状态查询"><a href="#3-状态查询" class="headerlink" title="3. 状态查询"></a><strong>3. 状态查询</strong></h6><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>int size()</code></td><td align="left">返回<code>Map</code>中键值对的数量。</td><td align="left">时间复杂度为O(1)。</td></tr><tr><td align="left"><code>boolean isEmpty()</code></td><td align="left">判断<code>Map</code>是否为空（即<code>size()</code>是否为0）。</td><td align="left">比 <code>size() == 0</code> 更具可读性。</td></tr><tr><td align="left"><code>void clear()</code></td><td align="left">清空<code>Map</code>中所有的键值对。</td><td align="left">调用后<code>size()</code>将变为0。</td></tr></tbody></table><h6 id="4-Java-8-增强方法"><a href="#4-Java-8-增强方法" class="headerlink" title="4. Java 8+ 增强方法"></a><strong>4. Java 8+ 增强方法</strong></h6><p>Java 8 引入了一系列函数式方法，极大地简化了代码。</p><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>V getOrDefault(Object key, V defaultValue)</code></td><td align="left"><strong>强烈推荐</strong>。获取值，若<code>key</code>不存在则返回一个指定的<code>defaultValue</code>。</td><td align="left">优雅地解决了<code>get()</code>可能返回<code>null</code>的问题，避免了<code>if (map.get(key) != null)</code>的样板代码。</td></tr><tr><td align="left"><code>V putIfAbsent(K key, V value)</code></td><td align="left">仅当<code>key</code>不存在或其值为<code>null</code>时，才存入该键值对。</td><td align="left">可用于实现缓存、单例初始化等原子性操作，避免覆盖已有值。</td></tr><tr><td align="left"><code>void forEach(BiConsumer&lt;? super K, ? super V&gt; action)</code></td><td align="left">使用Lambda表达式遍历<code>Map</code>的每个键值对。</td><td align="left">是目前最简洁、最推荐的遍历方式之一。</td></tr><tr><td align="left"><code>V compute(K key, BiFunction&lt;...&gt; remappingFunction)</code></td><td align="left">对指定<code>key</code>的值进行计算和更新，功能强大且原子。</td><td align="left">适用于需要先<code>get</code>、再计算、最后<code>put</code>的复杂更新场景。</td></tr><tr><td align="left"><code>V merge(K key, V value, BiFunction&lt;...&gt; remappingFunction)</code></td><td align="left">合并值。如果<code>key</code>不存在，存入<code>value</code>；如果存在，则用旧值和新<code>value</code>执行函数，并将结果存入。</td><td align="left">非常适合实现<strong>计数统计</strong>等聚合操作，比<code>getOrDefault</code>更强大。</td></tr><tr><td align="left"><code>V replace(K key, V value)</code></td><td align="left">仅当<code>key</code>存在时，才用新<code>value</code>替换旧值。</td><td align="left"></td></tr></tbody></table><h6 id="5-批量操作"><a href="#5-批量操作" class="headerlink" title="**5. 批量操作 **"></a>**5. 批量操作 **</h6><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th><th align="left">注意事项 / 最佳实践</th></tr></thead><tbody><tr><td align="left"><code>void putAll(Map&lt;? extends K, ? extends V&gt; m)</code></td><td align="left">将另一个<code>Map</code>中所有的键值对都复制到当前<code>Map</code>中。</td><td align="left">如果键冲突，会用新<code>Map</code>中的值覆盖当前<code>Map</code>中的值。</td></tr></tbody></table><hr><p><strong>总结与建议</strong>：</p><p>在日常开发中，应熟练掌握<strong>核心操作</strong>和<strong>视图操作</strong>。同时，强烈建议多利用<strong>Java 8+提供的新方法</strong>（如 <code>getOrDefault</code>, <code>putIfAbsent</code>, <code>merge</code>, <code>forEach</code> 等），它们能让您的代码变得更简洁、更安全、更具表现力。</p><h5 id="核心用途与场景-4"><a href="#核心用途与场景-4" class="headerlink" title="核心用途与场景"></a><strong>核心用途与场景</strong></h5><p><code>HashMap</code>是<code>Map</code>接口最通用的实现，是日常开发中使用频率最高的集合之一。它适用于任何需要通过一个唯一标识来存取、管理一系列数据的场景。</p><ul><li><strong>典型场景</strong>:<ul><li><strong>实现内存缓存</strong>：快速存取热点数据，减轻数据库压力。</li><li><strong>存储配置信息</strong>：加载应用的配置项，键为配置名，值为配置值。</li><li><strong>数据索引</strong>：将<code>List</code>中的数据按某个字段（如用户ID）转为<code>Map</code>，实现快速查找。</li><li><strong>计数统计</strong>：统计文本中的词频，或集合中各元素的出现次数。</li><li><strong>传递灵活参数</strong>：在方法间传递一组不固定的参数，类似一个动态对象。</li></ul></li></ul><hr><h6 id="场景一：实现内存缓存"><a href="#场景一：实现内存缓存" class="headerlink" title="场景一：实现内存缓存"></a><strong>场景一：实现内存缓存</strong></h6><blockquote><p><strong>目的</strong>：将耗时操作（如数据库查询、网络请求）的结果存储起来。当再次需要相同数据时，直接从内存中快速获取，避免重复执行耗时操作，从而提升系统性能。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> java.util.HashMap;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="keyword">import</span> java.util.concurrent.TimeUnit;</span><br><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 一个简单的内存缓存服务</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">SimpleCacheService</span> {</span><br><span class="line">    <span class="comment">// 使用HashMap作为缓存容器</span></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> Map&lt;String, Object&gt; cache = <span class="keyword">new</span> <span class="title class_">HashMap</span>&lt;&gt;();</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 根据key获取数据。如果缓存中有，则直接返回；如果没有，则模拟一次耗时操作后存入缓存再返回。</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> key 数据的唯一标识</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 获取到的数据</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="keyword">public</span> Object <span class="title function_">getData</span><span class="params">(String key)</span> {</span><br><span class="line">        <span class="comment">// 1. 先从缓存中查找</span></span><br><span class="line">        <span class="keyword">if</span> (cache.containsKey(key)) {</span><br><span class="line">            System.out.println(<span class="string">"成功从缓存中获取数据: "</span> + key);</span><br><span class="line">            <span class="keyword">return</span> cache.get(key);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. 缓存中没有，执行耗时操作（例如查询数据库）</span></span><br><span class="line">        System.out.println(<span class="string">"缓存未命中，开始执行数据库查询: "</span> + key);</span><br><span class="line">        <span class="type">Object</span> <span class="variable">data</span> <span class="operator">=</span> fetchDataFromDB(key);</span><br><span class="line">        </span><br><span class="line">        <span class="comment">// 3. 将结果存入缓存</span></span><br><span class="line">        cache.put(key, data);</span><br><span class="line">        System.out.println(<span class="string">"数据已存入缓存: "</span> + key);</span><br><span class="line"></span><br><span class="line">        <span class="keyword">return</span> data;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 模拟一个耗时的数据库查询</span></span><br><span class="line">    <span class="keyword">private</span> Object <span class="title function_">fetchDataFromDB</span><span class="params">(String key)</span> {</span><br><span class="line">        <span class="keyword">try</span> {</span><br><span class="line">            TimeUnit.SECONDS.sleep(<span class="number">1</span>); <span class="comment">// 模拟1秒的延迟</span></span><br><span class="line">        } <span class="keyword">catch</span> (InterruptedException e) {</span><br><span class="line">            Thread.currentThread().interrupt();</span><br><span class="line">        }</span><br><span class="line">        <span class="keyword">return</span> <span class="string">"Data for "</span> + key;</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">SimpleCacheService</span> <span class="variable">cacheService</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">SimpleCacheService</span>();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 第一次请求，会执行慢速操作</span></span><br><span class="line">        System.out.println(<span class="string">"第一次请求结果: "</span> + cacheService.getData(<span class="string">"user:1001"</span>));</span><br><span class="line">        System.out.println(<span class="string">"--------------------"</span>);</span><br><span class="line">        <span class="comment">// 第二次请求，应直接从缓存返回，速度很快</span></span><br><span class="line">        System.out.println(<span class="string">"第二次请求结果: "</span> + cacheService.getData(<span class="string">"user:1001"</span>));</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">/*</span></span><br><span class="line"><span class="comment">输出:</span></span><br><span class="line"><span class="comment">缓存未命中，开始执行数据库查询: user:1001</span></span><br><span class="line"><span class="comment">数据已存入缓存: user:1001</span></span><br><span class="line"><span class="comment">第一次请求结果: Data for user:1001</span></span><br><span class="line"><span class="comment">--------------------</span></span><br><span class="line"><span class="comment">成功从缓存中获取数据: user:1001</span></span><br><span class="line"><span class="comment">第二次请求结果: Data for user:1001</span></span><br><span class="line"><span class="comment">*/</span></span><br></pre></td></tr></tbody></table></figure><h6 id="场景二：存储配置信息"><a href="#场景二：存储配置信息" class="headerlink" title="场景二：存储配置信息"></a><strong>场景二：存储配置信息</strong></h6><blockquote><p><strong>目的</strong>：在程序启动时，将配置文件（如<code>.properties</code>或<code>YAML</code>）中的键值对加载到<code>Map</code>中，便于在程序运行期间随时、快速地获取配置项。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> java.util.HashMap;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 模拟一个应用配置管理器</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">ConfigManager</span> {</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> Map&lt;String, String&gt; configMap = <span class="keyword">new</span> <span class="title class_">HashMap</span>&lt;&gt;();</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">ConfigManager</span><span class="params">()</span> {</span><br><span class="line">        <span class="comment">// 在构造时模拟加载配置</span></span><br><span class="line">        loadConfigurations();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">void</span> <span class="title function_">loadConfigurations</span><span class="params">()</span> {</span><br><span class="line">        System.out.println(<span class="string">"正在加载应用配置..."</span>);</span><br><span class="line">        configMap.put(<span class="string">"app.name"</span>, <span class="string">"AwesomeApp"</span>);</span><br><span class="line">        configMap.put(<span class="string">"server.port"</span>, <span class="string">"8080"</span>);</span><br><span class="line">        configMap.put(<span class="string">"db.url"</span>, <span class="string">"********************************"</span>);</span><br><span class="line">        configMap.put(<span class="string">"api.timeout.ms"</span>, <span class="string">"5000"</span>);</span><br><span class="line">        System.out.println(<span class="string">"配置加载完成。"</span>);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">getConfig</span><span class="params">(String key)</span> {</span><br><span class="line">        <span class="keyword">return</span> configMap.get(key);</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">public</span> <span class="type">int</span> <span class="title function_">getIntConfig</span><span class="params">(String key, <span class="type">int</span> defaultValue)</span> {</span><br><span class="line">        <span class="type">String</span> <span class="variable">value</span> <span class="operator">=</span> configMap.get(key);</span><br><span class="line">        <span class="keyword">if</span> (value != <span class="literal">null</span>) {</span><br><span class="line">            <span class="keyword">try</span> {</span><br><span class="line">                <span class="keyword">return</span> Integer.parseInt(value);</span><br><span class="line">            } <span class="keyword">catch</span> (NumberFormatException e) {</span><br><span class="line">                <span class="keyword">return</span> defaultValue;</span><br><span class="line">            }</span><br><span class="line">        }</span><br><span class="line">        <span class="keyword">return</span> defaultValue;</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ConfigExample</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">ConfigManager</span> <span class="variable">config</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">ConfigManager</span>();</span><br><span class="line">        </span><br><span class="line">        <span class="type">String</span> <span class="variable">appName</span> <span class="operator">=</span> config.getConfig(<span class="string">"app.name"</span>);</span><br><span class="line">        <span class="type">int</span> <span class="variable">port</span> <span class="operator">=</span> config.getIntConfig(<span class="string">"server.port"</span>, <span class="number">9090</span>); <span class="comment">// 获取int类型配置</span></span><br><span class="line">        </span><br><span class="line">        System.out.println(<span class="string">"应用名称: "</span> + appName);</span><br><span class="line">        System.out.println(<span class="string">"服务器端口: "</span> + port);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">/*</span></span><br><span class="line"><span class="comment">输出:</span></span><br><span class="line"><span class="comment">正在加载应用配置...</span></span><br><span class="line"><span class="comment">配置加载完成。</span></span><br><span class="line"><span class="comment">应用名称: AwesomeApp</span></span><br><span class="line"><span class="comment">服务器端口: 8080</span></span><br><span class="line"><span class="comment">*/</span></span><br></pre></td></tr></tbody></table></figure><h6 id="场景三：将列表数据转换为索引"><a href="#场景三：将列表数据转换为索引" class="headerlink" title="场景三：将列表数据转换为索引"></a><strong>场景三：将列表数据转换为索引</strong></h6><blockquote><p><strong>目的</strong>：将一个对象列表（<code>List&lt;T&gt;</code>）转换为以对象的某个唯一标识（如ID）为键的<code>Map&lt;ID, T&gt;</code>，从而将原先O(n)的遍历查找操作，优化为O(1)的直接访问操作。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"><span class="keyword">import</span> lombok.*;</span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="keyword">import</span> java.util.function.Function;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Collectors;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line"></span><br><span class="line">        List&lt;Product&gt; productList = Arrays.asList(</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"P001"</span>, <span class="string">"Laptop"</span>),</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"P002"</span>, <span class="string">"Mouse"</span>),</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"P003"</span>, <span class="string">"Keyboard"</span>)</span><br><span class="line">        );</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 使用Java 8 Stream API，将List高效地转换为Map</span></span><br><span class="line">        Map&lt;String, Product&gt; productIndex = productList.stream()</span><br><span class="line">                .collect(Collectors.toMap(Product::getSku, Function.identity()));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 现在可以通过SKU直接获取产品对象，无需遍历List</span></span><br><span class="line">        <span class="type">Product</span> <span class="variable">mouse</span> <span class="operator">=</span> productIndex.get(<span class="string">"P002"</span>);</span><br><span class="line">        System.out.println(<span class="string">"通过SKU 'P002' 快速查找到的产品: "</span> + mouse);</span><br><span class="line"></span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="meta">@Data</span></span><br><span class="line"><span class="meta">@ToString</span></span><br><span class="line"><span class="meta">@AllArgsConstructor</span></span><br><span class="line"><span class="meta">@NoArgsConstructor(force = true)</span> <span class="comment">// 强制生成无参构造函数</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Product</span> {</span><br><span class="line">    <span class="keyword">private</span> String sku; <span class="comment">// 假设sku是必需的，但没有默认值</span></span><br><span class="line">    <span class="keyword">private</span> String name; <span class="comment">// name也是必需的，但没有默认值</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h6 id="场景四：计数统计"><a href="#场景四：计数统计" class="headerlink" title="场景四：计数统计"></a><strong>场景四：计数统计</strong></h6><blockquote><p><strong>目的</strong>：统计一个集合或文本中，每个独立元素出现的次数。<code>HashMap</code>是实现该功能的完美数据结构。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> java.util.HashMap;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">FrequencyCountExample</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        String[] words = {<span class="string">"Java"</span>, <span class="string">"Python"</span>, <span class="string">"Java"</span>, <span class="string">"Go"</span>, <span class="string">"Java"</span>, <span class="string">"Python"</span>, <span class="string">"Rust"</span>};</span><br><span class="line">        </span><br><span class="line">        Map&lt;String, Integer&gt; wordFrequency = <span class="keyword">new</span> <span class="title class_">HashMap</span>&lt;&gt;();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 遍历数组，进行词频统计</span></span><br><span class="line">        <span class="keyword">for</span> (String word : words) {</span><br><span class="line">            <span class="comment">// getOrDefault是Java 8+的优雅写法，避免了繁琐的null判断</span></span><br><span class="line">            <span class="comment">// 如果word已存在，获取其当前计数值；如果不存在，返回默认值0。</span></span><br><span class="line">            <span class="comment">// 然后将计数值+1，再存入Map。</span></span><br><span class="line">            wordFrequency.put(word, wordFrequency.getOrDefault(word, <span class="number">0</span>) + <span class="number">1</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"各单词出现频率:"</span>);</span><br><span class="line">        wordFrequency.forEach((word, count) -&gt; </span><br><span class="line">            System.out.println(<span class="string">"'"</span> + word + <span class="string">"': "</span> + count + <span class="string">"次"</span>)</span><br><span class="line">        );</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">/*</span></span><br><span class="line"><span class="comment">输出:</span></span><br><span class="line"><span class="comment">各单词出现频率:</span></span><br><span class="line"><span class="comment">'Java': 3次</span></span><br><span class="line"><span class="comment">'Python': 2次</span></span><br><span class="line"><span class="comment">'Rust': 1次</span></span><br><span class="line"><span class="comment">'Go': 1次</span></span><br><span class="line"><span class="comment">*/</span></span><br></pre></td></tr></tbody></table></figure><h5 id="TreeMap-简介"><a href="#TreeMap-简介" class="headerlink" title="TreeMap 简介"></a><strong><code>TreeMap</code> 简介</strong></h5><h6 id="核心用途与场景-5"><a href="#核心用途与场景-5" class="headerlink" title="核心用途与场景"></a><strong>核心用途与场景</strong></h6><p>当你需要一个键（Key）时刻<strong>保持排序状态</strong>的<code>Map</code>时，<code>TreeMap</code>是你的不二之选。</p><ul><li><strong>最佳场景</strong>：<ul><li>需要按键的自然顺序或自定义顺序遍历<code>Map</code>。</li><li>需要对<code>Map</code>的键进行范围查找，如“查找所有ID在100到200之间的用户”。</li></ul></li></ul><h6 id="类型介绍与排序原理-1"><a href="#类型介绍与排序原理-1" class="headerlink" title="类型介绍与排序原理"></a><strong>类型介绍与排序原理</strong></h6><p><code>TreeMap</code>底层基于<strong>红黑树</strong>实现。排序规则与<code>TreeSet</code>完全相同，依赖于键的<code>Comparable</code>接口（自然排序）或在构造时传入的<code>Comparator</code>（定制排序）。</p><h6 id="代码示例详解-4"><a href="#代码示例详解-4" class="headerlink" title="代码示例详解"></a><strong>代码示例详解</strong></h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"><span class="keyword">import</span> java.util.Comparator;</span><br><span class="line"><span class="keyword">import</span> java.util.TreeMap;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 创建时传入Comparator，使key（字符串）按长度排序</span></span><br><span class="line">        TreeMap&lt;String, String&gt; mapByLength = <span class="keyword">new</span> <span class="title class_">TreeMap</span>&lt;&gt;(Comparator.comparingInt(String::length));</span><br><span class="line">        mapByLength.put(<span class="string">"Java"</span>, <span class="string">"is a language"</span>);</span><br><span class="line">        mapByLength.put(<span class="string">"Go"</span>, <span class="string">"is fast"</span>);</span><br><span class="line">        mapByLength.put(<span class="string">"Python"</span>, <span class="string">"is simple"</span>);</span><br><span class="line">        <span class="comment">// 遍历时，输出会按key的长度排序</span></span><br><span class="line">        System.out.println(mapByLength); <span class="comment">// {Go=is fast, Java=is a language, Python=is simple}</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="面试题-HashMap-vs-Hashtable-vs-ConcurrentHashMap"><a href="#面试题-HashMap-vs-Hashtable-vs-ConcurrentHashMap" class="headerlink" title="[面试题] HashMap vs Hashtable vs ConcurrentHashMap"></a><strong>[面试题] <code>HashMap</code> vs <code>Hashtable</code> vs <code>ConcurrentHashMap</code></strong></h5><p>这是其余的两个Map与最常用的HashMap作为对比</p><table><thead><tr><th align="left">特性</th><th align="left"><code>HashMap</code></th><th align="left"><code>Hashtable</code> (已不推荐使用)</th><th align="left"><code>ConcurrentHashMap</code> (推荐)</th></tr></thead><tbody><tr><td align="left"><strong>线程安全</strong></td><td align="left"><strong>非线程安全</strong></td><td align="left"><strong>线程安全</strong> (对整个表加<code>synchronized</code>锁)</td><td align="left"><strong>线程安全</strong> (分段锁/CAS，性能远超Hashtable)</td></tr><tr><td align="left"><strong>null支持</strong></td><td align="left"><strong>允许</strong> key和value为<code>null</code></td><td align="left"><strong>不允许</strong> key和value为<code>null</code> (会抛<code>NullPointerException</code>)</td><td align="left"><strong>不允许</strong> key和value为<code>null</code></td></tr><tr><td align="left"><strong>性能</strong></td><td align="left">最高（单线程）</td><td align="left">最低（锁竞争激烈）</td><td align="left">高（并发环境）</td></tr><tr><td align="left"><strong>推荐用法</strong></td><td align="left">单线程环境下的首选。</td><td align="left"><strong>不推荐使用</strong>，是过时的历史遗留类。</td><td align="left"><strong>并发环境</strong>下的首选。</td></tr></tbody></table><hr><h3 id="2-2-运算符详解"><a href="#2-2-运算符详解" class="headerlink" title="2.2 运算符详解"></a><strong>2.2 运算符详解</strong></h3><p>本节将直接深入运算符的核心与难点，剔除基础部分。我们将聚焦于位运算符的强大能力、逻辑与自增/自减运算的常见陷阱、优先级的避坑指南，以及Java新版本带来的语法糖，但在这之前，要深入理解位运算符，我们首先需要回归计算机的“母语”——二进制，并掌握不同进制间的转换，以及计算机内部表示数字（尤其是负数）的精妙方式：补码。</p><h4 id="2-2-0-计算机基础：进制与编码"><a href="#2-2-0-计算机基础：进制与编码" class="headerlink" title="2.2.0 计算机基础：进制与编码"></a><strong>2.2.0 计算机基础：进制与编码</strong></h4><p>要深入理解位运算符，我们首先需要回归计算机的“母语”——二进制，并掌握不同进制间的转换，以及计算机内部表示数字（尤其是负数）的精妙方式：补码。</p><h5 id="常见进制介绍"><a href="#常见进制介绍" class="headerlink" title="常见进制介绍"></a>常见进制介绍</h5><p>在日常生活中我们使用十进制，但在计算机科学中，我们必须熟悉以下几种进制：</p><ul><li><strong>二进制 (Binary)</strong>：基数为2，由 <code>0</code> 和 <code>1</code> 组成。是计算机物理层面的通用语言。在Java中以 <code>0b</code> 或 <code>0B</code> 开头，如 <code>0b1011</code>。</li><li><strong>八进制 (Octal)</strong>：基数为8，由 <code>0-7</code> 组成。在Java中以 <code>0</code> 开头，如 <code>055</code>。</li><li><strong>十进制 (Decimal)</strong>：基数为10，由 <code>0-9</code> 组成。我们最熟悉的进制。</li><li><strong>十六进制 (Hexadecimal)</strong>：基数为16，由 <code>0-9</code> 和 <code>A-F</code> (代表10-15) 组成。常用于表示内存地址、颜色值等。在Java中以 <code>0x</code> 或 <code>0X</code> 开头，如 <code>0x2D</code>。</li></ul><h5 id="进制转换核心方法"><a href="#进制转换核心方法" class="headerlink" title="进制转换核心方法"></a><strong>进制转换核心方法</strong></h5><h6 id="十进制转二进制（除2取余法）"><a href="#十进制转二进制（除2取余法）" class="headerlink" title="十进制转二进制（除2取余法）"></a><strong>十进制转二进制（除2取余法）</strong></h6><ul><li><p><strong>手算方法</strong>：将十进制数连续除以2，直到商为0，然后将每步得到的余数<strong>逆序</strong>排列。</p></li><li><p><strong>手算示例</strong>：将十进制 <strong>45</strong> 转换为二进制。</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">45 ÷ 2 = 22 ... 余 1</span><br><span class="line">22 ÷ 2 = 11 ... 余 0</span><br><span class="line">11 ÷ 2 = 5  ... 余 1</span><br><span class="line">5  ÷ 2 = 2  ... 余 1</span><br><span class="line">2  ÷ 2 = 1  ... 余 0</span><br><span class="line">1  ÷ 2 = 0  ... 余 1</span><br></pre></td></tr></tbody></table></figure><p><strong>结果</strong>：从下往上倒序取余数，得到 <code>101101</code>。</p></li></ul><hr><h6 id="二进制转十进制（按权展开法）"><a href="#二进制转十进制（按权展开法）" class="headerlink" title="二进制转十进制（按权展开法）"></a><strong>二进制转十进制（按权展开法）</strong></h6><p><strong>手算方法</strong>：<strong>权重法</strong>（8421 法则）</p><p>从右到左写出每位的权重：$2^0, 2^1, 2^2, \dots$</p><p>取出二进制中为 <code>1</code> 的权重，累加即可。</p><p><strong>手算示例</strong>： 将 <strong>101101</strong> 转换为十进制：</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"> 32   16    8    4    2    1   (权重)</span><br><span class="line">  1    0    1    1    0    1</span><br><span class="line">32 + 8 + 4 + 1 = 45</span><br></pre></td></tr></tbody></table></figure><h4 id="2-2-3-计算机基础-原码、反码、补码"><a href="#2-2-3-计算机基础-原码、反码、补码" class="headerlink" title="2.2.3 [计算机基础] 原码、反码、补码"></a><strong>2.2.3 [计算机基础] 原码、反码、补码</strong></h4><h5 id="为何需要补码？"><a href="#为何需要补码？" class="headerlink" title="为何需要补码？"></a><strong>为何需要补码？</strong></h5><p>计算机硬件层面，只有加法器。为了简化电路设计，希望将减法运算也统一为加法运算。例如 <code>5 - 3</code> 希望能变成 <code>5 + (-3)</code>。原码无法满足这个要求，而<strong>补码</strong>巧妙地解决了这个问题，并统一了<code>+0</code>和<code>-0</code>的表示。</p><h5 id="正数与负数的编码表示"><a href="#正数与负数的编码表示" class="headerlink" title="正数与负数的编码表示"></a><strong>正数与负数的编码表示</strong></h5><ul><li><p><strong>正数</strong>：<strong>原码、反码、补码都相同</strong>。</p></li><li><p><strong>负数</strong>：</p><ul><li>**原码 **：最高位为符号位（1代表负），其余位是其绝对值的二进制。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250708201248831.png" alt="image-20250708201248831"></p><p>例如我们之前计算的**<code>45</code><strong>的源码就为<code>00101101</code>,不足八位则不足，若是</strong><code>-45</code>**则为<code>10101101</code></p><ul><li><strong>反码：在原码的基础上</strong>若为正数，则反码不变，若为负数，则符号位不变，其他全部取反（0变1）</li></ul><p>例如<code>-45</code>的反码就是<code>01010010</code></p><ul><li>**补码 ：在反码的基础上，**末位加1。</li></ul><p>例如<code>-45</code>的补码就是<code>01010011</code></p></li></ul><p><strong>计算机内存中，所有整数都以补码的形式存储。</strong></p><h5 id="补码的计算过程"><a href="#补码的计算过程" class="headerlink" title="补码的计算过程"></a><strong>补码的计算过程</strong></h5><ul><li><strong>示例</strong>：计算 <code>-5</code> 在一个 <code>byte</code>（8位）中的补码。<ol><li>先求 <code>+5</code> 的原码：<code>0000 0101</code></li><li>求 <code>-5</code> 的原码（符号位变1）：<code>1000 0101</code></li><li>求 <code>-5</code> 的反码（符号位不变，其余取反）：<code>1111 1010</code></li><li>求 <code>-5</code> 的补码（反码加1）：<code>1111 1011</code><br>所以，<code>-5</code> 在内存中存储的就是 <code>1111 1011</code>。</li></ol></li></ul><h4 id="2-2-1-面试高频-位运算符深度剖析"><a href="#2-2-1-面试高频-位运算符深度剖析" class="headerlink" title="2.2.1 [面试高频] 位运算符深度剖析"></a><strong>2.2.1 [面试高频] 位运算符深度剖析</strong></h4><p>位运算符直接在整数的二进制位（bit）上进行操作，不关心其十进制值。它们之所以在面试和高性能场景中备受青睐，主要源于两大优势：<strong>极致的运行效率</strong>（因为更接近硬件操作）和<strong>高效的空间利用</strong>（例如用一个<code>int</code>存储32个开关状态），现代JVM的JIT（即时编译器）已经非常智能，可能会自动将 <code>x * 2</code> 这样的代码优化为 <code>x &lt;&lt; 1</code>。但在一些对性能要求极为苛刻的场景，或者在阅读一些经典框架（如<code>ArrayList</code>、<code>HashMap</code>）的源码时，你会发现它们的身影</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// ArrayList.java 源码片段</span></span><br><span class="line"><span class="type">int</span> <span class="variable">oldCapacity</span> <span class="operator">=</span> ...;</span><br><span class="line"><span class="type">int</span> <span class="variable">newCapacity</span> <span class="operator">=</span> oldCapacity + (oldCapacity &gt;&gt; <span class="number">1</span>); <span class="comment">// 这里的 oldCapacity &gt;&gt; 1 就是 oldCapacity / 2</span></span><br></pre></td></tr></tbody></table></figure><h5 id="核心对比：-算术右移-vs-逻辑右移"><a href="#核心对比：-算术右移-vs-逻辑右移" class="headerlink" title="核心对比：>> (算术右移) vs. >>> (逻辑右移)"></a><strong>核心对比：<code>&gt;&gt;</code> (算术右移) vs. <code>&gt;&gt;&gt;</code> (逻辑右移)</strong></h5><p>这个区别是面试中的经典考点，它<strong>仅在处理负数时有所不同</strong>。</p><ul><li><strong><code>&gt;&gt;</code> (带符号右移 / 算术右移)</strong>：进行右移操作时，空出的高位会用原始数字的<strong>符号位</strong>来填充。如果原数是正数（符号位为0），则高位补0；如果原数是负数（符号位为1），则高位补1。这样做的目的是<strong>保持数字的正负性质不变</strong>。</li><li><strong><code>&gt;&gt;&gt;</code> (无符号右移 / 逻辑右移)</strong>：进行右移操作时，无论原始数字是正数还是负数，空出的高位<strong>一律用0填充</strong>。这意味着，对一个负数进行无符号右移后，其结果会变成一个非常大的正数。</li></ul><h5 id="代码示例：对比-和"><a href="#代码示例：对比-和" class="headerlink" title="代码示例：对比 >> 和 >>>"></a><strong>代码示例：对比 <code>&gt;&gt;</code> 和 <code>&gt;&gt;&gt;</code></strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">int</span> <span class="variable">negativeNum</span> <span class="operator">=</span> -<span class="number">8</span>; <span class="comment">// 32位二进制补码: 11111111 11111111 11111111 11111000</span></span><br><span class="line">        <span class="comment">// 调用 formatBinary 方法格式化二进制字符串</span></span><br><span class="line">        <span class="type">String</span> <span class="variable">formattedBinary</span> <span class="operator">=</span> formatBinary(Integer.toBinaryString(negativeNum));</span><br><span class="line">        System.out.println(<span class="string">"负数 -8 的二进制: "</span> + formattedBinary);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 带符号右移，高位补1，保持负数性质</span></span><br><span class="line">        <span class="type">int</span> <span class="variable">signedShiftResult</span> <span class="operator">=</span> negativeNum &gt;&gt; <span class="number">2</span>;</span><br><span class="line">        System.out.println(<span class="string">"负数 -8 &gt;&gt; 2: "</span> + formatBinary(Integer.toBinaryString(signedShiftResult))); <span class="comment">// 结果得到: -2</span></span><br><span class="line">        <span class="comment">// -2 的32位二进制补码: 11111111 11111111 11111111 11111110</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 无符号右移, 高位补0</span></span><br><span class="line">        <span class="type">int</span> <span class="variable">unsignedShiftResult</span> <span class="operator">=</span> negativeNum &gt;&gt;&gt; <span class="number">2</span>;</span><br><span class="line">        System.out.println(<span class="string">"负数 -8 &gt;&gt;&gt; 2: "</span> + formatBinary(Integer.toBinaryString(unsignedShiftResult))); <span class="comment">// 结果得到: 1073741822</span></span><br><span class="line">        System.out.println(unsignedShiftResult);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 将二进制字符串按每 8 位分割，并用空格连接</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> binary 原始二进制字符串</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 格式化后的二进制字符串</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">static</span> String <span class="title function_">formatBinary</span><span class="params">(String binary)</span> {</span><br><span class="line">        <span class="type">StringBuilder</span> <span class="variable">sb</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">StringBuilder</span>();</span><br><span class="line">        <span class="comment">// 从字符串末尾开始，每 8 位添加一个空格</span></span><br><span class="line">        <span class="keyword">for</span> (<span class="type">int</span> <span class="variable">i</span> <span class="operator">=</span> binary.length(); i &gt; <span class="number">0</span>; i -= <span class="number">8</span>) {</span><br><span class="line">            sb.insert(<span class="number">0</span>, binary.substring(Math.max(i - <span class="number">8</span>, <span class="number">0</span>), i));</span><br><span class="line">            <span class="keyword">if</span> (i &gt; <span class="number">8</span>) {</span><br><span class="line">                sb.insert(<span class="number">0</span>, <span class="string">" "</span>);</span><br><span class="line">            }</span><br><span class="line">        }</span><br><span class="line">        <span class="keyword">return</span> sb.toString();</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><hr><p><strong>第一步：确定-8的32位二进制补码表示</strong></p><p>计算机不会直接处理 <code>-8</code> 这个符号，而是处理它的二进制补码。</p><ol><li><p><strong>求 <code>+8</code> 的原码</strong>：<br>在一个32位的<code>int</code>中，<code>+8</code>的二进制表示非常简单：<br><code>00000000 00000000 00000000 00001000</code></p></li><li><p><strong>求 <code>-8</code> 的反码</strong>：<br>在<code>+8</code>原码的基础上，符号位（最高位）变为<code>1</code>，其余位按位取反（0变1，1变0）。<br><code>11111111 11111111 11111111 11110111</code></p></li><li><p><strong>求 <code>-8</code> 的补码</strong>：<br>将反码加1，就得到了 <code>-8</code> 在内存中实际存储的形式。<br><code>11111111 11111111 11111111 11111000</code><br>这就是我们操作的起始状态。</p></li></ol><p><strong>第二步：执行带符号右移 <code>&gt;&gt; 2</code></strong></p><p>操作 <code>negativeNum &gt;&gt; 2</code> 意味着将 <code>-8</code> 的补码向右移动两位。</p><ul><li><p><strong>原始补码</strong>:<br><code>11111111 11111111 11111111 11111000</code></p></li><li><p><strong>向右移动两位</strong>:<br>所有的32位都向右平移2个位置，最右边的两位 <code>00</code> 被丢弃。左边空出了两个位置。</p><p><code>??111111 11111111 11111111 11111111 111110</code></p></li><li><p><strong>填充高位</strong>:<br>因为是 <code>&gt;&gt;</code> (带符号右移)，所以空出的高位会用<strong>原始的符号位</strong>来填充。<code>-8</code> 的符号位是 <code>1</code>，所以用 <code>1</code> 来填充。</p><p><code>11111111 11111111 11111111 11111110</code></p></li></ul><p>现在，我们就得到了右移操作后的二进制结果。</p><p><strong>第三步：将结果转换回十进制</strong></p><p>我们需要将这个新的补码 <code>11111111 11111111 11111111 11111110</code> 转换回我们能理解的十进制数，以验证它就是 <code>-2</code>。</p><ol><li><strong>观察符号位</strong>：<br>最高位是 <code>1</code>，说明这是一个负数。</li><li><strong>求其反码</strong>（补码减1）：<br><code>11111111 11111111 11111111 11111101</code></li><li><strong>求其原码</strong>（符号位不变，其余位取反）：<br><code>10000000 00000000 00000000 00000010</code></li><li><strong>读取数值</strong>：<br>这个原码表示的数值就是 <code>-2</code>。</li></ol><hr><h5 id="实战场景与代码详解"><a href="#实战场景与代码详解" class="headerlink" title="实战场景与代码详解"></a><strong>实战场景与代码详解</strong></h5><h6 id="场景一：高效运算"><a href="#场景一：高效运算" class="headerlink" title="场景一：高效运算"></a><strong>场景一：高效运算</strong></h6><ul><li><p><strong>判断奇偶数</strong>：<code>n &amp; 1</code>比<code>n % 2</code>效率更高。因为任何整数的二进制表示中，最低位是1则为奇数，是0则为偶数。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// num &amp; 1 用于检查 num 的二进制最低位是否为 1，若不为1则返回0</span></span><br><span class="line">        <span class="type">int</span> <span class="variable">num1</span> <span class="operator">=</span> <span class="number">100</span>; <span class="comment">// 偶数</span></span><br><span class="line">        <span class="keyword">if</span> ((num1 &amp; <span class="number">1</span>) == <span class="number">0</span>) {</span><br><span class="line">            System.out.println(num1 + <span class="string">" 是偶数。"</span>);</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>代替乘除2的幂运算</strong>：<code>n &lt;&lt; x</code> 相当于 <code>n * 2^x</code>，<code>n &gt;&gt; x</code> 相当于 <code>n / 2^x</code>。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">int</span> <span class="variable">num</span> <span class="operator">=</span> <span class="number">10</span>;</span><br><span class="line">        <span class="comment">// 10 * 8 (2^3)</span></span><br><span class="line">        <span class="type">int</span> <span class="variable">multiplied</span> <span class="operator">=</span> num &lt;&lt; <span class="number">3</span>;</span><br><span class="line">        System.out.println(<span class="string">"10 * 8 = "</span> + multiplied); <span class="comment">// 输出: 80</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="2-2-2-避坑指南-逻辑与自增-自减运算符陷阱"><a href="#2-2-2-避坑指南-逻辑与自增-自减运算符陷阱" class="headerlink" title="2.2.2 [避坑指南] 逻辑与自增/自减运算符陷阱"></a><strong>2.2.2 [避坑指南] 逻辑与自增/自减运算符陷阱</strong></h4><h5 id="短路逻辑-和"><a href="#短路逻辑-和" class="headerlink" title="短路逻辑 (&amp;&amp; 和 ||)"></a><strong>短路逻辑 (<code>&amp;&amp;</code> 和 <code>||</code>)</strong></h5><p><code>&amp;&amp;</code> (与) 和 <code>||</code> (或) 具有短路特性，这是面试和日常编码中必须注意的细节。</p><ul><li><strong><code>&amp;&amp;</code> (短路与)</strong>：如果第一个操作数为<code>false</code>，则<strong>不会再执行</strong>第二个操作数，直接判定整个表达式为<code>false</code>。</li><li><strong><code>||</code> (短路或)</strong>：如果第一个操作数为<code>true</code>，则<strong>不会再执行</strong>第二个操作数，直接判定整个表达式为<code>true</code>。</li></ul><p><code>&amp;</code> 和 <code>|</code> 也可以用作逻辑运算符，但它们<strong>不具备短路特性</strong>，会执行所有操作数。</p><hr><h5 id="i-vs-i"><a href="#i-vs-i" class="headerlink" title="i++ vs. ++i"></a><strong><code>i++</code> vs. <code>++i</code></strong></h5><ul><li><code>++i</code> (前自增)：<strong>先自增，后取值</strong>。表达式返回的是<code>i</code>加1之后的值。</li><li><code>i++</code> (后自增)：<strong>先取值，后自增</strong>。表达式返回的是<code>i</code>加1之前的原始值。</li></ul><h6 id="经典面试题：i-i"><a href="#经典面试题：i-i" class="headerlink" title="经典面试题：i = i++"></a><strong>经典面试题：<code>i = i++</code></strong></h6><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">PostIncrementPuzzle</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">int</span> <span class="variable">i</span> <span class="operator">=</span> <span class="number">0</span>;</span><br><span class="line">        i = i++;</span><br><span class="line">        System.out.println(<span class="string">"i 的最终值是: "</span> + i);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><ul><li><strong>结果与原理解析</strong>：<blockquote><p>输出结果是 <strong>0</strong>。<br><strong>JVM底层执行步骤</strong>：</p><ol><li>JVM将 <code>i</code> 的当前值（0）加载到一个临时变量区，我们称之为 <code>temp</code>。(<code>temp = 0</code>)</li><li><code>i</code> 自身的值加1，此时 <code>i</code> 变量变为1。</li><li><code>i++</code> 这个表达式返回的是<strong>加1前</strong>的原始值，即 <code>temp</code> 的值（0）。</li><li>执行赋值操作 <code>i = ...</code>，将表达式的返回值（0）赋给 <code>i</code>。<br>最终，<code>i</code> 的值被重新覆盖为了0。</li></ol></blockquote></li></ul><h4 id="2-2-3-运算符优先级与核心建议"><a href="#2-2-3-运算符优先级与核心建议" class="headerlink" title="2.2.3 运算符优先级与核心建议"></a><strong>2.2.3 运算符优先级与核心建议</strong></h4><p>完全记住运算符优先级表是困难且不切实际的。我们仅需关注几个易错点，并养成一个好习惯。</p><ul><li><strong>易错点1</strong>：位运算符的优先级低于关系运算符。如 <code>(permissions &amp; MASK) == MASK</code>，括号必不可少。</li><li><strong>易错点2</strong>：<code>&amp;&amp;</code> 的优先级高于 <code>||</code>。如 <code>a || b &amp;&amp; c</code> 等价于 <code>a || (b &amp;&amp; c)</code>。</li></ul><h5 id="核心开发建议：不要依赖隐式优先级"><a href="#核心开发建议：不要依赖隐式优先级" class="headerlink" title="核心开发建议：不要依赖隐式优先级"></a><strong>核心开发建议：不要依赖隐式优先级</strong></h5><blockquote><p><strong>代码首先是写给人看的，其次才是给机器执行的。</strong></p></blockquote><p>在任何可能产生歧义的复杂表达式中，请毫不犹豫地使用圆括号 <code>()</code> 来明确指定运算顺序。这不仅能100%避免由优先级问题导致的、难以察V觉的BUG，更能极大地提升代码的可读性和可维护性。</p><hr><h3 id="2-3-深度-循环与异常处理进阶"><a href="#2-3-深度-循环与异常处理进阶" class="headerlink" title="2.3 [深度] 循环与异常处理进阶"></a><strong>2.3 [深度] 循环与异常处理进阶</strong></h3><p>本章将绕开基础的<code>for</code>/<code>while</code>循环和<code>if</code>判断的语法，直接深入探讨程序流程控制的“内功”——循环的底层机制、现代化的语法演进，以及构建健壮、可靠程序的基石：Java的异常处理框架。</p><h4 id="2-3-1-深度-for-each循环与Iterator迭代器原理"><a href="#2-3-1-深度-for-each循环与Iterator迭代器原理" class="headerlink" title="2.3.1 [深度] for-each循环与Iterator迭代器原理"></a><strong>2.3.1 [深度] <code>for-each</code>循环与<code>Iterator</code>迭代器原理</strong></h4><p><code>for-each</code>循环（或称增强型for循环）是Java 5引入的语法糖，它极大地简化了对数组和集合的遍历。但要真正掌握它，必须理解其背后的<code>Iterator</code>机制。</p><h5 id="for-each的底层真相"><a href="#for-each的底层真相" class="headerlink" title="for-each的底层真相"></a><strong><code>for-each</code>的底层真相</strong></h5><p><code>for-each</code>循环并非一种新的循环结构，而是编译器为我们提供的便利。编译器在处理<code>for-each</code>循环时，会将其转换为不同的遍历方式：</p><ul><li><strong>对于数组</strong>：它会被转换为一个传统的、带索引的<code>for</code>循环。</li><li><strong>对于集合</strong>：它会被转换为使用<code>Iterator</code>迭代器的<code>while</code>循环。这是理解所有相关问题的关键。</li></ul><h5 id="代码示例：for-each的编译后等价代码"><a href="#代码示例：for-each的编译后等价代码" class="headerlink" title="代码示例：for-each的编译后等价代码"></a><strong>代码示例：<code>for-each</code>的编译后等价代码</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.ArrayList;</span><br><span class="line"><span class="keyword">import</span> java.util.Iterator;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        List&lt;String&gt; list = <span class="keyword">new</span> <span class="title class_">ArrayList</span>&lt;&gt;();</span><br><span class="line">        list.add(<span class="string">"A"</span>);</span><br><span class="line">        list.add(<span class="string">"B"</span>);</span><br><span class="line">        list.add(<span class="string">"C"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 我们写的 for-each 循环</span></span><br><span class="line">        System.out.println(<span class="string">"--- for-each 循环 ---"</span>);</span><br><span class="line">        <span class="keyword">for</span> (String item : list) {</span><br><span class="line">            System.out.println(item);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 编译器实际生成的代码（等价形式）</span></span><br><span class="line">        System.out.println(<span class="string">"\n--- 编译器生成的等价代码 ---"</span>);</span><br><span class="line">        Iterator&lt;String&gt; iterator = list.iterator();</span><br><span class="line">        <span class="keyword">while</span> (iterator.hasNext()) {</span><br><span class="line">            <span class="type">String</span> <span class="variable">item</span> <span class="operator">=</span> iterator.next();</span><br><span class="line">            System.out.println(item);</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="面试高频-Iterator-与-Fail-Fast-机制"><a href="#面试高频-Iterator-与-Fail-Fast-机制" class="headerlink" title="[面试高频] Iterator 与 Fail-Fast 机制"></a><strong>[面试高频] <code>Iterator</code> 与 <code>Fail-Fast</code> 机制</strong></h5><ul><li><strong><code>Iterator</code>接口</strong>: 提供了统一的遍历集合的方式，其核心方法为<code>hasNext()</code>（检查是否有下一个元素）、<code>next()</code>（获取下一个元素并后移指针）和<code>remove()</code>（从集合中删除<code>next()</code>方法最后返回的那个元素）。</li><li><strong><code>Fail-Fast</code>（快速失败）机制</strong>: 这是<code>ArrayList</code>等非并发集合的一个重要特性。在集合内部，有一个名为<code>modCount</code>的变量，记录着集合结构被修改（如add、remove）的次数。当创建<code>Iterator</code>时，迭代器会记下当时的<code>modCount</code>值。在迭代过程中，每次调用<code>iterator.next()</code>时，都会检查迭代器的记录值与集合当前的<code>modCount</code>是否一致。<strong>如果不一致，说明在迭代期间，集合被外部（非迭代器自身）修改了，迭代器会立刻抛出<code>ConcurrentModificationException</code></strong>，以避免在数据不一致的状态下继续操作，这就是“快速失败”。</li></ul><h5 id="代码示例：触发ConcurrentModificationException与正确删除"><a href="#代码示例：触发ConcurrentModificationException与正确删除" class="headerlink" title="代码示例：触发ConcurrentModificationException与正确删除"></a><strong>代码示例：触发<code>ConcurrentModificationException</code>与正确删除</strong></h5><p>在任何情况下，都<strong>绝对不要</strong>在<code>for-each</code>循环中直接调用集合的<code>remove()</code>或<code>add()</code>方法。这是非常危险且不可靠的编码方式。<strong>唯一正确且安全的方式是使用<code>Iterator</code>的<code>remove()</code>方法</strong>。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.ArrayList;</span><br><span class="line"><span class="keyword">import</span> java.util.Iterator;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        List&lt;String&gt; list = <span class="keyword">new</span> <span class="title class_">ArrayList</span>&lt;&gt;();</span><br><span class="line">        list.add(<span class="string">"A"</span>);</span><br><span class="line">        list.add(<span class="string">"B"</span>);</span><br><span class="line">        list.add(<span class="string">"C"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 错误的删除方式：在for-each循环中直接修改集合</span></span><br><span class="line">        <span class="keyword">try</span> {</span><br><span class="line">            <span class="keyword">for</span> (String item : list) {</span><br><span class="line">                <span class="keyword">if</span> (<span class="string">"A"</span>.equals(item)) {</span><br><span class="line">                    list.remove(item); <span class="comment">// 这会改变list的modCount，导致异常</span></span><br><span class="line">                }</span><br><span class="line">            }</span><br><span class="line">        } <span class="keyword">catch</span> (java.util.ConcurrentModificationException e) {</span><br><span class="line">            System.out.println(<span class="string">"错误演示：触发了 ConcurrentModificationException！"</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 正确的删除方式：使用迭代器自身的remove()方法</span></span><br><span class="line">        Iterator&lt;String&gt; iterator = list.iterator();</span><br><span class="line">        <span class="keyword">while</span> (iterator.hasNext()) {</span><br><span class="line">            <span class="type">String</span> <span class="variable">item</span> <span class="operator">=</span> iterator.next();</span><br><span class="line">            <span class="keyword">if</span> (<span class="string">"A"</span>.equals(item)) {</span><br><span class="line">                iterator.remove(); <span class="comment">// 这是唯一安全的方式</span></span><br><span class="line">            }</span><br><span class="line">        }</span><br><span class="line">        System.out.println(<span class="string">"正确删除后的列表: "</span> + list); <span class="comment">// 输出：[B,C]</span></span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="2-3-2-进阶-带标签的-break-和-continue"><a href="#2-3-2-进阶-带标签的-break-和-continue" class="headerlink" title="2.3.2 [进阶] 带标签的 break 和 continue"></a><strong>2.3.2 [进阶] 带标签的 <code>break</code> 和 <code>continue</code></strong></h4><p>这是一个不常用但非常有用的语法，它解决了如何从<strong>内层循环直接跳出外层循环</strong>的问题。</p><h5 id="代码示例：在二维数组中查找并跳出所有循环"><a href="#代码示例：在二维数组中查找并跳出所有循环" class="headerlink" title="代码示例：在二维数组中查找并跳出所有循环"></a><strong>代码示例：在二维数组中查找并跳出所有循环</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">int</span>[][] matrix = { {<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>}, {<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>}, {<span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>} };</span><br><span class="line">        <span class="type">int</span> <span class="variable">target</span> <span class="operator">=</span> <span class="number">6</span>;</span><br><span class="line">        <span class="type">boolean</span> <span class="variable">found</span> <span class="operator">=</span> <span class="literal">false</span>;</span><br><span class="line"></span><br><span class="line">        <span class="comment">// outerLoop就是一个标签</span></span><br><span class="line">        outerLoop: </span><br><span class="line">        <span class="keyword">for</span> (<span class="type">int</span> <span class="variable">i</span> <span class="operator">=</span> <span class="number">0</span>; i &lt; matrix.length; i++) {</span><br><span class="line">            <span class="keyword">for</span> (<span class="type">int</span> <span class="variable">j</span> <span class="operator">=</span> <span class="number">0</span>; j &lt; matrix[i].length; j++) {</span><br><span class="line">                <span class="keyword">if</span> (matrix[i][j] == target) {</span><br><span class="line">                    System.out.println(<span class="string">"找到了目标 "</span> + target + <span class="string">" 在位置 ("</span> + i + <span class="string">", "</span> + j + <span class="string">")"</span>);</span><br><span class="line">                    found = <span class="literal">true</span>;</span><br><span class="line">                    <span class="comment">// 使用带标签的break，直接跳出名为outerLoop的外层循环</span></span><br><span class="line">                    <span class="keyword">break</span> outerLoop; </span><br><span class="line">                }</span><br><span class="line">            }</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (!found) {</span><br><span class="line">            System.out.println(<span class="string">"未找到目标 "</span> + target);</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h4 id="2-3-3-核心-Java异常处理机制"><a href="#2-3-3-核心-Java异常处理机制" class="headerlink" title="2.3.3 [核心] Java异常处理机制"></a><strong>2.3.3 [核心] Java异常处理机制</strong></h4><h5 id="面试题引入"><a href="#面试题引入" class="headerlink" title="面试题引入"></a><strong>面试题引入</strong></h5><blockquote><p>“谈谈你对Java异常体系的理解。<code>Error</code>和<code>Exception</code>有什么区别？<code>Checked Exception</code>和<code>Unchecked Exception</code>呢？”</p></blockquote><h5 id="底层-Throwable-家族：异常体系结构"><a href="#底层-Throwable-家族：异常体系结构" class="headerlink" title="[底层] Throwable 家族：异常体系结构"></a><strong>[底层] <code>Throwable</code> 家族：异常体系结构</strong></h5><p>Java中所有可抛出的东西都继承自<code>Throwable</code>类，它有两个重要的子类：<code>Error</code>和<code>Exception</code>。</p><ul><li><strong><code>Error</code></strong>：代表了JVM本身无法恢复的严重内部错误，如<code>StackOverflowError</code>（栈溢出）、<code>OutOfMemoryError</code>（内存耗尽）。应用程序<strong>不应该也无法</strong>捕获或处理这类错误。</li><li><strong><code>Exception</code></strong>：代表了应用程序层面可以处理的异常情况。它又分为两大类：<ul><li><strong><code>Checked Exception</code> (受检异常)</strong>：继承自<code>Exception</code>但非<code>RuntimeException</code>的异常。编译器会<strong>强制</strong>开发者处理它们，必须使用<code>try-catch</code>捕获或在方法签名上用<code>throws</code>声明。它们通常代表了可预见的、可恢复的外部问题，如<code>IOException</code>、<code>SQLException</code>。</li><li><strong><code>Unchecked Exception</code> (非受检异常)</strong>：即<code>RuntimeException</code>及其所有子类。编译器<strong>不强制</strong>处理它们。它们通常是由程序自身的逻辑错误（BUG）引起的，如<code>NullPointerException</code>、<code>IllegalArgumentException</code>、<code>ArrayIndexOutOfBoundsException</code>。</li></ul></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i-blog.csdnimg.cn/blog_migrate/14c709e083d859cca243d8a1052ad1c0.png" alt="img"></p><h5 id="面试高频-try-catch-finally-的执行内幕"><a href="#面试高频-try-catch-finally-的执行内幕" class="headerlink" title="[面试高频] try-catch-finally 的执行内幕"></a><strong>[面试高频] <code>try-catch-finally</code> 的执行内幕</strong></h5><ul><li><p><strong>面试题 1</strong>：“<code>finally</code>块一定会执行吗？”</p><blockquote><p><strong>答</strong>：绝大多数情况下是的。<code>finally</code>块的设立目的就是为了保证无论是否发生异常，某些清理代码（如关闭资源）都能得到执行。只有两种极端情况<code>finally</code>不会执行：</p><ol><li>在<code>try</code>或<code>catch</code>块中调用了<code>System.exit()</code>；</li><li><ol start="2"><li>JVM崩溃或线程被强制杀死。</li></ol></li></ol></blockquote></li><li><p><strong>面试题 2</strong>：“如果<code>catch</code>块中有<code>return</code>语句，<code>finally</code>块会执行吗？”</p><blockquote><p><strong>答</strong>：<strong>会执行</strong>。执行流程是：先执行<code>catch</code>块中的代码，当遇到<code>return</code>时，会先将要返回的值保存起来，然后去执行<code>finally</code>块，<code>finally</code>块执行完毕后，方法再带着之前保存的值返回。</p></blockquote></li></ul><h4 id="2-3-4-Java-7-try-with-resources-最佳实践"><a href="#2-3-4-Java-7-try-with-resources-最佳实践" class="headerlink" title="2.3.4 [Java 7+] try-with-resources 最佳实践"></a><strong>2.3.4 [Java 7+] <code>try-with-resources</code> 最佳实践</strong></h4><h5 id="核心用途与场景-6"><a href="#核心用途与场景-6" class="headerlink" title="核心用途与场景"></a><strong>核心用途与场景</strong></h5><p>用于自动管理和关闭实现了<code>java.lang.AutoCloseable</code>或<code>java.io.Closeable</code>接口的资源，如文件流、数据库连接等，以防止资源泄漏。</p><h5 id="代码示例：优雅地关闭资源"><a href="#代码示例：优雅地关闭资源" class="headerlink" title="代码示例：优雅地关闭资源"></a><strong>代码示例：优雅地关闭资源</strong></h5><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.BufferedReader;</span><br><span class="line"><span class="keyword">import</span> java.io.FileReader;</span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// Java 7 之前的写法，需要在finally中手动关闭，繁琐且容易出错</span></span><br><span class="line">        <span class="type">BufferedReader</span> <span class="variable">br_old</span> <span class="operator">=</span> <span class="literal">null</span>;</span><br><span class="line">        <span class="keyword">try</span> {</span><br><span class="line">            br_old = <span class="keyword">new</span> <span class="title class_">BufferedReader</span>(<span class="keyword">new</span> <span class="title class_">FileReader</span>(<span class="string">"file.txt"</span>));</span><br><span class="line">            <span class="comment">// ... 读取文件 ...</span></span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            e.printStackTrace();</span><br><span class="line">        } <span class="keyword">finally</span> {</span><br><span class="line">            <span class="keyword">if</span> (br_old != <span class="literal">null</span>) {</span><br><span class="line">                <span class="keyword">try</span> {</span><br><span class="line">                    br_old.close();</span><br><span class="line">                } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">                    e.printStackTrace();</span><br><span class="line">                }</span><br><span class="line">            }</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// Java 7+ 的 try-with-resources 写法，简洁且安全</span></span><br><span class="line">        <span class="comment">// try()括号内声明的资源，在try块结束时会自动关闭</span></span><br><span class="line">        <span class="keyword">try</span> (<span class="type">BufferedReader</span> <span class="variable">br_new</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BufferedReader</span>(<span class="keyword">new</span> <span class="title class_">FileReader</span>(<span class="string">"file.txt"</span>))) {</span><br><span class="line">            <span class="comment">// ... 直接读取文件，无需关心关闭 ...</span></span><br><span class="line">            System.out.println(br_new.readLine());</span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            <span class="comment">// 只需处理读取过程中的异常</span></span><br><span class="line">            System.out.println(<span class="string">"文件读取失败: "</span> + e.getMessage());</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>结论</strong>：在处理任何可关闭的资源时，<strong>永远优先使用 <code>try-with-resources</code></strong>，这是现代Java开发的标准实践。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/35626.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/35626.html&quot;)">Java（二）：2.0 Java基础</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/35626.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=Java（二）：2.0 Java基础&amp;url=https://prorise666.site/posts/35626.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java基础知识总汇<span class="tagsPageCount">9</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/30645.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Java（一）：1.0 Java语言概述与核心生态</div></div></a></div><div class="next-post pull-right"><a href="/posts/42235.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Java（三）：3.0 [核心] 面向对象编程</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/30645.html" title="Java（一）：1.0 Java语言概述与核心生态"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（一）：1.0 Java语言概述与核心生态</div></div></a></div><div><a href="/posts/43523.html" title="Java（9）：9.0 JavaWeb核心知识点速查"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/814899.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-09</div><div class="title">Java（9）：9.0 JavaWeb核心知识点速查</div></div></a></div><div><a href="/posts/62133.html" title="Java（五）：5.0 [元编程] 反射、注解"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（五）：5.0 [元编程] 反射、注解</div></div></a></div><div><a href="/posts/42235.html" title="Java（三）：3.0 [核心] 面向对象编程"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（三）：3.0 [核心] 面向对象编程</div></div></a></div><div><a href="/posts/6760.html" title="Java（四）：4.0 [核心] Java I/O 流体系与实战"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（四）：4.0 [核心] Java I/O 流体系与实战</div></div></a></div><div><a href="/posts/14501.html" title="Java（八）：8.0 Java新语法总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（八）：8.0 Java新语法总结</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Java（二）：2.0 Java基础",date:"2025-05-08 17:13:45",updated:"2025-07-14 16:38:56",tags:["Java基础知识总汇"],categories:["后端技术","Java"],content:'\n## **2.0 Java基础**\n\n本章将深入Java语言的根基——构成程序万物的基础语法。我们将从“数据类型”这个核心筒开始，对Java世界中所有用于承载数据的基本单元和复杂结构，进行一次全面、详尽的探险。这趟旅程不仅涵盖它们的用法，更会深入其设计原理与实战中的避坑技巧，为后续所有高阶概念的学习打下最坚实的地基。\n\n### **2.1 数据类型全解**\n\n#### **2.1.1 引言：Java数据世界的两大基石**\n\n在开始学习具体的数据类型之前，我们必须先建立一个宏观的认知：Java中的所有数据类型归根结底分为两大阵营。理解它们的本质区别，是掌握Java内存管理和程序性能的关键。\n\n##### 基本数据类型\n\n  * **定义**：这是Java语言内置的、最基础的8种数据类型。它们并非对象。\n  * **特点**：变量本身直接存储**数据的值**。通常存储在**栈（Stack）内存**中（特指方法内的局部变量），这使得它们的存取速度非常快。\n\n##### **引用数据类型**\n\n  * **定义**：除了8种基本数据类型之外的所有类型，包括我们自定义的类（Class）、接口（Interface）、数组（Array）、枚举（Enum）以及后面要讲的`String`和集合等，都属于引用类型。\n  * **特点**：变量存储的是一个**内存地址**，这个地址指向了真正的数据（即对象实例）。变量的引用地址存储在**栈**上，而对象实例则存储在**堆（Heap）内存**中。\n\n\n\n#### 2.1.2 基本数据类型 \n\n这部分类型是构成程序运算的最小单位，它们不具备对象的方法，但性能极高。\n\n##### **整数家族 (`byte`, `short`, `int`, `long`)**\n\n  * **核心用途**\n\n      * `byte`: 主要用于文件I/O操作，作为字节流进行数据传输，或在内存敏感的场景下节省空间。\n      * `short`: 使用场景较少，通常在一些底层或兼容性代码中可见。\n      * `int`: **最常用**的整数类型，用于计数、索引、ID等绝大多数整数运算场景。\n      * `long`: 用于表示需要超出`int`范围的整数，如时间戳（毫秒数）、大型文件大小、大型计数等2。\n\n  * **类型介绍与面试题**\n    | 类型  | 大小(位) | 取值范围        | 默认值 |\n    | :---- | :------- | :-------------- | :----- |\n    | `byte`  | 8        | -128 \\~ 127      | `0`    |\n    | `short` | 16       | -32,768 \\~ 32,767| `0`    |\n    | `int`   | 32       | -2³¹ \\~ 2³¹-1    | `0`    |\n    | `long`  | 64       | -2⁶³ \\~ 2⁶³-1    | `0L`   |\n\n      * **[面试题] `byte b = 127; b++;` 结果是什么？**\n        \n    > 答：结果是 `-128`。这是因为`byte`类型最大值为127（二进制 `01111111`），加1后发生**溢出**，二进制变为`10000000`，这在计算机补码表示中恰好是-128。\n    \n  * **代码示例**\n\n    ```java\n    package com.example;\n    \n    //TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or\n// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.\n    public class Main {\n        public static void main(String[] args) {\n            long timestamp = 1672531200000L;\n            byte b1 = 10;\n            byte b2 = 20;\n            // byte b3 = b1 + b2; // 这行会编译错误，因为b1+b2的结果已经是int类型\n            int result = b1 + b2; // 正确的做法\n            System.out.println("byte类型运算结果（已提升为int）: " + result);\n        }\n    }\n    ```\n\n##### **浮点数家族 (`float`, `double`)**\n\n  * **核心用途**：用于需要小数的计算，如科学计算、图形学等。`double`（双精度）比`float`（单精度）更常用，因为它精度更高。\n\n  * **类型介绍与避坑指南**\n\n      * `float`: 32位，数值后需加`F`或`f`后缀。\n      * `double`: 64位，是默认的小数类型。\n      * **[面试必考][避坑指南] 为何金融计算禁用`float`/`double`？**\n        \n        > 答：因为`float`和`double`采用二进制浮点数表示法，无法精确表示所有十进制小数（例如0.1）。这会导致舍入误差，在要求高精度的金融或商业计算中是致命的。**最佳实践是使用`java.math.BigDecimal`类**。\n\n  * **代码示例**\n\n    ```java\n    package com.example;\n    \n    import java.math.BigDecimal;\n    public class Main {\n            public static void main(String[] args) {\n                double d1 = 0.2;\n                double d2 = 0.3;\n                System.out.println("0.1 + 0.2 = " + (d1 + d2)); // 输出通常不是精确的0.3\n                // 使用BigDecimal进行精确计算\n                var bd1 = new BigDecimal("0.1"); // 注意：使用字符串构造以保证精度\n                var bd2 = new BigDecimal("0.2");\n                System.out.println("BigDecimal 精确计算: " + bd1.add(bd2));\n            }\n        }\n    \n    ```\n\n##### **`char` (字符类型) 与 `boolean` (布尔类型)**\n\n  * **核心用途**\n      * `char`: 表示单个字符，如字母、数字或符号。\n      * `boolean`: 用于逻辑判断，只有`true`和`false`两个值。\n  * **类型介绍与面试题**\n      * `char`在Java中占16位（2字节），采用Unicode编码，因此可以表示世界上绝大多数语言的字符。\n      * **[面试题] `char`类型能否存储一个中文汉字？**\n        \n        > 答：可以。因为Java的`char`类型使用Unicode编码，其范围覆盖了绝大多数汉字。\n\n#### 2.1.3 包装类\n\n##### **核心用途**\n\n包装类的存在是为了解决基本数据类型无法像对象一样被操作的问题。核心用途包括：\n\n1.  在集合框架中使用，如 `List<Integer>`，因为泛型参数必须是对象。\n2.  允许值为`null`，用于表示缺失或未定义的状态。\n3.  包含了许多实用的静态方法，如类型转换、进制转换等。\n\n##### **`Integer` 深度剖析**\n\n  * **类型介绍与面试题**\n\n    * **自动装箱 (Autoboxing)**: `Integer i = 100;` 编译器自动转换为 `Integer i = Integer.valueOf(100);`。\n    * **自动拆箱 (Unboxing)**: `int n = i;` 编译器自动转换为 `int n = i.intValue();`。\n\n    ```java\n    package com.example;\n    \n    public class Main {\n        public static void main(String[] args) {\n            Integer i = 100; // 自动装箱\n            // 约等于这一行 -> Integer i = Integer.valueOf(100);\n            int n = i; // 自动拆箱\n            // 约等于这一行 -> int n = i.intValue();\n            System.out.println("n = " + n); // 输出 100\n        }\n    }\n    ```\n\n    \n\n    **[面试必考] `Integer`缓存池**\n\n      > 为了提高性能，`Integer.valueOf()`方法对 **-128到127** 之间的整数进行了缓存。当通过自动装箱或`valueOf()`创建这个范围内的`Integer`对象时，会直接返回缓存中的同一个对象。超出这个范围，则会`new`一个新的对象。因此，使用 `==` 比较时，若两个对象不是同一个实例，就会得到 `false`，从而引发问题。建议使用 `.equals()` 方法进行值比较。\n\n    ```java\n    package com.example;\n    \n    public class Main {\n        public static void main(String[] args) {\n            // Integer 缓存池演示\n            Integer a = 100;\n            Integer b = 100;\n            System.out.println("a == b (100): " + (a == b)); // true, 因为在缓存池内\n    \n            Integer c = 200;\n            Integer d = 200;\n            System.out.println("c == d (200): " + (c == d)); // false, 超出缓存范围，创建了新对象\n        }\n    }\n    ```\n\n  * **常用方法速查表**\n    | 方法签名                                | 功能描述                                                     |\n    | :-------------------------------------- | :----------------------------------------------------------- |\n    | `parseInt(String s)`                    | 将字符串解析为`int`基本类型。                                |\n    | `valueOf(String s / int i)`             | 将字符串或`int`转换为`Integer`对象。（推荐使用，会利用缓存） |\n    | `intValue()`                            | 将`Integer`对象转换为`int`基本类型。                         |\n    | int `compareTo(Integer anotherInteger)` | 比较两个`Integer`对象的大小。                                |\n    | `boolean equals(Object obj)`            | 比较两个`Integer`对象的值是否相等。                          |\n\n```java\npackage com.example;\n\npublic class Main {\n    public static void main(String[] args) {\n        // parseInt：将字符串解析为int基本类型\n        int i1 = Integer.parseInt("123");\n        System.out.println(i1); // 输出：Int(123)\n\n        // valueOf：将字符串或int转换为Integer对象（推荐使用，可利用缓存）\n        Integer i2 = Integer.valueOf("456");\n        Integer i3 = Integer.valueOf(789);\n\n        // intValue：将Integer对象转为int基本类型\n        int i4 = i3.intValue();\n        System.out.println(i4); // 输出：789\n\n        // compareTo：比较两个Integer对象的大小\n        int result = i2.compareTo(i3); // 返回 -1、0 或 1\n        System.out.println(result); // 输出：-1，因为i2 < i3\n\n        // equals：比较两个Integer对象的值是否相等\n        boolean isEqual = i2.equals(i3); // 返回 false，因为i2=456，i3=789\n        System.out.println(isEqual);\n    }\n}\n```\n\n\n\n#### **2.1.4 字符串：`String`**\n\n##### **核心用途**\n\n用于表示和操作一切文本信息，是Java中使用最频繁的类之一。\n\n##### **类型介绍与核心面试题**\n\n  * **[面试必考] `String`的不可变性**：`String`对象一旦被创建，其内容就不能被修改。任何对`String`的修改操作（如拼接、替换）都会返回一个**新的**`String`对象。**好处**：\n\n  * 1. **线程安全**；\n\n\t2. **利于缓存**（字符串常量池）；\n\n\t3.作为`HashMap`的键时，可保证`hashCode`不变。\n\n  * **[面试] 字符串常量池(String Pool)**：位于堆内存中。当使用字面量（如 `String s = "Java";`）创建字符串时，JVM会先检查池中是否存在"Java"，如果存在则直接返回其引用，否则创建新的并放入池中。\n\n```java\npackage com.example;\n\npublic class Main {\n    public static void main(String[] args) {\n        // 创建字符串并赋值\n        String str1 = "Hello";\n        String str2 = "Hello";\n\n        // 检查两个字符串是否引用相同\n        boolean areSame = (str1 == str2); // 结果为true，因为str1和str2指向同一个对象\n        System.out.println(areSame);\n\n        // 创建一个新的字符串对象\n        String str3 = new String("Hello");\n\n        // 检查str1和str3是否引用相同\n        boolean areSame2 = (str1 == str3); // 结果为false，因为str3是新生成的对象\n        System.out.println(areSame2);\n    }\n}\n```\n\n  * **[面试题] `new String("abc")` 创建了几个对象？**\n    \n    > 答：可能是一个，也可能是两个。如果常量池中已有"abc"，则只在堆中创建一个新的`String`对象。如果常量池中没有，则会在池中创建一个，同时在堆中也创建一个，共两个对象。\n\n##### **常用方法速查表**\n\n| 分类          | 方法签名                                                       | 功能描述                                   |\n| :------------ | :------------------------------------------------------------- | :----------------------------------------- |\n| **获取/判断** | `length()`, `isEmpty()`, `charAt(int index)`, `contains(CharSequence s)` | 获取长度、判空、获取字符、判断包含         |\n| **查找** | `indexOf(String str)`, `lastIndexOf(String str)`               | 查找子串首次/末次出现的位置              |\n| **比较** | `equals(Object anObject)`, `equalsIgnoreCase(String anotherString)` | 内容比较（区分/不区分大小写）            |\n| **截取/分割** | `substring(int beginIndex, int endIndex)`, `split(String regex)` | 截取子串，按正则表达式分割               |\n| **替换** | `replace(char oldChar, char newChar)`, `replaceAll(String regex, String replacement)` | 字符替换，正则替换                         |\n| **转换** | `toLowerCase()`, `toUpperCase()`, `trim()`, `toCharArray()`, `getBytes()` | 大小写转换、去首尾空格、转数组             |\n\n##### **代码示例详解**\n\n```java\nString str = "Hello, World!";\n\n// 获取长度\nint length = str.length(); // 13\n\n// 判空\nboolean isEmpty = str.isEmpty(); // false\n\n// 获取字符\nchar c = str.charAt(0); // \'H\'\n\n// 判断包含\nboolean contains = str.contains("World"); // true\n\n// 查找子串首次出现的位置\nint indexOf = str.indexOf("World"); // 7\n\n// 查找子串末次出现的位置\nint lastIndexOf = str.lastIndexOf("o"); // 8\n\n// 内容比较（区分大小写）\nboolean equals = str.equals("Hello, World!"); // true\n\n// 内容比较（不区分大小写）\nboolean equalsIgnoreCase = str.equalsIgnoreCase("hello, world!"); // true\n\n// 截取子串\nString substring = str.substring(0, 5); // "Hello"\n\n// 按正则表达式分割\nString[] split = str.split(", "); // ["Hello", "World!"]\n\n// 字符替换\nString replace = str.replace(\'o\', \'a\'); // "Hella, Warld!"\n\n// 正则替换\nString replaceAll = str.replaceAll("[a-zA-Z]", "*"); // "*****, *****!"\n\n// 小写转换\nString toLowerCase = str.toLowerCase(); // "hello, world!"\n\n// 大写转换\nString toUpperCase = str.toUpperCase(); // "HELLO, WORLD!"\n\n// 去首尾空格\nString trim = "   Hello   ".trim(); // "Hello"\n\n// 转字符数组\nchar[] toCharArray = str.toCharArray(); // [\'H\',\'e\',\'l\',\'l\',\'o\',\',\',\' \',\'W\',\'o\',\'r\',\'l\',\'d\',\'!\']\n\n// 转字节数组\nbyte[] getBytes = str.getBytes(); // 字节形式的字符串\n```\n\n\n\n\n\n\n\n\n\n##### 关联类型：`StringBuilder`与`StringBuffer`\n\n###### **核心用途与场景**\n\n当我们需要频繁地修改或拼接字符串时，使用不可变的 `String` 会因创建大量临时对象而导致性能低下。`StringBuilder` 和 `StringBuffer` 正是为解决这一问题而生的可变字符串序列。\n\n  * **`StringBuilder`**: 适用于**单线程**环境下的字符串拼接或修改。是绝大多数场景下的首选，因为它性能最高。\n  * **`StringBuffer`**: 适用于**多线程**环境下，需要保证共享字符串数据线程安全的场景。\n\n###### **类型介绍与原理**\n\n`StringBuilder` 和 `StringBuffer` 本质上都是一个**可变的字符数组容器**。与 `String` 每次操作都返回新对象不同，它们的大部分操作（如`append`）都是在内部的字符数组上直接进行的，只有在数组容量不足时才会进行扩容，从而避免了不必要的对象创建。\n\n  * **可变性 (Mutability)**：它们的内部 `char[]` 数组不是 `final` 的，并且长度可以动态改变。\n  * **线程安全机制 (面试必考)**：\n      * `StringBuffer`：它的所有公开方法（如 `append`, `insert`）都被 `synchronized` 关键字修饰，这意味着在同一时刻，只有一个线程能访问这些方法，从而保证了线程安全。但加锁也带来了额外的性能开销。\n      * `StringBuilder`：它在Java 5中被引入，可以看作是 `StringBuffer` 的一个非线程安全版本，去掉了 `synchronized` 关键字，因此在单线程环境下性能更优。\n\n###### **常用方法速查表**\n\n（以下方法对 `StringBuilder` 和 `StringBuffer` 均适用）\n\n| 方法签名                             | 功能描述                                                       |\n| :----------------------------------- | :------------------------------------------------------------- |\n| `append(...)`                        | 在序列末尾追加内容。此方法被重载以接受所有基本类型、`String`等。 |\n| `insert(int offset, ...)`            | 在指定索引位置插入内容。                                       |\n| `delete(int start, int end)`         | 删除指定范围内的字符。                                         |\n| `deleteCharAt(int index)`            | 删除指定位置的单个字符。                                       |\n| `replace(int start, int end, String str)` | 用指定字符串替换范围内的内容。                                 |\n| `reverse()`                          | 将序列反转。                                                   |\n| `length()`                           | 返回当前序列的长度。                                           |\n| `capacity()`                         | 返回当前内部数组的容量。                                       |\n| `toString()`                         | 将当前的可变序列转换为一个不可变的`String`对象。               |\n\n###### **代码示例详解**\n\n  * **场景一：循环中的高效拼接**\n\n    > 这是 `StringBuilder` 最核心、最经典的应用场景。\n\n    ```java\n    package com.example;\n    \n    public class Main {\n        public static void main(String[] args) {\n            // 低效的方法\n            String str = "";\n            for (int i = 0; i < 10; i++) {\n                str += i;\n            }\n            System.out.println("String \'+\' 拼接结果: " + str);\n    \n    \n            // 高效的方法：使用StringBuilder\n            StringBuilder sb = new StringBuilder();\n            for (int i = 0; i < 10; i++) {\n                sb.append(i);\n            }\n            String result = sb.toString(); // 最后需要时再转换为String\n            System.out.println("StringBuilder 拼接结果: " + result);\n        }\n    }\n    ```\n\n  * **场景二：链式调用构建复杂字符串**\n\n    > `append`等方法返回对象本身，使得链式编程成为可能，代码更简洁。\n\n    ```java\n    package com.example;\n    \n    public class Main {\n        public static void main(String[] args) {\n            StringBuilder queryBuilder = new StringBuilder();\n            queryBuilder.append("SELECT ")\n                    .append("id, name, email ")\n                    .append("FROM users ")\n                    .append("WHERE age > ?");\n            String sqlQuery = queryBuilder.toString();\n            System.out.println("构建的SQL查询: " + sqlQuery);\n        }\n    }\n    ```\n\n  * **场景三：字符串反转**\n\n    > `String`本身没有提供反转方法，使用`StringBuilder`可以轻松实现。\n\n    ```java\n    package com.example;\n    \n    public class Main {\n        public static void main(String[] args) {\n            String original = "level";\n            StringBuilder reverseBuilder = new StringBuilder(original);\n            String reversed = reverseBuilder.reverse().toString();\n            System.out.println("\'" + original + "\' 的反转是 \'" + reversed + "\'"); // 🤭\n        }\n    }\n    ```\n\n###### **[面试题] 何时使用`StringBuilder`和`StringBuffer`？**\n\n答：当需要进行**大量或循环内的字符串拼接**时，应使用它们来避免创建大量临时的`String`对象，从而提高性能。在选择时：\n\n  * **单线程环境**：优先且总是使用 `StringBuilder`，因为它没有同步开销，性能更好。\n  * **多线程环境**：如果一个字符串对象需要被多个线程共享和修改，必须使用 `StringBuffer` 来保证线程安全。\n\n\n\n\n\n\n\n\n-----\n\n#### **2.1.5 数组 (Arrays) 与其工具类**\n\n##### **核心用途与场景**\n\n数组是Java中最基础、最高效的数据结构之一，其核心用途是在内存中存储**固定大小**、**同一类型**的元素序列。\n\n  * **核心场景**:\n      * 当元素数量固定，且对性能有较高要求时（如算法题、底层数据缓冲）。\n      * 作为更复杂数据结构（如`ArrayList`, `HashMap`）的内部实现。\n      * 表示和操作矩阵或表格（使用多维数组）。\n      * 方法的参数或返回值，尤其是`main(String[] args)`。\n\n##### **类型介绍与初始化**\n\n  * **数组即对象**：在Java中，数组是一个**引用类型**。数组变量存储在栈中，它指向堆内存中一块**连续开辟**的空间。这也解释了为什么数组的长度一旦创建就不可改变，因为其内存空间是连续且固定的。\n\n  * **`length` 属性**：数组拥有一个公共的`final`属性`length`来获取其长度，注意它是一个**属性**，而非方法（区别于`List`的`size()`方法）。\n\n  * **初始化方式**：\n\n    1.  **静态初始化**：在创建时直接指定内容。\n        ```java\n        int[] staticArray = {10, 20, 30};\n        String[] names = new String[]{"Java", "Python"};\n        ```\n    2.  **动态初始化**：指定数组长度，由系统分配默认值。\n        ```java\n        int[] dynamicArray = new int[5]; // 所有元素默认为 0\n        boolean[] flags = new boolean[3]; // 所有元素默认为 false\n        String[] strings = new String[4]; // 所有元素默认为 null\n        ```\n\n  * **[进阶] 多维数组**\n\n      * Java中的多维数组本质上是“**数组的数组**”。例如，一个二维数组 `int[][]` 实际上是一个`int[]`类型的数组，它的每个元素都是一个`int[]`数组。\n      * 因为是“数组的数组”，所以Java支持**不规则数组**（Ragged Array），即二维数组的每一行可以有不同的长度。\n      \n      ```java\n      package com.example;\n      \n      public class Main {\n          public static void main(String[] args) {\n              int[][] arr = new int[3][];\n              arr[0] = new int[2];  // 第一行长度为 2\n              arr[1] = new int[3];  // 第二行长度为 3\n              arr[2] = new int[5];  // 第三行长度为 5\n              \n          }\n      }\n      ```\n\n##### **`java.util.Arrays` 核心工具方法详解**\n\n`java.util.Arrays` 是一个专门用于操作数组的工具类，提供了大量高效的静态方法。\n\n| 分类       | 方法签名                                                       | 功能描述与注意事项                                                                                                            |\n| :--------- | :------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------- |\n| **排序** | `sort(T[] a)` / `sort(T[] a, Comparator c)`                    | 对数组进行升序排序。**底层算法**：为对象使用TimSort，为基本类型使用优化的快速排序。可提供自定义比较器（位于Java8新语法会详讲`Comparator`） |\n| **查找** | `binarySearch(T[] a, T key)`                                   | **必须在已排序数组上使用**。如果找到，返回索引；否则返回 `-(insertion point) - 1`。                                                     |\n| **比较** | `equals(T[] a, T[] a2)` / `deepEquals(Object[] a1, Object[] a2)` | `equals` 比较一维数组内容。`deepEquals` 用于递归比较**多维数组**。                                                             |\n| **复制** | `copyOf(T[] original, int len)` / `copyOfRange(T[] o, int f, int t)` | `copyOf` 复制整个数组到新长度。`copyOfRange` 复制指定范围。是实现数组**扩容/缩容**的常用手段。                                   |\n| **填充** | `fill(T[] a, T val)`                                           | 用同一个值填充数组的所有元素，常用于初始化。                                                                                       |\n| **转换** | `toString(T[] a)` / `deepToString(Object[] a)`                 | `toString` 用于优雅地打印一维数组。`deepToString` 用于打印**多维数组**。                                                         |\n| **转换** | `asList(T... a)`                                               | **[高频避坑]** 返回一个**固定大小**的`List`视图，**不支持add/remove操作**。对列表的修改会直接反映到原数组上，反之亦然。         |\n| **转换** | `stream(T[] array)`                                            | **[Java 8+]** 将数组转换为一个`Stream`，便于使用函数式编程进行链式操作，极大增强了数组的处理能力。                              |\n\n##### **代码示例详解**\n\n###### 排序\n\n```java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.Comparator;\n\npublic class Main {\n    public static void main(String[] args) {\n        Integer[] arr = {5, 2, 9, 1, 8, 6};\n        Arrays.sort(arr); // 升序排序\n        System.out.println("升序排序结果：" + Arrays.toString(arr)); // [1, 2, 5, 6, 8, 9]\n\n        Arrays.sort(arr, Comparator.reverseOrder()); // 降序排序\n        System.out.println(Arrays.toString(arr)); // [9, 8, 6, 5, 2, 1]\n    }\n}\n```\n\n\n\n###### 查找\n\n```java\npackage com.example;\n\nimport java.util.Arrays;\n\npublic class Main {\n    public static void main(String[] args) {\n        int[] arr = {10, 20, 30, 40, 50};\n        int index = Arrays.binarySearch(arr, 30);\n        System.out.println("索引: " + index); // 索引: 2\n\n        index = Arrays.binarySearch(arr, 25);\n        System.out.println("未找到时返回: " + index); // 未找到时返回: -3\n    }\n}\n```\n\n\n\n###### 比较\n\n```java\npackage com.example;\n\nimport java.util.Arrays;\n\npublic class Main {\n    public static void main(String[] args) {\n        int[] a = {1, 2, 3};\n        int[] b = {1, 2, 3};\n        int[] c = {1, 2, 4};\n\n        System.out.println(Arrays.equals(a, b)); // true\n        System.out.println(Arrays.equals(a, c)); // false\n\n        int[][] d = {{1, 2}, {3, 4}};\n        int[][] e = {{1, 2}, {3, 4}};\n        int[][] f = {{1, 2}, {3, 5}};\n        // false 因为d和e是不同的对象,在双重数组中,equals方法比较的是数组的引用,而不是数组的内容\n        System.out.println(Arrays.equals(d,e));\n        // 这时候可以采用deepEquals方法,比较的是数组的内容\n        System.out.println(Arrays.deepEquals(d, e)); // true\n\n    }\n}\n```\n\n\n\n###### 复制\n\n```java\npackage com.example;\n\nimport java.util.Arrays;\n\npublic class Main {\n    public static void main(String[] args) {\n        int[] original = {1, 2, 3, 4, 5};\n        int[] copy = Arrays.copyOf(original, 3); // 复制前3个元素\n        System.out.println(Arrays.toString(copy)); // [1, 2, 3]\n\n        int[] rangeCopy = Arrays.copyOfRange(original, 1, 4); // 从索引1到3（不包含4）\n        System.out.println(Arrays.toString(rangeCopy)); // [2, 3, 4]\n\n    }\n}\n```\n\n\n\n###### 填充\n\n```java\npackage com.example;\n\nimport java.util.Arrays;\n\npublic class Main {\n    public static void main(String[] args) {\n        int[] arr = new int[5];\n        Arrays.fill(arr, 10);\n        System.out.println(Arrays.toString(arr)); // [10, 10, 10, 10, 10]\n    }\n}\n```\n\n\n\n###### 转换\n\n```java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.List;\n\npublic class Main {\n    public static void main(String[] args) {\n        String[] arr = {"a", "b", "c"};\n        System.out.println(Arrays.toString(arr)); // [a, b, c]\n\n        List<String> list = Arrays.asList(arr);\n        System.out.println(list); // [a, b, c]\n\n        // 注意：asList 返回的是固定大小的列表，不能添加或删除元素\n        // list.add("d"); // 抛出 UnsupportedOperationException\n    }\n}\n```\n\n\n\n###### 流处理\n\n这在后续的Java8语法中是至关重要的一个方法，开启一个流，并将每一个元素作为一个流来处理\n\n```java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.List;\nimport java.util.stream.Stream;\n\npublic class Main {\n    public static void main(String[] args) {\n        String[] arr = {"apple", "banana", "cherry"};\n        Stream<String> stream = Arrays.stream(arr);\n        stream.forEach(System.out::println);\n        // 输出:\n        // apple\n        // banana\n        // cherry\n    }\n}\n```\n\n\n\n\n\n##### **[面试题] 数组 (Array) vs. 列表 (`ArrayList`)**\n\n| 对比维度           | 数组 (Array)                                             | `ArrayList`                               |\n| :----------------- | :------------------------------------------------------- | :---------------------------------------- |\n| **大小** | **固定**，创建时必须指定，不可改变。                     | **动态**，可根据需要自动扩容。            |\n| **元素类型** | 可存储**基本数据类型**和对象引用。                       | **只能存储对象引用**（基本类型需自动装箱）。 |\n| **性能** | 访问（get/set）极快 ，增删慢（需手动实现）。       | 访问快，增删（尤其在中间）相对较慢。 |\n| **泛型支持** | 不支持泛型。                                             | 支持泛型，提供编译时类型安全检查。        |\n| **API与功能** | 功能有限，需依赖`Arrays`工具类。                         | 功能强大，提供了丰富的增删改查方法。      |\n| **核心选择依据** | 数量固定且追求极致性能时选**数组**。                     | 数量不固定，需要灵活增删和丰富API时选`ArrayList`。 |\n\n\n\n#### **2.1.6 集合框架：`List`**\n\n##### **核心用途**\n\n存储**有序、可重复**的元素集合，长度可动态改变。是日常开发中最常用的集合类型之一。\n\n##### **`ArrayList` 详解**\n\n  * **核心用途**：最常用的`List`实现，适用于**高频的随机访问（查、改）**场景。\n  * **类型介绍与底层原理**：底层基于**动态数组**实现。当添加元素导致容量不足时，会触发**扩容**机制，通常是创建一个1.5倍于原容量的新数组，并将旧数据复制过去。\n  * **常用方法速查表**\n    | 方法签名                      | 功能描述                  |\n    | :---------------------------- | :------------------------ |\n    | `boolean add(E e)`              | 在列表末尾添加元素。      |\n    | `void add(int index, E element)`| 在指定索引处插入元素。    |\n    | `E get(int index)`              | 获取指定索引处的元素。    |\n    | `E set(int index, E element)`   | 替换指定索引处的元素。    |\n    | `E remove(int index)`           | 移除指定索引处的元素。    |\n    | `int size()`                    | 返回列表中的元素数量。    |\n  * **代码示例详解**\n    ```java\n    package com.example;\n    \n    import java.util.ArrayList;\n    import java.util.Arrays;\nimport java.util.List;\n    import java.util.stream.Stream;\n    \n    public class Main {\n        public static void main(String[] args) {\n            ArrayList<String> fruits = new ArrayList<>();\n            fruits.add("Apple");\n            fruits.add("Banana");\n            fruits.add(0, "Orange"); // 在索引0处插入\n    \n            System.out.println("第一个水果: " + fruits.get(0)); // Orange\n            fruits.set(1, "Grape"); // 替换\n            System.out.println("所有水果: " + fruits); // [Orange, Grape, Banana]\n    \n        }\n    }\n    ```\n\n##### **`LinkedList` 详解**\n\n  * **核心用途**：适用于**高频的头尾增删**操作场景。它还实现了`Deque`接口，可作为**队列**或**栈**使用。\n  * **类型介绍与底层原理**：底层基于**双向链表**实现。每个节点都存储着数据以及前后节点的引用。\n  * **常用方法速查表**\n    | 方法签名                  | 功能描述                         | 接口来源        |\n    | :------------------------ | :------------------------------- | :-------------- |\n    | `void addFirst(E e)`        | 在列表头部添加元素。             | `Deque`         |\n    | `void addLast(E e)`         | 在列表尾部添加元素。             | `Deque`         |\n    | `E poll()` / `E pollFirst()`| 获取并移除列表头部元素。         | `Queue`/`Deque` |\n    | `E pollLast()`              | 获取并移除列表尾部元素。         | `Deque`         |\n    | `E peek()` / `E peekFirst()`| 查看列表头部元素（不移除）。     | `Queue`/`Deque` |\n  * **代码示例**\n    \n    ```java\n    package com.example;\n    \n    import java.util.ArrayList;\n    import java.util.Arrays;\n    import java.util.LinkedList;\n    import java.util.List;\n    import java.util.stream.Stream;\n    \n    public class Main {\n        public static void main(String[] args) {\n            LinkedList<String> taskQueue = new LinkedList<>();\n            // 作为队列使用\n            taskQueue.offer("Task 1"); // 入队\n            taskQueue.offer("Task 2");\n            System.out.println("处理任务: " + taskQueue.poll()); // 出队\n            System.out.println("下一个任务: " + taskQueue.peek());\n        }\n    }\n    ```\n\n##### **[面试题] `ArrayList` vs `LinkedList` 对比**\n\n| 特性              | `ArrayList`                         | `LinkedList`                             |\n| :---------------- | :---------------------------------- | :--------------------------------------- |\n| **底层结构** | 动态数组                            | 双向链表                                 |\n| **随机访问(get)** | 快                         | 慢                          |\n| **增/删(add/remove)** | 末尾快，中间慢（需移动元素）        | 头尾极快，中间慢（需遍历定位）  |\n| **内存占用** | 较少，内存连续                      | 较大，需额外空间存节点引用             |\n| **适用场景** | 读多写少，随机访问多                | 写多读少，头尾操作多                   |\n\n\n\n\n\n-----\n\n#### **2.1.7 集合框架：`Set`**\n\n`Set`接口继承自`Collection`接口，它代表一个不包含重复元素的集合。这是`Set`与`List`最本质的区别。`Set`的主要设计目标就是确保其中每个元素的唯一性，并提供快速的成员资格检查。\n\n  * **核心特性**:\n    1.  **不重复 (No Duplicates)**：`Set`中不允许出现重复的元素。尝试添加一个已经存在的元素将会失败，且不会抛出异常。\n    2.  **通常无序 (Generally Unordered)**：大部分`Set`的实现（如`HashSet`）不保证元素的存储和迭代顺序。但也有例外，如`LinkedHashSet`会保持插入顺序，`TreeSet`会保持排序顺序。\n\n\n\n##### **`HashSet` 详解**\n\n**核心用途与场景**\n\n`HashSet`是`Set`接口最常用、性能最高的实现类，其核心价值在于**高效的元素去重与查找**，他是无序的，在去重一个列表中会将元素打乱，顺序不一定按照顺序\n\n  * **最佳场景**：\n      * 对一个数据集（如`List`）进行快速去重。\n      * 需要快速判断某个元素是否存在于一个庞大的集合中。\n      * 存储一组唯一的ID或标识符。\n\n**类型介绍与去重原理**\n\n  * **[面试] 去重流程**：\n    `HashSet`保证元素唯一的两大基石是`hashCode()`和`equals()`方法。当调用`add(element)`方法时，其内部会执行`map.put(element, PRESENT)`,参数 `PRESENT` 是一个常量，通常用于表示键已存在，但不需要存储额外的值。它常用于 `HashSet` 或 `HashMap` 的实现中，作为占位符值，以区分键是否被插入过。\n    \n  * `HashMap`的`put`流程如下：\n    首先，计算`element`的`hashCode()`值，通过哈希算法定位到内部数组的某个“桶”（bucket）索引。如果这个桶是空的，元素直接存入。如果桶中已经有其他元素（即发生哈希冲突），则会遍历这个桶中的所有元素，逐个用`equals()`方法与新元素进行比较。只要有一次`equals()`返回`true`，就认定元素已存在，添加失败；如果所有比较结果都为`false`，则将新元素添加到这个桶中（通常是链表或红黑树的末端）。\n    \n> **结论**：若想让自定义的类（如`User`）对象能在`HashSet`中被正确去重，**必须同时、正确地重写`hashCode()`和`equals()`方法**。\n\n###### **常用方法速查表**\n\n| 方法签名                 | 功能描述                                                       |\n| :----------------------- | :------------------------------------------------------------- |\n| `boolean add(E e)`       | 添加元素。如果元素已存在，则返回`false`，集合不变。         |\n| `boolean remove(Object o)` | 移除指定元素。如果成功移除，返回`true`。                     |\n| `boolean contains(Object o)`| 判断是否包含指定元素。这是`Set`的核心优势之一。                |\n| `int size()`             | 返回集合中的元素数量。                                         |\n| `void clear()`           | 清空集合中的所有元素。                                         |\n| `Iterator<E> iterator()` | 获取用于遍历集合的迭代器。                                     |\n\n###### **代码示例详解**\n\n  * **场景一：基本数据类型去重**\n\n    ```java\n    package com.example;\n    \n    import java.util.*;\n    import java.util.stream.Stream;\n    \n    public class Main {\n        public static void main(String[] args) {\n            // 使用HashSet为List去重\n            List<String> nameList = Arrays.asList("Alice", "Bob", "Alice", "Charlie");\n            Set<String> uniqueNames = new HashSet<>(nameList);\n    \n            System.out.println("原始列表: " + nameList);      // 输出: [Alice, Bob, Alice, Charlie]\n            System.out.println("去重后集合: " + uniqueNames); // 输出: [Alice, Bob, Charlie] (顺序不保证)\n        }\n    }\n    ```\n\n  * **场景二：自定义对象的正确去重**\n\n    ```java\n    package com.example;\n    \n    import java.util.*;\n    \n    public class Main {\n        public static void main(String[] args) {\n            Set<User> users = new HashSet<>();\n            users.add(new User("U001", "Alice"));\n            users.add(new User("U002", "Bob"));\n            users.add(new User("U001", "Alice V2")); // id相同，被认为是重复对象，无法添加\n    \n            System.out.println("用户集合大小: " + users.size()); // 输出: 2\n            System.out.println(users); // 输出两个User对象\n        }\n    }\n    \n    class User {\n        String id;\n        String name;\n    \n        // 构造方法\n        public User(String id, String name) {\n            this.id = id;\n            this.name = name;\n        }\n    \n        @Override\n        public int hashCode() {\n            return java.util.Objects.hash(id); // 通常用唯一标识（如ID）来计算哈希\n        }\n    \n        @Override\n        public boolean equals(Object obj) {\n            if (this == obj) return true;\n            if (obj == null || getClass() != obj.getClass()) return false;\n            User user = (User) obj;\n            return java.util.Objects.equals(id, user.id);\n        }\n    }\n        \n    ```\n\n##### **`LinkedHashSet` 详解**\n\n###### **核心用途与场景**\n\n当你在需要`Set`的去重特性的同时，还希望**保持元素的插入顺序**时，`LinkedHashSet`是最佳选择。\n\n  * **最佳场景**：\n      * 记录用户操作序列，并去除重复操作。\n      * 需要去重，但后续的展示或处理需要按照添加的先后顺序。\n\n###### **类型介绍与底层原理**\n\n`LinkedHashSet`继承自`HashSet`。它的实现方式与`HashSet`类似，但其内部使用的是一个`LinkedHashMap`实例。`LinkedHashMap`在`HashMap`的基础上，额外维护了一个贯穿所有元素的双向链表，正是这个链表保证了迭代的顺序与元素插入的顺序一致。\n\n```java\npackage com.example;\n\nimport java.util.LinkedHashSet;\nimport java.util.Set;\n\npublic class Main {\n    public static void main(String[] args) {\n        // 创建一个LinkedHashSet来记录用户操作\n        Set<String> userActions = new LinkedHashSet<>();\n\n        // 模拟用户操作\n        userActions.add("登录");\n        userActions.add("查看个人信息");\n        userActions.add("退出");\n        userActions.add("登录"); // 重复操作\n\n        // 打印用户操作序列\n        System.out.println("User Actions: " + userActions); // [登录, 查看个人信息, 退出]\n    }\n}\n```\n\n\n\n##### **`TreeSet` 详解**\n\n##### **核心用途与场景**\n\n当你需要一个时刻保持**排序状态**的、且元素唯一的集合时，`TreeSet`是唯一的选择。\n\n  * **最佳场景**：\n      * 排行榜的实时更新与展示。\n      * 需要从一个集合中快速获取最大或最小元素。\n      * 存储需要按特定规则排序的唯一数据。\n\n```java\npackage com.example;\n\n\nimport java.util.TreeSet;\n\npublic class Main {\n    public static void main(String[] args) {\n        // 创建一个 TreeSet 实例，自动按升序排序\n        TreeSet<Integer> treeSet = new TreeSet<>();\n        treeSet.add(10);\n        treeSet.add(30);\n        treeSet.add(20);\n        treeSet.add(10); // 重复元素不会被添加\n\n        // 输出 TreeSet 中的元素（自动排序）\n        System.out.println("TreeSet 中的元素: " + treeSet);\n\n        // 获取最小值和最大值\n        System.out.println("最小值: " + treeSet.first());\n        System.out.println("最大值: " + treeSet.last());\n\n        // 检查是否包含某个元素\n        System.out.println("是否包含 20: " + treeSet.contains(20));\n\n        // 删除元素\n        treeSet.remove(20);\n        System.out.println("删除 20 后的 TreeSet: " + treeSet);\n    }\n}\n```\n\n\n\n##### **类型介绍与排序原理**\n\n  * **[底层] 数据结构**：`TreeSet`的底层是基于**红黑树（Red-Black Tree）**实现的，这是一种自平衡的二叉搜索树。元素在被添加时，会根据其排序规则被放置在树的正确位置，从而保证了集合始终处于有序状态。实际上，`TreeSet`内部使用的是一个`TreeMap`。\n\n  * **[面试必考] 排序规则**:\n    `TreeSet`判断元素大小和唯一性的依据是元素的**比较**结果，而非`hashCode()`和`equals()`。它有两种排序方式：\n\n    1.  **自然排序**：存入`TreeSet`的元素所属的类必须实现`java.lang.Comparable`接口，并重写`compareTo(T o)`方法。Java中许多核心类如`Integer`、`String`都已实现此接口。\n    2.  **定制排序**：如果在创建`TreeSet`时，通过构造函数传入一个`java.util.Comparator`的实现类，那么`TreeSet`将使用这个比较器来对元素进行排序。这种方式更灵活，也更常用。\n\n##### **常用方法速查表**\n\n除了`Set`接口的通用方法外，`TreeSet`还提供了一系列强大的导航方法。\n\n| 方法签名          | 功能描述                           |\n| :---------------- | :--------------------------------- |\n| `E first()`       | 返回集合中的第一个（最小）元素。   |\n| `E last()`        | 返回集合中的最后一个（最大）元素。   |\n| `E lower(E e)`    | 返回小于给定元素e的最大元素。      |\n| `E higher(E e)`   | 返回大于给定元素e的最小元素。      |\n| `E floor(E e)`    | 返回小于等于给定元素e的最大元素。  |\n| `E ceiling(E e)`  | 返回大于等于给定元素e的最小元素。  |\n| `E pollFirst()`   | 移除并返回第一个（最小）元素。     |\n| `E pollLast()`    | 移除并返回最后一个（最大）元素。     |\n\n\n\n\n\n-----\n\n#### 2.1.8 集合框架：Map（重点）\n\n##### **`Map` 接口核心特性**\n\n`Map`接口是Java集合框架的另一大分支，它专门用于存储**键值对（Key-Value）**数据。`Map`中的每一个元素都包含一个唯一的键（Key）和一个与之关联的值（Value）。\n\n  * **核心特性**:\n    1.  **键的唯一性 (Unique Keys)**：`Map`中不允许存在重复的键。如果尝试用一个已存在的键`put`新值，新值会覆盖旧值。键的唯一性判断依赖于其`hashCode()`和`equals()`方法。\n    2.  **值可重复**：不同的键可以关联相同的值。\n    3.  **快速查找**：`Map`的核心价值在于能通过键来快速定位到值\n\n\n---\n\n\n##### **`HashMap` 核心方法速查表**\n\n###### **1. 核心操作**\n\n这是日常使用中最频繁的增、删、改、查操作。\n\n| 方法签名                  | 功能描述                                                       | 注意事项 / 最佳实践                                          |\n| :------------------------ | :------------------------------------------------------------- | :----------------------------------------------------------- |\n| `V put(K key, V value)`     | 将指定的键值对存入`Map`。如果键已存在，则**覆盖**旧值。     | **返回值**：返回与`key`关联的**旧值**；如果`key`是新的，则返回`null`。 |\n| `V get(Object key)`       | 根据键获取其对应的值。                                         | 如果`key`不存在，返回`null`。因此，`get()`返回`null`不一定代表`key`不存在，也可能`key`对应的值本身就是`null`。 |\n| `V remove(Object key)`    | 根据键移除对应的键值对。                                       | **返回值**：返回被移除的`key`所对应的`value`；如果`key`不存在，则返回`null`。 |\n| `boolean containsKey(Object key)` | 判断`Map`中是否包含指定的键。                                |                     |\n\n###### **2. 视图操作**\n\n`HashMap`提供了三种视图，用于以不同的角度审视`Map`中的数据。这些视图与`Map`本身是联动的。\n\n| 方法签名                      | 功能描述                                                       | 注意事项 / 最佳实践                                          |\n| :---------------------------- | :------------------------------------------------------------- | :----------------------------------------------------------- |\n| `Set<K> keySet()`               | 返回`Map`中所有**键（Key）**组成的一个`Set`集合。                | 返回的是一个**视图**，不是副本。对这个`Set`进行移除操作会同步影响到原`Map`，但**不支持添加操作**。 |\n| `Collection<V> values()`        | 返回`Map`中所有**值（Value）**组成的一个`Collection`。           | 同样是视图。可以包含重复元素。对这个集合的修改同样会影响原`Map`。 |\n| `Set<Map.Entry<K, V>> entrySet()`| 返回`Map`中所有**键值对节点（`Map.Entry`）**组成的`Set`集合。 | **最高效的遍历方式**。`Map.Entry`对象提供了`getKey()`和`getValue()`方法。 |\n\n###### **3. 状态查询**\n\n| 方法签名            | 功能描述                      | 注意事项 / 最佳实践                |\n| :------------------ | :---------------------------- | :--------------------------------- |\n| `int size()`        | 返回`Map`中键值对的数量。     | 时间复杂度为O(1)。               |\n| `boolean isEmpty()` | 判断`Map`是否为空（即`size()`是否为0）。 | 比 `size() == 0` 更具可读性。      |\n| `void clear()`      | 清空`Map`中所有的键值对。     | 调用后`size()`将变为0。          |\n\n###### **4. Java 8+ 增强方法**\n\nJava 8 引入了一系列函数式方法，极大地简化了代码。\n\n| 方法签名                                    | 功能描述                                                       | 注意事项 / 最佳实践                                          |\n| :------------------------------------------ | :------------------------------------------------------------- | :----------------------------------------------------------- |\n| `V getOrDefault(Object key, V defaultValue)`  | **强烈推荐**。获取值，若`key`不存在则返回一个指定的`defaultValue`。 | 优雅地解决了`get()`可能返回`null`的问题，避免了`if (map.get(key) != null)`的样板代码。 |\n| `V putIfAbsent(K key, V value)`               | 仅当`key`不存在或其值为`null`时，才存入该键值对。            | 可用于实现缓存、单例初始化等原子性操作，避免覆盖已有值。     |\n| `void forEach(BiConsumer<? super K, ? super V> action)` | 使用Lambda表达式遍历`Map`的每个键值对。                        | 是目前最简洁、最推荐的遍历方式之一。                         |\n| `V compute(K key, BiFunction<...> remappingFunction)` | 对指定`key`的值进行计算和更新，功能强大且原子。                | 适用于需要先`get`、再计算、最后`put`的复杂更新场景。         |\n| `V merge(K key, V value, BiFunction<...> remappingFunction)` | 合并值。如果`key`不存在，存入`value`；如果存在，则用旧值和新`value`执行函数，并将结果存入。 | 非常适合实现**计数统计**等聚合操作，比`getOrDefault`更强大。 |\n| `V replace(K key, V value)`                   | 仅当`key`存在时，才用新`value`替换旧值。                       |                                                              |\n\n###### **5. 批量操作 **\n\n| 方法签名                    | 功能描述                             | 注意事项 / 最佳实践                                   |\n| :-------------------------- | :----------------------------------- | :------------------------------------------------------ |\n| `void putAll(Map<? extends K, ? extends V> m)` | 将另一个`Map`中所有的键值对都复制到当前`Map`中。 | 如果键冲突，会用新`Map`中的值覆盖当前`Map`中的值。 |\n\n---\n**总结与建议**：\n\n在日常开发中，应熟练掌握**核心操作**和**视图操作**。同时，强烈建议多利用**Java 8+提供的新方法**（如 `getOrDefault`, `putIfAbsent`, `merge`, `forEach` 等），它们能让您的代码变得更简洁、更安全、更具表现力。\n\n\n\n##### **核心用途与场景**\n\n`HashMap`是`Map`接口最通用的实现，是日常开发中使用频率最高的集合之一。它适用于任何需要通过一个唯一标识来存取、管理一系列数据的场景。\n\n  * **典型场景**:\n      * **实现内存缓存**：快速存取热点数据，减轻数据库压力。\n      * **存储配置信息**：加载应用的配置项，键为配置名，值为配置值。\n      * **数据索引**：将`List`中的数据按某个字段（如用户ID）转为`Map`，实现快速查找。\n      * **计数统计**：统计文本中的词频，或集合中各元素的出现次数。\n      * **传递灵活参数**：在方法间传递一组不固定的参数，类似一个动态对象。\n\n\n\n-----\n\n###### **场景一：实现内存缓存**\n\n> **目的**：将耗时操作（如数据库查询、网络请求）的结果存储起来。当再次需要相同数据时，直接从内存中快速获取，避免重复执行耗时操作，从而提升系统性能。\n\n```java\nimport java.util.HashMap;\nimport java.util.Map;\nimport java.util.concurrent.TimeUnit;\npackage com.example;\n\n/**\n * 一个简单的内存缓存服务\n */\nclass SimpleCacheService {\n    // 使用HashMap作为缓存容器\n    private final Map<String, Object> cache = new HashMap<>();\n\n    /**\n     * 根据key获取数据。如果缓存中有，则直接返回；如果没有，则模拟一次耗时操作后存入缓存再返回。\n     * @param key 数据的唯一标识\n     * @return 获取到的数据\n     */\n    public Object getData(String key) {\n        // 1. 先从缓存中查找\n        if (cache.containsKey(key)) {\n            System.out.println("成功从缓存中获取数据: " + key);\n            return cache.get(key);\n        }\n\n        // 2. 缓存中没有，执行耗时操作（例如查询数据库）\n        System.out.println("缓存未命中，开始执行数据库查询: " + key);\n        Object data = fetchDataFromDB(key);\n        \n        // 3. 将结果存入缓存\n        cache.put(key, data);\n        System.out.println("数据已存入缓存: " + key);\n\n        return data;\n    }\n\n    // 模拟一个耗时的数据库查询\n    private Object fetchDataFromDB(String key) {\n        try {\n            TimeUnit.SECONDS.sleep(1); // 模拟1秒的延迟\n        } catch (InterruptedException e) {\n            Thread.currentThread().interrupt();\n        }\n        return "Data for " + key;\n    }\n}\n\npublic class Main {\n    public static void main(String[] args) {\n        SimpleCacheService cacheService = new SimpleCacheService();\n\n        // 第一次请求，会执行慢速操作\n        System.out.println("第一次请求结果: " + cacheService.getData("user:1001"));\n        System.out.println("--------------------");\n        // 第二次请求，应直接从缓存返回，速度很快\n        System.out.println("第二次请求结果: " + cacheService.getData("user:1001"));\n    }\n}\n/*\n输出:\n缓存未命中，开始执行数据库查询: user:1001\n数据已存入缓存: user:1001\n第一次请求结果: Data for user:1001\n--------------------\n成功从缓存中获取数据: user:1001\n第二次请求结果: Data for user:1001\n*/\n```\n\n###### **场景二：存储配置信息**\n\n> **目的**：在程序启动时，将配置文件（如`.properties`或`YAML`）中的键值对加载到`Map`中，便于在程序运行期间随时、快速地获取配置项。\n\n```java\nimport java.util.HashMap;\nimport java.util.Map;\n\n/**\n * 模拟一个应用配置管理器\n */\nclass ConfigManager {\n    private final Map<String, String> configMap = new HashMap<>();\n\n    public ConfigManager() {\n        // 在构造时模拟加载配置\n        loadConfigurations();\n    }\n\n    private void loadConfigurations() {\n        System.out.println("正在加载应用配置...");\n        configMap.put("app.name", "AwesomeApp");\n        configMap.put("server.port", "8080");\n        configMap.put("db.url", "********************************");\n        configMap.put("api.timeout.ms", "5000");\n        System.out.println("配置加载完成。");\n    }\n\n    public String getConfig(String key) {\n        return configMap.get(key);\n    }\n    \n    public int getIntConfig(String key, int defaultValue) {\n        String value = configMap.get(key);\n        if (value != null) {\n            try {\n                return Integer.parseInt(value);\n            } catch (NumberFormatException e) {\n                return defaultValue;\n            }\n        }\n        return defaultValue;\n    }\n}\n\npublic class ConfigExample {\n    public static void main(String[] args) {\n        ConfigManager config = new ConfigManager();\n        \n        String appName = config.getConfig("app.name");\n        int port = config.getIntConfig("server.port", 9090); // 获取int类型配置\n        \n        System.out.println("应用名称: " + appName);\n        System.out.println("服务器端口: " + port);\n    }\n}\n/*\n输出:\n正在加载应用配置...\n配置加载完成。\n应用名称: AwesomeApp\n服务器端口: 8080\n*/\n```\n\n###### **场景三：将列表数据转换为索引**\n\n> **目的**：将一个对象列表（`List<T>`）转换为以对象的某个唯一标识（如ID）为键的`Map<ID, T>`，从而将原先O(n)的遍历查找操作，优化为O(1)的直接访问操作。\n\n```java\npackage com.example;\nimport lombok.*;\nimport java.util.Arrays;\nimport java.util.List;\nimport java.util.Map;\nimport java.util.function.Function;\nimport java.util.stream.Collectors;\n\npublic class Main {\n    public static void main(String[] args) {\n\n        List<Product> productList = Arrays.asList(\n                new Product("P001", "Laptop"),\n                new Product("P002", "Mouse"),\n                new Product("P003", "Keyboard")\n        );\n\n        // 使用Java 8 Stream API，将List高效地转换为Map\n        Map<String, Product> productIndex = productList.stream()\n                .collect(Collectors.toMap(Product::getSku, Function.identity()));\n\n        // 现在可以通过SKU直接获取产品对象，无需遍历List\n        Product mouse = productIndex.get("P002");\n        System.out.println("通过SKU \'P002\' 快速查找到的产品: " + mouse);\n\n    }\n}\n\n\n@Data\n@ToString\n@AllArgsConstructor\n@NoArgsConstructor(force = true) // 强制生成无参构造函数\nclass Product {\n    private String sku; // 假设sku是必需的，但没有默认值\n    private String name; // name也是必需的，但没有默认值\n}\n```\n\n###### **场景四：计数统计**\n\n> **目的**：统计一个集合或文本中，每个独立元素出现的次数。`HashMap`是实现该功能的完美数据结构。\n\n```java\nimport java.util.HashMap;\nimport java.util.Map;\n\npublic class FrequencyCountExample {\n    public static void main(String[] args) {\n        String[] words = {"Java", "Python", "Java", "Go", "Java", "Python", "Rust"};\n        \n        Map<String, Integer> wordFrequency = new HashMap<>();\n\n        // 遍历数组，进行词频统计\n        for (String word : words) {\n            // getOrDefault是Java 8+的优雅写法，避免了繁琐的null判断\n            // 如果word已存在，获取其当前计数值；如果不存在，返回默认值0。\n            // 然后将计数值+1，再存入Map。\n            wordFrequency.put(word, wordFrequency.getOrDefault(word, 0) + 1);\n        }\n\n        System.out.println("各单词出现频率:");\n        wordFrequency.forEach((word, count) -> \n            System.out.println("\'" + word + "\': " + count + "次")\n        );\n    }\n}\n/*\n输出:\n各单词出现频率:\n\'Java\': 3次\n\'Python\': 2次\n\'Rust\': 1次\n\'Go\': 1次\n*/\n```\n\n\n\n##### **`TreeMap` 简介**\n\n###### **核心用途与场景**\n\n当你需要一个键（Key）时刻**保持排序状态**的`Map`时，`TreeMap`是你的不二之选。\n\n  * **最佳场景**：\n      * 需要按键的自然顺序或自定义顺序遍历`Map`。\n      * 需要对`Map`的键进行范围查找，如“查找所有ID在100到200之间的用户”。\n\n###### **类型介绍与排序原理**\n\n`TreeMap`底层基于**红黑树**实现。排序规则与`TreeSet`完全相同，依赖于键的`Comparable`接口（自然排序）或在构造时传入的`Comparator`（定制排序）。\n\n###### **代码示例详解**\n\n```java\npackage com.example;\nimport java.util.Comparator;\nimport java.util.TreeMap;\n\npublic class Main {\n    public static void main(String[] args) {\n        // 创建时传入Comparator，使key（字符串）按长度排序\n        TreeMap<String, String> mapByLength = new TreeMap<>(Comparator.comparingInt(String::length));\n        mapByLength.put("Java", "is a language");\n        mapByLength.put("Go", "is fast");\n        mapByLength.put("Python", "is simple");\n        // 遍历时，输出会按key的长度排序\n        System.out.println(mapByLength); // {Go=is fast, Java=is a language, Python=is simple}\n    }\n}\n```\n\n##### **[面试题] `HashMap` vs `Hashtable` vs `ConcurrentHashMap`**\n\n这是其余的两个Map与最常用的HashMap作为对比\n\n| 特性       | `HashMap`                     | `Hashtable` (已不推荐使用)                       | `ConcurrentHashMap` (推荐)                 |\n| :--------- | :---------------------------- | :----------------------------------------------- | :----------------------------------------- |\n| **线程安全** | **非线程安全** | **线程安全** (对整个表加`synchronized`锁)          | **线程安全** (分段锁/CAS，性能远超Hashtable) |\n| **null支持** | **允许** key和value为`null`     | **不允许** key和value为`null` (会抛`NullPointerException`) | **不允许** key和value为`null`                |\n| **性能** | 最高（单线程）                | 最低（锁竞争激烈）                               | 高（并发环境）                             |\n| **推荐用法** | 单线程环境下的首选。          | **不推荐使用**，是过时的历史遗留类。               | **并发环境**下的首选。                       |\n\n\n\n\n-----\n\n### **2.2 运算符详解**\n\n本节将直接深入运算符的核心与难点，剔除基础部分。我们将聚焦于位运算符的强大能力、逻辑与自增/自减运算的常见陷阱、优先级的避坑指南，以及Java新版本带来的语法糖，但在这之前，要深入理解位运算符，我们首先需要回归计算机的“母语”——二进制，并掌握不同进制间的转换，以及计算机内部表示数字（尤其是负数）的精妙方式：补码。\n\n#### **2.2.0 计算机基础：进制与编码**\n\n要深入理解位运算符，我们首先需要回归计算机的“母语”——二进制，并掌握不同进制间的转换，以及计算机内部表示数字（尤其是负数）的精妙方式：补码。\n\n#####  常见进制介绍\n\n在日常生活中我们使用十进制，但在计算机科学中，我们必须熟悉以下几种进制：\n\n  * **二进制 (Binary)**：基数为2，由 `0` 和 `1` 组成。是计算机物理层面的通用语言。在Java中以 `0b` 或 `0B` 开头，如 `0b1011`。\n  * **八进制 (Octal)**：基数为8，由 `0-7` 组成。在Java中以 `0` 开头，如 `055`。\n  * **十进制 (Decimal)**：基数为10，由 `0-9` 组成。我们最熟悉的进制。\n  * **十六进制 (Hexadecimal)**：基数为16，由 `0-9` 和 `A-F` (代表10-15) 组成。常用于表示内存地址、颜色值等。在Java中以 `0x` 或 `0X` 开头，如 `0x2D`。\n\n\n\n##### **进制转换核心方法**\n\n###### **十进制转二进制（除2取余法）**\n\n  * **手算方法**：将十进制数连续除以2，直到商为0，然后将每步得到的余数**逆序**排列。\n\n  * **手算示例**：将十进制 **45** 转换为二进制。\n    ```bash\n    45 ÷ 2 = 22 ... 余 1\n    22 ÷ 2 = 11 ... 余 0\n    11 ÷ 2 = 5  ... 余 1\n    5  ÷ 2 = 2  ... 余 1\n    2  ÷ 2 = 1  ... 余 0\n    1  ÷ 2 = 0  ... 余 1\n    ```\n    **结果**：从下往上倒序取余数，得到 `101101`。\n\n\n\n----\n\n###### **二进制转十进制（按权展开法）**\n\n**手算方法**：**权重法**（8421 法则）\n\n从右到左写出每位的权重：$2^0, 2^1, 2^2, \\dots$\n\n取出二进制中为 `1` 的权重，累加即可。\n\n**手算示例**： 将 **101101** 转换为十进制：\n\n```bash\n 32   16    8    4    2    1   (权重)\n  1    0    1    1    0    1\n32 + 8 + 4 + 1 = 45\n```\n\n\n\n#### **2.2.3 [计算机基础] 原码、反码、补码**\n\n##### **为何需要补码？**\n\n计算机硬件层面，只有加法器。为了简化电路设计，希望将减法运算也统一为加法运算。例如 `5 - 3` 希望能变成 `5 + (-3)`。原码无法满足这个要求，而**补码**巧妙地解决了这个问题，并统一了`+0`和`-0`的表示。\n\n##### **正数与负数的编码表示**\n\n  * **正数**：**原码、反码、补码都相同**。\n\n  * **负数**：\n      * **原码 **：最高位为符号位（1代表负），其余位是其绝对值的二进制。\n      \n      ![image-20250708201248831](https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250708201248831.png)\n      \n      例如我们之前计算的**`45`**的源码就为`00101101`,不足八位则不足，若是**`-45`**则为`10101101`\n      \n      * **反码：在原码的基础上**若为正数，则反码不变，若为负数，则符号位不变，其他全部取反（0变1）\n      \n      例如`-45`的反码就是`01010010`\n      \n      * **补码 ：在反码的基础上，**末位加1。\n      \n      例如`-45`的补码就是`01010011`\n      \n      \n\n**计算机内存中，所有整数都以补码的形式存储。**\n\n##### **补码的计算过程**\n\n  * **示例**：计算 `-5` 在一个 `byte`（8位）中的补码。\n    1.  先求 `+5` 的原码：`0000 0101`\n    2.  求 `-5` 的原码（符号位变1）：`1000 0101`\n    3.  求 `-5` 的反码（符号位不变，其余取反）：`1111 1010`\n    4.  求 `-5` 的补码（反码加1）：`1111 1011`\n        所以，`-5` 在内存中存储的就是 `1111 1011`。\n\n\n\n#### **2.2.1 [面试高频] 位运算符深度剖析**\n\n位运算符直接在整数的二进制位（bit）上进行操作，不关心其十进制值。它们之所以在面试和高性能场景中备受青睐，主要源于两大优势：**极致的运行效率**（因为更接近硬件操作）和**高效的空间利用**（例如用一个`int`存储32个开关状态），现代JVM的JIT（即时编译器）已经非常智能，可能会自动将 `x * 2` 这样的代码优化为 `x << 1`。但在一些对性能要求极为苛刻的场景，或者在阅读一些经典框架（如`ArrayList`、`HashMap`）的源码时，你会发现它们的身影\n\n```java\n// ArrayList.java 源码片段\nint oldCapacity = ...;\nint newCapacity = oldCapacity + (oldCapacity >> 1); // 这里的 oldCapacity >> 1 就是 oldCapacity / 2\n```\n\n##### **核心对比：`>>` (算术右移) vs. `>>>` (逻辑右移)**\n\n这个区别是面试中的经典考点，它**仅在处理负数时有所不同**。\n\n  * **`>>` (带符号右移 / 算术右移)**：进行右移操作时，空出的高位会用原始数字的**符号位**来填充。如果原数是正数（符号位为0），则高位补0；如果原数是负数（符号位为1），则高位补1。这样做的目的是**保持数字的正负性质不变**。\n  * **`>>>` (无符号右移 / 逻辑右移)**：进行右移操作时，无论原始数字是正数还是负数，空出的高位**一律用0填充**。这意味着，对一个负数进行无符号右移后，其结果会变成一个非常大的正数。\n\n##### **代码示例：对比 `>>` 和 `>>>`**\n\n```java\npackage com.example;\n\npublic class Main {\n    public static void main(String[] args) {\n        int negativeNum = -8; // 32位二进制补码: 11111111 11111111 11111111 11111000\n        // 调用 formatBinary 方法格式化二进制字符串\n        String formattedBinary = formatBinary(Integer.toBinaryString(negativeNum));\n        System.out.println("负数 -8 的二进制: " + formattedBinary);\n\n        // 带符号右移，高位补1，保持负数性质\n        int signedShiftResult = negativeNum >> 2;\n        System.out.println("负数 -8 >> 2: " + formatBinary(Integer.toBinaryString(signedShiftResult))); // 结果得到: -2\n        // -2 的32位二进制补码: 11111111 11111111 11111111 11111110\n\n        // 无符号右移, 高位补0\n        int unsignedShiftResult = negativeNum >>> 2;\n        System.out.println("负数 -8 >>> 2: " + formatBinary(Integer.toBinaryString(unsignedShiftResult))); // 结果得到: 1073741822\n        System.out.println(unsignedShiftResult);\n    }\n\n    /**\n     * 将二进制字符串按每 8 位分割，并用空格连接\n     * @param binary 原始二进制字符串\n     * @return 格式化后的二进制字符串\n     */\n    private static String formatBinary(String binary) {\n        StringBuilder sb = new StringBuilder();\n        // 从字符串末尾开始，每 8 位添加一个空格\n        for (int i = binary.length(); i > 0; i -= 8) {\n            sb.insert(0, binary.substring(Math.max(i - 8, 0), i));\n            if (i > 8) {\n                sb.insert(0, " ");\n            }\n        }\n        return sb.toString();\n    }\n}\n```\n\n---\n\n**第一步：确定-8的32位二进制补码表示**\n\n计算机不会直接处理 `-8` 这个符号，而是处理它的二进制补码。\n\n1.  **求 `+8` 的原码**：\n    在一个32位的`int`中，`+8`的二进制表示非常简单：\n    `00000000 00000000 00000000 00001000`\n\n2.  **求 `-8` 的反码**：\n    在`+8`原码的基础上，符号位（最高位）变为`1`，其余位按位取反（0变1，1变0）。\n    `11111111 11111111 11111111 11110111`\n\n3.  **求 `-8` 的补码**：\n    将反码加1，就得到了 `-8` 在内存中实际存储的形式。\n    `11111111 11111111 11111111 11111000`\n    这就是我们操作的起始状态。\n\n**第二步：执行带符号右移 `>> 2`**\n\n操作 `negativeNum >> 2` 意味着将 `-8` 的补码向右移动两位。\n\n* **原始补码**:\n    `11111111 11111111 11111111 11111000`\n\n* **向右移动两位**:\n    所有的32位都向右平移2个位置，最右边的两位 `00` 被丢弃。左边空出了两个位置。\n\n    `??111111 11111111 11111111 11111111 111110`\n\n* **填充高位**:\n    因为是 `>>` (带符号右移)，所以空出的高位会用**原始的符号位**来填充。`-8` 的符号位是 `1`，所以用 `1` 来填充。\n\n    `11111111 11111111 11111111 11111110`\n\n现在，我们就得到了右移操作后的二进制结果。\n\n**第三步：将结果转换回十进制**\n\n我们需要将这个新的补码 `11111111 11111111 11111111 11111110` 转换回我们能理解的十进制数，以验证它就是 `-2`。\n\n1.  **观察符号位**：\n    最高位是 `1`，说明这是一个负数。\n2.  **求其反码**（补码减1）：\n    `11111111 11111111 11111111 11111101`\n3.  **求其原码**（符号位不变，其余位取反）：\n    `10000000 00000000 00000000 00000010`\n4.  **读取数值**：\n    这个原码表示的数值就是 `-2`。\n\n---\n\n##### **实战场景与代码详解**\n\n###### **场景一：高效运算**\n\n  * **判断奇偶数**：`n & 1`比`n % 2`效率更高。因为任何整数的二进制表示中，最低位是1则为奇数，是0则为偶数。\n    \n    ```java\n    package com.example;\n    \n    public class Main {\n        public static void main(String[] args) {\n            // num & 1 用于检查 num 的二进制最低位是否为 1，若不为1则返回0\n            int num1 = 100; // 偶数\n            if ((num1 & 1) == 0) {\n                System.out.println(num1 + " 是偶数。");\n            }\n        }\n    }\n    ```\n    \n  * **代替乘除2的幂运算**：`n << x` 相当于 `n * 2^x`，`n >> x` 相当于 `n / 2^x`。\n    \n    ```java\n    package com.example;\n    public class Main {\n        public static void main(String[] args) {\n            int num = 10;\n            // 10 * 8 (2^3)\n            int multiplied = num << 3;\n            System.out.println("10 * 8 = " + multiplied); // 输出: 80\n        }\n    }\n    ```\n\n\n\n\n\n#### **2.2.2 [避坑指南] 逻辑与自增/自减运算符陷阱**\n\n##### **短路逻辑 (`&&` 和 `||`)**\n\n`&&` (与) 和 `||` (或) 具有短路特性，这是面试和日常编码中必须注意的细节。\n\n  * **`&&` (短路与)**：如果第一个操作数为`false`，则**不会再执行**第二个操作数，直接判定整个表达式为`false`。\n  * **`||` (短路或)**：如果第一个操作数为`true`，则**不会再执行**第二个操作数，直接判定整个表达式为`true`。\n\n`&` 和 `|` 也可以用作逻辑运算符，但它们**不具备短路特性**，会执行所有操作数。\n\n----\n\n##### **`i++` vs. `++i`**\n\n  * `++i` (前自增)：**先自增，后取值**。表达式返回的是`i`加1之后的值。\n  * `i++` (后自增)：**先取值，后自增**。表达式返回的是`i`加1之前的原始值。\n\n###### **经典面试题：`i = i++`**\n\n```java\npublic class PostIncrementPuzzle {\n    public static void main(String[] args) {\n        int i = 0;\n        i = i++;\n        System.out.println("i 的最终值是: " + i);\n    }\n}\n```\n\n  * **结果与原理解析**：\n    > 输出结果是 **0**。\n    > **JVM底层执行步骤**：\n    > 1.  JVM将 `i` 的当前值（0）加载到一个临时变量区，我们称之为 `temp`。(`temp = 0`)\n    > 2.  `i` 自身的值加1，此时 `i` 变量变为1。\n    > 3.  `i++` 这个表达式返回的是**加1前**的原始值，即 `temp` 的值（0）。\n    > 4.  执行赋值操作 `i = ...`，将表达式的返回值（0）赋给 `i`。\n    >     最终，`i` 的值被重新覆盖为了0。\n\n#### **2.2.3 运算符优先级与核心建议**\n\n完全记住运算符优先级表是困难且不切实际的。我们仅需关注几个易错点，并养成一个好习惯。\n\n  * **易错点1**：位运算符的优先级低于关系运算符。如 `(permissions & MASK) == MASK`，括号必不可少。\n  * **易错点2**：`&&` 的优先级高于 `||`。如 `a || b && c` 等价于 `a || (b && c)`。\n\n##### **核心开发建议：不要依赖隐式优先级**\n\n> **代码首先是写给人看的，其次才是给机器执行的。**\n\n在任何可能产生歧义的复杂表达式中，请毫不犹豫地使用圆括号 `()` 来明确指定运算顺序。这不仅能100%避免由优先级问题导致的、难以察V觉的BUG，更能极大地提升代码的可读性和可维护性。\n\n\n\n-----\n\n### **2.3 [深度] 循环与异常处理进阶**\n\n本章将绕开基础的`for`/`while`循环和`if`判断的语法，直接深入探讨程序流程控制的“内功”——循环的底层机制、现代化的语法演进，以及构建健壮、可靠程序的基石：Java的异常处理框架。\n\n#### **2.3.1 [深度] `for-each`循环与`Iterator`迭代器原理**\n\n`for-each`循环（或称增强型for循环）是Java 5引入的语法糖，它极大地简化了对数组和集合的遍历。但要真正掌握它，必须理解其背后的`Iterator`机制。\n\n##### **`for-each`的底层真相**\n\n`for-each`循环并非一种新的循环结构，而是编译器为我们提供的便利。编译器在处理`for-each`循环时，会将其转换为不同的遍历方式：\n\n  * **对于数组**：它会被转换为一个传统的、带索引的`for`循环。\n  * **对于集合**：它会被转换为使用`Iterator`迭代器的`while`循环。这是理解所有相关问题的关键。\n\n##### **代码示例：`for-each`的编译后等价代码**\n\n```java\npackage com.example;\n\nimport java.util.ArrayList;\nimport java.util.Iterator;\nimport java.util.List;\n\npublic class Main {\n    public static void main(String[] args) {\n        List<String> list = new ArrayList<>();\n        list.add("A");\n        list.add("B");\n        list.add("C");\n\n        // 我们写的 for-each 循环\n        System.out.println("--- for-each 循环 ---");\n        for (String item : list) {\n            System.out.println(item);\n        }\n\n        // 编译器实际生成的代码（等价形式）\n        System.out.println("\\n--- 编译器生成的等价代码 ---");\n        Iterator<String> iterator = list.iterator();\n        while (iterator.hasNext()) {\n            String item = iterator.next();\n            System.out.println(item);\n        }\n    }\n}\n```\n\n##### **[面试高频] `Iterator` 与 `Fail-Fast` 机制**\n\n  * **`Iterator`接口**: 提供了统一的遍历集合的方式，其核心方法为`hasNext()`（检查是否有下一个元素）、`next()`（获取下一个元素并后移指针）和`remove()`（从集合中删除`next()`方法最后返回的那个元素）。\n  * **`Fail-Fast`（快速失败）机制**: 这是`ArrayList`等非并发集合的一个重要特性。在集合内部，有一个名为`modCount`的变量，记录着集合结构被修改（如add、remove）的次数。当创建`Iterator`时，迭代器会记下当时的`modCount`值。在迭代过程中，每次调用`iterator.next()`时，都会检查迭代器的记录值与集合当前的`modCount`是否一致。**如果不一致，说明在迭代期间，集合被外部（非迭代器自身）修改了，迭代器会立刻抛出`ConcurrentModificationException`**，以避免在数据不一致的状态下继续操作，这就是“快速失败”。\n\n##### **代码示例：触发`ConcurrentModificationException`与正确删除**\n\n在任何情况下，都**绝对不要**在`for-each`循环中直接调用集合的`remove()`或`add()`方法。这是非常危险且不可靠的编码方式。**唯一正确且安全的方式是使用`Iterator`的`remove()`方法**。\n\n```java\npackage com.example;\n\nimport java.util.ArrayList;\nimport java.util.Iterator;\nimport java.util.List;\n\npublic class Main {\n    public static void main(String[] args) {\n        List<String> list = new ArrayList<>();\n        list.add("A");\n        list.add("B");\n        list.add("C");\n\n        // 错误的删除方式：在for-each循环中直接修改集合\n        try {\n            for (String item : list) {\n                if ("A".equals(item)) {\n                    list.remove(item); // 这会改变list的modCount，导致异常\n                }\n            }\n        } catch (java.util.ConcurrentModificationException e) {\n            System.out.println("错误演示：触发了 ConcurrentModificationException！");\n        }\n\n        // 正确的删除方式：使用迭代器自身的remove()方法\n        Iterator<String> iterator = list.iterator();\n        while (iterator.hasNext()) {\n            String item = iterator.next();\n            if ("A".equals(item)) {\n                iterator.remove(); // 这是唯一安全的方式\n            }\n        }\n        System.out.println("正确删除后的列表: " + list); // 输出：[B,C]\n    }\n}\n```\n\n#### **2.3.2 [进阶] 带标签的 `break` 和 `continue`**\n\n这是一个不常用但非常有用的语法，它解决了如何从**内层循环直接跳出外层循环**的问题。\n\n##### **代码示例：在二维数组中查找并跳出所有循环**\n\n```java\npackage com.example;\n\npublic class Main {\n    public static void main(String[] args) {\n        int[][] matrix = { {1, 2, 3}, {4, 5, 6}, {7, 8, 9} };\n        int target = 6;\n        boolean found = false;\n\n        // outerLoop就是一个标签\n        outerLoop: \n        for (int i = 0; i < matrix.length; i++) {\n            for (int j = 0; j < matrix[i].length; j++) {\n                if (matrix[i][j] == target) {\n                    System.out.println("找到了目标 " + target + " 在位置 (" + i + ", " + j + ")");\n                    found = true;\n                    // 使用带标签的break，直接跳出名为outerLoop的外层循环\n                    break outerLoop; \n                }\n            }\n        }\n\n        if (!found) {\n            System.out.println("未找到目标 " + target);\n        }\n    }\n}\n```\n\n#### **2.3.3 [核心] Java异常处理机制**\n\n##### **面试题引入**\n\n> “谈谈你对Java异常体系的理解。`Error`和`Exception`有什么区别？`Checked Exception`和`Unchecked Exception`呢？”\n\n##### **[底层] `Throwable` 家族：异常体系结构**\n\nJava中所有可抛出的东西都继承自`Throwable`类，它有两个重要的子类：`Error`和`Exception`。\n\n  * **`Error`**：代表了JVM本身无法恢复的严重内部错误，如`StackOverflowError`（栈溢出）、`OutOfMemoryError`（内存耗尽）。应用程序**不应该也无法**捕获或处理这类错误。\n  * **`Exception`**：代表了应用程序层面可以处理的异常情况。它又分为两大类：\n      * **`Checked Exception` (受检异常)**：继承自`Exception`但非`RuntimeException`的异常。编译器会**强制**开发者处理它们，必须使用`try-catch`捕获或在方法签名上用`throws`声明。它们通常代表了可预见的、可恢复的外部问题，如`IOException`、`SQLException`。\n      * **`Unchecked Exception` (非受检异常)**：即`RuntimeException`及其所有子类。编译器**不强制**处理它们。它们通常是由程序自身的逻辑错误（BUG）引起的，如`NullPointerException`、`IllegalArgumentException`、`ArrayIndexOutOfBoundsException`。\n\n![img](https://i-blog.csdnimg.cn/blog_migrate/14c709e083d859cca243d8a1052ad1c0.png)\n\n##### **[面试高频] `try-catch-finally` 的执行内幕**\n\n  * **面试题 1**：“`finally`块一定会执行吗？”\n\n    > **答**：绝大多数情况下是的。`finally`块的设立目的就是为了保证无论是否发生异常，某些清理代码（如关闭资源）都能得到执行。只有两种极端情况`finally`不会执行：\n    >\n    > 1. 在`try`或`catch`块中调用了`System.exit()`；\n    > 2. 2. JVM崩溃或线程被强制杀死。\n\n  * **面试题 2**：“如果`catch`块中有`return`语句，`finally`块会执行吗？”\n\n    > **答**：**会执行**。执行流程是：先执行`catch`块中的代码，当遇到`return`时，会先将要返回的值保存起来，然后去执行`finally`块，`finally`块执行完毕后，方法再带着之前保存的值返回。\n\n\n\n#### **2.3.4 [Java 7+] `try-with-resources` 最佳实践**\n\n##### **核心用途与场景**\n\n用于自动管理和关闭实现了`java.lang.AutoCloseable`或`java.io.Closeable`接口的资源，如文件流、数据库连接等，以防止资源泄漏。\n\n##### **代码示例：优雅地关闭资源**\n\n```java\npackage com.example;\n\nimport java.io.BufferedReader;\nimport java.io.FileReader;\nimport java.io.IOException;\n\npublic class Main {\n    public static void main(String[] args) {\n        // Java 7 之前的写法，需要在finally中手动关闭，繁琐且容易出错\n        BufferedReader br_old = null;\n        try {\n            br_old = new BufferedReader(new FileReader("file.txt"));\n            // ... 读取文件 ...\n        } catch (IOException e) {\n            e.printStackTrace();\n        } finally {\n            if (br_old != null) {\n                try {\n                    br_old.close();\n                } catch (IOException e) {\n                    e.printStackTrace();\n                }\n            }\n        }\n\n        // Java 7+ 的 try-with-resources 写法，简洁且安全\n        // try()括号内声明的资源，在try块结束时会自动关闭\n        try (BufferedReader br_new = new BufferedReader(new FileReader("file.txt"))) {\n            // ... 直接读取文件，无需关心关闭 ...\n            System.out.println(br_new.readLine());\n        } catch (IOException e) {\n            // 只需处理读取过程中的异常\n            System.out.println("文件读取失败: " + e.getMessage());\n        }\n    }\n}\n```\n\n**结论**：在处理任何可关闭的资源时，**永远优先使用 `try-with-resources`**，这是现代Java开发的标准实践。\n\n\n\n---'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#2-0-Java%E5%9F%BA%E7%A1%80"><span class="toc-number">1.</span> <span class="toc-text">2.0 Java基础</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B%E5%85%A8%E8%A7%A3"><span class="toc-number">1.1.</span> <span class="toc-text">2.1 数据类型全解</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-1-%E5%BC%95%E8%A8%80%EF%BC%9AJava%E6%95%B0%E6%8D%AE%E4%B8%96%E7%95%8C%E7%9A%84%E4%B8%A4%E5%A4%A7%E5%9F%BA%E7%9F%B3"><span class="toc-number">1.1.1.</span> <span class="toc-text">2.1.1 引言：Java数据世界的两大基石</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9F%BA%E6%9C%AC%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.1.1.1.</span> <span class="toc-text">基本数据类型</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BC%95%E7%94%A8%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.1.1.2.</span> <span class="toc-text">引用数据类型</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-2-%E5%9F%BA%E6%9C%AC%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.1.2.</span> <span class="toc-text">2.1.2 基本数据类型</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%95%B4%E6%95%B0%E5%AE%B6%E6%97%8F-byte-short-int-long"><span class="toc-number">1.1.2.1.</span> <span class="toc-text">整数家族 (byte, short, int, long)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B5%AE%E7%82%B9%E6%95%B0%E5%AE%B6%E6%97%8F-float-double"><span class="toc-number">1.1.2.2.</span> <span class="toc-text">浮点数家族 (float, double)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#char-%E5%AD%97%E7%AC%A6%E7%B1%BB%E5%9E%8B-%E4%B8%8E-boolean-%E5%B8%83%E5%B0%94%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.1.2.3.</span> <span class="toc-text">char (字符类型) 与 boolean (布尔类型)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-3-%E5%8C%85%E8%A3%85%E7%B1%BB"><span class="toc-number">1.1.3.</span> <span class="toc-text">2.1.3 包装类</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94"><span class="toc-number">1.1.3.1.</span> <span class="toc-text">核心用途</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#Integer-%E6%B7%B1%E5%BA%A6%E5%89%96%E6%9E%90"><span class="toc-number">1.1.3.2.</span> <span class="toc-text">Integer 深度剖析</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-4-%E5%AD%97%E7%AC%A6%E4%B8%B2%EF%BC%9AString"><span class="toc-number">1.1.4.</span> <span class="toc-text">2.1.4 字符串：String</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94-1"><span class="toc-number">1.1.4.1.</span> <span class="toc-text">核心用途</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E6%A0%B8%E5%BF%83%E9%9D%A2%E8%AF%95%E9%A2%98"><span class="toc-number">1.1.4.2.</span> <span class="toc-text">类型介绍与核心面试题</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-number">1.1.4.3.</span> <span class="toc-text">常用方法速查表</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.1.4.4.</span> <span class="toc-text">代码示例详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%85%B3%E8%81%94%E7%B1%BB%E5%9E%8B%EF%BC%9AStringBuilder%E4%B8%8EStringBuffer"><span class="toc-number">1.1.4.5.</span> <span class="toc-text">关联类型：StringBuilder与StringBuffer</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF"><span class="toc-number">1.1.4.5.1.</span> <span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E5%8E%9F%E7%90%86"><span class="toc-number">1.1.4.5.2.</span> <span class="toc-text">类型介绍与原理</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8-1"><span class="toc-number">1.1.4.5.3.</span> <span class="toc-text">常用方法速查表</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3-1"><span class="toc-number">1.1.4.5.4.</span> <span class="toc-text">代码示例详解</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98-%E4%BD%95%E6%97%B6%E4%BD%BF%E7%94%A8StringBuilder%E5%92%8CStringBuffer%EF%BC%9F"><span class="toc-number">1.1.4.5.5.</span> <span class="toc-text">[面试题] 何时使用StringBuilder和StringBuffer？</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-5-%E6%95%B0%E7%BB%84-Arrays-%E4%B8%8E%E5%85%B6%E5%B7%A5%E5%85%B7%E7%B1%BB"><span class="toc-number">1.1.5.</span> <span class="toc-text">2.1.5 数组 (Arrays) 与其工具类</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-1"><span class="toc-number">1.1.5.1.</span> <span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E5%88%9D%E5%A7%8B%E5%8C%96"><span class="toc-number">1.1.5.2.</span> <span class="toc-text">类型介绍与初始化</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#java-util-Arrays-%E6%A0%B8%E5%BF%83%E5%B7%A5%E5%85%B7%E6%96%B9%E6%B3%95%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.1.5.3.</span> <span class="toc-text">java.util.Arrays 核心工具方法详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3-2"><span class="toc-number">1.1.5.4.</span> <span class="toc-text">代码示例详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%8E%92%E5%BA%8F"><span class="toc-number">1.1.5.4.1.</span> <span class="toc-text">排序</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%9F%A5%E6%89%BE"><span class="toc-number">1.1.5.4.2.</span> <span class="toc-text">查找</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%AF%94%E8%BE%83"><span class="toc-number">1.1.5.4.3.</span> <span class="toc-text">比较</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%A4%8D%E5%88%B6"><span class="toc-number">1.1.5.4.4.</span> <span class="toc-text">复制</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%A1%AB%E5%85%85"><span class="toc-number">1.1.5.4.5.</span> <span class="toc-text">填充</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E8%BD%AC%E6%8D%A2"><span class="toc-number">1.1.5.4.6.</span> <span class="toc-text">转换</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%B5%81%E5%A4%84%E7%90%86"><span class="toc-number">1.1.5.4.7.</span> <span class="toc-text">流处理</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98-%E6%95%B0%E7%BB%84-Array-vs-%E5%88%97%E8%A1%A8-ArrayList"><span class="toc-number">1.1.5.5.</span> <span class="toc-text">[面试题] 数组 (Array) vs. 列表 (ArrayList)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-6-%E9%9B%86%E5%90%88%E6%A1%86%E6%9E%B6%EF%BC%9AList"><span class="toc-number">1.1.6.</span> <span class="toc-text">2.1.6 集合框架：List</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94-2"><span class="toc-number">1.1.6.1.</span> <span class="toc-text">核心用途</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#ArrayList-%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.1.6.2.</span> <span class="toc-text">ArrayList 详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#LinkedList-%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.1.6.3.</span> <span class="toc-text">LinkedList 详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98-ArrayList-vs-LinkedList-%E5%AF%B9%E6%AF%94"><span class="toc-number">1.1.6.4.</span> <span class="toc-text">[面试题] ArrayList vs LinkedList 对比</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-7-%E9%9B%86%E5%90%88%E6%A1%86%E6%9E%B6%EF%BC%9ASet"><span class="toc-number">1.1.7.</span> <span class="toc-text">2.1.7 集合框架：Set</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#HashSet-%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.1.7.1.</span> <span class="toc-text">HashSet 详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8-2"><span class="toc-number">1.1.7.1.1.</span> <span class="toc-text">常用方法速查表</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3-3"><span class="toc-number">1.1.7.1.2.</span> <span class="toc-text">代码示例详解</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#LinkedHashSet-%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.1.7.2.</span> <span class="toc-text">LinkedHashSet 详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-2"><span class="toc-number">1.1.7.2.1.</span> <span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E5%BA%95%E5%B1%82%E5%8E%9F%E7%90%86"><span class="toc-number">1.1.7.2.2.</span> <span class="toc-text">类型介绍与底层原理</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#TreeSet-%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.1.7.3.</span> <span class="toc-text">TreeSet 详解</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-3"><span class="toc-number">1.1.7.4.</span> <span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E6%8E%92%E5%BA%8F%E5%8E%9F%E7%90%86"><span class="toc-number">1.1.7.5.</span> <span class="toc-text">类型介绍与排序原理</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8-3"><span class="toc-number">1.1.7.6.</span> <span class="toc-text">常用方法速查表</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-1-8-%E9%9B%86%E5%90%88%E6%A1%86%E6%9E%B6%EF%BC%9AMap%EF%BC%88%E9%87%8D%E7%82%B9%EF%BC%89"><span class="toc-number">1.1.8.</span> <span class="toc-text">2.1.8 集合框架：Map（重点）</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#Map-%E6%8E%A5%E5%8F%A3%E6%A0%B8%E5%BF%83%E7%89%B9%E6%80%A7"><span class="toc-number">1.1.8.1.</span> <span class="toc-text">Map 接口核心特性</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#HashMap-%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E9%80%9F%E6%9F%A5%E8%A1%A8"><span class="toc-number">1.1.8.2.</span> <span class="toc-text">HashMap 核心方法速查表</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E6%93%8D%E4%BD%9C"><span class="toc-number">1.1.8.2.1.</span> <span class="toc-text">1. 核心操作</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E8%A7%86%E5%9B%BE%E6%93%8D%E4%BD%9C"><span class="toc-number">1.1.8.2.2.</span> <span class="toc-text">2. 视图操作</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E7%8A%B6%E6%80%81%E6%9F%A5%E8%AF%A2"><span class="toc-number">1.1.8.2.3.</span> <span class="toc-text">3. 状态查询</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#4-Java-8-%E5%A2%9E%E5%BC%BA%E6%96%B9%E6%B3%95"><span class="toc-number">1.1.8.2.4.</span> <span class="toc-text">4. Java 8+ 增强方法</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#5-%E6%89%B9%E9%87%8F%E6%93%8D%E4%BD%9C"><span class="toc-number">1.1.8.2.5.</span> <span class="toc-text">**5. 批量操作 **</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-4"><span class="toc-number">1.1.8.3.</span> <span class="toc-text">核心用途与场景</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E5%AE%9E%E7%8E%B0%E5%86%85%E5%AD%98%E7%BC%93%E5%AD%98"><span class="toc-number">1.1.8.3.1.</span> <span class="toc-text">场景一：实现内存缓存</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9A%E5%AD%98%E5%82%A8%E9%85%8D%E7%BD%AE%E4%BF%A1%E6%81%AF"><span class="toc-number">1.1.8.3.2.</span> <span class="toc-text">场景二：存储配置信息</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9A%E5%B0%86%E5%88%97%E8%A1%A8%E6%95%B0%E6%8D%AE%E8%BD%AC%E6%8D%A2%E4%B8%BA%E7%B4%A2%E5%BC%95"><span class="toc-number">1.1.8.3.3.</span> <span class="toc-text">场景三：将列表数据转换为索引</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E5%9B%9B%EF%BC%9A%E8%AE%A1%E6%95%B0%E7%BB%9F%E8%AE%A1"><span class="toc-number">1.1.8.3.4.</span> <span class="toc-text">场景四：计数统计</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#TreeMap-%E7%AE%80%E4%BB%8B"><span class="toc-number">1.1.8.4.</span> <span class="toc-text">TreeMap 简介</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-5"><span class="toc-number">1.1.8.4.1.</span> <span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%B1%BB%E5%9E%8B%E4%BB%8B%E7%BB%8D%E4%B8%8E%E6%8E%92%E5%BA%8F%E5%8E%9F%E7%90%86-1"><span class="toc-number">1.1.8.4.2.</span> <span class="toc-text">类型介绍与排序原理</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%E8%AF%A6%E8%A7%A3-4"><span class="toc-number">1.1.8.4.3.</span> <span class="toc-text">代码示例详解</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98-HashMap-vs-Hashtable-vs-ConcurrentHashMap"><span class="toc-number">1.1.8.5.</span> <span class="toc-text">[面试题] HashMap vs Hashtable vs ConcurrentHashMap</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-%E8%BF%90%E7%AE%97%E7%AC%A6%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.2.</span> <span class="toc-text">2.2 运算符详解</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-0-%E8%AE%A1%E7%AE%97%E6%9C%BA%E5%9F%BA%E7%A1%80%EF%BC%9A%E8%BF%9B%E5%88%B6%E4%B8%8E%E7%BC%96%E7%A0%81"><span class="toc-number">1.2.1.</span> <span class="toc-text">2.2.0 计算机基础：进制与编码</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%B8%B8%E8%A7%81%E8%BF%9B%E5%88%B6%E4%BB%8B%E7%BB%8D"><span class="toc-number">1.2.1.1.</span> <span class="toc-text">常见进制介绍</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%BF%9B%E5%88%B6%E8%BD%AC%E6%8D%A2%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95"><span class="toc-number">1.2.1.2.</span> <span class="toc-text">进制转换核心方法</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%8D%81%E8%BF%9B%E5%88%B6%E8%BD%AC%E4%BA%8C%E8%BF%9B%E5%88%B6%EF%BC%88%E9%99%A42%E5%8F%96%E4%BD%99%E6%B3%95%EF%BC%89"><span class="toc-number">1.2.1.2.1.</span> <span class="toc-text">十进制转二进制（除2取余法）</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E4%BA%8C%E8%BF%9B%E5%88%B6%E8%BD%AC%E5%8D%81%E8%BF%9B%E5%88%B6%EF%BC%88%E6%8C%89%E6%9D%83%E5%B1%95%E5%BC%80%E6%B3%95%EF%BC%89"><span class="toc-number">1.2.1.2.2.</span> <span class="toc-text">二进制转十进制（按权展开法）</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-3-%E8%AE%A1%E7%AE%97%E6%9C%BA%E5%9F%BA%E7%A1%80-%E5%8E%9F%E7%A0%81%E3%80%81%E5%8F%8D%E7%A0%81%E3%80%81%E8%A1%A5%E7%A0%81"><span class="toc-number">1.2.2.</span> <span class="toc-text">2.2.3 [计算机基础] 原码、反码、补码</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81%E8%A1%A5%E7%A0%81%EF%BC%9F"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">为何需要补码？</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%AD%A3%E6%95%B0%E4%B8%8E%E8%B4%9F%E6%95%B0%E7%9A%84%E7%BC%96%E7%A0%81%E8%A1%A8%E7%A4%BA"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">正数与负数的编码表示</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%A1%A5%E7%A0%81%E7%9A%84%E8%AE%A1%E7%AE%97%E8%BF%87%E7%A8%8B"><span class="toc-number">1.2.2.3.</span> <span class="toc-text">补码的计算过程</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-1-%E9%9D%A2%E8%AF%95%E9%AB%98%E9%A2%91-%E4%BD%8D%E8%BF%90%E7%AE%97%E7%AC%A6%E6%B7%B1%E5%BA%A6%E5%89%96%E6%9E%90"><span class="toc-number">1.2.3.</span> <span class="toc-text">2.2.1 [面试高频] 位运算符深度剖析</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E5%AF%B9%E6%AF%94%EF%BC%9A-%E7%AE%97%E6%9C%AF%E5%8F%B3%E7%A7%BB-vs-%E9%80%BB%E8%BE%91%E5%8F%B3%E7%A7%BB"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">核心对比：&gt;&gt; (算术右移) vs. &gt;&gt;&gt; (逻辑右移)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E5%AF%B9%E6%AF%94-%E5%92%8C"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">代码示例：对比 &gt;&gt; 和 &gt;&gt;&gt;</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%AE%9E%E6%88%98%E5%9C%BA%E6%99%AF%E4%B8%8E%E4%BB%A3%E7%A0%81%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.2.3.3.</span> <span class="toc-text">实战场景与代码详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9A%E9%AB%98%E6%95%88%E8%BF%90%E7%AE%97"><span class="toc-number">1.2.3.3.1.</span> <span class="toc-text">场景一：高效运算</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-2-%E9%81%BF%E5%9D%91%E6%8C%87%E5%8D%97-%E9%80%BB%E8%BE%91%E4%B8%8E%E8%87%AA%E5%A2%9E-%E8%87%AA%E5%87%8F%E8%BF%90%E7%AE%97%E7%AC%A6%E9%99%B7%E9%98%B1"><span class="toc-number">1.2.4.</span> <span class="toc-text">2.2.2 [避坑指南] 逻辑与自增/自减运算符陷阱</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%9F%AD%E8%B7%AF%E9%80%BB%E8%BE%91-%E5%92%8C"><span class="toc-number">1.2.4.1.</span> <span class="toc-text">短路逻辑 (&amp;&amp; 和 ||)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#i-vs-i"><span class="toc-number">1.2.4.2.</span> <span class="toc-text">i++ vs. ++i</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%BB%8F%E5%85%B8%E9%9D%A2%E8%AF%95%E9%A2%98%EF%BC%9Ai-i"><span class="toc-number">1.2.4.2.1.</span> <span class="toc-text">经典面试题：i = i++</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-2-3-%E8%BF%90%E7%AE%97%E7%AC%A6%E4%BC%98%E5%85%88%E7%BA%A7%E4%B8%8E%E6%A0%B8%E5%BF%83%E5%BB%BA%E8%AE%AE"><span class="toc-number">1.2.5.</span> <span class="toc-text">2.2.3 运算符优先级与核心建议</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E5%BC%80%E5%8F%91%E5%BB%BA%E8%AE%AE%EF%BC%9A%E4%B8%8D%E8%A6%81%E4%BE%9D%E8%B5%96%E9%9A%90%E5%BC%8F%E4%BC%98%E5%85%88%E7%BA%A7"><span class="toc-number">1.2.5.1.</span> <span class="toc-text">核心开发建议：不要依赖隐式优先级</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-3-%E6%B7%B1%E5%BA%A6-%E5%BE%AA%E7%8E%AF%E4%B8%8E%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E8%BF%9B%E9%98%B6"><span class="toc-number">1.3.</span> <span class="toc-text">2.3 [深度] 循环与异常处理进阶</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#2-3-1-%E6%B7%B1%E5%BA%A6-for-each%E5%BE%AA%E7%8E%AF%E4%B8%8EIterator%E8%BF%AD%E4%BB%A3%E5%99%A8%E5%8E%9F%E7%90%86"><span class="toc-number">1.3.1.</span> <span class="toc-text">2.3.1 [深度] for-each循环与Iterator迭代器原理</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#for-each%E7%9A%84%E5%BA%95%E5%B1%82%E7%9C%9F%E7%9B%B8"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">for-each的底层真相</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9Afor-each%E7%9A%84%E7%BC%96%E8%AF%91%E5%90%8E%E7%AD%89%E4%BB%B7%E4%BB%A3%E7%A0%81"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">代码示例：for-each的编译后等价代码</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%AB%98%E9%A2%91-Iterator-%E4%B8%8E-Fail-Fast-%E6%9C%BA%E5%88%B6"><span class="toc-number">1.3.1.3.</span> <span class="toc-text">[面试高频] Iterator 与 Fail-Fast 机制</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E8%A7%A6%E5%8F%91ConcurrentModificationException%E4%B8%8E%E6%AD%A3%E7%A1%AE%E5%88%A0%E9%99%A4"><span class="toc-number">1.3.1.4.</span> <span class="toc-text">代码示例：触发ConcurrentModificationException与正确删除</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-3-2-%E8%BF%9B%E9%98%B6-%E5%B8%A6%E6%A0%87%E7%AD%BE%E7%9A%84-break-%E5%92%8C-continue"><span class="toc-number">1.3.2.</span> <span class="toc-text">2.3.2 [进阶] 带标签的 break 和 continue</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E5%9C%A8%E4%BA%8C%E7%BB%B4%E6%95%B0%E7%BB%84%E4%B8%AD%E6%9F%A5%E6%89%BE%E5%B9%B6%E8%B7%B3%E5%87%BA%E6%89%80%E6%9C%89%E5%BE%AA%E7%8E%AF"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">代码示例：在二维数组中查找并跳出所有循环</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-3-3-%E6%A0%B8%E5%BF%83-Java%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E6%9C%BA%E5%88%B6"><span class="toc-number">1.3.3.</span> <span class="toc-text">2.3.3 [核心] Java异常处理机制</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%A2%98%E5%BC%95%E5%85%A5"><span class="toc-number">1.3.3.1.</span> <span class="toc-text">面试题引入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%95%E5%B1%82-Throwable-%E5%AE%B6%E6%97%8F%EF%BC%9A%E5%BC%82%E5%B8%B8%E4%BD%93%E7%B3%BB%E7%BB%93%E6%9E%84"><span class="toc-number">1.3.3.2.</span> <span class="toc-text">[底层] Throwable 家族：异常体系结构</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E9%9D%A2%E8%AF%95%E9%AB%98%E9%A2%91-try-catch-finally-%E7%9A%84%E6%89%A7%E8%A1%8C%E5%86%85%E5%B9%95"><span class="toc-number">1.3.3.3.</span> <span class="toc-text">[面试高频] try-catch-finally 的执行内幕</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-3-4-Java-7-try-with-resources-%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5"><span class="toc-number">1.3.4.</span> <span class="toc-text">2.3.4 [Java 7+] try-with-resources 最佳实践</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%A0%B8%E5%BF%83%E7%94%A8%E9%80%94%E4%B8%8E%E5%9C%BA%E6%99%AF-6"><span class="toc-number">1.3.4.1.</span> <span class="toc-text">核心用途与场景</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B%EF%BC%9A%E4%BC%98%E9%9B%85%E5%9C%B0%E5%85%B3%E9%97%AD%E8%B5%84%E6%BA%90"><span class="toc-number">1.3.4.2.</span> <span class="toc-text">代码示例：优雅地关闭资源</span></a></li></ol></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>