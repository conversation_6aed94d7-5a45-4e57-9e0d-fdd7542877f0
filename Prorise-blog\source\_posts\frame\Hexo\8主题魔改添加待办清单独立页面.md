---
title: 8.主题魔改：添加“待办清单”独立页面
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 50416
date: 2025-07-11 00:13:45
---

### **8.主题魔改：添加“待办清单”独立页面**

###### **前言：功能介绍与重要提示**

本指南将引导您为博客添加一个独立的“待办清单”页面。这个页面采用双栏布局，可以清晰地分类和展示您的各种待办事项。

> **警告：** 这是一项涉及多个文件修改的“魔改”操作。在开始前，**强烈建议您备份整个 `themes/anzhiyu` 文件夹**，以便在出现问题时可以随时恢复。

---
###### **第一步：创建并编辑您的待办事项数据**

我们首先来准备清单页面的“内容数据”。

1.  **创建数据文件 (`todolist.yml`)**
  
* 在您博客根目录的 `source/_data/` 文件夹内，新建一个名为 `todolist.yml` 的文件。如果 `_data` 文件夹不存在，请手动创建。
  
2.  **编辑 `todolist.yml` 文件**
    * 将下面的**模板**完整复制到 `todolist.yml` 文件中，然后**根据注释修改为您自己的待办事项**。

    ```yaml
    # seat: 控制该分类显示在左栏(left)还是右栏(right)
    # completed: 控制该事项是否已完成 (true 表示已完成，会显示对勾和删除线)

    - class_name: "学习计划"
      seat: left
      todo_list:
        - content: "深入学习 Vue 3 组合式 API"
          completed: false
        - content: "完成一个完整的 Node.js 项目"
          completed: true

    - class_name: "想看的书"
      seat: left
      todo_list:
        - content: "《代码整洁之道》"
          completed: true
        - content: "《你不知道的JavaScript》"
          completed: false

    - class_name: "想去的地方"
      seat: right
      todo_list:
        - content: "日本，京都"
          completed: false
        - content: "中国，新疆"
          completed: false
    
    - class_name: "想买的东西"
      seat: right
      todo_list:
        - content: "一把舒适的人体工学椅"
          completed: false
        - content: "降噪耳机"
          completed: true
    ```

---
###### **第二步：创建新的页面布局与样式**

现在，我们需要为这个新页面类型创建专属的模板和CSS样式。

1.  **创建 Pug 模板文件**
    * **文件路径**：`themes/anzhiyu/layout/includes/page/todolist.pug`
    * **文件内容**（请将以下代码完整复制进去）：
    ```pug
    #todolist-box
        - let todo_background = page.top_background
        .author-content.author-content-item.todolist.single(style=`${todo_background ? `background: url(${todo_background}) top / cover no-repeat;` : ""}`)
            .card-content
                .author-content-item-tips Todo
                span.author-content-item-title 待办清单
                .content-bottom
                .tips 耽误太多时间，事情可就做不完了
                .banner-button-group
                  a.banner-button(onclick='pjax.loadUrl("/about/")')
                    i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.5rem')
                    span.banner-button-text 我的更多
        #todolist-main
            #todolist-left
                each i in site.data.todolist
                    if i.seat == 'left'
                        .todolist-item
                            h3.todolist-title=i.class_name
                            ul.todolist-ul
                                each item in i.todo_list
                                     - var listItemClass = item.completed ? 'todolist-li-done' : 'todolist-li'
                                     li(class=listItemClass)
                                        if item.completed
                                            i.fa-regular.fa-circle-check
                                        else 
                                            i.fa-regular.fa-circle
                                        span=item.content
            #todolist-right
                each i in site.data.todolist
                    if i.seat == 'right'
                        .todolist-item
                            h3.todolist-title=i.class_name
                            ul.todolist-ul
                                each item in i.todo_list
                                     - var listItemClass = item.completed ? 'todolist-li-done' : 'todolist-li'
                                     li(class=listItemClass)
                                        if item.completed
                                            i.fa-regular.fa-circle-check
                                        else 
                                            i.fa-regular.fa-circle
                                        span=item.content
    ```

2.  **创建 CSS 样式文件**
    * **文件路径**： `themes/anzhiyu/source/custom/css/todolist.css`
    * **文件内容**（请将以下代码完整复制进去）：
    ```css
    body[data-type="todolist"] #web_bg {
      background: var(--anzhiyu-background);
    }
    body[data-type="todolist"] #page {
      border: 0;
      -webkit-box-shadow: none !important;
      box-shadow: none !important;
      padding: 0 !important;
      background: 0 0 !important;
    }
    body[data-type="todolist"] #page .page-title {
      display: none;
    }
    /* Todolist */
    #todolist-box{
      margin: 0 10px;
    }
    #todolist-main{
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;
      margin: 16px 0 10px;
    }
    #todolist-main li{
      list-style:none;
      font-size: 17px;
    }
    #todolist-main ul{
      margin: 0;
      padding: 0;
    }
    #todolist-left{
      width: 50%;
      padding: 0 8px 0 0;
    }
    #todolist-right{
      width: 50%;
      padding: 0 0 0 8px;
    }
    .todolist-item{
      position: relative;
      background: #fae4df;
      border-radius: 12px;
      padding: 10px 1rem 1.2rem;
      border: 2px dashed #f7a796;
      margin-bottom: 1rem;
    }
    [data-theme=dark]
    .todolist-item{
      background: #242424;
      border: 2px dashed #51908b;
    }
    li.todolist-li i{
      margin-left: 10px;
    }
    h3.todolist-title{
      margin: 0!important;
      border-bottom: var(--todo-border);
    }
    li.todolist-li{
      border-bottom: var(--todo-border);
      font-weight: normal;
      margin-left: -10px;
    }
    li.todolist-li-done{
      border-bottom: var(--todo-border);
      font-weight: normal;
      text-decoration: line-through;
    }
    .todolist-li span{
      margin-left: 5px;
    }
    .todolist-li-done span{
      margin-left: 5px;
    }
    @media screen and (max-width:700px){
      #todolist-left,#todolist-right{
          width: 100%;
          padding: 0;
      }
    }
    ```

---
###### **第三步：让主题识别“待办清单”页面类型**

1.  **打开主布局文件**：`themes/anzhiyu/layout/page.pug`
2.  **添加 `when 'todolist'`**：在 `case page.type` 的逻辑判断中，添加一个新的分支，告诉主题当遇到 `type` 为 `todolist` 的页面时，应该使用我们刚刚创建的 `todolist.pug` 模板来渲染。

    **修改指引：**
    ```diff
          when 'music'
            include includes/page/music.pug
    +     when 'todolist'
    +       include includes/page/todolist.pug
          default
            include includes/page/default-page.pug
    ```
    *(您只需在 `when 'music'` 代码块的下方，添加那两行以 `+` 开头的代码即可)*

---
###### **第四步：创建并配置“待办清单”页面本身**

1.  **执行创建页面命令**
    * （如果之前没创建过）在终端运行：
    ```bash
    hexo new page todolist
    ```
2.  **修改页面 Front-matter**
  
    * 打开 `source/todolist/index.md` 文件。
    * 确保其 `type` 为 `"todolist"`。
    ```yaml
    ---
    title: 待办清单
    date: 2025-06-14 12:00:00
    type: "todolist"
    top_background: "/img/background.png" # 可选，页面顶部背景图
    comments: false
    aside: false
    ---
    ```

---
###### **第五步：在主题配置中注入新样式**
1.  **打开主题配置文件** (`themes/anzhiyu/_config.yml`)。
2.  **找到 `inject:` 配置项**，在 `head:` 列表中添加我们新建的CSS文件。

```yaml
inject:
  head:
    # - 其他 head 内容...
    - '<link rel="stylesheet" href="/custom/css/todolist.css">'
```

3**.菜单项更新**

```yaml
  个人:
    # 友人帐: /link/ || anzhiyu-icon-link
    # 朋友圈: /fcircle/ || anzhiyu-icon-artstation
    留言板: /comments/ || anzhiyu-icon-envelope
    代办清单: /todolist/ || fas fa-check-double
```




---