- const highlight = theme.aside.tags.highlight_list || []
.card-tag-cloud
    each tag in site.tags.find({ parent: { $exists: false } }).data
        if highlight.includes(tag.name)
            a.highlight(href=url_for(tag.path))= tag.name
                sup= tag.length
        else
            a(href=url_for(tag.path))= tag.name
                sup= tag.length
if site.tags.length >= theme.aside.tags.limit
    span#more-tags-btn(onclick="sco.openAllTags()")= __('aside.tagmore')
else
    style.
        .card-tag-cloud::after {
            display: none !important;
        }