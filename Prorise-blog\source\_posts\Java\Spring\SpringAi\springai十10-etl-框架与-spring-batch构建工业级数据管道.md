---
title: SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道
categories:
  - 后端技术
  - Java
tags:
  - Java微服务篇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp'
comments: true
toc: true
ai: false
abbrlink: 64777
date: 2025-03-21 19:13:45
---

## 10\. ETL 框架与 Spring Batch：构建工业级数据管道

在第九章，我们通过“手动播种”数据，成功搭建了一个 RAG 问答原型，验证了其核心流程。然而，一个生产级的 RAG 应用，其成败往往取决于知识库的质量和数据更新的效率。所谓“垃圾进，垃圾出”，只有通过一个健壮、高效、可扩展的数据处理流水线，我们才能为 AI 提供源源不断的高质量“养料”。

本章，我们将深入探讨如何使用 Spring 生态中强大的批处理框架——**Spring Batch**，结合 Spring AI 的 ETL 工具，来构建一个真正企业级的数据摄取（Ingestion）流水线，彻底替换掉第九章中仅适用于演示的 `DataSeedingService`。

### 10.1 从“一次性脚本”到“企业级作业”：为何选择 Spring Batch？

你可能会问，我们之前使用 `@PostConstruct` 在应用启动时加载数据不是挺好的吗？为什么需要引入 Spring Batch 这么“重”的框架？

对于简单的、一次性的数据加载任务，`@PostConstruct` 确实足够。但当我们面对生产环境的复杂需求时，它的局限性就暴露无遗了。

| 特性 | `@PostConstruct` (简单脚本方式) | Spring Batch (企业级作业框架) |
| :--- | :--- | :--- |
| **可重启性** | **不支持**。如果加载 1000 个文件，在第 500 个失败，则必须手动清理已入库的数据，然后从头开始。 | **核心特性**。自动记录每个文件的处理状态。任务失败后，可从失败的那个文件**断点续传**，无需重复处理已成功的部分。 |
| **事务管理** | **粗糙**。需要手动管理大事务，一旦失败，回滚成本高，数据一致性难以保证。 | **精细化**。提供基于块（Chunk）的事务管理，可以做到“处理一个文件，提交一次事务”，确保了数据处理的原子性。 |
| **扩展性** | **差**。默认单线程执行，面对海量数据时，处理速度成为瓶颈，难以水平或垂直扩展。 | **极强**。原生支持多线程步骤（`Multi-threaded Step`）和并行处理（`Parallel Steps`），可轻松扩展以处理海量数据。 |
| **可监控性** | **无**。执行过程像一个黑盒，无法得知进度、耗时、错误详情等。 | **全面**。提供了一套完整的元数据表（`BATCH_JOB_INSTANCE`, `BATCH_STEP_EXECUTION` 等），详细记录每次作业执行的详情、状态、读写数量、耗时等。 |
| **调度与管理** | **原始**。需要自己结合 `@Scheduled` 等实现简单的定时任务。 | **专业**。可轻松与 Spring Scheduler 或企业级调度工具（如 Quartz, Control-M）集成，并能通过 API 进行启停、查询等管理操作。 |

简而言之，当我们的数据摄取任务需要**可靠性、可扩展性和可管理性**时，Spring Batch 就是不二之选。它将我们的 ETL 过程从一个临时的脚本，提升为了一个受管理的、生产级的企业作业。

### 10.2 Spring AI ETL 框架深度解析

在用 Spring Batch 编排任务之前，我们必须先深入了解流水线上的每一个“工具”——即 Spring AI 提供的 ETL 组件。

#### 10.2.1 API 概述与核心接口

ETL 管道负责创建、转换和存储 `Document` 实例。整个流程由三个核心接口定义：

| 接口 | 实现的函数式接口 | 核心职责 |
| :--- | :--- | :--- |
| `DocumentReader` | `Supplier<List<Document>>` | **提取 (Extract)**：作为数据源，提供原始的 `Document` 列表。 |
| `DocumentTransformer`| `Function<List<Document>, List<Document>>` | **转换 (Transform)**：接收一批文档，进行处理后，返回新的一批文档。 |
| `DocumentWriter` | `Consumer<List<Document>>` | **加载 (Load)**：消费一批文档，并将其写入最终目的地。 |

这三个接口的设计，使得我们可以用非常优雅的、链式调用的方式来构建一个简单的数据处理流。例如，一个典型的 PDF 处理流程可以这样表示：

```java
// 这是一个演示性的函数式调用链
// 1. pdfReader.get() -> 提取
// 2. tokenTextSplitter.apply(...) -> 转换
// 3. vectorStore.accept(...) -> 加载
vectorStore.accept(tokenTextSplitter.apply(pdfReader.get()));
```

接下来，我们将详细探索框架为我们提供的各种 `DocumentReader` 和 `DocumentTransformer` 实现。

#### 10.2.2 `DocumentReader` 详解 (数据提取)

##### **1. `JsonReader`**

用于处理 JSON 文件，能将 JSON 数组中的每个对象或整个 JSON 文件转换为 `Document`。

  * **示例数据 (`docs/bikes.json`)**:

    ```json
    [
      { "id": 1, "brand": "Trek", "model": "Marlin 5", "description": "一款适合入门级越野骑行的高性能山地车。" },
      { "id": 2, "brand": "Giant", "model": "TCR Advanced", "description": "为竞赛爱好者打造的空气动力学公路自行车。" }
    ]
    ```

  * **代码示例**:
    我们可以指定只使用 `description` 和 `brand` 字段来构成 `Document` 的文本内容。

    ```java
    @Slf4j
    @Component
    public class JsonReaderDemo {

        @Value("classpath:docs/bikes.json")
        private Resource jsonData;

        // @PostConstruct
        public void run() {
            log.info("--- 演示 JsonReader ---");
            // 只使用 "description" 和 "brand" 字段作为文本内容
            JsonReader jsonReader = new JsonReader(jsonData, "description", "brand");
            List<Document> documents = jsonReader.get();
            documents.forEach(doc -> log.info("读取到JSON文档: {}", doc.getText()));
        }
    }
    ```

  * **预期输出**:

    ```
    读取到JSON文档: {description=一款适合入门级越野骑行的高性能山地车。, brand=Trek}
    读取到JSON文档: {description=为竞赛爱好者打造的空气动力学公路自行车。, brand=Giant}
    ```

##### **2. `PagePdfDocumentReader` (常用)**

处理 PDF 文档的核心工具，基于 Apache PDFBox 库，它会将 PDF 的**每一页**解析成一个独立的 `Document` 对象，并自动附加页码等元数据。

  * **依赖 (`pom.xml`)**:

    ```xml
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-pdf-document-reader</artifactId>
    </dependency>
    ```

  * **代码示例**:

    ```java
    @Slf4j
    @Component
    public class PdfReaderDemo {

        @Value("classpath:docs/spring-ai-reference.pdf")
        private Resource pdfResource;

        // @PostConstruct
        public void run() {
            log.info("--- 演示 PagePdfDocumentReader ---");
            PagePdfDocumentReader pdfReader = new PagePdfDocumentReader(pdfResource);
            List<Document> documents = pdfReader.get();
            log.info("从 {} 中读取到 {} 页内容。", pdfResource.getFilename(), documents.size());
            if (!documents.isEmpty()) {
                // PDF Reader 会自动添加页码等元数据
                log.info("第一页的元数据: {}", documents.get(0).getMetadata());
            }
        }
    }
    ```

##### **3. `TikaDocumentReader` (通用)**

当需要处理 Word (`.docx`)、PPT (`.pptx`) 等多种 Office 文档格式时，`TikaDocumentReader` 是不二之选。它基于强大的 Apache Tika 库，能从上百种文件格式中提取纯文本内容。

  * **依赖 (`pom.xml`)**:

    ```xml
    <dependency>
        <groupId>org.springframework.ai</groupId>
        <artifactId>spring-ai-tika-document-reader</artifactId>
    </dependency>
    ```

  * **代码示例**:

    ```java
    @Slf4j
    @Component
    public class TikaReaderDemo {

        @Value("classpath:docs/rag-best-practices.docx")
        private Resource docxResource;

        // @PostConstruct
        public void run() {
            log.info("--- 演示 TikaDocumentReader ---");
            TikaDocumentReader tikaReader = new TikaDocumentReader(docxResource);
            List<Document> documents = tikaReader.get();
            log.info("从 {} 中成功提取内容。", docxResource.getFilename());
            documents.forEach(doc -> log.info("提取到的文本片段: {}...", doc.getText().substring(0, 50)));
        }
    }
    ```

#### 10.2.3 `DocumentTransformer` 详解 (数据转换)

这是整个 ETL 流程中技术含量最高、对最终 RAG 效果影响最大的一步。

##### **1. `TokenTextSplitter` (核心)**

其核心任务是**文档分割 (Splitting/Chunking)**，将长文档切分为符合 LLM 上下文窗口限制、同时又保持语义完整的文本块。

| 核心参数 | 作用与解释 | 最佳实践建议 |
| :--- | :--- | :--- |
| `defaultChunkSize` | **块大小**：每个文本块的目标 Token 数量。 | 从 `512` 开始实验。较小值（如256）使上下文更聚焦；较大值（如1024）保留更完整上下文。 |
| `minChunkSizeChars`| **最小块字符数**：防止产生过小的、无意义的碎片化文本块。 | 保持默认或根据文本特性微调。 |
| `chunkOverlap` | **块重叠**：相邻两个文本块之间重叠的 Token 数量。 | **至关重要**。设为 `chunkSize` 的 10%-20%（如 `chunkSize=512`, `chunkOverlap=64`）。它能确保一个完整的句子不会在块边界被切断。 |

  * **代码示例**:
    ```java
    // 假设 documents 是从 DocumentReader 读取到的列表

    // 1. 创建一个 TokenTextSplitter 实例
    TokenTextSplitter textSplitter = new TokenTextSplitter(
        512,  // 每个块的目标大小为 512 token
        100,  // 块的最小字符数
        64,   // 相邻块之间重叠 64 token
        10000,// 一个文档最多被分割成的块数
        true  // 保留分隔符（如换行符）
    );

    // 2. 应用分割器，它会智能地将长文档分割，并自动将元数据复制到所有分块中
    List<Document> splitDocuments = textSplitter.apply(documents);
    ```

##### **2. `SummaryMetadataEnricher` (增强)**

这是一个非常有用的“元数据增强器”。它能利用 `ChatModel` 为每个文档块**自动生成摘要**，并将摘要作为新的元数据（如 `section_summary`）添加回去。这可以用于构建更复杂的检索策略。

  * **代码示例**:
    ```java
    @Configuration
    class EnricherConfig {
        // 将 Enricher 配置为一个 Bean
        @Bean
        public SummaryMetadataEnricher summaryEnricher(ChatModel chatModel) {
            return new SummaryMetadataEnricher(chatModel, List.of(SummaryMetadataEnricher.SummaryType.CURRENT));
        }
    }

    @Component
    class MyTransformerDemo {
        @Autowired
        private SummaryMetadataEnricher summaryEnricher;

        public void run(List<Document> documents) {
            // 在分割之后，加载之前，对文档块进行摘要增强
            List<Document> enrichedDocuments = this.summaryEnricher.apply(documents);
        }
    }
    ```
    *`KeywordMetadataEnricher`* 与之类似，可以自动提取关键词作为元数据。

### 10.3 Spring Batch 核心概念入门

在了解了 ETL 的“工具”后，我们来学习如何使用 Spring Batch 这个“工厂生产线”来编排它们。

一个典型的 Spring Batch 作业（Job）由一个或多个步骤（Step）组成。最常见的步骤类型是\*\*面向块（Chunk-Oriented）\*\*的处理，它像一条精密的工厂流水线，完美地契合了我们的 ETL 流程：

1.  **`ItemReader` (读取器)**: 流水线的**起点**。它的职责是从数据源（如文件系统）中**读取**一个数据项（Item）。在我们的场景中，一个 Item 就是一个待处理的 PDF 文件资源 (`Resource`)。
2.  **`ItemProcessor` (处理器)**: 流水线的**加工站**。它接收 `ItemReader` 传来的单个数据项，对其进行任意复杂的**处理**和**转换**（如读取+分割），然后输出处理后的结果。
3.  **`ItemWriter` (写入器)**: 流水线的**终点**。它接收 `ItemProcessor` 输出的一“块”（Chunk）处理结果，并将它们**写入**目标系统（如 `VectorStore`）。

Spring Batch 会以“块”为单位，驱动数据从 Reader -\> Processor -\> Writer 流动，并在每个块处理完成后提交事务，极大地提升了效率和健壮性。

### 10.4 实战：使用 Spring Batch 构建生产级 ETL 作业

现在，让我们动手将第十章的 ETL 逻辑，重构为一个结构清晰、功能强大的 Spring Batch 作业。

#### 10.4.1 第一步：添加依赖与启用 Batch

  * **`pom.xml`**:

    ```xml
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-batch</artifactId>
    </dependency>
    ```

  * **启用 Batch 处理**:
    在主启动类上，添加 `@EnableBatchProcessing` 注解。

    ```java
    @SpringBootApplication
    @EnableBatchProcessing // 启用 Spring Batch 功能
    public class AiCopilotBackendApplication {
        // ...
    }
    ```

#### 10.4.2 第二步：定义作业配置 (`RagEtlBatchConfig.java`)

我们将创建一个专门的配置类，来定义和组装我们的作业。

```java
package com.copilot.aicopilotbackend.config.batch;

import org.springframework.ai.document.Document;
import org.springframework.ai.reader.pdf.PagePdfDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.support.ListItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.Arrays;
import java.util.List;

@Configuration
public class RagEtlBatchConfig {

    @Value("classpath:/docs/*.pdf")
    private Resource[] pdfResources;

    @Bean
    public ItemReader<Resource> pdfResourceReader() {
        return new ListItemReader<>(Arrays.asList(pdfResources));
    }

    @Bean
    public ItemProcessor<Resource, List<Document>> ragDocumentProcessor() {
        return resource -> {
            PagePdfDocumentReader pdfReader = new PagePdfDocumentReader(resource);
            List<Document> documents = pdfReader.get();
            TokenTextSplitter textSplitter = new TokenTextSplitter(); // 使用默认配置
            return textSplitter.apply(documents);
        };
    }

    @Bean
    public ItemWriter<List<Document>> vectorStoreWriter(VectorStore vectorStore) {
        return chunk -> chunk.getItems().forEach(vectorStore::add);
    }

    @Bean
    public Step ragEtlStep(JobRepository jobRepository, PlatformTransactionManager transactionManager,
                           ItemReader<Resource> pdfResourceReader,
                           ItemProcessor<Resource, List<Document>> ragDocumentProcessor,
                           ItemWriter<List<Document>> vectorStoreWriter) {
        return new StepBuilder("documentProcessingStep", jobRepository)
                .<Resource, List<Document>>chunk(1, transactionManager)
                .reader(pdfResourceReader)
                .processor(ragDocumentProcessor)
                .writer(vectorStoreWriter)
                .build();
    }

    @Bean
    public Job ragEtlJob(JobRepository jobRepository, Step ragEtlStep) {
        return new JobBuilder("ragEtlJob", jobRepository)
                .incrementer(new RunIdIncrementer())
                .flow(ragEtlStep)
                .end()
                .build();
    }
}
```

#### 10.4.3 第三步：创建 API 接口触发作业

我们创建一个 REST API 端点来按需、可控地触发它。

```java
package com.copilot.aicopilotbackend.controller;

// ... imports ...
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;

@Slf4j
@RestController
@RequestMapping("/api/v1/etl")
@RequiredArgsConstructor
public class EtlJobController {

    private final JobLauncher jobLauncher;
    private final Job ragEtlJob; // Spring 会根据 @Bean 的名称自动注入

    @PostMapping("/run")
    public String runRagEtlJob() {
        try {
            log.info("接收到 ETL 作业启动请求...");
            jobLauncher.run(ragEtlJob, new JobParametersBuilder()
                    .addDate("timestamp", new Date())
                    .toJobParameters());
            return "RAG ETL 作业已成功异步启动。";
        } catch (Exception e) {
            log.error("启动 RAG ETL 作业失败", e);
            return "作业启动失败: " + e.getMessage();
        }
    }
}
```

#### 10.4.4 测试与验证

1.  **清空数据**: 建议先通过 Navicat 或 `redis-cli` (`FLUSHDB` 命令) 清空 Redis 中的旧数据。
2.  **启动应用**并**准备PDF**：在 `src/main/resources/docs` 目录下放入一些PDF文件。
3.  **触发作业**：通过 Postman 向 `POST http://localhost:8080/api/v1/etl/run` 发送一个请求。
4.  **观察日志**: 你会在控制台看到 Spring Batch 的详细执行日志。
5.  **验证数据与问答**: 作业完成后，通过我们第九章的 RAG 问答接口，提出一个只有你上传的 PDF 中才有的问题，验证 AI-Copilot 是否能正确回答。





-----