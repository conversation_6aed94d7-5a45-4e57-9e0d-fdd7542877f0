---
title: 产品经理入门（四）：第四章：流程图与结构图
categories:
  - 产品经理
tags:
  - 产品经理教程
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp'
comments: true
toc: true
ai: true
abbrlink: 13237
date: 2025-07-20 19:13:45
---

# 第四章：流程图与结构图

在我看来，如果说需求文档是用文字来描述“做什么”和“为什么做”，那么流程图和结构图就是我用来清晰、无歧义地表达“怎么做”的**视觉语言**。

它们是我与设计师、工程师、测试，甚至是老板和业务方进行高效沟通，确保大家对产品理解一致的最重要的工具。掌握这两种图的绘制，是我们产品经理的基本功。

## 4.1 认识流程图

我们先从流程图开始。我用它来描述一个**动态的过程**，即一系列随时间先后发生的动作和决策。它回答的核心问题是：“接下来会发生什么？”。

### 4.1.1 流程图的定义与分类

![image-20250719215002454](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215002454.png)

#### 1. 流程图的定义

**流程图**，对我而言，就是一种**将一个复杂的做事过程，通过标准化的图形和箭头，进行可视化表达的图示**。它的最大价值，就是能把抽象的逻辑、繁琐的步骤，变得直观、清晰，让团队里的每一个人都能快速理解。

#### 2. 常见流程图类型

在我的日常工作中，根据我要沟通的对象和目的不同，我会绘制三种不同类型的流程图。混淆它们，常常是新手产品经理犯的错误。

* **业务流程图 (Business Flowchart)**
    ![image-20250719215053441](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215053441.png)

    我用它来描述一个**完整的、端到端的业务场景**，特别是当这个场景涉及到多个角色或系统交互时。它聚焦的是业务活动本身，而不是产品内部的具体功能。

    图中的“医院挂号”案例就是绝佳的示范。它清晰地展示了“病人”、“医院服务”、的流程，在项目初期，我会用这种图来和老板、业务方统一对整个商业模式的认知。

* **功能流程图 (Functional Flowchart)**
    ![image-20250719215234409](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215234409.png)

    我用它来详细说明**某一个具体功能内部的、严谨的逻辑**。它的粒度比业务流程图要细得多。

    我们来看这张“在线挂号”的流程图，它就是一个完美的例子。它描述的是“挂号”这**单个功能**内部的完整逻辑。从“选择科室”开始，到系统进行判断“当天是否已约满”，再到用户选择具体时间、确认就诊人，最后系统再次判断“是否符合科室要求”，直到最终“预约成功”或“提示约满”。

    它把所有可能的情况和分支都严谨地表达了出来。我就是用这种图，来和开发、测试工程师沟通一个功能的具体实现规则，确保没有遗漏任何用户场景和异常情况。
    
    
    
* **页面流程图 (Page Flowchart)**
    ![image-20250719215727998](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215727998.png)

    我用它来表达**用户在产品不同界面之间的跳转路径**。它关注的是用户为了完成一个任务，需要“从哪个页面”流转到“哪个页面”。

    图中的从“App首页”到“搜索结果页”再到“商品详情页”的流程，就是一个典型的页面流程图。我用它和UI/UX设计师合作，来保证整个产品的导航体验是顺畅、无断点的，确保用户不会在我们的产品里“迷路”。

---
为了方便我们记忆和区分，我将这三种流程图的核心特点总结在了一张表格里：

| **流程图类型** | **核心描述** | **我用它来回答什么问题？** | **主要沟通对象** |
| :--- | :--- | :--- | :--- |
| **业务流程图** | 描述完整的商业活动，涉及**多角色/系统**。 | “我们的整体业务是如何运转的？” | 老板、业务方、运营 |
| **功能流程图** | 描述**单个功能**的内部逻辑和异常处理。 | “这个功能内部是如何工作的？” | 开发、测试工程师 |
| **页面流程图** | 描述用户在**不同界面**间的跳转路径。 | “用户为了完成任务，需要经过哪些页面？” | UI/UX设计师、开发工程师 |




-----

## 4.2 流程图的绘制

对我来说，画流程图就像在用一种通用的视觉语言写作。要写好，我们得先掌握它的“基本词汇”（元素）和“核心句型”（结构）。

### 4.2.1 流程图常见元素

![image-20250719220137457](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220137457.png)

为了让流程图具有通用性，我始终坚持使用一套标准化的符号。这些符号就是构成流程图的“词汇”。

| **元素样式** | **元素名称** | **我的使用说明** |
| :--- | :--- | :--- |
|  | **开始/结束** | 我用它来明确标识一个流程的**起点**和所有可能的**终点**。一个流程只有一个“开始”，但可以有多个“结束”。 |
|  | **节点/处理** | 这是最常用的符号，代表一个具体的操作、动作或状态。比如“用户输入密码”、“系统保存数据”。 |
|  | **判定** | 代表一个需要做“是/否”或多分支**判断**的地方。菱形必须有至少两个出口，对应不同的判断结果。 |
|  | **子流程** | 当一个流程中的某个步骤本身又是一个复杂的流程时（比如“支付流程”），我用这个符号来表示，可以避免主流程图过于臃肿。 |
|  | **连接线** | 用来连接各个元素，表示流程的**走向**。箭头方向至关重要，我有时还会在连接线上标注文字，比如“是”或“否”。 |

### 4.2.2 流程图常见结构

掌握了基本符号后，我就用它们来组合成三种最基本的“句型”或“结构”。几乎所有复杂的流程，都可以通过这三种基本结构的嵌套和组合来表达。

#### 1\. 顺序结构

![image-20250719220305242](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220305242.png)

这是最简单的结构，表示一组操作**按照时间先后、从上到下地依次执行**，中间没有任何分支或重复。

图中的“发布新闻评论”流程就是一个典型的顺序结构。用户从`浏览新闻`，到`查看新闻详情`，再到`发布评论`，整个过程是一条直线走到底的（其中“是否已登录”是一个选择结构，我们下面会讲）。

#### 2\. 选择结构

这是用来表达“判断”和“分支”的结构。当流程走到某一步需要根据不同情况，走向不同路径时，我就用它。

  * **二元选择结构**
    ![image-20250719220457761](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220457761.png)

    这就是一个简单的“**二选一**”逻辑。流程在决策点上，根据条件“是”或“否”，走向两条不同的道路。

    图中“校验手机号”的例子很清晰：系统判断`手机号是否符合规范？`。如果“是”，流程就继续往下走到`获取验证码`；如果“否”，流程就走另一条路，回到`输入手机号`这一步，让用户重新输入。

  * **多元选择结构**
    ![image-20250719220534751](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220534751.png)

    当一个决策点可能产生**多于两个**的分支时，我就使用多元选择结构。

    图中的`用户选择登录方式`就是一个很好的例子。用户在这里可以做出三种选择，分别走向`手机号登录`、`账号密码登录`、`第三方登录`这三条完全不同的、并行的路径。

#### 3\. 循环结构

![image-20250719220609300](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220609300.png)

当流程中的某一个或几个步骤，需要**被重复执行**，直到某个条件满足为止时，我就使用循环结构。

图中“发送验证码”的例子非常经典：

1.  系统执行`发送验证码`操作。
2.  然后进入判断`是否发送成功？`。
3.  如果“否”，则执行`重新发送`，然后**流程线绕回去**，再次进入`是否发送成功？`的判断。
4.  这个“发送-判断-重发”的过程会一直循环，直到“是否发送成功？”的判断结果为“是”，流程才会跳出这个循环，继续执行下一步`输入验证码`。


---


### 4.2.3 流程图绘制工具

[此处放置“流程图绘制工具”的图片]

“工欲善其事，必先利其器”。虽然理论上用纸笔就能画流程图，但在实际工作中，我一定会使用专业的工具，因为它们更高效、更规范，也便于修改和分享。市面上的工具很多，我将几款主流工具的特点总结在了下面的表格里。

| 工具名称 | 核心特点 | 我推荐的使用场景 |
| :--- | :--- | :--- |
| `墨刀白板` | 国产在线一体化平台，集原型、设计、流程图于一体，协作功能强大，上手快。 | **强烈推荐新手使用**。尤其适合移动端产品团队，需要快速产出原型并进行协作评审的场景。 |
| **Axure RP 9** | 功能强大的专业原型工具，同时内置了流程图功能。 | 当你需要在一个工具里，同时完成高保真原型和详细流程图的绘制时，无缝衔接。 |
| **Visio** | 微软出品，功能全面，模板库强大，非常标准化。 | Windows环境下，需要绘制非常专业、复杂的企业级流程图或网络拓扑图等。 |
| **OmniGraffle** | Mac平台专属，界面精美，交互体验流畅。 | Mac重度用户，对绘图的视觉效果和体验有较高要求。 |
| **ProcessOn** | 国产在线协作绘图工具，专注于流程图、思维导图等。 | 需要多人实时共同编辑一份流程图，进行头脑风暴或在线评审的场景。 |
| **EdrawMax (亿图图示)** | 国产跨平台软件，内置海量模板和素材库。 | 希望快速套用模板，高效产出多种类型图表的用户。 |

**我的建议：**
对于新手，我通常推荐从 **墨刀 (MockingBot)** 这样的在线一体化工具开始，因为它免费、易用，并且集成了我们产品经理最高频使用的多种功能，协作起来也非常方便。它的确太好用了。
### 4.2.4 流程图绘制思路与注意事项

选好了工具，接下来就是最重要的部分——如何思考。画图只是思考结果的表达，图画得好不好，本质上是思路清不清晰。我总结了自己的一套“四步思考法”和“五大注意事项”。

#### 1. 绘制思路

![image-20250720080050483](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080050483.png)

* **明确核心目的**：在动笔前，我一定会先用一句话说清楚：我画这张图，是为了给谁看？想说明白一件什么事？比如，是为了跟开发讲清楚一个功能的逻辑，还是为了跟老板讲明白一个业务模式。
* **先想后画**：我从不直接在软件上拖拽图形。我习惯先在草稿纸或白板上，把关键节点和流程大致地勾勒出来，想清楚了再用工具画，这样效率最高，也避免了在细节上反复修改。
* **先主线后支线**：我总是先把一个流程最理想、最通畅的“主干道”画出来。然后再回头，去补充那些异常情况、判断分支等“小路”。这样能保证我的逻辑主线是清晰的。
* **多思考边界异常**：一个产品经理的价值，很大程度上体现在对异常情况的考虑是否周全。比如，用户输错密码怎么办？网络断了怎么办？库存不足了怎么办？我会尽可能地把这些边界和异常情况都考虑到我的流程图里。

#### 2. 绘制注意事项

![image-20250720080153245](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080153245.png)

* **顺序排列**：尽量保持从上到下、从左到右的统一流向，避免连接线交叉、混乱。
* **开头结尾**：一个完整的流程必须有明确的“开始”和“结束”符号。我绝不允许画一个没有终点的流程。
* **是否闭环**：我要确保流程的每一个分支都有一个明确的去向，最终都能导向一个结束节点或回到主流程，不能出现“断头路”。
* **善用标注**：当图形本身无法完全说清楚逻辑时，我会毫不犹豫地使用文字标注来补充说明，清晰永远是第一位的。
* **化繁为简**：如果一个流程图变得过于巨大和复杂，我会思考是否可以把它拆分成几个子流程来表达。我们的目标是用最简洁的图，说明白最复杂的事。

---

### 练习：绘制“找工作”流程图

![image-20250720080301030](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080301030.png)

现在，我们来做一个练习。请根据我们在图片中看到的“找工作流程图”案例，亲手绘制几张图。这个案例的流程如下：
a. 先在各个招聘网站投简历
b. 公司的HR看到你的简历后，初步评估，如果符合岗位需求，就邀请你去公司面试
c. 接到面试通知后，你就去公司参加面试，先由HR面试，再由该岗位的产品经理给你初面
d. 上面两次面试都通过后，HR会再约你谈薪资，最后确认录用你，就会给你发offer

**【练习任务】**

1.  **任务一：绘制业务流程图**
    请思考一下，这个流程涉及到哪些核心角色？（比如：求职者、HR、用人部门等）。

    请你画一张**业务流程图**，清晰地表达出这些角色以及他们在整个求职过程中的主要交互和行为。
    
    ![未命名白板](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E6%9C%AA%E5%91%BD%E5%90%8D%E7%99%BD%E6%9D%BF.png)

在画的这张图里，所有的动作都放在了一条线上。但实际上，“找工作”这个业务，至少涉及到三个角色：

- **求职者**
- **HR**
- **用人部门** (这里就是产品经理)

这三个角色在不同的时间点，做着不同的事，互相配合才完成了整个流程。而我们画“业务流程图”的核心目的，就是要清晰地展现这种**“跨角色的协作关系”**。

**那么，如何优化呢？**

我推荐使用一种最经典的业务流程图——**泳道图 (Swimlane Diagram)**。

您可以想象一个游泳池，我们为“求职者”、“HR”、“用人部门”这三个角色，分别划分出一条独立的“泳道”。然后，我们把现在画的这些步骤，按照“**这个动作是谁做的**”，放回到对应角色的泳道里。



---
## 4.3 泳道图
在上一节的练习中，我们提到了一个关键概念——**泳道图 (Swimlane Diagram)**，用它来优化我们画的业务流程图。

现在，我们就来系统地学习一下这个我个人非常推崇的、能清晰表达多角色协作关系的强大工具。


### 4.3.1 泳道图定义

![image-20250720082100314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720082100314.png)

#### 1. 跨职能（多角色）流程图

正如它的名字一样，泳道图，我把它就看作是带“泳道”的流程图。它的官方定义是**跨职能流程图**。

它的核心价值在于，它不仅能展示**“要做什么”（What/流程）**，更能清晰地展示**“由谁来做”（Who/角色/部门）**。

#### 2. 多角色协同与多阶段协同

在我的实践中，泳道的划分方式主要有两种：

* **按角色/部门划分**：这是我最常用的一种。就像我们“找工作”案例中的'求职者'、'HR'、'产品经理'。我用它来理清不同的人或团队之间的工作交接关系和职责边界。
* **按阶段划分**：有时，一个流程会经历几个大的阶段，比如“需求阶段”、“设计阶段”、“开发阶段”、“测试阶段”。我也可以用泳道来划分这几个阶段，清晰地展示任务在不同阶段的流转。

不过，在日常工作中，我们绝大多数时候都是**按角色划分**。

### 4.3.2 泳道图绘制思路

![image-20250720082152503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720082152503.png)

绘制泳道图，我的思路比画普通流程图会多几个“规划”步骤，这能确保最终的图清晰、准确。

1.  **明确目标对象**：和画所有图一样，第一步永远是明确我画这张图的目的。我要说明的是一个什么样的流程？
2.  **梳理角色/阶段**：这是泳道图独有的一步。我会把这个流程中涉及到的所有**参与方（角色/部门）**全部罗列出来。这是构建泳道的基础。
3.  **划分归属**：我会把流程中的每一个动作（节点），明确地分配给上一步中罗列出的角色。也就是回答“这件事，到底该归谁管？”这个问题。
4.  **对应绘制**：最后一步才是动手画。我先画好垂直或水平的泳道，然后把上一步中“划分好归属”的动作节点，一个一个放到各自的泳道里，再用流程线将它们连接起来。

---

#### **案例解析：找工作泳道图**

![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/38bd1fda5226237bb446e314a9183d54.png)

理论说完了，我们直接来看上一节练习的“标准答案”——**找工作泳道图案例**。这张图完美地诠释了泳道图的绘制思路和价值。

1.  **第一步：梳理角色。**
    我们看到，这张图清晰地定义了四个泳道，也就是四个核心角色：`求职者`、`HR`、`产品经理`、`产品总监`。

2.  **第二步：划分归属并绘制。**
    我们跟着流程线走一遍，就能清晰地看到动作和角色的对应关系：
    * 流程从 **求职者** 泳道的 `投递简历` 开始。
    * 箭头跨越泳道，流向 **HR** 泳道的 `查看简历` 和 `系统初筛`。
    * 如果通过，流程继续在 **HR** 泳道里走到 `邀请面试`，然后再次跨越泳道，信息流转回 **求职者** 的 `接收信息`。
    * 后续的 `初面` 由 **产品经理** 负责，`复面` 由 **产品总监** 负责，最后的 `薪资沟通` 又回到了 **HR** 这里。

**我的洞察：**
通过这张图，我不仅知道了找工作的完整步骤，更重要的是，我能一眼看清**在每个环节，我应该去找谁，谁是负责人，以及信息和任务是如何在不同角色之间流转交接的**。

这种对“职责”和“协作”的清晰表达，是普通流程图无法给予的。这就是泳道图的威力所在，也是为什么它在表达复杂业务流程时，是我最重要的工具。




---

## 4.4 结构图介绍

我们已经掌握了用来描述**动态“过程”**的流程图。现在，我们来学习与它互补的、用来描述**静态“组成”**的结构图。

如果说流程图是产品的“电影剧本”，那么结构图就是产品的“骨骼X光片”或“解剖图”。它不关心先后顺序，只关心“**这个东西，是由哪些部分构成的？**”

### 4.4.1 结构图的定义与分类

![image-20250720093047853](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093047853.png)

#### 1. 结构图的定义

我给**结构图**的定义是：**一种通过树状或脑图等形式，来表达产品、功能或信息层级关系的可视化图表。**
它的核心作用，就是帮助我把一个复杂、混沌的整体，拆解成一个个清晰、有序、有归属的部分。

#### 2. 常见结构图类型

就像流程图一样，根据我拆解的对象不同，我主要会用到三种结构图。

* **功能结构图 (Functional Structure Diagram)**
当我需要梳理一个产品或模块**“有哪些功能”**时，我就会画功能结构图。它是一个从抽象到具体的功能拆解过程，帮我梳理出完整的功能清单（Function List）。
![image-20250720093116350](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093116350.png)


我们来看“挂号功能结构图”这个案例。它清晰地将“挂号”这个大功能，拆解为`返回首页`、`搜索`、`选择科室`、`医院列表`等子功能，然后又把`医院列表`这个功能，进一步拆解为`筛选`和`查看医院`这两个孙子级功能。通过这张图，我就能确保在设计时，不会遗漏任何一个必要的功能点。

![image-20250720093127402](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093127402.png)
    

​    


* **信息结构图 (Information Structure Diagram)**
  

当我需要梳理一个页面或模块**“要展示哪些信息”**时，我就会画信息结构图。它拆解的不是“功能”，而是“数据和信息”。
    
![image-20250720093358095](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093358095.png)
    
在“挂号信息结构图”这个案例中，我们看到，它把“医院列表”这个模块，拆解为它需要展示的`封面图`、`名称`、`评分`、`地区`、`等级`等信息字段。这张图是我和UI设计师沟通界面内容、以及和开发工程师沟通数据字段时的重要依据。

![image-20250720093411563](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093411563.png)


* **产品结构图 (Product Structure Diagram)**
    产品结构图，在我看来，是**功能结构图和信息结构图的集合体**，是产品最全面、最宏观的一张“鸟瞰地图”。

    ![image-20250720093505990](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093505990.png)

    我们看“挂号产品结构图”这个案例，它既包含了`搜索`这样的**功能模块**，也包含了`科室金刚区`、`查看Banner`这样的**信息模块**和**界面元素**。它是我在进行原型设计之前，用来组织整体产品框架的“总设计图”，能帮我从全局视角思考产品每个部分的构成和关系。

![image-20250720093515445](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093515445.png)

---
## 4.5 结构图的绘制

### 4.5.1 结构图绘制注意事项

绘制结构图虽然比流程图要更自由一些，但我依然会遵循一些基本原则，来保证图表的清晰和易读。

![image-20250720093556927](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093556927.png)

1.  **层级数量**
    我画结构图时，会尽量把层级控制在**3-4层**以内。如果一个分支拆解得过深，说明这个模块可能太复杂了，我会考虑把它单独拎出来，为它画一张新的、更详细的结构图。

2.  **绘制方式**
    我习惯用“**自顶向下，逐层分解**”的方式来画。先确定最顶层的核心主题，然后拆分出第二层的主要构成，再把第二层的每一项继续往下拆，这样能保证逻辑的清晰和结构的完整。

3.  **顺序**
    和流程图不同，结构图同一层级的节点，左右顺序并没有严格的规定。我的原则是“**表达清楚即可**”，有时我会把逻辑上更重要或更核心的模块放在左边或上边，但这并不是硬性要求。



---

### 课习：拆解“视频播放页面”

现在，我们来做一个结构图的练习。请你想象一下，我们正在设计一个类似于YouTube或Bilibili的视频网站，你的任务是，对最重要的**“视频播放页面”**进行结构化拆解。

这个页面通常包含以下元素：
* 主视频播放器窗口
* 视频标题、UP主（上传者）信息（头像、昵称、粉丝数）、订阅按钮
* 点赞、不喜欢、分享、下载、收藏等互动按钮
* 视频简介、播放量、发布日期等数据
* 评论区（包括评论输入框、评论列表）
* 右侧的相关视频推荐列表

**【练习任务】**

1.  **任务：绘制功能结构图**
    请你画一张**功能结构图**，来拆解这个页面上所有**用户可以进行的操作**。
    
* 从顶层的“视频播放页功能”开始，往下拆解出例如“播放器控制”（如：播放/暂停、调节音量、全屏）、“视频互动”（如：点赞、收藏）、“作者互动”（如：订阅）、“评论互动”等几大功能模块，并思考这些模块下还可以有哪些更细分的子功能。
  
    ![未命名白板 (3)](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E6%9C%AA%E5%91%BD%E5%90%8D%E7%99%BD%E6%9D%BF%20(3).png)
    

---