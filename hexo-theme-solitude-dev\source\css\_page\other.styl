div#banners
  display flex
  width 100%
  background var(--efu-card-bg)
  border var(--style-border-always)
  border-radius 12px
  overflow hidden
  position relative
  box-shadow var(--efu-shadow-border)
  flex-direction column
  transition .3s
  will-change transform
  animation slide-in .6s .1s backwards

  #home_top &
    height 100%
    +maxWidth1200()
      height calc(328px + .5rem)

  :not(.flink)&:hover
    box-shadow var(--efu-shadow-main)

  .banner-button-group
    position absolute
    right 2rem
    top 2rem
    display flex

    +maxWidth768()
      display none

  .banner-button
    padding 8px 12px
    background var(--efu-fontcolor)
    border-radius 12px
    color var(--efu-card-bg)
    display flex
    align-items center
    z-index 1
    transition .3s
    cursor pointer
    box-shadow var(--efu-shadow-black)

    &.secondary
      background var(--efu-secondbg)
      border var(--style-border-always)
      color var(--efu-lighttext)
      margin-right 1rem
      box-shadow var(--efu-shadow-border)

    &:hover
      background var(--efu-theme)
      color var(--efu-white)

    i
      margin-right 8px
      font-size 1rem

  /.banners-title
    top 2.6rem
    left 2rem
    position absolute
    display flex
    flex-direction column

    &-big
      font-size 36px
      line-height 1
      font-weight 700
      margin-bottom 8px

    &-small
      font-size 12px
      line-height 1
      color var(--efu-secondtext)
      margin-top 8px
      margin-bottom 0.5rem
      margin-left 2px

  /.flink
    .banners-title
      top 1.5rem

  .bannerText
    font-size 4rem
    line-height 4rem
    font-weight 700

/.tags-group-all
  display flex

  #home_top &
    +maxWidth600()
      opacity 0.06
      filter blur(8px)
      z-index -1

  &.nowrapMove
    padding-bottom 2rem

    .tags-group-wrapper
      margin-top: 7rem
      animation: rowleft-quarter 30s linear infinite
      display flex

      +maxWidth768()
        margin-top 7.5rem

    .tags-group-icon
      border-radius 50%

      img
        min-width 100%
        min-height 100%
        border-radius 50%
        object-fit cover

  .tags-group-icon-pair
    margin-left 1rem
    user-select none

    a img
      border-radius 50%

    /.tags-group-icon
      position relative
      width 120px
      height 120px
      border-radius 30px
      display flex
      align-items center
      justify-content center
      color #fff
      font-size 66px
      font-weight 700
      box-shadow var(--efu-shadow-blackdeep)

      +maxWidth768()
        .flink &
          width 70px
          height 70px

      &:nth-child(even)
        margin-top 1rem
        transform translate(-60px)

      img
        width 60%

.author-content
  display flex
  flex-wrap wrap
  justify-content space-between
  width 100%
  gap .5rem

  +maxWidth768()
    margin-top 0
    flex-direction column

  .author-content-item
    +maxWidth1300()
      animation slide-in .6s 0s backwards
    +maxWidth768()
      width 100% !important
      padding 1rem

.author-content.author-content-item.sharePage
  height 19rem
  background-size cover
  color var(--efu-white)
  overflow hidden
  margin-top 0
  margin-bottom: 12px

  &::before
    content ''
    position absolute
    width 100%
    height 100%
    background var(--efu-black)
    opacity .3
    z-index 1
    top 0
    left 0

.author-content-item
  flex 4
  border-radius 12px
  background var(--efu-card-bg)
  border var(--style-border-always)
  box-shadow var(--efu-shadow-border)
  position relative
  overflow hidden
  padding 1rem 2rem

  &.single
    width 100%

  .author-content-item-title
    font-size 36px
    font-weight 700
    line-height 1

  .author-content-item-tips
    opacity .8
    font-size .6rem
    margin-bottom .5rem

  .content-bottom
    margin-top auto
    display flex
    align-items center
    justify-content space-between

    .icon-group
      display flex

      i
        display inline-block
        width 22px
        height 18px
        margin-right .5rem

  .card-content
    position absolute
    width 100%
    z-index 2
    height 100%
    top 0
    left 0
    display flex
    flex-direction column
    padding 1rem 2rem

    +maxWidth768()
      padding 1rem

    .author-content-item-title
      margin-bottom .5rem

    .banner-button-group
      position absolute
      bottom 1rem
      right 2rem

      .banner-button
        height 40px
        padding .5rem .7rem
        border-radius 20px
        justify-content center
        background var(--efu-black-op)
        color var(--efu-white)
        display flex
        align-items center
        z-index 1
        transition .3s
        cursor pointer
        backdrop-filter saturate(180%) blur(20px)
        -webkit-backdrop-filter blur(20px)
        transform translateZ(0)

        &:hover
          background var(--efu-main)
          color var(--efu-card-bg)

        i
          margin-right 8px
          font-size 22px

+maxWidth900()
  .author-content-item
    .card-content
      .banner-button-group
        right 1rem
        bottom 1rem

      .banner-button-group
        .banner-button
          background 0 0
          color var(--efu-white)
          padding 0
          width 30px
          height 30px

          i
            margin-right 0
            font-size 1.5rem

      .banner-button-text
        display none
