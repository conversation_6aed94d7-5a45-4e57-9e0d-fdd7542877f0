case theme.display_mode.type
    when "auto"
        script.
            initTheme = () => {
                let isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
                const cachedMode = utils.saveToLocal.get('theme');
                if (cachedMode === undefined) {
                    const nowMode =
                        isDarkMode ? 'dark' : 'light'
                    document.documentElement.setAttribute('data-theme', nowMode);
                } else {
                    document.documentElement.setAttribute('data-theme', cachedMode);
                }
                typeof rm === 'object' && rm.mode(cachedMode === 'dark' && isDarkMode)
            }
            initTheme()
    when "dark"
        script.
            initTheme = () => {
                const cachedMode = utils.saveToLocal.get('theme');
                if (cachedMode === undefined)
                    document.documentElement.setAttribute('data-theme', 'dark');
                else
                    document.documentElement.setAttribute('data-theme', cachedMode);
                typeof rm === 'object' && rm.mode(cachedMode === undefined || cachedMode === 'dark')
            }
            initTheme()
    when "light"
        script.
            initTheme = () => {
                const cachedMode = utils.saveToLocal.get('theme');
                if (cachedMode === undefined)
                    document.documentElement.setAttribute('data-theme', 'light');
                else
                    document.documentElement.setAttribute('data-theme', cachedMode);
                typeof rm === 'object' && rm.mode(cachedMode === 'dark')
            }
            initTheme()