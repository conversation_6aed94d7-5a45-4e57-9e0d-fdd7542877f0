if theme.pwa.enable
    meta(name="theme-color", content=theme.pwa.theme_color)
    meta(name="apple-mobile-web-app-status-bar-style", content=theme.pwa.theme_color)
    link(rel="manifest" href=url_for(theme.pwa.manifest))
    if theme.pwa.theme_color
        meta(name="msapplication-TileColor" content=theme.pwa.theme_color)
    if theme.pwa.mask_icon
        link(rel="mask-icon", href=url_for(theme.pwa.mask_icon), color=theme.pwa.theme_color)
    if theme.pwa.apple_touch_icon
        link(rel="apple-touch-icon" sizes="180x180" href=url_for(theme.pwa.apple_touch_icon))
    if theme.pwa.favicon_16_16
        link(rel="icon", type="image/png", sizes="16x16", href=url_for(theme.pwa.favicon_16_16))
    if theme.pwa.favicon_32_32
        link(rel="icon", type="image/png", sizes="32x32", href=url_for(theme.pwa.favicon_32_32))
    if theme.pwa.bookmark_icon
        link(rel="bookmark", href=url_for(theme.pwa.bookmark_icon))
else
    meta(name="apple-mobile-web-app-capable", content=config.title)
    meta(name="theme-color", content="var(--efu-main)")
    meta(name="apple-mobile-web-app-status-bar-style", content="var(--efu-main)")
    link(rel="bookmark", href=url_for(theme.site.icon))
    link(rel="apple-touch-icon", href=url_for(theme.site.icon), sizes="180x180")
