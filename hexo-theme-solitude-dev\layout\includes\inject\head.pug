each item in theme.verify_site || []
    meta(name=item.name, content=item.content)

link(rel="canonical" href=urlNoIndex())
link(rel="stylesheet", href=url_for(theme.cdn.fontawesome))

// aplayer
if theme.capsule.enable || theme.music.enable || theme.brevity.enable && theme.brevity.music
    link(rel="stylesheet", href=url_for(theme.cdn.aplayer_css))

// swiper
if (theme.brevity.home_mini && theme.brevity.enable)
    link(rel="stylesheet", href=url_for(theme.cdn.swiper_css))

// fancybox ui
if theme.lightbox && theme.fancybox
    link(rel="stylesheet", href=url_for(theme.cdn.fancyapps_css))

// katex
if theme.katex && theme.katex.enable
    link(rel="stylesheet", href=url_for(theme.cdn.katex))

// Open Graph
include ../head/opengraph.pug

// pwa
include ../head/pwa.pug

script.
    console.log(' %c Solitude %c ' + '!{packageVersion()}' + ' %c https://github.com/everfu/hexo-theme-solitude',
        'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
        'background:#ff9a9a ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
        'background:unset ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff')

if theme.memorial.enable
    script.
        let mdate = "!{theme.memorial.date}";
        mdate = (mdate.split(","));
        let ndate = new Date();
        for (let i of mdate) {
            if (i === (ndate.getMonth()+1) + "-" + (ndate.getDate())) {
                document.documentElement.classList.add('memorial');
            }
        }

if theme.extends.head
    each item in theme.extends.head
        != item

!=fragment_cache('injectHeadJs', function(){return inject_head_js()})

// theme
include ../head/mode

if theme.page.echart
    script(src=url_for(theme.cdn.echarts))
