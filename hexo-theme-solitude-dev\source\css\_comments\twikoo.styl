#twikoo
  .tk-submit
    margin-top 0

  .tk-admin-config-group-title
    margin-top 0
    line-height normal

  .tk-row
    .tk-col
      flex-direction column-reverse !important

  > div.tk-comments
    > div.tk-comments-container
      > div.tk-comments-title
        > span:nth-child(1)
          display none !important

    > div.tk-submit
      > div.tk-row.actions
        > a
          display none !important

.tk-meta
  display flex
  align-items center

.twikoo-info
  color var(--efu-secondtext)

.tk-action
  .tk-action-link:first-child
    display none

.tk-action-link
  color var(--efu-lighttext) !important
  cursor pointer
  user-select none
  padding 0 12px
  transition all .3s
  border-radius 8px
  background-color var(--efu-secondbg)
  border var(--style-border-always)

  &:hover
    background-color var(--efu-lighttext)
    color var(--efu-card-bg) !important

    .tk-action-icon svg
      fill var(--efu-card-bg) !important

.tk-action-icon
  svg
    transition all .3s
    fill var(--efu-lighttext) !important

.tk-content
  img
    border-radius 12px

.tk-avatar
  width 32px !important
  height 32px !important
  box-shadow var(--efu-shadow-border)
  margin-right 16px !important

  &.tk-has-avatar
    width 32px !important
    height 32px !important
    border-radius 32px !important

.tk-row
  .tk-avatar
    display none

.tk-avatar
  .tk-avatar-img:hover
    transform rotate(360deg)

.tk-nick
  font-size 1rem !important
  line-height 32px

#page
  .tk-meta-input
    .el-input
      box-shadow var(--efu-shadow-border)

.el-loading-mask
  background-color none !important

.tk-tag
  margin-left 4px

.tk-tag-green
  border-radius 4px !important
  border 0 solid #e1f3d8 !important
  font-size .5rem !important

[data-theme=dark]
  .tk-tag-green
    background-color #67c23a21 !important

.tk-tag-yellow
  border-radius 4px !important
  border 0 solid #e1f3d8 !important
  font-size .5rem !important

[data-theme=dark]
  .tk-tag-green
    background-color #c0c23a21 !important

.tk-tag-red
  border-radius 4px !important
  border 0 solid #f3d8d8 !important
  font-size .5rem !important

[data-theme=dark]
  .tk-tag-red
    background-color #c23a3a21 !important

.tk-submit-action-icon.__markdown
  display none

.tk-comments
  .el-button--primary
    border-color var(--efu-fontcolor) !important
    color var(--efu-card-bg) !important
    border-radius 12px !important
    box-shadow var(--efu-shadow-black)
    transition .3s
    width 5rem
    position absolute
    top -44px
    right 0
    margin-left .5rem !important
    height 34px

  .el-button--primary.is-disabled,
  .el-button--primary.is-disabled:active,
  .el-button--primary.is-disabled:focus,
  .el-button--primary.is-disabled:hover
    opacity .2

.tk-row-actions-start
  position absolute
  top -84px
  left 17px

@media screen and (max-width: 768px)
  .tk-submit
    .el-button--primary
      width 5rem
      height 122px
      top -132px

  .tk-row-actions-start
    top -170px

.tk-comments-title
  position absolute
  bottom 0
  left 0

.tk-extras
  margin-top .5rem
  padding-bottom .5rem

.tk-icon.__comments:first-child
  display none

.tk-icon.__comments
  margin-left 0 !important

.tk-row.actions
  margin-bottom 0 !important
  margin-left 0 !important
  margin-top 0 !important
  justify-content space-around !important

.tk-meta-input
  position relative !important
  margin-top 8px
  width calc(100% - 5.5rem)

.tk-content
  .tk-owo-emotion
    width 3em
    margin 0 2px

.tk-owo-emotion,
.twikoo
  .OwO-item
    img
      pointer-events none

.tk-extra
  background var(--efu-card-bg)
  border var(--style-border-always)
  padding 1px 5px 1px 2px
  border-radius 8px
  margin-right 4px !important
  color var(--efu-secondtext) !important
  display inline !important
  margin-top 6px !important
  font-size .5rem

.tk-extra
  .tk-icon
    display none

.tk-expand
  background var(--efu-card-bg) !important
  color var(--efu-fontcolor) !important
  border var(--style-border-always)
  box-shadow var(--efu-shadow-border)
  border-radius 12px
  user-select none

  &:hover
    background var(--efu-theme) !important
    color var(--efu-white) !important

.tk-time
  color var(--efu-secondtext) !important
  font-size .6rem
  margin-left .5rem

.tk-comments-container
  > .tk-comment
    margin-top 0 !important
    margin-bottom 0.5rem !important
    background var(--efu-card-bg)
    transition .3s
    border-radius 12px
    padding 0
    padding-top 0.5rem
    border none
    border-top var(--style-border-dashed)

#page
  .tk-comments-container
    > .tk-comment
      padding 1rem 1rem 1.5rem
      border var(--style-border)
      border-top var(--style-border)
      box-shadow var(--efu-shadow-border)

@media screen and (max-width: 768px)
  .tk-comments-container
    > .tk-comment
      padding 1rem
      border var(--style-border-always)
      box-shadow var(--efu-shadow-border)

  .tk-icon.__comments
    left .5rem

.tk-icon
  position absolute

.tk-comments-no
  display none !important

.tk-comments-container
  min-height 0 !important

.tk-replies
  > .tk-comment
    background var(--efu-card-bg)
    border-top var(--style-border-dashed)
    border-radius 12px
    transition .3s
    padding 1rem 0 0
    margin-top 0

.tk-content
  p
    margin 0 !important

  pre
  code
  kbd
  samp
    font-family $code-font-family

  pre
    background var(--efu-secondbg)
    padding .5rem
    margin 0.5rem 0
    overflow auto
    border-radius 0.5em

    +maxWidth768()
      max-width 90%

.tk-replies
  .tk-content
    span:first-child
      font-size .5rem
      color var(--efu-secondtext)

.tk-content
  margin-top 0 !important

.tk-meta-input
  .el-input
    .el-input-group__prepend
      box-shadow none
      border-radius 12px 0 0 12px
      user-select none

@media screen and (max-width: 768px)
  .tk-meta-input
    .el-input
      .el-input-group__prepend
        padding 0 .3rem !important

  .tk-meta-input
    display flex
    flex-direction column
    top 0
    position inherit !important

  .tk-meta-input
    .el-input
      margin-left 0 !important
      width 100% !important

  .tk-icon
    position absolute
    right 0

img.tk-avatar-img
  height 32px !important
  border-radius 32px
  border var(--style-border-always)

.el-input--small
  .el-input__inner
    padding 8px

  .el-input__inner, .el-textarea__inner
    border-radius 12px

.el-input.el-input--small.el-input-group.el-input-group--prepend
  border-radius 12px
  background var(--efu-secondbg)
  border var(--style-border-always)

.el-input-group__append, .el-input-group__prepend
  background-color var(--efu-card-bg)
  color var(--efu-fontcolor) !important
  border 0 !important
  font-weight 700

.el-input__inner
  background-color var(--efu-secondbg) !important
  border 0 !important
  color var(--efu-fontcolor) !important
  padding-left 8px
  height 32px
  line-height 32px
  border-radius 12px

.page
  .el-input__inner
    background var(--efu-card-bg) !important

.el-input__inner:focus
  border none

.el-textarea__inner
  background-color var(--efu-secondbg)
  color var(--efu-fontcolor) !important
  border-radius 12px !important
  min-height 100px !important
  padding 16px 16px 40px 16px !important
  border var(--style-border-always) !important
  box-shadow none !important

@media screen and (max-width: 768px)
  .el-textarea__inner
    background var(--efu-card-bg) !important

#page
  .el-textarea__inner
    background var(--efu-card-bg) !important
    box-shadow var(--efu-shadow-border)

.el-textarea__inner:focus
  border var(--style-border-hover-always) !important
  box-shadow var(--efu-shadow-main)

.el-button
  background-color var(--efu-fontcolor) !important
  border 0 solid var(--efu-main) !important
  color var(--efu-background) !important
  border-radius 8px !important

.el-button:hover
  background var(--efu-black) !important
  color var(--efu-white) !important

.el-button.tk-preview
  display none !important

button.el-button.tk-cancel.el-button--default.el-button--small
  background var(--efu-secondbg) !important
  border-radius 8px !important
  color var(--efu-fontcolor) !important

.OwO
  &.OwO-open
    .OwO-body
      animation .3s ease .1s 1 normal both running donate_effcet

  .OwO-body
    z-index 102
    width 500px
    border var(--style-border-always) !important
    border-radius 8px !important
    overflow hidden
    background-color var(--efu-maskbg) !important
    backdrop-filter saturate(180%) blur(10px)
    -webkit-backdrop-filter blur(10px)
    transform translateZ(0)
    +maxWidth768()
      width 300px

    .OwO-bar
      border-top none !important

      .OwO-packages
        li
          margin-right 0 !important
          height 48px
          transition .3s

        li:hover
          transition .3s

    .OwO-items
      .OwO-item:hover
        box-shadow var(--efu-shadow-lightblack) !important
        border-radius 8px

.OwO-packages
  background var(--efu-background)
  padding-left 8px !important

.OwO
  .OwO-body
    .OwO-bar
      .OwO-packages
        .OwO-package-active
          background var(--efu-secondbg) !important

    .OwO-items-show
      margin 12px 8px !important
      min-height 197px

    .OwO-bar
      .OwO-packages
        li
          line-height 45px !important
