extends includes/layout.pug

block content
    main.layout#content-inner
        #category
            #category-bar
                include includes/widgets/home/<USER>
            .recent-posts#recent-posts
                each post,index in page.posts.find({ parent: { $exists: false } }).data
                    include includes/widgets/home/<USER>
                include includes/mixins/pagination
            if theme.comment.hot_tip.enable
                include ./includes/widgets/third-party/hot/index.pug
        include includes/widgets/aside/aside
