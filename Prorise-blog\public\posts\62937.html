<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Python（十三）：第十二章： 异常处理 | Prorise的小站</title><meta name="keywords" content="Python基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Python（十三）：第十二章： 异常处理"><meta name="application-name" content="Python（十三）：第十二章： 异常处理"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="Python（十三）：第十二章： 异常处理"><meta property="og:url" content="https://prorise666.site/posts/62937.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第十二章： 异常处理异常处理是 Python 编程中的重要环节，它允许程序在遇到错误时优雅地恢复或退出，而不是直接崩溃。 12.1 基本异常处理异常处理的核心是 try-except 结构，它允许程序捕获并处理运行时错误。 12.1.1 异常的概念与意义异常是程序运行时发生的错误，会打断正常的程序执"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta name="description" content="第十二章： 异常处理异常处理是 Python 编程中的重要环节，它允许程序在遇到错误时优雅地恢复或退出，而不是直接崩溃。 12.1 基本异常处理异常处理的核心是 try-except 结构，它允许程序捕获并处理运行时错误。 12.1.1 异常的概念与意义异常是程序运行时发生的错误，会打断正常的程序执"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/62937.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"Python（十三）：第十二章： 异常处理",postAI:"true",pageFillDescription:"第十二章： 异常处理, 12.1 基本异常处理, 12.1.1 异常的概念与意义, 12.1.2 基础 try-except 结构, 12.2 完整的异常处理结构, 12.2.1 try-except-else-finally 完整结构, 12.2.2 各代码块执行条件总结, 12.3 自定义异常, 12.3.1 创建自定义异常类, 12.3.2 使用自定义异常, 12.4 异常的传播与重新抛出, 12.4.1 异常传播机制, 12.4.2 重新抛出异常, 12.5 使用上下文管理器, 12.5.1 with 语句和资源管理, 12.5.2 自定义上下文管理器, 12.5.3 实际应用场景, 12.5.4 使用 contextlib 简化上下文管理器创建, 12.6 异常处理最佳实践, 12.6.1 不良实践与改进, 12.6.2 实际开发中的异常处理策略, 12.7 高级异常处理技术, 12.7.1 使用装饰器简化异常处理, 12.7.2 异常链与异常组, 12.7.3 EAFP vs LBYL 编程风格第十二章异常处理异常处理是编程中的重要环节它允许程序在遇到错误时优雅地恢复或退出而不是直接崩溃基本异常处理异常处理的核心是结构它允许程序捕获并处理运行时错误异常的概念与意义异常是程序运行时发生的错误会打断正常的程序执行流程提供了强大的异常处理机制使程序能够预测可能的错误并妥善处理提供用户友好的错误信息防止程序意外终止实现优雅的错误恢复策略基础结构可能引发异常的代码请输入一个数字可能引发可能引发结果是处理特定异常输入必须是数字处理另一种特定异常不能除以零捕获所有其他异常不推荐这种写法发生了其他错误常见内置异常触发场景示例传入无效值类型不匹配除数为零索引超出范围字典中不存在的键文件不存在不存在导入模块失败不存在模块对象没有特定属性完整的异常处理结构完整的异常处理结构包括和四个部分每个部分负责不同的功能完整结构可能引发异常的代码请输入一个数字结果是处理特定异常包含异常的详细信息输入错误可能显示处理另一种特定异常除零错误可能显示处理所有其他异常这种方式比空更好其他错误只有当块中的代码执行成功且没有异常发生时执行计算成功完成无论是否有异常都会执行的代码块异常处理结束各代码块执行条件总结代码块执行条件典型用途必定执行放置可能出错的代码对应类型异常发生时处理特定类型错误块无异常发生时执行成功后的操作无论有无异常均执行资源清理释放自定义异常虽然提供了丰富的内置异常但在开发特定应用时创建自定义异常可以使代码更具可读性和针对性创建自定义异常类自定义异常类继承自当账户余额不足时引发的异常创建有意义的错误消息余额不足当前余额元尝试提取元使用自定义异常在业务逻辑中使用自定义异常在适当的条件下抛出自定义异常处理自定义异常的实际场景尝试提取超过余额的金额针对性地处理特定业务异常操作失败您需要再存入至少元可以在这里提供补救措施比如自动转入资金或提供贷款选项自定义异常命名惯例示例适用场景以结尾程序错误需纠正以结尾警告级别的问题以具体领域开头特定领域的异常异常的传播与重新抛出了解异常如何在调用栈中传播以及如何重新抛出异常对于构建稳健的错误处理系统至关重要异常传播机制当异常发生时会沿着调用栈向上查找直到找到相应的子句处理该异常如果没有处理程序程序将终止异常传播示例这里的异常会向上传播引发没有处理异常所以异常继续传播在这里捕获来自更深层次函数的异常捕获了除零错误调用最外层函数输出捕获了除零错误重新抛出异常重新抛出异常有两种方式直接使用语句不带参数使用结构表明异常的因果关系尝试处理数据记录错误并重新抛出当前异常除数不能为零直接重新抛出当前捕获的异常捕获后转换为更具体的应用级异常并保留原始错误信息数据格式不正确需要至少两个元素调用函数并处理异常尝试处理带有问题的数据数组只有一个元素会引发处理转换后的异常发生错误访问原始异常原始错误重新抛出方式语法适用场景简单重抛仅记录错误后继续传播转换异常将低级异常转换为应用级异常清除上下文隐藏原始异常不推荐使用上下文管理器上下文管理器是的一种强大机制通过语句实现自动资源管理特别适合处理需要显式打开和关闭的资源语句和资源管理文件操作最常见的上下文管理器应用场景可能发生异常的代码演示异常即使发生异常文件也会自动关闭自定义上下文管理器进入上下文时调用返回值被赋给后的变量连接到数据库在实际应用中这里会创建真正的数据库连接已连接离开上下文时调用无论是正常退出还是异常退出参数异常类型异常值异常回溯信息关闭数据库连接释放资源返回值决定异常处理表示异常已处理不再传播表示需要继续传播异常让异常继续传播实际应用场景使用自定义上下文管理器进行数据库操作使用连接数据库操作代码模拟操作失败数据插入失败捕获到异常处理数据库操作异常可能的恢复策略重试记录日志发送报警等常见上下文管理器示例自动管理的资源文件句柄线程锁忽略特定异常临时文件会话使用简化上下文管理器创建一个使用生成器函数创建的上下文管理器设置阶段获取资源文件已打开语句将控制权传递给块内的代码清理阶段释放资源文件已关闭使用自定义上下文管理器这是一个使用创建的上下文管理器示例异常处理最佳实践掌握异常处理的模式和反模式对于编写健壮的代码至关重要不良实践与改进不好的做法过于宽泛的异常捕获大量不同类型的操作混在一起捕获所有异常无法区分不同错误出错了无法提供有价值的错误信息无法针对性恢复好的做法精确捕获和处理异常只包含读取配置文件的代码针对性处理配置文件缺失配置文件不存在将使用默认配置针对性处理权限问题没有读取配置文件的权限可以请求提升权限或使用备用方案确保文件被关闭解析配置的代码单独放在块中处理配置格式错误配置格式错误后续操作实际开发中的异常处理策略分层异常处理示例应用请求处理底层数据访问层转换为应用层可理解的异常数据库操作转换为应用级异常访问被拒绝数据库访问被拒绝连接失败无法连接到数据库数据库错误资源清理业务逻辑层处理应用级异常应用逻辑异常用户不存在日志记录并决定是否传播获取用户数据失败可能的重试策略传播异常供上层处理接口层向用户展示友好错误返回适当的状态码用户不存在返回服务暂时不可用服务暂时不可用意外错误记录并返回通用错误未处理的错误服务器内部错误高级异常处理技术使用装饰器简化异常处理一个用于自动重试的装饰器参数最大尝试次数重试之间的延迟秒达到最大尝试次数重新抛出异常操作失败秒后重试不会执行到这里使用重试装饰器连接到远程服务器可能会失败模拟的失败率连接服务器失败连接成功调用带重试功能的函数结果连接服务器最终失败异常链与异常组引入了异常组和语法用于处理多个异常同时存在的情况特性异常组用于收集任务处理过程中的错误尝试处理任务处理任务输入值为任务处理结果为如果有错误以异常组的形式抛出处理任务过程中发生错误任务处理失败使用处理异常组处理所有除零错误除零错误这里就可以抓到的异常处理所有类型错误类型错误这里就可以抓到的异常处理其他所有错误其他错误编程风格通常推崇而非风格先检查后操作风格先操作后处理异常",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-13 22:13:01",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E4%BA%8C%E7%AB%A0%EF%BC%9A-%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86"><span class="toc-text">第十二章： 异常处理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#12-1-%E5%9F%BA%E6%9C%AC%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86"><span class="toc-text">12.1 基本异常处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-1-1-%E5%BC%82%E5%B8%B8%E7%9A%84%E6%A6%82%E5%BF%B5%E4%B8%8E%E6%84%8F%E4%B9%89"><span class="toc-text">12.1.1 异常的概念与意义</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-1-2-%E5%9F%BA%E7%A1%80-try-except-%E7%BB%93%E6%9E%84"><span class="toc-text">12.1.2 基础 try-except 结构</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-2-%E5%AE%8C%E6%95%B4%E7%9A%84%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E7%BB%93%E6%9E%84"><span class="toc-text">12.2 完整的异常处理结构</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-2-1-try-except-else-finally-%E5%AE%8C%E6%95%B4%E7%BB%93%E6%9E%84"><span class="toc-text">12.2.1 try-except-else-finally 完整结构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-2-2-%E5%90%84%E4%BB%A3%E7%A0%81%E5%9D%97%E6%89%A7%E8%A1%8C%E6%9D%A1%E4%BB%B6%E6%80%BB%E7%BB%93"><span class="toc-text">12.2.2 各代码块执行条件总结</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-3-%E8%87%AA%E5%AE%9A%E4%B9%89%E5%BC%82%E5%B8%B8"><span class="toc-text">12.3 自定义异常</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-3-1-%E5%88%9B%E5%BB%BA%E8%87%AA%E5%AE%9A%E4%B9%89%E5%BC%82%E5%B8%B8%E7%B1%BB"><span class="toc-text">12.3.1 创建自定义异常类</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-3-2-%E4%BD%BF%E7%94%A8%E8%87%AA%E5%AE%9A%E4%B9%89%E5%BC%82%E5%B8%B8"><span class="toc-text">12.3.2 使用自定义异常</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-4-%E5%BC%82%E5%B8%B8%E7%9A%84%E4%BC%A0%E6%92%AD%E4%B8%8E%E9%87%8D%E6%96%B0%E6%8A%9B%E5%87%BA"><span class="toc-text">12.4 异常的传播与重新抛出</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-4-1-%E5%BC%82%E5%B8%B8%E4%BC%A0%E6%92%AD%E6%9C%BA%E5%88%B6"><span class="toc-text">12.4.1 异常传播机制</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-4-2-%E9%87%8D%E6%96%B0%E6%8A%9B%E5%87%BA%E5%BC%82%E5%B8%B8"><span class="toc-text">12.4.2 重新抛出异常</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-5-%E4%BD%BF%E7%94%A8%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8"><span class="toc-text">12.5 使用上下文管理器</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-5-1-with-%E8%AF%AD%E5%8F%A5%E5%92%8C%E8%B5%84%E6%BA%90%E7%AE%A1%E7%90%86"><span class="toc-text">12.5.1 with 语句和资源管理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-5-2-%E8%87%AA%E5%AE%9A%E4%B9%89%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8"><span class="toc-text">12.5.2 自定义上下文管理器</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-5-3-%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">12.5.3 实际应用场景</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-5-4-%E4%BD%BF%E7%94%A8-contextlib-%E7%AE%80%E5%8C%96%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8%E5%88%9B%E5%BB%BA"><span class="toc-text">12.5.4 使用 contextlib 简化上下文管理器创建</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-6-%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5"><span class="toc-text">12.6 异常处理最佳实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-6-1-%E4%B8%8D%E8%89%AF%E5%AE%9E%E8%B7%B5%E4%B8%8E%E6%94%B9%E8%BF%9B"><span class="toc-text">12.6.1 不良实践与改进</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-6-2-%E5%AE%9E%E9%99%85%E5%BC%80%E5%8F%91%E4%B8%AD%E7%9A%84%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E7%AD%96%E7%95%A5"><span class="toc-text">12.6.2 实际开发中的异常处理策略</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-7-%E9%AB%98%E7%BA%A7%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E6%8A%80%E6%9C%AF"><span class="toc-text">12.7 高级异常处理技术</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-7-1-%E4%BD%BF%E7%94%A8%E8%A3%85%E9%A5%B0%E5%99%A8%E7%AE%80%E5%8C%96%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86"><span class="toc-text">12.7.1 使用装饰器简化异常处理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-7-2-%E5%BC%82%E5%B8%B8%E9%93%BE%E4%B8%8E%E5%BC%82%E5%B8%B8%E7%BB%84"><span class="toc-text">12.7.2 异常链与异常组</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-7-3-EAFP-vs-LBYL-%E7%BC%96%E7%A8%8B%E9%A3%8E%E6%A0%BC"><span class="toc-text">12.7.3 EAFP vs LBYL 编程风格</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Python基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Python（十三）：第十二章： 异常处理</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-18T20:13:45.000Z" title="发表于 2025-04-19 04:13:45">2025-04-19</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.530Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.7k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>14分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Python（十三）：第十二章： 异常处理"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/62937.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/62937.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Python基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Python（十三）：第十二章： 异常处理</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-04-18T20:13:45.000Z" title="发表于 2025-04-19 04:13:45">2025-04-19</time><time itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.530Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></header><div id="postchat_postcontent"><h2 id="第十二章：-异常处理"><a href="#第十二章：-异常处理" class="headerlink" title="第十二章： 异常处理"></a>第十二章： 异常处理</h2><p>异常处理是 Python 编程中的重要环节，它允许程序在遇到错误时优雅地恢复或退出，而不是直接崩溃。</p><h3 id="12-1-基本异常处理"><a href="#12-1-基本异常处理" class="headerlink" title="12.1 基本异常处理"></a>12.1 基本异常处理</h3><p>异常处理的核心是 <code>try-except</code> 结构，它允许程序捕获并处理运行时错误。</p><h4 id="12-1-1-异常的概念与意义"><a href="#12-1-1-异常的概念与意义" class="headerlink" title="12.1.1 异常的概念与意义"></a>12.1.1 异常的概念与意义</h4><p>异常是程序运行时发生的错误，会打断正常的程序执行流程。Python 提供了强大的异常处理机制，使程序能够：</p><ul><li>预测可能的错误并妥善处理</li><li>提供用户友好的错误信息</li><li>防止程序意外终止</li><li>实现优雅的错误恢复策略</li></ul><h4 id="12-1-2-基础-try-except-结构"><a href="#12-1-2-基础-try-except-结构" class="headerlink" title="12.1.2 基础 try-except 结构"></a>12.1.2 基础 try-except 结构</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    <span class="comment"># 可能引发异常的代码</span></span><br><span class="line">    num = <span class="built_in">int</span>(<span class="built_in">input</span>(<span class="string">"请输入一个数字: "</span>))  <span class="comment"># 可能引发 ValueError</span></span><br><span class="line">    result = <span class="number">10</span> / num  <span class="comment"># 可能引发 ZeroDivisionError</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"结果是: <span class="subst">{result}</span>"</span>)</span><br><span class="line"><span class="keyword">except</span> ValueError:</span><br><span class="line">    <span class="comment"># 处理特定异常</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"输入必须是数字!"</span>)</span><br><span class="line"><span class="keyword">except</span> ZeroDivisionError:</span><br><span class="line">    <span class="comment"># 处理另一种特定异常</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"不能除以零!"</span>)</span><br><span class="line"><span class="keyword">except</span>:</span><br><span class="line">    <span class="comment"># 捕获所有其他异常(不推荐这种写法)</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"发生了其他错误!"</span>)</span><br></pre></td></tr></tbody></table></figure><table><thead><tr><th>常见内置异常</th><th>触发场景</th><th>示例</th></tr></thead><tbody><tr><td>ValueError</td><td>传入无效值</td><td><code>int("abc")</code></td></tr><tr><td>TypeError</td><td>类型不匹配</td><td><code>"2" + 2</code></td></tr><tr><td>ZeroDivisionError</td><td>除数为零</td><td><code>10/0</code></td></tr><tr><td>IndexError</td><td>索引超出范围</td><td><code>[1,2,3][10]</code></td></tr><tr><td>KeyError</td><td>字典中不存在的键</td><td><code>{"a":1}["b"]</code></td></tr><tr><td>FileNotFoundError</td><td>文件不存在</td><td><code>open("不存在.txt")</code></td></tr><tr><td>ImportError</td><td>导入模块失败</td><td><code>import 不存在模块</code></td></tr><tr><td>AttributeError</td><td>对象没有特定属性</td><td><code>"hello".append(1)</code></td></tr></tbody></table><h3 id="12-2-完整的异常处理结构"><a href="#12-2-完整的异常处理结构" class="headerlink" title="12.2 完整的异常处理结构"></a>12.2 完整的异常处理结构</h3><p>完整的异常处理结构包括 <code>try</code>, <code>except</code>, <code>else</code>, 和 <code>finally</code> 四个部分，每个部分负责不同的功能。</p><h4 id="12-2-1-try-except-else-finally-完整结构"><a href="#12-2-1-try-except-else-finally-完整结构" class="headerlink" title="12.2.1 try-except-else-finally 完整结构"></a>12.2.1 try-except-else-finally 完整结构</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    <span class="comment"># 可能引发异常的代码</span></span><br><span class="line">    num = <span class="built_in">int</span>(<span class="built_in">input</span>(<span class="string">"请输入一个数字: "</span>))</span><br><span class="line">    result = <span class="number">10</span> / num</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"结果是: <span class="subst">{result}</span>"</span>)</span><br><span class="line"><span class="keyword">except</span> ValueError <span class="keyword">as</span> e:</span><br><span class="line">    <span class="comment"># 处理特定异常，e 包含异常的详细信息</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"输入错误: <span class="subst">{e}</span>"</span>)  <span class="comment"># e 可能显示: "invalid literal for int() with base 10: 'abc'"</span></span><br><span class="line"><span class="keyword">except</span> ZeroDivisionError <span class="keyword">as</span> e:</span><br><span class="line">    <span class="comment"># 处理另一种特定异常</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"除零错误: <span class="subst">{e}</span>"</span>)  <span class="comment"># e 可能显示: "division by zero"</span></span><br><span class="line"><span class="keyword">except</span> Exception <span class="keyword">as</span> e:</span><br><span class="line">    <span class="comment"># 处理所有其他异常(这种方式比空except更好)</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"其他错误: <span class="subst">{e}</span>"</span>)</span><br><span class="line"><span class="keyword">else</span>:</span><br><span class="line">    <span class="comment"># 只有当try块中的代码执行成功且没有异常发生时执行</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"计算成功完成!"</span>)</span><br><span class="line"><span class="keyword">finally</span>:</span><br><span class="line">    <span class="comment"># 无论是否有异常都会执行的代码块</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"异常处理结束"</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="12-2-2-各代码块执行条件总结"><a href="#12-2-2-各代码块执行条件总结" class="headerlink" title="12.2.2 各代码块执行条件总结"></a>12.2.2 各代码块执行条件总结</h4><table><thead><tr><th>代码块</th><th>执行条件</th><th>典型用途</th></tr></thead><tbody><tr><td><code>try</code></td><td>必定执行</td><td>放置可能出错的代码</td></tr><tr><td><code>except</code></td><td>对应类型异常发生时</td><td>处理特定类型错误</td></tr><tr><td><code>else</code></td><td>try 块无异常发生时</td><td>执行成功后的操作</td></tr><tr><td><code>finally</code></td><td>无论有无异常均执行</td><td>资源清理、释放</td></tr></tbody></table><h3 id="12-3-自定义异常"><a href="#12-3-自定义异常" class="headerlink" title="12.3 自定义异常"></a>12.3 自定义异常</h3><p>虽然 Python 提供了丰富的内置异常，但在开发特定应用时，创建自定义异常可以使代码更具可读性和针对性。</p><h4 id="12-3-1-创建自定义异常类"><a href="#12-3-1-创建自定义异常类" class="headerlink" title="12.3.1 创建自定义异常类"></a>12.3.1 创建自定义异常类</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 自定义异常类，继承自 Exception</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">InsufficientFundsError</span>(<span class="title class_ inherited__">Exception</span>):</span><br><span class="line">    <span class="string">"""当账户余额不足时引发的异常"""</span></span><br><span class="line">    </span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, balance, amount</span>):</span><br><span class="line">        <span class="variable language_">self</span>.balance = balance</span><br><span class="line">        <span class="variable language_">self</span>.amount = amount</span><br><span class="line">        <span class="comment"># 创建有意义的错误消息</span></span><br><span class="line">        <span class="variable language_">self</span>.message = <span class="string">f"余额不足: 当前余额 <span class="subst">{balance}</span> 元，尝试提取 <span class="subst">{amount}</span> 元"</span></span><br><span class="line">        <span class="built_in">super</span>().__init__(<span class="variable language_">self</span>.message)</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__str__</span>(<span class="params">self</span>):</span><br><span class="line">        <span class="keyword">return</span> <span class="variable language_">self</span>.message</span><br></pre></td></tr></tbody></table></figure><h4 id="12-3-2-使用自定义异常"><a href="#12-3-2-使用自定义异常" class="headerlink" title="12.3.2 使用自定义异常"></a>12.3.2 使用自定义异常</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 在业务逻辑中使用自定义异常</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">BankAccount</span>:</span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, balance=<span class="number">0</span></span>):</span><br><span class="line">        <span class="variable language_">self</span>.balance = balance</span><br><span class="line">        </span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">withdraw</span>(<span class="params">self, amount</span>):</span><br><span class="line">        <span class="keyword">if</span> amount &gt; <span class="variable language_">self</span>.balance:</span><br><span class="line">            <span class="comment"># 在适当的条件下抛出自定义异常</span></span><br><span class="line">            <span class="keyword">raise</span> InsufficientFundsError(<span class="variable language_">self</span>.balance, amount)</span><br><span class="line">        <span class="variable language_">self</span>.balance -= amount</span><br><span class="line">        <span class="keyword">return</span> amount</span><br><span class="line"></span><br><span class="line"><span class="comment"># 处理自定义异常的实际场景</span></span><br><span class="line">account = BankAccount(<span class="number">100</span>)</span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    <span class="comment"># 尝试提取超过余额的金额</span></span><br><span class="line">    account.withdraw(<span class="number">150</span>)</span><br><span class="line"><span class="keyword">except</span> InsufficientFundsError <span class="keyword">as</span> e:</span><br><span class="line">    <span class="comment"># 针对性地处理特定业务异常</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"操作失败: <span class="subst">{e}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"您需要再存入至少 <span class="subst">{e.amount - e.balance}</span> 元"</span>)</span><br><span class="line">    <span class="comment"># 可以在这里提供补救措施，比如自动转入资金或提供贷款选项</span></span><br></pre></td></tr></tbody></table></figure><table><thead><tr><th>自定义异常命名惯例</th><th>示例</th><th>适用场景</th></tr></thead><tbody><tr><td>以 “Error” 结尾</td><td><code>ValidationError</code></td><td>程序错误，需纠正</td></tr><tr><td>以 “Warning” 结尾</td><td><code>DeprecationWarning</code></td><td>警告级别的问题</td></tr><tr><td>以具体领域开头</td><td><code>DatabaseConnectionError</code></td><td>特定领域的异常</td></tr></tbody></table><h3 id="12-4-异常的传播与重新抛出"><a href="#12-4-异常的传播与重新抛出" class="headerlink" title="12.4 异常的传播与重新抛出"></a>12.4 异常的传播与重新抛出</h3><p>了解异常如何在调用栈中传播以及如何重新抛出异常对于构建稳健的错误处理系统至关重要。</p><h4 id="12-4-1-异常传播机制"><a href="#12-4-1-异常传播机制" class="headerlink" title="12.4.1 异常传播机制"></a>12.4.1 异常传播机制</h4><p>当异常发生时，Python 会沿着调用栈向上查找，直到找到相应的 <code>except</code> 子句处理该异常，如果没有处理程序，程序将终止。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 异常传播示例</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">func_inner</span>():</span><br><span class="line">    <span class="comment"># 这里的异常会向上传播</span></span><br><span class="line">    <span class="keyword">return</span> <span class="number">10</span> / <span class="number">0</span>  <span class="comment"># 引发 ZeroDivisionError</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">func_middle</span>():</span><br><span class="line">    <span class="comment"># 没有处理异常，所以异常继续传播</span></span><br><span class="line">    <span class="keyword">return</span> func_inner()</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">func_outer</span>():</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># 在这里捕获来自更深层次函数的异常</span></span><br><span class="line">        <span class="keyword">return</span> func_middle()</span><br><span class="line">    <span class="keyword">except</span> ZeroDivisionError:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"捕获了除零错误!"</span>)</span><br><span class="line">        <span class="keyword">return</span> <span class="literal">None</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 调用最外层函数</span></span><br><span class="line">result = func_outer()  <span class="comment"># 输出: 捕获了除零错误!</span></span><br></pre></td></tr></tbody></table></figure><h4 id="12-4-2-重新抛出异常"><a href="#12-4-2-重新抛出异常" class="headerlink" title="12.4.2 重新抛出异常"></a>12.4.2 重新抛出异常</h4><p>重新抛出异常有两种方式：</p><ol><li>直接使用 <code>raise</code> 语句不带参数</li><li>使用 <code>raise ... from ...</code> 结构表明异常的因果关系</li></ol><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">def</span> <span class="title function_">process_data</span>(<span class="params">data</span>):</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># 尝试处理数据</span></span><br><span class="line">        result = data[<span class="number">0</span>] / data[<span class="number">1</span>]</span><br><span class="line">        <span class="keyword">return</span> result</span><br><span class="line">    <span class="keyword">except</span> ZeroDivisionError:</span><br><span class="line">        <span class="comment"># 记录错误并重新抛出当前异常</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"除数不能为零！"</span>)</span><br><span class="line">        <span class="keyword">raise</span>  <span class="comment"># 直接重新抛出当前捕获的异常</span></span><br><span class="line">    <span class="keyword">except</span> IndexError <span class="keyword">as</span> e:</span><br><span class="line">        <span class="comment"># 捕获后转换为更具体的应用级异常，并保留原始错误信息</span></span><br><span class="line">        <span class="keyword">raise</span> ValueError(<span class="string">"数据格式不正确，需要至少两个元素"</span>) <span class="keyword">from</span> e</span><br><span class="line"></span><br><span class="line"><span class="comment"># 调用函数并处理异常</span></span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    <span class="comment"># 尝试处理带有问题的数据</span></span><br><span class="line">    result = process_data([<span class="number">10</span>])  <span class="comment"># 数组只有一个元素，会引发 IndexError</span></span><br><span class="line"><span class="keyword">except</span> ValueError <span class="keyword">as</span> e:</span><br><span class="line">    <span class="comment"># 处理转换后的异常</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"发生错误: <span class="subst">{e}</span>"</span>)</span><br><span class="line">    <span class="comment"># 访问原始异常</span></span><br><span class="line">    <span class="keyword">if</span> e.__cause__:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"原始错误: <span class="subst">{e.__cause__}</span>"</span>)  </span><br></pre></td></tr></tbody></table></figure><table><thead><tr><th>重新抛出方式</th><th>语法</th><th>适用场景</th></tr></thead><tbody><tr><td>简单重抛</td><td><code>raise</code></td><td>仅记录错误后继续传播</td></tr><tr><td>转换异常</td><td><code>raise NewError() from original_error</code></td><td>将低级异常转换为应用级异常</td></tr><tr><td>清除上下文</td><td><code>raise NewError() from None</code></td><td>隐藏原始异常(不推荐)</td></tr></tbody></table><h3 id="12-5-使用上下文管理器"><a href="#12-5-使用上下文管理器" class="headerlink" title="12.5 使用上下文管理器"></a>12.5 使用上下文管理器</h3><p>上下文管理器是 Python 的一种强大机制，通过 <code>with</code> 语句实现自动资源管理，特别适合处理需要显式打开和关闭的资源。</p><h4 id="12-5-1-with-语句和资源管理"><a href="#12-5-1-with-语句和资源管理" class="headerlink" title="12.5.1 with 语句和资源管理"></a>12.5.1 with 语句和资源管理</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 文件操作 - 最常见的上下文管理器应用场景</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'file.txt'</span>, <span class="string">'w'</span>) <span class="keyword">as</span> f:</span><br><span class="line">    f.write(<span class="string">'Hello, World!'</span>)</span><br><span class="line">    <span class="comment"># 可能发生异常的代码</span></span><br><span class="line">    <span class="comment"># raise ValueError("演示异常")</span></span><br><span class="line"><span class="comment"># 即使发生异常，文件也会自动关闭</span></span><br></pre></td></tr></tbody></table></figure><h4 id="12-5-2-自定义上下文管理器"><a href="#12-5-2-自定义上下文管理器" class="headerlink" title="12.5.2 自定义上下文管理器"></a>12.5.2 自定义上下文管理器</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">class</span> <span class="title class_">DatabaseConnection</span>:</span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, connection_string</span>):</span><br><span class="line">        <span class="variable language_">self</span>.connection_string = connection_string</span><br><span class="line">        <span class="variable language_">self</span>.connection = <span class="literal">None</span></span><br><span class="line">    </span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__enter__</span>(<span class="params">self</span>):</span><br><span class="line">        <span class="string">"""进入上下文时调用，返回值被赋给as后的变量"""</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"连接到数据库: <span class="subst">{self.connection_string}</span>"</span>)</span><br><span class="line">        <span class="comment"># 在实际应用中，这里会创建真正的数据库连接</span></span><br><span class="line">        <span class="variable language_">self</span>.connection = <span class="string">"已连接"</span>  </span><br><span class="line">        <span class="keyword">return</span> <span class="variable language_">self</span>.connection</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__exit__</span>(<span class="params">self, exc_type, exc_val, exc_tb</span>):</span><br><span class="line">        <span class="string">"""离开上下文时调用，无论是正常退出还是异常退出</span></span><br><span class="line"><span class="string">           参数: 异常类型、异常值、异常回溯信息"""</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"关闭数据库连接"</span>)</span><br><span class="line">        <span class="comment"># 释放资源</span></span><br><span class="line">        <span class="variable language_">self</span>.connection = <span class="literal">None</span></span><br><span class="line">        </span><br><span class="line">        <span class="comment"># 返回值决定异常处理:</span></span><br><span class="line">        <span class="comment"># - True: 表示异常已处理，不再传播</span></span><br><span class="line">        <span class="comment"># - False/None: 表示需要继续传播异常</span></span><br><span class="line">        <span class="keyword">return</span> <span class="literal">False</span>  <span class="comment"># 让异常继续传播</span></span><br></pre></td></tr></tbody></table></figure><h4 id="12-5-3-实际应用场景"><a href="#12-5-3-实际应用场景" class="headerlink" title="12.5.3 实际应用场景"></a>12.5.3 实际应用场景</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 使用自定义上下文管理器进行数据库操作</span></span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    <span class="keyword">with</span> DatabaseConnection(<span class="string">"mysql://localhost/mydb"</span>) <span class="keyword">as</span> conn:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"使用连接: <span class="subst">{conn}</span>"</span>)</span><br><span class="line">        <span class="comment"># 数据库操作代码</span></span><br><span class="line">        <span class="comment"># 模拟操作失败</span></span><br><span class="line">        <span class="comment"># raise ValueError("数据插入失败")</span></span><br><span class="line"><span class="keyword">except</span> Exception <span class="keyword">as</span> e:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"捕获到异常: <span class="subst">{e}</span>"</span>)</span><br><span class="line">    <span class="comment"># 处理数据库操作异常</span></span><br><span class="line">    <span class="comment"># 可能的恢复策略: 重试、记录日志、发送报警等</span></span><br></pre></td></tr></tbody></table></figure><table><thead><tr><th>常见上下文管理器</th><th>示例</th><th>自动管理的资源</th></tr></thead><tbody><tr><td><code>open()</code></td><td><code>with open('file.txt') as f:</code></td><td>文件句柄</td></tr><tr><td><code>threading.Lock()</code></td><td><code>with lock:</code></td><td>线程锁</td></tr><tr><td><code>contextlib.suppress()</code></td><td><code>with suppress(FileNotFoundError):</code></td><td>忽略特定异常</td></tr><tr><td><code>tempfile.NamedTemporaryFile()</code></td><td><code>with NamedTemporaryFile() as tmp:</code></td><td>临时文件</td></tr><tr><td><code>requests.Session()</code></td><td><code>with Session() as session:</code></td><td>HTTP 会话</td></tr></tbody></table><h4 id="12-5-4-使用-contextlib-简化上下文管理器创建"><a href="#12-5-4-使用-contextlib-简化上下文管理器创建" class="headerlink" title="12.5.4 使用 contextlib 简化上下文管理器创建"></a>12.5.4 使用 contextlib 简化上下文管理器创建</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> contextlib <span class="keyword">import</span> contextmanager</span><br><span class="line"></span><br><span class="line"><span class="meta">@contextmanager</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">file_manager</span>(<span class="params">filename, mode</span>):</span><br><span class="line">    <span class="string">"""一个使用生成器函数创建的上下文管理器"""</span></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># 设置阶段 - 获取资源</span></span><br><span class="line">        f = <span class="built_in">open</span>(filename, mode)</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"文件 <span class="subst">{filename}</span> 已打开"</span>)</span><br><span class="line">        <span class="comment"># yield 语句将控制权传递给 with 块内的代码</span></span><br><span class="line">        <span class="keyword">yield</span> f</span><br><span class="line">    <span class="keyword">finally</span>:</span><br><span class="line">        <span class="comment"># 清理阶段 - 释放资源</span></span><br><span class="line">        f.close()</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"文件 <span class="subst">{filename}</span> 已关闭"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用自定义上下文管理器</span></span><br><span class="line"><span class="keyword">with</span> file_manager(<span class="string">'example.txt'</span>, <span class="string">'w'</span>) <span class="keyword">as</span> file:</span><br><span class="line">    file.write(<span class="string">'这是一个使用contextlib创建的上下文管理器示例'</span>)</span><br></pre></td></tr></tbody></table></figure><h3 id="12-6-异常处理最佳实践"><a href="#12-6-异常处理最佳实践" class="headerlink" title="12.6 异常处理最佳实践"></a>12.6 异常处理最佳实践</h3><p>掌握异常处理的模式和反模式对于编写健壮的代码至关重要。</p><h4 id="12-6-1-不良实践与改进"><a href="#12-6-1-不良实践与改进" class="headerlink" title="12.6.1 不良实践与改进"></a>12.6.1 不良实践与改进</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 不好的做法：过于宽泛的异常捕获</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">bad_practice</span>():</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># 大量不同类型的操作混在一起</span></span><br><span class="line">        config = <span class="built_in">open</span>(<span class="string">"config.ini"</span>).read()</span><br><span class="line">        settings = parse_config(config)</span><br><span class="line">        result = process_data(settings)</span><br><span class="line">        save_result(result)</span><br><span class="line">    <span class="keyword">except</span>:</span><br><span class="line">        <span class="comment"># 捕获所有异常，无法区分不同错误</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"出错了"</span>)</span><br><span class="line">        <span class="comment"># 无法提供有价值的错误信息</span></span><br><span class="line">        <span class="comment"># 无法针对性恢复</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 好的做法：精确捕获和处理异常</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">good_practice</span>():</span><br><span class="line">    config = <span class="literal">None</span></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># 只包含读取配置文件的代码</span></span><br><span class="line">        config = <span class="built_in">open</span>(<span class="string">"config.ini"</span>)</span><br><span class="line">        config_text = config.read()</span><br><span class="line">    <span class="keyword">except</span> FileNotFoundError:</span><br><span class="line">        <span class="comment"># 针对性处理配置文件缺失</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"配置文件不存在，将使用默认配置"</span>)</span><br><span class="line">        config_text = DEFAULT_CONFIG</span><br><span class="line">    <span class="keyword">except</span> PermissionError:</span><br><span class="line">        <span class="comment"># 针对性处理权限问题</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"没有读取配置文件的权限"</span>)</span><br><span class="line">        <span class="comment"># 可以请求提升权限或使用备用方案</span></span><br><span class="line">        <span class="keyword">return</span> <span class="literal">None</span></span><br><span class="line">    <span class="keyword">finally</span>:</span><br><span class="line">        <span class="comment"># 确保文件被关闭</span></span><br><span class="line">        <span class="keyword">if</span> config:</span><br><span class="line">            config.close()</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># 解析配置的代码单独放在try块中</span></span><br><span class="line">        settings = parse_config(config_text)</span><br><span class="line">    <span class="keyword">except</span> ValueError <span class="keyword">as</span> e:</span><br><span class="line">        <span class="comment"># 处理配置格式错误</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"配置格式错误: <span class="subst">{e}</span>"</span>)</span><br><span class="line">        <span class="keyword">return</span> <span class="literal">None</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 后续操作...</span></span><br></pre></td></tr></tbody></table></figure><h4 id="12-6-2-实际开发中的异常处理策略"><a href="#12-6-2-实际开发中的异常处理策略" class="headerlink" title="12.6.2 实际开发中的异常处理策略"></a>12.6.2 实际开发中的异常处理策略</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 分层异常处理示例 - Web应用请求处理</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 1. 底层数据访问层: 转换为应用层可理解的异常</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">fetch_user_data</span>(<span class="params">user_id</span>):</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># 数据库操作</span></span><br><span class="line">        connection = get_db_connection()</span><br><span class="line">        cursor = connection.cursor()</span><br><span class="line">        cursor.execute(<span class="string">"SELECT * FROM users WHERE id = %s"</span>, (user_id,))</span><br><span class="line">        result = cursor.fetchone()</span><br><span class="line">        <span class="keyword">return</span> result</span><br><span class="line">    <span class="keyword">except</span> MySQLError <span class="keyword">as</span> e:</span><br><span class="line">        <span class="comment"># 转换为应用级异常</span></span><br><span class="line">        <span class="keyword">if</span> e.errno == <span class="number">1045</span>:  <span class="comment"># 访问被拒绝</span></span><br><span class="line">            <span class="keyword">raise</span> DatabaseAccessError(<span class="string">"数据库访问被拒绝"</span>) <span class="keyword">from</span> e</span><br><span class="line">        <span class="keyword">elif</span> e.errno == <span class="number">2003</span>:  <span class="comment"># 连接失败</span></span><br><span class="line">            <span class="keyword">raise</span> DatabaseConnectionError(<span class="string">"无法连接到数据库"</span>) <span class="keyword">from</span> e</span><br><span class="line">        <span class="keyword">else</span>:</span><br><span class="line">            <span class="keyword">raise</span> DatabaseError(<span class="string">f"数据库错误: <span class="subst">{e}</span>"</span>) <span class="keyword">from</span> e</span><br><span class="line">    <span class="keyword">finally</span>:</span><br><span class="line">        <span class="comment"># 资源清理</span></span><br><span class="line">        cursor.close()</span><br><span class="line">        connection.close()</span><br><span class="line"></span><br><span class="line"><span class="comment"># 2. 业务逻辑层: 处理应用级异常</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">get_user_profile</span>(<span class="params">user_id</span>):</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        user_data = fetch_user_data(user_id)</span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> user_data:</span><br><span class="line">            <span class="comment"># 应用逻辑异常</span></span><br><span class="line">            <span class="keyword">raise</span> UserNotFoundError(<span class="string">f"用户ID <span class="subst">{user_id}</span> 不存在"</span>)</span><br><span class="line">        <span class="keyword">return</span> format_user_profile(user_data)</span><br><span class="line">    <span class="keyword">except</span> DatabaseError <span class="keyword">as</span> e:</span><br><span class="line">        <span class="comment"># 日志记录并决定是否传播</span></span><br><span class="line">        logger.error(<span class="string">f"获取用户数据失败: <span class="subst">{e}</span>"</span>)</span><br><span class="line">        <span class="comment"># 可能的重试策略</span></span><br><span class="line">        <span class="keyword">if</span> <span class="built_in">isinstance</span>(e, DatabaseConnectionError) <span class="keyword">and</span> retry_count &lt; MAX_RETRIES:</span><br><span class="line">            <span class="keyword">return</span> get_user_profile_with_retry(user_id, retry_count + <span class="number">1</span>)</span><br><span class="line">        <span class="comment"># 传播异常供上层处理</span></span><br><span class="line">        <span class="keyword">raise</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 3. 接口层: 向用户展示友好错误</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">api_get_user</span>(<span class="params">request, user_id</span>):</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        profile = get_user_profile(user_id)</span><br><span class="line">        <span class="keyword">return</span> JSONResponse(status_code=<span class="number">200</span>, content=profile)</span><br><span class="line">    <span class="keyword">except</span> UserNotFoundError:</span><br><span class="line">        <span class="comment"># 返回适当的HTTP状态码</span></span><br><span class="line">        <span class="keyword">return</span> JSONResponse(status_code=<span class="number">404</span>, content={<span class="string">"error"</span>: <span class="string">"用户不存在"</span>})</span><br><span class="line">    <span class="keyword">except</span> DatabaseConnectionError:</span><br><span class="line">        <span class="comment"># 返回服务暂时不可用</span></span><br><span class="line">        <span class="keyword">return</span> JSONResponse(status_code=<span class="number">503</span>, content={<span class="string">"error"</span>: <span class="string">"服务暂时不可用"</span>})</span><br><span class="line">    <span class="keyword">except</span> Exception <span class="keyword">as</span> e:</span><br><span class="line">        <span class="comment"># 意外错误: 记录并返回通用错误</span></span><br><span class="line">        logger.critical(<span class="string">f"未处理的错误: <span class="subst">{e}</span>"</span>, exc_info=<span class="literal">True</span>)</span><br><span class="line">        <span class="keyword">return</span> JSONResponse(status_code=<span class="number">500</span>, content={<span class="string">"error"</span>: <span class="string">"服务器内部错误"</span>})</span><br></pre></td></tr></tbody></table></figure><h3 id="12-7-高级异常处理技术"><a href="#12-7-高级异常处理技术" class="headerlink" title="12.7 高级异常处理技术"></a>12.7 高级异常处理技术</h3><h4 id="12-7-1-使用装饰器简化异常处理"><a href="#12-7-1-使用装饰器简化异常处理" class="headerlink" title="12.7.1 使用装饰器简化异常处理"></a>12.7.1 使用装饰器简化异常处理</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> functools</span><br><span class="line"><span class="keyword">import</span> time</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">retry</span>(<span class="params">max_attempts=<span class="number">3</span>, delay=<span class="number">1</span></span>):</span><br><span class="line">    <span class="string">"""一个用于自动重试的装饰器</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    参数:</span></span><br><span class="line"><span class="string">        max_attempts: 最大尝试次数</span></span><br><span class="line"><span class="string">        delay: 重试之间的延迟(秒)</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">decorator</span>(<span class="params">func</span>):</span><br><span class="line"><span class="meta">        @functools.wraps(<span class="params">func</span>)</span></span><br><span class="line">        <span class="keyword">def</span> <span class="title function_">wrapper</span>(<span class="params">*args, **kwargs</span>):</span><br><span class="line">            attempts = <span class="number">0</span></span><br><span class="line">            <span class="keyword">while</span> attempts &lt; max_attempts:</span><br><span class="line">                <span class="keyword">try</span>:</span><br><span class="line">                    <span class="keyword">return</span> func(*args, **kwargs)</span><br><span class="line">                <span class="keyword">except</span> (ConnectionError, TimeoutError) <span class="keyword">as</span> e:</span><br><span class="line">                    attempts += <span class="number">1</span></span><br><span class="line">                    <span class="keyword">if</span> attempts &gt;= max_attempts:</span><br><span class="line">                        <span class="keyword">raise</span>  <span class="comment"># 达到最大尝试次数，重新抛出异常</span></span><br><span class="line">                    <span class="built_in">print</span>(<span class="string">f"操作失败: <span class="subst">{e}</span>，<span class="subst">{delay}</span>秒后重试 (<span class="subst">{attempts}</span>/<span class="subst">{max_attempts}</span>)"</span>)</span><br><span class="line">                    time.sleep(delay)</span><br><span class="line">            <span class="keyword">return</span> <span class="literal">None</span>  <span class="comment"># 不会执行到这里</span></span><br><span class="line">        <span class="keyword">return</span> wrapper</span><br><span class="line">    <span class="keyword">return</span> decorator</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用重试装饰器</span></span><br><span class="line"><span class="meta">@retry(<span class="params">max_attempts=<span class="number">3</span>, delay=<span class="number">2</span></span>)</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">connect_to_server</span>(<span class="params">url</span>):</span><br><span class="line">    <span class="string">"""连接到远程服务器，可能会失败"""</span></span><br><span class="line">    <span class="keyword">import</span> random</span><br><span class="line">    <span class="keyword">if</span> random.random() &lt; <span class="number">0.7</span>:  <span class="comment"># 模拟70%的失败率</span></span><br><span class="line">        <span class="keyword">raise</span> ConnectionError(<span class="string">"连接服务器失败"</span>)</span><br><span class="line">    <span class="keyword">return</span> <span class="string">"连接成功"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 调用带重试功能的函数</span></span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    result = connect_to_server(<span class="string">"https://example.com"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"结果: <span class="subst">{result}</span>"</span>)</span><br><span class="line"><span class="keyword">except</span> ConnectionError:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"连接服务器最终失败"</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="12-7-2-异常链与异常组"><a href="#12-7-2-异常链与异常组" class="headerlink" title="12.7.2 异常链与异常组"></a>12.7.2 异常链与异常组</h4><p>Python 3.10+ 引入了异常组(ExceptionGroup)和 except*语法，用于处理多个异常同时存在的情况：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> <span class="type">List</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># Python 3.10+ 特性：异常组</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">process_multiple_tasks</span>():</span><br><span class="line">    <span class="comment"># 用于收集任务处理过程中的错误</span></span><br><span class="line">    exceptions: <span class="type">List</span>[<span class="built_in">tuple</span>[<span class="built_in">str</span>, Exception]] = []</span><br><span class="line"></span><br><span class="line">    tasks = [(<span class="string">"task1"</span>, <span class="number">0</span>), (<span class="string">"task2"</span>, <span class="number">2</span>), (<span class="string">"task3"</span>, <span class="string">"not_a_number"</span>)]</span><br><span class="line"></span><br><span class="line">    <span class="keyword">for</span> task_name, value <span class="keyword">in</span> tasks:</span><br><span class="line">        <span class="keyword">try</span>:</span><br><span class="line">            <span class="comment"># 尝试处理任务</span></span><br><span class="line">            <span class="built_in">print</span>(<span class="string">f"处理任务 <span class="subst">{task_name}</span>，输入值为 <span class="subst">{value}</span>"</span>)</span><br><span class="line">            result = <span class="number">10</span> / value</span><br><span class="line">            <span class="built_in">print</span>(<span class="string">f"任务 <span class="subst">{task_name}</span> 处理结果为 <span class="subst">{result}</span>"</span>)</span><br><span class="line">        <span class="keyword">except</span> Exception <span class="keyword">as</span> e:</span><br><span class="line">            exceptions.append((task_name, e))</span><br><span class="line">    <span class="comment"># 如果有错误，以异常组的形式抛出</span></span><br><span class="line">    <span class="keyword">if</span> exceptions:</span><br><span class="line">        <span class="keyword">raise</span> ExceptionGroup(<span class="string">"处理任务过程中发生错误"</span>,</span><br><span class="line">                             [ValueError(<span class="string">f"任务 <span class="subst">{name}</span> 处理失败：<span class="subst">{err}</span>"</span>) <span class="keyword">for</span> name, err <span class="keyword">in</span> exceptions])</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用except*处理异常组</span></span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    process_multiple_tasks()</span><br><span class="line"><span class="keyword">except</span>* ZeroDivisionError <span class="keyword">as</span> eg:</span><br><span class="line">    <span class="comment"># 处理所有除零错误</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"除零错误: <span class="subst">{eg.exceptions}</span>"</span>) <span class="comment"># 这里就可以抓到task1的异常</span></span><br><span class="line"><span class="keyword">except</span>* TypeError <span class="keyword">as</span> eg:</span><br><span class="line">    <span class="comment"># 处理所有类型错误</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"类型错误: <span class="subst">{eg.exceptions}</span>"</span>) <span class="comment"># 这里就可以抓到task3的异常</span></span><br><span class="line"><span class="keyword">except</span>* Exception <span class="keyword">as</span> eg:</span><br><span class="line">    <span class="comment"># 处理其他所有错误</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"其他错误: <span class="subst">{eg.exceptions}</span>"</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="12-7-3-EAFP-vs-LBYL-编程风格"><a href="#12-7-3-EAFP-vs-LBYL-编程风格" class="headerlink" title="12.7.3 EAFP vs LBYL 编程风格"></a>12.7.3 EAFP vs LBYL 编程风格</h4><p>Python 通常推崇 EAFP（”Easier to Ask Forgiveness than Permission”）而非 LBYL（”Look Before You Leap”）：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># LBYL风格（先检查后操作）</span></span><br><span class="line"><span class="keyword">if</span> <span class="string">'key'</span> <span class="keyword">in</span> my_dict <span class="keyword">and</span> my_dict[<span class="string">'key'</span>] <span class="keyword">is</span> <span class="keyword">not</span> <span class="literal">None</span>:</span><br><span class="line">    value = my_dict[<span class="string">'key'</span>]</span><br><span class="line"><span class="keyword">else</span>:</span><br><span class="line">    value = <span class="string">'default'</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># EAFP风格（先操作后处理异常）</span></span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    value = my_dict[<span class="string">'key'</span>]</span><br><span class="line"><span class="keyword">except</span> (KeyError, TypeError):</span><br><span class="line">    value = <span class="string">'default'</span></span><br></pre></td></tr></tbody></table></figure></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/62937.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/62937.html&quot;)">Python（十三）：第十二章： 异常处理</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/62937.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=Python（十三）：第十二章： 异常处理&amp;url=https://prorise666.site/posts/62937.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Python<span class="categoryesPageCount">22</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Python基础知识总汇<span class="tagsPageCount">22</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/52396.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Python（十二）：第十一章：面向对象编程</div></div></a></div><div class="next-post pull-right"><a href="/posts/9962.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Python（十四）：第十三章： 高级数据处理</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/17730.html" title="Python（一）：Python 语言特性"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（一）：Python 语言特性</div></div></a></div><div><a href="/posts/8019.html" title="Python（三）：第二章：转义字符"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（三）：第二章：转义字符</div></div></a></div><div><a href="/posts/45310.html" title="Python（七）：第六章：条件循环分支"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（七）：第六章：条件循环分支</div></div></a></div><div><a href="/posts/56572.html" title="Python（九）：第八章： 函数知识总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（九）：第八章： 函数知识总结</div></div></a></div><div><a href="/posts/55902.html" title="Python（二十一）：第二十章：Python 语法新特性总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十一）：第二十章：Python 语法新特性总结</div></div></a></div><div><a href="/posts/2501.html" title="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Python（十三）：第十二章： 异常处理",date:"2025-04-19 04:13:45",updated:"2025-07-13 22:13:01",tags:["Python基础知识总汇"],categories:["后端技术","Python"],content:'\n## 第十二章： 异常处理\n\n异常处理是 Python 编程中的重要环节，它允许程序在遇到错误时优雅地恢复或退出，而不是直接崩溃。\n\n### 12.1 基本异常处理\n\n异常处理的核心是 `try-except` 结构，它允许程序捕获并处理运行时错误。\n\n#### 12.1.1 异常的概念与意义\n\n异常是程序运行时发生的错误，会打断正常的程序执行流程。Python 提供了强大的异常处理机制，使程序能够：\n\n- 预测可能的错误并妥善处理\n- 提供用户友好的错误信息\n- 防止程序意外终止\n- 实现优雅的错误恢复策略\n\n#### 12.1.2 基础 try-except 结构\n\n```python\ntry:\n    # 可能引发异常的代码\n    num = int(input("请输入一个数字: "))  # 可能引发 ValueError\n    result = 10 / num  # 可能引发 ZeroDivisionError\n    print(f"结果是: {result}")\nexcept ValueError:\n    # 处理特定异常\n    print("输入必须是数字!")\nexcept ZeroDivisionError:\n    # 处理另一种特定异常\n    print("不能除以零!")\nexcept:\n    # 捕获所有其他异常(不推荐这种写法)\n    print("发生了其他错误!")\n```\n\n| 常见内置异常      | 触发场景         | 示例                 |\n| ----------------- | ---------------- | -------------------- |\n| ValueError        | 传入无效值       | `int("abc")`         |\n| TypeError         | 类型不匹配       | `"2" + 2`            |\n| ZeroDivisionError | 除数为零         | `10/0`               |\n| IndexError        | 索引超出范围     | `[1,2,3][10]`        |\n| KeyError          | 字典中不存在的键 | `{"a":1}["b"]`       |\n| FileNotFoundError | 文件不存在       | `open("不存在.txt")` |\n| ImportError       | 导入模块失败     | `import 不存在模块`  |\n| AttributeError    | 对象没有特定属性 | `"hello".append(1)`  |\n\n### 12.2 完整的异常处理结构\n\n完整的异常处理结构包括 `try`, `except`, `else`, 和 `finally` 四个部分，每个部分负责不同的功能。\n\n#### 12.2.1 try-except-else-finally 完整结构\n\n```python\ntry:\n    # 可能引发异常的代码\n    num = int(input("请输入一个数字: "))\n    result = 10 / num\n    print(f"结果是: {result}")\nexcept ValueError as e:\n    # 处理特定异常，e 包含异常的详细信息\n    print(f"输入错误: {e}")  # e 可能显示: "invalid literal for int() with base 10: \'abc\'"\nexcept ZeroDivisionError as e:\n    # 处理另一种特定异常\n    print(f"除零错误: {e}")  # e 可能显示: "division by zero"\nexcept Exception as e:\n    # 处理所有其他异常(这种方式比空except更好)\n    print(f"其他错误: {e}")\nelse:\n    # 只有当try块中的代码执行成功且没有异常发生时执行\n    print("计算成功完成!")\nfinally:\n    # 无论是否有异常都会执行的代码块\n    print("异常处理结束")\n```\n\n#### 12.2.2 各代码块执行条件总结\n\n| 代码块    | 执行条件           | 典型用途           |\n| --------- | ------------------ | ------------------ |\n| `try`     | 必定执行           | 放置可能出错的代码 |\n| `except`  | 对应类型异常发生时 | 处理特定类型错误   |\n| `else`    | try 块无异常发生时 | 执行成功后的操作   |\n| `finally` | 无论有无异常均执行 | 资源清理、释放     |\n\n### 12.3 自定义异常\n\n虽然 Python 提供了丰富的内置异常，但在开发特定应用时，创建自定义异常可以使代码更具可读性和针对性。\n\n#### 12.3.1 创建自定义异常类\n\n```python\n# 自定义异常类，继承自 Exception\nclass InsufficientFundsError(Exception):\n    """当账户余额不足时引发的异常"""\n    \n    def __init__(self, balance, amount):\n        self.balance = balance\n        self.amount = amount\n        # 创建有意义的错误消息\n        self.message = f"余额不足: 当前余额 {balance} 元，尝试提取 {amount} 元"\n        super().__init__(self.message)\n    \n    def __str__(self):\n        return self.message\n```\n\n#### 12.3.2 使用自定义异常\n\n```python\n# 在业务逻辑中使用自定义异常\nclass BankAccount:\n    def __init__(self, balance=0):\n        self.balance = balance\n        \n    def withdraw(self, amount):\n        if amount > self.balance:\n            # 在适当的条件下抛出自定义异常\n            raise InsufficientFundsError(self.balance, amount)\n        self.balance -= amount\n        return amount\n\n# 处理自定义异常的实际场景\naccount = BankAccount(100)\ntry:\n    # 尝试提取超过余额的金额\n    account.withdraw(150)\nexcept InsufficientFundsError as e:\n    # 针对性地处理特定业务异常\n    print(f"操作失败: {e}")\n    print(f"您需要再存入至少 {e.amount - e.balance} 元")\n    # 可以在这里提供补救措施，比如自动转入资金或提供贷款选项\n```\n\n| 自定义异常命名惯例 | 示例                      | 适用场景         |\n| ------------------ | ------------------------- | ---------------- |\n| 以 "Error" 结尾    | `ValidationError`         | 程序错误，需纠正 |\n| 以 "Warning" 结尾  | `DeprecationWarning`      | 警告级别的问题   |\n| 以具体领域开头     | `DatabaseConnectionError` | 特定领域的异常   |\n\n### 12.4 异常的传播与重新抛出\n\n了解异常如何在调用栈中传播以及如何重新抛出异常对于构建稳健的错误处理系统至关重要。\n\n#### 12.4.1 异常传播机制\n\n当异常发生时，Python 会沿着调用栈向上查找，直到找到相应的 `except` 子句处理该异常，如果没有处理程序，程序将终止。\n\n```python\n# 异常传播示例\ndef func_inner():\n    # 这里的异常会向上传播\n    return 10 / 0  # 引发 ZeroDivisionError\n\ndef func_middle():\n    # 没有处理异常，所以异常继续传播\n    return func_inner()\n\ndef func_outer():\n    try:\n        # 在这里捕获来自更深层次函数的异常\n        return func_middle()\n    except ZeroDivisionError:\n        print("捕获了除零错误!")\n        return None\n\n# 调用最外层函数\nresult = func_outer()  # 输出: 捕获了除零错误!\n```\n\n#### 12.4.2 重新抛出异常\n\n重新抛出异常有两种方式：\n1. 直接使用 `raise` 语句不带参数\n2. 使用 `raise ... from ...` 结构表明异常的因果关系\n\n```python\ndef process_data(data):\n    try:\n        # 尝试处理数据\n        result = data[0] / data[1]\n        return result\n    except ZeroDivisionError:\n        # 记录错误并重新抛出当前异常\n        print("除数不能为零！")\n        raise  # 直接重新抛出当前捕获的异常\n    except IndexError as e:\n        # 捕获后转换为更具体的应用级异常，并保留原始错误信息\n        raise ValueError("数据格式不正确，需要至少两个元素") from e\n\n# 调用函数并处理异常\ntry:\n    # 尝试处理带有问题的数据\n    result = process_data([10])  # 数组只有一个元素，会引发 IndexError\nexcept ValueError as e:\n    # 处理转换后的异常\n    print(f"发生错误: {e}")\n    # 访问原始异常\n    if e.__cause__:\n        print(f"原始错误: {e.__cause__}")  \n```\n\n| 重新抛出方式 | 语法                                   | 适用场景                   |\n| ------------ | -------------------------------------- | -------------------------- |\n| 简单重抛     | `raise`                                | 仅记录错误后继续传播       |\n| 转换异常     | `raise NewError() from original_error` | 将低级异常转换为应用级异常 |\n| 清除上下文   | `raise NewError() from None`           | 隐藏原始异常(不推荐)       |\n\n### 12.5 使用上下文管理器\n\n上下文管理器是 Python 的一种强大机制，通过 `with` 语句实现自动资源管理，特别适合处理需要显式打开和关闭的资源。\n\n#### 12.5.1 with 语句和资源管理\n\n```python\n# 文件操作 - 最常见的上下文管理器应用场景\nwith open(\'file.txt\', \'w\') as f:\n    f.write(\'Hello, World!\')\n    # 可能发生异常的代码\n    # raise ValueError("演示异常")\n# 即使发生异常，文件也会自动关闭\n```\n\n#### 12.5.2 自定义上下文管理器\n\n```python\nclass DatabaseConnection:\n    def __init__(self, connection_string):\n        self.connection_string = connection_string\n        self.connection = None\n    \n    def __enter__(self):\n        """进入上下文时调用，返回值被赋给as后的变量"""\n        print(f"连接到数据库: {self.connection_string}")\n        # 在实际应用中，这里会创建真正的数据库连接\n        self.connection = "已连接"  \n        return self.connection\n    \n    def __exit__(self, exc_type, exc_val, exc_tb):\n        """离开上下文时调用，无论是正常退出还是异常退出\n           参数: 异常类型、异常值、异常回溯信息"""\n        print("关闭数据库连接")\n        # 释放资源\n        self.connection = None\n        \n        # 返回值决定异常处理:\n        # - True: 表示异常已处理，不再传播\n        # - False/None: 表示需要继续传播异常\n        return False  # 让异常继续传播\n```\n\n#### 12.5.3 实际应用场景\n\n```python\n# 使用自定义上下文管理器进行数据库操作\ntry:\n    with DatabaseConnection("mysql://localhost/mydb") as conn:\n        print(f"使用连接: {conn}")\n        # 数据库操作代码\n        # 模拟操作失败\n        # raise ValueError("数据插入失败")\nexcept Exception as e:\n    print(f"捕获到异常: {e}")\n    # 处理数据库操作异常\n    # 可能的恢复策略: 重试、记录日志、发送报警等\n```\n\n| 常见上下文管理器                | 示例                                | 自动管理的资源 |\n| ------------------------------- | ----------------------------------- | -------------- |\n| `open()`                        | `with open(\'file.txt\') as f:`       | 文件句柄       |\n| `threading.Lock()`              | `with lock:`                        | 线程锁         |\n| `contextlib.suppress()`         | `with suppress(FileNotFoundError):` | 忽略特定异常   |\n| `tempfile.NamedTemporaryFile()` | `with NamedTemporaryFile() as tmp:` | 临时文件       |\n| `requests.Session()`            | `with Session() as session:`        | HTTP 会话      |\n\n#### 12.5.4 使用 contextlib 简化上下文管理器创建\n\n```python\nfrom contextlib import contextmanager\n\n@contextmanager\ndef file_manager(filename, mode):\n    """一个使用生成器函数创建的上下文管理器"""\n    try:\n        # 设置阶段 - 获取资源\n        f = open(filename, mode)\n        print(f"文件 {filename} 已打开")\n        # yield 语句将控制权传递给 with 块内的代码\n        yield f\n    finally:\n        # 清理阶段 - 释放资源\n        f.close()\n        print(f"文件 {filename} 已关闭")\n\n# 使用自定义上下文管理器\nwith file_manager(\'example.txt\', \'w\') as file:\n    file.write(\'这是一个使用contextlib创建的上下文管理器示例\')\n```\n\n### 12.6 异常处理最佳实践\n\n掌握异常处理的模式和反模式对于编写健壮的代码至关重要。\n\n#### 12.6.1 不良实践与改进\n\n```python\n# 不好的做法：过于宽泛的异常捕获\ndef bad_practice():\n    try:\n        # 大量不同类型的操作混在一起\n        config = open("config.ini").read()\n        settings = parse_config(config)\n        result = process_data(settings)\n        save_result(result)\n    except:\n        # 捕获所有异常，无法区分不同错误\n        print("出错了")\n        # 无法提供有价值的错误信息\n        # 无法针对性恢复\n\n# 好的做法：精确捕获和处理异常\ndef good_practice():\n    config = None\n    try:\n        # 只包含读取配置文件的代码\n        config = open("config.ini")\n        config_text = config.read()\n    except FileNotFoundError:\n        # 针对性处理配置文件缺失\n        print("配置文件不存在，将使用默认配置")\n        config_text = DEFAULT_CONFIG\n    except PermissionError:\n        # 针对性处理权限问题\n        print("没有读取配置文件的权限")\n        # 可以请求提升权限或使用备用方案\n        return None\n    finally:\n        # 确保文件被关闭\n        if config:\n            config.close()\n    \n    try:\n        # 解析配置的代码单独放在try块中\n        settings = parse_config(config_text)\n    except ValueError as e:\n        # 处理配置格式错误\n        print(f"配置格式错误: {e}")\n        return None\n    \n    # 后续操作...\n```\n\n#### 12.6.2 实际开发中的异常处理策略\n\n```python\n# 分层异常处理示例 - Web应用请求处理\n\n# 1. 底层数据访问层: 转换为应用层可理解的异常\ndef fetch_user_data(user_id):\n    try:\n        # 数据库操作\n        connection = get_db_connection()\n        cursor = connection.cursor()\n        cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))\n        result = cursor.fetchone()\n        return result\n    except MySQLError as e:\n        # 转换为应用级异常\n        if e.errno == 1045:  # 访问被拒绝\n            raise DatabaseAccessError("数据库访问被拒绝") from e\n        elif e.errno == 2003:  # 连接失败\n            raise DatabaseConnectionError("无法连接到数据库") from e\n        else:\n            raise DatabaseError(f"数据库错误: {e}") from e\n    finally:\n        # 资源清理\n        cursor.close()\n        connection.close()\n\n# 2. 业务逻辑层: 处理应用级异常\ndef get_user_profile(user_id):\n    try:\n        user_data = fetch_user_data(user_id)\n        if not user_data:\n            # 应用逻辑异常\n            raise UserNotFoundError(f"用户ID {user_id} 不存在")\n        return format_user_profile(user_data)\n    except DatabaseError as e:\n        # 日志记录并决定是否传播\n        logger.error(f"获取用户数据失败: {e}")\n        # 可能的重试策略\n        if isinstance(e, DatabaseConnectionError) and retry_count < MAX_RETRIES:\n            return get_user_profile_with_retry(user_id, retry_count + 1)\n        # 传播异常供上层处理\n        raise\n\n# 3. 接口层: 向用户展示友好错误\ndef api_get_user(request, user_id):\n    try:\n        profile = get_user_profile(user_id)\n        return JSONResponse(status_code=200, content=profile)\n    except UserNotFoundError:\n        # 返回适当的HTTP状态码\n        return JSONResponse(status_code=404, content={"error": "用户不存在"})\n    except DatabaseConnectionError:\n        # 返回服务暂时不可用\n        return JSONResponse(status_code=503, content={"error": "服务暂时不可用"})\n    except Exception as e:\n        # 意外错误: 记录并返回通用错误\n        logger.critical(f"未处理的错误: {e}", exc_info=True)\n        return JSONResponse(status_code=500, content={"error": "服务器内部错误"})\n```\n\n### 12.7 高级异常处理技术\n\n#### 12.7.1 使用装饰器简化异常处理\n\n```python\nimport functools\nimport time\n\ndef retry(max_attempts=3, delay=1):\n    """一个用于自动重试的装饰器\n    \n    参数:\n        max_attempts: 最大尝试次数\n        delay: 重试之间的延迟(秒)\n    """\n    def decorator(func):\n        @functools.wraps(func)\n        def wrapper(*args, **kwargs):\n            attempts = 0\n            while attempts < max_attempts:\n                try:\n                    return func(*args, **kwargs)\n                except (ConnectionError, TimeoutError) as e:\n                    attempts += 1\n                    if attempts >= max_attempts:\n                        raise  # 达到最大尝试次数，重新抛出异常\n                    print(f"操作失败: {e}，{delay}秒后重试 ({attempts}/{max_attempts})")\n                    time.sleep(delay)\n            return None  # 不会执行到这里\n        return wrapper\n    return decorator\n\n# 使用重试装饰器\n@retry(max_attempts=3, delay=2)\ndef connect_to_server(url):\n    """连接到远程服务器，可能会失败"""\n    import random\n    if random.random() < 0.7:  # 模拟70%的失败率\n        raise ConnectionError("连接服务器失败")\n    return "连接成功"\n\n# 调用带重试功能的函数\ntry:\n    result = connect_to_server("https://example.com")\n    print(f"结果: {result}")\nexcept ConnectionError:\n    print("连接服务器最终失败")\n```\n\n#### 12.7.2 异常链与异常组\n\nPython 3.10+ 引入了异常组(ExceptionGroup)和 except*语法，用于处理多个异常同时存在的情况：\n\n```python\nfrom typing import List\n\n\n# Python 3.10+ 特性：异常组\ndef process_multiple_tasks():\n    # 用于收集任务处理过程中的错误\n    exceptions: List[tuple[str, Exception]] = []\n\n    tasks = [("task1", 0), ("task2", 2), ("task3", "not_a_number")]\n\n    for task_name, value in tasks:\n        try:\n            # 尝试处理任务\n            print(f"处理任务 {task_name}，输入值为 {value}")\n            result = 10 / value\n            print(f"任务 {task_name} 处理结果为 {result}")\n        except Exception as e:\n            exceptions.append((task_name, e))\n    # 如果有错误，以异常组的形式抛出\n    if exceptions:\n        raise ExceptionGroup("处理任务过程中发生错误",\n                             [ValueError(f"任务 {name} 处理失败：{err}") for name, err in exceptions])\n\n\n# 使用except*处理异常组\ntry:\n    process_multiple_tasks()\nexcept* ZeroDivisionError as eg:\n    # 处理所有除零错误\n    print(f"除零错误: {eg.exceptions}") # 这里就可以抓到task1的异常\nexcept* TypeError as eg:\n    # 处理所有类型错误\n    print(f"类型错误: {eg.exceptions}") # 这里就可以抓到task3的异常\nexcept* Exception as eg:\n    # 处理其他所有错误\n    print(f"其他错误: {eg.exceptions}")\n```\n\n#### 12.7.3 EAFP vs LBYL 编程风格\n\nPython 通常推崇 EAFP（"Easier to Ask Forgiveness than Permission"）而非 LBYL（"Look Before You Leap"）：\n\n```python\n# LBYL风格（先检查后操作）\nif \'key\' in my_dict and my_dict[\'key\'] is not None:\n    value = my_dict[\'key\']\nelse:\n    value = \'default\'\n\n# EAFP风格（先操作后处理异常）\ntry:\n    value = my_dict[\'key\']\nexcept (KeyError, TypeError):\n    value = \'default\'\n```'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E4%BA%8C%E7%AB%A0%EF%BC%9A-%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86"><span class="toc-number">1.</span> <span class="toc-text">第十二章： 异常处理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#12-1-%E5%9F%BA%E6%9C%AC%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86"><span class="toc-number">1.1.</span> <span class="toc-text">12.1 基本异常处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-1-1-%E5%BC%82%E5%B8%B8%E7%9A%84%E6%A6%82%E5%BF%B5%E4%B8%8E%E6%84%8F%E4%B9%89"><span class="toc-number">1.1.1.</span> <span class="toc-text">12.1.1 异常的概念与意义</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-1-2-%E5%9F%BA%E7%A1%80-try-except-%E7%BB%93%E6%9E%84"><span class="toc-number">1.1.2.</span> <span class="toc-text">12.1.2 基础 try-except 结构</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-2-%E5%AE%8C%E6%95%B4%E7%9A%84%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E7%BB%93%E6%9E%84"><span class="toc-number">1.2.</span> <span class="toc-text">12.2 完整的异常处理结构</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-2-1-try-except-else-finally-%E5%AE%8C%E6%95%B4%E7%BB%93%E6%9E%84"><span class="toc-number">1.2.1.</span> <span class="toc-text">12.2.1 try-except-else-finally 完整结构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-2-2-%E5%90%84%E4%BB%A3%E7%A0%81%E5%9D%97%E6%89%A7%E8%A1%8C%E6%9D%A1%E4%BB%B6%E6%80%BB%E7%BB%93"><span class="toc-number">1.2.2.</span> <span class="toc-text">12.2.2 各代码块执行条件总结</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-3-%E8%87%AA%E5%AE%9A%E4%B9%89%E5%BC%82%E5%B8%B8"><span class="toc-number">1.3.</span> <span class="toc-text">12.3 自定义异常</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-3-1-%E5%88%9B%E5%BB%BA%E8%87%AA%E5%AE%9A%E4%B9%89%E5%BC%82%E5%B8%B8%E7%B1%BB"><span class="toc-number">1.3.1.</span> <span class="toc-text">12.3.1 创建自定义异常类</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-3-2-%E4%BD%BF%E7%94%A8%E8%87%AA%E5%AE%9A%E4%B9%89%E5%BC%82%E5%B8%B8"><span class="toc-number">1.3.2.</span> <span class="toc-text">12.3.2 使用自定义异常</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-4-%E5%BC%82%E5%B8%B8%E7%9A%84%E4%BC%A0%E6%92%AD%E4%B8%8E%E9%87%8D%E6%96%B0%E6%8A%9B%E5%87%BA"><span class="toc-number">1.4.</span> <span class="toc-text">12.4 异常的传播与重新抛出</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-4-1-%E5%BC%82%E5%B8%B8%E4%BC%A0%E6%92%AD%E6%9C%BA%E5%88%B6"><span class="toc-number">1.4.1.</span> <span class="toc-text">12.4.1 异常传播机制</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-4-2-%E9%87%8D%E6%96%B0%E6%8A%9B%E5%87%BA%E5%BC%82%E5%B8%B8"><span class="toc-number">1.4.2.</span> <span class="toc-text">12.4.2 重新抛出异常</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-5-%E4%BD%BF%E7%94%A8%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8"><span class="toc-number">1.5.</span> <span class="toc-text">12.5 使用上下文管理器</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-5-1-with-%E8%AF%AD%E5%8F%A5%E5%92%8C%E8%B5%84%E6%BA%90%E7%AE%A1%E7%90%86"><span class="toc-number">1.5.1.</span> <span class="toc-text">12.5.1 with 语句和资源管理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-5-2-%E8%87%AA%E5%AE%9A%E4%B9%89%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8"><span class="toc-number">1.5.2.</span> <span class="toc-text">12.5.2 自定义上下文管理器</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-5-3-%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.5.3.</span> <span class="toc-text">12.5.3 实际应用场景</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-5-4-%E4%BD%BF%E7%94%A8-contextlib-%E7%AE%80%E5%8C%96%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8%E5%88%9B%E5%BB%BA"><span class="toc-number">1.5.4.</span> <span class="toc-text">12.5.4 使用 contextlib 简化上下文管理器创建</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-6-%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5"><span class="toc-number">1.6.</span> <span class="toc-text">12.6 异常处理最佳实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-6-1-%E4%B8%8D%E8%89%AF%E5%AE%9E%E8%B7%B5%E4%B8%8E%E6%94%B9%E8%BF%9B"><span class="toc-number">1.6.1.</span> <span class="toc-text">12.6.1 不良实践与改进</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-6-2-%E5%AE%9E%E9%99%85%E5%BC%80%E5%8F%91%E4%B8%AD%E7%9A%84%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E7%AD%96%E7%95%A5"><span class="toc-number">1.6.2.</span> <span class="toc-text">12.6.2 实际开发中的异常处理策略</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#12-7-%E9%AB%98%E7%BA%A7%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86%E6%8A%80%E6%9C%AF"><span class="toc-number">1.7.</span> <span class="toc-text">12.7 高级异常处理技术</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#12-7-1-%E4%BD%BF%E7%94%A8%E8%A3%85%E9%A5%B0%E5%99%A8%E7%AE%80%E5%8C%96%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86"><span class="toc-number">1.7.1.</span> <span class="toc-text">12.7.1 使用装饰器简化异常处理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-7-2-%E5%BC%82%E5%B8%B8%E9%93%BE%E4%B8%8E%E5%BC%82%E5%B8%B8%E7%BB%84"><span class="toc-number">1.7.2.</span> <span class="toc-text">12.7.2 异常链与异常组</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#12-7-3-EAFP-vs-LBYL-%E7%BC%96%E7%A8%8B%E9%A3%8E%E6%A0%BC"><span class="toc-number">1.7.3.</span> <span class="toc-text">12.7.3 EAFP vs LBYL 编程风格</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>