---
title: 产品经理进阶（四）：第四章：电商后台产品设计
categories: 产品经理
tags:
  - 产品经理教程
cover: 'https://bu.dusays.com/2025/07/25/6882f31a48223.webp'
comments: true
toc: true
ai: true
abbrlink: 26490
date: 2025-07-24 19:13:45
---

# 第四章：电商后台产品设计

欢迎来到第四章。在上一章，我们已经为用户，设计出了一套完整、华丽的“**前台**”购物体验。但支撑这一切前台体验能够顺利运转的，是一个我们普通用户永远看不到的、强大而复杂的“**后台**”系统。

后台，就是我们电商平台的“**中央厨房**”与“**指挥中心**”。本章，我们就将学习如何设计这个核心系统。

## 4.1 学习目标

在本节中，我的核心目标是，带大家建立起对电商后台的**宏观架构认知**。我们将学习后台的核心作用，并重点理解，在招商模式下，如何清晰地划分“**商家业务**”与“**平台业务**”，并最终绘制出我们整个电商生态的“**业务模块架构图**”。

## 4.2 电商后台核心作用及架构

### 4.2.1 后台对前台业务的支撑作用

![image-20250722161730158](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722161730158.png)

我理解的电商后台，其最核心、最根本的作用，就是**为前台的所有业务，提供支撑**。
* 前台用户能看到的每一个**商品**，都是由后台的“**商品中心**”来管理的。
* 用户下的每一笔**订单**，都是由后台的“**订单中心**”和“**支付中心**”来处理的。
* 订单能被顺利**配送**，则依赖于后台的“**物流中心**”和“**WMS（仓储管理系统）**”。

前台是光鲜亮丽的“餐厅”，而后台，就是保证餐厅能正常运转的“厨房”、“仓库”和“收银系统”的总和。

### 4.2.2 商家业务 vs 平台运营业务划分

![image-20250722161835579](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722161835579.png)

对于我们“大P超级电商”这样的**招商模式**平台，我需要特别澄清一点：我们的“后台”，其实并不是一个单一的系统，而是**两个既相互独立、又紧密关联的系统**：

**1. 商家后台**
这是我设计给**入驻商家**使用的后台。它是商家在我们平台上“开店经营”的专属工作台。其核心的业务方向，是服务于商家的日常经营，包括：

* `入驻平台/管理店铺`
* `发布/管理商品`
* `订单跟进`
* `售后处理`

**2. 平台运营后台**
这是我设计给我们**自己公司内部员工**（运营、审核、客服等）使用的后台。它是我们管理整个平台的“上帝视角”控制台。其核心的业务方向，是**管理和监督**，包括：

* `审核商家入驻`
* `审核商家发布的商品`
* `订单跟进（仲裁）`
* `管理平台所有的商家和用户`

清晰地划分这两套后台的业务边界，是我进行后台架构设计的第一步。

### 4.2.3 整体业务模块架构

![image-20250722162014848](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722162014848.png)

最后，我会将我们整个电商生态的所有功能模块，汇总成一张“**整体业务模块架构图**”。这张图，就是我们整个项目的“总蓝图”。

如案例图所示，这张架构图，清晰地划分出了我们项目的三大组成部分：

* **用户端**：即我们在第三章设计的，面向C端消费者的前台App。
* **商家端**：即我们即将设计的，面向B端商家的后台管理系统。
* **平台端**：即我们即将设计的，面向我们自己内部员工的后台管理系统。

在每一个“端”的下面，我会罗列出它所包含的核心功能模块。比如，`平台端`就包含了`商品管理`、`订单管理`、`系统管理`、`店铺管理`等模块。

![image-20250722162110994](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722162110994.png)

这张架构图，不仅能让所有项目成员，对我们产品的全貌一目了然，更能清晰地揭示出，前台的每一个核心业务，是由后台的哪一个模块来提供支撑的。它是我们后续进行详细设计和技术架构设计的“最高纲领”。




---
## 4.3 平台运营后台核心设计

现在，我们开始设计我们自己公司内部员工使用的“**上帝视角**”后台——**平台运营后台**。

这个系统的设计，我最看重的三个原则是：**安全、高效、可追溯**。因为在这里的每一个操作，都可能会对我们平台上的用户、商家，乃至整个平台的声誉，产生直接的影响。

本节，我们重点设计其中两个最高频、也最重要的模块：**C端用户管理**和**商家管理**。

### 4.3.1 C端用户管理（用户状态与权限操作）

这个模块，赋予了我们的运营和客服同事，管理平台上所有C端消费者（买家）的权力。

#### 1. 用户列表与查询

![image-20250722170937934](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722170937934.png)

正如案例图所示，这个界面的核心，是一个完整的用户列表。为了让我的运营同事，能从数以万计的用户中，精准地找到目标，我必须为他们设计一套强大的查询和筛选功能。

| 筛选字段 | 我的设计说明 |
| :--- | :--- |
| **用户信息** | 提供一个统一的输入框，支持按`用户昵称`、`手机号`、甚至`用户ID`进行模糊或精确搜索。 |
| **注册时间** | 提供一个日期范围选择器，方便运营分析特定时期内（如：某次活动期间）的新增用户。 |
| **用户状态**| **这是最重要的筛选条件**。提供一个下拉菜单，让客服或风控团队能快速筛选出所有`正常`、`已禁用`的用户，进行集中的管理。 |

#### 2. 用户状态与权限操作

“**操作**”列，是这个模块“权力”的体现。一个严谨的设计，是**根据用户当前的状态，来提供不同的操作权限**。

* **当用户状态为“正常”时**：
    我提供的核心操作是 `查看`、`编辑`、`禁用`。
    * **我的拓展设计**：点击“**禁用**”时，我不会让它立刻生效。我会设计一个弹窗，要求操作员必须**选择一个禁用的时长**（如：7天、30天、永久）并**填写禁用的原因**。这个原因，一部分会展示给用户，另一部分则会作为内部日志，记录在案，以备查验。

* **当用户状态为“禁用”时**：
    “禁用”按钮，则会变为“**解禁**”按钮。操作员点击后，二次确认，即可将该用户恢复为正常状态。

这种“**基于状态的权限设计**”，能极大地避免运营人员的误操作，让后台管理更规范、更安全。

### 4.3.2 商家入驻审核与店铺管理

管理商家（卖家），比管理用户要复杂得多，因为它直接关系到我们平台的商品供给、服务质量和核心收入。

#### 1. 商家入驻审核

![image-20250722171315184](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171315184.png)

这是我们平台的“**第一道门卫**”。所有想在我们平台开店的商家，都必须经过这一环节的资质审核。我会把它设计成一个独立的工作台。
* **待审核列表**：页面的默认视图，是一个“**待审核**”队列，展示了所有提交了入驻申请，但尚未被处理的商家。
* **审核详情页**：运营点击“审核”后，会进入一个详情页，能看到该商家提交的所有信息，包括`营业执照`、`法人信息`、`品牌授权书`等。在这个页面，只有两个核心的操作按钮：“**通过**”和“**驳回**”。
* **我的拓展设计**：点击“**驳回**”时，我同样会要求运营，必须从一个预设的“驳回原因列表”（如：资质不全、信息不符）中选择一项，或手动输入驳回原因。这个原因，将通过短信或站内信，清晰地告知申请者，提升平台的专业度。

#### 2. 店铺管理

![image-20250722171327591](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171327591.png)

当商家审核通过，拥有了自己的店铺后，他们就会进入到“**店铺管理**”列表中。
* **核心信息**：这个列表，除了展示商家的基本信息外，更需要展示`店铺名称`、`主营类目`、`入驻时间`、`店铺状态`等运营信息。
* **核心操作**：针对“店铺”这个主体，我设计的操作权限，会比针对“用户”的权限，更谨慎、层级更高。
    * `查看店铺`：可以一键跳转到该店铺的前台页面。
    * `冻结店铺`：当商家出现较严重的违规行为（如售假）时，运营可以暂时“**冻结**”其店铺。冻结期间，店铺所有商品都会被下架，商家也无法登录后台。
    * `关闭店铺`：这是最严厉的处罚。意味着与该商家终止合作，将其清退出平台。这个操作，我通常会设计为，需要“**运营主管**”及以上级别的角色，才能执行。



---
## 4.4 商家后台核心设计

在上一节，我们设计了我们自己内部团队使用的“平台运营后台”。现在，我们要来设计一个同样重要，但使用者完全不同的系统——**商家后台**。

这是我们为所有入驻我们“大P超级电商”平台的第三方商家，提供的专属“**线上店铺办公室**”。这个后台的体验好坏，直接决定了我们能否吸引和留住优质的商家，是保障我们平台商品丰富度和供给侧质量的生命线。

### 4.4.1 商家入驻流程与类型（京东、淘宝/天猫、自营/非自营）

在我设计商家后台的第一个功能“**商家入驻**”时，我首先要思考一个战略问题：**我们希望吸引什么样的商家？**
* 像**淘宝**一样，追求“让天下没有难做的生意”，允许个人和企业“**免费开店**”，降低门槛，最大化地丰富商品供给？
* 还是像**天猫**一样，只面向具备一定**品质和资质的企业**，抬高门槛，保障平台的品牌调性？

![image-20250722171019089](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171019089.png)

这个决策，决定了我们入驻流程的复杂度和审核的严格度。对于我们 V1.0 的招商模式，我们将主要设计一个**非自营（POP）商家的入驻流程**。

![image-20250722171035591](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171035591.png)

一个专业的商家入驻流程，远比用户注册要复杂。我设计的流程，通常包含以下几个核心阶段：
1.  **入驻前准备**：在申请入口，我会清晰地告知商家，需要提前准备好哪些资质文件。
2.  **在线申请**：引导商家填写一系列的申请信息。
3.  **平台审核**：商家提交后，申请单会进入我们上一节设计的“平台运营后台-商家审核”模块，由我们的运营同事进行审核。
4.  **开店任务**：审核通过后，我会引导商家完成一系列的开店准备工作，如**缴纳费用、签署在线合同**等。

![image-20250722171136628](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171136628.png)

在“在线申请”这个环节，我设计的表单，会清晰地区分“**个人类型**”和“**企业类型**”的商家，因为他们需要提交的`个人信息`和`公司信息`（如营业执照）是完全不同的。

![image-20250722171206667](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171206667.png)

在“开店任务”环节，我设计的流程，必须清晰地向商家展示需要缴纳的费用。这通常包括：
* **平台使用费/年费**：按年收取的技术服务费。
* **保证金**：一笔押金，用于在商家出现违规行为、损害消费者利益时，对消费者进行赔付。

### 4.4.2 商家子账号设置与权限划分

![image-20250722171544019](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171544019.png)

当一个商家的店铺成功开张，生意越做越大，一个新需求就出现了：**店主（主账号）一个人忙不过来了**，他需要让手下的员工（如：客服、运营、库管）来一起管理店铺。但是，他又不能把最高权限的“主账号”密码，告诉所有人。

为了解决这个痛点，我必须为商家后台，设计一个“**子账号**”管理功能。这本质上，是为每一个商家，提供了一套**迷你的、店铺内部的RBAC（基于角色的访问控制）系统**。

我的设计，主要包含两个核心功能：

![image-20250722171852869](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722171852869.png)

1.  **角色管理**：我会允许“**主账号**”，在后台**创建不同的角色**（如：“客服专员”、“运营专员”），并为这些角色，**勾选分配不同的操作权限**（如：“客服专员”只能查看订单和回复咨询，但无权修改商品价格）。
2.  **员工管理**：主账号可以在这里，为他的每一位员工，**创建一个子账号**，并为这个子账号，**指定一个我们上面创建好的角色**。

这个功能，是区分一个“玩具级”和一个“企业级”商家后台的重要标志。

## 4.5 本章总结

在本章，我们深入到了电商“冰山”的水下部分，系统地学习了后台的设计。
* **核心作用与架构**：我们明确了后台是为前台服务的“指挥中心”，并学会了如何划分**商家业务**和**平台业务**，搭建起**用户端、商家端、平台端**三位一体的宏观产品架构。
* **平台运营后台**：我们设计了平台自己使用的核心模块，包括如何进行**C端用户管理**（特别是状态控制），以及如何建立一套严谨的**商家入驻审核与店铺管理**流程。
* **商家后台**：我们设计了服务于商家的核心模块，包括如何根据不同平台定位，设计差异化的**商家入驻流程**，以及如何通过**子账号**功能，来满足商家的团队协作与权限管理需求。

---