<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>第五部分：性能巅峰优化策略与实践 | Prorise的小站</title><meta name="keywords" content="博客搭建教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="第五部分：性能巅峰优化策略与实践"><meta name="application-name" content="第五部分：性能巅峰优化策略与实践"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="第五部分：性能巅峰优化策略与实践"><meta property="og:url" content="https://prorise666.site/posts/30787.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第五部分：性能巅峰优化策略与实践构建一个高性能的Hexo博客对于提升用户体验至关重要，同时也是搜索引擎排名的重要考量因素。要实现“毫秒级”的加载速度，我们需要系统性地优化，而优化的第一步，也是最有效的一步，就是处理网站中体积最大的资源——图片。核心原则是：让访客从离他们地理位置最近的服务器加载图片。"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp"><meta name="description" content="第五部分：性能巅峰优化策略与实践构建一个高性能的Hexo博客对于提升用户体验至关重要，同时也是搜索引擎排名的重要考量因素。要实现“毫秒级”的加载速度，我们需要系统性地优化，而优化的第一步，也是最有效的一步，就是处理网站中体积最大的资源——图片。核心原则是：让访客从离他们地理位置最近的服务器加载图片。"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/30787.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"第五部分：性能巅峰优化策略与实践",postAI:"true",pageFillDescription:"第五部分：性能巅峰优化策略与实践, 1.图片加载优化, 国内CDN加速镜像节点汇总, 如何科学地测试这些节点的速度, 在线多节点测速工具（全国访问体验测试 - 最重要）, 方案一：GitHub + 国内CDN镜像 (零成本最优方案), 方案二：国内对象存储 + CDN (专业级性能方案), 总结与最终选择建议, 2.图片压缩策略, 前言：为什么图片优化至关重要？, 1. 图片压缩：在画质与体积之间找到平衡, 2. 格式选择：为不同场景选择最优格式, 3. 图片懒加载 (Lazy Loading)：提升首屏速度的关键, 3. 静态资源（JSx2FCSS）优化：压缩与按需加载, 2. 浏览器缓存与CDN：让回头客秒开网站, 3. 字体加载优化：告别文字消失术, 4.关键渲染优化, 优化目标, 第一部分：问题诊断与分析, 1.1 检查当前配置状态, 1.2 性能问题分析, 第二部分：核心配置优化, 2.1 精简inject配置, 第三部分：智能资源管理器, 3.1 创建load-on-demand.js文件, 3.2 功能特性说明, 第四部分：ECharts统计页面优化, 4.1 修改统计页面配置, 4.2 创建Charts页面模板, 第五部分：页面模板清理, 5.1 清理Essay页面模板, 5.2 清理Todolist页面模板, 第六部分：测试与验证, 6.1 清理和重新生成, 6.2 浏览器缓存清理, 6.3 性能测试, 6.4 功能验证, 5.使用Lighthouse进行科学的性能评测, 前言：从感觉快到数据快——为何需要线上评测？, 第一步：临时部署到 GitHub Pages 获取测试网址, 第二步：使用Lighthouse进行性能评测第五部分性能巅峰优化策略与实践构建一个高性能的博客对于提升用户体验至关重要同时也是搜索引擎排名的重要考量因素要实现毫秒级的加载速度我们需要系统性地优化而优化的第一步也是最有效的一步就是处理网站中体积最大的资源图片核心原则是让访客从离他们地理位置最近的服务器加载图片对于以国内用户为主的中文博客这意味着必须使用位于中国大陆的存储和网络图片加载优化现在您可以根据您的核心需求来做决定如果您的第一要务是零成本并且您的读者遍布全球或您不介意国内偶尔访问慢那么方案一是一个完美的起点如果您的第一要务是为国内访客提供最快最稳定的体验并且您追求性能最强的网站这个目标愿意为此投入少量预算那么方案二国内对象存储是毫无疑问的更专业的最佳选择对于大多数博客用户尤其是在项目初期或面向全球读者时这是一个免费便捷且高效的图床方案国内加速镜像节点汇总以下几类提供可用的国内加速镜像镜像用于加速和资源这些节点可以用来替换官方的智云加速镜像专门加速包这些节点可以用来替换官方的使用时需在域名后加饿了么知乎镜像这个节点可以用来替换官方的南方科技大学如何科学地测试这些节点的速度要找到最适合您的节点不能只凭感觉需要进行客观测试我推荐您结合使用以下两种方法在线多节点测速工具全国访问体验测试最重要这个方法可以模拟全国各地不同网络电信联通移动的用户访问您的资源时的速度这比您自己本地测试的结果更具参考价值选择一个测试工具站长工具超级准备测试链接从我们上面的镜像列表中选择一个您想测试的节点的完整资源链接例如进行测试将这个链接粘贴到测速网站的输入框中开始测试网站会从全国各地的服务器去请求这个链接并返回每个地点的响应时间和状态分析结果看平均响应时间时间越短越好看失败率选择那些全国范围内绿色正常节点多红色超时失败节点少的重复测试对您感兴趣的几个不同镜像重复这个过程进行横向对比对于公益镜像请记住它们的稳定性可能随时变化建议您可以定期进行抽查测试方案一国内镜像零成本最优方案这个方案是当前针对国内环境的最佳免费方案放弃了官方不稳定的转而使用由国内社区或第三方提供的对中国大陆网络更友好的加速镜像工作流程创建图床仓库在上创建一个新的公开仓库专门用于存放您的博客图片例如命名为生成前往设置生成一个新在权限选择中至少需要勾选请务必复制并保存好这个因为它只显示一次安装并配置关键步骤下载并安装客户端在插件设置中搜索并安装插件在图床设置中填写您的信息最大的不同在于字段配置示例您的用户名您的仓库名您的可选的图片存储路径关键将设置为您测试过最快的国内加速镜像您的用户名您的仓库名关于国内镜像除了您也可以尝试使用其他镜像地址替换选择一个您测试下来最稳定的即可上传与使用设置好后通过拖拽图片到窗口上传会自动将基于国内镜像的链接复制到您的剪贴板您直接在文章中粘贴使用优缺点分析优点完全免费配置相对简单国内访问速度远超官方缺点稳定性依赖于镜像提供方这些是公益服务可能随时会因为滥用成本或政策原因而失效不适合对稳定性有极高要求的生产环境方案二国内对象存储专业级性能方案如果您追求的是企业级的稳定性和最顶级的加载速度那么这个方案是您的不二之选工作流程开通云服务在阿里云或腾讯云注册并开通对象存储服务创建一个存储桶并将其读写权限设置为公共读配置加速在对应的云服务商后台为您的存储桶开通并绑定一个加速域名强烈建议使用您自己的一个子域名例如并根据引导完成解析获取访问密钥在云服务商的访问控制后台创建一个专用的子用户并为其生成和重要为这个子用户授予仅能管理您那个存储桶的权限而不是全局权限配置客户端在的插件设置中安装对应的插件例如或在图床设置中填入您的名称存储区域以及您在第二步中配置好的加速域名优缺点分析优点速度最快最稳定有服务保障并支持图片处理防盗链等丰富的专业功能缺点需要少量费用但个人博客用量成本极低每月通常在几元以内初始配置步骤最多总结与最终选择建议感谢您的指正让这份笔记变得更加完整和准确对于起步阶段或个人实验性项目如果您追求零成本方案一是一个非常出色的选择它解决了的主要痛点对于追求极致性能和长期稳定性的严肃博客方案二是更值得投资的专业选择它能为您的国内访客提供最顶级的访问体验图片压缩策略前言为什么图片优化至关重要图片通常是网页中体积最大的资源它直接决定了您网站的加载速度一个加载缓慢的网站会严重影响用户体验并可能导致访客流失因此对图片进行全面优化是在追求毫秒级性能道路上投入产出比最高的一环一个完整的图片优化流程包含四个核心策略压缩格式选择懒加载以及加速图片压缩在画质与体积之间找到平衡是什么通过特定的算法在保持可接受的图像质量的前提下尽可能地减小图片文件的体积它分为有损压缩会损失部分图像细节压缩率高和无损压缩不损失细节压缩率较低格式选择为不同场景选择最优格式不同的图片格式有不同的特性为您的图片选择最合适的格式能从源头上优化性能方法工具作用推荐场景优势劣势图片压缩有损无损减小文件体积任何图片直接减小传输大小有损压缩可能略微影响图片质量在线工具在线批量压缩零散图片处理无需安装方便快捷压缩率高依赖网络可能受限于上传大小数量客户端工具离线批量压缩图片大量图片处理离线操作离线处理可配置压缩参数需要安装软件图片格式选择使用更优格式新增图片格式体积更小支持透明和动画兼容性旧浏览器部分工具不支持新一代图片格式现代浏览器要求性能体积小质量高部分旧浏览器可能不支持摄影图片色彩丰富广泛兼容适合照片不支持透明度压缩明显透明背景图片图标截图需要透明度支持透明度体积通常比大不适合照片矢量图图标图表无损缩放体积小可编辑支持动画不适合复杂照片图片懒加载延迟加载非首屏图片页面图片多提升首屏速度节省带宽需要支持可能影响部分图片索引内置主题集成用户配置简单无需额外插件功能由主题决定手动插件各种主题高度定制灵活性高可控性强配置复杂可能需要编写代码插件图床加速图片分发加速任何博客提升图片加载速度减轻服务器压力需要额外服务或配置配合免费个人博客静态站点免费方便配合全球节点依赖可用性偶尔不稳定对象存储商业存储流量大要求高可用性稳定可靠专业服务有成本图片懒加载提升首屏速度的关键是什么懒加载是一种延迟加载技术它只在用户即将滚动到图片位置时才开始加载这张图片这可以极大地减少页面的初始加载体积让首屏内容更快地显示出来对也很有利怎么做好消息是使用的主题已经内置了强大的懒加载功能您只需在主题配置文件中确保它是开启的在中确保这里是表示全站生效加载前的占位图请确保此路径有图片或使用图床链接图片加载前是否显示模糊效果静态资源优化压缩与按需加载是什么压缩移除和代码中所有不影响功能的字符如空格换行注释等以减小文件体积合并将多个小的或文件合并成一个大文件以减少浏览器需要发起的请求次数按需加载只在需要某个功能的页面才加载对应的或文件避免在全站加载不必要的资源怎么做解决方案使用压缩插件这是实现压缩和合并最简单的方法推荐使用插件安装插件在您博客根目录的终端中运行配置插件在根目录文件末尾添加配置它会自动在时处理您的文件压缩配置压缩压缩排除已经压缩过的文件压缩排除已经压缩过的文件利用主题功能实现按需加载正如我们之前在配置代码沙箱自定义评论样式等功能时所做的将只在特定页面使用的和通过在文章的文件中直接编写和标签来引入而不是通过主题的进行全局注入这就是一种有效的按需加载实践浏览器缓存与让回头客秒开网站浏览器缓存是什么您可以把它想象成浏览器在第一次访问您的网站后拍下了一张快照即将图片等资源保存在您的电脑上当您第二次访问时浏览器会直接使用本地的快照而无需再次从服务器下载从而实现秒开怎么做这通常不需要您在中配置它是在您的网站托管平台上进行设置的好消息是像这类现代化的托管平台都已经为您配置了非常合理的默认缓存策略您通常无需关心是什么我们之前在讨论图床时已经深入了解过它的核心是将您的静态资源不仅仅是图片也包括和文件部署到全球各地的服务器节点上让用户从最近的节点加载怎么做主题的中已经有了配置项它允许您将主题依赖的第三方库如等的提供商从默认的更换为速度更快的国内镜像例如我们之前讨论过的或这也是一种重要的优化我们将使用这个配置为每一个重要的库指定一个经过我们测试的速度最快的国内镜像源核心与基础库主题内部核心文件如等建议保持由直接生成以确保主题功能稳定页面功能与特效评论系统如有需要可自行查找的链接如有需要可自行查找的链接搜索系统本地搜索通常与主题内部关联建议保持音乐播放器其他不常用或建议保持默认的库不蒜子官方脚本通常不建议替换字体加载优化告别文字消失术自定义字体虽然能提升美观度但巨大的字体文件是拖慢速度的元凶之一并可能导致在字体加载完成前页面上的文字完全不可见怎么做三大策略使用格式这是目前最先进的网页字体格式压缩率最高文件体积最小请务必将您使用的或字体通过在线工具转换为格式再使用字体子集化这是一项高级技术如果您的字体只用于或标题仅使用了少数几十个字符您可以通过字体子集化工具从完整的几万个字符的字体文件中只抽取出您用到的这几十个字符生成一个体积极小的定制化的字体文件善用这是最简单也最重要的优化它告诉浏览器在自定义字体还没下载好之前请先用电脑里有的默认字体把文字显示出来不要让用户看一片空白等我的字体下载好了你再悄悄地把它换上配置方法在您的自定义文件例如中确保您的规则里包含了这一行规则定义自定义字体定义字体族名称格式优先格式作为后备字体粗细字体样式关键字体加载策略先用系统字体加载完再替换在或特定元素上应用自定义字体使用自定义字体作为备用通过以上这些针对静态资源和字体的优化您博客的性能和加载体验将得到全面的提升关键渲染优化在我们前面的无脑集成中的确步骤都非常方便但所有的样式和样式都在首页时渲染在用户进入首页时加载库加载别的页面的这无疑会大大降低了首页的渲染速度那么为什么我们要到这一章节才来优化而不在一开始就做呢由于我们是按需集成的市面上大部分的集成步骤也都是这样比起在一开始就要注意每一个的集成步骤不如我们集成完了之后再来统一管理会更好一些优化目标解决等重型库导致的首页秒加载问题实现谁生病谁吃药的按需加载策略保持所有功能完整性的前提下大幅提升性能预期效果首页加载时间秒秒减少首页资源数量个文件个文件减少移动端体验显著提升第一部分问题诊断与分析检查当前配置状态首先打开您的主题配置文件检查当前的资源注入情况文件位置找到配置段您可能会看到类似这样的配置大量文件全局加载大量文件全局加载性能问题分析问题诊断库的重型图表库在每个页面都加载冗余即刻短文待办清单等功能的样式在所有页面加载无用首页加载了文章页才需要的评论脚本移动端负担大量资源严重影响移动设备体验性能影响首页加载个个文件网络请求过多瀑布流效应明显解析和执行时间过长内存占用过高第二部分核心配置优化精简配置打开配置文件找到配置段通常在文件末尾附近完全替换配置将原有的配置替换为以下内容只保留全站必需的基础资源修复首页样式优先加载避免首屏渲染延迟轻量级按需加载资源管理器全站必需重要说明我们只保留了字体全站必需添加了即将创建的智能资源管理器移除了所有页面专用的资源第三部分智能资源管理器创建文件操作步骤创建文件目录如果不存在创建资源管理器文件文件位置完整代码请复制粘贴整个文件内容按需加载资源管理器用于优化网站性能只在需要时加载特定资源动态加载文件文件路径可选的元素动态加载文件文件路径可选的元素检测页面内容并按需加载相关资源检测是否为首页修复现在由头部优先加载只需加载检测是否为文章页检测站视频内容检测代码块检测评论区检测即刻短文页面检测待办清单页面检测侧边栏相关功能侧边栏脚本加载失败创建全局实例页面加载完成后自动检测为提供支持功能特性说明智能检测机制页面类型检测根据路径和元素判断页面类型内容特征检测检测站视频代码块评论区等特殊内容防重复加载确保每个资源只加载一次兼容支持无刷新页面跳转错误处理加载失败时的优雅降级详细的错误日志记录不影响其他功能的正常运行第四部分统计页面优化修改统计页面配置检查统计页面找到统计页面文件检查是否存在统计页面编辑统计页面配置文件位置查找并修改如果文件包含内联的脚本将其替换为网站统计创建页面模板操作步骤检查是否支持类型文件位置找到语句确保包含处理其他类型如果没有行请添加创建页面模板文件位置创建文件并添加完整内容统计页面专用模板显示页面内容统计页面专用资源加载延迟加载以优化性能正在加载库加载完成加载失败页面加载完成后再加载给用户一个加载提示正在加载图表延迟秒后开始加载避免阻塞页面渲染加载完成初始化图表函数未找到请确保相关脚本已加载清除加载提示正在加载图表加载失败显示错误信息图表加载失败请刷新页面重试第五部分页面模板清理清理页面模板文件位置查找文件开头如果包含资源加载脚本请移除修改前如果存在类似代码即刻短文专用样式修改后简化为本人不太会所以可能代码不太好看保留原有的页面内容代码清理页面模板文件位置查找文件开头如果包含资源加载脚本请移除修改前如果存在类似代码页面专用样式修改后简化为保留原有的页面内容代码第六部分测试与验证清理和重新生成必须按顺序执行以下命令清理缓存重新生成网站启动本地服务器测试浏览器缓存清理清理浏览器缓存按或按或或者打开开发者工具标签页右键刷新按钮清空缓存并硬性重新加载性能测试开发者工具验证打开开发者工具标签页刷新首页观察加载的资源数量应该只看到个主要文件和注意等大文件不应该在首页加载标签页录制首页加载过程对比优化前后的加载时间标签页检查是否有错误信息查看资源加载的日志信息功能验证逐一测试各个页面首页样式正常加载速度快文章页评论样式正常文章样式完整统计页图表正常显示有加载提示即刻短文页样式正常待办清单页样式正常侧边栏日历背景等功能正常恭喜优化完成如果一切正常您已经成功完成了性能优化使用进行科学的性能评测前言从感觉快到数据快为何需要线上评测我们之前做的所有优化最终效果都需要通过客观数据来衡量是完成这项任务最专业的工具但是和这类外部评测工具无法访问您电脑上的本地地址为了获得一份准确的性能报告我们首先需要将博客临时部署到一个任何人都可以公开访问的地址是完成这个临时部署以供测试任务最简单最快速的免费平台第一步临时部署到获取测试网址这个阶段的唯一目的就是为我们的博客生成一个公开的创建仓库登录您的账号创建一个新的公开仓库仓库名称必须遵循特殊格式您的用户名根据之前的我的仓库名应该就是安装部署插件在您博客的根目录终端中运行配置站点配置文件打开您博客根目录下的文件修改字段将其临时设置为您的地址这是为了让生成的所有链接在测试环境中都是正确的暂时将设置为你的地址修改字段在文件末尾配置部署信息推荐使用地址请进入您在上的仓库页面点击顶部的设置选项卡在左侧菜单中点击在打开的页面中找到构建和部署部分在源下面请确保选项是从分支部署在分支下面请再次确认下拉菜单中选择的是并且文件夹选项选择的是执行部署在终端中运行获取您的测试等待一两分钟让完成部署后在浏览器中访问您的线上地址如果能看到您的博客说明临时部署成功这个地址就是我们下一步要用来测试的地址如果这里的设置不正确请修改并点击如果这里的设置已经是正确的那么通常只是部署生效的延迟第二步使用进行性能评测现在我们有了公开的网址可以开始正式评测了在开发者工具中运行最推荐在浏览器中打开您刚刚部署好的线上博客地址按打开开发者工具找到并切换到选项卡在设备中选择手机在类别中勾选性能以及其他您关心的项目点击分析页面加载按钮解读报告核心指标测试完成后您会得到一份详细报告请重点关注分数以及下面这几个核心指标指标名称测量内容理想得分浏览器渲染出第一个内容的时间秒以内视口内最大可见元素加载完成的时间秒以内主线程被长任务阻塞的总时间毫秒以内页面加载过程中视觉元素的跳动量以下关注优化建议报告中最有价值的部分是下方的机遇和诊断它会明确告诉您哪些图片过大哪些阻塞了渲染哪些没有被使用等等您可以根据这些具体的建议回头再去调整您的配置或代码后续步骤在您根据的报告对您的博客进行了多轮的优化和测试并对性能分数感到满意之后我们才算真正完成了性能优化这个章节到那时真正的上一个服务器即将您的博客部署到您自己的服务器并绑定这样的顶级域名才会是我们的下一个大主题",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-19 19:21:28",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E9%83%A8%E5%88%86%EF%BC%9A%E6%80%A7%E8%83%BD%E5%B7%85%E5%B3%B0%E4%BC%98%E5%8C%96%E7%AD%96%E7%95%A5%E4%B8%8E%E5%AE%9E%E8%B7%B5"><span class="toc-text">第五部分：性能巅峰优化策略与实践</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%9B%BE%E7%89%87%E5%8A%A0%E8%BD%BD%E4%BC%98%E5%8C%96"><span class="toc-text">1.图片加载优化</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%9B%BD%E5%86%85CDN%E5%8A%A0%E9%80%9F%E9%95%9C%E5%83%8F%E8%8A%82%E7%82%B9%E6%B1%87%E6%80%BB"><span class="toc-text">国内CDN加速镜像节点汇总</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%A6%82%E4%BD%95%E7%A7%91%E5%AD%A6%E5%9C%B0%E6%B5%8B%E8%AF%95%E8%BF%99%E4%BA%9B%E8%8A%82%E7%82%B9%E7%9A%84%E9%80%9F%E5%BA%A6"><span class="toc-text">如何科学地测试这些节点的速度</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%A8%E7%BA%BF%E5%A4%9A%E8%8A%82%E7%82%B9%E6%B5%8B%E9%80%9F%E5%B7%A5%E5%85%B7%EF%BC%88%E5%85%A8%E5%9B%BD%E8%AE%BF%E9%97%AE%E4%BD%93%E9%AA%8C%E6%B5%8B%E8%AF%95-%E6%9C%80%E9%87%8D%E8%A6%81%EF%BC%89"><span class="toc-text">在线多节点测速工具（全国访问体验测试 - 最重要）</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%96%B9%E6%A1%88%E4%B8%80%EF%BC%9AGitHub-%E5%9B%BD%E5%86%85CDN%E9%95%9C%E5%83%8F-%E9%9B%B6%E6%88%90%E6%9C%AC%E6%9C%80%E4%BC%98%E6%96%B9%E6%A1%88"><span class="toc-text">方案一：GitHub + 国内CDN镜像 (零成本最优方案)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%96%B9%E6%A1%88%E4%BA%8C%EF%BC%9A%E5%9B%BD%E5%86%85%E5%AF%B9%E8%B1%A1%E5%AD%98%E5%82%A8-CDN-%E4%B8%93%E4%B8%9A%E7%BA%A7%E6%80%A7%E8%83%BD%E6%96%B9%E6%A1%88"><span class="toc-text">方案二：国内对象存储 + CDN (专业级性能方案)</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%80%BB%E7%BB%93%E4%B8%8E%E6%9C%80%E7%BB%88%E9%80%89%E6%8B%A9%E5%BB%BA%E8%AE%AE"><span class="toc-text">总结与最终选择建议</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%9B%BE%E7%89%87%E5%8E%8B%E7%BC%A9%E7%AD%96%E7%95%A5"><span class="toc-text">2.图片压缩策略</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A%E4%B8%BA%E4%BB%80%E4%B9%88%E5%9B%BE%E7%89%87%E4%BC%98%E5%8C%96%E8%87%B3%E5%85%B3%E9%87%8D%E8%A6%81%EF%BC%9F"><span class="toc-text">前言：为什么图片优化至关重要？</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E5%9B%BE%E7%89%87%E5%8E%8B%E7%BC%A9%EF%BC%9A%E5%9C%A8%E7%94%BB%E8%B4%A8%E4%B8%8E%E4%BD%93%E7%A7%AF%E4%B9%8B%E9%97%B4%E6%89%BE%E5%88%B0%E5%B9%B3%E8%A1%A1"><span class="toc-text">1. 图片压缩：在画质与体积之间找到平衡</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E6%A0%BC%E5%BC%8F%E9%80%89%E6%8B%A9%EF%BC%9A%E4%B8%BA%E4%B8%8D%E5%90%8C%E5%9C%BA%E6%99%AF%E9%80%89%E6%8B%A9%E6%9C%80%E4%BC%98%E6%A0%BC%E5%BC%8F"><span class="toc-text">2. 格式选择：为不同场景选择最优格式</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E5%9B%BE%E7%89%87%E6%87%92%E5%8A%A0%E8%BD%BD-Lazy-Loading-%EF%BC%9A%E6%8F%90%E5%8D%87%E9%A6%96%E5%B1%8F%E9%80%9F%E5%BA%A6%E7%9A%84%E5%85%B3%E9%94%AE"><span class="toc-text">3. 图片懒加载 (Lazy Loading)：提升首屏速度的关键</span></a></li></ol></li></ol></li></ol><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E9%9D%99%E6%80%81%E8%B5%84%E6%BA%90%EF%BC%88JS-CSS%EF%BC%89%E4%BC%98%E5%8C%96%EF%BC%9A%E5%8E%8B%E7%BC%A9%E4%B8%8E%E6%8C%89%E9%9C%80%E5%8A%A0%E8%BD%BD"><span class="toc-text">3. 静态资源（JS/CSS）优化：压缩与按需加载</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E6%B5%8F%E8%A7%88%E5%99%A8%E7%BC%93%E5%AD%98%E4%B8%8ECDN%EF%BC%9A%E8%AE%A9%E5%9B%9E%E5%A4%B4%E5%AE%A2%E2%80%9C%E7%A7%92%E5%BC%80%E2%80%9D%E7%BD%91%E7%AB%99"><span class="toc-text">2. 浏览器缓存与CDN：让回头客“秒开”网站</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E5%AD%97%E4%BD%93%E5%8A%A0%E8%BD%BD%E4%BC%98%E5%8C%96%EF%BC%9A%E5%91%8A%E5%88%AB%E2%80%9C%E6%96%87%E5%AD%97%E6%B6%88%E5%A4%B1%E6%9C%AF%E2%80%9D"><span class="toc-text">3. 字体加载优化：告别“文字消失术”</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E5%85%B3%E9%94%AE%E6%B8%B2%E6%9F%93%E4%BC%98%E5%8C%96"><span class="toc-text">4.关键渲染优化</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%BC%98%E5%8C%96%E7%9B%AE%E6%A0%87"><span class="toc-text">优化目标</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E9%83%A8%E5%88%86%EF%BC%9A%E9%97%AE%E9%A2%98%E8%AF%8A%E6%96%AD%E4%B8%8E%E5%88%86%E6%9E%90"><span class="toc-text">第一部分：问题诊断与分析</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-1-%E6%A3%80%E6%9F%A5%E5%BD%93%E5%89%8D%E9%85%8D%E7%BD%AE%E7%8A%B6%E6%80%81"><span class="toc-text">1.1 检查当前配置状态</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#1-2-%E6%80%A7%E8%83%BD%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90"><span class="toc-text">1.2 性能问题分析</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E9%83%A8%E5%88%86%EF%BC%9A%E6%A0%B8%E5%BF%83%E9%85%8D%E7%BD%AE%E4%BC%98%E5%8C%96"><span class="toc-text">第二部分：核心配置优化</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#2-1-%E7%B2%BE%E7%AE%80inject%E9%85%8D%E7%BD%AE"><span class="toc-text">2.1 精简inject配置</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E9%83%A8%E5%88%86%EF%BC%9A%E6%99%BA%E8%83%BD%E8%B5%84%E6%BA%90%E7%AE%A1%E7%90%86%E5%99%A8"><span class="toc-text">第三部分：智能资源管理器</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#3-1-%E5%88%9B%E5%BB%BAload-on-demand-js%E6%96%87%E4%BB%B6"><span class="toc-text">3.1 创建load-on-demand.js文件</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-2-%E5%8A%9F%E8%83%BD%E7%89%B9%E6%80%A7%E8%AF%B4%E6%98%8E"><span class="toc-text">3.2 功能特性说明</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E5%9B%9B%E9%83%A8%E5%88%86%EF%BC%9AECharts%E7%BB%9F%E8%AE%A1%E9%A1%B5%E9%9D%A2%E4%BC%98%E5%8C%96"><span class="toc-text">第四部分：ECharts统计页面优化</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#4-1-%E4%BF%AE%E6%94%B9%E7%BB%9F%E8%AE%A1%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE"><span class="toc-text">4.1 修改统计页面配置</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-2-%E5%88%9B%E5%BB%BACharts%E9%A1%B5%E9%9D%A2%E6%A8%A1%E6%9D%BF"><span class="toc-text">4.2 创建Charts页面模板</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E9%83%A8%E5%88%86%EF%BC%9A%E9%A1%B5%E9%9D%A2%E6%A8%A1%E6%9D%BF%E6%B8%85%E7%90%86"><span class="toc-text">第五部分：页面模板清理</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#5-1-%E6%B8%85%E7%90%86Essay%E9%A1%B5%E9%9D%A2%E6%A8%A1%E6%9D%BF"><span class="toc-text">5.1 清理Essay页面模板</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#5-2-%E6%B8%85%E7%90%86Todolist%E9%A1%B5%E9%9D%A2%E6%A8%A1%E6%9D%BF"><span class="toc-text">5.2 清理Todolist页面模板</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E5%85%AD%E9%83%A8%E5%88%86%EF%BC%9A%E6%B5%8B%E8%AF%95%E4%B8%8E%E9%AA%8C%E8%AF%81"><span class="toc-text">第六部分：测试与验证</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#6-1-%E6%B8%85%E7%90%86%E5%92%8C%E9%87%8D%E6%96%B0%E7%94%9F%E6%88%90"><span class="toc-text">6.1 清理和重新生成</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#6-2-%E6%B5%8F%E8%A7%88%E5%99%A8%E7%BC%93%E5%AD%98%E6%B8%85%E7%90%86"><span class="toc-text">6.2 浏览器缓存清理</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#6-3-%E6%80%A7%E8%83%BD%E6%B5%8B%E8%AF%95"><span class="toc-text">6.3 性能测试</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#6-4-%E5%8A%9F%E8%83%BD%E9%AA%8C%E8%AF%81"><span class="toc-text">6.4 功能验证</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-%E4%BD%BF%E7%94%A8Lighthouse%E8%BF%9B%E8%A1%8C%E7%A7%91%E5%AD%A6%E7%9A%84%E6%80%A7%E8%83%BD%E8%AF%84%E6%B5%8B"><span class="toc-text">5.使用Lighthouse进行科学的性能评测</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A%E4%BB%8E%E2%80%9C%E6%84%9F%E8%A7%89%E5%BF%AB%E2%80%9D%E5%88%B0%E2%80%9C%E6%95%B0%E6%8D%AE%E5%BF%AB%E2%80%9D%E2%80%94%E2%80%94%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81%E7%BA%BF%E4%B8%8A%E8%AF%84%E6%B5%8B%EF%BC%9F"><span class="toc-text">前言：从“感觉快”到“数据快”——为何需要线上评测？</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E4%B8%B4%E6%97%B6%E9%83%A8%E7%BD%B2%E5%88%B0-GitHub-Pages-%E8%8E%B7%E5%8F%96%E6%B5%8B%E8%AF%95%E7%BD%91%E5%9D%80"><span class="toc-text">第一步：临时部署到 GitHub Pages 获取测试网址</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E4%BD%BF%E7%94%A8Lighthouse%E8%BF%9B%E8%A1%8C%E6%80%A7%E8%83%BD%E8%AF%84%E6%B5%8B"><span class="toc-text">第二步：使用Lighthouse进行性能评测</span></a></li></ol></li></ol></li></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5f2a23">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#277340">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#c72008">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#11a7a2">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#276d10">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#6d6a95">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>博客搭建教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">第五部分：性能巅峰优化策略与实践</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-02T08:13:45.000Z" title="发表于 2025-07-02 16:13:45">2025-07-02</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-19T11:21:28.512Z" title="更新于 2025-07-19 19:21:28">2025-07-19</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">9.2k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>34分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="第五部分：性能巅峰优化策略与实践"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/30787.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/30787.html"><header><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">博客搭建教程</a><h1 id="CrawlerTitle" itemprop="name headline">第五部分：性能巅峰优化策略与实践</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-02T08:13:45.000Z" title="发表于 2025-07-02 16:13:45">2025-07-02</time><time itemprop="dateCreated datePublished" datetime="2025-07-19T11:21:28.512Z" title="更新于 2025-07-19 19:21:28">2025-07-19</time></header><div id="postchat_postcontent"><h2 id="第五部分：性能巅峰优化策略与实践"><a href="#第五部分：性能巅峰优化策略与实践" class="headerlink" title="第五部分：性能巅峰优化策略与实践"></a>第五部分：性能巅峰优化策略与实践</h2><p>构建一个高性能的Hexo博客对于提升用户体验至关重要，同时也是搜索引擎排名的重要考量因素。要实现“毫秒级”的加载速度，我们需要系统性地优化，而优化的第一步，也是最有效的一步，就是处理网站中体积最大的资源——图片。<br>核心原则是：让访客从离他们地理位置最近的服务器加载图片。 对于以国内用户为主的中文博客，这意味着必须使用位于中国大陆的存储和CDN网络</p><hr><h3 id="1-图片加载优化"><a href="#1-图片加载优化" class="headerlink" title="1.图片加载优化"></a>1.图片加载优化</h3><p>现在，您可以根据您的核心需求来做决定：</p><ul><li><p>如果您的<strong>第一要务是【零成本】</strong>，并且您的读者遍布全球（或您不介意国内偶尔访问慢），那么 <strong>方案一 (GitHub + jsDelivr)</strong> 是一个完美的起点。</p></li><li><p>如果您的<strong>第一要务是【为国内访客提供最快、最稳定的体验】</strong>，并且您追求“性能最强的网站”这个目标，愿意为此投入少量预算，那么 <strong>方案二 (国内对象存储 + CDN)</strong> 是毫无疑问的、更专业的最佳选择。<br>对于大多数 Hexo 博客用户，尤其是在项目初期或面向全球读者时，这是一个免费、便捷且高效的图床方案。</p></li></ul><hr><h4 id="国内CDN加速镜像节点汇总"><a href="#国内CDN加速镜像节点汇总" class="headerlink" title="国内CDN加速镜像节点汇总"></a><strong>国内CDN加速镜像节点汇总</strong></h4><p>以下几类提供可用的国内CDN加速镜像：</p><p><strong>1. jsDelivr 镜像 (用于加速 GitHub 和 npm 资源)</strong><br>这些节点可以用来替换官方的 <code>cdn.jsdelivr.net</code>。</p><ul><li><code>jsd-proxy.ygxz.in</code></li><li><code>cdn.iocdn.cc</code></li><li><code>cdn.jsdmirror.com</code></li><li><code>jsdelivr.topthink.com</code></li><li><code>cdn.smartcis.cn</code> (智云加速)</li><li><code>jsd.cdn.zzko.cn</code></li><li><code>jsd.onmicrosoft.cn</code></li><li><code>jsdelivr.b-cdn.net</code></li><li><code>cdn.jsdelivr.us</code></li></ul><p><strong>2. unpkg / npm 镜像 (专门加速 npm 包)</strong><br>这些节点可以用来替换官方的 <code>unpkg.com</code>。</p><ul><li><code>s4.zstatic.net/npm</code> (Zstatic，使用时需在域名后加 <code>/npm</code>)</li><li><code>npm.elemecdn.com</code> (饿了么)</li><li><code>npm.onmicrosoft.cn</code></li><li><code>unpkg.zhimg.com</code> (知乎)</li></ul><p><strong>3. cdnjs 镜像</strong><br>这个节点可以用来替换官方的 <code>cdnjs.cloudflare.com</code>。</p><ul><li><code>s4.zstatic.net</code> (Zstatic)</li><li><code>mirrors.sustech.edu.cn/cdnjs</code> (南方科技大学)</li></ul><hr><h5 id="如何科学地测试这些节点的速度"><a href="#如何科学地测试这些节点的速度" class="headerlink" title="如何科学地测试这些节点的速度"></a><strong>如何科学地测试这些节点的速度</strong></h5><p>要找到最适合您的节点，不能只凭感觉，需要进行客观测试。我推荐您结合使用以下两种方法：</p><h6 id="在线多节点测速工具（全国访问体验测试-最重要）"><a href="#在线多节点测速工具（全国访问体验测试-最重要）" class="headerlink" title="在线多节点测速工具（全国访问体验测试 - 最重要）"></a><strong>在线多节点测速工具（全国访问体验测试 - 最重要）</strong></h6><p>这个方法可以模拟全国各地、不同网络（电信、联通、移动）的用户访问您的资源时的速度，这比您自己本地测试的结果更具参考价值。</p><ol><li><p><strong>选择一个测试工具</strong>：</p><ul><li><strong><a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly93d3cuMTdjZS5jb20v">17CE</a></strong></li><li><strong><a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly9waW5nLmNoaW5hei5jb20v">站长工具-超级Ping</a></strong></li></ul></li><li><p><strong>准备测试链接</strong>：</p><ul><li>从我们上面的CDN镜像列表中，选择一个您想测试的节点的完整资源链接。例如：</li></ul><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line">https://jsd-proxy.ygxz.in/npm/jquery@3.6.0/dist/jquery.min.js</span><br><span class="line">https://cdn.iocdn.cc/npm/jquery@3.6.0/dist/jquery.min.js</span><br><span class="line">https://cdn.jsdmirror.com/npm/jquery@3.6.0/dist/jquery.min.js</span><br><span class="line">https://jsdelivr.topthink.com/npm/jquery@3.6.0/dist/jquery.min.js</span><br><span class="line">https://cdn.smartcis.cn/npm/jquery@3.6.0/dist/jquery.min.js</span><br><span class="line">https://jsd.cdn.zzko.cn/npm/jquery@3.6.0/dist/jquery.min.js</span><br><span class="line">https://jsd.onmicrosoft.cn/npm/jquery@3.6.0/dist/jquery.min.js</span><br><span class="line">https://jsdelivr.b-cdn.net/npm/jquery@3.6.0/dist/jquery.min.js</span><br><span class="line">https://cdn.jsdelivr.us/npm/jquery@3.6.0/dist/jquery.min.js</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>进行测试</strong>：</p><ul><li>将这个链接粘贴到测速网站的输入框中，开始测试。</li><li>网站会从全国各地的服务器去请求这个链接，并返回每个地点的响应时间和状态。</li></ul></li><li><p><strong>分析结果</strong>：</p><ul><li><strong>看平均响应时间</strong>：时间越短越好。</li><li><strong>看失败率</strong>：选择那些全国范围内绿色（正常）节点多、红色（超时/失败）节点少的CDN。</li><li><strong>重复测试</strong>：对您感兴趣的几个不同CDN镜像，重复这个过程，进行横向对比。</li></ul></li></ol><ul><li>对于“公益”镜像，请记住它们的<strong>稳定性可能随时变化</strong>。建议您可以定期进行抽查测试。</li></ul><h6 id="方案一：GitHub-国内CDN镜像-零成本最优方案"><a href="#方案一：GitHub-国内CDN镜像-零成本最优方案" class="headerlink" title="方案一：GitHub + 国内CDN镜像 (零成本最优方案)"></a><strong>方案一：GitHub + 国内CDN镜像 (零成本最优方案)</strong></h6><p>这个方案是当前针对国内环境的<strong>最佳免费方案</strong>。放弃了官方不稳定的 <code>cdn.jsdelivr.net</code>，转而使用由国内社区或第三方提供的、对中国大陆网络更友好的<strong>加速镜像</strong>。</p><p><strong>工作流程：</strong></p><ol><li><p><strong>创建 GitHub 图床仓库:</strong></p><ul><li>在 GitHub 上创建一个新的<strong>公开 (Public)</strong> 仓库，专门用于存放您的博客图片，例如命名为 <code>prorise-blog-assets</code>。</li></ul></li><li><p><strong>生成 GitHub Personal Access Token:</strong></p><ul><li>前往 GitHub <code>设置(Settings)</code> &gt; <code>Developer settings</code> &gt; <code>Personal access tokens</code> &gt; <code>Tokens (classic)</code>，生成一个新Token。</li><li>在权限 (Scopes) 选择中，至少需要勾选 <code>repo</code>。</li><li><strong>请务必复制并保存好这个Token</strong>，因为它只显示一次。</li></ul></li><li><p><strong>安装并配置 PicGo (关键步骤):</strong></p><ul><li>下载并安装 <a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly9naXRodWIuY29tL01vbHVuZXJmaW5uL1BpY0dvL3JlbGVhc2Vz">PicGo 客户端</a>。</li><li>在“插件设置”中搜索并安装 <code>github-plus</code> 插件。</li><li>在“图床设置” &gt; “GitHubPlus”中，填写您的信息。最大的不同在于 <code>customUrl</code> 字段：<figure class="highlight text"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"># PicGo GitHubPlus 配置示例</span><br><span class="line">repo: 您的用户名/您的仓库名</span><br><span class="line">branch: main</span><br><span class="line">token: 您的GitHub Token</span><br><span class="line">path: img/ # 可选的图片存储路径</span><br><span class="line"># 【关键】将 Custom URL 设置为您测试过最快的国内加速镜像</span><br><span class="line">customUrl: https://jsd-proxy.ygxz.in/gh/您的用户名/您的仓库名@main</span><br></pre></td></tr></tbody></table></figure></li><li><strong>关于国内镜像</strong>：除了 <code>jsd-proxy.ygxz.in</code>，您也可以尝试使用其他镜像地址替换 <code>cdn.jsdelivr.net</code>，选择一个您测试下来最稳定的即可。</li></ul></li><li><p><strong>上传与使用</strong>:</p><ul><li>设置好后，通过拖拽图片到 PicGo 窗口上传。PicGo 会自动将基于国内镜像的CDN链接复制到您的剪贴板，您直接在文章中粘贴使用。</li></ul></li></ol><p><strong>优缺点分析：</strong></p><ul><li><strong>优点</strong>: 完全免费，配置相对简单，<strong>国内访问速度远超官方 jsDelivr</strong>。</li><li><strong>缺点</strong>: <strong>稳定性依赖于镜像提供方</strong>。这些是“公益”服务，可能随时会因为滥用、成本或政策原因而失效，不适合对稳定性有极高要求的生产环境。</li></ul><hr><h6 id="方案二：国内对象存储-CDN-专业级性能方案"><a href="#方案二：国内对象存储-CDN-专业级性能方案" class="headerlink" title="方案二：国内对象存储 + CDN (专业级性能方案)"></a><strong>方案二：国内对象存储 + CDN (专业级性能方案)</strong></h6><p>如果您追求的是<strong>企业级的稳定性和最顶级的加载速度</strong>，那么这个方案是您的不二之选。</p><p><strong>工作流程</strong>:</p><ol><li><p><strong>开通云服务</strong>:</p><ul><li>在<strong>阿里云</strong>或<strong>腾讯云</strong>注册并开通**对象存储（OSS/COS）**服务。</li><li>创建一个存储桶 (Bucket)，并将其读写权限设置为**“公共读”**。</li></ul></li><li><p><strong>配置 CDN 加速</strong>:</p><ul><li>在对应的云服务商后台，为您的存储桶开通并绑定一个<strong>CDN加速域名</strong>。强烈建议使用您自己的一个子域名（例如 <code>img.prorise.com</code>），并根据引导完成 CNAME 解析。</li></ul></li><li><p><strong>获取访问密钥 (Access Key)</strong>:</p><ul><li>在云服务商的**访问控制（RAM/CAM）**后台，创建一个专用的子用户，并为其生成 <code>AccessKey ID</code> 和 <code>AccessKey Secret</code>。</li><li><strong>重要</strong>：为这个子用户授予<strong>仅能管理您那个存储桶</strong>的权限，而不是全局权限。</li></ul></li><li><p><strong>配置 PicGo 客户端</strong>:</p><ul><li>在 PicGo 的插件设置中，安装对应的插件（例如 <code>ali-oss</code> 或 <code>tencent-cos</code>）。</li><li>在图床设置中，填入您的 <code>AccessKey ID</code>, <code>AccessKey Secret</code>, <code>Bucket</code> 名称、存储区域以及您在第二步中配置好的<strong>CDN加速域名</strong>。</li></ul></li></ol><p><strong>优缺点分析：</strong></p><ul><li><strong>优点</strong>: <strong>速度最快、最稳定</strong>，有SLA服务保障，并支持图片处理、防盗链等丰富的专业功能。</li><li><strong>缺点</strong>: 需要少量费用（但个人博客用量成本极低，每月通常在几元以内），初始配置步骤最多。</li></ul><hr><h5 id="总结与最终选择建议"><a href="#总结与最终选择建议" class="headerlink" title="总结与最终选择建议"></a><strong>总结与最终选择建议</strong></h5><p>感谢您的指正，让这份笔记变得更加完整和准确。</p><ul><li>对于<strong>起步阶段</strong>或<strong>个人实验性项目</strong>，如果您追求零成本，<strong>【方案一】</strong> 是一个非常出色的选择，它解决了 jsDelivr 的主要痛点。</li><li>对于<strong>追求极致性能和长期稳定性的严肃博客</strong>，<strong>【方案二】</strong> 是更值得投资的专业选择，它能为您的国内访客提供最顶级的访问体验。</li></ul><hr><h3 id="2-图片压缩策略"><a href="#2-图片压缩策略" class="headerlink" title="2.图片压缩策略"></a><strong>2.图片压缩策略</strong></h3><h6 id="前言：为什么图片优化至关重要？"><a href="#前言：为什么图片优化至关重要？" class="headerlink" title="前言：为什么图片优化至关重要？"></a><strong>前言：为什么图片优化至关重要？</strong></h6><p>图片通常是网页中体积最大的资源，它直接决定了您网站的加载速度。一个加载缓慢的网站会严重影响用户体验，并可能导致访客流失。因此，对图片进行全面优化，是在追求“毫秒级”性能道路上，投入产出比最高的一环。</p><p>一个完整的图片优化流程包含四个核心策略：<strong>压缩、格式选择、懒加载、以及CDN加速</strong>。</p><hr><h6 id="1-图片压缩：在画质与体积之间找到平衡"><a href="#1-图片压缩：在画质与体积之间找到平衡" class="headerlink" title="1. 图片压缩：在画质与体积之间找到平衡"></a><strong>1. 图片压缩：在画质与体积之间找到平衡</strong></h6><ul><li><strong>是什么？</strong><br>通过特定的算法，在保持可接受的图像质量的前提下，尽可能地减小图片文件的体积。它分为“有损压缩”（会损失部分图像细节，压缩率高）和“无损压缩”（不损失细节，压缩率较低）。</li></ul><hr><h6 id="2-格式选择：为不同场景选择最优格式"><a href="#2-格式选择：为不同场景选择最优格式" class="headerlink" title="2. 格式选择：为不同场景选择最优格式"></a><strong>2. 格式选择：为不同场景选择最优格式</strong></h6><p>不同的图片格式有不同的特性，为您的图片选择最合适的格式，能从源头上优化性能。</p><table><thead><tr><th align="left">方法/工具</th><th align="left">作用</th><th align="left">推荐场景</th><th align="left">优势</th><th align="left">劣势</th></tr></thead><tbody><tr><td align="left"><strong>图片压缩（有损/无损）</strong></td><td align="left">减小文件体积</td><td align="left">任何图片</td><td align="left">直接减小传输大小</td><td align="left">有损压缩可能略微影响图片质量</td></tr><tr><td align="left"><a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly93d3cuaW1nZGlldC5jb20vemgtQ04vY29tcHJlc3M">在线工具</a></td><td align="left">在线批量压缩PNG/JPG</td><td align="left">零散图片处理，无需安装</td><td align="left">方便快捷，压缩率高</td><td align="left">依赖网络，可能受限于上传大小/数量</td></tr><tr><td align="left">客户端工具 (Caesium)</td><td align="left">离线批量压缩图片</td><td align="left">大量图片处理，离线操作</td><td align="left">离线处理，可配置压缩参数</td><td align="left">需要安装软件</td></tr><tr><td align="left"><strong>图片格式选择</strong></td><td align="left">使用更优格式</td><td align="left">新增图片</td><td align="left">WebP格式体积更小，支持透明和动画</td><td align="left">兼容性（旧浏览器），部分工具不支持</td></tr><tr><td align="left">WebP</td><td align="left">新一代图片格式</td><td align="left">现代浏览器，要求性能</td><td align="left">体积小，质量高</td><td align="left">部分旧浏览器可能不支持</td></tr><tr><td align="left">JPG</td><td align="left">摄影图片</td><td align="left">色彩丰富</td><td align="left">广泛兼容，适合照片</td><td align="left">不支持透明度，压缩artifacts明显</td></tr><tr><td align="left">PNG</td><td align="left">透明背景图片</td><td align="left">图标，截图，需要透明度</td><td align="left">支持透明度</td><td align="left">体积通常比JPG大，不适合照片</td></tr><tr><td align="left">SVG</td><td align="left">矢量图</td><td align="left">图标，Logo，图表</td><td align="left">无损缩放，体积小，可编辑，支持动画</td><td align="left">不适合复杂照片</td></tr><tr><td align="left"><strong>图片懒加载 (Lazy Load)</strong></td><td align="left">延迟加载非首屏图片</td><td align="left">页面图片多</td><td align="left">提升首屏速度，节省带宽</td><td align="left">需要JS支持，可能影响部分图片索引</td></tr><tr><td align="left">Butterfly内置</td><td align="left">主题集成</td><td align="left">Butterfly用户</td><td align="left">配置简单，无需额外插件</td><td align="left">功能由主题决定</td></tr><tr><td align="left">手动/插件</td><td align="left">各种主题</td><td align="left">高度定制</td><td align="left">灵活性高，可控性强</td><td align="left">配置复杂，可能需要编写代码/插件</td></tr><tr><td align="left"><strong>图床CDN加速</strong></td><td align="left">图片分发加速</td><td align="left">任何博客</td><td align="left">提升图片加载速度，减轻服务器压力</td><td align="left">需要额外服务或配置</td></tr><tr><td align="left">jsDelivr (配合GitHub)</td><td align="left">免费CDN</td><td align="left">个人博客，静态站点</td><td align="left">免费，方便（配合PicGo），全球节点</td><td align="left">依赖GitHub可用性，偶尔不稳定</td></tr><tr><td align="left">对象存储 (OSS/COS)</td><td align="left">商业CDN+存储</td><td align="left">流量大，要求高可用性</td><td align="left">稳定可靠，专业服务</td><td align="left">有成本</td></tr></tbody></table><hr><h6 id="3-图片懒加载-Lazy-Loading-：提升首屏速度的关键"><a href="#3-图片懒加载-Lazy-Loading-：提升首屏速度的关键" class="headerlink" title="3. 图片懒加载 (Lazy Loading)：提升首屏速度的关键"></a><strong>3. 图片懒加载 (Lazy Loading)：提升首屏速度的关键</strong></h6><ul><li><p><strong>是什么？</strong><br>懒加载是一种延迟加载技术，它只在用户即将滚动到图片位置时，才开始加载这张图片。这可以极大地减少页面的初始加载体积，让首屏内容更快地显示出来，对SEO也很有利。</p></li><li><p><strong>怎么做？</strong><br>好消息是，使用的 Anzhiyu 主题<strong>已经内置了强大的懒加载功能</strong>。您只需在<strong>主题配置文件</strong> (<code>_config.anzhiyu.yml</code>) 中确保它是开启的。</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 在 themes/anzhiyu/_config.yml 中</span></span><br><span class="line"><span class="attr">lazyload:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span> <span class="comment"># 确保这里是 true</span></span><br><span class="line">  <span class="attr">field:</span> <span class="string">site</span> <span class="comment"># site 表示全站生效</span></span><br><span class="line">  <span class="attr">placeholder:</span> <span class="string">/img/loading.gif</span> <span class="comment"># 加载前的占位图，请确保此路径有图片，或使用图床链接</span></span><br><span class="line">  <span class="attr">blur:</span> <span class="literal">true</span> <span class="comment"># 图片加载前是否显示模糊效果</span></span><br></pre></td></tr></tbody></table></figure></li></ul><hr><h3 id="3-静态资源（JS-CSS）优化：压缩与按需加载"><a href="#3-静态资源（JS-CSS）优化：压缩与按需加载" class="headerlink" title="3. 静态资源（JS/CSS）优化：压缩与按需加载"></a><strong>3. 静态资源（JS/CSS）优化：压缩与按需加载</strong></h3><ul><li><p><strong>是什么？</strong></p><ul><li><strong>压缩 (Minify)</strong>：移除JS和CSS代码中所有不影响功能的字符，如空格、换行、注释等，以减小文件体积。</li><li><strong>合并 (Bundle)</strong>：将多个小的JS或CSS文件合并成一个大文件，以减少浏览器需要发起的HTTP请求次数。</li><li><strong>按需加载</strong>：只在需要某个功能的页面，才加载对应的JS或CSS文件，避免在全站加载不必要的资源。</li></ul></li><li><p><strong>怎么做？（解决方案）</strong></p><ul><li><strong>使用Hexo压缩插件</strong>：这是实现压缩和合并最简单的方法。推荐使用 <code>hexo-neat</code> 插件。<ol><li><strong>安装插件</strong>：在您博客根目录的终端中运行：<figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install hexo-neat --save</span><br></pre></td></tr></tbody></table></figure></li><li><strong>配置插件</strong>：在<strong>根目录 <code>_config.yml</code></strong> 文件末尾添加配置，它会自动在 <code>hexo g</code> 时处理您的文件。<figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># hexo-neat 压缩配置</span></span><br><span class="line"><span class="attr">neat_enable:</span> <span class="literal">true</span></span><br><span class="line"><span class="comment"># 压缩HTML</span></span><br><span class="line"><span class="attr">neat_html:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">exclude:</span></span><br><span class="line"><span class="comment"># 压缩CSS</span></span><br><span class="line"><span class="attr">neat_css:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">exclude:</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">'**/*.min.css'</span> <span class="comment"># 排除已经压缩过的.min.css文件</span></span><br><span class="line"><span class="comment"># 压缩JS</span></span><br><span class="line"><span class="attr">neat_js:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">exclude:</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">'**/*.min.js'</span> <span class="comment"># 排除已经压缩过的.min.js文件</span></span><br></pre></td></tr></tbody></table></figure></li></ol></li><li><strong>利用主题<code>inject</code>功能实现按需加载</strong>：正如我们之前在配置<strong>代码沙箱</strong>、<strong>自定义评论样式</strong>等功能时所做的，将只在特定页面使用的JS和CSS，通过在<strong>文章的 <code>.md</code> 文件</strong>中直接编写 <code>&lt;script&gt;</code> 和 <code>&lt;link&gt;</code> 标签来引入，而不是通过主题的 <code>inject</code> 进行全局注入，这就是一种有效的按需加载实践。</li></ul></li></ul><hr><h6 id="2-浏览器缓存与CDN：让回头客“秒开”网站"><a href="#2-浏览器缓存与CDN：让回头客“秒开”网站" class="headerlink" title="2. 浏览器缓存与CDN：让回头客“秒开”网站"></a><strong>2. 浏览器缓存与CDN：让回头客“秒开”网站</strong></h6><ul><li><p><strong>浏览器缓存 (Browser Caching)</strong></p><ul><li><strong>是什么？</strong>：您可以把它想象成，浏览器在第一次访问您的网站后，拍下了一张“快照”（即将CSS、JS、图片等资源保存在您的电脑上）。当您第二次访问时，浏览器会直接使用本地的“快照”，而无需再次从服务器下载，从而实现“秒开”。</li><li><strong>怎么做？</strong>：这通常<strong>不需要您在Hexo中配置</strong>。它是在您的<strong>网站托管平台</strong>上进行设置的。好消息是，像 <strong>Vercel</strong>、<strong>Netlify</strong>、<strong>GitHub Pages</strong> 这类现代化的托管平台，都已经为您配置了非常合理的默认缓存策略，您通常无需关心。</li></ul></li><li><p><strong>CDN (Content Delivery Network)</strong></p><ul><li><strong>是什么？</strong>：我们之前在讨论图床时已经深入了解过。它的核心是将您的静态资源（不仅仅是图片，也包括CSS和JS文件）部署到全球各地的服务器节点上，让用户从最近的节点加载。</li><li><strong>怎么做？</strong>：主题的 <code>_config.yml</code> 中已经有了 <code>CDN</code> 配置项，它允许您将主题依赖的第三方库（如jQuery, Fancybox等）的CDN提供商，从默认的 <code>jsdelivr</code> 更换为速度更快的国内镜像（例如我们之前讨论过的 <code>cdn.bootcdn.net</code> 或 <code>s4.zstatic.net</code>），这也是一种重要的CDN优化。</li></ul></li></ul><p>我们将使用 <code>CDN.option</code> 这个配置，为每一个重要的库指定一个经过我们测试的、速度最快的国内镜像源，</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">option:</span></span><br><span class="line">  <span class="comment"># --- 核心与基础库 ---</span></span><br><span class="line">  <span class="comment"># 主题内部核心JS/CSS文件，如 main.js, utils.js 等，建议保持 local，由 Hexo 直接生成，以确保主题功能稳定。</span></span><br><span class="line">  <span class="comment"># main_css: </span></span><br><span class="line">  <span class="comment"># main: ""</span></span><br><span class="line">  <span class="comment"># utils: ""</span></span><br><span class="line">  <span class="comment"># jquery: ""</span></span><br><span class="line">  <span class="attr">pjax:</span> <span class="string">https://lib.baomitu.com/pjax/0.2.8/pjax.min.js</span></span><br><span class="line">  </span><br><span class="line">  <span class="comment"># --- 页面功能与特效 ---</span></span><br><span class="line">  <span class="attr">lazyload:</span> <span class="string">https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/vanilla-lazyload/17.3.1/lazyload.iife.min.js</span></span><br><span class="line">  <span class="attr">instantpage:</span> <span class="string">https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js</span></span><br><span class="line">  <span class="attr">typed:</span> <span class="string">https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js</span></span><br><span class="line">  <span class="attr">pangu:</span> <span class="string">https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js</span></span><br><span class="line">  <span class="comment"># fancybox: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/fancybox/3.5.7/jquery.fancybox.min.js</span></span><br><span class="line">  <span class="comment"># fancybox_css: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/fancybox/3.5.7/jquery.fancybox.min.css</span></span><br><span class="line">  <span class="attr">medium_zoom:</span> <span class="string">https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/medium-zoom/1.0.6/medium-zoom.min.js</span></span><br><span class="line">  <span class="attr">snackbar:</span> <span class="string">https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.js</span></span><br><span class="line">  <span class="attr">snackbar_css:</span> <span class="string">https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css</span></span><br><span class="line">  <span class="attr">fontawesome:</span> <span class="string">https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css</span></span><br><span class="line">  </span><br><span class="line">  <span class="comment"># --- 评论系统 ---</span></span><br><span class="line">  <span class="comment"># valine: # 如有需要，可自行查找Valine的CDN链接</span></span><br><span class="line">  <span class="comment"># twikoo: ""</span></span><br><span class="line"> <span class="comment"># waline_js: ""</span></span><br><span class="line">  <span class="comment">#waline_css: ""</span></span><br><span class="line">  <span class="comment"># artalk_js: # 如有需要，可自行查找Artalk的CDN链接</span></span><br><span class="line">  <span class="comment"># artalk_css:</span></span><br><span class="line"></span><br><span class="line">  <span class="comment"># --- 搜索系统 ---</span></span><br><span class="line">  <span class="comment"># local_search: # 本地搜索通常与主题内部JS关联，建议保持local</span></span><br><span class="line">  <span class="comment">#algolia_search: ""</span></span><br><span class="line"></span><br><span class="line">  <span class="comment"># --- 音乐播放器 ---</span></span><br><span class="line">  <span class="attr">aplayer_css:</span> <span class="string">https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css</span></span><br><span class="line">  <span class="attr">aplayer_js:</span> <span class="string">https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js</span></span><br><span class="line">  <span class="attr">meting_js:</span> <span class="string">https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js</span></span><br><span class="line"></span><br><span class="line">  <span class="attr">sharejs:</span> <span class="string">https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"></span><br><span class="line">  <span class="comment"># --- 其他不常用或建议保持默认的库 ---</span></span><br><span class="line">  <span class="comment"># mathjax:</span></span><br><span class="line">  <span class="comment"># katex:</span></span><br><span class="line">  <span class="comment"># katex_copytex:</span></span><br><span class="line">  <span class="comment"># mermaid:</span></span><br><span class="line">  <span class="comment"># busuanzi: # 不蒜子官方脚本通常不建议替换</span></span><br></pre></td></tr></tbody></table></figure><hr><h6 id="3-字体加载优化：告别“文字消失术”"><a href="#3-字体加载优化：告别“文字消失术”" class="headerlink" title="3. 字体加载优化：告别“文字消失术”"></a><strong>3. 字体加载优化：告别“文字消失术”</strong></h6><p>自定义字体虽然能提升美观度，但巨大的字体文件是拖慢速度的元凶之一，并可能导致在字体加载完成前，页面上的文字完全不可见（Flash of Invisible Text - FOIT）。</p><ul><li><p><strong>怎么做？（三大策略）</strong></p><ol><li><strong>使用 WOFF2 格式</strong>：这是目前最先进的网页字体格式，压缩率最高，文件体积最小。请务必将您使用的 <code>.otf</code> 或 <code>.ttf</code> 字体，通过在线工具转换为 <code>.woff2</code> 格式再使用。</li><li><strong>字体子集化 (Subsetting)</strong>：这是一项高级技术。如果您的字体只用于Logo或标题，仅使用了少数几十个字符，您可以通过“字体子集化”工具，从完整的几万个字符的字体文件中，只抽取出您用到的这几十个字符，生成一个体积极小的、定制化的字体文件。</li><li><strong>善用 <code>font-display: swap</code></strong>：这是<strong>最简单也最重要</strong>的优化。它告诉浏览器：“在自定义字体还没下载好之前，请先用电脑里有的默认字体把文字显示出来，不要让用户看一片空白。等我的字体下载好了，你再悄悄地把它换上。”</li></ol><p><strong>配置方法</strong>：</p><ul><li>在您的自定义CSS文件（例如 <code>source/css/font.css</code>）中，确保您的 <code>@font-face</code> 规则里包含了 <code>font-display: swap;</code> 这一行。</li></ul><figure class="highlight css"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/* @font-face规则定义自定义字体 */</span></span><br><span class="line"><span class="keyword">@font-face</span> {</span><br><span class="line">  <span class="attribute">font-family</span>: <span class="string">'Your Blog Font'</span>; <span class="comment">/* 定义字体族名称 */</span></span><br><span class="line">  <span class="attribute">src</span>: <span class="built_in">url</span>(<span class="string">'/fonts/your-blog-font.woff2'</span>) <span class="built_in">format</span>(<span class="string">'woff2'</span>), <span class="comment">/* WOFF2格式优先 */</span></span><br><span class="line">       <span class="built_in">url</span>(<span class="string">'/fonts/your-blog-font.woff'</span>) <span class="built_in">format</span>(<span class="string">'woff'</span>);  <span class="comment">/* WOFF格式作为后备 */</span></span><br><span class="line">  <span class="attribute">font-weight</span>: normal; <span class="comment">/* 字体粗细 */</span></span><br><span class="line">  <span class="attribute">font-style</span>: normal;  <span class="comment">/* 字体样式 */</span></span><br><span class="line">  <span class="attribute">font-display</span>: swap; <span class="comment">/* 【关键】字体加载策略：先用系统字体，加载完再替换 */</span></span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 在body或特定元素上应用自定义字体 */</span></span><br><span class="line"><span class="selector-tag">body</span> {</span><br><span class="line">  <span class="attribute">font-family</span>: <span class="string">'Your Blog Font'</span>, sans-serif; <span class="comment">/* 使用自定义字体，sans-serif作为备用 */</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><p>通过以上这些针对静态资源和字体的优化，您博客的性能和加载体验将得到全面的提升。</p><h3 id="4-关键渲染优化"><a href="#4-关键渲染优化" class="headerlink" title="4.关键渲染优化"></a>4.关键渲染优化</h3><p>在我们前面的无脑集成中，的确步骤都非常方便，但所有的CSS样式和JS样式都在首页时渲染，在用户进入首页时，加载Echats库、加载别的页面的CSS/Js这无疑会大大降低了首页的渲染速度，那么为什么我们要到这一章节才来优化，而不在一开始就做呢，由于我们是按需集成的，市面上大部分的集成步骤也都是这样，比起在一开始就要注意每一个的集成步骤，不如我们集成完了之后再来统一管理会更好一些</p><h4 id="优化目标"><a href="#优化目标" class="headerlink" title="优化目标"></a>优化目标</h4><ul><li>解决ECharts等重型库导致的首页5秒加载问题</li><li>实现”谁生病，谁吃药”的按需加载策略</li><li>保持所有功能完整性的前提下大幅提升性能</li></ul><p><strong>预期效果</strong></p><ul><li><strong>首页加载时间</strong>: 5-8秒 → 1-2秒 (减少70%+)</li><li><strong>首页资源数量</strong>: 20个文件 → 2个文件 (减少90%+)</li><li><strong>移动端体验</strong>: 显著提升</li></ul><h4 id="第一部分：问题诊断与分析"><a href="#第一部分：问题诊断与分析" class="headerlink" title="第一部分：问题诊断与分析"></a>第一部分：问题诊断与分析</h4><h5 id="1-1-检查当前配置状态"><a href="#1-1-检查当前配置状态" class="headerlink" title="1.1 检查当前配置状态"></a>1.1 检查当前配置状态</h5><p>首先，打开您的主题配置文件，检查当前的资源注入情况：</p><p><strong>文件位置</strong>: <code>_config.anzhiyu.yml</code></p><p>找到 <code>inject:</code> 配置段，您可能会看到类似这样的配置：</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">inject:</span></span><br><span class="line">  <span class="attr">head:</span></span><br><span class="line">    <span class="comment"># 大量CSS文件全局加载</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/css/index_media.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/css/post-ui.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/css/essay-style.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/css/custom-comment.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/custom/css/schedule.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/css/bilibili.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/custom/css/todolist.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/custom/css/tip_style.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/custom/css/background-box.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/css/font.css"&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;link</span> <span class="string">rel="stylesheet"</span> <span class="string">href="/custom/css/sandbox_style.css"&gt;</span></span><br><span class="line"></span><br><span class="line">  <span class="attr">bottom:</span></span><br><span class="line">    <span class="comment"># 大量JS文件全局加载</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="/js/index_media.js"&gt;&lt;/script&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="/js/comments.js"&gt;&lt;/script&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="https://cdn.bootcdn.net/ajax/libs/echarts/4.9.0-rc.1/echarts.min.js"&gt;&lt;/script&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="/js/fixed_comment.js"&gt;&lt;/script&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="/custom/js/chineselunar.js"&gt;&lt;/script&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="/custom/js/schedule.js"&gt;&lt;/script&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="https://cdn.jsdelivr.net/npm/winbox@0.2.82/dist/winbox.bundle.min.js"&gt;&lt;/script&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="/custom/js/tip_main.js"&gt;&lt;/script&gt;</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">&lt;script</span> <span class="string">src="/custom/js/background-box.js"&gt;&lt;/script&gt;</span></span><br></pre></td></tr></tbody></table></figure><h5 id="1-2-性能问题分析"><a href="#1-2-性能问题分析" class="headerlink" title="1.2 性能问题分析"></a>1.2 性能问题分析</h5><p><strong>问题诊断</strong>:</p><ol><li><strong>ECharts库</strong>: 2-3MB的重型图表库在每个页面都加载</li><li><strong>冗余CSS</strong>: 即刻短文、待办清单等功能的样式在所有页面加载</li><li><strong>无用JS</strong>: 首页加载了文章页才需要的评论脚本</li><li><strong>移动端负担</strong>: 大量资源严重影响移动设备体验</li></ol><p><strong>性能影响</strong>:</p><ul><li>首页加载11个CSS + 9个JS文件</li><li>网络请求过多，瀑布流效应明显</li><li>解析和执行时间过长</li><li>内存占用过高</li></ul><h4 id="第二部分：核心配置优化"><a href="#第二部分：核心配置优化" class="headerlink" title="第二部分：核心配置优化"></a>第二部分：核心配置优化</h4><h5 id="2-1-精简inject配置"><a href="#2-1-精简inject配置" class="headerlink" title="2.1 精简inject配置"></a>2.1 精简inject配置</h5><ol><li><p><strong>打开配置文件</strong>:</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">_config.anzhiyu.yml</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>找到inject配置段</strong> (通常在文件末尾附近)</p></li><li><p><strong>完全替换inject配置</strong>:</p><p>将原有的inject配置替换为以下内容：</p></li></ol><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">inject:</span></span><br><span class="line">  <span class="attr">head:</span></span><br><span class="line">    <span class="comment"># 只保留全站必需的基础资源</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">'&lt;link rel="stylesheet" href="/css/font.css"&gt;'</span></span><br><span class="line">    <span class="comment"># 修复：首页样式优先加载，避免首屏渲染延迟</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">'&lt;link rel="stylesheet" href="/css/index_media.css" media="(max-width: 0), screen and (prefers-reduced-motion: reduce)" onload="this.media=`screen`"&gt;'</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">'&lt;link rel="stylesheet" href="/css/post-ui.css""&gt;'</span></span><br><span class="line"></span><br><span class="line">  <span class="attr">bottom:</span></span><br><span class="line">    <span class="comment"># 轻量级按需加载资源管理器（全站必需）</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">'&lt;script src="/js/load-on-demand.js"&gt;&lt;/script&gt;'</span></span><br></pre></td></tr></tbody></table></figure><p><strong>⚠️ 重要说明</strong>:</p><ul><li>我们只保留了字体CSS（全站必需）</li><li>添加了即将创建的智能资源管理器</li><li>移除了所有页面专用的资源</li></ul><hr><h4 id="第三部分：智能资源管理器"><a href="#第三部分：智能资源管理器" class="headerlink" title="第三部分：智能资源管理器"></a>第三部分：智能资源管理器</h4><h5 id="3-1-创建load-on-demand-js文件"><a href="#3-1-创建load-on-demand-js文件" class="headerlink" title="3.1 创建load-on-demand.js文件"></a>3.1 创建load-on-demand.js文件</h5><p><strong>操作步骤</strong>:</p><ol><li><p><strong>创建文件目录</strong> (如果不存在):</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="built_in">mkdir</span> -p themes/anzhiyu/source/js</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>创建资源管理器文件</strong>:</p><p><strong>文件位置</strong>: <code>themes/anzhiyu/source/js/load-on-demand.js</code></p><p><strong>完整代码</strong> (请复制粘贴整个文件内容):</p></li></ol><figure class="highlight javascript"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 按需加载资源管理器</span></span><br><span class="line"><span class="comment"> * 用于优化网站性能，只在需要时加载特定资源</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">ResourceLoader</span> {</span><br><span class="line">    <span class="title function_">constructor</span>(<span class="params"></span>) {</span><br><span class="line">        <span class="variable language_">this</span>.<span class="property">loadedCSS</span> = <span class="keyword">new</span> <span class="title class_">Set</span>();</span><br><span class="line">        <span class="variable language_">this</span>.<span class="property">loadedJS</span> = <span class="keyword">new</span> <span class="title class_">Set</span>();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 动态加载CSS文件</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">href</span> - CSS文件路径</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">id</span> - 可选的link元素ID</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="title function_">loadCSS</span>(<span class="params">href, id = <span class="literal">null</span></span>) {</span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">this</span>.<span class="property">loadedCSS</span>.<span class="title function_">has</span>(href) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">`link[href="<span class="subst">${href}</span>"]`</span>)) {</span><br><span class="line">            <span class="keyword">return</span> <span class="title class_">Promise</span>.<span class="title function_">resolve</span>();</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">Promise</span>(<span class="function">(<span class="params">resolve, reject</span>) =&gt;</span> {</span><br><span class="line">            <span class="keyword">const</span> link = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">'link'</span>);</span><br><span class="line">            link.<span class="property">rel</span> = <span class="string">'stylesheet'</span>;</span><br><span class="line">            link.<span class="property">href</span> = href;</span><br><span class="line">            <span class="keyword">if</span> (id) link.<span class="property">id</span> = id;</span><br><span class="line"></span><br><span class="line">            link.<span class="property">onload</span> = <span class="function">() =&gt;</span> {</span><br><span class="line">                <span class="variable language_">this</span>.<span class="property">loadedCSS</span>.<span class="title function_">add</span>(href);</span><br><span class="line">                <span class="title function_">resolve</span>();</span><br><span class="line">            };</span><br><span class="line">            link.<span class="property">onerror</span> = reject;</span><br><span class="line"></span><br><span class="line">            <span class="variable language_">document</span>.<span class="property">head</span>.<span class="title function_">appendChild</span>(link);</span><br><span class="line">        });</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 动态加载JS文件</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">src</span> - JS文件路径</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">id</span> - 可选的script元素ID</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="title function_">loadJS</span>(<span class="params">src, id = <span class="literal">null</span></span>) {</span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">this</span>.<span class="property">loadedJS</span>.<span class="title function_">has</span>(src) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">`script[src="<span class="subst">${src}</span>"]`</span>)) {</span><br><span class="line">            <span class="keyword">return</span> <span class="title class_">Promise</span>.<span class="title function_">resolve</span>();</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">Promise</span>(<span class="function">(<span class="params">resolve, reject</span>) =&gt;</span> {</span><br><span class="line">            <span class="keyword">const</span> script = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">'script'</span>);</span><br><span class="line">            script.<span class="property">src</span> = src;</span><br><span class="line">            <span class="keyword">if</span> (id) script.<span class="property">id</span> = id;</span><br><span class="line"></span><br><span class="line">            script.<span class="property">onload</span> = <span class="function">() =&gt;</span> {</span><br><span class="line">                <span class="variable language_">this</span>.<span class="property">loadedJS</span>.<span class="title function_">add</span>(src);</span><br><span class="line">                <span class="title function_">resolve</span>();</span><br><span class="line">            };</span><br><span class="line">            script.<span class="property">onerror</span> = reject;</span><br><span class="line"></span><br><span class="line">            <span class="variable language_">document</span>.<span class="property">body</span>.<span class="title function_">appendChild</span>(script);</span><br><span class="line">        });</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 检测页面内容并按需加载相关资源</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line">    <span class="title function_">autoDetectAndLoad</span>(<span class="params"></span>) {</span><br><span class="line">        <span class="comment">// 检测是否为首页</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">pathname</span> === <span class="string">'/'</span> || <span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">pathname</span> === <span class="string">'/index.html'</span>) {</span><br><span class="line">            <span class="comment">// 修复：index_media.css 现在由头部优先加载，只需加载JS</span></span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/js/index_media.js'</span>, <span class="string">'index-media-script'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测是否为文章页</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#post'</span>) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'.post-content'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/css/custom-comment.css'</span>, <span class="string">'custom-comment-style'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/tip_style.css'</span>, <span class="string">'tip-style'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/js/fixed_comment.js'</span>, <span class="string">'fixed-comment-script'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/custom/js/tip_main.js'</span>, <span class="string">'tip-main-script'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测B站视频内容</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'iframe[src*="bilibili.com"]'</span>) ||</span><br><span class="line">            <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'iframe[src*="player.bilibili.com"]'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/css/bilibili.css'</span>, <span class="string">'bilibili-style'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测代码块</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'pre code'</span>) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'.highlight'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/sandbox_style.css'</span>, <span class="string">'sandbox-style'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测评论区</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#twikoo'</span>) ||</span><br><span class="line">            <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#waline'</span>) ||</span><br><span class="line">            <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#valine'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/js/comments.js'</span>, <span class="string">'comments-script'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测即刻短文页面</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">pathname</span>.<span class="title function_">includes</span>(<span class="string">'/essay/'</span>) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#essay_page'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/css/essay-style.css'</span>, <span class="string">'essay-style'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测待办清单页面</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">pathname</span>.<span class="title function_">includes</span>(<span class="string">'/todolist/'</span>) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#todolist-box'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/todolist.css'</span>, <span class="string">'todolist-style'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测侧边栏相关功能</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#sidebar'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/schedule.css'</span>, <span class="string">'schedule-style'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/background-box.css'</span>, <span class="string">'background-style'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'https://cdn.jsdelivr.net/npm/winbox@0.2.82/dist/winbox.bundle.min.js'</span>, <span class="string">'winbox-lib'</span>)</span><br><span class="line">                .<span class="title function_">then</span>(<span class="function">() =&gt;</span> <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/custom/js/chineselunar.js'</span>, <span class="string">'chineselunar-script'</span>))</span><br><span class="line">                .<span class="title function_">then</span>(<span class="function">() =&gt;</span> <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/custom/js/schedule.js'</span>, <span class="string">'schedule-script'</span>))</span><br><span class="line">                .<span class="title function_">then</span>(<span class="function">() =&gt;</span> <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/custom/js/background-box.js'</span>, <span class="string">'background-script'</span>))</span><br><span class="line">                .<span class="title function_">catch</span>(<span class="function"><span class="params">err</span> =&gt;</span> <span class="variable language_">console</span>.<span class="title function_">warn</span>(<span class="string">'侧边栏脚本加载失败:'</span>, err));</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 创建全局实例</span></span><br><span class="line"><span class="variable language_">window</span>.<span class="property">resourceLoader</span> = <span class="keyword">new</span> <span class="title class_">ResourceLoader</span>();</span><br><span class="line"></span><br><span class="line"><span class="comment">// 页面加载完成后自动检测</span></span><br><span class="line"><span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'DOMContentLoaded'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">    <span class="variable language_">window</span>.<span class="property">resourceLoader</span>.<span class="title function_">autoDetectAndLoad</span>();</span><br><span class="line">});</span><br><span class="line"></span><br><span class="line"><span class="comment">// 为PJAX提供支持</span></span><br><span class="line"><span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'pjax:complete'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">    <span class="variable language_">window</span>.<span class="property">resourceLoader</span>.<span class="title function_">autoDetectAndLoad</span>();</span><br><span class="line">}); </span><br></pre></td></tr></tbody></table></figure><h5 id="3-2-功能特性说明"><a href="#3-2-功能特性说明" class="headerlink" title="3.2 功能特性说明"></a>3.2 功能特性说明</h5><p><strong>智能检测机制</strong>:</p><ul><li><strong>页面类型检测</strong>: 根据URL路径和DOM元素判断页面类型</li><li><strong>内容特征检测</strong>: 检测B站视频、代码块、评论区等特殊内容</li><li><strong>防重复加载</strong>: 确保每个资源只加载一次</li><li><strong>PJAX兼容</strong>: 支持无刷新页面跳转</li></ul><p><strong>错误处理</strong>:</p><ul><li>加载失败时的优雅降级</li><li>详细的错误日志记录</li><li>不影响其他功能的正常运行</li></ul><hr><h4 id="第四部分：ECharts统计页面优化"><a href="#第四部分：ECharts统计页面优化" class="headerlink" title="第四部分：ECharts统计页面优化"></a>第四部分：ECharts统计页面优化</h4><h5 id="4-1-修改统计页面配置"><a href="#4-1-修改统计页面配置" class="headerlink" title="4.1 修改统计页面配置"></a>4.1 修改统计页面配置</h5><p><strong>检查统计页面</strong>:</p><ol><li><p><strong>找到统计页面文件</strong>:</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 检查是否存在统计页面</span></span><br><span class="line"><span class="built_in">ls</span> <span class="built_in">source</span>/charts/</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>编辑统计页面配置</strong>:</p><p><strong>文件位置</strong>: <code>source/charts/index.md</code></p><p><strong>查找并修改</strong>，如果文件包含内联的ECharts脚本，将其替换为：</p></li></ol><figure class="highlight markdown"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line">---</span><br><span class="line">title: 网站统计</span><br><span class="line">type: "charts"</span><br><span class="line">comments: false</span><br><span class="line"><span class="section">aside: false</span></span><br><span class="line"><span class="section">---</span></span><br><span class="line"></span><br><span class="line"><span class="language-xml"><span class="tag">&lt;<span class="name">div</span> <span class="attr">id</span>=<span class="string">"posts-chart"</span> <span class="attr">data-start</span>=<span class="string">"2024-09"</span> <span class="attr">style</span>=<span class="string">"border-radius: 8px; height: 300px; padding: 10px; margin-bottom: 20px;"</span>&gt;</span></span><span class="language-xml"><span class="tag">&lt;/<span class="name">div</span>&gt;</span></span></span><br><span class="line"></span><br><span class="line"><span class="language-xml"><span class="tag">&lt;<span class="name">div</span> <span class="attr">id</span>=<span class="string">"tags-chart"</span> <span class="attr">data-length</span>=<span class="string">"10"</span> <span class="attr">style</span>=<span class="string">"border-radius: 8px; height: 300px; padding: 10px; margin-bottom: 20px;"</span>&gt;</span></span><span class="language-xml"><span class="tag">&lt;/<span class="name">div</span>&gt;</span></span></span><br><span class="line"></span><br><span class="line"><span class="language-xml"><span class="tag">&lt;<span class="name">div</span> <span class="attr">id</span>=<span class="string">"categories-chart"</span> <span class="attr">data-parent</span>=<span class="string">"true"</span> <span class="attr">style</span>=<span class="string">"border-radius: 8px; height: 300px; padding: 10px;"</span>&gt;</span></span><span class="language-xml"><span class="tag">&lt;/<span class="name">div</span>&gt;</span></span></span><br></pre></td></tr></tbody></table></figure><h5 id="4-2-创建Charts页面模板"><a href="#4-2-创建Charts页面模板" class="headerlink" title="4.2 创建Charts页面模板"></a>4.2 创建Charts页面模板</h5><p><strong>操作步骤</strong>:</p><ol><li><p><strong>检查page.pug是否支持charts类型</strong>:</p><p><strong>文件位置</strong>: <code>themes/anzhiyu/layout/page.pug</code></p><p><strong>找到case语句，确保包含charts处理</strong>:</p></li></ol><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line">case page.type</span><br><span class="line">  when 'tags'</span><br><span class="line">    include includes/page/tags.pug</span><br><span class="line">  when 'link'</span><br><span class="line">    include includes/page/flink.pug</span><br><span class="line">  when 'categories'</span><br><span class="line">    include includes/page/categories.pug</span><br><span class="line">  when 'charts'</span><br><span class="line">    include includes/page/charts.pug</span><br><span class="line">  // ... 其他类型</span><br></pre></td></tr></tbody></table></figure><p><strong>如果没有charts行，请添加</strong>:<br></p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">when 'charts'</span><br><span class="line">  include includes/page/charts.pug</span><br></pre></td></tr></tbody></table></figure><p></p><ol start="2"><li><p><strong>创建charts页面模板</strong>:</p><p><strong>文件位置</strong>: <code>themes/anzhiyu/layout/includes/page/charts.pug</code></p><p><strong>创建文件并添加完整内容</strong>:</p></li></ol><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br></pre></td><td class="code"><pre><span class="line">//- charts.pug - 统计页面专用模板</span><br><span class="line"></span><br><span class="line">//- 显示页面内容</span><br><span class="line">!= page.content</span><br><span class="line"></span><br><span class="line">//- 统计页面专用资源加载</span><br><span class="line">script.</span><br><span class="line">  // 延迟加载ECharts以优化性能</span><br><span class="line">  function loadECharts() {</span><br><span class="line">    return new Promise((resolve, reject) =&gt; {</span><br><span class="line">      if (window.echarts) {</span><br><span class="line">        resolve();</span><br><span class="line">        return;</span><br><span class="line">      }</span><br><span class="line">      </span><br><span class="line">      console.log('📊 正在加载ECharts库...');</span><br><span class="line">      const script = document.createElement('script');</span><br><span class="line">      script.src = 'https://cdn.bootcdn.net/ajax/libs/echarts/4.9.0-rc.1/echarts.min.js';</span><br><span class="line">      script.onload = () =&gt; {</span><br><span class="line">        console.log('✅ ECharts加载完成');</span><br><span class="line">        resolve();</span><br><span class="line">      };</span><br><span class="line">      script.onerror = (err) =&gt; {</span><br><span class="line">        console.error('❌ ECharts加载失败:', err);</span><br><span class="line">        reject(err);</span><br><span class="line">      };</span><br><span class="line">      document.head.appendChild(script);</span><br><span class="line">    });</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  // 页面加载完成后再加载ECharts</span><br><span class="line">  document.addEventListener('DOMContentLoaded', () =&gt; {</span><br><span class="line">    // 给用户一个加载提示</span><br><span class="line">    const charts = document.querySelectorAll('[id$="-chart"]');</span><br><span class="line">    charts.forEach(chart =&gt; {</span><br><span class="line">      chart.innerHTML = '&lt;div style="text-align:center;padding:50px;color:#666;"&gt;📊 正在加载图表...&lt;/div&gt;';</span><br><span class="line">    });</span><br><span class="line">    </span><br><span class="line">    // 延迟1秒后开始加载，避免阻塞页面渲染</span><br><span class="line">    setTimeout(() =&gt; {</span><br><span class="line">      loadECharts()</span><br><span class="line">        .then(() =&gt; {</span><br><span class="line">          // ECharts加载完成，初始化图表</span><br><span class="line">          if (typeof initChartsPage === 'function') {</span><br><span class="line">            initChartsPage();</span><br><span class="line">          } else {</span><br><span class="line">            console.warn('initChartsPage函数未找到，请确保相关脚本已加载');</span><br><span class="line">          }</span><br><span class="line">          </span><br><span class="line">          // 清除加载提示</span><br><span class="line">          charts.forEach(chart =&gt; {</span><br><span class="line">            if (chart.innerHTML.includes('正在加载图表')) {</span><br><span class="line">              chart.innerHTML = '';</span><br><span class="line">            }</span><br><span class="line">          });</span><br><span class="line">        })</span><br><span class="line">        .catch(err =&gt; {</span><br><span class="line">          console.error('ECharts加载失败:', err);</span><br><span class="line">          // 显示错误信息</span><br><span class="line">          charts.forEach(chart =&gt; {</span><br><span class="line">            chart.innerHTML = '&lt;div style="text-align:center;padding:50px;color:#ff6b6b;"&gt;📊 图表加载失败，请刷新页面重试&lt;/div&gt;';</span><br><span class="line">          });</span><br><span class="line">        });</span><br><span class="line">    }, 1000);</span><br><span class="line">  });</span><br></pre></td></tr></tbody></table></figure><hr><h4 id="第五部分：页面模板清理"><a href="#第五部分：页面模板清理" class="headerlink" title="第五部分：页面模板清理"></a>第五部分：页面模板清理</h4><h5 id="5-1-清理Essay页面模板"><a href="#5-1-清理Essay页面模板" class="headerlink" title="5.1 清理Essay页面模板"></a>5.1 清理Essay页面模板</h5><p><strong>文件位置</strong>: <code>themes/anzhiyu/layout/includes/page/essay.pug</code></p><p><strong>查找文件开头</strong>，如果包含资源加载脚本，请移除：</p><p><strong>修改前</strong> (如果存在类似代码):</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">//- 即刻短文专用样式</span><br><span class="line">script.</span><br><span class="line">  if (!document.querySelector('link[href="/css/essay-style.css"]')) {</span><br><span class="line">    const essayCSS = document.createElement('link');</span><br><span class="line">    essayCSS.rel = 'stylesheet';</span><br><span class="line">    essayCSS.href = '/css/essay-style.css';</span><br><span class="line">    document.head.appendChild(essayCSS);</span><br><span class="line">  }</span><br></pre></td></tr></tbody></table></figure><p><strong>修改后</strong> (简化为):</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line">//- essay.pug 本人不太会pug所以可能代码不太好看</span><br><span class="line">if site.data.essay</span><br><span class="line">  // ... 保留原有的页面内容代码</span><br></pre></td></tr></tbody></table></figure><h5 id="5-2-清理Todolist页面模板"><a href="#5-2-清理Todolist页面模板" class="headerlink" title="5.2 清理Todolist页面模板"></a>5.2 清理Todolist页面模板</h5><p><strong>文件位置</strong>: <code>themes/anzhiyu/layout/includes/page/todolist.pug</code></p><p><strong>查找文件开头</strong>，如果包含资源加载脚本，请移除：</p><p><strong>修改前</strong> (如果存在类似代码):</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">//- todolist页面专用样式  </span><br><span class="line">script.</span><br><span class="line">  if (!document.querySelector('link[href="/custom/css/todolist.css"]')) {</span><br><span class="line">    const todolistCSS = document.createElement('link');</span><br><span class="line">    todolistCSS.rel = 'stylesheet';</span><br><span class="line">    todolistCSS.href = '/custom/css/todolist.css';</span><br><span class="line">    document.head.appendChild(todolistCSS);</span><br><span class="line">  }</span><br></pre></td></tr></tbody></table></figure><p><strong>修改后</strong> (简化为):</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">#todolist-box</span><br><span class="line">  // ... 保留原有的页面内容代码</span><br></pre></td></tr></tbody></table></figure><h4 id="第六部分：测试与验证"><a href="#第六部分：测试与验证" class="headerlink" title="第六部分：测试与验证"></a>第六部分：测试与验证</h4><h5 id="6-1-清理和重新生成"><a href="#6-1-清理和重新生成" class="headerlink" title="6.1 清理和重新生成"></a>6.1 清理和重新生成</h5><p><strong>必须按顺序执行以下命令</strong>:</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 1. 清理Hexo缓存</span></span><br><span class="line">hexo clean</span><br><span class="line"></span><br><span class="line"><span class="comment"># 2. 重新生成网站</span></span><br><span class="line">hexo g</span><br><span class="line"></span><br><span class="line"><span class="comment"># 3. 启动本地服务器测试</span></span><br><span class="line">hexo s</span><br></pre></td></tr></tbody></table></figure><h5 id="6-2-浏览器缓存清理"><a href="#6-2-浏览器缓存清理" class="headerlink" title="6.2 浏览器缓存清理"></a>6.2 浏览器缓存清理</h5><p><strong>清理浏览器缓存</strong>:</p><ul><li><strong>Chrome/Edge</strong>: 按 <code>Ctrl + Shift + R</code> (Windows) 或 <code>Cmd + Shift + R</code> (Mac)</li><li><strong>Firefox</strong>: 按 <code>Ctrl + F5</code> (Windows) 或 <code>Cmd + Shift + R</code> (Mac)</li><li>或者打开开发者工具(F12) → Network标签页 → 右键刷新按钮 → “清空缓存并硬性重新加载”</li></ul><h6 id="6-3-性能测试"><a href="#6-3-性能测试" class="headerlink" title="6.3 性能测试"></a>6.3 性能测试</h6><p><strong>开发者工具验证</strong>:</p><ol><li><p><strong>打开开发者工具</strong> (F12)</p></li><li><p><strong>Network标签页</strong>:</p><ul><li>刷新首页，观察加载的资源数量</li><li>应该只看到2个主要文件: <code>font.css</code> 和 <code>load-on-demand.js</code></li><li>注意ECharts等大文件不应该在首页加载</li></ul></li><li><p><strong>Performance标签页</strong>:</p><ul><li>录制首页加载过程</li><li>对比优化前后的加载时间</li></ul></li><li><p><strong>Console标签页</strong>:</p><ul><li>检查是否有错误信息</li><li>查看资源加载的日志信息</li></ul></li></ol><h5 id="6-4-功能验证"><a href="#6-4-功能验证" class="headerlink" title="6.4 功能验证"></a>6.4 功能验证</h5><p><strong>逐一测试各个页面</strong>:</p><ul><li>✅ <strong>首页</strong>: 样式正常，加载速度快</li><li>✅ <strong>文章页</strong>: 评论样式正常，文章样式完整</li><li>✅ <strong>统计页</strong>: ECharts图表正常显示，有加载提示</li><li>✅ <strong>即刻短文页</strong>: 样式正常</li><li>✅ <strong>待办清单页</strong>: 样式正常</li><li>✅ <strong>侧边栏</strong>: 日历、背景等功能正常</li></ul><hr><p>🚀 恭喜！优化完成</p><p>如果一切正常，您已经成功完成了性能优化！</p><hr><h3 id="5-使用Lighthouse进行科学的性能评测"><a href="#5-使用Lighthouse进行科学的性能评测" class="headerlink" title="5.使用Lighthouse进行科学的性能评测"></a><strong>5.使用Lighthouse进行科学的性能评测</strong></h3><h4 id="前言：从“感觉快”到“数据快”——为何需要线上评测？"><a href="#前言：从“感觉快”到“数据快”——为何需要线上评测？" class="headerlink" title="前言：从“感觉快”到“数据快”——为何需要线上评测？"></a><strong>前言：从“感觉快”到“数据快”——为何需要线上评测？</strong></h4><p>我们之前做的所有优化，最终效果都需要通过客观数据来衡量。Google Lighthouse是完成这项任务最专业的工具。</p><p>但是，Lighthouse 和 PageSpeed Insights 这类外部评测工具，无法访问您电脑上的本地地址（<code>localhost:4000</code>）。为了获得一份准确的性能报告，我们首先需要将博客<strong>临时部署</strong>到一个任何人都可以公开访问的地址。</p><p><strong>GitHub Pages</strong> 是完成这个“临时部署以供测试”任务最简单、最快速的免费平台。</p><hr><h6 id="第一步：临时部署到-GitHub-Pages-获取测试网址"><a href="#第一步：临时部署到-GitHub-Pages-获取测试网址" class="headerlink" title="第一步：临时部署到 GitHub Pages 获取测试网址"></a><strong>第一步：临时部署到 GitHub Pages 获取测试网址</strong></h6><p>这个阶段的唯一目的，就是为我们的博客生成一个公开的URL。</p><ol><li><p><strong>创建 GitHub 仓库</strong></p><ul><li>登录您的 GitHub 账号，创建一个新的<strong>公开 (Public)</strong> 仓库。</li><li><strong>仓库名称必须遵循特殊格式</strong>：<code>您的GitHub用户名.github.io</code></li><li>根据之前的ID，我的仓库名应该就是：<strong><code>Prorise-cool.github.io</code></strong></li></ul></li><li><p><strong>安装部署插件</strong></p><ul><li>在您博客的根目录终端中，运行：</li></ul><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install hexo-deployer-git --save</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>配置 <code>_config.yml</code> (站点配置文件)</strong></p><ul><li>打开您博客<strong>根目录</strong>下的 <code>_config.yml</code> 文件。</li><li><strong>修改 <code>url</code> 字段</strong>：将其<strong>临时</strong>设置为您的 GitHub Pages 地址。这是为了让生成的所有链接在测试环境中都是正确的。<figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 暂时将url设置为你的GitHub Pages地址</span></span><br><span class="line"><span class="attr">url:</span> <span class="string">https://Prorise-cool.github.io</span></span><br></pre></td></tr></tbody></table></figure></li><li><strong>修改 <code>deploy</code> 字段</strong>：在文件末尾，配置部署信息。<figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">deploy:</span></span><br><span class="line">  <span class="attr">type:</span> <span class="string">git</span></span><br><span class="line">  <span class="attr">repo:</span> <span class="string">'**************:Prorise-cool/Prorise-cool.github.io.git'</span> <span class="comment"># 推荐使用SSH地址</span></span><br><span class="line">  <span class="attr">branch:</span> <span class="string">main</span></span><br></pre></td></tr></tbody></table></figure></li></ul></li><li><p>请进入您在 GitHub 上的 <code>Prorise-cool.github.io</code> 仓库页面。</p><p>点击顶部的 <strong><code>Settings</code> (设置)</strong> 选项卡。</p><p>在左侧菜单中，点击 <strong><code>Pages</code></strong>。</p><p>在打开的页面中，找到 <strong><code>Build and deployment</code></strong> (构建和部署) 部分。</p><p>在 <strong><code>Source</code></strong> (源) 下面，请确保选项是 <strong><code>Deploy from a branch</code></strong> (从分支部署)。</p><p>在 <strong><code>Branch</code></strong> (分支) 下面，请<strong>再次确认</strong>下拉菜单中选择的是 <strong><code>main</code></strong>，并且文件夹选项选择的是 <strong><code>/(root)</code></strong>。</p></li><li><p><strong>执行部署</strong></p><ul><li>在终端中运行：</li></ul><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo clean &amp;&amp; hexo g -d</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>获取您的测试URL</strong></p><ul><li>等待一两分钟让 GitHub Pages 完成部署后，在浏览器中访问您的线上地址：<br><code>https://Prorise-cool.github.io</code></li><li>如果能看到您的博客，说明临时部署成功！<strong>这个地址就是我们下一步要用来测试的地址。</strong></li></ul></li></ol><ul><li>如果这里的设置不正确，请修改并点击 <strong><code>Save</code></strong>。</li><li>如果这里的设置已经是正确的，那么通常只是<strong>部署生效的延迟</strong>。</li></ul><hr><h6 id="第二步：使用Lighthouse进行性能评测"><a href="#第二步：使用Lighthouse进行性能评测" class="headerlink" title="第二步：使用Lighthouse进行性能评测"></a><strong>第二步：使用Lighthouse进行性能评测</strong></h6><p>现在我们有了公开的网址，可以开始正式评测了。</p><ol><li><p><strong>在 Chrome 开发者工具中运行（最推荐）</strong></p><ul><li>在 Chrome 浏览器中，打开您刚刚部署好的线上博客地址。</li><li>按 <code>F12</code> 打开开发者工具，找到并切换到 <strong><code>Lighthouse</code></strong> 选项卡。</li><li>在“Device (设备)”中，选择 <strong><code>Mobile</code></strong>（手机）。</li><li>在“Categories (类别)”中，勾选 <strong><code>Performance</code></strong>（性能）以及其他您关心的项目。</li><li>点击 <strong><code>Analyze page load</code></strong>（分析页面加载）按钮。</li></ul></li><li><p><strong>解读报告核心指标</strong></p><ul><li>测试完成后，您会得到一份详细报告。请重点关注 <strong>Performance</strong> 分数，以及下面这几个核心指标：</li></ul></li></ol><table><thead><tr><th align="left">指标名称 (Metric)</th><th align="left">测量内容</th><th align="left">理想得分 (Ideal Score)</th></tr></thead><tbody><tr><td align="left"><strong>FCP</strong> (First Contentful Paint)</td><td align="left">浏览器渲染出第一个内容的时间</td><td align="left"><code>1.8秒</code> 以内</td></tr><tr><td align="left"><strong>LCP</strong> (Largest Contentful Paint)</td><td align="left">视口内最大可见元素加载完成的时间</td><td align="left"><code>2.5秒</code> 以内</td></tr><tr><td align="left"><strong>TBT</strong> (Total Blocking Time)</td><td align="left">主线程被长任务阻塞的总时间</td><td align="left"><code>200毫秒</code> 以内</td></tr><tr><td align="left"><strong>CLS</strong> (Cumulative Layout Shift)</td><td align="left">页面加载过程中视觉元素的“跳动”量</td><td align="left"><code>0.1</code> 以下</td></tr></tbody></table><ol start="3"><li><strong>关注优化建议</strong><ul><li><strong>报告中最有价值的部分</strong>是下方的 <strong>“Opportunities (机遇)”</strong> 和 <strong>“Diagnostics (诊断)”</strong>。</li><li>它会明确告诉您哪些图片过大、哪些JS阻塞了渲染、哪些CSS没有被使用等等。您可以根据这些具体的建议，回头再去调整您的配置或代码。</li></ul></li></ol><hr><p><strong>后续步骤</strong></p><p>在您根据Lighthouse的报告，对您的博客进行了多轮的优化和测试，并对性能分数感到满意之后，我们才算真正完成了“性能优化”这个章节。</p><p>到那时，<strong>真正的“上一个服务器”</strong>——即将您的博客部署到您自己的服务器并绑定 <code>prorise.com</code> 这样的顶级域名——才会是我们的下一个大主题。</p></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/30787.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/30787.html&quot;)">第五部分：性能巅峰优化策略与实践</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/30787.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=第五部分：性能巅峰优化策略与实践&amp;url=https://prorise666.site/posts/30787.html&amp;pic=https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>框架技术<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Hexo<span class="categoryesPageCount">31</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>博客搭建教程<span class="tagsPageCount">31</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/58950.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">第三部分：集成与配置 Butterfly 主题——赋予博客华丽外观</div></div></a></div><div class="next-post pull-right"><a href="/posts/5555.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">第六部分：SEO终极攻略与实践</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/65188.html" title="11.Twikoo 美化：添加自定义表情包"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">11.Twikoo 美化：添加自定义表情包</div></div></a></div><div><a href="/posts/24286.html" title="10.内容扩展：添加“安全跳转”中间页"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">10.内容扩展：添加“安全跳转”中间页</div></div></a></div><div><a href="/posts/20246.html" title="13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）</div></div></a></div><div><a href="/posts/43263.html" title="14.主题魔改：添加“背景切换”弹窗面板"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">14.主题魔改：添加“背景切换”弹窗面板</div></div></a></div><div><a href="/posts/57565.html" title="12.Twikoo 美化：自定义评论回复邮件模板"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">12.Twikoo 美化：自定义评论回复邮件模板</div></div></a></div><div><a href="/posts/10882.html" title="16.主题魔改：文章顶图根据封面图片自动配色"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">16.主题魔改：文章顶图根据封面图片自动配色</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData = {
  title: "第五部分：性能巅峰优化策略与实践",
  date: "2025-07-02 16:13:45",
  updated: "2025-07-19 19:21:28",
  tags: ["博客搭建教程"],
  categories: ["框架技术","Hexo"],
  content: "\n## 第五部分：性能巅峰优化策略与实践\n\n构建一个高性能的Hexo博客对于提升用户体验至关重要，同时也是搜索引擎排名的重要考量因素。要实现“毫秒级”的加载速度，我们需要系统性地优化，而优化的第一步，也是最有效的一步，就是处理网站中体积最大的资源——图片。\n核心原则是：让访客从离他们地理位置最近的服务器加载图片。 对于以国内用户为主的中文博客，这意味着必须使用位于中国大陆的存储和CDN网络\n\n---\n\n### 1.图片加载优化\n现在，您可以根据您的核心需求来做决定：\n\n* 如果您的**第一要务是【零成本】**，并且您的读者遍布全球（或您不介意国内偶尔访问慢），那么 **方案一 (GitHub + jsDelivr)** 是一个完美的起点。\n\n* 如果您的**第一要务是【为国内访客提供最快、最稳定的体验】**，并且您追求“性能最强的网站”这个目标，愿意为此投入少量预算，那么 **方案二 (国内对象存储 + CDN)** 是毫无疑问的、更专业的最佳选择。\n对于大多数 Hexo 博客用户，尤其是在项目初期或面向全球读者时，这是一个免费、便捷且高效的图床方案。\n\n\n-----\n\n#### **国内CDN加速镜像节点汇总**\n\n以下几类提供可用的国内CDN加速镜像：\n\n**1. jsDelivr 镜像 (用于加速 GitHub 和 npm 资源)**\n这些节点可以用来替换官方的 `cdn.jsdelivr.net`。\n\n  * `jsd-proxy.ygxz.in`\n  * `cdn.iocdn.cc`\n  * `cdn.jsdmirror.com`\n  * `jsdelivr.topthink.com`\n  * `cdn.smartcis.cn` (智云加速)\n  * `jsd.cdn.zzko.cn`\n  * `jsd.onmicrosoft.cn`\n  * `jsdelivr.b-cdn.net`\n  * `cdn.jsdelivr.us`\n\n**2. unpkg / npm 镜像 (专门加速 npm 包)**\n这些节点可以用来替换官方的 `unpkg.com`。\n\n  * `s4.zstatic.net/npm` (Zstatic，使用时需在域名后加 `/npm`)\n  * `npm.elemecdn.com` (饿了么)\n  * `npm.onmicrosoft.cn`\n  * `unpkg.zhimg.com` (知乎)\n\n**3. cdnjs 镜像**\n这个节点可以用来替换官方的 `cdnjs.cloudflare.com`。\n\n  * `s4.zstatic.net` (Zstatic)\n  * `mirrors.sustech.edu.cn/cdnjs` (南方科技大学)\n\n-----\n\n##### **如何科学地测试这些节点的速度**\n\n要找到最适合您的节点，不能只凭感觉，需要进行客观测试。我推荐您结合使用以下两种方法：\n\n###### **在线多节点测速工具（全国访问体验测试 - 最重要）**\n\n这个方法可以模拟全国各地、不同网络（电信、联通、移动）的用户访问您的资源时的速度，这比您自己本地测试的结果更具参考价值。\n\n1.  **选择一个测试工具**：\n\n      * **[17CE](https://www.17ce.com/)**\n      * **[站长工具-超级Ping](https://ping.chinaz.com/)**\n\n2.  **准备测试链接**：\n\n      * 从我们上面的CDN镜像列表中，选择一个您想测试的节点的完整资源链接。例如：\n\n      ```bash\n      https://jsd-proxy.ygxz.in/npm/jquery@3.6.0/dist/jquery.min.js\n      https://cdn.iocdn.cc/npm/jquery@3.6.0/dist/jquery.min.js\n      https://cdn.jsdmirror.com/npm/jquery@3.6.0/dist/jquery.min.js\n      https://jsdelivr.topthink.com/npm/jquery@3.6.0/dist/jquery.min.js\n      https://cdn.smartcis.cn/npm/jquery@3.6.0/dist/jquery.min.js\n      https://jsd.cdn.zzko.cn/npm/jquery@3.6.0/dist/jquery.min.js\n      https://jsd.onmicrosoft.cn/npm/jquery@3.6.0/dist/jquery.min.js\n      https://jsdelivr.b-cdn.net/npm/jquery@3.6.0/dist/jquery.min.js\n      https://cdn.jsdelivr.us/npm/jquery@3.6.0/dist/jquery.min.js\n      ```\n\n3.  **进行测试**：\n\n      * 将这个链接粘贴到测速网站的输入框中，开始测试。\n      * 网站会从全国各地的服务器去请求这个链接，并返回每个地点的响应时间和状态。\n\n4.  **分析结果**：\n\n      * **看平均响应时间**：时间越短越好。\n      * **看失败率**：选择那些全国范围内绿色（正常）节点多、红色（超时/失败）节点少的CDN。\n      * **重复测试**：对您感兴趣的几个不同CDN镜像，重复这个过程，进行横向对比。\n\n  * 对于“公益”镜像，请记住它们的**稳定性可能随时变化**。建议您可以定期进行抽查测试。\n\n\n\n\n\n###### **方案一：GitHub + 国内CDN镜像 (零成本最优方案)**\n\n这个方案是当前针对国内环境的**最佳免费方案**。放弃了官方不稳定的 `cdn.jsdelivr.net`，转而使用由国内社区或第三方提供的、对中国大陆网络更友好的**加速镜像**。\n\n**工作流程：**\n\n1.  **创建 GitHub 图床仓库:**\n\n      * 在 GitHub 上创建一个新的**公开 (Public)** 仓库，专门用于存放您的博客图片，例如命名为 `prorise-blog-assets`。\n\n2.  **生成 GitHub Personal Access Token:**\n\n      * 前往 GitHub `设置(Settings)` \\> `Developer settings` \\> `Personal access tokens` \\> `Tokens (classic)`，生成一个新Token。\n      * 在权限 (Scopes) 选择中，至少需要勾选 `repo`。\n      * **请务必复制并保存好这个Token**，因为它只显示一次。\n\n3.  **安装并配置 PicGo (关键步骤):**\n\n      * 下载并安装 [PicGo 客户端](https://github.com/Molunerfinn/PicGo/releases)。\n      * 在“插件设置”中搜索并安装 `github-plus` 插件。\n      * 在“图床设置” \\> “GitHubPlus”中，填写您的信息。最大的不同在于 `customUrl` 字段：\n        ```text\n        # PicGo GitHubPlus 配置示例\n        repo: 您的用户名/您的仓库名\n        branch: main\n        token: 您的GitHub Token\n        path: img/ # 可选的图片存储路径\n        # 【关键】将 Custom URL 设置为您测试过最快的国内加速镜像\n        customUrl: https://jsd-proxy.ygxz.in/gh/您的用户名/您的仓库名@main\n        ```\n      * **关于国内镜像**：除了 `jsd-proxy.ygxz.in`，您也可以尝试使用其他镜像地址替换 `cdn.jsdelivr.net`，选择一个您测试下来最稳定的即可。\n\n4.  **上传与使用**:\n\n      * 设置好后，通过拖拽图片到 PicGo 窗口上传。PicGo 会自动将基于国内镜像的CDN链接复制到您的剪贴板，您直接在文章中粘贴使用。\n\n**优缺点分析：**\n\n  * **优点**: 完全免费，配置相对简单，**国内访问速度远超官方 jsDelivr**。\n  * **缺点**: **稳定性依赖于镜像提供方**。这些是“公益”服务，可能随时会因为滥用、成本或政策原因而失效，不适合对稳定性有极高要求的生产环境。\n\n-----\n\n###### **方案二：国内对象存储 + CDN (专业级性能方案)**\n\n如果您追求的是**企业级的稳定性和最顶级的加载速度**，那么这个方案是您的不二之选。\n\n**工作流程**:\n\n1.  **开通云服务**:\n\n      * 在**阿里云**或**腾讯云**注册并开通\\*\\*对象存储（OSS/COS）\\*\\*服务。\n      * 创建一个存储桶 (Bucket)，并将其读写权限设置为\\*\\*“公共读”\\*\\*。\n\n2.  **配置 CDN 加速**:\n\n      * 在对应的云服务商后台，为您的存储桶开通并绑定一个**CDN加速域名**。强烈建议使用您自己的一个子域名（例如 `img.prorise.com`），并根据引导完成 CNAME 解析。\n\n3.  **获取访问密钥 (Access Key)**:\n\n      * 在云服务商的\\*\\*访问控制（RAM/CAM）\\*\\*后台，创建一个专用的子用户，并为其生成 `AccessKey ID` 和 `AccessKey Secret`。\n      * **重要**：为这个子用户授予**仅能管理您那个存储桶**的权限，而不是全局权限。\n\n4.  **配置 PicGo 客户端**:\n\n      * 在 PicGo 的插件设置中，安装对应的插件（例如 `ali-oss` 或 `tencent-cos`）。\n      * 在图床设置中，填入您的 `AccessKey ID`, `AccessKey Secret`, `Bucket` 名称、存储区域以及您在第二步中配置好的**CDN加速域名**。\n\n**优缺点分析：**\n\n  * **优点**: **速度最快、最稳定**，有SLA服务保障，并支持图片处理、防盗链等丰富的专业功能。\n  * **缺点**: 需要少量费用（但个人博客用量成本极低，每月通常在几元以内），初始配置步骤最多。\n\n-----\n\n##### **总结与最终选择建议**\n\n感谢您的指正，让这份笔记变得更加完整和准确。\n\n  * 对于**起步阶段**或**个人实验性项目**，如果您追求零成本，**【方案一】** 是一个非常出色的选择，它解决了 jsDelivr 的主要痛点。\n  * 对于**追求极致性能和长期稳定性的严肃博客**，**【方案二】** 是更值得投资的专业选择，它能为您的国内访客提供最顶级的访问体验。\n\n\n\n\n\n\n---\n### **2.图片压缩策略**\n\n###### **前言：为什么图片优化至关重要？**\n\n图片通常是网页中体积最大的资源，它直接决定了您网站的加载速度。一个加载缓慢的网站会严重影响用户体验，并可能导致访客流失。因此，对图片进行全面优化，是在追求“毫秒级”性能道路上，投入产出比最高的一环。\n\n一个完整的图片优化流程包含四个核心策略：**压缩、格式选择、懒加载、以及CDN加速**。\n\n---\n###### **1. 图片压缩：在画质与体积之间找到平衡**\n\n* **是什么？**\n    通过特定的算法，在保持可接受的图像质量的前提下，尽可能地减小图片文件的体积。它分为“有损压缩”（会损失部分图像细节，压缩率高）和“无损压缩”（不损失细节，压缩率较低）。\n\n\n---\n###### **2. 格式选择：为不同场景选择最优格式**\n\n不同的图片格式有不同的特性，为您的图片选择最合适的格式，能从源头上优化性能。\n| 方法/工具        | 作用             | 推荐场景             | 优势                               | 劣势                           |\n| :--------------- | :--------------- | :------------------- | :--------------------------------- | :----------------------------- |\n| **图片压缩（有损/无损）** | 减小文件体积       | 任何图片             | 直接减小传输大小                   | 有损压缩可能略微影响图片质量   |\n| [在线工具](https://www.imgdiet.com/zh-CN/compress) | 在线批量压缩PNG/JPG | 零散图片处理，无需安装 | 方便快捷，压缩率高                 | 依赖网络，可能受限于上传大小/数量 |\n| 客户端工具 (Caesium) | 离线批量压缩图片   | 大量图片处理，离线操作 | 离线处理，可配置压缩参数             | 需要安装软件                   |\n| **图片格式选择**     | 使用更优格式     | 新增图片             | WebP格式体积更小，支持透明和动画   | 兼容性（旧浏览器），部分工具不支持 |\n| WebP             | 新一代图片格式     | 现代浏览器，要求性能 | 体积小，质量高                     | 部分旧浏览器可能不支持         |\n| JPG              | 摄影图片         | 色彩丰富             | 广泛兼容，适合照片                   | 不支持透明度，压缩artifacts明显 |\n| PNG              | 透明背景图片     | 图标，截图，需要透明度 | 支持透明度                         | 体积通常比JPG大，不适合照片      |\n| SVG              | 矢量图           | 图标，Logo，图表     | 无损缩放，体积小，可编辑，支持动画 | 不适合复杂照片                   |\n| **图片懒加载 (Lazy Load)** | 延迟加载非首屏图片 | 页面图片多           | 提升首屏速度，节省带宽           | 需要JS支持，可能影响部分图片索引 |\n| Butterfly内置    | 主题集成         | Butterfly用户        | 配置简单，无需额外插件             | 功能由主题决定                 |\n| 手动/插件        | 各种主题         | 高度定制             | 灵活性高，可控性强                 | 配置复杂，可能需要编写代码/插件 |\n| **图床CDN加速**      | 图片分发加速     | 任何博客             | 提升图片加载速度，减轻服务器压力   | 需要额外服务或配置               |\n| jsDelivr (配合GitHub) | 免费CDN          | 个人博客，静态站点     | 免费，方便（配合PicGo），全球节点    | 依赖GitHub可用性，偶尔不稳定     |\n| 对象存储 (OSS/COS) | 商业CDN+存储    | 流量大，要求高可用性 | 稳定可靠，专业服务                 | 有成本                     |\n\n\n---\n###### **3. 图片懒加载 (Lazy Loading)：提升首屏速度的关键**\n\n* **是什么？**\n    懒加载是一种延迟加载技术，它只在用户即将滚动到图片位置时，才开始加载这张图片。这可以极大地减少页面的初始加载体积，让首屏内容更快地显示出来，对SEO也很有利。\n\n* **怎么做？**\n    好消息是，使用的 Anzhiyu 主题**已经内置了强大的懒加载功能**。您只需在**主题配置文件** (`_config.anzhiyu.yml`) 中确保它是开启的。\n\n    ```yaml\n    # 在 themes/anzhiyu/_config.yml 中\n    lazyload:\n      enable: true # 确保这里是 true\n      field: site # site 表示全站生效\n      placeholder: /img/loading.gif # 加载前的占位图，请确保此路径有图片，或使用图床链接\n      blur: true # 图片加载前是否显示模糊效果\n    ```\n\n---\n### **3. 静态资源（JS/CSS）优化：压缩与按需加载**\n\n* **是什么？**\n    * **压缩 (Minify)**：移除JS和CSS代码中所有不影响功能的字符，如空格、换行、注释等，以减小文件体积。\n    * **合并 (Bundle)**：将多个小的JS或CSS文件合并成一个大文件，以减少浏览器需要发起的HTTP请求次数。\n    * **按需加载**：只在需要某个功能的页面，才加载对应的JS或CSS文件，避免在全站加载不必要的资源。\n\n* **怎么做？（解决方案）**\n    * **使用Hexo压缩插件**：这是实现压缩和合并最简单的方法。推荐使用 `hexo-neat` 插件。\n        1.  **安装插件**：在您博客根目录的终端中运行：\n            ```bash\n            npm install hexo-neat --save\n            ```\n        2.  **配置插件**：在**根目录 `_config.yml`** 文件末尾添加配置，它会自动在 `hexo g` 时处理您的文件。\n            ```yaml\n            # hexo-neat 压缩配置\n            neat_enable: true\n            # 压缩HTML\n            neat_html:\n              enable: true\n              exclude:\n            # 压缩CSS\n            neat_css:\n              enable: true\n              exclude:\n                - '**/*.min.css' # 排除已经压缩过的.min.css文件\n            # 压缩JS\n            neat_js:\n              enable: true\n              exclude:\n                - '**/*.min.js' # 排除已经压缩过的.min.js文件\n            ```\n    * **利用主题`inject`功能实现按需加载**：正如我们之前在配置**代码沙箱**、**自定义评论样式**等功能时所做的，将只在特定页面使用的JS和CSS，通过在**文章的 `.md` 文件**中直接编写 `<script>` 和 `<link>` 标签来引入，而不是通过主题的 `inject` 进行全局注入，这就是一种有效的按需加载实践。\n\n---\n###### **2. 浏览器缓存与CDN：让回头客“秒开”网站**\n\n* **浏览器缓存 (Browser Caching)**\n    * **是什么？**：您可以把它想象成，浏览器在第一次访问您的网站后，拍下了一张“快照”（即将CSS、JS、图片等资源保存在您的电脑上）。当您第二次访问时，浏览器会直接使用本地的“快照”，而无需再次从服务器下载，从而实现“秒开”。\n    * **怎么做？**：这通常**不需要您在Hexo中配置**。它是在您的**网站托管平台**上进行设置的。好消息是，像 **Vercel**、**Netlify**、**GitHub Pages** 这类现代化的托管平台，都已经为您配置了非常合理的默认缓存策略，您通常无需关心。\n\n* **CDN (Content Delivery Network)**\n    * **是什么？**：我们之前在讨论图床时已经深入了解过。它的核心是将您的静态资源（不仅仅是图片，也包括CSS和JS文件）部署到全球各地的服务器节点上，让用户从最近的节点加载。\n    * **怎么做？**：主题的 `_config.yml` 中已经有了 `CDN` 配置项，它允许您将主题依赖的第三方库（如jQuery, Fancybox等）的CDN提供商，从默认的 `jsdelivr` 更换为速度更快的国内镜像（例如我们之前讨论过的 `cdn.bootcdn.net` 或 `s4.zstatic.net`），这也是一种重要的CDN优化。\n\n我们将使用 `CDN.option` 这个配置，为每一个重要的库指定一个经过我们测试的、速度最快的国内镜像源，\n\n```yaml\n  option:\n    # --- 核心与基础库 ---\n    # 主题内部核心JS/CSS文件，如 main.js, utils.js 等，建议保持 local，由 Hexo 直接生成，以确保主题功能稳定。\n    # main_css: \n    # main: \"\"\n    # utils: \"\"\n    # jquery: \"\"\n    pjax: https://lib.baomitu.com/pjax/0.2.8/pjax.min.js\n    \n    # --- 页面功能与特效 ---\n    lazyload: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/vanilla-lazyload/17.3.1/lazyload.iife.min.js\n    instantpage: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js\n    typed: https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js\n    pangu: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js\n    # fancybox: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/fancybox/3.5.7/jquery.fancybox.min.js\n    # fancybox_css: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/fancybox/3.5.7/jquery.fancybox.min.css\n    medium_zoom: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/medium-zoom/1.0.6/medium-zoom.min.js\n    snackbar: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.js\n    snackbar_css: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css\n    fontawesome: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css\n    \n    # --- 评论系统 ---\n    # valine: # 如有需要，可自行查找Valine的CDN链接\n    # twikoo: \"\"\n   # waline_js: \"\"\n    #waline_css: \"\"\n    # artalk_js: # 如有需要，可自行查找Artalk的CDN链接\n    # artalk_css:\n\n    # --- 搜索系统 ---\n    # local_search: # 本地搜索通常与主题内部JS关联，建议保持local\n    #algolia_search: \"\"\n\n    # --- 音乐播放器 ---\n    aplayer_css: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css\n    aplayer_js: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js\n    meting_js: https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js\n\n    sharejs: https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js\n\n\n\n    # --- 其他不常用或建议保持默认的库 ---\n    # mathjax:\n    # katex:\n    # katex_copytex:\n    # mermaid:\n    # busuanzi: # 不蒜子官方脚本通常不建议替换\n```\n\n---\n###### **3. 字体加载优化：告别“文字消失术”**\n\n自定义字体虽然能提升美观度，但巨大的字体文件是拖慢速度的元凶之一，并可能导致在字体加载完成前，页面上的文字完全不可见（Flash of Invisible Text - FOIT）。\n\n* **怎么做？（三大策略）**\n    1.  **使用 WOFF2 格式**：这是目前最先进的网页字体格式，压缩率最高，文件体积最小。请务必将您使用的 `.otf` 或 `.ttf` 字体，通过在线工具转换为 `.woff2` 格式再使用。\n    2.  **字体子集化 (Subsetting)**：这是一项高级技术。如果您的字体只用于Logo或标题，仅使用了少数几十个字符，您可以通过“字体子集化”工具，从完整的几万个字符的字体文件中，只抽取出您用到的这几十个字符，生成一个体积极小的、定制化的字体文件。\n    3.  **善用 `font-display: swap`**：这是**最简单也最重要**的优化。它告诉浏览器：“在自定义字体还没下载好之前，请先用电脑里有的默认字体把文字显示出来，不要让用户看一片空白。等我的字体下载好了，你再悄悄地把它换上。”\n\n    **配置方法**：\n    * 在您的自定义CSS文件（例如 `source/css/font.css`）中，确保您的 `@font-face` 规则里包含了 `font-display: swap;` 这一行。\n    ```css\n    /* @font-face规则定义自定义字体 */\n    @font-face {\n      font-family: 'Your Blog Font'; /* 定义字体族名称 */\n      src: url('/fonts/your-blog-font.woff2') format('woff2'), /* WOFF2格式优先 */\n           url('/fonts/your-blog-font.woff') format('woff');  /* WOFF格式作为后备 */\n      font-weight: normal; /* 字体粗细 */\n      font-style: normal;  /* 字体样式 */\n      font-display: swap; /* 【关键】字体加载策略：先用系统字体，加载完再替换 */\n    }\n\n    /* 在body或特定元素上应用自定义字体 */\n    body {\n      font-family: 'Your Blog Font', sans-serif; /* 使用自定义字体，sans-serif作为备用 */\n    }\n    ```\n\n通过以上这些针对静态资源和字体的优化，您博客的性能和加载体验将得到全面的提升。\n\n\n\n### 4.关键渲染优化\n\n在我们前面的无脑集成中，的确步骤都非常方便，但所有的CSS样式和JS样式都在首页时渲染，在用户进入首页时，加载Echats库、加载别的页面的CSS/Js这无疑会大大降低了首页的渲染速度，那么为什么我们要到这一章节才来优化，而不在一开始就做呢，由于我们是按需集成的，市面上大部分的集成步骤也都是这样，比起在一开始就要注意每一个的集成步骤，不如我们集成完了之后再来统一管理会更好一些\n\n\n\n#### 优化目标\n\n- 解决ECharts等重型库导致的首页5秒加载问题\n- 实现\"谁生病，谁吃药\"的按需加载策略\n- 保持所有功能完整性的前提下大幅提升性能\n\n**预期效果**\n\n- **首页加载时间**: 5-8秒 → 1-2秒 (减少70%+)\n- **首页资源数量**: 20个文件 → 2个文件 (减少90%+)\n- **移动端体验**: 显著提升\n\n\n\n#### 第一部分：问题诊断与分析\n\n##### 1.1 检查当前配置状态\n\n首先，打开您的主题配置文件，检查当前的资源注入情况：\n\n**文件位置**: `_config.anzhiyu.yml`\n\n找到 `inject:` 配置段，您可能会看到类似这样的配置：\n\n```yaml\ninject:\n  head:\n    # 大量CSS文件全局加载\n    - <link rel=\"stylesheet\" href=\"/css/index_media.css\">\n    - <link rel=\"stylesheet\" href=\"/css/post-ui.css\">\n    - <link rel=\"stylesheet\" href=\"/css/essay-style.css\">\n    - <link rel=\"stylesheet\" href=\"/css/custom-comment.css\">\n    - <link rel=\"stylesheet\" href=\"/custom/css/schedule.css\">\n    - <link rel=\"stylesheet\" href=\"/css/bilibili.css\">\n    - <link rel=\"stylesheet\" href=\"/custom/css/todolist.css\">\n    - <link rel=\"stylesheet\" href=\"/custom/css/tip_style.css\">\n    - <link rel=\"stylesheet\" href=\"/custom/css/background-box.css\">\n    - <link rel=\"stylesheet\" href=\"/css/font.css\">\n    - <link rel=\"stylesheet\" href=\"/custom/css/sandbox_style.css\">\n\n  bottom:\n    # 大量JS文件全局加载\n    - <script src=\"/js/index_media.js\"></script>\n - <script src="\&quot;/js/comments.js\&quot;">\n - <script src=\"https://cdn.bootcdn.net/ajax/libs/echarts/4.9.0-rc.1/echarts.min.js\">\n - <script src=\"/js/fixed_comment.js\">\n - <script src=\"/custom/js/chineselunar.js\">\n - <script src=\"/custom/js/schedule.js\">\n - <script src=\"https://cdn.jsdelivr.net/npm/winbox@0.2.82/dist/winbox.bundle.min.js\">\n - <script src=\"/custom/js/tip_main.js\">\n - <script src=\"/custom/js/background-box.js\">\n```\n\n##### 1.2 性能问题分析\n\n**问题诊断**:\n\n1. **ECharts库**: 2-3MB的重型图表库在每个页面都加载\n2. **冗余CSS**: 即刻短文、待办清单等功能的样式在所有页面加载\n3. **无用JS**: 首页加载了文章页才需要的评论脚本\n4. **移动端负担**: 大量资源严重影响移动设备体验\n\n**性能影响**:\n\n- 首页加载11个CSS + 9个JS文件\n- 网络请求过多，瀑布流效应明显\n- 解析和执行时间过长\n- 内存占用过高\n\n\n\n#### 第二部分：核心配置优化\n\n##### 2.1 精简inject配置\n\n1. **打开配置文件**:\n \n ```bash\n _config.anzhiyu.yml\n ```\n\n2. **找到inject配置段** (通常在文件末尾附近)\n\n3. **完全替换inject配置**:\n \n 将原有的inject配置替换为以下内容：\n\n```yaml\ninject:\n head:\n # 只保留全站必需的基础资源\n - '<link rel=\"stylesheet\" href=\"/css/font.css\">'\n # 修复：首页样式优先加载，避免首屏渲染延迟\n - '<link rel=\"stylesheet\" href=\"/css/index_media.css\" media=\"(max-width: 0), screen and (prefers-reduced-motion: reduce)\" onload=\"this.media=`screen`\">'\n - '<link rel=\"stylesheet\" href=\"/css/post-ui.css\"\">'\n\n bottom:\n # 轻量级按需加载资源管理器（全站必需）\n - '<script src=\"/js/load-on-demand.js\">'\n```\n\n**⚠️ 重要说明**:\n- 我们只保留了字体CSS（全站必需）\n- 添加了即将创建的智能资源管理器\n- 移除了所有页面专用的资源\n\n------\n\n#### 第三部分：智能资源管理器\n\n##### 3.1 创建load-on-demand.js文件\n\n**操作步骤**:\n\n1. **创建文件目录** (如果不存在):\n ```bash\n mkdir -p themes/anzhiyu/source/js\n ```\n\n2. **创建资源管理器文件**:\n \n **文件位置**: `themes/anzhiyu/source/js/load-on-demand.js`\n \n **完整代码** (请复制粘贴整个文件内容):\n\n```javascript\n/**\n * 按需加载资源管理器\n * 用于优化网站性能，只在需要时加载特定资源\n */\n\nclass ResourceLoader {\n constructor() {\n this.loadedCSS = new Set();\n this.loadedJS = new Set();\n }\n\n /**\n * 动态加载CSS文件\n * @param {string} href - CSS文件路径\n * @param {string} id - 可选的link元素ID\n */\n loadCSS(href, id = null) {\n if (this.loadedCSS.has(href) || document.querySelector(`link[href=\"${href}\"]`)) {\n return Promise.resolve();\n }\n\n return new Promise((resolve, reject) => {\n const link = document.createElement('link');\n link.rel = 'stylesheet';\n link.href = href;\n if (id) link.id = id;\n\n link.onload = () => {\n this.loadedCSS.add(href);\n resolve();\n };\n link.onerror = reject;\n\n document.head.appendChild(link);\n });\n }\n\n /**\n * 动态加载JS文件\n * @param {string} src - JS文件路径\n * @param {string} id - 可选的script元素ID\n */\n loadJS(src, id = null) {\n if (this.loadedJS.has(src) || document.querySelector(`script[src=\"${src}\"]`)) {\n return Promise.resolve();\n }\n\n return new Promise((resolve, reject) => {\n const script = document.createElement('script');\n script.src = src;\n if (id) script.id = id;\n\n script.onload = () => {\n this.loadedJS.add(src);\n resolve();\n };\n script.onerror = reject;\n\n document.body.appendChild(script);\n });\n }\n\n /**\n * 检测页面内容并按需加载相关资源\n */\n autoDetectAndLoad() {\n // 检测是否为首页\n if (window.location.pathname === '/' || window.location.pathname === '/index.html') {\n // 修复：index_media.css 现在由头部优先加载，只需加载JS\n this.loadJS('/js/index_media.js', 'index-media-script');\n }\n\n // 检测是否为文章页\n if (document.querySelector('#post') || document.querySelector('.post-content')) {\n this.loadCSS('/css/custom-comment.css', 'custom-comment-style');\n this.loadCSS('/custom/css/tip_style.css', 'tip-style');\n this.loadJS('/js/fixed_comment.js', 'fixed-comment-script');\n this.loadJS('/custom/js/tip_main.js', 'tip-main-script');\n }\n\n // 检测B站视频内容\n if (document.querySelector('iframe[src*=\"bilibili.com\"]') ||\n document.querySelector('iframe[src*=\"player.bilibili.com\"]')) {\n this.loadCSS('/css/bilibili.css', 'bilibili-style');\n }\n\n // 检测代码块\n if (document.querySelector('pre code') || document.querySelector('.highlight')) {\n this.loadCSS('/custom/css/sandbox_style.css', 'sandbox-style');\n }\n\n // 检测评论区\n if (document.querySelector('#twikoo') ||\n document.querySelector('#waline') ||\n document.querySelector('#valine')) {\n this.loadJS('/js/comments.js', 'comments-script');\n }\n\n // 检测即刻短文页面\n if (window.location.pathname.includes('/essay/') || document.querySelector('#essay_page')) {\n this.loadCSS('/css/essay-style.css', 'essay-style');\n }\n\n // 检测待办清单页面\n if (window.location.pathname.includes('/todolist/') || document.querySelector('#todolist-box')) {\n this.loadCSS('/custom/css/todolist.css', 'todolist-style');\n }\n\n // 检测侧边栏相关功能\n if (document.querySelector('#sidebar')) {\n this.loadCSS('/custom/css/schedule.css', 'schedule-style');\n this.loadCSS('/custom/css/background-box.css', 'background-style');\n this.loadJS('https://cdn.jsdelivr.net/npm/winbox@0.2.82/dist/winbox.bundle.min.js', 'winbox-lib')\n .then(() => this.loadJS('/custom/js/chineselunar.js', 'chineselunar-script'))\n .then(() => this.loadJS('/custom/js/schedule.js', 'schedule-script'))\n .then(() => this.loadJS('/custom/js/background-box.js', 'background-script'))\n .catch(err => console.warn('侧边栏脚本加载失败:', err));\n }\n }\n}\n\n// 创建全局实例\nwindow.resourceLoader = new ResourceLoader();\n\n// 页面加载完成后自动检测\ndocument.addEventListener('DOMContentLoaded', () => {\n window.resourceLoader.autoDetectAndLoad();\n});\n\n// 为PJAX提供支持\ndocument.addEventListener('pjax:complete', () => {\n window.resourceLoader.autoDetectAndLoad();\n}); \n```\n\n##### 3.2 功能特性说明\n\n**智能检测机制**:\n- **页面类型检测**: 根据URL路径和DOM元素判断页面类型\n- **内容特征检测**: 检测B站视频、代码块、评论区等特殊内容\n- **防重复加载**: 确保每个资源只加载一次\n- **PJAX兼容**: 支持无刷新页面跳转\n\n**错误处理**:\n\n- 加载失败时的优雅降级\n- 详细的错误日志记录\n- 不影响其他功能的正常运行\n\n---\n\n\n#### 第四部分：ECharts统计页面优化\n\n##### 4.1 修改统计页面配置\n\n**检查统计页面**:\n\n1. **找到统计页面文件**:\n ```bash\n # 检查是否存在统计页面\n ls source/charts/\n ```\n\n2. **编辑统计页面配置**:\n \n **文件位置**: `source/charts/index.md`\n \n **查找并修改**，如果文件包含内联的ECharts脚本，将其替换为：\n\n```markdown\n---\ntitle: 网站统计\ntype: \"charts\"\ncomments: false\naside: false\n---\n\n<div id=\"posts-chart\" data-start=\"2024-09\" style=\"border-radius: 8px; height: 300px; padding: 10px; margin-bottom: 20px;\"></div>\n\n<div id=\"tags-chart\" data-length=\"10\" style=\"border-radius: 8px; height: 300px; padding: 10px; margin-bottom: 20px;\"></div>\n\n<div id=\"categories-chart\" data-parent=\"true\" style=\"border-radius: 8px; height: 300px; padding: 10px;\"></main></div>\n```\n\n##### 4.2 创建Charts页面模板\n\n**操作步骤**:\n\n1. **检查page.pug是否支持charts类型**:\n \n **文件位置**: `themes/anzhiyu/layout/page.pug`\n \n **找到case语句，确保包含charts处理**:\n\n```pug\ncase page.type\n when 'tags'\n include includes/page/tags.pug\n when 'link'\n include includes/page/flink.pug\n when 'categories'\n include includes/page/categories.pug\n when 'charts'\n include includes/page/charts.pug\n // ... 其他类型\n```\n\n **如果没有charts行，请添加**:\n ```pug\n when 'charts'\n include includes/page/charts.pug\n ```\n\n2. **创建charts页面模板**:\n \n **文件位置**: `themes/anzhiyu/layout/includes/page/charts.pug`\n \n **创建文件并添加完整内容**:\n\n```pug\n//- charts.pug - 统计页面专用模板\n\n//- 显示页面内容\n!= page.content\n\n//- 统计页面专用资源加载\nscript.\n // 延迟加载ECharts以优化性能\n function loadECharts() {\n return new Promise((resolve, reject) => {\n if (window.echarts) {\n resolve();\n return;\n }\n \n console.log('📊 正在加载ECharts库...');\n const script = document.createElement('script');\n script.src = 'https://cdn.bootcdn.net/ajax/libs/echarts/4.9.0-rc.1/echarts.min.js';\n script.onload = () => {\n console.log('✅ ECharts加载完成');\n resolve();\n };\n script.onerror = (err) => {\n console.error('❌ ECharts加载失败:', err);\n reject(err);\n };\n document.head.appendChild(script);\n });\n }\n \n // 页面加载完成后再加载ECharts\n document.addEventListener('DOMContentLoaded', () => {\n // 给用户一个加载提示\n const charts = document.querySelectorAll('[id$=\"-chart\"]');\n charts.forEach(chart => {\n chart.innerHTML = '<div style=\"text-align:center;padding:50px;color:#666;\">📊 正在加载图表...';\n });\n \n // 延迟1秒后开始加载，避免阻塞页面渲染\n setTimeout(() => {\n loadECharts()\n .then(() => {\n // ECharts加载完成，初始化图表\n if (typeof initChartsPage === 'function') {\n initChartsPage();\n } else {\n console.warn('initChartsPage函数未找到，请确保相关脚本已加载');\n }\n \n // 清除加载提示\n charts.forEach(chart => {\n if (chart.innerHTML.includes('正在加载图表')) {\n chart.innerHTML = '';\n }\n });\n })\n .catch(err => {\n console.error('ECharts加载失败:', err);\n // 显示错误信息\n charts.forEach(chart => {\n chart.innerHTML = '<div style=\"text-align:center;padding:50px;color:#ff6b6b;\">📊 图表加载失败，请刷新页面重试';\n });\n });\n }, 1000);\n });\n```\n\n---\n\n#### 第五部分：页面模板清理\n\n##### 5.1 清理Essay页面模板\n\n**文件位置**: `themes/anzhiyu/layout/includes/page/essay.pug`\n\n**查找文件开头**，如果包含资源加载脚本，请移除：\n\n**修改前** (如果存在类似代码):\n\n```pug\n//- 即刻短文专用样式\nscript.\n if (!document.querySelector('link[href=\"/css/essay-style.css\"]')) {\n const essayCSS = document.createElement('link');\n essayCSS.rel = 'stylesheet';\n essayCSS.href = '/css/essay-style.css';\n document.head.appendChild(essayCSS);\n }\n```\n\n**修改后** (简化为):\n```pug\n//- essay.pug 本人不太会pug所以可能代码不太好看\nif site.data.essay\n // ... 保留原有的页面内容代码\n```\n\n##### 5.2 清理Todolist页面模板\n\n**文件位置**: `themes/anzhiyu/layout/includes/page/todolist.pug`\n\n**查找文件开头**，如果包含资源加载脚本，请移除：\n\n**修改前** (如果存在类似代码):\n```pug\n//- todolist页面专用样式 \nscript.\n if (!document.querySelector('link[href=\"/custom/css/todolist.css\"]')) {\n const todolistCSS = document.createElement('link');\n todolistCSS.rel = 'stylesheet';\n todolistCSS.href = '/custom/css/todolist.css';\n document.head.appendChild(todolistCSS);\n }\n```\n\n**修改后** (简化为):\n```pug\n#todolist-box\n // ... 保留原有的页面内容代码\n```\n\n\n\n#### 第六部分：测试与验证\n\n##### 6.1 清理和重新生成\n\n**必须按顺序执行以下命令**:\n\n```bash\n# 1. 清理Hexo缓存\nhexo clean\n\n# 2. 重新生成网站\nhexo g\n\n# 3. 启动本地服务器测试\nhexo s\n```\n\n##### 6.2 浏览器缓存清理\n\n**清理浏览器缓存**:\n- **Chrome/Edge**: 按 `Ctrl + Shift + R` (Windows) 或 `Cmd + Shift + R` (Mac)\n- **Firefox**: 按 `Ctrl + F5` (Windows) 或 `Cmd + Shift + R` (Mac)\n- 或者打开开发者工具(F12) → Network标签页 → 右键刷新按钮 → \"清空缓存并硬性重新加载\"\n\n###### 6.3 性能测试\n\n**开发者工具验证**:\n\n1. **打开开发者工具** (F12)\n2. **Network标签页**:\n - 刷新首页，观察加载的资源数量\n - 应该只看到2个主要文件: `font.css` 和 `load-on-demand.js`\n - 注意ECharts等大文件不应该在首页加载\n\n3. **Performance标签页**:\n - 录制首页加载过程\n - 对比优化前后的加载时间\n\n4. **Console标签页**:\n - 检查是否有错误信息\n - 查看资源加载的日志信息\n\n##### 6.4 功能验证\n\n**逐一测试各个页面**:\n\n- ✅ **首页**: 样式正常，加载速度快\n- ✅ **文章页**: 评论样式正常，文章样式完整\n- ✅ **统计页**: ECharts图表正常显示，有加载提示\n- ✅ **即刻短文页**: 样式正常\n- ✅ **待办清单页**: 样式正常\n- ✅ **侧边栏**: 日历、背景等功能正常\n\n---\n🚀 恭喜！优化完成\n\n如果一切正常，您已经成功完成了性能优化！\n\n\n\n\n---\n\n### **5.使用Lighthouse进行科学的性能评测**\n\n#### **前言：从“感觉快”到“数据快”——为何需要线上评测？**\n\n我们之前做的所有优化，最终效果都需要通过客观数据来衡量。Google Lighthouse是完成这项任务最专业的工具。\n\n但是，Lighthouse 和 PageSpeed Insights 这类外部评测工具，无法访问您电脑上的本地地址（`localhost:4000`）。为了获得一份准确的性能报告，我们首先需要将博客**临时部署**到一个任何人都可以公开访问的地址。\n\n**GitHub Pages** 是完成这个“临时部署以供测试”任务最简单、最快速的免费平台。\n\n---\n###### **第一步：临时部署到 GitHub Pages 获取测试网址**\n\n这个阶段的唯一目的，就是为我们的博客生成一个公开的URL。\n\n1. **创建 GitHub 仓库**\n * 登录您的 GitHub 账号，创建一个新的**公开 (Public)** 仓库。\n * **仓库名称必须遵循特殊格式**：`您的GitHub用户名.github.io`\n * 根据之前的ID，我的仓库名应该就是：**`Prorise-cool.github.io`**\n\n2. **安装部署插件**\n * 在您博客的根目录终端中，运行：\n ```bash\n npm install hexo-deployer-git --save\n ```\n\n3. **配置 `_config.yml` (站点配置文件)**\n * 打开您博客**根目录**下的 `_config.yml` 文件。\n * **修改 `url` 字段**：将其**临时**设置为您的 GitHub Pages 地址。这是为了让生成的所有链接在测试环境中都是正确的。\n ```yaml\n # 暂时将url设置为你的GitHub Pages地址\n url: https://Prorise-cool.github.io\n ```\n * **修改 `deploy` 字段**：在文件末尾，配置部署信息。\n ```yaml\n deploy:\n type: git\n repo: '**************:Prorise-cool/Prorise-cool.github.io.git' # 推荐使用SSH地址\n branch: main\n ```\n\n4. 请进入您在 GitHub 上的 `Prorise-cool.github.io` 仓库页面。\n\n 点击顶部的 **`Settings` (设置)** 选项卡。\n\n 在左侧菜单中，点击 **`Pages`**。\n\n 在打开的页面中，找到 **`Build and deployment`** (构建和部署) 部分。\n\n 在 **`Source`** (源) 下面，请确保选项是 **`Deploy from a branch`** (从分支部署)。\n\n 在 **`Branch`** (分支) 下面，请**再次确认**下拉菜单中选择的是 **`main`**，并且文件夹选项选择的是 **`/(root)`**。\n\n5. **执行部署**\n\n * 在终端中运行：\n ```bash\n hexo clean && hexo g -d\n ```\n\n6. **获取您的测试URL**\n * 等待一两分钟让 GitHub Pages 完成部署后，在浏览器中访问您的线上地址：\n `https://Prorise-cool.github.io`\n * 如果能看到您的博客，说明临时部署成功！**这个地址就是我们下一步要用来测试的地址。**\n\n- 如果这里的设置不正确，请修改并点击 **`Save`**。\n- 如果这里的设置已经是正确的，那么通常只是**部署生效的延迟**。\n\n---\n###### **第二步：使用Lighthouse进行性能评测**\n\n现在我们有了公开的网址，可以开始正式评测了。\n\n1. **在 Chrome 开发者工具中运行（最推荐）**\n * 在 Chrome 浏览器中，打开您刚刚部署好的线上博客地址。\n * 按 `F12` 打开开发者工具，找到并切换到 **`Lighthouse`** 选项卡。\n * 在“Device (设备)”中，选择 **`Mobile`**（手机）。\n * 在“Categories (类别)”中，勾选 **`Performance`**（性能）以及其他您关心的项目。\n * 点击 **`Analyze page load`**（分析页面加载）按钮。\n\n2. **解读报告核心指标**\n * 测试完成后，您会得到一份详细报告。请重点关注 **Performance** 分数，以及下面这几个核心指标：\n\n| 指标名称 (Metric) | 测量内容 | 理想得分 (Ideal Score) |\n| :--- | :--- | :--- |\n| **FCP** (First Contentful Paint) | 浏览器渲染出第一个内容的时间 | `1.8秒` 以内 |\n| **LCP** (Largest Contentful Paint) | 视口内最大可见元素加载完成的时间| `2.5秒` 以内 |\n| **TBT** (Total Blocking Time) | 主线程被长任务阻塞的总时间 | `200毫秒` 以内 |\n| **CLS** (Cumulative Layout Shift) | 页面加载过程中视觉元素的“跳动”量| `0.1` 以下 |\n\n3. **关注优化建议**\n * **报告中最有价值的部分**是下方的 **“Opportunities (机遇)”** 和 **“Diagnostics (诊断)”**。\n * 它会明确告诉您哪些图片过大、哪些JS阻塞了渲染、哪些CSS没有被使用等等。您可以根据这些具体的建议，回头再去调整您的配置或代码。\n\n---\n**后续步骤**\n\n在您根据Lighthouse的报告，对您的博客进行了多轮的优化和测试，并对性能分数感到满意之后，我们才算真正完成了“性能优化”这个章节。\n\n到那时，**真正的“上一个服务器”**——即将您的博客部署到您自己的服务器并绑定 `prorise.com` 这样的顶级域名——才会是我们的下一个大主题。" };<div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror='this.onerror=null,this.src="https://bu.dusays.com/2024/06/20/6673caa5bca18.gif"'><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E9%83%A8%E5%88%86%EF%BC%9A%E6%80%A7%E8%83%BD%E5%B7%85%E5%B3%B0%E4%BC%98%E5%8C%96%E7%AD%96%E7%95%A5%E4%B8%8E%E5%AE%9E%E8%B7%B5"><span class="toc-number">1.</span> <span class="toc-text">第五部分：性能巅峰优化策略与实践</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E5%9B%BE%E7%89%87%E5%8A%A0%E8%BD%BD%E4%BC%98%E5%8C%96"><span class="toc-number">1.1.</span> <span class="toc-text">1.图片加载优化</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%9B%BD%E5%86%85CDN%E5%8A%A0%E9%80%9F%E9%95%9C%E5%83%8F%E8%8A%82%E7%82%B9%E6%B1%87%E6%80%BB"><span class="toc-number">1.1.1.</span> <span class="toc-text">国内CDN加速镜像节点汇总</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%A6%82%E4%BD%95%E7%A7%91%E5%AD%A6%E5%9C%B0%E6%B5%8B%E8%AF%95%E8%BF%99%E4%BA%9B%E8%8A%82%E7%82%B9%E7%9A%84%E9%80%9F%E5%BA%A6"><span class="toc-number">1.1.1.1.</span> <span class="toc-text">如何科学地测试这些节点的速度</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%9C%A8%E7%BA%BF%E5%A4%9A%E8%8A%82%E7%82%B9%E6%B5%8B%E9%80%9F%E5%B7%A5%E5%85%B7%EF%BC%88%E5%85%A8%E5%9B%BD%E8%AE%BF%E9%97%AE%E4%BD%93%E9%AA%8C%E6%B5%8B%E8%AF%95-%E6%9C%80%E9%87%8D%E8%A6%81%EF%BC%89"><span class="toc-number">1.1.1.1.1.</span> <span class="toc-text">在线多节点测速工具（全国访问体验测试 - 最重要）</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%96%B9%E6%A1%88%E4%B8%80%EF%BC%9AGitHub-%E5%9B%BD%E5%86%85CDN%E9%95%9C%E5%83%8F-%E9%9B%B6%E6%88%90%E6%9C%AC%E6%9C%80%E4%BC%98%E6%96%B9%E6%A1%88"><span class="toc-number">1.1.1.1.2.</span> <span class="toc-text">方案一：GitHub + 国内CDN镜像 (零成本最优方案)</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E6%96%B9%E6%A1%88%E4%BA%8C%EF%BC%9A%E5%9B%BD%E5%86%85%E5%AF%B9%E8%B1%A1%E5%AD%98%E5%82%A8-CDN-%E4%B8%93%E4%B8%9A%E7%BA%A7%E6%80%A7%E8%83%BD%E6%96%B9%E6%A1%88"><span class="toc-number">1.1.1.1.3.</span> <span class="toc-text">方案二：国内对象存储 + CDN (专业级性能方案)</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%80%BB%E7%BB%93%E4%B8%8E%E6%9C%80%E7%BB%88%E9%80%89%E6%8B%A9%E5%BB%BA%E8%AE%AE"><span class="toc-number">1.1.1.2.</span> <span class="toc-text">总结与最终选择建议</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%9B%BE%E7%89%87%E5%8E%8B%E7%BC%A9%E7%AD%96%E7%95%A5"><span class="toc-number">1.2.</span> <span class="toc-text">2.图片压缩策略</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A%E4%B8%BA%E4%BB%80%E4%B9%88%E5%9B%BE%E7%89%87%E4%BC%98%E5%8C%96%E8%87%B3%E5%85%B3%E9%87%8D%E8%A6%81%EF%BC%9F"><span class="toc-number">1.2.0.0.1.</span> <span class="toc-text">前言：为什么图片优化至关重要？</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E5%9B%BE%E7%89%87%E5%8E%8B%E7%BC%A9%EF%BC%9A%E5%9C%A8%E7%94%BB%E8%B4%A8%E4%B8%8E%E4%BD%93%E7%A7%AF%E4%B9%8B%E9%97%B4%E6%89%BE%E5%88%B0%E5%B9%B3%E8%A1%A1"><span class="toc-number">1.2.0.0.2.</span> <span class="toc-text">1. 图片压缩：在画质与体积之间找到平衡</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E6%A0%BC%E5%BC%8F%E9%80%89%E6%8B%A9%EF%BC%9A%E4%B8%BA%E4%B8%8D%E5%90%8C%E5%9C%BA%E6%99%AF%E9%80%89%E6%8B%A9%E6%9C%80%E4%BC%98%E6%A0%BC%E5%BC%8F"><span class="toc-number">1.2.0.0.3.</span> <span class="toc-text">2. 格式选择：为不同场景选择最优格式</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E5%9B%BE%E7%89%87%E6%87%92%E5%8A%A0%E8%BD%BD-Lazy-Loading-%EF%BC%9A%E6%8F%90%E5%8D%87%E9%A6%96%E5%B1%8F%E9%80%9F%E5%BA%A6%E7%9A%84%E5%85%B3%E9%94%AE"><span class="toc-number">1.2.0.0.4.</span> <span class="toc-text">3. 图片懒加载 (Lazy Loading)：提升首屏速度的关键</span></a></li></ol></li></ol></li></ol><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E9%9D%99%E6%80%81%E8%B5%84%E6%BA%90%EF%BC%88JS-CSS%EF%BC%89%E4%BC%98%E5%8C%96%EF%BC%9A%E5%8E%8B%E7%BC%A9%E4%B8%8E%E6%8C%89%E9%9C%80%E5%8A%A0%E8%BD%BD"><span class="toc-number">1.3.</span> <span class="toc-text">3. 静态资源（JS&#x2F;CSS）优化：压缩与按需加载</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E6%B5%8F%E8%A7%88%E5%99%A8%E7%BC%93%E5%AD%98%E4%B8%8ECDN%EF%BC%9A%E8%AE%A9%E5%9B%9E%E5%A4%B4%E5%AE%A2%E2%80%9C%E7%A7%92%E5%BC%80%E2%80%9D%E7%BD%91%E7%AB%99"><span class="toc-number">1.3.0.0.1.</span> <span class="toc-text">2. 浏览器缓存与CDN：让回头客“秒开”网站</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E5%AD%97%E4%BD%93%E5%8A%A0%E8%BD%BD%E4%BC%98%E5%8C%96%EF%BC%9A%E5%91%8A%E5%88%AB%E2%80%9C%E6%96%87%E5%AD%97%E6%B6%88%E5%A4%B1%E6%9C%AF%E2%80%9D"><span class="toc-number">1.3.0.0.2.</span> <span class="toc-text">3. 字体加载优化：告别“文字消失术”</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E5%85%B3%E9%94%AE%E6%B8%B2%E6%9F%93%E4%BC%98%E5%8C%96"><span class="toc-number">1.4.</span> <span class="toc-text">4.关键渲染优化</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%BC%98%E5%8C%96%E7%9B%AE%E6%A0%87"><span class="toc-number">1.4.1.</span> <span class="toc-text">优化目标</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E9%83%A8%E5%88%86%EF%BC%9A%E9%97%AE%E9%A2%98%E8%AF%8A%E6%96%AD%E4%B8%8E%E5%88%86%E6%9E%90"><span class="toc-number">1.4.2.</span> <span class="toc-text">第一部分：问题诊断与分析</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-1-%E6%A3%80%E6%9F%A5%E5%BD%93%E5%89%8D%E9%85%8D%E7%BD%AE%E7%8A%B6%E6%80%81"><span class="toc-number">1.4.2.1.</span> <span class="toc-text">1.1 检查当前配置状态</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#1-2-%E6%80%A7%E8%83%BD%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90"><span class="toc-number">1.4.2.2.</span> <span class="toc-text">1.2 性能问题分析</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E9%83%A8%E5%88%86%EF%BC%9A%E6%A0%B8%E5%BF%83%E9%85%8D%E7%BD%AE%E4%BC%98%E5%8C%96"><span class="toc-number">1.4.3.</span> <span class="toc-text">第二部分：核心配置优化</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#2-1-%E7%B2%BE%E7%AE%80inject%E9%85%8D%E7%BD%AE"><span class="toc-number">1.4.3.1.</span> <span class="toc-text">2.1 精简inject配置</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E9%83%A8%E5%88%86%EF%BC%9A%E6%99%BA%E8%83%BD%E8%B5%84%E6%BA%90%E7%AE%A1%E7%90%86%E5%99%A8"><span class="toc-number">1.4.4.</span> <span class="toc-text">第三部分：智能资源管理器</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#3-1-%E5%88%9B%E5%BB%BAload-on-demand-js%E6%96%87%E4%BB%B6"><span class="toc-number">1.4.4.1.</span> <span class="toc-text">3.1 创建load-on-demand.js文件</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-2-%E5%8A%9F%E8%83%BD%E7%89%B9%E6%80%A7%E8%AF%B4%E6%98%8E"><span class="toc-number">1.4.4.2.</span> <span class="toc-text">3.2 功能特性说明</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E5%9B%9B%E9%83%A8%E5%88%86%EF%BC%9AECharts%E7%BB%9F%E8%AE%A1%E9%A1%B5%E9%9D%A2%E4%BC%98%E5%8C%96"><span class="toc-number">1.4.5.</span> <span class="toc-text">第四部分：ECharts统计页面优化</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#4-1-%E4%BF%AE%E6%94%B9%E7%BB%9F%E8%AE%A1%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE"><span class="toc-number">1.4.5.1.</span> <span class="toc-text">4.1 修改统计页面配置</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-2-%E5%88%9B%E5%BB%BACharts%E9%A1%B5%E9%9D%A2%E6%A8%A1%E6%9D%BF"><span class="toc-number">1.4.5.2.</span> <span class="toc-text">4.2 创建Charts页面模板</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E9%83%A8%E5%88%86%EF%BC%9A%E9%A1%B5%E9%9D%A2%E6%A8%A1%E6%9D%BF%E6%B8%85%E7%90%86"><span class="toc-number">1.4.6.</span> <span class="toc-text">第五部分：页面模板清理</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#5-1-%E6%B8%85%E7%90%86Essay%E9%A1%B5%E9%9D%A2%E6%A8%A1%E6%9D%BF"><span class="toc-number">1.4.6.1.</span> <span class="toc-text">5.1 清理Essay页面模板</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#5-2-%E6%B8%85%E7%90%86Todolist%E9%A1%B5%E9%9D%A2%E6%A8%A1%E6%9D%BF"><span class="toc-number">1.4.6.2.</span> <span class="toc-text">5.2 清理Todolist页面模板</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E7%AC%AC%E5%85%AD%E9%83%A8%E5%88%86%EF%BC%9A%E6%B5%8B%E8%AF%95%E4%B8%8E%E9%AA%8C%E8%AF%81"><span class="toc-number">1.4.7.</span> <span class="toc-text">第六部分：测试与验证</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#6-1-%E6%B8%85%E7%90%86%E5%92%8C%E9%87%8D%E6%96%B0%E7%94%9F%E6%88%90"><span class="toc-number">1.4.7.1.</span> <span class="toc-text">6.1 清理和重新生成</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#6-2-%E6%B5%8F%E8%A7%88%E5%99%A8%E7%BC%93%E5%AD%98%E6%B8%85%E7%90%86"><span class="toc-number">1.4.7.2.</span> <span class="toc-text">6.2 浏览器缓存清理</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#6-3-%E6%80%A7%E8%83%BD%E6%B5%8B%E8%AF%95"><span class="toc-number">1.4.7.2.1.</span> <span class="toc-text">6.3 性能测试</span></a></li></ol></li><li class="toc-item toc-level-5"><a class="toc-link" href="#6-4-%E5%8A%9F%E8%83%BD%E9%AA%8C%E8%AF%81"><span class="toc-number">1.4.7.3.</span> <span class="toc-text">6.4 功能验证</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-%E4%BD%BF%E7%94%A8Lighthouse%E8%BF%9B%E8%A1%8C%E7%A7%91%E5%AD%A6%E7%9A%84%E6%80%A7%E8%83%BD%E8%AF%84%E6%B5%8B"><span class="toc-number">1.5.</span> <span class="toc-text">5.使用Lighthouse进行科学的性能评测</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A%E4%BB%8E%E2%80%9C%E6%84%9F%E8%A7%89%E5%BF%AB%E2%80%9D%E5%88%B0%E2%80%9C%E6%95%B0%E6%8D%AE%E5%BF%AB%E2%80%9D%E2%80%94%E2%80%94%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81%E7%BA%BF%E4%B8%8A%E8%AF%84%E6%B5%8B%EF%BC%9F"><span class="toc-number">1.5.1.</span> <span class="toc-text">前言：从“感觉快”到“数据快”——为何需要线上评测？</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E4%B8%B4%E6%97%B6%E9%83%A8%E7%BD%B2%E5%88%B0-GitHub-Pages-%E8%8E%B7%E5%8F%96%E6%B5%8B%E8%AF%95%E7%BD%91%E5%9D%80"><span class="toc-number">1.5.1.0.1.</span> <span class="toc-text">第一步：临时部署到 GitHub Pages 获取测试网址</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E4%BD%BF%E7%94%A8Lighthouse%E8%BF%9B%E8%A1%8C%E6%80%A7%E8%83%BD%E8%AF%84%E6%B5%8B"><span class="toc-number">1.5.1.0.2.</span> <span class="toc-text">第二步：使用Lighthouse进行性能评测</span></a></li></ol></li></ol></li></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror='this.onerror=null,this.src="https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg"' alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror='this.onerror=null,this.src="https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg"' alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror='this.onerror=null,this.src="https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg"' alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror='this.onerror=null,this.src="https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg"' alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror='this.onerror=null,this.src="https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg"' alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">&copy;2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></main></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>