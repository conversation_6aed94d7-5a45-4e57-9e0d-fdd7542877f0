extends includes/layout.pug

block content
    main.layout#content-inner(class=page.aside ? '' : 'hide-aside')
        #page
            case page.type
                when 'categories'
                    include includes/page/categories
                when 'tags'
                    include includes/page/tags
                when 'links'
                    include includes/page/links
                when 'about'
                    include includes/page/about
                when 'brevity'
                    include includes/page/brevity
                when 'kit'
                    include includes/page/kit
                when 'music'
                    include includes/page/music
                when 'message'
                    !=partial('includes/page/message', {}, {cache: true})
                when 'recentcomment'
                    !=partial('includes/page/recentcomment', {}, {cache: true})
                when 'banner'
                    include includes/widgets/page/banner
                    if page.container
                        include includes/page/default
                    else
                        != page.content
                default
                    include includes/page/default
            if page.comment
                - var comment_js = true
                include includes/widgets/third-party/comments/comment
        if page.aside
            include includes/widgets/aside/aside
