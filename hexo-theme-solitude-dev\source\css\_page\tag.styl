#tag
  +maxWidth768()
    padding 0 1rem

  #tag-page-tags
    display flex
    flex-direction row
    flex-wrap wrap
    width 100%
    position absolute
    z-index 1
    left 0
    top 0
    max-height 44px
    overflow: hidden
    background var(--efu-card-bg)
    backdrop-filter saturate(180%) blur(20px)
    transition .1s ease-out
    border-radius 12px
    border var(--style-border-always)
    +maxWidth768()
      display none

    &:hover
      max-height 1000px
      transition .3s ease-out
      background var(--efu-maskbgdeep)

    a
      padding .1rem .5rem
      margin .25rem .25rem
      line-height 1.6
      border-radius 8px
      color var(--efu-fontcolor) !important
      border var(--style-border-always)
      display flex
      align-items center
      background var(--efu-card-bg)
      transition all .3s ease-out 0s

      &.select
        order -1
        color var(--efu-card-bg) !important
        background var(--efu-theme)
        box-shadow var(--efu-shadow-theme)

      &:hover
        color var(--efu-white) !important
        background var(--efu-theme)
        box-shadow var(--efu-shadow-theme)

      &.select .tagsPageCount
        background var(--efu-card-bg)
        color var(--efu-lighttext)