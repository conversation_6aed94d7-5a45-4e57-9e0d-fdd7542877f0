---
title: 产品经理入门（六）：第六章：产品需求文档（PRD）撰写
categories:
  - 产品经理
tags:
  - 产品经理教程
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp'
comments: true
toc: true
ai: true
abbrlink: 8024
date: 2025-07-20 21:13:45
---

# 第六章：产品需求文档（PRD）撰写



![image-20250720133044016](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133044016.png)

到目前为止，我们已经走过了漫长的旅程：从收集需求、分析需求，到梳理设计思路、绘制可交互的原型。现在，我们手上已经有了一套清晰的解决方案。

一个很自然的问题是：**我可以直接把我的原型，丢给开发和设计师，让他们照着做出来就行了吗？**

我的答案是：**绝对不行**。

因为原型，尤其是低保真原型，只能展示“**它看起来长什么样**”，却无法说清楚“**它在各种情况下应该如何工作**”。为了弥补这个信息鸿沟，确保我们的想法能被100%精准地实现，我们就需要产出产品开发流程中，最核心、最正式的一份交付文档

——**产品需求说明文档（Product Requirements Document, PRD）**。

## 6.1 产品需求说明（PRD）概述

在这一节，我将带大家全面地认识PRD——它是什么？为什么如此重要？以及一份专业的PRD，应该包含哪些内容？

### 6.1.1 学习目标

我的目标是，让我们深刻理解PRD在产品研发流程中不可或缺的“契约”作用。学完本节，我希望我们都能明确一份合格PRD的定义、目的、常见形式和核心构成要素。

### 6.1.2 产品需求说明的定义

![image-20250720133145066](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133145066.png)

我给PRD的定义是：**一份针对即将开发的产品功能或方案，进行全面、详细、无歧义的必要说明，以确保方案被完整、准确地实现的正式文档。**

它的本质，就是用文档的形式，**把产品方案的每一个细节都解释得清清楚楚**。

![image-20250720133240188](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133240188.png)

我们看这个“注册登录”的案例。这个页面看起来很简单，但背后隐藏了无数的逻辑规则：密码的格式要求是什么？输入错误时如何提示？手机号已被注册怎么办？连续输错5次密码会怎样？……这些细节，单靠一张原型图是绝对无法表达的，必须通过PRD来详细说明。

### 6.1.3 产品需求说明的目的

我之所以不厌其烦地撰写PRD，是因为它能为我和我的团队，带来三大不可替代的价值：

1.  **确保方案的完整性**：撰写的过程，本身就是我自己对产品方案进行“极限测试”的过程，它会逼迫我去思考所有可能的异常流程和边界情况，确保方案没有漏洞。
2.  **确保团队理解一致**：PRD是研发团队（包括设计、开发、测试）开展工作的“唯一”和“必须”的依据。它能消除信息偏差，避免因为口头沟通带来的误解和返工。
3.  **方便未来回溯和迭代**：当产品上线后，PRD就是一份“历史档案”。未来的产品经理或团队成员，可以通过它，准确地了解当时我们为什么要做这个功能，以及当时的设计思路。

### 6.1.4 产品需求文档常见形式

![image-20250720133353206](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133353206.png)

PRD并没有一个全球统一的格式，在我的工作中，最常见的两种形式是：

* **Word文档格式**：这是最传统、最正式的形式。通过Word或类似的文档工具，撰写一份包含详细目录、图文并茂的说明书。优点是结构清晰、非常全面。
* **原型内嵌格式**：这是一种更敏捷的形式。我直接在原型工具（如Axure、墨刀）中，为每个页面和元件添加详细的文字标注和说明。优点是原型和文档合二为一，查看和理解更直观。

### 6.1.5 产品需求文档维护形式

![image-20250720133417399](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133417399.png)

在团队协作中，PRD的维护和共享方式也很重要。

* **本地化形式**：就是通过邮件、微信等方式，来回发送Word或原型文件。这种方式在小团队或不规范的团队里很常见，但极易造成版本混乱。
* **第三方工具**：这是我极力推荐的现代协作方式。我们将PRD统一维护在一个在线的、团队共享的平台上，比如**Confluence、Tapd、语雀**，甚至是**墨刀**的项目空间里。所有人访问的都是同一个、最新的版本，沟通和反馈都在线上进行，效率极高。

### 6.1.6 产品需求文档常见内容

![image-20250720133446836](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133446836.png)

无论形式如何，一份合格的PRD，在我看来，都必须包含以下六大核心模块：

1.  **文档更新记录**：记录每次文档的修改时间、修改人、修改内容。
2.  **需求背景与目的**：清晰地告诉团队，我们“为什么”要做这次的需求。
3.  **产品流程图**：附上我们在第四章学过的业务流程图、功能流程图等，帮助团队理解用户路径和功能逻辑。
4.  **产品结构图**：附上功能结构图或产品结构图，帮助团队理解产品的功能构成和信息框架。
5.  **产品交互需求说明**：**这是PRD最最核心的部分**。它需要结合原型图，对每一个页面的每一个元素的每一个状态和交互规则，进行详细的说明。
6.  **非功能性说明**：对一些非界面功能的需求进行说明，比如性能要求（页面加载速度）、兼容性要求、数据统计需求等。

### 6.1.7 产品需求说明小结

我将PRD的核心要点总结如下：

| **核心概念** | **我的理解与实践** |
| :--- | :--- |
| **PRD的定义** | 是连接“想法”与“实现”的**唯一、完整、准确**的说明书。 |
| **PRD的目的** | **确保方案完整、团队理解一致、方便未来回溯**。 |
| **PRD的内容** | 必须包含**背景目的、流程结构、交互说明**等六大核心模块。 |


---

## 6.2 产品交互需求说明详解

在我看来，这部分是PRD的“灵魂”。它详细定义了产品的行为和逻辑，是开发和测试工程师工作的直接依据。

我的目标是写出一份“让开发人员无需再问我任何问题”的说明。

### 6.2.1 学习目标

在本节中，我的目标是，让我们掌握撰写一份清晰、无歧义的交互需求说明的方法。

我们将学习说明的两个核心内容方向，并通过一系列真实案例，学会如何为我们原型中最高频出现的8种元件或场景，定义滴水不漏的规则。

### 6.2.2 产品交互需求说明内容方向

![image-20250720133948254](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720133948254.png)

我撰写的所有交互说明，都围绕着两大方向展开，这能确保我不会遗漏关键信息：

1.  **交互动作说明 (Interaction Actions)**
    这部分是描述“因果关系”，我习惯用“**When(当…)/If(如果…)/Then(那么…)**”的逻辑来思考：
    * **行为 (When)**：当用户做了什么操作时？（如：单击、滑动）
    * **条件 (If)**：这个操作在什么条件下会触发？（如：用户已登录、输入框有内容）
    * **反馈 (Then)**：系统应该给出什么样的回应？（如：跳转页面、弹出提示）

2.  **字段信息说明 (Field Information)**
    这部分是描述“静态规则”，主要包含两点：
    * **显示**：这个区域默认应该显示什么文案或内容？（如：输入框的提示文字）
    * **规则**：这个字段或内容需要遵循什么规则？（如：最多输入10个字）

### 6.2.3 常见交互功能及需求说明示例

现在，我们就用“交互动作”和“字段信息”这两把“手术刀”，来解剖几个最常见的交互功能。

#### 1. 功能按钮说明

![image-20250720134028596](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134028596.png)

对于一个按钮，我绝不会只说“点击按钮如何”，而是会定义它在不同条件下的状态和反馈。
* **案例：“发表”按钮**
    1.  **条件1**：当输入框内容为空时，按钮置灰，为不可点击状态。
    2.  **条件2**：当输入框有内容时，按钮高亮，为可点击状态。
    3.  **行为**：点击该按钮，**反馈**为：提示“发表成功”1.5秒后自动消失，并跳转到朋友圈首页。

#### 2. 选项选择说明

![image-20250720134051854](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134051854.png)

对于下拉选择、单选/复选框等，我需要定义选项的规则。
* **案例：朋友圈日期选择**
    1.  **选项来源**：选项中的年份和月份，来源于该用户所有发过朋友圈的年份和月份。
    2.  **选项规则**：仅支持单选。
    3.  **默认显示**：默认提示文案为最近发表朋友圈的年限。
    4.  **异常情况**：若用户从未发表过朋友圈，“2022年”选项隐藏。

#### 3. 输入框说明

![image-20250720134215852](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134215852.png)

对于输入框，我需要明确其显示和限制规则。
* **案例：设置名字输入框**
    1.  **默认显示**：提示文案为“请输入20个字以内的名字”。
    2.  **输入规则**：不限制字符类型，但限制数量为1-20个字符（定义：1个中文算2个字符，1个字母/数字算1个字符）。
    3.  **异常处理**：如输入超过20个字符，则无法继续输入。

#### 4. 时间/日期选择说明

![image-20250720134303279](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134303279.png)

对于时间选择器，我需要定义其范围和关联规则。
* **案例：自定义日期选择框**
    1.  **默认显示**：“本年本月01日”到“本年本月当日”。
    2.  **可选范围**：日期选择精确到年月日，不得选择超过当天的日期。
    3.  **关联规则**：开始日期不得超过结束日期；开始和结束日期跨度最长为半年。

#### 5. 内容显示说明

![image-20250720134346210](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134346210.png)

对于列表中的内容，我需要定义其截取和显示规则。
* **案例：快报内容显示**
    1.  **显示内容**：包含时间（时分）和内容简讯。
    2.  **截断规则**：内容简讯取自正文开头部分内容，在列表中最多显示三行，超出三行的部分，末尾显示“...”。

#### 6. 状态类说明

![image-20250720134417496](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134417496.png)

对于有“状态”概念的业务（如订单、任务），我必须定义清楚状态的流转。
* **案例：订单状态**
    1.  **状态定义**：一个订单包含“待付款、待发货、待收货、交易成功”等状态。
    2.  **状态变更逻辑**：
        * 用户付款后，状态由“待付款”变为“待发货”。
        * 用户点击确认收货，或超过最长收货时间（如7天）后，状态自动变为“交易成功”。
    3.  **状态影响**：只有在“交易成功”状态下，用户才能删除订单或申请售后。

#### 7. 数字显示类说明

![image-20250720134444217](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134444217.png)

对于需要计数的数字，我需要定义计算和显示规则。
* **案例：阅读/点赞数显示**
    1.  **计算规则**：用户每产生一次阅读/点赞/评论/分享行为，对应数据实时+1。
    2.  **显示规则**：
        * 当数据为0时，只显示icon，不显示数字。
        * 当数据在1000及以内，显示实际数字。
        * 当数据在1000以上，以千为单位显示，如1k+、10k+。

#### 8. 时间显示类说明

![image-20250720134513602](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134513602.png)

对于时间戳的显示，为了提升用户体验，我需要定义其相对显示规则。
* **案例：信息发布时间显示**
    1.  **显示规则**：
        * 1小时内，以分钟为单位显示，如“xx分钟前”。
        * 当天内，以小时为单位显示，如“xx小时前”。
        * 昨天/前天内，显示“昨天/前天+具体时分”，如“昨天 15:05”。
        * 超过3天，则显示为具体年月日+时分，如“09-09 15:05”。

### 6.2.4 产品交互需求说明小结

撰写交互说明，是一项极其考验产品经理**严谨性**和**同理心**的工作。

| **核心原则** | **我的实践清单** |
| :--- | :--- |
| **杜绝歧义** | 我会穷尽每一个元件、每一个状态、每一个用户操作、每一个异常情况，并为它们都写下清晰的规则。 |
| **结构清晰** | 我总是围绕 **交互动作 (When/If/Then)** 和 **字段信息 (Display/Rules)** 这两大方向来组织我的说明。 |
| **换位思考** | 我会站在开发者的角度思考：我的说明是否清晰到他不需要再来问我任何问题就能直接开始写代码？ |






---

## 6.3 如何撰写产品交互需求说明

我们已经知道了交互说明要包含哪些内容，但从“知道”到“做到”之间，还需要一套行之有效的方法论。在这一节，我将毫无保留地，把我自己撰写交互说明的思路、步骤和技巧分享给大家。

### 6.3.1 学习目标

我的目标是，让我们掌握一套可以被反复使用的、结构化的PRD撰写流程。学完本节，我希望我们都能自信地、有条不紊地，为任何一个复杂的页面，撰写出清晰、完整的交互说明。

### 6.3.2 产品交互需求说明撰写思路

![image-20250720134943468](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720134943468.png)

我的核心撰写思路，是**“由外到内，由静到动”**。

1.  **先考虑页面**：在描述任何一个按钮之前，我总是先从整个“页面”的视角出发。这个页面从哪里来？它整体需要遵循什么规则（比如列表的排序规则、分页逻辑等）？
2.  **再考虑控件**：把页面的整体规则定义清楚后，我再“钻”进去，分析页面里的每一个“控件”（也就是元件）。对于每个控件，我同样遵循一个顺序：
    * **静态**：先说明它的静态规则，比如默认的提示文案、输入框的字数限制等。
    * **动态**：再说明它的动态交互，比如正常的交互动作，以及各种异常情况下的交互动作。

### 6.3.3 产品交互需求说明撰写步骤

![image-20250720135827503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720135827503.png)

这个思路，具体落地就变成了我写PRD时雷打不动的三个步骤：

1.  **页面说明**：在文档的开头，我会先对当前页面进行一个整体的介绍，包括页面的主要功能、从哪些页面可以进入此页面、以及该页面的一些全局性规则。
2.  **区域划分**：为了让说明更有条理，我会把一个复杂的页面，划分成几个逻辑区域，比如“顶部导航区”、“内容列表区”、“底部菜单区”。我通常会在原型图上用数字角标，清晰地标注出这些区域。
3.  **详细说明**：这是工作量最大的部分。我会按照区域划分的顺序，逐一地、详细地说明每个区域内，每一个控件的“静态规则”和“动态交互”。

![image-20250720140210704](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720140210704.png)
这种“**在原型图上分区编号，再在文档中分点说明**”的方式，是我认为最清晰、最高效的撰写范式，开发和设计同事都非常喜欢，因为它简单明了，对应关系一目了然。

### 6.3.4 产品交互需求说明撰写案例及练习

![image-20250720140554438](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720140554438.png)





我们来看“注册登录页面”的这份详细说明。它完美地应用了我们刚才讲的三个步骤：

![image-20250720140647069](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720140647069.png)

* 它首先有**页面说明**。
* 然后将页面划分为“**1. 顶部导航栏**”和“**2. 登录区域**”两大块。
* 接着，在“登录区域”内，又详细地拆分说明了“**2.1 手机号输入框**”、“**2.2 验证码输入框**”等每一个控件的详细规则，内容细致到了“光标默认置于输入框”、“键盘用哪种样式”等程度。

这种对细节的极致追求，就是一份专业PRD的体现。这也是我们接下来需要练习达到的标准。

### 6.3.5 产品交互需求全局说明

![image-20250720141030178](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720141030178.png)

这时，一个问题来了：**有一些规则，比如“网络异常时如何提示”，是每个页面都会出现的，难道我需要在每个页面都重复写一遍吗？**

答案是不需要。为了解决这个问题，我会在PRD的开头，建立一个**“全局说明”**的章节。

在这个章节里，我会把所有非某个页面独有的、全产品通用的规则，进行统一的说明。这通常包括：
* **角色/权限说明**：不同角色的用户，在使用功能上有什么权限差异。

* **加载方式**：页面加载、数据加载时的默认动画样式。

* **全局弹层**：产品中统一的弹窗、提示（Toast）的样式和规则。
* **常用字段**：常用字段的统一规则，如昵称、密码的格式要求。
* **网络异常**：在各种网络问题下，产品应该如何向用户反馈。
* **全局交互**：通用的手势操作等。

建立“全局说明”，能极大地减少我的重复工作量，并保证产品体验的一致性。

### 6.3.6 产品交互需求说明撰写注意事项与技巧

![image-20250720143359987](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720143359987.png)

最后，分享四个我多年总结下来的技巧和注意事项：

1.  **先明确产品逻辑**：在写文档前，一定先把产品的流程、结构都想清楚。PRD是思考的结果，而不是思考的过程。
2.  **条件和反馈**：对每一个交互动作，都要像侦探一样，把所有的“条件”和“反馈”都考虑到，特别是异常情况。
3.  **不要边画原型边写说明**：我建议把这两个工作分开。先专注地把低保真原型画完，再进入“贤者时间”，专注地为这个静态的原型撰写说明。一心二用，两边都做不好。
4.  **灵活运用表达方式**：除了大段的文字，我也会大量使用**表格**（比如用来表达状态机）、**流程图**等方式，来更清晰、更简洁地表达复杂的逻辑。

---