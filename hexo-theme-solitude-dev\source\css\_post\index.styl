if hexo-config('comment.commentBarrage')
  @import 'commentBarrage'

@import "copyright"

@import "meta"

@import "relatedPost"

@import "tools"

@import "pagination"

if hexo-config('google_adsense.enable')
  @import "ads.styl"

if hexo-config('post.ai.enable')
  .post-ai
    background var(--efu-secondbg)
    border-radius 12px
    padding 12px
    line-height 1.3
    border var(--style-border-always)
    margin-top 16px
    min-height 101.22px
    box-shadow var(--efu-shadow-border)

    +maxWidth768()
      margin-top 0

    .ai-title
      display flex
      color var(--efu-lighttext)
      border-radius 8px
      align-items center
      user-select none

      .ai-title-left
        display flex
        align-items center
        color var(--efu-lighttext)

        i.ai-title-icon
          width 24px
          height 24px
          display flex
          background var(--efu-lighttext)
          color var(--efu-card-bg)
          font-size 14px
          border-radius 20px
          justify-content center
          align-items center

        .ai-title-text
          font-weight 700
          margin-left 8px
          line-height 1
          font-size 14px

      .ai-tag
        font-size 12px
        background-color var(--efu-lighttext)
        box-shadow var(--efu-shadow-main)
        color var(--efu-card-bg)
        font-weight 700
        border-radius 12px
        margin-left auto
        line-height 12px
        padding 6px 8px
        display flex
        align-items center
        justify-content center
        transition .3s

        &.loadingAI
          animation-duration 2s
          animation-name AILoading
          animation-iteration-count infinite
          animation-direction alternate

    .ai-explanation
      margin-top 12px
      overflow hidden
      padding 8px 12px
      background var(--efu-card-bg)
      border-radius 8px
      border var(--style-border-always)
      font-size 15px
      line-height 1.4
      display none
      text-align left

      .blinking-cursor
        background-color var(--efu-lighttext)
        width 14px
        height 14px
        border-radius 16px
        display inline-block
        vertical-align middle
        animation blinking-cursor 2s infinite
        margin-left 4px
        margin-bottom 3px
        transform scale(.6)

  .char
    display inline-block
    opacity 0
    animation chat-float .5s ease forwards;

  @keyframes chat-float
    0% 
      opacity 0
      transform translateY(20px)
    100% 
        opacity 1
        transform translateY(0)
    
#post
  border var(--style-border)
  border-radius var(--radius)
  box-shadow var(--efu-shadow-border)
  background var(--efu-card-bg)