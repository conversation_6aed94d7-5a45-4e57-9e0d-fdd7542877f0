<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Python（十九）：第十八章 单元测试 | Prorise的小站</title><meta name="keywords" content="Python基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Python（十九）：第十八章 单元测试"><meta name="application-name" content="Python（十九）：第十八章 单元测试"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="Python（十九）：第十八章 单元测试"><meta property="og:url" content="https://prorise666.site/posts/34823.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第十八章 单元测试软件开发的核心目标之一是交付高质量、运行稳定的代码。单元测试 (Unit Testing) 是保障这一目标的重要手段，它专注于验证软件中最小可测试单元（通常是函数、方法或类）的行为是否符合预期。 在本章中，我们将深入学习 Python 中广受欢迎的测试框架 —— pytest。 1"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta name="description" content="第十八章 单元测试软件开发的核心目标之一是交付高质量、运行稳定的代码。单元测试 (Unit Testing) 是保障这一目标的重要手段，它专注于验证软件中最小可测试单元（通常是函数、方法或类）的行为是否符合预期。 在本章中，我们将深入学习 Python 中广受欢迎的测试框架 —— pytest。 1"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/34823.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"Python（十九）：第十八章 单元测试",postAI:"true",pageFillDescription:"第十八章 单元测试, 18.1 单元测试简介：, 18.2 Pytest 简介与核心优势, 18.3 Pytest 核心特性概览, 18.4 Pytest 基础实践, 18.4.1 编写第一个 Pytest 测试：测试加法函数, 18.4.2 测试异常：pytest.raises, 18.5 Pytest Fixtures：强大的依赖注入与测试准备, 18.5.1 Fixture 基本概念与应用：@pytest.fixture, 18.5.2 Fixture 的作用域 (Scope), 18.5.3 使用 yield 实现 Fixture 的 Teardown (清理操作)第十八章单元测试软件开发的核心目标之一是交付高质量运行稳定的代码单元测试是保障这一目标的重要手段它专注于验证软件中最小可测试单元通常是函数方法或类的行为是否符合预期在本章中我们将深入学习中广受欢迎的测试框架单元测试简介单元测试通过在开发早期发现并修复问题从而提升代码质量增强代码重构的信心并作为一种活文档辅助理解代码功能基本流程隔离单元确定要测试的函数方法或类定义预期明确该单元在特定输入下应有的输出或行为编写测试使用测试框架编写代码来验证这些预期执行测试运行测试并检查结果迭代优化根据测试结果修改代码或测试本身简介与核心优势是一个成熟且功能齐全的测试框架它使得编写小型易读的测试变得简单并且可以扩展以支持复杂的函数式接口或系统级测试为什么选择极简样板代码相比需要的模板代码更少测试函数就是普通的函数不需要继承任何类强大的语句直接使用标准的语句进行断言会提供详细的断言失败信息灵活的的系统非常强大用于管理测试依赖和测试上下文的准备与清理比传统的更灵活丰富的插件生态拥有大量高质量的第三方插件如覆盖率并行测试等良好的兼容性可以运行基于和编写的测试用例清晰的测试报告默认提供易读的测试报告安装您可以使用来安装核心特性概览的强大功能主要体现在以下几个核心特性上本笔记将逐一介绍特性概念简介涉及的主要元素用法测试发现自动查找符合特定命名约定的测试文件和函数文件名或函数方法名基本测试函数普通函数即可作为测试用例无需继承特定类断言使用内置的语句进行结果验证提供详细的错误报告异常测试优雅地测试代码是否按预期抛出异常上下文管理器测试固件管理测试函数的依赖状态和资源实现代码复用和模块化装饰器用于参数化测试使用不同的参数多次运行同一个测试函数避免代码重复装饰器标记为测试函数添加元数据用于分类跳过标记预期失败等如运行测试通过命令行工具运行测试并提供多种选项命令及其参数如基础实践编写第一个测试测试加法函数会自动发现当前目录及其子目录下所有命名为或的文件中的开头的函数被测代码计算两个数的和测试代码导入需要测试的函数测试基本的加法功能直接使用进行断言应该等于可选的错误信息这里一定会报错因为测试负数相加代码注释与讲解测试文件命名为遵循的发现约定测试函数和以开头直接使用语句如果后的表达式为会将该测试标记为失败并提供详细的上下文信息最后的部分是可选的如果断言失败这个消息不会像的参数那样直接显示会通过其内省机制提供更丰富的失败信息运行测试会自动找到并执行和测试异常当需要验证代码是否按预期抛出特定异常时可以使用被测代码包含函数计算一个数的平方输入必须是数字测试代码需要导入来使用等特性测试正数的平方测试负数的平方测试零的平方测试当输入无效时函数是否抛出是一个上下文管理器用于断言在块内执行的代码会抛出指定类型的异常如果代码块中没有抛出测试将失败如果代码块中抛出了其他类型的异常测试也将失败呵呵呵呵呵这行代码应该抛出这个测试能通过是因为函数在接收到字符串参数时会抛出当使用上下文管理器时如果代码块内抛出了会捕获这个异常并使测试通过这正是我们期望的行为测试异常的错误信息是否符合预期可选的参数允许提供一个正则表达式用于验证抛出的异常实例的错误消息这对于确保错误信息对用户友好且准确非常有用如果抛出的异常消息与提供的正则表达式不匹配测试将失败输入必须是数字参数使用正则表达式匹配错误信息强大的依赖注入与测试准备测试固件是中一个非常核心且强大的特性它们用于为测试函数类模块或整个会话设置必要的预置条件如数据对象实例服务连接等并在测试结束后进行清理基本概念与应用通过装饰器可以将一个函数标记为测试函数可以通过将其名称作为参数来请求使用这个示例一个提供简单列表数据的返回的值会注入到测试函数中一个提供简单字典数据的将名称作为参数测试的长度测试的内容一个测试可以使用多个测试的值并同时使用确认也被正确注入代码注释与讲解装饰器将和函数转换为当测试函数如在其参数列表中包含名称时会在执行该测试函数之前先执行对应的函数并将其返回值注入到测试函数的同名参数中复用性同一个可以被多个测试函数使用避免了重复的设置代码声明式依赖测试函数清晰地声明了它所依赖的上下文或数据的作用域可以定义不同的作用域以控制其执行的频率和生命周期作用域通过的参数指定作为单元测试来说没有必要区分的这么死板平常来说使用默认值即可若有严格需求再详细区分作用域默认每个测试函数执行一次是开销最小隔离性最好的作用域每个测试类执行一次用于类中所有测试方法共享的创建开销较大的资源每个模块执行一次每个包执行一次且整个测试会话即一次命令的完整执行过程执行一次适用于全局的创建非常昂贵的资源示例函数级别每个测试函数都会重新创建一次设置函数级别的每个测试函数都会重新创建函数值清理函数级别的类级别每个测试类只创建一次类中所有测试方法共享设置类级别的每个测试类只创建一次类值清理类级别的模块级别整个测试模块文件只创建一次所有测试共享设置模块级别的整个测试文件只创建一次模块值清理模块级别的会话级别整个测试会话只创建一次所有测试模块共享设置会话级别的整个测试会话只创建一次会话值清理会话级别的代码注释与讲解观察运行用于显示输出时的输出可以清晰地看到不同作用域的时机使用实现的清理操作如果需要在测试使用完毕后执行清理操作类似于中的可以使用语句之前的代码是部分之后的代码是部分示例使用用于创建更安全的临时文件目录每个测试函数都会得到一个新的临时文件创建一个包含内容的临时文件测试结束后该临时文件会被自动清理创建临时文件并写入内容创建一个有名字的临时文件确保在块结束后文件不会立即删除这样测试函数才能访问它我们需要手动删除或者使用见会更简单使用可以获得文件名和文件描述符更可控设置创建临时文件使用文件描述符打开将文件路径和预期的内容传递给测试函数删除临时文件清理删除临时文件测试从创建的临时文件中读取内容解包返回的值测试运行中访问测试临时文件在测试执行期间确实存在测试运行中检查代码注释与讲解语句是和的分界点它将和提供给测试函数之后的代码在使用该的测试函数执行完毕后无论成功或失败执行",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-13 22:13:01",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E5%85%AB%E7%AB%A0-%E5%8D%95%E5%85%83%E6%B5%8B%E8%AF%95"><span class="toc-text">第十八章 单元测试</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#18-1-%E5%8D%95%E5%85%83%E6%B5%8B%E8%AF%95%E7%AE%80%E4%BB%8B%EF%BC%9A"><span class="toc-text">18.1 单元测试简介：</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#18-2-Pytest-%E7%AE%80%E4%BB%8B%E4%B8%8E%E6%A0%B8%E5%BF%83%E4%BC%98%E5%8A%BF"><span class="toc-text">18.2 Pytest 简介与核心优势</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#18-3-Pytest-%E6%A0%B8%E5%BF%83%E7%89%B9%E6%80%A7%E6%A6%82%E8%A7%88"><span class="toc-text">18.3 Pytest 核心特性概览</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#18-4-Pytest-%E5%9F%BA%E7%A1%80%E5%AE%9E%E8%B7%B5"><span class="toc-text">18.4 Pytest 基础实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#18-4-1-%E7%BC%96%E5%86%99%E7%AC%AC%E4%B8%80%E4%B8%AA-Pytest-%E6%B5%8B%E8%AF%95%EF%BC%9A%E6%B5%8B%E8%AF%95%E5%8A%A0%E6%B3%95%E5%87%BD%E6%95%B0"><span class="toc-text">18.4.1 编写第一个 Pytest 测试：测试加法函数</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#18-4-2-%E6%B5%8B%E8%AF%95%E5%BC%82%E5%B8%B8%EF%BC%9Apytest-raises"><span class="toc-text">18.4.2 测试异常：pytest.raises</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#18-5-Pytest-Fixtures%EF%BC%9A%E5%BC%BA%E5%A4%A7%E7%9A%84%E4%BE%9D%E8%B5%96%E6%B3%A8%E5%85%A5%E4%B8%8E%E6%B5%8B%E8%AF%95%E5%87%86%E5%A4%87"><span class="toc-text">18.5 Pytest Fixtures：强大的依赖注入与测试准备</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#18-5-1-Fixture-%E5%9F%BA%E6%9C%AC%E6%A6%82%E5%BF%B5%E4%B8%8E%E5%BA%94%E7%94%A8%EF%BC%9A-pytest-fixture"><span class="toc-text">18.5.1 Fixture 基本概念与应用：@pytest.fixture</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#18-5-2-Fixture-%E7%9A%84%E4%BD%9C%E7%94%A8%E5%9F%9F-Scope"><span class="toc-text">18.5.2 Fixture 的作用域 (Scope)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#18-5-3-%E4%BD%BF%E7%94%A8-yield-%E5%AE%9E%E7%8E%B0-Fixture-%E7%9A%84-Teardown-%E6%B8%85%E7%90%86%E6%93%8D%E4%BD%9C"><span class="toc-text">18.5.3 使用 yield 实现 Fixture 的 Teardown (清理操作)</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Python基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Python（十九）：第十八章 单元测试</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-19T02:13:45.000Z" title="发表于 2025-04-19 10:13:45">2025-04-19</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.532Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.3k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>12分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Python（十九）：第十八章 单元测试"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/34823.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/34823.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Python基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Python（十九）：第十八章 单元测试</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-04-19T02:13:45.000Z" title="发表于 2025-04-19 10:13:45">2025-04-19</time><time itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.532Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></header><div id="postchat_postcontent"><h2 id="第十八章-单元测试"><a href="#第十八章-单元测试" class="headerlink" title="第十八章 单元测试"></a>第十八章 单元测试</h2><p>软件开发的核心目标之一是交付高质量、运行稳定的代码。<strong>单元测试 (Unit Testing)</strong> 是保障这一目标的重要手段，它专注于验证软件中最小可测试单元（通常是函数、方法或类）的行为是否符合预期。</p><p>在本章中，我们将深入学习 Python 中广受欢迎的测试框架 —— <code>pytest</code>。</p><h3 id="18-1-单元测试简介："><a href="#18-1-单元测试简介：" class="headerlink" title="18.1 单元测试简介："></a>18.1 单元测试简介：</h3><p>单元测试通过在开发早期发现并修复问题，从而提升代码质量，增强代码重构的信心，并作为一种“活文档”辅助理解代码功能。</p><p><strong>基本流程：</strong></p><ol><li><strong>隔离单元</strong>：确定要测试的函数、方法或类。</li><li><strong>定义预期</strong>：明确该单元在特定输入下应有的输出或行为。</li><li><strong>编写测试</strong>：使用测试框架编写代码来验证这些预期。</li><li><strong>执行测试</strong>：运行测试并检查结果。</li><li><strong>迭代优化</strong>：根据测试结果修改代码或测试本身。</li></ol><h3 id="18-2-Pytest-简介与核心优势"><a href="#18-2-Pytest-简介与核心优势" class="headerlink" title="18.2 Pytest 简介与核心优势"></a>18.2 Pytest 简介与核心优势</h3><p><code>pytest</code> 是一个成熟且功能齐全的 Python 测试框架，它使得编写小型、易读的测试变得简单，并且可以扩展以支持复杂的函数式、接口或系统级测试。</p><p><strong>为什么选择 <code>pytest</code>？</strong></p><ul><li><strong>极简样板代码</strong>：相比 <code>unittest</code>，<code>pytest</code> 需要的模板代码更少。测试函数就是普通的 Python 函数，不需要继承任何类。</li><li><strong>强大的 <code>assert</code> 语句</strong>：直接使用标准的 <code>assert</code> 语句进行断言，<code>pytest</code> 会提供详细的断言失败信息。</li><li><strong>灵活的 Fixtures</strong>：<code>pytest</code> 的 Fixture 系统非常强大，用于管理测试依赖和测试上下文的准备与清理，比传统的 <code>setUp/tearDown</code> 更灵活。</li><li><strong>丰富的插件生态</strong>：拥有大量高质量的第三方插件（如 <code>pytest-django</code>, <code>pytest-cov</code> (覆盖率), <code>pytest-xdist</code> (并行测试) 等）。</li><li><strong>良好的兼容性</strong>：可以运行基于 <code>unittest</code> 和 <code>nose</code> 编写的测试用例。</li><li><strong>清晰的测试报告</strong>：默认提供易读的测试报告。</li></ul><p><strong>安装 <code>pytest</code></strong></p><p>您可以使用 pip 来安装 <code>pytest</code>：</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">pip install pytest</span><br></pre></td></tr></tbody></table></figure><h3 id="18-3-Pytest-核心特性概览"><a href="#18-3-Pytest-核心特性概览" class="headerlink" title="18.3 Pytest 核心特性概览"></a>18.3 Pytest 核心特性概览</h3><p><code>pytest</code> 的强大功能主要体现在以下几个核心特性上，本笔记将逐一介绍：</p><table><thead><tr><th align="left">特性/概念</th><th align="left">简介</th><th align="left">涉及的主要 pytest 元素/用法</th></tr></thead><tbody><tr><td align="left"><strong>测试发现 (Test Discovery)</strong></td><td align="left"><code>pytest</code> 自动查找符合特定命名约定的测试文件和函数。</td><td align="left">文件名 <code>test_*.py</code> 或 <code>*_test.py</code>；函数/方法名 <code>test_*</code>。</td></tr><tr><td align="left"><strong>基本测试函数 (Basic Test Functions)</strong></td><td align="left">普通 Python 函数即可作为测试用例，无需继承特定类。</td><td align="left"><code>def test_example(): ...</code></td></tr><tr><td align="left"><strong>断言 (Assertions)</strong></td><td align="left">使用 Python 内置的 <code>assert</code> 语句进行结果验证，<code>pytest</code> 提供详细的错误报告。</td><td align="left"><code>assert expression</code></td></tr><tr><td align="left"><strong>异常测试 (Exception Testing)</strong></td><td align="left">优雅地测试代码是否按预期抛出异常。</td><td align="left"><code>pytest.raises()</code> 上下文管理器。</td></tr><tr><td align="left"><strong>Fixtures (测试固件)</strong></td><td align="left">管理测试函数的依赖、状态和资源，实现代码复用和模块化。</td><td align="left"><code>@pytest.fixture</code> 装饰器, <code>yield</code> 用于 teardown。</td></tr><tr><td align="left"><strong>参数化测试 (Parametrization)</strong></td><td align="left">使用不同的参数多次运行同一个测试函数，避免代码重复。</td><td align="left"><code>@pytest.mark.parametrize</code> 装饰器。</td></tr><tr><td align="left"><strong>标记 (Markers)</strong></td><td align="left">为测试函数添加元数据，用于分类、跳过、标记预期失败等。</td><td align="left"><code>@pytest.mark.&lt;marker_name&gt;</code> (如 <code>skip</code>, <code>xfail</code>)。</td></tr><tr><td align="left"><strong>运行测试 (Running Tests)</strong></td><td align="left">通过命令行工具 <code>pytest</code> 运行测试，并提供多种选项。</td><td align="left"><code>pytest</code> 命令及其参数 (如 <code>-v</code>, <code>-k</code>, <code>-m</code>)。</td></tr></tbody></table><h3 id="18-4-Pytest-基础实践"><a href="#18-4-Pytest-基础实践" class="headerlink" title="18.4 Pytest 基础实践"></a>18.4 Pytest 基础实践</h3><h4 id="18-4-1-编写第一个-Pytest-测试：测试加法函数"><a href="#18-4-1-编写第一个-Pytest-测试：测试加法函数" class="headerlink" title="18.4.1 编写第一个 Pytest 测试：测试加法函数"></a>18.4.1 编写第一个 Pytest 测试：测试加法函数</h4><p><code>pytest</code> 会自动发现当前目录及其子目录下所有命名为 <code>test_*.py</code> 或 <code>*_test.py</code> 的文件中的 <code>test_*</code> 开头的函数。</p><p><em>被测代码 (<code>my_math.py</code>):</em></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># my_math.py</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">add</span>(<span class="params">x, y</span>):</span><br><span class="line">    <span class="string">"""计算两个数的和"""</span></span><br><span class="line">    <span class="keyword">return</span> x + y</span><br></pre></td></tr></tbody></table></figure><p><em>测试代码 (<code>test_my_math_pytest.py</code>):</em></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># test_my_math_pytest.py</span></span><br><span class="line"><span class="keyword">from</span> my_math <span class="keyword">import</span> add <span class="comment"># 导入需要测试的函数</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_simple_addition</span>():</span><br><span class="line">    <span class="string">"""测试基本的加法功能。"""</span></span><br><span class="line">    <span class="comment"># 直接使用 assert进行断言</span></span><br><span class="line">    <span class="keyword">assert</span> add(<span class="number">1</span>, <span class="number">2</span>) == <span class="number">3</span>, <span class="string">"1 + 2 应该等于 3"</span> <span class="comment"># 可选的错误信息</span></span><br><span class="line">    <span class="keyword">assert</span> add(-<span class="number">1</span>, <span class="number">1</span>) == <span class="number">0</span></span><br><span class="line">    <span class="comment"># assert add(0, 3) == 这里一定会报错 因为 0 # 3 != 0</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_negative_addition</span>():</span><br><span class="line">    <span class="string">"""测试负数相加。"""</span></span><br><span class="line">    <span class="keyword">assert</span> add(-<span class="number">5</span>, -<span class="number">10</span>) == -<span class="number">15</span></span><br><span class="line"></span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><p><strong>代码注释与讲解：</strong></p><ul><li>测试文件命名为 <code>test_my_math_pytest.py</code>，遵循 <code>pytest</code> 的发现约定。</li><li>测试函数 <code>test_simple_addition</code> 和 <code>test_negative_addition</code> 以 <code>test_</code> 开头。</li><li>直接使用 <code>assert</code> 语句。如果 <code>assert</code> 后的表达式为 <code>False</code>，<code>pytest</code> 会将该测试标记为失败，并提供详细的上下文信息。</li><li>最后的 <code>, "message"</code> 部分是可选的，如果断言失败，这个消息不会像 <code>unittest</code> 的 <code>msg</code> 参数那样直接显示，<code>pytest</code> 会通过其内省机制提供更丰富的失败信息。</li></ul><p><strong>运行测试：</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250510175654130.png" alt="image-20250510175654130"></p><p><code>pytest</code> 会自动找到并执行 <code>test_simple_addition</code> 和 <code>test_negative_addition</code>。</p><h4 id="18-4-2-测试异常：pytest-raises"><a href="#18-4-2-测试异常：pytest-raises" class="headerlink" title="18.4.2 测试异常：pytest.raises"></a>18.4.2 测试异常：<code>pytest.raises</code></h4><p>当需要验证代码是否按预期抛出特定异常时，可以使用 <code>pytest.raises</code>。</p><p><em>被测代码 (<code>more_math.py</code>，包含 <code>square</code> 函数):</em></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># more_math.py</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">square</span>(<span class="params">n</span>):</span><br><span class="line">    <span class="string">"""计算一个数的平方。"""</span></span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> <span class="built_in">isinstance</span>(n, (<span class="built_in">int</span>, <span class="built_in">float</span>)):</span><br><span class="line">        <span class="keyword">raise</span> TypeError(<span class="string">"输入必须是数字"</span>)</span><br><span class="line">    <span class="keyword">return</span> n * n</span><br></pre></td></tr></tbody></table></figure><p><em>测试代码 (<code>test_more_math_pytest.py</code>):</em></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># test_more_math_pytest.py</span></span><br><span class="line"><span class="keyword">import</span> pytest  <span class="comment"># 需要导入 pytest 来使用 pytest.raises 等特性</span></span><br><span class="line"><span class="keyword">from</span> more_math <span class="keyword">import</span> square</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_square_positive_numbers</span>():</span><br><span class="line">    <span class="string">"""测试正数的平方。"""</span></span><br><span class="line">    <span class="keyword">assert</span> square(<span class="number">2</span>) == <span class="number">4</span></span><br><span class="line">    <span class="keyword">assert</span> square(<span class="number">3.0</span>) == <span class="number">9.0</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_square_negative_numbers</span>():</span><br><span class="line">    <span class="string">"""测试负数的平方。"""</span></span><br><span class="line">    <span class="keyword">assert</span> square(-<span class="number">2</span>) == <span class="number">4</span></span><br><span class="line">    <span class="keyword">assert</span> square(-<span class="number">1.5</span>) == <span class="number">2.25</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_square_zero</span>():</span><br><span class="line">    <span class="string">"""测试零的平方。"""</span></span><br><span class="line">    <span class="keyword">assert</span> square(<span class="number">0</span>) == <span class="number">0</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_square_invalid_input_raises_typeerror</span>():</span><br><span class="line">    <span class="string">"""测试当输入无效时，square 函数是否抛出 TypeError。"""</span></span><br><span class="line">    <span class="comment"># pytest.raises 是一个上下文管理器，用于断言在 with 块内执行的代码会抛出指定类型的异常</span></span><br><span class="line">    <span class="comment"># 如果代码块中没有抛出 TypeError，测试将失败</span></span><br><span class="line">    <span class="comment"># 如果代码块中抛出了其他类型的异常，测试也将失败</span></span><br><span class="line">    <span class="keyword">with</span> pytest.raises(TypeError):</span><br><span class="line">        square(<span class="string">'呵呵呵呵呵'</span>)  <span class="comment"># 这行代码应该抛出 TypeError</span></span><br><span class="line">    <span class="comment"># 这个测试能通过是因为 square 函数在接收到字符串参数时会抛出 TypeError</span></span><br><span class="line">    <span class="comment"># 当使用 pytest.raises(TypeError) 上下文管理器时，如果代码块内抛出了 TypeError</span></span><br><span class="line">    <span class="comment"># pytest 会捕获这个异常并使测试通过，这正是我们期望的行为</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_square_invalid_input_message</span>():</span><br><span class="line">    <span class="string">"""测试 TypeError 异常的错误信息是否符合预期 (可选)。"""</span></span><br><span class="line">    <span class="comment"># pytest.raises 的 match 参数允许提供一个正则表达式，用于验证抛出的异常实例的错误消息</span></span><br><span class="line">    <span class="comment"># 这对于确保错误信息对用户友好且准确非常有用</span></span><br><span class="line">    <span class="comment"># 如果抛出的异常消息与提供的正则表达式不匹配，测试将失败</span></span><br><span class="line">    <span class="keyword">with</span> pytest.raises(TypeError, <span class="keyword">match</span>=<span class="string">"输入必须是数字"</span>):  <span class="comment"># match 参数使用正则表达式匹配错误信息</span></span><br><span class="line">        square(<span class="string">'text'</span>)</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h3 id="18-5-Pytest-Fixtures：强大的依赖注入与测试准备"><a href="#18-5-Pytest-Fixtures：强大的依赖注入与测试准备" class="headerlink" title="18.5 Pytest Fixtures：强大的依赖注入与测试准备"></a>18.5 Pytest Fixtures：强大的依赖注入与测试准备</h3><p>Fixtures（测试固件）是 <code>pytest</code> 中一个非常核心且强大的特性。它们用于为测试函数、类、模块或整个会话设置必要的预置条件（如数据、对象实例、服务连接等），并在测试结束后进行清理。</p><h4 id="18-5-1-Fixture-基本概念与应用：-pytest-fixture"><a href="#18-5-1-Fixture-基本概念与应用：-pytest-fixture" class="headerlink" title="18.5.1 Fixture 基本概念与应用：@pytest.fixture"></a>18.5.1 Fixture 基本概念与应用：<code>@pytest.fixture</code></h4><p>通过 <code>@pytest.fixture</code> 装饰器可以将一个函数标记为 fixture。测试函数可以通过将其名称作为参数来请求使用这个 fixture。</p><p><em>示例 (<code>test_fixtures_basic.py</code>):</em></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># test_fixtures_basic.py</span></span><br><span class="line"><span class="keyword">import</span> pytest</span><br><span class="line"></span><br><span class="line"><span class="meta">@pytest.fixture</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">sample_list</span>():</span><br><span class="line">    <span class="string">"""一个提供简单列表数据的 fixture。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [Fixture setup] Creating sample_list..."</span>)</span><br><span class="line">    data = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]</span><br><span class="line">    <span class="keyword">return</span> data <span class="comment"># fixture 返回的值会注入到测试函数中</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="meta">@pytest.fixture</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">sample_dict</span>():</span><br><span class="line">    <span class="string">"""一个提供简单字典数据的 fixture。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [Fixture setup] Creating sample_dict..."</span>)</span><br><span class="line">    <span class="keyword">return</span> {<span class="string">"name"</span>: <span class="string">"pytest"</span>, <span class="string">"type"</span>: <span class="string">"framework"</span>}</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_list_length</span>(<span class="params">sample_list</span>): <span class="comment"># 将 fixture 名称作为参数</span></span><br><span class="line">    <span class="string">"""测试 sample_list 的长度。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n      [Test running] test_list_length using sample_list"</span>)</span><br><span class="line">    <span class="keyword">assert</span> <span class="built_in">len</span>(sample_list) == <span class="number">5</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_list_content</span>(<span class="params">sample_list</span>):</span><br><span class="line">    <span class="string">"""测试 sample_list 的内容。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n      [Test running] test_list_content using sample_list"</span>)</span><br><span class="line">    <span class="keyword">assert</span> <span class="number">3</span> <span class="keyword">in</span> sample_list</span><br><span class="line">    <span class="keyword">assert</span> sample_list[<span class="number">0</span>] == <span class="number">1</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_dict_values</span>(<span class="params">sample_dict, sample_list</span>): <span class="comment"># 一个测试可以使用多个 fixtures</span></span><br><span class="line">    <span class="string">"""测试 sample_dict 的值，并同时使用 sample_list。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n      [Test running] test_dict_values using sample_dict and sample_list"</span>)</span><br><span class="line">    <span class="keyword">assert</span> sample_dict[<span class="string">"name"</span>] == <span class="string">"pytest"</span></span><br><span class="line">    <span class="keyword">assert</span> <span class="built_in">len</span>(sample_list) &gt; <span class="number">0</span> <span class="comment"># 确认 sample_list 也被正确注入</span></span><br></pre></td></tr></tbody></table></figure><p><strong>代码注释与讲解：</strong></p><ul><li><code>@pytest.fixture</code>: 装饰器，将 <code>sample_list</code> 和 <code>sample_dict</code> 函数转换为 fixture。</li><li>当测试函数（如 <code>test_list_length(sample_list)</code>）在其参数列表中包含 fixture 名称时，<code>pytest</code> 会在执行该测试函数之前先执行对应的 fixture 函数，并将其返回值注入到测试函数的同名参数中。</li><li><strong>复用性</strong>：同一个 fixture 可以被多个测试函数使用，避免了重复的设置代码。</li><li><strong>声明式依赖</strong>：测试函数清晰地声明了它所依赖的上下文或数据。</li></ul><h4 id="18-5-2-Fixture-的作用域-Scope"><a href="#18-5-2-Fixture-的作用域-Scope" class="headerlink" title="18.5.2 Fixture 的作用域 (Scope)"></a>18.5.2 Fixture 的作用域 (Scope)</h4><p>Fixture 可以定义不同的作用域，以控制其执行（setup/teardown）的频率和生命周期。作用域通过 <code>@pytest.fixture</code> 的 <code>scope</code> 参数指定</p><p>作为单元测试来说，没有必要区分的这么死板，<strong>平常来说使用默认值即可</strong>，若有严格需求再详细区分作用域</p><ul><li><strong><code>function</code> (默认)</strong>: 每个测试函数执行一次。是开销最小、隔离性最好的作用域。</li><li><strong><code>class</code></strong>: 每个测试类执行一次。用于类中所有测试方法共享的、创建开销较大的资源。</li><li><strong><code>module</code></strong>: 每个模块执行一次。</li><li><strong><code>package</code></strong>: 每个包执行一次 (Python 3.7+, 且 <code>pytest</code> 4.3+)。</li><li><strong><code>session</code></strong>: 整个测试会话（即一次 <code>pytest</code> 命令的完整执行过程）执行一次。适用于全局的、创建非常昂贵的资源。</li></ul><p><em>示例 (<code>test_fixture_scopes.py</code>):</em></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># test_fixture_scopes.py</span></span><br><span class="line"><span class="keyword">import</span> pytest</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 函数级别 - 每个测试函数都会重新创建一次</span></span><br><span class="line"><span class="meta">@pytest.fixture </span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">function_scoped_fixture</span>():</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [设置] 函数级别的fixture - 每个测试函数都会重新创建"</span>)</span><br><span class="line">    <span class="keyword">yield</span> <span class="string">"函数值"</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [清理] 函数级别的fixture"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 类级别 - 每个测试类只创建一次，类中所有测试方法共享</span></span><br><span class="line"><span class="meta">@pytest.fixture(<span class="params">scope=<span class="string">"class"</span></span>)</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">class_scoped_fixture</span>():</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [设置] 类级别的fixture - 每个测试类只创建一次"</span>)</span><br><span class="line">    <span class="keyword">yield</span> <span class="string">"类值"</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [清理] 类级别的fixture"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 模块级别 - 整个测试模块文件只创建一次，所有测试共享</span></span><br><span class="line"><span class="meta">@pytest.fixture(<span class="params">scope=<span class="string">"module"</span></span>)</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">module_scoped_fixture</span>():</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [设置] 模块级别的fixture - 整个测试文件只创建一次"</span>)</span><br><span class="line">    <span class="keyword">yield</span> <span class="string">"模块值"</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [清理] 模块级别的fixture"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 会话级别 - 整个测试会话只创建一次，所有测试模块共享</span></span><br><span class="line"><span class="meta">@pytest.fixture(<span class="params">scope=<span class="string">"session"</span></span>)</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">session_scoped_fixture</span>():</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [设置] 会话级别的fixture - 整个测试会话只创建一次"</span>)</span><br><span class="line">    <span class="keyword">yield</span> <span class="string">"会话值"</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n   [清理] 会话级别的fixture"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><p><strong>代码注释与讲解：</strong></p><ul><li>观察运行 <code>pytest -s -v test_fixture_scopes.py</code> (<code>-s</code> 用于显示 print 输出) 时的输出，可以清晰地看到不同作用域 fixture 的 setup 时机。</li></ul><h4 id="18-5-3-使用-yield-实现-Fixture-的-Teardown-清理操作"><a href="#18-5-3-使用-yield-实现-Fixture-的-Teardown-清理操作" class="headerlink" title="18.5.3 使用 yield 实现 Fixture 的 Teardown (清理操作)"></a>18.5.3 使用 <code>yield</code> 实现 Fixture 的 Teardown (清理操作)</h4><p>如果 fixture 需要在测试使用完毕后执行清理操作（类似于 <code>unittest</code> 中的 <code>tearDown</code>），可以使用 <code>yield</code> 语句。<code>yield</code> 之前的代码是 setup 部分，<code>yield</code> 之后的代码是 teardown 部分。</p><p><em>示例 (使用 <code>pytest</code> fixture):</em></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># test_file_fixture_pytest.py</span></span><br><span class="line"><span class="keyword">import</span> pytest</span><br><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="keyword">import</span> tempfile  <span class="comment"># 用于创建更安全的临时文件/目录</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="meta">@pytest.fixture(<span class="params">scope=<span class="string">"function"</span></span>)  </span><span class="comment"># 每个测试函数都会得到一个新的临时文件</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">temp_file_with_content</span>():</span><br><span class="line">    <span class="string">"""</span></span><br><span class="line"><span class="string">    创建一个包含内容的临时文件。</span></span><br><span class="line"><span class="string">    测试结束后，该临时文件会被自动清理。</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    <span class="comment"># Setup: 创建临时文件并写入内容</span></span><br><span class="line">    <span class="comment"># tempfile.NamedTemporaryFile 创建一个有名字的临时文件，delete=False 确保在with块结束后文件不会立即删除，</span></span><br><span class="line">    <span class="comment"># 这样测试函数才能访问它。我们需要手动删除。</span></span><br><span class="line">    <span class="comment"># 或者使用 tmp_path fixture (见 18.5.4) 会更简单。</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 使用 tempfile.mkstemp() 可以获得文件名和文件描述符，更可控</span></span><br><span class="line">    fd, file_path = tempfile.mkstemp(text=<span class="literal">True</span>, prefix=<span class="string">"pytest_temp_"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"\n   [Fixture 设置] 创建临时文件: <span class="subst">{file_path}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    file_content = <span class="string">"Hello, pytest fixtures!"</span></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(fd, <span class="string">"w"</span>) <span class="keyword">as</span> f:  <span class="comment"># 使用文件描述符打开</span></span><br><span class="line">        f.write(file_content)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># yield 将文件路径和预期的内容传递给测试函数</span></span><br><span class="line">    <span class="keyword">yield</span> file_path, file_content</span><br><span class="line"></span><br><span class="line">    <span class="comment"># Teardown: 删除临时文件</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"\n   [Fixture 清理] 删除临时文件: <span class="subst">{file_path}</span>"</span>)</span><br><span class="line">    os.remove(file_path)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_read_from_temp_file</span>(<span class="params">temp_file_with_content</span>):</span><br><span class="line">    <span class="string">"""测试从 fixture 创建的临时文件中读取内容。"""</span></span><br><span class="line">    file_path, expected_content = temp_file_with_content  <span class="comment"># 解包 fixture 返回的值</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"\n      [测试运行中] test_read_from_temp_file 访问 <span class="subst">{file_path}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">"r"</span>) <span class="keyword">as</span> f:</span><br><span class="line">        content = f.read()</span><br><span class="line">    <span class="keyword">assert</span> content == expected_content</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">test_temp_file_exists_during_test</span>(<span class="params">temp_file_with_content</span>):</span><br><span class="line">    <span class="string">"""测试临时文件在测试执行期间确实存在。"""</span></span><br><span class="line">    file_path, _ = temp_file_with_content</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"\n      [测试运行中] test_temp_file_exists_during_test 检查 <span class="subst">{file_path}</span>"</span>)</span><br><span class="line">    <span class="keyword">assert</span> os.path.exists(file_path)</span><br></pre></td></tr></tbody></table></figure><p><strong>代码注释与讲解：</strong></p><ul><li><code>yield file_path, file_content</code>: <code>yield</code> 语句是 setup 和 teardown 的分界点。它将 <code>file_path</code> 和 <code>file_content</code> 提供给测试函数。</li><li><code>yield</code> 之后的代码 (<code>os.remove(file_path)</code>) 在使用该 fixture 的测试函数执行完毕后（无论成功或失败）执行。</li></ul></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/34823.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/34823.html&quot;)">Python（十九）：第十八章 单元测试</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/34823.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=Python（十九）：第十八章 单元测试&amp;url=https://prorise666.site/posts/34823.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Python<span class="categoryesPageCount">22</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Python基础知识总汇<span class="tagsPageCount">22</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/33663.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Python（十八）：第十七章 音视频处理</div></div></a></div><div class="next-post pull-right"><a href="/posts/27024.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Python（二十）：第十九章 标准内置库详解</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/17730.html" title="Python（一）：Python 语言特性"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（一）：Python 语言特性</div></div></a></div><div><a href="/posts/8019.html" title="Python（三）：第二章：转义字符"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（三）：第二章：转义字符</div></div></a></div><div><a href="/posts/45310.html" title="Python（七）：第六章：条件循环分支"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（七）：第六章：条件循环分支</div></div></a></div><div><a href="/posts/56572.html" title="Python（九）：第八章： 函数知识总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（九）：第八章： 函数知识总结</div></div></a></div><div><a href="/posts/55902.html" title="Python（二十一）：第二十章：Python 语法新特性总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十一）：第二十章：Python 语法新特性总结</div></div></a></div><div><a href="/posts/2501.html" title="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Python（十九）：第十八章 单元测试",date:"2025-04-19 10:13:45",updated:"2025-07-13 22:13:01",tags:["Python基础知识总汇"],categories:["后端技术","Python"],content:'\n## 第十八章 单元测试\n\n软件开发的核心目标之一是交付高质量、运行稳定的代码。**单元测试 (Unit Testing)** 是保障这一目标的重要手段，它专注于验证软件中最小可测试单元（通常是函数、方法或类）的行为是否符合预期。\n\n在本章中，我们将深入学习 Python 中广受欢迎的测试框架 —— `pytest`。\n\n### 18.1 单元测试简介：\n\n单元测试通过在开发早期发现并修复问题，从而提升代码质量，增强代码重构的信心，并作为一种“活文档”辅助理解代码功能。\n\n**基本流程：**\n\n1.  **隔离单元**：确定要测试的函数、方法或类。\n2.  **定义预期**：明确该单元在特定输入下应有的输出或行为。\n3.  **编写测试**：使用测试框架编写代码来验证这些预期。\n4.  **执行测试**：运行测试并检查结果。\n5.  **迭代优化**：根据测试结果修改代码或测试本身。\n\n### 18.2 Pytest 简介与核心优势\n\n`pytest` 是一个成熟且功能齐全的 Python 测试框架，它使得编写小型、易读的测试变得简单，并且可以扩展以支持复杂的函数式、接口或系统级测试。\n\n**为什么选择 `pytest`？**\n\n  * **极简样板代码**：相比 `unittest`，`pytest` 需要的模板代码更少。测试函数就是普通的 Python 函数，不需要继承任何类。\n  * **强大的 `assert` 语句**：直接使用标准的 `assert` 语句进行断言，`pytest` 会提供详细的断言失败信息。\n  * **灵活的 Fixtures**：`pytest` 的 Fixture 系统非常强大，用于管理测试依赖和测试上下文的准备与清理，比传统的 `setUp/tearDown` 更灵活。\n  * **丰富的插件生态**：拥有大量高质量的第三方插件（如 `pytest-django`, `pytest-cov` (覆盖率), `pytest-xdist` (并行测试) 等）。\n  * **良好的兼容性**：可以运行基于 `unittest` 和 `nose` 编写的测试用例。\n  * **清晰的测试报告**：默认提供易读的测试报告。\n\n**安装 `pytest`**\n\n您可以使用 pip 来安装 `pytest`：\n\n```bash\npip install pytest\n```\n\n### 18.3 Pytest 核心特性概览\n\n`pytest` 的强大功能主要体现在以下几个核心特性上，本笔记将逐一介绍：\n\n| 特性/概念          | 简介                                      | 涉及的主要 pytest 元素/用法                               |\n| :----------------------------------- | :------------------------------------------------------------ | :------------------------------------------------------- |\n| **测试发现 (Test Discovery)** | `pytest` 自动查找符合特定命名约定的测试文件和函数。                 | 文件名 `test_*.py` 或 `*_test.py`；函数/方法名 `test_*`。 |\n| **基本测试函数 (Basic Test Functions)**| 普通 Python 函数即可作为测试用例，无需继承特定类。                | `def test_example(): ...`                                |\n| **断言 (Assertions)** | 使用 Python 内置的 `assert` 语句进行结果验证，`pytest` 提供详细的错误报告。 | `assert expression`                                      |\n| **异常测试 (Exception Testing)** | 优雅地测试代码是否按预期抛出异常。                                | `pytest.raises()` 上下文管理器。                           |\n| **Fixtures (测试固件)** | 管理测试函数的依赖、状态和资源，实现代码复用和模块化。            | `@pytest.fixture` 装饰器, `yield` 用于 teardown。        |\n| **参数化测试 (Parametrization)** | 使用不同的参数多次运行同一个测试函数，避免代码重复。                | `@pytest.mark.parametrize` 装饰器。                      |\n| **标记 (Markers)** | 为测试函数添加元数据，用于分类、跳过、标记预期失败等。            | `@pytest.mark.<marker_name>` (如 `skip`, `xfail`)。    |\n| **运行测试 (Running Tests)** | 通过命令行工具 `pytest` 运行测试，并提供多种选项。                | `pytest` 命令及其参数 (如 `-v`, `-k`, `-m`)。            |\n\n### 18.4 Pytest 基础实践\n\n#### 18.4.1 编写第一个 Pytest 测试：测试加法函数\n\n`pytest` 会自动发现当前目录及其子目录下所有命名为 `test_*.py` 或 `*_test.py` 的文件中的 `test_*` 开头的函数。\n\n*被测代码 (`my_math.py`):*\n\n```python\n# my_math.py\ndef add(x, y):\n    """计算两个数的和"""\n    return x + y\n```\n\n*测试代码 (`test_my_math_pytest.py`):*\n\n```python\n# test_my_math_pytest.py\nfrom my_math import add # 导入需要测试的函数\n\ndef test_simple_addition():\n    """测试基本的加法功能。"""\n    # 直接使用 assert进行断言\n    assert add(1, 2) == 3, "1 + 2 应该等于 3" # 可选的错误信息\n    assert add(-1, 1) == 0\n    # assert add(0, 3) == 这里一定会报错 因为 0 # 3 != 0\n\ndef test_negative_addition():\n    """测试负数相加。"""\n    assert add(-5, -10) == -15\n\n\n```\n\n**代码注释与讲解：**\n\n  * 测试文件命名为 `test_my_math_pytest.py`，遵循 `pytest` 的发现约定。\n  * 测试函数 `test_simple_addition` 和 `test_negative_addition` 以 `test_` 开头。\n  * 直接使用 `assert` 语句。如果 `assert` 后的表达式为 `False`，`pytest` 会将该测试标记为失败，并提供详细的上下文信息。\n  * 最后的 `, "message"` 部分是可选的，如果断言失败，这个消息不会像 `unittest` 的 `msg` 参数那样直接显示，`pytest` 会通过其内省机制提供更丰富的失败信息。\n\n**运行测试：**\n\n![image-20250510175654130](https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250510175654130.png)\n\n`pytest` 会自动找到并执行 `test_simple_addition` 和 `test_negative_addition`。\n\n#### 18.4.2 测试异常：`pytest.raises`\n\n当需要验证代码是否按预期抛出特定异常时，可以使用 `pytest.raises`。\n\n*被测代码 (`more_math.py`，包含 `square` 函数):*\n\n```python\n# more_math.py\ndef square(n):\n    """计算一个数的平方。"""\n    if not isinstance(n, (int, float)):\n        raise TypeError("输入必须是数字")\n    return n * n\n```\n\n*测试代码 (`test_more_math_pytest.py`):*\n\n```python\n# test_more_math_pytest.py\nimport pytest  # 需要导入 pytest 来使用 pytest.raises 等特性\nfrom more_math import square\n\n\ndef test_square_positive_numbers():\n    """测试正数的平方。"""\n    assert square(2) == 4\n    assert square(3.0) == 9.0\n\n\ndef test_square_negative_numbers():\n    """测试负数的平方。"""\n    assert square(-2) == 4\n    assert square(-1.5) == 2.25\n\n\ndef test_square_zero():\n    """测试零的平方。"""\n    assert square(0) == 0\n\ndef test_square_invalid_input_raises_typeerror():\n    """测试当输入无效时，square 函数是否抛出 TypeError。"""\n    # pytest.raises 是一个上下文管理器，用于断言在 with 块内执行的代码会抛出指定类型的异常\n    # 如果代码块中没有抛出 TypeError，测试将失败\n    # 如果代码块中抛出了其他类型的异常，测试也将失败\n    with pytest.raises(TypeError):\n        square(\'呵呵呵呵呵\')  # 这行代码应该抛出 TypeError\n    # 这个测试能通过是因为 square 函数在接收到字符串参数时会抛出 TypeError\n    # 当使用 pytest.raises(TypeError) 上下文管理器时，如果代码块内抛出了 TypeError\n    # pytest 会捕获这个异常并使测试通过，这正是我们期望的行为\n\n\ndef test_square_invalid_input_message():\n    """测试 TypeError 异常的错误信息是否符合预期 (可选)。"""\n    # pytest.raises 的 match 参数允许提供一个正则表达式，用于验证抛出的异常实例的错误消息\n    # 这对于确保错误信息对用户友好且准确非常有用\n    # 如果抛出的异常消息与提供的正则表达式不匹配，测试将失败\n    with pytest.raises(TypeError, match="输入必须是数字"):  # match 参数使用正则表达式匹配错误信息\n        square(\'text\')\n\n```\n\n\n\n### 18.5 Pytest Fixtures：强大的依赖注入与测试准备\n\nFixtures（测试固件）是 `pytest` 中一个非常核心且强大的特性。它们用于为测试函数、类、模块或整个会话设置必要的预置条件（如数据、对象实例、服务连接等），并在测试结束后进行清理。\n\n#### 18.5.1 Fixture 基本概念与应用：`@pytest.fixture`\n\n通过 `@pytest.fixture` 装饰器可以将一个函数标记为 fixture。测试函数可以通过将其名称作为参数来请求使用这个 fixture。\n\n*示例 (`test_fixtures_basic.py`):*\n\n```python\n# test_fixtures_basic.py\nimport pytest\n\<EMAIL>\ndef sample_list():\n    """一个提供简单列表数据的 fixture。"""\n    print("\\n   [Fixture setup] Creating sample_list...")\n    data = [1, 2, 3, 4, 5]\n    return data # fixture 返回的值会注入到测试函数中\n\n\<EMAIL>\ndef sample_dict():\n    """一个提供简单字典数据的 fixture。"""\n    print("\\n   [Fixture setup] Creating sample_dict...")\n    return {"name": "pytest", "type": "framework"}\n\ndef test_list_length(sample_list): # 将 fixture 名称作为参数\n    """测试 sample_list 的长度。"""\n    print("\\n      [Test running] test_list_length using sample_list")\n    assert len(sample_list) == 5\n\n\ndef test_list_content(sample_list):\n    """测试 sample_list 的内容。"""\n    print("\\n      [Test running] test_list_content using sample_list")\n    assert 3 in sample_list\n    assert sample_list[0] == 1\n\n\n\ndef test_dict_values(sample_dict, sample_list): # 一个测试可以使用多个 fixtures\n    """测试 sample_dict 的值，并同时使用 sample_list。"""\n    print("\\n      [Test running] test_dict_values using sample_dict and sample_list")\n    assert sample_dict["name"] == "pytest"\n    assert len(sample_list) > 0 # 确认 sample_list 也被正确注入\n```\n\n**代码注释与讲解：**\n\n  * `@pytest.fixture`: 装饰器，将 `sample_list` 和 `sample_dict` 函数转换为 fixture。\n  * 当测试函数（如 `test_list_length(sample_list)`）在其参数列表中包含 fixture 名称时，`pytest` 会在执行该测试函数之前先执行对应的 fixture 函数，并将其返回值注入到测试函数的同名参数中。\n  * **复用性**：同一个 fixture 可以被多个测试函数使用，避免了重复的设置代码。\n  * **声明式依赖**：测试函数清晰地声明了它所依赖的上下文或数据。\n\n#### 18.5.2 Fixture 的作用域 (Scope)\n\nFixture 可以定义不同的作用域，以控制其执行（setup/teardown）的频率和生命周期。作用域通过 `@pytest.fixture` 的 `scope` 参数指定\n\n作为单元测试来说，没有必要区分的这么死板，**平常来说使用默认值即可**，若有严格需求再详细区分作用域\n\n  * **`function` (默认)**: 每个测试函数执行一次。是开销最小、隔离性最好的作用域。\n  * **`class`**: 每个测试类执行一次。用于类中所有测试方法共享的、创建开销较大的资源。\n  * **`module`**: 每个模块执行一次。\n  * **`package`**: 每个包执行一次 (Python 3.7+, 且 `pytest` 4.3+)。\n  * **`session`**: 整个测试会话（即一次 `pytest` 命令的完整执行过程）执行一次。适用于全局的、创建非常昂贵的资源。\n\n*示例 (`test_fixture_scopes.py`):*\n\n```python\n# test_fixture_scopes.py\nimport pytest\n\n\n# 函数级别 - 每个测试函数都会重新创建一次\<EMAIL> \ndef function_scoped_fixture():\n    print("\\n   [设置] 函数级别的fixture - 每个测试函数都会重新创建")\n    yield "函数值"\n    print("\\n   [清理] 函数级别的fixture")\n\n# 类级别 - 每个测试类只创建一次，类中所有测试方法共享\<EMAIL>(scope="class")\ndef class_scoped_fixture():\n    print("\\n   [设置] 类级别的fixture - 每个测试类只创建一次")\n    yield "类值"\n    print("\\n   [清理] 类级别的fixture")\n\n# 模块级别 - 整个测试模块文件只创建一次，所有测试共享\<EMAIL>(scope="module")\ndef module_scoped_fixture():\n    print("\\n   [设置] 模块级别的fixture - 整个测试文件只创建一次")\n    yield "模块值"\n    print("\\n   [清理] 模块级别的fixture")\n\n# 会话级别 - 整个测试会话只创建一次，所有测试模块共享\<EMAIL>(scope="session")\ndef session_scoped_fixture():\n    print("\\n   [设置] 会话级别的fixture - 整个测试会话只创建一次")\n    yield "会话值"\n    print("\\n   [清理] 会话级别的fixture")\n\n\n\n```\n\n**代码注释与讲解：**\n\n  * 观察运行 `pytest -s -v test_fixture_scopes.py` (`-s` 用于显示 print 输出) 时的输出，可以清晰地看到不同作用域 fixture 的 setup 时机。\n\n#### 18.5.3 使用 `yield` 实现 Fixture 的 Teardown (清理操作)\n\n如果 fixture 需要在测试使用完毕后执行清理操作（类似于 `unittest` 中的 `tearDown`），可以使用 `yield` 语句。`yield` 之前的代码是 setup 部分，`yield` 之后的代码是 teardown 部分。\n\n*示例 (使用 `pytest` fixture):*\n\n```python\n# test_file_fixture_pytest.py\nimport pytest\nimport os\nimport tempfile  # 用于创建更安全的临时文件/目录\n\n\<EMAIL>(scope="function")  # 每个测试函数都会得到一个新的临时文件\ndef temp_file_with_content():\n    """\n    创建一个包含内容的临时文件。\n    测试结束后，该临时文件会被自动清理。\n    """\n    # Setup: 创建临时文件并写入内容\n    # tempfile.NamedTemporaryFile 创建一个有名字的临时文件，delete=False 确保在with块结束后文件不会立即删除，\n    # 这样测试函数才能访问它。我们需要手动删除。\n    # 或者使用 tmp_path fixture (见 18.5.4) 会更简单。\n\n    # 使用 tempfile.mkstemp() 可以获得文件名和文件描述符，更可控\n    fd, file_path = tempfile.mkstemp(text=True, prefix="pytest_temp_")\n    print(f"\\n   [Fixture 设置] 创建临时文件: {file_path}")\n\n    file_content = "Hello, pytest fixtures!"\n    with open(fd, "w") as f:  # 使用文件描述符打开\n        f.write(file_content)\n\n    # yield 将文件路径和预期的内容传递给测试函数\n    yield file_path, file_content\n\n    # Teardown: 删除临时文件\n    print(f"\\n   [Fixture 清理] 删除临时文件: {file_path}")\n    os.remove(file_path)\n\n\ndef test_read_from_temp_file(temp_file_with_content):\n    """测试从 fixture 创建的临时文件中读取内容。"""\n    file_path, expected_content = temp_file_with_content  # 解包 fixture 返回的值\n    print(f"\\n      [测试运行中] test_read_from_temp_file 访问 {file_path}")\n\n    with open(file_path, "r") as f:\n        content = f.read()\n    assert content == expected_content\n\n\ndef test_temp_file_exists_during_test(temp_file_with_content):\n    """测试临时文件在测试执行期间确实存在。"""\n    file_path, _ = temp_file_with_content\n    print(f"\\n      [测试运行中] test_temp_file_exists_during_test 检查 {file_path}")\n    assert os.path.exists(file_path)\n```\n\n**代码注释与讲解：**\n\n  * `yield file_path, file_content`: `yield` 语句是 setup 和 teardown 的分界点。它将 `file_path` 和 `file_content` 提供给测试函数。\n  * `yield` 之后的代码 (`os.remove(file_path)`) 在使用该 fixture 的测试函数执行完毕后（无论成功或失败）执行。'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E5%85%AB%E7%AB%A0-%E5%8D%95%E5%85%83%E6%B5%8B%E8%AF%95"><span class="toc-number">1.</span> <span class="toc-text">第十八章 单元测试</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#18-1-%E5%8D%95%E5%85%83%E6%B5%8B%E8%AF%95%E7%AE%80%E4%BB%8B%EF%BC%9A"><span class="toc-number">1.1.</span> <span class="toc-text">18.1 单元测试简介：</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#18-2-Pytest-%E7%AE%80%E4%BB%8B%E4%B8%8E%E6%A0%B8%E5%BF%83%E4%BC%98%E5%8A%BF"><span class="toc-number">1.2.</span> <span class="toc-text">18.2 Pytest 简介与核心优势</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#18-3-Pytest-%E6%A0%B8%E5%BF%83%E7%89%B9%E6%80%A7%E6%A6%82%E8%A7%88"><span class="toc-number">1.3.</span> <span class="toc-text">18.3 Pytest 核心特性概览</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#18-4-Pytest-%E5%9F%BA%E7%A1%80%E5%AE%9E%E8%B7%B5"><span class="toc-number">1.4.</span> <span class="toc-text">18.4 Pytest 基础实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#18-4-1-%E7%BC%96%E5%86%99%E7%AC%AC%E4%B8%80%E4%B8%AA-Pytest-%E6%B5%8B%E8%AF%95%EF%BC%9A%E6%B5%8B%E8%AF%95%E5%8A%A0%E6%B3%95%E5%87%BD%E6%95%B0"><span class="toc-number">1.4.1.</span> <span class="toc-text">18.4.1 编写第一个 Pytest 测试：测试加法函数</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#18-4-2-%E6%B5%8B%E8%AF%95%E5%BC%82%E5%B8%B8%EF%BC%9Apytest-raises"><span class="toc-number">1.4.2.</span> <span class="toc-text">18.4.2 测试异常：pytest.raises</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#18-5-Pytest-Fixtures%EF%BC%9A%E5%BC%BA%E5%A4%A7%E7%9A%84%E4%BE%9D%E8%B5%96%E6%B3%A8%E5%85%A5%E4%B8%8E%E6%B5%8B%E8%AF%95%E5%87%86%E5%A4%87"><span class="toc-number">1.5.</span> <span class="toc-text">18.5 Pytest Fixtures：强大的依赖注入与测试准备</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#18-5-1-Fixture-%E5%9F%BA%E6%9C%AC%E6%A6%82%E5%BF%B5%E4%B8%8E%E5%BA%94%E7%94%A8%EF%BC%9A-pytest-fixture"><span class="toc-number">1.5.1.</span> <span class="toc-text">18.5.1 Fixture 基本概念与应用：@pytest.fixture</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#18-5-2-Fixture-%E7%9A%84%E4%BD%9C%E7%94%A8%E5%9F%9F-Scope"><span class="toc-number">1.5.2.</span> <span class="toc-text">18.5.2 Fixture 的作用域 (Scope)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#18-5-3-%E4%BD%BF%E7%94%A8-yield-%E5%AE%9E%E7%8E%B0-Fixture-%E7%9A%84-Teardown-%E6%B8%85%E7%90%86%E6%93%8D%E4%BD%9C"><span class="toc-number">1.5.3.</span> <span class="toc-text">18.5.3 使用 yield 实现 Fixture 的 Teardown (清理操作)</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>