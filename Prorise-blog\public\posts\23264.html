<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理入门（五）：第五章：产品设计与原型制作 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理入门（五）：第五章：产品设计与原型制作"><meta name="application-name" content="产品经理入门（五）：第五章：产品设计与原型制作"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="产品经理入门（五）：第五章：产品设计与原型制作"><meta property="og:url" content="https://prorise666.site/posts/23264.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第五章：产品设计与原型制作在前面的章节里，我们投入了大量精力去“听”和“想”，我们学会了如何收集、分析、管理需求，这些都属于“问题域”的范畴——即，我们应该解决什么问题。 从这一章开始，我们将进入“解决方案域”——即，我们应该如何设计产品，来优雅地解决这些问题。我会带大家走完从梳理设计思路，到最终绘"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta name="description" content="第五章：产品设计与原型制作在前面的章节里，我们投入了大量精力去“听”和“想”，我们学会了如何收集、分析、管理需求，这些都属于“问题域”的范畴——即，我们应该解决什么问题。 从这一章开始，我们将进入“解决方案域”——即，我们应该如何设计产品，来优雅地解决这些问题。我会带大家走完从梳理设计思路，到最终绘"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/23264.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"产品经理入门（五）：第五章：产品设计与原型制作",postAI:"true",pageFillDescription:"第五章：产品设计与原型制作, 5.1 产品设计思路, 5.1.2 案例：直播间需求分析, 5.1.3 产品设计流程, 1. 角色 (Role), 2. 场景 (Scene), 3. 目的 (Goal), 4. 流程 (Flow), 5. 功能 (Function), 5.1.4 功能清单, 5.1.5 功能清单与需求池的区别, 5.1.6 产品设计思路工具运用方式, 5.2 原型的概念及分类, 5.2.1 学习目标, 5.2.2 原型的概念及分类, 1. 草图原型 (Sketch), 2. 低保真原型 (Low-fidelity Prototype), 3. 高保真原型 (High-fidelity Prototype), 5.3 原型绘制工具, 5.3.1 学习目标, 5.3.2 原型绘制工具介绍及作用, 5.3.3 常用原型绘制工具, 5.3.4 原型工具的核心工作区, 5.3.5 常见元件的使用场景, 5.4 原型设计规范, 5.4.1 学习目标, 5.4.2 尺寸规范, 1. Web端尺寸规范, 2. 移动端尺寸规范, 5.4.3 结构规范, 5.4.4 原型设计规范注意事项, 5.4.5 原型设计规范小结, 5.5 墨刀制作基础交互, 5.5.1 学习目标, 5.5.2 什么是交互, 5.5.3 什么是交互设计, 5.5.4 常见交互设计, 5.5.5 墨刀制作基础交互案例展示第五章产品设计与原型制作在前面的章节里我们投入了大量精力去听和想我们学会了如何收集分析管理需求这些都属于问题域的范畴即我们应该解决什么问题从这一章开始我们将进入解决方案域即我们应该如何设计产品来优雅地解决这些问题我会带大家走完从梳理设计思路到最终绘制出可交互原型的全过程产品设计思路在我正式开始画任何一个线框图之前我的脑海里必须有一套清晰结构化的设计思路这能确保我设计出的功能是源于真实的用户场景并且逻辑是通顺的我的这套思路可以用一个公式来表达在明确的角色和场景下为了达成用户的目的他需要走通一个怎样的流程而我们需要提供什么样的功能来支撑这个流程下面我们就用一个完整的案例来贯穿这套设计思路案例直播间需求分析我们机构的某个毕业学员小入职了一家做在线英语培训的公司公司有很多外籍教师学员大都为中国学生目前公司的产品经理在每周对需求池当中的原始需求进行整理分析时发现之前有个叫的外籍老师上周提出了如下需求希望可以在上课时在网页版的直播间里可以打字进行答疑并且在直播课里最好提供举手邀请某人语音的功能当在公司当中遇到了这样的一个需求你会怎样去考虑呢产品设计流程我会严格按照角色场景目的流程功能这五个步骤来一步步地推导出我的产品方案角色首先我需要明确这个场景下我的核心用户是谁外籍老师她的一个关键特征是不认识中文直播间学员中国学员他们的特征是需要在直播中与老师互动场景这两个角色在直播间上课这个大场景下会发生哪些具体的交互子场景子场景老师答疑老师在讲课过程中学员随时会产生疑问需要老师解答子场景点学员回答问题老师为了增强互动需要主动挑选一位学员来回答问题其他场景比如老师需要在直播间内布置作业等目的在这些具体的子场景下各个角色的核心目的是什么老师答疑场景的目的方便老师能及时地对学员提出的问题进行答疑点学员回答问题场景的目的方便不认识中文昵称的外籍老师能方便地挑选学生回答问题流程为了达成上述目的一个理想化的操作流程是怎样的老师答疑的流程老师讲课学员产生疑问学员通过某个方式提出问题老师看到问题并解答点学员回答问题的流程老师想提问老师通过某个方式主动挑选学生被选中的学生通过某个方式回答问题功能最后也是最关键的一步为了支撑上述流程的顺畅运转我们需要提供哪些核心功能支撑老师答疑流程最直接的功能就是提供一个聊天区让学生和老师都可以用文字进行实时的提问和回答支撑点学员回答问题流程针对老师方便挑选我们可以提供一个举手功能想回答问题的学生可以举手老师就能从举手的学生里选针对学生方便回答我们可以提供一个拉上麦功能老师可以直接点击举手学生的头像邀请他上麦进行语音回答到此为止我们就通过一套严谨的思路把一个模糊的需求推导出了三个具体可执行的功能点聊天区举手拉上麦功能清单当我通过上述流程推导出多个功能点后我会把它们整理成一份功能清单这份清单详细地列出了为了满足本次产品目标我们需要开发的所有功能模块和子功能它是我们后续进行原型设计和与开发团队沟通的基础功能清单与需求池的区别我需要强调一下功能清单和我们之前提过的需求池的区别对比维度需求池功能清单内容未经处理的原始需求集合包含了各种想法问题建议是发散的经过分析和设计后得出的产品解决方案是明确收敛可执行的功能项阶段处于问题域是我们分析的起点处于解决方案域是我们设计和开发的起点简单来说需求池是原材料仓库而功能清单是加工图纸产品设计思路工具运用方式我们前面学过的很多工具都会在这个阶段被综合运用我会用用户访谈来明确角色场景和目的我会用流程图来梳理和表达流程我会用结构图特别是功能结构图来整理我的功能清单原型的概念及分类对我来说原型是连接需求文档与最终产品之间最重要的一座桥梁它是产品想法的第一次可视化具象化的表达学习目标在本节中我的目标是带大家清晰地理解原型的不同保真度的概念我们将学习区分草图低保真原型和高保真原型的差异以及我会在什么样的情况下选择使用哪一种原型原型的概念及分类我给原型的定义是用线条图形绘制出的产品框架也称线框图是需求和功能的具体化表象在我的工作中我从不会把原型看作是一个东西而是根据项目的不同阶段和沟通目的把它分为三种不同的类型草图原型这是我进行产品设计的第一步也是最快速最低成本的一种方式特点顾名思义它就是用笔和纸或者在白板上随手画出的草稿我画草图时核心是梳理逻辑框架和页面流程完全不讲究排版对齐和美观也不需要表达出所有的页面元素我的适用场景我通常在个人进行方案构思的阶段或者在团队内部进行头脑风暴时大量使用草图它的使命就是快速表达快速讨论快速迭代画完就可以随时扔掉没有任何心理负担低保真原型当我的思路通过草图基本确定后我就会使用墨刀等专业工具来绘制正式的低保真原型这在我的日常工作中是产出最多也最重要的一类原型特点它要求绘图整齐布局规范我通常只使用黑白灰三种颜色用简单的线框和色块来表示图片文字和各类组件虽然它看起来很朴素但它必须完整准确地表达出产品方案页面上所有的功能按钮文案跳转关系都必须清晰无误我的适用场景低保真原型是我用来进行正式方案交付的文档我会用它来召开需求评审会并把它作为最终交付给开发和测试工程师的研发依据它剥离了所有视觉干扰让大家都能聚焦在功能和流程本身高保真原型这是保真度最高的原型它在视觉上已经和最终的线上产品非常接近了特点它不仅绘图规范排版求真还包含了丰富的视觉元素如配色图标字体图片等更重要的是它通常是可以交互的用户可以像使用真实一样在上面点击跳转来模拟真实的使用体验我的适用场景因为制作成本很高我只在特定的场景下才会制作高保真原型比如需要向老板或投资人进行路演宣传时或者在产品上线前需要进行用户体验测试时以及有些公司的管理流程要求在开发前必须有高保真原型用于最终决策原型绘制工具学习目标我的目标是带大家熟悉一款现代化的原型工具的核心使用逻辑我们将了解原型工具的界面通常是如何分布的并掌握那些最常用的基础元件也就是我们画原型时的砖块应该在什么场景下使用原型绘制工具介绍及作用在上一节我们明确了原型有草图低保真高保真之分要绘制出规范的低保真和高保真原型我们就必须借助专业的工具这些工具能帮助我们高效地搭建页面结构添加交互并方便地进行分享和评审常用原型绘制工具正如我们之前讨论并达成共识的在众多工具中我个人非常推荐像墨刀这样集设计原型协作为一体的在线平台它功能强大上手简单非常适合我们当前的学习和未来的团队协作接下来的内容我会以通用原型工具的核心逻辑进行讲解其中的概念和操作您都可以在我们选定的墨刀中找到并熟练应用原型工具的核心工作区无论我们使用哪款工具其主界面通常都由几个核心的工作区构成我将这些区域的功能总结如下这能帮助我们快速熟悉任何一款新工具的布局菜单与工具栏通常在界面的最上方这里集成了软件的通用功能比如文件操作新建保存导出常用工具选择放大缩小等页面管理区通常在左侧这是我们整个项目的目录树我在这里管理原型的所有页面可以进行新增删除重命名和调整层级元件库这是我们的工具箱和素材库通常也在左侧里面包含了我们绘制原型需要的所有砖块如按钮文本框图片等我只需要把它们拖拽到画布上即可使用画布这是界面中心最大的一块区域是我们的画板我们所有的设计工作都在这里完成检视区通常在右侧这是我用来精细调整元件的属性面板当我选中画布上的任何一个元件时都可以在这里修改它的尺寸颜色字体边框以及为它添加交互效果概要图层区这个区域会以列表的形式显示出当前画布上所有的元件及其层级关系当页面变得复杂元件相互重叠时我通过这里可以非常方便地选中和管理它们母版组件区这是一个进阶但非常有用的功能对于那些需要在多个页面重复使用的元素比如导航栏页脚我会把它们创建为母版或公共组件这样我只需要修改一次母版所有引用了它的页面都会同步更新极大地提升了效率常见元件的使用场景掌握了工作区布局后我们就要来认识一下元件库里那些最常用的砖块了熟悉它们各自的用途是画好原型的基础元件我的使用场景说明矩形图片占位符这是我用来搭建页面基本骨架的积木我用它们来快速划分页面区域表示图片或等内容占位按钮各类用于触发核心操作是用户与系统交互最直接最重要的途径比如登录提交购买等标题文本用于构建页面的信息层级清晰地传达各类文字内容是页面的血肉文本框文本域当需要用户输入单行或多行文字时使用比如用户名输入框搜索框评论输入区下拉列表单选复选当需要用户从一组固定的选项中进行选择时使用单选只能选一项复选可以选多项表格列表用于结构化地清晰地展示大量数据或信息比如后台管理系统的数据列表热区这是一个隐形的矩形当我想让一张图片或一组元素实现整体点击跳转时我就会在上面覆盖一个热区来添加交互它本身在预览时是看不见的原型设计规范在我看来画原型绝不仅仅是把各种元件拖到画布上就完事了为了让我的原型图清晰专业具备可交付性我必须遵循一套严格的设计规范这套规范不是为了限制我们的创意恰恰相反它是为了提升我们整个团队的沟通效率一个遵循规范的原型就像一篇字迹工整标点清晰的文章能让读它的人设计师开发测试一目了然学习目标在本节中我的目标是带大家掌握我绘制原型时所遵循的几项基本规范我们将学习端和移动端的标准尺寸常见的页面结构以及能让你的原型图专业度瞬间提升的五大注意事项尺寸规范在我开始绘制任何页面之前我首先要确定的就是我的画板尺寸端尺寸规范对于端的网页原型现在主流的显示器分辨率是因此我的画布宽度通常会设置为或更高但更重要的一个概念是版心版心指的是网页上承载核心内容的有效显示区域为了保证在不同尺寸的宽屏显示器上内容都清晰易读不会过分拉伸我通常会将版心的宽度控制在到之间并让它在页面上水平居中移动端尺寸规范对于移动端的原型为了保持所有页面的一致性我会选择一个基准尺寸来作图目前我以及行业内最通用的低保真原型尺寸是基于的逻辑分辨率在这个基准尺寸内我还对几个系统级的区域高度严格遵守规范状态栏就是手机最顶部显示信号时间电量的那一条它的标准高度是导航栏是页面顶部的包含页面标题和返回按钮的区域它的标准高度是标签栏是底部的主菜单导航它的标准高度是从一开始就遵循这些尺寸规范能让我的原型图显得非常专业也便于后续设计师进行视觉稿的还原结构规范尺寸确定后我会思考页面的整体布局结构端最常见的两种结构是左右布局左侧为导航右侧为内容区常见于后台管理系统和居中布局导航和内容区都在页面中心常见于官网博客等移动端一个典型的页面其结构通常由上至下由状态栏导航栏内容区标签栏这几个固定的区块构成熟悉这些通用结构能帮我快速规范地搭建页面原型设计规范注意事项最后也是最重要的我总结了我个人在绘制原型时一定会遵守的五大黄金法则做好这五点你的原型图就能立刻和业余拉开差距页面结构在原型工具中我会用文件夹和清晰的命名来组织我的页面层级让整个项目的结构一目了然框架比例我会先定好页面的基础布局和比例并在所有页面中保持一致这能带来稳定舒适的视觉感受间距一致这是专业性的关键体现我会确保元素与元素之间的间距是有规律且统一的比如卡片与卡片的间距是那在所有地方都应该是位置对齐我要求自己做到像素眼借助工具的对齐功能确保页面上所有的元素要么左对齐要么居中对齐要么右对齐绝不允许出现肉眼可见的错位元件大小相同类型的元件尺寸必须保持一致比如所有主要按钮的高度都是所有正文的字号都是这能让界面看起来更和谐更具秩序感原型设计规范小结我将原型设计的核心规范总结为下面这张自检表规范维度我的核心原则尺寸端关注版心移动端以为基准结构采用通用布局如居中布局上下导航结构注意事项对齐间距大小比例结构五大要素在整个原型中必须保持高度一致性墨刀制作基础交互一个只会展示不能点击的原型就像一张没有灵魂的皮囊而交互就是我们为这具皮囊注入灵魂的过程它能把一张张孤立的页面串联成一个完整可体验的产品故事在这一节我将带大家学习交互设计的基本逻辑并掌握如何使用我们选定的工具墨刀来制作几种最常见最核心的交互效果学习目标我的目标是让我们掌握交互设计的核心公式并能熟练运用墨刀独立制作出页面跳转弹窗悬浮提示和轮播图这四种基础但至关重要的交互效果什么是交互我理解的交互就是用户与产品之间的一场对话用户通过点击滑动输入等行为说话而产品则通过页面变化动画提示等方式来回应什么是交互设计那么交互设计就是我们作为产品经理去预设这场对话的规则和剧本在墨刀这样的原型工具里这个剧本的创作遵循着一个万能公式这也是交互设计的核心交互事件动作事件就是当用户做什么的时候这是触发器比如当用户单击时当鼠标移入时当页面加载时动作就是产品应该发生什么变化这是响应比如链接到某个页面显示隐藏某个元素改变某个元件的状态我们所有的交互设计都是围绕着在什么事件下执行什么动作来展开的常见交互设计掌握了事件动作这个核心公式我们就可以组合出千变万化的交互以下是我在工作中最高频使用的四种跳转这是最基础的交互它将页面串联起来在墨刀里我选中一个按钮为它添加一个单击的事件再选择链接到页面这个动作并指定目标页面即可显示隐藏常用于制作弹窗和下拉菜单我先将要弹出的内容比如一个弹窗设置为默认隐藏然后给一个触发按钮添加单击事件并选择显示隐藏动作作用于那个隐藏的弹窗悬浮显示常用于制作提示信息我会给目标元件添加鼠标移入事件触发显示某个提示框的动作同时再添加一个鼠标移出事件触发隐藏这个提示框的动作动态面板轮播用于制作轮播图等效果在墨刀里这个交互被简化了我可以直接使用它自带的轮播组件把几张图片放进去它就能自动实现切换效果其背后的逻辑就是通过延时这个事件来触发切换到下一状态的动作墨刀制作基础交互案例展示我们来看这四种交互在真实场景下的应用我已经预设做好了两个模板分别为微信的原型图以及对应的聊天详情如下跳转页面案例就像微信的聊天列表当我要实现点击某个好友就进入和他聊天的页面时我就会为列表里的每一项都添加一个单击事件并分别链接到对应的聊天页面实现效果如下弹框提示案例当用户点击某个按钮时为了响应他们我需要弹出一个确认框这个确认框我会在墨刀里提前画好并设置为隐藏然后给对应按钮添加单击事件动作为显示这个确认框悬浮显示案例当鼠标移到一个被缩略的标题上我希望显示完整的标题我就会做一个隐藏的包含完整标题的文本框然后通过鼠标移入移出事件来控制它的显示和隐藏",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-21 14:52:21",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E7%AB%A0%EF%BC%9A%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E4%B8%8E%E5%8E%9F%E5%9E%8B%E5%88%B6%E4%BD%9C"><span class="toc-text">第五章：产品设计与原型制作</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#5-1-%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-text">5.1 产品设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-2-%E6%A1%88%E4%BE%8B%EF%BC%9A%E7%9B%B4%E6%92%AD%E9%97%B4%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">5.1.2 案例：直播间需求分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-3-%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%B5%81%E7%A8%8B"><span class="toc-text">5.1.3 产品设计流程</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%A7%92%E8%89%B2-Role"><span class="toc-text">1. 角色 (Role)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9C%BA%E6%99%AF-Scene"><span class="toc-text">2. 场景 (Scene)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%9B%AE%E7%9A%84-Goal"><span class="toc-text">3. 目的 (Goal)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E6%B5%81%E7%A8%8B-Flow"><span class="toc-text">4. 流程 (Flow)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-%E5%8A%9F%E8%83%BD-Function"><span class="toc-text">5. 功能 (Function)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-4-%E5%8A%9F%E8%83%BD%E6%B8%85%E5%8D%95"><span class="toc-text">5.1.4 功能清单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-5-%E5%8A%9F%E8%83%BD%E6%B8%85%E5%8D%95%E4%B8%8E%E9%9C%80%E6%B1%82%E6%B1%A0%E7%9A%84%E5%8C%BA%E5%88%AB"><span class="toc-text">5.1.5 功能清单与需求池的区别</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-6-%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF%E5%B7%A5%E5%85%B7%E8%BF%90%E7%94%A8%E6%96%B9%E5%BC%8F"><span class="toc-text">5.1.6 产品设计思路工具运用方式</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-2-%E5%8E%9F%E5%9E%8B%E7%9A%84%E6%A6%82%E5%BF%B5%E5%8F%8A%E5%88%86%E7%B1%BB"><span class="toc-text">5.2 原型的概念及分类</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">5.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-2-2-%E5%8E%9F%E5%9E%8B%E7%9A%84%E6%A6%82%E5%BF%B5%E5%8F%8A%E5%88%86%E7%B1%BB"><span class="toc-text">5.2.2 原型的概念及分类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%8D%89%E5%9B%BE%E5%8E%9F%E5%9E%8B-Sketch"><span class="toc-text">1. 草图原型 (Sketch)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BD%8E%E4%BF%9D%E7%9C%9F%E5%8E%9F%E5%9E%8B-Low-fidelity-Prototype"><span class="toc-text">2. 低保真原型 (Low-fidelity Prototype)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E9%AB%98%E4%BF%9D%E7%9C%9F%E5%8E%9F%E5%9E%8B-High-fidelity-Prototype"><span class="toc-text">3. 高保真原型 (High-fidelity Prototype)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-3-%E5%8E%9F%E5%9E%8B%E7%BB%98%E5%88%B6%E5%B7%A5%E5%85%B7"><span class="toc-text">5.3 原型绘制工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">5.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-2-%E5%8E%9F%E5%9E%8B%E7%BB%98%E5%88%B6%E5%B7%A5%E5%85%B7%E4%BB%8B%E7%BB%8D%E5%8F%8A%E4%BD%9C%E7%94%A8"><span class="toc-text">5.3.2 原型绘制工具介绍及作用</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-3-%E5%B8%B8%E7%94%A8%E5%8E%9F%E5%9E%8B%E7%BB%98%E5%88%B6%E5%B7%A5%E5%85%B7"><span class="toc-text">5.3.3 常用原型绘制工具</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-4-%E5%8E%9F%E5%9E%8B%E5%B7%A5%E5%85%B7%E7%9A%84%E6%A0%B8%E5%BF%83%E5%B7%A5%E4%BD%9C%E5%8C%BA"><span class="toc-text">5.3.4 原型工具的核心工作区</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-5-%E5%B8%B8%E8%A7%81%E5%85%83%E4%BB%B6%E7%9A%84%E4%BD%BF%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">5.3.5 常见元件的使用场景</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-4-%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83"><span class="toc-text">5.4 原型设计规范</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">5.4.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-2-%E5%B0%BA%E5%AF%B8%E8%A7%84%E8%8C%83"><span class="toc-text">5.4.2 尺寸规范</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-Web%E7%AB%AF%E5%B0%BA%E5%AF%B8%E8%A7%84%E8%8C%83"><span class="toc-text">1. Web端尺寸规范</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%A7%BB%E5%8A%A8%E7%AB%AF%E5%B0%BA%E5%AF%B8%E8%A7%84%E8%8C%83"><span class="toc-text">2. 移动端尺寸规范</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-3-%E7%BB%93%E6%9E%84%E8%A7%84%E8%8C%83"><span class="toc-text">5.4.3 结构规范</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-4-%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-text">5.4.4 原型设计规范注意事项</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-5-%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83%E5%B0%8F%E7%BB%93"><span class="toc-text">5.4.5 原型设计规范小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-5-%E5%A2%A8%E5%88%80%E5%88%B6%E4%BD%9C%E5%9F%BA%E7%A1%80%E4%BA%A4%E4%BA%92"><span class="toc-text">5.5 墨刀制作基础交互</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">5.5.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-2-%E4%BB%80%E4%B9%88%E6%98%AF%E4%BA%A4%E4%BA%92"><span class="toc-text">5.5.2 什么是交互</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-3-%E4%BB%80%E4%B9%88%E6%98%AF%E4%BA%A4%E4%BA%92%E8%AE%BE%E8%AE%A1"><span class="toc-text">5.5.3 什么是交互设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-4-%E5%B8%B8%E8%A7%81%E4%BA%A4%E4%BA%92%E8%AE%BE%E8%AE%A1"><span class="toc-text">5.5.4 常见交互设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-5-%E5%A2%A8%E5%88%80%E5%88%B6%E4%BD%9C%E5%9F%BA%E7%A1%80%E4%BA%A4%E4%BA%92%E6%A1%88%E4%BE%8B%E5%B1%95%E7%A4%BA"><span class="toc-text">5.5.5 墨刀制作基础交互案例展示</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理入门（五）：第五章：产品设计与原型制作</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-20T12:13:45.000Z" title="发表于 2025-07-20 20:13:45">2025-07-20</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:21.088Z" title="更新于 2025-07-21 14:52:21">2025-07-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">5.9k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>17分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理入门（五）：第五章：产品设计与原型制作"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/23264.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/23264.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理入门（五）：第五章：产品设计与原型制作</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-20T12:13:45.000Z" title="发表于 2025-07-20 20:13:45">2025-07-20</time><time itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:21.088Z" title="更新于 2025-07-21 14:52:21">2025-07-21</time></header><div id="postchat_postcontent"><h1 id="第五章：产品设计与原型制作"><a href="#第五章：产品设计与原型制作" class="headerlink" title="第五章：产品设计与原型制作"></a>第五章：产品设计与原型制作</h1><p>在前面的章节里，我们投入了大量精力去“听”和“想”，我们学会了如何收集、分析、管理需求，这些都属于“<strong>问题域</strong>”的范畴——即，<strong>我们应该解决什么问题</strong>。</p><p>从这一章开始，我们将进入“<strong>解决方案域</strong>”——即，<strong>我们应该如何设计产品，来优雅地解决这些问题</strong>。我会带大家走完从梳理设计思路，到最终绘制出可交互原型的全过程。</p><h2 id="5-1-产品设计思路"><a href="#5-1-产品设计思路" class="headerlink" title="5.1 产品设计思路"></a>5.1 产品设计思路</h2><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720095709353.png" alt="image-20250720095709353"></p><p>在我正式开始画任何一个线框图（Wireframe）之前，我的脑海里必须有一套清晰、结构化的设计思路。这能确保我设计出的功能，是源于真实的用户场景，并且逻辑是通顺的。</p><p>我的这套思路，可以用一个公式来表达：<strong>在明确的【角色】和【场景】下，为了达成用户的【目的】，他需要走通一个怎样的【流程】，而我们需要提供什么样的【功能】来支撑这个流程。</strong></p><p>下面，我们就用一个完整的案例，来贯穿这套设计思路。</p><h3 id="5-1-2-案例：直播间需求分析"><a href="#5-1-2-案例：直播间需求分析" class="headerlink" title="5.1.2 案例：直播间需求分析"></a>5.1.2 案例：直播间需求分析</h3><p>我们机构的某个毕业学员 P小M 入职了一家做在线英语培训的公司，公司有很多外籍教师，学员大都为中国学生。目前公司的产品经理在每周对需求池当中的原始需求进行整理分析时，发现之前有个叫Zoe的外籍老师上周提出了如下需求：<br><strong>“希望可以在上课时在网页版的直播间里可以打字，进行答疑，并且在直播课里最好提供举手、邀请某人语音的功能”</strong><br>当在公司当中遇到了这样的一个需求，你会怎样去考虑呢？</p><h3 id="5-1-3-产品设计流程"><a href="#5-1-3-产品设计流程" class="headerlink" title="5.1.3 产品设计流程"></a>5.1.3 产品设计流程</h3><p>我会严格按照“角色 → 场景 → 目的 → 流程 → 功能”这五个步骤，来一步步地推导出我的产品方案。</p><h4 id="1-角色-Role"><a href="#1-角色-Role" class="headerlink" title="1. 角色 (Role)"></a>1. 角色 (Role)</h4><p>首先，我需要明确，这个场景下，我的核心用户是谁？</p><ul><li><strong>Zoe</strong>：外籍老师，她的一个关键特征是<strong>不认识中文</strong>。</li><li><strong>直播间学员</strong>：中国学员，他们的特征是需要在直播中与老师互动。</li></ul><h4 id="2-场景-Scene"><a href="#2-场景-Scene" class="headerlink" title="2. 场景 (Scene)"></a>2. 场景 (Scene)</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720095921040.png" alt="image-20250720095921040"></p><p>这两个角色，在“直播间上课”这个大场景下，会发生哪些具体的交互子场景？</p><ul><li><strong>子场景1：老师答疑</strong>。老师在讲课过程中，学员随时会产生疑问，需要老师解答。</li><li><strong>子场景2：点学员回答问题</strong>。老师为了增强互动，需要主动挑选一位学员来回答问题。</li><li><strong>其他场景</strong>：比如老师需要在直播间内布置作业等。</li></ul><h4 id="3-目的-Goal"><a href="#3-目的-Goal" class="headerlink" title="3. 目的 (Goal)"></a>3. 目的 (Goal)</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100201566.png" alt="image-20250720100201566"></p><p>在这些具体的子场景下，各个角色的核心目的是什么？</p><ul><li><strong>老师答疑场景的目的</strong>：方便老师能<strong>及时地</strong>对学员提出的问题进行答疑。</li><li><strong>点学员回答问题场景的目的</strong>：方便<strong>不认识中文昵称</strong>的外籍老师，能<strong>方便地</strong>挑选学生回答问题。</li></ul><h4 id="4-流程-Flow"><a href="#4-流程-Flow" class="headerlink" title="4. 流程 (Flow)"></a>4. 流程 (Flow)</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100036943.png" alt="image-20250720100036943"></p><p>为了达成上述目的，一个理想化的操作流程是怎样的？</p><ul><li><strong>老师答疑的流程</strong>：老师讲课 → 学员产生疑问 → 学员通过某个方式提出问题 → 老师看到问题并解答。</li><li><strong>点学员回答问题的流程</strong>：老师想提问 → 老师通过某个方式主动挑选学生 → 被选中的学生通过某个方式回答问题。</li></ul><h4 id="5-功能-Function"><a href="#5-功能-Function" class="headerlink" title="5. 功能 (Function)"></a>5. 功能 (Function)</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100113753.png" alt="image-20250720100113753"></p><p>最后，也是最关键的一步：为了支撑上述流程的顺畅运转，我们需要提供哪些核心功能？</p><ul><li><strong>支撑“老师答疑”流程</strong>：最直接的功能就是<strong>提供一个聊天区</strong>，让学生和老师都可以用文字进行实时的提问和回答。</li><li><strong>支撑“点学员回答问题”流程</strong>：<ol><li>针对“老师方便挑选”：我们可以<strong>提供一个“举手”功能</strong>，想回答问题的学生可以“举手”，老师就能从举手的学生里选。</li><li>针对“学生方便回答”：我们可以<strong>提供一个“拉上麦”功能</strong>，老师可以直接点击举手学生的头像，邀请他上麦进行语音回答。</li></ol></li></ul><p>到此为止，我们就通过一套严谨的思路，把一个模糊的需求，推导出了三个具体、可执行的功能点：<strong>聊天区、举手、拉上麦</strong>。</p><hr><h3 id="5-1-4-功能清单"><a href="#5-1-4-功能清单" class="headerlink" title="5.1.4 功能清单"></a>5.1.4 功能清单</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100327337.png" alt="image-20250720100327337"></p><p>当我通过上述流程推导出多个功能点后，我会把它们整理成一份**“功能清单（Function List）”**。这份清单详细地列出了为了满足本次产品目标，我们需要开发的所有功能模块和子功能。它是我们后续进行原型设计和与开发团队沟通的基础。</p><h3 id="5-1-5-功能清单与需求池的区别"><a href="#5-1-5-功能清单与需求池的区别" class="headerlink" title="5.1.5 功能清单与需求池的区别"></a>5.1.5 功能清单与需求池的区别</h3><p>我需要强调一下“功能清单”和我们之前提过的“需求池”的区别。</p><table><thead><tr><th align="left"><strong>对比维度</strong></th><th align="left"><strong>需求池 (Requirement Pool)</strong></th><th align="left"><strong>功能清单 (Function List)</strong></th></tr></thead><tbody><tr><td align="left"><strong>内容</strong></td><td align="left"><strong>未经处理的“原始需求”集合</strong>。包含了各种想法、问题、建议，是发散的。</td><td align="left"><strong>经过分析和设计后，得出的“产品解决方案”</strong>。是明确、收敛、可执行的功能项。</td></tr><tr><td align="left"><strong>阶段</strong></td><td align="left">处于**“问题域”**，是我们分析的起点。</td><td align="left">处于**“解决方案域”**，是我们设计和开发的起点。</td></tr></tbody></table><p>简单来说，<strong>需求池是“原材料仓库”，而功能清单是“加工图纸”</strong>。</p><h3 id="5-1-6-产品设计思路工具运用方式"><a href="#5-1-6-产品设计思路工具运用方式" class="headerlink" title="5.1.6 产品设计思路工具运用方式"></a>5.1.6 产品设计思路工具运用方式</h3><p>我们前面学过的很多工具，都会在这个阶段被综合运用。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100442872.png" alt="image-20250720100442872"></p><ul><li>我会用<strong>用户访谈</strong>来明确角色、场景和目的。</li><li>我会用<strong>流程图</strong>来梳理和表达流程。</li><li>我会用<strong>结构图</strong>（特别是功能结构图）来整理我的功能清单。</li></ul><hr><h2 id="5-2-原型的概念及分类"><a href="#5-2-原型的概念及分类" class="headerlink" title="5.2 原型的概念及分类"></a>5.2 原型的概念及分类</h2><p>对我来说，原型是连接“需求文档”与“最终产品”之间最重要的一座桥梁。它是产品想法的第一次可视化、具象化的表达。</p><h3 id="5-2-1-学习目标"><a href="#5-2-1-学习目标" class="headerlink" title="5.2.1 学习目标"></a>5.2.1 学习目标</h3><p>在本节中，我的目标是带大家清晰地理解原型的不同“保真度”（Fidelity）的概念。我们将学习区分草图、低保真原型和高保真原型的差异，以及我会在什么样的情况下，选择使用哪一种原型。</p><h3 id="5-2-2-原型的概念及分类"><a href="#5-2-2-原型的概念及分类" class="headerlink" title="5.2.2 原型的概念及分类"></a>5.2.2 原型的概念及分类</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100747254.png" alt="image-20250720100747254"></p><p>我给原型的定义是：<strong>用线条、图形绘制出的产品框架，也称线框图，是需求和功能的具体化表象。</strong></p><p>在我的工作中，我从不会把原型看作是“一个东西”，而是根据项目的不同阶段和沟通目的，把它分为三种不同的类型。</p><h4 id="1-草图原型-Sketch"><a href="#1-草图原型-Sketch" class="headerlink" title="1. 草图原型 (Sketch)"></a>1. 草图原型 (Sketch)</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100813975.png" alt="image-20250720100813975"></p><p>这是我进行产品设计的第一步，也是最快速、最低成本的一种方式。</p><ul><li><strong>特点</strong>：顾名思义，它就是用笔和纸（或者在白板上）随手画出的草稿。我画草图时，<strong>核心是梳理逻辑框架和页面流程</strong>，完全不讲究排版、对齐和美观，也不需要表达出所有的页面元素。</li><li><strong>我的适用场景</strong>：我通常在个人进行<strong>方案构思</strong>的阶段，或者在<strong>团队内部进行头脑风暴</strong>时，大量使用草图。它的使命就是快速表达、快速讨论、快速迭代，画完就可以随时扔掉，没有任何心理负担。</li></ul><h4 id="2-低保真原型-Low-fidelity-Prototype"><a href="#2-低保真原型-Low-fidelity-Prototype" class="headerlink" title="2. 低保真原型 (Low-fidelity Prototype)"></a>2. 低保真原型 (Low-fidelity Prototype)</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100947720.png" alt="image-20250720100947720"></p><p>当我的思路通过草图基本确定后，我就会使用Axure、墨刀等专业工具，来绘制正式的<strong>低保真原型</strong>。这在我的日常工作中，是产出最多、也最重要的一类原型。</p><ul><li><strong>特点</strong>：它要求<strong>绘图整齐、布局规范</strong>。我通常只使用黑、白、灰三种颜色，用简单的线框和色块来表示图片、文字和各类组件。虽然它看起来很朴素，但它必须<strong>完整、准确地表达出产品方案</strong>，页面上所有的功能、按钮、文案、跳转关系都必须清晰无误。</li><li><strong>我的适用场景</strong>：低保真原型是我用来进行<strong>正式方案交付</strong>的“文档”。我会用它来召开<strong>需求评审会</strong>，并把它作为最终交付给开发和测试工程师的<strong>研发依据</strong>。它剥离了所有视觉干扰，让大家都能聚焦在功能和流程本身。</li></ul><h4 id="3-高保真原型-High-fidelity-Prototype"><a href="#3-高保真原型-High-fidelity-Prototype" class="headerlink" title="3. 高保真原型 (High-fidelity Prototype)"></a>3. 高保真原型 (High-fidelity Prototype)</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720101038793.png" alt="image-20250720101038793"></p><p>这是保真度最高的原型，它在视觉上已经和最终的线上产品非常接近了。</p><ul><li><strong>特点</strong>：它不仅<strong>绘图规范、排版求真</strong>，还包含了丰富的视觉元素，如配色、图标、字体、图片等。更重要的是，它通常是<strong>可以交互的</strong>，用户可以像使用真实App一样在上面点击、跳转，来模拟真实的使用体验。</li><li><strong>我的适用场景</strong>：因为制作成本很高，我只在特定的场景下才会制作高保真原型。比如，需要向老板或投资人进行<strong>路演宣传</strong>时；或者，在产品上线前，需要进行**用户体验测试（Usability Testing）**时；以及，有些公司的管理流程，要求在开发前必须有高保真原型用于最终决策。</li></ul><hr><h2 id="5-3-原型绘制工具"><a href="#5-3-原型绘制工具" class="headerlink" title="5.3 原型绘制工具"></a>5.3 原型绘制工具</h2><h3 id="5-3-1-学习目标"><a href="#5-3-1-学习目标" class="headerlink" title="5.3.1 学习目标"></a>5.3.1 学习目标</h3><p>我的目标是带大家熟悉一款现代化的原型工具的核心使用逻辑。我们将了解原型工具的界面通常是如何分布的，并掌握那些最常用的基础元件（也就是我们画原型时的“砖块”）应该在什么场景下使用。</p><h3 id="5-3-2-原型绘制工具介绍及作用"><a href="#5-3-2-原型绘制工具介绍及作用" class="headerlink" title="5.3.2 原型绘制工具介绍及作用"></a>5.3.2 原型绘制工具介绍及作用</h3><p>在上一节，我们明确了原型有草图、低保真、高保真之分。要绘制出规范的低保真和高保真原型，我们就必须借助专业的工具。这些工具能帮助我们高效地搭建页面结构、添加交互，并方便地进行分享和评审。</p><h3 id="5-3-3-常用原型绘制工具"><a href="#5-3-3-常用原型绘制工具" class="headerlink" title="5.3.3 常用原型绘制工具"></a>5.3.3 常用原型绘制工具</h3><p>正如我们之前讨论并达成共识的，在众多工具中，我个人非常推荐像 <strong>墨刀 (MockingBot)</strong> 这样集设计、原型、协作为一体的在线平台。它功能强大、上手简单，非常适合我们当前的学习和未来的团队协作。</p><p><strong>接下来的内容，我会以通用原型工具的核心逻辑进行讲解，其中的概念和操作，您都可以在我们选定的“墨刀”中找到并熟练应用。</strong></p><h3 id="5-3-4-原型工具的核心工作区"><a href="#5-3-4-原型工具的核心工作区" class="headerlink" title="5.3.4 原型工具的核心工作区"></a>5.3.4 原型工具的核心工作区</h3><p>无论我们使用哪款工具，其主界面通常都由几个核心的“工作区”构成。我将这些区域的功能总结如下，这能帮助我们快速熟悉任何一款新工具的布局。</p><p><strong>菜单与工具栏 (Menu &amp; Toolbar)</strong> 通常在界面的最上方。这里集成了软件的通用功能，比如文件操作（新建、保存、导出）、常用工具（选择、放大、缩小）等。</p><p><strong>页面管理区 (Page Management Area)</strong> 通常在左侧。这是我们整个项目的“目录树”，我在这里管理原型的所有页面，可以进行新增、删除、重命名和调整层级。</p><p><strong>元件库 (Widget/Component Library)</strong> 这是我们的“工具箱”和“素材库”，通常也在左侧。里面包含了我们绘制原型需要的所有“砖块”，如按钮、文本框、图片等，我只需要把它们拖拽到画布上即可使用。</p><p><strong>画布 (Canvas)</strong> 这是界面中心最大的一块区域，是我们的“画板”。我们所有的设计工作都在这里完成。</p><p><strong>检视区 (Inspector Panel)</strong> 通常在右侧。这是我用来精细调整元件的“属性面板”。当我选中画布上的任何一个元件时，都可以在这里修改它的尺寸、颜色、字体、边框，以及为它添加交互效果。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720101923527.png" alt="image-20250720101923527"></p><p><strong>概要/图层区 (Outline/Layers Area)</strong> 这个区域会以列表的形式，显示出当前画布上所有的元件及其层级关系。当页面变得复杂、元件相互重叠时，我通过这里可以非常方便地选中和管理它们。</p><p><strong>母版/组件区 (Masters/Components Area)</strong> 这是一个进阶但非常有用的功能。对于那些需要在多个页面重复使用的元素（比如导航栏、页脚），我会把它们创建为“母版”或“公共组件”。这样，我只需要修改一次母版，所有引用了它的页面都会同步更新，极大地提升了效率。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102108869.png" alt="image-20250720102108869"></p><h3 id="5-3-5-常见元件的使用场景"><a href="#5-3-5-常见元件的使用场景" class="headerlink" title="5.3.5 常见元件的使用场景"></a>5.3.5 常见元件的使用场景</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102252484.png" alt="image-20250720102252484"></p><p>掌握了工作区布局后，我们就要来认识一下“元件库”里那些最常用的“砖块”了。熟悉它们各自的用途，是画好原型的基础。</p><table><thead><tr><th align="left">元件 (Widget)</th><th align="left">我的使用场景说明</th></tr></thead><tbody><tr><td align="left"><strong>矩形/图片/占位符</strong></td><td align="left">这是我用来搭建页面基本骨架的“积木”。我用它们来快速划分页面区域、表示图片或Banner等内容占位。</td></tr><tr><td align="left"><strong>按钮 (各类)</strong></td><td align="left">用于触发核心操作，是用户与系统交互最直接、最重要的途径。比如“登录”、“提交”、“购买”等。</td></tr><tr><td align="left"><strong>标题/文本</strong></td><td align="left">用于构建页面的信息层级，清晰地传达各类文字内容，是页面的“血肉”。</td></tr><tr><td align="left"><strong>文本框/文本域</strong></td><td align="left">当需要用户<strong>输入</strong>单行或多行文字时使用。比如：用户名输入框、搜索框、评论输入区。</td></tr><tr><td align="left"><strong>下拉列表/单选/复选</strong></td><td align="left">当需要用户从一组<strong>固定的选项</strong>中进行选择时使用。单选只能选一项，复选可以选多项。</td></tr><tr><td align="left"><strong>表格/列表</strong></td><td align="left">用于结构化地、清晰地<strong>展示大量数据</strong>或信息。比如后台管理系统的数据列表。</td></tr><tr><td align="left"><strong><code>热区</code></strong></td><td align="left">这是一个“隐形”的矩形。当我想让一张图片或一组元素实现整体点击跳转时，我就会在上面覆盖一个热区来添加交互，它本身在预览时是看不见的。</td></tr></tbody></table><hr><h2 id="5-4-原型设计规范"><a href="#5-4-原型设计规范" class="headerlink" title="5.4 原型设计规范"></a>5.4 原型设计规范</h2><p>在我看来，画原型绝不仅仅是把各种元件拖到画布上就完事了。为了让我的原型图清晰、专业、具备可交付性，我必须遵循一套严格的<strong>设计规范</strong>。</p><p>这套规范，不是为了限制我们的创意，恰恰相反，它是为了<strong>提升我们整个团队的沟通效率</strong>。一个遵循规范的原型，就像一篇字迹工整、标点清晰的文章，能让读它的人（设计师、开发、测试）一目了然。</p><h3 id="5-4-1-学习目标"><a href="#5-4-1-学习目标" class="headerlink" title="5.4.1 学习目标"></a>5.4.1 学习目标</h3><p>在本节中，我的目标是带大家掌握我绘制原型时所遵循的几项基本规范。我们将学习Web端和移动端的标准尺寸、常见的页面结构，以及能让你的原型图专业度瞬间提升的五大注意事项。</p><h3 id="5-4-2-尺寸规范"><a href="#5-4-2-尺寸规范" class="headerlink" title="5.4.2 尺寸规范"></a>5.4.2 尺寸规范</h3><p>在我开始绘制任何页面之前，我首先要确定的，就是我的“画板”尺寸。</p><h4 id="1-Web端尺寸规范"><a href="#1-Web端尺寸规范" class="headerlink" title="1. Web端尺寸规范"></a>1. Web端尺寸规范</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102904090.png" alt="image-20250720102904090"></p><p>对于Web端的网页原型，现在主流的显示器分辨率是1920*1080。因此，我的画布宽度通常会设置为1920px或更高。</p><p>但更重要的一个概念是“<strong>版心</strong>”。版心指的是网页上承载核心内容的有效显示区域。为了保证在不同尺寸的宽屏显示器上，内容都清晰易读、不会过分拉伸，我通常会将<strong>版心的宽度控制在1000px到1200px之间</strong>，并让它在页面上水平居中。</p><h4 id="2-移动端尺寸规范"><a href="#2-移动端尺寸规范" class="headerlink" title="2. 移动端尺寸规范"></a>2. 移动端尺寸规范</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103150325.png" alt="image-20250720103150325"></p><p>对于移动端的App原型，为了保持所有页面的一致性，我会选择一个基准尺寸来作图。目前，我以及行业内最通用的低保真原型尺寸，是基于iPhone 6/7/8的逻辑分辨率：<strong>375 x 667 px</strong>。</p><p>在这个基准尺寸内，我还对几个系统级的区域高度，严格遵守规范：</p><ul><li><strong>状态栏 (Status Bar)</strong>：就是手机最顶部显示信号、时间、电量的那一条。它的标准高度是 <strong>20px</strong>。</li><li><strong>导航栏 (Navigation Bar)</strong>：是页面顶部的、包含页面标题和返回按钮的区域。它的标准高度是 <strong>44px</strong>。</li><li><strong>标签栏 (Tab Bar)</strong>：是App底部的主菜单导航。它的标准高度是 <strong>49px</strong>。</li></ul><p>从一开始就遵循这些尺寸规范，能让我的原型图显得非常专业，也便于后续UI设计师进行视觉稿的还原。</p><h3 id="5-4-3-结构规范"><a href="#5-4-3-结构规范" class="headerlink" title="5.4.3 结构规范"></a>5.4.3 结构规范</h3><p>尺寸确定后，我会思考页面的整体布局结构。</p><ul><li><strong>Web端</strong>：最常见的两种结构是<strong>左右布局</strong>（左侧为导航，右侧为内容区，常见于后台管理系统）和<strong>居中布局</strong>（导航和内容区都在页面中心，常见于官网、博客等）。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103106633.png" alt="image-20250720103106633"></p><ul><li><strong>移动端</strong>：一个典型的App页面，其结构通常由上至下由“<strong>状态栏 + 导航栏 + 内容区 + 标签栏</strong>”这几个固定的区块构成。熟悉这些通用结构，能帮我快速、规范地搭建页面。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103247946.png" alt="image-20250720103247946"></p><h3 id="5-4-4-原型设计规范注意事项"><a href="#5-4-4-原型设计规范注意事项" class="headerlink" title="5.4.4 原型设计规范注意事项"></a>5.4.4 原型设计规范注意事项</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103507688.png" alt="image-20250720103507688"></p><p>最后，也是最重要的，我总结了我个人在绘制原型时，一定会遵守的“五大黄金法则”。做好这五点，你的原型图就能立刻和“业余”拉开差距。</p><ol><li><strong>页面结构</strong>：在原型工具中，我会用文件夹和清晰的命名，来组织我的页面层级，让整个项目的结构一目了然。</li><li><strong>框架比例</strong>：我会先定好页面的基础布局和比例，并在所有页面中保持一致，这能带来稳定、舒适的视觉感受。</li><li><strong>间距一致</strong>：这是专业性的关键体现。我会确保元素与元素之间的“间距”是有规律且统一的。比如，卡片与卡片的间距是16px，那在所有地方都应该是16px。</li><li><strong>位置对齐</strong>：我要求自己做到“像素眼”，借助工具的对齐功能，确保页面上所有的元素，要么左对齐，要么居中对齐，要么右对齐。绝不允许出现肉眼可见的错位。</li><li><strong>元件大小</strong>：相同类型的元件，尺寸必须保持一致。比如，所有主要按钮的高度都是44px，所有正文的字号都是14px。这能让界面看起来更和谐、更具秩序感。</li></ol><h3 id="5-4-5-原型设计规范小结"><a href="#5-4-5-原型设计规范小结" class="headerlink" title="5.4.5 原型设计规范小结"></a>5.4.5 原型设计规范小结</h3><p>我将原型设计的核心规范，总结为下面这张自检表：</p><table><thead><tr><th align="left"><strong>规范维度</strong></th><th align="left"><strong>我的核心原则</strong></th></tr></thead><tbody><tr><td align="left"><strong>尺寸 (Size)</strong></td><td align="left">Web端关注<strong>1200px版心</strong>，移动端以<strong>375x667</strong>为基准。</td></tr><tr><td align="left"><strong>结构 (Structure)</strong></td><td align="left">采用<strong>通用布局</strong>（如Web居中布局，App上下导航结构）。</td></tr><tr><td align="left"><strong>注意事项</strong></td><td align="left"><strong>对齐、间距、大小、比例、结构</strong>，五大要素在整个原型中，必须保持高度<strong>一致性</strong>。</td></tr></tbody></table><hr><h2 id="5-5-墨刀制作基础交互"><a href="#5-5-墨刀制作基础交互" class="headerlink" title="5.5 墨刀制作基础交互"></a>5.5 墨刀制作基础交互</h2><p>一个只会展示、不能点击的原型，就像一张没有灵魂的皮囊。而<strong>交互</strong>，就是我们为这具皮囊注入灵魂的过程。它能把一张张孤立的页面，串联成一个完整、可体验的产品故事。</p><p>在这一节，我将带大家学习交互设计的基本逻辑，并掌握如何使用我们选定的工具——<strong>墨刀 (MockingBot)</strong>，来制作几种最常见、最核心的交互效果。</p><h3 id="5-5-1-学习目标"><a href="#5-5-1-学习目标" class="headerlink" title="5.5.1 学习目标"></a>5.5.1 学习目标</h3><p>我的目标是，让我们掌握交互设计的核心公式，并能熟练运用墨刀，独立制作出页面跳转、弹窗、悬浮提示和轮播图这四种基础但至关重要的交互效果。</p><h3 id="5-5-2-什么是交互"><a href="#5-5-2-什么是交互" class="headerlink" title="5.5.2 什么是交互"></a>5.5.2 什么是交互</h3><p>我理解的“交互”，就是<strong>用户与产品之间的一场对话</strong>。用户通过点击、滑动、输入等行为“说话”，而产品则通过页面变化、动画、提示等方式来“回应”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110600853.png" alt="image-20250720110600853"></p><h3 id="5-5-3-什么是交互设计"><a href="#5-5-3-什么是交互设计" class="headerlink" title="5.5.3 什么是交互设计"></a>5.5.3 什么是交互设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110625509.png" alt="image-20250720110625509"></p><p>那么，“交互设计”，就是我们作为产品经理，去<strong>预设这场对话的规则和剧本</strong>。</p><p>在墨刀这样的原型工具里，这个剧本的创作遵循着一个万能公式，这也是交互设计的核心：<br><strong>交互 = 事件 (Event) + 动作 (Action)</strong></p><ul><li><strong>事件</strong>：就是“<strong>当用户做什么的时候</strong>”。这是触发器。比如：<code>当用户单击时</code>、<code>当鼠标移入时</code>、<code>当页面加载时</code>。</li><li><strong>动作</strong>：就是“<strong>产品应该发生什么变化</strong>”。这是响应。比如：<code>链接到某个页面</code>、<code>显示/隐藏某个元素</code>、<code>改变某个元件的状态</code>。</li></ul><p>我们所有的交互设计，都是围绕着“在什么事件下，执行什么动作”来展开的。</p><h3 id="5-5-4-常见交互设计"><a href="#5-5-4-常见交互设计" class="headerlink" title="5.5.4 常见交互设计"></a>5.5.4 常见交互设计</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110650638.png" alt="image-20250720110650638"></p><p>掌握了“事件+动作”这个核心公式，我们就可以组合出千变万化的交互。以下是我在工作中最高频使用的四种。</p><ol><li><strong>跳转</strong>：这是最基础的交互，它将页面串联起来。在墨刀里，我选中一个按钮，为它添加一个“<strong>单击</strong>”的<strong>事件</strong>，再选择“<strong>链接到页面</strong>”这个<strong>动作</strong>，并指定目标页面即可。</li><li><strong>显示/隐藏</strong>：常用于制作弹窗和下拉菜单。我先将要弹出的内容（比如一个弹窗）设置为默认隐藏。然后给一个触发按钮添加“<strong>单击</strong>”<strong>事件</strong>，并选择“<strong>显示/隐藏</strong>”<strong>动作</strong>，作用于那个隐藏的弹窗。</li><li><strong>悬浮显示</strong>：常用于制作提示信息（Tooltip）。我会给目标元件添加“<strong>鼠标移入</strong>”<strong>事件</strong>，触发“<strong>显示</strong>”某个提示框的<strong>动作</strong>；同时再添加一个“<strong>鼠标移出</strong>”<strong>事件</strong>，触发“<strong>隐藏</strong>”这个提示框的<strong>动作</strong>。</li><li><strong>动态面板/轮播</strong>：用于制作轮播图等效果。在墨刀里，这个交互被简化了。我可以直接使用它自带的“<strong>轮播</strong>”组件，把几张图片放进去，它就能自动实现切换效果。其背后的逻辑，就是通过“<strong>延时</strong>”这个<strong>事件</strong>，来触发“<strong>切换到下一状态</strong>”的<strong>动作</strong>。</li></ol><h3 id="5-5-5-墨刀制作基础交互案例展示"><a href="#5-5-5-墨刀制作基础交互案例展示" class="headerlink" title="5.5.5 墨刀制作基础交互案例展示"></a>5.5.5 墨刀制作基础交互案例展示</h3><p>我们来看这四种交互在真实场景下的应用，我已经预设做好了两个模板，分别为微信的原型图以及对应的聊天详情，如下：</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720123723714.png" alt="image-20250720123723714"></p><ol><li><strong>跳转页面案例</strong><br>就像微信的聊天列表，当我要实现点击某个好友，就进入和他聊天的页面时，我就会为列表里的每一项，都添加一个“单击”事件，并分别链接到对应的聊天页面。</li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720123838731.png" alt="image-20250720123838731"></p><p><strong>实现效果如下：</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/PixPin_2025-07-20_12-39-15.webp"></p><ol start="2"><li><p><strong>弹框提示案例</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720125649096.png" alt="image-20250720125649096"></p><p>当用户点击某个按钮时，为了响应他们，我需要弹出一个确认框。这个确认框，我会在墨刀里提前画好并设置为隐藏。然后给对应按钮添加“单击”事件，动作为“显示”这个确认框。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/PixPin_2025-07-20_12-57-24.webp" alt="img"></p></li><li><p><strong>悬浮显示案例</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/e0990e1c-08c9-42c9-81a6-443e3b2ef0cc.png" alt="img"><br>当鼠标移到一个被缩略的标题上，我希望显示完整的标题。我就会做一个隐藏的、包含完整标题的文本框，然后通过“鼠标移入/移出”事件，来控制它的显示和隐藏。</p></li></ol><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/23264.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/23264.html&quot;)">产品经理入门（五）：第五章：产品设计与原型制作</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/23264.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=产品经理入门（五）：第五章：产品设计与原型制作&amp;url=https://prorise666.site/posts/23264.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/13237.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理入门（四）：第四章：流程图与结构图</div></div></a></div><div class="next-post pull-right"><a href="/posts/8024.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理入门（六）：第六章：产品需求文档（PRD）撰写</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理入门（五）：第五章：产品设计与原型制作",date:"2025-07-20 20:13:45",updated:"2025-07-21 14:52:21",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第五章：产品设计与原型制作\n\n在前面的章节里，我们投入了大量精力去“听”和“想”，我们学会了如何收集、分析、管理需求，这些都属于“**问题域**”的范畴——即，**我们应该解决什么问题**。\n\n从这一章开始，我们将进入“**解决方案域**”——即，**我们应该如何设计产品，来优雅地解决这些问题**。我会带大家走完从梳理设计思路，到最终绘制出可交互原型的全过程。\n\n## 5.1 产品设计思路\n\n![image-20250720095709353](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720095709353.png)\n\n在我正式开始画任何一个线框图（Wireframe）之前，我的脑海里必须有一套清晰、结构化的设计思路。这能确保我设计出的功能，是源于真实的用户场景，并且逻辑是通顺的。\n\n我的这套思路，可以用一个公式来表达：**在明确的【角色】和【场景】下，为了达成用户的【目的】，他需要走通一个怎样的【流程】，而我们需要提供什么样的【功能】来支撑这个流程。**\n\n下面，我们就用一个完整的案例，来贯穿这套设计思路。\n\n### 5.1.2 案例：直播间需求分析\n\n我们机构的某个毕业学员 P小M 入职了一家做在线英语培训的公司，公司有很多外籍教师，学员大都为中国学生。目前公司的产品经理在每周对需求池当中的原始需求进行整理分析时，发现之前有个叫Zoe的外籍老师上周提出了如下需求：\n**“希望可以在上课时在网页版的直播间里可以打字，进行答疑，并且在直播课里最好提供举手、邀请某人语音的功能”**\n当在公司当中遇到了这样的一个需求，你会怎样去考虑呢？\n\n### 5.1.3 产品设计流程\n\n我会严格按照“角色 → 场景 → 目的 → 流程 → 功能”这五个步骤，来一步步地推导出我的产品方案。\n\n#### 1. 角色 (Role)\n\n首先，我需要明确，这个场景下，我的核心用户是谁？\n* **Zoe**：外籍老师，她的一个关键特征是**不认识中文**。\n* **直播间学员**：中国学员，他们的特征是需要在直播中与老师互动。\n\n#### 2. 场景 (Scene)\n\n![image-20250720095921040](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720095921040.png)\n\n这两个角色，在“直播间上课”这个大场景下，会发生哪些具体的交互子场景？\n* **子场景1：老师答疑**。老师在讲课过程中，学员随时会产生疑问，需要老师解答。\n* **子场景2：点学员回答问题**。老师为了增强互动，需要主动挑选一位学员来回答问题。\n* **其他场景**：比如老师需要在直播间内布置作业等。\n\n#### 3. 目的 (Goal)\n\n![image-20250720100201566](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100201566.png)\n\n在这些具体的子场景下，各个角色的核心目的是什么？\n* **老师答疑场景的目的**：方便老师能**及时地**对学员提出的问题进行答疑。\n* **点学员回答问题场景的目的**：方便**不认识中文昵称**的外籍老师，能**方便地**挑选学生回答问题。\n\n#### 4. 流程 (Flow)\n\n![image-20250720100036943](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100036943.png)\n\n为了达成上述目的，一个理想化的操作流程是怎样的？\n* **老师答疑的流程**：老师讲课 → 学员产生疑问 → 学员通过某个方式提出问题 → 老师看到问题并解答。\n* **点学员回答问题的流程**：老师想提问 → 老师通过某个方式主动挑选学生 → 被选中的学生通过某个方式回答问题。\n\n#### 5. 功能 (Function)\n\n![image-20250720100113753](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100113753.png)\n\n最后，也是最关键的一步：为了支撑上述流程的顺畅运转，我们需要提供哪些核心功能？\n* **支撑“老师答疑”流程**：最直接的功能就是**提供一个聊天区**，让学生和老师都可以用文字进行实时的提问和回答。\n* **支撑“点学员回答问题”流程**：\n    1.  针对“老师方便挑选”：我们可以**提供一个“举手”功能**，想回答问题的学生可以“举手”，老师就能从举手的学生里选。\n    2.  针对“学生方便回答”：我们可以**提供一个“拉上麦”功能**，老师可以直接点击举手学生的头像，邀请他上麦进行语音回答。\n\n到此为止，我们就通过一套严谨的思路，把一个模糊的需求，推导出了三个具体、可执行的功能点：**聊天区、举手、拉上麦**。\n\n---\n\n### 5.1.4 功能清单\n\n![image-20250720100327337](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100327337.png)\n\n当我通过上述流程推导出多个功能点后，我会把它们整理成一份**“功能清单（Function List）”**。这份清单详细地列出了为了满足本次产品目标，我们需要开发的所有功能模块和子功能。它是我们后续进行原型设计和与开发团队沟通的基础。\n\n### 5.1.5 功能清单与需求池的区别\n\n我需要强调一下“功能清单”和我们之前提过的“需求池”的区别。\n\n| **对比维度** | **需求池 (Requirement Pool)** | **功能清单 (Function List)** |\n| :--- | :--- | :--- |\n| **内容** | **未经处理的“原始需求”集合**。包含了各种想法、问题、建议，是发散的。 | **经过分析和设计后，得出的“产品解决方案”**。是明确、收敛、可执行的功能项。 |\n| **阶段** | 处于**“问题域”**，是我们分析的起点。 | 处于**“解决方案域”**，是我们设计和开发的起点。 |\n\n简单来说，**需求池是“原材料仓库”，而功能清单是“加工图纸”**。\n\n### 5.1.6 产品设计思路工具运用方式\n\n我们前面学过的很多工具，都会在这个阶段被综合运用。\n\n![image-20250720100442872](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100442872.png)\n\n* 我会用**用户访谈**来明确角色、场景和目的。\n* 我会用**流程图**来梳理和表达流程。\n* 我会用**结构图**（特别是功能结构图）来整理我的功能清单。\n\n\n\n\n---\n\n## 5.2 原型的概念及分类\n\n对我来说，原型是连接“需求文档”与“最终产品”之间最重要的一座桥梁。它是产品想法的第一次可视化、具象化的表达。\n\n### 5.2.1 学习目标\n\n在本节中，我的目标是带大家清晰地理解原型的不同“保真度”（Fidelity）的概念。我们将学习区分草图、低保真原型和高保真原型的差异，以及我会在什么样的情况下，选择使用哪一种原型。\n\n### 5.2.2 原型的概念及分类\n\n![image-20250720100747254](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100747254.png)\n\n我给原型的定义是：**用线条、图形绘制出的产品框架，也称线框图，是需求和功能的具体化表象。**\n\n在我的工作中，我从不会把原型看作是“一个东西”，而是根据项目的不同阶段和沟通目的，把它分为三种不同的类型。\n\n#### 1. 草图原型 (Sketch)\n\n![image-20250720100813975](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100813975.png)\n\n这是我进行产品设计的第一步，也是最快速、最低成本的一种方式。\n\n* **特点**：顾名思义，它就是用笔和纸（或者在白板上）随手画出的草稿。我画草图时，**核心是梳理逻辑框架和页面流程**，完全不讲究排版、对齐和美观，也不需要表达出所有的页面元素。\n* **我的适用场景**：我通常在个人进行**方案构思**的阶段，或者在**团队内部进行头脑风暴**时，大量使用草图。它的使命就是快速表达、快速讨论、快速迭代，画完就可以随时扔掉，没有任何心理负担。\n\n#### 2. 低保真原型 (Low-fidelity Prototype)\n\n![image-20250720100947720](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720100947720.png)\n\n当我的思路通过草图基本确定后，我就会使用Axure、墨刀等专业工具，来绘制正式的**低保真原型**。这在我的日常工作中，是产出最多、也最重要的一类原型。\n\n* **特点**：它要求**绘图整齐、布局规范**。我通常只使用黑、白、灰三种颜色，用简单的线框和色块来表示图片、文字和各类组件。虽然它看起来很朴素，但它必须**完整、准确地表达出产品方案**，页面上所有的功能、按钮、文案、跳转关系都必须清晰无误。\n* **我的适用场景**：低保真原型是我用来进行**正式方案交付**的“文档”。我会用它来召开**需求评审会**，并把它作为最终交付给开发和测试工程师的**研发依据**。它剥离了所有视觉干扰，让大家都能聚焦在功能和流程本身。\n\n#### 3. 高保真原型 (High-fidelity Prototype)\n\n![image-20250720101038793](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720101038793.png)\n\n这是保真度最高的原型，它在视觉上已经和最终的线上产品非常接近了。\n\n* **特点**：它不仅**绘图规范、排版求真**，还包含了丰富的视觉元素，如配色、图标、字体、图片等。更重要的是，它通常是**可以交互的**，用户可以像使用真实App一样在上面点击、跳转，来模拟真实的使用体验。\n* **我的适用场景**：因为制作成本很高，我只在特定的场景下才会制作高保真原型。比如，需要向老板或投资人进行**路演宣传**时；或者，在产品上线前，需要进行**用户体验测试（Usability Testing）**时；以及，有些公司的管理流程，要求在开发前必须有高保真原型用于最终决策。\n\n\n\n---\n\n## 5.3 原型绘制工具\n\n### 5.3.1 学习目标\n\n我的目标是带大家熟悉一款现代化的原型工具的核心使用逻辑。我们将了解原型工具的界面通常是如何分布的，并掌握那些最常用的基础元件（也就是我们画原型时的“砖块”）应该在什么场景下使用。\n\n### 5.3.2 原型绘制工具介绍及作用\n\n在上一节，我们明确了原型有草图、低保真、高保真之分。要绘制出规范的低保真和高保真原型，我们就必须借助专业的工具。这些工具能帮助我们高效地搭建页面结构、添加交互，并方便地进行分享和评审。\n\n### 5.3.3 常用原型绘制工具\n\n正如我们之前讨论并达成共识的，在众多工具中，我个人非常推荐像 **墨刀 (MockingBot)** 这样集设计、原型、协作为一体的在线平台。它功能强大、上手简单，非常适合我们当前的学习和未来的团队协作。\n\n**接下来的内容，我会以通用原型工具的核心逻辑进行讲解，其中的概念和操作，您都可以在我们选定的“墨刀”中找到并熟练应用。**\n\n### 5.3.4 原型工具的核心工作区\n\n无论我们使用哪款工具，其主界面通常都由几个核心的“工作区”构成。我将这些区域的功能总结如下，这能帮助我们快速熟悉任何一款新工具的布局。\n\n**菜单与工具栏 (Menu & Toolbar)** 通常在界面的最上方。这里集成了软件的通用功能，比如文件操作（新建、保存、导出）、常用工具（选择、放大、缩小）等。\n\n**页面管理区 (Page Management Area)** 通常在左侧。这是我们整个项目的“目录树”，我在这里管理原型的所有页面，可以进行新增、删除、重命名和调整层级。\n\n**元件库 (Widget/Component Library)** 这是我们的“工具箱”和“素材库”，通常也在左侧。里面包含了我们绘制原型需要的所有“砖块”，如按钮、文本框、图片等，我只需要把它们拖拽到画布上即可使用。\n\n**画布 (Canvas)** 这是界面中心最大的一块区域，是我们的“画板”。我们所有的设计工作都在这里完成。\n\n**检视区 (Inspector Panel)** 通常在右侧。这是我用来精细调整元件的“属性面板”。当我选中画布上的任何一个元件时，都可以在这里修改它的尺寸、颜色、字体、边框，以及为它添加交互效果。\n\n![image-20250720101923527](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720101923527.png)\n\n**概要/图层区 (Outline/Layers Area)** 这个区域会以列表的形式，显示出当前画布上所有的元件及其层级关系。当页面变得复杂、元件相互重叠时，我通过这里可以非常方便地选中和管理它们。\n\n**母版/组件区 (Masters/Components Area)** 这是一个进阶但非常有用的功能。对于那些需要在多个页面重复使用的元素（比如导航栏、页脚），我会把它们创建为“母版”或“公共组件”。这样，我只需要修改一次母版，所有引用了它的页面都会同步更新，极大地提升了效率。\n\n![image-20250720102108869](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102108869.png)\n\n\n\n### 5.3.5 常见元件的使用场景\n\n![image-20250720102252484](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102252484.png)\n\n掌握了工作区布局后，我们就要来认识一下“元件库”里那些最常用的“砖块”了。熟悉它们各自的用途，是画好原型的基础。\n\n| 元件 (Widget) | 我的使用场景说明 |\n| :--- | :--- |\n| **矩形/图片/占位符** | 这是我用来搭建页面基本骨架的“积木”。我用它们来快速划分页面区域、表示图片或Banner等内容占位。 |\n| **按钮 (各类)** | 用于触发核心操作，是用户与系统交互最直接、最重要的途径。比如“登录”、“提交”、“购买”等。 |\n| **标题/文本** | 用于构建页面的信息层级，清晰地传达各类文字内容，是页面的“血肉”。 |\n| **文本框/文本域** | 当需要用户**输入**单行或多行文字时使用。比如：用户名输入框、搜索框、评论输入区。 |\n| **下拉列表/单选/复选** | 当需要用户从一组**固定的选项**中进行选择时使用。单选只能选一项，复选可以选多项。 |\n| **表格/列表** | 用于结构化地、清晰地**展示大量数据**或信息。比如后台管理系统的数据列表。 |\n| **`热区`** | 这是一个“隐形”的矩形。当我想让一张图片或一组元素实现整体点击跳转时，我就会在上面覆盖一个热区来添加交互，它本身在预览时是看不见的。 |\n\n\n\n\n\n\n\n\n---\n\n## 5.4 原型设计规范\n\n在我看来，画原型绝不仅仅是把各种元件拖到画布上就完事了。为了让我的原型图清晰、专业、具备可交付性，我必须遵循一套严格的**设计规范**。\n\n这套规范，不是为了限制我们的创意，恰恰相反，它是为了**提升我们整个团队的沟通效率**。一个遵循规范的原型，就像一篇字迹工整、标点清晰的文章，能让读它的人（设计师、开发、测试）一目了然。\n\n### 5.4.1 学习目标\n\n在本节中，我的目标是带大家掌握我绘制原型时所遵循的几项基本规范。我们将学习Web端和移动端的标准尺寸、常见的页面结构，以及能让你的原型图专业度瞬间提升的五大注意事项。\n\n### 5.4.2 尺寸规范\n\n在我开始绘制任何页面之前，我首先要确定的，就是我的“画板”尺寸。\n\n#### 1. Web端尺寸规范\n\n![image-20250720102904090](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720102904090.png)\n\n对于Web端的网页原型，现在主流的显示器分辨率是1920*1080。因此，我的画布宽度通常会设置为1920px或更高。\n\n但更重要的一个概念是“**版心**”。版心指的是网页上承载核心内容的有效显示区域。为了保证在不同尺寸的宽屏显示器上，内容都清晰易读、不会过分拉伸，我通常会将**版心的宽度控制在1000px到1200px之间**，并让它在页面上水平居中。\n\n#### 2. 移动端尺寸规范\n\n![image-20250720103150325](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103150325.png)\n\n对于移动端的App原型，为了保持所有页面的一致性，我会选择一个基准尺寸来作图。目前，我以及行业内最通用的低保真原型尺寸，是基于iPhone 6/7/8的逻辑分辨率：**375 x 667 px**。\n\n在这个基准尺寸内，我还对几个系统级的区域高度，严格遵守规范：\n\n* **状态栏 (Status Bar)**：就是手机最顶部显示信号、时间、电量的那一条。它的标准高度是 **20px**。\n* **导航栏 (Navigation Bar)**：是页面顶部的、包含页面标题和返回按钮的区域。它的标准高度是 **44px**。\n* **标签栏 (Tab Bar)**：是App底部的主菜单导航。它的标准高度是 **49px**。\n\n从一开始就遵循这些尺寸规范，能让我的原型图显得非常专业，也便于后续UI设计师进行视觉稿的还原。\n\n### 5.4.3 结构规范\n\n\n\n尺寸确定后，我会思考页面的整体布局结构。\n\n* **Web端**：最常见的两种结构是**左右布局**（左侧为导航，右侧为内容区，常见于后台管理系统）和**居中布局**（导航和内容区都在页面中心，常见于官网、博客等）。\n\n![image-20250720103106633](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103106633.png)\n\n* **移动端**：一个典型的App页面，其结构通常由上至下由“**状态栏 + 导航栏 + 内容区 + 标签栏**”这几个固定的区块构成。熟悉这些通用结构，能帮我快速、规范地搭建页面。\n\n![image-20250720103247946](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103247946.png)\n\n### 5.4.4 原型设计规范注意事项\n\n![image-20250720103507688](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720103507688.png)\n\n最后，也是最重要的，我总结了我个人在绘制原型时，一定会遵守的“五大黄金法则”。做好这五点，你的原型图就能立刻和“业余”拉开差距。\n\n1.  **页面结构**：在原型工具中，我会用文件夹和清晰的命名，来组织我的页面层级，让整个项目的结构一目了然。\n2.  **框架比例**：我会先定好页面的基础布局和比例，并在所有页面中保持一致，这能带来稳定、舒适的视觉感受。\n3.  **间距一致**：这是专业性的关键体现。我会确保元素与元素之间的“间距”是有规律且统一的。比如，卡片与卡片的间距是16px，那在所有地方都应该是16px。\n4.  **位置对齐**：我要求自己做到“像素眼”，借助工具的对齐功能，确保页面上所有的元素，要么左对齐，要么居中对齐，要么右对齐。绝不允许出现肉眼可见的错位。\n5.  **元件大小**：相同类型的元件，尺寸必须保持一致。比如，所有主要按钮的高度都是44px，所有正文的字号都是14px。这能让界面看起来更和谐、更具秩序感。\n\n### 5.4.5 原型设计规范小结\n\n我将原型设计的核心规范，总结为下面这张自检表：\n\n| **规范维度** | **我的核心原则** |\n| :--- | :--- |\n| **尺寸 (Size)** | Web端关注**1200px版心**，移动端以**375x667**为基准。 |\n| **结构 (Structure)** | 采用**通用布局**（如Web居中布局，App上下导航结构）。 |\n| **注意事项** | **对齐、间距、大小、比例、结构**，五大要素在整个原型中，必须保持高度**一致性**。 |\n\n\n\n\n\n\n---\n\n## 5.5 墨刀制作基础交互\n\n一个只会展示、不能点击的原型，就像一张没有灵魂的皮囊。而**交互**，就是我们为这具皮囊注入灵魂的过程。它能把一张张孤立的页面，串联成一个完整、可体验的产品故事。\n\n在这一节，我将带大家学习交互设计的基本逻辑，并掌握如何使用我们选定的工具——**墨刀 (MockingBot)**，来制作几种最常见、最核心的交互效果。\n\n### 5.5.1 学习目标\n\n我的目标是，让我们掌握交互设计的核心公式，并能熟练运用墨刀，独立制作出页面跳转、弹窗、悬浮提示和轮播图这四种基础但至关重要的交互效果。\n\n### 5.5.2 什么是交互\n\n我理解的“交互”，就是**用户与产品之间的一场对话**。用户通过点击、滑动、输入等行为“说话”，而产品则通过页面变化、动画、提示等方式来“回应”。\n\n![image-20250720110600853](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110600853.png)\n\n### 5.5.3 什么是交互设计\n\n![image-20250720110625509](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110625509.png)\n\n那么，“交互设计”，就是我们作为产品经理，去**预设这场对话的规则和剧本**。\n\n在墨刀这样的原型工具里，这个剧本的创作遵循着一个万能公式，这也是交互设计的核心：\n**交互 = 事件 (Event) + 动作 (Action)**\n\n* **事件**：就是“**当用户做什么的时候**”。这是触发器。比如：`当用户单击时`、`当鼠标移入时`、`当页面加载时`。\n* **动作**：就是“**产品应该发生什么变化**”。这是响应。比如：`链接到某个页面`、`显示/隐藏某个元素`、`改变某个元件的状态`。\n\n我们所有的交互设计，都是围绕着“在什么事件下，执行什么动作”来展开的。\n\n### 5.5.4 常见交互设计\n\n![image-20250720110650638](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720110650638.png)\n\n掌握了“事件+动作”这个核心公式，我们就可以组合出千变万化的交互。以下是我在工作中最高频使用的四种。\n\n1.  **跳转**：这是最基础的交互，它将页面串联起来。在墨刀里，我选中一个按钮，为它添加一个“**单击**”的**事件**，再选择“**链接到页面**”这个**动作**，并指定目标页面即可。\n2.  **显示/隐藏**：常用于制作弹窗和下拉菜单。我先将要弹出的内容（比如一个弹窗）设置为默认隐藏。然后给一个触发按钮添加“**单击**”**事件**，并选择“**显示/隐藏**”**动作**，作用于那个隐藏的弹窗。\n3.  **悬浮显示**：常用于制作提示信息（Tooltip）。我会给目标元件添加“**鼠标移入**”**事件**，触发“**显示**”某个提示框的**动作**；同时再添加一个“**鼠标移出**”**事件**，触发“**隐藏**”这个提示框的**动作**。\n4.  **动态面板/轮播**：用于制作轮播图等效果。在墨刀里，这个交互被简化了。我可以直接使用它自带的“**轮播**”组件，把几张图片放进去，它就能自动实现切换效果。其背后的逻辑，就是通过“**延时**”这个**事件**，来触发“**切换到下一状态**”的**动作**。\n\n### 5.5.5 墨刀制作基础交互案例展示\n\n我们来看这四种交互在真实场景下的应用，我已经预设做好了两个模板，分别为微信的原型图以及对应的聊天详情，如下：\n\n![image-20250720123723714](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720123723714.png)\n\n1.  **跳转页面案例**\n    就像微信的聊天列表，当我要实现点击某个好友，就进入和他聊天的页面时，我就会为列表里的每一项，都添加一个“单击”事件，并分别链接到对应的聊天页面。\n\n![image-20250720123838731](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720123838731.png)\n\n**实现效果如下：**\n\n![](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/PixPin_2025-07-20_12-39-15.webp)\n\n\n\n\n\n\n\n2.  **弹框提示案例**\n  \n    ![image-20250720125649096](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720125649096.png)\n  \n    当用户点击某个按钮时，为了响应他们，我需要弹出一个确认框。这个确认框，我会在墨刀里提前画好并设置为隐藏。然后给对应按钮添加“单击”事件，动作为“显示”这个确认框。\n    \n    ![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/PixPin_2025-07-20_12-57-24.webp)\n\n\n\n1.  **悬浮显示案例**\n    ![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/e0990e1c-08c9-42c9-81a6-443e3b2ef0cc.png)\n    当鼠标移到一个被缩略的标题上，我希望显示完整的标题。我就会做一个隐藏的、包含完整标题的文本框，然后通过“鼠标移入/移出”事件，来控制它的显示和隐藏。\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E7%AB%A0%EF%BC%9A%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E4%B8%8E%E5%8E%9F%E5%9E%8B%E5%88%B6%E4%BD%9C"><span class="toc-number">1.</span> <span class="toc-text">第五章：产品设计与原型制作</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#5-1-%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-number">1.1.</span> <span class="toc-text">5.1 产品设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-2-%E6%A1%88%E4%BE%8B%EF%BC%9A%E7%9B%B4%E6%92%AD%E9%97%B4%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.1.1.</span> <span class="toc-text">5.1.2 案例：直播间需求分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-3-%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%B5%81%E7%A8%8B"><span class="toc-number">1.1.2.</span> <span class="toc-text">5.1.3 产品设计流程</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%A7%92%E8%89%B2-Role"><span class="toc-number">1.1.2.1.</span> <span class="toc-text">1. 角色 (Role)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9C%BA%E6%99%AF-Scene"><span class="toc-number">1.1.2.2.</span> <span class="toc-text">2. 场景 (Scene)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%9B%AE%E7%9A%84-Goal"><span class="toc-number">1.1.2.3.</span> <span class="toc-text">3. 目的 (Goal)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E6%B5%81%E7%A8%8B-Flow"><span class="toc-number">1.1.2.4.</span> <span class="toc-text">4. 流程 (Flow)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-%E5%8A%9F%E8%83%BD-Function"><span class="toc-number">1.1.2.5.</span> <span class="toc-text">5. 功能 (Function)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-4-%E5%8A%9F%E8%83%BD%E6%B8%85%E5%8D%95"><span class="toc-number">1.1.3.</span> <span class="toc-text">5.1.4 功能清单</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-5-%E5%8A%9F%E8%83%BD%E6%B8%85%E5%8D%95%E4%B8%8E%E9%9C%80%E6%B1%82%E6%B1%A0%E7%9A%84%E5%8C%BA%E5%88%AB"><span class="toc-number">1.1.4.</span> <span class="toc-text">5.1.5 功能清单与需求池的区别</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-6-%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF%E5%B7%A5%E5%85%B7%E8%BF%90%E7%94%A8%E6%96%B9%E5%BC%8F"><span class="toc-number">1.1.5.</span> <span class="toc-text">5.1.6 产品设计思路工具运用方式</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-2-%E5%8E%9F%E5%9E%8B%E7%9A%84%E6%A6%82%E5%BF%B5%E5%8F%8A%E5%88%86%E7%B1%BB"><span class="toc-number">1.2.</span> <span class="toc-text">5.2 原型的概念及分类</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.2.1.</span> <span class="toc-text">5.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-2-2-%E5%8E%9F%E5%9E%8B%E7%9A%84%E6%A6%82%E5%BF%B5%E5%8F%8A%E5%88%86%E7%B1%BB"><span class="toc-number">1.2.2.</span> <span class="toc-text">5.2.2 原型的概念及分类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%8D%89%E5%9B%BE%E5%8E%9F%E5%9E%8B-Sketch"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">1. 草图原型 (Sketch)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BD%8E%E4%BF%9D%E7%9C%9F%E5%8E%9F%E5%9E%8B-Low-fidelity-Prototype"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">2. 低保真原型 (Low-fidelity Prototype)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E9%AB%98%E4%BF%9D%E7%9C%9F%E5%8E%9F%E5%9E%8B-High-fidelity-Prototype"><span class="toc-number">1.2.2.3.</span> <span class="toc-text">3. 高保真原型 (High-fidelity Prototype)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-3-%E5%8E%9F%E5%9E%8B%E7%BB%98%E5%88%B6%E5%B7%A5%E5%85%B7"><span class="toc-number">1.3.</span> <span class="toc-text">5.3 原型绘制工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.3.1.</span> <span class="toc-text">5.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-2-%E5%8E%9F%E5%9E%8B%E7%BB%98%E5%88%B6%E5%B7%A5%E5%85%B7%E4%BB%8B%E7%BB%8D%E5%8F%8A%E4%BD%9C%E7%94%A8"><span class="toc-number">1.3.2.</span> <span class="toc-text">5.3.2 原型绘制工具介绍及作用</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-3-%E5%B8%B8%E7%94%A8%E5%8E%9F%E5%9E%8B%E7%BB%98%E5%88%B6%E5%B7%A5%E5%85%B7"><span class="toc-number">1.3.3.</span> <span class="toc-text">5.3.3 常用原型绘制工具</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-4-%E5%8E%9F%E5%9E%8B%E5%B7%A5%E5%85%B7%E7%9A%84%E6%A0%B8%E5%BF%83%E5%B7%A5%E4%BD%9C%E5%8C%BA"><span class="toc-number">1.3.4.</span> <span class="toc-text">5.3.4 原型工具的核心工作区</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-5-%E5%B8%B8%E8%A7%81%E5%85%83%E4%BB%B6%E7%9A%84%E4%BD%BF%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.3.5.</span> <span class="toc-text">5.3.5 常见元件的使用场景</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-4-%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83"><span class="toc-number">1.4.</span> <span class="toc-text">5.4 原型设计规范</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.4.1.</span> <span class="toc-text">5.4.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-2-%E5%B0%BA%E5%AF%B8%E8%A7%84%E8%8C%83"><span class="toc-number">1.4.2.</span> <span class="toc-text">5.4.2 尺寸规范</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-Web%E7%AB%AF%E5%B0%BA%E5%AF%B8%E8%A7%84%E8%8C%83"><span class="toc-number">1.4.2.1.</span> <span class="toc-text">1. Web端尺寸规范</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%A7%BB%E5%8A%A8%E7%AB%AF%E5%B0%BA%E5%AF%B8%E8%A7%84%E8%8C%83"><span class="toc-number">1.4.2.2.</span> <span class="toc-text">2. 移动端尺寸规范</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-3-%E7%BB%93%E6%9E%84%E8%A7%84%E8%8C%83"><span class="toc-number">1.4.3.</span> <span class="toc-text">5.4.3 结构规范</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-4-%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-number">1.4.4.</span> <span class="toc-text">5.4.4 原型设计规范注意事项</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-5-%E5%8E%9F%E5%9E%8B%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83%E5%B0%8F%E7%BB%93"><span class="toc-number">1.4.5.</span> <span class="toc-text">5.4.5 原型设计规范小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#5-5-%E5%A2%A8%E5%88%80%E5%88%B6%E4%BD%9C%E5%9F%BA%E7%A1%80%E4%BA%A4%E4%BA%92"><span class="toc-number">1.5.</span> <span class="toc-text">5.5 墨刀制作基础交互</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.5.1.</span> <span class="toc-text">5.5.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-2-%E4%BB%80%E4%B9%88%E6%98%AF%E4%BA%A4%E4%BA%92"><span class="toc-number">1.5.2.</span> <span class="toc-text">5.5.2 什么是交互</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-3-%E4%BB%80%E4%B9%88%E6%98%AF%E4%BA%A4%E4%BA%92%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.5.3.</span> <span class="toc-text">5.5.3 什么是交互设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-4-%E5%B8%B8%E8%A7%81%E4%BA%A4%E4%BA%92%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.5.4.</span> <span class="toc-text">5.5.4 常见交互设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-5-%E5%A2%A8%E5%88%80%E5%88%B6%E4%BD%9C%E5%9F%BA%E7%A1%80%E4%BA%A4%E4%BA%92%E6%A1%88%E4%BE%8B%E5%B1%95%E7%A4%BA"><span class="toc-number">1.5.5.</span> <span class="toc-text">5.5.5 墨刀制作基础交互案例展示</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>