<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>第三章：活动管理-总价活动 | Prorise的小站</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="第三章：活动管理-总价活动"><meta name="application-name" content="第三章：活动管理-总价活动"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="第三章：活动管理-总价活动"><meta property="og:url" content="https://prorise666.site/posts/27803.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第三章：活动管理-总价活动欢迎来到第三章。在上一章，我们的所有活动都聚焦于“单个商品”的降价。但作为运营，我还有一个更重要的目标：如何让用户一次买得更多？ 这就引出了我们本章的主题——总价活动。这类活动不再关注单个商品的价格，而是着眼于用户的“购物车总价”，通过设置一个“满X元”的门槛，来激励用户为"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp"><meta name="description" content="第三章：活动管理-总价活动欢迎来到第三章。在上一章，我们的所有活动都聚焦于“单个商品”的降价。但作为运营，我还有一个更重要的目标：如何让用户一次买得更多？ 这就引出了我们本章的主题——总价活动。这类活动不再关注单个商品的价格，而是着眼于用户的“购物车总价”，通过设置一个“满X元”的门槛，来激励用户为"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/27803.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"第三章：活动管理-总价活动",postAI:"true",pageFillDescription:"第三章：活动管理-总价活动, 3.1 营销活动, 3.1.1 总价活动-满减活动管理, 3.1.2 总价活动-满赠活动管理, 3.1.3 总价活动-满折活动管理, 3.1.4 总价活动-套装活动, 3.2 营销规则, 3.2.1 规则一：叠加与互斥, 3.2.2 规则二：活动顺序, 3.2.3 规则三：优惠分摊规则（深度解析）, 1. 基础场景：单一总价优惠的分摊, 2. 进阶问题：计算精度与尾差处理, 3. 终极挑战：多商品、多活动、多层级优惠的混合分摊, 3.2.4 规则四：风险与防范, 3.3 营销工具, 3.3.1 抽奖工具, 1. 抽奖工具的需求分析, 2. 抽奖工具的产品设计, 一、 商家端（B端）产品设计, 二、 用户端（C端）产品设计, 3.3.2 优惠券工具, 1.优惠券工具的需求分析, 2.优惠券工具的产品设计, 一、设置基本信息与领取规则, 二、填写使用规则——玩法的核心, 三、 优惠券的管理, 3. 优惠券的逻辑规则, 规则一：叠加与互斥, 规则二：推荐原则, 规则三：分摊规则, 4. 优惠券工具的数据第三章活动管理总价活动欢迎来到第三章在上一章我们的所有活动都聚焦于单个商品的降价但作为运营我还有一个更重要的目标如何让用户一次买得更多这就引出了我们本章的主题总价活动这类活动不再关注单个商品的价格而是着眼于用户的购物车总价通过设置一个满元的门槛来激励用户为了凑单而购买更多商品营销活动我们将要学习的第一个也是最经典的总价活动就是满减总价活动满减活动管理我为什么要设计满减功能其核心的业务诉求正如上图所述是为了提升客单价在日常运营中我会遇到很多类似的场景比如满减订单金额满元减免元满赠订单金额满元就赠送一个小礼物满折订单满件就享受折优惠这些玩法的本质都是在用户下单的最后一步临门一脚通过一个有吸引力的优惠引导他们再多买一件本节我们就来从到地设计出满减这个功能角色与流程满减活动的设计思路依然遵循我们熟悉的框架角色流程功能字段核心角色依然是商家活动的创建者和用户活动的参与者整体流程商家在后台创建满减活动用户在前台浏览商品并参与活动对于用户来说参与的体验必须是无缝且直观的我的设计要点是在商品详情页就要清晰地展示出这个商品正在参与的满减活动比如上图案例中的满减标签这会成为用户把它加入购物车的重要动力而这又分为直接满减与阶梯满减对于阶梯满减来说用户购买的越多优惠越多在结算页当用户选购的商品满足了满减门槛时系统需要自动地计算优惠并清晰地展示出减免的金额给用户带来占到便宜的满足感商家创建满减活动对于商家我设计的创建流程依然是清晰的四步走选择活动类型商家首先在活动中心选择创建满减活动填写基本信息设置活动的名称时间面向人群等设置满减规则这是满减活动的核心也是设计的重点选择活动商品圈定参与本次满减活动的商品范围现在我们把上述流程细化为具体的字段基本信息活动名称活动时间参与人群等这些都是我们之前设计中可复用的标准组件活动规则这是设计的关键与单品活动不同这里的规则是作用于总价的我通常会为商家提供两种优惠类型直接满减最简单的模式例如满元减元阶梯满减更灵活的模式例如满减满减满减通过多个档位进一步刺激用户提高客单价活动商品商家可以选择全部商品都参与也可以指定部分商品参与最终这些字段会构成我们商家后台的创建页面请特别注意优惠类型的设计我通过单选框让商家可以在直接满减和阶梯满减中切换当选择阶梯满减时我还提供了一个号按钮让商家可以动态地增加优惠层级这种设计兼顾了功能的强大性和操作的灵活性满减活动列表当商家创建完活动后他需要一个统一的列表来管理我设计的这张列表除了包含活动名称时间状态等常规字段外还增加了一个非常重要的字段优惠信息在这个字段里我会清晰地展示出这个活动的核心规则如满元减元这能让商家在不点进详情的情况下快速地浏览和识别每一个活动极大地提升了管理效率同时列表页也提供了查看编辑结束等必要的操作入口其背后的状态机逻辑与我们之前设计的完全一致总价活动满赠活动管理在开始设计前我们先来看一个我经常遇到的真实业务场景某个商品库存积压严重直接打折降价怕影响品牌形象怎么办一个非常有效的策略就是把它作为赠品去搭配那些热销的商品这就是满赠活动的核心价值之一它不仅能像满减一样提升客单价还能帮助我们优化库存结构并给用户带来一种意外之喜的超值感我给满赠的定义是在活动时间段内用户购买主商品当订单金额或商品件数满足预设的门槛时即可免费获得指定赠品的营销活动和满减一样我也把它分为两种常见形式直接满赠规则简单直接例如满元赠风扇阶梯满赠设置多个优惠档位例如满元赠风扇满元赠小熊电饭煲以此激励用户冲击更高的消费档次商家创建满赠活动满赠活动的端创建流程与满减活动的主干完全一致我依然采用了我们熟悉的创建四部曲选择活动类型活动基本信息满赠规则设置活动商品选择这体现了我在设计后台系统时的一个重要原则保持操作逻辑的一致性降低用户的学习成本我们把创建流程拆解为具体的字段这个功能的关键在于满赠规则设置和活动商品选择这两步有了全新的内涵现在我们来看这张创建满赠活动的页面原型这是本节功能设计的核心它的上半部分活动信息优惠类型与满减的设计几乎可以完全复用真正的区别在下半部分选择商品主商品这里的商品列表指的是用户必须购买才能享受优惠的主商品商家可以选择全店商品参与也可以指定部分热销商品参与选择赠品这部分是满赠功能设计的灵魂我需要在这里为商家提供另一个商品选择器让他可以从自己的商品库中选择一个或多个商品作为本次活动的赠品我的设计要点是赠品的选择必须灵活商家不仅可以指定赠品还可以设置满元可在赠品或赠品中任选其一把选择权交给用户从而提升活动的吸引力用户端体验一个后台功能设计得再好如果用户在前端无感知那也是失败的所以我必须在端用户端清晰地把优惠信息传达出去商品详情页当一个商品参与了满赠活动我会在其价格旁边增加一个醒目的赠字标签用户点击后可以看到详细的活动规则例如满元即可获赠商品一件结算页当用户的订单满足满赠条件时在结算页的商品清单中我会把赠品作为一个单独的行给展示出来价格标注为元这能给用户带来实实在在的获得感让他清晰地感知到自己享受到的优惠满赠活动管理最后商家创建的所有满赠活动也会进入到我们统一的活动管理列表中这张列表的整体框架是完全复用的我需要做的仅仅是确保优惠信息这一列能够准确地显示出满赠活动的核心规则例如满元赠商品通过这种方式商家可以高效地对进行中或未开始的活动进行统一的管理和后续操作总价活动满折活动管理我们已经有了满减针对订单金额的优惠和满赠针对订单价值的提升现在我需要一个能直接激励用户购买更多件数的工具尤其是在服装图书日用品这类客单价不一定高但用户常常会一次性购买多件的品类中这个工具就显得尤为重要于是满折活动就应运而生了满折即当用户购买指定商品的件数达到预设门槛时即可享受整单相应折扣的优惠例如指定商品任选件即可享受折优惠商家创建满折活动在端设计上我依然沿用了标准化的创建四部曲流程这能确保商家在使用我们后台时有一种统一连贯的操作体验而不需要为每一种新活动都重新学习一遍我们把目光聚焦到这个功能的核心填写活动规则我们直接来看创建满折活动的页面原型它的核心设计全部体现在活动规则这个模块里规则设置这里的规则不再是满元而是满件优惠方式也从减元变成了打折阶梯折扣和满减满赠一样我也为满折设计了阶梯模式商家可以设置满件打折满件打折通过一个更优惠的折扣力度来强力吸引用户再多拿一件从而有效提升订单的商品件数即购物深度和销售总额用户端体验在端为了让用户能清晰地感知到满折优惠我的设计思路和满减是完全一致的活动感知我会在参与活动的商品详情页上用一个醒目的满折标签来吸引用户的注意力优惠计算在结算页当用户购物车中参与活动的商品件数满足了规则后系统会自动计算出折扣金额并在优惠金额处明确展示让用户直观地看到自己省了多少钱满折活动管理在活动管理后台商家也能清晰地看到所有已创建的满折活动在列表的优惠类型这一列我会直观地显示出满件折这样的核心规则便于运营人员进行后续的查看编辑或结束等管理操作通过对列表创建页等核心组件的复用我能用最低的成本最高效地扩展出新的营销玩法到此为止我们已经系统性地掌握了总价活动中的三剑客满减满赠满折它们在端的设计上有很多共通之处但在端的体感和核心运营策略上又各有侧重最后我给你留一个思考题如果我们要做一个更复杂的满返比如订单满元返还元优惠券或积分活动它的端和端产品设计与我们已经学过的这三种相比又会有哪些共同点和差异点呢带着这个问题我们将在后续的课程中继续探索总价活动套装活动在之前的几种总价活动中我们都是设定一个规则门槛让用户自由地选择商品去凑单但很多时候我作为商家希望能更主动地为用户规划好一组合集并给出一个打包优惠价来提升整体销量比如快餐店里汉堡薯条可乐的套餐美妆领域水乳精华的护肤品套装或者服饰店里上衣裤子的搭配组合这些就是我们这节要设计的套装活动套装活动设计思路套装活动的核心是将多个独立的商品打包成一个新的销售单元进行促销它的设计思路依然遵循我们的标准框架关键在于对套装规则的定义在端设计上我将套装规则进一步细分为了两种核心类型以满足商家多样化的营销需求固定套装一个打包好的组合用户必须完整购买不可更改搭配套装提供一定的选择空间例如主商品搭配商品任选其一设计详解固定套装固定套装是最简单最常见的模式它的逻辑就是商品商品商品一个固定的打包价我们来看它的创建页面套装类型商家首先选择固定套装套装价格商家需要为这个打包好的组合设置一个全新的有吸引力的套装价选择商品商家在下方的商品选择器中勾选出所有要包含在这个固定套餐里的商品这种模式的优点是规则清晰用户决策成本低缺点是灵活性较差设计详解搭配套装搭配套装则为商家和用户提供了更高的灵活性它的逻辑更像是主商品区任选一件搭配商品区任选一件优惠组合价在创建页面上它的设计也更为复杂套装类型商家需要选择搭配套装选择主商品商家首先要指定一批主商品选择搭配商品然后再指定一批可供搭配的副商品这种模式非常适用于服饰配件等品类例如我可以设置一个买任意一款手机主商品即可半价换购任意一款手机壳搭配商品的活动这给了用户一定的自主选择权体验更好也更容易促成关联销售用户端体验当一个商品参与了套装活动时我该如何在端把它呈现给用户呢我的方案是在该商品的详情页下方专门开辟一个优惠套餐的区域如上图所示这个区域会清晰地展示出套餐内的所有商品图片名称以及最具吸引力的套餐价并提供一个立即购买套餐的按钮通过这种直观的对比用户能立刻感知到购买套餐的超值之处从而被引导完成购买套装活动管理最后在端的活动管理后台商家可以统一管理所有已创建的套装活动为了便于商家区分我特意在活动列表中增加了一列套装类型通过这一列商家可以一目了然地分清哪些是固定套装哪些是搭配套装从而进行更有针对性的管理和数据分析营销规则我们已经为商家设计了品类丰富的单品活动和总价活动但当这些武器可以被同时使用时一个新的也是更复杂的问题就摆在了我的面前为了解决这个优惠爆炸的问题防止出现混乱和亏损我作为产品经理必须设计一套清晰严谨的营销规则它就像我们营销系统的基本法规定了所有活动之间应该如何协同工作我将这套复杂的规则拆解为四大核心模块接下来我们将逐一攻克规则一叠加与互斥我们先来看一个真实的运营场景新款上市运营同学恨不得把秒杀拼团满减满赠所有优惠都给它加上让它看起来优惠到极致但这样真的可以吗答案是否定的如果没有任何限制多个大力度的单品活动叠加商品价格可能会变成负数因此我必须定义清楚哪些活动之间是互斥的不能同时享受哪些又是可以叠加的可以同时享受要定义规则我首先需要把平台内所有的优惠形式按照性质进行归类我将它们划分为四大类单品活动直接作用于商品本身的优惠如秒杀直降拼团总价活动作用于订单总价的优惠如满减满赠满折抵扣活动用户使用虚拟资产进行抵扣的活动如优惠券积分礼品卡支付活动与支付渠道绑定的优惠如信用卡支付立减理清了分类我就可以制定出上面这张优惠叠加互斥规则表这是我们整个营销系统的交通法规它的核心逻辑可以总结为同类互斥一个商品不能同时参与两个单品活动例如你不能让一个商品既是秒杀价又是拼团价同理一个订单也不能同时满足两个总价活动异类叠加不同类型的活动原则上是可以叠加享受的例如一个商品可以先享受秒杀价单品活动达到门槛后可以再享受满减总价活动结算时还可以用优惠券抵扣活动最后用信用卡支付支付活动再减一点钱规则二活动顺序我们已经知道哪些活动可以一起用了但新的问题又来了先算哪个后算哪个顺序不同结果可能天差地别我的设计原则是模拟用户真实的交易环节定义一条雷打不动的计算链路第一步计算单品活动先算出商品经过秒杀直降等活动后的价格第二步计算总价活动用第一步得出的价格总和去判断是否满足满减满折的门槛第三步计算抵扣活动用第二步得出的价格去使用优惠券积分等进行抵扣第四步计算支付活动用第三步得出的最终应付金额去享受支付渠道的优惠但在这个大原则下还有一个更细致的问题当同一种类型的活动有多个时又该怎么算比如一个订单同时满足满减和满减这里我设计了两种模式供运营人员选择递进式先计算第一个门槛的优惠用优惠后的金额再去判断是否满足下一个门槛这种模式对平台最有利能严格控制成本但计算逻辑复杂平行式所有满足门槛的优惠都基于原始金额进行计算然后全部生效这种模式对用户最友好计算速度快但商家有亏损的风险例如用户买元商品同时享受了满减和满减平行计算下总共优惠了元通过上面这个案例你可以清晰地看到一个元的鼠标在递进式和平行式两种不同规则下最终的成交价是不同的在后台为运营设计这个功能时我必须把这两种模式的选择权交给他们并讲清楚其中的利弊规则三优惠分摊规则深度解析我们必须认识到这个规则的存在是为了解决一个核心的财务问题当一笔享受了总价优惠的订单发生部分退款时如何确保退款金额的计算是公平且准确的以防止平台或商家产生亏损基础场景单一总价优惠的分摊我们从一个最基础也最常见的场景开始如上图所示一个元的订单通过满减的活动用户实际支付了元现在用户需要退掉其中元的商品我们应该退给他元吗绝对不行如果退元就意味着用户用元买到了价值元的和商品享受了满减的优惠这与我们满减的活动规则相悖平台或商家平白无故地亏损了要解决这个问题就必须引入我们分摊规则的第一性原理任何一笔作用于订单整体的优惠都必须按比例分摊到订单内的每一个商品上我制定的核心分摊公式如下商品优惠金额总优惠金额商品金额参与活动商品的价格总和现在我们用这个公式来精确计算商品的退款金额计算商品分摊到的优惠金额商品优惠金额元元元元计算商品应退款金额商品应退款商品原价商品分摊到的优惠金额元元元只有这样我才能确保退款后剩余的两件商品其合计支付金额元与它们应该享受的优惠是匹配的进阶问题计算精度与尾差处理应用这个公式我们可以继续计算出和的应退款金额但是在真实的计算机系统中除法运算常常会导致无限循环小数例如这会带来精度问题如果的优惠金额分别是三者相加可能等于也可能等于或这个微小的误差在海量订单下会累积成巨大的财务漏洞为了确保万无一失我设计了一条尾差处理规则最后一个商品的优惠金额总优惠金额之前所有商品已分摊的优惠金额之和同时为了让计算过程更稳定我还会制定一条工程上的最佳实践按商品金额从小到大进行计算然后将所有的计算尾差都归结到最后一个即金额最大的商品上这能保证无论如何计算一个订单内所有商品分摊的优惠总和绝对等于这笔订单享受的优惠总额一分不多一分不少终极挑战多商品多活动多层级优惠的混合分摊现在我们来挑战一个最复杂的场景它融合了我们前面学到的所有规则这个场景的复杂性在于优惠不再是单一的满减而是包含了单品活动总价活动抵扣活动的多层级优惠要解决这个问题我必须严格遵循我们在上一节定义的活动顺序我们必须再次重申这条计算的生命线单品活动总价活动抵扣活动优惠的计算和分摊必须严格按照这个优先级层层递进现在我们对这个终极案例进行庖丁解牛式的拆解第一步计算单品活动商品参加直降元其优惠后的价格变为元商品不参与单品活动价格仍为元此时用于下一步计算的订单价格基础是元元第二步计算总价活动商品参加满的满减活动其价格变为元商品不参与总价活动价格仍为元此时用于下一步计算的订单价格基础是元元第三步分摊抵扣活动优惠券现在我们需要将这张元的优惠券分摊到和两个商品上用于分摊的商品价格总和为元的折后价元的折后价元商品应分摊的优惠券金额元元元元商品应分摊的优惠券金额元元元应用尾差处理规则第四步得出结论商品总共优惠了元直降元优惠券元商品总共优惠了元满减元优惠券元通过以上严谨的层层递进的规则设计我才能确保无论运营人员配置出多么复杂的优惠组合我的系统都能准确公平安全地计算出最终价格和退款金额守住平台和商家资金安全的生命线这就是分摊规则设计的严肃性和重要性所在规则四风险与防范作为产品经理我不仅要设计功能更要保护平台和商家的利益防止他们因为误操作而造成亏损为此我设计了一套风险防范组合拳低价预警当系统检测到商家设置的优惠力度过大可能导致亏损时例如折后价低于成本价自动弹出醒目的预警提示让商家进行二次确认活动审核对于一些重要的或者新手商家创建的活动我可以设计一个审核流程活动创建后不会立刻生效而是进入待审核状态需要由运营主管或平台管理员审核通过后才能正式上线安全策略为了防止专业的羊毛党通过技术手段刷单我还需要设计一些基础的反作弊策略例如限制同一个地址同一个设备同一个收货地址的参与次数等最后我们总结一下以上就是我设计的营销规则体系它就像一张无形的精密的大网确保了我们整个营销活动系统能够在复杂多变的场景下依然能够公平稳定安全地运行营销工具在第三章的后续部分我们将进入一个更有趣更具互动性的领域营销工具它不再是简单的让利而是通过游戏化的玩法来提升用户的参与度和粘性实现品效合一的营销目标抽奖工具抽奖是一种低成本高回报的互动营销玩法它通过设置有吸引力的奖品来驱动用户完成我们期望的特定行为如每日访问分享拉新等抽奖工具的需求分析在我动手设计具体的产品功能前我必须首先回归原点搞清楚我们为什么要做这个功能我之所以要在系统中增加一个看似和卖货没有直接关系的抽奖功能其核心驱动力来自于商家提升用户活跃度与忠诚度的真实诉求通过上图的需求背景我提炼出抽奖工具需要满足的两大核心业务目标提升老用户粘性通过每日免费抽奖等形式为老用户提供一个持续访问我们或店铺的理由提升日活跃用户促进新用户增长将分享与增加抽奖次数进行绑定激励老用户主动去进行社交分享从而为店铺带来低成本的新流量明确了业务目标后我就需要从最宏观的视角来构思这个工具的完整生态我首先要定义其中的核心角色和整体流程核心角色抽奖工具的生态中主要有三方参与者平台方我作为平台的产品经理负责设计和提供稳定通用的抽奖工具商家是抽奖活动的发起者和成本承担者他们使用我提供的工具来配置活动规则和奖品用户是抽奖活动的最终参与者整体流程整个业务的生命周期是一个清晰的闭环如上图所示平台提供工具商家配置活动用户参与抽奖商家发放奖品用户查看奖品我的产品设计必须确保这个链条上的每一个环节都顺畅无误在宏观流程中用户参与抽奖活动是整个玩法能否成功的关键那么用户的体验旅程应该是怎样的呢为了让用户体验顺畅且富有激励性我为端用户设计了上面这条完整的参与流程闭环我们来一步步拆解这个流程触发抽奖用户进入活动页面点击立即抽奖按钮前置判断系统首先判断用户是否还有抽奖次数次数用完如果次数已用完系统会弹出提示并引导用户去通过分享获得更多抽奖次数这正是我们实现拉新裂变的关键设计执行抽奖如果次数未用完系统则根据后台配置的算法来判断本次抽奖是否中奖结果反馈如果中奖则弹出恭喜中奖的强提示并引导用户去查看奖品信息如果未中奖则弹出谢谢参与的安慰性提示通过对业务目标宏观流程用户旅程的完整分析我就为接下来进行具体的产品设计打下了坚实的基础抽奖工具的产品设计根据我们之前的分析抽奖工具的设计必须同时兼顾商家端端的易用性和灵活性以及用户端端的趣味性和流畅体验我将为你分别进行拆解一商家端端产品设计我们首先来看商家后台的设计我需要为商家提供一个足够强大但操作又不能过于复杂的活动创建流程我设计的商家创建流程依然遵循我们熟悉的四部曲确保了后台操作的一致性接下来我们详细看一下每一步的具体设计设置基本信息这是活动创建的第一步商家需要在这里设置活动的身份信息包括活动名称活动时间活动平台是在内还是页面生效以及活动说明即活动规则的文字描述填写抽奖规则这是抽奖功能设计的灵魂它决定了整个活动的核心玩法在设计这部分功能时我主要思考并解决了上面这两个核心问题抽奖类型我为商家提供了两种模式来回答第一个问题即时抽奖用户抽完立刻知道结果这是最常见的模式能提供即时反馈和刺激非即时抽奖用户参与后需要等待统一的开奖时间例如每周五开奖这种模式适用于需要营造悬念和持续关注度的活动抽奖条件我允许商家设置参与门槛例如用户必须使用积分达到会员等级或者完成订单后才能获得抽奖资格参与次数商家可以灵活控制用户参与的频率是每人每天可抽次还是在整个活动周期内每人一共可抽次分享设置这是实现裂变增长的关键我需要让商家可以配置用户分享活动后可以额外增加次抽奖机会的规则提示文案为了让体验更友好我允许商家自定义各种场景下的提示文案如中奖提示未中奖提示活动未开始提示等选择抽奖奖品在这一步商家需要设置本次活动的奖池为了回答如何控制奖品数量这个问题我要求商家在设置每一个奖品时都必须明确两项核心信息奖品数量和中奖概率系统会根据这两项配置通过抽奖算法来精确控制奖品的发放为了丰富奖品的类型我设计的奖池支持多种奖品形态实物商品优惠券积分通过这种模块化的设计商家就可以非常灵活地配置出具有吸引力的奖品组合二用户端端产品设计当商家在后台配置好活动后端用户看到和体验到的必须是一个有趣流畅的界面抽奖主页面我采用了最经典的九宫格抽奖样式这个页面的核心元素包括抽奖区域九个格子中分布着不同的奖品和谢谢参与的选项抽奖按钮用户点击立即抽奖转盘开始转动当用户次数用尽按钮会变为明天再来或分享获取次数等不可用状态中奖名单页面下方会实时滚动最新的中奖信息营造一种热闹很多人中奖的氛围来激励其他用户参与抽奖结果反馈对于即时抽奖来说及时的结果反馈至关重要中奖立刻弹出强提示的恭喜中奖弹窗告知用户获得了什么奖品未中奖弹出安慰性的祝您下次中奖弹窗并引导用户下次再来我的奖品列表所有用户中奖的记录都会沉淀在我的中奖记录这个页面用户可以在这里清晰地看到自己获得的所有奖品以及每一个奖品的当前状态是待兑换还是已兑换方便进行后续的核销与使用优惠券工具如果说抽奖是提升趣味性和互动性的利器那么优惠券则是我工具箱中用途最广泛玩法最灵活最能实现精细化运营的万能钥匙它几乎可以和任何营销场景进行组合是我们刺激用户行为提升转化和复购的核心手段优惠券工具的需求分析我为什么要设计一套独立的复杂的优惠券系统我们来看一个商家最常见的困惑用户在一次大促中尽兴消费后就消失了如何能有效地把他们拉回来产生第二次第三次消费呢满减满折这类活动解决的是当下的转化问题而优惠券则是我用来连接当下与未来的桥梁我将优惠券的核心业务价值总结为两点提升复购率通过在用户完成交易后或在日常的互动中向其发放一张带有有效期的优惠券我就为他创造了一个必须在未来某个时间点回来消费的强烈理由精准控制成本与全场打折不同优惠券可以指哪打哪我可以控制它的发放数量发放人群使用门槛和适用商品从而将营销预算精准地花在最有价值的用户和商品上优惠券的构成要素在设计功能前我首先要像解剖麻雀一样拆解优惠券这个事物的核心构成要素一张小小的优惠券看似简单实则包含了丰富的信息和规则我作为产品经理在设计时必须考虑到以下所有要素要素分类核心字段我的解读券面价值面值折扣这是优惠券最核心的价值例如元代金券或折折扣券使用门槛使用条件用户需要满足什么条件才能使用这张券例如满元可用无门槛券则没有此项适用范围使用范围使用平台这张券可以用在哪些地方是全场通用还是仅限购买商品可用是仅限内还是小程序也可用有效期限使用时间这是刺激用户在未来消费的关键是领取后天内有效还是只能在固定的月日到月日之间使用发放与领取发放数量领取人这张券总共准备发多少张是所有人都可以公开领取还是只发给用户的专属福利只有将这些要素全部定义清楚我才能设计出一套足够灵活能满足各种运营场景的优惠券系统优惠券的生命周期与用户旅程定义了优惠券的核心要素后我们再从宏观视角看一下优惠券从诞生到消亡的一生也就是它的生命周期流程核心角色商家创建者和发放者与用户领取者和使用者生命周期商家创建发放用户领取用户使用商家统计我的产品设计必须支撑起这个完整的闭环对于用户来说他们与优惠券的互动主要发生在两个核心环节领券和用券领券环节的场景设计我必须在用户消费决策的关键路径上为他们提供清晰便捷的领券入口例如在商品详情页我会明确地告诉用户本店现有以下优惠券可供领取用户点击后即可在弹窗中一键领取用券环节的场景设计当用户选好了商品来到订单结算页时这是优惠券发挥作用的最后也是最关键的环节在这个页面我会设计一个优惠券的选择栏用户点击后系统会自动判断用户当前订单满足了哪些优惠券的使用门槛将可用优惠券高亮地展示在最前方对于那些用户已领取但当前订单不可用的优惠券我也会展示出来并清晰地告知用户不可用的原因例如未达到满减金额这是一种反向的激励可能会促使用户返回去再多买一件商品来凑单通过对业务目标核心要素生命周期和用户旅程的完整分析我们就为接下来进行具体的端优惠券创建功能设计铺平了道路好的我们已经清晰地定义了优惠券工具的需求接下来我将带你进入产品设计的核心环节看看我是如何将这些需求转化为一个强大灵活且易于商家使用的后台功能优惠券工具的产品设计我的设计哲学是把复杂留给自己把简单交给用户对于优惠券这种玩法极其丰富的工具端商家端的设计尤其考验产品经理的抽象和归纳能力我需要将万千种运营场景收敛到一套结构化标准化的创建流程中我设计的优惠券创建过程主要分为三大步骤设置基本信息填写领取规则填写使用规则一设置基本信息与领取规则在创建优惠券的第一步我将基本信息和领取规则放在了一起因为它们共同定义了这张优惠券的身份和发放方式基本信息优惠券名称方便商家在后台进行识别和管理优惠券数量即库存控制了这张券的总发放量是控制成本的第一道闸门使用平台定义这张券是在还是小程序中生效领取规则这是实现精细化运营的关键领取用户我为商家提供了多种用户圈定方式可以是全部用户可领的普惠券也可以是针对用户等级如钻石会员专享或用户标签如高潜流失用户的精准券甚至支持上传文件针对特定的用户列表进行一对一发放领取张数可以限制每人限领张防止被羊毛党恶意刷取领取时间定义这张优惠券可以被领取的起止时间公开设置这是一个非常重要的开关如果勾选了公开领取这张券就会出现在商品详情页等端入口供用户主动领取如果不勾选它就是一张私有券不会对外展示只能由运营人员通过后台手动发放给指定用户常用于客服补偿等场景二填写使用规则玩法的核心这是优惠券设计的灵魂所在一张券到底怎么用决定了它的营销价值我设计了多种优惠券类型来满足不同的业务场景满减券的设计这是最常见的一种优惠券它的核心规则包括优惠券类型首先我定义了券的适用范围是仅限购买某些商品的商品券还是全场通用的通用券或者是只能抵扣运费的运费券使用门槛即满元可用优惠券面额即减元有效期这是刺激用户复购的关键我设计了两种模式固定时间例如国庆节专用券只能在月日到月日之间使用相对时效这种模式更为灵活例如自领取之日起天内可用或者自领取次日起天内可用这能确保每个领到券的用户都有一个完整的有效期适用范围这里可以更精细地控制券能用于哪些商品是全部商品还是指定商品指定类目或指定品牌当商家选择指定商品时我会提供一个与我们之前设计完全一致的可复用的商品选择器组件让他可以方便地进行勾选折扣券的设计折扣券的设计与满减券大部分相同核心区别在于优惠方式的定义商家不再是输入一个固定的面额而是输入一个折扣率例如打折立减券的设计立减券又称现金券是优惠力度最大的一种它的特点是无使用门槛在设计上我只需要让商家输入一个立减金额即可这种券通常用于新用户注册礼包或高价值用户的回归召回等关键场景三优惠券的管理当商家创建完所有优惠券后他可以在这张优惠券管理列表中对所有券进行统一的查看和操作我为这张列表设计了清晰的信息维度核心信息优惠券的名称类型满减折扣发放数量有效期等一目了然优惠券状态我通过未开始领取中已结束已失效这几种状态让商家可以清晰地了解每一张券当前的生命周期阶段快捷操作商家可以对不同状态的券进行查看编辑结束活动或查看数据等操作通过以上这套端产品设计我就为商家提供了一个功能强大配置灵活管理方便的优惠券弹药库让他们可以根据不同的营销战役自由地组合和使用这些弹药优惠券的逻辑规则当一个订单中存在多个商品多张可用优惠券时系统必须有一套清晰的规则来决定最终如何计算优惠我将它总结为三大核心规则规则一叠加与互斥这个问题的答案取决于优惠券的类型我制定的核心原则非常简单同一类型的优惠券一个订单只能使用一张不同类型的优惠券在不冲突的情况下可以叠加使用例如用户可以同时使用一张店铺满减券一张平台品类券和一张运费券但不能同时使用两张店铺满减券上图就是一个非常典型的真实案例一个订单同时叠加了多种不同类型的优惠最终形成了一个极具吸引力的价格我的系统设计就必须能够支持这种复杂的叠加计算我们来看上面这个非常经典的模拟真实购物场景的案例要判断这张优惠券能否同时使用我设计的系统会遵循以下逻辑进行严谨的校验第一步识别每张优惠券的类型与范围我首先会将这张券进行归类优惠券券类型作用范围使用门槛优惠券商品券仅限鼠标这个商品无门槛立减优惠券店铺券仅限店铺内的商品订单金额满元优惠券平台券跨店铺所有商品订单总金额满元优惠券运费券仅限店铺的运费无门槛免邮第二步逐一校验每张券的门槛与叠加规则校验优惠券它是一张商品券直接作用于鼠标无使用门槛可用校验优惠券它是一张店铺券计算店铺的商品总价为元鼠标元鼠标垫元这个价格满足了满元的使用门槛由于它和优惠券的类型店铺券商品券不同因此可叠加使用校验优惠券它是一张平台券计算跨店订单的总价为元店铺元店铺元这个价格满足了满元的使用门槛由于它和前两张券的类型平台券店铺券商品券都不同因此可叠加使用校验优惠券它是一张运费券属于特殊类型用于抵扣店铺的运费通常可以和所有其他类型的优惠券叠加可用第三步得出最终结论这张优惠券可以同时使用吗可以因为这四张券分别属于商品券店铺券平台券运费券类型各不相同且订单情况满足了它们各自的使用门槛因此它们可以完美地叠加使用系统应该推荐使用哪张优惠券呢全部推荐使用在这个场景下由于所有券都可以叠加并且都能带来优惠系统的最优策略就是默认将这张券全部勾选并应用从而为用户计算出最终的优惠力度最大的订单价格在电商后台我定义优惠券的类型其核心依据并不是它长什么样而是它的作用范围和成本由谁承担只有基于这两个维度我才能建立起一套严谨无歧义的叠加互斥规则我将优惠券严格划分为以下几个层级完全不同的类型优惠券类型定义与作用范围成本承担方核心目的单品券层级最低仅对指定的某一个商品生效商家推广单一爆款或清仓店铺券层级居中对指定店铺内的所有或部分商品生效商家提升本店的客单价和转化率平台券层级最高可跨店使用对平台上所有或部分店铺的商品生效平台提升整个平台的和用户活跃度运费券类型特殊仅用于抵扣运费商家或平台降低用户的购买决策门槛核心规则只有同一个层级的优惠券才存在互斥关系不同层级的优惠券因为其作用范围和成本方完全不同所以可以叠加规则二推荐原则当一个订单同时满足多张优惠券的使用门槛时系统应该如何帮助用户做出最优决策商家或许并不想让用户同时使用多张卷所以在我们上一小结的设计中三个劵同时归类为了商品券这时候我们的优先计算原则就是优惠最大的金额我的设计原则是永远帮助用户做出最省钱的选择系统后台会自动计算所有可能的可叠加的优惠券组合方式并默认选中那个优惠总金额最大的最佳组合我们来看一个复杂的实战案例面对这个复杂的场景我的系统后台会进行如下的智能计算如上图所示系统会匹配首先判断每个商品分别适用哪些优惠券组合然后尝试所有可行的叠加组合择优最后计算出水杯类满减券店铺满减券运费券这个组合可以优惠元是所有组合中优惠力度最大的因此系统会向用户默认推荐这个方案规则三分摊规则我们再次遇到了这个至关重要的财务规则当一个订单使用了多张作用范围不同的优惠券后发生部分退款时分摊计算就变得更加复杂我将这个计算过程用一张表格为您清晰地呈现最终结论当用户想要退货商品鼠标原价元时我设计的系统会从其原价中扣除掉它所分摊到的元优惠最终应退款元只有这样才能保证财务的绝对严谨优惠券工具的数据作为一名专业的产品或运营我绝不能只满足于把功能做出来我必须知道我策划的每一次活动效果如何成本怎样因此为优惠券工具设计一套完善的数据详情是必不可少的一步我将优惠券的数据监控分为了四大维度共计个核心指标通过对这个核心数据指标的持续监控和分析我作为运营就能够精准地洞察每一次优惠券活动的成败得失并为下一次的优化提供可靠的数据支撑",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-26 21:11:09",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E7%AB%A0%EF%BC%9A%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8"><span class="toc-text">第三章：活动管理-总价活动</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#3-1-%E8%90%A5%E9%94%80%E6%B4%BB%E5%8A%A8"><span class="toc-text">3.1 营销活动</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-1-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8-%E6%BB%A1%E5%87%8F%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86"><span class="toc-text">3.1.1 总价活动-满减活动管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-2-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8-%E6%BB%A1%E8%B5%A0%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86"><span class="toc-text">3.1.2 总价活动-满赠活动管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-3-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8-%E6%BB%A1%E6%8A%98%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86"><span class="toc-text">3.1.3 总价活动-满折活动管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-4-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8-%E5%A5%97%E8%A3%85%E6%B4%BB%E5%8A%A8"><span class="toc-text">3.1.4 总价活动-套装活动</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-2-%E8%90%A5%E9%94%80%E8%A7%84%E5%88%99"><span class="toc-text">3.2 营销规则</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-1-%E8%A7%84%E5%88%99%E4%B8%80%EF%BC%9A%E5%8F%A0%E5%8A%A0%E4%B8%8E%E4%BA%92%E6%96%A5"><span class="toc-text">3.2.1 规则一：叠加与互斥</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-2-%E8%A7%84%E5%88%99%E4%BA%8C%EF%BC%9A%E6%B4%BB%E5%8A%A8%E9%A1%BA%E5%BA%8F"><span class="toc-text">3.2.2 规则二：活动顺序</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-3-%E8%A7%84%E5%88%99%E4%B8%89%EF%BC%9A%E4%BC%98%E6%83%A0%E5%88%86%E6%91%8A%E8%A7%84%E5%88%99%EF%BC%88%E6%B7%B1%E5%BA%A6%E8%A7%A3%E6%9E%90%EF%BC%89"><span class="toc-text">3.2.3 规则三：优惠分摊规则（深度解析）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9F%BA%E7%A1%80%E5%9C%BA%E6%99%AF%EF%BC%9A%E5%8D%95%E4%B8%80%E6%80%BB%E4%BB%B7%E4%BC%98%E6%83%A0%E7%9A%84%E5%88%86%E6%91%8A"><span class="toc-text">1. 基础场景：单一总价优惠的分摊</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%BF%9B%E9%98%B6%E9%97%AE%E9%A2%98%EF%BC%9A%E8%AE%A1%E7%AE%97%E7%B2%BE%E5%BA%A6%E4%B8%8E%E5%B0%BE%E5%B7%AE%E5%A4%84%E7%90%86"><span class="toc-text">2. 进阶问题：计算精度与尾差处理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%BB%88%E6%9E%81%E6%8C%91%E6%88%98%EF%BC%9A%E5%A4%9A%E5%95%86%E5%93%81%E3%80%81%E5%A4%9A%E6%B4%BB%E5%8A%A8%E3%80%81%E5%A4%9A%E5%B1%82%E7%BA%A7%E4%BC%98%E6%83%A0%E7%9A%84%E6%B7%B7%E5%90%88%E5%88%86%E6%91%8A"><span class="toc-text">3. 终极挑战：多商品、多活动、多层级优惠的混合分摊</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-4-%E8%A7%84%E5%88%99%E5%9B%9B%EF%BC%9A%E9%A3%8E%E9%99%A9%E4%B8%8E%E9%98%B2%E8%8C%83"><span class="toc-text">3.2.4 规则四：风险与防范</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-3-%E8%90%A5%E9%94%80%E5%B7%A5%E5%85%B7"><span class="toc-text">3.3 营销工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-1-%E6%8A%BD%E5%A5%96%E5%B7%A5%E5%85%B7"><span class="toc-text">3.3.1 抽奖工具</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%8A%BD%E5%A5%96%E5%B7%A5%E5%85%B7%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">1. 抽奖工具的需求分析</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%8A%BD%E5%A5%96%E5%B7%A5%E5%85%B7%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">2. 抽奖工具的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%80%E3%80%81-%E5%95%86%E5%AE%B6%E7%AB%AF%EF%BC%88B%E7%AB%AF%EF%BC%89%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">一、 商家端（B端）产品设计</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BA%8C%E3%80%81-%E7%94%A8%E6%88%B7%E7%AB%AF%EF%BC%88C%E7%AB%AF%EF%BC%89%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">二、 用户端（C端）产品设计</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-2-%E4%BC%98%E6%83%A0%E5%88%B8%E5%B7%A5%E5%85%B7"><span class="toc-text">3.3.2 优惠券工具</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BC%98%E6%83%A0%E5%88%B8%E5%B7%A5%E5%85%B7%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">1.优惠券工具的需求分析</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BC%98%E6%83%A0%E5%88%B8%E5%B7%A5%E5%85%B7%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">2.优惠券工具的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%80%E3%80%81%E8%AE%BE%E7%BD%AE%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AF%E4%B8%8E%E9%A2%86%E5%8F%96%E8%A7%84%E5%88%99"><span class="toc-text">一、设置基本信息与领取规则</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BA%8C%E3%80%81%E5%A1%AB%E5%86%99%E4%BD%BF%E7%94%A8%E8%A7%84%E5%88%99%E2%80%94%E2%80%94%E7%8E%A9%E6%B3%95%E7%9A%84%E6%A0%B8%E5%BF%83"><span class="toc-text">二、填写使用规则——玩法的核心</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%89%E3%80%81-%E4%BC%98%E6%83%A0%E5%88%B8%E7%9A%84%E7%AE%A1%E7%90%86"><span class="toc-text">三、 优惠券的管理</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%BC%98%E6%83%A0%E5%88%B8%E7%9A%84%E9%80%BB%E8%BE%91%E8%A7%84%E5%88%99"><span class="toc-text">3. 优惠券的逻辑规则</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%A7%84%E5%88%99%E4%B8%80%EF%BC%9A%E5%8F%A0%E5%8A%A0%E4%B8%8E%E4%BA%92%E6%96%A5"><span class="toc-text">规则一：叠加与互斥</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%A7%84%E5%88%99%E4%BA%8C%EF%BC%9A%E6%8E%A8%E8%8D%90%E5%8E%9F%E5%88%99"><span class="toc-text">规则二：推荐原则</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%A7%84%E5%88%99%E4%B8%89%EF%BC%9A%E5%88%86%E6%91%8A%E8%A7%84%E5%88%99"><span class="toc-text">规则三：分摊规则</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E4%BC%98%E6%83%A0%E5%88%B8%E5%B7%A5%E5%85%B7%E7%9A%84%E6%95%B0%E6%8D%AE"><span class="toc-text">4. 优惠券工具的数据</span></a></li></ol></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">第三章：活动管理-总价活动</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-26T13:11:09.134Z" title="更新于 2025-07-26 21:11:09">2025-07-26</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">13.3k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>39分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="第三章：活动管理-总价活动"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/27803.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/27803.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">第三章：活动管理-总价活动</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time><time itemprop="dateCreated datePublished" datetime="2025-07-26T13:11:09.134Z" title="更新于 2025-07-26 21:11:09">2025-07-26</time></header><div id="postchat_postcontent"><h1 id="第三章：活动管理-总价活动"><a href="#第三章：活动管理-总价活动" class="headerlink" title="第三章：活动管理-总价活动"></a>第三章：活动管理-总价活动</h1><p>欢迎来到第三章。在上一章，我们的所有活动都聚焦于“<strong>单个商品</strong>”的降价。但作为运营，我还有一个更重要的目标：<strong>如何让用户一次买得更多？</strong> 这就引出了我们本章的主题——<strong>总价活动</strong>。这类活动不再关注单个商品的价格，而是着眼于用户的“<strong>购物车总价</strong>”，通过设置一个“满X元”的门槛，来激励用户为了凑单而购买更多商品。</p><h2 id="3-1-营销活动"><a href="#3-1-营销活动" class="headerlink" title="3.1 营销活动"></a>3.1 营销活动</h2><p>我们将要学习的第一个，也是最经典的总价活动，就是“满减”。</p><h3 id="3-1-1-总价活动-满减活动管理"><a href="#3-1-1-总价活动-满减活动管理" class="headerlink" title="3.1.1 总价活动-满减活动管理"></a>3.1.1 总价活动-满减活动管理</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725201823505.png" alt="image-20250725201823505"></p><p>我为什么要设计“满减”功能？其核心的业务诉求，正如上图所述，是为了<strong>提升客单价</strong>。</p><p>在日常运营中，我会遇到很多类似的场景，比如：</p><ul><li><strong>满减</strong>：订单金额满100元，减免20元。</li><li><strong>满赠</strong>：订单金额满200元，就赠送一个小礼物。</li><li><strong>满折</strong>：订单满3件，就享受8折优惠。</li></ul><p>这些玩法的本质，都是在用户下单的最后一步“临门一脚”，通过一个有吸引力的优惠，引导他们“再多买一件”。本节，我们就来从0到1地设计出“满减”这个功能。</p><p><strong>1. 角色与流程</strong></p><p>满减活动的设计思路，依然遵循我们熟悉的框架：<code>角色</code> -&gt; <code>流程</code> -&gt; <code>功能</code> -&gt; <code>字段</code>。</p><ul><li><strong>核心角色</strong>：依然是<strong>商家</strong>（活动的创建者）和<strong>用户</strong>（活动的参与者）。</li><li><strong>整体流程</strong>：商家在后台创建满减活动，用户在前台浏览商品并参与活动。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202251529.png" alt="image-20250725202251529"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202329925.png" alt="image-20250725202329925"></p><p>对于用户来说，参与的体验必须是无缝且直观的。我的设计要点是：</p><ul><li><strong>在商品详情页</strong>：就要清晰地展示出这个商品正在参与的满减活动，比如上图案例中的“满200减20”标签，这会成为用户把它加入购物车的重要动力，而这又分为<code>直接满减</code>与<code>阶梯满减</code>，对于阶梯满减来说，用户购买的越多优惠越多</li><li><strong>在结算页</strong>：当用户选购的商品满足了满减门槛时，系统需要自动地计算优惠，并清晰地展示出减免的金额，给用户带来“占到便宜”的满足感。</li></ul><p><strong>2. 商家创建满减活动</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202431819.png" alt="image-20250725202431819"></p><p>对于商家，我设计的创建流程依然是清晰的四步走：</p><ol><li><strong>选择活动类型</strong>：商家首先在活动中心选择创建“满减活动”。</li><li><strong>填写基本信息</strong>：设置活动的名称、时间、面向人群等。</li><li><strong>设置满减规则</strong>：这是满减活动的核心，也是设计的重点。</li><li><strong>选择活动商品</strong>：圈定参与本次满减活动的商品范围。</li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202812573.png"></p><p>现在，我们把上述流程，细化为具体的字段。</p><ul><li><strong>基本信息</strong>：<code>活动名称</code>、<code>活动时间</code>、<code>参与人群</code>等，这些都是我们之前设计中可复用的标准组件。</li><li><strong>活动规则</strong>：这是设计的关键。与单品活动不同，这里的规则是作用于总价的。我通常会为商家提供两种优惠类型：<ul><li><strong>直接满减</strong>：最简单的模式，例如“满100元，减10元”。</li><li><strong>阶梯满减</strong>：更灵活的模式，例如“满100减10，满200减30，满300减50”，通过多个档位，进一步刺激用户提高客单价。</li></ul></li><li><strong>活动商品</strong>：商家可以选择<code>全部商品</code>都参与，也可以<code>指定部分商品</code>参与。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202902097.png" alt="image-20250725202902097"></p><p>最终，这些字段会构成我们商家后台的创建页面。请特别注意“<strong>优惠类型</strong>”的设计，我通过单选框（Radio Button）让商家可以在“直接满减”和“阶梯满减”中切换。当选择“阶梯满减”时，我还提供了一个“<strong>+</strong>”号按钮，让商家可以动态地增加优惠层级。这种设计，兼顾了功能的强大性和操作的灵活性。</p><p><strong>3. 满减活动列表</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202925437.png" alt="image-20250725202925437"></p><p>当商家创建完活动后，他需要一个统一的列表来管理。我设计的这张列表，除了包含活动名称、时间、状态等常规字段外，还增加了一个非常重要的字段——“<strong>优惠信息</strong>”。</p><p>在这个字段里，我会清晰地展示出这个活动的核心规则，如“满300元，减30元”。这能让商家在不点进详情的情况下，快速地浏览和识别每一个活动，极大地提升了管理效率。同时，列表页也提供了<code>查看</code>、<code>编辑</code>、<code>结束</code>等必要的操作入口，其背后的状态机逻辑与我们之前设计的完全一致。</p><hr><h3 id="3-1-2-总价活动-满赠活动管理"><a href="#3-1-2-总价活动-满赠活动管理" class="headerlink" title="3.1.2 总价活动-满赠活动管理"></a>3.1.2 总价活动-满赠活动管理</h3><p>在开始设计前，我们先来看一个我经常遇到的真实业务场景：<strong>某个商品库存积压严重，直接打折降价怕影响品牌形象，怎么办？</strong></p><p>一个非常有效的策略，就是把它作为“<strong>赠品</strong>”，去搭配那些热销的商品。这就是“满赠”活动的核心价值之一：它不仅能像“满减”一样提升客单价，还能帮助我们优化库存结构，并给用户带来一种“意外之喜”的超值感。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203555950.png" alt="image-20250725203555950"></p><p>我给<strong>满赠</strong>的定义是：<strong>在活动时间段内，用户购买主商品，当订单金额或商品件数满足预设的门槛时，即可免费获得指定赠品的营销活动。</strong></p><p>和满减一样，我也把它分为两种常见形式：</p><ol><li><strong>直接满赠</strong>：规则简单直接，例如“满100元赠USB风扇”。</li><li><strong>阶梯满赠</strong>：设置多个优惠档位，例如“满100元赠USB风扇，满200元赠小熊电饭煲”，以此激励用户冲击更高的消费档次。</li></ol><p><strong>1. 商家创建满赠活动</strong></p><p>满赠活动的B端创建流程，与满减活动的主干完全一致，我依然采用了我们熟悉的“<strong>创建四部曲</strong>”。</p><p><code>选择活动类型 -&gt; 活动基本信息 -&gt; 满赠规则设置 -&gt; 活动商品选择</code></p><p>这体现了我在设计后台系统时的一个重要原则：<strong>保持操作逻辑的一致性，降低用户的学习成本</strong>。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203652822.png" alt="image-20250725203652822"></p><p>我们把创建流程拆解为具体的字段。这个功能的关键，在于“<strong>满赠规则设置</strong>”和“<strong>活动商品选择</strong>”这两步，有了全新的内涵。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203726873.png" alt="image-20250725203726873"></p><p>现在，我们来看这张“创建满赠活动”的页面原型，这是本节功能设计的核心。</p><p>它的上半部分（活动信息、优惠类型）与“满减”的设计几乎可以完全复用。真正的区别在下半部分：</p><ul><li><strong>选择商品（主商品）</strong>：这里的商品列表，指的是用户<strong>必须购买</strong>才能享受优惠的“<strong>主商品</strong>”。商家可以选择全店商品参与，也可以指定部分热销商品参与。</li><li><strong>选择赠品</strong>：这部分是“满赠”功能设计的灵魂。我需要在这里，为商家提供另一个商品选择器，让他可以从自己的商品库中，选择一个或多个商品，作为本次活动的“<strong>赠品</strong>”。</li></ul><p>我的设计要点是，赠品的选择必须灵活。商家不仅可以指定“赠品A”，还可以设置“满200元，可在赠品B或赠品C中任选其一”，把选择权交给用户，从而提升活动的吸引力。</p><p><strong>2. 用户端体验</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203831933.png" alt="image-20250725203831933"></p><p>一个后台功能设计得再好，如果用户在前端无感知，那也是失败的。所以，我必须在C端（用户端）清晰地把优惠信息传达出去。</p><ul><li><strong>商品详情页</strong>：当一个商品参与了满赠活动，我会在其价格旁边，增加一个醒目的“<strong>赠</strong>”字标签。用户点击后，可以看到详细的活动规则，例如“满100元即可获赠XX商品一件”。</li><li><strong>结算页</strong>：当用户的订单满足满赠条件时，在结算页的商品清单中，我会<strong>把赠品作为一个单独的行给展示出来，价格标注为0元</strong>。这能给用户带来实实在在的“获得感”，让他清晰地感知到自己享受到的优惠。</li></ul><p><strong>3. 满赠活动管理</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203937920.png" alt="image-20250725203937920"></p><p>最后，商家创建的所有满赠活动，也会进入到我们统一的“<strong>活动管理</strong>”列表中。</p><p>这张列表的整体框架是完全复用的。我需要做的，仅仅是确保“<strong>优惠信息</strong>”这一列，能够准确地显示出满赠活动的核心规则，例如“满100元赠A商品”。通过这种方式，商家可以高效地对进行中或未开始的活动，进行统一的管理和后续操作</p><hr><h3 id="3-1-3-总价活动-满折活动管理"><a href="#3-1-3-总价活动-满折活动管理" class="headerlink" title="3.1.3 总价活动-满折活动管理"></a>3.1.3 总价活动-满折活动管理</h3><p>我们已经有了“满减”（针对订单金额的优惠）和“满赠”（针对订单价值的提升），现在，我需要一个能直接激励用户购买“<strong>更多件数</strong>”的工具。尤其是在服装、图书、日用品这类客单价不一定高，但用户常常会一次性购买多件的品类中，这个工具就显得尤为重要。于是，“<strong>满折</strong>”活动就应运而生了。</p><p>满折，即<strong>当用户购买指定商品的件数，达到预设门槛时，即可享受整单相应折扣的优惠</strong>。例如，“指定商品任选3件，即可享受8折优惠”。</p><p><strong>1. 商家创建满折活动</strong></p><p>在B端设计上，我依然沿用了标准化的“<strong>创建四部曲</strong>”流程，这能确保商家在使用我们后台时，有一种统一、连贯的操作体验，而不需要为每一种新活动都重新学习一遍。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092532568.png" alt="image-20250726092532568"></p><p>我们把目光聚焦到这个功能的核心——“<strong>填写活动规则</strong>”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092602653.png" alt="image-20250726092602653"></p><p>我们直接来看“创建满折活动”的页面原型。它的核心设计，全部体现在“<strong>活动规则</strong>”这个模块里。</p><ul><li><strong>规则设置</strong>：这里的规则不再是“满X元”，而是“<strong>满X件</strong>”，优惠方式也从“减Y元”变成了“<strong>打Z折</strong>”。</li><li><strong>阶梯折扣</strong>：和满减、满赠一样，我也为“满折”设计了阶梯模式。商家可以设置“满2件打9折，满3件打8折”，通过一个更优惠的折扣力度，来强力吸引用户“再多拿一件”，从而有效提升订单的商品件数（即购物深度）和销售总额。</li></ul><p><strong>2. 用户端体验</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092913868.png" alt="image-20250726092913868"></p><p>在C端，为了让用户能清晰地感知到“满折”优惠，我的设计思路和“满减”是完全一致的。</p><ul><li><strong>活动感知</strong>：我会在参与活动的商品详情页上，用一个醒目的“<strong>满折</strong>”标签来吸引用户的注意力。</li><li><strong>优惠计算</strong>：在结算页，当用户购物车中参与活动的商品件数满足了规则后，系统会自动计算出折扣金额，并在“优惠金额”处明确展示，让用户直观地看到自己省了多少钱。</li></ul><p><strong>3. 满折活动管理</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726093347431.png" alt="image-20250726093347431"></p><p>在“<strong>活动管理</strong>”后台，商家也能清晰地看到所有已创建的满折活动。</p><p>在列表的“<strong>优惠类型</strong>”这一列，我会直观地显示出“满3件，8折”这样的核心规则，便于运营人员进行后续的查看、编辑或结束等管理操作。通过对列表、创建页等核心组件的复用，我能用最低的成本，最高效地扩展出新的营销玩法。</p><p>到此为止，我们已经系统性地掌握了总价活动中的“三剑客”：<strong>满减、满赠、满折</strong>。它们在B端的设计上有很多共通之处，但在C端的体感和核心运营策略上又各有侧重。</p><p>最后，我给你留一个思考题：如果我们要做一个更复杂的“<strong>满返</strong>”（比如，订单满100元，返还20元优惠券或2000积分）活动，它的B端和C端产品设计，与我们已经学过的这三种相比，又会有哪些共同点和差异点呢？带着这个问题，我们将在后续的课程中继续探索。</p><hr><h3 id="3-1-4-总价活动-套装活动"><a href="#3-1-4-总价活动-套装活动" class="headerlink" title="3.1.4 总价活动-套装活动"></a>3.1.4 总价活动-套装活动</h3><p>在之前的几种总价活动中，我们都是设定一个“规则门槛”，让用户<strong>自由地选择商品</strong>去凑单。但很多时候，我作为商家，希望能更“主动”地为用户规划好一组合集，并给出一个打包优惠价来提升整体销量。</p><p>比如，快餐店里“汉堡+薯条+可乐”的套餐，美妆领域“水、乳、精华”的护肤品套装，或者服饰店里“上衣+裤子”的搭配组合。这些，就是我们这节要设计的——<strong>套装活动</strong>。</p><p><strong>1. 套装活动设计思路</strong></p><p>套装活动的核心，是<strong>将多个独立的商品，打包成一个新的销售单元进行促销</strong>。它的设计思路依然遵循我们的标准框架，关键在于对“套装规则”的定义。</p><p>在B端设计上，我将“套装规则”进一步细分为了两种核心类型，以满足商家多样化的营销需求：</p><ol><li><strong>固定套装</strong>：一个打包好的组合，用户必须完整购买，不可更改。</li><li><strong>搭配套装</strong>：提供一定的选择空间，例如“主商品A + 搭配商品B/C/D任选其一”。</li></ol><p><strong>2. 设计详解：固定套装</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094401049.png" alt="image-20250726094401049"></p><p>“固定套装”是最简单、最常见的模式。它的逻辑就是“<strong>商品A + 商品B + 商品C = 一个固定的打包价</strong>”。</p><p>我们来看它的创建页面：</p><ul><li><strong>套装类型</strong>：商家首先选择“固定套装”。</li><li><strong>套装价格</strong>：商家需要为这个打包好的组合，设置一个全新的、有吸引力的“套装价”。</li><li><strong>选择商品</strong>：商家在下方的商品选择器中，勾选出所有要包含在这个固定套餐里的商品。</li></ul><p>这种模式的优点是规则清晰，用户决策成本低。缺点是灵活性较差。</p><p><strong>3. 设计详解：搭配套装</strong></p><p>“搭配套装”则为商家和用户提供了更高的灵活性。它的逻辑更像是“<strong>主商品区任选一件 + 搭配商品区任选一件 = 优惠组合价</strong>”。</p><p>在创建页面上，它的设计也更为复杂：</p><ul><li><strong>套装类型</strong>：商家需要选择“搭配套装”。</li><li><strong>选择主商品</strong>：商家首先要指定一批“主商品”。</li><li><strong>选择搭配商品</strong>：然后，再指定一批可供搭配的“副商品”。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094747956.png" alt="image-20250726094747956"></p><p>这种模式非常适用于服饰、3C配件等品类。例如，我可以设置一个“买任意一款手机（主商品），即可半价换购任意一款手机壳（搭配商品）”的活动。这给了用户一定的自主选择权，体验更好，也更容易促成关联销售。</p><p><strong>4. 用户端体验</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094729297.png" alt="image-20250726094729297"></p><p>当一个商品参与了套装活动时，我该如何在C端把它呈现给用户呢？</p><p>我的方案是，在该商品的详情页下方，专门开辟一个“<strong>优惠套餐</strong>”的区域。</p><p>如上图所示，这个区域会清晰地展示出套餐内的所有商品图片、名称，以及最具吸引力的“<strong>套餐价</strong>”，并提供一个“立即购买套餐”的按钮。通过这种直观的对比，用户能立刻感知到购买套餐的超值之处，从而被引导完成购买。</p><p><strong>5. 套装活动管理</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095246261.png" alt="image-20250726095246261"></p><p>最后，在B端的活动管理后台，商家可以统一管理所有已创建的套装活动。</p><p>为了便于商家区分，我特意在活动列表中增加了一列“<strong>套装类型</strong>”。通过这一列，商家可以一目了然地分清，哪些是“固定套装”，哪些是“搭配套装”，从而进行更有针对性的管理和数据分析。</p><hr><h2 id="3-2-营销规则"><a href="#3-2-营销规则" class="headerlink" title="3.2 营销规则"></a>3.2 营销规则</h2><p>我们已经为商家设计了品类丰富的单品活动和总价活动。但当这些“武器”可以被同时使用时，一个新的、也是更复杂的问题就摆在了我的面前。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095759645.png" alt="image-20250726095759645"></p><p>为了解决这个“优惠爆炸”的问题，防止出现混乱和亏损，我作为产品经理，必须设计一套清晰、严谨的“<strong>营销规则</strong>”。它就像我们营销系统的“基本法”，规定了所有活动之间应该如何协同工作。</p><p>我将这套复杂的规则，拆解为四大核心模块。接下来，我们将逐一攻克。</p><h3 id="3-2-1-规则一：叠加与互斥"><a href="#3-2-1-规则一：叠加与互斥" class="headerlink" title="3.2.1 规则一：叠加与互斥"></a>3.2.1 规则一：叠加与互斥</h3><p>我们先来看一个真实的运营场景：新款iPhone上市，运营同学恨不得把秒杀、拼团、满减、满赠所有优惠都给它加上，让它看起来“优惠到极致”。但，这样真的可以吗？</p><p>答案是否定的。如果没有任何限制，多个大力度的单品活动叠加，商品价格可能会变成负数。因此，我必须定义清楚</p><p>哪些活动之间是“<strong>互斥</strong>”的（不能同时享受），哪些又是可以“<strong>叠加</strong>”的（可以同时享受）。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095825205.png" alt="image-20250726095825205"></p><p>要定义规则，我首先需要把平台内所有的“优惠”形式，按照性质进行归类。我将它们划分为四大类：</p><ol><li><strong>单品活动</strong>：直接作用于<strong>商品本身</strong>的优惠，如秒杀、直降、拼团。</li><li><strong>总价活动</strong>：作用于<strong>订单总价</strong>的优惠，如满减、满赠、满折。</li><li><strong>抵扣活动</strong>：用户使用<strong>虚拟资产</strong>进行抵扣的活动，如优惠券、积分、礼品卡。</li><li><strong>支付活动</strong>：与<strong>支付渠道</strong>绑定的优惠，如信用卡支付立减。</li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104251355.png" alt="image-20250726104251355"></p><p>理清了分类，我就可以制定出上面这张“<strong>优惠叠加互斥规则表</strong>”。这是我们整个营销系统的“交通法规”。</p><p>它的核心逻辑可以总结为：</p><ul><li><strong>同类互斥</strong>：一个商品不能同时参与两个“单品活动”（例如，你不能让一个商品既是秒杀价，又是拼团价）。同理，一个订单也不能同时满足两个“总价活动”。</li><li><strong>异类叠加</strong>：不同类型的活动，原则上是可以叠加享受的。例如，一个商品可以先享受“秒杀”价（单品活动），达到门槛后可以再享受“满减”（总价活动），结算时还可以用“优惠券”（抵扣活动），最后用“信用卡支付”（支付活动）再减一点钱。</li></ul><h3 id="3-2-2-规则二：活动顺序"><a href="#3-2-2-规则二：活动顺序" class="headerlink" title="3.2.2 规则二：活动顺序"></a>3.2.2 规则二：活动顺序</h3><p>我们已经知道哪些活动可以一起用了。但新的问题又来了：<strong>先算哪个，后算哪个？</strong> 顺序不同，结果可能天差地别。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726103851360.png" alt="image-20250726103851360"></p><p>我的设计原则是，<strong>模拟用户真实的交易环节，定义一条雷打不动的计算链路</strong>。</p><ol><li><strong>第一步：计算单品活动</strong>。先算出商品经过秒杀、直降等活动后的价格。</li><li><strong>第二步：计算总价活动</strong>。用第一步得出的价格总和，去判断是否满足满减、满折的门槛。</li><li><strong>第三步：计算抵扣活动</strong>。用第二步得出的价格，去使用优惠券、积分等进行抵扣。</li><li><strong>第四步：计算支付活动</strong>。用第三步得出的最终应付金额，去享受支付渠道的优惠。</li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726103917986.png" alt="image-20250726103917986"></p><p>但在这个大原则下，还有一个更细致的问题：当同一种类型的活动有多个时，又该怎么算？比如，一个订单同时满足“满200减20”和“满300减40”。</p><p>这里，我设计了两种模式供运营人员选择：</p><ul><li><strong>递进式</strong>：先计算第一个门槛的优惠，用优惠后的金额，再去判断是否满足下一个门槛。这种模式对平台最有利，能严格控制成本，但计算逻辑复杂。</li><li><strong>平行式</strong>：所有满足门槛的优惠，都基于原始金额进行计算，然后全部生效。这种模式对用户最友好，计算速度快，但商家有亏损的风险（例如，用户买300元商品，同时享受了“满200减20”和“满300减40”，平行计算下总共优惠了60元）。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104208247.png" alt="image-20250726104208247"></p><p>通过上面这个案例，你可以清晰地看到，一个999元的鼠标，在“递进式”和“平行式”两种不同规则下，最终的成交价是不同的。在后台为运营设计这个功能时，我必须把这两种模式的选择权交给他们，并讲清楚其中的利弊。</p><h3 id="3-2-3-规则三：优惠分摊规则（深度解析）"><a href="#3-2-3-规则三：优惠分摊规则（深度解析）" class="headerlink" title="3.2.3 规则三：优惠分摊规则（深度解析）"></a>3.2.3 规则三：优惠分摊规则（深度解析）</h3><p>我们必须认识到，这个规则的存在，是为了解决一个核心的财务问题：<strong>当一笔享受了总价优惠的订单发生部分退款时，如何确保退款金额的计算是公平且准确的，以防止平台或商家产生亏损。</strong></p><h4 id="1-基础场景：单一总价优惠的分摊"><a href="#1-基础场景：单一总价优惠的分摊" class="headerlink" title="1. 基础场景：单一总价优惠的分摊"></a>1. 基础场景：单一总价优惠的分摊</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104708001.png" alt="image-20250726104708001"></p><p>我们从一个最基础，也最常见的场景开始。如上图所示，一个60元的订单，通过“满60减20”的活动，用户实际支付了40元。现在，用户需要退掉其中10元的A商品。我们应该退给他10元吗？</p><p>绝对不行。如果退10元，就意味着用户用30元买到了价值50元的B和C商品，享受了“满50减20”的优惠，这与我们“满60减20”的活动规则相悖，平台或商家平白无故地亏损了。</p><p>要解决这个问题，就必须引入我们分摊规则的“<strong>第一性原理</strong>”：<strong>任何一笔作用于订单整体的优惠，都必须按比例分摊到订单内的每一个商品上。</strong></p><p>我制定的核心分摊公式如下：<br><code>商品优惠金额 = 总优惠金额 × (商品金额 / 参与活动商品的价格总和)</code></p><p>现在，我们用这个公式来精确计算A商品的退款金额：</p><ol><li><strong>计算A商品分摊到的优惠金额</strong>：<br><code>A商品优惠金额 = 20元 × (10元 / 60元) = 3.33元</code></li><li><strong>计算A商品应退款金额</strong>：<br><code>A商品应退款 = A商品原价 - A商品分摊到的优惠金额 = 10元 - 3.33元 = 6.67元</code></li></ol><p>只有这样，我才能确保退款后，剩余的B、C两件商品，其合计支付金额（40-6.67=33.33元）与它们应该享受的优惠是匹配的。</p><h4 id="2-进阶问题：计算精度与尾差处理"><a href="#2-进阶问题：计算精度与尾差处理" class="headerlink" title="2. 进阶问题：计算精度与尾差处理"></a>2. 进阶问题：计算精度与尾差处理</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110938190.png" alt="image-20250726110938190"></p><p>应用这个公式，我们可以继续计算出B和C的应退款金额。但是，在真实的计算机系统中，除法运算常常会导致无限循环小数（例如 <code>10/60 = 0.1666...</code>），这会带来精度问题。如果A、B、C的优惠金额分别是3.33, 6.67, 10.00，三者相加可能等于20.00，也可能等于19.99或20.01。这个微小的误差，在海量订单下，会累积成巨大的财务漏洞。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111003639.png" alt="image-20250726111003639"></p><p>为了确保万无一失，我设计了一条“<strong>尾差处理规则</strong>”：<strong>最后一个商品的优惠金额 = 总优惠金额 - 之前所有商品已分摊的优惠金额之和</strong>。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111530614.png" alt="image-20250726111530614"></p><p>同时，为了让计算过程更稳定，我还会制定一条工程上的最佳实践：<br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111541687.png" alt="image-20250726111541687"><br><strong>按商品金额从小到大进行计算</strong>，然后将所有的计算尾差，都归结到最后一个（即金额最大）的商品上。这能保证，无论如何计算，<strong>一个订单内所有商品分摊的优惠总和，绝对等于这笔订单享受的优惠总额</strong>，一分不多，一分不少。</p><h4 id="3-终极挑战：多商品、多活动、多层级优惠的混合分摊"><a href="#3-终极挑战：多商品、多活动、多层级优惠的混合分摊" class="headerlink" title="3. 终极挑战：多商品、多活动、多层级优惠的混合分摊"></a>3. 终极挑战：多商品、多活动、多层级优惠的混合分摊</h4><p>现在，我们来挑战一个最复杂的场景，它融合了我们前面学到的所有规则。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111624491.png" alt="image-20250726111624491"></p><p>这个场景的复杂性在于，优惠不再是单一的“满减”，而是包含了<strong>单品活动、总价活动、抵扣活动</strong>的多层级优惠。</p><p>要解决这个问题，我必须严格遵循我们在上一节定义的“<strong>活动顺序</strong>”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111749384.png" alt="image-20250726111749384"></p><p>我们必须再次重申这条计算的生命线：<strong>单品活动 &gt; 总价活动 &gt; 抵扣活动</strong>。优惠的计算和分摊，必须严格按照这个优先级，层层递进。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111732314.png" alt="image-20250726111732314"></p><p>现在，我们对这个终极案例，进行庖丁解牛式的拆解：</p><ul><li><p><strong>第一步：计算单品活动。</strong></p><ul><li>A商品参加“直降1000元”，其优惠后的价格变为 <code>3000 - 1000 = 2000</code> 元。</li><li>B商品不参与单品活动，价格仍为 <code>100</code> 元。</li><li><strong>此时，用于下一步计算的订单价格基础是 A=2000元, B=100元。</strong></li></ul></li><li><p><strong>第二步：计算总价活动。</strong></p><ul><li>B商品参加“满100-50”的满减活动，其价格变为 <code>100 - 50 = 50</code> 元。</li><li>A商品不参与总价活动，价格仍为 <code>2000</code> 元。</li><li><strong>此时，用于下一步计算的订单价格基础是 A=2000元, B=50元。</strong></li></ul></li><li><p><strong>第三步：分摊抵扣活动（优惠券）。</strong></p><ul><li>现在，我们需要将这张1500元的优惠券，分摊到A和B两个商品上。</li><li><strong>用于分摊的商品价格总和为</strong>：<code>2000元（A的折后价） + 50元（B的折后价） = 2050元</code>。</li><li><strong>B商品应分摊的优惠券金额</strong> = <code>1500元 × (50元 / 2050元) ≈ 36.59元</code>。</li><li><strong>A商品应分摊的优惠券金额</strong> = <code>1500元 - 36.59元 = 1463.41元</code> （应用尾差处理规则）。</li></ul></li><li><p><strong>第四步：得出结论。</strong></p><ul><li>A商品总共优惠了：<code>1000元（直降） + 1463.41元（优惠券） = 2463.41元</code>。</li><li>B商品总共优惠了：<code>50元（满减） + 36.59元（优惠券） = 86.59元</code>。</li></ul></li></ul><p>通过以上严谨的、层层递进的规则设计，我才能确保，无论运营人员配置出多么复杂的优惠组合，我的系统都能准确、公平、安全地计算出最终价格和退款金额，守住平台和商家资金安全的生命线。这，就是“分摊规则”设计的严肃性和重要性所在。</p><h3 id="3-2-4-规则四：风险与防范"><a href="#3-2-4-规则四：风险与防范" class="headerlink" title="3.2.4 规则四：风险与防范"></a>3.2.4 规则四：风险与防范</h3><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110124008.png" alt="image-20250726110124008"></p><p>作为产品经理，我不仅要设计功能，更要保护平台和商家的利益，防止他们因为误操作而造成亏损。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110131853.png" alt="image-20250726110131853"></p><p>为此，我设计了一套“<strong>风险防范组合拳</strong>”：</p><ol><li><strong>低价预警</strong>：当系统检测到商家设置的优惠力度过大，可能导致亏损时（例如，折后价低于成本价），自动弹出醒目的预警提示，让商家进行二次确认。</li><li><strong>活动审核</strong>：对于一些重要的、或者新手商家创建的活动，我可以设计一个“审核”流程。活动创建后不会立刻生效，而是进入“待审核”状态，需要由运营主管或平台管理员审核通过后，才能正式上线。</li><li><strong>安全策略</strong>：为了防止专业的“羊毛党”通过技术手段刷单，我还需要设计一些基础的“反作弊”策略，例如限制同一个IP地址、同一个设备、同一个收货地址的参与次数等。</li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110202924.png" alt="image-20250726110202924"></p><p>最后我们总结一下，以上就是我设计的营销规则体系。它就像一张无形的、精密的大网，确保了我们整个营销活动系统，能够在复杂多变的场景下，依然能够公平、稳定、安全地运行。</p><h2 id="3-3-营销工具"><a href="#3-3-营销工具" class="headerlink" title="3.3 营销工具"></a>3.3 营销工具</h2><p>在第三章的后续部分，我们将进入一个更有趣、更具互动性的领域——<strong>营销工具</strong>。它不再是简单的让利，而是通过游戏化的玩法，来提升用户的参与度和粘性，实现“品效合一”的营销目标。</p><h3 id="3-3-1-抽奖工具"><a href="#3-3-1-抽奖工具" class="headerlink" title="3.3.1 抽奖工具"></a>3.3.1 抽奖工具</h3><p>抽奖，是一种低成本、高回报的互动营销玩法。它通过设置有吸引力的奖品，来驱动用户完成我们期望的特定行为，如每日访问、分享拉新等。</p><h4 id="1-抽奖工具的需求分析"><a href="#1-抽奖工具的需求分析" class="headerlink" title="1. 抽奖工具的需求分析"></a>1. 抽奖工具的需求分析</h4><p>在我动手设计具体的产品功能前，我必须首先回归原点，搞清楚我们“为什么”要做这个功能。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112559235.png" alt="image-20250726112559235"></p><p>我之所以要在系统中，增加一个看似和“卖货”没有直接关系的“抽奖”功能，其核心驱动力，来自于商家提升用户活跃度与忠诚度的真实诉求。</p><p>通过上图的需求背景，我提炼出抽奖工具需要满足的两大核心业务目标：</p><ol><li><strong>提升老用户粘性</strong>：通过每日免费抽奖等形式，为老用户提供一个持续访问我们App或店铺的理由，提升DAU（日活跃用户）。</li><li><strong>促进新用户增长</strong>：将“分享”与“增加抽奖次数”进行绑定，激励老用户主动去进行社交分享，从而为店铺带来低成本的新流量。</li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112647407.png" alt="image-20250726112647407"></p><p>明确了业务目标后，我就需要从最宏观的视角，来构思这个工具的完整生态。我首先要定义其中的“<strong>核心角色</strong>”和“<strong>整体流程</strong>”。</p><ul><li><strong>核心角色</strong>：抽奖工具的生态中，主要有三方参与者：<ul><li><strong>平台方</strong>：我作为平台的产品经理，负责设计和提供稳定、通用的抽奖工具。</li><li><strong>商家</strong>：是抽奖活动的发起者和成本承担者，他们使用我提供的工具，来配置活动规则和奖品。</li><li><strong>用户</strong>：是抽奖活动的最终参与者。</li></ul></li><li><strong>整体流程</strong>：整个业务的生命周期，是一个清晰的闭环。如上图所示，<code>平台提供工具</code> -&gt; <code>商家配置活动</code> -&gt; <code>用户参与抽奖</code> -&gt; <code>商家发放奖品</code> -&gt; <code>用户查看奖品</code>。我的产品设计，必须确保这个链条上的每一个环节都顺畅无误。</li></ul><p>在宏观流程中，“<strong>用户参与抽奖活动</strong>”是整个玩法能否成功的关键。那么，用户的体验旅程应该是怎样的呢？</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112736166.png" alt="image-20250726112736166"></p><p>为了让用户体验顺畅、且富有激励性，我为C端用户设计了上面这条完整的“<strong>参与流程</strong>”闭环。</p><p>我们来一步步拆解这个流程：</p><ol><li><strong>触发抽奖</strong>：用户进入活动页面，点击“立即抽奖”按钮。</li><li><strong>前置判断</strong>：系统首先判断用户“是否还有抽奖次数”。</li><li><strong>次数用完</strong>：如果次数已用完，系统会弹出提示，并引导用户去“<strong>通过分享获得更多抽奖次数</strong>”。这正是我们实现拉新裂变的关键设计。</li><li><strong>执行抽奖</strong>：如果次数未用完，系统则根据后台配置的算法，来判断本次抽奖“是否中奖”。</li><li><strong>结果反馈</strong>：<ul><li>如果<strong>中奖</strong>，则弹出“恭喜中奖”的强提示，并引导用户去“查看奖品信息”。</li><li>如果<strong>未中奖</strong>，则弹出“谢谢参与”的安慰性提示。</li></ul></li></ol><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124606407.png" alt="image-20250726124606407"></p><p>通过对业务目标、宏观流程、用户旅程的完整分析，我就为接下来进行具体的“产品设计”，打下了坚实的基础。</p><hr><h4 id="2-抽奖工具的产品设计"><a href="#2-抽奖工具的产品设计" class="headerlink" title="2. 抽奖工具的产品设计"></a>2. 抽奖工具的产品设计</h4><p>根据我们之前的分析，抽奖工具的设计，必须同时兼顾**商家端（B端）<strong>的易用性和灵活性，以及</strong>用户端（C端）**的趣味性和流畅体验。我将为你分别进行拆解。</p><h5 id="一、-商家端（B端）产品设计"><a href="#一、-商家端（B端）产品设计" class="headerlink" title="一、 商家端（B端）产品设计"></a><strong>一、 商家端（B端）产品设计</strong></h5><p>我们首先来看商家后台的设计。我需要为商家提供一个足够强大，但操作又不能过于复杂的活动创建流程。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124513436.png" alt="image-20250726124513436"></p><p>我设计的商家创建流程，依然遵循我们熟悉的“四部曲”，确保了后台操作的一致性。接下来，我们详细看一下每一步的具体设计。</p><p><strong>1. 设置基本信息</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124625992.png" alt="image-20250726124625992"></p><p>这是活动创建的第一步，商家需要在这里设置活动的“身份信息”，包括<code>活动名称</code>、<code>活动时间</code>、<code>活动平台</code>（是在App内还是H5页面生效）以及<code>活动说明</code>（即活动规则的文字描述）。</p><p><strong>2. 填写抽奖规则</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124651499.png" alt="image-20250726124651499"></p><p>这是抽奖功能设计的灵魂，它决定了整个活动的核心玩法。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124725381.png" alt="image-20250726124725381"></p><p>在设计这部分功能时，我主要思考并解决了上面这两个核心问题。</p><ul><li><strong>抽奖类型</strong>：我为商家提供了两种模式，来回答第一个问题。<ul><li><code>即时抽奖</code>：用户抽完立刻知道结果。这是最常见的模式，能提供即时反馈和刺激。</li><li><code>非即时抽奖</code>：用户参与后，需要等待统一的开奖时间（例如，每周五开奖）。这种模式适用于需要营造悬念和持续关注度的活动。</li></ul></li><li><strong>抽奖条件</strong>：我允许商家设置参与门槛，例如用户必须<code>使用XX积分</code>、达到<code>XX会员等级</code>，或者<code>完成订单后</code>才能获得抽奖资格。</li><li><strong>参与次数</strong>：商家可以灵活控制用户参与的频率，是<code>每人每天可抽N次</code>，还是在整个活动周期内<code>每人一共可抽N次</code>。</li><li><strong>分享设置</strong>：这是实现裂变增长的关键。我需要让商家可以配置“<strong>用户分享活动后，可以额外增加N次抽奖机会</strong>”的规则。</li><li><strong>提示文案</strong>：为了让体验更友好，我允许商家自定义各种场景下的提示文案，如<code>中奖提示</code>、<code>未中奖提示</code>、<code>活动未开始提示</code>等。</li></ul><p><strong>3. 选择抽奖奖品</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124857807.png" alt="image-20250726124857807"></p><p>在这一步，商家需要设置本次活动的“奖池”。为了回答“如何控制奖品数量”这个问题，我要求商家在设置每一个奖品时，都必须明确两项核心信息：<strong><code>奖品数量</code><strong>和</strong><code>中奖概率</code></strong>。系统会根据这两项配置，通过抽奖算法来精确控制奖品的发放。</p><p>为了丰富奖品的类型，我设计的奖池支持多种奖品形态：</p><ul><li><p><strong>实物商品</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125038417.png" alt="image-20250726125038417"></p></li><li><p><strong>优惠券</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125104812.png" alt="image-20250726125104812"></p></li><li><p><strong>积分</strong><br><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125113561.png" alt="image-20250726125113561"></p></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125326357.png" alt="image-20250726125326357"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125341599.png" alt="image-20250726125341599"></p><p>通过这种模块化的设计，商家就可以非常灵活地配置出具有吸引力的奖品组合。</p><h5 id="二、-用户端（C端）产品设计"><a href="#二、-用户端（C端）产品设计" class="headerlink" title="二、 用户端（C端）产品设计"></a><strong>二、 用户端（C端）产品设计</strong></h5><p>当商家在后台配置好活动后，C端用户看到和体验到的，必须是一个有趣、流畅的界面。</p><p><strong>1. 抽奖主页面</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125205586.png" alt="image-20250726125205586"></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125128157.png" alt="image-20250726125128157"></p><p>我采用了最经典的“<strong>九宫格</strong>”抽奖样式。这个页面的核心元素包括：</p><ul><li><strong>抽奖区域</strong>：九个格子中，分布着不同的奖品和“谢谢参与”的选项。</li><li><strong>抽奖按钮</strong>：用户点击“立即抽奖”，转盘开始转动。当用户次数用尽，按钮会变为“明天再来”或“分享获取次数”等不可用状态。</li><li><strong>中奖名单</strong>：页面下方会实时滚动最新的中奖信息，营造一种热闹、很多人中奖的氛围，来激励其他用户参与。</li></ul><p><strong>2. 抽奖结果反馈</strong></p><p>对于“即时抽奖”来说，及时的结果反馈至关重要。</p><ul><li><strong>中奖</strong>：立刻弹出强提示的“恭喜中奖”弹窗，告知用户获得了什么奖品。</li><li><strong>未中奖</strong>：弹出安慰性的“祝您下次中奖”弹窗，并引导用户“下次再来”。</li></ul><p><strong>3. 我的奖品列表</strong></p><p>所有用户中奖的记录，都会沉淀在“<strong>我的中奖记录</strong>”这个页面。用户可以在这里，清晰地看到自己获得的所有奖品，以及每一个奖品的当前状态，是“<strong>待兑换</strong>”还是“<strong>已兑换</strong>”，方便进行后续的核销与使用。</p><hr><h3 id="3-3-2-优惠券工具"><a href="#3-3-2-优惠券工具" class="headerlink" title="3.3.2 优惠券工具"></a>3.3.2 优惠券工具</h3><p>如果说“抽奖”是提升趣味性和互动性的利器，那么“<strong>优惠券</strong>”则是我工具箱中，用途最广泛、玩法最灵活、最能实现精细化运营的“万能钥匙”。它几乎可以和任何营销场景进行组合，是我们刺激用户行为、提升转化和复购的核心手段。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130639644.png" alt="image-20250726130639644"></p><h4 id="1-优惠券工具的需求分析"><a href="#1-优惠券工具的需求分析" class="headerlink" title="1.优惠券工具的需求分析"></a>1.优惠券工具的需求分析</h4><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130159412.png" alt="image-20250726130159412"></p><p>我为什么要设计一套独立的、复杂的优惠券系统？我们来看一个商家最常见的困惑：<strong>用户在一次大促中尽兴消费后，就“消失”了，如何能有效地把他们“拉回来”，产生第二次、第三次消费呢？</strong></p><p>“满减”、“满折”这类活动，解决的是“当下”的转化问题。而优惠券，则是我用来连接“<strong>当下</strong>”与“<strong>未来</strong>”的桥梁。我将优惠券的核心业务价值，总结为两点：</p><ol><li><strong>提升复购率</strong>：通过在用户完成交易后、或在日常的互动中，向其发放一张带有有效期的优惠券，我就为他创造了一个“必须在未来某个时间点回来消费”的强烈理由。</li><li><strong>精准控制成本</strong>：与全场打折不同，优惠券可以“<strong>指哪打哪</strong>”。我可以控制它的发放数量、发放人群、使用门槛和适用商品，从而将营销预算，精准地花在最有价值的用户和商品上。</li></ol><p><strong>1. 优惠券的构成要素</strong></p><p>在设计功能前，我首先要像解剖麻雀一样，拆解“优惠券”这个事物的核心构成要素。</p><p>一张小小的优惠券，看似简单，实则包含了丰富的信息和规则。我作为产品经理，在设计时必须考虑到以下所有要素：</p><table><thead><tr><th align="left"><strong>要素分类</strong></th><th align="left"><strong>核心字段</strong></th><th align="left"><strong>我的解读</strong></th></tr></thead><tbody><tr><td align="left"><strong>券面价值</strong></td><td align="left"><code>面值</code> / <code>折扣</code></td><td align="left">这是优惠券最核心的价值。例如，10元代金券，或8折折扣券。</td></tr><tr><td align="left"><strong>使用门槛</strong></td><td align="left"><code>使用条件</code></td><td align="left">用户需要满足什么条件才能使用这张券。例如，“满100元可用”。无门槛券则没有此项。</td></tr><tr><td align="left"><strong>适用范围</strong></td><td align="left"><code>使用范围</code> / <code>使用平台</code></td><td align="left">这张券可以用在哪些地方。是“全场通用”，还是仅限“购买A商品”可用？是仅限App内，还是小程序也可用？</td></tr><tr><td align="left"><strong>有效期限</strong></td><td align="left"><code>使用时间</code></td><td align="left">这是刺激用户在未来消费的关键。是“领取后7天内有效”，还是只能在“固定的10月1日到10月7日”之间使用？</td></tr><tr><td align="left"><strong>发放与领取</strong></td><td align="left"><code>发放数量</code> / <code>领取人</code></td><td align="left">这张券总共准备发多少张？是所有人都可以公开领取，还是只发给“VIP用户”的专属福利？</td></tr></tbody></table><p>只有将这些要素全部定义清楚，我才能设计出一套足够灵活、能满足各种运营场景的优惠券系统。</p><p><strong>2. 优惠券的生命周期与用户旅程</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130240979.png" alt="image-20250726130240979"></p><p>定义了优惠券的核心要素后，我们再从宏观视角，看一下优惠券从诞生到消亡的“一生”，也就是它的生命周期流程。</p><ul><li><strong>核心角色</strong>：<strong>商家</strong>（创建者和发放者）与<strong>用户</strong>（领取者和使用者）。</li><li><strong>生命周期</strong>：<code>商家创建/发放</code> -&gt; <code>用户领取</code> -&gt; <code>用户使用</code> -&gt; <code>商家统计</code>。我的产品设计，必须支撑起这个完整的闭环。</li></ul><p>对于用户来说，他们与优惠券的互动，主要发生在两个核心环节：“<strong>领券</strong>”和“<strong>用券</strong>”。</p><ul><li><strong>“领券”环节的场景设计</strong></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130516515.png" alt="image-20250726130516515"></p><p>我必须在用户消费决策的关键路径上，为他们提供清晰、便捷的领券入口。例如，在商品详情页，我会明确地告诉用户“<strong>本店现有以下优惠券可供领取</strong>”，用户点击后，即可在弹窗中一键领取。</p><ul><li><strong>“用券”环节的场景设计</strong></li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130544241.png" alt="image-20250726130544241">当用户选好了商品，来到订单结算页时，这是优惠券发挥作用的最后、也是最关键的环节。在这个页面，我会设计一个“优惠券”的选择栏，用户点击后：</p><ol><li>系统会自动判断用户当前订单满足了哪些优惠券的使用门槛，将“<strong>可用优惠券</strong>”高亮地展示在最前方。</li><li>对于那些用户已领取、但当前订单“<strong>不可用</strong>”的优惠券，我也会展示出来，并清晰地告知用户“不可用”的原因（例如，“未达到满减金额”）。这是一种反向的激励，可能会促使用户返回去，再多买一件商品来凑单。</li></ol><p>通过对业务目标、核心要素、生命周期和用户旅程的完整分析，我们就为接下来进行具体的B端“优惠券创建”功能设计，铺平了道路。</p><p>好的，我们已经清晰地定义了优惠券工具的需求。接下来，我将带你进入产品设计的核心环节，看看我是如何将这些需求，转化为一个强大、灵活且易于商家使用的后台功能。</p><hr><h4 id="2-优惠券工具的产品设计"><a href="#2-优惠券工具的产品设计" class="headerlink" title="2.优惠券工具的产品设计"></a>2.优惠券工具的产品设计</h4><p>我的设计哲学是，<strong>把复杂留给自己，把简单交给用户</strong>。对于优惠券这种玩法极其丰富的工具，B端（商家端）的设计尤其考验产品经理的抽象和归纳能力。我需要将万千种运营场景，收敛到一套结构化、标准化的创建流程中。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726131021810.png" alt="image-20250726131021810"></p><p>我设计的优惠券创建过程，主要分为三大步骤：<strong>设置基本信息 -&gt; 填写领取规则 -&gt; 填写使用规则</strong>。</p><h5 id="一、设置基本信息与领取规则"><a href="#一、设置基本信息与领取规则" class="headerlink" title="一、设置基本信息与领取规则"></a>一、设置基本信息与领取规则</h5><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726131936090.png" alt="image-20250726131936090"></p><p>在创建优惠券的第一步，我将“基本信息”和“领取规则”放在了一起，因为它们共同定义了这张优惠券的“<strong>身份</strong>”和“<strong>发放方式</strong>”。</p><ul><li><strong>基本信息</strong>：<ul><li><code>优惠券名称</code>：方便商家在后台进行识别和管理。</li><li><code>优惠券数量</code>：即“库存”，控制了这张券的总发放量，是控制成本的第一道闸门。</li><li><code>使用平台</code>：定义这张券是在App、H5还是小程序中生效。</li></ul></li><li><strong>领取规则</strong>：这是实现“<strong>精细化运营</strong>”的关键。<ul><li><code>领取用户</code>：我为商家提供了多种用户圈定方式。可以是<code>全部用户</code>可领的普惠券；也可以是针对<code>用户等级</code>（如：钻石会员专享）或<code>用户标签</code>（如：高潜流失用户）的精准券；甚至支持<code>上传文件</code>，针对特定的用户ID列表进行一对一发放。</li><li><code>领取张数</code>：可以限制<code>每人限领N张</code>，防止被“羊毛党”恶意刷取。</li><li><code>领取时间</code>：定义这张优惠券可以被领取的起止时间。</li><li><code>公开设置</code>：这是一个非常重要的开关。如果勾选了“<strong>公开领取</strong>”，这张券就会出现在商品详情页等C端入口，供用户主动领取。如果不勾选，它就是一张“<strong>私有券</strong>”，不会对外展示，只能由运营人员通过后台手动发放给指定用户，常用于客服补偿等场景。</li></ul></li></ul><h5 id="二、填写使用规则——玩法的核心"><a href="#二、填写使用规则——玩法的核心" class="headerlink" title="二、填写使用规则——玩法的核心"></a>二、填写使用规则——玩法的核心</h5><p>这是优惠券设计的灵魂所在。一张券到底“怎么用”，决定了它的营销价值。我设计了多种优惠券类型，来满足不同的业务场景。</p><p><strong>2.1 满减券的设计</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132042176.png" alt="image-20250726132042176"></p><p>这是最常见的一种优惠券。它的核心规则包括：</p><ul><li><strong>优惠券类型</strong>：首先，我定义了券的适用范围。是仅限购买某些商品的<code>商品券</code>，还是全场通用的<code>通用券</code>，或者是只能抵扣运费的<code>运费券</code>。</li><li><strong>使用门槛</strong>：即“满X元可用”。</li><li><strong>优惠券面额</strong>：即“减Y元”。</li><li><strong>有效期</strong>：这是刺激用户复购的关键。我设计了两种模式：<ul><li><code>固定时间</code>：例如，国庆节专用券，只能在10月1日到10月7日之间使用。</li><li><code>相对时效</code>：这种模式更为灵活，例如<code>自领取之日起N天内可用</code>，或者<code>自领取次日起N天内可用</code>。这能确保每个领到券的用户，都有一个完整的有效期。</li></ul></li><li><strong>适用范围</strong>：这里可以更精细地控制券能用于哪些商品，是<code>全部商品</code>，还是<code>指定商品</code>、<code>指定类目</code>或<code>指定品牌</code>。</li></ul><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132157048.png" alt="image-20250726132157048"></p><p>当商家选择“指定商品”时，我会提供一个与我们之前设计完全一致的、可复用的商品选择器组件，让他可以方便地进行勾选。</p><p><strong>2.2 折扣券的设计</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132136682.png" alt="image-20250726132136682"></p><p>折扣券的设计，与满减券大部分相同，核心区别在于“优惠方式”的定义。商家不再是输入一个固定的“面额”，而是输入一个“<strong>折扣率</strong>”，例如“打8折”。</p><p><strong>2.3 立减券的设计</strong></p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132143949.png" alt="image-20250726132143949"></p><p>立减券，又称“现金券”，是优惠力度最大的一种。它的特点是“<strong>无使用门槛</strong>”。在设计上，我只需要让商家输入一个“<strong>立减金额</strong>”即可。这种券通常用于新用户注册礼包、或高价值用户的回归召回等关键场景。</p><h5 id="三、-优惠券的管理"><a href="#三、-优惠券的管理" class="headerlink" title="三、 优惠券的管理"></a>三、 优惠券的管理</h5><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132209133.png" alt="image-20250726132209133"></p><p>当商家创建完所有优惠券后，他可以在这张“<strong>优惠券管理列表</strong>”中，对所有券进行统一的查看和操作。</p><p>我为这张列表设计了清晰的信息维度：</p><ul><li><strong>核心信息</strong>：优惠券的<code>名称</code>、<code>类型</code>（满减/折扣）、<code>发放数量</code>、<code>有效期</code>等一目了然。</li><li><strong>优惠券状态</strong>：我通过<code>未开始</code>、<code>领取中</code>、<code>已结束</code>、<code>已失效</code>这几种状态，让商家可以清晰地了解每一张券当前的生命周期阶段。</li><li><strong>快捷操作</strong>：商家可以对不同状态的券，进行<code>查看</code>、<code>编辑</code>、<code>结束活动</code>或查看<code>数据</code>等操作。</li></ul><p>通过以上这套B端产品设计，我就为商家提供了一个功能强大、配置灵活、管理方便的“优惠券弹药库”，让他们可以根据不同的营销战役，自由地组合和使用这些“弹药”。</p><hr><h4 id="3-优惠券的逻辑规则"><a href="#3-优惠券的逻辑规则" class="headerlink" title="3. 优惠券的逻辑规则"></a>3. 优惠券的逻辑规则</h4><p>当一个订单中，存在多个商品、多张可用优惠券时，系统必须有一套清晰的规则，来决定最终如何计算优惠。我将它总结为三大核心规则。</p><h5 id="规则一：叠加与互斥"><a href="#规则一：叠加与互斥" class="headerlink" title="规则一：叠加与互斥"></a><strong>规则一：叠加与互斥</strong></h5><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132817387.png" alt="image-20250726132817387"></p><p>这个问题的答案，取决于优惠券的类型。我制定的核心原则非常简单：<strong>同一类型的优惠券，一个订单只能使用一张；不同类型的优惠券，在不冲突的情况下，可以叠加使用。</strong></p><p>例如，用户可以同时使用一张“店铺满减券”、一张“平台品类券”和一张“运费券”，但不能同时使用两张“店铺满减券”。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132847661.png" alt="image-20250726132847661"></p><p>上图就是一个非常典型的真实案例，一个订单同时叠加了多种不同类型的优惠，最终形成了一个极具吸引力的价格。我的系统设计，就必须能够支持这种复杂的叠加计算。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726133202001.png" alt="image-20250726133202001"></p><p>我们来看上面这个非常经典的、模拟真实购物场景的案例。要判断这4张优惠券能否同时使用，我设计的系统，会遵循以下逻辑进行严谨的校验。</p><p><strong>第一步：识别每张优惠券的“类型”与“范围”</strong></p><p>我首先会将这4张券进行归类：</p><table><thead><tr><th><strong>优惠券</strong></th><th><strong>券类型</strong></th><th><strong>作用范围</strong></th><th><strong>使用门槛</strong></th></tr></thead><tbody><tr><td><strong>优惠券1</strong></td><td><code>商品券</code></td><td>仅限“鼠标”这个商品</td><td>无门槛（立减）</td></tr><tr><td><strong>优惠券2</strong></td><td><code>店铺券</code></td><td>仅限“A店铺”内的商品</td><td>订单金额满100元</td></tr><tr><td><strong>优惠券3</strong></td><td><code>平台券</code></td><td>跨店铺所有商品</td><td>订单总金额满400元</td></tr><tr><td><strong>优惠券4</strong></td><td><code>运费券</code></td><td>仅限“A店铺”的运费</td><td>无门槛（免邮）</td></tr></tbody></table><p><strong>第二步：逐一校验每张券的“门槛”与“叠加规则”</strong></p><ol><li><strong>校验【优惠券1】</strong>：它是一张<code>商品券</code>，直接作用于鼠标，无使用门槛。<strong>可用</strong>。</li><li><strong>校验【优惠券2】</strong>：它是一张<code>店铺券</code>。计算A店铺的商品总价为 <code>99元(鼠标) + 30元(鼠标垫) = 129元</code>。这个价格满足了“满100元”的使用门槛。由于它和优惠券1的类型（<code>店铺券</code> vs <code>商品券</code>）不同，因此<strong>可叠加使用</strong>。</li><li><strong>校验【优惠券3】</strong>：它是一张<code>平台券</code>。计算跨店订单的总价为 <code>129元(A店铺) + 398元(B店铺) = 527元</code>。这个价格满足了“满400元”的使用门槛。由于它和前两张券的类型（<code>平台券</code> vs <code>店铺券</code>/<code>商品券</code>）都不同，因此<strong>可叠加使用</strong>。</li><li><strong>校验【优惠券4】</strong>：它是一张<code>运费券</code>，属于特殊类型，用于抵扣A店铺的运费，通常可以和所有其他类型的优惠券叠加。<strong>可用</strong>。</li></ol><p><strong>第三步：得出最终结论</strong></p><ul><li><strong>这4张优惠券可以同时使用吗？</strong><ul><li><strong>可以。</strong> 因为这四张券分别属于<strong>商品券、店铺券、平台券、运费券</strong>，类型各不相同，且订单情况满足了它们各自的使用门槛，因此它们可以完美地叠加使用。</li></ul></li><li><strong>系统应该推荐使用哪张优惠券呢？</strong><ul><li><strong>全部推荐使用。</strong> 在这个场景下，由于所有券都可以叠加，并且都能带来优惠，系统的最优策略就是默认将这4张券<strong>全部勾选并应用</strong>，从而为用户计算出最终的、优惠力度最大的订单价格。</li></ul></li></ul><p>在电商后台，我定义优惠券的“类型”，其核心依据，并不是它“长什么样”，而是它的“<strong>作用范围</strong>”和“<strong>成本由谁承担</strong>”。只有基于这两个维度，我才能建立起一套严谨、无歧义的叠加互斥规则。</p><p>我将优惠券，严格划分为以下几个<strong>层级完全不同</strong>的类型：</p><table><thead><tr><th align="left"><strong>优惠券类型</strong></th><th align="left"><strong>定义与作用范围</strong></th><th align="left"><strong>成本承担方</strong></th><th align="left"><strong>核心目的</strong></th></tr></thead><tbody><tr><td align="left"><strong>单品券</strong></td><td align="left"><strong>层级最低</strong>。仅对指定的某一个商品（SKU/SPU）生效。</td><td align="left">商家</td><td align="left">推广单一爆款或清仓。</td></tr><tr><td align="left"><strong>店铺券</strong></td><td align="left"><strong>层级居中</strong>。对<strong>指定店铺内</strong>的所有或部分商品生效。</td><td align="left"><strong>商家</strong></td><td align="left">提升<strong>本店的客单价</strong>和转化率。</td></tr><tr><td align="left"><strong>平台券</strong></td><td align="left"><strong>层级最高</strong>。可<strong>跨店使用</strong>，对平台上所有或部分店铺的商品生效。</td><td align="left"><strong>平台</strong></td><td align="left">提升<strong>整个平台的GMV</strong>和用户活跃度。</td></tr><tr><td align="left"><strong>运费券</strong></td><td align="left"><strong>类型特殊</strong>。仅用于抵扣运费。</td><td align="left">商家 或 平台</td><td align="left">降低用户的购买决策门槛。</td></tr></tbody></table><p><strong>核心规则</strong>：只有<strong>同一个层级</strong>的优惠券，才存在“互斥”关系。<strong>不同层级</strong>的优惠券，因为其作用范围和成本方完全不同，所以<strong>可以叠加</strong>。</p><hr><h5 id="规则二：推荐原则"><a href="#规则二：推荐原则" class="headerlink" title="规则二：推荐原则"></a><strong>规则二：推荐原则</strong></h5><p>当一个订单同时满足多张优惠券的使用门槛时，系统应该如何帮助用户做出最优决策，商家或许并不想让用户同时使用多张卷，所以在我们上一小结的设计中，三个劵同时归类为了<code>商品券</code>，这时候我们的优先计算原则就是优惠最大的金额</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726133421088.png" alt="image-20250726133421088"></p><p>我的设计原则是——<strong>永远帮助用户做出“最省钱”的选择</strong>。系统后台会自动计算所有可能的、可叠加的优惠券组合方式，并默认选中那个“<strong>优惠总金额最大</strong>”的最佳组合。</p><p>我们来看一个复杂的实战案例：</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134305240.png" alt="image-20250726134305240"></p><p>面对这个复杂的场景，我的系统后台会进行如下的智能计算：</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134246505.png" alt="image-20250726134246505"></p><p>如上图所示，系统会：</p><ol><li><strong>匹配</strong>：首先判断每个商品分别适用哪些优惠券。</li><li><strong>组合</strong>：然后尝试所有可行的叠加组合。</li><li><strong>择优</strong>：最后计算出“水杯类满减券 + A店铺满减券 + 运费券”这个组合，可以优惠131元，是所有组合中优惠力度最大的，因此系统会向用户默认推荐这个方案。</li></ol><h5 id="规则三：分摊规则"><a href="#规则三：分摊规则" class="headerlink" title="规则三：分摊规则"></a><strong>规则三：分摊规则</strong></h5><p>我们再次遇到了这个至关重要的财务规则。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134517071.png" alt="image-20250726134517071"></p><p>当一个订单使用了多张、作用范围不同的优惠券后，发生部分退款时，分摊计算就变得更加复杂。我将这个计算过程，用一张表格为您清晰地呈现：</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134534335.png" alt="image-20250726134534335"></p><p><strong>最终结论</strong>：当用户想要退货A商品（鼠标，原价99元）时，我设计的系统会从其原价中，扣除掉它所分摊到的<code>23.35</code>元优惠，最终应退款 <code>99 - 23.35 = 75.65</code> 元。只有这样，才能保证财务的绝对严谨。</p><h4 id="4-优惠券工具的数据"><a href="#4-优惠券工具的数据" class="headerlink" title="4. 优惠券工具的数据"></a>4. 优惠券工具的数据</h4><p>作为一名专业的产品或运营，我绝不能只满足于“把功能做出来”。我必须知道我策划的每一次活动，效果如何，成本怎样。因此，为优惠券工具设计一套完善的“<strong>数据详情</strong>”，是必不可少的一步。</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134849709.png" alt="image-20250726134849709"></p><p>我将优惠券的数据监控，分为了四大维度，共计11个核心指标：</p><p>通过对这11个核心数据指标的持续监控和分析，我作为运营，就能够精准地洞察每一次优惠券活动的成败得失，并为下一次的优化，提供可靠的数据支撑。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/27803.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/27803.html&quot;)">第三章：活动管理-总价活动</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/27803.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=第三章：活动管理-总价活动&amp;url=https://prorise666.site/posts/27803.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/10822.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">第二章：活动管理-单品活动</div></div></a></div><div class="next-post pull-right"><a href="/posts/19658.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">第四章：内容管理</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"第三章：活动管理-总价活动",date:"2025-07-26 11:13:45",updated:"2025-07-26 21:11:09",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第三章：活动管理-总价活动\n\n欢迎来到第三章。在上一章，我们的所有活动都聚焦于“**单个商品**”的降价。但作为运营，我还有一个更重要的目标：**如何让用户一次买得更多？** 这就引出了我们本章的主题——**总价活动**。这类活动不再关注单个商品的价格，而是着眼于用户的“**购物车总价**”，通过设置一个“满X元”的门槛，来激励用户为了凑单而购买更多商品。\n\n## 3.1 营销活动\n\n我们将要学习的第一个，也是最经典的总价活动，就是“满减”。\n\n### 3.1.1 总价活动-满减活动管理\n\n![image-20250725201823505](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725201823505.png)\n\n我为什么要设计“满减”功能？其核心的业务诉求，正如上图所述，是为了**提升客单价**。\n\n在日常运营中，我会遇到很多类似的场景，比如：\n* **满减**：订单金额满100元，减免20元。\n* **满赠**：订单金额满200元，就赠送一个小礼物。\n* **满折**：订单满3件，就享受8折优惠。\n\n这些玩法的本质，都是在用户下单的最后一步“临门一脚”，通过一个有吸引力的优惠，引导他们“再多买一件”。本节，我们就来从0到1地设计出“满减”这个功能。\n\n**1. 角色与流程**\n\n满减活动的设计思路，依然遵循我们熟悉的框架：`角色` -> `流程` -> `功能` -> `字段`。\n\n* **核心角色**：依然是**商家**（活动的创建者）和**用户**（活动的参与者）。\n* **整体流程**：商家在后台创建满减活动，用户在前台浏览商品并参与活动。\n\n![image-20250725202251529](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202251529.png)\n\n![image-20250725202329925](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202329925.png)\n\n对于用户来说，参与的体验必须是无缝且直观的。我的设计要点是：\n\n* **在商品详情页**：就要清晰地展示出这个商品正在参与的满减活动，比如上图案例中的“满200减20”标签，这会成为用户把它加入购物车的重要动力，而这又分为`直接满减`与`阶梯满减`，对于阶梯满减来说，用户购买的越多优惠越多\n* **在结算页**：当用户选购的商品满足了满减门槛时，系统需要自动地计算优惠，并清晰地展示出减免的金额，给用户带来“占到便宜”的满足感。\n\n**2. 商家创建满减活动**\n\n![image-20250725202431819](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202431819.png)\n\n对于商家，我设计的创建流程依然是清晰的四步走：\n\n1.  **选择活动类型**：商家首先在活动中心选择创建“满减活动”。\n2.  **填写基本信息**：设置活动的名称、时间、面向人群等。\n3.  **设置满减规则**：这是满减活动的核心，也是设计的重点。\n4.  **选择活动商品**：圈定参与本次满减活动的商品范围。\n\n![](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202812573.png)\n\n现在，我们把上述流程，细化为具体的字段。\n\n* **基本信息**：`活动名称`、`活动时间`、`参与人群`等，这些都是我们之前设计中可复用的标准组件。\n* **活动规则**：这是设计的关键。与单品活动不同，这里的规则是作用于总价的。我通常会为商家提供两种优惠类型：\n    * **直接满减**：最简单的模式，例如“满100元，减10元”。\n    * **阶梯满减**：更灵活的模式，例如“满100减10，满200减30，满300减50”，通过多个档位，进一步刺激用户提高客单价。\n* **活动商品**：商家可以选择`全部商品`都参与，也可以`指定部分商品`参与。\n\n![image-20250725202902097](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202902097.png)\n\n最终，这些字段会构成我们商家后台的创建页面。请特别注意“**优惠类型**”的设计，我通过单选框（Radio Button）让商家可以在“直接满减”和“阶梯满减”中切换。当选择“阶梯满减”时，我还提供了一个“**+**”号按钮，让商家可以动态地增加优惠层级。这种设计，兼顾了功能的强大性和操作的灵活性。\n\n**3. 满减活动列表**\n\n![image-20250725202925437](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202925437.png)\n\n当商家创建完活动后，他需要一个统一的列表来管理。我设计的这张列表，除了包含活动名称、时间、状态等常规字段外，还增加了一个非常重要的字段——“**优惠信息**”。\n\n在这个字段里，我会清晰地展示出这个活动的核心规则，如“满300元，减30元”。这能让商家在不点进详情的情况下，快速地浏览和识别每一个活动，极大地提升了管理效率。同时，列表页也提供了`查看`、`编辑`、`结束`等必要的操作入口，其背后的状态机逻辑与我们之前设计的完全一致。\n\n\n---\n### 3.1.2 总价活动-满赠活动管理\n\n在开始设计前，我们先来看一个我经常遇到的真实业务场景：**某个商品库存积压严重，直接打折降价怕影响品牌形象，怎么办？**\n\n一个非常有效的策略，就是把它作为“**赠品**”，去搭配那些热销的商品。这就是“满赠”活动的核心价值之一：它不仅能像“满减”一样提升客单价，还能帮助我们优化库存结构，并给用户带来一种“意外之喜”的超值感。\n\n![image-20250725203555950](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203555950.png)\n\n我给**满赠**的定义是：**在活动时间段内，用户购买主商品，当订单金额或商品件数满足预设的门槛时，即可免费获得指定赠品的营销活动。**\n\n和满减一样，我也把它分为两种常见形式：\n1.  **直接满赠**：规则简单直接，例如“满100元赠USB风扇”。\n2.  **阶梯满赠**：设置多个优惠档位，例如“满100元赠USB风扇，满200元赠小熊电饭煲”，以此激励用户冲击更高的消费档次。\n\n**1. 商家创建满赠活动**\n\n满赠活动的B端创建流程，与满减活动的主干完全一致，我依然采用了我们熟悉的“**创建四部曲**”。\n\n`选择活动类型 -> 活动基本信息 -> 满赠规则设置 -> 活动商品选择`\n\n这体现了我在设计后台系统时的一个重要原则：**保持操作逻辑的一致性，降低用户的学习成本**。\n\n![image-20250725203652822](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203652822.png)\n\n我们把创建流程拆解为具体的字段。这个功能的关键，在于“**满赠规则设置**”和“**活动商品选择**”这两步，有了全新的内涵。\n\n![image-20250725203726873](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203726873.png)\n\n现在，我们来看这张“创建满赠活动”的页面原型，这是本节功能设计的核心。\n\n它的上半部分（活动信息、优惠类型）与“满减”的设计几乎可以完全复用。真正的区别在下半部分：\n\n* **选择商品（主商品）**：这里的商品列表，指的是用户**必须购买**才能享受优惠的“**主商品**”。商家可以选择全店商品参与，也可以指定部分热销商品参与。\n* **选择赠品**：这部分是“满赠”功能设计的灵魂。我需要在这里，为商家提供另一个商品选择器，让他可以从自己的商品库中，选择一个或多个商品，作为本次活动的“**赠品**”。\n\n我的设计要点是，赠品的选择必须灵活。商家不仅可以指定“赠品A”，还可以设置“满200元，可在赠品B或赠品C中任选其一”，把选择权交给用户，从而提升活动的吸引力。\n\n**2. 用户端体验**\n\n![image-20250725203831933](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203831933.png)\n\n一个后台功能设计得再好，如果用户在前端无感知，那也是失败的。所以，我必须在C端（用户端）清晰地把优惠信息传达出去。\n\n* **商品详情页**：当一个商品参与了满赠活动，我会在其价格旁边，增加一个醒目的“**赠**”字标签。用户点击后，可以看到详细的活动规则，例如“满100元即可获赠XX商品一件”。\n* **结算页**：当用户的订单满足满赠条件时，在结算页的商品清单中，我会**把赠品作为一个单独的行给展示出来，价格标注为0元**。这能给用户带来实实在在的“获得感”，让他清晰地感知到自己享受到的优惠。\n\n**3. 满赠活动管理**\n\n![image-20250725203937920](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203937920.png)\n\n最后，商家创建的所有满赠活动，也会进入到我们统一的“**活动管理**”列表中。\n\n这张列表的整体框架是完全复用的。我需要做的，仅仅是确保“**优惠信息**”这一列，能够准确地显示出满赠活动的核心规则，例如“满100元赠A商品”。通过这种方式，商家可以高效地对进行中或未开始的活动，进行统一的管理和后续操作\n\n\n---\n### 3.1.3 总价活动-满折活动管理\n\n我们已经有了“满减”（针对订单金额的优惠）和“满赠”（针对订单价值的提升），现在，我需要一个能直接激励用户购买“**更多件数**”的工具。尤其是在服装、图书、日用品这类客单价不一定高，但用户常常会一次性购买多件的品类中，这个工具就显得尤为重要。于是，“**满折**”活动就应运而生了。\n\n满折，即**当用户购买指定商品的件数，达到预设门槛时，即可享受整单相应折扣的优惠**。例如，“指定商品任选3件，即可享受8折优惠”。\n\n**1. 商家创建满折活动**\n\n在B端设计上，我依然沿用了标准化的“**创建四部曲**”流程，这能确保商家在使用我们后台时，有一种统一、连贯的操作体验，而不需要为每一种新活动都重新学习一遍。\n\n![image-20250726092532568](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092532568.png)\n\n我们把目光聚焦到这个功能的核心——“**填写活动规则**”。\n\n![image-20250726092602653](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092602653.png)\n\n我们直接来看“创建满折活动”的页面原型。它的核心设计，全部体现在“**活动规则**”这个模块里。\n\n* **规则设置**：这里的规则不再是“满X元”，而是“**满X件**”，优惠方式也从“减Y元”变成了“**打Z折**”。\n* **阶梯折扣**：和满减、满赠一样，我也为“满折”设计了阶梯模式。商家可以设置“满2件打9折，满3件打8折”，通过一个更优惠的折扣力度，来强力吸引用户“再多拿一件”，从而有效提升订单的商品件数（即购物深度）和销售总额。\n\n**2. 用户端体验**\n\n![image-20250726092913868](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092913868.png)\n\n在C端，为了让用户能清晰地感知到“满折”优惠，我的设计思路和“满减”是完全一致的。\n\n* **活动感知**：我会在参与活动的商品详情页上，用一个醒目的“**满折**”标签来吸引用户的注意力。\n* **优惠计算**：在结算页，当用户购物车中参与活动的商品件数满足了规则后，系统会自动计算出折扣金额，并在“优惠金额”处明确展示，让用户直观地看到自己省了多少钱。\n\n**3. 满折活动管理**\n\n![image-20250726093347431](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726093347431.png)\n\n在“**活动管理**”后台，商家也能清晰地看到所有已创建的满折活动。\n\n在列表的“**优惠类型**”这一列，我会直观地显示出“满3件，8折”这样的核心规则，便于运营人员进行后续的查看、编辑或结束等管理操作。通过对列表、创建页等核心组件的复用，我能用最低的成本，最高效地扩展出新的营销玩法。\n\n到此为止，我们已经系统性地掌握了总价活动中的“三剑客”：**满减、满赠、满折**。它们在B端的设计上有很多共通之处，但在C端的体感和核心运营策略上又各有侧重。\n\n最后，我给你留一个思考题：如果我们要做一个更复杂的“**满返**”（比如，订单满100元，返还20元优惠券或2000积分）活动，它的B端和C端产品设计，与我们已经学过的这三种相比，又会有哪些共同点和差异点呢？带着这个问题，我们将在后续的课程中继续探索。\n\n---\n### 3.1.4 总价活动-套装活动\n\n在之前的几种总价活动中，我们都是设定一个“规则门槛”，让用户**自由地选择商品**去凑单。但很多时候，我作为商家，希望能更“主动”地为用户规划好一组合集，并给出一个打包优惠价来提升整体销量。\n\n比如，快餐店里“汉堡+薯条+可乐”的套餐，美妆领域“水、乳、精华”的护肤品套装，或者服饰店里“上衣+裤子”的搭配组合。这些，就是我们这节要设计的——**套装活动**。\n\n**1. 套装活动设计思路**\n\n套装活动的核心，是**将多个独立的商品，打包成一个新的销售单元进行促销**。它的设计思路依然遵循我们的标准框架，关键在于对“套装规则”的定义。\n\n在B端设计上，我将“套装规则”进一步细分为了两种核心类型，以满足商家多样化的营销需求：\n1.  **固定套装**：一个打包好的组合，用户必须完整购买，不可更改。\n2.  **搭配套装**：提供一定的选择空间，例如“主商品A + 搭配商品B/C/D任选其一”。\n\n**2. 设计详解：固定套装**\n\n![image-20250726094401049](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094401049.png)\n\n“固定套装”是最简单、最常见的模式。它的逻辑就是“**商品A + 商品B + 商品C = 一个固定的打包价**”。\n\n我们来看它的创建页面：\n* **套装类型**：商家首先选择“固定套装”。\n* **套装价格**：商家需要为这个打包好的组合，设置一个全新的、有吸引力的“套装价”。\n* **选择商品**：商家在下方的商品选择器中，勾选出所有要包含在这个固定套餐里的商品。\n\n这种模式的优点是规则清晰，用户决策成本低。缺点是灵活性较差。\n\n**3. 设计详解：搭配套装**\n\n“搭配套装”则为商家和用户提供了更高的灵活性。它的逻辑更像是“**主商品区任选一件 + 搭配商品区任选一件 = 优惠组合价**”。\n\n在创建页面上，它的设计也更为复杂：\n* **套装类型**：商家需要选择“搭配套装”。\n* **选择主商品**：商家首先要指定一批“主商品”。\n* **选择搭配商品**：然后，再指定一批可供搭配的“副商品”。\n\n![image-20250726094747956](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094747956.png)\n\n这种模式非常适用于服饰、3C配件等品类。例如，我可以设置一个“买任意一款手机（主商品），即可半价换购任意一款手机壳（搭配商品）”的活动。这给了用户一定的自主选择权，体验更好，也更容易促成关联销售。\n\n\n\n**4. 用户端体验**\n\n![image-20250726094729297](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094729297.png)\n\n当一个商品参与了套装活动时，我该如何在C端把它呈现给用户呢？\n\n我的方案是，在该商品的详情页下方，专门开辟一个“**优惠套餐**”的区域。\n\n如上图所示，这个区域会清晰地展示出套餐内的所有商品图片、名称，以及最具吸引力的“**套餐价**”，并提供一个“立即购买套餐”的按钮。通过这种直观的对比，用户能立刻感知到购买套餐的超值之处，从而被引导完成购买。\n\n**5. 套装活动管理**\n\n![image-20250726095246261](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095246261.png)\n\n最后，在B端的活动管理后台，商家可以统一管理所有已创建的套装活动。\n\n为了便于商家区分，我特意在活动列表中增加了一列“**套装类型**”。通过这一列，商家可以一目了然地分清，哪些是“固定套装”，哪些是“搭配套装”，从而进行更有针对性的管理和数据分析。\n\n\n---\n## 3.2 营销规则\n\n我们已经为商家设计了品类丰富的单品活动和总价活动。但当这些“武器”可以被同时使用时，一个新的、也是更复杂的问题就摆在了我的面前。\n\n![image-20250726095759645](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095759645.png)\n\n为了解决这个“优惠爆炸”的问题，防止出现混乱和亏损，我作为产品经理，必须设计一套清晰、严谨的“**营销规则**”。它就像我们营销系统的“基本法”，规定了所有活动之间应该如何协同工作。\n\n我将这套复杂的规则，拆解为四大核心模块。接下来，我们将逐一攻克。\n\n### 3.2.1 规则一：叠加与互斥\n\n我们先来看一个真实的运营场景：新款iPhone上市，运营同学恨不得把秒杀、拼团、满减、满赠所有优惠都给它加上，让它看起来“优惠到极致”。但，这样真的可以吗？\n\n答案是否定的。如果没有任何限制，多个大力度的单品活动叠加，商品价格可能会变成负数。因此，我必须定义清楚\n\n哪些活动之间是“**互斥**”的（不能同时享受），哪些又是可以“**叠加**”的（可以同时享受）。\n\n![image-20250726095825205](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095825205.png)\n\n要定义规则，我首先需要把平台内所有的“优惠”形式，按照性质进行归类。我将它们划分为四大类：\n1.  **单品活动**：直接作用于**商品本身**的优惠，如秒杀、直降、拼团。\n2.  **总价活动**：作用于**订单总价**的优惠，如满减、满赠、满折。\n3.  **抵扣活动**：用户使用**虚拟资产**进行抵扣的活动，如优惠券、积分、礼品卡。\n4.  **支付活动**：与**支付渠道**绑定的优惠，如信用卡支付立减。\n\n![image-20250726104251355](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104251355.png)\n\n理清了分类，我就可以制定出上面这张“**优惠叠加互斥规则表**”。这是我们整个营销系统的“交通法规”。\n\n它的核心逻辑可以总结为：\n* **同类互斥**：一个商品不能同时参与两个“单品活动”（例如，你不能让一个商品既是秒杀价，又是拼团价）。同理，一个订单也不能同时满足两个“总价活动”。\n* **异类叠加**：不同类型的活动，原则上是可以叠加享受的。例如，一个商品可以先享受“秒杀”价（单品活动），达到门槛后可以再享受“满减”（总价活动），结算时还可以用“优惠券”（抵扣活动），最后用“信用卡支付”（支付活动）再减一点钱。\n\n### 3.2.2 规则二：活动顺序\n\n我们已经知道哪些活动可以一起用了。但新的问题又来了：**先算哪个，后算哪个？** 顺序不同，结果可能天差地别。\n\n![image-20250726103851360](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726103851360.png)\n\n我的设计原则是，**模拟用户真实的交易环节，定义一条雷打不动的计算链路**。\n\n1.  **第一步：计算单品活动**。先算出商品经过秒杀、直降等活动后的价格。\n2.  **第二步：计算总价活动**。用第一步得出的价格总和，去判断是否满足满减、满折的门槛。\n3.  **第三步：计算抵扣活动**。用第二步得出的价格，去使用优惠券、积分等进行抵扣。\n4.  **第四步：计算支付活动**。用第三步得出的最终应付金额，去享受支付渠道的优惠。\n\n![image-20250726103917986](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726103917986.png)\n\n但在这个大原则下，还有一个更细致的问题：当同一种类型的活动有多个时，又该怎么算？比如，一个订单同时满足“满200减20”和“满300减40”。\n\n这里，我设计了两种模式供运营人员选择：\n* **递进式**：先计算第一个门槛的优惠，用优惠后的金额，再去判断是否满足下一个门槛。这种模式对平台最有利，能严格控制成本，但计算逻辑复杂。\n* **平行式**：所有满足门槛的优惠，都基于原始金额进行计算，然后全部生效。这种模式对用户最友好，计算速度快，但商家有亏损的风险（例如，用户买300元商品，同时享受了“满200减20”和“满300减40”，平行计算下总共优惠了60元）。\n\n![image-20250726104208247](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104208247.png)\n\n通过上面这个案例，你可以清晰地看到，一个999元的鼠标，在“递进式”和“平行式”两种不同规则下，最终的成交价是不同的。在后台为运营设计这个功能时，我必须把这两种模式的选择权交给他们，并讲清楚其中的利弊。\n\n\n### 3.2.3 规则三：优惠分摊规则（深度解析）\n\n我们必须认识到，这个规则的存在，是为了解决一个核心的财务问题：**当一笔享受了总价优惠的订单发生部分退款时，如何确保退款金额的计算是公平且准确的，以防止平台或商家产生亏损。**\n\n#### 1. 基础场景：单一总价优惠的分摊\n\n![image-20250726104708001](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104708001.png)\n\n我们从一个最基础，也最常见的场景开始。如上图所示，一个60元的订单，通过“满60减20”的活动，用户实际支付了40元。现在，用户需要退掉其中10元的A商品。我们应该退给他10元吗？\n\n绝对不行。如果退10元，就意味着用户用30元买到了价值50元的B和C商品，享受了“满50减20”的优惠，这与我们“满60减20”的活动规则相悖，平台或商家平白无故地亏损了。\n\n要解决这个问题，就必须引入我们分摊规则的“**第一性原理**”：**任何一笔作用于订单整体的优惠，都必须按比例分摊到订单内的每一个商品上。**\n\n我制定的核心分摊公式如下：\n`商品优惠金额 = 总优惠金额 × (商品金额 / 参与活动商品的价格总和)`\n\n现在，我们用这个公式来精确计算A商品的退款金额：\n1.  **计算A商品分摊到的优惠金额**：\n    `A商品优惠金额 = 20元 × (10元 / 60元) = 3.33元`\n2.  **计算A商品应退款金额**：\n    `A商品应退款 = A商品原价 - A商品分摊到的优惠金额 = 10元 - 3.33元 = 6.67元`\n\n只有这样，我才能确保退款后，剩余的B、C两件商品，其合计支付金额（40-6.67=33.33元）与它们应该享受的优惠是匹配的。\n\n#### 2. 进阶问题：计算精度与尾差处理\n\n![image-20250726110938190](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110938190.png)\n\n应用这个公式，我们可以继续计算出B和C的应退款金额。但是，在真实的计算机系统中，除法运算常常会导致无限循环小数（例如 `10/60 = 0.1666...`），这会带来精度问题。如果A、B、C的优惠金额分别是3.33, 6.67, 10.00，三者相加可能等于20.00，也可能等于19.99或20.01。这个微小的误差，在海量订单下，会累积成巨大的财务漏洞。\n\n![image-20250726111003639](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111003639.png)\n\n为了确保万无一失，我设计了一条“**尾差处理规则**”：**最后一个商品的优惠金额 = 总优惠金额 - 之前所有商品已分摊的优惠金额之和**。\n\n![image-20250726111530614](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111530614.png)\n\n同时，为了让计算过程更稳定，我还会制定一条工程上的最佳实践：\n![image-20250726111541687](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111541687.png)\n**按商品金额从小到大进行计算**，然后将所有的计算尾差，都归结到最后一个（即金额最大）的商品上。这能保证，无论如何计算，**一个订单内所有商品分摊的优惠总和，绝对等于这笔订单享受的优惠总额**，一分不多，一分不少。\n\n#### 3. 终极挑战：多商品、多活动、多层级优惠的混合分摊\n\n现在，我们来挑战一个最复杂的场景，它融合了我们前面学到的所有规则。\n\n![image-20250726111624491](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111624491.png)\n\n这个场景的复杂性在于，优惠不再是单一的“满减”，而是包含了**单品活动、总价活动、抵扣活动**的多层级优惠。\n\n要解决这个问题，我必须严格遵循我们在上一节定义的“**活动顺序**”。\n\n![image-20250726111749384](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111749384.png)\n\n我们必须再次重申这条计算的生命线：**单品活动 > 总价活动 > 抵扣活动**。优惠的计算和分摊，必须严格按照这个优先级，层层递进。\n\n![image-20250726111732314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111732314.png)\n\n现在，我们对这个终极案例，进行庖丁解牛式的拆解：\n\n* **第一步：计算单品活动。**\n    * A商品参加“直降1000元”，其优惠后的价格变为 `3000 - 1000 = 2000` 元。\n    * B商品不参与单品活动，价格仍为 `100` 元。\n    * **此时，用于下一步计算的订单价格基础是 A=2000元, B=100元。**\n\n* **第二步：计算总价活动。**\n    * B商品参加“满100-50”的满减活动，其价格变为 `100 - 50 = 50` 元。\n    * A商品不参与总价活动，价格仍为 `2000` 元。\n    * **此时，用于下一步计算的订单价格基础是 A=2000元, B=50元。**\n\n* **第三步：分摊抵扣活动（优惠券）。**\n    * 现在，我们需要将这张1500元的优惠券，分摊到A和B两个商品上。\n    * **用于分摊的商品价格总和为**：`2000元（A的折后价） + 50元（B的折后价） = 2050元`。\n    * **B商品应分摊的优惠券金额** = `1500元 × (50元 / 2050元) ≈ 36.59元`。\n    * **A商品应分摊的优惠券金额** = `1500元 - 36.59元 = 1463.41元` （应用尾差处理规则）。\n\n* **第四步：得出结论。**\n    * A商品总共优惠了：`1000元（直降） + 1463.41元（优惠券） = 2463.41元`。\n    * B商品总共优惠了：`50元（满减） + 36.59元（优惠券） = 86.59元`。\n\n通过以上严谨的、层层递进的规则设计，我才能确保，无论运营人员配置出多么复杂的优惠组合，我的系统都能准确、公平、安全地计算出最终价格和退款金额，守住平台和商家资金安全的生命线。这，就是“分摊规则”设计的严肃性和重要性所在。\n\n\n### 3.2.4 规则四：风险与防范\n\n![image-20250726110124008](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110124008.png)\n\n作为产品经理，我不仅要设计功能，更要保护平台和商家的利益，防止他们因为误操作而造成亏损。\n\n![image-20250726110131853](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110131853.png)\n\n为此，我设计了一套“**风险防范组合拳**”：\n\n1.  **低价预警**：当系统检测到商家设置的优惠力度过大，可能导致亏损时（例如，折后价低于成本价），自动弹出醒目的预警提示，让商家进行二次确认。\n2.  **活动审核**：对于一些重要的、或者新手商家创建的活动，我可以设计一个“审核”流程。活动创建后不会立刻生效，而是进入“待审核”状态，需要由运营主管或平台管理员审核通过后，才能正式上线。\n3.  **安全策略**：为了防止专业的“羊毛党”通过技术手段刷单，我还需要设计一些基础的“反作弊”策略，例如限制同一个IP地址、同一个设备、同一个收货地址的参与次数等。\n\n![image-20250726110202924](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110202924.png)\n\n最后我们总结一下，以上就是我设计的营销规则体系。它就像一张无形的、精密的大网，确保了我们整个营销活动系统，能够在复杂多变的场景下，依然能够公平、稳定、安全地运行。\n\n\n\n## 3.3 营销工具\n在第三章的后续部分，我们将进入一个更有趣、更具互动性的领域——**营销工具**。它不再是简单的让利，而是通过游戏化的玩法，来提升用户的参与度和粘性，实现“品效合一”的营销目标。\n\n\n### 3.3.1 抽奖工具\n\n抽奖，是一种低成本、高回报的互动营销玩法。它通过设置有吸引力的奖品，来驱动用户完成我们期望的特定行为，如每日访问、分享拉新等。\n\n#### 1. 抽奖工具的需求分析\n\n在我动手设计具体的产品功能前，我必须首先回归原点，搞清楚我们“为什么”要做这个功能。\n\n![image-20250726112559235](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112559235.png)\n\n我之所以要在系统中，增加一个看似和“卖货”没有直接关系的“抽奖”功能，其核心驱动力，来自于商家提升用户活跃度与忠诚度的真实诉求。\n\n通过上图的需求背景，我提炼出抽奖工具需要满足的两大核心业务目标：\n1.  **提升老用户粘性**：通过每日免费抽奖等形式，为老用户提供一个持续访问我们App或店铺的理由，提升DAU（日活跃用户）。\n2.  **促进新用户增长**：将“分享”与“增加抽奖次数”进行绑定，激励老用户主动去进行社交分享，从而为店铺带来低成本的新流量。\n\n![image-20250726112647407](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112647407.png)\n\n明确了业务目标后，我就需要从最宏观的视角，来构思这个工具的完整生态。我首先要定义其中的“**核心角色**”和“**整体流程**”。\n\n* **核心角色**：抽奖工具的生态中，主要有三方参与者：\n    * **平台方**：我作为平台的产品经理，负责设计和提供稳定、通用的抽奖工具。\n    * **商家**：是抽奖活动的发起者和成本承担者，他们使用我提供的工具，来配置活动规则和奖品。\n    * **用户**：是抽奖活动的最终参与者。\n* **整体流程**：整个业务的生命周期，是一个清晰的闭环。如上图所示，`平台提供工具` -> `商家配置活动` -> `用户参与抽奖` -> `商家发放奖品` -> `用户查看奖品`。我的产品设计，必须确保这个链条上的每一个环节都顺畅无误。\n\n在宏观流程中，“**用户参与抽奖活动**”是整个玩法能否成功的关键。那么，用户的体验旅程应该是怎样的呢？\n\n![image-20250726112736166](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112736166.png)\n\n为了让用户体验顺畅、且富有激励性，我为C端用户设计了上面这条完整的“**参与流程**”闭环。\n\n我们来一步步拆解这个流程：\n1.  **触发抽奖**：用户进入活动页面，点击“立即抽奖”按钮。\n2.  **前置判断**：系统首先判断用户“是否还有抽奖次数”。\n3.  **次数用完**：如果次数已用完，系统会弹出提示，并引导用户去“**通过分享获得更多抽奖次数**”。这正是我们实现拉新裂变的关键设计。\n4.  **执行抽奖**：如果次数未用完，系统则根据后台配置的算法，来判断本次抽奖“是否中奖”。\n5.  **结果反馈**：\n    * 如果**中奖**，则弹出“恭喜中奖”的强提示，并引导用户去“查看奖品信息”。\n    * 如果**未中奖**，则弹出“谢谢参与”的安慰性提示。\n\n![image-20250726124606407](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124606407.png)\n\n通过对业务目标、宏观流程、用户旅程的完整分析，我就为接下来进行具体的“产品设计”，打下了坚实的基础。\n\n---\n\n#### 2. 抽奖工具的产品设计\n\n根据我们之前的分析，抽奖工具的设计，必须同时兼顾**商家端（B端）**的易用性和灵活性，以及**用户端（C端）**的趣味性和流畅体验。我将为你分别进行拆解。\n\n##### **一、 商家端（B端）产品设计**\n\n我们首先来看商家后台的设计。我需要为商家提供一个足够强大，但操作又不能过于复杂的活动创建流程。\n\n![image-20250726124513436](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124513436.png)\n\n我设计的商家创建流程，依然遵循我们熟悉的“四部曲”，确保了后台操作的一致性。接下来，我们详细看一下每一步的具体设计。\n\n**1. 设置基本信息**\n\n![image-20250726124625992](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124625992.png)\n\n这是活动创建的第一步，商家需要在这里设置活动的“身份信息”，包括`活动名称`、`活动时间`、`活动平台`（是在App内还是H5页面生效）以及`活动说明`（即活动规则的文字描述）。\n\n**2. 填写抽奖规则**\n\n![image-20250726124651499](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124651499.png)\n\n这是抽奖功能设计的灵魂，它决定了整个活动的核心玩法。\n\n![image-20250726124725381](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124725381.png)\n\n在设计这部分功能时，我主要思考并解决了上面这两个核心问题。\n\n* **抽奖类型**：我为商家提供了两种模式，来回答第一个问题。\n    * `即时抽奖`：用户抽完立刻知道结果。这是最常见的模式，能提供即时反馈和刺激。\n    * `非即时抽奖`：用户参与后，需要等待统一的开奖时间（例如，每周五开奖）。这种模式适用于需要营造悬念和持续关注度的活动。\n* **抽奖条件**：我允许商家设置参与门槛，例如用户必须`使用XX积分`、达到`XX会员等级`，或者`完成订单后`才能获得抽奖资格。\n* **参与次数**：商家可以灵活控制用户参与的频率，是`每人每天可抽N次`，还是在整个活动周期内`每人一共可抽N次`。\n* **分享设置**：这是实现裂变增长的关键。我需要让商家可以配置“**用户分享活动后，可以额外增加N次抽奖机会**”的规则。\n* **提示文案**：为了让体验更友好，我允许商家自定义各种场景下的提示文案，如`中奖提示`、`未中奖提示`、`活动未开始提示`等。\n\n**3. 选择抽奖奖品**\n\n![image-20250726124857807](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124857807.png)\n\n在这一步，商家需要设置本次活动的“奖池”。为了回答“如何控制奖品数量”这个问题，我要求商家在设置每一个奖品时，都必须明确两项核心信息：**`奖品数量`**和**`中奖概率`**。系统会根据这两项配置，通过抽奖算法来精确控制奖品的发放。\n\n为了丰富奖品的类型，我设计的奖池支持多种奖品形态：\n\n* **实物商品**\n\n    ![image-20250726125038417](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125038417.png)\n\n* **优惠券**\n    ![image-20250726125104812](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125104812.png)\n    \n* **积分**\n    ![image-20250726125113561](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125113561.png)\n\n\n\n![image-20250726125326357](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125326357.png)\n\n![image-20250726125341599](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125341599.png)\n\n通过这种模块化的设计，商家就可以非常灵活地配置出具有吸引力的奖品组合。\n\n##### **二、 用户端（C端）产品设计**\n\n当商家在后台配置好活动后，C端用户看到和体验到的，必须是一个有趣、流畅的界面。\n\n**1. 抽奖主页面**\n\n![image-20250726125205586](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125205586.png)\n\n![image-20250726125128157](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125128157.png)\n\n我采用了最经典的“**九宫格**”抽奖样式。这个页面的核心元素包括：\n* **抽奖区域**：九个格子中，分布着不同的奖品和“谢谢参与”的选项。\n* **抽奖按钮**：用户点击“立即抽奖”，转盘开始转动。当用户次数用尽，按钮会变为“明天再来”或“分享获取次数”等不可用状态。\n* **中奖名单**：页面下方会实时滚动最新的中奖信息，营造一种热闹、很多人中奖的氛围，来激励其他用户参与。\n\n**2. 抽奖结果反馈**\n\n对于“即时抽奖”来说，及时的结果反馈至关重要。\n* **中奖**：立刻弹出强提示的“恭喜中奖”弹窗，告知用户获得了什么奖品。\n* **未中奖**：弹出安慰性的“祝您下次中奖”弹窗，并引导用户“下次再来”。\n\n**3. 我的奖品列表**\n\n\n所有用户中奖的记录，都会沉淀在“**我的中奖记录**”这个页面。用户可以在这里，清晰地看到自己获得的所有奖品，以及每一个奖品的当前状态，是“**待兑换**”还是“**已兑换**”，方便进行后续的核销与使用。\n\n---\n### 3.3.2 优惠券工具\n\n如果说“抽奖”是提升趣味性和互动性的利器，那么“**优惠券**”则是我工具箱中，用途最广泛、玩法最灵活、最能实现精细化运营的“万能钥匙”。它几乎可以和任何营销场景进行组合，是我们刺激用户行为、提升转化和复购的核心手段。\n\n![image-20250726130639644](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130639644.png)\n\n#### 1.优惠券工具的需求分析\n\n![image-20250726130159412](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130159412.png)\n\n我为什么要设计一套独立的、复杂的优惠券系统？我们来看一个商家最常见的困惑：**用户在一次大促中尽兴消费后，就“消失”了，如何能有效地把他们“拉回来”，产生第二次、第三次消费呢？**\n\n“满减”、“满折”这类活动，解决的是“当下”的转化问题。而优惠券，则是我用来连接“**当下**”与“**未来**”的桥梁。我将优惠券的核心业务价值，总结为两点：\n\n1.  **提升复购率**：通过在用户完成交易后、或在日常的互动中，向其发放一张带有有效期的优惠券，我就为他创造了一个“必须在未来某个时间点回来消费”的强烈理由。\n2.  **精准控制成本**：与全场打折不同，优惠券可以“**指哪打哪**”。我可以控制它的发放数量、发放人群、使用门槛和适用商品，从而将营销预算，精准地花在最有价值的用户和商品上。\n\n**1. 优惠券的构成要素**\n\n在设计功能前，我首先要像解剖麻雀一样，拆解“优惠券”这个事物的核心构成要素。\n\n一张小小的优惠券，看似简单，实则包含了丰富的信息和规则。我作为产品经理，在设计时必须考虑到以下所有要素：\n\n| **要素分类** | **核心字段** | **我的解读** |\n| :--- | :--- | :--- |\n| **券面价值** | `面值` / `折扣` | 这是优惠券最核心的价值。例如，10元代金券，或8折折扣券。 |\n| **使用门槛** | `使用条件` | 用户需要满足什么条件才能使用这张券。例如，“满100元可用”。无门槛券则没有此项。|\n| **适用范围** | `使用范围` / `使用平台` | 这张券可以用在哪些地方。是“全场通用”，还是仅限“购买A商品”可用？是仅限App内，还是小程序也可用？ |\n| **有效期限** | `使用时间` | 这是刺激用户在未来消费的关键。是“领取后7天内有效”，还是只能在“固定的10月1日到10月7日”之间使用？ |\n| **发放与领取**| `发放数量` / `领取人` | 这张券总共准备发多少张？是所有人都可以公开领取，还是只发给“VIP用户”的专属福利？ |\n\n只有将这些要素全部定义清楚，我才能设计出一套足够灵活、能满足各种运营场景的优惠券系统。\n\n**2. 优惠券的生命周期与用户旅程**\n\n![image-20250726130240979](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130240979.png)\n\n定义了优惠券的核心要素后，我们再从宏观视角，看一下优惠券从诞生到消亡的“一生”，也就是它的生命周期流程。\n\n* **核心角色**：**商家**（创建者和发放者）与**用户**（领取者和使用者）。\n* **生命周期**：`商家创建/发放` -> `用户领取` -> `用户使用` -> `商家统计`。我的产品设计，必须支撑起这个完整的闭环。\n\n对于用户来说，他们与优惠券的互动，主要发生在两个核心环节：“**领券**”和“**用券**”。\n\n* **“领券”环节的场景设计**\n\n![image-20250726130516515](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130516515.png)\n\n我必须在用户消费决策的关键路径上，为他们提供清晰、便捷的领券入口。例如，在商品详情页，我会明确地告诉用户“**本店现有以下优惠券可供领取**”，用户点击后，即可在弹窗中一键领取。\n\n* **“用券”环节的场景设计**\n\n![image-20250726130544241](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130544241.png)当用户选好了商品，来到订单结算页时，这是优惠券发挥作用的最后、也是最关键的环节。在这个页面，我会设计一个“优惠券”的选择栏，用户点击后：\n\n1.  系统会自动判断用户当前订单满足了哪些优惠券的使用门槛，将“**可用优惠券**”高亮地展示在最前方。\n2.  对于那些用户已领取、但当前订单“**不可用**”的优惠券，我也会展示出来，并清晰地告知用户“不可用”的原因（例如，“未达到满减金额”）。这是一种反向的激励，可能会促使用户返回去，再多买一件商品来凑单。\n\n通过对业务目标、核心要素、生命周期和用户旅程的完整分析，我们就为接下来进行具体的B端“优惠券创建”功能设计，铺平了道路。\n\n好的，我们已经清晰地定义了优惠券工具的需求。接下来，我将带你进入产品设计的核心环节，看看我是如何将这些需求，转化为一个强大、灵活且易于商家使用的后台功能。\n\n\n\n---\n\n#### 2.优惠券工具的产品设计\n\n我的设计哲学是，**把复杂留给自己，把简单交给用户**。对于优惠券这种玩法极其丰富的工具，B端（商家端）的设计尤其考验产品经理的抽象和归纳能力。我需要将万千种运营场景，收敛到一套结构化、标准化的创建流程中。\n\n![image-20250726131021810](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726131021810.png)\n\n我设计的优惠券创建过程，主要分为三大步骤：**设置基本信息 -> 填写领取规则 -> 填写使用规则**。\n\n##### 一、设置基本信息与领取规则\n\n![image-20250726131936090](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726131936090.png)\n\n在创建优惠券的第一步，我将“基本信息”和“领取规则”放在了一起，因为它们共同定义了这张优惠券的“**身份**”和“**发放方式**”。\n\n* **基本信息**：\n    * `优惠券名称`：方便商家在后台进行识别和管理。\n    * `优惠券数量`：即“库存”，控制了这张券的总发放量，是控制成本的第一道闸门。\n    * `使用平台`：定义这张券是在App、H5还是小程序中生效。\n* **领取规则**：这是实现“**精细化运营**”的关键。\n    * `领取用户`：我为商家提供了多种用户圈定方式。可以是`全部用户`可领的普惠券；也可以是针对`用户等级`（如：钻石会员专享）或`用户标签`（如：高潜流失用户）的精准券；甚至支持`上传文件`，针对特定的用户ID列表进行一对一发放。\n    * `领取张数`：可以限制`每人限领N张`，防止被“羊毛党”恶意刷取。\n    * `领取时间`：定义这张优惠券可以被领取的起止时间。\n    * `公开设置`：这是一个非常重要的开关。如果勾选了“**公开领取**”，这张券就会出现在商品详情页等C端入口，供用户主动领取。如果不勾选，它就是一张“**私有券**”，不会对外展示，只能由运营人员通过后台手动发放给指定用户，常用于客服补偿等场景。\n\n##### 二、填写使用规则——玩法的核心\n\n这是优惠券设计的灵魂所在。一张券到底“怎么用”，决定了它的营销价值。我设计了多种优惠券类型，来满足不同的业务场景。\n\n**2.1 满减券的设计**\n\n![image-20250726132042176](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132042176.png)\n\n这是最常见的一种优惠券。它的核心规则包括：\n\n* **优惠券类型**：首先，我定义了券的适用范围。是仅限购买某些商品的`商品券`，还是全场通用的`通用券`，或者是只能抵扣运费的`运费券`。\n* **使用门槛**：即“满X元可用”。\n* **优惠券面额**：即“减Y元”。\n* **有效期**：这是刺激用户复购的关键。我设计了两种模式：\n    * `固定时间`：例如，国庆节专用券，只能在10月1日到10月7日之间使用。\n    * `相对时效`：这种模式更为灵活，例如`自领取之日起N天内可用`，或者`自领取次日起N天内可用`。这能确保每个领到券的用户，都有一个完整的有效期。\n* **适用范围**：这里可以更精细地控制券能用于哪些商品，是`全部商品`，还是`指定商品`、`指定类目`或`指定品牌`。\n\n![image-20250726132157048](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132157048.png)\n\n当商家选择“指定商品”时，我会提供一个与我们之前设计完全一致的、可复用的商品选择器组件，让他可以方便地进行勾选。\n\n**2.2 折扣券的设计**\n\n![image-20250726132136682](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132136682.png)\n\n折扣券的设计，与满减券大部分相同，核心区别在于“优惠方式”的定义。商家不再是输入一个固定的“面额”，而是输入一个“**折扣率**”，例如“打8折”。\n\n**2.3 立减券的设计**\n\n![image-20250726132143949](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132143949.png)\n\n立减券，又称“现金券”，是优惠力度最大的一种。它的特点是“**无使用门槛**”。在设计上，我只需要让商家输入一个“**立减金额**”即可。这种券通常用于新用户注册礼包、或高价值用户的回归召回等关键场景。\n\n##### 三、 优惠券的管理\n\n![image-20250726132209133](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132209133.png)\n\n当商家创建完所有优惠券后，他可以在这张“**优惠券管理列表**”中，对所有券进行统一的查看和操作。\n\n我为这张列表设计了清晰的信息维度：\n* **核心信息**：优惠券的`名称`、`类型`（满减/折扣）、`发放数量`、`有效期`等一目了然。\n* **优惠券状态**：我通过`未开始`、`领取中`、`已结束`、`已失效`这几种状态，让商家可以清晰地了解每一张券当前的生命周期阶段。\n* **快捷操作**：商家可以对不同状态的券，进行`查看`、`编辑`、`结束活动`或查看`数据`等操作。\n\n通过以上这套B端产品设计，我就为商家提供了一个功能强大、配置灵活、管理方便的“优惠券弹药库”，让他们可以根据不同的营销战役，自由地组合和使用这些“弹药”。\n\n---\n#### 3. 优惠券的逻辑规则\n\n当一个订单中，存在多个商品、多张可用优惠券时，系统必须有一套清晰的规则，来决定最终如何计算优惠。我将它总结为三大核心规则。\n\n##### **规则一：叠加与互斥**\n\n![image-20250726132817387](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132817387.png)\n\n这个问题的答案，取决于优惠券的类型。我制定的核心原则非常简单：**同一类型的优惠券，一个订单只能使用一张；不同类型的优惠券，在不冲突的情况下，可以叠加使用。**\n\n例如，用户可以同时使用一张“店铺满减券”、一张“平台品类券”和一张“运费券”，但不能同时使用两张“店铺满减券”。\n\n![image-20250726132847661](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132847661.png)\n\n上图就是一个非常典型的真实案例，一个订单同时叠加了多种不同类型的优惠，最终形成了一个极具吸引力的价格。我的系统设计，就必须能够支持这种复杂的叠加计算。\n\n![image-20250726133202001](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726133202001.png)\n\n我们来看上面这个非常经典的、模拟真实购物场景的案例。要判断这4张优惠券能否同时使用，我设计的系统，会遵循以下逻辑进行严谨的校验。\n\n**第一步：识别每张优惠券的“类型”与“范围”**\n\n我首先会将这4张券进行归类：\n\n| **优惠券**  | **券类型** | **作用范围**        | **使用门槛**      |\n| ----------- | ---------- | ------------------- | ----------------- |\n| **优惠券1** | `商品券`   | 仅限“鼠标”这个商品  | 无门槛（立减）    |\n| **优惠券2** | `店铺券`   | 仅限“A店铺”内的商品 | 订单金额满100元   |\n| **优惠券3** | `平台券`   | 跨店铺所有商品      | 订单总金额满400元 |\n| **优惠券4** | `运费券`   | 仅限“A店铺”的运费   | 无门槛（免邮）    |\n\n**第二步：逐一校验每张券的“门槛”与“叠加规则”**\n\n1. **校验【优惠券1】**：它是一张`商品券`，直接作用于鼠标，无使用门槛。**可用**。\n2. **校验【优惠券2】**：它是一张`店铺券`。计算A店铺的商品总价为 `99元(鼠标) + 30元(鼠标垫) = 129元`。这个价格满足了“满100元”的使用门槛。由于它和优惠券1的类型（`店铺券` vs `商品券`）不同，因此**可叠加使用**。\n3. **校验【优惠券3】**：它是一张`平台券`。计算跨店订单的总价为 `129元(A店铺) + 398元(B店铺) = 527元`。这个价格满足了“满400元”的使用门槛。由于它和前两张券的类型（`平台券` vs `店铺券`/`商品券`）都不同，因此**可叠加使用**。\n4. **校验【优惠券4】**：它是一张`运费券`，属于特殊类型，用于抵扣A店铺的运费，通常可以和所有其他类型的优惠券叠加。**可用**。\n\n**第三步：得出最终结论**\n\n- **这4张优惠券可以同时使用吗？**\n\t- **可以。** 因为这四张券分别属于**商品券、店铺券、平台券、运费券**，类型各不相同，且订单情况满足了它们各自的使用门槛，因此它们可以完美地叠加使用。\n- **系统应该推荐使用哪张优惠券呢？**\n\t- **全部推荐使用。** 在这个场景下，由于所有券都可以叠加，并且都能带来优惠，系统的最优策略就是默认将这4张券**全部勾选并应用**，从而为用户计算出最终的、优惠力度最大的订单价格。\n\n在电商后台，我定义优惠券的“类型”，其核心依据，并不是它“长什么样”，而是它的“**作用范围**”和“**成本由谁承担**”。只有基于这两个维度，我才能建立起一套严谨、无歧义的叠加互斥规则。\n\n我将优惠券，严格划分为以下几个**层级完全不同**的类型：\n\n| **优惠券类型** | **定义与作用范围** | **成本承担方** | **核心目的** |\n| :--- | :--- | :--- | :--- |\n| **单品券** | **层级最低**。仅对指定的某一个商品（SKU/SPU）生效。 | 商家 | 推广单一爆款或清仓。 |\n| **店铺券** | **层级居中**。对**指定店铺内**的所有或部分商品生效。 | **商家** | 提升**本店的客单价**和转化率。 |\n| **平台券** | **层级最高**。可**跨店使用**，对平台上所有或部分店铺的商品生效。| **平台** | 提升**整个平台的GMV**和用户活跃度。 |\n| **运费券** | **类型特殊**。仅用于抵扣运费。 | 商家 或 平台 | 降低用户的购买决策门槛。 |\n\n**核心规则**：只有**同一个层级**的优惠券，才存在“互斥”关系。**不同层级**的优惠券，因为其作用范围和成本方完全不同，所以**可以叠加**。\n\n-----\n\n##### **规则二：推荐原则**\n\n当一个订单同时满足多张优惠券的使用门槛时，系统应该如何帮助用户做出最优决策，商家或许并不想让用户同时使用多张卷，所以在我们上一小结的设计中，三个劵同时归类为了`商品券`，这时候我们的优先计算原则就是优惠最大的金额\n\n![image-20250726133421088](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726133421088.png)\n\n我的设计原则是——**永远帮助用户做出“最省钱”的选择**。系统后台会自动计算所有可能的、可叠加的优惠券组合方式，并默认选中那个“**优惠总金额最大**”的最佳组合。\n\n我们来看一个复杂的实战案例：\n\n![image-20250726134305240](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134305240.png)\n\n面对这个复杂的场景，我的系统后台会进行如下的智能计算：\n\n![image-20250726134246505](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134246505.png)\n\n如上图所示，系统会：\n1.  **匹配**：首先判断每个商品分别适用哪些优惠券。\n2.  **组合**：然后尝试所有可行的叠加组合。\n3.  **择优**：最后计算出“水杯类满减券 + A店铺满减券 + 运费券”这个组合，可以优惠131元，是所有组合中优惠力度最大的，因此系统会向用户默认推荐这个方案。\n\n##### **规则三：分摊规则**\n\n我们再次遇到了这个至关重要的财务规则。\n\n![image-20250726134517071](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134517071.png)\n\n当一个订单使用了多张、作用范围不同的优惠券后，发生部分退款时，分摊计算就变得更加复杂。我将这个计算过程，用一张表格为您清晰地呈现：\n\n![image-20250726134534335](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134534335.png)\n\n**最终结论**：当用户想要退货A商品（鼠标，原价99元）时，我设计的系统会从其原价中，扣除掉它所分摊到的`23.35`元优惠，最终应退款 `99 - 23.35 = 75.65` 元。只有这样，才能保证财务的绝对严谨。\n\n#### 4. 优惠券工具的数据\n\n作为一名专业的产品或运营，我绝不能只满足于“把功能做出来”。我必须知道我策划的每一次活动，效果如何，成本怎样。因此，为优惠券工具设计一套完善的“**数据详情**”，是必不可少的一步。\n\n![image-20250726134849709](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134849709.png)\n\n我将优惠券的数据监控，分为了四大维度，共计11个核心指标：\n\n通过对这11个核心数据指标的持续监控和分析，我作为运营，就能够精准地洞察每一次优惠券活动的成败得失，并为下一次的优化，提供可靠的数据支撑。\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E7%AB%A0%EF%BC%9A%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8"><span class="toc-number">1.</span> <span class="toc-text">第三章：活动管理-总价活动</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#3-1-%E8%90%A5%E9%94%80%E6%B4%BB%E5%8A%A8"><span class="toc-number">1.1.</span> <span class="toc-text">3.1 营销活动</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-1-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8-%E6%BB%A1%E5%87%8F%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86"><span class="toc-number">1.1.1.</span> <span class="toc-text">3.1.1 总价活动-满减活动管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-2-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8-%E6%BB%A1%E8%B5%A0%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86"><span class="toc-number">1.1.2.</span> <span class="toc-text">3.1.2 总价活动-满赠活动管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-3-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8-%E6%BB%A1%E6%8A%98%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86"><span class="toc-number">1.1.3.</span> <span class="toc-text">3.1.3 总价活动-满折活动管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-4-%E6%80%BB%E4%BB%B7%E6%B4%BB%E5%8A%A8-%E5%A5%97%E8%A3%85%E6%B4%BB%E5%8A%A8"><span class="toc-number">1.1.4.</span> <span class="toc-text">3.1.4 总价活动-套装活动</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-2-%E8%90%A5%E9%94%80%E8%A7%84%E5%88%99"><span class="toc-number">1.2.</span> <span class="toc-text">3.2 营销规则</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-1-%E8%A7%84%E5%88%99%E4%B8%80%EF%BC%9A%E5%8F%A0%E5%8A%A0%E4%B8%8E%E4%BA%92%E6%96%A5"><span class="toc-number">1.2.1.</span> <span class="toc-text">3.2.1 规则一：叠加与互斥</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-2-%E8%A7%84%E5%88%99%E4%BA%8C%EF%BC%9A%E6%B4%BB%E5%8A%A8%E9%A1%BA%E5%BA%8F"><span class="toc-number">1.2.2.</span> <span class="toc-text">3.2.2 规则二：活动顺序</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-3-%E8%A7%84%E5%88%99%E4%B8%89%EF%BC%9A%E4%BC%98%E6%83%A0%E5%88%86%E6%91%8A%E8%A7%84%E5%88%99%EF%BC%88%E6%B7%B1%E5%BA%A6%E8%A7%A3%E6%9E%90%EF%BC%89"><span class="toc-number">1.2.3.</span> <span class="toc-text">3.2.3 规则三：优惠分摊规则（深度解析）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9F%BA%E7%A1%80%E5%9C%BA%E6%99%AF%EF%BC%9A%E5%8D%95%E4%B8%80%E6%80%BB%E4%BB%B7%E4%BC%98%E6%83%A0%E7%9A%84%E5%88%86%E6%91%8A"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">1. 基础场景：单一总价优惠的分摊</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%BF%9B%E9%98%B6%E9%97%AE%E9%A2%98%EF%BC%9A%E8%AE%A1%E7%AE%97%E7%B2%BE%E5%BA%A6%E4%B8%8E%E5%B0%BE%E5%B7%AE%E5%A4%84%E7%90%86"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">2. 进阶问题：计算精度与尾差处理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%BB%88%E6%9E%81%E6%8C%91%E6%88%98%EF%BC%9A%E5%A4%9A%E5%95%86%E5%93%81%E3%80%81%E5%A4%9A%E6%B4%BB%E5%8A%A8%E3%80%81%E5%A4%9A%E5%B1%82%E7%BA%A7%E4%BC%98%E6%83%A0%E7%9A%84%E6%B7%B7%E5%90%88%E5%88%86%E6%91%8A"><span class="toc-number">1.2.3.3.</span> <span class="toc-text">3. 终极挑战：多商品、多活动、多层级优惠的混合分摊</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-4-%E8%A7%84%E5%88%99%E5%9B%9B%EF%BC%9A%E9%A3%8E%E9%99%A9%E4%B8%8E%E9%98%B2%E8%8C%83"><span class="toc-number">1.2.4.</span> <span class="toc-text">3.2.4 规则四：风险与防范</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-3-%E8%90%A5%E9%94%80%E5%B7%A5%E5%85%B7"><span class="toc-number">1.3.</span> <span class="toc-text">3.3 营销工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-1-%E6%8A%BD%E5%A5%96%E5%B7%A5%E5%85%B7"><span class="toc-number">1.3.1.</span> <span class="toc-text">3.3.1 抽奖工具</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%8A%BD%E5%A5%96%E5%B7%A5%E5%85%B7%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">1. 抽奖工具的需求分析</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%8A%BD%E5%A5%96%E5%B7%A5%E5%85%B7%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">2. 抽奖工具的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%80%E3%80%81-%E5%95%86%E5%AE%B6%E7%AB%AF%EF%BC%88B%E7%AB%AF%EF%BC%89%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.1.2.1.</span> <span class="toc-text">一、 商家端（B端）产品设计</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BA%8C%E3%80%81-%E7%94%A8%E6%88%B7%E7%AB%AF%EF%BC%88C%E7%AB%AF%EF%BC%89%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.1.2.2.</span> <span class="toc-text">二、 用户端（C端）产品设计</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-2-%E4%BC%98%E6%83%A0%E5%88%B8%E5%B7%A5%E5%85%B7"><span class="toc-number">1.3.2.</span> <span class="toc-text">3.3.2 优惠券工具</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BC%98%E6%83%A0%E5%88%B8%E5%B7%A5%E5%85%B7%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">1.优惠券工具的需求分析</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BC%98%E6%83%A0%E5%88%B8%E5%B7%A5%E5%85%B7%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.2.2.</span> <span class="toc-text">2.优惠券工具的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%80%E3%80%81%E8%AE%BE%E7%BD%AE%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AF%E4%B8%8E%E9%A2%86%E5%8F%96%E8%A7%84%E5%88%99"><span class="toc-number">1.3.2.2.1.</span> <span class="toc-text">一、设置基本信息与领取规则</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BA%8C%E3%80%81%E5%A1%AB%E5%86%99%E4%BD%BF%E7%94%A8%E8%A7%84%E5%88%99%E2%80%94%E2%80%94%E7%8E%A9%E6%B3%95%E7%9A%84%E6%A0%B8%E5%BF%83"><span class="toc-number">1.3.2.2.2.</span> <span class="toc-text">二、填写使用规则——玩法的核心</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%89%E3%80%81-%E4%BC%98%E6%83%A0%E5%88%B8%E7%9A%84%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.2.2.3.</span> <span class="toc-text">三、 优惠券的管理</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%BC%98%E6%83%A0%E5%88%B8%E7%9A%84%E9%80%BB%E8%BE%91%E8%A7%84%E5%88%99"><span class="toc-number">1.3.2.3.</span> <span class="toc-text">3. 优惠券的逻辑规则</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%A7%84%E5%88%99%E4%B8%80%EF%BC%9A%E5%8F%A0%E5%8A%A0%E4%B8%8E%E4%BA%92%E6%96%A5"><span class="toc-number">1.3.2.3.1.</span> <span class="toc-text">规则一：叠加与互斥</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%A7%84%E5%88%99%E4%BA%8C%EF%BC%9A%E6%8E%A8%E8%8D%90%E5%8E%9F%E5%88%99"><span class="toc-number">1.3.2.3.2.</span> <span class="toc-text">规则二：推荐原则</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E8%A7%84%E5%88%99%E4%B8%89%EF%BC%9A%E5%88%86%E6%91%8A%E8%A7%84%E5%88%99"><span class="toc-number">1.3.2.3.3.</span> <span class="toc-text">规则三：分摊规则</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E4%BC%98%E6%83%A0%E5%88%B8%E5%B7%A5%E5%85%B7%E7%9A%84%E6%95%B0%E6%8D%AE"><span class="toc-number">1.3.2.4.</span> <span class="toc-text">4. 优惠券工具的数据</span></a></li></ol></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>