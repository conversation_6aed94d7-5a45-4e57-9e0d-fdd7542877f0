<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Python（六）：第五章：数据类型 | Prorise的小站</title><meta name="keywords" content="Python基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Python（六）：第五章：数据类型"><meta name="application-name" content="Python（六）：第五章：数据类型"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="Python（六）：第五章：数据类型"><meta property="og:url" content="https://prorise666.site/posts/50189.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第五章：数据类型5.1 字符串类型(str)字符串输出方式   占位符类型 说明    %s 字符串（使用 str() 方法转换任何 Python 对象）   %d 十进制整数   %f 十进制浮点数(小数), 自动保留六位小数。   123456789101112# 占位符方式print(&amp;amp;#x2"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta name="description" content="第五章：数据类型5.1 字符串类型(str)字符串输出方式   占位符类型 说明    %s 字符串（使用 str() 方法转换任何 Python 对象）   %d 十进制整数   %f 十进制浮点数(小数), 自动保留六位小数。   123456789101112# 占位符方式print(&amp;amp;#x2"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/50189.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"Python（六）：第五章：数据类型",postAI:"true",pageFillDescription:"第五章：数据类型, 5.1 字符串类型(str), 字符串输出方式, 字符串常用方法, 5.2 列表类型(list), 添加元素, 删除元素, 查找元素, 排序, 访问嵌套列表, 5.3 元组类型(tuple), 元组的创建方式, 元组的特性与操作, 不可变性, 访问与切片, 元组解包, 元组方法和内置函数, 元组的应用场景, 作为函数的返回值, 作为不可变集合, 确保数据不被修改, , 5.4 字典类型(dict), 字典常用方法, 5.5 集合类型(set), 集合操作第五章数据类型字符串类型字符串输出方式占位符类型说明字符串使用方法转换任何对象十进制整数十进制浮点数小数自动保留六位小数占位符方式我的名字是我的年龄是岁小明我的名字是我的年龄是岁小明带索引我的名字是我的年龄是岁小明方式推荐小明我的名字是我的年龄是岁字符串常用方法方法描述示例判断是否以指定内容开头判断是否以指定内容结尾判断是否为数字组成判断是否为文字组成统计元素出现次数查找子串位置未找到返回转为大写转为小写替换字符串分割字符串为列表拼接列表为字符串去除两端空格或指定字符去除左侧空格或指定字符去除右侧空格或指定字符将字符串标题化字符串方法示例判断是否以指定内容开头是否以开头是否以开头判断是否以指定内容结尾是否以结尾是否以结尾判断是否为数字组成是否全为数字是否全为数字判断是否为文字组成是否全为字母是否全为字母统计元素出现次数中出现的次数中出现的次数查找子串位置未找到返回中的位置中的位置转为大写转大写转为小写转小写替换字符串替换为替换所有为分割字符串为列表按逗号分割按空格分割拼接列表为字符串用逗号连接用空格连接去除两端空格或指定字符去除两端空格去除两端的去除左侧空格或指定字符去除左侧空格去除左侧的去除右侧空格或指定字符去除右侧空格去除右侧的将字符串标题化每个单词首字母大写标题化列表类型添加元素在列表末尾添加元素批量添加元素逐一添加在指定位置插入元素删除元素删除指定元素第一个匹配项删除指定索引元素返回被删除的元素不指定索引默认删除最后一个清空列表删除指定元素或切片删除单个元素删除切片范围的元素查找元素查找元素索引返回第一个匹配元素的索引运算符判断元素是否存在元素存在同时获取索引和元素索引值统计元素出现次数返回元素出现次数排序原地排序降序返回新列表原列表不变原列表不变自定义排序按字符串长度可以编写一个匿名函数作为条件使用函数结合函数进行复杂排序创建一个包含学生信息的列表姓名年龄成绩是否参加课外活动张三李四王五赵六钱七基本排序按照学生成绩从高到低排序定义一个匿名函数接收一个参数列表中的每个元素返回的值表示降序排序从高到低按照学生成绩从高到低排序姓名年龄学生成绩访问嵌套列表访问嵌套列表的元素元组类型元组的创建方式使用小括号创建省略小括号创建单元素元组必须加逗号否则只是普通值不是元组正确这是一个元组错误这是一个整数不是元组正确这也是一个元组使用函数从其他可迭代对象创建从列表创建从字符串创建从字典创建只保留键创建空元组嵌套元组不同类型的元素元组的特性与操作不可变性但可以通过连接创建新元组新元组重复元组元组的不可变性让它可以作为字典的键元组中可变对象的行为元组中的可变对象如列表的内容可以修改正确修改元组中列表的内容重要说明元组的不可变性只适用于元组本身的结构元素的标识符不能改变而不适用于元组中所包含的可变对象的内容访问与切片索引访问从开始负索引从末尾开始切片步长为逆序元组解包基本解包使用星号收集多余的元素忽略某些值使用下划线作为惯例交换变量值使用元组解包交换值元组方法和内置函数元组的方法比列表少因为它是不可变的主要方法有计算元素出现的次数返回元素首次出现的索引使用内置函数元组长度最小元素最大元素元素和返回排序后的列表非元组元组的应用场景作为函数的返回值返回多个值调用函数并解包返回值判断返回值是否为一个元组姓名年龄邮箱输出姓名年龄邮箱返回值不是一个元组作为不可变集合使用元组作为字典键更新字典不更改元组确保数据不被修改是一个元组确保处理过程中不被修改处理配置安全的操作使用字典类型创建字典张三北京访问值张三设置修改值添加新键值对删除键值对字典常用方法方法描述示例获取值键不存在时返回默认值默认值获取所有键获取所有值获取所有键值对获取值键不存在则设置默认值默认值更新字典移除指定键值对并返回值集合类型创建集合结果添加元素删除元素元素不存在不会报错元素不存在会报错集合操作操作描述示例交集共同元素并集去重合并差集属于不属于对称差集不同时属于和求两个集合的交集共同元素求两个集合的并集去重后在合并求两个集合的差集中有而中没有的元素求两个集合的对称差集和中不同时存在的元素",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-13 22:13:01",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E7%AB%A0%EF%BC%9A%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B"><span class="toc-text">第五章：数据类型</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-%E5%AD%97%E7%AC%A6%E4%B8%B2%E7%B1%BB%E5%9E%8B-str"><span class="toc-text">5.1 字符串类型(str)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AD%97%E7%AC%A6%E4%B8%B2%E8%BE%93%E5%87%BA%E6%96%B9%E5%BC%8F"><span class="toc-text">字符串输出方式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AD%97%E7%AC%A6%E4%B8%B2%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95"><span class="toc-text">字符串常用方法</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-2-%E5%88%97%E8%A1%A8%E7%B1%BB%E5%9E%8B-list"><span class="toc-text">5.2 列表类型(list)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%B7%BB%E5%8A%A0%E5%85%83%E7%B4%A0"><span class="toc-text">添加元素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%88%A0%E9%99%A4%E5%85%83%E7%B4%A0"><span class="toc-text">删除元素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%9F%A5%E6%89%BE%E5%85%83%E7%B4%A0"><span class="toc-text">查找元素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%8E%92%E5%BA%8F"><span class="toc-text">排序</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%AE%BF%E9%97%AE%E5%B5%8C%E5%A5%97%E5%88%97%E8%A1%A8"><span class="toc-text">访问嵌套列表</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-%E5%85%83%E7%BB%84%E7%B1%BB%E5%9E%8B-tuple"><span class="toc-text">5.3 元组类型(tuple)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E7%9A%84%E5%88%9B%E5%BB%BA%E6%96%B9%E5%BC%8F"><span class="toc-text">元组的创建方式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E7%9A%84%E7%89%B9%E6%80%A7%E4%B8%8E%E6%93%8D%E4%BD%9C"><span class="toc-text">元组的特性与操作</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%8D%E5%8F%AF%E5%8F%98%E6%80%A7"><span class="toc-text">不可变性</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%AE%BF%E9%97%AE%E4%B8%8E%E5%88%87%E7%89%87"><span class="toc-text">访问与切片</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E8%A7%A3%E5%8C%85"><span class="toc-text">元组解包</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E6%96%B9%E6%B3%95%E5%92%8C%E5%86%85%E7%BD%AE%E5%87%BD%E6%95%B0"><span class="toc-text">元组方法和内置函数</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E7%9A%84%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">元组的应用场景</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BD%9C%E4%B8%BA%E5%87%BD%E6%95%B0%E7%9A%84%E8%BF%94%E5%9B%9E%E5%80%BC"><span class="toc-text">作为函数的返回值</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BD%9C%E4%B8%BA%E4%B8%8D%E5%8F%AF%E5%8F%98%E9%9B%86%E5%90%88"><span class="toc-text">作为不可变集合</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%A1%AE%E4%BF%9D%E6%95%B0%E6%8D%AE%E4%B8%8D%E8%A2%AB%E4%BF%AE%E6%94%B9"><span class="toc-text">确保数据不被修改</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#postchat_postcontent"><span class="toc-text"></span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-%E5%AD%97%E5%85%B8%E7%B1%BB%E5%9E%8B-dict"><span class="toc-text">5.4 字典类型(dict)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AD%97%E5%85%B8%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95"><span class="toc-text">字典常用方法</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-%E9%9B%86%E5%90%88%E7%B1%BB%E5%9E%8B-set"><span class="toc-text">5.5 集合类型(set)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%9B%86%E5%90%88%E6%93%8D%E4%BD%9C"><span class="toc-text">集合操作</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Python基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Python（六）：第五章：数据类型</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-18T13:13:45.000Z" title="发表于 2025-04-18 21:13:45">2025-04-18</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.528Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>15分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Python（六）：第五章：数据类型"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/50189.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/50189.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Python基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Python（六）：第五章：数据类型</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-04-18T13:13:45.000Z" title="发表于 2025-04-18 21:13:45">2025-04-18</time><time itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.528Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></header><div id="postchat_postcontent"><h2 id="第五章：数据类型"><a href="#第五章：数据类型" class="headerlink" title="第五章：数据类型"></a>第五章：数据类型</h2><h3 id="5-1-字符串类型-str"><a href="#5-1-字符串类型-str" class="headerlink" title="5.1 字符串类型(str)"></a>5.1 字符串类型(str)</h3><h4 id="字符串输出方式"><a href="#字符串输出方式" class="headerlink" title="字符串输出方式"></a>字符串输出方式</h4><table><thead><tr><th><code>占位符类型</code></th><th>说明</th></tr></thead><tbody><tr><td><code>%s</code></td><td>字符串（使用 <code>str()</code> 方法转换任何 Python 对象）</td></tr><tr><td><code>%d</code></td><td>十进制整数</td></tr><tr><td><code>%f</code></td><td>十进制浮点数(小数), 自动保留六位小数。</td></tr></tbody></table><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 占位符方式</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'我的名字是 %s, 我的年龄是 %d岁'</span> % (<span class="string">'小明'</span>, <span class="number">28</span>))</span><br><span class="line"></span><br><span class="line"><span class="comment">#    </span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'我的名字是 {}, 我的年龄是 {}岁'</span>.<span class="built_in">format</span>(<span class="string">'小明'</span>, <span class="number">28</span>))</span><br><span class="line"><span class="comment"># format带索引</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'我的名字是 {1}, 我的年龄是 {0}岁'</span>.<span class="built_in">format</span>(<span class="number">28</span>, <span class="string">'小明'</span>))</span><br><span class="line"></span><br><span class="line"><span class="comment"># f-string方式(推荐)</span></span><br><span class="line">name = <span class="string">'小明'</span></span><br><span class="line">age = <span class="number">28</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f'我的名字是 <span class="subst">{name}</span>, 我的年龄是 <span class="subst">{age}</span>岁'</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="字符串常用方法"><a href="#字符串常用方法" class="headerlink" title="字符串常用方法"></a>字符串常用方法</h4><table><thead><tr><th>方法</th><th>描述</th><th>示例</th></tr></thead><tbody><tr><td>startswith()</td><td>判断是否以指定内容开头</td><td><code>str.startswith('hello')</code></td></tr><tr><td>endswith()</td><td>判断是否以指定内容结尾</td><td><code>str.endswith('world')</code></td></tr><tr><td>isdigit()</td><td>判断是否为数字组成</td><td><code>'12345'.isdigit()</code></td></tr><tr><td>isalpha()</td><td>判断是否为文字组成</td><td><code>'hello'.isalpha()</code></td></tr><tr><td>count()</td><td>统计元素出现次数</td><td><code>'hello'.count('l')</code></td></tr><tr><td>find()</td><td>查找子串位置，未找到返回-1</td><td><code>'hello world'.find('world')</code></td></tr><tr><td>upper()</td><td>转为大写</td><td><code>'hello'.upper()</code></td></tr><tr><td>lower()</td><td>转为小写</td><td><code>'HELLO'.lower()</code></td></tr><tr><td>replace()</td><td>替换字符串</td><td><code>'hello'.replace('h', 'H')</code></td></tr><tr><td>split()</td><td>分割字符串为列表</td><td><code>'a,b,c'.split(',')</code></td></tr><tr><td>join()</td><td>拼接列表为字符串</td><td><code>','.join(['a', 'b', 'c'])</code></td></tr><tr><td>strip()</td><td>去除两端空格或指定字符</td><td><code>' hello '.strip()</code></td></tr><tr><td>lstrip()</td><td>去除左侧空格或指定字符</td><td><code>' hello'.lstrip()</code></td></tr><tr><td>rstrip()</td><td>去除右侧空格或指定字符</td><td><code>'hello '.rstrip()</code></td></tr><tr><td>title()</td><td>将字符串标题化</td><td><code>'hello world'.title()</code></td></tr></tbody></table><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># ======== 字符串方法示例 ========</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># startswith() - 判断是否以指定内容开头</span></span><br><span class="line">text = <span class="string">"Hello, World!"</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'Hello, World!' 是否以'Hello'开头: <span class="subst">{text.startswith(<span class="string">'Hello'</span>)}</span>"</span>)  <span class="comment"># True</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'Hello, World!' 是否以'World'开头: <span class="subst">{text.startswith(<span class="string">'World'</span>)}</span>"</span>)  <span class="comment"># False</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># endswith() - 判断是否以指定内容结尾</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'Hello, World!' 是否以'!'结尾: <span class="subst">{text.endswith(<span class="string">'!'</span>)}</span>"</span>)  <span class="comment"># True</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'Hello, World!' 是否以'Hello'结尾: <span class="subst">{text.endswith(<span class="string">'Hello'</span>)}</span>"</span>)  <span class="comment"># False</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># isdigit() - 判断是否为数字组成</span></span><br><span class="line">num_str = <span class="string">"12345"</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'12345' 是否全为数字: <span class="subst">{num_str.isdigit()}</span>"</span>)  <span class="comment"># True</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'Hello' 是否全为数字: <span class="subst">{<span class="string">'Hello'</span>.isdigit()}</span>"</span>)  <span class="comment"># False</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># isalpha() - 判断是否为文字组成</span></span><br><span class="line">alpha_str = <span class="string">"hello"</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello' 是否全为字母: <span class="subst">{alpha_str.isalpha()}</span>"</span>)  <span class="comment"># True</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello123' 是否全为字母: <span class="subst">{<span class="string">'hello123'</span>.isalpha()}</span>"</span>)  <span class="comment"># False</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># count() - 统计元素出现次数</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello' 中 'l' 出现的次数: <span class="subst">{<span class="string">'hello'</span>.count(<span class="string">'l'</span>)}</span>"</span>)  <span class="comment"># 2</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'Mississippi' 中 's' 出现的次数: <span class="subst">{<span class="string">'Mississippi'</span>.count(<span class="string">'s'</span>)}</span>"</span>)  <span class="comment"># 4</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># find() - 查找子串位置，未找到返回-1</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello world' 中 'world' 的位置: <span class="subst">{<span class="string">'hello world'</span>.find(<span class="string">'world'</span>)}</span>"</span>)  <span class="comment"># 6</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello world' 中 'python' 的位置: <span class="subst">{<span class="string">'hello world'</span>.find(<span class="string">'python'</span>)}</span>"</span>)  <span class="comment"># -1</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># upper() - 转为大写</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello' 转大写: <span class="subst">{<span class="string">'hello'</span>.upper()}</span>"</span>)  <span class="comment"># HELLO</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># lower() - 转为小写</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'HELLO' 转小写: <span class="subst">{<span class="string">'HELLO'</span>.lower()}</span>"</span>)  <span class="comment"># hello</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># replace() - 替换字符串</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello' 替换 'h' 为 'H': <span class="subst">{<span class="string">'hello'</span>.replace(<span class="string">'h'</span>, <span class="string">'H'</span>)}</span>"</span>)  <span class="comment"># Hello</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello hello' 替换所有 'l' 为 'L': <span class="subst">{<span class="string">'hello hello'</span>.replace(<span class="string">'l'</span>, <span class="string">'L'</span>)}</span>"</span>)  <span class="comment"># heLLo heLLo</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># split() - 分割字符串为列表</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'a,b,c' 按逗号分割: <span class="subst">{<span class="string">'a,b,c'</span>.split(<span class="string">','</span>)}</span>"</span>)  <span class="comment"># ['a', 'b', 'c']</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello world' 按空格分割: <span class="subst">{<span class="string">'hello world'</span>.split()}</span>"</span>)  <span class="comment"># ['hello', 'world']</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># join() - 拼接列表为字符串</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"用逗号连接 ['a', 'b', 'c']: <span class="subst">{<span class="string">','</span>.join([<span class="string">'a'</span>, <span class="string">'b'</span>, <span class="string">'c'</span>])}</span>"</span>)  <span class="comment"># a,b,c</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"用空格连接 ['hello', 'world']: <span class="subst">{<span class="string">' '</span>.join([<span class="string">'hello'</span>, <span class="string">'world'</span>])}</span>"</span>)  <span class="comment"># hello world</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># strip() - 去除两端空格或指定字符</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"' hello ' 去除两端空格: <span class="subst">{<span class="string">' hello '</span>.strip()}</span>"</span>)  <span class="comment"># hello</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'xxxhelloxxx' 去除两端的x: <span class="subst">{<span class="string">'xxxhelloxxx'</span>.strip(<span class="string">'x'</span>)}</span>"</span>)  <span class="comment"># hello</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># lstrip() - 去除左侧空格或指定字符</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"' hello' 去除左侧空格: <span class="subst">{<span class="string">' hello'</span>.lstrip()}</span>"</span>)  <span class="comment"># hello</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'xxxhello' 去除左侧的x: <span class="subst">{<span class="string">'xxxhello'</span>.lstrip(<span class="string">'x'</span>)}</span>"</span>)  <span class="comment"># hello</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># rstrip() - 去除右侧空格或指定字符</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello ' 去除右侧空格: <span class="subst">{<span class="string">'hello '</span>.rstrip()}</span>"</span>)  <span class="comment"># hello</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'helloxxx' 去除右侧的x: <span class="subst">{<span class="string">'helloxxx'</span>.rstrip(<span class="string">'x'</span>)}</span>"</span>)  <span class="comment"># hello</span></span><br><span class="line"><span class="comment"># ======== ======== ======== ======== ======== ======== ======== ======== ======== ========</span></span><br><span class="line"><span class="comment"># title() - 将字符串标题化（每个单词首字母大写）</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"'hello world' 标题化: <span class="subst">{<span class="string">'hello world'</span>.title()}</span>"</span>)  <span class="comment"># Hello World</span></span><br></pre></td></tr></tbody></table></figure><h3 id="5-2-列表类型-list"><a href="#5-2-列表类型-list" class="headerlink" title="5.2 列表类型(list)"></a>5.2 列表类型(list)</h3><h4 id="添加元素"><a href="#添加元素" class="headerlink" title="添加元素"></a>添加元素</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># append()：在列表末尾添加元素</span></span><br><span class="line">l = [<span class="string">'Python'</span>, <span class="string">'C++'</span>, <span class="string">'Java'</span>]</span><br><span class="line">l.append(<span class="string">'PHP'</span>)  <span class="comment"># ['Python', 'C++', 'Java', 'PHP']</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># extend()：批量添加元素，逐一添加</span></span><br><span class="line">l = [<span class="string">'Python'</span>, <span class="string">'C++'</span>, <span class="string">'Java'</span>]</span><br><span class="line">l.extend([<span class="string">'C#'</span>, <span class="string">'C'</span>, <span class="string">'JavaScript'</span>])  <span class="comment"># ['Python', 'C++', 'Java', 'C#', 'C', 'JavaScript']</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># insert()：在指定位置插入元素</span></span><br><span class="line">l = [<span class="string">'Python'</span>, <span class="string">'C++'</span>, <span class="string">'Java'</span>]</span><br><span class="line">l.insert(<span class="number">1</span>, <span class="string">'C'</span>)  <span class="comment"># ['Python', 'C', 'C++', 'Java']</span></span><br></pre></td></tr></tbody></table></figure><h4 id="删除元素"><a href="#删除元素" class="headerlink" title="删除元素"></a>删除元素</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># remove()：删除指定元素（第一个匹配项）</span></span><br><span class="line">nums = [<span class="number">40</span>, <span class="number">36</span>, <span class="number">89</span>, <span class="number">2</span>, <span class="number">36</span>, <span class="number">100</span>, <span class="number">7</span>]</span><br><span class="line">nums.remove(<span class="number">36</span>)  <span class="comment"># [40, 89, 2, 36, 100, 7]</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># pop()：删除指定索引元素，返回被删除的元素</span></span><br><span class="line">nums = [<span class="number">40</span>, <span class="number">36</span>, <span class="number">89</span>, <span class="number">2</span>, <span class="number">36</span>, <span class="number">100</span>, <span class="number">7</span>]</span><br><span class="line">nums.pop(<span class="number">3</span>)  <span class="comment"># [40, 36, 89, 36, 100, 7]</span></span><br><span class="line">nums.pop()   <span class="comment"># 不指定索引，默认删除最后一个</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># clear()：清空列表</span></span><br><span class="line">nums = [<span class="number">40</span>, <span class="number">36</span>, <span class="number">89</span>, <span class="number">2</span>, <span class="number">36</span>, <span class="number">100</span>, <span class="number">7</span>]</span><br><span class="line">nums.clear()  <span class="comment"># []</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># del：删除指定元素或切片</span></span><br><span class="line">nums = [<span class="number">40</span>, <span class="number">36</span>, <span class="number">89</span>, <span class="number">2</span>, <span class="number">36</span>, <span class="number">100</span>, <span class="number">7</span>]</span><br><span class="line"><span class="keyword">del</span> nums[<span class="number">2</span>]    <span class="comment"># 删除单个元素</span></span><br><span class="line"><span class="keyword">del</span> nums[:<span class="number">2</span>]   <span class="comment"># 删除切片范围的元素</span></span><br></pre></td></tr></tbody></table></figure><h4 id="查找元素"><a href="#查找元素" class="headerlink" title="查找元素"></a>查找元素</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># index()：查找元素索引</span></span><br><span class="line">nums = [<span class="number">40</span>, <span class="number">36</span>, <span class="number">89</span>, <span class="number">2</span>, <span class="number">36</span>, <span class="number">100</span>]</span><br><span class="line"><span class="built_in">print</span>(nums.index(<span class="number">36</span>))  <span class="comment"># 返回第一个匹配元素的索引：1</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># in 运算符：判断元素是否存在</span></span><br><span class="line"><span class="keyword">if</span> <span class="number">89</span> <span class="keyword">in</span> nums:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"元素存在"</span>)</span><br><span class="line">    </span><br><span class="line"><span class="comment"># enumerate()：同时获取索引和元素</span></span><br><span class="line"><span class="keyword">for</span> index, value <span class="keyword">in</span> <span class="built_in">enumerate</span>(nums):</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"索引 <span class="subst">{index}</span>: 值 <span class="subst">{value}</span>"</span>)</span><br><span class="line">    </span><br><span class="line"><span class="comment"># count()：统计元素出现次数</span></span><br><span class="line"><span class="built_in">print</span>(nums.count(<span class="number">36</span>))  <span class="comment"># 返回元素出现次数：2</span></span><br></pre></td></tr></tbody></table></figure><h4 id="排序"><a href="#排序" class="headerlink" title="排序"></a>排序</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># sort()：原地排序</span></span><br><span class="line">nums = [<span class="number">40</span>, <span class="number">36</span>, <span class="number">89</span>, <span class="number">2</span>, <span class="number">100</span>, <span class="number">7</span>]</span><br><span class="line">nums.sort()  <span class="comment"># [2, 7, 36, 40, 89, 100]</span></span><br><span class="line">nums.sort(reverse=<span class="literal">True</span>)  <span class="comment"># 降序：[100, 89, 40, 36, 7, 2]</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># sorted()：返回新列表，原列表不变</span></span><br><span class="line">nums = [<span class="number">40</span>, <span class="number">36</span>, <span class="number">89</span>, <span class="number">2</span>, <span class="number">100</span>, <span class="number">7</span>]</span><br><span class="line">sorted_nums = <span class="built_in">sorted</span>(nums)  <span class="comment"># [2, 7, 36, 40, 89, 100]</span></span><br><span class="line"><span class="built_in">print</span>(nums)  <span class="comment"># 原列表不变：[40, 36, 89, 2, 100, 7]</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 自定义排序（按字符串长度）,key可以编写一个匿名函数作为条件</span></span><br><span class="line">words = [<span class="string">'apple'</span>, <span class="string">'banana'</span>, <span class="string">'cherry'</span>, <span class="string">'date'</span>]</span><br><span class="line">sorted_words = <span class="built_in">sorted</span>(words, key=<span class="built_in">len</span>)  <span class="comment"># ['date', 'apple', 'cherry', 'banana']</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用sorted函数结合lambda函数进行复杂排序</span></span><br><span class="line"><span class="comment"># 创建一个包含学生信息的列表（姓名、年龄、成绩、是否参加课外活动）</span></span><br><span class="line">students = [</span><br><span class="line">    {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"age"</span>: <span class="number">18</span>, <span class="string">"score"</span>: <span class="number">85</span>, <span class="string">"extracurricular"</span>: <span class="literal">True</span>},</span><br><span class="line">    {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"age"</span>: <span class="number">17</span>, <span class="string">"score"</span>: <span class="number">92</span>, <span class="string">"extracurricular"</span>: <span class="literal">False</span>},</span><br><span class="line">    {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"age"</span>: <span class="number">19</span>, <span class="string">"score"</span>: <span class="number">78</span>, <span class="string">"extracurricular"</span>: <span class="literal">True</span>},</span><br><span class="line">    {<span class="string">"name"</span>: <span class="string">"赵六"</span>, <span class="string">"age"</span>: <span class="number">18</span>, <span class="string">"score"</span>: <span class="number">85</span>, <span class="string">"extracurricular"</span>: <span class="literal">False</span>},</span><br><span class="line">    {<span class="string">"name"</span>: <span class="string">"钱七"</span>, <span class="string">"age"</span>: <span class="number">20</span>, <span class="string">"score"</span>: <span class="number">90</span>, <span class="string">"extracurricular"</span>: <span class="literal">True</span>}</span><br><span class="line">]</span><br><span class="line"></span><br><span class="line"><span class="comment"># 基本排序：按照学生成绩从高到低排序</span></span><br><span class="line"><span class="comment"># lambda x: x["score"] - 定义一个匿名函数，接收一个参数x（列表中的每个元素），返回x的score值</span></span><br><span class="line"><span class="comment"># reverse=True - 表示降序排序（从高到低）</span></span><br><span class="line">sorted_by_score = <span class="built_in">sorted</span>(students, key=<span class="keyword">lambda</span> x: x[<span class="string">"score"</span>], reverse=<span class="literal">True</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">"按照学生成绩从高到低排序："</span>)</span><br><span class="line"><span class="keyword">for</span> student <span class="keyword">in</span> sorted_by_score:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"姓名：<span class="subst">{student[<span class="string">'name'</span>]}</span>，年龄：<span class="subst">{student[<span class="string">'age'</span>]}</span>，学生成绩：<span class="subst">{student[<span class="string">'score'</span>]}</span>"</span>)</span><br><span class="line"></span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h4 id="访问嵌套列表"><a href="#访问嵌套列表" class="headerlink" title="访问嵌套列表"></a>访问嵌套列表</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">nested_list = [<span class="string">'prorise'</span>, <span class="number">185</span>, <span class="literal">True</span>, [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]]</span><br><span class="line"><span class="built_in">print</span>(nested_list[<span class="number">3</span>][<span class="number">1</span>])  <span class="comment"># 访问嵌套列表的元素：2</span></span><br></pre></td></tr></tbody></table></figure><h3 id="5-3-元组类型-tuple"><a href="#5-3-元组类型-tuple" class="headerlink" title="5.3 元组类型(tuple)"></a>5.3 元组类型(tuple)</h3><h4 id="元组的创建方式"><a href="#元组的创建方式" class="headerlink" title="元组的创建方式"></a>元组的创建方式</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 1. 使用小括号创建</span></span><br><span class="line">t1 = (<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 2. 省略小括号创建</span></span><br><span class="line">t2 = <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 3. 单元素元组（必须加逗号，否则只是普通值，不是元组）</span></span><br><span class="line">t3 = (<span class="number">42</span>,)   <span class="comment"># 正确：这是一个元组</span></span><br><span class="line">t4 = (<span class="number">42</span>)    <span class="comment"># 错误：这是一个整数，不是元组</span></span><br><span class="line">t5 = <span class="number">42</span>,     <span class="comment"># 正确：这也是一个元组</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 4. 使用tuple()函数从其他可迭代对象创建</span></span><br><span class="line">t6 = <span class="built_in">tuple</span>([<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>])        <span class="comment"># 从列表创建</span></span><br><span class="line">t7 = <span class="built_in">tuple</span>(<span class="string">"hello"</span>)          <span class="comment"># 从字符串创建: ('h', 'e', 'l', 'l', 'o')</span></span><br><span class="line">t8 = <span class="built_in">tuple</span>({<span class="number">1</span>: <span class="string">'a'</span>, <span class="number">2</span>: <span class="string">'b'</span>}) <span class="comment"># 从字典创建: (1, 2) - 只保留键</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 5. 创建空元组</span></span><br><span class="line">empty_tuple = ()</span><br><span class="line">also_empty = <span class="built_in">tuple</span>()</span><br><span class="line"></span><br><span class="line"><span class="comment"># 6. 嵌套元组</span></span><br><span class="line">nested = (<span class="number">1</span>, (<span class="number">2</span>, <span class="number">3</span>), (<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>))</span><br><span class="line"></span><br><span class="line"><span class="comment"># 7. 不同类型的元素</span></span><br><span class="line">mixed = (<span class="number">1</span>, <span class="string">"hello"</span>, <span class="literal">True</span>, <span class="literal">None</span>, <span class="number">3.14</span>)</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h4 id="元组的特性与操作"><a href="#元组的特性与操作" class="headerlink" title="元组的特性与操作"></a>元组的特性与操作</h4><h5 id="不可变性"><a href="#不可变性" class="headerlink" title="不可变性"></a>不可变性</h5><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line">t = (<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>)</span><br><span class="line"><span class="comment"># t[0] = 5  # TypeError: 'tuple' object does not support item assignment</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 但可以通过连接创建新元组</span></span><br><span class="line">t = t + (<span class="number">4</span>, <span class="number">5</span>)  <span class="comment"># 新元组: (1, 2, 3, 4, 5)</span></span><br><span class="line">t = t * <span class="number">2</span>       <span class="comment"># 重复元组: (1, 2, 3, 4, 5, 1, 2, 3, 4, 5)</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 元组的不可变性让它可以作为字典的键</span></span><br><span class="line">d = {(<span class="number">1</span>, <span class="number">2</span>): <span class="string">"tuple as key"</span>}</span><br><span class="line"><span class="comment"># d[[1, 2]] = "list as key"  # TypeError: unhashable type: 'list'</span></span><br></pre></td></tr></tbody></table></figure><p>元组中可变对象的行为</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 元组中的可变对象（如列表）的内容可以修改</span></span><br><span class="line">t = (<span class="number">1</span>, [<span class="number">2</span>, <span class="number">3</span>], <span class="number">4</span>)</span><br><span class="line">t[<span class="number">1</span>].append(<span class="number">5</span>)  <span class="comment"># 正确：修改元组中列表的内容</span></span><br><span class="line"><span class="built_in">print</span>(t)        <span class="comment"># (1, [2, 3, 5], 4)</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># t[1] = [6, 7]  # TypeError: 'tuple' object does not support item assignment</span></span><br></pre></td></tr></tbody></table></figure><p><strong>重要说明</strong>：元组的不可变性只适用于元组本身的结构（元素的标识符不能改变），而不适用于元组中所包含的可变对象的内容。</p><h4 id="访问与切片"><a href="#访问与切片" class="headerlink" title="访问与切片"></a>访问与切片</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line">t = (<span class="number">0</span>, <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 索引访问（从0开始）</span></span><br><span class="line"><span class="built_in">print</span>(t[<span class="number">0</span>])    <span class="comment"># 0</span></span><br><span class="line"><span class="built_in">print</span>(t[-<span class="number">1</span>])   <span class="comment"># 5 (负索引从末尾开始)</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 切片</span></span><br><span class="line"><span class="built_in">print</span>(t[<span class="number">1</span>:<span class="number">4</span>])  <span class="comment"># (1, 2, 3)</span></span><br><span class="line"><span class="built_in">print</span>(t[:<span class="number">3</span>])   <span class="comment"># (0, 1, 2)</span></span><br><span class="line"><span class="built_in">print</span>(t[<span class="number">3</span>:])   <span class="comment"># (3, 4, 5)</span></span><br><span class="line"><span class="built_in">print</span>(t[::<span class="number">2</span>])  <span class="comment"># (0, 2, 4) - 步长为2</span></span><br><span class="line"><span class="built_in">print</span>(t[::-<span class="number">1</span>]) <span class="comment"># (5, 4, 3, 2, 1, 0) - 逆序</span></span><br></pre></td></tr></tbody></table></figure><h4 id="元组解包"><a href="#元组解包" class="headerlink" title="元组解包"></a>元组解包</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 基本解包</span></span><br><span class="line">t = (<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>)</span><br><span class="line">a, b, c = t</span><br><span class="line"><span class="built_in">print</span>(a, b, c)  <span class="comment"># 1 2 3</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用星号（*）收集多余的元素（Python 3.x）</span></span><br><span class="line">t = (<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>)</span><br><span class="line">a, b, *rest = t</span><br><span class="line"><span class="built_in">print</span>(a, b, rest)  <span class="comment"># 1 2 [3, 4, 5]</span></span><br><span class="line"></span><br><span class="line">first, *middle, last = t</span><br><span class="line"><span class="built_in">print</span>(first, middle, last)  <span class="comment"># 1 [2, 3, 4] 5</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 忽略某些值（使用下划线作为惯例）</span></span><br><span class="line">a, _, c = (<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>)</span><br><span class="line"><span class="built_in">print</span>(a, c)  <span class="comment"># 1 3</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 交换变量值</span></span><br><span class="line">a, b = <span class="number">10</span>, <span class="number">20</span></span><br><span class="line">a, b = b, a  <span class="comment"># 使用元组解包交换值</span></span><br><span class="line"><span class="built_in">print</span>(a, b)  <span class="comment"># 20 10</span></span><br></pre></td></tr></tbody></table></figure><h4 id="元组方法和内置函数"><a href="#元组方法和内置函数" class="headerlink" title="元组方法和内置函数"></a>元组方法和内置函数</h4><p>元组的方法比列表少，因为它是不可变的。主要方法有：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line">t = (<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">2</span>, <span class="number">4</span>, <span class="number">2</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 1. count() - 计算元素出现的次数</span></span><br><span class="line"><span class="built_in">print</span>(t.count(<span class="number">2</span>))  <span class="comment"># 3</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 2. index() - 返回元素首次出现的索引</span></span><br><span class="line"><span class="built_in">print</span>(t.index(<span class="number">3</span>))  <span class="comment"># 2</span></span><br><span class="line"><span class="comment"># print(t.index(5))  # ValueError: tuple.index(x): x not in tuple</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 3. 使用内置函数</span></span><br><span class="line"><span class="built_in">print</span>(<span class="built_in">len</span>(t))      <span class="comment"># 6 - 元组长度</span></span><br><span class="line"><span class="built_in">print</span>(<span class="built_in">min</span>(t))      <span class="comment"># 1 - 最小元素</span></span><br><span class="line"><span class="built_in">print</span>(<span class="built_in">max</span>(t))      <span class="comment"># 4 - 最大元素</span></span><br><span class="line"><span class="built_in">print</span>(<span class="built_in">sum</span>(t))      <span class="comment"># 14 - 元素和</span></span><br><span class="line"><span class="built_in">print</span>(<span class="built_in">sorted</span>(t))   <span class="comment"># [1, 2, 2, 2, 3, 4] - 返回排序后的列表（非元组）</span></span><br></pre></td></tr></tbody></table></figure><h4 id="元组的应用场景"><a href="#元组的应用场景" class="headerlink" title="元组的应用场景"></a>元组的应用场景</h4><h5 id="作为函数的返回值"><a href="#作为函数的返回值" class="headerlink" title="作为函数的返回值"></a>作为函数的返回值</h5><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">def</span> <span class="title function_">get_user_info</span>():</span><br><span class="line">    <span class="comment"># 返回多个值</span></span><br><span class="line">    <span class="keyword">return</span> <span class="string">"Alice"</span>, <span class="number">30</span>, <span class="string">"<EMAIL>"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 调用函数并解包返回值</span></span><br><span class="line"><span class="comment"># 判断返回值是否为一个元组</span></span><br><span class="line"><span class="keyword">if</span> <span class="built_in">isinstance</span>(get_user_info(), <span class="built_in">tuple</span>):</span><br><span class="line">    name, age, email = get_user_info()</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"姓名: <span class="subst">{name}</span>, 年龄: <span class="subst">{age}</span>, 邮箱: <span class="subst">{email}</span>"</span>) <span class="comment"># 输出姓名: Alice, 年龄: 30, 邮箱: <EMAIL></span></span><br><span class="line"><span class="keyword">else</span>:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"返回值不是一个元组"</span>)</span><br></pre></td></tr></tbody></table></figure><h5 id="作为不可变集合"><a href="#作为不可变集合" class="headerlink" title="作为不可变集合"></a>作为不可变集合</h5><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 使用元组作为字典键</span></span><br><span class="line">locations = {</span><br><span class="line">    (<span class="number">40.730610</span>, -<span class="number">73.935242</span>): <span class="string">"New York"</span>,</span><br><span class="line">    (<span class="number">34.052235</span>, -<span class="number">118.243683</span>): <span class="string">"Los Angeles"</span>,</span><br><span class="line">    (<span class="number">41.878113</span>, -<span class="number">87.629799</span>): <span class="string">"Chicago"</span></span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment"># 更新字典，不更改元组</span></span><br><span class="line">locations[(<span class="number">51.507351</span>, -<span class="number">0.127758</span>)] = <span class="string">"London"</span></span><br></pre></td></tr></tbody></table></figure><h5 id="确保数据不被修改"><a href="#确保数据不被修改" class="headerlink" title="确保数据不被修改"></a>确保数据不被修改</h5><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">def</span> <span class="title function_">process_config</span>(<span class="params">config</span>):</span><br><span class="line">    <span class="comment"># config是一个元组，确保处理过程中不被修改</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"处理配置:"</span>, config)</span><br><span class="line">    <span class="comment"># 安全的操作...</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用</span></span><br><span class="line">settings = (<span class="string">"debug"</span>, <span class="string">"verbose"</span>, <span class="string">"log_level=info"</span>)</span><br><span class="line">process_config(settings)</span><br></pre></td></tr></tbody></table></figure><h5><a href="#" class="headerlink"></a></h5><h3 id="5-4-字典类型-dict"><a href="#5-4-字典类型-dict" class="headerlink" title="5.4 字典类型(dict)"></a>5.4 字典类型(dict)</h3><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 创建字典</span></span><br><span class="line">person = {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"age"</span>: <span class="number">25</span>, <span class="string">"city"</span>: <span class="string">"北京"</span>}</span><br><span class="line"></span><br><span class="line"><span class="comment"># 访问值</span></span><br><span class="line"><span class="built_in">print</span>(person[<span class="string">"name"</span>])  <span class="comment"># 张三</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 设置/修改值</span></span><br><span class="line">person[<span class="string">"age"</span>] = <span class="number">26</span></span><br><span class="line">person[<span class="string">"email"</span>] = <span class="string">"<EMAIL>"</span>  <span class="comment"># 添加新键值对</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 删除键值对</span></span><br><span class="line"><span class="keyword">del</span> person[<span class="string">"city"</span>]</span><br></pre></td></tr></tbody></table></figure><h4 id="字典常用方法"><a href="#字典常用方法" class="headerlink" title="字典常用方法"></a>字典常用方法</h4><table><thead><tr><th>方法</th><th>描述</th><th>示例</th></tr></thead><tbody><tr><td>get()</td><td>获取值，键不存在时返回默认值</td><td><code>dict.get('key', 默认值)</code></td></tr><tr><td>keys()</td><td>获取所有键</td><td><code>dict.keys()</code></td></tr><tr><td>values()</td><td>获取所有值</td><td><code>dict.values()</code></td></tr><tr><td>items()</td><td>获取所有键值对</td><td><code>dict.items()</code></td></tr><tr><td>setdefault()</td><td>获取值，键不存在则设置默认值</td><td><code>dict.setdefault('key', 默认值)</code></td></tr><tr><td>update()</td><td>更新字典</td><td><code>dict1.update(dict2)</code></td></tr><tr><td>pop()</td><td>移除指定键值对并返回值</td><td><code>dict.pop('key')</code></td></tr></tbody></table><h3 id="5-5-集合类型-set"><a href="#5-5-集合类型-set" class="headerlink" title="5.5 集合类型(set)"></a>5.5 集合类型(set)</h3><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 创建集合</span></span><br><span class="line">s1 = {<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>}</span><br><span class="line">s2 = <span class="built_in">set</span>([<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">3</span>, <span class="number">2</span>, <span class="number">1</span>])  <span class="comment"># 结果：{1, 2, 3}</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加元素</span></span><br><span class="line">s1.add(<span class="number">6</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 删除元素</span></span><br><span class="line">s1.discard(<span class="number">3</span>)  <span class="comment"># 元素不存在不会报错</span></span><br><span class="line">s1.remove(<span class="number">2</span>)   <span class="comment"># 元素不存在会报错</span></span><br></pre></td></tr></tbody></table></figure><h4 id="集合操作"><a href="#集合操作" class="headerlink" title="集合操作"></a>集合操作</h4><table><thead><tr><th>操作</th><th>描述</th><th>示例</th></tr></thead><tbody><tr><td>&amp;</td><td>交集（共同元素）</td><td><code>s1 &amp; s2</code></td></tr><tr><td>|</td><td>并集（去重合并）</td><td><code>s1 | s2</code></td></tr><tr><td>-</td><td>差集（属于 s1 不属于 s2）</td><td><code>s1 - s2</code></td></tr><tr><td>^</td><td>对称差集（不同时属于 s1 和 s2）</td><td><code>s1 ^ s2</code></td></tr></tbody></table><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 求两个集合的交集(共同元素)</span></span><br><span class="line">s1 = {<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>}</span><br><span class="line">s2 = {<span class="number">1</span>, <span class="number">3</span>, <span class="number">5</span>, <span class="number">7</span>, <span class="number">9</span>}</span><br><span class="line"><span class="built_in">print</span>(s1 &amp; s2)  <span class="comment"># {1, 3, 5}</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 求两个集合的并集（去重后在合并）</span></span><br><span class="line"><span class="built_in">print</span>(s1 | s2)  <span class="comment"># {1, 2, 3, 4, 5, 7, 9}</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 求两个集合的差集（s1中有而s2中没有的元素）</span></span><br><span class="line"><span class="built_in">print</span>(s1 - s2)  <span class="comment"># {2, 4}</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 求两个集合的对称差集（s1和s2中不同时存在的元素）</span></span><br><span class="line"><span class="built_in">print</span>(s1 ^ s2)  <span class="comment"># {2, 4, 7, 9}</span></span><br></pre></td></tr></tbody></table></figure></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/50189.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/50189.html&quot;)">Python（六）：第五章：数据类型</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/50189.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=Python（六）：第五章：数据类型&amp;url=https://prorise666.site/posts/50189.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Python<span class="categoryesPageCount">22</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Python基础知识总汇<span class="tagsPageCount">22</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/56691.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Python（五）：第四章：类型转换详解</div></div></a></div><div class="next-post pull-right"><a href="/posts/45310.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Python（七）：第六章：条件循环分支</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/17730.html" title="Python（一）：Python 语言特性"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（一）：Python 语言特性</div></div></a></div><div><a href="/posts/8019.html" title="Python（三）：第二章：转义字符"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（三）：第二章：转义字符</div></div></a></div><div><a href="/posts/45310.html" title="Python（七）：第六章：条件循环分支"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（七）：第六章：条件循环分支</div></div></a></div><div><a href="/posts/56572.html" title="Python（九）：第八章： 函数知识总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（九）：第八章： 函数知识总结</div></div></a></div><div><a href="/posts/55902.html" title="Python（二十一）：第二十章：Python 语法新特性总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十一）：第二十章：Python 语法新特性总结</div></div></a></div><div><a href="/posts/2501.html" title="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Python（六）：第五章：数据类型",date:"2025-04-18 21:13:45",updated:"2025-07-13 22:13:01",tags:["Python基础知识总汇"],categories:["后端技术","Python"],content:"\n## 第五章：数据类型\n\n### 5.1 字符串类型(str)\n\n#### 字符串输出方式\n\n| `占位符类型` | 说明                                            |\n| ------------ | ----------------------------------------------- |\n| `%s`         | 字符串（使用 `str()` 方法转换任何 Python 对象） |\n| `%d`         | 十进制整数                                      |\n| `%f`         | 十进制浮点数(小数), 自动保留六位小数。          |\n\n```python\n# 占位符方式\nprint('我的名字是 %s, 我的年龄是 %d岁' % ('小明', 28))\n\n#    \nprint('我的名字是 {}, 我的年龄是 {}岁'.format('小明', 28))\n# format带索引\nprint('我的名字是 {1}, 我的年龄是 {0}岁'.format(28, '小明'))\n\n# f-string方式(推荐)\nname = '小明'\nage = 28\nprint(f'我的名字是 {name}, 我的年龄是 {age}岁')\n```\n\n#### 字符串常用方法\n\n| 方法         | 描述                       | 示例                          |\n| ------------ | -------------------------- | ----------------------------- |\n| startswith() | 判断是否以指定内容开头     | `str.startswith('hello')`     |\n| endswith()   | 判断是否以指定内容结尾     | `str.endswith('world')`       |\n| isdigit()    | 判断是否为数字组成         | `'12345'.isdigit()`           |\n| isalpha()    | 判断是否为文字组成         | `'hello'.isalpha()`           |\n| count()      | 统计元素出现次数           | `'hello'.count('l')`          |\n| find()       | 查找子串位置，未找到返回-1 | `'hello world'.find('world')` |\n| upper()      | 转为大写                   | `'hello'.upper()`             |\n| lower()      | 转为小写                   | `'HELLO'.lower()`             |\n| replace()    | 替换字符串                 | `'hello'.replace('h', 'H')`   |\n| split()      | 分割字符串为列表           | `'a,b,c'.split(',')`          |\n| join()       | 拼接列表为字符串           | `','.join(['a', 'b', 'c'])`   |\n| strip()      | 去除两端空格或指定字符     | `' hello '.strip()`           |\n| lstrip()     | 去除左侧空格或指定字符     | `' hello'.lstrip()`           |\n| rstrip()     | 去除右侧空格或指定字符     | `'hello '.rstrip()`           |\n| title()      | 将字符串标题化             | `'hello world'.title()`       |\n\n```python\n# ======== 字符串方法示例 ========\n\n# startswith() - 判断是否以指定内容开头\ntext = \"Hello, World!\"\nprint(f\"'Hello, World!' 是否以'Hello'开头: {text.startswith('Hello')}\")  # True\nprint(f\"'Hello, World!' 是否以'World'开头: {text.startswith('World')}\")  # False\n\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n\n# endswith() - 判断是否以指定内容结尾\nprint(f\"'Hello, World!' 是否以'!'结尾: {text.endswith('!')}\")  # True\nprint(f\"'Hello, World!' 是否以'Hello'结尾: {text.endswith('Hello')}\")  # False\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# isdigit() - 判断是否为数字组成\nnum_str = \"12345\"\nprint(f\"'12345' 是否全为数字: {num_str.isdigit()}\")  # True\nprint(f\"'Hello' 是否全为数字: {'Hello'.isdigit()}\")  # False\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# isalpha() - 判断是否为文字组成\nalpha_str = \"hello\"\nprint(f\"'hello' 是否全为字母: {alpha_str.isalpha()}\")  # True\nprint(f\"'hello123' 是否全为字母: {'hello123'.isalpha()}\")  # False\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# count() - 统计元素出现次数\nprint(f\"'hello' 中 'l' 出现的次数: {'hello'.count('l')}\")  # 2\nprint(f\"'Mississippi' 中 's' 出现的次数: {'Mississippi'.count('s')}\")  # 4\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# find() - 查找子串位置，未找到返回-1\nprint(f\"'hello world' 中 'world' 的位置: {'hello world'.find('world')}\")  # 6\nprint(f\"'hello world' 中 'python' 的位置: {'hello world'.find('python')}\")  # -1\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# upper() - 转为大写\nprint(f\"'hello' 转大写: {'hello'.upper()}\")  # HELLO\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# lower() - 转为小写\nprint(f\"'HELLO' 转小写: {'HELLO'.lower()}\")  # hello\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# replace() - 替换字符串\nprint(f\"'hello' 替换 'h' 为 'H': {'hello'.replace('h', 'H')}\")  # Hello\nprint(f\"'hello hello' 替换所有 'l' 为 'L': {'hello hello'.replace('l', 'L')}\")  # heLLo heLLo\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# split() - 分割字符串为列表\nprint(f\"'a,b,c' 按逗号分割: {'a,b,c'.split(',')}\")  # ['a', 'b', 'c']\nprint(f\"'hello world' 按空格分割: {'hello world'.split()}\")  # ['hello', 'world']\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# join() - 拼接列表为字符串\nprint(f\"用逗号连接 ['a', 'b', 'c']: {','.join(['a', 'b', 'c'])}\")  # a,b,c\nprint(f\"用空格连接 ['hello', 'world']: {' '.join(['hello', 'world'])}\")  # hello world\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# strip() - 去除两端空格或指定字符\nprint(f\"' hello ' 去除两端空格: {' hello '.strip()}\")  # hello\nprint(f\"'xxxhelloxxx' 去除两端的x: {'xxxhelloxxx'.strip('x')}\")  # hello\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# lstrip() - 去除左侧空格或指定字符\nprint(f\"' hello' 去除左侧空格: {' hello'.lstrip()}\")  # hello\nprint(f\"'xxxhello' 去除左侧的x: {'xxxhello'.lstrip('x')}\")  # hello\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# rstrip() - 去除右侧空格或指定字符\nprint(f\"'hello ' 去除右侧空格: {'hello '.rstrip()}\")  # hello\nprint(f\"'helloxxx' 去除右侧的x: {'helloxxx'.rstrip('x')}\")  # hello\n# ======== ======== ======== ======== ======== ======== ======== ======== ======== ========\n# title() - 将字符串标题化（每个单词首字母大写）\nprint(f\"'hello world' 标题化: {'hello world'.title()}\")  # Hello World\n```\n\n### 5.2 列表类型(list)\n\n#### 添加元素\n```python\n# append()：在列表末尾添加元素\nl = ['Python', 'C++', 'Java']\nl.append('PHP')  # ['Python', 'C++', 'Java', 'PHP']\n\n# extend()：批量添加元素，逐一添加\nl = ['Python', 'C++', 'Java']\nl.extend(['C#', 'C', 'JavaScript'])  # ['Python', 'C++', 'Java', 'C#', 'C', 'JavaScript']\n\n# insert()：在指定位置插入元素\nl = ['Python', 'C++', 'Java']\nl.insert(1, 'C')  # ['Python', 'C', 'C++', 'Java']\n```\n\n#### 删除元素\n```python\n# remove()：删除指定元素（第一个匹配项）\nnums = [40, 36, 89, 2, 36, 100, 7]\nnums.remove(36)  # [40, 89, 2, 36, 100, 7]\n\n# pop()：删除指定索引元素，返回被删除的元素\nnums = [40, 36, 89, 2, 36, 100, 7]\nnums.pop(3)  # [40, 36, 89, 36, 100, 7]\nnums.pop()   # 不指定索引，默认删除最后一个\n\n# clear()：清空列表\nnums = [40, 36, 89, 2, 36, 100, 7]\nnums.clear()  # []\n\n# del：删除指定元素或切片\nnums = [40, 36, 89, 2, 36, 100, 7]\ndel nums[2]    # 删除单个元素\ndel nums[:2]   # 删除切片范围的元素\n```\n\n#### 查找元素\n```python\n# index()：查找元素索引\nnums = [40, 36, 89, 2, 36, 100]\nprint(nums.index(36))  # 返回第一个匹配元素的索引：1\n\n# in 运算符：判断元素是否存在\nif 89 in nums:\n    print(\"元素存在\")\n    \n# enumerate()：同时获取索引和元素\nfor index, value in enumerate(nums):\n    print(f\"索引 {index}: 值 {value}\")\n    \n# count()：统计元素出现次数\nprint(nums.count(36))  # 返回元素出现次数：2\n```\n\n#### 排序\n```python\n# sort()：原地排序\nnums = [40, 36, 89, 2, 100, 7]\nnums.sort()  # [2, 7, 36, 40, 89, 100]\nnums.sort(reverse=True)  # 降序：[100, 89, 40, 36, 7, 2]\n\n# sorted()：返回新列表，原列表不变\nnums = [40, 36, 89, 2, 100, 7]\nsorted_nums = sorted(nums)  # [2, 7, 36, 40, 89, 100]\nprint(nums)  # 原列表不变：[40, 36, 89, 2, 100, 7]\n\n# 自定义排序（按字符串长度）,key可以编写一个匿名函数作为条件\nwords = ['apple', 'banana', 'cherry', 'date']\nsorted_words = sorted(words, key=len)  # ['date', 'apple', 'cherry', 'banana']\n\n# 使用sorted函数结合lambda函数进行复杂排序\n# 创建一个包含学生信息的列表（姓名、年龄、成绩、是否参加课外活动）\nstudents = [\n    {\"name\": \"张三\", \"age\": 18, \"score\": 85, \"extracurricular\": True},\n    {\"name\": \"李四\", \"age\": 17, \"score\": 92, \"extracurricular\": False},\n    {\"name\": \"王五\", \"age\": 19, \"score\": 78, \"extracurricular\": True},\n    {\"name\": \"赵六\", \"age\": 18, \"score\": 85, \"extracurricular\": False},\n    {\"name\": \"钱七\", \"age\": 20, \"score\": 90, \"extracurricular\": True}\n]\n\n# 基本排序：按照学生成绩从高到低排序\n# lambda x: x[\"score\"] - 定义一个匿名函数，接收一个参数x（列表中的每个元素），返回x的score值\n# reverse=True - 表示降序排序（从高到低）\nsorted_by_score = sorted(students, key=lambda x: x[\"score\"], reverse=True)\nprint(\"按照学生成绩从高到低排序：\")\nfor student in sorted_by_score:\n    print(f\"姓名：{student['name']}，年龄：{student['age']}，学生成绩：{student['score']}\")\n\n\n```\n\n#### 访问嵌套列表\n```python\nnested_list = ['prorise', 185, True, [1, 2, 3]]\nprint(nested_list[3][1])  # 访问嵌套列表的元素：2\n```\n\n### 5.3 元组类型(tuple)\n\n#### 元组的创建方式\n\n```python\n# 1. 使用小括号创建\nt1 = (1, 2, 3, 4, 5)\n\n# 2. 省略小括号创建\nt2 = 1, 2, 3, 4, 5\n\n# 3. 单元素元组（必须加逗号，否则只是普通值，不是元组）\nt3 = (42,)   # 正确：这是一个元组\nt4 = (42)    # 错误：这是一个整数，不是元组\nt5 = 42,     # 正确：这也是一个元组\n\n# 4. 使用tuple()函数从其他可迭代对象创建\nt6 = tuple([1, 2, 3])        # 从列表创建\nt7 = tuple(\"hello\")          # 从字符串创建: ('h', 'e', 'l', 'l', 'o')\nt8 = tuple({1: 'a', 2: 'b'}) # 从字典创建: (1, 2) - 只保留键\n\n# 5. 创建空元组\nempty_tuple = ()\nalso_empty = tuple()\n\n# 6. 嵌套元组\nnested = (1, (2, 3), (4, 5, 6))\n\n# 7. 不同类型的元素\nmixed = (1, \"hello\", True, None, 3.14)\n\n```\n\n#### 元组的特性与操作\n\n##### 不可变性\n\n```python\nt = (1, 2, 3)\n# t[0] = 5  # TypeError: 'tuple' object does not support item assignment\n\n# 但可以通过连接创建新元组\nt = t + (4, 5)  # 新元组: (1, 2, 3, 4, 5)\nt = t * 2       # 重复元组: (1, 2, 3, 4, 5, 1, 2, 3, 4, 5)\n\n# 元组的不可变性让它可以作为字典的键\nd = {(1, 2): \"tuple as key\"}\n# d[[1, 2]] = \"list as key\"  # TypeError: unhashable type: 'list'\n```\n\n元组中可变对象的行为\n\n```python\n# 元组中的可变对象（如列表）的内容可以修改\nt = (1, [2, 3], 4)\nt[1].append(5)  # 正确：修改元组中列表的内容\nprint(t)        # (1, [2, 3, 5], 4)\n\n# t[1] = [6, 7]  # TypeError: 'tuple' object does not support item assignment\n```\n\n**重要说明**：元组的不可变性只适用于元组本身的结构（元素的标识符不能改变），而不适用于元组中所包含的可变对象的内容。\n\n#### 访问与切片\n\n```python\nt = (0, 1, 2, 3, 4, 5)\n\n# 索引访问（从0开始）\nprint(t[0])    # 0\nprint(t[-1])   # 5 (负索引从末尾开始)\n\n# 切片\nprint(t[1:4])  # (1, 2, 3)\nprint(t[:3])   # (0, 1, 2)\nprint(t[3:])   # (3, 4, 5)\nprint(t[::2])  # (0, 2, 4) - 步长为2\nprint(t[::-1]) # (5, 4, 3, 2, 1, 0) - 逆序\n```\n\n#### 元组解包\n\n```python\n# 基本解包\nt = (1, 2, 3)\na, b, c = t\nprint(a, b, c)  # 1 2 3\n\n# 使用星号（*）收集多余的元素（Python 3.x）\nt = (1, 2, 3, 4, 5)\na, b, *rest = t\nprint(a, b, rest)  # 1 2 [3, 4, 5]\n\nfirst, *middle, last = t\nprint(first, middle, last)  # 1 [2, 3, 4] 5\n\n# 忽略某些值（使用下划线作为惯例）\na, _, c = (1, 2, 3)\nprint(a, c)  # 1 3\n\n# 交换变量值\na, b = 10, 20\na, b = b, a  # 使用元组解包交换值\nprint(a, b)  # 20 10\n```\n\n#### 元组方法和内置函数\n\n元组的方法比列表少，因为它是不可变的。主要方法有：\n\n```python\nt = (1, 2, 3, 2, 4, 2)\n\n# 1. count() - 计算元素出现的次数\nprint(t.count(2))  # 3\n\n# 2. index() - 返回元素首次出现的索引\nprint(t.index(3))  # 2\n# print(t.index(5))  # ValueError: tuple.index(x): x not in tuple\n\n# 3. 使用内置函数\nprint(len(t))      # 6 - 元组长度\nprint(min(t))      # 1 - 最小元素\nprint(max(t))      # 4 - 最大元素\nprint(sum(t))      # 14 - 元素和\nprint(sorted(t))   # [1, 2, 2, 2, 3, 4] - 返回排序后的列表（非元组）\n```\n\n\n\n#### 元组的应用场景\n\n##### 作为函数的返回值\n\n```python\ndef get_user_info():\n    # 返回多个值\n    return \"Alice\", 30, \"<EMAIL>\"\n\n# 调用函数并解包返回值\n# 判断返回值是否为一个元组\nif isinstance(get_user_info(), tuple):\n    name, age, email = get_user_info()\n    print(f\"姓名: {name}, 年龄: {age}, 邮箱: {email}\") # 输出姓名: Alice, 年龄: 30, 邮箱: <EMAIL>\nelse:\n    print(\"返回值不是一个元组\")\n```\n\n##### 作为不可变集合\n\n```python\n# 使用元组作为字典键\nlocations = {\n    (40.730610, -73.935242): \"New York\",\n    (34.052235, -118.243683): \"Los Angeles\",\n    (41.878113, -87.629799): \"Chicago\"\n}\n\n# 更新字典，不更改元组\nlocations[(51.507351, -0.127758)] = \"London\"\n```\n\n##### 确保数据不被修改\n\n```python\ndef process_config(config):\n    # config是一个元组，确保处理过程中不被修改\n    print(\"处理配置:\", config)\n    # 安全的操作...\n\n# 使用\nsettings = (\"debug\", \"verbose\", \"log_level=info\")\nprocess_config(settings)\n```\n\n##### \n\n\n\n### 5.4 字典类型(dict)\n\n```python\n# 创建字典\nperson = {\"name\": \"张三\", \"age\": 25, \"city\": \"北京\"}\n\n# 访问值\nprint(person[\"name\"])  # 张三\n\n# 设置/修改值\nperson[\"age\"] = 26\nperson[\"email\"] = \"<EMAIL>\"  # 添加新键值对\n\n# 删除键值对\ndel person[\"city\"]\n```\n\n#### 字典常用方法\n\n| 方法         | 描述                         | 示例                             |\n| ------------ | ---------------------------- | -------------------------------- |\n| get()        | 获取值，键不存在时返回默认值 | `dict.get('key', 默认值)`        |\n| keys()       | 获取所有键                   | `dict.keys()`                    |\n| values()     | 获取所有值                   | `dict.values()`                  |\n| items()      | 获取所有键值对               | `dict.items()`                   |\n| setdefault() | 获取值，键不存在则设置默认值 | `dict.setdefault('key', 默认值)` |\n| update()     | 更新字典                     | `dict1.update(dict2)`            |\n| pop()        | 移除指定键值对并返回值       | `dict.pop('key')`                |\n\n### 5.5 集合类型(set)\n\n```python\n# 创建集合\ns1 = {1, 2, 3, 4, 5}\ns2 = set([1, 2, 3, 3, 2, 1])  # 结果：{1, 2, 3}\n\n# 添加元素\ns1.add(6)\n\n# 删除元素\ns1.discard(3)  # 元素不存在不会报错\ns1.remove(2)   # 元素不存在会报错\n```\n\n#### 集合操作\n\n| 操作 | 描述                            | 示例       |\n| ---- | ------------------------------- | ---------- |\n| &    | 交集（共同元素）                | `s1 & s2`  |\n| \\|   | 并集（去重合并）                | `s1 \\| s2` |\n| -    | 差集（属于 s1 不属于 s2）       | `s1 - s2`  |\n| ^    | 对称差集（不同时属于 s1 和 s2） | `s1 ^ s2`  |\n\n```python\n# 求两个集合的交集(共同元素)\ns1 = {1, 2, 3, 4, 5}\ns2 = {1, 3, 5, 7, 9}\nprint(s1 & s2)  # {1, 3, 5}\n\n# 求两个集合的并集（去重后在合并）\nprint(s1 | s2)  # {1, 2, 3, 4, 5, 7, 9}\n\n# 求两个集合的差集（s1中有而s2中没有的元素）\nprint(s1 - s2)  # {2, 4}\n\n# 求两个集合的对称差集（s1和s2中不同时存在的元素）\nprint(s1 ^ s2)  # {2, 4, 7, 9}\n```"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%94%E7%AB%A0%EF%BC%9A%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.</span> <span class="toc-text">第五章：数据类型</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#5-1-%E5%AD%97%E7%AC%A6%E4%B8%B2%E7%B1%BB%E5%9E%8B-str"><span class="toc-number">1.1.</span> <span class="toc-text">5.1 字符串类型(str)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AD%97%E7%AC%A6%E4%B8%B2%E8%BE%93%E5%87%BA%E6%96%B9%E5%BC%8F"><span class="toc-number">1.1.1.</span> <span class="toc-text">字符串输出方式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AD%97%E7%AC%A6%E4%B8%B2%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95"><span class="toc-number">1.1.2.</span> <span class="toc-text">字符串常用方法</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-2-%E5%88%97%E8%A1%A8%E7%B1%BB%E5%9E%8B-list"><span class="toc-number">1.2.</span> <span class="toc-text">5.2 列表类型(list)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%B7%BB%E5%8A%A0%E5%85%83%E7%B4%A0"><span class="toc-number">1.2.1.</span> <span class="toc-text">添加元素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%88%A0%E9%99%A4%E5%85%83%E7%B4%A0"><span class="toc-number">1.2.2.</span> <span class="toc-text">删除元素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%9F%A5%E6%89%BE%E5%85%83%E7%B4%A0"><span class="toc-number">1.2.3.</span> <span class="toc-text">查找元素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%8E%92%E5%BA%8F"><span class="toc-number">1.2.4.</span> <span class="toc-text">排序</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%AE%BF%E9%97%AE%E5%B5%8C%E5%A5%97%E5%88%97%E8%A1%A8"><span class="toc-number">1.2.5.</span> <span class="toc-text">访问嵌套列表</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-3-%E5%85%83%E7%BB%84%E7%B1%BB%E5%9E%8B-tuple"><span class="toc-number">1.3.</span> <span class="toc-text">5.3 元组类型(tuple)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E7%9A%84%E5%88%9B%E5%BB%BA%E6%96%B9%E5%BC%8F"><span class="toc-number">1.3.1.</span> <span class="toc-text">元组的创建方式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E7%9A%84%E7%89%B9%E6%80%A7%E4%B8%8E%E6%93%8D%E4%BD%9C"><span class="toc-number">1.3.2.</span> <span class="toc-text">元组的特性与操作</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%B8%8D%E5%8F%AF%E5%8F%98%E6%80%A7"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">不可变性</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%AE%BF%E9%97%AE%E4%B8%8E%E5%88%87%E7%89%87"><span class="toc-number">1.3.3.</span> <span class="toc-text">访问与切片</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E8%A7%A3%E5%8C%85"><span class="toc-number">1.3.4.</span> <span class="toc-text">元组解包</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E6%96%B9%E6%B3%95%E5%92%8C%E5%86%85%E7%BD%AE%E5%87%BD%E6%95%B0"><span class="toc-number">1.3.5.</span> <span class="toc-text">元组方法和内置函数</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%85%83%E7%BB%84%E7%9A%84%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.3.6.</span> <span class="toc-text">元组的应用场景</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BD%9C%E4%B8%BA%E5%87%BD%E6%95%B0%E7%9A%84%E8%BF%94%E5%9B%9E%E5%80%BC"><span class="toc-number">1.3.6.1.</span> <span class="toc-text">作为函数的返回值</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E4%BD%9C%E4%B8%BA%E4%B8%8D%E5%8F%AF%E5%8F%98%E9%9B%86%E5%90%88"><span class="toc-number">1.3.6.2.</span> <span class="toc-text">作为不可变集合</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%A1%AE%E4%BF%9D%E6%95%B0%E6%8D%AE%E4%B8%8D%E8%A2%AB%E4%BF%AE%E6%94%B9"><span class="toc-number">1.3.6.3.</span> <span class="toc-text">确保数据不被修改</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#postchat_postcontent"><span class="toc-number">1.3.6.4.</span> <span class="toc-text"></span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-4-%E5%AD%97%E5%85%B8%E7%B1%BB%E5%9E%8B-dict"><span class="toc-number">1.4.</span> <span class="toc-text">5.4 字典类型(dict)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AD%97%E5%85%B8%E5%B8%B8%E7%94%A8%E6%96%B9%E6%B3%95"><span class="toc-number">1.4.1.</span> <span class="toc-text">字典常用方法</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#5-5-%E9%9B%86%E5%90%88%E7%B1%BB%E5%9E%8B-set"><span class="toc-number">1.5.</span> <span class="toc-text">5.5 集合类型(set)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%9B%86%E5%90%88%E6%93%8D%E4%BD%9C"><span class="toc-number">1.5.1.</span> <span class="toc-text">集合操作</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>