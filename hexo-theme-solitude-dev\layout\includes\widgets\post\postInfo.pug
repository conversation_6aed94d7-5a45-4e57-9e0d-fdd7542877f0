#post-info
    #post-firstinfo
        .meta-firstline
            - var cc = page.reprint ? _p('post.reprint') : _p('post.original')
            a.post-meta-original(title=_p('post.cc').replace(/\$\{cc}/, cc))= cc
            if page.categories.data.length > 0
                span.post-meta-categories
                    a.post-meta-categories(href=url_for('/' + page.categories.data[0].path))= page.categories.data[0].name
            .tag_share
                .post-meta__tag-list
                    each tag in page.tags.data
                        a.post-meta__tags(href=url_for(tag.path))
                            span.tags-name.tags-punctuation
                                i.solitude.fas.fa-hashtag
                                = tag.name
    h1.post-title= page.title
    #post-meta
        .meta-secondline
            if theme.post.meta.date
                span.post-meta-date(title=_p('post.posted') + ' ' + full_date(page.date))
                    i.post-meta-icon.solitude.fas.fa-calendar-days
                    time(datetime=date_xml(page.date))= date_xml(page.date)
            if theme.post.meta.updated
                span.post-meta-date(title=_p('post.updated') + ' ' + full_date(page.updated))
                    i.post-meta-icon.solitude.fas.fa-arrows-rotate
                    time(datetime=date_xml(page.updated))= date_xml(page.updated)
            if theme.wordcount
                span.post-meta-wordcount
                    if theme.post.meta.wordcount
                        i.post-meta-icon.solitude.fas.fa-file-word(title=_p('post.wordcount'))
                        span.word-count= wordcount(page.content)
                    span.post-meta-separator
                    if theme.post.meta.readtime
                        i.post-meta-icon.solitude.fas.fa-clock(title=_p('post.minread'))
                        span= min2read(page.content) + ' ' + _p('post.min')
            if theme.post.meta.locate
                span.post-meta-position(title=_p('post.ip') + page.locate)
                    i.post-meta-icon.solitude.fas.fa-location-dot
                    span= page.locate
            if theme.post.meta.pv && (theme.comment.use && theme.comment.pv || theme.busuanzi)
                a.post-meta-pv(href=url_for(page.path), title=_p('post.pv'))
                    i.post-meta-icon.solitude.fas.fa-fire-flame-curved
                    if theme.comment.use && theme.comment.pv
                        case theme.comment.use[0]
                            when "Twikoo"
                                span#twikoo_visitors
                                    i.solitude.fas.fa-spinner.fa-spin
                            when "Waline"
                                span.waline-pageview-count
                                    i.solitude.fas.fa-spinner.fa-spin
                            when "Valine"
                                span.leancloud_visitors(id=url_for(page.path))
                                    span.leancloud-visitors-count
                                        i.solitude.fas.fa-spinner.fa-spin
                            when 'Artalk'
                                span.artalk-pv-count
                                    i.solitude.fas.fa-spinner.fa-spin
                    else
                        if theme.busuanzi
                            if theme.busuanzi_use === 0
                                span#busuanzi_value_page_pv
                                    i.solitude.fas.fa-spinner.fa-spin
                            else
                                span#busuanzi_page_pv
                                    i.solitude.fas.fa-spinner.fa-spin
            if theme.post.meta.comment && page.comment && theme.comment.count && theme.comment.use
                span.post-meta-commentcount(title=_p('post.comment'), onclick="sco.scrollTo('post-comment')")
                    i.solitude.fas.fa-comment
                    a(href=url_for(page.path) + "#post-comment")
                        case theme.comment.use[0]
                            when "Twikoo"
                                span.twikoo-count
                                    i.solitude.fas.fa-spinner.fa-spin
                            when "Waline"
                                span.waline-comment-count
                                    i.solitude.fas.fa-spinner.fa-spin
                            when "Valine"
                                span.valine-comment-count(data-xid=url_for(page.path) itemprop="commentCount")
                                    i.solitude.fas.fa-spinner.fa-spin
                            when 'Artalk'
                                span.artalk-comment-count
                                    i.solitude.fas.fa-spinner.fa-spin