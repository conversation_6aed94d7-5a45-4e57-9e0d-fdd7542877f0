---
title: 3️⃣ 电商运营实战
categories:
  - 产品经理实战
tags:
  - 产品经理实战
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp'
comments: true
toc: true
ai: true
abbrlink: 45404
date: 2025-07-25 11:22:34
---
# 第一章：电商运营基础

欢迎来到我们课程的第一章。在之前的学习中，我们可能更关注如何“从0到1”地去规划和设计一个产品。但一个产品上线，仅仅是万里长征的第一步。如何让这个产品活下去、活得好，如何让用户愿意来、愿意留、愿意付费——这些，就是我们本章要探讨的核心命题：**运营**。

## 1.1 运营基础知识

在这一节，我将带你建立起对“运营”这个岗位最基本、也是最重要的认知。我们会一起探讨：运营究竟是什么？它在不同阶段又分别扮演着怎样的角色？

### 1.1.1 什么是运营

![image-20250725114001247](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114001247.png)

很多刚入行的同学会问我：“运营到底是干什么的？” 我通常会用上面这张图来回答。我常常把运营比作一座“**桥梁**”，它的两端，连接着“**产品**”和“**用户**”。

* **产品是基础**：我们有一款精心打磨的产品。
* **用户是目标**：我们希望有大量的、活跃的用户来使用它。

而运营的工作，就是通过一系列的手段和策略，在这两者之间建立起高效、顺畅的连接。正如图片下方文字所说，我们运营的核心目的，就是“**使得产品能够活得更久，并且延长用户在产品当中的生命周期**”。没有运营，再好的产品也可能无人问津；没有运营，用户来了也可能很快流失。

![image-20250725114038113](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114038113.png)

要理解运营，我们就必须先理解我们所服务的“**产品**”，它本身是有生命周期的。我将一个产品的生命，通常划分为四个典型阶段：

1.  **导入期**：就像一个刚出生的婴儿，产品还在探索和打磨，用户对它还不了解，所以这个阶段的用户增长通常比较缓慢。
2.  **成长期**：产品的功能和模式逐渐被验证，开始发力推广，用户量会快速增长。
3.  **成熟期**：产品模式已经非常成熟，在市场上拥有了稳定的用户群体和品牌依赖，增长会进入一个相对平稳的平台期。
4.  **衰退期**：随着市场竞争或用户需求的变化，可能会出现新的替代品，用户开始流失，产品走向衰退。

我的运营策略，在产品的不同生命阶段，其侧重点是完全不同的，这一点我们稍后会详细展开。

![image-20250725114120468](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114120468.png)

与产品生命周期并行的，是我们运营工作中更具体、更微观的视角——**用户的生命周期**。从一个用户第一次接触我们的产品，到最后可能离开我们，我把他划分为这样几个阶段：

* **新手阶段**：用户刚刚进来，对一切都还很陌生，正在探索。
* **成长阶段**：用户度过了新手期，开始频繁地使用我们的核心功能，逐渐成为“熟客”。
* **沉默阶段**：用户因为某些原因，连续一段时间不再登录和使用我们的产品。
* **流失阶段**：用户长时间不再登录，甚至卸载了App，成为了“流失用户”。

我作为运营的核心工作之一，就是想尽一切办法，**引导用户从新手走向成长，激活沉默用户，召回流失用户**。

### 1.1.2 运营的主要工作

理解了我们工作的“战场”（产品和用户的生命周期）之后，现在，我们就来看看运营的“兵法”——在不同的阶段，我们的具体工作是什么。

![image-20250725114227133](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114227133.png)

这张图，是我为运营工作定义的一张“**全景地图**”。它清晰地告诉我们，运营的工作贯穿于产品从诞生到消亡的整个过程。接下来，我将为你详细拆解在每一个关键节点，我的工作重心是什么。

![image-20250725114254272](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114254272.png)

**1. 产品研发阶段：当好“领航员”**

在很多人以为运营是在产品上线后才介入时，我想强调，**一个优秀的运营，必须在产品研发阶段就深度参与**。在这个阶段，我的核心工作是确保我们“**做正确的事**”：

* **定义目标用户**：我们的产品，到底是为谁服务的？
* **挖掘用户需求**：这些目标用户，他们最真实的痛点是什么？
* **提炼产品价值**：我们的产品，准备如何为用户解决这些痛点，创造什么价值？

![image-20250725114316365](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114316365.png)

**2. 产品上线阶段：当好“侦察兵”**

当产品终于上线，我的工作重心立刻转移。我需要像一个侦察兵一样，密切关注市场和用户的“敌情”：

* **收集用户反馈**：用户在哪里吐槽？他们对什么功能最满意？
* **进行数据分析**：用户的访问路径是怎样的？哪个环节的转化率最低？
* **制定迭代策略**：基于反馈和数据，我会向产品经理提出清晰的、可执行的优化建议。

![image-20250725114343608](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114343608.png)

**3. 产品成长阶段：当好“突击手”**

进入成长期，我们的核心目标就是“**搞增长**”！这时，我需要像一个突击手，带领团队冲锋陷阵：

* **制定营销策略**：通过什么样的渠道（如抖音、小红书）去精准地获取用户？
* **策划拉新活动**：设计各种裂变、促销活动（就像图中的“年终大促”），实现用户量的快速爆发。
* **做好用户维护**：将好不容易拉来的新用户，通过社群、会员体系等方式维护好，防止他们流失。

![image-20250725114408185](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114408185.png)

**4. 产品成熟阶段：当好“农场主”**

当产品进入成熟期，用户增长放缓，我的关注点会更加务实，就像一个农场主，需要精耕细作，实现“丰收”：

* **探索用户变现**：设计更丰富的商业化模式，如会员、广告、增值服务等，实现收入增长。
* **强化品牌建设**：通过品牌活动、跨界合作等，提升品牌形象和行业地位。
* **精细化用户维护**：对高价值用户进行VIP服务，维持产品的核心用户群。

![image-20250725114431376](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114431376.png)

**5. 产品衰退阶段：当好“守护者”**

任何产品都不可避免地会走向衰退，但这并不意味着我们束手无策。在这个阶段，我的工作更像一个守护者：

* **用户召回与促活**：通过情感关怀、专属福利等方式，尽可能地唤醒和召回那些已经流失或沉默的老用户，延缓产品的衰退速度。

![image-20250725114449053](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114449053.png)

除了按照“时间轴”（生命周期）来划分，我更喜欢从“**职能**”的角度，把我日常的运营工作，归纳为以下四大核心模块。这能帮助你更清晰地理解运营团队内部的分工。

| **运营类型** | **我的解读和工作重心** |
| :--- | :--- |
| **产品运营** | 我的工作是**对产品负责**。我会深入到产品细节中，通过数据分析和用户调研，推动产品功能的优化和迭代，提升产品的核心指标（如：用户留存率、核心功能使用率）。|
| **活动运营** | 我的工作是**对增长负责**。我会策划各种线上线下的活动，比如优惠券、秒杀、拼团等，核心目标就是在短期内快速拉升某项业务指标（如：新用户数、订单量）。 |
| **用户运营** | 我的工作是**对人负责**。我会围绕用户，建立一套从“拉新-促活-留存-转化”的完整体系，比如搭建会员体系、用户社群，核心目标是提升用户的生命周期总价值（LTV）。 |
| **内容运营** | 我的工作是**对流量和氛围负责**。我会生产高质量的图文、短视频等内容，在站内（如社区）和站外（如抖音）进行分发，核心目标是吸引用户关注，营造社区氛围，打造品牌影响力。|

---
## 1.2 营销中心的功能

在我们投入了大量资源，搭建了“营销中心”这个复杂的系统后，我经常会问我的团队一个问题：它到底给我们带来了什么好处？运营工作有了它，和没有它，到底有什么变化？

要回答这个问题，我们必须先理解它的本质。

![image-20250725114924355](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725114924355.png)

我给**营销中心**的定义是：**它是我作为运营人员的“武器库”，是一个用来集中管理和执行各类营销活动的工具平台。**

它的最终目的只有一个：**提升GMV（商品交易总额）**。而要达成这个最终目的，我所有的工作，都可以被拆解为两大方向：

1.  **提高成交用户数**：让更多的人来买东西。
2.  **提升客单价**：让每个来买东西的人，买得更多。

![image-20250725115000139](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725115000139.png)

为了能够方便、高效地执行上述任务，我设计的“营销中心”后台，通常会包含三大核心功能模块：

* **活动管理**：这是最核心的模块，我可以用它来创建和管理各种促销活动，比如优惠券、秒杀、拼团、满减等。
* **内容管理**：我可以用它来发布和管理站内的广告、专题文章、种草视频等，通过内容来吸引和转化用户。
* **用户管理**：我可以用它来进行用户的分层和筛选（比如筛选出新用户、高价值用户），以便对不同的人群，进行精准的营销触达。

### 1.2.1 营销的维度与场景

现在我们知道了营销中心“有什么”，接下来就要看“怎么用”。在实际工作中，我会从不同的维度来思考和组织我的营销活动。

![image-20250725115045634](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725115045634.png)

首先，我需要从**营销的发起方**这个维度进行区分。在我们的电商平台上，搞营销的，不只有我们官方自己，还有入驻的成千上万的商家。因此，我把营销活动分为两类：

1.  **平台营销**：由我们平台官方运营团队发起，通常服务于整个平台的战略目标，比如“双十一”、“618”这样的大型促销活动，流量是面向全站的。
2.  **店铺营销**：由入驻的商家自己发起，服务于他们自己店铺的经营目标，比如某个服装店自己搞的“换季清仓”活动，流量主要是在他自己的店铺内。

![image-20250725115118164](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725115118164.png)

无论是我们平台，还是入驻的商家，我们发起一场营销活动，都不是凭空想象的，而是基于特定的“**营销场景**”。

上图这个轮盘，就很好地概括了我们日常工作的核心场景。比如：
* **当我想拉新时**：我会策划“新人专享礼包”。
* **当我想清库存时**：我会策划“限时秒杀”。
* **当我想提升客单价时**：我会策划“满200减20”的活动。
* **当老用户不活跃时**：我会给他们推送“老用户回归福利”。

### 1.2.2 场景、方式与功能的联动

![image-20250725115149190](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725115149190.png)

这张图，是我梳理的“营销中心”工作逻辑的核心。它清晰地展示了，我们如何从一个“**业务目标（营销场景）**”出发，选择合适的“**打法（营销方式）**”，并最终落到系统提供的“**武器（产品功能）**”上。

我把它整理成一个表格，你会看得更清楚：

| **我的业务目标（场景）** | **我采用的打法（方式）** | **我使用的系统功能** | **我的解读** |
| :--- | :--- | :--- | :--- |
| **提升品牌口碑** | 投放品牌故事广告、制作专题页 | **内容管理** | 我需要通过高质量的内容，来占领用户心智，这就要用到内容管理功能。|
| **尾货清库存** | 多买多优惠、第二件半价 | **活动管理** | 我需要快速刺激销量，因此选择促销活动，这就要用到活动管理功能。|
| **提升复购率** | 发放老用户专享优惠券 | **用户管理** + **活动管理**| 我首先要用“用户管理”筛选出“老用户”，然后用“活动管理”为他们创建专属的优惠券。|
| **提高客单价** | 满200减20、满3件赠礼品 | **活动管理** | 我通过设置阶梯式的优惠，来引导用户为了凑单而买得更多。 |
| **推广爆款商品** | 限时特价、秒杀 | **活动管理** | 我通过制造稀缺感和紧迫感，来为某个单品瞬间导入巨大流量和销量。 |

你看，通过这样一套“**场景 -> 方式 -> 功能**”的思考链路，我作为运营，就可以有条不紊地，利用“营销中心”这个武器库，来达成各种复杂的业务目标。

通过这一小节的学习，我们明确了“营销中心”这个产品模块，对我我们运营人员来说，它的核心作用，就是通过提供丰富的营销工具，帮助营销同时从“**提升成交用户数**”和“**提升客单价**”这两个维度去提升GMV。

而要实现这个目标，我们团队所依赖的，就是营销中心提供的“**活动管理**”、“**内容管理**”和“**用户管理**”这三大核心功能。



# 第二章：活动管理-单品活动

在上一章，我们对营销中心的“活动管理、内容管理、用户管理”三大模块有了整体认知。从本章开始，我们将聚焦于其中最核心、也是我作为运营最常使用的模块——**活动管理**。我将带你一起，从0到1地设计出电商平台最主流、最有效的几种营销活动。

![image-20250725134006164](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725134006164.png)

如上图所示，我所说的“活动管理”，本质上是一个集“**营销工具**”（如优惠券、折扣）、“**营销规则**”（如满减门槛、活动时间）和“**营销活动**”（将工具和规则组合成一个完整的活动）于一体的强大系统。





## 2.1 营销活动概述

在动手设计具体的功能之前，我需要先为你建立一个清晰的“活动地图”，让你了解我们手中的“武器”都有哪些种类。

### 2.1.1 营销活动分类

![image-20250725134058873](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725134058873.png)

面对五花八门的促销玩法，我习惯按照**优惠最终作用的范围**，把它们清晰地划分为两大类：

1.  **单品活动**：这类活动的优惠，是**精确到某一个或某几个具体商品（SKU）上的**。例如，“这件T恤限时秒杀价99元”、“那款手机参与预售，定金100抵200”。我们本章要学习的**预售、秒杀、直降、折扣、拼团**都属于这一类。
2.  **总价活动**：这类活动的优惠，是作用于用户**整个购物车的订单总金额上的**。例如，“全场实付金额满200元减20元”、“订单满3件打8折”。我们将在下一章学习的**满减/满折、满赠、优惠券**等，都属于这一类。

本章，我们的核心任务就是，把“单品活动”这一类的典型玩法，彻底学会、学透。

## 2.2 预售活动管理

我们首先要设计的，是“预售”这个非常重要的活动。它不仅是“双十一”这类大促的标配，也是很多新品发布时，我用来试探市场、引爆声量的利器。

### 2.2.1 预售活动需求分析

在我决定要设计“预售”功能之前，我首先会问自己：**业务上为什么需要它？它能解决什么问题？**

经过分析，我总结出预售的核心价值主要在于：

* **新品首发**：对于即将上市的新品，通过预售可以提前预热市场，并根据定金数据来预测首批备货量，降低库存风险。
* **大促蓄水**：在像“双十一”这样的大促开始前，通过预售提前锁定大量用户的购买意向和定金，为大促当天的爆发积蓄能量。
* **锁定用户**：一旦用户支付了定金，他的“反悔成本”就会增加，这就在很大程度上提前锁定了这笔交易。

明确了“Why”，接下来我就要思考“What”和“How”。我的设计思路，通常包含对**角色、流程、功能、字段**的完整定义。

### 2.2.2 预售活动产品方案设计

现在，我们就进入产品方案设计的核心环节。我将从“**角色与流程**”、“**功能与字段**”、“**列表与状态**”这三个方面，为你详细拆解我的设计。

**1. 角色与流程**

![image-20250725134243737](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725134243737.png)



![image-20250725134250658](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725134250658.png)

一个功能的设计，我首先要理清参与者（角色）以及他们之间的互动方式（流程）。

* **商家创建预售流程**：对于商家来说，创建一个活动的流程必须清晰、简单。我设计的流程是：
	- `选择活动类型(预售)` -> `填写基本信息` -> `设置活动规则` -> `选择参与商品`
	- 这是一个线性的、引导式的创建路径，能有效降低商家的使用门槛
* **用户参与预售流程**：对于用户，参与的流程则要顺畅、易懂。他们的核心流程是：
	* `浏览预售商品详情页` -> `支付定金` -> `（等待尾款支付时间）` -> `支付尾款`。

**2. 创建预售活动功能及字段信息**

![image-20250725134412506](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725134412506.png)

理清流程后，我们就可以定义商家创建活动时，到底需要填写哪些信息了。我将它分解为三个部分：

1.  **设置基本信息**：这部分是活动的“身份信息”，包括`活动名称`（方便商家自己识别）、`参与人群`（如新用户专享、VIP用户专享等）、`活动平台`（是在App生效还是H5生效）等。
2.  **填写活动规则**：这是预售活动的核心，我需要让商家可以设置`定金金额`、`定金膨胀系数`（例如定金100元可抵200元）、`定金支付时间`和`尾款支付时间`。
3.  **选择活动商品**：商家需要明确指定，是哪一个或哪些商品参与这次预售活动。

![image-20250725134701408](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725134701408.png)

上面那些字段，最终会构成我们商家后台的活动创建页面。通过这样一个结构化的表单，商家就可以清晰、高效地完成一个预售活动的创建。

![image-20250725134750203](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725134750203.png)

特别是在“选择活动商品”这一步，我还需要为商家设计一个便捷的商品选择器。如上图所示，当商家点击“选择商品”后，我会提供一个弹窗，让他能方便地从店铺的商品库中进行搜索和勾选，甚至能进一步选择到具体要参与活动的**商品SKU**（如：红色 L码）。

**3. 预售活动列表页与状态机**

![image-20250725194116572](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725194116572.png)

当商家创建完一系列活动后，他需要一个列表来统一查看和管理。我设计的活动列表页，会清晰地展示每个活动的`活动名称`、`定金/尾款时间`、`活动状态`等关键信息，并提供必要的操作入口。

![image-20250725194216822](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725194216822.png)

为了让管理工作清晰可控，我为每一个预售活动，都设计了一套“**状态机**”。这是后台产品设计中一个至关重要的概念。

* **状态流转**：一个活动从被创建到结束，它的生命周期是单向的：`未开始` -> `进行中` -> `已结束`。我不会允许一个已结束的活动再回到进行中。
* **状态与操作**：活动在不同的状态下，商家可以进行的操作是不同的。我通过上面这张表格来严格定义这些权限：
    * **未开始**：活动还没开始，一切都还来得及，所以商家可以对它进行`编辑`修改，或者直接`关闭`（取消活动）。
    * **进行中**：活动已经开始了，有用户可能已经付了定金。为了防止混乱和纠纷，我**不允许商家再对活动进行`编辑`**，但可以强制`结束`活动。
    * **已结束**：活动已经彻底结束，商家只能`查看`历史记录，不能再进行任何修改。

通过这样严谨的状态机设计，我就能确保我的预售活动功能，在被大量商家使用时，依然能够稳定、有序地运行。


---
## 2.3 秒杀、直降与折扣活动管理

在这一节，我将带你设计一组最常用、最直接的降价促销工具。它们的核心逻辑都是“**降低商品价格**”，但在活动氛围、技术实现和应用场景上，又各有侧重。

### 2.3.1 秒杀活动需求分析

在我设计的众多活动中，“**秒杀**”无疑是为平台引流、为商品制造爆点最有效的武器之一。它的核心价值在于，通过“**超低价 + 限时 + 限量**”这三大要素，营造出一种极度的稀缺感和紧迫感。

我设计秒杀功能，通常是为满足以下业务需求：
1.  **拉新引流**：用一两款超低价的商品，在短时间内吸引海量新用户访问平台。
2.  **爆款促销**：为重点商品或新品，制造一个现象级的抢购事件，提升其知名度和销量。
3.  **清仓甩货**：快速处理掉临期或过季的商品，回笼资金。

### 2.3.2 秒杀活动产品方案设计

秒杀活动的设计思路与预售活动一脉相承，我同样会从“角色与流程”、“功能与字段”、“列表与状态”等方面展开，但其中会有一些关键的差异点。

**1. 角色与流程**

秒杀活动的角色和流程与预售类似，但节奏更快：
* **商家创建流程**：依然是`选择活动类型(秒杀)` -> `填写基本信息` -> `设置活动规则` -> `选择参与商品`的线性流程。
* **用户参与流程**：用户的路径被大大缩短，变为`秒杀活动页` -> `（等待开抢）` -> `立即抢购` -> `下单支付`。整个过程必须在极短时间内完成。

**2. 创建秒杀活动功能及字段信息**

![image-20250725194912426](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725194912426.png)

我们来看创建秒杀活动需要哪些字段。它的大体结构与预售一致，但在“活动规则”上，有秒杀专属的特殊设计。

* **活动基本信息**：`活动名称`、`活动时间`（秒杀的起止时间通常很短）、`参与人群`等。
* **活动规则信息**：这是与预售最大的不同。这里没有复杂的定金和尾款，取而代之的是一个至关重要的规则——`是否限购`。
* **活动商品信息**：需要设置`秒杀价格`和`秒杀库存`。

![image-20250725195000199](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725195000199.png)

在创建秒杀活动的页面上，我必须重点突出“**是否限购**”这个选项。比如，我可以设计为“`限购 X 件`”。这是秒杀活动的生命线，既能防止“黄牛”刷单，也能让更多普通用户能享受到优惠，保证了活动的公平性和参与度。

![image-20250725195013198](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725195013198.png)

商品选择的模块，我可以完全复用之前为预售活动设计的组件。这正是我作为产品经理，在设计时需要时刻具备的“**模块化思维**”，能极大地提升研发效率。

**3. 秒杀活动列表页**

![image-20250725195033616](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725195033616.png)

商家创建好的秒杀活动，会统一进入这张列表进行管理。列表的字段和状态机（未开始、进行中、已结束、已失效）的设计，与预售活动基本一致，这里我就不再赘述。

**4. 平台固定时间秒杀专场实现**

![image-20250725195052650](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725195052650.png)

一个常见的运营需求是，平台希望有一个固定的秒杀频道，比如像上图案例中那样，有“8点场”、“10点场”、“12点场”等。作为商家，只能报名参加这些固定的场次，而不能随意设置秒杀时间。这个功能我该如何实现呢？

![image-20250725195111770](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725195111770.png)

我的方案是，在平台运营总后台，增加一个“**秒杀场次管理**”的功能。

如上图所示，我们平台的运营人员，可以在这里**预先设定**好一天内所有的秒杀时间段（如：`08:00-10:00`，`10:00-12:00`...）。

配置好之后，商家在创建秒杀活动时，`活动时间`的设置方式就从“自由选择时间”，变成了“**从已有场次中选择一个进行报名**”。这样，我就能将所有商家的秒杀活动，规整到平台统一的、固定的频道页面中，便于集中展示和引流。

### 2.3.3 直降与折扣活动设计

![image-20250725195143264](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725195143264.png)

我们已经彻底搞懂了最复杂的秒杀活动。那么，它的两个“简化版”兄弟——**直降**和**折扣**，又该如何设计呢？我通常会通过对比来理清思路。

**1. 与秒杀活动对比分析**

直降和折扣，本质上是“**弱化版**”的秒杀。它们弱化了秒杀最核心的“紧迫感”和“稀缺感”，从而变成了更常规、更温和的促销工具。

| **活动类型** | **核心** | **氛围营造** | **我的设计侧重** |
| :--- | :--- | :--- | :--- |
| **秒杀** | 限时、限量、超低价 | **强**（紧张、刺激） | 必须有**限购**，活动时间**短** |
| **直降** | 在一定时间内，直接降价 | **弱**（直观、清晰） | 只需要设置**活动价**和**活动时间** |
| **折扣** | 在一定时间内，打折销售 | **弱**（有计算成本）| 只需要设置**折扣率**和**活动时间** |

**2. 创建活动所需信息**

基于以上的对比，我在设计“直降”和“折扣”的创建功能时，就可以在“秒杀”的基础上做减法：

* **创建直降活动**：我只需要商家设置`活动时间`和`直降后的价格`。其他信息（如活动名称、参与商品等）完全可以复用秒杀的设计。
* **创建折扣活动**：我只需要商家设置`活动时间`和`折扣率`（例如，输入“8”代表八折）。系统会自动根据原价计算出折后价。

通过这种“**设计复用+做减法**”的思路，我就能用最低的研发成本，快速地为运营团队，提供一套功能覆盖全面的价格管理工具。



---
## 2.4 拼团活动管理

在掌握了预售、秒杀等传统促销工具后，现在，我们要学习一个与众不同的、自带“**社交裂变**”属性的强大玩法——**拼团**。它不仅仅是降价，更是驱动用户主动为我们去拉新用户的增长利器。

### 2.4.1 拼团活动需求分析

![image-20250725195740483](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725195740483.png)

我之所以要设计拼团功能，其核心的业务诉求，正如上图所述，是为了满足商家“**低成本拉新**”的强烈需求。

拼团的本质，是一种“**利益共享**”的社交电商模式。我把它总结为一个循环：
1.  平台或商家提供一个极具吸引力的“**拼团价**”。
2.  老用户被价格吸引，为了成功购买，他必须**分享链接**给好友。
3.  新用户看到好友分享的优惠信息，基于社交信任和价格吸引，也参与进来，从而**完成了一次拉新**。

通过这种方式，商家把一部分营销预算，直接补贴给了消费者，并巧妙地利用他们的社交关系链，为自己带来了精准、低成本的新流量。

### 2.4.2 拼团活动产品方案设计

拼团的设计比之前的活动要复杂，因为它同时涉及“**B端（商家）**”和“**C端（用户）**”，并且引入了“**社交分享**”的流程。我将为你一步步拆解。

**1. 角色与流程**

![image-20250725195831494](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725195831494.png)

在拼团这个玩法中，我定义了两个新的C端用户角色：

* **团长 (Leader)**：第一个发起拼团的人。
* **参团人 (Member)**：通过团长分享的链接，加入这个团的人。

基于这两个角色，我梳理出了B端和C端两套核心流程：

* **B端 - 商家创建流程**：这个流程对商家来说必须简单，我设计的步骤是：
	*  `选择活动类型(拼团)` -> `填写活动信息` -> `设置活动规则` -> `选择参与商品`。
* **C端 - 用户参与流程**：这是拼团玩法的核心，用户的路径是一个社交闭环：
	* `发起拼团` -> `分享拼团` -> `好友参与拼团` -> `（拼团成功）`。

**2. B端 - 创建拼团活动功能**

![image-20250725195922219](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725195922219.png)

我们重点来看商家创建活动时，“**活动规则**”的配置，这是拼团功能的灵魂。

* **成团人数**：一个团需要多少人才能成功。通常我会建议商家设置为2人，因为这是裂变效率最高的模式。
* **成团有效期**：开团后，必须在多长时间内邀请到足够的好友，否则拼团失败。例如，24小时。
* **模拟成团**：这是一个非常重要的“**用户体验优化**”功能。如果我勾选了它，就意味着当一个团在有效期结束时，如果还差一个人，**系统会自动模拟一个“机器人”用户参与进来，让这个团强制成功**。这能极大地降低因拼团失败给真实用户带来的挫败感。

![image-20250725200041910](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725200041910.png)

这些字段最终会落地为我们商家后台的创建页面。商家可以根据自己的商品属性和营销目标，灵活地配置拼团的玩法。

**3. B端 - 拼团数据与管理**

商家创建完活动，还需要对进行中的团订单进行管理。

![image-20250725200130988](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725200130988.png)

首先，商家创建的拼团活动，会和其他活动（秒杀、预售等）一起，出现在这张总的“**活动列表**”里，方便进行统一的启停和编辑操作。

但拼团的特殊性在于，一个“拼团活动”下面，会由不同用户发起无数个具体的“**团（Pingtuan Order）**”。因此，我还需要为商家设计一个专门的“**拼团数据**”列表。

在这里，商家可以清晰地看到每一个“团”的状态：是**拼团中**、**拼团成功**，还是**拼团失败**。这对于客服介入处理售后问题至关重要。

![image-20250725200327195](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725200327195.png)

当商家需要处理某个具体团的问题时，他可以点击“查看”，进入“**拼团详情**”页。这里有这个团所有成员的昵称、下单时间、订单状态等详细信息，方便客服进行精细化的管理和沟通。

![image-20250725200210298](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725200210298.png)



**4. C端 - 核心页面设计（讲解）**

虽然我们这里没有C端的页面原型，但我可以为你讲解一下，要支撑起整个拼团流程，我至少需要为用户设计这几个核心页面：
* **商品详情页**：在这个页面，我会同时展示“单独购买价”和“发起拼团价”，用巨大的价差来吸引用户发起拼团。
* **拼团进行页**：当用户开团后，会进入这个页面。页面上必须有清晰的“**成团倒计时**”、已加入成员的头像列表、以及一个最醒目的“**邀请好友参团**”的分享按钮。
* **参团页**：当好友通过分享链接点进来时，看到的页面。他可以清楚地看到是谁邀请他、还差几个人成团，并可以直接点击“一键参团”完成支付。

至此，一个完整的、兼顾了B端管理和C端体验的拼团功能，我们就设计完成了。

## 2.5 本章总结

恭喜你！我们已经完整地学习了“**活动管理-单品活动**”这一核心章节。现在，让我们一起回顾一下本章最重要的知识点。

**1. 活动运营，主要的单品活动有哪些？**

在本章，我们系统性地学习并设计了四种最主流的单品活动，你需要牢记它们的定位和核心价值：

* **预售**：核心是“**锁定需求**”，常用于新品首发和大型促销的蓄水期。
* **秒杀**：核心是“**制造稀缺**”，是短期内吸引流量、打造爆款的终极武器。
* **直降/折扣**：核心是“**直接让利**”，是最常用、最灵活的常规促销手段。
* **拼团**：核心是“**社交裂变**”，是利用社交关系链，实现低成本拉新的增长引擎。

**2. 创建活动的三个主要步骤是什么？**

通过对四种不同活动的反复设计，我们发现，无论玩法如何变化，我作为产品经理，在设计B端（商家后台）创建流程时，其底层逻辑是高度一致的。我把它总结为“**创建三部曲**”：

1.  **设置基本信息**：明确活动叫什么、给谁用、在哪里生效。
2.  **配置活动规则**：定义活动的核心玩法，例如预售的定金、秒杀的限购、拼团的人数等。
3.  **选择参与商品**：圈定本次优惠具体生效的商品范围。

掌握了这个结构化的设计思路，未来无论你遇到多么新颖的营销玩法，都能够快速、清晰地把它转化为一套完整的产品方案。

到这里，我们关于单品活动的设计就全部完成了。在下一章，我们将继续挑战“总价活动”的设计，学习优惠券、满减满赠等更复杂的玩法。



---

# 第三章：活动管理-总价活动

欢迎来到第三章。在上一章，我们的所有活动都聚焦于“**单个商品**”的降价。但作为运营，我还有一个更重要的目标：**如何让用户一次买得更多？** 这就引出了我们本章的主题——**总价活动**。这类活动不再关注单个商品的价格，而是着眼于用户的“**购物车总价**”，通过设置一个“满X元”的门槛，来激励用户为了凑单而购买更多商品。

## 3.1 营销活动

我们将要学习的第一个，也是最经典的总价活动，就是“满减”。

### 3.1.1 总价活动-满减活动管理

![image-20250725201823505](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725201823505.png)

我为什么要设计“满减”功能？其核心的业务诉求，正如上图所述，是为了**提升客单价**。

在日常运营中，我会遇到很多类似的场景，比如：
* **满减**：订单金额满100元，减免20元。
* **满赠**：订单金额满200元，就赠送一个小礼物。
* **满折**：订单满3件，就享受8折优惠。

这些玩法的本质，都是在用户下单的最后一步“临门一脚”，通过一个有吸引力的优惠，引导他们“再多买一件”。本节，我们就来从0到1地设计出“满减”这个功能。

**1. 角色与流程**

满减活动的设计思路，依然遵循我们熟悉的框架：`角色` -> `流程` -> `功能` -> `字段`。

* **核心角色**：依然是**商家**（活动的创建者）和**用户**（活动的参与者）。
* **整体流程**：商家在后台创建满减活动，用户在前台浏览商品并参与活动。

![image-20250725202251529](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202251529.png)

![image-20250725202329925](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202329925.png)

对于用户来说，参与的体验必须是无缝且直观的。我的设计要点是：

* **在商品详情页**：就要清晰地展示出这个商品正在参与的满减活动，比如上图案例中的“满200减20”标签，这会成为用户把它加入购物车的重要动力，而这又分为`直接满减`与`阶梯满减`，对于阶梯满减来说，用户购买的越多优惠越多
* **在结算页**：当用户选购的商品满足了满减门槛时，系统需要自动地计算优惠，并清晰地展示出减免的金额，给用户带来“占到便宜”的满足感。

**2. 商家创建满减活动**

![image-20250725202431819](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202431819.png)

对于商家，我设计的创建流程依然是清晰的四步走：

1.  **选择活动类型**：商家首先在活动中心选择创建“满减活动”。
2.  **填写基本信息**：设置活动的名称、时间、面向人群等。
3.  **设置满减规则**：这是满减活动的核心，也是设计的重点。
4.  **选择活动商品**：圈定参与本次满减活动的商品范围。

![](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202812573.png)

现在，我们把上述流程，细化为具体的字段。

* **基本信息**：`活动名称`、`活动时间`、`参与人群`等，这些都是我们之前设计中可复用的标准组件。
* **活动规则**：这是设计的关键。与单品活动不同，这里的规则是作用于总价的。我通常会为商家提供两种优惠类型：
    * **直接满减**：最简单的模式，例如“满100元，减10元”。
    * **阶梯满减**：更灵活的模式，例如“满100减10，满200减30，满300减50”，通过多个档位，进一步刺激用户提高客单价。
* **活动商品**：商家可以选择`全部商品`都参与，也可以`指定部分商品`参与。

![image-20250725202902097](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202902097.png)

最终，这些字段会构成我们商家后台的创建页面。请特别注意“**优惠类型**”的设计，我通过单选框（Radio Button）让商家可以在“直接满减”和“阶梯满减”中切换。当选择“阶梯满减”时，我还提供了一个“**+**”号按钮，让商家可以动态地增加优惠层级。这种设计，兼顾了功能的强大性和操作的灵活性。

**3. 满减活动列表**

![image-20250725202925437](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725202925437.png)

当商家创建完活动后，他需要一个统一的列表来管理。我设计的这张列表，除了包含活动名称、时间、状态等常规字段外，还增加了一个非常重要的字段——“**优惠信息**”。

在这个字段里，我会清晰地展示出这个活动的核心规则，如“满300元，减30元”。这能让商家在不点进详情的情况下，快速地浏览和识别每一个活动，极大地提升了管理效率。同时，列表页也提供了`查看`、`编辑`、`结束`等必要的操作入口，其背后的状态机逻辑与我们之前设计的完全一致。


---
### 3.1.2 总价活动-满赠活动管理

在开始设计前，我们先来看一个我经常遇到的真实业务场景：**某个商品库存积压严重，直接打折降价怕影响品牌形象，怎么办？**

一个非常有效的策略，就是把它作为“**赠品**”，去搭配那些热销的商品。这就是“满赠”活动的核心价值之一：它不仅能像“满减”一样提升客单价，还能帮助我们优化库存结构，并给用户带来一种“意外之喜”的超值感。

![image-20250725203555950](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203555950.png)

我给**满赠**的定义是：**在活动时间段内，用户购买主商品，当订单金额或商品件数满足预设的门槛时，即可免费获得指定赠品的营销活动。**

和满减一样，我也把它分为两种常见形式：
1.  **直接满赠**：规则简单直接，例如“满100元赠USB风扇”。
2.  **阶梯满赠**：设置多个优惠档位，例如“满100元赠USB风扇，满200元赠小熊电饭煲”，以此激励用户冲击更高的消费档次。

**1. 商家创建满赠活动**

满赠活动的B端创建流程，与满减活动的主干完全一致，我依然采用了我们熟悉的“**创建四部曲**”。

`选择活动类型 -> 活动基本信息 -> 满赠规则设置 -> 活动商品选择`

这体现了我在设计后台系统时的一个重要原则：**保持操作逻辑的一致性，降低用户的学习成本**。

![image-20250725203652822](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203652822.png)

我们把创建流程拆解为具体的字段。这个功能的关键，在于“**满赠规则设置**”和“**活动商品选择**”这两步，有了全新的内涵。

![image-20250725203726873](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203726873.png)

现在，我们来看这张“创建满赠活动”的页面原型，这是本节功能设计的核心。

它的上半部分（活动信息、优惠类型）与“满减”的设计几乎可以完全复用。真正的区别在下半部分：

* **选择商品（主商品）**：这里的商品列表，指的是用户**必须购买**才能享受优惠的“**主商品**”。商家可以选择全店商品参与，也可以指定部分热销商品参与。
* **选择赠品**：这部分是“满赠”功能设计的灵魂。我需要在这里，为商家提供另一个商品选择器，让他可以从自己的商品库中，选择一个或多个商品，作为本次活动的“**赠品**”。

我的设计要点是，赠品的选择必须灵活。商家不仅可以指定“赠品A”，还可以设置“满200元，可在赠品B或赠品C中任选其一”，把选择权交给用户，从而提升活动的吸引力。

**2. 用户端体验**

![image-20250725203831933](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203831933.png)

一个后台功能设计得再好，如果用户在前端无感知，那也是失败的。所以，我必须在C端（用户端）清晰地把优惠信息传达出去。

* **商品详情页**：当一个商品参与了满赠活动，我会在其价格旁边，增加一个醒目的“**赠**”字标签。用户点击后，可以看到详细的活动规则，例如“满100元即可获赠XX商品一件”。
* **结算页**：当用户的订单满足满赠条件时，在结算页的商品清单中，我会**把赠品作为一个单独的行给展示出来，价格标注为0元**。这能给用户带来实实在在的“获得感”，让他清晰地感知到自己享受到的优惠。

**3. 满赠活动管理**

![image-20250725203937920](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725203937920.png)

最后，商家创建的所有满赠活动，也会进入到我们统一的“**活动管理**”列表中。

这张列表的整体框架是完全复用的。我需要做的，仅仅是确保“**优惠信息**”这一列，能够准确地显示出满赠活动的核心规则，例如“满100元赠A商品”。通过这种方式，商家可以高效地对进行中或未开始的活动，进行统一的管理和后续操作


---
### 3.1.3 总价活动-满折活动管理

我们已经有了“满减”（针对订单金额的优惠）和“满赠”（针对订单价值的提升），现在，我需要一个能直接激励用户购买“**更多件数**”的工具。尤其是在服装、图书、日用品这类客单价不一定高，但用户常常会一次性购买多件的品类中，这个工具就显得尤为重要。于是，“**满折**”活动就应运而生了。

满折，即**当用户购买指定商品的件数，达到预设门槛时，即可享受整单相应折扣的优惠**。例如，“指定商品任选3件，即可享受8折优惠”。

**1. 商家创建满折活动**

在B端设计上，我依然沿用了标准化的“**创建四部曲**”流程，这能确保商家在使用我们后台时，有一种统一、连贯的操作体验，而不需要为每一种新活动都重新学习一遍。

![image-20250726092532568](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092532568.png)

我们把目光聚焦到这个功能的核心——“**填写活动规则**”。

![image-20250726092602653](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092602653.png)

我们直接来看“创建满折活动”的页面原型。它的核心设计，全部体现在“**活动规则**”这个模块里。

* **规则设置**：这里的规则不再是“满X元”，而是“**满X件**”，优惠方式也从“减Y元”变成了“**打Z折**”。
* **阶梯折扣**：和满减、满赠一样，我也为“满折”设计了阶梯模式。商家可以设置“满2件打9折，满3件打8折”，通过一个更优惠的折扣力度，来强力吸引用户“再多拿一件”，从而有效提升订单的商品件数（即购物深度）和销售总额。

**2. 用户端体验**

![image-20250726092913868](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726092913868.png)

在C端，为了让用户能清晰地感知到“满折”优惠，我的设计思路和“满减”是完全一致的。

* **活动感知**：我会在参与活动的商品详情页上，用一个醒目的“**满折**”标签来吸引用户的注意力。
* **优惠计算**：在结算页，当用户购物车中参与活动的商品件数满足了规则后，系统会自动计算出折扣金额，并在“优惠金额”处明确展示，让用户直观地看到自己省了多少钱。

**3. 满折活动管理**

![image-20250726093347431](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726093347431.png)

在“**活动管理**”后台，商家也能清晰地看到所有已创建的满折活动。

在列表的“**优惠类型**”这一列，我会直观地显示出“满3件，8折”这样的核心规则，便于运营人员进行后续的查看、编辑或结束等管理操作。通过对列表、创建页等核心组件的复用，我能用最低的成本，最高效地扩展出新的营销玩法。

到此为止，我们已经系统性地掌握了总价活动中的“三剑客”：**满减、满赠、满折**。它们在B端的设计上有很多共通之处，但在C端的体感和核心运营策略上又各有侧重。

最后，我给你留一个思考题：如果我们要做一个更复杂的“**满返**”（比如，订单满100元，返还20元优惠券或2000积分）活动，它的B端和C端产品设计，与我们已经学过的这三种相比，又会有哪些共同点和差异点呢？带着这个问题，我们将在后续的课程中继续探索。

---
### 3.1.4 总价活动-套装活动

在之前的几种总价活动中，我们都是设定一个“规则门槛”，让用户**自由地选择商品**去凑单。但很多时候，我作为商家，希望能更“主动”地为用户规划好一组合集，并给出一个打包优惠价来提升整体销量。

比如，快餐店里“汉堡+薯条+可乐”的套餐，美妆领域“水、乳、精华”的护肤品套装，或者服饰店里“上衣+裤子”的搭配组合。这些，就是我们这节要设计的——**套装活动**。

**1. 套装活动设计思路**

套装活动的核心，是**将多个独立的商品，打包成一个新的销售单元进行促销**。它的设计思路依然遵循我们的标准框架，关键在于对“套装规则”的定义。

在B端设计上，我将“套装规则”进一步细分为了两种核心类型，以满足商家多样化的营销需求：
1.  **固定套装**：一个打包好的组合，用户必须完整购买，不可更改。
2.  **搭配套装**：提供一定的选择空间，例如“主商品A + 搭配商品B/C/D任选其一”。

**2. 设计详解：固定套装**

![image-20250726094401049](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094401049.png)

“固定套装”是最简单、最常见的模式。它的逻辑就是“**商品A + 商品B + 商品C = 一个固定的打包价**”。

我们来看它的创建页面：
* **套装类型**：商家首先选择“固定套装”。
* **套装价格**：商家需要为这个打包好的组合，设置一个全新的、有吸引力的“套装价”。
* **选择商品**：商家在下方的商品选择器中，勾选出所有要包含在这个固定套餐里的商品。

这种模式的优点是规则清晰，用户决策成本低。缺点是灵活性较差。

**3. 设计详解：搭配套装**

“搭配套装”则为商家和用户提供了更高的灵活性。它的逻辑更像是“**主商品区任选一件 + 搭配商品区任选一件 = 优惠组合价**”。

在创建页面上，它的设计也更为复杂：
* **套装类型**：商家需要选择“搭配套装”。
* **选择主商品**：商家首先要指定一批“主商品”。
* **选择搭配商品**：然后，再指定一批可供搭配的“副商品”。

![image-20250726094747956](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094747956.png)

这种模式非常适用于服饰、3C配件等品类。例如，我可以设置一个“买任意一款手机（主商品），即可半价换购任意一款手机壳（搭配商品）”的活动。这给了用户一定的自主选择权，体验更好，也更容易促成关联销售。



**4. 用户端体验**

![image-20250726094729297](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726094729297.png)

当一个商品参与了套装活动时，我该如何在C端把它呈现给用户呢？

我的方案是，在该商品的详情页下方，专门开辟一个“**优惠套餐**”的区域。

如上图所示，这个区域会清晰地展示出套餐内的所有商品图片、名称，以及最具吸引力的“**套餐价**”，并提供一个“立即购买套餐”的按钮。通过这种直观的对比，用户能立刻感知到购买套餐的超值之处，从而被引导完成购买。

**5. 套装活动管理**

![image-20250726095246261](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095246261.png)

最后，在B端的活动管理后台，商家可以统一管理所有已创建的套装活动。

为了便于商家区分，我特意在活动列表中增加了一列“**套装类型**”。通过这一列，商家可以一目了然地分清，哪些是“固定套装”，哪些是“搭配套装”，从而进行更有针对性的管理和数据分析。


---
## 3.2 营销规则

我们已经为商家设计了品类丰富的单品活动和总价活动。但当这些“武器”可以被同时使用时，一个新的、也是更复杂的问题就摆在了我的面前。

![image-20250726095759645](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095759645.png)

为了解决这个“优惠爆炸”的问题，防止出现混乱和亏损，我作为产品经理，必须设计一套清晰、严谨的“**营销规则**”。它就像我们营销系统的“基本法”，规定了所有活动之间应该如何协同工作。

我将这套复杂的规则，拆解为四大核心模块。接下来，我们将逐一攻克。

### 3.2.1 规则一：叠加与互斥

我们先来看一个真实的运营场景：新款iPhone上市，运营同学恨不得把秒杀、拼团、满减、满赠所有优惠都给它加上，让它看起来“优惠到极致”。但，这样真的可以吗？

答案是否定的。如果没有任何限制，多个大力度的单品活动叠加，商品价格可能会变成负数。因此，我必须定义清楚

哪些活动之间是“**互斥**”的（不能同时享受），哪些又是可以“**叠加**”的（可以同时享受）。

![image-20250726095825205](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726095825205.png)

要定义规则，我首先需要把平台内所有的“优惠”形式，按照性质进行归类。我将它们划分为四大类：
1.  **单品活动**：直接作用于**商品本身**的优惠，如秒杀、直降、拼团。
2.  **总价活动**：作用于**订单总价**的优惠，如满减、满赠、满折。
3.  **抵扣活动**：用户使用**虚拟资产**进行抵扣的活动，如优惠券、积分、礼品卡。
4.  **支付活动**：与**支付渠道**绑定的优惠，如信用卡支付立减。

![image-20250726104251355](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104251355.png)

理清了分类，我就可以制定出上面这张“**优惠叠加互斥规则表**”。这是我们整个营销系统的“交通法规”。

它的核心逻辑可以总结为：
* **同类互斥**：一个商品不能同时参与两个“单品活动”（例如，你不能让一个商品既是秒杀价，又是拼团价）。同理，一个订单也不能同时满足两个“总价活动”。
* **异类叠加**：不同类型的活动，原则上是可以叠加享受的。例如，一个商品可以先享受“秒杀”价（单品活动），达到门槛后可以再享受“满减”（总价活动），结算时还可以用“优惠券”（抵扣活动），最后用“信用卡支付”（支付活动）再减一点钱。

### 3.2.2 规则二：活动顺序

我们已经知道哪些活动可以一起用了。但新的问题又来了：**先算哪个，后算哪个？** 顺序不同，结果可能天差地别。

![image-20250726103851360](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726103851360.png)

我的设计原则是，**模拟用户真实的交易环节，定义一条雷打不动的计算链路**。

1.  **第一步：计算单品活动**。先算出商品经过秒杀、直降等活动后的价格。
2.  **第二步：计算总价活动**。用第一步得出的价格总和，去判断是否满足满减、满折的门槛。
3.  **第三步：计算抵扣活动**。用第二步得出的价格，去使用优惠券、积分等进行抵扣。
4.  **第四步：计算支付活动**。用第三步得出的最终应付金额，去享受支付渠道的优惠。

![image-20250726103917986](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726103917986.png)

但在这个大原则下，还有一个更细致的问题：当同一种类型的活动有多个时，又该怎么算？比如，一个订单同时满足“满200减20”和“满300减40”。

这里，我设计了两种模式供运营人员选择：
* **递进式**：先计算第一个门槛的优惠，用优惠后的金额，再去判断是否满足下一个门槛。这种模式对平台最有利，能严格控制成本，但计算逻辑复杂。
* **平行式**：所有满足门槛的优惠，都基于原始金额进行计算，然后全部生效。这种模式对用户最友好，计算速度快，但商家有亏损的风险（例如，用户买300元商品，同时享受了“满200减20”和“满300减40”，平行计算下总共优惠了60元）。

![image-20250726104208247](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104208247.png)

通过上面这个案例，你可以清晰地看到，一个999元的鼠标，在“递进式”和“平行式”两种不同规则下，最终的成交价是不同的。在后台为运营设计这个功能时，我必须把这两种模式的选择权交给他们，并讲清楚其中的利弊。


### 3.2.3 规则三：优惠分摊规则（深度解析）

我们必须认识到，这个规则的存在，是为了解决一个核心的财务问题：**当一笔享受了总价优惠的订单发生部分退款时，如何确保退款金额的计算是公平且准确的，以防止平台或商家产生亏损。**

#### 1. 基础场景：单一总价优惠的分摊

![image-20250726104708001](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726104708001.png)

我们从一个最基础，也最常见的场景开始。如上图所示，一个60元的订单，通过“满60减20”的活动，用户实际支付了40元。现在，用户需要退掉其中10元的A商品。我们应该退给他10元吗？

绝对不行。如果退10元，就意味着用户用30元买到了价值50元的B和C商品，享受了“满50减20”的优惠，这与我们“满60减20”的活动规则相悖，平台或商家平白无故地亏损了。

要解决这个问题，就必须引入我们分摊规则的“**第一性原理**”：**任何一笔作用于订单整体的优惠，都必须按比例分摊到订单内的每一个商品上。**

我制定的核心分摊公式如下：
`商品优惠金额 = 总优惠金额 × (商品金额 / 参与活动商品的价格总和)`

现在，我们用这个公式来精确计算A商品的退款金额：
1.  **计算A商品分摊到的优惠金额**：
    `A商品优惠金额 = 20元 × (10元 / 60元) = 3.33元`
2.  **计算A商品应退款金额**：
    `A商品应退款 = A商品原价 - A商品分摊到的优惠金额 = 10元 - 3.33元 = 6.67元`

只有这样，我才能确保退款后，剩余的B、C两件商品，其合计支付金额（40-6.67=33.33元）与它们应该享受的优惠是匹配的。

#### 2. 进阶问题：计算精度与尾差处理

![image-20250726110938190](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110938190.png)

应用这个公式，我们可以继续计算出B和C的应退款金额。但是，在真实的计算机系统中，除法运算常常会导致无限循环小数（例如 `10/60 = 0.1666...`），这会带来精度问题。如果A、B、C的优惠金额分别是3.33, 6.67, 10.00，三者相加可能等于20.00，也可能等于19.99或20.01。这个微小的误差，在海量订单下，会累积成巨大的财务漏洞。

![image-20250726111003639](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111003639.png)

为了确保万无一失，我设计了一条“**尾差处理规则**”：**最后一个商品的优惠金额 = 总优惠金额 - 之前所有商品已分摊的优惠金额之和**。

![image-20250726111530614](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111530614.png)

同时，为了让计算过程更稳定，我还会制定一条工程上的最佳实践：
![image-20250726111541687](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111541687.png)
**按商品金额从小到大进行计算**，然后将所有的计算尾差，都归结到最后一个（即金额最大）的商品上。这能保证，无论如何计算，**一个订单内所有商品分摊的优惠总和，绝对等于这笔订单享受的优惠总额**，一分不多，一分不少。

#### 3. 终极挑战：多商品、多活动、多层级优惠的混合分摊

现在，我们来挑战一个最复杂的场景，它融合了我们前面学到的所有规则。

![image-20250726111624491](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111624491.png)

这个场景的复杂性在于，优惠不再是单一的“满减”，而是包含了**单品活动、总价活动、抵扣活动**的多层级优惠。

要解决这个问题，我必须严格遵循我们在上一节定义的“**活动顺序**”。

![image-20250726111749384](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111749384.png)

我们必须再次重申这条计算的生命线：**单品活动 > 总价活动 > 抵扣活动**。优惠的计算和分摊，必须严格按照这个优先级，层层递进。

![image-20250726111732314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726111732314.png)

现在，我们对这个终极案例，进行庖丁解牛式的拆解：

* **第一步：计算单品活动。**
    * A商品参加“直降1000元”，其优惠后的价格变为 `3000 - 1000 = 2000` 元。
    * B商品不参与单品活动，价格仍为 `100` 元。
    * **此时，用于下一步计算的订单价格基础是 A=2000元, B=100元。**

* **第二步：计算总价活动。**
    * B商品参加“满100-50”的满减活动，其价格变为 `100 - 50 = 50` 元。
    * A商品不参与总价活动，价格仍为 `2000` 元。
    * **此时，用于下一步计算的订单价格基础是 A=2000元, B=50元。**

* **第三步：分摊抵扣活动（优惠券）。**
    * 现在，我们需要将这张1500元的优惠券，分摊到A和B两个商品上。
    * **用于分摊的商品价格总和为**：`2000元（A的折后价） + 50元（B的折后价） = 2050元`。
    * **B商品应分摊的优惠券金额** = `1500元 × (50元 / 2050元) ≈ 36.59元`。
    * **A商品应分摊的优惠券金额** = `1500元 - 36.59元 = 1463.41元` （应用尾差处理规则）。

* **第四步：得出结论。**
    * A商品总共优惠了：`1000元（直降） + 1463.41元（优惠券） = 2463.41元`。
    * B商品总共优惠了：`50元（满减） + 36.59元（优惠券） = 86.59元`。

通过以上严谨的、层层递进的规则设计，我才能确保，无论运营人员配置出多么复杂的优惠组合，我的系统都能准确、公平、安全地计算出最终价格和退款金额，守住平台和商家资金安全的生命线。这，就是“分摊规则”设计的严肃性和重要性所在。


### 3.2.4 规则四：风险与防范

![image-20250726110124008](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110124008.png)

作为产品经理，我不仅要设计功能，更要保护平台和商家的利益，防止他们因为误操作而造成亏损。

![image-20250726110131853](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110131853.png)

为此，我设计了一套“**风险防范组合拳**”：

1.  **低价预警**：当系统检测到商家设置的优惠力度过大，可能导致亏损时（例如，折后价低于成本价），自动弹出醒目的预警提示，让商家进行二次确认。
2.  **活动审核**：对于一些重要的、或者新手商家创建的活动，我可以设计一个“审核”流程。活动创建后不会立刻生效，而是进入“待审核”状态，需要由运营主管或平台管理员审核通过后，才能正式上线。
3.  **安全策略**：为了防止专业的“羊毛党”通过技术手段刷单，我还需要设计一些基础的“反作弊”策略，例如限制同一个IP地址、同一个设备、同一个收货地址的参与次数等。

![image-20250726110202924](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726110202924.png)

最后我们总结一下，以上就是我设计的营销规则体系。它就像一张无形的、精密的大网，确保了我们整个营销活动系统，能够在复杂多变的场景下，依然能够公平、稳定、安全地运行。



## 3.3 营销工具
在第三章的后续部分，我们将进入一个更有趣、更具互动性的领域——**营销工具**。它不再是简单的让利，而是通过游戏化的玩法，来提升用户的参与度和粘性，实现“品效合一”的营销目标。


### 3.3.1 抽奖工具

抽奖，是一种低成本、高回报的互动营销玩法。它通过设置有吸引力的奖品，来驱动用户完成我们期望的特定行为，如每日访问、分享拉新等。

#### 1. 抽奖工具的需求分析

在我动手设计具体的产品功能前，我必须首先回归原点，搞清楚我们“为什么”要做这个功能。

![image-20250726112559235](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112559235.png)

我之所以要在系统中，增加一个看似和“卖货”没有直接关系的“抽奖”功能，其核心驱动力，来自于商家提升用户活跃度与忠诚度的真实诉求。

通过上图的需求背景，我提炼出抽奖工具需要满足的两大核心业务目标：
1.  **提升老用户粘性**：通过每日免费抽奖等形式，为老用户提供一个持续访问我们App或店铺的理由，提升DAU（日活跃用户）。
2.  **促进新用户增长**：将“分享”与“增加抽奖次数”进行绑定，激励老用户主动去进行社交分享，从而为店铺带来低成本的新流量。

![image-20250726112647407](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112647407.png)

明确了业务目标后，我就需要从最宏观的视角，来构思这个工具的完整生态。我首先要定义其中的“**核心角色**”和“**整体流程**”。

* **核心角色**：抽奖工具的生态中，主要有三方参与者：
    * **平台方**：我作为平台的产品经理，负责设计和提供稳定、通用的抽奖工具。
    * **商家**：是抽奖活动的发起者和成本承担者，他们使用我提供的工具，来配置活动规则和奖品。
    * **用户**：是抽奖活动的最终参与者。
* **整体流程**：整个业务的生命周期，是一个清晰的闭环。如上图所示，`平台提供工具` -> `商家配置活动` -> `用户参与抽奖` -> `商家发放奖品` -> `用户查看奖品`。我的产品设计，必须确保这个链条上的每一个环节都顺畅无误。

在宏观流程中，“**用户参与抽奖活动**”是整个玩法能否成功的关键。那么，用户的体验旅程应该是怎样的呢？

![image-20250726112736166](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726112736166.png)

为了让用户体验顺畅、且富有激励性，我为C端用户设计了上面这条完整的“**参与流程**”闭环。

我们来一步步拆解这个流程：
1.  **触发抽奖**：用户进入活动页面，点击“立即抽奖”按钮。
2.  **前置判断**：系统首先判断用户“是否还有抽奖次数”。
3.  **次数用完**：如果次数已用完，系统会弹出提示，并引导用户去“**通过分享获得更多抽奖次数**”。这正是我们实现拉新裂变的关键设计。
4.  **执行抽奖**：如果次数未用完，系统则根据后台配置的算法，来判断本次抽奖“是否中奖”。
5.  **结果反馈**：
    * 如果**中奖**，则弹出“恭喜中奖”的强提示，并引导用户去“查看奖品信息”。
    * 如果**未中奖**，则弹出“谢谢参与”的安慰性提示。

![image-20250726124606407](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124606407.png)

通过对业务目标、宏观流程、用户旅程的完整分析，我就为接下来进行具体的“产品设计”，打下了坚实的基础。

---

#### 2. 抽奖工具的产品设计

根据我们之前的分析，抽奖工具的设计，必须同时兼顾**商家端（B端）**的易用性和灵活性，以及**用户端（C端）**的趣味性和流畅体验。我将为你分别进行拆解。

##### **一、 商家端（B端）产品设计**

我们首先来看商家后台的设计。我需要为商家提供一个足够强大，但操作又不能过于复杂的活动创建流程。

![image-20250726124513436](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124513436.png)

我设计的商家创建流程，依然遵循我们熟悉的“四部曲”，确保了后台操作的一致性。接下来，我们详细看一下每一步的具体设计。

**1. 设置基本信息**

![image-20250726124625992](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124625992.png)

这是活动创建的第一步，商家需要在这里设置活动的“身份信息”，包括`活动名称`、`活动时间`、`活动平台`（是在App内还是H5页面生效）以及`活动说明`（即活动规则的文字描述）。

**2. 填写抽奖规则**

![image-20250726124651499](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124651499.png)

这是抽奖功能设计的灵魂，它决定了整个活动的核心玩法。

![image-20250726124725381](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124725381.png)

在设计这部分功能时，我主要思考并解决了上面这两个核心问题。

* **抽奖类型**：我为商家提供了两种模式，来回答第一个问题。
    * `即时抽奖`：用户抽完立刻知道结果。这是最常见的模式，能提供即时反馈和刺激。
    * `非即时抽奖`：用户参与后，需要等待统一的开奖时间（例如，每周五开奖）。这种模式适用于需要营造悬念和持续关注度的活动。
* **抽奖条件**：我允许商家设置参与门槛，例如用户必须`使用XX积分`、达到`XX会员等级`，或者`完成订单后`才能获得抽奖资格。
* **参与次数**：商家可以灵活控制用户参与的频率，是`每人每天可抽N次`，还是在整个活动周期内`每人一共可抽N次`。
* **分享设置**：这是实现裂变增长的关键。我需要让商家可以配置“**用户分享活动后，可以额外增加N次抽奖机会**”的规则。
* **提示文案**：为了让体验更友好，我允许商家自定义各种场景下的提示文案，如`中奖提示`、`未中奖提示`、`活动未开始提示`等。

**3. 选择抽奖奖品**

![image-20250726124857807](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726124857807.png)

在这一步，商家需要设置本次活动的“奖池”。为了回答“如何控制奖品数量”这个问题，我要求商家在设置每一个奖品时，都必须明确两项核心信息：**`奖品数量`**和**`中奖概率`**。系统会根据这两项配置，通过抽奖算法来精确控制奖品的发放。

为了丰富奖品的类型，我设计的奖池支持多种奖品形态：

* **实物商品**

    ![image-20250726125038417](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125038417.png)

* **优惠券**
    ![image-20250726125104812](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125104812.png)
    
* **积分**
    ![image-20250726125113561](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125113561.png)



![image-20250726125326357](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125326357.png)

![image-20250726125341599](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125341599.png)

通过这种模块化的设计，商家就可以非常灵活地配置出具有吸引力的奖品组合。

##### **二、 用户端（C端）产品设计**

当商家在后台配置好活动后，C端用户看到和体验到的，必须是一个有趣、流畅的界面。

**1. 抽奖主页面**

![image-20250726125205586](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125205586.png)

![image-20250726125128157](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726125128157.png)

我采用了最经典的“**九宫格**”抽奖样式。这个页面的核心元素包括：
* **抽奖区域**：九个格子中，分布着不同的奖品和“谢谢参与”的选项。
* **抽奖按钮**：用户点击“立即抽奖”，转盘开始转动。当用户次数用尽，按钮会变为“明天再来”或“分享获取次数”等不可用状态。
* **中奖名单**：页面下方会实时滚动最新的中奖信息，营造一种热闹、很多人中奖的氛围，来激励其他用户参与。

**2. 抽奖结果反馈**

对于“即时抽奖”来说，及时的结果反馈至关重要。
* **中奖**：立刻弹出强提示的“恭喜中奖”弹窗，告知用户获得了什么奖品。
* **未中奖**：弹出安慰性的“祝您下次中奖”弹窗，并引导用户“下次再来”。

**3. 我的奖品列表**


所有用户中奖的记录，都会沉淀在“**我的中奖记录**”这个页面。用户可以在这里，清晰地看到自己获得的所有奖品，以及每一个奖品的当前状态，是“**待兑换**”还是“**已兑换**”，方便进行后续的核销与使用。

---
### 3.3.2 优惠券工具

如果说“抽奖”是提升趣味性和互动性的利器，那么“**优惠券**”则是我工具箱中，用途最广泛、玩法最灵活、最能实现精细化运营的“万能钥匙”。它几乎可以和任何营销场景进行组合，是我们刺激用户行为、提升转化和复购的核心手段。

![image-20250726130639644](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130639644.png)

#### 1.优惠券工具的需求分析

![image-20250726130159412](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130159412.png)

我为什么要设计一套独立的、复杂的优惠券系统？我们来看一个商家最常见的困惑：**用户在一次大促中尽兴消费后，就“消失”了，如何能有效地把他们“拉回来”，产生第二次、第三次消费呢？**

“满减”、“满折”这类活动，解决的是“当下”的转化问题。而优惠券，则是我用来连接“**当下**”与“**未来**”的桥梁。我将优惠券的核心业务价值，总结为两点：

1.  **提升复购率**：通过在用户完成交易后、或在日常的互动中，向其发放一张带有有效期的优惠券，我就为他创造了一个“必须在未来某个时间点回来消费”的强烈理由。
2.  **精准控制成本**：与全场打折不同，优惠券可以“**指哪打哪**”。我可以控制它的发放数量、发放人群、使用门槛和适用商品，从而将营销预算，精准地花在最有价值的用户和商品上。

**1. 优惠券的构成要素**

在设计功能前，我首先要像解剖麻雀一样，拆解“优惠券”这个事物的核心构成要素。

一张小小的优惠券，看似简单，实则包含了丰富的信息和规则。我作为产品经理，在设计时必须考虑到以下所有要素：

| **要素分类** | **核心字段** | **我的解读** |
| :--- | :--- | :--- |
| **券面价值** | `面值` / `折扣` | 这是优惠券最核心的价值。例如，10元代金券，或8折折扣券。 |
| **使用门槛** | `使用条件` | 用户需要满足什么条件才能使用这张券。例如，“满100元可用”。无门槛券则没有此项。|
| **适用范围** | `使用范围` / `使用平台` | 这张券可以用在哪些地方。是“全场通用”，还是仅限“购买A商品”可用？是仅限App内，还是小程序也可用？ |
| **有效期限** | `使用时间` | 这是刺激用户在未来消费的关键。是“领取后7天内有效”，还是只能在“固定的10月1日到10月7日”之间使用？ |
| **发放与领取**| `发放数量` / `领取人` | 这张券总共准备发多少张？是所有人都可以公开领取，还是只发给“VIP用户”的专属福利？ |

只有将这些要素全部定义清楚，我才能设计出一套足够灵活、能满足各种运营场景的优惠券系统。

**2. 优惠券的生命周期与用户旅程**

![image-20250726130240979](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130240979.png)

定义了优惠券的核心要素后，我们再从宏观视角，看一下优惠券从诞生到消亡的“一生”，也就是它的生命周期流程。

* **核心角色**：**商家**（创建者和发放者）与**用户**（领取者和使用者）。
* **生命周期**：`商家创建/发放` -> `用户领取` -> `用户使用` -> `商家统计`。我的产品设计，必须支撑起这个完整的闭环。

对于用户来说，他们与优惠券的互动，主要发生在两个核心环节：“**领券**”和“**用券**”。

* **“领券”环节的场景设计**

![image-20250726130516515](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130516515.png)

我必须在用户消费决策的关键路径上，为他们提供清晰、便捷的领券入口。例如，在商品详情页，我会明确地告诉用户“**本店现有以下优惠券可供领取**”，用户点击后，即可在弹窗中一键领取。

* **“用券”环节的场景设计**

![image-20250726130544241](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726130544241.png)当用户选好了商品，来到订单结算页时，这是优惠券发挥作用的最后、也是最关键的环节。在这个页面，我会设计一个“优惠券”的选择栏，用户点击后：

1.  系统会自动判断用户当前订单满足了哪些优惠券的使用门槛，将“**可用优惠券**”高亮地展示在最前方。
2.  对于那些用户已领取、但当前订单“**不可用**”的优惠券，我也会展示出来，并清晰地告知用户“不可用”的原因（例如，“未达到满减金额”）。这是一种反向的激励，可能会促使用户返回去，再多买一件商品来凑单。

通过对业务目标、核心要素、生命周期和用户旅程的完整分析，我们就为接下来进行具体的B端“优惠券创建”功能设计，铺平了道路。

好的，我们已经清晰地定义了优惠券工具的需求。接下来，我将带你进入产品设计的核心环节，看看我是如何将这些需求，转化为一个强大、灵活且易于商家使用的后台功能。



---

#### 2.优惠券工具的产品设计

我的设计哲学是，**把复杂留给自己，把简单交给用户**。对于优惠券这种玩法极其丰富的工具，B端（商家端）的设计尤其考验产品经理的抽象和归纳能力。我需要将万千种运营场景，收敛到一套结构化、标准化的创建流程中。

![image-20250726131021810](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726131021810.png)

我设计的优惠券创建过程，主要分为三大步骤：**设置基本信息 -> 填写领取规则 -> 填写使用规则**。

##### 一、设置基本信息与领取规则

![image-20250726131936090](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726131936090.png)

在创建优惠券的第一步，我将“基本信息”和“领取规则”放在了一起，因为它们共同定义了这张优惠券的“**身份**”和“**发放方式**”。

* **基本信息**：
    * `优惠券名称`：方便商家在后台进行识别和管理。
    * `优惠券数量`：即“库存”，控制了这张券的总发放量，是控制成本的第一道闸门。
    * `使用平台`：定义这张券是在App、H5还是小程序中生效。
* **领取规则**：这是实现“**精细化运营**”的关键。
    * `领取用户`：我为商家提供了多种用户圈定方式。可以是`全部用户`可领的普惠券；也可以是针对`用户等级`（如：钻石会员专享）或`用户标签`（如：高潜流失用户）的精准券；甚至支持`上传文件`，针对特定的用户ID列表进行一对一发放。
    * `领取张数`：可以限制`每人限领N张`，防止被“羊毛党”恶意刷取。
    * `领取时间`：定义这张优惠券可以被领取的起止时间。
    * `公开设置`：这是一个非常重要的开关。如果勾选了“**公开领取**”，这张券就会出现在商品详情页等C端入口，供用户主动领取。如果不勾选，它就是一张“**私有券**”，不会对外展示，只能由运营人员通过后台手动发放给指定用户，常用于客服补偿等场景。

##### 二、填写使用规则——玩法的核心

这是优惠券设计的灵魂所在。一张券到底“怎么用”，决定了它的营销价值。我设计了多种优惠券类型，来满足不同的业务场景。

**2.1 满减券的设计**

![image-20250726132042176](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132042176.png)

这是最常见的一种优惠券。它的核心规则包括：

* **优惠券类型**：首先，我定义了券的适用范围。是仅限购买某些商品的`商品券`，还是全场通用的`通用券`，或者是只能抵扣运费的`运费券`。
* **使用门槛**：即“满X元可用”。
* **优惠券面额**：即“减Y元”。
* **有效期**：这是刺激用户复购的关键。我设计了两种模式：
    * `固定时间`：例如，国庆节专用券，只能在10月1日到10月7日之间使用。
    * `相对时效`：这种模式更为灵活，例如`自领取之日起N天内可用`，或者`自领取次日起N天内可用`。这能确保每个领到券的用户，都有一个完整的有效期。
* **适用范围**：这里可以更精细地控制券能用于哪些商品，是`全部商品`，还是`指定商品`、`指定类目`或`指定品牌`。

![image-20250726132157048](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132157048.png)

当商家选择“指定商品”时，我会提供一个与我们之前设计完全一致的、可复用的商品选择器组件，让他可以方便地进行勾选。

**2.2 折扣券的设计**

![image-20250726132136682](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132136682.png)

折扣券的设计，与满减券大部分相同，核心区别在于“优惠方式”的定义。商家不再是输入一个固定的“面额”，而是输入一个“**折扣率**”，例如“打8折”。

**2.3 立减券的设计**

![image-20250726132143949](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132143949.png)

立减券，又称“现金券”，是优惠力度最大的一种。它的特点是“**无使用门槛**”。在设计上，我只需要让商家输入一个“**立减金额**”即可。这种券通常用于新用户注册礼包、或高价值用户的回归召回等关键场景。

##### 三、 优惠券的管理

![image-20250726132209133](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132209133.png)

当商家创建完所有优惠券后，他可以在这张“**优惠券管理列表**”中，对所有券进行统一的查看和操作。

我为这张列表设计了清晰的信息维度：
* **核心信息**：优惠券的`名称`、`类型`（满减/折扣）、`发放数量`、`有效期`等一目了然。
* **优惠券状态**：我通过`未开始`、`领取中`、`已结束`、`已失效`这几种状态，让商家可以清晰地了解每一张券当前的生命周期阶段。
* **快捷操作**：商家可以对不同状态的券，进行`查看`、`编辑`、`结束活动`或查看`数据`等操作。

通过以上这套B端产品设计，我就为商家提供了一个功能强大、配置灵活、管理方便的“优惠券弹药库”，让他们可以根据不同的营销战役，自由地组合和使用这些“弹药”。

---
#### 3. 优惠券的逻辑规则

当一个订单中，存在多个商品、多张可用优惠券时，系统必须有一套清晰的规则，来决定最终如何计算优惠。我将它总结为三大核心规则。

##### **规则一：叠加与互斥**

![image-20250726132817387](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132817387.png)

这个问题的答案，取决于优惠券的类型。我制定的核心原则非常简单：**同一类型的优惠券，一个订单只能使用一张；不同类型的优惠券，在不冲突的情况下，可以叠加使用。**

例如，用户可以同时使用一张“店铺满减券”、一张“平台品类券”和一张“运费券”，但不能同时使用两张“店铺满减券”。

![image-20250726132847661](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726132847661.png)

上图就是一个非常典型的真实案例，一个订单同时叠加了多种不同类型的优惠，最终形成了一个极具吸引力的价格。我的系统设计，就必须能够支持这种复杂的叠加计算。

![image-20250726133202001](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726133202001.png)

我们来看上面这个非常经典的、模拟真实购物场景的案例。要判断这4张优惠券能否同时使用，我设计的系统，会遵循以下逻辑进行严谨的校验。

**第一步：识别每张优惠券的“类型”与“范围”**

我首先会将这4张券进行归类：

| **优惠券**  | **券类型** | **作用范围**        | **使用门槛**      |
| ----------- | ---------- | ------------------- | ----------------- |
| **优惠券1** | `商品券`   | 仅限“鼠标”这个商品  | 无门槛（立减）    |
| **优惠券2** | `店铺券`   | 仅限“A店铺”内的商品 | 订单金额满100元   |
| **优惠券3** | `平台券`   | 跨店铺所有商品      | 订单总金额满400元 |
| **优惠券4** | `运费券`   | 仅限“A店铺”的运费   | 无门槛（免邮）    |

**第二步：逐一校验每张券的“门槛”与“叠加规则”**

1. **校验【优惠券1】**：它是一张`商品券`，直接作用于鼠标，无使用门槛。**可用**。
2. **校验【优惠券2】**：它是一张`店铺券`。计算A店铺的商品总价为 `99元(鼠标) + 30元(鼠标垫) = 129元`。这个价格满足了“满100元”的使用门槛。由于它和优惠券1的类型（`店铺券` vs `商品券`）不同，因此**可叠加使用**。
3. **校验【优惠券3】**：它是一张`平台券`。计算跨店订单的总价为 `129元(A店铺) + 398元(B店铺) = 527元`。这个价格满足了“满400元”的使用门槛。由于它和前两张券的类型（`平台券` vs `店铺券`/`商品券`）都不同，因此**可叠加使用**。
4. **校验【优惠券4】**：它是一张`运费券`，属于特殊类型，用于抵扣A店铺的运费，通常可以和所有其他类型的优惠券叠加。**可用**。

**第三步：得出最终结论**

- **这4张优惠券可以同时使用吗？**
	- **可以。** 因为这四张券分别属于**商品券、店铺券、平台券、运费券**，类型各不相同，且订单情况满足了它们各自的使用门槛，因此它们可以完美地叠加使用。
- **系统应该推荐使用哪张优惠券呢？**
	- **全部推荐使用。** 在这个场景下，由于所有券都可以叠加，并且都能带来优惠，系统的最优策略就是默认将这4张券**全部勾选并应用**，从而为用户计算出最终的、优惠力度最大的订单价格。

在电商后台，我定义优惠券的“类型”，其核心依据，并不是它“长什么样”，而是它的“**作用范围**”和“**成本由谁承担**”。只有基于这两个维度，我才能建立起一套严谨、无歧义的叠加互斥规则。

我将优惠券，严格划分为以下几个**层级完全不同**的类型：

| **优惠券类型** | **定义与作用范围** | **成本承担方** | **核心目的** |
| :--- | :--- | :--- | :--- |
| **单品券** | **层级最低**。仅对指定的某一个商品（SKU/SPU）生效。 | 商家 | 推广单一爆款或清仓。 |
| **店铺券** | **层级居中**。对**指定店铺内**的所有或部分商品生效。 | **商家** | 提升**本店的客单价**和转化率。 |
| **平台券** | **层级最高**。可**跨店使用**，对平台上所有或部分店铺的商品生效。| **平台** | 提升**整个平台的GMV**和用户活跃度。 |
| **运费券** | **类型特殊**。仅用于抵扣运费。 | 商家 或 平台 | 降低用户的购买决策门槛。 |

**核心规则**：只有**同一个层级**的优惠券，才存在“互斥”关系。**不同层级**的优惠券，因为其作用范围和成本方完全不同，所以**可以叠加**。

-----

##### **规则二：推荐原则**

当一个订单同时满足多张优惠券的使用门槛时，系统应该如何帮助用户做出最优决策，商家或许并不想让用户同时使用多张卷，所以在我们上一小结的设计中，三个劵同时归类为了`商品券`，这时候我们的优先计算原则就是优惠最大的金额

![image-20250726133421088](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726133421088.png)

我的设计原则是——**永远帮助用户做出“最省钱”的选择**。系统后台会自动计算所有可能的、可叠加的优惠券组合方式，并默认选中那个“**优惠总金额最大**”的最佳组合。

我们来看一个复杂的实战案例：

![image-20250726134305240](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134305240.png)

面对这个复杂的场景，我的系统后台会进行如下的智能计算：

![image-20250726134246505](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134246505.png)

如上图所示，系统会：
1.  **匹配**：首先判断每个商品分别适用哪些优惠券。
2.  **组合**：然后尝试所有可行的叠加组合。
3.  **择优**：最后计算出“水杯类满减券 + A店铺满减券 + 运费券”这个组合，可以优惠131元，是所有组合中优惠力度最大的，因此系统会向用户默认推荐这个方案。

##### **规则三：分摊规则**

我们再次遇到了这个至关重要的财务规则。

![image-20250726134517071](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134517071.png)

当一个订单使用了多张、作用范围不同的优惠券后，发生部分退款时，分摊计算就变得更加复杂。我将这个计算过程，用一张表格为您清晰地呈现：

![image-20250726134534335](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134534335.png)

**最终结论**：当用户想要退货A商品（鼠标，原价99元）时，我设计的系统会从其原价中，扣除掉它所分摊到的`23.35`元优惠，最终应退款 `99 - 23.35 = 75.65` 元。只有这样，才能保证财务的绝对严谨。

#### 4. 优惠券工具的数据

作为一名专业的产品或运营，我绝不能只满足于“把功能做出来”。我必须知道我策划的每一次活动，效果如何，成本怎样。因此，为优惠券工具设计一套完善的“**数据详情**”，是必不可少的一步。

![image-20250726134849709](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726134849709.png)

我将优惠券的数据监控，分为了四大维度，共计11个核心指标：

通过对这11个核心数据指标的持续监控和分析，我作为运营，就能够精准地洞察每一次优惠券活动的成败得失，并为下一次的优化，提供可靠的数据支撑。

---
# 第四章：内容管理

在前面的章节中，我们聚焦于“交易”本身，学习了如何设计商品、订单、促销等核心功能。但一个成功的电商平台，绝不仅仅是一个冷冰冰的交易场所。它还需要有温度、有吸引力、能够留住用户的内容。这一章，我们就来学习如何管理这些“内容”。


## 4.1 内容管理系统

### 4.1.1 内容管理系统的定义

在我早期的产品生涯中，如果运营同事想修改首页上的一句宣传语，或者替换一张活动图片，她需要给我提一个需求单，我再排期给研发工程师，工程师修改代码、测试、再发布上线……整个流程非常繁琐，效率极低。

为了解决这个问题，我需要为运营团队，提供一个可以“**自主、高效地管理网站内容**”的后台工具。这，就是内容管理系统（Content Management System, CMS）诞生的初衷。

![image-20250726150544242](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150544242.png)

从广义上讲，我们之前课程中为自媒体平台设计的“文章管理”后台，其实就是一个基础的CMS。它实现了最核心的一点：**让非技术人员（如运营、编辑），可以通过一个可视化的后台，去创建、修改、发布和管理网站上的内容**，而无需再依赖研发人员。

![image-20250726150610613](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150610613.png)

而在更严格的定义下，一个专业的CMS，如上图案例所示，其能力远不止于管理文章。它是一个能够让运营人员，在不写一行代码的情况下，自由定义网站的页面布局、栏目、以及填充具体内容的强大软件系统。

它的核心思想在于——**将“内容”与“展现”彻底分离**。内容（文字、图片）被存储在数据库中，而展现（页面的样式、布局）则由模板来控制。运营人员只需要关心“内容”的生产，而无需关心它最终会“如何展示”。

### 4.1.2 内容管理系统的原理

要理解CMS的原理，我最喜欢用“**搭乐高**”来做比喻。

![image-20250726150706862](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150706862.png)

* **最小单位（乐高积木）**：这就好比是我们系统里的基础“**内容组件**”，比如一个轮播图、一个商品推荐模块、一篇文字介绍。
* **不同的搭建（拼装过程）**：这就好比是我们的**CMS后台**。运营人员就像一个乐高大师，他可以自由地选择用哪些“积木”，以及把它们拼装在页面的哪个位置。
* **迥然各异的结果（乐高成品）**：这就好比是用户最终看到的**前端页面**。同样是那些“积木”，既可以搭成一辆跑车，也可以搭成一座城堡。

![image-20250726150744252](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726150744252.png)

这个“搭乐高”的过程，在我的产品设计中，被抽象为三个核心技术步骤：

1.  **基础组件**：首先，我需要和研发团队一起，把所有可能用到的内容元素，都预先开发成一个个独立的、可复用的“**组件**”。例如，‘轮播图组件’、‘商品推荐组件’、‘文章列表组件’、‘视频播放组件’。这些组件就是我们的“乐高积木”，是构成页面的原子单位。
2.  **位置+内容**：然后，在CMS后台，我需要提供一个可视化的页面编辑器。运营人员可以在这里，像“拖拽”积木一样，决定**哪个组件（内容），应该出现在页面的哪个位置**。比如，他可以决定“把轮播图组件放在页面顶部”，然后为这个组件上传5张要轮播的图片。
3.  **动态页面**：最后，当用户访问这个页面时，系统会根据运营人员在后台的配置，**实时地、动态地**将这些组件和它们绑定的内容，从数据库中读取出来，然后像“搭积木”一样，瞬间“组装”成一个完整的网页，呈现给用户。

总结一下，内容管理系统（CMS），本质上是一个**将内容生产与技术开发解耦**的软件系统。它通过“**组件化**”的设计思路，让运营人员可以像搭乐高一样，灵活、高效地创建和管理动态页面。

---
## 4.2 店铺装修

在上一节，我们学习了CMS的底层原理。现在，我们就将这个强大的理论，应用到电商平台最核心的场景之一——**店铺装修**。

### 4.2.1 店铺装修的设计思路

![image-20250726151509850](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151509850.png)

我为什么要设计“店铺装修”功能？因为我必须满足商家“**个性化经营**”的核心诉求。一个商家，除了上架和售卖商品外，更希望能方便地打造自己独特的店铺形象，以区别于其他店铺，吸引和留住属于自己的客户。

![image-20250726151519631](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151519631.png)

因此，我给“店铺装修”的定义是：**一套允许商家（或平台运营），在无需编写代码的情况下，对店铺页面进行动态配置的系统。** 它的本质，就是CMS在电商产品中的典型运用。

![image-20250726151534604](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151534604.png)

它的底层设计思路，完全源于我们之前学习的CMS三步曲：
1.  **提供基础组件**：我为商家预先准备好各种与电商相关的“装修材料”。
2.  **设置位置+内容**：商家可以通过一个可视化的后台，自由地组合这些“材料”，并填充自己的内容。
3.  **生成动态页面**：用户的店铺首页，会根据商家的配置，动态地生成，实现“千店千面”。

### 4.2.2 店铺装修的常见组件

![image-20250726151549452](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151549452.png)

那么，作为产品经理，我应该为商家提供一个怎样的“装修材料市场”呢？我会提供哪些基础组件给他们使用？

![image-20250726151603118](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151603118.png)

为了让商家能清晰地找到自己想要的“材料”，我将所有组件，归纳为四大类别：**商品类、图文类、营销类、及其他类**。下面我将为你逐一拆解每一类组件的设计。

#### 1. 商品类组件

这是店铺装修中最核心、最高频的组件类型。

* **定义**
    ![image-20250726151616933](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151616933.png)
    
* **常见的展示形式**
    ![image-20250726151629264](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151629264.png)为了满足不同的陈列需求，我至少会提供以上三种主流的样式：
    * **大图模式**：适合突出展示单个爆款或主打新品。
    * **双列模式**：在有限的屏幕空间内，可以展示更多商品，提升浏览效率。
    * **分类滑动模式**：允许用户在同一个组件内，通过横向滑动，来切换和浏览不同分类的商品。
    
* **配置逻辑**
    ![image-20250726151700053](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151700053.png)商家在使用这个组件时，他的操作流程是清晰的三步：首先选择一个心仪的“**展示样式**”，然后从自己的商品库中“**添加商品**”，最后再对这个组件的“**展示信息**”（如标题、要不要显示价格等）进行微调。
    
* **组件信息拆解**
    ![image-20250726151726638](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151726638.png)这张思维导图，完整地展示了我为“商品类组件”设计的所有可配置项。它允许商家对组件的每一个细节进行自定义。

![image-20250726151835815](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726151835815.png)最终，以上所有的设计，都会落地为像上图这样的后台操作界面。商家在右侧进行配置，左侧可以实时预览到最终的效果，真正做到“所见即所得”。

---
#### 2. 图文类组件

这是用于展示非商品内容的、最具灵活性的组件，常用于品牌宣传、活动介绍、榜单推荐等场景。

* **定义**
    ![image-20250726152332242](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152332242.png)
    它的核心价值，在于让商家可以用图文并茂的形式，向用户传递信息、讲述故事，从而提升店铺的“内容感”和“品牌调性”。

* **常见的展示形式**
    ![image-20250726152342998](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152342998.png)
    我为图文组件设计了多种主流的样式，以满足不同的内容承载需求：
    * **单图展示**：最简洁的形式，通常用于店铺头图、或者某个活动的巨幅海报，视觉冲击力强。
    * **轮播图形式**：可以在有限的区域内，承载多张图片信息，是首页黄金位置最常用的组件，用于轮播展示多个重要活动或商品。
    * **多图形式**：可以将多张小图以矩阵的方式进行陈列，常用于“买家秀”、“热门榜单”等场景。

* **配置逻辑**
    ![image-20250726152356474](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152356474.png)
    商家配置图文组件的流程非常直观：先选择一个自己喜欢的“**展示形式**”，然后上传图片并为每张图片“**配置跳转链接**”（可以链接到商品、分类或活动页），最后再对组件的“**样式**”（如背景色、间距）进行微调。

* **组件信息拆解**
    ![image-20250726152415434](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152415434.png)
    这张思维导图，完整地展示了我为“图文类组件”设计的所有可配置项。它主要分为“图片”和“文字”两个部分，商家可以自由地组合使用。例如，他可以只上传图片，构成一个图片广告；也可以只使用文字，发布一个店铺公告。

![image-20250726152454089](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152454089.png)
最终，商家在后台的操作界面就像这样。在右侧的配置面板，他可以输入文字、设置样式、调整间距，而在左侧就能实时地看到页面的最终效果，真正做到“所见即所得”。

#### 3. 营销类组件

这是将我们之前在第三章设计的各种营销活动，以“组件”的形式，直接“移植”到店铺首页的强大工具、页面的动态配置主要是为了满足各种活动，而活动中往往具备营销推广信息，因此除了常规图片和商品配置外，还有使用非常频繁的营销类组件

* **定义**
    ![image-20250726152548141](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152548141.png)
    它的核心目的，是让店铺内正在进行的营销活动，能够在首页最显眼的位置得到**曝光和引流**，从而提升活动的参与率和转化率。

* **常见的营销类型**
    ![image-20250726152557494](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152557494.png)
    通过这个组件，商家可以在首页，搭建出各式各样的“活动楼层”，例如：
    * **秒杀类型**：展示正在进行或即将开始的秒杀商品，并带有倒计时，营造紧张的抢购氛围。
    * **拼团类型**：以列表或卡片的形式，展示店铺内的拼团商品。
    * **优惠券类型**：将店铺内可领取的优惠券，直接展示出来，方便用户一键领取。

* **配置逻辑**
    ![image-20250726152639022](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152639022.png)
    商家使用此组件的逻辑是：先从下拉菜单中“**选择一种营销类型**”（如秒杀），然后系统会让他去“**关联一个已经创建好的具体活动**”，最后再对这个活动楼层的“**展示样式**”进行一些简单的编辑。

* **组件信息拆解**
    ![image-20250726152647885](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152647885.png)
    这张思维导图，归纳了营销类组件的可配置项，核心就是“**组件类型**”和“**添加优惠活动**”这两步。

这张图完美地诠释了“营销类组件”设计的灵活性和扩展性。当商家在后台…

![image-20250726152902959](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152902959.png)

* …选择“**秒杀**”类型时，配置面板就会让他去关联一个已经创建好的“秒杀活动”。

![image-20250726152943608](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152943608.png)

* …选择“**拼团**”类型时，配置面板则会让他去关联一个“拼团活动”。

![image-20250726152953465](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726152953465.png)

* …选择“**优惠券**”类型时，他则可以勾选多张希望展示给用户的优惠券。

通过这种**根据不同类型，动态变换配置项**的设计，我用一个“营销类组件”，就兼容了未来所有可能新增的营销玩法，是典型的“**对扩展开放，对修改关闭**”的设计原则的体现。


---
#### 4. 其他类组件

![image-20250726154021085](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154021085.png)

最后，我们来看“其他类组件”。我将它定义为：**除商品、图文、营销之外，那些用于完善页面布局、优化用户导航体验的基础性辅助组件。**

这类组件中，虽然包含了搜索框、辅助空白、分割线等，但从产品设计的角度看，最具设计价值和复杂性的，是“**店铺自定义分类导航**”组件。我将重点为你拆解这个组件的设计思路。

**1. 核心矛盾：后台类目 vs. 前台导航**

要理解这个组件，我们必须先理解一个平台电商的底层逻辑。

* **后台类目：平台的“标准语言”**

![image-20250726154039950](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154039950.png)

为了方便平台管理和统一搜索，所有商品在上传时，都必须归属到平台预设的、标准化的“**后台类目**”中。这个类目体系是庞大且固定的，就像一个国家图书馆的图书分类法（如：服装鞋包 > 女鞋 > 高跟鞋），商家通常无法修改。

* **前台导航：商家的“营销语言”**

![image-20250726154115185](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154115185.png)

但是，一个商家从他自己经营的角度，可能完全不想按照平台的类目来组织商品。他可能希望在店铺首页，设置一个像上图这样，包含“**店长推荐**”、“**夏日清凉好物**”、“**新品速递**”等，更具营销感和个性化的“**店铺前台导航**”。这个导航是直接呈现给顾客的，必须灵活、可由商家自定义。

**2. 解决方案：建立映射关系**

那么，我作为产品经理，如何调和“后台类目的固定性”与“前台导航的灵活性”之间的矛盾呢？答案就是——**在产品设计中，建立一套映射关系。**

![image-20250726154215194](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726154215194.png)

这张后台截图，就揭示了这种映射关系。当一个商家上传商品时，他需要做两件事：

1.  **选择平台类目**：他必须为商品选择一个“后台类目”，例如图中的“食品饮料 > 糖果/巧克力 > 巧克力”。这是为了让**平台**能认识、索引这个商品。
2.  **选择店铺分组**：同时，他还可以为商品选择一个或多个自己创建的“**店铺分组**”（即自定义分类），例如图中的“新品上架”。这，是为了让这个商品能够出现在他**自己店铺**首页的“新品”这个导航栏下面。

**3. “店铺导航栏组件”的设计**

基于以上逻辑，我设计的“店铺导航栏组件”就水到渠成了。它包含了两个部分：

* **A. 分类管理后台**：首先，我需要在“商家中心”里，为商家提供一个独立的“**店铺分类管理**”功能。在这里，他们可以像管理文章目录一样，自由地创建、编辑、排序自己的导航项（如：首页、新品、活动、关于我们）。
* **B. 装修页的组件配置**：在店铺装修页面，商家可以将这个“导航栏组件”拖拽到页面上。系统会自动读取商家在A步骤中创建好的分类，并生成导航栏。当C端用户点击某个导航项（如“新品”）时，系统就会自动筛选并展示出所有被商家打上“新品”这个标签的商品。

通过这种“**后台管分类，前台配商品**”的映射设计，我就完美地解决了平台与商家在商品组织方式上的核心矛盾。

除了最复杂的导航栏组件，“其他类”还包括一些简单的布局工具，如**搜索框组件**（提供店内搜索功能）、**辅助空白和分割线组件**（用于调整页面布局和呼吸感），这些组件的配置相对简单，主要是样式和尺寸的调整，这里就不再赘述。

---

### 4.2.3 店铺装修的产品设计

#### 1. 整体产品架构

通过上面的讲解，接下来需要考虑下我们应该在哪些端口提供怎样的产品功能？

在着手设计具体界面前，我首先要从宏观上，规划整个系统的产品架构。

![image-20250726160112838](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726160112838.png)

我的设计，将整个店铺装修系统，拆分为两个相辅相成的部分：**商家端（B端）**和**用户端（C端）**。

* **商家端 (B端) - “装修工厂”**：这是我提供给商家的核心工具。它的定位是一个功能强大的“装修工厂”，商家可以在这里，像室内设计师一样，对自己的店铺进行随心所欲的设计和改造。它必须包含**组件的选择、页面的编辑、内容的填充**等一系列后台功能。
* **用户端 (C端) - “品牌橱窗”**：这是最终呈现给消费者的店铺页面。它的定位是一个精致的“品牌橱窗”，它的唯一任务，就是**忠实地、美观地**，将商家在B端配置好的装修效果，给渲染和展示出来。

#### 2. 商家端（B端）设计：所见即所得的装修后台

对于商家来说，装修后台的体验必须直观、易用，不能有太高的学习门槛。为此，我设计了一个“**所见即所得**”（WYSIWYG）的三栏式布局。

![image-20250726160119503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726160119503.png)

这个后台的核心，由三个区域构成：
1.  **左侧：组件库**：这里是“装修材料市场”，陈列着我们在上一节设计的所有基础组件（商品、图文、营销等）。
2.  **中间：实时预览区**：这里是“装修画布”，以手机模型的样式，实时地、1:1地展示店铺最终的模样。
3.  **右侧：配置面板**：这里是“工具箱”，当商家在中间的画布上选中某一个组件时，这个区域就会浮现，提供针对该组件的所有详细配置项。

![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/df4eeb09bf2108cd642388d650247522.png)

上图就是一个真实的商家端装修后台。我们可以清晰地看到整个交互流程：商家从**左侧**的组件库中，将一个“大图广告”组件，拖拽到**中间**的手机预览区中。当他点击这个广告后，**右侧**就会立刻浮现出针对这个广告的设置选项，例如上传图片、修改样式、设置跳转链接等。整个过程非常流畅、直观。



---
## 4.3 专题页产品设计

在掌握了“店铺装修”这一赋予商家个性的工具后，我们还需要一个能让平台运营人员，针对特定活动或主题，快速搭建聚合页面的强大武器。这，就是“**专题页**”。

### 4.3.1 什么是专题页

![image-20250726161653423](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161653423.png)

我给专题页的定义是：**一个围绕着特定主题，聚合了多种内容元素（如商品、图片、优惠券、文章等）的独立页面。**

它的核心价值，在于解决了大促活动中，信息“**碎片化**”的问题。如果没有专题页，一个大型活动（例如，“双十一手机会场”）的各种信息，会散落在App的各个角落，用户无法形成整体认知。而专题页，就是将所有相关信息，都汇集到一个统一的入口，为用户打造一个“一站式”的沉浸式购物场景。

![image-20250726161714252](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161714252.png)

我们来看京东的这个案例。为了“联合利华超级品牌日”这个活动，他们打造了一个专题页。这个页面里，有活动头图、有代言人、有优惠券、有不同楼层的商品推荐...所有与活动相关的信息，都被“聚合”在了这一个页面里，为用户提供了沉浸式、一站式的购物体验，极大地提升了活动的转化效率。

### 4.3.2 专题页的需求分析

在大型电商平台中，运营活动是常态。每周、甚至每天，都可能有新的促销主题上线。如果每次活动都需要研发人员去从零开发一个新页面，效率会极其低下，运营的需求会被严重阻塞。

因此，我作为产品经理，在设计这个功能时，核心的需求就是：**打造一个能让运营人员“高效率、低成本、可复用”地批量生产活动专题页的后台系统。**

### 4.3.3 专题页的产品设计

要实现“高效率”和“可复用”，我的核心设计思路，是将专题页的生产，拆分为两个阶段：
* **第一阶段**：由更专业的设计师或高级运营人员，负责“**创建模板**”。
* **第二阶段**：由一线的普通运营人员，负责“**使用模板**”来快速生成页面。

![image-20250726161759444](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161759444.png)

上面这张流程图，完整地展示了我这套“两阶段”的设计思想。

#### 1. “工厂”：创建与管理模板

“创建模板”的过程，就和我们之前学习的“店铺装修”非常相似。

![image-20250726161819056](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161819056.png)

高级用户（设计师或高级运营）可以使用我们提供的基础组件（商品、图文、营销等），通过拖拽的方式，自由地“搭建”出一个页面的通用框架或版式。例如，他可以创建一个“双十一主会场模板”，包含顶部轮播图、中腰部秒杀楼层、底部商品列表等固定结构。

![image-20250726161927025](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726161927025.png)

所有搭建好的模板，都会统一存放在“**模板列表**”中，形成一个可供随时调用的模板库。运营主管可以在这里，对模板进行启用、停用等管理，确保一线运营人员只能使用审核过的、规范的模板。

#### 2. “流水线”：使用模板创建专题页

“顺着上面梳理出的流程，先来考虑下B端如何让商家使用模板创建专题页，包含哪些页面及功能？

当模板库搭建好之后，一线运营人员创建专题页的过程，就变成了一个极其简单的“流水线”作业。我为他们设计了一个三步走的创建向导。

* **第一步：选择模板**

![image-20250726162030043](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162030043.png)

当运营人员接到一个新活动需求时（例如，做一个“夏季清仓”专题），他创建专题页的第一步，就是从我们预设好的模板库中，选择一个最符合本次活动调性的模板。

* **第二步：配置信息**

![image-20250726162103204](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162103204.png)

选好模板后，运营就进入了“**填空模式**”。他完全不需要关心页面的布局和样式，只需要根据模板预留好的“坑位”，上传对应的素材（如专题主图），并从商品库中选择要填充的商品即可。

* **第三步：创建完成**

![image-20250726162122774](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162122774.png)

完成信息配置后，点击创建，一个全新的、精美的专题页就瞬间生成了。运营人员可以点击“预览”，也可以直接返回列表。

#### 3. “仓库”：管理已创建的专题页

![image-20250726162134047](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726162134047.png)

所有通过模板创建好的专题页，都会进入这张“**专题列表**”进行统一的仓储和管理。运营可以在这里，精准地控制每一个专题页的**上线/下线状态**（通过设置活动起止时间），并对已上线的活动，进行后续的编辑或查看数据等操作。

通过这套“**模板化、流程化**”的产品设计，我成功地将原本可能需要数天开发周期的专题页搭建工作，变成了运营人员可以在几分钟内就高效完成的常规工作，极大地提升了整个公司的运营效率和活动的迭代速度。

---
## 4.4 频道页产品设计

我们最后来学习一种在大型平台中，承担着“二级门户”角色的重要页面——**频道页**。

### 4.4.1 什么是频道页

![image-20250726163039627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163039627.png)

我给**频道页**的定义是：**一个大型业务分类的、常态化的首页。**

它和我们之前学习的“专题页”有本质的区别：
* **专题页**：通常是**临时的、活动导向的**，生命周期短，例如“双十一手机会场”。
* **频道页**：则是**常态化的、分类导向的**，是App内一个固定的、长期的流量入口，例如“手机频道”、“女装频道”。

只有当一个平台的业务体量足够大，某一个垂直品类（如“美妆护肤”）的运营足够复杂和精细时，才需要为它建立一个专属的“频道页”。

![image-20250726163058003](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163058003.png)

我们来看京东的这个案例。这两个频道页，都不是一个简单的商品列表。它们都聚合了二级分类导航、品牌精选、促销活动、热门榜单等多种内容形态。每一个频道页，都像是一个“美妆护肤”或“运动户外”领域里的“小首页”，承担着对这个垂直品类进行深度运营和流量分发的职责。

### 4.4.2 频道页的需求分析及产品设计

**需求分析**

我为什么需要设计一个独立的频道页系统，而不是直接用“店铺装修”功能来搭建呢？核心需求在于**业务的垂直深度和运营的独立性**。

对于一个大型平台来说，“电脑”、“手机”、“女装”等一级类目，其体量和运营复杂度，堪比一个小型的垂直电商。他们需要一个专属的、高度自定义的“首页”，来承载该品类下所有的内容和活动，从而更好地引导用户、分发流量。

**产品设计：高效率的产品复用**

在明确了需求后，我进行产品设计时，发现频道页的搭建过程，与我们上一节学习的“**专题页**”，在底层逻辑上是**完全一致的**。

![image-20250726163146623](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163146623.png)

因此，我完全可以复用“**模板化、流程化**”这套成熟的设计思路，用一套后台系统，同时支撑起“专题页”和“频道页”这两种核心的内容承载形式。这不仅极大地节约了研发资源，也保证了运营后台体验的一致性。

* **第一步：“工厂” - 创建频道页模板**
    ![image-20250726163202855](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163202855.png)
    同样，由高级运营或设计师，使用我们标准化的组件库，来搭建不同频道（如“手机频道”、“女装频道”）的通用“模板”。

* **第二步：“流水线” - 使用模板创建/更新频道页**
    ![image-20250726163220667](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163220667.png)
    ![image-20250726163229983](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163229983.png)
    ![image-20250726163239268](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163239268.png)
    一线的品类运营人员，只需要像流水线作业一样，选择一个对应自己品类的模板，然后“填空”，就能快速生成或更新自己的频道页内容。

* **第三步：“仓库” - 管理频道页**
    ![image-20250726163248180](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726163248180.png)
    所有创建好的频道页，也会进入一个专属的列表进行统一管理。运营可以在这里控制每一个频道页的发布状态和内容。

通过这种高度的“**产品复用**”，我就能用最低的成本，最高效地满足不同业务线的内容运营需求。

---

## 4.5 本章总结

在本章，我们系统性地学习了电商“内容侧”的产品设计。

* **核心思想**：我们明确了所有内容型产品设计的底层灵魂，就是**内容管理系统（CMS）**。它的本质，是**将“内容”与“展现”分离**，从而赋予非技术人员自主管理内容的能力。

* **三大应用**：我们深入探讨了CMS在电商平台中的三大核心应用：
    * **店铺装修**：赋予**商家**“千店千面”的个性化能力。
    * **专题页**：赋予**平台运营**“高效率、低成本”地搭建活动页的能力。
    * **频道页**：赋予**核心品类**“垂直化、精细化”地运营其“二级门户”的能力。

* **底层哲学**：而支撑起这所有应用场景的底层设计哲学，都是**将“内容”与“展现”分离，通过“组件化”提供装修材料，再通过“模板化”提升生产效率**。

掌握了这套思想，你就能应对任何复杂的内容型产品的设计挑战。至此，我们第四章的学习全部结束。

---
# 第五章：用户运营

在前面的章节中，我们已经为我们的电商平台，搭建了从商品、交易、促销到内容管理的强大“武器库”。我们设计了各种功能，让用户可以顺畅地完成购买。

但武器本身不会自己打仗。从本章开始，我们的视角将发生一次关键的跃迁：从以“**功能**”为中心，转向以“**人**”为中心。我们将深入探讨，如何运营我们最宝贵的资产——用户。而用户运营的第一课，也是最重要的一课，就是“精细化运营”。

## 5.1 精细化运营的目的

### 5.1.1 为什么要进行精细化运营

在我设计任何一个产品时，早期我追求的是“**功能的普适性**”。我提供的优惠券、拼团、秒杀等，是面向所有用户的“通用武器”，目的是在产品初期，快速验证模式、吸引第一批用户。

![image-20250726200150576](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726200150576.png)

但随着产品发展，用户量不断提升，我逐渐发现一个严峻的问题：**我们的增长变慢了，用户的活跃度在逐渐下降。**

当用户规模扩大后，`用户类型`变得极其丰富（有学生、有白领、有宝妈），`用户需求`也变得高度多元化（有人追求性价比、有人追求品质、有人追求新品）。此时，如果我还用“一招鲜，吃遍天”的统一运营方式，结果必然是吃力不讨好。

这就好比用大水漫灌田地，对一部分“口渴”的用户或许有效，但对另一部分“不渴”的用户就是一种资源浪费和体验骚扰，而对那些“需要特定养分”的用户则完全无效。

![image-20250726200108788](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726200108788.png)

一线运营同事的复盘，也从实践层面印证了我的判断。我们发现：
* 大量的优惠券成本投下去，只刺激了部分“**价格敏感用户**”，他们本来就需要强优惠才会下单。
* 对于那些“**忠诚的高价值用户**”，他们本就会购买，这些普适的优惠对他们没有带来任何增量消费，相当于浪费了营销成本。
* 而对于那些“**从不购买的沉默用户**”，这点优惠力度又不足以打动他们，没有起到激活的作用。

结论是：**我们粗放式的运营成本，没有花在刀刃上。**

![image-20250726200453467](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726200453467.png)

正是基于以上种种痛点，我意识到，运营的思路，必须从“**广撒网**”，转变为“**精准点射**”。这，就是精细化运营的由来。

我给**精细化运营**的定义是：**一种基于数据分析，对不同的人群、在不同的场景下，推送不同的内容，并引导他们完成特定目标的、差异化细分的运营策略。** 它的核心，就是四个字：**因材施教**。

### 5.1.2 精细化运营的目标是什么

明确了“为什么”要做，我们再来看精细化运营的“目标”是什么。

![image-20250726200522267](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726200522267.png)

总的来说，所有精细化的手段，最终都服务于一个终极目的——**提升用户的生命周期总价值（LTV）**，从而实现产品的长期健康增长和商业化营收目标。

我将这个总目标，拆解为以下几个可执行的分层目标：

1.  **对全体用户：实现用户规模最大化**
    这不仅仅是指通过A/B测试优化注册按钮。更深层地，是通过精细化运营，去分析不同渠道来源用户的后续留存和消费数据，从而判断出哪些是“高价值渠道”，并将预算向这些渠道倾斜，实现更高质量的用户增长。

2.  **对活跃用户：提升留存与粘性**
    这是为了让用户“留下来，并爱上我们”。通过分析用户的行为偏好，我可以为喜欢数码的用户，推送新品手机的资讯；为美妆爱好者，推送护肤品的使用教程。通过这种个性化的内容和活动，来维持用户的活跃度，并在他们产生需求时，第一个想到我们的产品。

3.  **对商业化：实现精准的营收目标**
    这是精细化运营价值兑现的核心。通过对用户进行分层，我可以实现“**好钢用在刀刃上**”。

![image-20250726201008132](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726201008132.png)

而要达成这些目标，我的核心搭建思路，主要分为两步：

* **第一步：用户分层（识人）**
    我需要基于数据，建立起一套用户分层模型。例如，根据经典的RFM模型，我可以将用户分为“高价值用户”、“潜力用户”、“价格敏感用户”、“沉默流失用户”等不同的群体。

* **第二步：用户触达（施策）**
    在识人的基础上，我就可以进行精准的“因材施教”了：
    * 对“**高价值用户**”，我可以推送新品通知、提供VIP专属客服，维护好他们的忠诚度。
    * 对“**价格敏感用户**”，我可以在大促前，精准地给他们推送大额优惠券，刺激他们下单转化。
    * 对“**沉默流失用户**”，我则可以通过短信、App Push等渠道，用“老友回归大礼包”这样的强激励手段，尝试将他们召回。

通过这一整套“分层-触达”的精细化运营体系，我才能摆脱低效、昂贵的“大水漫灌”模式，走向一种更高效、更个性化、也最终能带来更高回报的用户关系管理模式。



---
## 5.2 用户画像

在上一节，我们明确了精细化运营的核心思路是“因材施教”。但要做到因材施教，我们首先必须搞清楚，我们的用户，到底都是些什么样的“材”。这，就是“用户画像”要解决的问题。

### 5.2.1 什么是用户画像

![image-20250726201733165](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726201733165.png)

在业界，“用户画像”这个词，其实常常包含两种不同的概念：**User Persona（用户角色）** 和 **User Profile（用户资料）**。我必须先为你理清这两者的区别，因为它们服务于完全不同的目的。

* **User Persona (定性画像)**：这是一种**定性的、偏研究**的方法。它通常是通过访谈、调研等方式，创造出来的一个“**虚拟的、典型的**”用户代表。他会有姓名、照片、职业、甚至生活信条和烦恼。它的核心目的，是帮助我们产品和设计团队，在规划功能时，能时刻记住我们是在为“谁”而设计，从而产生同理心，做出更贴合用户真实场景的决策。

* **User Profile (定量画像)**：这是我们本章要深入学习的、服务于“精细化运营”的、**定量的、数据驱动的**画像。它不是一个虚拟的人，而是“**一个用户身上所有标签的集合**”。它描述了一个真实用户的客观事实（如：女，25岁，消费能力高，最近7天活跃），其核心目的，是**让系统和运营人员，可以对用户进行批量的筛选、分类和触达**。

简单来说，**Persona是用来“理解和共情”的，而Profile是用来“筛选和运营”的。**

### 5.2.2 用户画像的搭建思路

![image-20250726201847308](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726201847308.png)

要搭建一套能驱动精细化运营的用户画像体系（User Profile），我作为产品经理，需要设计一个包含四大步骤的完整闭环。这套闭环，清晰地回答了“如何从数据，到最终的运营动作”的全过程。

#### 1. 搭建标签体系

标签，是用户画像的“**原子**”和“**砖块**”，是我们认知用户的最基本单位。

![image-20250726203000275](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726203000275.png)

我通常会将标签，归纳为以下四大类别：

| **标签类别** | **我的解读** | **举例** |
| :--- | :--- | :--- |
| **基本属性** | 用户**固有**的、相对静态的人口学特征。 | 性别、年龄、地域 |
| **社会属性** | 用户在社会关系网络中的特征。 | 职业、收入、教育程度 |
| **行为属性** | 用户在我们产品内的**互动行为**。 | 登录天数、活跃时长、浏览偏好 |
| **消费属性** | 用户的**交易行为**。 | 消费金额、消费频次、客单价 |

![image-20250726203037858](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726203037858.png)

在产品设计上，我需要在后台，为运营人员提供一个“**标签管理系统**”，让他们可以清晰地看到平台目前拥有哪些标签，以及这些标签的定义、更新方式和状态。

#### 2. 进行用户分群

有了成千上万的标签后，我就可以进行第二步：把拥有相似标签的用户，“圈”在一起，形成“**用户分群**”。这是精细化运营能够规模化执行的前提。

![image-20250726203129232](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726203129232.png)

![image-20250726203451759](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726203451759.png)

为此，我需要在后台设计一个强大的“**用户分群工具**”。它允许运营人员，像搭积木一样，通过自定义“规则”（例如：`最近7天登录过` AND `消费金额>1000元` AND `用户等级=钻石会员`），来创建自己想要的任何用户群体。

除了让运营人员“自定义”分群，我还会内置一些业界成熟、通用的分群模型，作为基础的用户洞察工具。最经典的就是“**用户价值模型**”和“**RFM模型**”。

* **用户价值模型**
    ![image-20250726203203209](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726203203209.png)
    这个模型，源于经典的“二八定律”。它将用户分为“高价值用户”、“中坚用户”和“普通用户”三层，帮助我们快速识别出那些贡献了绝大部分利润的20%的核心用户，以便为他们提供更好的服务。

* **RFM模型**
    ![image-20250726203234427](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726203234427.png)
    这是在电商和零售领域，应用最广泛、最有效的一个用户价值分析模型。它通过三个核心指标，来衡量用户的价值：
    
    * **R (Recency)**：最近一次消费时间。离现在越近，价值越高。
    * **F (Frequency)**：消费频率。一段时间内买得越频繁，价值越高。
* **M (Monetary)**：消费金额。一段时间内花钱越多，价值越高。
  
    ![image-20250726203311287](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726203311287.png)
RFM模型，通过对这三个维度进行“高/低”（通常以平均值为分界线）的组合，可以将我们的用户，精准地划分为8个价值完全不同的群体。
    
    ![image-20250726203357468](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726203357468.png)
    例如：
    
    * **重要价值客户 (R高-F高-M高)**：他们是我们的“财神爷”，需要重点维护，提供VIP服务。
    * **重要挽留客户 (R低-F高-M高)**：他们曾经是“财神爷”，但最近不来了。必须立刻采取措施（如专属客服回访、大额优惠券召回）去挽留他们。

#### 3. 制定运营策略 & 实施用户触达

有了清晰的用户分群，精细化运营的最后两步就水到渠成了。

运营人员可以针对“重要挽留客户”，制定“**大额优惠券召回**”的策略；针对“重要价值客户”，制定“**新品优先体验**”的策略。

然后，再通过我们前面设计的优惠券、站内信、Push等“**触达工具**”，将这些策略精准地推送给对应的用户群体，最终形成一个从“识人”到“施策”的完整闭环。





---
## 5.3 积分体系

在精细化运营的工具箱中，如果说“用户画像”是我们用来洞察用户的“作战地图”，那么“**积分体系**”就是我们用来**引导和激励用户长期行为**的、平台自建的“**经济系统**”。它是一项重要的、长期的用户忠诚度计划。

### 5.3.1 积分系统的需求分析

#### 1. 为什么要搭建积分体系？

![image-20250726203858063](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726203858063.png)

在我完成了初步的用户增长，拥有了一定规模的用户后，我的下一个核心目标，就是如何**提升用户的长期留存和生命周期总价值（LTV）**。我需要一套能够持续激励用户与我们互动的体系。在对比了大量竞品后，我决定搭建一套“积分体系”。

它的核心战略价值在于：
1.  **量化用户贡献**：通过积分为用户在平台内的各种“积极行为”（如购买、签到、评价）进行量化和奖励。
2.  **划分用户等级**：以积分为标尺，将用户划分为不同的等级，为后续针对不同等级用户，进行差异化的运营，打下基础。

![image-20250726204102401](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726204102401.png)

在电商场景中，积分最核心的价值，就是建立一个“**消费 -> 奖励 -> 再消费**”的良性循环，从而提升用户的“**长期活跃**”和复购率。

#### 2. 积分体系的设计思路：建立闭环

![image-20250726204126224](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726204126224.png)

要设计一个健康的、可持续的积分体系，我必须遵循一个核心原则：**建立积分的“闭环”**。

这意味着，积分必须有来路（获取），也要有去路（消耗），并且整个过程是安全可控的。如果只进不出，积分就会严重“通货膨胀”，变得毫无价值；如果只出不进，则无法起到激励作用。因此，我的设计思路，将围绕“**积分获取**”、“**积分消耗**”和“**积分风控**”这三大支柱展开。

* **积分获取的需求**
    ![image-20250726204243777](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726204243777.png)
    在“获取”端，我需要同时满足“用户”和“平台运营”的需求：
    * **对于用户**：需要清晰地看到“如何获取积分”的规则，有明确的入口去完成这些任务，并且能随时查看自己的积分获取记录。
    * **对于平台运营**：需要有一个后台，可以灵活地“定义哪些用户行为可以获得积分”（如下单、评价、签到等），并且可以根据积分的不同提供不同的用户等级作为指标

* **积分消耗的需求**
    ![image-20250726204349492](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726204349492.png)
    在“消耗”端，需求与获取端相辅相成：
    * **对于用户**：需要知道“积分能用来做什么”（如抵扣现金、兑换礼品），有明确的场景去使用积分，并能查看自己的积分消耗记录。
    * **对于平台运营**：需要有一个后台，可以灵活地“定义积分的消耗渠道和规则”，例如设置“积分商城”、配置“积分抵现比例”等。

* **积分风控的需求**
    ![image-20250726204423160](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726204423160.png)
    最后，作为一个“经济系统”，积分体系必须有“监管机制”，防止被黑产“薅羊毛”而导致系统崩溃。因此，“风控”是平台侧至关重要的需求。平台运营需要系统能够**监控特殊或异常的积分获取行为**，并能对这些行为进行**手动或自动的处理**（如冻结积分、封禁账号等）。

#### 3. 核心业务流程

![image-20250726204441423](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726204441423.png)

将以上所有的需求点进行串联，我就能提炼出我们积分体系的“**核心业务流程**”。

这张流程图，完整地展示了积分**获取与消耗的闭环**，以及平台在其中扮演的“**规则制定者**”和“**秩序维护者**”的角色。

* 用户根据平台设定的规则，通过各种行为**获取积分**。
* 平台侧则会对用户的行为进行校验，一旦发现违规行为，就会触发**风控机制**。
* 用户可以在消耗场景**使用积分**。
* 整个过程中，用户和平台，都可以清晰地看到每一笔积分的**流入和流出明细**。

这个流程，就是我们下一节进行具体产品设计的“总纲”和指导蓝图。

---
#### 2. 积分体系的产品设计

我将积分体系的产品设计，拆分为**用户端（C端）**和**平台端（B端）**两大部分。C端负责用户的**体验和互动**，B端则负责平台的**管理和调控**。

##### 一、 用户端（C端）产品设计

对于用户来说，积分体系的体验必须是**清晰的、有激励感的、且值得信赖的**。

* **积分入口**
    首先，我需要在用户最容易找到的地方，为他提供一个固定的入口。通常，这个入口会放在“**个人中心**”页面，直观地展示出用户当前的积分总额。
    
* **“我的积分”主页**
    ![image-20250726204745142](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726204745142.png)
    点击入口后，用户会进入“我的积分”主页。这是用户与积分体系互动的核心枢纽。
    * **概览区**：页面的顶部，会展示用户的头像、等级（Lv.10）、以及当前的积分总额，并提供一个“查看明细”的入口。
    * **任务列表（积分获取）**：页面的核心区域，是一个“**任务列表**”。我会把所有可以获取积分的行为，都以任务卡片的形式，清晰地陈列在这里。例如，“`签到`”、“`邀请新用户`”、“`购物下单`”、“`晒单评价`”等。用户每完成一项，对应的任务状态就会变为“已完成”。
    * **每日签到**：上图中间和右侧的截图，详细展示了“每日签到”这个最常见的积分获取任务。我通过一个日历，来直观地记录用户的连续签到行为，并通过“**连续签到X天，可领额外奖励**”的规则，来激励用户保持每日访问的习惯。
    * **积分消耗（讲解）**：虽然截图中未包含“积分消耗”的页面，但在一个完整的体系中，这个主页上通常还必须有“**积分商城**”（用积分兑换商品）或“**积分抵现**”（在下单时用积分抵扣现金）的入口。这是让用户辛苦赚来的积分“**有处可花**”的关键，是构成闭环不可或缺的一环。

* **积分明细**
    ![image-20250726204838848](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726204838848.png)
    为了建立用户对积分体系的信任，我必须提供一个绝对清晰、透明的账单。在“积分明细”页面，用户可以查到自己**每一笔**积分的流入（获取）和流出（消耗）记录，就像查看自己的银行账单一样。页面顶部的`全部`、`获取`、`消耗`三个Tab，可以帮助用户快速地进行筛选。

##### 二、 平台端（B端）产品设计

如果说C端是“公民”体验积分价值的地方，那么B端就是我作为平台方，进行“宏观调控”和“维护秩序”的“**中央银行**”和“**监管机构**”。

* **规则制定：积分规则管理**
    ![image-20250726204855929](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726204855929.png)
    这是整个积分经济系统的“**立法机构**”。在这个后台，运营人员可以：
    1.  **定义规则**：创建所有积分的获取和消耗规则。例如，可以创建一条“`规则名称`：发表话题，`规则类型`：每日N次，`积分`：+20”的规则。
    2.  **管理规则**：对已创建的规则，进行启用、停用、编辑等操作。这赋予了运营极大的灵活性，可以根据平台的运营节奏，来动态地调整积分的发放策略。

* **等级体系：等级管理**
    ![image-20250726205015265](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726205015265.png)
    这是积分体系与用户成长体系相结合的核心。在这个后台，运营人员可以：
    1.  **定义等级**：创建不同的用户等级，如 Lv.1, Lv.2 ...
    2.  **设置门槛**：为每一个等级，设置一个“**所需积分**”的门槛。当用户的累计积分达到这个门槛时，系统就会自动为他升级。
    3.  **配置权益**：为不同等级的用户，配置不同的权益，例如更高倍数的积分获取系数、或者专属的兑换商品等。

* **数据监控：积分明细记录**
    ![image-20250726205029492](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726205029492.png)
    这是平台的“**审计系统**”。它记录了系统内**所有用户**的**每一笔**积分流水。当出现用户申诉或需要排查问题时，运营人员可以在这里，通过用户ID或手机号，快速地查询到该用户的完整积分历史，为客诉处理和数据分析，提供依据。

* **风险控制：积分风控**
    ![image-20250726205040929](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726205040929.png)
    这是积分体系的“**监管和执法机构**”，用于处理异常情况，维护系统公平。
    1.  **用户查询**：运营可以在这里，查询到任何一个用户的当前积分状况。
    2.  **手动调分**：当需要进行人工补偿或处罚时，运营可以通过“**调整积分**”功能，手动为用户增加或扣除积分，并记录原因。
    3.  **账号处理**：当发现有用户存在严重违规的“刷分”行为时，运营可以将其“**加入黑名单**”，禁止该用户再参与任何积分相关的活动，以维护积分系统的秩序。

通过这样一套权责分明、功能完备的C端和B端产品设计，我才能确保我们搭建的“积分经济系统”，既能有效激励用户，又能被平台牢牢掌控，最终实现健康、可持续的长期运营。


---
## 5.4 会员体系
我们已经学习了积分体系，它像是一个普惠制的“游戏币”系统。现在，我们要学习一个更高阶的用户运营玩法——**会员体系**。
如果说积分是“游戏币”，那么会员体系就是“**VIP俱乐部**”。它存在的目的，不是激励所有用户，而是要**筛选和深度服务**我们平台最核心、最有价值的用户群体。

### 5.4.1 什么是会员体系

在开始设计前，我们首先要回答这个核心问题：**我们已经有了积分，为什么还需要会员？**

我的答案是：**积分和会员，解决的是不同层面的问题**。
* **积分**：是一种**普惠制的、可量化的行为奖励**，人人都可以参与，像游戏币。它的目的是**引导用户的日常行为**。
* **会员**：则是一种**身份的象征和权益的集合**，它具有一定的门槛，是一种**筛选机制**，像俱乐部入场券。它的目的是**筛选和锁定高价值用户**。

基于这个定位，我设计会员体系的核心目的，主要有三个：
1.  **用户管理**：通过会员等级，将用户清晰地划分为“核心用户”、“潜力用户”、“普通用户”等不同群体，实现用户分层。
2.  **价值管理**：识别出平台最有价值的用户，并通过专属权益，深度绑定他们，从而提升他们的生命周期总价值（LTV）。
3.  **用户增长**：以极具吸引力的会员权益作为“诱饵”，既能激励潜力用户不断“升级打怪”，也能吸引平台外的新用户加入。

![image-20250726210254504](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726210254504.png)

综上，我给**会员体系**的定义是：**一套通过设置身份门槛和提供差异化的专属权益，筛选出核心用户，并深度绑定、提升其长期价值的运营机制。**

正如我们熟知的视频网站会员，通过付费这个门槛，筛选出愿意为内容付费的用户，并为他们提供“免广告”、“看独家内容”等专属权益。

### 5.4.2 会员体系搭建思路

要搭建一个成功的会员体系，我作为产品经理，必须从顶层设计好三大支柱：**定义会员类型、设计会员权益、以及建立风险控制机制。**

#### 1. 定义会员类型

首先，我要决定我的“俱乐部”，是用什么方式来招募和划分会员的。

![image-20250726210331211](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726210331211.png)

我通常会从这两个维度来思考和组合：

* **维度一：是否付费**
    * `付费会员`：用户需要支付一笔费用，才能获得会员身份和权益。这是最直接的筛选方式，门槛高，用户价值也最高。例如，Amazon Prime, 京东PLUS。
    * `免费会员`：用户无需付费，通过注册即可成为会员，但通常权益较少或需要通过成长来解锁。
* **维度二：成长体系**
    * `等级会员`：会员身份分为多个等级（如Lv.1 - Lv.10），用户需要通过特定行为（如消费、获取积分）来“升级”，不同等级享有不同权益。我们之前设计的“积分体系”，就是为“等级会员”服务的。
    * `无差别会员`：所有会员的身份和权益，都是完全一样的，没有等级之分。

![image-20250726210416260](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250726210416260.png)

在实践中，最成功的会员体系，往往是**混合模式**。以淘宝的88VIP为例，它首先是一个“**付费会员**”，你需要花88元/年购买。但它的购买资格，又与一个“**成长体系**”（淘气值）挂钩，只有淘气值超过1000的用户，才能以88元的优惠价购买。这种“**成长+付费**”的双重门槛，精准地筛选出了平台消费能力强、且活跃度高的最优质用户。

#### 2. 设计会员权益

会员权益，是整个体系的“灵魂”，是用户加入和升级的唯一动力。

我设计的权益，必须能为用户带来**稀缺感、尊贵感和差异化**的体验。并且，这些权益必须是“**阶梯式**”的，即等级越高的会员，享受的权益越好、越独特。

我将电商平台的会员权益，归纳为以下几类：

| **权益类型** | **权益举例** | **我的设计思考** |
| :--- | :--- | :--- |
| **交易类权益** | 会员专享价、每月免运费券、积分加速 | 这是最基础、最直接的权益，能让会员在购物时感受到实实在在的优惠。|
| **服务类权益** | 专属客服通道、极速退款、生日礼包 | 这是提升会员“尊贵感”的关键，让他们感受到被特殊对待的服务。 |
| **身份类权益** | 专属身份标识(V标)、会员日活动 | 这是满足会员“荣誉感”的设计，让他们在平台内拥有与众不同的身份象征。|
| **生态类权益** | 免费阅读、视频会员兑换、线下活动资格 | 这是大型平台的“护城河”，通过打通集团内其他业务，为会员提供跨界的、独特的价值。|

#### 3. 建立风险控制与关怀机制

最后，一个健康的会员体系，还需要有完善的“秩序维护”和“关系维护”机制。

* **风险控制**：我需要为运营人员，设计必要的后台干预工具。例如，`黑白名单处理`功能，可以手动将会员加入黑名单（取消其资格），或加入白名单（破格授予资格）；`人工干预`功能，可以在出现问题时，手动调整用户的等级或权益。
* **会员关怀**：这本质上是一种**基于会员数据的精细化运营**。我需要将用户的会员身份和等级，作为一个重要的“标签”，纳入我们的用户画像体系。运营人员可以基于这个标签，对不同等级的会员，进行差异化的沟通和关怀。例如，在会员即将降级前，发送提醒通知；在会员生日时，自动发放生日礼包等，以此来维系好我们与这些高价值用户的关系。



