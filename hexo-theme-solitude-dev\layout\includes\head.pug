- let title = page.title, subtitle =''
- if (is_home()) title = config.title
- else if (is_archive()) title = _p('page.archives') + (page.year || "")
- else if (is_tag()) title = _p('page.tag')+': '  + page.tag
- else if (is_category()) title = _p('page.category') +': ' + page.category
- else if (page.type === '404') title = _p('page.404')
- else if (page.type === 'tags') title = _p('page.tags')
- else if (page.type === 'categories') title = _p('page.categories')
- else if (is_post() || is_page()) title = page.title

- if (is_home()) subtitle = config.subtitle ? ' - ' + config.subtitle : ''
- else subtitle = title ? ' | ' + config.title : config.title

meta(charset="utf-8")
meta(http-equiv="X-UA-Compatible", content="IE=edge")
meta(name="viewport" content="width=device-width, initial-scale=1")

title= title + subtitle
noscript= _p('head.noscript')
link(rel="icon", href=url_for(theme.site.icon))

// index.css
link(rel="stylesheet", href=url_for(theme.cdn.main_css))

// inject head
include ./inject/head.pug

// global head
include ./head/config.pug

// page-config head
include ./head/page_config
