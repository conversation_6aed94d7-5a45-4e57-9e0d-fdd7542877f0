<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>14.主题魔改：添加“背景切换”弹窗面板 | Prorise的小站</title><meta name="keywords" content="博客搭建教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="14.主题魔改：添加“背景切换”弹窗面板"><meta name="application-name" content="14.主题魔改：添加“背景切换”弹窗面板"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="14.主题魔改：添加“背景切换”弹窗面板"><meta property="og:url" content="https://prorise666.site/posts/43263.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="14.主题魔改：添加“背景切换”弹窗面板前言：功能介绍与重要提示本指南将引导您为博客添加一个弹窗式的背景切换面板。访客可以通过点击右下角的一个新按钮，打开一个窗口，并自由选择预设的图片或颜色来作为网站的背景。  警告： 这是一项涉及修改主题核心文件的“魔改”操作。在开始前，强烈建议您备份整个 the"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"><meta name="description" content="14.主题魔改：添加“背景切换”弹窗面板前言：功能介绍与重要提示本指南将引导您为博客添加一个弹窗式的背景切换面板。访客可以通过点击右下角的一个新按钮，打开一个窗口，并自由选择预设的图片或颜色来作为网站的背景。  警告： 这是一项涉及修改主题核心文件的“魔改”操作。在开始前，强烈建议您备份整个 the"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/43263.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"14.主题魔改：添加“背景切换”弹窗面板",postAI:"true",pageFillDescription:"14.主题魔改：添加背景切换弹窗面板, 前言：功能介绍与重要提示, 第一步：创建自定义样式与脚本, 第二步：添加侧边栏触发按钮, 第三步：在主题配置中注入所需文件主题魔改添加背景切换弹窗面板前言功能介绍与重要提示本指南将引导您为博客添加一个弹窗式的背景切换面板访客可以通过点击右下角的一个新按钮打开一个窗口并自由选择预设的图片或颜色来作为网站的背景警告这是一项涉及修改主题核心文件的魔改操作在开始前强烈建议您备份整个文件夹以便在出现问题时可以随时恢复第一步创建自定义样式与脚本此功能依赖一个文件和一个文件来共同实现创建文件在目录下新建一个文件命名为将下面的代码完整复制进去弹窗样式修复全屏按钮可能导致的弹窗内背景选项的容器背景选项的通用样式手机壁纸尺寸电脑壁纸尺寸纯色渐变色块尺寸移动端适配创建文件在目录下新建一个文件命名为将下面的代码完整复制进去数据持久化函数页面加载时自动读取并应用背景切换背景函数创建弹窗切换背景使用主题主色调重要在这里定义你的背景图片和颜色库恢复默认背景图片手机图片手机图片电脑图片电脑渐变色渐变色纯色纯色红色系红色系粉色系粉色系紫色系紫色系蓝色系蓝色系青色系青色系绿色系绿色系橙色系橙色系灰色系灰色系适应窗口大小切换弹窗显示隐藏如何自定义您的背景库请打开您刚刚创建的文件找到这一部分窗口内的所有内容都由这段字符串定义您可以替换图片将标签中的和里的链接替换为您自己的图片链接增加删除图片直接复制或删除整个标签修改颜色修改纯色和渐变色部分标签的和事件中的颜色值第二步添加侧边栏触发按钮打开模板文件修改内容我们需要在导航栏上方的按钮组中添加一个新的按钮来调用函数第一处修改在语句下方添加一个新的按钮切换背景第三步在主题配置中注入所需文件打开主题配置文件找到配置项我们需要在这里引入三样东西库本身以及我们自定义的和文件",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-19 19:56:32",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#14-%E4%B8%BB%E9%A2%98%E9%AD%94%E6%94%B9%EF%BC%9A%E6%B7%BB%E5%8A%A0%E2%80%9C%E8%83%8C%E6%99%AF%E5%88%87%E6%8D%A2%E2%80%9D%E5%BC%B9%E7%AA%97%E9%9D%A2%E6%9D%BF"><span class="toc-text">14.主题魔改：添加“背景切换”弹窗面板</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D%E4%B8%8E%E9%87%8D%E8%A6%81%E6%8F%90%E7%A4%BA"><span class="toc-text">前言：功能介绍与重要提示</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA%E8%87%AA%E5%AE%9A%E4%B9%89%E6%A0%B7%E5%BC%8F%E4%B8%8E%E8%84%9A%E6%9C%AC"><span class="toc-text">第一步：创建自定义样式与脚本</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E6%B7%BB%E5%8A%A0%E4%BE%A7%E8%BE%B9%E6%A0%8F%E8%A7%A6%E5%8F%91%E6%8C%89%E9%92%AE"><span class="toc-text">第二步：添加侧边栏触发按钮</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%9C%A8%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E4%B8%AD%E6%B3%A8%E5%85%A5%E6%89%80%E9%9C%80%E6%96%87%E4%BB%B6"><span class="toc-text">第三步：在主题配置中注入所需文件</span></a></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/" itemprop="url">魔改</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>博客搭建教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">14.主题魔改：添加“背景切换”弹窗面板</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-10T22:13:45.000Z" title="发表于 2025-07-11 06:13:45">2025-07-11</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-19T11:56:32.924Z" title="更新于 2025-07-19 19:56:32">2025-07-19</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.3k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>17分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="14.主题魔改：添加“背景切换”弹窗面板"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/43263.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/43263.html"><header><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/" itemprop="url">魔改</a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">博客搭建教程</a><h1 id="CrawlerTitle" itemprop="name headline">14.主题魔改：添加“背景切换”弹窗面板</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-10T22:13:45.000Z" title="发表于 2025-07-11 06:13:45">2025-07-11</time><time itemprop="dateCreated datePublished" datetime="2025-07-19T11:56:32.924Z" title="更新于 2025-07-19 19:56:32">2025-07-19</time></header><div id="postchat_postcontent"><h3 id="14-主题魔改：添加“背景切换”弹窗面板"><a href="#14-主题魔改：添加“背景切换”弹窗面板" class="headerlink" title="14.主题魔改：添加“背景切换”弹窗面板"></a><strong>14.主题魔改：添加“背景切换”弹窗面板</strong></h3><h6 id="前言：功能介绍与重要提示"><a href="#前言：功能介绍与重要提示" class="headerlink" title="前言：功能介绍与重要提示"></a><strong>前言：功能介绍与重要提示</strong></h6><p>本指南将引导您为博客添加一个弹窗式的背景切换面板。访客可以通过点击右下角的一个新按钮，打开一个窗口，并自由选择预设的图片或颜色来作为网站的背景。</p><blockquote><p><strong>警告：</strong> 这是一项涉及修改主题核心文件的“魔改”操作。在开始前，<strong>强烈建议您备份整个 <code>themes/anzhiyu</code> 文件夹</strong>，以便在出现问题时可以随时恢复。</p></blockquote><hr><h6 id="第一步：创建自定义样式与脚本"><a href="#第一步：创建自定义样式与脚本" class="headerlink" title="第一步：创建自定义样式与脚本"></a><strong>第一步：创建自定义样式与脚本</strong></h6><p>此功能依赖一个CSS文件和一个JS文件来共同实现。</p><ol><li><p><strong>创建CSS文件</strong></p><ul><li>在 <code>themes/anzhiyu/source/custom/css/</code> 目录下，新建一个文件，命名为 <code>background-box.css</code>。</li><li><strong>将下面的CSS代码完整复制进去</strong>：</li></ul><figure class="highlight css"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/* 弹窗样式 */</span></span><br><span class="line"><span class="selector-class">.winbox</span> {</span><br><span class="line">    <span class="attribute">border-radius</span>: <span class="number">12px</span>;</span><br><span class="line">    <span class="attribute">overflow</span>: hidden;</span><br><span class="line">}</span><br><span class="line"><span class="comment">/* 修复全屏按钮可能导致的bug */</span></span><br><span class="line"><span class="selector-class">.wb-full</span> {</span><br><span class="line">    <span class="attribute">display</span>: none;</span><br><span class="line">}</span><br><span class="line"><span class="selector-class">.wb-min</span> {</span><br><span class="line">    <span class="attribute">background-position</span>: center;</span><br><span class="line">}</span><br><span class="line"><span class="selector-attr">[data-theme=<span class="string">'dark'</span>]</span> <span class="selector-class">.wb-body</span>,</span><br><span class="line"><span class="selector-attr">[data-theme=<span class="string">'dark'</span>]</span> <span class="selector-id">#changeBgBox</span> {</span><br><span class="line">    <span class="attribute">background</span>: <span class="number">#333</span> <span class="meta">!important</span>;</span><br><span class="line">}</span><br><span class="line"><span class="comment">/* 弹窗内背景选项的容器 */</span></span><br><span class="line"><span class="selector-class">.bgbox</span> {</span><br><span class="line">    <span class="attribute">display</span>: flex;</span><br><span class="line">    <span class="attribute">flex-wrap</span>: wrap;</span><br><span class="line">    <span class="attribute">justify-content</span>: space-between;</span><br><span class="line">}</span><br><span class="line"><span class="comment">/* 背景选项的通用样式 */</span></span><br><span class="line"><span class="selector-class">.pimgbox</span>,</span><br><span class="line"><span class="selector-class">.imgbox</span>,</span><br><span class="line"><span class="selector-class">.box</span> {</span><br><span class="line">    <span class="attribute">width</span>: <span class="number">166px</span>;</span><br><span class="line">    <span class="attribute">margin</span>: <span class="number">10px</span>;</span><br><span class="line">    <span class="attribute">background-size</span>: cover;</span><br><span class="line">    <span class="attribute">cursor</span>: pointer;</span><br><span class="line">}</span><br><span class="line"><span class="selector-class">.pimgbox</span>,</span><br><span class="line"><span class="selector-class">.imgbox</span> {</span><br><span class="line">    <span class="attribute">border-radius</span>: <span class="number">10px</span>;</span><br><span class="line">    <span class="attribute">overflow</span>: hidden;</span><br><span class="line">}</span><br><span class="line"><span class="selector-class">.pimgbox</span> { <span class="attribute">height</span>: <span class="number">240px</span>; } <span class="comment">/* 手机壁纸尺寸 */</span></span><br><span class="line"><span class="selector-class">.imgbox</span> { <span class="attribute">height</span>: <span class="number">95px</span>; }  <span class="comment">/* 电脑壁纸尺寸 */</span></span><br><span class="line"><span class="selector-class">.box</span> { <span class="attribute">height</span>: <span class="number">100px</span>; } <span class="comment">/* 纯色/渐变色块尺寸 */</span></span><br><span class="line"></span><br><span class="line"><span class="comment">/* 移动端适配 */</span></span><br><span class="line"><span class="keyword">@media</span> screen <span class="keyword">and</span> (<span class="attribute">max-width</span>: <span class="number">768px</span>) {</span><br><span class="line">    <span class="selector-class">.pimgbox</span>,</span><br><span class="line">    <span class="selector-class">.imgbox</span>,</span><br><span class="line">    <span class="selector-class">.box</span> {</span><br><span class="line">        <span class="attribute">height</span>: <span class="number">73px</span>;</span><br><span class="line">        <span class="attribute">width</span>: <span class="number">135px</span>;</span><br><span class="line">    }</span><br><span class="line">    <span class="selector-class">.pimgbox</span> {</span><br><span class="line">        <span class="attribute">height</span>: <span class="number">205px</span>;</span><br><span class="line">    }</span><br><span class="line">    <span class="selector-class">.wb-min</span> {</span><br><span class="line">        <span class="attribute">display</span>: none;</span><br><span class="line">    }</span><br><span class="line">    <span class="selector-id">#changeBgBox</span> <span class="selector-class">.wb-body</span>::-webkit-scrollbar {</span><br><span class="line">        <span class="attribute">display</span>: none;</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>创建JS文件</strong></p><ul><li>在 <code>themes/anzhiyu/source/custom/js/</code> 目录下，新建一个文件，命名为 <code>background-box.js</code>。</li><li><strong>将下面的JavaScript代码完整复制进去</strong>：</li></ul><figure class="highlight javascript"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 数据持久化函数</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">saveData</span>(<span class="params">name, data</span>) {</span><br><span class="line">    <span class="variable language_">localStorage</span>.<span class="title function_">setItem</span>(name, <span class="title class_">JSON</span>.<span class="title function_">stringify</span>({ <span class="string">'time'</span>: <span class="title class_">Date</span>.<span class="title function_">now</span>(), <span class="string">'data'</span>: data }))</span><br><span class="line">}</span><br><span class="line"><span class="keyword">function</span> <span class="title function_">loadData</span>(<span class="params">name, time</span>) {</span><br><span class="line">    <span class="keyword">let</span> d = <span class="title class_">JSON</span>.<span class="title function_">parse</span>(<span class="variable language_">localStorage</span>.<span class="title function_">getItem</span>(name));</span><br><span class="line">    <span class="keyword">if</span> (d) {</span><br><span class="line">        <span class="keyword">let</span> t = <span class="title class_">Date</span>.<span class="title function_">now</span>() - d.<span class="property">time</span></span><br><span class="line">        <span class="keyword">if</span> (t &lt; (time * <span class="number">60</span> * <span class="number">1000</span>) &amp;&amp; t &gt; -<span class="number">1</span>) <span class="keyword">return</span> d.<span class="property">data</span>;</span><br><span class="line">    }</span><br><span class="line">    <span class="keyword">return</span> <span class="number">0</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 页面加载时自动读取并应用背景</span></span><br><span class="line"><span class="keyword">try</span> {</span><br><span class="line">    <span class="keyword">let</span> data = <span class="title function_">loadData</span>(<span class="string">'blogbg'</span>, <span class="number">1440</span>)</span><br><span class="line">    <span class="keyword">if</span> (data) <span class="title function_">changeBg</span>(data, <span class="number">1</span>)</span><br><span class="line">    <span class="keyword">else</span> <span class="variable language_">localStorage</span>.<span class="title function_">removeItem</span>(<span class="string">'blogbg'</span>);</span><br><span class="line">} <span class="keyword">catch</span> (error) { <span class="variable language_">localStorage</span>.<span class="title function_">removeItem</span>(<span class="string">'blogbg'</span>); }</span><br><span class="line"></span><br><span class="line"><span class="comment">// 切换背景函数</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">changeBg</span>(<span class="params">s, flag</span>) {</span><br><span class="line">    <span class="keyword">let</span> bg = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'web_bg'</span>)</span><br><span class="line">    <span class="keyword">if</span> (s.<span class="title function_">charAt</span>(<span class="number">0</span>) == <span class="string">'#'</span>) {</span><br><span class="line">        bg.<span class="property">style</span>.<span class="property">backgroundColor</span> = s</span><br><span class="line">        bg.<span class="property">style</span>.<span class="property">backgroundImage</span> = <span class="string">'none'</span></span><br><span class="line">    } <span class="keyword">else</span> {</span><br><span class="line">        bg.<span class="property">style</span>.<span class="property">backgroundImage</span> = s</span><br><span class="line">    }</span><br><span class="line">    <span class="keyword">if</span> (!flag) { <span class="title function_">saveData</span>(<span class="string">'blogbg'</span>, s) }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">var</span> winbox = <span class="string">''</span></span><br><span class="line"><span class="comment">// 创建弹窗</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">createWinbox</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="keyword">let</span> div = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">'div'</span>)</span><br><span class="line">    <span class="variable language_">document</span>.<span class="property">body</span>.<span class="title function_">appendChild</span>(div)</span><br><span class="line">    winbox = <span class="title class_">WinBox</span>({</span><br><span class="line">        <span class="attr">id</span>: <span class="string">'changeBgBox'</span>,</span><br><span class="line">        <span class="attr">index</span>: <span class="number">999</span>,</span><br><span class="line">        <span class="attr">title</span>: <span class="string">"切换背景"</span>,</span><br><span class="line">        <span class="attr">x</span>: <span class="string">"center"</span>,</span><br><span class="line">        <span class="attr">y</span>: <span class="string">"center"</span>,</span><br><span class="line">        <span class="attr">minwidth</span>: <span class="string">'300px'</span>,</span><br><span class="line">        <span class="attr">height</span>: <span class="string">"60%"</span>,</span><br><span class="line">        <span class="attr">background</span>: <span class="string">'var(--anzhiyu-main)'</span>, <span class="comment">// 使用主题主色调</span></span><br><span class="line">        <span class="attr">onmaximize</span>: <span class="function">() =&gt;</span> { div.<span class="property">innerHTML</span> = <span class="string">`&lt;style&gt;body::-webkit-scrollbar {display: none;}div#changeBgBox {width: 100% !important;}&lt;/style&gt;`</span> },</span><br><span class="line">        <span class="attr">onrestore</span>: <span class="function">() =&gt;</span> { div.<span class="property">innerHTML</span> = <span class="string">''</span> }</span><br><span class="line">    });</span><br><span class="line">    <span class="title function_">winResize</span>();</span><br><span class="line">    <span class="variable language_">window</span>.<span class="title function_">addEventListener</span>(<span class="string">'resize'</span>, winResize)</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 【重要】在这里定义你的背景图片和颜色库</span></span><br><span class="line">    winbox.<span class="property">body</span>.<span class="property">innerHTML</span> = <span class="string">`</span></span><br><span class="line"><span class="string">    &lt;div id="article-container" style="padding:10px;"&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;p&gt;&lt;button onclick="localStorage.removeItem('blogbg');location.reload();" style="background:#ff7242;display:block;width:100%;padding: 15px 0;border-radius:6px;color:white;"&gt;&lt;i class="fas fa-arrows-rotate"&gt;&lt;/i&gt; 恢复默认背景&lt;/button&gt;&lt;/p&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h2 id="图片（手机）"&gt;图片（手机）&lt;/h2&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://img.vm.laomishuo.com/image/2021/12/2021122715170589.jpeg)" class="pimgbox" onclick="changeBg('url(https://img.vm.laomishuo.com/image/2021/12/2021122715170589.jpeg)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h2 id="图片（电脑）"&gt;图片（电脑）&lt;/h2&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://cn.bing.com/th?id=OHR.GBRTurtle_ZH-CN6069093254_1920x1080.jpg)" class="imgbox" onclick="changeBg('url(https://cn.bing.com/th?id=OHR.GBRTurtle_ZH-CN6069093254_1920x1080.jpg)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5badc9c03.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5badc9c03.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5ba422dc2.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5ba422dc2.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c0c20cf4.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5c0c20cf4.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c12c172a.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5c12c172a.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c1828ab2.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5c1828ab2.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c205288d.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5c205288d.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c282ebac.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5c282ebac.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c286636f.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5c286636f.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c2f88638.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5c2f88638.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c3063f58.png)" class="imgbox" onclick="changeBg('url(https://bu.dusays.com/2025/06/15/684e5c3063f58.png)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h2 id="渐变色"&gt;渐变色&lt;/h2&gt;</span></span><br><span class="line"><span class="string">&lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: linear-gradient(to right, #eecda3, #ef629f)" onclick="changeBg('linear-gradient(to right, #eecda3, #ef629f)')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h2 id="纯色"&gt;纯色&lt;/h2&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #7D9D9C" onclick="changeBg('#7D9D9C')"&gt;&lt;/a&gt; </span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h3 id="红色系"&gt;红色系&lt;/h3&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EF9A9A" onclick="changeBg('#EF9A9A')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">  &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EF5350" onclick="changeBg('#EF5350')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #E53935" onclick="changeBg('#E53935')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #C62828" onclick="changeBg('#C62828')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h3 id="粉色系"&gt;粉色系&lt;/h3&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #F48FB1" onclick="changeBg('#F48FB1')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EC407A" onclick="changeBg('#EC407A')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #D81B60" onclick="changeBg('#D81B60')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #AD1457" onclick="changeBg('#AD1457')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h3 id="紫色系"&gt;紫色系&lt;/h3&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #CE93D8" onclick="changeBg('#CE93D8')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #AB47BC" onclick="changeBg('#AB47BC')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #8E24AA" onclick="changeBg('#8E24AA')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #6A1B9A" onclick="changeBg('#6A1B9A')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h3 id="蓝色系"&gt;蓝色系&lt;/h3&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #90CAF9" onclick="changeBg('#90CAF9')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #42A5F5" onclick="changeBg('#42A5F5')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #1E88E5" onclick="changeBg('#1E88E5')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #1565C0" onclick="changeBg('#1565C0')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h3 id="青色系"&gt;青色系&lt;/h3&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #80DEEA" onclick="changeBg('#80DEEA')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #26C6DA" onclick="changeBg('#26C6DA')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #00ACC1" onclick="changeBg('#00ACC1')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #00838F" onclick="changeBg('#00838F')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h3 id="绿色系"&gt;绿色系&lt;/h3&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #A5D6A7" onclick="changeBg('#A5D6A7')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #66BB6A" onclick="changeBg('#66BB6A')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #43A047" onclick="changeBg('#43A047')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #2E7D32" onclick="changeBg('#2E7D32')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h3 id="橙色系"&gt;橙色系&lt;/h3&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #FFCC80" onclick="changeBg('#FFCC80')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #FFA726" onclick="changeBg('#FFA726')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #FB8C00" onclick="changeBg('#FB8C00')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EF6C00" onclick="changeBg('#EF6C00')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    &lt;h3 id="灰色系"&gt;灰色系&lt;/h3&gt;</span></span><br><span class="line"><span class="string">    &lt;div class="bgbox"&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EEEEEE" onclick="changeBg('#EEEEEE')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #BDBDBD" onclick="changeBg('#BDBDBD')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #757575" onclick="changeBg('#757575')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">      &lt;a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #424242" onclick="changeBg('#424242')"&gt;&lt;/a&gt;</span></span><br><span class="line"><span class="string">    &lt;/div&gt;</span></span><br><span class="line"><span class="string">`</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 适应窗口大小</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">winResize</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="keyword">let</span> box = <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#changeBgBox'</span>)</span><br><span class="line">    <span class="keyword">if</span> (!box || box.<span class="property">classList</span>.<span class="title function_">contains</span>(<span class="string">'min'</span>) || box.<span class="property">classList</span>.<span class="title function_">contains</span>(<span class="string">'max'</span>)) <span class="keyword">return</span></span><br><span class="line">    <span class="keyword">var</span> offsetWid = <span class="variable language_">document</span>.<span class="property">documentElement</span>.<span class="property">clientWidth</span>;</span><br><span class="line">    <span class="keyword">if</span> (offsetWid &lt;= <span class="number">768</span>) {</span><br><span class="line">        winbox.<span class="title function_">resize</span>(offsetWid * <span class="number">0.95</span> + <span class="string">"px"</span>, <span class="string">"90%"</span>).<span class="title function_">move</span>(<span class="string">"center"</span>, <span class="string">"center"</span>);</span><br><span class="line">    } <span class="keyword">else</span> {</span><br><span class="line">        winbox.<span class="title function_">resize</span>(offsetWid * <span class="number">0.6</span> + <span class="string">"px"</span>, <span class="string">"70%"</span>).<span class="title function_">move</span>(<span class="string">"center"</span>, <span class="string">"center"</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 切换弹窗显示/隐藏</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">toggleWinbox</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#changeBgBox'</span>)) winbox.<span class="title function_">toggleClass</span>(<span class="string">'hide'</span>);</span><br><span class="line">    <span class="keyword">else</span> <span class="title function_">createWinbox</span>();</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>如何自定义您的背景库？</strong></p><ul><li>请打开您刚刚创建的 <code>background-box.js</code> 文件，找到 <code>winbox.body.innerHTML = \</code>…`;` 这一部分。</li><li>窗口内的所有内容都由这段HTML字符串定义。您可以：<ul><li><strong>替换图片</strong>：将 <code>&lt;a&gt;</code> 标签中的 <code>background-image:url(...)</code> 和 <code>onclick="changeBg('url(...)')"</code> 里的链接替换为您自己的图片链接。</li><li><strong>增加/删除图片</strong>：直接复制或删除整个 <code>&lt;a&gt;</code> 标签。</li><li><strong>修改颜色</strong>：修改“纯色”和“渐变色”部分 <code>&lt;a&gt;</code> 标签的 <code>style</code> 和 <code>onclick</code> 事件中的颜色值。</li></ul></li></ul></li></ol><hr><h6 id="第二步：添加侧边栏触发按钮"><a href="#第二步：添加侧边栏触发按钮" class="headerlink" title="第二步：添加侧边栏触发按钮"></a><strong>第二步：添加侧边栏触发按钮</strong></h6><ol><li><p><strong>打开模板文件</strong>：<code>themes/anzhiyu/layout/includes/header/nav.pug</code></p></li><li><p><strong>修改内容</strong>：我们需要在导航栏上方的按钮组中，添加一个新的按钮来调用 <code>toggleWinbox()</code> 函数。</p><ul><li><p><strong>第一处修改</strong>：在 <code>i.anzhiyufont.anzhiyu-icon-dice</code> 语句下方添加一个新的按钮。</p><figure class="highlight diff"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line">.nav-button#background-change-button</span><br><span class="line">  a.site-page(onclick='toggleWinbox()', title='切换背景', href='javascript:void(0);')</span><br><span class="line">    i.fas.fa-palette</span><br></pre></td></tr></tbody></table></figure></li></ul></li></ol><hr><h6 id="第三步：在主题配置中注入所需文件"><a href="#第三步：在主题配置中注入所需文件" class="headerlink" title="第三步：在主题配置中注入所需文件"></a><strong>第三步：在主题配置中注入所需文件</strong></h6><ol><li><strong>打开主题配置文件</strong> (<code>themes/anzhiyu/_config.yml</code>)。</li><li><strong>找到 <code>inject:</code> 配置项</strong>。我们需要在这里引入三样东西：<code>WinBox</code>库本身，以及我们自定义的CSS和JS文件。</li></ol></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/43263.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/43263.html&quot;)">14.主题魔改：添加“背景切换”弹窗面板</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/43263.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=14.主题魔改：添加“背景切换”弹窗面板&amp;url=https://prorise666.site/posts/43263.html&amp;pic=https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>框架技术<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Hexo<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>魔改<span class="categoryesPageCount">23</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>博客搭建教程<span class="tagsPageCount">31</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/20246.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）</div></div></a></div><div class="next-post pull-right"><a href="/posts/34091.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">15.主题魔改：自定义全站字体</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/57565.html" title="12.Twikoo 美化：自定义评论回复邮件模板"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">12.Twikoo 美化：自定义评论回复邮件模板</div></div></a></div><div><a href="/posts/24286.html" title="10.内容扩展：添加“安全跳转”中间页"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">10.内容扩展：添加“安全跳转”中间页</div></div></a></div><div><a href="/posts/65188.html" title="11.Twikoo 美化：添加自定义表情包"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">11.Twikoo 美化：添加自定义表情包</div></div></a></div><div><a href="/posts/20246.html" title="13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）</div></div></a></div><div><a href="/posts/34091.html" title="15.主题魔改：自定义全站字体"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">15.主题魔改：自定义全站字体</div></div></a></div><div><a href="/posts/11486.html" title="17.内容扩展：添加“前端代码实时预览”沙箱"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">17.内容扩展：添加“前端代码实时预览”沙箱</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"14.主题魔改：添加“背景切换”弹窗面板",date:"2025-07-11 06:13:45",updated:"2025-07-19 19:56:32",tags:["博客搭建教程"],categories:["框架技术","Hexo","魔改"],content:'\n### **14.主题魔改：添加“背景切换”弹窗面板**\n\n###### **前言：功能介绍与重要提示**\n\n本指南将引导您为博客添加一个弹窗式的背景切换面板。访客可以通过点击右下角的一个新按钮，打开一个窗口，并自由选择预设的图片或颜色来作为网站的背景。\n\n> **警告：** 这是一项涉及修改主题核心文件的“魔改”操作。在开始前，**强烈建议您备份整个 `themes/anzhiyu` 文件夹**，以便在出现问题时可以随时恢复。\n\n---\n###### **第一步：创建自定义样式与脚本**\n\n此功能依赖一个CSS文件和一个JS文件来共同实现。\n\n1.  **创建CSS文件**\n    * 在 `themes/anzhiyu/source/custom/css/` 目录下，新建一个文件，命名为 `background-box.css`。\n    * **将下面的CSS代码完整复制进去**：\n    ```css\n    /* 弹窗样式 */\n    .winbox {\n        border-radius: 12px;\n        overflow: hidden;\n    }\n    /* 修复全屏按钮可能导致的bug */\n    .wb-full {\n        display: none;\n    }\n    .wb-min {\n        background-position: center;\n    }\n    [data-theme=\'dark\'] .wb-body,\n    [data-theme=\'dark\'] #changeBgBox {\n        background: #333 !important;\n    }\n    /* 弹窗内背景选项的容器 */\n    .bgbox {\n        display: flex;\n        flex-wrap: wrap;\n        justify-content: space-between;\n    }\n    /* 背景选项的通用样式 */\n    .pimgbox,\n    .imgbox,\n    .box {\n        width: 166px;\n        margin: 10px;\n        background-size: cover;\n        cursor: pointer;\n    }\n    .pimgbox,\n    .imgbox {\n        border-radius: 10px;\n        overflow: hidden;\n    }\n    .pimgbox { height: 240px; } /* 手机壁纸尺寸 */\n    .imgbox { height: 95px; }  /* 电脑壁纸尺寸 */\n    .box { height: 100px; } /* 纯色/渐变色块尺寸 */\n\n    /* 移动端适配 */\n    @media screen and (max-width: 768px) {\n        .pimgbox,\n        .imgbox,\n        .box {\n            height: 73px;\n            width: 135px;\n        }\n        .pimgbox {\n            height: 205px;\n        }\n        .wb-min {\n            display: none;\n        }\n        #changeBgBox .wb-body::-webkit-scrollbar {\n            display: none;\n        }\n    }\n    ```\n\n2.  **创建JS文件**\n    * 在 `themes/anzhiyu/source/custom/js/` 目录下，新建一个文件，命名为 `background-box.js`。\n    * **将下面的JavaScript代码完整复制进去**：\n    ```javascript\n    // 数据持久化函数\n    function saveData(name, data) {\n        localStorage.setItem(name, JSON.stringify({ \'time\': Date.now(), \'data\': data }))\n    }\n    function loadData(name, time) {\n        let d = JSON.parse(localStorage.getItem(name));\n        if (d) {\n            let t = Date.now() - d.time\n            if (t < (time * 60 * 1000) && t > -1) return d.data;\n        }\n        return 0;\n    }\n\n    // 页面加载时自动读取并应用背景\n    try {\n        let data = loadData(\'blogbg\', 1440)\n        if (data) changeBg(data, 1)\n        else localStorage.removeItem(\'blogbg\');\n    } catch (error) { localStorage.removeItem(\'blogbg\'); }\n\n    // 切换背景函数\n    function changeBg(s, flag) {\n        let bg = document.getElementById(\'web_bg\')\n        if (s.charAt(0) == \'#\') {\n            bg.style.backgroundColor = s\n            bg.style.backgroundImage = \'none\'\n        } else {\n            bg.style.backgroundImage = s\n        }\n        if (!flag) { saveData(\'blogbg\', s) }\n    }\n\n    var winbox = \'\'\n    // 创建弹窗\n    function createWinbox() {\n        let div = document.createElement(\'div\')\n        document.body.appendChild(div)\n        winbox = WinBox({\n            id: \'changeBgBox\',\n            index: 999,\n            title: "切换背景",\n            x: "center",\n            y: "center",\n            minwidth: \'300px\',\n            height: "60%",\n            background: \'var(--anzhiyu-main)\', // 使用主题主色调\n            onmaximize: () => { div.innerHTML = `<style>body::-webkit-scrollbar {display: none;}div#changeBgBox {width: 100% !important;}</style>` },\n            onrestore: () => { div.innerHTML = \'\' }\n        });\n        winResize();\n        window.addEventListener(\'resize\', winResize)\n\n        // 【重要】在这里定义你的背景图片和颜色库\n        winbox.body.innerHTML = `\n        <div id="article-container" style="padding:10px;">\n        \n        <p><button onclick="localStorage.removeItem(\'blogbg\');location.reload();" style="background:#ff7242;display:block;width:100%;padding: 15px 0;border-radius:6px;color:white;"><i class="fas fa-arrows-rotate"></i> 恢复默认背景</button></p>\n        \n        <h2 id="图片（手机）">图片（手机）</h2>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://img.vm.laomishuo.com/image/2021/12/2021122715170589.jpeg)" class="pimgbox" onclick="changeBg(\'url(https://img.vm.laomishuo.com/image/2021/12/2021122715170589.jpeg)\')"></a>\n        </div>\n        \n        <h2 id="图片（电脑）">图片（电脑）</h2>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://cn.bing.com/th?id=OHR.GBRTurtle_ZH-CN6069093254_1920x1080.jpg)" class="imgbox" onclick="changeBg(\'url(https://cn.bing.com/th?id=OHR.GBRTurtle_ZH-CN6069093254_1920x1080.jpg)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5badc9c03.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5badc9c03.png)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5ba422dc2.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5ba422dc2.png)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c0c20cf4.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5c0c20cf4.png)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c12c172a.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5c12c172a.png)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c1828ab2.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5c1828ab2.png)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c205288d.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5c205288d.png)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c282ebac.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5c282ebac.png)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c286636f.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5c286636f.png)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c2f88638.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5c2f88638.png)\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" style="background-image:url(https://bu.dusays.com/2025/06/15/684e5c3063f58.png)" class="imgbox" onclick="changeBg(\'url(https://bu.dusays.com/2025/06/15/684e5c3063f58.png)\')"></a>\n        </div>\n        \n        <h2 id="渐变色">渐变色</h2>\n    <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: linear-gradient(to right, #eecda3, #ef629f)" onclick="changeBg(\'linear-gradient(to right, #eecda3, #ef629f)\')"></a>\n        </div>\n        \n        <h2 id="纯色">纯色</h2>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #7D9D9C" onclick="changeBg(\'#7D9D9C\')"></a> \n        </div>\n        \n        <h3 id="红色系">红色系</h3>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EF9A9A" onclick="changeBg(\'#EF9A9A\')"></a>\n      <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EF5350" onclick="changeBg(\'#EF5350\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #E53935" onclick="changeBg(\'#E53935\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #C62828" onclick="changeBg(\'#C62828\')"></a>\n        </div>\n        \n        <h3 id="粉色系">粉色系</h3>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #F48FB1" onclick="changeBg(\'#F48FB1\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EC407A" onclick="changeBg(\'#EC407A\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #D81B60" onclick="changeBg(\'#D81B60\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #AD1457" onclick="changeBg(\'#AD1457\')"></a>\n        </div>\n        \n        <h3 id="紫色系">紫色系</h3>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #CE93D8" onclick="changeBg(\'#CE93D8\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #AB47BC" onclick="changeBg(\'#AB47BC\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #8E24AA" onclick="changeBg(\'#8E24AA\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #6A1B9A" onclick="changeBg(\'#6A1B9A\')"></a>\n        </div>\n        \n        <h3 id="蓝色系">蓝色系</h3>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #90CAF9" onclick="changeBg(\'#90CAF9\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #42A5F5" onclick="changeBg(\'#42A5F5\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #1E88E5" onclick="changeBg(\'#1E88E5\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #1565C0" onclick="changeBg(\'#1565C0\')"></a>\n        </div>\n        \n        <h3 id="青色系">青色系</h3>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #80DEEA" onclick="changeBg(\'#80DEEA\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #26C6DA" onclick="changeBg(\'#26C6DA\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #00ACC1" onclick="changeBg(\'#00ACC1\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #00838F" onclick="changeBg(\'#00838F\')"></a>\n        </div>\n        \n        <h3 id="绿色系">绿色系</h3>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #A5D6A7" onclick="changeBg(\'#A5D6A7\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #66BB6A" onclick="changeBg(\'#66BB6A\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #43A047" onclick="changeBg(\'#43A047\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #2E7D32" onclick="changeBg(\'#2E7D32\')"></a>\n        </div>\n        \n        <h3 id="橙色系">橙色系</h3>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #FFCC80" onclick="changeBg(\'#FFCC80\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #FFA726" onclick="changeBg(\'#FFA726\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #FB8C00" onclick="changeBg(\'#FB8C00\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EF6C00" onclick="changeBg(\'#EF6C00\')"></a>\n        </div>\n        \n        <h3 id="灰色系">灰色系</h3>\n        <div class="bgbox">\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #EEEEEE" onclick="changeBg(\'#EEEEEE\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #BDBDBD" onclick="changeBg(\'#BDBDBD\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #757575" onclick="changeBg(\'#757575\')"></a>\n          <a href="javascript:;" rel="noopener external nofollow" class="box" style="background: #424242" onclick="changeBg(\'#424242\')"></a>\n        </div>\n    `;\n    }\n    \n    // 适应窗口大小\n    function winResize() {\n        let box = document.querySelector(\'#changeBgBox\')\n        if (!box || box.classList.contains(\'min\') || box.classList.contains(\'max\')) return\n        var offsetWid = document.documentElement.clientWidth;\n        if (offsetWid <= 768) {\n            winbox.resize(offsetWid * 0.95 + "px", "90%").move("center", "center");\n        } else {\n            winbox.resize(offsetWid * 0.6 + "px", "70%").move("center", "center");\n        }\n    }\n    \n    // 切换弹窗显示/隐藏\n    function toggleWinbox() {\n        if (document.querySelector(\'#changeBgBox\')) winbox.toggleClass(\'hide\');\n        else createWinbox();\n    }\n    ```\n    **如何自定义您的背景库？**\n    * 请打开您刚刚创建的 `background-box.js` 文件，找到 `winbox.body.innerHTML = \\`...\\`;` 这一部分。\n    * 窗口内的所有内容都由这段HTML字符串定义。您可以：\n        * **替换图片**：将 `<a>` 标签中的 `background-image:url(...)` 和 `onclick="changeBg(\'url(...)\')"` 里的链接替换为您自己的图片链接。\n        * **增加/删除图片**：直接复制或删除整个 `<a>` 标签。\n        * **修改颜色**：修改“纯色”和“渐变色”部分 `<a>` 标签的 `style` 和 `onclick` 事件中的颜色值。\n\n---\n###### **第二步：添加侧边栏触发按钮**\n\n1.  **打开模板文件**：`themes/anzhiyu/layout/includes/header/nav.pug`\n2.  **修改内容**：我们需要在导航栏上方的按钮组中，添加一个新的按钮来调用 `toggleWinbox()` 函数。\n  \n    * **第一处修改**：在 `i.anzhiyufont.anzhiyu-icon-dice` 语句下方添加一个新的按钮。\n      \n        ```diff\n              .nav-button#background-change-button\n                a.site-page(onclick=\'toggleWinbox()\', title=\'切换背景\', href=\'javascript:void(0);\')\n                  i.fas.fa-palette\n        ```\n\n---\n###### **第三步：在主题配置中注入所需文件**\n\n1.  **打开主题配置文件** (`themes/anzhiyu/_config.yml`)。\n2.  **找到 `inject:` 配置项**。我们需要在这里引入三样东西：`WinBox`库本身，以及我们自定义的CSS和JS文件。\n\n'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#14-%E4%B8%BB%E9%A2%98%E9%AD%94%E6%94%B9%EF%BC%9A%E6%B7%BB%E5%8A%A0%E2%80%9C%E8%83%8C%E6%99%AF%E5%88%87%E6%8D%A2%E2%80%9D%E5%BC%B9%E7%AA%97%E9%9D%A2%E6%9D%BF"><span class="toc-number">1.</span> <span class="toc-text">14.主题魔改：添加“背景切换”弹窗面板</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D%E4%B8%8E%E9%87%8D%E8%A6%81%E6%8F%90%E7%A4%BA"><span class="toc-number">1.0.0.1.</span> <span class="toc-text">前言：功能介绍与重要提示</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA%E8%87%AA%E5%AE%9A%E4%B9%89%E6%A0%B7%E5%BC%8F%E4%B8%8E%E8%84%9A%E6%9C%AC"><span class="toc-number">1.0.0.2.</span> <span class="toc-text">第一步：创建自定义样式与脚本</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E6%B7%BB%E5%8A%A0%E4%BE%A7%E8%BE%B9%E6%A0%8F%E8%A7%A6%E5%8F%91%E6%8C%89%E9%92%AE"><span class="toc-number">1.0.0.3.</span> <span class="toc-text">第二步：添加侧边栏触发按钮</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%9C%A8%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E4%B8%AD%E6%B3%A8%E5%85%A5%E6%89%80%E9%9C%80%E6%96%87%E4%BB%B6"><span class="toc-number">1.0.0.4.</span> <span class="toc-text">第三步：在主题配置中注入所需文件</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>