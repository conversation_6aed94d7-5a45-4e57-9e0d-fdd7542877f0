// Solitude风格作者卡片样式
.card-author-solitude.card-info
  position relative
  overflow hidden
  border-radius 12px
  height 280px

  // 上半部分：渐变背景区域
  .card-top-section
    position relative
    height 140px
    background linear-gradient(-25deg, var(--anzhiyu-main), var(--anzhiyu-main-op-deep))
    display flex
    flex-direction column
    align-items center
    justify-content center
    color var(--anzhiyu-white)
    z-index 1

    .sayhi
      position absolute
      top 15px
      left 50%
      transform translateX(-50%)
      width fit-content
      font-size 11px
      background var(--anzhiyu-white-op)
      border-radius 15px
      cursor pointer
      min-width 80px
      padding 3px 10px
      color var(--anzhiyu-white)
      transition all .3s
      text-align center

      &:hover
        background var(--anzhiyu-white)
        color var(--anzhiyu-main)
        transform translateX(-50%) scale(1.05)

    .avatar
      width 70px
      height 70px
      position relative
      margin-top 10px
      transition all .3s

      img
        border-radius 50%
        width 100%
        height 100%
        border 3px solid var(--anzhiyu-white)
        overflow hidden
        object-fit cover

      .sticker
        position absolute
        bottom -2px
        right -2px
        width 22px
        height 22px
        display flex
        align-items center
        justify-content center
        background var(--anzhiyu-white)
        border-radius 50%

        .sticker-img
          width 16px
          height 16px
          border-radius 50%

  // 下半部分：白色背景区域
  .card-bottom-section
    background var(--anzhiyu-card-bg)
    padding 15px
    color var(--anzhiyu-fontcolor)
    height 140px
    position relative
    z-index 2

    .author-info
      text-align center
      margin-bottom 15px

      .name
        font-size 16px
        font-weight 700
        color var(--anzhiyu-fontcolor)
        margin-bottom 5px

      .desc
        font-size 12px
        color var(--anzhiyu-fontcolor)
        opacity 0.7
        line-height 1.3

    // 站点统计样式
    .site-stats
      display flex
      justify-content space-around
      margin-bottom 12px

      .stat-item
        text-align center
        flex 1

        a
          display block
          color var(--anzhiyu-fontcolor)
          text-decoration none
          transition all .3s
          padding 5px
          border-radius 6px

          &:hover
            background var(--anzhiyu-secondbg)
            transform translateY(-1px)

        .stat-number
          font-size 20px
          font-weight 700
          line-height 1
          margin-bottom 2px
          color var(--anzhiyu-fontcolor)

        .stat-label
          font-size 11px
          color var(--anzhiyu-fontcolor)
          opacity 0.7

    // 社交图标样式
    .social-icons
      display flex
      justify-content center
      gap 10px

      .social-icon
        display inline-flex
        align-items center
        justify-content center
        width 30px
        height 30px
        background var(--anzhiyu-secondbg)
        border-radius 50%
        color var(--anzhiyu-fontcolor)
        text-decoration none
        transition all .3s

        &:hover
          background var(--anzhiyu-main)
          color var(--anzhiyu-white)
          transform scale(1.1)

        i
          font-size 14px

// 响应式适配
+maxWidth768()
  .card-author-solitude.card-info
    height 250px

    .card-top-section
      height 120px

      .avatar
        width 60px
        height 60px

      .sticker
        width 18px
        height 18px

        .sticker-img
          width 12px
          height 12px

    .card-bottom-section
      padding 12px
      height 130px

      .site-stats
        .stat-item
          .stat-number
            font-size 18px
          .stat-label
            font-size 10px
