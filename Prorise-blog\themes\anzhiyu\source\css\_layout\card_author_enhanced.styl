// Solitude风格作者卡片样式
.card-author-solitude.card-info
  position relative
  overflow hidden
  border-radius 12px
  min-height 320px

  // 上半部分：渐变背景区域
  .card-top-section
    position relative
    height 200px
    background linear-gradient(-25deg, var(--anzhiyu-main), var(--anzhiyu-main-op-deep))
    display flex
    flex-direction column
    align-items center
    justify-content center
    color var(--anzhiyu-white)

    .sayhi
      position absolute
      top 20px
      left 50%
      transform translateX(-50%)
      width fit-content
      font-size 12px
      background var(--anzhiyu-white-op)
      border-radius 20px
      cursor pointer
      min-width 100px
      padding 4px 12px
      color var(--anzhiyu-white)
      transition all .3s
      text-align center

      &:hover
        background var(--anzhiyu-white)
        color var(--anzhiyu-main)
        transform translateX(-50%) scale(1.05)

    .avatar
      width 80px
      height 80px
      position relative
      margin-top 20px
      transition all .3s

      img
        border-radius 50%
        width 100%
        height 100%
        border 3px solid var(--anzhiyu-white)
        overflow hidden
        object-fit cover

      .sticker
        position absolute
        bottom -2px
        right -2px
        width 24px
        height 24px
        display flex
        align-items center
        justify-content center
        background var(--anzhiyu-white)
        border-radius 50%

        .sticker-img
          width 18px
          height 18px
          border-radius 50%

  // 下半部分：白色背景区域
  .card-bottom-section
    background var(--anzhiyu-card-bg)
    padding 20px
    color var(--anzhiyu-fontcolor)

    .author-info
      text-align center
      margin-bottom 20px

      .name
        font-size 18px
        font-weight 700
        color var(--anzhiyu-fontcolor)
        margin-bottom 8px

      .desc
        font-size 14px
        color var(--anzhiyu-fontcolor)
        opacity 0.7
        line-height 1.4

    // 站点统计样式
    .site-stats
      display flex
      justify-content space-around
      margin-bottom 20px

      .stat-item
        text-align center
        flex 1

        a
          display block
          color var(--anzhiyu-fontcolor)
          text-decoration none
          transition all .3s
          padding 8px
          border-radius 8px

          &:hover
            background var(--anzhiyu-secondbg)
            transform translateY(-2px)

        .stat-number
          font-size 24px
          font-weight 700
          line-height 1
          margin-bottom 4px
          color var(--anzhiyu-fontcolor)

        .stat-label
          font-size 12px
          color var(--anzhiyu-fontcolor)
          opacity 0.7

    // 社交图标样式
    .social-icons
      display flex
      justify-content center
      gap 12px

      .social-icon
        display inline-flex
        align-items center
        justify-content center
        width 36px
        height 36px
        background var(--anzhiyu-secondbg)
        border-radius 50%
        color var(--anzhiyu-fontcolor)
        text-decoration none
        transition all .3s

        &:hover
          background var(--anzhiyu-main)
          color var(--anzhiyu-white)
          transform scale(1.1)

        i
          font-size 16px

// 响应式适配
+maxWidth768()
  .card-author-solitude.card-info
    min-height 280px

    .card-top-section
      height 160px

      .avatar
        width 60px
        height 60px

      .sticker
        width 20px
        height 20px

        .sticker-img
          width 14px
          height 14px

    .card-bottom-section
      padding 15px

      .site-stats
        .stat-item
          .stat-number
            font-size 20px
          .stat-label
            font-size 11px
