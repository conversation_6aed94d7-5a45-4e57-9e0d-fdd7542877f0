// Solitude风格作者卡片样式
.card-author-solitude.card-info
  position relative
  overflow hidden
  border-radius 12px
  // 移除固定高度，让内容自然撑开

  // 上半部分：渐变背景区域
  .card-top-section
    position relative
    height 140px
    background linear-gradient(-25deg, var(--anzhiyu-main), var(--anzhiyu-main-op-deep))
    display flex
    flex-direction column
    align-items center
    justify-content center
    color var(--anzhiyu-white)
    z-index 1

    .sayhi
      position absolute
      top 15px
      left 50%
      transform translateX(-50%)
      width fit-content
      font-size 11px
      background var(--anzhiyu-white-op)
      border-radius 15px
      cursor pointer
      min-width 80px
      padding 3px 10px
      color var(--anzhiyu-white)
      transition all .3s
      text-align center

      &:hover
        background var(--anzhiyu-white)
        color var(--anzhiyu-main)
        transform translateX(-50%) scale(1.05)

    // 头像区域 - 在card-top-section内部，绝对定位到分界线上
    .avatar
      position absolute
      top 70px  // 调整到更接近交接线的位置（140px - 50px = 90px，头像中心在交接线上）
      left 50%
      transform translateX(-50%)
      width 100px  // 增大头像尺寸
      height 100px
      z-index 100  // 提高z-index确保在所有内容之上
      transition all .3s

      img
        border-radius 50%
        width 100%
        height 100%
        border 4px solid var(--anzhiyu-white)  // 增加边框厚度
        overflow hidden
        object-fit cover

      .sticker
        position absolute
        bottom 0px
        right 0px
        width 28px  // 增大贴纸尺寸
        height 28px
        display flex
        align-items center
        justify-content center
        background var(--anzhiyu-white)
        border-radius 50%

        .sticker-img
          width 20px
          height 20px
          border-radius 50%

  // 下半部分：白色背景区域
  .card-bottom-section
    background var(--anzhiyu-card-bg)
    padding 60px 15px 15px  // 增加顶部padding为头像留出空间
    color var(--anzhiyu-fontcolor)
    // 移除固定高度，让内容自然撑开
    position relative
    z-index 2

    .author-info
      text-align center
      margin-bottom 15px

      .name
        font-size 16px
        font-weight 700
        color var(--anzhiyu-fontcolor)
        margin-bottom 5px

      .desc
        font-size 12px
        color var(--anzhiyu-fontcolor)
        opacity 0.7
        line-height 1.3

    // 站点统计样式
    .site-stats
      display flex
      justify-content space-around
      margin-bottom 12px

      .stat-item
        text-align center
        flex 1

        a
          display block
          color var(--anzhiyu-fontcolor)
          text-decoration none
          transition all .3s
          padding 5px
          border-radius 6px

          &:hover
            background var(--anzhiyu-secondbg)
            transform translateY(-1px)

        .stat-number
          font-size 20px
          font-weight 700
          line-height 1
          margin-bottom 2px
          color var(--anzhiyu-fontcolor)

        .stat-label
          font-size 11px
          color var(--anzhiyu-fontcolor)
          opacity 0.7

    // 社交图标样式
    .social-icons
      display flex
      justify-content center
      gap 10px

      .social-icon
        display inline-flex
        align-items center
        justify-content center
        width 30px
        height 30px
        background var(--anzhiyu-secondbg)
        border-radius 50%
        color var(--anzhiyu-fontcolor)
        text-decoration none
        transition all .3s

        &:hover
          background var(--anzhiyu-main)
          color var(--anzhiyu-white)
          transform scale(1.1)

        i
          font-size 14px

// 响应式适配
+maxWidth768()
  .card-author-solitude.card-info
    height 280px  // 增加移动端总高度

    .card-top-section
      height 120px

      .avatar
        top 20px  // 调整移动端头像位置，相对于card-top-section
        width 80px  // 移动端头像尺寸
        height 80px

        .sticker
          width 24px
          height 24px

          .sticker-img
            width 16px
            height 16px

    .card-bottom-section
      padding 50px 12px 12px  // 移动端也为头像留出空间
      height 160px  // 增加移动端下半部分高度

      .site-stats
        .stat-item
          .stat-number
            font-size 18px
          .stat-label
            font-size 10px
