- var datalinks = []
- var data = site.data.links.links
if(data)
    each item in data
            each y in item.link_list
                - datalinks.push({ name: y.name,link: y.link})

script.
    const links = !{JSON.stringify(datalinks)}
    const randomText = '!{_p('link.random')}'
    function travelling() {
        const link = links[utils.randomNum(links.length)];
        Snackbar.show({
            text: randomText.replace(/\$\{name}/,link.name),
            duration: 8000,
            pos: 'top-center',
            actionText: '!{_p('link.to')}',
            onActionClick: function(element) {
                element.style.opacity = 0;
                window.open(link.link, '_blank');
            }
        });
    }

if theme.footer.randomlink
    script.
        function randomLinksList() {
            const linkcard = document.getElementById('friend-links-in-footer');
            if (linkcard == null) return;

            let data = '';
            let linksCopy = [...links];
            let count = Math.min(3, linksCopy.length);

            for (let i = 0; i < count; i++) {
                let index = utils.randomNum(linksCopy.length);
                const {link, name} = linksCopy[index];
                data += `<a class="footer-item" href="${link}" target="_blank" rel="noopener noreferrer nofollow">${name}</a>`;
                linksCopy.splice(index, 1);
            }

            linkcard.innerHTML = data + '<a class="footer-item" href="#{url_for(/links/)}">#{_p('more')}</a>';
        }
