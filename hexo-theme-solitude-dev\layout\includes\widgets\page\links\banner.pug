.banners-title
    .banners-title-small= page.desc
    .banners-title-big= page.title

.banner-button-group
    if theme.footer.randomlink
        a.banner-button.secondary(onclick="travelling()")
            i.solitude.fas.fa-tower-broadcast
            span.banner-button-text=_p('link.banner.random')
    if theme.comment.use
        a.banner-button(onclick="sco.scrollToComment()")
            i.solitude.fas.fa-circle-chevron-right
            span.solitude.banner-button-text=_p('link.banner.toComment')

- if (site.data.links.swiper !== false)
    .tags-group-all.nowrapMove
        - const data = site.data.links.links.filter(x => x.type !== 'discn')
        - const links = data.flatMap(x => x.link_list)
        .tags-group-wrapper
            - const pairs = links.reduce(function(acc, link, index) {
            -    if (index % 2 === 0) acc.push([link])
            -    else acc[acc.length - 1].push(link)
            -    return acc
            - }, [])
            each i in [0, 1]
                each pair in pairs
                    .tags-group-icon-pair
                        each y in pair
                            a.tags-group-icon(href=url_for(y.link), title=y.name)
                                img(src=y.avatar + (site.data.links.banner_suffix || ''), title=y.name)
                                span.tags-group-title=y.name
- else
    .tags-group-all(style="height:calc(3rem + 72px);")