/* 文档式左侧目录样式 - 最终优化版 (字体最大化 + 动态高亮) */

.doc-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 17rem; /* 因字体增大，稍微加宽侧边栏 */
  height: 100vh;
  background: var(--anzhiyu-card-bg, #ffffff);
  border-right: var(--style-border-always);
  padding: 1rem 0;
  overflow-y: auto;
  z-index: 1000;
  transition: transform 0.3s ease-in-out, background-color 0.3s ease;
  transform: translateX(0);
}

@media (min-width: 1024px) {
  .doc-sidebar {
    width: 19rem; /* 大屏也相应加宽 */
  }
}
/* 其他媒体查询保持不变 */
@media (max-width: 768px) { .doc-sidebar { transform: translateX(-100%); } }
.doc-sidebar.hidden { transform: translateX(-100%); }

.doc-toc-content {
  padding: 0 1rem;
  margin-top: 1.5rem;
  max-height: calc(100vh - 150px);
  overflow-y: auto;
}

.doc-toc-content ul, .doc-toc-content ol {
  list-style: none !important;
  margin: 0;
  padding: 0;
}
.doc-toc-content li {
  list-style-type: none !important;
  margin: 0;
  padding: 0;
  position: relative;
}

.doc-toc-content .toc-child {
  padding-left: 1.35rem; /* 适配大字体和图标 */
  position: relative;
}
.doc-toc-content .toc-child::before {
  content: '';
  position: absolute;
  left: 9px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: var(--anzhiyu-gray-op, #e5e7eb);
}

.doc-toc-content a {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.6rem;
  color: var(--anzhiyu-secondtext);
  text-decoration: none;
  border-radius: 8px;
  transition: color 0.2s ease, font-weight 0.2s ease, transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* 设置一个较大的基础字号 */
  font-size: 17px; 
  line-height: 1.6;
  transform-origin: left center; /* 设置缩放基点为左侧中心 */
}

.doc-toc-content a:hover {
  color: var(--anzhiyu-fontcolor);
  background-color: var(--anzhiyu-ahoverbg);
}

/* --- 核心修改：动态高亮效果 (放大+加粗+变色) --- */
.doc-toc-content li.active > .toc-link,
.doc-toc-content a.active {
  background-color: transparent !important; /* 确保无背景 */
  color: var(--anzhiyu-theme) !important;
  font-weight: 700; /* 使用更粗的字重 */
  transform: scale(1.05); /* 放大5%，效果非常明显 */
}

/* 层级字体，现在更注重字重和颜色区分 */
.doc-toc-content .toc-level-1 > .toc-link,
.doc-toc-content .toc-level-2 > .toc-link {
  font-size: 18px; /* 顶级章节最大 */
  font-weight: 500;
  color: var(--anzhiyu-fontcolor);
}
/* 激活时，顶级章节也要应用动态效果 */
.doc-toc-content li.active > .toc-level-1,
.doc-toc-content li.active > .toc-level-2 {
  font-weight: 700;
  transform: scale(1.05);
}

.doc-toc-content .toc-level-3 > .toc-link {
  font-size: 17px;
}
.doc-toc-content .toc-level-4 > .toc-link {
  font-size: 16px;
}
.doc-toc-content .toc-level-5 > .toc-link,
.doc-toc-content .toc-level-6 > .toc-link {
  font-size: 16px;
  opacity: 0.8;
}

/* 折叠图标样式 */
.collapse-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 6px;
  font-size: 14px;
  color: var(--anzhiyu-secondtext);
  transition: transform 0.2s ease, color 0.2s ease;
  cursor: pointer;
  user-select: none;
  flex-shrink: 0;
}
.doc-toc-content a:hover .collapse-icon {
  color: var(--anzhiyu-fontcolor);
}
.doc-toc-content a.active .collapse-icon {
  color: var(--anzhiyu-theme);
}
.toc-item.expanded > .toc-link > .collapse-icon {
  transform: rotate(90deg);
}

/* 暗色主题适配 */
[data-theme="dark"] .doc-sidebar {
  background: var(--anzhiyu-card-bg, #282c34);
  border-right-color: var(--anzhiyu-card-border);
}
[data-theme="dark"] .doc-toc-content a:hover {
  background-color: rgba(255, 255, 255, 0.08);
}
[data-theme="dark"] .doc-toc-content .toc-child::before {
  background-color: rgba(255, 255, 255, 0.15);
}

/* 其他样式保持不变 */
body.doc-sidebar-active:not(.hide-doc-sidebar) #body-wrap { margin-left: 17rem; transition: margin-left 0.3s ease-in-out; }
@media (min-width: 1024px) { body.doc-sidebar-active:not(.hide-doc-sidebar) #body-wrap { margin-left: 19rem; } }
@media (max-width: 768px) { body.doc-sidebar-active:not(.hide-doc-sidebar) #body-wrap { margin-left: 0; } }
body.doc-sidebar-active.hide-doc-sidebar #body-wrap { margin-left: 0; transition: margin-left 0.3s ease-in-out; }
.doc-sidebar::-webkit-scrollbar, .doc-toc-content::-webkit-scrollbar { width: 8px; }
.doc-sidebar::-webkit-scrollbar-track, .doc-toc-content::-webkit-scrollbar-track { background: transparent; }
.doc-sidebar::-webkit-scrollbar-thumb, .doc-toc-content::-webkit-scrollbar-thumb { background: var(--anzhiyu-scrollbar); border-radius: 8px; border: 2px solid transparent; background-clip: content-box; cursor: pointer; }
.doc-sidebar::-webkit-scrollbar-thumb:hover, .doc-toc-content::-webkit-scrollbar-thumb:hover { background: var(--anzhiyu-theme); background-clip: content-box; }

/* 修复侧边栏隐藏功能 */
body.hide-doc-sidebar .doc-sidebar {
  transform: translateX(-100%);
}

/* 确保过渡动画平滑 */
.doc-sidebar {
  transition: transform 0.3s ease-in-out;
}