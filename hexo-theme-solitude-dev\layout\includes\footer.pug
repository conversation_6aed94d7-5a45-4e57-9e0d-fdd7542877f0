- var { information , group } = theme.footer

if theme.post.footer.enable && is_post()
    div#st-footer-bar
        div.footer-logo
            if theme.site.name.class === 'i_class'
                i.solitude(class=theme.site.name.custom)
            else if theme.site.name.class === 'img'
                img.solitude.nolazyload(src=url_for(theme.site.name.custom))
            else if theme.site.name.class === 'text'
                span= theme.site.name.custom
        div.footer-bar-description=theme.post.footer.desc
        if theme.post.footer.button.enable
            a.footer-bar-link(href=url_for(theme.post.footer.button.url))= theme.post.footer.button.name
if information.left || information.right || information.author
    div#footer_deal
        - var leftInfo = information.left || []
        - var rightInfo = information.right || []

        each value, label in leftInfo
            - var array = value.split('||')
            a.deal_link(href=url_for(trim(array[0])), title=label)
                i.solitude(class=array[1])

        if information.author
            div#footer_mini_logo.nolazyload.footer_mini_logo(title=_p('nav.backtop'), onclick="sco.toTop()")
                img(src=information.author, alt=_p('nav.backtop'))

        each value, label in rightInfo
            - var array = value.split('||')
            a.deal_link(href=url_for(trim(array[0])), title=label)
                i.solitude(class=array[1])
if group
    div#st-footer
        each value, x in group || []
            div.footer-group
                h3.footer-title= x
                div.footer-links
                    each url, y in value
                        a.footer-item(href=url_for(url), title=y)= y

        if theme.footer && theme.footer.randomlink
            div.footer-group
                div.footer-title-group
                    h3.footer-title= _p('footer.randomlink')
                    button.random-friends-btn(onclick='randomLinksList()', title=_p('footer.randomlink'))
                        i.solitude.fas.fa-arrows-rotate
                div.footer-links#friend-links-in-footer
div#footer-bar
    div.footer-bar-links
        div.footer-bar-left
            if moment(theme.aside.siteinfo.runtime).year() === new Date().getFullYear()
                div.copyright © #{moment(theme.aside.siteinfo.runtime).year()} By&nbsp;
                    a.footer-bar-link(href=url_for("/"))
                        img.author-avatar(src=url_for(theme.site.icon))
                        = config.author
                .beian-group
                    if theme.footer.beian
                        - var beian = theme.footer.beian || []
                        each item in beian
                            a.footer-bar-link(href=url_for(item.url), title=item.name)
                                if item.icon 
                                    img.beian-icon(src=url_for(item.icon), alt=item.name)
                                span.beian-name= item.name
                    a.footer-bar-link(href=_p('hexo'))
                        = _p('framework_by') + 'Hexo'
                    a.footer-bar-link(href=_p('repo'))
                        = _p('theme_by') + 'Solitude'
            else
                div.copyright © #{moment(theme.aside.siteinfo.runtime).year()} - #{new Date().getFullYear()} By&nbsp;
                    a.footer-bar-link(href=url_for("/"))
                        img.author-avatar(src=url_for(theme.site.icon))
                        = config.author
                .beian-group
                    if theme.footer.beian
                        - var beian = theme.footer.beian || []
                        each item in beian
                            a.footer-bar-link(href=url_for(item.url), title=item.name)
                                if item.icon 
                                    img.beian-icon(src=url_for(item.icon), alt=item.name)
                                span.beian-name= item.name
                    a.footer-bar-link(href=_p('hexo'))
                        = _p('framework_by') + 'Hexo'
                    a.footer-bar-link(href=_p('repo'))
                        = _p('theme_by') + 'Solitude'
        if theme.footer.links
            div.footer-bar-right
                each item in theme.footer.links
                    if item.icon
                        a.footer-bar-link(href=url_for(item.url), alt=item.name)
                                each icon in item.icon
                                    i.solitude(class=icon)
                    else if item.img
                        a.footer-bar-link(href=url_for(item.url), alt=item.name)
                            img(src=url_for(item.img), alt=item.name)
                    else
                        a.footer-bar-link(href=url_for(item.url), alt=item.name)!= item.name
                        
if theme.comment.use && theme.comment.commentBarrage
    div.comment-barrage.needEndHide
