- const google_adsense = theme.google_adsense.enable
- const select = ['title','#body-wrap','#site-config','meta[name="description"]','.js-pjax','meta[property^="og:"]','#config-diff', '.rs_show', '.rs_hide']

script.
    const pjax = new Pjax({
        elements: 'a:not([target="_blank"])',
        selectors: !{JSON.stringify(select)},
        cacheBust: false,
        analytics: !{google_adsense},
        scrollRestoration: false
    })

    document.querySelectorAll('script[data-pjax]').forEach(item => {
        const newScript = document.createElement('script')
        const content = item.text || item.textContent || item.innerHTML || ""
        Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
        newScript.appendChild(document.createTextNode(content))
        item.parentNode.replaceChild(newScript, item)
    })

    document.addEventListener('pjax:complete', () => {
        window.refreshFn()

        document.querySelectorAll('script[data-pjax]').forEach(item => {
            const newScript = document.createElement('script')
            const content = item.text || item.textContent || item.innerHTML || ""
            Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
            newScript.appendChild(document.createTextNode(content))
            item.parentNode.replaceChild(newScript, item)
        })

        GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

    })

    document.addEventListener('pjax:error', (e) => {
        if (e.request.status === 404) {
            pjax.loadUrl('/404.html')
        }
    })
