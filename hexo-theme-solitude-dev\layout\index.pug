extends includes/layout

block content
    if theme.hometop.enable || (theme.brevity.home_mini && theme.brevity.enable)
        #home_top
            if theme.brevity.home_mini && theme.brevity.enable
                include ./includes/widgets/home/<USER>
            if theme.hometop.enable && is_home_first_page()
                include ./includes/widgets/home/<USER>
    main.layout#content-inner
        #home
            #category-bar
                include ./includes/widgets/home/<USER>
            .recent-posts#recent-posts
                if theme.comment.hot_tip.enable
                    include ./includes/widgets/third-party/hot/index.pug
                for post, index in page.posts.sort("-sticky" || "-date").data
                    include ./includes/widgets/home/<USER>

                // pagination
                include ./includes/mixins/pagination
        // aside
        include ./includes/widgets/aside/aside