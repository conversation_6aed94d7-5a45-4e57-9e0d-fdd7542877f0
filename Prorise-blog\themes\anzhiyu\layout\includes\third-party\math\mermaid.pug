script.
  (() => {
    const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
    if ($mermaid.length === 0) return
    const runMermaid = () => {
      window.loadMermaid = true
      const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? '!{theme.mermaid.theme.dark}' : '!{theme.mermaid.theme.light}'

      Array.from($mermaid).forEach((item, index) => {
        const mermaidSrc = item.firstElementChild
        const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
        const mermaidID = 'mermaid-' + index
        const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

        const renderFn = mermaid.render(mermaidID, mermaidDefinition)

        const renderV10 = () => {
          renderFn.then(({svg}) => {
            mermaidSrc.insertAdjacentHTML('afterend', svg)
          })
        }

        const renderV9 = svg => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        }

        typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
      })
    }

    const loadMermaid = () => {
      window.loadMermaid ? runMermaid() : getScript('!{url_for(theme.asset.mermaid)}').then(runMermaid)
    }

    anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

    window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
  })()