---
title: 产品经理进阶（五）：第五章：电商后台 - 商品管理
categories: 产品经理
tags:
  - 产品经理教程
cover: 'https://bu.dusays.com/2025/07/25/6882f31a48223.webp'
comments: true
toc: true
ai: true
abbrlink: 4512
date: 2025-07-24 20:13:45
---

# 第五章：电商后台 - 商品管理

在上一章，我们已经设计好了商家入驻的流程，让第一批商家成功进入了我们的平台。现在，他们最迫切的需求就是：**我应该如何，把我的商品，发布到平台上进行售卖？**

本章，我们就将为商家，以及我们自己平台的运营，设计一套完整、专业、可扩展的商品管理系统。

## 5.1 学习目标

![image-20250722174132300](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722174132300.png)

在本章中，我的核心目标是，带大家掌握电商后台商品管理模块的完整设计。我们将首先从宏观上，**推导出商品从“录入”到“呈现”的核心业务流程**，然后再深入到微观，学习**SPU/SKU、类目、属性**等构建商品体系的“原子”概念，并最终将它们组合成一个完整的“**商品发布功能**”。

---
## 5.2 商品发布流程推导

在我动手设计“商品发布”这个后台功能之前，我一定会先将支撑这个功能的**端到端业务流程**，梳理得一清二楚。

### 1. 从“最小商品模型”开始思考

我的思考，会先从“终局”出发，即，从一个**普通用户**的视角来看：**要让我能看懂一件商品，至少需要呈现哪些信息？**

![image-20250722180034500](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180034500.png)

这个问题的答案，构成了我们电商系统的“**最小商品模型**”。它至少需要包含以下五个核心信息：
1.  **标题**：商品叫什么名字。
2.  **图片**：商品长什么样。
3.  **价格**：商品卖多少钱。
4.  **库存**：商品还有没有货。
5.  **描述**：商品的详细介绍。

![image-20250722180117296](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180117296.png)

### 2. 用“特殊场景”挑战简单模型

那么，基于这个最小模型，一个最简单的设计思路就是：我只需要给商家提供一个包含这五个字段的表单，让他填完提交，不就可以了吗？

**这样够吗？**
当我把这个简单的模型，放入真实的、复杂的电商场景中去检验时，会立刻发现它的不足。

![image-20250722180223346](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180223346.png)

我必须考虑到以下这些“**特殊场景**”：
* **分类场景**：任何一件商品，都需要被归属到一个明确的“**商品分类**”下（如：电脑/办公 -> 电脑组件 -> 硬盘），这样用户才能通过分类导航找到它。

![image-20250722180312976](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180312976.png)

* **品牌场景**：用户也常常会通过“**品牌**”的维度来找商品（如：联想品牌馆）。因此，商品也需要和品牌进行关联。



![image-20250722180255028](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180255028.png)

* **属性场景**：不同品类的商品，其需要展示的“**商品参数**”是完全不同的。比如，硬盘需要展示`容量`、`接口`等参数；而一件衣服，则需要展示`材质`、`适用季节`等参数。一个固定的、简单的表单，是无法满足这种多样性的。

### 3. 推导出核心发布流程

![image-20250722180337574](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180337574.png)

为了解决上述所有特殊场景带来的问题，我推导出的、一个专业的“**商家发布商品核心流程**”，必须是一个**结构化的、分步骤**的流程：



![image-20250722180649979](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180649979.png)

1.  **第一步：选择商品类目**
    这是商家发布商品的**起点**。我会让商家，先从我们后台预设的“商品类目”树中，精准地选择他将要发布的商品，属于哪一个最细分的叶子类目。
    * **我的设计思考**：这一步至关重要，因为**用户选择的类目，将直接决定了下一步他需要填写的“商品属性”**。

2.  **第二步：选择商品品牌**
    在确定了类目后，商家需要选择该商品所属的“品牌”。

3.  **第三步：设置商品信息**
    只有在完成了前两步之后，系统才会展示出最终的“商品信息设置”页面。这个页面，除了包含我们前面提到的“最小商品模型”（标题、价格、图片、库存、描述）的填写区域外，还会根据第一步选择的类目，**动态地**加载出该类目专属的“**商品属性**”填写项。

### 4. 流程总结

至此，我们就完成了一次完整的流程推导。这个“**先选类目 -> 再选品牌 -> 最后填写信息**”的三步走流程，就是我为商家设计的、既能满足复杂场景，又能保证后台数据结构化、规范化的核心解决方案。

这个流程，完美地嵌入到了我们之前梳理的“**商家录入 -> 平台审核 -> 用户浏览**”的宏观业务流程中，构成了其中“**商家录入**”这一环节的具体实现。



---
## 5.3 商品类目

在我推导出的“**先选类目 -> 再选品牌 -> 最后填写信息**”的商品发布流程中，“**选择商品类目**”是所有流程的起点。

因此，**商品类目**体系，是我在设计商品管理后台时，第一个要搭建的、也是最底层的“**地基**”。一个清晰、稳定、可扩展的类目体系，是整个电商平台有序运转的保障。

### 1. 商品类目的核心作用

![image-20250722180926020](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722180926020.png)

我设计商品类目体系，是因为它同时为我们生态中的三大核心角色，都提供了不可或缺的价值。

| 角色 | 核心作用 | 我的解读 |
| :--- | :--- | :--- |
| **平台** | **确定服务范围，进行监管** | 我后台设置的类目，直接定义了“**我们平台允许卖什么，不允许卖什么**”。这是我进行平台治理、控制风险、明确业务边界的根本。 |
| **商家**| **分门别类，便于管理** | 我为商家提供了一套标准化的“**商品货架**”。商家只需要按照我的类目规范，将商品“上架”到对应的位置，就能实现规范化的管理。 |
| **用户**| **方便查找商品** | 我为用户提供了一套清晰的“**商场导览图**”。用户可以通过分类导航，快速地找到自己想要逛的“区域”，极大地提升了购物效率。 |

### 2. 从角色需求到产品功能

![image-20250722181002478](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181002478.png)

基于上述的作用，我可以清晰地提炼出不同角色的产品需求，并推导出我们需要提供的核心功能。

这个推导的结论非常清晰，也是我设计的核心原则：“**平台来管理类目，商家使用类目**”。
* **平台的需求**是“限定范围、监管”，这要求我必须设计一套强大的“**后台类目管理**”功能。
* **商家的需求**是“加载类目模板、方便操作”，这要求我必须在“**商品发布**”流程中，提供一个易用的“**类目选择**”功能。

### 3. 平台端：类目管理功能设计

![image-20250722181719285](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181719285.png)

这是我为平台运营人员设计的“**类目配置中心**”。
* **多级类目结构**：我设计的后台，必须支持**多级类目**的创建和管理。如案例图所示，一个“笔记本”商品，它的类目层级可能是“数码家电（一级）” -> “家电（二级）” -> “笔记本（三级）”。
* **基础管理操作**：后台必须提供对每一级类目的**新增、编辑、删除、查询**等基础操作。
* **我的拓展设计（属性与品牌关联）**：这是后台类目管理最核心的、也是最高阶的功能。在运营人员新增或编辑一个“叶子类目”（即，不能再往下分的最后一级类目，如“笔记本”）时，我设计的后台，**必须允许他，将这个类目，与一组特定的“商品属性”和“品牌”进行关联**。
    * 例如，在配置“笔记本”这个类目时，运营就要为它关联上“屏幕尺寸”、“内存”、“CPU型号”等**属性**，并关联上“联想”、“华为”、“苹果”等**品牌**。

### 4. 商家端与用户端：类目的使用

我们平台运营在后台辛辛苦苦搭建好的这套类目体系，最终会在商家端和用户端，被“使用”和“呈现”。

![image-20250722181801927](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181801927.png)

* **在商家端**：当商家发布新商品时，我们流程的**第一步**，就是让他从我们后台预设好的类目树中，选择一个。当他选择了“笔记本”之后，系统就会因为我们后台的“关联”配置，而**动态地**为他加载出需要填写的“屏幕尺寸”、“内存”等属性。

![image-20250722181837353](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722181837353.png)

* **在用户端**：用户则会在我们App的“**分类**”Tab页，看到我们后台配置好的类目树结构，并可以逐级点击，进行商品的浏览和筛选。

一个后台设计得清晰、合理的类目体系，是前台商家发布体验流畅、用户浏览体验清晰的根本保障。






---
## 5.4 品牌管理

在我看来，如果说“类目”是商品的**物理属性**分类，那么“**品牌**”就是商品的**心智属性**分类。

用户在购买决策时，品牌是影响他们信任和选择的、极其重要的一个因素。

因此，我必须在后台，建立一套完善的、由平台强管控的品牌管理体系。

### 1. 品牌管理的核心价值

![image-20250722213811999](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213811999.png)

我设计品牌管理系统，同样是为了服务好我们生态中的三大核心角色。

| **角色** | **核心需求** | **我需要提供的产品能力** |
| :--- | :--- | :--- |
| **平台** | **规避假冒伪劣、山寨产品**，保证平台的商品品质和声誉。 | 必须建立一套**品牌的审核与认证机制**，确保只有合规的、真实的品牌才能在平台上被售卖。 |
| **商家** | 能够清晰地标明自己所售卖商品的**品牌归属**。 | 我需要在商品发布流程中，为商家提供一个清晰、准确的**品牌选择器**。 |
| **用户** | **“我只想看某个品牌的商品”**，能够通过品牌维度，快速找到自己想要的商品。| 我需要在用户端，提供**按品牌进行搜索和筛选**的功能。 |

![image-20250722213850767](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213850767.png)

基于上述的需求，我推导出的核心功能是：
* **平台端**：必须有后台**品牌管理**（增删改查）功能。
* **商家端**：必须在商品发布时，有**品牌选择**功能。
* **用户端**：必须有**品牌搜索/筛选**功能。

### 2. 平台端：品牌库管理功能设计

![image-20250722213944716](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722213944716.png)

所有品牌功能的核心，在于我们平台运营后台，必须建立一个“**品牌库（Brand Library）**”。这个品牌库，是由我们**平台统一进行维护**的，它是我们平台上所有“合法品牌”的唯一真实来源。

我设计的品牌库后台，主要包含以下功能：
* **品牌信息字段**：在新增一个品牌时，运营需要填写该品牌的`Logo`、`中文名`、`英文名`、`简介`等信息。
* **基础管理功能**：运营可以对品牌库中的品牌，进行常规的**新增、编辑、查询**操作。
* **状态管理**：每个品牌，都有“**启用/停用**”两种状态。当某个品牌出现问题（如：被曝出重大质量问题、品牌方与我们合作终止）时，运营可以将其状态，设置为“停用”。设置为“停用”后，商家在发布商品时，就无法再选择这个品牌了。

### 3. 特殊流程：自主品牌入驻

这时，一个非常常见的业务场景就出现了：**如果一个商家，想售卖一个我们品牌库里，还没有收录的新品牌，怎么办？**

我不能让商家随意地、手动地填写品牌名称，这会导致品牌库数据混乱，出现大量山寨和无效品牌。因此，我必须设计一套严谨的“**新品牌入驻审核**”流程。

![image-20250722214029207](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214029207.png)

我的设计流程如下：
1.  **商家提交申请**：在商家后台，我会提供一个“**新增品牌申请**”的入口。商家需要在这个页面，填写新品牌的基础信息，并**必须上传该品牌的《商标注册证》**作为资质证明。
2.  **平台审核**：商家的申请，会进入我们平台运营后台的“品牌审核”列表中。运营同事的核心工作，是**核实《商标注册证》的真实性**和有效性。
3.  **品牌入库**：审核通过后，运营同事，会将这个新品牌的信息，正式录入到我们的“品牌库”中，并设置为“启用”状态。
4.  **商家选用**：一旦品牌成功入库，商家（以及其他所有获得了该品牌授权的商家），就可以在发布商品时，从品牌选择器中，选择这个新的品牌了。

通过这套流程，我既满足了商家引入新品牌的需求，又确保了平台对所有品牌的“强管控”，保证了我们电商品牌生态的健康。

---
## 5.5 SKU与SPU

在我设计任何电商后台的商品系统时，我的第一个思考，就是要清晰地定义**SPU**和**SKU**。这两个概念，是整个商品世界的“**基本粒子**”，理解了它们，就理解了所有复杂商品体系的构成。

### 1. 核心定义

![image-20250722214844754](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214844754.png)

* **SPU - 标准产品单元 (Standard Product Unit)**
    我把它理解为“**一款商品**”。它是一组具有共同的、标准化的核心属性的商品的集合，是商品信息聚合的最小单位。
    * **例如**：“iPhone 11”就是一个SPU。它代表了“iPhone 11”这个产品系列，与它的颜色、内存大小无关。我们通常用SPU，来做商品的通用性描述、展示和搜索。

* **SKU - 库存量单位 (Stock Keeping Unit)**
    我把它理解为“**一件货品**”。它是库存控制的最小可用单位，是真正物理存在的、可以被用户购买的最小单元。
    * **例如**：“一台白色的、内存为64G的iPhone 11”，就是一个SKU。“一台红色的、内存为128G的iPhone 11”，则是另一个完全不同的SKU。**每一个SKU，都有自己独立的库存和价格**。

### 2. SPU与SKU的关系

![image-20250722214946907](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722214946907.png)

SPU和SKU之间，是一个“**一对多**”的层级关系。一个SPU，通过不同的“**销售属性**”的组合，可以衍生出多个不同的SKU。

**销售属性**，就是那些能影响到商品最终售价和库存的属性，比如`颜色`、`尺码`、`内存大小`、`套餐类型`等。

所以，它们之间的关系公式是：
> **SKU = SPU + 一组确定的销售属性**

![image-20250722215055577](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215055577.png)

我们在京东看到的这个iPhone 11商品详情页，就是一个完美的现实案例。
* **SPU**：是“Apple iPhone 11”这个商品本身。
* **销售属性**：是“选择颜色”和“选择版本”这两个维度。
* **SKU**：当用户选择了“黑色”和“128GB”之后，页面上展示的特定价格`¥5269.00`和库存状态，就是属于这个唯一SKU的。

### 3. 技术实现浅谈

那么，为了实现这套逻辑，我作为产品经理，需要如何与我的研发同学沟通呢？我需要向他们讲清楚**后端的数据模型**和**前端的交互逻辑**。

* **后端设计（数据模型）**
    在后台数据库中，我们至少需要设计几张表，来清晰地表达SPU和SKU的关系。

| **数据表** | **我的设计说明** |
| :--- | :--- |
| **SPU表 (spu_table)** | 这张表，用来存放“iPhone 11”这个SPU的通用信息，比如`商品名称`、`商品描述`、`品牌`、`类目`等。 |
| **销售属性名表 (attribute_name_table)** | 这张表，用来定义SPU有哪些销售属性。比如，它会记录：“iPhone 11”这个SPU，有“颜色”和“内存”这两个销售属性。 |
| **销售属性值表 (attribute_value_table)**| 这张表，用来定义每个销售属性有哪些可选值。比如，它会记录：“颜色”这个属性，有“黑色”、“白色”、“红色”等可选值。 |
| **SKU表 (sku_table)** | **这是最核心的表**。它用来存放每一个具体的“货品”。比如，“iPhone 11 黑色 128GB”就是这张表里的一行记录。这行记录里，会包含它**自己专属的`价格`、`库存`**，并会关联到它的父级SPU，以及它所对应的属性值（“黑色”、“128GB”）。 |

* **前端设计（交互逻辑）**
    当用户打开一个商品详情页时，前端与后端的交互流程是这样的：
    1.  前端App向后端服务器，发送一个请求，告诉它：“我需要SPU ID为‘iPhone 11’的商品数据”。
    2.  后端服务器收到请求后，会把**SPU表**里的通用信息（描述、主图等），以及**与这个SPU关联的所有SKU表里的记录**（比如：黑色64G的价格/库存、白色64G的价格/库存、黑色128G的价格/库存……），一次性地，全部返回给前端App。
    3.  前端App拿到这些数据后，就会在页面上，**动态地渲染**出“颜色”和“内存”这两个维度的、所有可点击的**选择按钮**。
    4.  当用户点击“黑色”和“128GB”这两个按钮时，**前端App会直接在本地已经拿到的数据中，查找到对应的那个SKU**，然后**瞬间**将页面上的价格和库存，更新为这个SKU专属的价格和库存。这个过程，通常**不需要再次请求后端服务器**，因此用户会感觉体验非常流畅。

---
## 5.6 商品属性

在上一节，我们学习了SPU和SKU。我们知道，一个“黑色、128G的iPhone 13”是一个SKU。但这立刻引出了一个核心问题：系统是怎么知道“iPhone 13”会有“颜色”和“内存”这两个选项的？商家在发布商品时，并不是随意填写这些信息的。

这背后的答案，就是我们这一节要学习的，一套由平台预先定义好的、结构化的数据体系——**商品属性**。

### 1. 属性的存在方式：属性名 + 属性值

![image-20250722215725696](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215725696.png)

首先，我们要明确一个“属性”最基本的构成。任何一个属性，都是由一个“**属性名**”和一个“**属性值**”配对组成的。
* **属性名**：相当于“问题”，比如：`型号`、`颜色`、`传输速度`。
* **属性值**：相当于“答案”，比如：`CR111`、`黑色`、`1000Mbps`。

我设计的整个商品信息体系，就是由成千上万个这样的“**键值对**”构成的。

### 2. 属性的分类

![image-20250722215842159](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722215842159.png)

为了让这成千上万的属性，能够被系统有序地管理和使用，我必须对它们进行**分类**。在我的电商产品设计中，我会严格地将所有属性，划分为以下三种类型：


| 属性分类 | 简单来说 | 案例（以“iPhone 13”为例） |
| :--- | :--- | :--- |
| **关键属性** | 就像商品的“身份证”，**确定是哪一款商品**，不会变，也不能选。 | “iPhone 13”这个名字本身，以及它的**具体型号**（比如A2634）。 |
| **销售属性** | 决定你**最终买哪个具体商品**，你**必须选**，选了之后**价格或库存可能就不同**。 | **颜色**（星光色、午夜色等）；**存储空间**（128GB、256GB等）。 |
| **其他属性** | 就是商品的**各种特点介绍**，**看看就行，不能选**，也不影响价格。 | 处理器型号 (A15)、屏幕是OLED、防水级别是IP68等等。 |


![image-20250722220341997](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220341997.png)

我们可以通过这张网卡的案例图，清晰地看到这三类属性，在真实商品详情页上的分布。

### 3. 属性池：平台端的统一管理

下一个关键问题是：这么多属性名（如`颜色`、`尺寸`、`CPU型号`），它们是从哪里来的？

为了保证数据的规范和统一（比如，避免商家A填写“颜色”，商家B填写“色彩”）

我必须在平台运营后台，建立一个**由平台统一管理**的`属性池`。

![image-20250722220452045](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220452045.png)

“属性池”，是我为平台运营人员设计的、一个用来**集中管理所有“属性名”**的后台功能。在这里，运营同事可以对平台中可能出现的所有属性名，进行**增、删、改、查**。

* **我的拓展设计（属性与类目的关联）**
    这个“属性池”并不是孤立存在的。它设计的精髓，在于和我们`5.3`节学习的“**商品类目**”进行**深度绑定**。
    
    在我设计的“**类目管理**”后台，当运营人员在编辑“笔记本电脑”这个类目时，他就可以从“属性池”中，为这个类目，勾选上它应该具备的属性，比如`CPU型号`、`内存容量`、`屏幕尺寸`、`硬盘容量`等。
    
    这样一来，当商家在发布商品、第一步选择了“笔记本电脑”这个类目后，系统就会自动地、智能地，为他加载出需要填写的`CPU型号`、`内存容量`等属性，从而实现了整个商品发布流程的结构化和智能化。




---
## 5.7 商品发布功能设计

![image-20250722220910285](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220910285.png)

在前面的小节中，我们已经将“商品”这个复杂的概念，拆解为了`类目`、`品牌`、`SPU`、`SKU`、`属性`等一系列结构化的“原子”部件。

现在，我们的任务，就是**将这些“原子”部件，重新组合起来**，为我们的商家，设计一个功能强大、体验流畅的“**商品发布**”功能。这个功能，就是商家后台的“**核心生产力工具**”。

### 1. 页面信息结构

![image-20250722220935810](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722220935810.png)

在我设计这个复杂的“发布商品”页面时，我首先会对需要商家填写的**信息**，进行一次高层级的**结构化分类**。我会把整个页面，划分为三大信息模块：

1.  **商品的基本信息**：即SPU层级的通用信息，如商品名称、图片等。
2.  **商品的属性**：包括决定SKU的销售属性，以及其他描述性属性。
3.  **商品的详情**：即图文并茂的、用于营销的“长图文”描述。

### 2. 功能模块详细设计

现在，我们来逐一设计承载这三类信息的功能模块。

#### **模块一：填写商品基本信息**

![image-20250722221017448](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221017448.png)

这是商家进入发布流程后，看到的第一个表单区域。它用来采集这件商品（SPU）最基础的信息。

| **字段** | **我的设计说明** |
| :--- | :--- |
| **商品分类** | 这个字段通常是**只读**的，它会显示商家在上一个步骤（`5.2`节推导的流程）中所选择的类目。 |
| **商品名称** | 文本输入框，用于填写SPU的标题。 |
| **商品品牌** | 一个**下拉选择框**。我设计的逻辑是：这个下拉框里，只会出现我们后台**与该“商品分类”相关联**的品牌，而不是全部的品牌。这是一种智能化的设计。 |
| **商品价格** | 商家可以填写商品的“市场价”或“划线价”。每个SKU的具体售价，会在下一步设置。 |
| **商品展示图** | 一个图片上传控件。我会明确地标注出，**最多可以上传几张**，以及推荐的**尺寸和格式**，以保证前端展示效果的统一。 |

#### **模块二：设置销售属性与SKU**

![image-20250722221242154](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221242154.png)

这是整个商品发布功能中，**技术和交互最复杂，但也最核心**的一个模块。我把它分为两步：

**第一步：定义销售属性**
我会提供一个交互区域，让商家可以为他的商品，添加“**销售属性**”。
* **属性名**：通过一个下拉框，让商家从该类目下，我们预设好的属性（如`颜色`、`尺寸`）中进行选择。
* **属性值**：在选定了属性名后，商家可以手动地，添加多个属性值（如`红色`、`蓝色`；`S`、`M`、`L`）。

**第二步：生成SKU并填写明细**
当商家定义好所有的销售属性和属性值后，我设计的后台，最智能的地方就体现出来了：**系统会自动地，将这些属性值进行“笛卡尔积”组合，生成一个完整的SKU列表**。

商家的工作，不是去手动组合SKU，而是在这个自动生成的表格里，“**做填空题**”。他只需要为每一个SKU，填写它专属的`销售价格`和`销售库存`即可。这个设计，极大地降低了商家的操作复杂度和出错率。

#### **模块三：编辑商品描述**

![image-20250722221359012](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722221359012.png)

这是页面的最后一个模块，用于上传商品的“长图文”详情。

* **我的拓展设计（分端描述）**：为了追求极致的用户体验，一个专业的电商后台，应该允许商家，为**PC端**和**移动端**，分别上传和编辑**两套不同**的商品描述。
* **为什么？** 因为PC端屏幕大，可以展示更丰富、更复杂的图文内容；而移动端屏幕小，则需要更简洁、加载速度更快的图片和文字。提供两个独立的“**富文本编辑器**”，能让有能力的商家，为不同设备的用户，提供最优的浏览体验。

通过将这三大模块，有机地组合在一个页面中，我们就为商家，提供了一个功能强大、逻辑清晰、体验智能的商品发布功能。


---
## 5.8 类目关联的相关场景

在前面的小节中，我们已经独立地设计了`商品类目`、`品牌`和`商品属性`这三个核心的数据模块。但如果它们只是三个孤立的列表，那我们的后台依然是“**笨拙**”的。

一个智能的后台，必须能理解这三者之间的**内在关联**。本节，我们就来设计这套“**关联系统**”。

### 1. 类目与属性的关联

我们首先思考一个场景：我们的“**属性池**”里，包含了`颜色`、`尺码`，也包含了`CPU型号`、`屏幕尺寸`。当一个商家来发布一件“T恤”时，如果我们在“设置属性”的环节，把所有这些属性都展示给他，那将是一场灾难。

![image-20250722222404342](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222404342.png)



**我的解决方案**：我必须为商品属性，打上“**类目**”的烙印，即创建“**类目属性**”。

![image-20250722222453447](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222453447.png)

**我的设计思路如下：**

![image-20250722222550441](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222550441.png)

1.  **在平台端（后台）进行关联**：
    在我设计的“**类目管理**”后台，当运营同事在编辑一个“叶子类目”（如：“T恤”）时，我必须提供一个功能，让他可以从“属性池”中，**勾选**出所有与“T恤”相关的属性（如：`颜色`、`尺码`、`材质`、`适用季节`），并将它们**与“T恤”这个类目进行绑定**。

2.  **在商家端（后台）智能调用**：
    经过了后台的“绑定”操作后，商家在发布商品时，当他在第一步选择了“T恤”这个类目，那么在后续的“设置商品属性”环节，系统就会**只加载并显示**出`颜色`、`尺码`、`材质`等这几个已经绑定好的属性，供他填写。

3.  **在用户端（前台）精准呈现**：
    这个设计，最终会惠及我们的用户。当用户在前台浏览“T恤”这个分类列表时，页面左侧的“**筛选器**”，也同样只会展示出`颜色`、`尺码`、`材质`等这些与T恤强相关的、有意义的筛选条件。

![image-20250722222826573](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222826573.png)

### 2. 类目与品牌的关联

同样的逻辑，也完全适用于**品牌管理**。当商家发布一件“NIKE”的T恤时，如果让他从一个包含“海尔”、“华为”等上千个品牌的总列表里去寻找，体验会非常糟糕。

![image-20250722222903391](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222903391.png)

**我的解决方案**：我同样需要在后台，建立**品牌与类目的关联**。这样做的好处是：
* 提升商家发布商品的**便捷性**，避免出错。
* 让我们的品牌管理更**标准化**。
* 让用户在前台按分类+品牌进行**筛选时，速度更快**。

**我的设计思路如下：**

![image-20250722222919119](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722222919119.png)

1.  **在平台端（后台）进行关联**：
    在“**类目管理**”后台，当运营编辑“T恤”这个类目时，除了关联属性，他还需要**关联品牌**。他会从“品牌库”中，勾选出所有属于“服饰”类目的品牌（如：`NIKE`、`阿迪达斯`、`优衣库`）。

2.  **在商家端（后台）智能调用**：
    当商家发布商品，选择了“T恤”类目后，他在“选择品牌”的下拉菜单里，看到的，就将是一个被**智能筛选**过的、只包含“`NIKE`”、“`阿迪达斯`”等服饰品牌的短列表。

**总结**：
“**类目-属性-品牌**”的后台关联设计，是我认为的电商后台商品管理系统中，**最能体现设计功力**的一环。它是一个“**后台配置一小步，前台体验一大步**”的经典设计，能让我们的整个商品体系，变得井然有序、充满智慧。


---
## 5.9 属性管理特殊规则

在我们`5.6`节的设计中，我们确立了“**类目-属性关联**”的核心思想。但在面对一个拥有成千上万类目和属性的大型电商平台时，简单的关联会带来两个新的问题：**一是商家端填写体验杂乱，二是平台端配置效率低下**。

为了解决这两个问题，我必须在我的设计中，引入两个高级的特殊规则：**属性分组**和**属性继承**。

### 1. 属性分组 - 让信息更有序

* **遇到的问题**：
    一个“笔记本电脑”类目，可能会关联几十个属性。如果我在商家发布商品的页面，把这几十个属性输入框，从上到下平铺直叙地排列下来，整个页面会显得极其冗长和混乱，商家很难快速找到自己要填写的项目。

![image-20250722223727689](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223727689.png)

* **我的解决方案：**
    我会引入“**属性组**”的概念。正如我们看到的大部分商品详情页一样，属性信息天然就是可以被“**分组**”的（如：显示器参数、处理器、内存、硬盘等）。

* **我的后台设计：**
    ![image-20250722223657032](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223657032.png)

    1.  **第一步：创建属性组**。在平台运营后台，我会设计一个独立的“**属性组管理**”功能。在这里，运营同事可以创建不同的“属性组”（比如，创建一个名为“`CPU组`”的分组），然后从我们的“属性池”中，将相关的属性（如`CPU型号`、`CPU核心数`）添加进这个组里。
    2.  **第二步：类目关联属性组**。在“类目管理”后台，运营同事在为类目关联属性时，他关联的，就不再是一个个零散的属性，而是一个个已经打包好的“**属性组**”。

* **最终效果：**
    ![image-20250722223907299](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223907299.png)

    经过后台的这一番配置，商家在发布商品，选择了“笔记本电脑”类目后，他看到的属性填写区，就不再是混乱的长列表，而是像图中这样，被清晰地规整在“`处理器`”、“`内存`”、“`硬盘`”等区块之下，一目了然，填写体验大幅提升。

### 2. 属性继承 - 让配置更高效

![image-20250722223601055](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722223601055.png)

* **遇到的问题**：
    我们后台的类目，是树状的多级结构。有一些属性，是非常通用的，比如“`商品毛重`”，它几乎适用于所有实物商品。如果按照我们现有的逻辑，我的运营同事，需要手动地，为成百上千个“叶子类目”，都去重复地关联一次“`商品毛重`”这个属性，这无疑是一场噩梦。

* **我的解决方案：**
    我会为我们的类目-属性关联系统，设计一个“**属性继承**”的规则。

![image-20250722224101627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224101627.png)

* **规则定义**：**任何一个子类目，都会自动地，继承其所有父级类目所关联的全部属性**。

* **我的设计应用**：
    ![image-20250722224138541](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224138541.png)

    有了这条继承规则，我的运营同事的工作，就变得极其高效了：
    1.  他只需要将“`商品毛重`”这个通用属性，关联到最顶级的“**一级分类**”（如：“数码”）上。
    2.  那么，所有属于“数码”下的“**二级分类**”（如：“摄影摄像”）和“**三级分类**”（如：“单反相机”），就都**自动地、无需任何操作地，拥有了**“`商品毛重`”这个属性。
    3.  最终，一个“单反相机”类目下的商品，它所需要填写的属性，就等于“**单反相机”自己关联的属性 + 它继承自“摄影摄像”的属性 + 它继承自“数码”的属性**的总和。

![image-20250722224321109](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722224321109.png)

这个“**属性继承**”的设计，极大地减少了后台运营的重复配置工作量，并保证了整个商品体系属性的规范性和一致性。



---
## 5.10 运费模板

当用户在我们的电商平台下单时，除了商品价格，他最关心的另一个问题就是：**这件商品，寄到我这里，需要多少运费？**

这个问题的背后，对我们产品经理来说，则是另一个问题：**商家是如何，为成千上万、发往全国各地的商品，去设定如此复杂的运费规则的？**

答案，就是我们必须为商家，设计一套功能强大、体验灵活的“**运费模板**”系统。

### 1. 什么是运费模板？

我给**运费模板**的定义是：**一套由商家预先设置并保存好的、包含了复杂运费计算规则的“配置方案”**。

它的核心价值在于“**一次配置，多次复用**”。商家只需要根据自己的物流合作方和商品特性，创建好几套模板（比如：“大件商品-德邦模板”、“小件商品-顺丰模板”），就可以方便地，将这些模板，应用到成千上万的商品上。这种“**一对多**”的设计，能极大地提升商家的运营效率。

### 2. 运费模板功能设计

我设计运费模板功能，主要包含两个核心模块：**模板的创建与管理**，和**商品对模板的应用**。

#### **模块一：创建与管理运费模板**

![image-20250723095615173](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095615173.png)

在商家后台，我需要提供一个“运费管理”的模块，让商家可以“**新建运费模板**”。这个新建模板的表单，是我设计的核心，它必须包含以下配置项：

| **配置项** | **我的设计说明** |
| :--- | :--- |
| **模板名称** | 一个自定义的名称，方便商家自己识别。比如：“顺丰-江浙沪包邮”。 |
| **是否包邮**| 一个简单的单选：**卖家承担运费（即包邮）**或**买家承担运费**。如果选择“包邮”，则下方的复杂规则可以被简化。 |
| **计费规则**| 这是运费计算的基础。通常分为三种：**按件数**、**按重量**、**按体积**。 |
| **默认运费规则**| **这是必填项**。用于设置一个“通用”的运费规则，它适用于所有未被“指定地区”规则覆盖的地区。这能有效避免因漏设地区而导致无法下单的问题。 |
| **指定地区运费规则**| **这是最核心、最灵活的功能**。我需要提供一个入口，让商家可以“**为指定城市设置运费**”。商家可以框选出特定的省市（如：江浙沪），为它们设定一套独立的、不同于“默认规则”的运费。一个模板可以添加多条指定地区的规则。 |
| **规则详情**| 每一条运费规则（无论是默认还是指定），都由“**首件/首重**”和“**续件/续重**”的费用构成。例如：“**1** 件内，**10** 元；每增加 **1** 件，增加运费 **5** 元”。 |

![image-20250723095749331](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095749331.png)

所有创建好的模板，都会在一个“**运费模板列表**”中进行展示，商家可以在这里，对已有的模板，进行**查看、编辑和删除**。

#### **模块二：商品关联运费模板**

![image-20250723095812700](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250723095812700.png)

当商家在后台创建好了运费模板，最后一步，就是将它“**应用**”到具体的商品上。

在我设计的“**发布/编辑商品**”页面（`5.7`节）中，我会增加一个名为“**运费模板**”的**下拉选择框**。这个下拉框的选项，就是该商家在后台创建的所有运费模板。

商家只需要在这里，为这件商品，选择一个合适的模板。那么，当用户在前台下单购买这件商品时，系统就会根据用户的收货地址，自动地、智能地，匹配上运费模板中对应的规则，计算出最终的、精准的运费。




---
## 5.11 本章总结

在本章，我们深入到了电商后台系统的“**发动机舱**”，系统性地学习和设计了整个**商品管理**模块。这套系统的设计优劣，直接决定了我们电商平台“**货**”这个核心要素的规范性、丰富性和可扩展性。

我们的设计旅程，是一次从“原子”到“分子”，再到“系统”的构建过程：
* **解构“原子”**：我们的旅程，是从解构一件“商品”最基本的“**原子**”开始的。我们深刻地理解了`SPU`（一款商品）与`SKU`（一件货品）的本质区别，并掌握了构成它们的三种核心`商品属性`——**关键属性、销售属性、其他属性**。
* **搭建“书架”**：接着，我们为这些“原子”，搭建了用于收纳和组织的“**书架**”。我们设计了平台的“**商品类目**”体系，它就像是图书馆的分类法；我们还设计了“**品牌管理**”体系，它就像是出版社的陈列柜。
* **赋予“智能”**：然后，我们为这套系统，注入了“**智能**”。通过设计“**类目关联**”和“**属性继承**”等特殊规则，我们让“书架”和“书”之间，产生了聪明的联动。
* **打造“工具”**：在所有底层数据模型都设计完毕后，我们最终将它们，组合成了一个面向商家的、功能强大的“**生产力工具**”——**商品发布功能**，并为它设计了必不可少的配套功能——**运费模板**。

我将本章最核心的几个概念，总结在下面的表格中：

| **核心概念** | **我的核心理解** |
| :--- | :--- |
| **SPU与SKU** | SPU是“一款商品”，SKU是“一件货品”。这是商品数据建模的绝对核心。 |
| **类目/品牌/属性**| 这是构成SPU和SKU的“原材料”。平台必须在后台对它们进行**集中、统一、结构化**的管理。 |
| **关联与继承** | 这是让后台变“聪明”的关键。通过**类目关联**，我们为商家提供了智能化的发布体验；通过**属性继承**，我们为运营提升了配置效率。 |
| **商品发布功能**| 这是所有后台数据模型最终的应用场景。一个好的发布流程，能引导商家，录入规范、准确、完整的商品数据。 |

到这里，我们电商产品关于“**用户**”（用户端）、“**后台**”（平台端与商家端）的设计，就已经全部完成了。我们已经拥有了一份足以应对复杂电商业务的、完整的“建筑蓝图”。



---