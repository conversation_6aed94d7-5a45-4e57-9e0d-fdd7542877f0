- const { appId, app<PERSON>ey, avatar, serverURLs } = theme.valine

script(pjax).
    (async () => {
        document.querySelector('!{sel}').textContent = `#{__("loading")}`
        const emojiReg = /:[a-z0-9_\u4e00-\u9fa5]+:/g
        let cache = utils.saveToLocal.get('!{str_name}')
        if (cache) {
            setHtml(document.querySelector('!{sel}'), cache)
            return
        }
        let ls = []
        if (typeof md5 === "undefined") await utils.getScript('!{url_for(theme.cdn.blueimp_md5)}')
        await fetch('!{serverURLs}/1.1/classes/Comment?limit=8&order=-createdAt', {
            method: "GET",
            headers: {
                "X-LC-Id": '!{appId}',
                "X-LC-Key": '!{appKey}',
                "Content-Type": "application/json"
            }
        }).then(async res => res.json()).then(async results => {
            for (const i of results.results) {
                let title = ''
                if (i.url) {
                    await fetch(i.url).then(res => res.text()).then(html => {
                        const parser = new DOMParser()
                        const doc = parser.parseFromString(html, 'text/html')
                        title = doc.querySelector('title').innerText
                    }).catch(() => {
                        title = i.url
                    })
                }
                if (title.indexOf('|') > 0) {
                    title = title.split('|')[0]
                }
                ls.push({
                    title: title,
                    url: i.url,
                    nick: i.nick,
                    avatar: '!{avatar}' + md5(i.mail.trim().toLowerCase()),
                    time: i.createdAt,
                    content: formatContent(i.comment)
                })
            }
            setHtml(document.querySelector('!{sel}'), ls)
            utils.saveToLocal.set('!{str_name}', ls, !{cache})
        })

        function setHtml(el, data) {
            el.innerHTML = data.length !== 0 ? data.map(i => `
                        <div class="comment-card" title="${i.title}" onclick="pjax.loadUrl('${i.url}')">
                            <div class="comment-info">
                                <img src="${i.avatar}" class="nolazyload" alt="${i.nick}">
                                <div>
                                    <span class="comment-user">${i.nick}</span>
                                </div>
                                <time class="comment-time" datetime="${i.time}"></time>
                            </div>
                            <div class="comment-content">${i.content}</div>
                            <div class="comment-title">
                            <i class="solitude fas fa-comment"></i>
                            ${i.title}</div>
                        </div>
                    `).join('') : `#{__("console.newest_comment.empty")}`
            if (typeof utils !== 'undefined') utils.diffDateFormat(document.querySelectorAll('.comment-time'))
            else {
                document.addEventListener('pjax:complete', () => utils.diffDateFormat(document.querySelectorAll('.comment-time')))
                document.addEventListener('DOMContentLoaded', () => utils.diffDateFormat(document.querySelectorAll('.comment-time')))
            }
        }

        function formatContent(content) {
            content = content.replace(emojiReg, '[!{__("console.newest_comment.emoji")}]')
            content = content.replace(/!\[.*?\]\((.*?)\)/g, '[!{__("console.newest_comment.image")}]')
            content = content.replace(/\[.*?\]\((.*?)\)/g, '[!{__("console.newest_comment.link")}]')
            content = content.replace(/```.*?```/g, '[!{__("console.newest_comment.code")}]')
            return content
        }
    })()