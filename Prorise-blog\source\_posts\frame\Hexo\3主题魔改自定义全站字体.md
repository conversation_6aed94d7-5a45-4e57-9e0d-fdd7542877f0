---
title: 3.主题魔改：自定义全站字体
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 7656
date: 2025-07-10 18:13:45
---

### **3.主题魔改：自定义全站字体**

###### **前言：功能介绍**
本指南将向您展示如何通过引入外部字体文件（如 `.ttf`, `.woff2` 等格式），来替换 Anzhiyu 主题的默认字体，让您的博客从视觉上独一无二。

###### **核心流程概览**
1.  **准备并放置字体文件**：将您想使用的字体文件放到博客的源文件目录中。
2.  **定义并引入字体**：通过自定义CSS中的 `@font-face` 规则来“声明”这个新字体。
3.  **在主题配置中应用字体**：告诉主题，网站的全局文本和标题应该使用这个新声明的字体。

---
###### **第一步：准备并放置您的字体文件**

1.  **获取字体文件**
    * 首先，您需要获得您想使用的字体文件。常见的格式有 `.ttf`, `.otf`, `.woff`, `.woff2`。**强烈推荐使用 `.woff2` 格式**，因为它专为网页优化，文件体积最小，加载速度最快。

2.  **放置字体文件**
    * 在您博客的 `source` 目录下，创建一个新文件夹，可以命名为 `fonts`。
    * 将您的字体文件（例如 `my-custom-font.woff2`）放入这个新创建的文件夹中。
    * **最终文件路径示例**：`source/fonts/my-custom-font.woff2`

---
###### **第二步：创建并注入字体CSS文件**

1.  **创建CSS文件**
  
* 在 `themes/anzhiyu/source/css/` 目录下，新建一个文件，命名为 `font.css`。
  
2.  **在 `font.css` 中定义字体**
    * 将下面这段CSS代码完整复制到您刚创建的 `font.css` 文件中，并根据注释进行修改。
    ```css
    /* 在这里定义您的自定义字体 */
    @font-face {
      /* 【重要】为您自己的字体起一个名字，后续配置会用到。建议使用英文字符。 */
      font-family: 'MyCustomFont'; 
      
      /* 浏览器加载字体时的策略，swap表示先显示系统后备字体，字体加载完再平滑替换，体验更佳。 */
      font-display: swap;
      
      /* 【重要】指向您在第一步中放置的字体文件的路径。 */
      /* 请确保这里的路径与您实际存放的位置一致。 */
      src: url('/fonts/my-custom-font.woff2') format("woff2");
    }
    ```

3.  **在主题配置中注入 `font.css`**
    * 打开您**主题的配置文件** (`themes/anzhiyu/_config.yml`)。
    * 找到 `inject:` 配置项，在 `head:` 列表中添加对 `font.css` 的引用。
    ```yaml
    inject:
      head:
        # - 其他 head 内容
        # 添加下面这一行来引入您的字体样式文件
        - <link rel="stylesheet" href="/css/font.css">
    ```
    *(原教程中使用了 `media="defer"` 等属性来异步加载，这里简化为直接加载，效果同样稳定。)*

---
###### **第三步：在主题配置中应用新字体**

1.  **打开主题配置文件** (`themes/anzhiyu/_config.yml`)。
2.  **找到 `font:` 和 `blog_title_font:` 这两个部分**。
3.  将 `font-family` 的值修改为您在 `font.css` 中定义的字体名称（在我们的例子中是 `MyCustomFont`）。

    ```yaml
    # Global font settings (全局字体设置)
    font:
      global-font-size: 16px
      code-font-size:
      # 【修改这里】将全局字体设置为您在CSS中定义的字体名
      # 建议在后面加上一个通用的后备字体，如 , sans-serif
      font-family: 'MyCustomFont', sans-serif
      code-font-family: consolas, Menlo, "PingFang SC", "Microsoft JhengHei", "Microsoft YaHei", sans-serif

    # Font settings for the site title and site subtitle (网站标题和副标题字体设置)
    blog_title_font:
      font_link:
      # 【修改这里】将博客标题字体也设置为您的自定义字体
      font-family: 'MyCustomFont', sans-serif
    ```
    * **请确保**这里填写的字体名称，与您在 `@font-face` 中定义的 `font-family` **完全一致**。

---