error_img = hexo-config('errorpage.img')

$tag_cloud_limit = hexo-config('aside.tags.limit')

$dark_theme = convert(hexo-config('theme_color.dark'))
$dark_theme_op = convert(hexo-config('theme_color.dark') + '23')
$dark_theme_op_deep = convert(hexo-config('theme_color.dark') + 'dd')
$dark_theme_none = convert(hexo-config('theme_color.dark') + '00')
$light_theme = convert(hexo-config('theme_color.light'))
$light_theme_op = convert(hexo-config('theme_color.light') + '23')
$light_theme_op_deep = convert(hexo-config('theme_color.light') + 'dd')
$light_theme_none = convert(hexo-config('theme_color.light') + '00')

$todayCardColor = convert(hexo-config('hometop.recommendList.color'))

// code
if hexo-config('highlight.theme') == 'default'
  $hl_bg_light = #fff
  $hltools_bg_light = #f7f7f9
  $hl_bg_dark = #1b1c20
  $hltools_bg_dark = #21232a
else if hexo-config('highlight.theme') == 'mac'
  $hl_bg_light = #FFFFFF
  $hltools_bg_light = #E7E7E7
  $hl_bg_dark = #1C1E1E
  $hltools_bg_dark = #454a50

$line-height-code-block = 1.6

// font-family
$font-size = unquote(hexo-config('font.font-size'))
$code-font-size = unquote(hexo-config('font.code-font-size'))
$font-family = unquote(hexo-config('font.font-family'))
$code-font-family = unquote(hexo-config('font.code-font-family'))
