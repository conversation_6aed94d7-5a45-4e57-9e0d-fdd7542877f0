- const {copyright} = theme.post.default
.post-copyright
    .post-copyright__author_group
        .post-copyright__author_img
            img.post-copyright__author_img_front(src=url_for(page.avatar || copyright.author || theme.site.icon))
        .post-copyright__author_name= page.author || config.author
        .post-copyright__author_desc= page.desc || config.subtitle
    .post-tools#post-tools
        .post-tools-left
            #quit-box(onclick="RemoveRewardMask()")
            if theme.post.award.enable
                include ./award
            if theme.post.rss
                .reward-link.mode
                    a.reward-link-button(href=url_for(theme.post.rss))
                        i.solitude.fas.fa-seedling
                        = _p('footer.rss')
    if theme.post.share.enable
        .social-share
            -  const path = config.url + url_for(page.path);
            -  const encodedPath = encodeURIComponent(path);
            -  const encodedTitle = encodeURIComponent(page.title);
            -  const encodedDescription = encodeURIComponent(page.description);
            -  const encodedIcon = encodeURIComponent(page.cover || theme.site.icon);
            each item in theme.post.share.list || []
                case item
                    when 'qq'
                        a.social-share-ico.icon-qq(href=`https://connect.qq.com/widget/shareqq/index.html?url=${encodedPath}&title=${encodedTitle}&desc=${encodedDescription}&summary=${encodedDescription}&site=${encodedTitle}&pics=${encodedIcon}` title=_p('post.share.qq'))
                            i.solitude.fab.fa-qq
                    when 'weibo'
                        a.social-share-ico.icon-weibo(href=`http://service.weibo.com/share/share.php?url=${encodedPath}&title=${encodedTitle}&pic=${encodedIcon}` title=_p('post.share.weibo'))
                            i.solitude.fab.fa-weibo
                    when 'telegram'
                        a.social-share-ico.icon-telegram(href=`https://t.me/share/url?url=${encodedPath}&text=${encodedTitle}` title=_p('post.share.telegram'))
                            i.solitude.fab.fa-telegram
                    when 'whatsapp'
                        a.social-share-ico.icon-whatsapp(href=`https://api.whatsapp.com/send?text=${encodedTitle} ${encodedPath}` title=_p('post.share.whatsapp'))
                            i.solitude.fab.fa-whatsapp
                    when 'linkedin'
                        a.social-share-ico.icon-linkedin(href=`https://www.linkedin.com/shareArticle?mini=true&url=${encodedPath}&title=${encodedTitle}&summary=${encodedDescription}&source=${encodedTitle}` title=_p('post.share.linkedin'))
                            i.solitude.fab.fa-linkedin
                    when 'facebook'
                        a.social-share-ico.icon-facebook(href=`https://www.facebook.com/sharer/sharer.php?u=${encodedPath}` title=_p('post.share.facebook'))
                            i.solitude.fab.fa-facebook
                    when 'twitter'
                        a.social-share-ico.icon-twitter(href=`https://twitter.com/intent/tweet?url=${encodedPath}&text=${encodedTitle}` title=_p('post.share.twitter'))
                            i.solitude.fab.fa-twitter
                    when 'link'
                        .social-share-ico.icon-link(onclick=`utils.copy("${path}")` title=_p('post.share.link'))
                            i.solitude.fas.fa-link
                    when 'qrcode'
                        .social-share-ico.icon-qrcode(title=_p('post.share.qrcode'))
                            i.solitude.fas.fa-qrcode
                            .share-main
                                .share-main-all
                                    #qrcode
                                    .reward-dec=_p('post.share.qrcode')
                            script(pjax).
                                typeof QRCode === 'function' && new QRCode(document.getElementById("qrcode"), {
                                    text: '!{path}',
                                    correctLevel : QRCode.CorrectLevel.L
                                });
                                window.addEventListener('DOMContentLoaded', () => {
                                    new QRCode(document.getElementById("qrcode"), {
                                        text: '!{path}',
                                        correctLevel : QRCode.CorrectLevel.L
                                    });
                                });

    if copyright.enable || page.copyright
        .post-copyright__notice
            span.post-copyright-info
                if page.reprint
                    = _p('post.copyright.reprint')
                else
                    = _p('post.copyright.original')
                    a(href=url_for(theme.post.default.copyright.licenurl))
                        | #{theme.post.default.copyright.license}
                    = _p('post.copyright.original_end')
                    a(href=url_for("/")) #{config.title}
