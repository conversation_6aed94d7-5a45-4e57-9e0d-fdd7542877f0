---
title: Python（三）：第二章：转义字符
categories:
  - 后端技术
  - Python
tags:
  - Python基础知识总汇
cover: 'https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp'
comments: true
toc: true
ai: true
abbrlink: 8019
date: 2025-04-18 18:13:45
---

## 第二章：转义字符

```python
# 常用转义字符示例
print("line1 \
line2 \
line3")  # 续行符，用于多行字符串

print("\\")  # 反斜杠符号 
print('\'')  # 单引号
print("\"")  # 双引号
print("\a")  # 响铃符号（某些终端会发声）
print("Hello \b World!")  # 退格符（删除前一个字符）
print("\000")  # 空字符（ASCII码为0）
print("Hello\nWorld")  # 换行符
print("Hello\tWorld")  # 水平制表符
print("Hello\rWorld")  # 回车符（将光标移到行首）
print("\f")  # 换页符

# 八进制和十六进制表示
print("\110\145\154\154\157")  # 八进制表示"Hello"
print("\x48\x65\x6c\x6c\x6f")  # 十六进制表示"Hello"
```