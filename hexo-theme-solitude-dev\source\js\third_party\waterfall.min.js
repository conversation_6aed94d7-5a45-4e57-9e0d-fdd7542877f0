/**
 * @license Copyright 2024 Ever Funnel. All rights reserved.
 * Licensed under the MIT License.
 */
function waterfall(t){function e(t,e){var n=window.getComputedStyle(t);return parseFloat(n["margin"+e])||0}function n(t){return t+"px"}function r(t){return parseFloat(t.style.top)||0}function o(t){return parseFloat(t.style.left)||0}function i(t){return t.clientWidth}function l(t){return t.clientHeight}function u(t){return r(t)+l(t)+e(t,"Bottom")}function a(t){return o(t)+i(t)+e(t,"Right")}function s(t){t.sort(function(t,e){var n=u(t),r=u(e);return n===r?o(e)-o(t):r-n})}function f(e){var containerWidth = i(t);i(t)!==containerWidth&&(window.removeEventListener(e.type,f),waterfall(t))}"string"==typeof t&&(t=document.querySelector(t));var c=Array.from(t.children).map(function(t){return(t.style.position="absolute"),t});t.style.position="relative";var p=Array.from(t.querySelectorAll("img")),y=p.map(function(t){return new Promise(function(e){t.complete?e():(t.addEventListener("load",e),t.addEventListener("error",e))})});return Promise.all(y).then(function(){var r=[];c.length&&(c[0].style.top="0px",c[0].style.left=n(e(c[0],"Left")),r.push(c[0]));for(var l=1;l<c.length;l++){var p=c[l-1],y=c[l];if(!(a(p)+i(y)<=i(t)))break;y.style.top=p.style.top;y.style.left=n(a(p));r.push(y)}for(var v=r.length;v<c.length;v++){s(r);var d=c[v],h=r.pop();d.style.top=n(u(h)+e(d,"Top"));d.style.left=n(o(h));r.push(d)}s(r);var m=r[0];t.style.height=n(u(m)+e(m,"Bottom"));i(t);window.addEventListener("resize",f)})}