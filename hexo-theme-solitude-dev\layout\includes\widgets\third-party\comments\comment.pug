- const {use,count} = theme.comment

#post-comment
    .comment-head
        .comment-headline
            i.solitude.fas.fa-comment
            span=' ' + _p('comment.title')
            if count && is_post()
                span.count
                    = ' ('
                    each name in use
                        case name
                            when "Twikoo"
                                span.twikoo-count
                                    i.solitude.fas.fa-spinner.fa-spin
                            when "Waline"
                                span.waline-comment-count
                                    i.solitude.fas.fa-spinner.fa-spin
                            when "Valine"
                                span.valine-comment-count(data-xid=url_for(page.path) itemprop="commentCount")
                                    i.solitude.fas.fa-spinner.fa-spin
                            when "Artalk"
                                span.artalk-comment-count
                            when "Giscus"
                                span.giscus-count
                    | )

        if use.length > 1
            .comment-switch
                span.first=use[0]
                span#switch-btn
                span.second=use[1]

    .comment-wrap
        each name in use
            case name
                when 'Valine'
                    #vcomment.vcomment
                when 'Twikoo'
                    #twikoo-wrap
                when 'Waline'
                    #waline-wrap
                when 'Artalk'
                    #artalk-wrap
                when 'Giscus'
                    #giscus-wrap