<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Python（二）：第一章：字符串打印格式化与PyCharm模板变量 | Prorise的小站</title><meta name="keywords" content="Python基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#cee8ff"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><meta name="application-name" content="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#cee8ff"><meta property="og:type" content="article"><meta property="og:title" content="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><meta property="og:url" content="https://prorise666.site/posts/2501.html"><meta property="og:site_name" content="Prorise的小站"><meta property="og:description" content="第一章：字符串打印格式化与PyCharm模板变量本章将分为两个主要部分：首先介绍如何在 Python 控制台中使用 ANSI 转义序列来实现文本的彩色和格式化输出，并提供一个实用的封装示例；其次，我们将探讨如何利用 IDE（特别是 PyCharm，但概念也适用于其他IDE）中的 Live Templ"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta name="description" content="第一章：字符串打印格式化与PyCharm模板变量本章将分为两个主要部分：首先介绍如何在 Python 控制台中使用 ANSI 转义序列来实现文本的彩色和格式化输出，并提供一个实用的封装示例；其次，我们将探讨如何利用 IDE（特别是 PyCharm，但概念也适用于其他IDE）中的 Live Templ"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/2501.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"]},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise的小站","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise的小站",title:"Python（二）：第一章：字符串打印格式化与PyCharm模板变量",postAI:"true",pageFillDescription:"第一章：字符串打印格式化与PyCharm模板变量, 字符串打印格式化 (ANSI 转义序列), 基本语法, ANSI 转义码表, 实用示例, 基本颜色设置, 组合使用, 实际应用场景 (封装为 print_utils.py 工具模块), PyCharm Live Templates 提升编码效率, 1. 基本循环, a. for...in 循环 (遍历序列), b. for...in enumerate 循环 (带索引遍历), c. for...in range 循环 (按次数), 2. 条件判断, a. if 语句, b. if-else 语句, c. if-elif-else 语句, 3. 打印与日志, a. print(f...) (f-string 打印), b. logger.info (快速日志记录 - 假设 logger 对象已配置), 4. Python 结构与定义, c. 类定义 (基本结构), d. @dataclass 类定义, 5. 异常处理, a. try-except 基本块, b. try-except-else-finally 完整块, 6. 文件操作, a. with open(...) 上下文管理器, 7. 推导式, a. 列表推导式 (List Comprehension), b. 字典推导式 (Dictionary Comprehension), 8. 其他, a. lambda 匿名函数, b. 注释标记第一章字符串打印格式化与模板变量本章将分为两个主要部分首先介绍如何在控制台中使用转义序列来实现文本的彩色和格式化输出并提供一个实用的封装示例其次我们将探讨如何利用特别是但概念也适用于其他中的实时模板代码片段功能通过预设的模板变量和缩写来大幅提升的编码效率字符串打印格式化转义序列允许在控制台中输出彩色文本和特殊格式这在创建命令行界面或需要突出显示特定输出时非常有用可以显著增强用户体验和信息的可读性这种效果通常是通过转义序列来实现的基本语法转义序列的基本格式如下参数内容或者在字符串中通常写作参数你的文本内容其中或这是字符的八进制或十六进制表示标志着转义序列的开始控制序列引导符参数一个或多个用分号分隔的数字这些数字代码控制着文本的显示方式前景色文字颜色和背景色表示设置图形再现参数的结束标志你的文本内容你希望应用这些格式的实际文本这是一个特殊的重置序列它会清除之前设置的所有格式属性使后续的文本恢复到终端的默认显示状态每次使用完特殊格式后都强烈建议使用此序列来重置以避免格式污染后续的输出转义码表下表列出了一些常用的参数代码显示方式代码前景色代码背景色代码默认黑色黑色高亮粗体红色红色通常不使用绿色绿色下划线黄色黄色闪烁蓝色蓝色反白紫红色紫红色不可见青蓝色青蓝色白色白色注意除了上述标准颜色现代终端通常还支持高强度颜色例如高亮红色使用或者单独的亮色代码色模式以及真彩色模式例如设置前景色为但这些高级模式的兼容性可能因终端模拟器而异闪烁代码和不可见代码的支持程度也取决于终端实用示例基本颜色设置红色文字这是红色文字绿色文字这是绿色文字黄色文字通常与高亮粗体结合使用效果更明显这是高亮黄色文字表示高亮粗体表示黄色蓝色文字这是蓝色文字组合使用可以同时设置显示方式前景色和背景色用分号分隔参数红色文字黄色背景红字黄底高亮绿色文字高亮绿色文字下划线蓝色文字带下划线的蓝色文字高亮紫红色文字白色背景高亮紫红色文字白色背景实际应用场景封装为工具模块为了在项目中更方便更一致地使用彩色打印通常我们会将这些转义序列封装成常量或函数打印工具模块提供彩色和结构化的打印函数彩色打印工具存储颜色和样式代码的常量亮紫色常用于标题亮蓝色亮青色亮绿色亮黄色亮红色粗体高亮下划线重置所有格式打印带特殊格式的标题打印带下划线的青色子标题打印普通信息默认颜色打印成功信息绿色打印警告信息黄色打印错误信息红色打印语句蓝色以结构化方式打印结果项特别是字典彩色打印工具如何使用这个模块将上述代码保存为文件在您的其他脚本中通过或来使用这些函数示例在另一个脚本中使用假设已导入应用程序任务调用封装好的函数任务已成功完成任务执行失败错误代码仅为结构示例提升编码效率实时模板或代码片段是现代集成开发环境如等提供的一项核心功能它允许开发者定义常用的代码结构并通过输入一个简短的缩写后按下特定按键通常是来快速插入这些代码块这些模板通常还支持占位符变量如或在模板展开后会引导用户快速填充这些变量或将光标定位到预设位置使用可以显著减少重复的样板代码输入提高编码速度和效率帮助保持代码风格和结构的一致性减少因手动输入而出错的可能性我们需要根据如下步骤去键入模板基本循环循环遍历序列模板用途快速生成一个遍历可迭代对象的循环建议缩写或您截图中的描述循环模板文本主要占位符说明循环中每次迭代的元素变量名要遍历的序列或可迭代对象模板展开后光标的初始位置循环带索引遍历模板用途快速生成一个同时遍历索引和元素的循环建议缩写或您截图中的描述循环模板文本主要占位符说明循环中每次迭代的索引变量名循环中每次迭代的元素变量名要遍历的序列或可迭代对象循环按次数模板用途快速生成一个按指定次数执行的循环建议缩写描述循环模板文本主要占位符说明循环计数变量名通常是循环的次数条件判断语句模板用途快速生成一个基本的条件判断语句建议缩写描述语句模板文本主要占位符说明语句的条件表达式光标位于代码块内部语句模板用途快速生成条件判断结构建议缩写描述语句模板文本主要占位符说明语句模板用途快速生成多条件判断结构建议缩写描述语句模板文本主要占位符说明打印与日志打印模板用途快速生成一个使用格式化的语句建议缩写描述语句模板文本主要占位符说明光标直接定位在的引号内快速日志记录假设对象已配置模板用途快速插入一条日志记录建议缩写描述模板文本主要占位符说明类似地可以为创建模板结构与定义模板用途快速生成一个带类型注解和文档字符串的函数定义建议缩写描述带类型注解和文档字符串的函数定义模板文本主要占位符说明可默认为可默认为类定义基本结构模板用途快速生成一个带方法的类定义建议缩写描述基本类定义含模板文本初始化对象主要占位符说明类定义模板用途快速生成一个使用模块定义的类建议缩写描述类定义模板文本表示的数据类主要占位符说明异常处理基本块模板用途快速生成一个基本的异常处理块建议缩写描述块模板文本如果使用日志简单打印主要占位符说明可默认为完整块模板用途快速生成包含和子句的块建议缩写描述块模板文本主要占位符说明文件操作上下文管理器模板用途快速生成使用语句安全打开和操作文件的代码建议缩写描述安全文件操作模板文本主要占位符说明可默认为可默认为推导式列表推导式模板用途快速生成列表推导式建议缩写或您截图中的描述模板文本带模板文本不带主要占位符说明可选字典推导式模板用途快速生成字典推导式建议缩写或您截图中的描述模板文本带模板文本不带主要占位符说明可选其他匿名函数模板用途快速创建一个简单的函数建议缩写描述模板文本主要占位符说明注释标记模板用途快速插入标准的注释建议缩写描述模板文本以为例主要占位符说明或可配置或可配置",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-13 22:13:01",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0F1C2E')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#cee8ff')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise的小站" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E7%AB%A0%EF%BC%9A%E5%AD%97%E7%AC%A6%E4%B8%B2%E6%89%93%E5%8D%B0%E6%A0%BC%E5%BC%8F%E5%8C%96%E4%B8%8EPyCharm%E6%A8%A1%E6%9D%BF%E5%8F%98%E9%87%8F"><span class="toc-text">第一章：字符串打印格式化与PyCharm模板变量</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AD%97%E7%AC%A6%E4%B8%B2%E6%89%93%E5%8D%B0%E6%A0%BC%E5%BC%8F%E5%8C%96-ANSI-%E8%BD%AC%E4%B9%89%E5%BA%8F%E5%88%97"><span class="toc-text">字符串打印格式化 (ANSI 转义序列)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%9F%BA%E6%9C%AC%E8%AF%AD%E6%B3%95"><span class="toc-text">基本语法</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#ANSI-%E8%BD%AC%E4%B9%89%E7%A0%81%E8%A1%A8"><span class="toc-text">ANSI 转义码表</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AE%9E%E7%94%A8%E7%A4%BA%E4%BE%8B"><span class="toc-text">实用示例</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9F%BA%E6%9C%AC%E9%A2%9C%E8%89%B2%E8%AE%BE%E7%BD%AE"><span class="toc-text">基本颜色设置</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%BB%84%E5%90%88%E4%BD%BF%E7%94%A8"><span class="toc-text">组合使用</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF-%E5%B0%81%E8%A3%85%E4%B8%BA-print-utils-py-%E5%B7%A5%E5%85%B7%E6%A8%A1%E5%9D%97"><span class="toc-text">实际应用场景 (封装为 print_utils.py 工具模块)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#PyCharm-Live-Templates-%E6%8F%90%E5%8D%87%E7%BC%96%E7%A0%81%E6%95%88%E7%8E%87"><span class="toc-text">PyCharm Live Templates 提升编码效率</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9F%BA%E6%9C%AC%E5%BE%AA%E7%8E%AF"><span class="toc-text">1. 基本循环</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-for-in-%E5%BE%AA%E7%8E%AF-%E9%81%8D%E5%8E%86%E5%BA%8F%E5%88%97"><span class="toc-text">a. for...in 循环 (遍历序列)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-for-in-enumerate-%E5%BE%AA%E7%8E%AF-%E5%B8%A6%E7%B4%A2%E5%BC%95%E9%81%8D%E5%8E%86"><span class="toc-text">b. for...in enumerate 循环 (带索引遍历)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#c-for-in-range-%E5%BE%AA%E7%8E%AF-%E6%8C%89%E6%AC%A1%E6%95%B0"><span class="toc-text">c. for...in range 循环 (按次数)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%9D%A1%E4%BB%B6%E5%88%A4%E6%96%AD"><span class="toc-text">2. 条件判断</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-if-%E8%AF%AD%E5%8F%A5"><span class="toc-text">a. if 语句</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-if-else-%E8%AF%AD%E5%8F%A5"><span class="toc-text">b. if-else 语句</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#c-if-elif-else-%E8%AF%AD%E5%8F%A5"><span class="toc-text">c. if-elif-else 语句</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%89%93%E5%8D%B0%E4%B8%8E%E6%97%A5%E5%BF%97"><span class="toc-text">3. 打印与日志</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-print-f-f-string-%E6%89%93%E5%8D%B0"><span class="toc-text">a. print(f"...") (f-string 打印)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-logger-info-%E5%BF%AB%E9%80%9F%E6%97%A5%E5%BF%97%E8%AE%B0%E5%BD%95-%E5%81%87%E8%AE%BE-logger-%E5%AF%B9%E8%B1%A1%E5%B7%B2%E9%85%8D%E7%BD%AE"><span class="toc-text">b. logger.info (快速日志记录 - 假设 logger 对象已配置)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-Python-%E7%BB%93%E6%9E%84%E4%B8%8E%E5%AE%9A%E4%B9%89"><span class="toc-text">4. Python 结构与定义</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#c-%E7%B1%BB%E5%AE%9A%E4%B9%89-%E5%9F%BA%E6%9C%AC%E7%BB%93%E6%9E%84"><span class="toc-text">c. 类定义 (基本结构)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#d-dataclass-%E7%B1%BB%E5%AE%9A%E4%B9%89"><span class="toc-text">d. @dataclass 类定义</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86"><span class="toc-text">5. 异常处理</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-try-except-%E5%9F%BA%E6%9C%AC%E5%9D%97"><span class="toc-text">a. try-except 基本块</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-try-except-else-finally-%E5%AE%8C%E6%95%B4%E5%9D%97"><span class="toc-text">b. try-except-else-finally 完整块</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#6-%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-text">6. 文件操作</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-with-open-%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8"><span class="toc-text">a. with open(...) 上下文管理器</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-%E6%8E%A8%E5%AF%BC%E5%BC%8F"><span class="toc-text">7. 推导式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-%E5%88%97%E8%A1%A8%E6%8E%A8%E5%AF%BC%E5%BC%8F-List-Comprehension"><span class="toc-text">a. 列表推导式 (List Comprehension)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-%E5%AD%97%E5%85%B8%E6%8E%A8%E5%AF%BC%E5%BC%8F-Dictionary-Comprehension"><span class="toc-text">b. 字典推导式 (Dictionary Comprehension)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-%E5%85%B6%E4%BB%96"><span class="toc-text">8. 其他</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-lambda-%E5%8C%BF%E5%90%8D%E5%87%BD%E6%95%B0"><span class="toc-text">a. lambda 匿名函数</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-%E6%B3%A8%E9%87%8A%E6%A0%87%E8%AE%B0"><span class="toc-text">b. 注释标记</span></a></li></ol></li></ol></li></ol></li></ol></div></nav></div><header class="post-bg" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise的小站</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">音乐</div><span class="author-content-item-title">灵魂的碰撞💥</span></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Python基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-18T09:13:45.000Z" title="发表于 2025-04-18 17:13:45">2025-04-18</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.521Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.4k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>13分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/2501.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><section class="main-hero-waves-area waves-area"><svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto"><defs><path id="gentle-wave" d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"></path></defs><g class="parallax"><use href="#gentle-wave" x="48" y="0"></use><use href="#gentle-wave" x="48" y="3"></use><use href="#gentle-wave" x="48" y="5"></use><use href="#gentle-wave" x="48" y="7"></use></g></svg></section><div id="post-top-cover"><img class="nolazyload" id="post-top-bg" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"></div></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/2501.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Python基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-04-18T09:13:45.000Z" title="发表于 2025-04-18 17:13:45">2025-04-18</time><time itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.521Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></header><div id="postchat_postcontent"><h2 id="第一章：字符串打印格式化与PyCharm模板变量"><a href="#第一章：字符串打印格式化与PyCharm模板变量" class="headerlink" title="第一章：字符串打印格式化与PyCharm模板变量"></a>第一章：字符串打印格式化与PyCharm模板变量</h2><p>本章将分为两个主要部分：首先介绍如何在 Python 控制台中使用 ANSI 转义序列来实现文本的彩色和格式化输出，并提供一个实用的封装示例；其次，我们将探讨如何利用 IDE（特别是 PyCharm，但概念也适用于其他IDE）中的 Live Template (实时模板/代码片段) 功能，通过预设的模板变量和缩写来大幅提升 Python 的编码效率。</p><h3 id="字符串打印格式化-ANSI-转义序列"><a href="#字符串打印格式化-ANSI-转义序列" class="headerlink" title="字符串打印格式化 (ANSI 转义序列)"></a>字符串打印格式化 (ANSI 转义序列)</h3><p>Python 允许在控制台中输出彩色文本和特殊格式，这在创建命令行界面 (CLI) 或需要突出显示特定输出时非常有用，可以显著增强用户体验和信息的可读性。这种效果通常是通过 <strong>ANSI 转义序列 (ANSI escape sequences)</strong> 来实现的。</p><h4 id="基本语法"><a href="#基本语法" class="headerlink" title="基本语法"></a>基本语法</h4><p>ANSI 转义序列的基本格式如下：</p><figure class="highlight text"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">\033[参数m内容\033[0m</span><br></pre></td></tr></tbody></table></figure><p>或者在 Python 字符串中，通常写作：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="string">'\033[&lt;参数&gt;m&lt;你的文本内容&gt;\033[0m'</span></span><br></pre></td></tr></tbody></table></figure><p>其中：</p><ul><li><code>\033</code> (或 <code>\x1b</code>)：这是 ESC 字符的八进制（或十六进制）表示，标志着转义序列的开始。</li><li><code>[</code>：控制序列引导符 (Control Sequence Introducer, CSI)。</li><li><code>&lt;参数&gt;</code>：一个或多个用分号 <code>;</code> 分隔的数字。这些数字代码控制着文本的显示方式、前景色（文字颜色）和背景色。</li><li><code>m</code>：表示设置图形再现参数 (Select Graphic Rendition, SGR) 的结束标志。</li><li><code>&lt;你的文本内容&gt;</code>：你希望应用这些格式的实际文本。</li><li><code>\033[0m</code>：这是一个特殊的重置序列，它会清除之前设置的所有格式属性，使后续的文本恢复到终端的默认显示状态。<strong>每次使用完特殊格式后，都强烈建议使用此序列来重置，以避免格式污染后续的输出。</strong></li></ul><h4 id="ANSI-转义码表"><a href="#ANSI-转义码表" class="headerlink" title="ANSI 转义码表"></a>ANSI 转义码表</h4><p>下表列出了一些常用的 ANSI SGR 参数代码：</p><table><thead><tr><th align="left">显示方式</th><th align="left">代码</th><th align="left">前景色</th><th align="left">代码</th><th align="left">背景色</th><th align="left">代码</th></tr></thead><tbody><tr><td align="left">默认</td><td align="left">0</td><td align="left">黑色</td><td align="left">30</td><td align="left">黑色</td><td align="left">40</td></tr><tr><td align="left">高亮/粗体</td><td align="left">1</td><td align="left">红色</td><td align="left">31</td><td align="left">红色</td><td align="left">41</td></tr><tr><td align="left">(通常不使用)</td><td align="left">2</td><td align="left">绿色</td><td align="left">32</td><td align="left">绿色</td><td align="left">42</td></tr><tr><td align="left">下划线</td><td align="left">4</td><td align="left">黄色</td><td align="left">33</td><td align="left">黄色</td><td align="left">43</td></tr><tr><td align="left">闪烁</td><td align="left">5</td><td align="left">蓝色</td><td align="left">34</td><td align="left">蓝色</td><td align="left">44</td></tr><tr><td align="left">反白</td><td align="left">7</td><td align="left">紫红色</td><td align="left">35</td><td align="left">紫红色</td><td align="left">45</td></tr><tr><td align="left">不可见</td><td align="left">8</td><td align="left">青蓝色</td><td align="left">36</td><td align="left">青蓝色</td><td align="left">46</td></tr><tr><td align="left"></td><td align="left"></td><td align="left">白色</td><td align="left">37</td><td align="left">白色</td><td align="left">47</td></tr></tbody></table><p><strong>注意</strong>：</p><ul><li>除了上述标准颜色 (30-37, 40-47)，现代终端通常还支持高强度颜色 (例如，高亮红色使用 <code>\033[1;31m</code> 或者单独的亮色代码 <code>\033[91m</code>)、256色模式以及 RGB 真彩色模式 (例如 <code>\033[38;2;r;g;bm</code> 设置前景色为 RGB(r,g,b))。但这些高级模式的兼容性可能因终端模拟器而异。</li><li>“闪烁”(代码5) 和 “不可见”(代码8) 的支持程度也取决于终端。</li></ul><h4 id="实用示例"><a href="#实用示例" class="headerlink" title="实用示例"></a>实用示例</h4><h5 id="基本颜色设置"><a href="#基本颜色设置" class="headerlink" title="基本颜色设置"></a>基本颜色设置</h5><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 红色文字</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'\033[31m这是红色文字\033[0m'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 绿色文字</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'\033[32m这是绿色文字\033[0m'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 黄色文字 (通常与高亮/粗体结合使用效果更明显)</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'\033[1;33m这是高亮黄色文字\033[0m'</span>) <span class="comment"># 1表示高亮/粗体，33表示黄色</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 蓝色文字</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'\033[34m这是蓝色文字\033[0m'</span>)</span><br></pre></td></tr></tbody></table></figure><h5 id="组合使用"><a href="#组合使用" class="headerlink" title="组合使用"></a>组合使用</h5><p>可以同时设置显示方式、前景色和背景色，用分号分隔参数。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 红色文字 + 黄色背景</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'\033[31;43m红字黄底\033[0m'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 高亮 + 绿色文字</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'\033[1;32m高亮绿色文字\033[0m'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 下划线 + 蓝色文字</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'\033[4;34m带下划线的蓝色文字\033[0m'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 高亮 + 紫红色文字 + 白色背景</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">'\033[1;35;47m高亮紫红色文字白色背景\033[0m'</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="实际应用场景-封装为-print-utils-py-工具模块"><a href="#实际应用场景-封装为-print-utils-py-工具模块" class="headerlink" title="实际应用场景 (封装为 print_utils.py 工具模块)"></a>实际应用场景 (封装为 <code>print_utils.py</code> 工具模块)</h4><p>为了在项目中更方便、更一致地使用彩色打印，通常我们会将这些 ANSI 转义序列封装成常量或函数。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br></pre></td><td class="code"><pre><span class="line"><span class="string">"""</span></span><br><span class="line"><span class="string">打印工具模块，提供彩色和结构化的打印函数。</span></span><br><span class="line"><span class="string">"""</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># ======== 彩色打印工具 ========</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Colors</span>:</span><br><span class="line">    <span class="string">"""存储 ANSI 颜色和样式代码的常量。"""</span></span><br><span class="line">    HEADER = <span class="string">'\033[95m'</span>    <span class="comment"># 亮紫色 (常用于标题)</span></span><br><span class="line">    BLUE = <span class="string">'\033[94m'</span>      <span class="comment"># 亮蓝色</span></span><br><span class="line">    CYAN = <span class="string">'\033[96m'</span>      <span class="comment"># 亮青色</span></span><br><span class="line">    GREEN = <span class="string">'\033[92m'</span>     <span class="comment"># 亮绿色</span></span><br><span class="line">    WARNING = <span class="string">'\033[93m'</span>   <span class="comment"># 亮黄色</span></span><br><span class="line">    FAIL = <span class="string">'\033[91m'</span>      <span class="comment"># 亮红色</span></span><br><span class="line">    BOLD = <span class="string">'\033[1m'</span>       <span class="comment"># 粗体/高亮</span></span><br><span class="line">    UNDERLINE = <span class="string">'\033[4m'</span>  <span class="comment"># 下划线</span></span><br><span class="line">    END = <span class="string">'\033[0m'</span>        <span class="comment"># 重置所有格式</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">print_header</span>(<span class="params">text: <span class="built_in">str</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""打印带特殊格式的标题。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"\n<span class="subst">{Colors.HEADER}</span><span class="subst">{Colors.BOLD}</span>--- <span class="subst">{text}</span> ---<span class="subst">{Colors.END}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">print_subheader</span>(<span class="params">text: <span class="built_in">str</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""打印带下划线的青色子标题。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"\n<span class="subst">{Colors.CYAN}</span><span class="subst">{Colors.UNDERLINE}</span>  <span class="subst">{text}</span><span class="subst">{Colors.END}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">print_info</span>(<span class="params">text: <span class="built_in">str</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""打印普通信息 (默认颜色)。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f" INFO: <span class="subst">{text}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">print_success</span>(<span class="params">text: <span class="built_in">str</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""打印成功信息 (绿色)。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"<span class="subst">{Colors.GREEN}</span>  ✔ <span class="subst">{text}</span><span class="subst">{Colors.END}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">print_warning</span>(<span class="params">text: <span class="built_in">str</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""打印警告信息 (黄色)。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"<span class="subst">{Colors.WARNING}</span>  ⚠️ [Warning] <span class="subst">{text}</span><span class="subst">{Colors.END}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">print_error</span>(<span class="params">text: <span class="built_in">str</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""打印错误信息 (红色)。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"<span class="subst">{Colors.FAIL}</span>  ❌ [Error] <span class="subst">{text}</span><span class="subst">{Colors.END}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">print_sql</span>(<span class="params">sql: <span class="built_in">str</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""打印SQL语句 (蓝色)。"""</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"<span class="subst">{Colors.BLUE}</span>    SQL: <span class="subst">{sql.strip()}</span><span class="subst">{Colors.END}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">print_result_item</span>(<span class="params">item: <span class="built_in">any</span>, indent: <span class="built_in">int</span> = <span class="number">4</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""以结构化方式打印结果项，特别是字典。"""</span></span><br><span class="line">    prefix = <span class="string">" "</span> * indent</span><br><span class="line">    <span class="keyword">if</span> <span class="built_in">isinstance</span>(item, <span class="built_in">dict</span>):</span><br><span class="line">        details = <span class="string">", "</span>.join([</span><br><span class="line">            <span class="string">f"<span class="subst">{Colors.BOLD}</span><span class="subst">{key}</span><span class="subst">{Colors.END}</span>: <span class="subst">{<span class="built_in">repr</span>(value)}</span>"</span> <span class="keyword">for</span> key, value <span class="keyword">in</span> item.items()</span><br><span class="line">        ])</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"<span class="subst">{prefix}</span>Row(<span class="subst">{details}</span>)"</span>)</span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"<span class="subst">{prefix}</span><span class="subst">{<span class="built_in">repr</span>(item)}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># ======== END 彩色打印工具 ========</span></span><br></pre></td></tr></tbody></table></figure><p><strong>如何使用这个 <code>print_utils</code> 模块：</strong></p><ol><li>将上述代码保存为 <code>print_utils.py</code> 文件。</li><li>在您的其他 Python 脚本中，通过 <code>from print_utils import *</code> 或 <code>import print_utils</code> 来使用这些函数。</li></ol><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 示例：在另一个脚本中使用 print_utils.py</span></span><br><span class="line"><span class="comment"># from print_utils import print_header, print_success, print_error # 假设已导入</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">"__main__"</span>:</span><br><span class="line">    <span class="comment"># print_header("应用程序任务") # 调用封装好的函数</span></span><br><span class="line">    <span class="comment"># print_success("任务A已成功完成！")</span></span><br><span class="line">    <span class="comment"># print_error("任务B执行失败，错误代码：1024")</span></span><br><span class="line">    <span class="keyword">pass</span> <span class="comment"># 仅为结构示例</span></span><br></pre></td></tr></tbody></table></figure><h3 id="PyCharm-Live-Templates-提升编码效率"><a href="#PyCharm-Live-Templates-提升编码效率" class="headerlink" title="PyCharm Live Templates 提升编码效率"></a>PyCharm Live Templates 提升编码效率</h3><p>Live Templates（实时模板或代码片段）是现代集成开发环境 (IDE) 如 PyCharm、VS Code 等提供的一项核心功能。它允许开发者定义常用的代码结构，并通过输入一个简短的<strong>缩写 (Abbreviation)</strong> 后按下特定按键（通常是 <code>Tab</code>）来快速插入这些<strong>代码块 (Template text)</strong>。这些模板通常还支持<strong>占位符变量</strong>，如 <code>$VAR$</code> 或 <code>$CURSOR$</code>，在模板展开后，IDE 会引导用户快速填充这些变量或将光标定位到预设位置。</p><p>使用 Live Templates 可以：</p><ul><li><strong>显著减少重复的样板代码输入</strong>。</li><li><strong>提高编码速度和效率</strong>。</li><li><strong>帮助保持代码风格和结构的一致性</strong>。</li><li><strong>减少因手动输入而出错的可能性</strong>。</li></ul><p>我们需要根据如下步骤去键入模板</p><p><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250518133509693.png" alt="image-20250518133509693"></p><h4 id="1-基本循环"><a href="#1-基本循环" class="headerlink" title="1. 基本循环"></a>1. 基本循环</h4><h5 id="a-for-in-循环-遍历序列"><a href="#a-for-in-循环-遍历序列" class="headerlink" title="a. for...in 循环 (遍历序列)"></a>a. <code>for...in</code> 循环 (遍历序列)</h5><ul><li><strong>模板用途</strong>: 快速生成一个遍历可迭代对象的 <code>for</code> 循环。</li><li><strong>建议缩写</strong>: <code>fori</code> (或您截图中的 <code>iter</code>)</li><li><strong>描述</strong>: <code>for item in iterable:</code> 循环</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">for</span> $ITEM$ <span class="keyword">in</span> $ITERABLE$:</span><br><span class="line">    $CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>:<ul><li><code>$ITEM$</code>: 循环中每次迭代的元素变量名。</li><li><code>$ITERABLE$</code>: 要遍历的序列或可迭代对象。</li><li><code>$CURSOR$</code>: 模板展开后光标的初始位置。</li></ul></li></ul><h5 id="b-for-in-enumerate-循环-带索引遍历"><a href="#b-for-in-enumerate-循环-带索引遍历" class="headerlink" title="b. for...in enumerate 循环 (带索引遍历)"></a>b. <code>for...in enumerate</code> 循环 (带索引遍历)</h5><ul><li><strong>模板用途</strong>: 快速生成一个同时遍历索引和元素的 <code>for</code> 循环。</li><li><strong>建议缩写</strong>: <code>forenum</code> (或您截图中的 <code>itere</code>)</li><li><strong>描述</strong>: <code>for index, item in enumerate(iterable):</code> 循环</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">for</span> $INDEX$, $ITEM$ <span class="keyword">in</span> <span class="built_in">enumerate</span>($ITERABLE$):</span><br><span class="line">    $CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>:<ul><li><code>$INDEX$</code>: 循环中每次迭代的索引变量名。</li><li><code>$ITEM$</code>: 循环中每次迭代的元素变量名。</li><li><code>$ITERABLE$</code>: 要遍历的序列或可迭代对象。</li></ul></li></ul><h5 id="c-for-in-range-循环-按次数"><a href="#c-for-in-range-循环-按次数" class="headerlink" title="c. for...in range 循环 (按次数)"></a>c. <code>for...in range</code> 循环 (按次数)</h5><ul><li><strong>模板用途</strong>: 快速生成一个按指定次数执行的 <code>for</code> 循环。</li><li><strong>建议缩写</strong>: <code>forr</code></li><li><strong>描述</strong>: <code>for i in range(count):</code> 循环</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">for</span> $VAR$ <span class="keyword">in</span> <span class="built_in">range</span>($COUNT$):</span><br><span class="line">    $CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>:<ul><li><code>$VAR$</code>: 循环计数变量名 (通常是 <code>i</code>)。</li><li><code>$COUNT$</code>: 循环的次数。</li></ul></li></ul><h4 id="2-条件判断"><a href="#2-条件判断" class="headerlink" title="2. 条件判断"></a>2. 条件判断</h4><h5 id="a-if-语句"><a href="#a-if-语句" class="headerlink" title="a. if 语句"></a>a. <code>if</code> 语句</h5><ul><li><strong>模板用途</strong>: 快速生成一个基本的 <code>if</code> 条件判断语句。</li><li><strong>建议缩写</strong>: <code>ifc</code></li><li><strong>描述</strong>: <code>if condition:</code> 语句</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">if</span> $CONDITION$:</span><br><span class="line">    $CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>:<ul><li><code>$CONDITION$</code>: <code>if</code> 语句的条件表达式。</li><li><code>$CURSOR$</code>: 光标位于 <code>if</code> 代码块内部。</li></ul></li></ul><h5 id="b-if-else-语句"><a href="#b-if-else-语句" class="headerlink" title="b. if-else 语句"></a>b. <code>if-else</code> 语句</h5><ul><li><strong>模板用途</strong>: 快速生成 <code>if-else</code> 条件判断结构。</li><li><strong>建议缩写</strong>: <code>ifel</code></li><li><strong>描述</strong>: <code>if condition: ... else: ...</code> 语句</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">if</span> $CONDITION$:</span><br><span class="line">    $ACTION_IF_TRUE$</span><br><span class="line"><span class="keyword">else</span>:</span><br><span class="line">    $ACTION_IF_FALSE$</span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$CONDITION$</code>, <code>$ACTION_IF_TRUE$</code>, <code>$ACTION_IF_FALSE$</code>, <code>$CURSOR$</code>。</li></ul><h5 id="c-if-elif-else-语句"><a href="#c-if-elif-else-语句" class="headerlink" title="c. if-elif-else 语句"></a>c. <code>if-elif-else</code> 语句</h5><ul><li><strong>模板用途</strong>: 快速生成 <code>if-elif-else</code> 多条件判断结构。</li><li><strong>建议缩写</strong>: <code>ifelifel</code></li><li><strong>描述</strong>: <code>if cond1: ... elif cond2: ... else: ...</code> 语句</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">if</span> $CONDITION1$:</span><br><span class="line">    $ACTION1$</span><br><span class="line"><span class="keyword">elif</span> $CONDITION2$:</span><br><span class="line">    $ACTION2$</span><br><span class="line"><span class="keyword">else</span>:</span><br><span class="line">    $ACTION_ELSE$</span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$CONDITION1$</code>, <code>$ACTION1$</code>, <code>$CONDITION2$</code>, <code>$ACTION2$</code>, <code>$ACTION_ELSE$</code>, <code>$CURSOR$</code>。</li></ul><h4 id="3-打印与日志"><a href="#3-打印与日志" class="headerlink" title="3. 打印与日志"></a>3. 打印与日志</h4><h5 id="a-print-f-f-string-打印"><a href="#a-print-f-f-string-打印" class="headerlink" title="a. print(f&quot;...&quot;) (f-string 打印)"></a>a. <code>print(f"...")</code> (f-string 打印)</h5><ul><li><strong>模板用途</strong>: 快速生成一个使用 f-string 格式化的 <code>print</code> 语句。</li><li><strong>建议缩写</strong>: <code>prf</code></li><li><strong>描述</strong>: <code>print(f"...")</code> 语句</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="built_in">print</span>(<span class="string">f"$CURSOR$"</span>)</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$CURSOR$</code>: 光标直接定位在 f-string 的引号内。</li></ul><h5 id="b-logger-info-快速日志记录-假设-logger-对象已配置"><a href="#b-logger-info-快速日志记录-假设-logger-对象已配置" class="headerlink" title="b. logger.info (快速日志记录 - 假设 logger 对象已配置)"></a>b. <code>logger.info</code> (快速日志记录 - 假设 <code>logger</code> 对象已配置)</h5><ul><li><strong>模板用途</strong>: 快速插入一条 <code>logger.info</code> 日志记录。</li><li><strong>建议缩写</strong>: <code>logi</code></li><li><strong>描述</strong>: <code>logger.info(f"...")</code></li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">logger.info(<span class="string">f"$MESSAGE$"</span>)</span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$MESSAGE$</code>, <code>$CURSOR$</code>。 (类似地，可以为 <code>debug</code>, <code>warning</code>, <code>error</code>, <code>exception</code> 创建模板)</li></ul><h4 id="4-Python-结构与定义"><a href="#4-Python-结构与定义" class="headerlink" title="4. Python 结构与定义"></a>4. Python 结构与定义</h4><ul><li><strong>模板用途</strong>: 快速生成一个带类型注解和文档字符串的函数定义。</li><li><strong>建议缩写</strong>: <code>defn</code></li><li><strong>描述</strong>: 带类型注解和文档字符串的函数定义</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">def</span> $FUNCTION_NAME$($PARAMS$) -&gt; $RETURN_TYPE$:</span><br><span class="line">    <span class="string">"""$DOCSTRING$"""</span></span><br><span class="line">    $CURSOR$</span><br><span class="line">    <span class="keyword">return</span> $RETURN_VALUE$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$FUNCTION_NAME$</code>, <code>$PARAMS$</code>, <code>$RETURN_TYPE$</code> (可默认为 <code>None</code>), <code>$DOCSTRING$</code>, <code>$CURSOR$</code>, <code>$RETURN_VALUE$</code> (可默认为 <code>None</code>)。</li></ul><h5 id="c-类定义-基本结构"><a href="#c-类定义-基本结构" class="headerlink" title="c. 类定义 (基本结构)"></a>c. 类定义 (基本结构)</h5><ul><li><strong>模板用途</strong>: 快速生成一个带 <code>__init__</code> 方法的类定义。</li><li><strong>建议缩写</strong>: <code>cls</code></li><li><strong>描述</strong>: 基本类定义 (含 <code>__init__</code>)</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">class</span> $ClassName$:</span><br><span class="line">    <span class="string">"""$CLASS_DOCSTRING$"""</span></span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, $ARGS$</span>):</span><br><span class="line">        <span class="string">"""初始化 $ClassName$ 对象。</span></span><br><span class="line"><span class="string">        </span></span><br><span class="line"><span class="string">        Args:</span></span><br><span class="line"><span class="string">            $ARGS_DOC$</span></span><br><span class="line"><span class="string">        """</span></span><br><span class="line">        $INIT_BODY$</span><br><span class="line">        $CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$ClassName$</code>, <code>$CLASS_DOCSTRING$</code>, <code>$ARGS$</code>, <code>$ARGS_DOC$</code>, <code>$INIT_BODY$</code>, <code>$CURSOR$</code>.</li></ul><h5 id="d-dataclass-类定义"><a href="#d-dataclass-类定义" class="headerlink" title="d. @dataclass 类定义"></a>d. <code>@dataclass</code> 类定义</h5><ul><li><strong>模板用途</strong>: 快速生成一个使用 <code>dataclasses</code> 模块定义的类。</li><li><strong>建议缩写</strong>: <code>dtcls</code></li><li><strong>描述</strong>: <code>@dataclass</code> 类定义</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> dataclasses <span class="keyword">import</span> dataclass</span><br><span class="line"></span><br><span class="line"><span class="meta">@dataclass</span></span><br><span class="line"><span class="keyword">class</span> $ClassName$:</span><br><span class="line">    <span class="string">"""表示 $ENTITY_DESCRIPTION$ 的数据类。"""</span></span><br><span class="line">    $FIELD_NAME$: $FIELD_TYPE$</span><br><span class="line">    $CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$ClassName$</code>, <code>$ENTITY_DESCRIPTION$</code>, <code>$FIELD_NAME$</code>, <code>$FIELD_TYPE$</code>, <code>$CURSOR$</code>.</li></ul><h4 id="5-异常处理"><a href="#5-异常处理" class="headerlink" title="5. 异常处理"></a>5. 异常处理</h4><h5 id="a-try-except-基本块"><a href="#a-try-except-基本块" class="headerlink" title="a. try-except 基本块"></a>a. <code>try-except</code> 基本块</h5><ul><li><strong>模板用途</strong>: 快速生成一个基本的 <code>try-except</code> 异常处理块。</li><li><strong>建议缩写</strong>: <code>tryex</code></li><li><strong>描述</strong>: <code>try...except Exception as e:</code> 块</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    $TRY_BODY$</span><br><span class="line"><span class="keyword">except</span> $EXCEPTION_TYPE$ <span class="keyword">as</span> e:</span><br><span class="line">    <span class="comment"># logger.exception(f"An error occurred: {e}") # 如果使用日志</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"An error occurred ($EXCEPTION_TYPE$): <span class="subst">{e}</span>"</span>) <span class="comment"># 简单打印</span></span><br><span class="line">    $CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$TRY_BODY$</code>, <code>$EXCEPTION_TYPE$</code> (可默认为 <code>Exception</code>), <code>$CURSOR$</code>.</li></ul><h5 id="b-try-except-else-finally-完整块"><a href="#b-try-except-else-finally-完整块" class="headerlink" title="b. try-except-else-finally 完整块"></a>b. <code>try-except-else-finally</code> 完整块</h5><ul><li><strong>模板用途</strong>: 快速生成包含 <code>else</code> 和 <code>finally</code> 子句的 <code>try-except</code> 块。</li><li><strong>建议缩写</strong>: <code>tryexelfi</code></li><li><strong>描述</strong>: <code>try...except...else...finally:</code> 块</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    $TRY_BODY$</span><br><span class="line"><span class="keyword">except</span> $EXCEPTION_TYPE$ <span class="keyword">as</span> e:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"An error occurred ($EXCEPTION_TYPE$): <span class="subst">{e}</span>"</span>)</span><br><span class="line">    $EXCEPT_BODY$</span><br><span class="line"><span class="keyword">else</span>:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"Operation successful, no exceptions."</span>)</span><br><span class="line">    $ELSE_BODY$</span><br><span class="line"><span class="keyword">finally</span>:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"Executing finally block."</span>)</span><br><span class="line">    $FINALLY_BODY$</span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$TRY_BODY$</code>, <code>$EXCEPTION_TYPE$</code>, <code>$EXCEPT_BODY$</code>, <code>$ELSE_BODY$</code>, <code>$FINALLY_BODY$</code>, <code>$CURSOR$</code>.</li></ul><h4 id="6-文件操作"><a href="#6-文件操作" class="headerlink" title="6. 文件操作"></a>6. 文件操作</h4><h5 id="a-with-open-上下文管理器"><a href="#a-with-open-上下文管理器" class="headerlink" title="a. with open(...) 上下文管理器"></a>a. <code>with open(...)</code> 上下文管理器</h5><ul><li><strong>模板用途</strong>: 快速生成使用 <code>with</code> 语句安全打开和操作文件的代码。</li><li><strong>建议缩写</strong>: <code>fwith</code></li><li><strong>描述</strong>: <code>with open(...) as f:</code> (安全文件操作)</li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"$FILEPATH$"</span>, mode=<span class="string">"$MODE$"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> $VARNAME$:</span><br><span class="line">        $CURSOR$</span><br><span class="line">        <span class="comment"># content = $VARNAME$.read()</span></span><br><span class="line"><span class="keyword">except</span> FileNotFoundError:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"Error: File '$FILEPATH$' not found."</span>)</span><br><span class="line"><span class="keyword">except</span> IOError <span class="keyword">as</span> e:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"Error reading/writing file '$FILEPATH$': <span class="subst">{e}</span>"</span>)</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$FILEPATH$</code>, <code>$MODE$</code> (可默认为 <code>'r'</code>), <code>$VARNAME$</code> (可默认为 <code>f</code>), <code>$CURSOR$</code>.</li></ul><h4 id="7-推导式"><a href="#7-推导式" class="headerlink" title="7. 推导式"></a>7. 推导式</h4><h5 id="a-列表推导式-List-Comprehension"><a href="#a-列表推导式-List-Comprehension" class="headerlink" title="a. 列表推导式 (List Comprehension)"></a>a. 列表推导式 (List Comprehension)</h5><ul><li><p><strong>模板用途</strong>: 快速生成列表推导式。</p></li><li><p><strong>建议缩写</strong>: <code>lc</code> (或您截图中的 <code>compl</code> / <code>compli</code> for with if)</p></li><li><p><strong>描述</strong>: <code>[expr for item in iterable if condition]</code></p></li><li><p><strong>模板文本 (带if)</strong>:</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">[$EXPRESSION$ <span class="keyword">for</span> $ITEM$ <span class="keyword">in</span> $ITERABLE$ <span class="keyword">if</span> $CONDITION$]</span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>模板文本 (不带if)</strong>:</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">[$EXPRESSION$ <span class="keyword">for</span> $ITEM$ <span class="keyword">in</span> $ITERABLE$]</span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>主要占位符说明</strong>: <code>$EXPRESSION$</code>, <code>$ITEM$</code>, <code>$ITERABLE$</code>, <code>$CONDITION$</code> (可选)。</p></li></ul><h5 id="b-字典推导式-Dictionary-Comprehension"><a href="#b-字典推导式-Dictionary-Comprehension" class="headerlink" title="b. 字典推导式 (Dictionary Comprehension)"></a>b. 字典推导式 (Dictionary Comprehension)</h5><ul><li><strong>模板用途</strong>: 快速生成字典推导式。</li><li><strong>建议缩写</strong>: <code>dc</code> (或您截图中的 <code>compd</code> / <code>compdi</code> for with if)</li><li><strong>描述</strong>: <code>{key_expr: val_expr for item in iterable if condition}</code></li><li><strong>模板文本 (带if)</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">{$KEY_EXPRESSION$: $VALUE_EXPRESSION$ <span class="keyword">for</span> $ITEM$ <span class="keyword">in</span> $ITERABLE$ <span class="keyword">if</span> $CONDITION$}</span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>模板文本 (不带if)</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">{$KEY_EXPRESSION$: $VALUE_EXPRESSION$ <span class="keyword">for</span> $ITEM$ <span class="keyword">in</span> $ITERABLE$}</span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$KEY_EXPRESSION$</code>, <code>$VALUE_EXPRESSION$</code>, <code>$ITEM$</code>, <code>$ITERABLE$</code>, <code>$CONDITION$</code> (可选)。</li></ul><h4 id="8-其他"><a href="#8-其他" class="headerlink" title="8. 其他"></a>8. 其他</h4><h5 id="a-lambda-匿名函数"><a href="#a-lambda-匿名函数" class="headerlink" title="a. lambda 匿名函数"></a>a. <code>lambda</code> 匿名函数</h5><ul><li><strong>模板用途</strong>: 快速创建一个简单的 <code>lambda</code> 函数。</li><li><strong>建议缩写</strong>: <code>lam</code></li><li><strong>描述</strong>: <code>lambda arguments: expression</code></li><li><strong>模板文本</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$LAMBDA_VAR$ = <span class="keyword">lambda</span> $ARGUMENTS$: $EXPRESSION$</span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$LAMBDA_VAR$</code>, <code>$ARGUMENTS$</code>, <code>$EXPRESSION$</code>.</li></ul><h5 id="b-注释标记"><a href="#b-注释标记" class="headerlink" title="b. 注释标记"></a>b. 注释标记</h5><ul><li><strong>模板用途</strong>: 快速插入标准的 TODO, FIXME, NOTE 注释。</li><li><strong>建议缩写</strong>: <code>todo</code> / <code>fixme</code> / <code>note</code></li><li><strong>描述</strong>: <code># TODO: ...</code> / <code># FIXME: ...</code> / <code># NOTE: ...</code></li><li><strong>模板文本 (以 <code>todo</code> 为例)</strong>:<figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># TODO ($USER$ @ $DATE$): $MESSAGE$</span></span><br><span class="line">$CURSOR$</span><br></pre></td></tr></tbody></table></figure></li><li><strong>主要占位符说明</strong>: <code>$USER$</code> (IDE或可配置), <code>$DATE$</code> (IDE或可配置), <code>$MESSAGE$</code>.</li></ul><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/2501.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/2501.html&quot;)">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/2501.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=Python（二）：第一章：字符串打印格式化与PyCharm模板变量&amp;url=https://prorise666.site/posts/2501.html&amp;pic=https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise的小站</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Python<span class="categoryesPageCount">22</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Python基础知识总汇<span class="tagsPageCount">22</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/17730.html"><img class="prev-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Python（一）：Python 语言特性</div></div></a></div><div class="next-post pull-right"><a href="/posts/8019.html"><img class="next-cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Python（三）：第二章：转义字符</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/17730.html" title="Python（一）：Python 语言特性"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（一）：Python 语言特性</div></div></a></div><div><a href="/posts/8019.html" title="Python（三）：第二章：转义字符"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（三）：第二章：转义字符</div></div></a></div><div><a href="/posts/45310.html" title="Python（七）：第六章：条件循环分支"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（七）：第六章：条件循环分支</div></div></a></div><div><a href="/posts/56572.html" title="Python（九）：第八章： 函数知识总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（九）：第八章： 函数知识总结</div></div></a></div><div><a href="/posts/55902.html" title="Python（二十一）：第二十章：Python 语法新特性总结"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十一）：第二十章：Python 语法新特性总结</div></div></a></div><div><a href="/posts/43091.html" title="Python（二十二）：第二十一章：项目结构规范与最佳实践"><img class="cover" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十二）：第二十一章：项目结构规范与最佳实践</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Python（二）：第一章：字符串打印格式化与PyCharm模板变量",date:"2025-04-18 17:13:45",updated:"2025-07-13 22:13:01",tags:["Python基础知识总汇"],categories:["后端技术","Python"],content:'\n## 第一章：字符串打印格式化与PyCharm模板变量\n\n本章将分为两个主要部分：首先介绍如何在 Python 控制台中使用 ANSI 转义序列来实现文本的彩色和格式化输出，并提供一个实用的封装示例；其次，我们将探讨如何利用 IDE（特别是 PyCharm，但概念也适用于其他IDE）中的 Live Template (实时模板/代码片段) 功能，通过预设的模板变量和缩写来大幅提升 Python 的编码效率。\n\n### 字符串打印格式化 (ANSI 转义序列)\n\nPython 允许在控制台中输出彩色文本和特殊格式，这在创建命令行界面 (CLI) 或需要突出显示特定输出时非常有用，可以显著增强用户体验和信息的可读性。这种效果通常是通过 **ANSI 转义序列 (ANSI escape sequences)** 来实现的。\n\n#### 基本语法\n\nANSI 转义序列的基本格式如下：\n\n```text\n\\033[参数m内容\\033[0m\n```\n\n或者在 Python 字符串中，通常写作：\n\n```python\n\'\\033[<参数>m<你的文本内容>\\033[0m\'\n```\n\n其中：\n\n  * `\\033` (或 `\\x1b`)：这是 ESC 字符的八进制（或十六进制）表示，标志着转义序列的开始。\n  * `[`：控制序列引导符 (Control Sequence Introducer, CSI)。\n  * `<参数>`：一个或多个用分号 `;` 分隔的数字。这些数字代码控制着文本的显示方式、前景色（文字颜色）和背景色。\n  * `m`：表示设置图形再现参数 (Select Graphic Rendition, SGR) 的结束标志。\n  * `<你的文本内容>`：你希望应用这些格式的实际文本。\n  * `\\033[0m`：这是一个特殊的重置序列，它会清除之前设置的所有格式属性，使后续的文本恢复到终端的默认显示状态。**每次使用完特殊格式后，都强烈建议使用此序列来重置，以避免格式污染后续的输出。**\n\n#### ANSI 转义码表\n\n下表列出了一些常用的 ANSI SGR 参数代码：\n\n| 显示方式   | 代码 | 前景色   | 代码 | 背景色   | 代码 |\n| :--------- | :--- | :------- | :--- | :------- | :--- |\n| 默认       | 0    | 黑色     | 30   | 黑色     | 40   |\n| 高亮/粗体  | 1    | 红色     | 31   | 红色     | 41   |\n| (通常不使用) | 2    | 绿色     | 32   | 绿色     | 42   |\n| 下划线     | 4    | 黄色     | 33   | 黄色     | 43   |\n| 闪烁       | 5    | 蓝色     | 34   | 蓝色     | 44   |\n| 反白       | 7    | 紫红色   | 35   | 紫红色   | 45   |\n| 不可见     | 8    | 青蓝色   | 36   | 青蓝色   | 46   |\n|            |      | 白色     | 37   | 白色     | 47   |\n\n**注意**：\n\n  * 除了上述标准颜色 (30-37, 40-47)，现代终端通常还支持高强度颜色 (例如，高亮红色使用 `\\033[1;31m` 或者单独的亮色代码 `\\033[91m`)、256色模式以及 RGB 真彩色模式 (例如 `\\033[38;2;r;g;bm` 设置前景色为 RGB(r,g,b))。但这些高级模式的兼容性可能因终端模拟器而异。\n  * “闪烁”(代码5) 和 “不可见”(代码8) 的支持程度也取决于终端。\n\n#### 实用示例\n\n##### 基本颜色设置\n\n```python\n# 红色文字\nprint(\'\\033[31m这是红色文字\\033[0m\')\n\n# 绿色文字\nprint(\'\\033[32m这是绿色文字\\033[0m\')\n\n# 黄色文字 (通常与高亮/粗体结合使用效果更明显)\nprint(\'\\033[1;33m这是高亮黄色文字\\033[0m\') # 1表示高亮/粗体，33表示黄色\n\n# 蓝色文字\nprint(\'\\033[34m这是蓝色文字\\033[0m\')\n```\n\n##### 组合使用\n\n可以同时设置显示方式、前景色和背景色，用分号分隔参数。\n\n```python\n# 红色文字 + 黄色背景\nprint(\'\\033[31;43m红字黄底\\033[0m\')\n\n# 高亮 + 绿色文字\nprint(\'\\033[1;32m高亮绿色文字\\033[0m\')\n\n# 下划线 + 蓝色文字\nprint(\'\\033[4;34m带下划线的蓝色文字\\033[0m\')\n\n# 高亮 + 紫红色文字 + 白色背景\nprint(\'\\033[1;35;47m高亮紫红色文字白色背景\\033[0m\')\n```\n\n#### 实际应用场景 (封装为 `print_utils.py` 工具模块)\n\n为了在项目中更方便、更一致地使用彩色打印，通常我们会将这些 ANSI 转义序列封装成常量或函数。\n\n```python\n"""\n打印工具模块，提供彩色和结构化的打印函数。\n"""\n\n# ======== 彩色打印工具 ========\nclass Colors:\n    """存储 ANSI 颜色和样式代码的常量。"""\n    HEADER = \'\\033[95m\'    # 亮紫色 (常用于标题)\n    BLUE = \'\\033[94m\'      # 亮蓝色\n    CYAN = \'\\033[96m\'      # 亮青色\n    GREEN = \'\\033[92m\'     # 亮绿色\n    WARNING = \'\\033[93m\'   # 亮黄色\n    FAIL = \'\\033[91m\'      # 亮红色\n    BOLD = \'\\033[1m\'       # 粗体/高亮\n    UNDERLINE = \'\\033[4m\'  # 下划线\n    END = \'\\033[0m\'        # 重置所有格式\n\n\ndef print_header(text: str) -> None:\n    """打印带特殊格式的标题。"""\n    print(f"\\n{Colors.HEADER}{Colors.BOLD}--- {text} ---{Colors.END}")\n\ndef print_subheader(text: str) -> None:\n    """打印带下划线的青色子标题。"""\n    print(f"\\n{Colors.CYAN}{Colors.UNDERLINE}  {text}{Colors.END}")\n\ndef print_info(text: str) -> None:\n    """打印普通信息 (默认颜色)。"""\n    print(f" INFO: {text}")\n\ndef print_success(text: str) -> None:\n    """打印成功信息 (绿色)。"""\n    print(f"{Colors.GREEN}  ✔ {text}{Colors.END}")\n\ndef print_warning(text: str) -> None:\n    """打印警告信息 (黄色)。"""\n    print(f"{Colors.WARNING}  ⚠️ [Warning] {text}{Colors.END}")\n\ndef print_error(text: str) -> None:\n    """打印错误信息 (红色)。"""\n    print(f"{Colors.FAIL}  ❌ [Error] {text}{Colors.END}")\n\ndef print_sql(sql: str) -> None:\n    """打印SQL语句 (蓝色)。"""\n    print(f"{Colors.BLUE}    SQL: {sql.strip()}{Colors.END}")\n\ndef print_result_item(item: any, indent: int = 4) -> None:\n    """以结构化方式打印结果项，特别是字典。"""\n    prefix = " " * indent\n    if isinstance(item, dict):\n        details = ", ".join([\n            f"{Colors.BOLD}{key}{Colors.END}: {repr(value)}" for key, value in item.items()\n        ])\n        print(f"{prefix}Row({details})")\n    else:\n        print(f"{prefix}{repr(item)}")\n\n# ======== END 彩色打印工具 ========\n```\n\n**如何使用这个 `print_utils` 模块：**\n\n1.  将上述代码保存为 `print_utils.py` 文件。\n2.  在您的其他 Python 脚本中，通过 `from print_utils import *` 或 `import print_utils` 来使用这些函数。\n\n```python\n# 示例：在另一个脚本中使用 print_utils.py\n# from print_utils import print_header, print_success, print_error # 假设已导入\n\nif __name__ == "__main__":\n    # print_header("应用程序任务") # 调用封装好的函数\n    # print_success("任务A已成功完成！")\n    # print_error("任务B执行失败，错误代码：1024")\n    pass # 仅为结构示例\n```\n\n### PyCharm Live Templates 提升编码效率\n\nLive Templates（实时模板或代码片段）是现代集成开发环境 (IDE) 如 PyCharm、VS Code 等提供的一项核心功能。它允许开发者定义常用的代码结构，并通过输入一个简短的**缩写 (Abbreviation)** 后按下特定按键（通常是 `Tab`）来快速插入这些**代码块 (Template text)**。这些模板通常还支持**占位符变量**，如 `$VAR$` 或 `$CURSOR$`，在模板展开后，IDE 会引导用户快速填充这些变量或将光标定位到预设位置。\n\n使用 Live Templates 可以：\n\n  * **显著减少重复的样板代码输入**。\n  * **提高编码速度和效率**。\n  * **帮助保持代码风格和结构的一致性**。\n  * **减少因手动输入而出错的可能性**。\n\n我们需要根据如下步骤去键入模板\n\n![image-20250518133509693](https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250518133509693.png)\n\n#### 1\\. 基本循环\n\n##### a. `for...in` 循环 (遍历序列)\n\n  * **模板用途**: 快速生成一个遍历可迭代对象的 `for` 循环。\n  * **建议缩写**: `fori` (或您截图中的 `iter`)\n  * **描述**: `for item in iterable:` 循环\n  * **模板文本**:\n    ```python\n    for $ITEM$ in $ITERABLE$:\n        $CURSOR$\n    ```\n  * **主要占位符说明**:\n      * `$ITEM$`: 循环中每次迭代的元素变量名。\n      * `$ITERABLE$`: 要遍历的序列或可迭代对象。\n      * `$CURSOR$`: 模板展开后光标的初始位置。\n\n##### b. `for...in enumerate` 循环 (带索引遍历)\n\n  * **模板用途**: 快速生成一个同时遍历索引和元素的 `for` 循环。\n  * **建议缩写**: `forenum` (或您截图中的 `itere`)\n  * **描述**: `for index, item in enumerate(iterable):` 循环\n  * **模板文本**:\n    ```python\n    for $INDEX$, $ITEM$ in enumerate($ITERABLE$):\n        $CURSOR$\n    ```\n  * **主要占位符说明**:\n      * `$INDEX$`: 循环中每次迭代的索引变量名。\n      * `$ITEM$`: 循环中每次迭代的元素变量名。\n      * `$ITERABLE$`: 要遍历的序列或可迭代对象。\n\n##### c. `for...in range` 循环 (按次数)\n\n  * **模板用途**: 快速生成一个按指定次数执行的 `for` 循环。\n  * **建议缩写**: `forr`\n  * **描述**: `for i in range(count):` 循环\n  * **模板文本**:\n    ```python\n    for $VAR$ in range($COUNT$):\n        $CURSOR$\n    ```\n  * **主要占位符说明**:\n      * `$VAR$`: 循环计数变量名 (通常是 `i`)。\n      * `$COUNT$`: 循环的次数。\n\n#### 2\\. 条件判断\n\n##### a. `if` 语句\n\n  * **模板用途**: 快速生成一个基本的 `if` 条件判断语句。\n  * **建议缩写**: `ifc`\n  * **描述**: `if condition:` 语句\n  * **模板文本**:\n    ```python\n    if $CONDITION$:\n        $CURSOR$\n    ```\n  * **主要占位符说明**:\n      * `$CONDITION$`: `if` 语句的条件表达式。\n      * `$CURSOR$`: 光标位于 `if` 代码块内部。\n\n##### b. `if-else` 语句\n\n  * **模板用途**: 快速生成 `if-else` 条件判断结构。\n  * **建议缩写**: `ifel`\n  * **描述**: `if condition: ... else: ...` 语句\n  * **模板文本**:\n    ```python\n    if $CONDITION$:\n        $ACTION_IF_TRUE$\n    else:\n        $ACTION_IF_FALSE$\n    $CURSOR$\n    ```\n  * **主要占位符说明**: `$CONDITION$`, `$ACTION_IF_TRUE$`, `$ACTION_IF_FALSE$`, `$CURSOR$`。\n\n##### c. `if-elif-else` 语句\n\n  * **模板用途**: 快速生成 `if-elif-else` 多条件判断结构。\n  * **建议缩写**: `ifelifel`\n  * **描述**: `if cond1: ... elif cond2: ... else: ...` 语句\n  * **模板文本**:\n    ```python\n    if $CONDITION1$:\n        $ACTION1$\n    elif $CONDITION2$:\n        $ACTION2$\n    else:\n        $ACTION_ELSE$\n    $CURSOR$\n    ```\n  * **主要占位符说明**: `$CONDITION1$`, `$ACTION1$`, `$CONDITION2$`, `$ACTION2$`, `$ACTION_ELSE$`, `$CURSOR$`。\n\n#### 3\\. 打印与日志\n\n##### a. `print(f"...")` (f-string 打印)\n\n  * **模板用途**: 快速生成一个使用 f-string 格式化的 `print` 语句。\n  * **建议缩写**: `prf`\n  * **描述**: `print(f"...")` 语句\n  * **模板文本**:\n    ```python\n    print(f"$CURSOR$")\n    ```\n  * **主要占位符说明**: `$CURSOR$`: 光标直接定位在 f-string 的引号内。\n\n##### b. `logger.info` (快速日志记录 - 假设 `logger` 对象已配置)\n\n  * **模板用途**: 快速插入一条 `logger.info` 日志记录。\n  * **建议缩写**: `logi`\n  * **描述**: `logger.info(f"...")`\n  * **模板文本**:\n    ```python\n    logger.info(f"$MESSAGE$")\n    $CURSOR$\n    ```\n  * **主要占位符说明**: `$MESSAGE$`, `$CURSOR$`。 (类似地，可以为 `debug`, `warning`, `error`, `exception` 创建模板)\n\n#### 4\\. Python 结构与定义\n\n  * **模板用途**: 快速生成一个带类型注解和文档字符串的函数定义。\n  * **建议缩写**: `defn`\n  * **描述**: 带类型注解和文档字符串的函数定义\n  * **模板文本**:\n    ```python\n    def $FUNCTION_NAME$($PARAMS$) -> $RETURN_TYPE$:\n        """$DOCSTRING$"""\n        $CURSOR$\n        return $RETURN_VALUE$\n    ```\n  * **主要占位符说明**: `$FUNCTION_NAME$`, `$PARAMS$`, `$RETURN_TYPE$` (可默认为 `None`), `$DOCSTRING$`, `$CURSOR$`, `$RETURN_VALUE$` (可默认为 `None`)。\n\n##### c. 类定义 (基本结构)\n\n  * **模板用途**: 快速生成一个带 `__init__` 方法的类定义。\n  * **建议缩写**: `cls`\n  * **描述**: 基本类定义 (含 `__init__`)\n  * **模板文本**:\n    ```python\n    class $ClassName$:\n        """$CLASS_DOCSTRING$"""\n\n        def __init__(self, $ARGS$):\n            """初始化 $ClassName$ 对象。\n            \n            Args:\n                $ARGS_DOC$\n            """\n            $INIT_BODY$\n            $CURSOR$\n    ```\n  * **主要占位符说明**: `$ClassName$`, `$CLASS_DOCSTRING$`, `$ARGS$`, `$ARGS_DOC$`, `$INIT_BODY$`, `$CURSOR$`.\n\n##### d. `@dataclass` 类定义\n\n  * **模板用途**: 快速生成一个使用 `dataclasses` 模块定义的类。\n  * **建议缩写**: `dtcls`\n  * **描述**: `@dataclass` 类定义\n  * **模板文本**:\n    ```python\n    from dataclasses import dataclass\n\n    @dataclass\n    class $ClassName$:\n        """表示 $ENTITY_DESCRIPTION$ 的数据类。"""\n        $FIELD_NAME$: $FIELD_TYPE$\n        $CURSOR$\n    ```\n  * **主要占位符说明**: `$ClassName$`, `$ENTITY_DESCRIPTION$`, `$FIELD_NAME$`, `$FIELD_TYPE$`, `$CURSOR$`.\n\n#### 5\\. 异常处理\n\n##### a. `try-except` 基本块\n\n  * **模板用途**: 快速生成一个基本的 `try-except` 异常处理块。\n  * **建议缩写**: `tryex`\n  * **描述**: `try...except Exception as e:` 块\n  * **模板文本**:\n    ```python\n    try:\n        $TRY_BODY$\n    except $EXCEPTION_TYPE$ as e:\n        # logger.exception(f"An error occurred: {e}") # 如果使用日志\n        print(f"An error occurred ($EXCEPTION_TYPE$): {e}") # 简单打印\n        $CURSOR$\n    ```\n  * **主要占位符说明**: `$TRY_BODY$`, `$EXCEPTION_TYPE$` (可默认为 `Exception`), `$CURSOR$`.\n\n##### b. `try-except-else-finally` 完整块\n\n  * **模板用途**: 快速生成包含 `else` 和 `finally` 子句的 `try-except` 块。\n  * **建议缩写**: `tryexelfi`\n  * **描述**: `try...except...else...finally:` 块\n  * **模板文本**:\n    ```python\n    try:\n        $TRY_BODY$\n    except $EXCEPTION_TYPE$ as e:\n        print(f"An error occurred ($EXCEPTION_TYPE$): {e}")\n        $EXCEPT_BODY$\n    else:\n        print("Operation successful, no exceptions.")\n        $ELSE_BODY$\n    finally:\n        print("Executing finally block.")\n        $FINALLY_BODY$\n    $CURSOR$\n    ```\n  * **主要占位符说明**: `$TRY_BODY$`, `$EXCEPTION_TYPE$`, `$EXCEPT_BODY$`, `$ELSE_BODY$`, `$FINALLY_BODY$`, `$CURSOR$`.\n\n#### 6\\. 文件操作\n\n##### a. `with open(...)` 上下文管理器\n\n  * **模板用途**: 快速生成使用 `with` 语句安全打开和操作文件的代码。\n  * **建议缩写**: `fwith`\n  * **描述**: `with open(...) as f:` (安全文件操作)\n  * **模板文本**:\n    ```python\n    try:\n        with open("$FILEPATH$", mode="$MODE$", encoding="utf-8") as $VARNAME$:\n            $CURSOR$\n            # content = $VARNAME$.read()\n    except FileNotFoundError:\n        print(f"Error: File \'$FILEPATH$\' not found.")\n    except IOError as e:\n        print(f"Error reading/writing file \'$FILEPATH$\': {e}")\n    ```\n  * **主要占位符说明**: `$FILEPATH$`, `$MODE$` (可默认为 `\'r\'`), `$VARNAME$` (可默认为 `f`), `$CURSOR$`.\n\n#### 7\\. 推导式\n\n##### a. 列表推导式 (List Comprehension)\n\n  * **模板用途**: 快速生成列表推导式。\n  * **建议缩写**: `lc` (或您截图中的 `compl` / `compli` for with if)\n  * **描述**: `[expr for item in iterable if condition]`\n  * **模板文本 (带if)**:\n    ```python\n    [$EXPRESSION$ for $ITEM$ in $ITERABLE$ if $CONDITION$]\n    $CURSOR$\n    ```\n  * **模板文本 (不带if)**:\n    \n    ```python\n    [$EXPRESSION$ for $ITEM$ in $ITERABLE$]\n    $CURSOR$\n    ```\n  * **主要占位符说明**: `$EXPRESSION$`, `$ITEM$`, `$ITERABLE$`, `$CONDITION$` (可选)。\n\n##### b. 字典推导式 (Dictionary Comprehension)\n\n  * **模板用途**: 快速生成字典推导式。\n  * **建议缩写**: `dc` (或您截图中的 `compd` / `compdi` for with if)\n  * **描述**: `{key_expr: val_expr for item in iterable if condition}`\n  * **模板文本 (带if)**:\n    ```python\n    {$KEY_EXPRESSION$: $VALUE_EXPRESSION$ for $ITEM$ in $ITERABLE$ if $CONDITION$}\n    $CURSOR$\n    ```\n  * **模板文本 (不带if)**:\n    ```python\n    {$KEY_EXPRESSION$: $VALUE_EXPRESSION$ for $ITEM$ in $ITERABLE$}\n    $CURSOR$\n    ```\n  * **主要占位符说明**: `$KEY_EXPRESSION$`, `$VALUE_EXPRESSION$`, `$ITEM$`, `$ITERABLE$`, `$CONDITION$` (可选)。\n\n\n\n#### 8. 其他\n\n##### a. `lambda` 匿名函数\n\n  * **模板用途**: 快速创建一个简单的 `lambda` 函数。\n  * **建议缩写**: `lam`\n  * **描述**: `lambda arguments: expression`\n  * **模板文本**:\n    ```python\n    $LAMBDA_VAR$ = lambda $ARGUMENTS$: $EXPRESSION$\n    $CURSOR$\n    ```\n  * **主要占位符说明**: `$LAMBDA_VAR$`, `$ARGUMENTS$`, `$EXPRESSION$`.\n\n##### b. 注释标记\n\n  * **模板用途**: 快速插入标准的 TODO, FIXME, NOTE 注释。\n  * **建议缩写**: `todo` / `fixme` / `note`\n  * **描述**: `# TODO: ...` / `# FIXME: ...` / `# NOTE: ...`\n  * **模板文本 (以 `todo` 为例)**:\n    ```python\n    # TODO ($USER$ @ $DATE$): $MESSAGE$\n    $CURSOR$\n    ```\n  * **主要占位符说明**: `$USER$` (IDE或可配置), `$DATE$` (IDE或可配置), `$MESSAGE$`.\n\n-----'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="author-info__sayhi" id="author-info__sayhi" onclick="anzhiyu.changeSayHelloText()"></div></div><div class="author-info-avatar"><img class="avatar-img" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"><div class="author-status"><img class="g-status" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div><div class="card-bottom-section"><div class="author-info__bottom-group"><a class="author-info__bottom-group-left" href="/"><h1 class="author-info__name">Prorise</h1><div class="author-info__desc">&lt;div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;"&gt;🚀 全栈开发者，专注于构建优雅高效的数字解决方案&lt;br&gt;&lt;span style="color:#fff;font-weight:bold;"&gt;前端 · 后端 · 架构 · 优化&lt;/span&gt;&lt;br&gt;📚 分享实战经验，一起探索技术边界&lt;/div&gt;</div></a><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">104</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">6</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">8</div><div class="stat-label">分类</div></a></div></div><div class="card-info-social-icons is-center"><a class="social-icon faa-parent animated-hover" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" target="_blank" title="BiliBili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a></div></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E7%AB%A0%EF%BC%9A%E5%AD%97%E7%AC%A6%E4%B8%B2%E6%89%93%E5%8D%B0%E6%A0%BC%E5%BC%8F%E5%8C%96%E4%B8%8EPyCharm%E6%A8%A1%E6%9D%BF%E5%8F%98%E9%87%8F"><span class="toc-number">1.</span> <span class="toc-text">第一章：字符串打印格式化与PyCharm模板变量</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AD%97%E7%AC%A6%E4%B8%B2%E6%89%93%E5%8D%B0%E6%A0%BC%E5%BC%8F%E5%8C%96-ANSI-%E8%BD%AC%E4%B9%89%E5%BA%8F%E5%88%97"><span class="toc-number">1.1.</span> <span class="toc-text">字符串打印格式化 (ANSI 转义序列)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%9F%BA%E6%9C%AC%E8%AF%AD%E6%B3%95"><span class="toc-number">1.1.1.</span> <span class="toc-text">基本语法</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#ANSI-%E8%BD%AC%E4%B9%89%E7%A0%81%E8%A1%A8"><span class="toc-number">1.1.2.</span> <span class="toc-text">ANSI 转义码表</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AE%9E%E7%94%A8%E7%A4%BA%E4%BE%8B"><span class="toc-number">1.1.3.</span> <span class="toc-text">实用示例</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%9F%BA%E6%9C%AC%E9%A2%9C%E8%89%B2%E8%AE%BE%E7%BD%AE"><span class="toc-number">1.1.3.1.</span> <span class="toc-text">基本颜色设置</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%BB%84%E5%90%88%E4%BD%BF%E7%94%A8"><span class="toc-number">1.1.3.2.</span> <span class="toc-text">组合使用</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF-%E5%B0%81%E8%A3%85%E4%B8%BA-print-utils-py-%E5%B7%A5%E5%85%B7%E6%A8%A1%E5%9D%97"><span class="toc-number">1.1.4.</span> <span class="toc-text">实际应用场景 (封装为 print_utils.py 工具模块)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#PyCharm-Live-Templates-%E6%8F%90%E5%8D%87%E7%BC%96%E7%A0%81%E6%95%88%E7%8E%87"><span class="toc-number">1.2.</span> <span class="toc-text">PyCharm Live Templates 提升编码效率</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9F%BA%E6%9C%AC%E5%BE%AA%E7%8E%AF"><span class="toc-number">1.2.1.</span> <span class="toc-text">1. 基本循环</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-for-in-%E5%BE%AA%E7%8E%AF-%E9%81%8D%E5%8E%86%E5%BA%8F%E5%88%97"><span class="toc-number">1.2.1.1.</span> <span class="toc-text">a. for...in 循环 (遍历序列)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-for-in-enumerate-%E5%BE%AA%E7%8E%AF-%E5%B8%A6%E7%B4%A2%E5%BC%95%E9%81%8D%E5%8E%86"><span class="toc-number">1.2.1.2.</span> <span class="toc-text">b. for...in enumerate 循环 (带索引遍历)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#c-for-in-range-%E5%BE%AA%E7%8E%AF-%E6%8C%89%E6%AC%A1%E6%95%B0"><span class="toc-number">1.2.1.3.</span> <span class="toc-text">c. for...in range 循环 (按次数)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%9D%A1%E4%BB%B6%E5%88%A4%E6%96%AD"><span class="toc-number">1.2.2.</span> <span class="toc-text">2. 条件判断</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-if-%E8%AF%AD%E5%8F%A5"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">a. if 语句</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-if-else-%E8%AF%AD%E5%8F%A5"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">b. if-else 语句</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#c-if-elif-else-%E8%AF%AD%E5%8F%A5"><span class="toc-number">1.2.2.3.</span> <span class="toc-text">c. if-elif-else 语句</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%89%93%E5%8D%B0%E4%B8%8E%E6%97%A5%E5%BF%97"><span class="toc-number">1.2.3.</span> <span class="toc-text">3. 打印与日志</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-print-f-f-string-%E6%89%93%E5%8D%B0"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">a. print(f"...") (f-string 打印)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-logger-info-%E5%BF%AB%E9%80%9F%E6%97%A5%E5%BF%97%E8%AE%B0%E5%BD%95-%E5%81%87%E8%AE%BE-logger-%E5%AF%B9%E8%B1%A1%E5%B7%B2%E9%85%8D%E7%BD%AE"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">b. logger.info (快速日志记录 - 假设 logger 对象已配置)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-Python-%E7%BB%93%E6%9E%84%E4%B8%8E%E5%AE%9A%E4%B9%89"><span class="toc-number">1.2.4.</span> <span class="toc-text">4. Python 结构与定义</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#c-%E7%B1%BB%E5%AE%9A%E4%B9%89-%E5%9F%BA%E6%9C%AC%E7%BB%93%E6%9E%84"><span class="toc-number">1.2.4.1.</span> <span class="toc-text">c. 类定义 (基本结构)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#d-dataclass-%E7%B1%BB%E5%AE%9A%E4%B9%89"><span class="toc-number">1.2.4.2.</span> <span class="toc-text">d. @dataclass 类定义</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#5-%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86"><span class="toc-number">1.2.5.</span> <span class="toc-text">5. 异常处理</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-try-except-%E5%9F%BA%E6%9C%AC%E5%9D%97"><span class="toc-number">1.2.5.1.</span> <span class="toc-text">a. try-except 基本块</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-try-except-else-finally-%E5%AE%8C%E6%95%B4%E5%9D%97"><span class="toc-number">1.2.5.2.</span> <span class="toc-text">b. try-except-else-finally 完整块</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#6-%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-number">1.2.6.</span> <span class="toc-text">6. 文件操作</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-with-open-%E4%B8%8A%E4%B8%8B%E6%96%87%E7%AE%A1%E7%90%86%E5%99%A8"><span class="toc-number">1.2.6.1.</span> <span class="toc-text">a. with open(...) 上下文管理器</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-%E6%8E%A8%E5%AF%BC%E5%BC%8F"><span class="toc-number">1.2.7.</span> <span class="toc-text">7. 推导式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-%E5%88%97%E8%A1%A8%E6%8E%A8%E5%AF%BC%E5%BC%8F-List-Comprehension"><span class="toc-number">1.2.7.1.</span> <span class="toc-text">a. 列表推导式 (List Comprehension)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-%E5%AD%97%E5%85%B8%E6%8E%A8%E5%AF%BC%E5%BC%8F-Dictionary-Comprehension"><span class="toc-number">1.2.7.2.</span> <span class="toc-text">b. 字典推导式 (Dictionary Comprehension)</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-%E5%85%B6%E4%BB%96"><span class="toc-number">1.2.8.</span> <span class="toc-text">8. 其他</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-lambda-%E5%8C%BF%E5%90%8D%E5%87%BD%E6%95%B0"><span class="toc-number">1.2.8.1.</span> <span class="toc-text">a. lambda 匿名函数</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-%E6%B3%A8%E9%87%8A%E6%A0%87%E8%AE%B0"><span class="toc-number">1.2.8.2.</span> <span class="toc-text">b. 注释标记</span></a></li></ol></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">104</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">6</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">8</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src= "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script defer="" id="fluttering_ribbon" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-fluttering-ribbon.min.js"></script><script id="canvas_nest" defer="" color="0,0,255" opacity="0.7" zindex="-1" count="99" mobile="false" src="https://cdn.cbd.int/butterfly-extsrc@1.1.3/dist/canvas-nest.min.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#4d648d",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 PRChat",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "https://bu.dusays.com/2025/07/07/686b7fe90170b.png",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>