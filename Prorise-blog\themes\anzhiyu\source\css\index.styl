if hexo-config('css_prefix')
  @import 'nib'

@import '_third-party/normalize.min.css'

// project
@import 'var'
@import '_global/*'
@import '_highlight/highlight'
@import '_page/*'
@import '_layout/*'
@import '_tags/*'
@import '_mode/*'
@import '_third-party/*'
@import '_extra/**/*.css'

// search
if hexo-config('algolia_search.enable')
  @import '_search/index'
  @import '_search/algolia'

if hexo-config('local_search') && hexo-config('local_search.enable')
  @import '_search/index'
  @import '_search/local-search'
