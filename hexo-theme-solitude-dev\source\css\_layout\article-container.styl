.article-container
  overflow-wrap break-word
  +maxWidth768()
    overflow hidden

  .post &
    padding 1rem 2rem
    +maxWidth768()
      padding .5rem .5rem 1rem .5rem

  iframe
    border-radius 12px
    margin 0 0 1rem

  a
    color var(--efu-fontcolor)
    border-radius 4px 4px 0 0
    border-bottom 2px dotted var(--efu-lighttext)
    font-weight 700

    &.fancybox
      border-bottom none

  .table-wrap
    margin 1rem 0
    border-radius 8px
    overflow-x scroll

  a.headerlink
    opacity .08
    position absolute
    right 0
    padding 0
    border none
    border-radius 8px
    line-height 1
    font-size .8rem

    #page &
      display none

    &::before
      content "\f0c1"
      font-family 'FontAwesome'
      cursor pointer

    &:hover
      border-bottom none
      box-shadow none
      background var(--efu-none)
      color var(--efu-lighttext)
      opacity 1

  p
    font-size .9rem
    line-height 1.7
    font-weight 400
    margin .5rem 0
    text-align justify
    letter-spacing .6px

    a
      &:not(.fancybox)
        text-decoration none
        border none
        color var(--efu-theme)
        font-weight 700
        padding 0 4px
        background linear-gradient(to top, var(--efu-theme-op), var(--efu-theme-op)) no-repeat bottom / 100% .1em
        transition all .2s

        &:hover
          border-radius 4px
          background-size: 100% 100%

  blockquote
    p
      padding 0
      margin 0
      font-size 15px

  code:not([class*='language-'])
    color var(--efu-fontcolor)
    padding .4em
    border-radius 4px
    font-size 12px
    background var(--efu-card-bg)
    line-height 1.5
    box-shadow var(--efu-shadow-border)
    border var(--style-border)

  img:not(.post_bg)
    border-radius 12px
    object-fit cover
    display block
    margin 0.8rem auto
    max-width 90%
    max-height 450px

  ul, ol
    padding 0
    margin-top 0.4rem
    list-style none
    counter-reset li 0

    li
      &:before
        position absolute
        top 0
        left 0
        color var(--efu-fontcolor)
        transition all .3s ease-out 0s
        background var(--efu-lighttext)

      &:not(.tab)
        position relative
        margin 0.2rem 0

        p
          margin 0

  ul
    > li
      &:not(.tab)
        padding 0.2em 0.2em 0.2em 1.4em

        &:before
          top .78em
          width .42em
          height .42em
          border-radius .42em
          background 0 0
          border .21em solid var(--efu-lighttext)
          content ""
          line-height .42em

  ol
    > li:before
      margin-top .65em
      width 1.45em
      height 1.45em
      border-radius .725em
      text-align center
      font-size .85em
      color var(--efu-white)
      line-height 1.45em

    ul li
      line-height 1.7

    > li

      &:not(.tab)
        padding .2em .2em .2em 1.8em

      &:before
        content counter(li)
        counter-increment li 1

      > ol
        > li
          &:before
            content counter(li, lower-alpha)
            counter-increment li

          > ol
            > li
              &:before
                content counter(li, lower-roman)
                counter-increment li

  &.post-content
    h1, h2, h3, h4
      padding-right 1rem
      position relative
      display flex
      flex-wrap wrap
      align-items center
      transition all .2s ease-out 0s

    h1
      font-size 1.5rem
      line-height 1.3

    h2
      font-size 1.3rem
      line-height 1.3
      border-top 1px dashed var(--efu-theme-op)
      padding-top 1rem

    h3, h4
      font-size 1.1rem
      line-height 1.3
  hr
    display flex
    position relative
    margin 0.5rem 0
    border-width 0
    border-top 1px dashed var(--efu-theme-op)

if hexo-config('mediumZoom')
  .medium-zoom--opened .medium-zoom-overlay
    z-index 10

  .medium-zoom-image--opened
    z-index 11
