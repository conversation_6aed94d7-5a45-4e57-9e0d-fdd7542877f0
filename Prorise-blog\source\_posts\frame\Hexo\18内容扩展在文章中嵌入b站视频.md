---
title: 18.内容扩展：在文章中嵌入B站视频
categories:
  - 框架技术
  - Hexo
  - 魔改
tags:
  - 博客搭建教程
cover: >-
  https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp
comments: true
toc: true
ai: true
abbrlink: 51074
date: 2025-07-11 10:13:45
---

### **18.内容扩展：在文章中嵌入B站视频**

###### **前言：功能介绍**

本指南将引导您通过添加一小段自定义CSS和标准的 `<iframe>` HTML代码，在您的任意文章或页面中，嵌入能够自适应不同屏幕尺寸的Bilibili视频播放器。

###### **核心流程概览**
1.  **添加响应式CSS样式**：确保嵌入的视频在手机和电脑上都能以合适的尺寸显示。
2.  **获取视频信息并嵌入文章**：找到您想分享的B站视频的关键ID，并将其填入HTML代码中。
3.  **自定义播放器参数**：通过修改链接，控制视频是否自动播放、画质等。

---
###### **第一步：添加响应式CSS样式**

为了让嵌入的Bilibili视频能够在不同尺寸的设备（如手机、平板、电脑）上都完美显示，我们需要先添加一段CSS来控制它的宽高。

1.  **创建CSS文件**
  
* 在 `themes/anzhiyu/source/css/` 目录下，新建一个文件，命名为 `bilibili.css`。
  
2.  **粘贴CSS代码**
    * 将下面的CSS代码完整复制到您刚创建的 `bilibili.css` 文件中。
    ```css
    .bilibili {
        position: relative;
        width: 100%;
    }
    @media only screen and (max-width: 767px) {
        .bilibili {height: 15em;max-width: 25em;}
    }
    @media only screen and (min-width: 768px) and (max-width: 991px) {
        .bilibili {height: 20em;max-width: 30em;}
    }
    @media only screen and (min-width: 992px) and (max-width: 1199px) {
        .bilibili {height: 30em;max-width: 40em;}
    }
    @media only screen and (min-width: 1200px) {
        .bilibili {height: 40em;max-width: 50em;}
    }
    ```

3.  **在主题配置中注入CSS文件**
    * 打开您**主题的配置文件** (`themes/anzhiyu/_config.yml`)。
    * 找到 `inject:` 配置项，在 `head:` 列表中添加我们新建的CSS文件。
    ```yaml
    inject:
      head:
        # - 其他 head 内容
        - '<link rel="stylesheet" href="/css/bilibili.css">'
    ```

---
###### **第二步：获取视频信息并嵌入文章**

1.  **找到视频的关键ID**
    * 打开您想嵌入的Bilibili视频页面。
    * 在视频的地址栏或下方的“分享”按钮中，您通常能找到两个关键ID：**`bvid`** (或 `aid`) 和 **`cid`**。
        * **bvid**: 是以‘BV’开头的字符串，是现在B站视频的主要ID。
        * **aid**: 是以‘av’开头的纯数字ID，是旧版的ID，两者通常都能用。
        * **cid**: 是视频的分P ID，对于多P视频，每个P都有一个独立的cid。获取它最简单的方法是，在播放页面按 `F12` 打开开发者工具，在“控制台(Console)”中输入 `window.__INITIAL_STATE__.cid` 然后回车即可看到。

2.  **粘贴HTML代码**
    * 在您想展示视频的文章或页面的 `.md` 文件中，将下面这段 `<iframe>` 代码粘贴进去。
    ```html
    <iframe class="bilibili" src="//player.bilibili.com/player.html?bvid=【您的视频BVID】&cid=【您的视频CID】&page=1" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"> </iframe>
    ```
    * 将代码中的 `【您的视频BVID】` 和 `【您的视频CID】` 替换为您在上一步中找到的真实ID。

---
###### **第三步：自定义播放器参数（可选）**

您可以通过在 `src` 链接后面添加不同的参数，来控制播放器的行为。多个参数用 `&` 连接。

| 参数 | 作用 | 示例 |
| :--- | :--- | :--- |
| **`&autoplay=0`** | **禁止自动播放**。 | `...&page=1&autoplay=0` |
| **`&high_quality=1`**| **请求更高画质**（默认为流畅）。 | `...&autoplay=0&high_quality=1` |
| `sandbox="..."` | **禁止跳转**。一个HTML属性，可以防止在iframe内点击B站logo时，整个页面跳转到B站。 | 在`<iframe>`标签上添加 `sandbox="allow-top-navigation allow-same-origin allow-forms allow-scripts"` |


**一个包含所有自定义项的最终示例：**
```html
<iframe 
  class="bilibili" 
  src="//player.bilibili.com/player.html?bvid=BV1Ch41137tR&cid=12345678&page=1&autoplay=0&high_quality=1" 
  scrolling="no" 
  border="0" 
  frameborder="no" 
  framespacing="0" 
  allowfullscreen="true"
  sandbox="allow-top-navigation allow-same-origin allow-forms allow-scripts">
</iframe>
```

---